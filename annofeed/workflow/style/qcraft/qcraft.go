package qcraft

import (
	"bufio"
	"bytes"
	"context"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"math"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"annofeed/internal/biz"
	"annofeed/workflow/common"
	"gitlab.rp.konvery.work/platform/apis/annofeed/v1"
	"gitlab.rp.konvery.work/platform/pkg/kfs"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"github.com/seqsense/pcgol/pc"
)

const (
	PointcloudFile           = "points.json"                 // 点云数据，世界坐标系
	PosesFile                = "poses.json"                  // ⻋辆位姿数据，有每一帧对应的⻋辆的坐标以及旋转⻆度，世界坐标系
	LidarExtrinsicParamsFile = "lidar_extrinsic_params.json" // 雷达的位姿数据，自车坐标系
	CamParamsFile            = "camera_params.json"          // 相机的内外参，雷达坐标系
	ImagePosesFile           = "image_poses.json"            // 为每个相机单独匹配的⻋辆位姿数据，可以实现图片上点云的准确投影
	VehicleParamsFile        = "vehicle.json"                // ⻋辆的尺寸数据
)

var camIdxToName = map[int]string{
	50: "cam_front_wide",
	52: "cam_front_tele",
	53: "cam_front_left",
	54: "cam_front_right",
	55: "cam_rear_left",
	56: "cam_rear_right",
	57: "cam_rear",
}

func Transform(ctx context.Context, data *biz.Data, dir string, startIdx int, hb common.Heartbeat) (dataType string, err error) {
	newdir := dir + "-1"
	o := newQcraftParser(dir, newdir)
	err = kfs.IterateDir(dir, func(entry os.DirEntry, subdir string, depth int) error {
		// skip directories
		if entry.IsDir() {
			return nil
		}
		hb(ctx)
		return o.parseFile(ctx, subdir, entry.Name())
	})
	if err != nil {
		return "", err
	}

	if err = o.closeFrame(ctx); err != nil {
		return "", fmt.Errorf("failed to close last frame when handling %v: %w", dir, err)
	}
	if o.pointsFile != nil {
		o.pointsFile.Close()
	}

	tmp := dir + "-tmp"
	if err = os.Rename(dir, tmp); err != nil {
		return "", fmt.Errorf("failed to backup %s to %s: %w", dir, tmp, err)
	}
	if err = os.Rename(newdir, dir); err != nil {
		os.Rename(tmp, dir)
		return "", fmt.Errorf("failed to rename %s to %s: %w", newdir, dir, err)
	}
	if err = os.RemoveAll(tmp); err != nil {
		// fmt.Errorf("failed to delete backup dir %s: %w", tmp, err)
	}
	return biz.DataTypeFusion3D, nil
}

type fileParser func(ctx context.Context, subdir, name string) (match bool, err error)

type qcraftParser struct {
	params       *Params
	elemParam    *biz.ElemParamV1
	pointsFile   *os.File
	pointsReader *bufio.Reader
	frmIdx       int
	fileIdx      int
	curSubdir    string
	oldBasedir   string
	newBasedir   string
	parsers      []fileParser
	log          *log.Helper
}

func newQcraftParser(olddir, newdir string) *qcraftParser {
	o := &qcraftParser{
		oldBasedir: olddir,
		newBasedir: newdir,
		// log: log.NewHelper(opts.Logger),
	}

	o.initParsers()
	return o
}

func (o *qcraftParser) initParsers() {
	o.parsers = []fileParser{
		o.ignoreDotFiles,
		o.ignoreParamsAndPointsFiles,
		o.parseImageFile,
		// o.defaultParser,
	}
}

func (o *qcraftParser) parseFile(ctx context.Context, subdir, name string) (err error) {
	for _, p := range o.parsers {
		match, err := p(ctx, subdir, name)
		if match {
			return err
		}
	}
	return fmt.Errorf("unparsed file: %v", filepath.Join(subdir, name))
}

func (o *qcraftParser) ignoreDotFiles(ctx context.Context, subdir, name string) (match bool, err error) {
	if !strings.HasPrefix(filepath.Base(name), ".") {
		return false, nil
	}
	// o.log.WithContext(ctx).Warnf("ignore a dot file %v", name)
	return true, nil
}

func (o *qcraftParser) ignoreParamsAndPointsFiles(ctx context.Context, subdir, name string) (match bool, err error) {
	switch name {
	case PointcloudFile, PosesFile, LidarExtrinsicParamsFile, CamParamsFile, ImagePosesFile, VehicleParamsFile:
		// o.log.WithContext(ctx).Warnf("skip file %v", name)
		return true, nil
	}
	return false, nil
}

func (o *qcraftParser) defaultParser(ctx context.Context, subdir, name string) (match bool, err error) {
	// o.log.WithContext(ctx).Warnf("ignore an unknown file %v", name)
	return true, nil
}

func (o *qcraftParser) loadParams() error {
	o.params = &Params{
		Transformers: map[string]*Matrix{
			transformerImgInter: {
				Rows:   4,
				Values: []float64{0, -1, 0, 0, 0, 0, -1, 0, 1, 0, 0, 0, 0, 0, 0, 1},
			}},
	}
	if err := o.parseParams(PosesFile, ParsePoses); err != nil {
		return err
	}
	if err := o.parseParams(LidarExtrinsicParamsFile, ParseLidarExtrinsic); err != nil {
		return err
	}
	if err := o.parseParams(CamParamsFile, ParseCameraParams); err != nil {
		return err
	}
	return nil
}

func (o *qcraftParser) addCamParams(cam string) {
	if o.fileIdx == 0 {
		o.elemParam = &biz.ElemParamV1{
			CoordSystems: map[string]*annofeed.CoordSys{
				common.KeyVehicle: {
					Name: common.KeyVehicle,
					// Type:   annofeed.CoordSys_Type_xx,
					Parent: "",
					Origin: o.params.Poses[o.frmIdx],
				},
			},
			Lidars: map[string]*annofeed.LidarParam{
				common.KeyLidar: {
					Name: common.KeyLidar,
					Pose: &annofeed.Pose{
						CoordSys: "",
						Pose:     o.params.Poses[o.frmIdx],
					},
				},
			},
			Cameras: map[string]*annofeed.CameraParam{},
		}
	}

	trans := o.params.GetImageTransforms(cam)
	ldrName := o.params.CameraNameToLidarName[cam]
	if _, ok := o.elemParam.CoordSystems[ldrName]; !ok {
		o.elemParam.CoordSystems[ldrName] = &annofeed.CoordSys{
			Name: ldrName,
			// Type:   annofeed.CoordSys_Type_xx,
			Parent: common.KeyVehicle,
			Origin: o.params.LidarExtrinsic[ldrName],
		}
	}
	o.elemParam.Cameras[cam] = &annofeed.CameraParam{
		Name: cam,
		Pose: &annofeed.Pose{
			CoordSys: ldrName,
			Pose:     o.params.CameraExtrinsic[cam],
		},
		Transforms: trans,
	}
}

func (o *qcraftParser) parseParams(name string, p func(o *Params, data []byte) error) error {
	fpath := filepath.Join(o.oldBasedir, o.curSubdir, name)
	data, err := os.ReadFile(fpath)
	if err != nil {
		return fmt.Errorf("failed to read file %v: %w", fpath, err)
	}
	return p(o.params, data)
}

func (o *qcraftParser) openPointsFile() error {
	if o.pointsFile != nil {
		o.pointsFile.Close()
	}

	fpath := filepath.Join(o.oldBasedir, o.curSubdir, PointcloudFile)
	f, err := os.Open(fpath)
	if err != nil {
		return fmt.Errorf("failed to open points file %s: %w", fpath, err)
	}
	o.pointsFile = f
	o.pointsReader = bufio.NewReader(o.pointsFile)
	return nil
}

func (o *qcraftParser) getPointcloudData() ([]byte, error) {
	if o.frmIdx == 0 {
		err := o.openPointsFile()
		if err != nil {
			return nil, err
		}
	}

	bufReader := o.pointsReader

	// eat leading character
	b, err := bufReader.ReadByte()
	if err != nil {
		return nil, fmt.Errorf("failed to read pointcloud file: %w", err)
	}
	leadingByte := ','
	if o.frmIdx == 0 {
		leadingByte = '['
	}
	if b != byte(leadingByte) {
		return nil, fmt.Errorf("expected %c, but got %c", leadingByte, b)
	}

	// read a frame
	frame := bytes.NewBuffer(nil)
	for n := 0; ; {
		b, err := bufReader.ReadByte()
		if err != nil {
			return nil, fmt.Errorf("failed to read pointcloud file: %w", err)
		}
		frame.WriteByte(b)
		if b == '[' {
			n++
		} else if b == ']' {
			n--
			if n == 0 {
				break
			}
		}
	}

	return frame.Bytes(), nil
}

func (o *qcraftParser) newFilePath(name string) string {
	return filepath.Join(o.newBasedir, o.curSubdir, strconv.Itoa(o.frmIdx), name)
}

func (o *qcraftParser) parseImageFile(ctx context.Context, subdir, name string) (match bool, err error) {
	if !common.IsSupportedImageFormat(name) {
		return false, nil
	}

	// parse frame index and camera index
	idx, camId := 0, 0
	_, err = fmt.Sscanf(filepath.Base(name), "%d_%d", &idx, &camId)
	if err != nil {
		return true, fmt.Errorf("failed to extract frame index and camera index from file name %v: %w", name, err)
	}

	if idx != o.frmIdx || subdir != o.curSubdir {
		err = o.startFrame(ctx, subdir, idx)
		if err != nil {
			return true, err
		}
	}
	camName := camIdxToName[camId]
	o.addCamParams(camName)

	// duplicate the image file instead of moving it, otherwise we will probably miss some camera params
	// when retrying the process if it is interrupted due to a temporary error
	old := filepath.Join(o.oldBasedir, subdir, name)
	newf := o.newFilePath(camName + filepath.Ext(name))
	err = os.Link(old, newf)
	if err != nil {
		return true, fmt.Errorf("failed to link file (%v -> %v): %w", newf, old, err)
	}

	// record its original name
	err = os.WriteFile(newf+common.OrigExt, []byte(filepath.Join(subdir, name)), 0644)
	if err != nil {
		return true, fmt.Errorf("failed to write file %v: %w", newf+common.OrigExt, err)
	}

	o.fileIdx++
	return true, nil
}

func (o *qcraftParser) closeFrame(ctx context.Context) error {
	if o.elemParam != nil {
		data, err := json.Marshal(o.elemParam)
		if err != nil {
			return fmt.Errorf("failed to marshal elemParam: %w", err)
		}
		fpath := o.newFilePath(common.ParamsFile)
		err = os.WriteFile(fpath, data, 0644)
		if err != nil {
			return fmt.Errorf("failed to write file %v: %w", fpath, err)
		}
	}
	return nil
}

func (o *qcraftParser) startFrame(ctx context.Context, subdir string, frmIdx int) error {
	err := o.closeFrame(ctx)
	if err != nil {
		return fmt.Errorf("failed to close frame: %w", err)
	}

	o.fileIdx = 0
	o.frmIdx = frmIdx
	o.curSubdir = subdir

	// make frame directory
	fpath := o.newFilePath("")
	err = os.MkdirAll(fpath, 0755)
	if err != nil {
		return fmt.Errorf("failed to create directory %v: %w", fpath, err)
	}

	if o.frmIdx == 0 {
		err = o.loadParams()
		if err != nil {
			return fmt.Errorf("failed to load params: %w", err)
		}
	}

	err = o.createPointcloud()
	if err != nil {
		return fmt.Errorf("failed to create pointcloud file in %v: %w", fpath, err)
	}
	return nil
}

func (o *qcraftParser) createPointcloud() error {
	// get point cloud data
	data, err := o.getPointcloudData()
	if err != nil {
		return err
	}
	// convert json format to pcd format and save
	fpath := o.newFilePath(common.LidarPCD)
	err = saveToPCD(fpath, data, o.params.Poses[o.frmIdx])
	if err != nil {
		return fmt.Errorf("failed to save PCD file %v: %w", fpath, err)
	}
	// record its original name
	err = os.WriteFile(fpath+common.OrigExt, []byte(filepath.Join(o.curSubdir, PointcloudFile)), 0644)
	if err != nil {
		return fmt.Errorf("failed to write file %v: %w", fpath+common.OrigExt, err)
	}
	return nil
}

func saveToPCD(fpath string, data []byte, viewpoint []float64) error {
	var points [][]any
	if err := json.Unmarshal(data, &points); err != nil {
		return fmt.Errorf("failed to parse points: %w", err)
	}
	pp, err := jsonToPCDBinary(points, viewpoint)
	if err != nil {
		return fmt.Errorf("failed to convert to PCD: %w", err)
	}
	f, err := os.Create(fpath)
	if err != nil {
		return fmt.Errorf("failed to create file %v: %w", fpath, err)
	}
	defer f.Close()
	return pc.Marshal(pp, f)
}

func jsonToPCDBinary(points [][]any, viewpoint []float64) (*pc.PointCloud, error) {
	// convert to (x, y, z, qw, qx, qy, qz) to be used in PCD
	vp32 := lo.Map(viewpoint, func(v float64, _ int) float32 { return float32(v) })
	qw := vp32[3]
	copy(vp32[3:], vp32[4:])
	vp32[6] = qw

	pp := &pc.PointCloud{
		PointCloudHeader: pc.PointCloudHeader{
			Version: 0.7,
			Fields:  []string{"x", "y", "z", "intensity"},
			Size:    []int{4, 4, 4, 1},
			Type:    []string{"F", "F", "F", "U"},
			Count:   []int{1, 1, 1, 1},
			Width:   len(points),
			Height:  1,
			// Viewpoint: []float32{0, 0, 0, 1, 0, 0, 0},
			Viewpoint: vp32,
		},
		Points: len(points),
	}

	// convert data
	pp.Data = make([]byte, pp.Points*pp.Stride()+3)
	dataOffset := 0
	for _, e := range points {
		xyzi, err := parseXYZI(e)
		if err != nil {
			return nil, fmt.Errorf("failed to parse XYZI %v: %w", e, err)
		}
		for i, f := range pp.Type {
			// for j := 0; j < pp.Count[i]; j++ {
			v := xyzi[i]
			switch f {
			case "F":
				b := math.Float32bits(float32(v))
				binary.LittleEndian.PutUint32(pp.Data[dataOffset:dataOffset+4], b)
			case "U":
				binary.LittleEndian.PutUint32(pp.Data[dataOffset:dataOffset+4], uint32(v))
			}
			dataOffset += pp.Size[i]
			// }
		}
	}
	pp.Data = pp.Data[:dataOffset]
	return pp, nil
}

func parseXYZI(v []any) (xyzi [4]float64, err error) {
	if len(v) != 2 {
		return xyzi, fmt.Errorf("point format is not [[x,y,z],i]: %v", v)
	}
	xyz, ok := v[0].([]any)
	if !ok {
		return xyzi, fmt.Errorf("xyz is not an array: %v", xyz)
	}
	if len(xyz) != 3 {
		return xyzi, fmt.Errorf("xyz length is not 3: %v", xyz)
	}
	for i, e := range xyz {
		if f, ok := e.(float64); !ok {
			return xyzi, fmt.Errorf("type of element %v is not a float64", e)
		} else {
			xyzi[i] = f
		}
	}

	f, ok := v[1].(float64)
	if !ok {
		return xyzi, fmt.Errorf("intensity is not a number: %v", v[1])
	}
	xyzi[3] = f
	return
}
