{"name": "docs-code-samples", "lockfileVersion": 2, "requires": true, "packages": {"": {"dependencies": {"@grpc/grpc-js": "^1.2.6", "@ory/keto-grpc-client": "file:../../proto", "google-protobuf": "^3.21.2"}}, "../../proto": {"name": "@ory/keto-grpc-client", "version": "0.6.0-alpha.1", "dependencies": {"@grpc/grpc-js": "^1.7.3", "google-protobuf": "^3.21.2"}}, "../../proto/ory/keto/relationTuples/v1alpha2": {"name": "@ory/keto-grpc-client", "version": "0.6.0-alpha.1-pre", "extraneous": true, "dependencies": {"@grpc/grpc-js": "^1.2.6", "google-protobuf": "^3.15.0-rc.1"}}, "node_modules/@grpc/grpc-js": {"version": "1.2.6", "license": "Apache-2.0", "dependencies": {"@types/node": ">=12.12.47", "google-auth-library": "^6.1.1", "semver": "^6.2.0"}, "engines": {"node": "^8.13.0 || >=10.10.0"}}, "node_modules/@grpc/grpc-js/node_modules/semver": {"version": "6.3.0", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@ory/keto-grpc-client": {"resolved": "../../proto", "link": true}, "node_modules/@types/node": {"version": "14.14.25", "license": "MIT"}, "node_modules/abort-controller": {"version": "3.0.0", "license": "MIT", "dependencies": {"event-target-shim": "^5.0.0"}, "engines": {"node": ">=6.5"}}, "node_modules/agent-base": {"version": "6.0.2", "license": "MIT", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/arrify": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/base64-js": {"version": "1.5.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/bignumber.js": {"version": "9.0.1", "license": "MIT", "engines": {"node": "*"}}, "node_modules/buffer-equal-constant-time": {"version": "1.0.1", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/debug": {"version": "4.3.1", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/ecdsa-sig-formatter": {"version": "1.0.11", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/event-target-shim": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/extend": {"version": "3.0.2", "license": "MIT"}, "node_modules/fast-text-encoding": {"version": "1.0.3", "license": "Apache-2.0"}, "node_modules/gaxios": {"version": "4.1.0", "license": "Apache-2.0", "dependencies": {"abort-controller": "^3.0.0", "extend": "^3.0.2", "https-proxy-agent": "^5.0.0", "is-stream": "^2.0.0", "node-fetch": "^2.3.0"}, "engines": {"node": ">=10"}}, "node_modules/gcp-metadata": {"version": "4.2.1", "license": "Apache-2.0", "dependencies": {"gaxios": "^4.0.0", "json-bigint": "^1.0.0"}, "engines": {"node": ">=10"}}, "node_modules/google-auth-library": {"version": "6.1.6", "license": "Apache-2.0", "dependencies": {"arrify": "^2.0.0", "base64-js": "^1.3.0", "ecdsa-sig-formatter": "^1.0.11", "fast-text-encoding": "^1.0.0", "gaxios": "^4.0.0", "gcp-metadata": "^4.2.0", "gtoken": "^5.0.4", "jws": "^4.0.0", "lru-cache": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/google-p12-pem": {"version": "3.1.4", "resolved": "https://registry.npmjs.org/google-p12-pem/-/google-p12-pem-3.1.4.tgz", "integrity": "sha512-HHuHmkLgwjdmVRngf5+gSmpkyaRI6QmOg77J8tkNBHhNEI62sGHyw4/+UkgyZEI7h84NbWprXDJ+sa3xOYFvTg==", "dependencies": {"node-forge": "^1.3.1"}, "bin": {"gp12-pem": "build/src/bin/gp12-pem.js"}, "engines": {"node": ">=10"}}, "node_modules/google-protobuf": {"version": "3.21.2", "resolved": "https://registry.npmjs.org/google-protobuf/-/google-protobuf-3.21.2.tgz", "integrity": "sha512-3MSOYFO5U9mPGikIYCzK0SaThypfGgS6bHqrUGXG3DPHCrb+txNqeEcns1W0lkGfk0rCyNXm7xB9rMxnCiZOoA=="}, "node_modules/gtoken": {"version": "5.2.1", "license": "MIT", "dependencies": {"gaxios": "^4.0.0", "google-p12-pem": "^3.0.3", "jws": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/https-proxy-agent": {"version": "5.0.0", "license": "MIT", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/is-stream": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/json-bigint": {"version": "1.0.0", "license": "MIT", "dependencies": {"bignumber.js": "^9.0.0"}}, "node_modules/jwa": {"version": "2.0.0", "license": "MIT", "dependencies": {"buffer-equal-constant-time": "1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}}, "node_modules/jws": {"version": "4.0.0", "license": "MIT", "dependencies": {"jwa": "^2.0.0", "safe-buffer": "^5.0.1"}}, "node_modules/lru-cache": {"version": "6.0.0", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/ms": {"version": "2.1.2", "license": "MIT"}, "node_modules/node-fetch": {"version": "2.6.7", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.7.tgz", "integrity": "sha512-ZjMPFEfVx5j+y2yF35Kzx5sF7kDzxuDj6ziH4FFbOp87zKDZNx8yExJIb05OGF4Nlt9IHFIMBkRl41VdvcNdbQ==", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-forge": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/node-forge/-/node-forge-1.3.1.tgz", "integrity": "sha512-dPEtOeMvF9VMcYV/1Wb8CPoVAXtp6MKMlcbAt4ddqmGqUJ6fQZFXkNZNkNlfevtNkGtaSoXf/vNNNSvgrdXwtA==", "engines": {"node": ">= 6.13.0"}}, "node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/tr46": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "integrity": "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o="}, "node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE="}, "node_modules/whatwg-url": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha1-lmRU6HZUYuN2RNNib2dCzotwll0=", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/yallist": {"version": "4.0.0", "license": "ISC"}}, "dependencies": {"@grpc/grpc-js": {"version": "1.2.6", "requires": {"@types/node": ">=12.12.47", "google-auth-library": "^6.1.1", "semver": "^6.2.0"}, "dependencies": {"semver": {"version": "6.3.0"}}}, "@ory/keto-grpc-client": {"version": "file:../../proto", "requires": {"@grpc/grpc-js": "^1.7.3", "google-protobuf": "^3.21.2"}}, "@types/node": {"version": "14.14.25"}, "abort-controller": {"version": "3.0.0", "requires": {"event-target-shim": "^5.0.0"}}, "agent-base": {"version": "6.0.2", "requires": {"debug": "4"}}, "arrify": {"version": "2.0.1"}, "base64-js": {"version": "1.5.1"}, "bignumber.js": {"version": "9.0.1"}, "buffer-equal-constant-time": {"version": "1.0.1"}, "debug": {"version": "4.3.1", "requires": {"ms": "2.1.2"}}, "ecdsa-sig-formatter": {"version": "1.0.11", "requires": {"safe-buffer": "^5.0.1"}}, "event-target-shim": {"version": "5.0.1"}, "extend": {"version": "3.0.2"}, "fast-text-encoding": {"version": "1.0.3"}, "gaxios": {"version": "4.1.0", "requires": {"abort-controller": "^3.0.0", "extend": "^3.0.2", "https-proxy-agent": "^5.0.0", "is-stream": "^2.0.0", "node-fetch": "^2.3.0"}}, "gcp-metadata": {"version": "4.2.1", "requires": {"gaxios": "^4.0.0", "json-bigint": "^1.0.0"}}, "google-auth-library": {"version": "6.1.6", "requires": {"arrify": "^2.0.0", "base64-js": "^1.3.0", "ecdsa-sig-formatter": "^1.0.11", "fast-text-encoding": "^1.0.0", "gaxios": "^4.0.0", "gcp-metadata": "^4.2.0", "gtoken": "^5.0.4", "jws": "^4.0.0", "lru-cache": "^6.0.0"}}, "google-p12-pem": {"version": "3.1.4", "resolved": "https://registry.npmjs.org/google-p12-pem/-/google-p12-pem-3.1.4.tgz", "integrity": "sha512-HHuHmkLgwjdmVRngf5+gSmpkyaRI6QmOg77J8tkNBHhNEI62sGHyw4/+UkgyZEI7h84NbWprXDJ+sa3xOYFvTg==", "requires": {"node-forge": "^1.3.1"}}, "google-protobuf": {"version": "3.21.2", "resolved": "https://registry.npmjs.org/google-protobuf/-/google-protobuf-3.21.2.tgz", "integrity": "sha512-3MSOYFO5U9mPGikIYCzK0SaThypfGgS6bHqrUGXG3DPHCrb+txNqeEcns1W0lkGfk0rCyNXm7xB9rMxnCiZOoA=="}, "gtoken": {"version": "5.2.1", "requires": {"gaxios": "^4.0.0", "google-p12-pem": "^3.0.3", "jws": "^4.0.0"}}, "https-proxy-agent": {"version": "5.0.0", "requires": {"agent-base": "6", "debug": "4"}}, "is-stream": {"version": "2.0.0"}, "json-bigint": {"version": "1.0.0", "requires": {"bignumber.js": "^9.0.0"}}, "jwa": {"version": "2.0.0", "requires": {"buffer-equal-constant-time": "1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}}, "jws": {"version": "4.0.0", "requires": {"jwa": "^2.0.0", "safe-buffer": "^5.0.1"}}, "lru-cache": {"version": "6.0.0", "requires": {"yallist": "^4.0.0"}}, "ms": {"version": "2.1.2"}, "node-fetch": {"version": "2.6.7", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.7.tgz", "integrity": "sha512-ZjMPFEfVx5j+y2yF35Kzx5sF7kDzxuDj6ziH4FFbOp87zKDZNx8yExJIb05OGF4Nlt9IHFIMBkRl41VdvcNdbQ==", "requires": {"whatwg-url": "^5.0.0"}}, "node-forge": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/node-forge/-/node-forge-1.3.1.tgz", "integrity": "sha512-dPEtOeMvF9VMcYV/1Wb8CPoVAXtp6MKMlcbAt4ddqmGqUJ6fQZFXkNZNkNlfevtNkGtaSoXf/vNNNSvgrdXwtA=="}, "safe-buffer": {"version": "5.1.2"}, "tr46": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "integrity": "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o="}, "webidl-conversions": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE="}, "whatwg-url": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha1-lmRU6HZUYuN2RNNib2dCzotwll0=", "requires": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "yallist": {"version": "4.0.0"}}}