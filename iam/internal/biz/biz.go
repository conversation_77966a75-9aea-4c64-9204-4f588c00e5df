package biz

import (
	"context"
	"strings"

	"github.com/go-kratos/kratos/v2/encoding"
	"github.com/google/wire"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// ProviderSet is biz providers.
var ProviderSet = wire.NewSet(NewPermsBiz, NewRolesBiz, NewTeamsBiz, NewUsersBiz, NewBizgrantsBiz)

type JSON = datatypes.JSON
type DeletedAt = gorm.DeletedAt
type Tx = *gorm.DB
type FieldMask = field.Mask
type TxAction func(ctx context.Context, v any) error

func JSONCodec() encoding.Codec { return encoding.GetCodec("json") }

type StrMap = serial.Map[string, string]

type Pager = repo.Pager
type PageToken = repo.PageToken

type ValidValue[T any] struct {
	Value T
	Valid bool
}

func NewValidValue[T any](v T) ValidValue[T] {
	return ValidValue[T]{
		Value: v,
		Valid: true,
	}
}

func (o *ValidValue[T]) Set(v T) {
	o.Value = v
	o.Valid = true
}

type BackgroundTask interface {
}

const SysTagPrefix = "sys."

func IsSysTag(tag string) bool    { return strings.HasPrefix(tag, SysTagPrefix) }
func NewSysTag(tag string) string { return SysTagPrefix + tag }
