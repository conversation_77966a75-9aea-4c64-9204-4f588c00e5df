package biz

import (
	"time"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
)

type Bizgrant struct {
	ID         int64     `json:"id" gorm:"default:null"`
	GrantorUid string    `json:"grantor_id" gorm:"default:null"`
	GranteeUid string    `json:"grantee_id" gorm:"default:null"`
	OrgUid     string    `json:"org_id" gorm:"default:null"`
	CreatedAt  time.Time `json:"created_at" gorm:"default:null"`

	Biz string `json:"biz,omitempty" gorm:"-"`
}

type BizgrantSlfd string

func (o BizgrantSlfd) String() string { return string(o) }

func (o BizgrantSlfd) WithTable() string { return "bizgrants." + string(o) }

const BizgrantTableName = "bizgrants"

var (
	bizgrant_             = field.RegObject(&Bizgrant{})
	BizgrantUpdatableFlds = field.NewModel(BizgrantTableName, bizgrant_)

	BizgrantSfldGrantorUid = BizgrantSlfd(field.Sname(&bizgrant_.GrantorUid))
	BizgrantSfldGranteeUid = BizgrantSlfd(field.Sname(&bizgrant_.GranteeUid))
	BizgrantSfldOrgUid     = BizgrantSlfd(field.Sname(&bizgrant_.OrgUid))
)

type BizgrantFilter struct {
	GrantorUid string
	GranteeUid string
	OrgUid     string
}

func (o *BizgrantFilter) Apply(tx Tx) Tx {
	tx = ApplyFieldFilter(tx, BizgrantSfldGrantorUid.WithTable(), o.GrantorUid)
	tx = ApplyFieldFilter(tx, BizgrantSfldGranteeUid.WithTable(), o.GranteeUid)
	tx = ApplyFieldFilter(tx, BizgrantSfldOrgUid.WithTable(), o.OrgUid)
	return tx
}

type BizgrantsRepo repo.GenericRepo[Bizgrant]
