package middleware

import (
	"context"
	"encoding/base64"
	"fmt"
	"time"

	"iam/internal/biz"

	"github.com/go-kratos/kratos/v2/encoding"
	"github.com/go-kratos/kratos/v2/encoding/json"
	"github.com/go-kratos/kratos/v2/metadata"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"gitlab.rp.konvery.work/platform/apis/client"
	"gitlab.rp.konvery.work/platform/apis/iam/v1"
	"gitlab.rp.konvery.work/platform/apis/middleware/authfilter"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"
)

var enc = encoding.GetCodec("json")

func init() {
	json.MarshalOptions.UseProtoNames = true
}

// LoadUser is a middleware to load user.
func LoadUser(bz *biz.UsersBiz, noauth []authfilter.NoauthFilter) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			if md, ok := metadata.FromServerContext(ctx); ok {
				var user, assumeBy *biz.User
				if userStr := md.Get(client.HeaderUser); userStr != "" {
					bs, err := base64.StdEncoding.DecodeString(userStr)
					if err != nil {
						return nil, fmt.Errorf("failed to decode user info: %w", err)
					}
					uc, err := parseUserCtx(bs)
					if err != nil {
						return nil, fmt.Errorf("failed to unmarshal user info: %w", err)
					}
					user, assumeBy = ToBizUser(uc.User), ToBizUser(uc.AssumeBy)
				} else if uid := md.Get(client.HeaderUserUid); uid != "" {
					user, err = bz.Get(ctx, uid)
					if err != nil {
						return nil, err
					}
				}
				if user != nil {
					uc := biz.UserFromCtx(ctx)
					if uc == nil {
						ctx = biz.NewCtxWithUser(ctx, user, assumeBy)
					} else {
						// fill into the slot reserved by a previous middleware, e.g. Log
						uc.User, uc.AssumeBy = user, assumeBy
					}
				}
			}

			if tr, ok := transport.FromServerContext(ctx); ok {
				if biz.UserFromCtx(ctx).IsEmpty() && !authfilter.SkipAuth(noauth, tr.Operation()) {
					return nil, errors.NewErrUnauthorized()
				}
			}
			return handler(ctx, req)
		}
	}
}

func parseUserCtx(bs []byte) (*iam.UserContext, error) {
	uc := &iam.UserContext{}
	if err := enc.Unmarshal(bs, &uc); err != nil {
		return nil, err
	}
	if !uc.IsEmpty() {
		return uc, nil
	}

	// compatible with old user context
	u := &iam.User{}
	if err := enc.Unmarshal(bs, &u); err != nil {
		return nil, err
	}
	if u.Uid == "" {
		return uc, nil
	}
	return &iam.UserContext{User: u}, nil
}

func ToBizUser(d *iam.User) *biz.User {
	if d == nil {
		return nil
	}
	birth := time.Time{}
	if d.Birthday != "" {
		birth, _ = time.Parse(time.RFC3339, d.Birthday)
	}
	o := &biz.User{
		ID:       kid.ParseID(d.Uid),
		Name:     d.Name,
		Phone:    d.Phone,
		Email:    d.Email,
		Avatar:   d.Avatar,
		Role:     d.Role,
		Province: d.Province,
		City:     d.City,
		Birthday: birth,
		Gender:   int(d.Gender),
		OrgID:    kid.ParseID(d.OrgUid),
	}
	return o
}
