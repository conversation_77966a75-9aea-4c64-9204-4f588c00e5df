package middleware

import (
	"context"
	"encoding/base64"
	"reflect"

	"github.com/go-kratos/kratos/v2/middleware"
	"gitlab.rp.konvery.work/platform/pkg/errors"
)

// CheckPager is a middleware to check page/pagesz, decode page_token and encode next_page_token.
func CheckPager() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			v := reflect.ValueOf(req)
			if v.Kind() != reflect.Pointer {
				return handler(ctx, req)
			}
			v = v.Elem()
			if v.Kind() != reflect.Struct {
				return handler(ctx, req)
			}
			page := v.<PERSON>y<PERSON>ame("Page")
			pagesz := v.FieldByName("Pagesz")
			pageToken := v.Field<PERSON>y<PERSON>ame("PageToken")

			// check page size
			if !pagesz.IsValid() {
				return handler(ctx, req)
			}
			if vv := pagesz.Int(); vv < 0 || vv > 500 {
				return nil, errors.NewErrInvalidField(errors.WithFields("pagesz"),
					errors.WithMessage("pagesz should be within [0, 500]"))
			} else if vv == 0 {
				pagesz.SetInt(50)
			}

			// check page number
			if page.IsValid() {
				if vv := page.Int(); vv < 0 {
					return nil, errors.NewErrInvalidField(errors.WithFields("page"),
						errors.WithMessage("negative page number"))
				}
			}

			// check page token
			if pageToken.IsValid() && pageToken.Kind() == reflect.String {
				if !pageToken.IsZero() {
					token, err := base64.RawURLEncoding.DecodeString(pageToken.String())
					if err != nil {
						return nil, errors.NewErrInvalidField(errors.WithFields("page_token"))
					}
					pageToken.SetString(string(token))
				}
				rsp, err := handler(ctx, req)
				if err == nil {
					v := reflect.ValueOf(rsp).Elem()
					nextPageToken := v.FieldByName("NextPageToken")
					if nextPageToken.IsValid() && !nextPageToken.IsZero() && nextPageToken.Kind() == reflect.String {
						nextPageToken.SetString(base64.RawURLEncoding.EncodeToString([]byte(nextPageToken.String())))
					}
				}
				return rsp, err
			}

			return handler(ctx, req)
		}
	}
}
