import json
import os
import numpy
import sys

sys.path.append(".")
from customer.common import tools

cur_sa_index = 20230705
lidar_seg_sample_token_count = 0
lidar_seg_bin_count = 0

preferred_lidar_label = ""
current_file_name = ""
origin_data_dir = ""
lidar_seg_dir = ""
current_sample_data_json_file = ""
output_dir = ""
test_in_config_path = ""

# NuScenes data
vis_data = []
lidar_seg = []
sample_data = []
err_json_file_list = []
anno_list = []
attrs = []

pcd_map = {}

category_index_mapping = {"": 35}
category_token_mapping = {}
instance_dict = {}
instance_category_mapping = {}
attribute_token_mapping = {}
lidar_seg_token_file_count_dict = {}
track_id_object_dict = {}

no_sample_annotation_json_file_count = 0
json_file_count = 0

category_mapping = {"car": "vehicle.car",
                    "truck": "vehicle,truck",
                    "trailer": "vehicle.trailer",
                    "bicycle": "vehicle.bicycle",
                    "motorcycle": "vehicle.motorcycle",
                    "wheelchair": "human.pedestrian.wheelchair",
                    "adult": "human.pedestrian.adult",
                    "child": "human.pedestrian.child",
                    "worker": "human.pedestrian.construction_worker",
                    "police_officer": "human.pedestrian.police_officer",
                    "barrier": "movable_object.barrier",
                    "trafficcone": "movable_object.trafficcone",
                    "bus": "",
                    "attachment": "",
                    "other_vehicle_small": "",
                    "other_vehicle_large": "",
                    "tricycle": ""}


def get_pcd_map(pcd_path):
    with open(pcd_path, "r") as f:
        return json.load(f)


def get_category_token_mapping(category_path):
    global category_token_mapping
    data = tools.get_json_data(category_path)
    for item in data:
        category_token_mapping[item["name"]] = item["token"]


def get_attribute_token_mapping(attribute_path):
    global attribute_token_mapping

    # "vehicle.moving" --> moving
    data = tools.get_json_data(attribute_path)
    for item in data:
        attribute_token_mapping[item["name"].split(".")[1]] = item["token"]


def get_lidar_seg_dir():
    global lidar_seg_dir
    lidar_seg_dir = os.path.join(origin_data_dir, "lidar_seg")
    if not os.path.exists(lidar_seg_dir):
        os.makedirs(lidar_seg_dir)
    return lidar_seg_dir


def get_sample_token(bin_name):
    sample_data_item = get_sample_data(bin_name)
    return sample_data_item["sample_token"]


def get_sample_data_token(bin_name):
    sample_data_item = get_sample_data(bin_name)
    # print(bin_name)
    return sample_data_item["token"], sample_data_item["is_key_frame"]


def get_sample_data(bin_name):
    # get sample data token from sample_data.json
    for item in sample_data:
        if -1 != item["filename"].find(bin_name):
            return item

    print(f"{bin_name} has no token in sample_data.json {current_sample_data_json_file}")
    # print(sample_data)


def write_lidar_seg(bin_name, seg_data):
    # write seg data to lidar_seg_data (lidarseg.json) and bin file
    res_list = []
    bin_path = output_dir + os.sep + "lidarseg" + os.sep + "v1.0" + os.sep + bin_name.split("/")[-1]

    sample_data_token, is_key = get_sample_data_token(bin_name)

    if is_key == "false":
        print(f"NOT a key frame! bin_name: {bin_name}", bin_name)
        return

    for item in seg_data:
        if item in category_index_mapping.keys():
            res_list.append(category_index_mapping[item])
        else:
            print(f"!!!Error, {item} is not in label_value_mapping")

    if len(res_list) == 0:
        print(f"!!!Error, {current_file_name} has no seg data")
        return

    os.makedirs(os.path.dirname(bin_path), exist_ok=True)

    global lidar_seg_bin_count
    lidar_seg_bin_count += 1

    with open(bin_path, "wb") as f:
        numpy.array(res_list).astype(numpy.uint8).tofile(f)

    bin_name = os.path.basename(bin_path)

    seg = {
        "token": sample_data_token,
        "sample_data_token": sample_data_token,
        "filename": "lidarseg" + os.sep + "v1.0" + os.sep + bin_name,
    }

    global lidar_seg
    lidar_seg.append(seg)


def get_attributes_token(label, attr):
    for item in attribute_token_mapping.keys():
        if label in item and attr in item:
            # print(item, attribute_token_mapping[item])
            return attribute_token_mapping[item]


def write_sample_annotation(box_data, bin_name):
    global cur_sa_index
    global instance_category_mapping
    global track_id_object_dict

    for box in box_data:

        position = box["annotation"]["data"]["position"]
        dimension = box["annotation"]["data"]["dimension"]
        rotation = box["annotation"]["data"]["rotation"]
        anno_token = tools.get_md5(str(cur_sa_index))

        instance_token = tools.get_md5(str(box["track_id"]))
        if instance_token not in instance_category_mapping.keys():
            instance_category_mapping[instance_token] = category_token_mapping[box["class"]]

        # print(box)
        #
        # print(box["class"], category_token_mapping[box["class"]])
        box_anno = {
            "token": anno_token,
            "sample_token": get_sample_token(bin_name),
            "instance_token": instance_token,
            # "track_id": str(box["track_id"]),
            # "visibility_token": visibility_token_mapping[box["attrs"]["occluded"][0]],
            # "attribute_tokens": get_attributes_token(box["class"][:5], box["attrs"]["Status"][0]) if "Status" in box[
            #     "attrs"].keys() else [],
            "attribute_tokens": attribute_token_mapping[box["attrs"]["Status"][0].lower()] if "Status" in box[
                "attrs"].keys() else [],

            "translation": [position["x"], position["y"], position["z"]],
            "size": [dimension["w"], dimension["l"], dimension["h"]],
            "rotation": tools.convert_xyz_to_quaternion(rotation["x"], rotation["y"], rotation["z"]),
            "num_lidar_pts": box["point_num"],
            "num_radar_pts": 0,
            "next": "",
            "prev": "",
        }

        cur_sa_index += 1
        track_id = str(box["track_id"])

        if track_id in track_id_object_dict.keys():
            track_id_object_dict[track_id]["anno"].append(box_anno)
        else:
            track_id_object_dict[track_id] = {"anno": [box_anno], "instance_token": instance_token}


def convert(data, filename):
    has_lidar_key = True
    # print(f"test_in: {filename}")
    has_segment_data = False

    if "segmentation" in data.keys():
        print(f"segmentation found in {current_file_name}")
        has_segment_data = True
        return

    if "lidar" not in data.keys():
        print(f"!!!Error, no sample annotation in {current_file_name}")
        global no_sample_annotation_json_file_count
        no_sample_annotation_json_file_count += 1
        has_lidar_key = False

    start_index = 0
    pcd_map_key = os.path.splitext(filename)[0]

    for bin_data in pcd_map[pcd_map_key]["bin"]:
        for f_key in bin_data.keys():
            bin_name, bin_len = f_key, bin_data[f_key]

            if has_segment_data:
                write_lidar_seg(
                    bin_name,
                    data["segmentation"]["result"][0][start_index: start_index + bin_len],
                )
                start_index += bin_len

            if has_lidar_key and preferred_lidar_label in bin_name:
                write_sample_annotation(data["lidar"], bin_name)


def process_test_in_data(test_in_input_dir, nu_scene_input_dir, output):
    global current_file_name
    global origin_data_dir
    global lidar_seg_dir
    global sample_data
    global pcd_map
    global instance_dict
    global category_index_mapping
    global output_dir
    global lidar_seg_bin_count
    global lidar_seg_token_file_count_dict
    global lidar_seg
    global current_sample_data_json_file
    global err_json_file_list
    global track_id_object_dict
    global anno_list
    global category_token_mapping
    global attribute_token_mapping
    global instance_category_mapping
    global json_file_count

    category_index_mapping = {"": 35}
    category_token_mapping = {}
    attribute_token_mapping = {}
    instance_dict = {}
    track_id_object_dict = {}
    instance_category_mapping = {}

    lidar_seg_bin_count = 0
    lidar_seg_token_file_count_dict = {}
    err_json_file_list = []
    anno_list = []
    lidar_seg = []
    json_file_list = []

    # file_count = 0
    lidar_seg_bin_count = 0

    # dirs
    output_dir = output
    test_in_input_dir = tools.unzip(test_in_input_dir)
    origin_data_dir = nu_scene_input_dir

    # For seg related project only
    get_category_token_mapping(nu_scene_input_dir + os.sep + "category.json")
    get_attribute_token_mapping(nu_scene_input_dir + os.sep + "attribute.json")

    current_sample_data_json_file = nu_scene_input_dir + os.sep + "sample_data.json"

    with open(current_sample_data_json_file) as f:
        sample_data = json.load(f)

    for f in tools.get_json_files(test_in_input_dir):
        if f.name.endswith(".json") and not f.name.startswith("."):
            json_file_count += 1
            json_file_list.append(f.path)

    json_file_list.sort()

    for f in json_file_list:
        print(f)
        with open(f) as json_file:
            try:
                name = os.path.basename(f)
                convert(json.load(json_file), name)
            except Exception as e:
                err_json_file_list.append(f)

    with open(nu_scene_input_dir + os.sep + "lidarseg.json", "w") as f:
        json.dump(lidar_seg, f)

    lidar_seg_token_file_count_dict[test_in_input_dir] = {"lidar_seg_json_files_count": lidar_seg_bin_count,
                                                          "sample_data_token_count": len(lidar_seg),
                                                          "error_found": True if lidar_seg_bin_count == len(
                                                              lidar_seg) else False}

    # print(lidar_seg_token_file_count_dict[test_in_input_dir])

    if not lidar_seg_token_file_count_dict[test_in_input_dir]["error_found"]:
        print(lidar_seg_token_file_count_dict[test_in_input_dir])

    anno_list = []
    instance_list = []

    if len(track_id_object_dict) > 0:
        for track_id in track_id_object_dict.keys():
            anno_count = len(track_id_object_dict[track_id]["anno"])
            if anno_count > 1:
                index = 1
                track_id_object_dict[track_id]["anno"][0]["next"] = track_id_object_dict[track_id]["anno"][1]["token"]
                while index < anno_count:
                    track_id_object_dict[track_id]["anno"][index]["prev"] = \
                        track_id_object_dict[track_id]["anno"][index - 1]["token"]
                    if index + 1 < anno_count:
                        track_id_object_dict[track_id]["anno"][index]["next"] = \
                            track_id_object_dict[track_id]["anno"][index + 1]["token"]
                    index = index + 1

            anno_list.extend(track_id_object_dict[track_id]["anno"])

            # instance
            instance_token = track_id_object_dict[track_id]["instance_token"]
            category_token = instance_category_mapping[instance_token]
            ins_value = {
                "token": instance_token,
                # "track_id": track_id,  # debug usage
                "nbr_annotation": len(track_id_object_dict[track_id]["anno"]),
                "category_token": category_token,
                "first_annotation_token": track_id_object_dict[track_id]["anno"][0]["token"],
                "last_annotation_token": track_id_object_dict[track_id]["anno"][-1]["token"],
            }
            instance_list.append(ins_value)

        with open(nu_scene_input_dir + os.sep + "sample_annotation.json", "w") as f:
            json.dump(anno_list, f)

        with open(nu_scene_input_dir + os.sep + "instance.json", "w") as f:
            json.dump(instance_list, f)

    tools.copy_dir(nu_scene_input_dir, output_dir)


def run(test_in_dir, nu_scene_dir):
    global pcd_map
    global output_dir
    code = 0
    err_msg = "成功"
    tools.check_dir(output_dir)
    version = "v1.0"

    pcd_map_path = nu_scene_dir + os.sep + "保留这个文件-total_pcd_map.json"
    pcd_map = get_pcd_map(pcd_map_path)

    test_in_dir = tools.unzip(test_in_dir)

    test_in_dir_list = tools.list_all_dirs(test_in_dir, "label")  # label是每个分包的标注结果
    nu_scene_dir_list = tools.list_all_dirs(nu_scene_dir, version)  # v1.0是每个分包的目录

    test_in_nu_scene_dir_map_list = []

    # find the match between test_in_dir and nu_scene_dir
    for test_in_subdir in test_in_dir_list:
        json_files = tools.get_json_files(test_in_subdir)
        for f in json_files:
            if f.name.endswith(".json") and not f.name.startswith("."):
                json_name = f.name.replace(".json", "")
                pcd_path = pcd_map[json_name]["path"]

                # 目前只支持最多两级目录  如：2023-09-09-18-22-27/2023-09-09-18-22-27__4
                # pcd_path: /Users/<USER>/Downloads/nlt_extracted_20240106/Per_20231117_155241 -- >
                # relative_path: nlt_extracted_20240106/Per_20231117_155241
                relative_path = pcd_path.split(os.sep)[-2:]

                # 判断目录是一级还是二级：默认目录的名字含有 "-" 且大于2个 如：2023-09-09-18-23-27
                # 默认值是一级目录
                res_output_dir = output_dir + os.sep + relative_path[1]

                search_name = relative_path[1]
                # "_"  下划线 时序的项目 目录名为 zc_07_0831_0901_new/Per_20230831_155239
                # "-"  减号   2D 分割的项目 目录名为 2023-09-09-18-22-27/2023-09-09-18-22-27__4
                if not (relative_path[0].count("-") <= 2 and relative_path[0].count("_") <= 2):
                    # 说明目录是二级
                    res_output_dir = output_dir + os.sep + relative_path[0] + os.sep + relative_path[1]

                # 在 nu_scene中找test_in子目录对应的目录
                for nu_scene_subdir in nu_scene_dir_list:
                    temp = nu_scene_subdir + os.sep
                    if -1 != temp.find(search_name + os.sep):  # 一定要加os.sep
                        nu_scene_subdir = nu_scene_subdir + os.sep + version
                        test_in_nu_scene_dir_map_list.append(
                            {"test_in_dir": test_in_subdir, "nu_scene_dir": nu_scene_subdir,
                             "output_dir": res_output_dir})
                        break

                break

    for elem in test_in_nu_scene_dir_map_list:
        print(elem["test_in_dir"], " " + elem["nu_scene_dir"])
        process_test_in_data(test_in_input_dir=elem["test_in_dir"], nu_scene_input_dir=elem["nu_scene_dir"],
                             output=elem["output_dir"])

    for data in lidar_seg_token_file_count_dict.keys():
        if not lidar_seg_token_file_count_dict[data]["error_found"]:
            print(data + " ", lidar_seg_token_file_count_dict[data])
            code = 1
            err_msg = "lidar bin 文件数量与 lidarseg.json中 sample_data_token 数量不一致"

    if len(err_json_file_list) > 0:
        code = 2
        err_msg = "云测json文件中有错误"

        print(f"\n{len(err_json_file_list)} ERROR FOUND:")
        for item in err_json_file_list:
            print(item)

    trans_result = {"code": code, "output": output_dir, "err_msg": err_msg, "error_count:": len(err_json_file_list),
                    "summary": {"云测结果题数": len(test_in_dir_list), "转换题数": len(test_in_nu_scene_dir_map_list)},
                    "json文件数量": json_file_count, "无标注(拉框)json文件数量": no_sample_annotation_json_file_count}

    print("\n" * 2)
    print(trans_result)


"""
    项目： 仙图
    平台： 云测新平台
    功能： 云测导出的数据转换成NuScene格式
            拉框结果写入 
                visibility.json
                instance.json
                sample_annotation.json
    输入： 云测json , pcd_mapping.json(记录叠帧相关点云数据)
    输出： https://yikongzhijia.feishu.cn/docx/TCaEd2Br4orzQAxRIysc2Zhlnkf
    注意事项！！！：
        1. “保留这个文件-total_pcd_map.json” 这个文件 要放在分包前原始文件的根目录下
        2. 时序和分割项目 目录名日期分融符不同
            时序项目 目录名分融符为 下划线 ：zc_07_0831_0901_new/Per_20230831_155239
            分割项目 目录名分融符为 减号   ：2023-09-09-18-22-27/2023-09-09-18-22-27__4
"""

if __name__ == "__main__":
    args = tools.get_args()

    if args.input == "../../input/test":
        date_dir = "03-20"
        base_dir = "demo"
        # keep unchanged
        args.pcd = "/Users/<USER>/Downloads/Convert/Kin/" + date_dir + os.sep + base_dir  # origin nu_scenes data root dir
        args.input = "/Users/<USER>/Downloads/Convert/Kin/test_in"
        args.out = "/Users/<USER>/Downloads/output-kin"

    if not args.txt:
        args.txt = "top"

    output_dir = args.out
    preferred_lidar_label = args.txt
    run(test_in_dir=args.input, nu_scene_dir=args.pcd)
