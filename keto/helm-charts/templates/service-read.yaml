{{- if .Values.service.read.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "keto.fullname" . }}-read
  {{- if .Release.Namespace }}
  namespace: {{ .Release.Namespace }}
  {{- end }}
  labels:
    app.kubernetes.io/component: read
    {{- include "keto.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.read.type }}
  ports:
    - port: {{ .Values.service.read.port }}
      targetPort: {{ .Values.service.read.name }}
      protocol: TCP
      name: {{ .Values.service.read.name }}
  selector:
    app.kubernetes.io/name: {{ include "keto.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
{{ end }}