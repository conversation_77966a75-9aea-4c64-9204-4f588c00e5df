package server

import (
	"anno/internal/conf"
	"anno/internal/service"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/apis/middleware"
	"gitlab.rp.konvery.work/platform/apis/middleware/authfilter"
	"gitlab.rp.konvery.work/platform/apis/middleware/common"
)

var unauthWhitelist = []string{
	"/anno.v1.Configs/ListErrors",
}
var noauthFilters = []authfilter.NoauthFilter{authfilter.WhitelistNoauthFilter(unauthWhitelist...)}

// NewGRPCServer new a gRPC server.
func NewGRPCServer(c *conf.Server,
	cfgs *service.ConfigsService,
	jobs *service.JobsService,
	lots *service.LotsService,
	lottpls *service.LottplsService,
	labelclz *service.LabelclzService,
	labelwidgets *service.LabelwidgetsService,
	orders *service.OrdersService,
	projects *service.ProjectsService,
	skills *service.SkillsService,
	grant *service.BizgrantsService,
	specgrant *service.SpecgrantsService,
	logger log.Logger) *grpc.Server {
	mws := common.Middlewares(logger,
		middleware.LoadUser(noauthFilters),
	)
	var opts = []grpc.ServerOption{
		grpc.Middleware(mws...),
	}
	if c.Grpc.Network != "" {
		opts = append(opts, grpc.Network(c.Grpc.Network))
	}
	if c.Grpc.Addr != "" {
		opts = append(opts, grpc.Address(c.Grpc.Addr))
	}
	if c.Grpc.Timeout != nil {
		opts = append(opts, grpc.Timeout(c.Grpc.Timeout.AsDuration()))
	}
	srv := grpc.NewServer(opts...)
	anno.RegisterConfigsServer(srv, cfgs)
	anno.RegisterJobsServer(srv, jobs)
	anno.RegisterLotsServer(srv, lots)
	anno.RegisterLottplsServer(srv, lottpls)
	anno.RegisterLabelclzServer(srv, labelclz)
	anno.RegisterLabelwidgetsServer(srv, labelwidgets)
	anno.RegisterOrdersServer(srv, orders)
	anno.RegisterProjectsServer(srv, projects)
	anno.RegisterSkillsServer(srv, skills)
	anno.RegisterBizgrantsServer(srv, grant)
	anno.RegisterSpecgrantsServer(srv, specgrant)
	return srv
}
