// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anno/v1/bizgrant.proto

package anno

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateBizgrantRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grantee uid
	GranteeUid string `protobuf:"bytes,1,opt,name=grantee_uid,json=granteeUid,proto3" json:"grantee_uid,omitempty"`
	// uid of the organization whose business permissions are granted
	OrgUid string `protobuf:"bytes,2,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
}

func (x *CreateBizgrantRequest) Reset() {
	*x = CreateBizgrantRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_bizgrant_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBizgrantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBizgrantRequest) ProtoMessage() {}

func (x *CreateBizgrantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_bizgrant_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBizgrantRequest.ProtoReflect.Descriptor instead.
func (*CreateBizgrantRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_bizgrant_proto_rawDescGZIP(), []int{0}
}

func (x *CreateBizgrantRequest) GetGranteeUid() string {
	if x != nil {
		return x.GranteeUid
	}
	return ""
}

func (x *CreateBizgrantRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

type Bizgrant struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grantor uid
	GrantorUid string `protobuf:"bytes,1,opt,name=grantor_uid,json=grantorUid,proto3" json:"grantor_uid,omitempty"`
	// grantee uid
	GranteeUid string `protobuf:"bytes,2,opt,name=grantee_uid,json=granteeUid,proto3" json:"grantee_uid,omitempty"`
	// uid of the organization whose business permissions are granted
	OrgUid    string                 `protobuf:"bytes,3,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *Bizgrant) Reset() {
	*x = Bizgrant{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_bizgrant_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bizgrant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bizgrant) ProtoMessage() {}

func (x *Bizgrant) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_bizgrant_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bizgrant.ProtoReflect.Descriptor instead.
func (*Bizgrant) Descriptor() ([]byte, []int) {
	return file_anno_v1_bizgrant_proto_rawDescGZIP(), []int{1}
}

func (x *Bizgrant) GetGrantorUid() string {
	if x != nil {
		return x.GrantorUid
	}
	return ""
}

func (x *Bizgrant) GetGranteeUid() string {
	if x != nil {
		return x.GranteeUid
	}
	return ""
}

func (x *Bizgrant) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *Bizgrant) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type BizgrantFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grantor uid
	GrantorUid string `protobuf:"bytes,1,opt,name=grantor_uid,json=grantorUid,proto3" json:"grantor_uid,omitempty"`
	// grantee uid
	GranteeUid string `protobuf:"bytes,2,opt,name=grantee_uid,json=granteeUid,proto3" json:"grantee_uid,omitempty"`
	// uid of the organization whose business permissions are granted
	OrgUid string `protobuf:"bytes,3,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
}

func (x *BizgrantFilter) Reset() {
	*x = BizgrantFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_bizgrant_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BizgrantFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BizgrantFilter) ProtoMessage() {}

func (x *BizgrantFilter) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_bizgrant_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BizgrantFilter.ProtoReflect.Descriptor instead.
func (*BizgrantFilter) Descriptor() ([]byte, []int) {
	return file_anno_v1_bizgrant_proto_rawDescGZIP(), []int{2}
}

func (x *BizgrantFilter) GetGrantorUid() string {
	if x != nil {
		return x.GrantorUid
	}
	return ""
}

func (x *BizgrantFilter) GetGranteeUid() string {
	if x != nil {
		return x.GranteeUid
	}
	return ""
}

func (x *BizgrantFilter) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

type DeleteBizgrantRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *BizgrantFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *DeleteBizgrantRequest) Reset() {
	*x = DeleteBizgrantRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_bizgrant_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteBizgrantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBizgrantRequest) ProtoMessage() {}

func (x *DeleteBizgrantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_bizgrant_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBizgrantRequest.ProtoReflect.Descriptor instead.
func (*DeleteBizgrantRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_bizgrant_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteBizgrantRequest) GetFilter() *BizgrantFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type ListBizgrantRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagesz int32 `protobuf:"varint,1,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// An opaque pagination token returned from a previous call.
	// An empty token denotes the first page.
	PageToken string          `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	Filter    *BizgrantFilter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListBizgrantRequest) Reset() {
	*x = ListBizgrantRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_bizgrant_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBizgrantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBizgrantRequest) ProtoMessage() {}

func (x *ListBizgrantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_bizgrant_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBizgrantRequest.ProtoReflect.Descriptor instead.
func (*ListBizgrantRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_bizgrant_proto_rawDescGZIP(), []int{4}
}

func (x *ListBizgrantRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListBizgrantRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListBizgrantRequest) GetFilter() *BizgrantFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type ListBizgrantReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Grants []*Bizgrant `protobuf:"bytes,1,rep,name=grants,proto3" json:"grants,omitempty"`
	// An opaque pagination token, if not empty, to be used to fetch the next page of results
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListBizgrantReply) Reset() {
	*x = ListBizgrantReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_bizgrant_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBizgrantReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBizgrantReply) ProtoMessage() {}

func (x *ListBizgrantReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_bizgrant_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBizgrantReply.ProtoReflect.Descriptor instead.
func (*ListBizgrantReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_bizgrant_proto_rawDescGZIP(), []int{5}
}

func (x *ListBizgrantReply) GetGrants() []*Bizgrant {
	if x != nil {
		return x.Grants
	}
	return nil
}

func (x *ListBizgrantReply) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

var File_anno_v1_bizgrant_proto protoreflect.FileDescriptor

var file_anno_v1_bizgrant_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69, 0x7a, 0x67, 0x72, 0x61,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9c, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42,
	0x69, 0x7a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36,
	0x0a, 0x0b, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x65, 0x65, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x6e,
	0x74, 0x65, 0x65, 0x55, 0x69, 0x64, 0x12, 0x2e, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x06,
	0x6f, 0x72, 0x67, 0x55, 0x69, 0x64, 0x3a, 0x1b, 0xba, 0x47, 0x18, 0xba, 0x01, 0x0b, 0x67, 0x72,
	0x61, 0x6e, 0x74, 0x65, 0x65, 0x5f, 0x75, 0x69, 0x64, 0xba, 0x01, 0x07, 0x6f, 0x72, 0x67, 0x5f,
	0x75, 0x69, 0x64, 0x22, 0x9d, 0x02, 0x0a, 0x08, 0x42, 0x69, 0x7a, 0x67, 0x72, 0x61, 0x6e, 0x74,
	0x12, 0x36, 0x0a, 0x0b, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b,
	0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x0a, 0x67, 0x72,
	0x61, 0x6e, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x0b, 0x67, 0x72, 0x61, 0x6e,
	0x74, 0x65, 0x65, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa,
	0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b,
	0x31, 0x31, 0x7d, 0x24, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x65, 0x65, 0x55, 0x69, 0x64,
	0x12, 0x2e, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64,
	0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x3a, 0x36, 0xba, 0x47, 0x33,
	0xba, 0x01, 0x0b, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64, 0xba, 0x01,
	0x0b, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x65, 0x65, 0x5f, 0x75, 0x69, 0x64, 0xba, 0x01, 0x07, 0x6f,
	0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0xba, 0x01, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x22, 0x6b, 0x0a, 0x0e, 0x42, 0x69, 0x7a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x6f, 0x72,
	0x5f, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x6e,
	0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x65,
	0x65, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x72, 0x61,
	0x6e, 0x74, 0x65, 0x65, 0x55, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64,
	0x22, 0x52, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x69, 0x7a, 0x67, 0x72, 0x61,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x7a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x22, 0x88, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x69, 0x7a,
	0x67, 0x72, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x06,
	0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2f,
	0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x7a, 0x67, 0x72, 0x61, 0x6e,
	0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22,
	0x86, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x69, 0x7a, 0x67, 0x72, 0x61, 0x6e, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x29, 0x0a, 0x06, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x69, 0x7a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x52, 0x06, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x73,
	0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50,
	0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x3a, 0x1e, 0xba, 0x47, 0x1b, 0xba, 0x01, 0x06,
	0x67, 0x72, 0x61, 0x6e, 0x74, 0x73, 0xba, 0x01, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x32, 0xac, 0x02, 0x0a, 0x09, 0x42, 0x69, 0x7a,
	0x67, 0x72, 0x61, 0x6e, 0x74, 0x73, 0x12, 0x5d, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x42, 0x69, 0x7a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x12, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x69, 0x7a, 0x67, 0x72, 0x61, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x69, 0x7a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x22, 0x18, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x12, 0x3a, 0x01, 0x2a, 0x22, 0x0d, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69, 0x7a, 0x67,
	0x72, 0x61, 0x6e, 0x74, 0x73, 0x12, 0x5f, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42,
	0x69, 0x7a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x12, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x69, 0x7a, 0x67, 0x72, 0x61, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x15, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x2a, 0x0d, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69, 0x7a,
	0x67, 0x72, 0x61, 0x6e, 0x74, 0x73, 0x12, 0x5f, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x69,
	0x7a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x12, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x69, 0x7a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x69, 0x7a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x15, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x12, 0x0d, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69,
	0x7a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x73, 0x42, 0x3e, 0x0a, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x31, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e,
	0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x2f,
	0x76, 0x31, 0x3b, 0x61, 0x6e, 0x6e, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_anno_v1_bizgrant_proto_rawDescOnce sync.Once
	file_anno_v1_bizgrant_proto_rawDescData = file_anno_v1_bizgrant_proto_rawDesc
)

func file_anno_v1_bizgrant_proto_rawDescGZIP() []byte {
	file_anno_v1_bizgrant_proto_rawDescOnce.Do(func() {
		file_anno_v1_bizgrant_proto_rawDescData = protoimpl.X.CompressGZIP(file_anno_v1_bizgrant_proto_rawDescData)
	})
	return file_anno_v1_bizgrant_proto_rawDescData
}

var file_anno_v1_bizgrant_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_anno_v1_bizgrant_proto_goTypes = []interface{}{
	(*CreateBizgrantRequest)(nil), // 0: anno.v1.CreateBizgrantRequest
	(*Bizgrant)(nil),              // 1: anno.v1.Bizgrant
	(*BizgrantFilter)(nil),        // 2: anno.v1.BizgrantFilter
	(*DeleteBizgrantRequest)(nil), // 3: anno.v1.DeleteBizgrantRequest
	(*ListBizgrantRequest)(nil),   // 4: anno.v1.ListBizgrantRequest
	(*ListBizgrantReply)(nil),     // 5: anno.v1.ListBizgrantReply
	(*timestamppb.Timestamp)(nil), // 6: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),         // 7: google.protobuf.Empty
}
var file_anno_v1_bizgrant_proto_depIdxs = []int32{
	6, // 0: anno.v1.Bizgrant.created_at:type_name -> google.protobuf.Timestamp
	2, // 1: anno.v1.DeleteBizgrantRequest.filter:type_name -> anno.v1.BizgrantFilter
	2, // 2: anno.v1.ListBizgrantRequest.filter:type_name -> anno.v1.BizgrantFilter
	1, // 3: anno.v1.ListBizgrantReply.grants:type_name -> anno.v1.Bizgrant
	0, // 4: anno.v1.Bizgrants.CreateBizgrant:input_type -> anno.v1.CreateBizgrantRequest
	3, // 5: anno.v1.Bizgrants.DeleteBizgrant:input_type -> anno.v1.DeleteBizgrantRequest
	4, // 6: anno.v1.Bizgrants.ListBizgrant:input_type -> anno.v1.ListBizgrantRequest
	1, // 7: anno.v1.Bizgrants.CreateBizgrant:output_type -> anno.v1.Bizgrant
	7, // 8: anno.v1.Bizgrants.DeleteBizgrant:output_type -> google.protobuf.Empty
	5, // 9: anno.v1.Bizgrants.ListBizgrant:output_type -> anno.v1.ListBizgrantReply
	7, // [7:10] is the sub-list for method output_type
	4, // [4:7] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_anno_v1_bizgrant_proto_init() }
func file_anno_v1_bizgrant_proto_init() {
	if File_anno_v1_bizgrant_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_anno_v1_bizgrant_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBizgrantRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_bizgrant_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bizgrant); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_bizgrant_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BizgrantFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_bizgrant_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteBizgrantRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_bizgrant_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBizgrantRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_bizgrant_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBizgrantReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anno_v1_bizgrant_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_anno_v1_bizgrant_proto_goTypes,
		DependencyIndexes: file_anno_v1_bizgrant_proto_depIdxs,
		MessageInfos:      file_anno_v1_bizgrant_proto_msgTypes,
	}.Build()
	File_anno_v1_bizgrant_proto = out.File
	file_anno_v1_bizgrant_proto_rawDesc = nil
	file_anno_v1_bizgrant_proto_goTypes = nil
	file_anno_v1_bizgrant_proto_depIdxs = nil
}
