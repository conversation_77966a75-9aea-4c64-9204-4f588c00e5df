package client

import (
	"context"
	"encoding/base64"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/encoding"
	"github.com/go-kratos/kratos/v2/encoding/json"
	md "github.com/go-kratos/kratos/v2/metadata"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	kgrpc "github.com/go-kratos/kratos/v2/transport/grpc"
	"google.golang.org/grpc"
)

type Service struct {
	Addr    string
	Timeout time.Duration
}

const maxGRPCMsgSize = 128 * 1024 * 1024

func newGrpcConn(service *Service) *grpc.ClientConn {
	fmt.Printf("=== gRPC Client Config ===\n")
	fmt.Printf("Service Addr: %s\n", service.Addr)
	fmt.Printf("Service Timeout: %v\n", service.Timeout)

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	mws := []middleware.Middleware{
		setClientUserMW(),
		metadata.Client(),
		tracing.Client(),
	}

	opts := []kgrpc.ClientOption{
		kgrpc.WithEndpoint(service.Addr),
		kgrpc.WithOptions(grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(maxGRPCMsgSize))),
		kgrpc.WithMiddleware(mws...),
	}

	// 如果配置了超时，则使用配置的超时时间
	if service.Timeout > 0 {
		fmt.Printf("Using configured timeout: %v\n", service.Timeout)
		opts = append(opts, kgrpc.WithTimeout(service.Timeout))
	} else {
		fmt.Printf("Using default kratos timeout: 2s\n")
	}

	conn, err := kgrpc.DialInsecure(ctx, opts...)
	if err != nil {
		panic(err)
	}
	fmt.Printf("=== gRPC Client Connected\n\n")
	return conn
}

func init() {
	json.MarshalOptions.UseProtoNames = true
}

func setClientUserMW() middleware.Middleware {
	enc := encoding.GetCodec("json")
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			k, v := HeaderUserUid, ""
			if user := UserFromCtx(ctx); !user.IsEmpty() {
				bs, err := enc.Marshal(user)
				if err != nil {
					return nil, fmt.Errorf("failed to marshal context user: %w", err)
				}
				userStr := base64.StdEncoding.EncodeToString(bs)
				k, v = HeaderUser, userStr
			} else if id := RPCIdentityFromCtx(ctx); !id.IsEmpty() {
				if id.Uid != "" {
					v = id.Uid
				} else {
					k, v = HeaderXmdAuthorization, id.AuthToken
				}
			}
			if v != "" {
				ctx = md.AppendToClientContext(ctx, k, v)
			}

			return handler(ctx, req)
		}
	}
}
