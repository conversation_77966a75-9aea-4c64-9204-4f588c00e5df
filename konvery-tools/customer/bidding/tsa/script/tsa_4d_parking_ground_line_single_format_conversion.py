import os
import sys
import numpy as np
import copy
from scipy.spatial.transform import Rotation as R
sys.path.append('.')
from customer.common import tools
from customer.bidding.tsa.script.tsa_4d_parking_ground_line_format_conversion import run as run_4d


def in_cur_frame(vehicle_mat, pt):
    pt_vehicle = vehicle_mat @ (pt + [1])
    return abs(pt_vehicle[0]) < 15 and abs(pt_vehicle[1]) < 15


def get_points_in_cur_frame(obj, viewpoint_mat):
    new_obj = copy.deepcopy(obj)
    if obj['type'] == 'box':
        if not in_cur_frame(viewpoint_mat, [obj['point']['x'], obj['point']['y'], obj['point']['z']]):
            new_obj['point'] = []
        # if in_cur_frame(viewpoint_mat, [obj['point']['x'], obj['point']['y'], obj['point']['z']]):
        #     old_pose_mat = np.array(
        #         obj['point'][:3] + R.from_euler('xyz', [0, 0, obj['point'][-1]]).as_quat().tolist())
        #     new_pose = tools.mat_to_pose(viewpoint_mat @ tools.pose_to_mat(old_pose_mat))
        #     new_obj['point'][:3] = new_pose[:3]
        #     new_obj['point'][-1] = R.from_quat(new_pose[-4:]).as_euler('xyz')[-1]
        # else:
        #     new_obj['point'] = []
    elif obj['type'] in ('polygon', 'polyline'):
        new_obj['point'].clear()
        for pt in obj['point']:
            if in_cur_frame(viewpoint_mat, pt):
                new_obj['point'].append(pt)
                # new_obj['point'].append((viewpoint_mat @ (pt + [1])).tolist()[:3])
    else:
        raise ValueError(f'unknown type: {obj["type"]}')
    return new_obj


def get_parking_slot_in_cur_frame(obj, viewpoint_mat):
    new_obj = copy.deepcopy(obj)
    new_obj['parking_line'].clear()
    for pt in obj['parking_line']:
        if in_cur_frame(viewpoint_mat, pt):
            new_obj['parking_line'].append(pt)
            # new_obj['parking_line'].append((viewpoint_mat @ (pt + [1])).tolist()[:3])
    return new_obj


def run(input_dir, output_dir):
    run_4d(input_dir, output_dir + '_4d')
    for clip_dir in tools.listdir(os.path.join(input_dir, 'annos'), full_path=True, sort=True):
        out_clip_dir = os.path.join(output_dir, os.path.basename(clip_dir))
        os.mkdir(out_clip_dir)
        first_json_file = os.path.join(tools.listdir(clip_dir, full_path=True, sort=True)[0], 'annos.json')
        res_4d_path = os.path.join(output_dir + '_4d', f'{os.path.basename(os.path.dirname(first_json_file))}.json')
        res_4d_data = tools.get_json_data(res_4d_path)
        frames = tools.listdir(os.path.join(input_dir, 'data', os.path.basename(clip_dir)), full_path=True, sort=True)
        for frame in frames:
            params_file = os.path.join(frame, 'params.json')
            params_data = tools.get_json_data(params_file)
            viewpoint_pose = params_data['lidar']['viewpoint']
            viewpoint_mat = tools.pose_to_mat(viewpoint_pose)
            viewpoint_mat = np.linalg.inv(viewpoint_mat)
            groundline_res = []
            groundmarking_res = []
            arrow_mark_res = []
            land_mark_res = []
            lane_res = []
            parking_slot_res = []

            for obj in res_4d_data['groundline']['anno']:
                new_obj = get_points_in_cur_frame(obj, viewpoint_mat)
                if new_obj['point']:
                    groundline_res.append(new_obj)
            for obj in res_4d_data['groundmarking']['anno']:
                new_obj = get_points_in_cur_frame(obj, viewpoint_mat)
                if new_obj['point']:
                    groundmarking_res.append(new_obj)
            for obj in res_4d_data['arrow_mark']['anno']:
                new_obj = get_points_in_cur_frame(obj, viewpoint_mat)
                if new_obj['point']:
                    arrow_mark_res.append(new_obj)
            for obj in res_4d_data['land_mark']['anno']:
                new_obj = get_points_in_cur_frame(obj, viewpoint_mat)
                if new_obj['point']:
                    land_mark_res.append(new_obj)
            for obj in res_4d_data['lane']['anno']:
                new_obj = get_points_in_cur_frame(obj, viewpoint_mat)
                if new_obj['point']:
                    lane_res.append(new_obj)
            for obj in res_4d_data['parking_slot']['anno']:
                new_obj = get_parking_slot_in_cur_frame(obj, viewpoint_mat)
                if new_obj['parking_line']:
                    parking_slot_res.append(obj)
            result = {
                "data_folder": f"{os.path.basename(clip_dir)}/{os.path.basename(frame)}",
                "timestamp": os.path.basename(frame),
                "groundline": {
                    "state": "exist" if len(groundline_res) > 0 else "non-exist",
                    "anno": groundline_res
                },
                "groundmarking": {
                    "state": "exist" if len(groundmarking_res) > 0 else "non-exist",
                    "anno": groundmarking_res
                },
                "arrow_mark": {
                    "state": "exist" if len(arrow_mark_res) > 0 else "non-exist",
                    "anno": arrow_mark_res
                },
                "land_mark": {
                    "state": "exist" if len(land_mark_res) > 0 else "non-exist",
                    "anno": land_mark_res
                },
                "lane": {
                    "state": "exist" if len(lane_res) > 0 else "non-exist",
                    "anno": lane_res
                },
                "parking_slot": {
                    "state": "exist" if len(parking_slot_res) > 0 else "non-exist",
                    "anno": parking_slot_res
                }
            }
            tools.write_json_file(result, os.path.join(out_clip_dir, f'{os.path.basename(frame)}.json'))


if __name__ == '__main__':
    args = tools.get_args()
    if args.input == "../../input/test":
        args.input = '/Users/<USER>/Downloads/TSA/tsa-4dbo-che-dian-yun-jie-di-xian-01_lot-chp6ykl8oez_order-chlgwddsqzb_ins-0_241114-1101'
        args.out = '/Users/<USER>/Downloads/TSA/tsa-4dbo-che-dian-yun-jie-di-xian-01_lot-chp6ykl8oez_order-chlgwddsqzb_ins-0_241114-1101_out'
    tools.check_dir(args.out)
    run(args.input, args.out)
