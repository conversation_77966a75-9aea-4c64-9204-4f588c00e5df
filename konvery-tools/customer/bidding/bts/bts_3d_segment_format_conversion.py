import os
import sys
import numpy as np

sys.path.append(".")
from customer.common import tools
from customer.common import pypcd


categorys = ['', 'noise', 'curb', 'freespace', 'unfreespace', 'vegetation', 'trunk', 'lift_rod', 'pole', 'sign',
             'traffic_light', 'barrier', 'air_obstacle', 'building', 'misc']

category2id = {v: k for k, v in dict(enumerate(categorys)).items()}


def trans(label_file, pcd_file, seg_output_dir):
    data = tools.get_json_data(label_file)
    pts_label = []
    for e in data['segmentation']['result'][0]:
        label = category2id.get(e)
        pts_label.append(label)
    pts_label = np.array(list(zip(pts_label, [0] * len(pts_label))),
                         dtype=np.dtype([('label', np.uint8), ('instance', np.uint8)]))

    label_mark_data = {
        'fields': ['type_id', 'status'],
        'count': [1, 1],
        'size': [1, 1],
        'type': ['U', 'U'],
    }

    pc = pypcd.point_cloud_from_path(pcd_file)
    pc = pypcd.add_fields(pc, label_mark_data, pts_label)
    pc = pypcd.delete_fields(pc, ['intensity'])
    out_pcd_file = os.path.join(seg_output_dir, os.path.basename(pcd_file))
    pc.save_pcd(out_pcd_file)


def run(input_dir, output_dir):
    tools.check_dir(output_dir)
    for date_dir in tools.listdir(input_dir, full_path=True):
        for outer_dir in tools.listdir(date_dir, full_path=True):
            for clip in tools.listdir(outer_dir, full_path=True):
                clip_output_dir = os.path.join(output_dir, os.path.basename(clip))
                tools.check_dir(clip_output_dir)
                label_files = tools.listdir(os.path.join(clip, 'label'), full_path=True, sort=True)
                for idx in range(5, len(label_files), 11):
                    label_file = label_files[idx]
                    pcd_file = os.path.join(clip, 'lidar', os.path.basename(label_file).replace('.json', '.pcd'))
                    trans(label_file, pcd_file, clip_output_dir)


"""
项目：bts 3d点云分割 格式转换
"""
if __name__ == '__main__':
    # --input /Users/<USER>/Downloads/bts/ds_8oylhzst7mg0u3wth007 --out /Users/<USER>/Downloads/bts/ds_8oylhzst7mg0u3wth007_out
    args = tools.get_args()
    run(args.input, args.out)
