import json
import os
import shutil
import sys
import glob

import numpy as np

from customer.bidding.stl_ema.script.transform import Transform

sys.path.append('.')
from customer.common import tools

code = 0
package_index = 0

pose_data = {}
test_in_camera_params = {
}

camera_model = {
    'front_narrow': 'pinhole',
    'front_wide': 'fisheye',
    'front_left': 'pinhole',
    'back_left': 'pinhole',
    'front_right': 'pinhole',
    'back_right': 'pinhole',
    'back': 'pinhole',
    'spherical-left': 'fisheye',
    'obstacle': 'fisheye',
    'spherical-right': 'fisheye',
    'spherical-backward': 'fisheye'
}

# cameras to be copied to package directory
camera_name_map = {
    "camera-71": "前视窄",
    "camera-73": "前视宽",
    "camera-75": "左后",
    "camera-76": "左前",
    "camera-77": "后右",
    "camera-78": "前右",
    "camera-83": "后",
}

camera_mapping_id = {
    'camera-71': 'front_narrow',
    'camera-73': 'front_wide',
    'camera-75': 'back_left',
    'camera-76': 'front_left',
    'camera-77': 'back_right',
    'camera-78': 'front_right',
    'camera-83': 'back',
}

camera_inv_mapping_id = {
    'front_narrow': 'camera-71',
    'front_wide': 'camera-73',
    'back_left': 'camera-75',
    'front_left': 'camera-76',
    'back_right': 'camera-77',
    'front_right': 'camera-78',
    'back': 'camera-83'
}


def get_code_msg(msg_code):
    msg = "失败"
    if 0 == msg_code:
        msg = "成功"
    return msg


def get_merged_pcd():
    return ""


def get_pose_data(pose_file):
    global pose_data

    data = tools.read_file(pose_file)
    for f in data:
        sep_list = f.split(" ")
        pcd = sep_list[0]
        pose = sep_list[1:]
        pose_data[pcd] = tools.pose_to_mat(pose).flatten().tolist()


def get_params(params_root_path):
    global test_in_camera_params

    camera_ids = ["front_narrow", "front_wide", "front_left", "back_left", "front_right", "back_right", "back"]
    calibration_type = 'SensorCalibration'
    camera_params = {}

    if calibration_type == 'SensorCalibration':
        camera_ids_ = camera_ids
    for camera_name in camera_ids_:
        if calibration_type == 'SensorCalibration':
            camera_name_extrinsic = 'top_center_lidar-to-' + camera_name + '-extrinsic'
            camera_name_intrinsic = camera_name + '-intrinsic'
            with open(os.path.join(params_root_path, camera_name_extrinsic + '.json')) as f:
                json_dict_extrinsic = json.load(f)
            with open(os.path.join(params_root_path, camera_name_intrinsic + '.json')) as f:
                json_dict_intrinsic = json.load(f)
            camera_params[camera_name] = Transform()
            extrinsic_mat = np.array(json_dict_extrinsic[camera_name_extrinsic]['param']['sensor_calib']['data'])
            camera_params[camera_name].SetExtrinsic2(extrinsic_mat[:3, 3], extrinsic_mat[:3, :3])
            intrinsic_mat = np.array(json_dict_intrinsic[camera_name_intrinsic]['param']['cam_K']['data'])
            camera_params[camera_name].SetIntrinsicMat(intrinsic_mat)
            dist_mat = json_dict_intrinsic[camera_name_intrinsic]['param']['cam_dist']['data']
            camera_params[camera_name].SetDistortion(dist_mat[0])
            camera_params[camera_name].SetImageHeight(json_dict_intrinsic[camera_name_intrinsic]['param']['img_dist_h'])
            camera_params[camera_name].SetImageWidth(json_dict_intrinsic[camera_name_intrinsic]['param']['img_dist_w'])

    global test_in_camera_params
    test_in_camera_params = {}

    for cam in camera_params:
        cam_data = camera_params[cam]
        extrinsic = cam_data.GetExtrinsicMat().tolist()
        intrinsic = cam_data.GetIntrinsicMat().flatten().tolist()
        distortion = cam_data.GetDistortion()
        cam_model = "pinhole" if not cam_data.is_fisheye else "fisheye"
        sensor = {}

        if cam_model == "pinhole":
            sensor = {
                "camera_model": "pinhole",
                "extrinsic": extrinsic,
                "fx": intrinsic[0],
                "fy": intrinsic[4],
                "cx": intrinsic[2],
                "cy": intrinsic[5],
                "k1": distortion[0],
                "k2": distortion[1],
                "p1": distortion[2],
                "p2": distortion[3],
                "k3": distortion[4],
                "k4": distortion[5],
                "k5": distortion[6],
                "k6": distortion[7]
            }
        else:
            sensor = {
                "camera_model": "fisheye",
                "extrinsic": extrinsic,
                "fx": intrinsic[0],
                "fy": intrinsic[4],
                "cx": intrinsic[2],
                "cy": intrinsic[5],
                "k1": distortion[0],
                "k2": distortion[1],
                "k3": distortion[4],
                "k4": distortion[5]
            }

        test_in_camera_params[camera_inv_mapping_id[cam]] = sensor

    return test_in_camera_params


def create_test_in_config(pcd_list, output_dir):
    global test_in_camera_params

    test_in_config_filename = output_dir + os.sep + "config.json"
    cameras = {}
    config = {
        "camera": cameras,
        "data_type": "fusion_pointcloud",
        "sensor_params": test_in_camera_params,
        # "poses": {}
    }

    for cam in camera_name_map.keys():
        cam = cam.lower()
        cameras[cam] = camera_name_map[cam]

    # for pcd in pcd_list:
    #     pcd = pcd.replace(".pcd", "")
    #     if pcd in pose_data:
    #         config["poses"][pcd] = pose_data[pcd]

    tools.write_json_file(file=test_in_config_filename, data=config, ensure_ascii=False, indent=4)


def copy_to_new_dir(input_dir, output_dir):
    """
        拷贝原始文件到分包后的目录
    """
    camera_dir = output_dir + os.sep + "camera"
    lidar_dir = output_dir + os.sep + "lidar"

    frame_name_base = os.path.basename(input_dir)
    huawei_pcd = glob.glob(os.path.join(input_dir, "hesai*pcd"))[0]
    st_pcd = glob.glob(os.path.join(input_dir, "st*pcd"))[0]
#    print(f"{huawei_pcd} and {st_pcd}")
    pcds = [huawei_pcd, st_pcd]
#    pcd_name = frame_name + ".pcd"

#    src_pcd = get_merged_pcd()
#    dst_pcd = lidar_dir + os.sep + pcd_name
    # shutil.copy(src_pcd, dst_pcd)

    jpg_list = tools.get_file_by_extension(input_dir, extension=".jpg")

    for idx, pcd in enumerate(pcds):
        frame_name = f"{frame_name_base}{idx+1:04}"
        dest_pcd_name = frame_name + ".pcd"
        shutil.copy(pcd, os.path.join(lidar_dir, dest_pcd_name))
        for src_img in jpg_list:
            src_img = src_img.path
            for cam in camera_mapping_id.keys():
                if cam in src_img:
                    dst_img = camera_dir + os.sep + cam + os.sep + frame_name + ".jpg"
                    shutil.copy2(src_img, dst_img)
                    break


def create_pack(input_dir, output_dir):
    """
        对原始文件进行分包
    """

    global package_index

    package_index += 1
    pack_root_dir = os.path.dirname(input_dir)
    pack_name = os.path.basename(pack_root_dir)
    param_dir = pack_root_dir + os.sep + "param"
    pose_file = pack_root_dir + os.sep + "global_imu_pose.txt"
    get_params(param_dir)
    # get_pose_data(pose_file)

    frame_list = tools.get_sub_dir(input_dir)

    seg_output_dir = output_dir + os.sep + str(package_index) + "_" + pack_name
    tools.check_dir(seg_output_dir)

    camera_dir = seg_output_dir + os.sep + "camera"
    lidar_dir = seg_output_dir + os.sep + "lidar"
    tools.check_dir(camera_dir)
    tools.check_dir(lidar_dir)

    for cam in camera_mapping_id.keys():
        tools.check_dir(camera_dir + os.sep + cam)

    frame_count = 0
    for frame in frame_list:
        frame_count += 1
        frame_dir = input_dir + os.sep + frame
        copy_to_new_dir(input_dir=frame_dir, output_dir=seg_output_dir)

    pcd_list = tools.listdir(input_dir)

    create_test_in_config(pcd_list=pcd_list, output_dir=seg_output_dir)

    print(f"Total frame {frame_count},  package count: {package_index}")


def add_pre_label(input_file, output_file):
    res = {
        "label_meta": {"mark_status": 0, "global": {}},
        "lidar": []
    }
    with open(input_file, encoding='utf-8') as f:
        lines = f.readlines()
    for idx, line in enumerate(lines, start=1):
        line = line.split()
        mark_data = {
            "class": line[0],
            "attrs": {},
            "track_id": idx,
            "group_id": 0,
            "annotation": {
                "type": "volume",
                "data": {
                    "position": {
                        "x": float(line[11]),
                        "y": float(line[12]),
                        "z": float(line[13]) + float(line[10]) / 2
                    },
                    "dimension": {
                        "l": float(line[8]),
                        "w": float(line[9]),
                        "h": float(line[10])
                    },
                    "rotation": {
                        "x": 0,
                        "y": 0,
                        "z": float(line[14])
                    }
                }
            }
        }
        res['lidar'].append(mark_data)
    tools.write_json_file(res, output_file)


def create_pack_2frame(input_dir, output_dir):
    """
        对原始文件进行分包
    """

    global package_index

    package_index += 1
    pack_root_dir = os.path.dirname(input_dir)
    pack_name = os.path.basename(pack_root_dir)
    param_dir = pack_root_dir + os.sep + "param"
    pose_file = pack_root_dir + os.sep + "global_imu_pose.txt"
    get_params(param_dir)
    # get_pose_data(pose_file)

    out_collection_dir = output_dir + os.sep + str(package_index) + "_" + pack_name
    tools.check_dir(out_collection_dir)

    for seg_dir in tools.listdir(input_dir):
        out_seg_dir = os.path.join(out_collection_dir, seg_dir)
        os.mkdir(out_seg_dir)
        out_cam_dir = os.path.join(out_seg_dir, 'camera')
        os.mkdir(out_cam_dir)
        out_lidar_dir = os.path.join(out_seg_dir, 'lidar')
        os.mkdir(out_lidar_dir)
        for camera_name in camera_name_map.keys():
            os.mkdir(os.path.join(out_cam_dir, camera_name))
            cam_imgs = glob.glob(os.path.join(input_dir, seg_dir, f'{camera_name}-*.jpg'))
            if len(cam_imgs) > 1:
                cam_imgs = [img for img in cam_imgs if 'resize' not in img]
            cam_img = cam_imgs[0]
            shutil.copyfile(cam_img, os.path.join(out_cam_dir, camera_name, f'{seg_dir}_hesai.jpg'))
            shutil.copyfile(cam_img, os.path.join(out_cam_dir, camera_name, f'{seg_dir}_st.jpg'))
        hesai_pcd = glob.glob(os.path.join(input_dir, seg_dir, 'hesai-*.pcd'))[0]
        st_pcd = glob.glob(os.path.join(input_dir, seg_dir, 'st-*.pcd'))[0]
        shutil.copyfile(hesai_pcd, os.path.join(out_lidar_dir, f'{seg_dir}_hesai.pcd'))
        shutil.copyfile(st_pcd, os.path.join(out_lidar_dir, f'{seg_dir}_st.pcd'))
        # add_pre_label(os.path.join(os.path.dirname(input_dir), 'result', 'final_result', seg_dir + '.txt'), os.path.join(out_seg_dir, 'pre_label', seg_dir + '.json'))
        pcd_list = [hesai_pcd, st_pcd]
        create_test_in_config(pcd_list=pcd_list, output_dir=out_seg_dir)



def run(input_dir, output_dir):
    """
       主函数
    """
    input_dir = tools.unzip(input_dir)
    tools.check_dir(output_dir)

    # 一个label_file是一个分包
    label_file_list = tools.find_dir_by_pre_name(input_dir, pre_name="label_file")
    for label_file in label_file_list:
        create_pack_2frame(input_dir=label_file, output_dir=output_dir)
    trans_result = {"code": code, "output": output_dir, "err_msg": get_code_msg(code)}

    print(trans_result)
    return trans_result


"""
    项目： STL项目 23D 拉框、分割
    对接平台：云测新平台 
    功能： 将客户的文件, 标注结果转换成云测的格式， 并按数量分包
    输入： 原始数据
    输出： 分包后的数据
    开发： Justin
"""
if __name__ == "__main__":

    args = tools.get_args()

#    if args.input == "../../input/test":
    args.input = "/Users/<USER>/Downloads/华为/3D障碍物/EP41-ORIN-0S13-00G_EP41_ORIN_02.10.16_1225_20231101-021208_0.mcap"
    args.out = "/Users/<USER>/Downloads/华为/3D障碍物/EP41-ORIN-0S13-00G_EP41_ORIN_02.10.16_1225_20231101-021208_0_out"

    run(input_dir=args.input, output_dir=args.out)
