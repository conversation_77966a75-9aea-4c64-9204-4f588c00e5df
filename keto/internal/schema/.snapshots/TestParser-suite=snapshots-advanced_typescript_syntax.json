{"Resource": [{"name": "admins", "types": [{"namespace": "Role", "relation": "member"}], "params": ["subject"]}, {"name": "supervisors", "types": [{"namespace": "Role", "relation": "member"}], "params": ["subject"]}, {"name": "annotators", "types": [{"namespace": "Role", "relation": "member"}], "params": ["subject"]}, {"name": "medicalAnnotators", "types": [{"namespace": "Role", "relation": "member"}], "params": ["subject"]}, {"name": "read", "rewrite": {"operator": "or", "children": [{"relation": "admins", "computed_subject_set_relation": "member", "args": [1]}, {"relation": "annotators", "computed_subject_set_relation": "member", "args": [1]}, {"relation": "medicalAnnotators", "computed_subject_set_relation": "member", "args": [1]}, {"relation": "supervisors", "computed_subject_set_relation": "member", "args": [1]}]}, "params": ["ctx"]}, {"name": "comment", "rewrite": {"operator": "or", "children": [{"relation": "read", "args": [0]}]}, "params": ["ctx"]}, {"name": "update", "rewrite": {"operator": "or", "children": [{"relation": "admins", "computed_subject_set_relation": "member", "args": [1]}, {"relation": "annotators", "computed_subject_set_relation": "member", "args": [1]}, {"relation": "medicalAnnotators", "computed_subject_set_relation": "member", "args": [1]}, {"relation": "supervisors", "computed_subject_set_relation": "member", "args": [1]}]}, "params": ["ctx"]}, {"name": "create", "rewrite": {"operator": "or", "children": [{"relation": "admins", "computed_subject_set_relation": "member", "args": [1]}, {"relation": "annotators", "computed_subject_set_relation": "member", "args": [1]}, {"relation": "supervisors", "computed_subject_set_relation": "member", "args": [1]}]}, "params": ["ctx"]}, {"name": "approve", "rewrite": {"operator": "or", "children": [{"relation": "admins", "computed_subject_set_relation": "member", "args": [1]}, {"relation": "supervisors", "computed_subject_set_relation": "member", "args": [1]}]}, "params": ["ctx"]}, {"name": "delete", "rewrite": {"operator": "or", "children": [{"relation": "admins", "computed_subject_set_relation": "member", "args": [1]}, {"relation": "supervisors", "computed_subject_set_relation": "member", "args": [1]}]}, "params": ["ctx"]}], "Role": [{"name": "member", "types": [{"namespace": "Role"}], "params": ["subject"]}]}