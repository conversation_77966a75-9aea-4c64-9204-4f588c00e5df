// Code generated by running "go generate" in golang.org/x/text. DO NOT EDIT.

//go:build go1.16
// +build go1.16

package precis

// UnicodeVersion is the Unicode version from which the tables in this package are derived.
const UnicodeVersion = "13.0.0"

// lookup returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *derivedPropertiesTrie) lookup(s []byte) (v uint8, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return derivedPropertiesValues[c0], 1
	case c0 < 0xC2:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := derivedPropertiesIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := derivedPropertiesIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = derivedPropertiesIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := derivedPropertiesIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = derivedPropertiesIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = derivedPropertiesIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 3 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *derivedPropertiesTrie) lookupUnsafe(s []byte) uint8 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return derivedPropertiesValues[c0]
	}
	i := derivedPropertiesIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = derivedPropertiesIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = derivedPropertiesIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// lookupString returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *derivedPropertiesTrie) lookupString(s string) (v uint8, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return derivedPropertiesValues[c0], 1
	case c0 < 0xC2:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := derivedPropertiesIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := derivedPropertiesIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = derivedPropertiesIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := derivedPropertiesIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = derivedPropertiesIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = derivedPropertiesIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 3 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupStringUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *derivedPropertiesTrie) lookupStringUnsafe(s string) uint8 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return derivedPropertiesValues[c0]
	}
	i := derivedPropertiesIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = derivedPropertiesIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = derivedPropertiesIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// derivedPropertiesTrie. Total size: 27776 bytes (27.12 KiB). Checksum: c8bfc091c977a4c.
type derivedPropertiesTrie struct{}

func newDerivedPropertiesTrie(i int) *derivedPropertiesTrie {
	return &derivedPropertiesTrie{}
}

// lookupValue determines the type of block n and looks up the value for b.
func (t *derivedPropertiesTrie) lookupValue(n uint32, b byte) uint8 {
	switch {
	default:
		return uint8(derivedPropertiesValues[n<<6+uint32(b)])
	}
}

// derivedPropertiesValues: 358 blocks, 22912 entries, 22912 bytes
// The third block is the zero block.
var derivedPropertiesValues = [22912]uint8{
	// Block 0x0, offset 0x0
	0x00: 0x0040, 0x01: 0x0040, 0x02: 0x0040, 0x03: 0x0040, 0x04: 0x0040, 0x05: 0x0040,
	0x06: 0x0040, 0x07: 0x0040, 0x08: 0x0040, 0x09: 0x0040, 0x0a: 0x0040, 0x0b: 0x0040,
	0x0c: 0x0040, 0x0d: 0x0040, 0x0e: 0x0040, 0x0f: 0x0040, 0x10: 0x0040, 0x11: 0x0040,
	0x12: 0x0040, 0x13: 0x0040, 0x14: 0x0040, 0x15: 0x0040, 0x16: 0x0040, 0x17: 0x0040,
	0x18: 0x0040, 0x19: 0x0040, 0x1a: 0x0040, 0x1b: 0x0040, 0x1c: 0x0040, 0x1d: 0x0040,
	0x1e: 0x0040, 0x1f: 0x0040, 0x20: 0x0080, 0x21: 0x00c0, 0x22: 0x00c0, 0x23: 0x00c0,
	0x24: 0x00c0, 0x25: 0x00c0, 0x26: 0x00c0, 0x27: 0x00c0, 0x28: 0x00c0, 0x29: 0x00c0,
	0x2a: 0x00c0, 0x2b: 0x00c0, 0x2c: 0x00c0, 0x2d: 0x00c0, 0x2e: 0x00c0, 0x2f: 0x00c0,
	0x30: 0x00c0, 0x31: 0x00c0, 0x32: 0x00c0, 0x33: 0x00c0, 0x34: 0x00c0, 0x35: 0x00c0,
	0x36: 0x00c0, 0x37: 0x00c0, 0x38: 0x00c0, 0x39: 0x00c0, 0x3a: 0x00c0, 0x3b: 0x00c0,
	0x3c: 0x00c0, 0x3d: 0x00c0, 0x3e: 0x00c0, 0x3f: 0x00c0,
	// Block 0x1, offset 0x40
	0x40: 0x00c0, 0x41: 0x00c0, 0x42: 0x00c0, 0x43: 0x00c0, 0x44: 0x00c0, 0x45: 0x00c0,
	0x46: 0x00c0, 0x47: 0x00c0, 0x48: 0x00c0, 0x49: 0x00c0, 0x4a: 0x00c0, 0x4b: 0x00c0,
	0x4c: 0x00c0, 0x4d: 0x00c0, 0x4e: 0x00c0, 0x4f: 0x00c0, 0x50: 0x00c0, 0x51: 0x00c0,
	0x52: 0x00c0, 0x53: 0x00c0, 0x54: 0x00c0, 0x55: 0x00c0, 0x56: 0x00c0, 0x57: 0x00c0,
	0x58: 0x00c0, 0x59: 0x00c0, 0x5a: 0x00c0, 0x5b: 0x00c0, 0x5c: 0x00c0, 0x5d: 0x00c0,
	0x5e: 0x00c0, 0x5f: 0x00c0, 0x60: 0x00c0, 0x61: 0x00c0, 0x62: 0x00c0, 0x63: 0x00c0,
	0x64: 0x00c0, 0x65: 0x00c0, 0x66: 0x00c0, 0x67: 0x00c0, 0x68: 0x00c0, 0x69: 0x00c0,
	0x6a: 0x00c0, 0x6b: 0x00c0, 0x6c: 0x00c7, 0x6d: 0x00c0, 0x6e: 0x00c0, 0x6f: 0x00c0,
	0x70: 0x00c0, 0x71: 0x00c0, 0x72: 0x00c0, 0x73: 0x00c0, 0x74: 0x00c0, 0x75: 0x00c0,
	0x76: 0x00c0, 0x77: 0x00c0, 0x78: 0x00c0, 0x79: 0x00c0, 0x7a: 0x00c0, 0x7b: 0x00c0,
	0x7c: 0x00c0, 0x7d: 0x00c0, 0x7e: 0x00c0, 0x7f: 0x0040,
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xc0: 0x0040, 0xc1: 0x0040, 0xc2: 0x0040, 0xc3: 0x0040, 0xc4: 0x0040, 0xc5: 0x0040,
	0xc6: 0x0040, 0xc7: 0x0040, 0xc8: 0x0040, 0xc9: 0x0040, 0xca: 0x0040, 0xcb: 0x0040,
	0xcc: 0x0040, 0xcd: 0x0040, 0xce: 0x0040, 0xcf: 0x0040, 0xd0: 0x0040, 0xd1: 0x0040,
	0xd2: 0x0040, 0xd3: 0x0040, 0xd4: 0x0040, 0xd5: 0x0040, 0xd6: 0x0040, 0xd7: 0x0040,
	0xd8: 0x0040, 0xd9: 0x0040, 0xda: 0x0040, 0xdb: 0x0040, 0xdc: 0x0040, 0xdd: 0x0040,
	0xde: 0x0040, 0xdf: 0x0040, 0xe0: 0x0080, 0xe1: 0x0080, 0xe2: 0x0080, 0xe3: 0x0080,
	0xe4: 0x0080, 0xe5: 0x0080, 0xe6: 0x0080, 0xe7: 0x0080, 0xe8: 0x0080, 0xe9: 0x0080,
	0xea: 0x0080, 0xeb: 0x0080, 0xec: 0x0080, 0xed: 0x0040, 0xee: 0x0080, 0xef: 0x0080,
	0xf0: 0x0080, 0xf1: 0x0080, 0xf2: 0x0080, 0xf3: 0x0080, 0xf4: 0x0080, 0xf5: 0x0080,
	0xf6: 0x0080, 0xf7: 0x004f, 0xf8: 0x0080, 0xf9: 0x0080, 0xfa: 0x0080, 0xfb: 0x0080,
	0xfc: 0x0080, 0xfd: 0x0080, 0xfe: 0x0080, 0xff: 0x0080,
	// Block 0x4, offset 0x100
	0x100: 0x00c0, 0x101: 0x00c0, 0x102: 0x00c0, 0x103: 0x00c0, 0x104: 0x00c0, 0x105: 0x00c0,
	0x106: 0x00c0, 0x107: 0x00c0, 0x108: 0x00c0, 0x109: 0x00c0, 0x10a: 0x00c0, 0x10b: 0x00c0,
	0x10c: 0x00c0, 0x10d: 0x00c0, 0x10e: 0x00c0, 0x10f: 0x00c0, 0x110: 0x00c0, 0x111: 0x00c0,
	0x112: 0x00c0, 0x113: 0x00c0, 0x114: 0x00c0, 0x115: 0x00c0, 0x116: 0x00c0, 0x117: 0x0080,
	0x118: 0x00c0, 0x119: 0x00c0, 0x11a: 0x00c0, 0x11b: 0x00c0, 0x11c: 0x00c0, 0x11d: 0x00c0,
	0x11e: 0x00c0, 0x11f: 0x00c0, 0x120: 0x00c0, 0x121: 0x00c0, 0x122: 0x00c0, 0x123: 0x00c0,
	0x124: 0x00c0, 0x125: 0x00c0, 0x126: 0x00c0, 0x127: 0x00c0, 0x128: 0x00c0, 0x129: 0x00c0,
	0x12a: 0x00c0, 0x12b: 0x00c0, 0x12c: 0x00c0, 0x12d: 0x00c0, 0x12e: 0x00c0, 0x12f: 0x00c0,
	0x130: 0x00c0, 0x131: 0x00c0, 0x132: 0x00c0, 0x133: 0x00c0, 0x134: 0x00c0, 0x135: 0x00c0,
	0x136: 0x00c0, 0x137: 0x0080, 0x138: 0x00c0, 0x139: 0x00c0, 0x13a: 0x00c0, 0x13b: 0x00c0,
	0x13c: 0x00c0, 0x13d: 0x00c0, 0x13e: 0x00c0, 0x13f: 0x00c0,
	// Block 0x5, offset 0x140
	0x140: 0x00c0, 0x141: 0x00c0, 0x142: 0x00c0, 0x143: 0x00c0, 0x144: 0x00c0, 0x145: 0x00c0,
	0x146: 0x00c0, 0x147: 0x00c0, 0x148: 0x00c0, 0x149: 0x00c0, 0x14a: 0x00c0, 0x14b: 0x00c0,
	0x14c: 0x00c0, 0x14d: 0x00c0, 0x14e: 0x00c0, 0x14f: 0x00c0, 0x150: 0x00c0, 0x151: 0x00c0,
	0x152: 0x00c0, 0x153: 0x00c0, 0x154: 0x00c0, 0x155: 0x00c0, 0x156: 0x00c0, 0x157: 0x00c0,
	0x158: 0x00c0, 0x159: 0x00c0, 0x15a: 0x00c0, 0x15b: 0x00c0, 0x15c: 0x00c0, 0x15d: 0x00c0,
	0x15e: 0x00c0, 0x15f: 0x00c0, 0x160: 0x00c0, 0x161: 0x00c0, 0x162: 0x00c0, 0x163: 0x00c0,
	0x164: 0x00c0, 0x165: 0x00c0, 0x166: 0x00c0, 0x167: 0x00c0, 0x168: 0x00c0, 0x169: 0x00c0,
	0x16a: 0x00c0, 0x16b: 0x00c0, 0x16c: 0x00c0, 0x16d: 0x00c0, 0x16e: 0x00c0, 0x16f: 0x00c0,
	0x170: 0x00c0, 0x171: 0x00c0, 0x172: 0x0080, 0x173: 0x0080, 0x174: 0x00c0, 0x175: 0x00c0,
	0x176: 0x00c0, 0x177: 0x00c0, 0x178: 0x00c0, 0x179: 0x00c0, 0x17a: 0x00c0, 0x17b: 0x00c0,
	0x17c: 0x00c0, 0x17d: 0x00c0, 0x17e: 0x00c0, 0x17f: 0x0080,
	// Block 0x6, offset 0x180
	0x180: 0x0080, 0x181: 0x00c0, 0x182: 0x00c0, 0x183: 0x00c0, 0x184: 0x00c0, 0x185: 0x00c0,
	0x186: 0x00c0, 0x187: 0x00c0, 0x188: 0x00c0, 0x189: 0x0080, 0x18a: 0x00c0, 0x18b: 0x00c0,
	0x18c: 0x00c0, 0x18d: 0x00c0, 0x18e: 0x00c0, 0x18f: 0x00c0, 0x190: 0x00c0, 0x191: 0x00c0,
	0x192: 0x00c0, 0x193: 0x00c0, 0x194: 0x00c0, 0x195: 0x00c0, 0x196: 0x00c0, 0x197: 0x00c0,
	0x198: 0x00c0, 0x199: 0x00c0, 0x19a: 0x00c0, 0x19b: 0x00c0, 0x19c: 0x00c0, 0x19d: 0x00c0,
	0x19e: 0x00c0, 0x19f: 0x00c0, 0x1a0: 0x00c0, 0x1a1: 0x00c0, 0x1a2: 0x00c0, 0x1a3: 0x00c0,
	0x1a4: 0x00c0, 0x1a5: 0x00c0, 0x1a6: 0x00c0, 0x1a7: 0x00c0, 0x1a8: 0x00c0, 0x1a9: 0x00c0,
	0x1aa: 0x00c0, 0x1ab: 0x00c0, 0x1ac: 0x00c0, 0x1ad: 0x00c0, 0x1ae: 0x00c0, 0x1af: 0x00c0,
	0x1b0: 0x00c0, 0x1b1: 0x00c0, 0x1b2: 0x00c0, 0x1b3: 0x00c0, 0x1b4: 0x00c0, 0x1b5: 0x00c0,
	0x1b6: 0x00c0, 0x1b7: 0x00c0, 0x1b8: 0x00c0, 0x1b9: 0x00c0, 0x1ba: 0x00c0, 0x1bb: 0x00c0,
	0x1bc: 0x00c0, 0x1bd: 0x00c0, 0x1be: 0x00c0, 0x1bf: 0x0080,
	// Block 0x7, offset 0x1c0
	0x1c0: 0x00c0, 0x1c1: 0x00c0, 0x1c2: 0x00c0, 0x1c3: 0x00c0, 0x1c4: 0x00c0, 0x1c5: 0x00c0,
	0x1c6: 0x00c0, 0x1c7: 0x00c0, 0x1c8: 0x00c0, 0x1c9: 0x00c0, 0x1ca: 0x00c0, 0x1cb: 0x00c0,
	0x1cc: 0x00c0, 0x1cd: 0x00c0, 0x1ce: 0x00c0, 0x1cf: 0x00c0, 0x1d0: 0x00c0, 0x1d1: 0x00c0,
	0x1d2: 0x00c0, 0x1d3: 0x00c0, 0x1d4: 0x00c0, 0x1d5: 0x00c0, 0x1d6: 0x00c0, 0x1d7: 0x00c0,
	0x1d8: 0x00c0, 0x1d9: 0x00c0, 0x1da: 0x00c0, 0x1db: 0x00c0, 0x1dc: 0x00c0, 0x1dd: 0x00c0,
	0x1de: 0x00c0, 0x1df: 0x00c0, 0x1e0: 0x00c0, 0x1e1: 0x00c0, 0x1e2: 0x00c0, 0x1e3: 0x00c0,
	0x1e4: 0x00c0, 0x1e5: 0x00c0, 0x1e6: 0x00c0, 0x1e7: 0x00c0, 0x1e8: 0x00c0, 0x1e9: 0x00c0,
	0x1ea: 0x00c0, 0x1eb: 0x00c0, 0x1ec: 0x00c0, 0x1ed: 0x00c0, 0x1ee: 0x00c0, 0x1ef: 0x00c0,
	0x1f0: 0x00c0, 0x1f1: 0x00c0, 0x1f2: 0x00c0, 0x1f3: 0x00c0, 0x1f4: 0x00c0, 0x1f5: 0x00c0,
	0x1f6: 0x00c0, 0x1f7: 0x00c0, 0x1f8: 0x00c0, 0x1f9: 0x00c0, 0x1fa: 0x00c0, 0x1fb: 0x00c0,
	0x1fc: 0x00c0, 0x1fd: 0x00c0, 0x1fe: 0x00c0, 0x1ff: 0x00c0,
	// Block 0x8, offset 0x200
	0x200: 0x00c0, 0x201: 0x00c0, 0x202: 0x00c0, 0x203: 0x00c0, 0x204: 0x0080, 0x205: 0x0080,
	0x206: 0x0080, 0x207: 0x0080, 0x208: 0x0080, 0x209: 0x0080, 0x20a: 0x0080, 0x20b: 0x0080,
	0x20c: 0x0080, 0x20d: 0x00c0, 0x20e: 0x00c0, 0x20f: 0x00c0, 0x210: 0x00c0, 0x211: 0x00c0,
	0x212: 0x00c0, 0x213: 0x00c0, 0x214: 0x00c0, 0x215: 0x00c0, 0x216: 0x00c0, 0x217: 0x00c0,
	0x218: 0x00c0, 0x219: 0x00c0, 0x21a: 0x00c0, 0x21b: 0x00c0, 0x21c: 0x00c0, 0x21d: 0x00c0,
	0x21e: 0x00c0, 0x21f: 0x00c0, 0x220: 0x00c0, 0x221: 0x00c0, 0x222: 0x00c0, 0x223: 0x00c0,
	0x224: 0x00c0, 0x225: 0x00c0, 0x226: 0x00c0, 0x227: 0x00c0, 0x228: 0x00c0, 0x229: 0x00c0,
	0x22a: 0x00c0, 0x22b: 0x00c0, 0x22c: 0x00c0, 0x22d: 0x00c0, 0x22e: 0x00c0, 0x22f: 0x00c0,
	0x230: 0x00c0, 0x231: 0x0080, 0x232: 0x0080, 0x233: 0x0080, 0x234: 0x00c0, 0x235: 0x00c0,
	0x236: 0x00c0, 0x237: 0x00c0, 0x238: 0x00c0, 0x239: 0x00c0, 0x23a: 0x00c0, 0x23b: 0x00c0,
	0x23c: 0x00c0, 0x23d: 0x00c0, 0x23e: 0x00c0, 0x23f: 0x00c0,
	// Block 0x9, offset 0x240
	0x240: 0x00c0, 0x241: 0x00c0, 0x242: 0x00c0, 0x243: 0x00c0, 0x244: 0x00c0, 0x245: 0x00c0,
	0x246: 0x00c0, 0x247: 0x00c0, 0x248: 0x00c0, 0x249: 0x00c0, 0x24a: 0x00c0, 0x24b: 0x00c0,
	0x24c: 0x00c0, 0x24d: 0x00c0, 0x24e: 0x00c0, 0x24f: 0x00c0, 0x250: 0x00c0, 0x251: 0x00c0,
	0x252: 0x00c0, 0x253: 0x00c0, 0x254: 0x00c0, 0x255: 0x00c0, 0x256: 0x00c0, 0x257: 0x00c0,
	0x258: 0x00c0, 0x259: 0x00c0, 0x25a: 0x00c0, 0x25b: 0x00c0, 0x25c: 0x00c0, 0x25d: 0x00c0,
	0x25e: 0x00c0, 0x25f: 0x00c0, 0x260: 0x00c0, 0x261: 0x00c0, 0x262: 0x00c0, 0x263: 0x00c0,
	0x264: 0x00c0, 0x265: 0x00c0, 0x266: 0x00c0, 0x267: 0x00c0, 0x268: 0x00c0, 0x269: 0x00c0,
	0x26a: 0x00c0, 0x26b: 0x00c0, 0x26c: 0x00c0, 0x26d: 0x00c0, 0x26e: 0x00c0, 0x26f: 0x00c0,
	0x270: 0x0080, 0x271: 0x0080, 0x272: 0x0080, 0x273: 0x0080, 0x274: 0x0080, 0x275: 0x0080,
	0x276: 0x0080, 0x277: 0x0080, 0x278: 0x0080, 0x279: 0x00c0, 0x27a: 0x00c0, 0x27b: 0x00c0,
	0x27c: 0x00c0, 0x27d: 0x00c0, 0x27e: 0x00c0, 0x27f: 0x00c0,
	// Block 0xa, offset 0x280
	0x280: 0x00c0, 0x281: 0x00c0, 0x282: 0x0080, 0x283: 0x0080, 0x284: 0x0080, 0x285: 0x0080,
	0x286: 0x00c0, 0x287: 0x00c0, 0x288: 0x00c0, 0x289: 0x00c0, 0x28a: 0x00c0, 0x28b: 0x00c0,
	0x28c: 0x00c0, 0x28d: 0x00c0, 0x28e: 0x00c0, 0x28f: 0x00c0, 0x290: 0x00c0, 0x291: 0x00c0,
	0x292: 0x0080, 0x293: 0x0080, 0x294: 0x0080, 0x295: 0x0080, 0x296: 0x0080, 0x297: 0x0080,
	0x298: 0x0080, 0x299: 0x0080, 0x29a: 0x0080, 0x29b: 0x0080, 0x29c: 0x0080, 0x29d: 0x0080,
	0x29e: 0x0080, 0x29f: 0x0080, 0x2a0: 0x0080, 0x2a1: 0x0080, 0x2a2: 0x0080, 0x2a3: 0x0080,
	0x2a4: 0x0080, 0x2a5: 0x0080, 0x2a6: 0x0080, 0x2a7: 0x0080, 0x2a8: 0x0080, 0x2a9: 0x0080,
	0x2aa: 0x0080, 0x2ab: 0x0080, 0x2ac: 0x00c0, 0x2ad: 0x0080, 0x2ae: 0x00c0, 0x2af: 0x0080,
	0x2b0: 0x0080, 0x2b1: 0x0080, 0x2b2: 0x0080, 0x2b3: 0x0080, 0x2b4: 0x0080, 0x2b5: 0x0080,
	0x2b6: 0x0080, 0x2b7: 0x0080, 0x2b8: 0x0080, 0x2b9: 0x0080, 0x2ba: 0x0080, 0x2bb: 0x0080,
	0x2bc: 0x0080, 0x2bd: 0x0080, 0x2be: 0x0080, 0x2bf: 0x0080,
	// Block 0xb, offset 0x2c0
	0x2c0: 0x00c3, 0x2c1: 0x00c3, 0x2c2: 0x00c3, 0x2c3: 0x00c3, 0x2c4: 0x00c3, 0x2c5: 0x00c3,
	0x2c6: 0x00c3, 0x2c7: 0x00c3, 0x2c8: 0x00c3, 0x2c9: 0x00c3, 0x2ca: 0x00c3, 0x2cb: 0x00c3,
	0x2cc: 0x00c3, 0x2cd: 0x00c3, 0x2ce: 0x00c3, 0x2cf: 0x00c3, 0x2d0: 0x00c3, 0x2d1: 0x00c3,
	0x2d2: 0x00c3, 0x2d3: 0x00c3, 0x2d4: 0x00c3, 0x2d5: 0x00c3, 0x2d6: 0x00c3, 0x2d7: 0x00c3,
	0x2d8: 0x00c3, 0x2d9: 0x00c3, 0x2da: 0x00c3, 0x2db: 0x00c3, 0x2dc: 0x00c3, 0x2dd: 0x00c3,
	0x2de: 0x00c3, 0x2df: 0x00c3, 0x2e0: 0x00c3, 0x2e1: 0x00c3, 0x2e2: 0x00c3, 0x2e3: 0x00c3,
	0x2e4: 0x00c3, 0x2e5: 0x00c3, 0x2e6: 0x00c3, 0x2e7: 0x00c3, 0x2e8: 0x00c3, 0x2e9: 0x00c3,
	0x2ea: 0x00c3, 0x2eb: 0x00c3, 0x2ec: 0x00c3, 0x2ed: 0x00c3, 0x2ee: 0x00c3, 0x2ef: 0x00c3,
	0x2f0: 0x00c3, 0x2f1: 0x00c3, 0x2f2: 0x00c3, 0x2f3: 0x00c3, 0x2f4: 0x00c3, 0x2f5: 0x00c3,
	0x2f6: 0x00c3, 0x2f7: 0x00c3, 0x2f8: 0x00c3, 0x2f9: 0x00c3, 0x2fa: 0x00c3, 0x2fb: 0x00c3,
	0x2fc: 0x00c3, 0x2fd: 0x00c3, 0x2fe: 0x00c3, 0x2ff: 0x00c3,
	// Block 0xc, offset 0x300
	0x300: 0x0083, 0x301: 0x0083, 0x302: 0x00c3, 0x303: 0x0083, 0x304: 0x0083, 0x305: 0x00c3,
	0x306: 0x00c3, 0x307: 0x00c3, 0x308: 0x00c3, 0x309: 0x00c3, 0x30a: 0x00c3, 0x30b: 0x00c3,
	0x30c: 0x00c3, 0x30d: 0x00c3, 0x30e: 0x00c3, 0x30f: 0x0040, 0x310: 0x00c3, 0x311: 0x00c3,
	0x312: 0x00c3, 0x313: 0x00c3, 0x314: 0x00c3, 0x315: 0x00c3, 0x316: 0x00c3, 0x317: 0x00c3,
	0x318: 0x00c3, 0x319: 0x00c3, 0x31a: 0x00c3, 0x31b: 0x00c3, 0x31c: 0x00c3, 0x31d: 0x00c3,
	0x31e: 0x00c3, 0x31f: 0x00c3, 0x320: 0x00c3, 0x321: 0x00c3, 0x322: 0x00c3, 0x323: 0x00c3,
	0x324: 0x00c3, 0x325: 0x00c3, 0x326: 0x00c3, 0x327: 0x00c3, 0x328: 0x00c3, 0x329: 0x00c3,
	0x32a: 0x00c3, 0x32b: 0x00c3, 0x32c: 0x00c3, 0x32d: 0x00c3, 0x32e: 0x00c3, 0x32f: 0x00c3,
	0x330: 0x00c8, 0x331: 0x00c8, 0x332: 0x00c8, 0x333: 0x00c8, 0x334: 0x0080, 0x335: 0x0050,
	0x336: 0x00c8, 0x337: 0x00c8, 0x33a: 0x0088, 0x33b: 0x00c8,
	0x33c: 0x00c8, 0x33d: 0x00c8, 0x33e: 0x0080, 0x33f: 0x00c8,
	// Block 0xd, offset 0x340
	0x344: 0x0088, 0x345: 0x0080,
	0x346: 0x00c8, 0x347: 0x0080, 0x348: 0x00c8, 0x349: 0x00c8, 0x34a: 0x00c8,
	0x34c: 0x00c8, 0x34e: 0x00c8, 0x34f: 0x00c8, 0x350: 0x00c8, 0x351: 0x00c8,
	0x352: 0x00c8, 0x353: 0x00c8, 0x354: 0x00c8, 0x355: 0x00c8, 0x356: 0x00c8, 0x357: 0x00c8,
	0x358: 0x00c8, 0x359: 0x00c8, 0x35a: 0x00c8, 0x35b: 0x00c8, 0x35c: 0x00c8, 0x35d: 0x00c8,
	0x35e: 0x00c8, 0x35f: 0x00c8, 0x360: 0x00c8, 0x361: 0x00c8, 0x363: 0x00c8,
	0x364: 0x00c8, 0x365: 0x00c8, 0x366: 0x00c8, 0x367: 0x00c8, 0x368: 0x00c8, 0x369: 0x00c8,
	0x36a: 0x00c8, 0x36b: 0x00c8, 0x36c: 0x00c8, 0x36d: 0x00c8, 0x36e: 0x00c8, 0x36f: 0x00c8,
	0x370: 0x00c8, 0x371: 0x00c8, 0x372: 0x00c8, 0x373: 0x00c8, 0x374: 0x00c8, 0x375: 0x00c8,
	0x376: 0x00c8, 0x377: 0x00c8, 0x378: 0x00c8, 0x379: 0x00c8, 0x37a: 0x00c8, 0x37b: 0x00c8,
	0x37c: 0x00c8, 0x37d: 0x00c8, 0x37e: 0x00c8, 0x37f: 0x00c8,
	// Block 0xe, offset 0x380
	0x380: 0x00c8, 0x381: 0x00c8, 0x382: 0x00c8, 0x383: 0x00c8, 0x384: 0x00c8, 0x385: 0x00c8,
	0x386: 0x00c8, 0x387: 0x00c8, 0x388: 0x00c8, 0x389: 0x00c8, 0x38a: 0x00c8, 0x38b: 0x00c8,
	0x38c: 0x00c8, 0x38d: 0x00c8, 0x38e: 0x00c8, 0x38f: 0x00c8, 0x390: 0x0088, 0x391: 0x0088,
	0x392: 0x0088, 0x393: 0x0088, 0x394: 0x0088, 0x395: 0x0088, 0x396: 0x0088, 0x397: 0x00c8,
	0x398: 0x00c8, 0x399: 0x00c8, 0x39a: 0x00c8, 0x39b: 0x00c8, 0x39c: 0x00c8, 0x39d: 0x00c8,
	0x39e: 0x00c8, 0x39f: 0x00c8, 0x3a0: 0x00c8, 0x3a1: 0x00c8, 0x3a2: 0x00c0, 0x3a3: 0x00c0,
	0x3a4: 0x00c0, 0x3a5: 0x00c0, 0x3a6: 0x00c0, 0x3a7: 0x00c0, 0x3a8: 0x00c0, 0x3a9: 0x00c0,
	0x3aa: 0x00c0, 0x3ab: 0x00c0, 0x3ac: 0x00c0, 0x3ad: 0x00c0, 0x3ae: 0x00c0, 0x3af: 0x00c0,
	0x3b0: 0x0088, 0x3b1: 0x0088, 0x3b2: 0x0088, 0x3b3: 0x00c8, 0x3b4: 0x0088, 0x3b5: 0x0088,
	0x3b6: 0x0088, 0x3b7: 0x00c8, 0x3b8: 0x00c8, 0x3b9: 0x0088, 0x3ba: 0x00c8, 0x3bb: 0x00c8,
	0x3bc: 0x00c8, 0x3bd: 0x00c8, 0x3be: 0x00c8, 0x3bf: 0x00c8,
	// Block 0xf, offset 0x3c0
	0x3c0: 0x00c0, 0x3c1: 0x00c0, 0x3c2: 0x0080, 0x3c3: 0x00c3, 0x3c4: 0x00c3, 0x3c5: 0x00c3,
	0x3c6: 0x00c3, 0x3c7: 0x00c3, 0x3c8: 0x0083, 0x3c9: 0x0083, 0x3ca: 0x00c0, 0x3cb: 0x00c0,
	0x3cc: 0x00c0, 0x3cd: 0x00c0, 0x3ce: 0x00c0, 0x3cf: 0x00c0, 0x3d0: 0x00c0, 0x3d1: 0x00c0,
	0x3d2: 0x00c0, 0x3d3: 0x00c0, 0x3d4: 0x00c0, 0x3d5: 0x00c0, 0x3d6: 0x00c0, 0x3d7: 0x00c0,
	0x3d8: 0x00c0, 0x3d9: 0x00c0, 0x3da: 0x00c0, 0x3db: 0x00c0, 0x3dc: 0x00c0, 0x3dd: 0x00c0,
	0x3de: 0x00c0, 0x3df: 0x00c0, 0x3e0: 0x00c0, 0x3e1: 0x00c0, 0x3e2: 0x00c0, 0x3e3: 0x00c0,
	0x3e4: 0x00c0, 0x3e5: 0x00c0, 0x3e6: 0x00c0, 0x3e7: 0x00c0, 0x3e8: 0x00c0, 0x3e9: 0x00c0,
	0x3ea: 0x00c0, 0x3eb: 0x00c0, 0x3ec: 0x00c0, 0x3ed: 0x00c0, 0x3ee: 0x00c0, 0x3ef: 0x00c0,
	0x3f0: 0x00c0, 0x3f1: 0x00c0, 0x3f2: 0x00c0, 0x3f3: 0x00c0, 0x3f4: 0x00c0, 0x3f5: 0x00c0,
	0x3f6: 0x00c0, 0x3f7: 0x00c0, 0x3f8: 0x00c0, 0x3f9: 0x00c0, 0x3fa: 0x00c0, 0x3fb: 0x00c0,
	0x3fc: 0x00c0, 0x3fd: 0x00c0, 0x3fe: 0x00c0, 0x3ff: 0x00c0,
	// Block 0x10, offset 0x400
	0x400: 0x00c0, 0x401: 0x00c0, 0x402: 0x00c0, 0x403: 0x00c0, 0x404: 0x00c0, 0x405: 0x00c0,
	0x406: 0x00c0, 0x407: 0x00c0, 0x408: 0x00c0, 0x409: 0x00c0, 0x40a: 0x00c0, 0x40b: 0x00c0,
	0x40c: 0x00c0, 0x40d: 0x00c0, 0x40e: 0x00c0, 0x40f: 0x00c0, 0x410: 0x00c0, 0x411: 0x00c0,
	0x412: 0x00c0, 0x413: 0x00c0, 0x414: 0x00c0, 0x415: 0x00c0, 0x416: 0x00c0, 0x417: 0x00c0,
	0x418: 0x00c0, 0x419: 0x00c0, 0x41a: 0x00c0, 0x41b: 0x00c0, 0x41c: 0x00c0, 0x41d: 0x00c0,
	0x41e: 0x00c0, 0x41f: 0x00c0, 0x420: 0x00c0, 0x421: 0x00c0, 0x422: 0x00c0, 0x423: 0x00c0,
	0x424: 0x00c0, 0x425: 0x00c0, 0x426: 0x00c0, 0x427: 0x00c0, 0x428: 0x00c0, 0x429: 0x00c0,
	0x42a: 0x00c0, 0x42b: 0x00c0, 0x42c: 0x00c0, 0x42d: 0x00c0, 0x42e: 0x00c0, 0x42f: 0x00c0,
	0x431: 0x00c0, 0x432: 0x00c0, 0x433: 0x00c0, 0x434: 0x00c0, 0x435: 0x00c0,
	0x436: 0x00c0, 0x437: 0x00c0, 0x438: 0x00c0, 0x439: 0x00c0, 0x43a: 0x00c0, 0x43b: 0x00c0,
	0x43c: 0x00c0, 0x43d: 0x00c0, 0x43e: 0x00c0, 0x43f: 0x00c0,
	// Block 0x11, offset 0x440
	0x440: 0x00c0, 0x441: 0x00c0, 0x442: 0x00c0, 0x443: 0x00c0, 0x444: 0x00c0, 0x445: 0x00c0,
	0x446: 0x00c0, 0x447: 0x00c0, 0x448: 0x00c0, 0x449: 0x00c0, 0x44a: 0x00c0, 0x44b: 0x00c0,
	0x44c: 0x00c0, 0x44d: 0x00c0, 0x44e: 0x00c0, 0x44f: 0x00c0, 0x450: 0x00c0, 0x451: 0x00c0,
	0x452: 0x00c0, 0x453: 0x00c0, 0x454: 0x00c0, 0x455: 0x00c0, 0x456: 0x00c0,
	0x459: 0x00c0, 0x45a: 0x0080, 0x45b: 0x0080, 0x45c: 0x0080, 0x45d: 0x0080,
	0x45e: 0x0080, 0x45f: 0x0080, 0x460: 0x00c0, 0x461: 0x00c0, 0x462: 0x00c0, 0x463: 0x00c0,
	0x464: 0x00c0, 0x465: 0x00c0, 0x466: 0x00c0, 0x467: 0x00c0, 0x468: 0x00c0, 0x469: 0x00c0,
	0x46a: 0x00c0, 0x46b: 0x00c0, 0x46c: 0x00c0, 0x46d: 0x00c0, 0x46e: 0x00c0, 0x46f: 0x00c0,
	0x470: 0x00c0, 0x471: 0x00c0, 0x472: 0x00c0, 0x473: 0x00c0, 0x474: 0x00c0, 0x475: 0x00c0,
	0x476: 0x00c0, 0x477: 0x00c0, 0x478: 0x00c0, 0x479: 0x00c0, 0x47a: 0x00c0, 0x47b: 0x00c0,
	0x47c: 0x00c0, 0x47d: 0x00c0, 0x47e: 0x00c0, 0x47f: 0x00c0,
	// Block 0x12, offset 0x480
	0x480: 0x00c0, 0x481: 0x00c0, 0x482: 0x00c0, 0x483: 0x00c0, 0x484: 0x00c0, 0x485: 0x00c0,
	0x486: 0x00c0, 0x487: 0x0080, 0x488: 0x00c0, 0x489: 0x0080, 0x48a: 0x0080,
	0x48d: 0x0080, 0x48e: 0x0080, 0x48f: 0x0080, 0x491: 0x00cb,
	0x492: 0x00cb, 0x493: 0x00cb, 0x494: 0x00cb, 0x495: 0x00cb, 0x496: 0x00cb, 0x497: 0x00cb,
	0x498: 0x00cb, 0x499: 0x00cb, 0x49a: 0x00cb, 0x49b: 0x00cb, 0x49c: 0x00cb, 0x49d: 0x00cb,
	0x49e: 0x00cb, 0x49f: 0x00cb, 0x4a0: 0x00cb, 0x4a1: 0x00cb, 0x4a2: 0x00cb, 0x4a3: 0x00cb,
	0x4a4: 0x00cb, 0x4a5: 0x00cb, 0x4a6: 0x00cb, 0x4a7: 0x00cb, 0x4a8: 0x00cb, 0x4a9: 0x00cb,
	0x4aa: 0x00cb, 0x4ab: 0x00cb, 0x4ac: 0x00cb, 0x4ad: 0x00cb, 0x4ae: 0x00cb, 0x4af: 0x00cb,
	0x4b0: 0x00cb, 0x4b1: 0x00cb, 0x4b2: 0x00cb, 0x4b3: 0x00cb, 0x4b4: 0x00cb, 0x4b5: 0x00cb,
	0x4b6: 0x00cb, 0x4b7: 0x00cb, 0x4b8: 0x00cb, 0x4b9: 0x00cb, 0x4ba: 0x00cb, 0x4bb: 0x00cb,
	0x4bc: 0x00cb, 0x4bd: 0x00cb, 0x4be: 0x008a, 0x4bf: 0x00cb,
	// Block 0x13, offset 0x4c0
	0x4c0: 0x008a, 0x4c1: 0x00cb, 0x4c2: 0x00cb, 0x4c3: 0x008a, 0x4c4: 0x00cb, 0x4c5: 0x00cb,
	0x4c6: 0x008a, 0x4c7: 0x00cb,
	0x4d0: 0x00ca, 0x4d1: 0x00ca,
	0x4d2: 0x00ca, 0x4d3: 0x00ca, 0x4d4: 0x00ca, 0x4d5: 0x00ca, 0x4d6: 0x00ca, 0x4d7: 0x00ca,
	0x4d8: 0x00ca, 0x4d9: 0x00ca, 0x4da: 0x00ca, 0x4db: 0x00ca, 0x4dc: 0x00ca, 0x4dd: 0x00ca,
	0x4de: 0x00ca, 0x4df: 0x00ca, 0x4e0: 0x00ca, 0x4e1: 0x00ca, 0x4e2: 0x00ca, 0x4e3: 0x00ca,
	0x4e4: 0x00ca, 0x4e5: 0x00ca, 0x4e6: 0x00ca, 0x4e7: 0x00ca, 0x4e8: 0x00ca, 0x4e9: 0x00ca,
	0x4ea: 0x00ca, 0x4ef: 0x00ca,
	0x4f0: 0x00ca, 0x4f1: 0x00ca, 0x4f2: 0x00ca, 0x4f3: 0x0051, 0x4f4: 0x0051,
	// Block 0x14, offset 0x500
	0x500: 0x0040, 0x501: 0x0040, 0x502: 0x0040, 0x503: 0x0040, 0x504: 0x0040, 0x505: 0x0040,
	0x506: 0x0080, 0x507: 0x0080, 0x508: 0x0080, 0x509: 0x0080, 0x50a: 0x0080, 0x50b: 0x0080,
	0x50c: 0x0080, 0x50d: 0x0080, 0x50e: 0x0080, 0x50f: 0x0080, 0x510: 0x00c3, 0x511: 0x00c3,
	0x512: 0x00c3, 0x513: 0x00c3, 0x514: 0x00c3, 0x515: 0x00c3, 0x516: 0x00c3, 0x517: 0x00c3,
	0x518: 0x00c3, 0x519: 0x00c3, 0x51a: 0x00c3, 0x51b: 0x0080, 0x51c: 0x0040,
	0x51e: 0x0080, 0x51f: 0x0080, 0x520: 0x00c2, 0x521: 0x00c0, 0x522: 0x00c4, 0x523: 0x00c4,
	0x524: 0x00c4, 0x525: 0x00c4, 0x526: 0x00c2, 0x527: 0x00c4, 0x528: 0x00c2, 0x529: 0x00c4,
	0x52a: 0x00c2, 0x52b: 0x00c2, 0x52c: 0x00c2, 0x52d: 0x00c2, 0x52e: 0x00c2, 0x52f: 0x00c4,
	0x530: 0x00c4, 0x531: 0x00c4, 0x532: 0x00c4, 0x533: 0x00c2, 0x534: 0x00c2, 0x535: 0x00c2,
	0x536: 0x00c2, 0x537: 0x00c2, 0x538: 0x00c2, 0x539: 0x00c2, 0x53a: 0x00c2, 0x53b: 0x00c2,
	0x53c: 0x00c2, 0x53d: 0x00c2, 0x53e: 0x00c2, 0x53f: 0x00c2,
	// Block 0x15, offset 0x540
	0x540: 0x0040, 0x541: 0x00c2, 0x542: 0x00c2, 0x543: 0x00c2, 0x544: 0x00c2, 0x545: 0x00c2,
	0x546: 0x00c2, 0x547: 0x00c2, 0x548: 0x00c4, 0x549: 0x00c2, 0x54a: 0x00c2, 0x54b: 0x00c3,
	0x54c: 0x00c3, 0x54d: 0x00c3, 0x54e: 0x00c3, 0x54f: 0x00c3, 0x550: 0x00c3, 0x551: 0x00c3,
	0x552: 0x00c3, 0x553: 0x00c3, 0x554: 0x00c3, 0x555: 0x00c3, 0x556: 0x00c3, 0x557: 0x00c3,
	0x558: 0x00c3, 0x559: 0x00c3, 0x55a: 0x00c3, 0x55b: 0x00c3, 0x55c: 0x00c3, 0x55d: 0x00c3,
	0x55e: 0x00c3, 0x55f: 0x00c3, 0x560: 0x0053, 0x561: 0x0053, 0x562: 0x0053, 0x563: 0x0053,
	0x564: 0x0053, 0x565: 0x0053, 0x566: 0x0053, 0x567: 0x0053, 0x568: 0x0053, 0x569: 0x0053,
	0x56a: 0x0080, 0x56b: 0x0080, 0x56c: 0x0080, 0x56d: 0x0080, 0x56e: 0x00c2, 0x56f: 0x00c2,
	0x570: 0x00c3, 0x571: 0x00c4, 0x572: 0x00c4, 0x573: 0x00c4, 0x574: 0x00c0, 0x575: 0x0084,
	0x576: 0x0084, 0x577: 0x0084, 0x578: 0x0082, 0x579: 0x00c2, 0x57a: 0x00c2, 0x57b: 0x00c2,
	0x57c: 0x00c2, 0x57d: 0x00c2, 0x57e: 0x00c2, 0x57f: 0x00c2,
	// Block 0x16, offset 0x580
	0x580: 0x00c2, 0x581: 0x00c2, 0x582: 0x00c2, 0x583: 0x00c2, 0x584: 0x00c2, 0x585: 0x00c2,
	0x586: 0x00c2, 0x587: 0x00c2, 0x588: 0x00c4, 0x589: 0x00c4, 0x58a: 0x00c4, 0x58b: 0x00c4,
	0x58c: 0x00c4, 0x58d: 0x00c4, 0x58e: 0x00c4, 0x58f: 0x00c4, 0x590: 0x00c4, 0x591: 0x00c4,
	0x592: 0x00c4, 0x593: 0x00c4, 0x594: 0x00c4, 0x595: 0x00c4, 0x596: 0x00c4, 0x597: 0x00c4,
	0x598: 0x00c4, 0x599: 0x00c4, 0x59a: 0x00c2, 0x59b: 0x00c2, 0x59c: 0x00c2, 0x59d: 0x00c2,
	0x59e: 0x00c2, 0x59f: 0x00c2, 0x5a0: 0x00c2, 0x5a1: 0x00c2, 0x5a2: 0x00c2, 0x5a3: 0x00c2,
	0x5a4: 0x00c2, 0x5a5: 0x00c2, 0x5a6: 0x00c2, 0x5a7: 0x00c2, 0x5a8: 0x00c2, 0x5a9: 0x00c2,
	0x5aa: 0x00c2, 0x5ab: 0x00c2, 0x5ac: 0x00c2, 0x5ad: 0x00c2, 0x5ae: 0x00c2, 0x5af: 0x00c2,
	0x5b0: 0x00c2, 0x5b1: 0x00c2, 0x5b2: 0x00c2, 0x5b3: 0x00c2, 0x5b4: 0x00c2, 0x5b5: 0x00c2,
	0x5b6: 0x00c2, 0x5b7: 0x00c2, 0x5b8: 0x00c2, 0x5b9: 0x00c2, 0x5ba: 0x00c2, 0x5bb: 0x00c2,
	0x5bc: 0x00c2, 0x5bd: 0x00c2, 0x5be: 0x00c2, 0x5bf: 0x00c2,
	// Block 0x17, offset 0x5c0
	0x5c0: 0x00c4, 0x5c1: 0x00c2, 0x5c2: 0x00c2, 0x5c3: 0x00c4, 0x5c4: 0x00c4, 0x5c5: 0x00c4,
	0x5c6: 0x00c4, 0x5c7: 0x00c4, 0x5c8: 0x00c4, 0x5c9: 0x00c4, 0x5ca: 0x00c4, 0x5cb: 0x00c4,
	0x5cc: 0x00c2, 0x5cd: 0x00c4, 0x5ce: 0x00c2, 0x5cf: 0x00c4, 0x5d0: 0x00c2, 0x5d1: 0x00c2,
	0x5d2: 0x00c4, 0x5d3: 0x00c4, 0x5d4: 0x0080, 0x5d5: 0x00c4, 0x5d6: 0x00c3, 0x5d7: 0x00c3,
	0x5d8: 0x00c3, 0x5d9: 0x00c3, 0x5da: 0x00c3, 0x5db: 0x00c3, 0x5dc: 0x00c3, 0x5dd: 0x0040,
	0x5de: 0x0080, 0x5df: 0x00c3, 0x5e0: 0x00c3, 0x5e1: 0x00c3, 0x5e2: 0x00c3, 0x5e3: 0x00c3,
	0x5e4: 0x00c3, 0x5e5: 0x00c0, 0x5e6: 0x00c0, 0x5e7: 0x00c3, 0x5e8: 0x00c3, 0x5e9: 0x0080,
	0x5ea: 0x00c3, 0x5eb: 0x00c3, 0x5ec: 0x00c3, 0x5ed: 0x00c3, 0x5ee: 0x00c4, 0x5ef: 0x00c4,
	0x5f0: 0x0054, 0x5f1: 0x0054, 0x5f2: 0x0054, 0x5f3: 0x0054, 0x5f4: 0x0054, 0x5f5: 0x0054,
	0x5f6: 0x0054, 0x5f7: 0x0054, 0x5f8: 0x0054, 0x5f9: 0x0054, 0x5fa: 0x00c2, 0x5fb: 0x00c2,
	0x5fc: 0x00c2, 0x5fd: 0x00c0, 0x5fe: 0x00c0, 0x5ff: 0x00c2,
	// Block 0x18, offset 0x600
	0x600: 0x0080, 0x601: 0x0080, 0x602: 0x0080, 0x603: 0x0080, 0x604: 0x0080, 0x605: 0x0080,
	0x606: 0x0080, 0x607: 0x0080, 0x608: 0x0080, 0x609: 0x0080, 0x60a: 0x0080, 0x60b: 0x0080,
	0x60c: 0x0080, 0x60d: 0x0080, 0x60f: 0x0040, 0x610: 0x00c4, 0x611: 0x00c3,
	0x612: 0x00c2, 0x613: 0x00c2, 0x614: 0x00c2, 0x615: 0x00c4, 0x616: 0x00c4, 0x617: 0x00c4,
	0x618: 0x00c4, 0x619: 0x00c4, 0x61a: 0x00c2, 0x61b: 0x00c2, 0x61c: 0x00c2, 0x61d: 0x00c2,
	0x61e: 0x00c4, 0x61f: 0x00c2, 0x620: 0x00c2, 0x621: 0x00c2, 0x622: 0x00c2, 0x623: 0x00c2,
	0x624: 0x00c2, 0x625: 0x00c2, 0x626: 0x00c2, 0x627: 0x00c2, 0x628: 0x00c4, 0x629: 0x00c2,
	0x62a: 0x00c4, 0x62b: 0x00c2, 0x62c: 0x00c4, 0x62d: 0x00c2, 0x62e: 0x00c2, 0x62f: 0x00c4,
	0x630: 0x00c3, 0x631: 0x00c3, 0x632: 0x00c3, 0x633: 0x00c3, 0x634: 0x00c3, 0x635: 0x00c3,
	0x636: 0x00c3, 0x637: 0x00c3, 0x638: 0x00c3, 0x639: 0x00c3, 0x63a: 0x00c3, 0x63b: 0x00c3,
	0x63c: 0x00c3, 0x63d: 0x00c3, 0x63e: 0x00c3, 0x63f: 0x00c3,
	// Block 0x19, offset 0x640
	0x640: 0x00c3, 0x641: 0x00c3, 0x642: 0x00c3, 0x643: 0x00c3, 0x644: 0x00c3, 0x645: 0x00c3,
	0x646: 0x00c3, 0x647: 0x00c3, 0x648: 0x00c3, 0x649: 0x00c3, 0x64a: 0x00c3,
	0x64d: 0x00c4, 0x64e: 0x00c2, 0x64f: 0x00c2, 0x650: 0x00c2, 0x651: 0x00c2,
	0x652: 0x00c2, 0x653: 0x00c2, 0x654: 0x00c2, 0x655: 0x00c2, 0x656: 0x00c2, 0x657: 0x00c2,
	0x658: 0x00c2, 0x659: 0x00c4, 0x65a: 0x00c4, 0x65b: 0x00c4, 0x65c: 0x00c2, 0x65d: 0x00c2,
	0x65e: 0x00c2, 0x65f: 0x00c2, 0x660: 0x00c2, 0x661: 0x00c2, 0x662: 0x00c2, 0x663: 0x00c2,
	0x664: 0x00c2, 0x665: 0x00c2, 0x666: 0x00c2, 0x667: 0x00c2, 0x668: 0x00c2, 0x669: 0x00c2,
	0x66a: 0x00c2, 0x66b: 0x00c4, 0x66c: 0x00c4, 0x66d: 0x00c2, 0x66e: 0x00c2, 0x66f: 0x00c2,
	0x670: 0x00c2, 0x671: 0x00c4, 0x672: 0x00c2, 0x673: 0x00c4, 0x674: 0x00c4, 0x675: 0x00c2,
	0x676: 0x00c2, 0x677: 0x00c2, 0x678: 0x00c4, 0x679: 0x00c4, 0x67a: 0x00c2, 0x67b: 0x00c2,
	0x67c: 0x00c2, 0x67d: 0x00c2, 0x67e: 0x00c2, 0x67f: 0x00c2,
	// Block 0x1a, offset 0x680
	0x680: 0x00c0, 0x681: 0x00c0, 0x682: 0x00c0, 0x683: 0x00c0, 0x684: 0x00c0, 0x685: 0x00c0,
	0x686: 0x00c0, 0x687: 0x00c0, 0x688: 0x00c0, 0x689: 0x00c0, 0x68a: 0x00c0, 0x68b: 0x00c0,
	0x68c: 0x00c0, 0x68d: 0x00c0, 0x68e: 0x00c0, 0x68f: 0x00c0, 0x690: 0x00c0, 0x691: 0x00c0,
	0x692: 0x00c0, 0x693: 0x00c0, 0x694: 0x00c0, 0x695: 0x00c0, 0x696: 0x00c0, 0x697: 0x00c0,
	0x698: 0x00c0, 0x699: 0x00c0, 0x69a: 0x00c0, 0x69b: 0x00c0, 0x69c: 0x00c0, 0x69d: 0x00c0,
	0x69e: 0x00c0, 0x69f: 0x00c0, 0x6a0: 0x00c0, 0x6a1: 0x00c0, 0x6a2: 0x00c0, 0x6a3: 0x00c0,
	0x6a4: 0x00c0, 0x6a5: 0x00c0, 0x6a6: 0x00c3, 0x6a7: 0x00c3, 0x6a8: 0x00c3, 0x6a9: 0x00c3,
	0x6aa: 0x00c3, 0x6ab: 0x00c3, 0x6ac: 0x00c3, 0x6ad: 0x00c3, 0x6ae: 0x00c3, 0x6af: 0x00c3,
	0x6b0: 0x00c3, 0x6b1: 0x00c0,
	// Block 0x1b, offset 0x6c0
	0x6c0: 0x00c0, 0x6c1: 0x00c0, 0x6c2: 0x00c0, 0x6c3: 0x00c0, 0x6c4: 0x00c0, 0x6c5: 0x00c0,
	0x6c6: 0x00c0, 0x6c7: 0x00c0, 0x6c8: 0x00c0, 0x6c9: 0x00c0, 0x6ca: 0x00c2, 0x6cb: 0x00c2,
	0x6cc: 0x00c2, 0x6cd: 0x00c2, 0x6ce: 0x00c2, 0x6cf: 0x00c2, 0x6d0: 0x00c2, 0x6d1: 0x00c2,
	0x6d2: 0x00c2, 0x6d3: 0x00c2, 0x6d4: 0x00c2, 0x6d5: 0x00c2, 0x6d6: 0x00c2, 0x6d7: 0x00c2,
	0x6d8: 0x00c2, 0x6d9: 0x00c2, 0x6da: 0x00c2, 0x6db: 0x00c2, 0x6dc: 0x00c2, 0x6dd: 0x00c2,
	0x6de: 0x00c2, 0x6df: 0x00c2, 0x6e0: 0x00c2, 0x6e1: 0x00c2, 0x6e2: 0x00c2, 0x6e3: 0x00c2,
	0x6e4: 0x00c2, 0x6e5: 0x00c2, 0x6e6: 0x00c2, 0x6e7: 0x00c2, 0x6e8: 0x00c2, 0x6e9: 0x00c2,
	0x6ea: 0x00c2, 0x6eb: 0x00c3, 0x6ec: 0x00c3, 0x6ed: 0x00c3, 0x6ee: 0x00c3, 0x6ef: 0x00c3,
	0x6f0: 0x00c3, 0x6f1: 0x00c3, 0x6f2: 0x00c3, 0x6f3: 0x00c3, 0x6f4: 0x00c0, 0x6f5: 0x00c0,
	0x6f6: 0x0080, 0x6f7: 0x0080, 0x6f8: 0x0080, 0x6f9: 0x0080, 0x6fa: 0x0040,
	0x6fd: 0x00c3, 0x6fe: 0x0080, 0x6ff: 0x0080,
	// Block 0x1c, offset 0x700
	0x700: 0x00c0, 0x701: 0x00c0, 0x702: 0x00c0, 0x703: 0x00c0, 0x704: 0x00c0, 0x705: 0x00c0,
	0x706: 0x00c0, 0x707: 0x00c0, 0x708: 0x00c0, 0x709: 0x00c0, 0x70a: 0x00c0, 0x70b: 0x00c0,
	0x70c: 0x00c0, 0x70d: 0x00c0, 0x70e: 0x00c0, 0x70f: 0x00c0, 0x710: 0x00c0, 0x711: 0x00c0,
	0x712: 0x00c0, 0x713: 0x00c0, 0x714: 0x00c0, 0x715: 0x00c0, 0x716: 0x00c3, 0x717: 0x00c3,
	0x718: 0x00c3, 0x719: 0x00c3, 0x71a: 0x00c0, 0x71b: 0x00c3, 0x71c: 0x00c3, 0x71d: 0x00c3,
	0x71e: 0x00c3, 0x71f: 0x00c3, 0x720: 0x00c3, 0x721: 0x00c3, 0x722: 0x00c3, 0x723: 0x00c3,
	0x724: 0x00c0, 0x725: 0x00c3, 0x726: 0x00c3, 0x727: 0x00c3, 0x728: 0x00c0, 0x729: 0x00c3,
	0x72a: 0x00c3, 0x72b: 0x00c3, 0x72c: 0x00c3, 0x72d: 0x00c3,
	0x730: 0x0080, 0x731: 0x0080, 0x732: 0x0080, 0x733: 0x0080, 0x734: 0x0080, 0x735: 0x0080,
	0x736: 0x0080, 0x737: 0x0080, 0x738: 0x0080, 0x739: 0x0080, 0x73a: 0x0080, 0x73b: 0x0080,
	0x73c: 0x0080, 0x73d: 0x0080, 0x73e: 0x0080,
	// Block 0x1d, offset 0x740
	0x740: 0x00c4, 0x741: 0x00c2, 0x742: 0x00c2, 0x743: 0x00c2, 0x744: 0x00c2, 0x745: 0x00c2,
	0x746: 0x00c4, 0x747: 0x00c4, 0x748: 0x00c2, 0x749: 0x00c4, 0x74a: 0x00c2, 0x74b: 0x00c2,
	0x74c: 0x00c2, 0x74d: 0x00c2, 0x74e: 0x00c2, 0x74f: 0x00c2, 0x750: 0x00c2, 0x751: 0x00c2,
	0x752: 0x00c2, 0x753: 0x00c2, 0x754: 0x00c4, 0x755: 0x00c2, 0x756: 0x00c4, 0x757: 0x00c4,
	0x758: 0x00c4, 0x759: 0x00c3, 0x75a: 0x00c3, 0x75b: 0x00c3,
	0x75e: 0x0080, 0x760: 0x00c2, 0x761: 0x00c0, 0x762: 0x00c2, 0x763: 0x00c2,
	0x764: 0x00c2, 0x765: 0x00c2, 0x766: 0x00c0, 0x767: 0x00c4, 0x768: 0x00c2, 0x769: 0x00c4,
	0x76a: 0x00c4,
	// Block 0x1e, offset 0x780
	0x7a0: 0x00c2, 0x7a1: 0x00c2, 0x7a2: 0x00c2, 0x7a3: 0x00c2,
	0x7a4: 0x00c2, 0x7a5: 0x00c2, 0x7a6: 0x00c2, 0x7a7: 0x00c2, 0x7a8: 0x00c2, 0x7a9: 0x00c2,
	0x7aa: 0x00c4, 0x7ab: 0x00c4, 0x7ac: 0x00c4, 0x7ad: 0x00c0, 0x7ae: 0x00c4, 0x7af: 0x00c2,
	0x7b0: 0x00c2, 0x7b1: 0x00c4, 0x7b2: 0x00c4, 0x7b3: 0x00c2, 0x7b4: 0x00c2,
	0x7b6: 0x00c2, 0x7b7: 0x00c2, 0x7b8: 0x00c2, 0x7b9: 0x00c4, 0x7ba: 0x00c2, 0x7bb: 0x00c2,
	0x7bc: 0x00c2, 0x7bd: 0x00c2, 0x7be: 0x00c2, 0x7bf: 0x00c2,
	// Block 0x1f, offset 0x7c0
	0x7c0: 0x00c2, 0x7c1: 0x00c2, 0x7c2: 0x00c2, 0x7c3: 0x00c2, 0x7c4: 0x00c2, 0x7c5: 0x00c2,
	0x7c6: 0x00c2, 0x7c7: 0x00c2,
	0x7d3: 0x00c3, 0x7d4: 0x00c3, 0x7d5: 0x00c3, 0x7d6: 0x00c3, 0x7d7: 0x00c3,
	0x7d8: 0x00c3, 0x7d9: 0x00c3, 0x7da: 0x00c3, 0x7db: 0x00c3, 0x7dc: 0x00c3, 0x7dd: 0x00c3,
	0x7de: 0x00c3, 0x7df: 0x00c3, 0x7e0: 0x00c3, 0x7e1: 0x00c3, 0x7e2: 0x0040, 0x7e3: 0x00c3,
	0x7e4: 0x00c3, 0x7e5: 0x00c3, 0x7e6: 0x00c3, 0x7e7: 0x00c3, 0x7e8: 0x00c3, 0x7e9: 0x00c3,
	0x7ea: 0x00c3, 0x7eb: 0x00c3, 0x7ec: 0x00c3, 0x7ed: 0x00c3, 0x7ee: 0x00c3, 0x7ef: 0x00c3,
	0x7f0: 0x00c3, 0x7f1: 0x00c3, 0x7f2: 0x00c3, 0x7f3: 0x00c3, 0x7f4: 0x00c3, 0x7f5: 0x00c3,
	0x7f6: 0x00c3, 0x7f7: 0x00c3, 0x7f8: 0x00c3, 0x7f9: 0x00c3, 0x7fa: 0x00c3, 0x7fb: 0x00c3,
	0x7fc: 0x00c3, 0x7fd: 0x00c3, 0x7fe: 0x00c3, 0x7ff: 0x00c3,
	// Block 0x20, offset 0x800
	0x800: 0x00c3, 0x801: 0x00c3, 0x802: 0x00c3, 0x803: 0x00c0, 0x804: 0x00c0, 0x805: 0x00c0,
	0x806: 0x00c0, 0x807: 0x00c0, 0x808: 0x00c0, 0x809: 0x00c0, 0x80a: 0x00c0, 0x80b: 0x00c0,
	0x80c: 0x00c0, 0x80d: 0x00c0, 0x80e: 0x00c0, 0x80f: 0x00c0, 0x810: 0x00c0, 0x811: 0x00c0,
	0x812: 0x00c0, 0x813: 0x00c0, 0x814: 0x00c0, 0x815: 0x00c0, 0x816: 0x00c0, 0x817: 0x00c0,
	0x818: 0x00c0, 0x819: 0x00c0, 0x81a: 0x00c0, 0x81b: 0x00c0, 0x81c: 0x00c0, 0x81d: 0x00c0,
	0x81e: 0x00c0, 0x81f: 0x00c0, 0x820: 0x00c0, 0x821: 0x00c0, 0x822: 0x00c0, 0x823: 0x00c0,
	0x824: 0x00c0, 0x825: 0x00c0, 0x826: 0x00c0, 0x827: 0x00c0, 0x828: 0x00c0, 0x829: 0x00c0,
	0x82a: 0x00c0, 0x82b: 0x00c0, 0x82c: 0x00c0, 0x82d: 0x00c0, 0x82e: 0x00c0, 0x82f: 0x00c0,
	0x830: 0x00c0, 0x831: 0x00c0, 0x832: 0x00c0, 0x833: 0x00c0, 0x834: 0x00c0, 0x835: 0x00c0,
	0x836: 0x00c0, 0x837: 0x00c0, 0x838: 0x00c0, 0x839: 0x00c0, 0x83a: 0x00c3, 0x83b: 0x00c0,
	0x83c: 0x00c3, 0x83d: 0x00c0, 0x83e: 0x00c0, 0x83f: 0x00c0,
	// Block 0x21, offset 0x840
	0x840: 0x00c0, 0x841: 0x00c3, 0x842: 0x00c3, 0x843: 0x00c3, 0x844: 0x00c3, 0x845: 0x00c3,
	0x846: 0x00c3, 0x847: 0x00c3, 0x848: 0x00c3, 0x849: 0x00c0, 0x84a: 0x00c0, 0x84b: 0x00c0,
	0x84c: 0x00c0, 0x84d: 0x00c6, 0x84e: 0x00c0, 0x84f: 0x00c0, 0x850: 0x00c0, 0x851: 0x00c3,
	0x852: 0x00c3, 0x853: 0x00c3, 0x854: 0x00c3, 0x855: 0x00c3, 0x856: 0x00c3, 0x857: 0x00c3,
	0x858: 0x0080, 0x859: 0x0080, 0x85a: 0x0080, 0x85b: 0x0080, 0x85c: 0x0080, 0x85d: 0x0080,
	0x85e: 0x0080, 0x85f: 0x0080, 0x860: 0x00c0, 0x861: 0x00c0, 0x862: 0x00c3, 0x863: 0x00c3,
	0x864: 0x0080, 0x865: 0x0080, 0x866: 0x00c0, 0x867: 0x00c0, 0x868: 0x00c0, 0x869: 0x00c0,
	0x86a: 0x00c0, 0x86b: 0x00c0, 0x86c: 0x00c0, 0x86d: 0x00c0, 0x86e: 0x00c0, 0x86f: 0x00c0,
	0x870: 0x0080, 0x871: 0x00c0, 0x872: 0x00c0, 0x873: 0x00c0, 0x874: 0x00c0, 0x875: 0x00c0,
	0x876: 0x00c0, 0x877: 0x00c0, 0x878: 0x00c0, 0x879: 0x00c0, 0x87a: 0x00c0, 0x87b: 0x00c0,
	0x87c: 0x00c0, 0x87d: 0x00c0, 0x87e: 0x00c0, 0x87f: 0x00c0,
	// Block 0x22, offset 0x880
	0x880: 0x00c0, 0x881: 0x00c3, 0x882: 0x00c0, 0x883: 0x00c0, 0x885: 0x00c0,
	0x886: 0x00c0, 0x887: 0x00c0, 0x888: 0x00c0, 0x889: 0x00c0, 0x88a: 0x00c0, 0x88b: 0x00c0,
	0x88c: 0x00c0, 0x88f: 0x00c0, 0x890: 0x00c0,
	0x893: 0x00c0, 0x894: 0x00c0, 0x895: 0x00c0, 0x896: 0x00c0, 0x897: 0x00c0,
	0x898: 0x00c0, 0x899: 0x00c0, 0x89a: 0x00c0, 0x89b: 0x00c0, 0x89c: 0x00c0, 0x89d: 0x00c0,
	0x89e: 0x00c0, 0x89f: 0x00c0, 0x8a0: 0x00c0, 0x8a1: 0x00c0, 0x8a2: 0x00c0, 0x8a3: 0x00c0,
	0x8a4: 0x00c0, 0x8a5: 0x00c0, 0x8a6: 0x00c0, 0x8a7: 0x00c0, 0x8a8: 0x00c0,
	0x8aa: 0x00c0, 0x8ab: 0x00c0, 0x8ac: 0x00c0, 0x8ad: 0x00c0, 0x8ae: 0x00c0, 0x8af: 0x00c0,
	0x8b0: 0x00c0, 0x8b2: 0x00c0,
	0x8b6: 0x00c0, 0x8b7: 0x00c0, 0x8b8: 0x00c0, 0x8b9: 0x00c0,
	0x8bc: 0x00c3, 0x8bd: 0x00c0, 0x8be: 0x00c0, 0x8bf: 0x00c0,
	// Block 0x23, offset 0x8c0
	0x8c0: 0x00c0, 0x8c1: 0x00c3, 0x8c2: 0x00c3, 0x8c3: 0x00c3, 0x8c4: 0x00c3,
	0x8c7: 0x00c0, 0x8c8: 0x00c0, 0x8cb: 0x00c0,
	0x8cc: 0x00c0, 0x8cd: 0x00c6, 0x8ce: 0x00c0,
	0x8d7: 0x00c0,
	0x8dc: 0x0080, 0x8dd: 0x0080,
	0x8df: 0x0080, 0x8e0: 0x00c0, 0x8e1: 0x00c0, 0x8e2: 0x00c3, 0x8e3: 0x00c3,
	0x8e6: 0x00c0, 0x8e7: 0x00c0, 0x8e8: 0x00c0, 0x8e9: 0x00c0,
	0x8ea: 0x00c0, 0x8eb: 0x00c0, 0x8ec: 0x00c0, 0x8ed: 0x00c0, 0x8ee: 0x00c0, 0x8ef: 0x00c0,
	0x8f0: 0x00c0, 0x8f1: 0x00c0, 0x8f2: 0x0080, 0x8f3: 0x0080, 0x8f4: 0x0080, 0x8f5: 0x0080,
	0x8f6: 0x0080, 0x8f7: 0x0080, 0x8f8: 0x0080, 0x8f9: 0x0080, 0x8fa: 0x0080, 0x8fb: 0x0080,
	0x8fc: 0x00c0, 0x8fd: 0x0080, 0x8fe: 0x00c3,
	// Block 0x24, offset 0x900
	0x901: 0x00c3, 0x902: 0x00c3, 0x903: 0x00c0, 0x905: 0x00c0,
	0x906: 0x00c0, 0x907: 0x00c0, 0x908: 0x00c0, 0x909: 0x00c0, 0x90a: 0x00c0,
	0x90f: 0x00c0, 0x910: 0x00c0,
	0x913: 0x00c0, 0x914: 0x00c0, 0x915: 0x00c0, 0x916: 0x00c0, 0x917: 0x00c0,
	0x918: 0x00c0, 0x919: 0x00c0, 0x91a: 0x00c0, 0x91b: 0x00c0, 0x91c: 0x00c0, 0x91d: 0x00c0,
	0x91e: 0x00c0, 0x91f: 0x00c0, 0x920: 0x00c0, 0x921: 0x00c0, 0x922: 0x00c0, 0x923: 0x00c0,
	0x924: 0x00c0, 0x925: 0x00c0, 0x926: 0x00c0, 0x927: 0x00c0, 0x928: 0x00c0,
	0x92a: 0x00c0, 0x92b: 0x00c0, 0x92c: 0x00c0, 0x92d: 0x00c0, 0x92e: 0x00c0, 0x92f: 0x00c0,
	0x930: 0x00c0, 0x932: 0x00c0, 0x933: 0x0080, 0x935: 0x00c0,
	0x936: 0x0080, 0x938: 0x00c0, 0x939: 0x00c0,
	0x93c: 0x00c3, 0x93e: 0x00c0, 0x93f: 0x00c0,
	// Block 0x25, offset 0x940
	0x940: 0x00c0, 0x941: 0x00c3, 0x942: 0x00c3,
	0x947: 0x00c3, 0x948: 0x00c3, 0x94b: 0x00c3,
	0x94c: 0x00c3, 0x94d: 0x00c6, 0x951: 0x00c3,
	0x959: 0x0080, 0x95a: 0x0080, 0x95b: 0x0080, 0x95c: 0x00c0,
	0x95e: 0x0080,
	0x966: 0x00c0, 0x967: 0x00c0, 0x968: 0x00c0, 0x969: 0x00c0,
	0x96a: 0x00c0, 0x96b: 0x00c0, 0x96c: 0x00c0, 0x96d: 0x00c0, 0x96e: 0x00c0, 0x96f: 0x00c0,
	0x970: 0x00c3, 0x971: 0x00c3, 0x972: 0x00c0, 0x973: 0x00c0, 0x974: 0x00c0, 0x975: 0x00c3,
	0x976: 0x0080,
	// Block 0x26, offset 0x980
	0x981: 0x00c3, 0x982: 0x00c3, 0x983: 0x00c0, 0x985: 0x00c0,
	0x986: 0x00c0, 0x987: 0x00c0, 0x988: 0x00c0, 0x989: 0x00c0, 0x98a: 0x00c0, 0x98b: 0x00c0,
	0x98c: 0x00c0, 0x98d: 0x00c0, 0x98f: 0x00c0, 0x990: 0x00c0, 0x991: 0x00c0,
	0x993: 0x00c0, 0x994: 0x00c0, 0x995: 0x00c0, 0x996: 0x00c0, 0x997: 0x00c0,
	0x998: 0x00c0, 0x999: 0x00c0, 0x99a: 0x00c0, 0x99b: 0x00c0, 0x99c: 0x00c0, 0x99d: 0x00c0,
	0x99e: 0x00c0, 0x99f: 0x00c0, 0x9a0: 0x00c0, 0x9a1: 0x00c0, 0x9a2: 0x00c0, 0x9a3: 0x00c0,
	0x9a4: 0x00c0, 0x9a5: 0x00c0, 0x9a6: 0x00c0, 0x9a7: 0x00c0, 0x9a8: 0x00c0,
	0x9aa: 0x00c0, 0x9ab: 0x00c0, 0x9ac: 0x00c0, 0x9ad: 0x00c0, 0x9ae: 0x00c0, 0x9af: 0x00c0,
	0x9b0: 0x00c0, 0x9b2: 0x00c0, 0x9b3: 0x00c0, 0x9b5: 0x00c0,
	0x9b6: 0x00c0, 0x9b7: 0x00c0, 0x9b8: 0x00c0, 0x9b9: 0x00c0,
	0x9bc: 0x00c3, 0x9bd: 0x00c0, 0x9be: 0x00c0, 0x9bf: 0x00c0,
	// Block 0x27, offset 0x9c0
	0x9c0: 0x00c0, 0x9c1: 0x00c3, 0x9c2: 0x00c3, 0x9c3: 0x00c3, 0x9c4: 0x00c3, 0x9c5: 0x00c3,
	0x9c7: 0x00c3, 0x9c8: 0x00c3, 0x9c9: 0x00c0, 0x9cb: 0x00c0,
	0x9cc: 0x00c0, 0x9cd: 0x00c6, 0x9d0: 0x00c0,
	0x9e0: 0x00c0, 0x9e1: 0x00c0, 0x9e2: 0x00c3, 0x9e3: 0x00c3,
	0x9e6: 0x00c0, 0x9e7: 0x00c0, 0x9e8: 0x00c0, 0x9e9: 0x00c0,
	0x9ea: 0x00c0, 0x9eb: 0x00c0, 0x9ec: 0x00c0, 0x9ed: 0x00c0, 0x9ee: 0x00c0, 0x9ef: 0x00c0,
	0x9f0: 0x0080, 0x9f1: 0x0080,
	0x9f9: 0x00c0, 0x9fa: 0x00c3, 0x9fb: 0x00c3,
	0x9fc: 0x00c3, 0x9fd: 0x00c3, 0x9fe: 0x00c3, 0x9ff: 0x00c3,
	// Block 0x28, offset 0xa00
	0xa01: 0x00c3, 0xa02: 0x00c0, 0xa03: 0x00c0, 0xa05: 0x00c0,
	0xa06: 0x00c0, 0xa07: 0x00c0, 0xa08: 0x00c0, 0xa09: 0x00c0, 0xa0a: 0x00c0, 0xa0b: 0x00c0,
	0xa0c: 0x00c0, 0xa0f: 0x00c0, 0xa10: 0x00c0,
	0xa13: 0x00c0, 0xa14: 0x00c0, 0xa15: 0x00c0, 0xa16: 0x00c0, 0xa17: 0x00c0,
	0xa18: 0x00c0, 0xa19: 0x00c0, 0xa1a: 0x00c0, 0xa1b: 0x00c0, 0xa1c: 0x00c0, 0xa1d: 0x00c0,
	0xa1e: 0x00c0, 0xa1f: 0x00c0, 0xa20: 0x00c0, 0xa21: 0x00c0, 0xa22: 0x00c0, 0xa23: 0x00c0,
	0xa24: 0x00c0, 0xa25: 0x00c0, 0xa26: 0x00c0, 0xa27: 0x00c0, 0xa28: 0x00c0,
	0xa2a: 0x00c0, 0xa2b: 0x00c0, 0xa2c: 0x00c0, 0xa2d: 0x00c0, 0xa2e: 0x00c0, 0xa2f: 0x00c0,
	0xa30: 0x00c0, 0xa32: 0x00c0, 0xa33: 0x00c0, 0xa35: 0x00c0,
	0xa36: 0x00c0, 0xa37: 0x00c0, 0xa38: 0x00c0, 0xa39: 0x00c0,
	0xa3c: 0x00c3, 0xa3d: 0x00c0, 0xa3e: 0x00c0, 0xa3f: 0x00c3,
	// Block 0x29, offset 0xa40
	0xa40: 0x00c0, 0xa41: 0x00c3, 0xa42: 0x00c3, 0xa43: 0x00c3, 0xa44: 0x00c3,
	0xa47: 0x00c0, 0xa48: 0x00c0, 0xa4b: 0x00c0,
	0xa4c: 0x00c0, 0xa4d: 0x00c6,
	0xa55: 0x00c3, 0xa56: 0x00c3, 0xa57: 0x00c0,
	0xa5c: 0x0080, 0xa5d: 0x0080,
	0xa5f: 0x00c0, 0xa60: 0x00c0, 0xa61: 0x00c0, 0xa62: 0x00c3, 0xa63: 0x00c3,
	0xa66: 0x00c0, 0xa67: 0x00c0, 0xa68: 0x00c0, 0xa69: 0x00c0,
	0xa6a: 0x00c0, 0xa6b: 0x00c0, 0xa6c: 0x00c0, 0xa6d: 0x00c0, 0xa6e: 0x00c0, 0xa6f: 0x00c0,
	0xa70: 0x0080, 0xa71: 0x00c0, 0xa72: 0x0080, 0xa73: 0x0080, 0xa74: 0x0080, 0xa75: 0x0080,
	0xa76: 0x0080, 0xa77: 0x0080,
	// Block 0x2a, offset 0xa80
	0xa82: 0x00c3, 0xa83: 0x00c0, 0xa85: 0x00c0,
	0xa86: 0x00c0, 0xa87: 0x00c0, 0xa88: 0x00c0, 0xa89: 0x00c0, 0xa8a: 0x00c0,
	0xa8e: 0x00c0, 0xa8f: 0x00c0, 0xa90: 0x00c0,
	0xa92: 0x00c0, 0xa93: 0x00c0, 0xa94: 0x00c0, 0xa95: 0x00c0,
	0xa99: 0x00c0, 0xa9a: 0x00c0, 0xa9c: 0x00c0,
	0xa9e: 0x00c0, 0xa9f: 0x00c0, 0xaa3: 0x00c0,
	0xaa4: 0x00c0, 0xaa8: 0x00c0, 0xaa9: 0x00c0,
	0xaaa: 0x00c0, 0xaae: 0x00c0, 0xaaf: 0x00c0,
	0xab0: 0x00c0, 0xab1: 0x00c0, 0xab2: 0x00c0, 0xab3: 0x00c0, 0xab4: 0x00c0, 0xab5: 0x00c0,
	0xab6: 0x00c0, 0xab7: 0x00c0, 0xab8: 0x00c0, 0xab9: 0x00c0,
	0xabe: 0x00c0, 0xabf: 0x00c0,
	// Block 0x2b, offset 0xac0
	0xac0: 0x00c3, 0xac1: 0x00c0, 0xac2: 0x00c0,
	0xac6: 0x00c0, 0xac7: 0x00c0, 0xac8: 0x00c0, 0xaca: 0x00c0, 0xacb: 0x00c0,
	0xacc: 0x00c0, 0xacd: 0x00c6, 0xad0: 0x00c0,
	0xad7: 0x00c0,
	0xae6: 0x00c0, 0xae7: 0x00c0, 0xae8: 0x00c0, 0xae9: 0x00c0,
	0xaea: 0x00c0, 0xaeb: 0x00c0, 0xaec: 0x00c0, 0xaed: 0x00c0, 0xaee: 0x00c0, 0xaef: 0x00c0,
	0xaf0: 0x0080, 0xaf1: 0x0080, 0xaf2: 0x0080, 0xaf3: 0x0080, 0xaf4: 0x0080, 0xaf5: 0x0080,
	0xaf6: 0x0080, 0xaf7: 0x0080, 0xaf8: 0x0080, 0xaf9: 0x0080, 0xafa: 0x0080,
	// Block 0x2c, offset 0xb00
	0xb00: 0x00c3, 0xb01: 0x00c0, 0xb02: 0x00c0, 0xb03: 0x00c0, 0xb04: 0x00c3, 0xb05: 0x00c0,
	0xb06: 0x00c0, 0xb07: 0x00c0, 0xb08: 0x00c0, 0xb09: 0x00c0, 0xb0a: 0x00c0, 0xb0b: 0x00c0,
	0xb0c: 0x00c0, 0xb0e: 0x00c0, 0xb0f: 0x00c0, 0xb10: 0x00c0,
	0xb12: 0x00c0, 0xb13: 0x00c0, 0xb14: 0x00c0, 0xb15: 0x00c0, 0xb16: 0x00c0, 0xb17: 0x00c0,
	0xb18: 0x00c0, 0xb19: 0x00c0, 0xb1a: 0x00c0, 0xb1b: 0x00c0, 0xb1c: 0x00c0, 0xb1d: 0x00c0,
	0xb1e: 0x00c0, 0xb1f: 0x00c0, 0xb20: 0x00c0, 0xb21: 0x00c0, 0xb22: 0x00c0, 0xb23: 0x00c0,
	0xb24: 0x00c0, 0xb25: 0x00c0, 0xb26: 0x00c0, 0xb27: 0x00c0, 0xb28: 0x00c0,
	0xb2a: 0x00c0, 0xb2b: 0x00c0, 0xb2c: 0x00c0, 0xb2d: 0x00c0, 0xb2e: 0x00c0, 0xb2f: 0x00c0,
	0xb30: 0x00c0, 0xb31: 0x00c0, 0xb32: 0x00c0, 0xb33: 0x00c0, 0xb34: 0x00c0, 0xb35: 0x00c0,
	0xb36: 0x00c0, 0xb37: 0x00c0, 0xb38: 0x00c0, 0xb39: 0x00c0,
	0xb3d: 0x00c0, 0xb3e: 0x00c3, 0xb3f: 0x00c3,
	// Block 0x2d, offset 0xb40
	0xb40: 0x00c3, 0xb41: 0x00c0, 0xb42: 0x00c0, 0xb43: 0x00c0, 0xb44: 0x00c0,
	0xb46: 0x00c3, 0xb47: 0x00c3, 0xb48: 0x00c3, 0xb4a: 0x00c3, 0xb4b: 0x00c3,
	0xb4c: 0x00c3, 0xb4d: 0x00c6,
	0xb55: 0x00c3, 0xb56: 0x00c3,
	0xb58: 0x00c0, 0xb59: 0x00c0, 0xb5a: 0x00c0,
	0xb60: 0x00c0, 0xb61: 0x00c0, 0xb62: 0x00c3, 0xb63: 0x00c3,
	0xb66: 0x00c0, 0xb67: 0x00c0, 0xb68: 0x00c0, 0xb69: 0x00c0,
	0xb6a: 0x00c0, 0xb6b: 0x00c0, 0xb6c: 0x00c0, 0xb6d: 0x00c0, 0xb6e: 0x00c0, 0xb6f: 0x00c0,
	0xb77: 0x0080, 0xb78: 0x0080, 0xb79: 0x0080, 0xb7a: 0x0080, 0xb7b: 0x0080,
	0xb7c: 0x0080, 0xb7d: 0x0080, 0xb7e: 0x0080, 0xb7f: 0x0080,
	// Block 0x2e, offset 0xb80
	0xb80: 0x00c0, 0xb81: 0x00c3, 0xb82: 0x00c0, 0xb83: 0x00c0, 0xb84: 0x0080, 0xb85: 0x00c0,
	0xb86: 0x00c0, 0xb87: 0x00c0, 0xb88: 0x00c0, 0xb89: 0x00c0, 0xb8a: 0x00c0, 0xb8b: 0x00c0,
	0xb8c: 0x00c0, 0xb8e: 0x00c0, 0xb8f: 0x00c0, 0xb90: 0x00c0,
	0xb92: 0x00c0, 0xb93: 0x00c0, 0xb94: 0x00c0, 0xb95: 0x00c0, 0xb96: 0x00c0, 0xb97: 0x00c0,
	0xb98: 0x00c0, 0xb99: 0x00c0, 0xb9a: 0x00c0, 0xb9b: 0x00c0, 0xb9c: 0x00c0, 0xb9d: 0x00c0,
	0xb9e: 0x00c0, 0xb9f: 0x00c0, 0xba0: 0x00c0, 0xba1: 0x00c0, 0xba2: 0x00c0, 0xba3: 0x00c0,
	0xba4: 0x00c0, 0xba5: 0x00c0, 0xba6: 0x00c0, 0xba7: 0x00c0, 0xba8: 0x00c0,
	0xbaa: 0x00c0, 0xbab: 0x00c0, 0xbac: 0x00c0, 0xbad: 0x00c0, 0xbae: 0x00c0, 0xbaf: 0x00c0,
	0xbb0: 0x00c0, 0xbb1: 0x00c0, 0xbb2: 0x00c0, 0xbb3: 0x00c0, 0xbb5: 0x00c0,
	0xbb6: 0x00c0, 0xbb7: 0x00c0, 0xbb8: 0x00c0, 0xbb9: 0x00c0,
	0xbbc: 0x00c3, 0xbbd: 0x00c0, 0xbbe: 0x00c0, 0xbbf: 0x00c3,
	// Block 0x2f, offset 0xbc0
	0xbc0: 0x00c0, 0xbc1: 0x00c0, 0xbc2: 0x00c0, 0xbc3: 0x00c0, 0xbc4: 0x00c0,
	0xbc6: 0x00c3, 0xbc7: 0x00c0, 0xbc8: 0x00c0, 0xbca: 0x00c0, 0xbcb: 0x00c0,
	0xbcc: 0x00c3, 0xbcd: 0x00c6,
	0xbd5: 0x00c0, 0xbd6: 0x00c0,
	0xbde: 0x00c0, 0xbe0: 0x00c0, 0xbe1: 0x00c0, 0xbe2: 0x00c3, 0xbe3: 0x00c3,
	0xbe6: 0x00c0, 0xbe7: 0x00c0, 0xbe8: 0x00c0, 0xbe9: 0x00c0,
	0xbea: 0x00c0, 0xbeb: 0x00c0, 0xbec: 0x00c0, 0xbed: 0x00c0, 0xbee: 0x00c0, 0xbef: 0x00c0,
	0xbf1: 0x00c0, 0xbf2: 0x00c0,
	// Block 0x30, offset 0xc00
	0xc00: 0x00c3, 0xc01: 0x00c3, 0xc02: 0x00c0, 0xc03: 0x00c0, 0xc04: 0x00c0, 0xc05: 0x00c0,
	0xc06: 0x00c0, 0xc07: 0x00c0, 0xc08: 0x00c0, 0xc09: 0x00c0, 0xc0a: 0x00c0, 0xc0b: 0x00c0,
	0xc0c: 0x00c0, 0xc0e: 0x00c0, 0xc0f: 0x00c0, 0xc10: 0x00c0,
	0xc12: 0x00c0, 0xc13: 0x00c0, 0xc14: 0x00c0, 0xc15: 0x00c0, 0xc16: 0x00c0, 0xc17: 0x00c0,
	0xc18: 0x00c0, 0xc19: 0x00c0, 0xc1a: 0x00c0, 0xc1b: 0x00c0, 0xc1c: 0x00c0, 0xc1d: 0x00c0,
	0xc1e: 0x00c0, 0xc1f: 0x00c0, 0xc20: 0x00c0, 0xc21: 0x00c0, 0xc22: 0x00c0, 0xc23: 0x00c0,
	0xc24: 0x00c0, 0xc25: 0x00c0, 0xc26: 0x00c0, 0xc27: 0x00c0, 0xc28: 0x00c0, 0xc29: 0x00c0,
	0xc2a: 0x00c0, 0xc2b: 0x00c0, 0xc2c: 0x00c0, 0xc2d: 0x00c0, 0xc2e: 0x00c0, 0xc2f: 0x00c0,
	0xc30: 0x00c0, 0xc31: 0x00c0, 0xc32: 0x00c0, 0xc33: 0x00c0, 0xc34: 0x00c0, 0xc35: 0x00c0,
	0xc36: 0x00c0, 0xc37: 0x00c0, 0xc38: 0x00c0, 0xc39: 0x00c0, 0xc3a: 0x00c0, 0xc3b: 0x00c6,
	0xc3c: 0x00c6, 0xc3d: 0x00c0, 0xc3e: 0x00c0, 0xc3f: 0x00c0,
	// Block 0x31, offset 0xc40
	0xc40: 0x00c0, 0xc41: 0x00c3, 0xc42: 0x00c3, 0xc43: 0x00c3, 0xc44: 0x00c3,
	0xc46: 0x00c0, 0xc47: 0x00c0, 0xc48: 0x00c0, 0xc4a: 0x00c0, 0xc4b: 0x00c0,
	0xc4c: 0x00c0, 0xc4d: 0x00c6, 0xc4e: 0x00c0, 0xc4f: 0x0080,
	0xc54: 0x00c0, 0xc55: 0x00c0, 0xc56: 0x00c0, 0xc57: 0x00c0,
	0xc58: 0x0080, 0xc59: 0x0080, 0xc5a: 0x0080, 0xc5b: 0x0080, 0xc5c: 0x0080, 0xc5d: 0x0080,
	0xc5e: 0x0080, 0xc5f: 0x00c0, 0xc60: 0x00c0, 0xc61: 0x00c0, 0xc62: 0x00c3, 0xc63: 0x00c3,
	0xc66: 0x00c0, 0xc67: 0x00c0, 0xc68: 0x00c0, 0xc69: 0x00c0,
	0xc6a: 0x00c0, 0xc6b: 0x00c0, 0xc6c: 0x00c0, 0xc6d: 0x00c0, 0xc6e: 0x00c0, 0xc6f: 0x00c0,
	0xc70: 0x0080, 0xc71: 0x0080, 0xc72: 0x0080, 0xc73: 0x0080, 0xc74: 0x0080, 0xc75: 0x0080,
	0xc76: 0x0080, 0xc77: 0x0080, 0xc78: 0x0080, 0xc79: 0x0080, 0xc7a: 0x00c0, 0xc7b: 0x00c0,
	0xc7c: 0x00c0, 0xc7d: 0x00c0, 0xc7e: 0x00c0, 0xc7f: 0x00c0,
	// Block 0x32, offset 0xc80
	0xc81: 0x00c3, 0xc82: 0x00c0, 0xc83: 0x00c0, 0xc85: 0x00c0,
	0xc86: 0x00c0, 0xc87: 0x00c0, 0xc88: 0x00c0, 0xc89: 0x00c0, 0xc8a: 0x00c0, 0xc8b: 0x00c0,
	0xc8c: 0x00c0, 0xc8d: 0x00c0, 0xc8e: 0x00c0, 0xc8f: 0x00c0, 0xc90: 0x00c0, 0xc91: 0x00c0,
	0xc92: 0x00c0, 0xc93: 0x00c0, 0xc94: 0x00c0, 0xc95: 0x00c0, 0xc96: 0x00c0,
	0xc9a: 0x00c0, 0xc9b: 0x00c0, 0xc9c: 0x00c0, 0xc9d: 0x00c0,
	0xc9e: 0x00c0, 0xc9f: 0x00c0, 0xca0: 0x00c0, 0xca1: 0x00c0, 0xca2: 0x00c0, 0xca3: 0x00c0,
	0xca4: 0x00c0, 0xca5: 0x00c0, 0xca6: 0x00c0, 0xca7: 0x00c0, 0xca8: 0x00c0, 0xca9: 0x00c0,
	0xcaa: 0x00c0, 0xcab: 0x00c0, 0xcac: 0x00c0, 0xcad: 0x00c0, 0xcae: 0x00c0, 0xcaf: 0x00c0,
	0xcb0: 0x00c0, 0xcb1: 0x00c0, 0xcb3: 0x00c0, 0xcb4: 0x00c0, 0xcb5: 0x00c0,
	0xcb6: 0x00c0, 0xcb7: 0x00c0, 0xcb8: 0x00c0, 0xcb9: 0x00c0, 0xcba: 0x00c0, 0xcbb: 0x00c0,
	0xcbd: 0x00c0,
	// Block 0x33, offset 0xcc0
	0xcc0: 0x00c0, 0xcc1: 0x00c0, 0xcc2: 0x00c0, 0xcc3: 0x00c0, 0xcc4: 0x00c0, 0xcc5: 0x00c0,
	0xcc6: 0x00c0, 0xcca: 0x00c6,
	0xccf: 0x00c0, 0xcd0: 0x00c0, 0xcd1: 0x00c0,
	0xcd2: 0x00c3, 0xcd3: 0x00c3, 0xcd4: 0x00c3, 0xcd6: 0x00c3,
	0xcd8: 0x00c0, 0xcd9: 0x00c0, 0xcda: 0x00c0, 0xcdb: 0x00c0, 0xcdc: 0x00c0, 0xcdd: 0x00c0,
	0xcde: 0x00c0, 0xcdf: 0x00c0,
	0xce6: 0x00c0, 0xce7: 0x00c0, 0xce8: 0x00c0, 0xce9: 0x00c0,
	0xcea: 0x00c0, 0xceb: 0x00c0, 0xcec: 0x00c0, 0xced: 0x00c0, 0xcee: 0x00c0, 0xcef: 0x00c0,
	0xcf2: 0x00c0, 0xcf3: 0x00c0, 0xcf4: 0x0080,
	// Block 0x34, offset 0xd00
	0xd01: 0x00c0, 0xd02: 0x00c0, 0xd03: 0x00c0, 0xd04: 0x00c0, 0xd05: 0x00c0,
	0xd06: 0x00c0, 0xd07: 0x00c0, 0xd08: 0x00c0, 0xd09: 0x00c0, 0xd0a: 0x00c0, 0xd0b: 0x00c0,
	0xd0c: 0x00c0, 0xd0d: 0x00c0, 0xd0e: 0x00c0, 0xd0f: 0x00c0, 0xd10: 0x00c0, 0xd11: 0x00c0,
	0xd12: 0x00c0, 0xd13: 0x00c0, 0xd14: 0x00c0, 0xd15: 0x00c0, 0xd16: 0x00c0, 0xd17: 0x00c0,
	0xd18: 0x00c0, 0xd19: 0x00c0, 0xd1a: 0x00c0, 0xd1b: 0x00c0, 0xd1c: 0x00c0, 0xd1d: 0x00c0,
	0xd1e: 0x00c0, 0xd1f: 0x00c0, 0xd20: 0x00c0, 0xd21: 0x00c0, 0xd22: 0x00c0, 0xd23: 0x00c0,
	0xd24: 0x00c0, 0xd25: 0x00c0, 0xd26: 0x00c0, 0xd27: 0x00c0, 0xd28: 0x00c0, 0xd29: 0x00c0,
	0xd2a: 0x00c0, 0xd2b: 0x00c0, 0xd2c: 0x00c0, 0xd2d: 0x00c0, 0xd2e: 0x00c0, 0xd2f: 0x00c0,
	0xd30: 0x00c0, 0xd31: 0x00c3, 0xd32: 0x00c0, 0xd33: 0x0080, 0xd34: 0x00c3, 0xd35: 0x00c3,
	0xd36: 0x00c3, 0xd37: 0x00c3, 0xd38: 0x00c3, 0xd39: 0x00c3, 0xd3a: 0x00c6,
	0xd3f: 0x0080,
	// Block 0x35, offset 0xd40
	0xd40: 0x00c0, 0xd41: 0x00c0, 0xd42: 0x00c0, 0xd43: 0x00c0, 0xd44: 0x00c0, 0xd45: 0x00c0,
	0xd46: 0x00c0, 0xd47: 0x00c3, 0xd48: 0x00c3, 0xd49: 0x00c3, 0xd4a: 0x00c3, 0xd4b: 0x00c3,
	0xd4c: 0x00c3, 0xd4d: 0x00c3, 0xd4e: 0x00c3, 0xd4f: 0x0080, 0xd50: 0x00c0, 0xd51: 0x00c0,
	0xd52: 0x00c0, 0xd53: 0x00c0, 0xd54: 0x00c0, 0xd55: 0x00c0, 0xd56: 0x00c0, 0xd57: 0x00c0,
	0xd58: 0x00c0, 0xd59: 0x00c0, 0xd5a: 0x0080, 0xd5b: 0x0080,
	// Block 0x36, offset 0xd80
	0xd81: 0x00c0, 0xd82: 0x00c0, 0xd84: 0x00c0,
	0xd86: 0x00c0, 0xd87: 0x00c0, 0xd88: 0x00c0, 0xd89: 0x00c0, 0xd8a: 0x00c0,
	0xd8c: 0x00c0, 0xd8d: 0x00c0, 0xd8e: 0x00c0, 0xd8f: 0x00c0, 0xd90: 0x00c0, 0xd91: 0x00c0,
	0xd92: 0x00c0, 0xd93: 0x00c0, 0xd94: 0x00c0, 0xd95: 0x00c0, 0xd96: 0x00c0, 0xd97: 0x00c0,
	0xd98: 0x00c0, 0xd99: 0x00c0, 0xd9a: 0x00c0, 0xd9b: 0x00c0, 0xd9c: 0x00c0, 0xd9d: 0x00c0,
	0xd9e: 0x00c0, 0xd9f: 0x00c0, 0xda0: 0x00c0, 0xda1: 0x00c0, 0xda2: 0x00c0, 0xda3: 0x00c0,
	0xda5: 0x00c0, 0xda7: 0x00c0, 0xda8: 0x00c0, 0xda9: 0x00c0,
	0xdaa: 0x00c0, 0xdab: 0x00c0, 0xdac: 0x00c0, 0xdad: 0x00c0, 0xdae: 0x00c0, 0xdaf: 0x00c0,
	0xdb0: 0x00c0, 0xdb1: 0x00c3, 0xdb2: 0x00c0, 0xdb3: 0x0080, 0xdb4: 0x00c3, 0xdb5: 0x00c3,
	0xdb6: 0x00c3, 0xdb7: 0x00c3, 0xdb8: 0x00c3, 0xdb9: 0x00c3, 0xdba: 0x00c6, 0xdbb: 0x00c3,
	0xdbc: 0x00c3, 0xdbd: 0x00c0,
	// Block 0x37, offset 0xdc0
	0xdc0: 0x00c0, 0xdc1: 0x00c0, 0xdc2: 0x00c0, 0xdc3: 0x00c0, 0xdc4: 0x00c0,
	0xdc6: 0x00c0, 0xdc8: 0x00c3, 0xdc9: 0x00c3, 0xdca: 0x00c3, 0xdcb: 0x00c3,
	0xdcc: 0x00c3, 0xdcd: 0x00c3, 0xdd0: 0x00c0, 0xdd1: 0x00c0,
	0xdd2: 0x00c0, 0xdd3: 0x00c0, 0xdd4: 0x00c0, 0xdd5: 0x00c0, 0xdd6: 0x00c0, 0xdd7: 0x00c0,
	0xdd8: 0x00c0, 0xdd9: 0x00c0, 0xddc: 0x0080, 0xddd: 0x0080,
	0xdde: 0x00c0, 0xddf: 0x00c0,
	// Block 0x38, offset 0xe00
	0xe00: 0x00c0, 0xe01: 0x0080, 0xe02: 0x0080, 0xe03: 0x0080, 0xe04: 0x0080, 0xe05: 0x0080,
	0xe06: 0x0080, 0xe07: 0x0080, 0xe08: 0x0080, 0xe09: 0x0080, 0xe0a: 0x0080, 0xe0b: 0x00c0,
	0xe0c: 0x0080, 0xe0d: 0x0080, 0xe0e: 0x0080, 0xe0f: 0x0080, 0xe10: 0x0080, 0xe11: 0x0080,
	0xe12: 0x0080, 0xe13: 0x0080, 0xe14: 0x0080, 0xe15: 0x0080, 0xe16: 0x0080, 0xe17: 0x0080,
	0xe18: 0x00c3, 0xe19: 0x00c3, 0xe1a: 0x0080, 0xe1b: 0x0080, 0xe1c: 0x0080, 0xe1d: 0x0080,
	0xe1e: 0x0080, 0xe1f: 0x0080, 0xe20: 0x00c0, 0xe21: 0x00c0, 0xe22: 0x00c0, 0xe23: 0x00c0,
	0xe24: 0x00c0, 0xe25: 0x00c0, 0xe26: 0x00c0, 0xe27: 0x00c0, 0xe28: 0x00c0, 0xe29: 0x00c0,
	0xe2a: 0x0080, 0xe2b: 0x0080, 0xe2c: 0x0080, 0xe2d: 0x0080, 0xe2e: 0x0080, 0xe2f: 0x0080,
	0xe30: 0x0080, 0xe31: 0x0080, 0xe32: 0x0080, 0xe33: 0x0080, 0xe34: 0x0080, 0xe35: 0x00c3,
	0xe36: 0x0080, 0xe37: 0x00c3, 0xe38: 0x0080, 0xe39: 0x00c3, 0xe3a: 0x0080, 0xe3b: 0x0080,
	0xe3c: 0x0080, 0xe3d: 0x0080, 0xe3e: 0x00c0, 0xe3f: 0x00c0,
	// Block 0x39, offset 0xe40
	0xe40: 0x00c0, 0xe41: 0x00c0, 0xe42: 0x00c0, 0xe43: 0x0080, 0xe44: 0x00c0, 0xe45: 0x00c0,
	0xe46: 0x00c0, 0xe47: 0x00c0, 0xe49: 0x00c0, 0xe4a: 0x00c0, 0xe4b: 0x00c0,
	0xe4c: 0x00c0, 0xe4d: 0x0080, 0xe4e: 0x00c0, 0xe4f: 0x00c0, 0xe50: 0x00c0, 0xe51: 0x00c0,
	0xe52: 0x0080, 0xe53: 0x00c0, 0xe54: 0x00c0, 0xe55: 0x00c0, 0xe56: 0x00c0, 0xe57: 0x0080,
	0xe58: 0x00c0, 0xe59: 0x00c0, 0xe5a: 0x00c0, 0xe5b: 0x00c0, 0xe5c: 0x0080, 0xe5d: 0x00c0,
	0xe5e: 0x00c0, 0xe5f: 0x00c0, 0xe60: 0x00c0, 0xe61: 0x00c0, 0xe62: 0x00c0, 0xe63: 0x00c0,
	0xe64: 0x00c0, 0xe65: 0x00c0, 0xe66: 0x00c0, 0xe67: 0x00c0, 0xe68: 0x00c0, 0xe69: 0x0080,
	0xe6a: 0x00c0, 0xe6b: 0x00c0, 0xe6c: 0x00c0,
	0xe71: 0x00c3, 0xe72: 0x00c3, 0xe73: 0x0083, 0xe74: 0x00c3, 0xe75: 0x0083,
	0xe76: 0x0083, 0xe77: 0x0083, 0xe78: 0x0083, 0xe79: 0x0083, 0xe7a: 0x00c3, 0xe7b: 0x00c3,
	0xe7c: 0x00c3, 0xe7d: 0x00c3, 0xe7e: 0x00c3, 0xe7f: 0x00c0,
	// Block 0x3a, offset 0xe80
	0xe80: 0x00c3, 0xe81: 0x0083, 0xe82: 0x00c3, 0xe83: 0x00c3, 0xe84: 0x00c6, 0xe85: 0x0080,
	0xe86: 0x00c3, 0xe87: 0x00c3, 0xe88: 0x00c0, 0xe89: 0x00c0, 0xe8a: 0x00c0, 0xe8b: 0x00c0,
	0xe8c: 0x00c0, 0xe8d: 0x00c3, 0xe8e: 0x00c3, 0xe8f: 0x00c3, 0xe90: 0x00c3, 0xe91: 0x00c3,
	0xe92: 0x00c3, 0xe93: 0x0083, 0xe94: 0x00c3, 0xe95: 0x00c3, 0xe96: 0x00c3, 0xe97: 0x00c3,
	0xe99: 0x00c3, 0xe9a: 0x00c3, 0xe9b: 0x00c3, 0xe9c: 0x00c3, 0xe9d: 0x0083,
	0xe9e: 0x00c3, 0xe9f: 0x00c3, 0xea0: 0x00c3, 0xea1: 0x00c3, 0xea2: 0x0083, 0xea3: 0x00c3,
	0xea4: 0x00c3, 0xea5: 0x00c3, 0xea6: 0x00c3, 0xea7: 0x0083, 0xea8: 0x00c3, 0xea9: 0x00c3,
	0xeaa: 0x00c3, 0xeab: 0x00c3, 0xeac: 0x0083, 0xead: 0x00c3, 0xeae: 0x00c3, 0xeaf: 0x00c3,
	0xeb0: 0x00c3, 0xeb1: 0x00c3, 0xeb2: 0x00c3, 0xeb3: 0x00c3, 0xeb4: 0x00c3, 0xeb5: 0x00c3,
	0xeb6: 0x00c3, 0xeb7: 0x00c3, 0xeb8: 0x00c3, 0xeb9: 0x0083, 0xeba: 0x00c3, 0xebb: 0x00c3,
	0xebc: 0x00c3, 0xebe: 0x0080, 0xebf: 0x0080,
	// Block 0x3b, offset 0xec0
	0xec0: 0x0080, 0xec1: 0x0080, 0xec2: 0x0080, 0xec3: 0x0080, 0xec4: 0x0080, 0xec5: 0x0080,
	0xec6: 0x00c3, 0xec7: 0x0080, 0xec8: 0x0080, 0xec9: 0x0080, 0xeca: 0x0080, 0xecb: 0x0080,
	0xecc: 0x0080, 0xece: 0x0080, 0xecf: 0x0080, 0xed0: 0x0080, 0xed1: 0x0080,
	0xed2: 0x0080, 0xed3: 0x0080, 0xed4: 0x0080, 0xed5: 0x0080, 0xed6: 0x0080, 0xed7: 0x0080,
	0xed8: 0x0080, 0xed9: 0x0080, 0xeda: 0x0080,
	// Block 0x3c, offset 0xf00
	0xf00: 0x00c0, 0xf01: 0x00c0, 0xf02: 0x00c0, 0xf03: 0x00c0, 0xf04: 0x00c0, 0xf05: 0x00c0,
	0xf06: 0x00c0, 0xf07: 0x00c0, 0xf08: 0x00c0, 0xf09: 0x00c0, 0xf0a: 0x00c0, 0xf0b: 0x00c0,
	0xf0c: 0x00c0, 0xf0d: 0x00c0, 0xf0e: 0x00c0, 0xf0f: 0x00c0, 0xf10: 0x00c0, 0xf11: 0x00c0,
	0xf12: 0x00c0, 0xf13: 0x00c0, 0xf14: 0x00c0, 0xf15: 0x00c0, 0xf16: 0x00c0, 0xf17: 0x00c0,
	0xf18: 0x00c0, 0xf19: 0x00c0, 0xf1a: 0x00c0, 0xf1b: 0x00c0, 0xf1c: 0x00c0, 0xf1d: 0x00c0,
	0xf1e: 0x00c0, 0xf1f: 0x00c0, 0xf20: 0x00c0, 0xf21: 0x00c0, 0xf22: 0x00c0, 0xf23: 0x00c0,
	0xf24: 0x00c0, 0xf25: 0x00c0, 0xf26: 0x00c0, 0xf27: 0x00c0, 0xf28: 0x00c0, 0xf29: 0x00c0,
	0xf2a: 0x00c0, 0xf2b: 0x00c0, 0xf2c: 0x00c0, 0xf2d: 0x00c3, 0xf2e: 0x00c3, 0xf2f: 0x00c3,
	0xf30: 0x00c3, 0xf31: 0x00c0, 0xf32: 0x00c3, 0xf33: 0x00c3, 0xf34: 0x00c3, 0xf35: 0x00c3,
	0xf36: 0x00c3, 0xf37: 0x00c3, 0xf38: 0x00c0, 0xf39: 0x00c6, 0xf3a: 0x00c6, 0xf3b: 0x00c0,
	0xf3c: 0x00c0, 0xf3d: 0x00c3, 0xf3e: 0x00c3, 0xf3f: 0x00c0,
	// Block 0x3d, offset 0xf40
	0xf40: 0x00c0, 0xf41: 0x00c0, 0xf42: 0x00c0, 0xf43: 0x00c0, 0xf44: 0x00c0, 0xf45: 0x00c0,
	0xf46: 0x00c0, 0xf47: 0x00c0, 0xf48: 0x00c0, 0xf49: 0x00c0, 0xf4a: 0x0080, 0xf4b: 0x0080,
	0xf4c: 0x0080, 0xf4d: 0x0080, 0xf4e: 0x0080, 0xf4f: 0x0080, 0xf50: 0x00c0, 0xf51: 0x00c0,
	0xf52: 0x00c0, 0xf53: 0x00c0, 0xf54: 0x00c0, 0xf55: 0x00c0, 0xf56: 0x00c0, 0xf57: 0x00c0,
	0xf58: 0x00c3, 0xf59: 0x00c3, 0xf5a: 0x00c0, 0xf5b: 0x00c0, 0xf5c: 0x00c0, 0xf5d: 0x00c0,
	0xf5e: 0x00c3, 0xf5f: 0x00c3, 0xf60: 0x00c3, 0xf61: 0x00c0, 0xf62: 0x00c0, 0xf63: 0x00c0,
	0xf64: 0x00c0, 0xf65: 0x00c0, 0xf66: 0x00c0, 0xf67: 0x00c0, 0xf68: 0x00c0, 0xf69: 0x00c0,
	0xf6a: 0x00c0, 0xf6b: 0x00c0, 0xf6c: 0x00c0, 0xf6d: 0x00c0, 0xf6e: 0x00c0, 0xf6f: 0x00c0,
	0xf70: 0x00c0, 0xf71: 0x00c3, 0xf72: 0x00c3, 0xf73: 0x00c3, 0xf74: 0x00c3, 0xf75: 0x00c0,
	0xf76: 0x00c0, 0xf77: 0x00c0, 0xf78: 0x00c0, 0xf79: 0x00c0, 0xf7a: 0x00c0, 0xf7b: 0x00c0,
	0xf7c: 0x00c0, 0xf7d: 0x00c0, 0xf7e: 0x00c0, 0xf7f: 0x00c0,
	// Block 0x3e, offset 0xf80
	0xf80: 0x00c0, 0xf81: 0x00c0, 0xf82: 0x00c3, 0xf83: 0x00c0, 0xf84: 0x00c0, 0xf85: 0x00c3,
	0xf86: 0x00c3, 0xf87: 0x00c0, 0xf88: 0x00c0, 0xf89: 0x00c0, 0xf8a: 0x00c0, 0xf8b: 0x00c0,
	0xf8c: 0x00c0, 0xf8d: 0x00c3, 0xf8e: 0x00c0, 0xf8f: 0x00c0, 0xf90: 0x00c0, 0xf91: 0x00c0,
	0xf92: 0x00c0, 0xf93: 0x00c0, 0xf94: 0x00c0, 0xf95: 0x00c0, 0xf96: 0x00c0, 0xf97: 0x00c0,
	0xf98: 0x00c0, 0xf99: 0x00c0, 0xf9a: 0x00c0, 0xf9b: 0x00c0, 0xf9c: 0x00c0, 0xf9d: 0x00c3,
	0xf9e: 0x0080, 0xf9f: 0x0080, 0xfa0: 0x00c0, 0xfa1: 0x00c0, 0xfa2: 0x00c0, 0xfa3: 0x00c0,
	0xfa4: 0x00c0, 0xfa5: 0x00c0, 0xfa6: 0x00c0, 0xfa7: 0x00c0, 0xfa8: 0x00c0, 0xfa9: 0x00c0,
	0xfaa: 0x00c0, 0xfab: 0x00c0, 0xfac: 0x00c0, 0xfad: 0x00c0, 0xfae: 0x00c0, 0xfaf: 0x00c0,
	0xfb0: 0x00c0, 0xfb1: 0x00c0, 0xfb2: 0x00c0, 0xfb3: 0x00c0, 0xfb4: 0x00c0, 0xfb5: 0x00c0,
	0xfb6: 0x00c0, 0xfb7: 0x00c0, 0xfb8: 0x00c0, 0xfb9: 0x00c0, 0xfba: 0x00c0, 0xfbb: 0x00c0,
	0xfbc: 0x00c0, 0xfbd: 0x00c0, 0xfbe: 0x00c0, 0xfbf: 0x00c0,
	// Block 0x3f, offset 0xfc0
	0xfc0: 0x00c0, 0xfc1: 0x00c0, 0xfc2: 0x00c0, 0xfc3: 0x00c0, 0xfc4: 0x00c0, 0xfc5: 0x00c0,
	0xfc7: 0x00c0,
	0xfcd: 0x00c0, 0xfd0: 0x00c0, 0xfd1: 0x00c0,
	0xfd2: 0x00c0, 0xfd3: 0x00c0, 0xfd4: 0x00c0, 0xfd5: 0x00c0, 0xfd6: 0x00c0, 0xfd7: 0x00c0,
	0xfd8: 0x00c0, 0xfd9: 0x00c0, 0xfda: 0x00c0, 0xfdb: 0x00c0, 0xfdc: 0x00c0, 0xfdd: 0x00c0,
	0xfde: 0x00c0, 0xfdf: 0x00c0, 0xfe0: 0x00c0, 0xfe1: 0x00c0, 0xfe2: 0x00c0, 0xfe3: 0x00c0,
	0xfe4: 0x00c0, 0xfe5: 0x00c0, 0xfe6: 0x00c0, 0xfe7: 0x00c0, 0xfe8: 0x00c0, 0xfe9: 0x00c0,
	0xfea: 0x00c0, 0xfeb: 0x00c0, 0xfec: 0x00c0, 0xfed: 0x00c0, 0xfee: 0x00c0, 0xfef: 0x00c0,
	0xff0: 0x00c0, 0xff1: 0x00c0, 0xff2: 0x00c0, 0xff3: 0x00c0, 0xff4: 0x00c0, 0xff5: 0x00c0,
	0xff6: 0x00c0, 0xff7: 0x00c0, 0xff8: 0x00c0, 0xff9: 0x00c0, 0xffa: 0x00c0, 0xffb: 0x0080,
	0xffc: 0x0080, 0xffd: 0x00c0, 0xffe: 0x00c0, 0xfff: 0x00c0,
	// Block 0x40, offset 0x1000
	0x1000: 0x0040, 0x1001: 0x0040, 0x1002: 0x0040, 0x1003: 0x0040, 0x1004: 0x0040, 0x1005: 0x0040,
	0x1006: 0x0040, 0x1007: 0x0040, 0x1008: 0x0040, 0x1009: 0x0040, 0x100a: 0x0040, 0x100b: 0x0040,
	0x100c: 0x0040, 0x100d: 0x0040, 0x100e: 0x0040, 0x100f: 0x0040, 0x1010: 0x0040, 0x1011: 0x0040,
	0x1012: 0x0040, 0x1013: 0x0040, 0x1014: 0x0040, 0x1015: 0x0040, 0x1016: 0x0040, 0x1017: 0x0040,
	0x1018: 0x0040, 0x1019: 0x0040, 0x101a: 0x0040, 0x101b: 0x0040, 0x101c: 0x0040, 0x101d: 0x0040,
	0x101e: 0x0040, 0x101f: 0x0040, 0x1020: 0x0040, 0x1021: 0x0040, 0x1022: 0x0040, 0x1023: 0x0040,
	0x1024: 0x0040, 0x1025: 0x0040, 0x1026: 0x0040, 0x1027: 0x0040, 0x1028: 0x0040, 0x1029: 0x0040,
	0x102a: 0x0040, 0x102b: 0x0040, 0x102c: 0x0040, 0x102d: 0x0040, 0x102e: 0x0040, 0x102f: 0x0040,
	0x1030: 0x0040, 0x1031: 0x0040, 0x1032: 0x0040, 0x1033: 0x0040, 0x1034: 0x0040, 0x1035: 0x0040,
	0x1036: 0x0040, 0x1037: 0x0040, 0x1038: 0x0040, 0x1039: 0x0040, 0x103a: 0x0040, 0x103b: 0x0040,
	0x103c: 0x0040, 0x103d: 0x0040, 0x103e: 0x0040, 0x103f: 0x0040,
	// Block 0x41, offset 0x1040
	0x1040: 0x00c0, 0x1041: 0x00c0, 0x1042: 0x00c0, 0x1043: 0x00c0, 0x1044: 0x00c0, 0x1045: 0x00c0,
	0x1046: 0x00c0, 0x1047: 0x00c0, 0x1048: 0x00c0, 0x104a: 0x00c0, 0x104b: 0x00c0,
	0x104c: 0x00c0, 0x104d: 0x00c0, 0x1050: 0x00c0, 0x1051: 0x00c0,
	0x1052: 0x00c0, 0x1053: 0x00c0, 0x1054: 0x00c0, 0x1055: 0x00c0, 0x1056: 0x00c0,
	0x1058: 0x00c0, 0x105a: 0x00c0, 0x105b: 0x00c0, 0x105c: 0x00c0, 0x105d: 0x00c0,
	0x1060: 0x00c0, 0x1061: 0x00c0, 0x1062: 0x00c0, 0x1063: 0x00c0,
	0x1064: 0x00c0, 0x1065: 0x00c0, 0x1066: 0x00c0, 0x1067: 0x00c0, 0x1068: 0x00c0, 0x1069: 0x00c0,
	0x106a: 0x00c0, 0x106b: 0x00c0, 0x106c: 0x00c0, 0x106d: 0x00c0, 0x106e: 0x00c0, 0x106f: 0x00c0,
	0x1070: 0x00c0, 0x1071: 0x00c0, 0x1072: 0x00c0, 0x1073: 0x00c0, 0x1074: 0x00c0, 0x1075: 0x00c0,
	0x1076: 0x00c0, 0x1077: 0x00c0, 0x1078: 0x00c0, 0x1079: 0x00c0, 0x107a: 0x00c0, 0x107b: 0x00c0,
	0x107c: 0x00c0, 0x107d: 0x00c0, 0x107e: 0x00c0, 0x107f: 0x00c0,
	// Block 0x42, offset 0x1080
	0x1080: 0x00c0, 0x1081: 0x00c0, 0x1082: 0x00c0, 0x1083: 0x00c0, 0x1084: 0x00c0, 0x1085: 0x00c0,
	0x1086: 0x00c0, 0x1087: 0x00c0, 0x1088: 0x00c0, 0x108a: 0x00c0, 0x108b: 0x00c0,
	0x108c: 0x00c0, 0x108d: 0x00c0, 0x1090: 0x00c0, 0x1091: 0x00c0,
	0x1092: 0x00c0, 0x1093: 0x00c0, 0x1094: 0x00c0, 0x1095: 0x00c0, 0x1096: 0x00c0, 0x1097: 0x00c0,
	0x1098: 0x00c0, 0x1099: 0x00c0, 0x109a: 0x00c0, 0x109b: 0x00c0, 0x109c: 0x00c0, 0x109d: 0x00c0,
	0x109e: 0x00c0, 0x109f: 0x00c0, 0x10a0: 0x00c0, 0x10a1: 0x00c0, 0x10a2: 0x00c0, 0x10a3: 0x00c0,
	0x10a4: 0x00c0, 0x10a5: 0x00c0, 0x10a6: 0x00c0, 0x10a7: 0x00c0, 0x10a8: 0x00c0, 0x10a9: 0x00c0,
	0x10aa: 0x00c0, 0x10ab: 0x00c0, 0x10ac: 0x00c0, 0x10ad: 0x00c0, 0x10ae: 0x00c0, 0x10af: 0x00c0,
	0x10b0: 0x00c0, 0x10b2: 0x00c0, 0x10b3: 0x00c0, 0x10b4: 0x00c0, 0x10b5: 0x00c0,
	0x10b8: 0x00c0, 0x10b9: 0x00c0, 0x10ba: 0x00c0, 0x10bb: 0x00c0,
	0x10bc: 0x00c0, 0x10bd: 0x00c0, 0x10be: 0x00c0,
	// Block 0x43, offset 0x10c0
	0x10c0: 0x00c0, 0x10c2: 0x00c0, 0x10c3: 0x00c0, 0x10c4: 0x00c0, 0x10c5: 0x00c0,
	0x10c8: 0x00c0, 0x10c9: 0x00c0, 0x10ca: 0x00c0, 0x10cb: 0x00c0,
	0x10cc: 0x00c0, 0x10cd: 0x00c0, 0x10ce: 0x00c0, 0x10cf: 0x00c0, 0x10d0: 0x00c0, 0x10d1: 0x00c0,
	0x10d2: 0x00c0, 0x10d3: 0x00c0, 0x10d4: 0x00c0, 0x10d5: 0x00c0, 0x10d6: 0x00c0,
	0x10d8: 0x00c0, 0x10d9: 0x00c0, 0x10da: 0x00c0, 0x10db: 0x00c0, 0x10dc: 0x00c0, 0x10dd: 0x00c0,
	0x10de: 0x00c0, 0x10df: 0x00c0, 0x10e0: 0x00c0, 0x10e1: 0x00c0, 0x10e2: 0x00c0, 0x10e3: 0x00c0,
	0x10e4: 0x00c0, 0x10e5: 0x00c0, 0x10e6: 0x00c0, 0x10e7: 0x00c0, 0x10e8: 0x00c0, 0x10e9: 0x00c0,
	0x10ea: 0x00c0, 0x10eb: 0x00c0, 0x10ec: 0x00c0, 0x10ed: 0x00c0, 0x10ee: 0x00c0, 0x10ef: 0x00c0,
	0x10f0: 0x00c0, 0x10f1: 0x00c0, 0x10f2: 0x00c0, 0x10f3: 0x00c0, 0x10f4: 0x00c0, 0x10f5: 0x00c0,
	0x10f6: 0x00c0, 0x10f7: 0x00c0, 0x10f8: 0x00c0, 0x10f9: 0x00c0, 0x10fa: 0x00c0, 0x10fb: 0x00c0,
	0x10fc: 0x00c0, 0x10fd: 0x00c0, 0x10fe: 0x00c0, 0x10ff: 0x00c0,
	// Block 0x44, offset 0x1100
	0x1100: 0x00c0, 0x1101: 0x00c0, 0x1102: 0x00c0, 0x1103: 0x00c0, 0x1104: 0x00c0, 0x1105: 0x00c0,
	0x1106: 0x00c0, 0x1107: 0x00c0, 0x1108: 0x00c0, 0x1109: 0x00c0, 0x110a: 0x00c0, 0x110b: 0x00c0,
	0x110c: 0x00c0, 0x110d: 0x00c0, 0x110e: 0x00c0, 0x110f: 0x00c0, 0x1110: 0x00c0,
	0x1112: 0x00c0, 0x1113: 0x00c0, 0x1114: 0x00c0, 0x1115: 0x00c0,
	0x1118: 0x00c0, 0x1119: 0x00c0, 0x111a: 0x00c0, 0x111b: 0x00c0, 0x111c: 0x00c0, 0x111d: 0x00c0,
	0x111e: 0x00c0, 0x111f: 0x00c0, 0x1120: 0x00c0, 0x1121: 0x00c0, 0x1122: 0x00c0, 0x1123: 0x00c0,
	0x1124: 0x00c0, 0x1125: 0x00c0, 0x1126: 0x00c0, 0x1127: 0x00c0, 0x1128: 0x00c0, 0x1129: 0x00c0,
	0x112a: 0x00c0, 0x112b: 0x00c0, 0x112c: 0x00c0, 0x112d: 0x00c0, 0x112e: 0x00c0, 0x112f: 0x00c0,
	0x1130: 0x00c0, 0x1131: 0x00c0, 0x1132: 0x00c0, 0x1133: 0x00c0, 0x1134: 0x00c0, 0x1135: 0x00c0,
	0x1136: 0x00c0, 0x1137: 0x00c0, 0x1138: 0x00c0, 0x1139: 0x00c0, 0x113a: 0x00c0, 0x113b: 0x00c0,
	0x113c: 0x00c0, 0x113d: 0x00c0, 0x113e: 0x00c0, 0x113f: 0x00c0,
	// Block 0x45, offset 0x1140
	0x1140: 0x00c0, 0x1141: 0x00c0, 0x1142: 0x00c0, 0x1143: 0x00c0, 0x1144: 0x00c0, 0x1145: 0x00c0,
	0x1146: 0x00c0, 0x1147: 0x00c0, 0x1148: 0x00c0, 0x1149: 0x00c0, 0x114a: 0x00c0, 0x114b: 0x00c0,
	0x114c: 0x00c0, 0x114d: 0x00c0, 0x114e: 0x00c0, 0x114f: 0x00c0, 0x1150: 0x00c0, 0x1151: 0x00c0,
	0x1152: 0x00c0, 0x1153: 0x00c0, 0x1154: 0x00c0, 0x1155: 0x00c0, 0x1156: 0x00c0, 0x1157: 0x00c0,
	0x1158: 0x00c0, 0x1159: 0x00c0, 0x115a: 0x00c0, 0x115d: 0x00c3,
	0x115e: 0x00c3, 0x115f: 0x00c3, 0x1160: 0x0080, 0x1161: 0x0080, 0x1162: 0x0080, 0x1163: 0x0080,
	0x1164: 0x0080, 0x1165: 0x0080, 0x1166: 0x0080, 0x1167: 0x0080, 0x1168: 0x0080, 0x1169: 0x0080,
	0x116a: 0x0080, 0x116b: 0x0080, 0x116c: 0x0080, 0x116d: 0x0080, 0x116e: 0x0080, 0x116f: 0x0080,
	0x1170: 0x0080, 0x1171: 0x0080, 0x1172: 0x0080, 0x1173: 0x0080, 0x1174: 0x0080, 0x1175: 0x0080,
	0x1176: 0x0080, 0x1177: 0x0080, 0x1178: 0x0080, 0x1179: 0x0080, 0x117a: 0x0080, 0x117b: 0x0080,
	0x117c: 0x0080,
	// Block 0x46, offset 0x1180
	0x1180: 0x00c0, 0x1181: 0x00c0, 0x1182: 0x00c0, 0x1183: 0x00c0, 0x1184: 0x00c0, 0x1185: 0x00c0,
	0x1186: 0x00c0, 0x1187: 0x00c0, 0x1188: 0x00c0, 0x1189: 0x00c0, 0x118a: 0x00c0, 0x118b: 0x00c0,
	0x118c: 0x00c0, 0x118d: 0x00c0, 0x118e: 0x00c0, 0x118f: 0x00c0, 0x1190: 0x0080, 0x1191: 0x0080,
	0x1192: 0x0080, 0x1193: 0x0080, 0x1194: 0x0080, 0x1195: 0x0080, 0x1196: 0x0080, 0x1197: 0x0080,
	0x1198: 0x0080, 0x1199: 0x0080,
	0x11a0: 0x00c0, 0x11a1: 0x00c0, 0x11a2: 0x00c0, 0x11a3: 0x00c0,
	0x11a4: 0x00c0, 0x11a5: 0x00c0, 0x11a6: 0x00c0, 0x11a7: 0x00c0, 0x11a8: 0x00c0, 0x11a9: 0x00c0,
	0x11aa: 0x00c0, 0x11ab: 0x00c0, 0x11ac: 0x00c0, 0x11ad: 0x00c0, 0x11ae: 0x00c0, 0x11af: 0x00c0,
	0x11b0: 0x00c0, 0x11b1: 0x00c0, 0x11b2: 0x00c0, 0x11b3: 0x00c0, 0x11b4: 0x00c0, 0x11b5: 0x00c0,
	0x11b6: 0x00c0, 0x11b7: 0x00c0, 0x11b8: 0x00c0, 0x11b9: 0x00c0, 0x11ba: 0x00c0, 0x11bb: 0x00c0,
	0x11bc: 0x00c0, 0x11bd: 0x00c0, 0x11be: 0x00c0, 0x11bf: 0x00c0,
	// Block 0x47, offset 0x11c0
	0x11c0: 0x00c0, 0x11c1: 0x00c0, 0x11c2: 0x00c0, 0x11c3: 0x00c0, 0x11c4: 0x00c0, 0x11c5: 0x00c0,
	0x11c6: 0x00c0, 0x11c7: 0x00c0, 0x11c8: 0x00c0, 0x11c9: 0x00c0, 0x11ca: 0x00c0, 0x11cb: 0x00c0,
	0x11cc: 0x00c0, 0x11cd: 0x00c0, 0x11ce: 0x00c0, 0x11cf: 0x00c0, 0x11d0: 0x00c0, 0x11d1: 0x00c0,
	0x11d2: 0x00c0, 0x11d3: 0x00c0, 0x11d4: 0x00c0, 0x11d5: 0x00c0, 0x11d6: 0x00c0, 0x11d7: 0x00c0,
	0x11d8: 0x00c0, 0x11d9: 0x00c0, 0x11da: 0x00c0, 0x11db: 0x00c0, 0x11dc: 0x00c0, 0x11dd: 0x00c0,
	0x11de: 0x00c0, 0x11df: 0x00c0, 0x11e0: 0x00c0, 0x11e1: 0x00c0, 0x11e2: 0x00c0, 0x11e3: 0x00c0,
	0x11e4: 0x00c0, 0x11e5: 0x00c0, 0x11e6: 0x00c0, 0x11e7: 0x00c0, 0x11e8: 0x00c0, 0x11e9: 0x00c0,
	0x11ea: 0x00c0, 0x11eb: 0x00c0, 0x11ec: 0x00c0, 0x11ed: 0x00c0, 0x11ee: 0x00c0, 0x11ef: 0x00c0,
	0x11f0: 0x00c0, 0x11f1: 0x00c0, 0x11f2: 0x00c0, 0x11f3: 0x00c0, 0x11f4: 0x00c0, 0x11f5: 0x00c0,
	0x11f8: 0x00c0, 0x11f9: 0x00c0, 0x11fa: 0x00c0, 0x11fb: 0x00c0,
	0x11fc: 0x00c0, 0x11fd: 0x00c0,
	// Block 0x48, offset 0x1200
	0x1200: 0x0080, 0x1201: 0x00c0, 0x1202: 0x00c0, 0x1203: 0x00c0, 0x1204: 0x00c0, 0x1205: 0x00c0,
	0x1206: 0x00c0, 0x1207: 0x00c0, 0x1208: 0x00c0, 0x1209: 0x00c0, 0x120a: 0x00c0, 0x120b: 0x00c0,
	0x120c: 0x00c0, 0x120d: 0x00c0, 0x120e: 0x00c0, 0x120f: 0x00c0, 0x1210: 0x00c0, 0x1211: 0x00c0,
	0x1212: 0x00c0, 0x1213: 0x00c0, 0x1214: 0x00c0, 0x1215: 0x00c0, 0x1216: 0x00c0, 0x1217: 0x00c0,
	0x1218: 0x00c0, 0x1219: 0x00c0, 0x121a: 0x00c0, 0x121b: 0x00c0, 0x121c: 0x00c0, 0x121d: 0x00c0,
	0x121e: 0x00c0, 0x121f: 0x00c0, 0x1220: 0x00c0, 0x1221: 0x00c0, 0x1222: 0x00c0, 0x1223: 0x00c0,
	0x1224: 0x00c0, 0x1225: 0x00c0, 0x1226: 0x00c0, 0x1227: 0x00c0, 0x1228: 0x00c0, 0x1229: 0x00c0,
	0x122a: 0x00c0, 0x122b: 0x00c0, 0x122c: 0x00c0, 0x122d: 0x00c0, 0x122e: 0x00c0, 0x122f: 0x00c0,
	0x1230: 0x00c0, 0x1231: 0x00c0, 0x1232: 0x00c0, 0x1233: 0x00c0, 0x1234: 0x00c0, 0x1235: 0x00c0,
	0x1236: 0x00c0, 0x1237: 0x00c0, 0x1238: 0x00c0, 0x1239: 0x00c0, 0x123a: 0x00c0, 0x123b: 0x00c0,
	0x123c: 0x00c0, 0x123d: 0x00c0, 0x123e: 0x00c0, 0x123f: 0x00c0,
	// Block 0x49, offset 0x1240
	0x1240: 0x00c0, 0x1241: 0x00c0, 0x1242: 0x00c0, 0x1243: 0x00c0, 0x1244: 0x00c0, 0x1245: 0x00c0,
	0x1246: 0x00c0, 0x1247: 0x00c0, 0x1248: 0x00c0, 0x1249: 0x00c0, 0x124a: 0x00c0, 0x124b: 0x00c0,
	0x124c: 0x00c0, 0x124d: 0x00c0, 0x124e: 0x00c0, 0x124f: 0x00c0, 0x1250: 0x00c0, 0x1251: 0x00c0,
	0x1252: 0x00c0, 0x1253: 0x00c0, 0x1254: 0x00c0, 0x1255: 0x00c0, 0x1256: 0x00c0, 0x1257: 0x00c0,
	0x1258: 0x00c0, 0x1259: 0x00c0, 0x125a: 0x00c0, 0x125b: 0x00c0, 0x125c: 0x00c0, 0x125d: 0x00c0,
	0x125e: 0x00c0, 0x125f: 0x00c0, 0x1260: 0x00c0, 0x1261: 0x00c0, 0x1262: 0x00c0, 0x1263: 0x00c0,
	0x1264: 0x00c0, 0x1265: 0x00c0, 0x1266: 0x00c0, 0x1267: 0x00c0, 0x1268: 0x00c0, 0x1269: 0x00c0,
	0x126a: 0x00c0, 0x126b: 0x00c0, 0x126c: 0x00c0, 0x126d: 0x0080, 0x126e: 0x0080, 0x126f: 0x00c0,
	0x1270: 0x00c0, 0x1271: 0x00c0, 0x1272: 0x00c0, 0x1273: 0x00c0, 0x1274: 0x00c0, 0x1275: 0x00c0,
	0x1276: 0x00c0, 0x1277: 0x00c0, 0x1278: 0x00c0, 0x1279: 0x00c0, 0x127a: 0x00c0, 0x127b: 0x00c0,
	0x127c: 0x00c0, 0x127d: 0x00c0, 0x127e: 0x00c0, 0x127f: 0x00c0,
	// Block 0x4a, offset 0x1280
	0x1280: 0x0080, 0x1281: 0x00c0, 0x1282: 0x00c0, 0x1283: 0x00c0, 0x1284: 0x00c0, 0x1285: 0x00c0,
	0x1286: 0x00c0, 0x1287: 0x00c0, 0x1288: 0x00c0, 0x1289: 0x00c0, 0x128a: 0x00c0, 0x128b: 0x00c0,
	0x128c: 0x00c0, 0x128d: 0x00c0, 0x128e: 0x00c0, 0x128f: 0x00c0, 0x1290: 0x00c0, 0x1291: 0x00c0,
	0x1292: 0x00c0, 0x1293: 0x00c0, 0x1294: 0x00c0, 0x1295: 0x00c0, 0x1296: 0x00c0, 0x1297: 0x00c0,
	0x1298: 0x00c0, 0x1299: 0x00c0, 0x129a: 0x00c0, 0x129b: 0x0080, 0x129c: 0x0080,
	0x12a0: 0x00c0, 0x12a1: 0x00c0, 0x12a2: 0x00c0, 0x12a3: 0x00c0,
	0x12a4: 0x00c0, 0x12a5: 0x00c0, 0x12a6: 0x00c0, 0x12a7: 0x00c0, 0x12a8: 0x00c0, 0x12a9: 0x00c0,
	0x12aa: 0x00c0, 0x12ab: 0x00c0, 0x12ac: 0x00c0, 0x12ad: 0x00c0, 0x12ae: 0x00c0, 0x12af: 0x00c0,
	0x12b0: 0x00c0, 0x12b1: 0x00c0, 0x12b2: 0x00c0, 0x12b3: 0x00c0, 0x12b4: 0x00c0, 0x12b5: 0x00c0,
	0x12b6: 0x00c0, 0x12b7: 0x00c0, 0x12b8: 0x00c0, 0x12b9: 0x00c0, 0x12ba: 0x00c0, 0x12bb: 0x00c0,
	0x12bc: 0x00c0, 0x12bd: 0x00c0, 0x12be: 0x00c0, 0x12bf: 0x00c0,
	// Block 0x4b, offset 0x12c0
	0x12c0: 0x00c0, 0x12c1: 0x00c0, 0x12c2: 0x00c0, 0x12c3: 0x00c0, 0x12c4: 0x00c0, 0x12c5: 0x00c0,
	0x12c6: 0x00c0, 0x12c7: 0x00c0, 0x12c8: 0x00c0, 0x12c9: 0x00c0, 0x12ca: 0x00c0, 0x12cb: 0x00c0,
	0x12cc: 0x00c0, 0x12cd: 0x00c0, 0x12ce: 0x00c0, 0x12cf: 0x00c0, 0x12d0: 0x00c0, 0x12d1: 0x00c0,
	0x12d2: 0x00c0, 0x12d3: 0x00c0, 0x12d4: 0x00c0, 0x12d5: 0x00c0, 0x12d6: 0x00c0, 0x12d7: 0x00c0,
	0x12d8: 0x00c0, 0x12d9: 0x00c0, 0x12da: 0x00c0, 0x12db: 0x00c0, 0x12dc: 0x00c0, 0x12dd: 0x00c0,
	0x12de: 0x00c0, 0x12df: 0x00c0, 0x12e0: 0x00c0, 0x12e1: 0x00c0, 0x12e2: 0x00c0, 0x12e3: 0x00c0,
	0x12e4: 0x00c0, 0x12e5: 0x00c0, 0x12e6: 0x00c0, 0x12e7: 0x00c0, 0x12e8: 0x00c0, 0x12e9: 0x00c0,
	0x12ea: 0x00c0, 0x12eb: 0x0080, 0x12ec: 0x0080, 0x12ed: 0x0080, 0x12ee: 0x0080, 0x12ef: 0x0080,
	0x12f0: 0x0080, 0x12f1: 0x00c0, 0x12f2: 0x00c0, 0x12f3: 0x00c0, 0x12f4: 0x00c0, 0x12f5: 0x00c0,
	0x12f6: 0x00c0, 0x12f7: 0x00c0, 0x12f8: 0x00c0,
	// Block 0x4c, offset 0x1300
	0x1300: 0x00c0, 0x1301: 0x00c0, 0x1302: 0x00c0, 0x1303: 0x00c0, 0x1304: 0x00c0, 0x1305: 0x00c0,
	0x1306: 0x00c0, 0x1307: 0x00c0, 0x1308: 0x00c0, 0x1309: 0x00c0, 0x130a: 0x00c0, 0x130b: 0x00c0,
	0x130c: 0x00c0, 0x130e: 0x00c0, 0x130f: 0x00c0, 0x1310: 0x00c0, 0x1311: 0x00c0,
	0x1312: 0x00c3, 0x1313: 0x00c3, 0x1314: 0x00c6,
	0x1320: 0x00c0, 0x1321: 0x00c0, 0x1322: 0x00c0, 0x1323: 0x00c0,
	0x1324: 0x00c0, 0x1325: 0x00c0, 0x1326: 0x00c0, 0x1327: 0x00c0, 0x1328: 0x00c0, 0x1329: 0x00c0,
	0x132a: 0x00c0, 0x132b: 0x00c0, 0x132c: 0x00c0, 0x132d: 0x00c0, 0x132e: 0x00c0, 0x132f: 0x00c0,
	0x1330: 0x00c0, 0x1331: 0x00c0, 0x1332: 0x00c3, 0x1333: 0x00c3, 0x1334: 0x00c6, 0x1335: 0x0080,
	0x1336: 0x0080,
	// Block 0x4d, offset 0x1340
	0x1340: 0x00c0, 0x1341: 0x00c0, 0x1342: 0x00c0, 0x1343: 0x00c0, 0x1344: 0x00c0, 0x1345: 0x00c0,
	0x1346: 0x00c0, 0x1347: 0x00c0, 0x1348: 0x00c0, 0x1349: 0x00c0, 0x134a: 0x00c0, 0x134b: 0x00c0,
	0x134c: 0x00c0, 0x134d: 0x00c0, 0x134e: 0x00c0, 0x134f: 0x00c0, 0x1350: 0x00c0, 0x1351: 0x00c0,
	0x1352: 0x00c3, 0x1353: 0x00c3,
	0x1360: 0x00c0, 0x1361: 0x00c0, 0x1362: 0x00c0, 0x1363: 0x00c0,
	0x1364: 0x00c0, 0x1365: 0x00c0, 0x1366: 0x00c0, 0x1367: 0x00c0, 0x1368: 0x00c0, 0x1369: 0x00c0,
	0x136a: 0x00c0, 0x136b: 0x00c0, 0x136c: 0x00c0, 0x136e: 0x00c0, 0x136f: 0x00c0,
	0x1370: 0x00c0, 0x1372: 0x00c3, 0x1373: 0x00c3,
	// Block 0x4e, offset 0x1380
	0x1380: 0x00c0, 0x1381: 0x00c0, 0x1382: 0x00c0, 0x1383: 0x00c0, 0x1384: 0x00c0, 0x1385: 0x00c0,
	0x1386: 0x00c0, 0x1387: 0x00c0, 0x1388: 0x00c0, 0x1389: 0x00c0, 0x138a: 0x00c0, 0x138b: 0x00c0,
	0x138c: 0x00c0, 0x138d: 0x00c0, 0x138e: 0x00c0, 0x138f: 0x00c0, 0x1390: 0x00c0, 0x1391: 0x00c0,
	0x1392: 0x00c0, 0x1393: 0x00c0, 0x1394: 0x00c0, 0x1395: 0x00c0, 0x1396: 0x00c0, 0x1397: 0x00c0,
	0x1398: 0x00c0, 0x1399: 0x00c0, 0x139a: 0x00c0, 0x139b: 0x00c0, 0x139c: 0x00c0, 0x139d: 0x00c0,
	0x139e: 0x00c0, 0x139f: 0x00c0, 0x13a0: 0x00c0, 0x13a1: 0x00c0, 0x13a2: 0x00c0, 0x13a3: 0x00c0,
	0x13a4: 0x00c0, 0x13a5: 0x00c0, 0x13a6: 0x00c0, 0x13a7: 0x00c0, 0x13a8: 0x00c0, 0x13a9: 0x00c0,
	0x13aa: 0x00c0, 0x13ab: 0x00c0, 0x13ac: 0x00c0, 0x13ad: 0x00c0, 0x13ae: 0x00c0, 0x13af: 0x00c0,
	0x13b0: 0x00c0, 0x13b1: 0x00c0, 0x13b2: 0x00c0, 0x13b3: 0x00c0, 0x13b4: 0x0040, 0x13b5: 0x0040,
	0x13b6: 0x00c0, 0x13b7: 0x00c3, 0x13b8: 0x00c3, 0x13b9: 0x00c3, 0x13ba: 0x00c3, 0x13bb: 0x00c3,
	0x13bc: 0x00c3, 0x13bd: 0x00c3, 0x13be: 0x00c0, 0x13bf: 0x00c0,
	// Block 0x4f, offset 0x13c0
	0x13c0: 0x00c0, 0x13c1: 0x00c0, 0x13c2: 0x00c0, 0x13c3: 0x00c0, 0x13c4: 0x00c0, 0x13c5: 0x00c0,
	0x13c6: 0x00c3, 0x13c7: 0x00c0, 0x13c8: 0x00c0, 0x13c9: 0x00c3, 0x13ca: 0x00c3, 0x13cb: 0x00c3,
	0x13cc: 0x00c3, 0x13cd: 0x00c3, 0x13ce: 0x00c3, 0x13cf: 0x00c3, 0x13d0: 0x00c3, 0x13d1: 0x00c3,
	0x13d2: 0x00c6, 0x13d3: 0x00c3, 0x13d4: 0x0080, 0x13d5: 0x0080, 0x13d6: 0x0080, 0x13d7: 0x00c0,
	0x13d8: 0x0080, 0x13d9: 0x0080, 0x13da: 0x0080, 0x13db: 0x0080, 0x13dc: 0x00c0, 0x13dd: 0x00c3,
	0x13e0: 0x00c0, 0x13e1: 0x00c0, 0x13e2: 0x00c0, 0x13e3: 0x00c0,
	0x13e4: 0x00c0, 0x13e5: 0x00c0, 0x13e6: 0x00c0, 0x13e7: 0x00c0, 0x13e8: 0x00c0, 0x13e9: 0x00c0,
	0x13f0: 0x0080, 0x13f1: 0x0080, 0x13f2: 0x0080, 0x13f3: 0x0080, 0x13f4: 0x0080, 0x13f5: 0x0080,
	0x13f6: 0x0080, 0x13f7: 0x0080, 0x13f8: 0x0080, 0x13f9: 0x0080,
	// Block 0x50, offset 0x1400
	0x1400: 0x0080, 0x1401: 0x0080, 0x1402: 0x0080, 0x1403: 0x0080, 0x1404: 0x0080, 0x1405: 0x0080,
	0x1406: 0x0080, 0x1407: 0x0082, 0x1408: 0x0080, 0x1409: 0x0080, 0x140a: 0x0080, 0x140b: 0x0040,
	0x140c: 0x0040, 0x140d: 0x0040, 0x140e: 0x0040, 0x1410: 0x00c0, 0x1411: 0x00c0,
	0x1412: 0x00c0, 0x1413: 0x00c0, 0x1414: 0x00c0, 0x1415: 0x00c0, 0x1416: 0x00c0, 0x1417: 0x00c0,
	0x1418: 0x00c0, 0x1419: 0x00c0,
	0x1420: 0x00c2, 0x1421: 0x00c2, 0x1422: 0x00c2, 0x1423: 0x00c2,
	0x1424: 0x00c2, 0x1425: 0x00c2, 0x1426: 0x00c2, 0x1427: 0x00c2, 0x1428: 0x00c2, 0x1429: 0x00c2,
	0x142a: 0x00c2, 0x142b: 0x00c2, 0x142c: 0x00c2, 0x142d: 0x00c2, 0x142e: 0x00c2, 0x142f: 0x00c2,
	0x1430: 0x00c2, 0x1431: 0x00c2, 0x1432: 0x00c2, 0x1433: 0x00c2, 0x1434: 0x00c2, 0x1435: 0x00c2,
	0x1436: 0x00c2, 0x1437: 0x00c2, 0x1438: 0x00c2, 0x1439: 0x00c2, 0x143a: 0x00c2, 0x143b: 0x00c2,
	0x143c: 0x00c2, 0x143d: 0x00c2, 0x143e: 0x00c2, 0x143f: 0x00c2,
	// Block 0x51, offset 0x1440
	0x1440: 0x00c2, 0x1441: 0x00c2, 0x1442: 0x00c2, 0x1443: 0x00c2, 0x1444: 0x00c2, 0x1445: 0x00c2,
	0x1446: 0x00c2, 0x1447: 0x00c2, 0x1448: 0x00c2, 0x1449: 0x00c2, 0x144a: 0x00c2, 0x144b: 0x00c2,
	0x144c: 0x00c2, 0x144d: 0x00c2, 0x144e: 0x00c2, 0x144f: 0x00c2, 0x1450: 0x00c2, 0x1451: 0x00c2,
	0x1452: 0x00c2, 0x1453: 0x00c2, 0x1454: 0x00c2, 0x1455: 0x00c2, 0x1456: 0x00c2, 0x1457: 0x00c2,
	0x1458: 0x00c2, 0x1459: 0x00c2, 0x145a: 0x00c2, 0x145b: 0x00c2, 0x145c: 0x00c2, 0x145d: 0x00c2,
	0x145e: 0x00c2, 0x145f: 0x00c2, 0x1460: 0x00c2, 0x1461: 0x00c2, 0x1462: 0x00c2, 0x1463: 0x00c2,
	0x1464: 0x00c2, 0x1465: 0x00c2, 0x1466: 0x00c2, 0x1467: 0x00c2, 0x1468: 0x00c2, 0x1469: 0x00c2,
	0x146a: 0x00c2, 0x146b: 0x00c2, 0x146c: 0x00c2, 0x146d: 0x00c2, 0x146e: 0x00c2, 0x146f: 0x00c2,
	0x1470: 0x00c2, 0x1471: 0x00c2, 0x1472: 0x00c2, 0x1473: 0x00c2, 0x1474: 0x00c2, 0x1475: 0x00c2,
	0x1476: 0x00c2, 0x1477: 0x00c2, 0x1478: 0x00c2,
	// Block 0x52, offset 0x1480
	0x1480: 0x00c0, 0x1481: 0x00c0, 0x1482: 0x00c0, 0x1483: 0x00c0, 0x1484: 0x00c0, 0x1485: 0x00c3,
	0x1486: 0x00c3, 0x1487: 0x00c2, 0x1488: 0x00c2, 0x1489: 0x00c2, 0x148a: 0x00c2, 0x148b: 0x00c2,
	0x148c: 0x00c2, 0x148d: 0x00c2, 0x148e: 0x00c2, 0x148f: 0x00c2, 0x1490: 0x00c2, 0x1491: 0x00c2,
	0x1492: 0x00c2, 0x1493: 0x00c2, 0x1494: 0x00c2, 0x1495: 0x00c2, 0x1496: 0x00c2, 0x1497: 0x00c2,
	0x1498: 0x00c2, 0x1499: 0x00c2, 0x149a: 0x00c2, 0x149b: 0x00c2, 0x149c: 0x00c2, 0x149d: 0x00c2,
	0x149e: 0x00c2, 0x149f: 0x00c2, 0x14a0: 0x00c2, 0x14a1: 0x00c2, 0x14a2: 0x00c2, 0x14a3: 0x00c2,
	0x14a4: 0x00c2, 0x14a5: 0x00c2, 0x14a6: 0x00c2, 0x14a7: 0x00c2, 0x14a8: 0x00c2, 0x14a9: 0x00c3,
	0x14aa: 0x00c2,
	0x14b0: 0x00c0, 0x14b1: 0x00c0, 0x14b2: 0x00c0, 0x14b3: 0x00c0, 0x14b4: 0x00c0, 0x14b5: 0x00c0,
	0x14b6: 0x00c0, 0x14b7: 0x00c0, 0x14b8: 0x00c0, 0x14b9: 0x00c0, 0x14ba: 0x00c0, 0x14bb: 0x00c0,
	0x14bc: 0x00c0, 0x14bd: 0x00c0, 0x14be: 0x00c0, 0x14bf: 0x00c0,
	// Block 0x53, offset 0x14c0
	0x14c0: 0x00c0, 0x14c1: 0x00c0, 0x14c2: 0x00c0, 0x14c3: 0x00c0, 0x14c4: 0x00c0, 0x14c5: 0x00c0,
	0x14c6: 0x00c0, 0x14c7: 0x00c0, 0x14c8: 0x00c0, 0x14c9: 0x00c0, 0x14ca: 0x00c0, 0x14cb: 0x00c0,
	0x14cc: 0x00c0, 0x14cd: 0x00c0, 0x14ce: 0x00c0, 0x14cf: 0x00c0, 0x14d0: 0x00c0, 0x14d1: 0x00c0,
	0x14d2: 0x00c0, 0x14d3: 0x00c0, 0x14d4: 0x00c0, 0x14d5: 0x00c0, 0x14d6: 0x00c0, 0x14d7: 0x00c0,
	0x14d8: 0x00c0, 0x14d9: 0x00c0, 0x14da: 0x00c0, 0x14db: 0x00c0, 0x14dc: 0x00c0, 0x14dd: 0x00c0,
	0x14de: 0x00c0, 0x14df: 0x00c0, 0x14e0: 0x00c0, 0x14e1: 0x00c0, 0x14e2: 0x00c0, 0x14e3: 0x00c0,
	0x14e4: 0x00c0, 0x14e5: 0x00c0, 0x14e6: 0x00c0, 0x14e7: 0x00c0, 0x14e8: 0x00c0, 0x14e9: 0x00c0,
	0x14ea: 0x00c0, 0x14eb: 0x00c0, 0x14ec: 0x00c0, 0x14ed: 0x00c0, 0x14ee: 0x00c0, 0x14ef: 0x00c0,
	0x14f0: 0x00c0, 0x14f1: 0x00c0, 0x14f2: 0x00c0, 0x14f3: 0x00c0, 0x14f4: 0x00c0, 0x14f5: 0x00c0,
	// Block 0x54, offset 0x1500
	0x1500: 0x00c0, 0x1501: 0x00c0, 0x1502: 0x00c0, 0x1503: 0x00c0, 0x1504: 0x00c0, 0x1505: 0x00c0,
	0x1506: 0x00c0, 0x1507: 0x00c0, 0x1508: 0x00c0, 0x1509: 0x00c0, 0x150a: 0x00c0, 0x150b: 0x00c0,
	0x150c: 0x00c0, 0x150d: 0x00c0, 0x150e: 0x00c0, 0x150f: 0x00c0, 0x1510: 0x00c0, 0x1511: 0x00c0,
	0x1512: 0x00c0, 0x1513: 0x00c0, 0x1514: 0x00c0, 0x1515: 0x00c0, 0x1516: 0x00c0, 0x1517: 0x00c0,
	0x1518: 0x00c0, 0x1519: 0x00c0, 0x151a: 0x00c0, 0x151b: 0x00c0, 0x151c: 0x00c0, 0x151d: 0x00c0,
	0x151e: 0x00c0, 0x1520: 0x00c3, 0x1521: 0x00c3, 0x1522: 0x00c3, 0x1523: 0x00c0,
	0x1524: 0x00c0, 0x1525: 0x00c0, 0x1526: 0x00c0, 0x1527: 0x00c3, 0x1528: 0x00c3, 0x1529: 0x00c0,
	0x152a: 0x00c0, 0x152b: 0x00c0,
	0x1530: 0x00c0, 0x1531: 0x00c0, 0x1532: 0x00c3, 0x1533: 0x00c0, 0x1534: 0x00c0, 0x1535: 0x00c0,
	0x1536: 0x00c0, 0x1537: 0x00c0, 0x1538: 0x00c0, 0x1539: 0x00c3, 0x153a: 0x00c3, 0x153b: 0x00c3,
	// Block 0x55, offset 0x1540
	0x1540: 0x0080, 0x1544: 0x0080, 0x1545: 0x0080,
	0x1546: 0x00c0, 0x1547: 0x00c0, 0x1548: 0x00c0, 0x1549: 0x00c0, 0x154a: 0x00c0, 0x154b: 0x00c0,
	0x154c: 0x00c0, 0x154d: 0x00c0, 0x154e: 0x00c0, 0x154f: 0x00c0, 0x1550: 0x00c0, 0x1551: 0x00c0,
	0x1552: 0x00c0, 0x1553: 0x00c0, 0x1554: 0x00c0, 0x1555: 0x00c0, 0x1556: 0x00c0, 0x1557: 0x00c0,
	0x1558: 0x00c0, 0x1559: 0x00c0, 0x155a: 0x00c0, 0x155b: 0x00c0, 0x155c: 0x00c0, 0x155d: 0x00c0,
	0x155e: 0x00c0, 0x155f: 0x00c0, 0x1560: 0x00c0, 0x1561: 0x00c0, 0x1562: 0x00c0, 0x1563: 0x00c0,
	0x1564: 0x00c0, 0x1565: 0x00c0, 0x1566: 0x00c0, 0x1567: 0x00c0, 0x1568: 0x00c0, 0x1569: 0x00c0,
	0x156a: 0x00c0, 0x156b: 0x00c0, 0x156c: 0x00c0, 0x156d: 0x00c0,
	0x1570: 0x00c0, 0x1571: 0x00c0, 0x1572: 0x00c0, 0x1573: 0x00c0, 0x1574: 0x00c0,
	// Block 0x56, offset 0x1580
	0x1580: 0x00c0, 0x1581: 0x00c0, 0x1582: 0x00c0, 0x1583: 0x00c0, 0x1584: 0x00c0, 0x1585: 0x00c0,
	0x1586: 0x00c0, 0x1587: 0x00c0, 0x1588: 0x00c0, 0x1589: 0x00c0, 0x158a: 0x00c0, 0x158b: 0x00c0,
	0x158c: 0x00c0, 0x158d: 0x00c0, 0x158e: 0x00c0, 0x158f: 0x00c0, 0x1590: 0x00c0, 0x1591: 0x00c0,
	0x1592: 0x00c0, 0x1593: 0x00c0, 0x1594: 0x00c0, 0x1595: 0x00c0, 0x1596: 0x00c0, 0x1597: 0x00c0,
	0x1598: 0x00c0, 0x1599: 0x00c0, 0x159a: 0x00c0, 0x159b: 0x00c0, 0x159c: 0x00c0, 0x159d: 0x00c0,
	0x159e: 0x00c0, 0x159f: 0x00c0, 0x15a0: 0x00c0, 0x15a1: 0x00c0, 0x15a2: 0x00c0, 0x15a3: 0x00c0,
	0x15a4: 0x00c0, 0x15a5: 0x00c0, 0x15a6: 0x00c0, 0x15a7: 0x00c0, 0x15a8: 0x00c0, 0x15a9: 0x00c0,
	0x15aa: 0x00c0, 0x15ab: 0x00c0,
	0x15b0: 0x00c0, 0x15b1: 0x00c0, 0x15b2: 0x00c0, 0x15b3: 0x00c0, 0x15b4: 0x00c0, 0x15b5: 0x00c0,
	0x15b6: 0x00c0, 0x15b7: 0x00c0, 0x15b8: 0x00c0, 0x15b9: 0x00c0, 0x15ba: 0x00c0, 0x15bb: 0x00c0,
	0x15bc: 0x00c0, 0x15bd: 0x00c0, 0x15be: 0x00c0, 0x15bf: 0x00c0,
	// Block 0x57, offset 0x15c0
	0x15c0: 0x00c0, 0x15c1: 0x00c0, 0x15c2: 0x00c0, 0x15c3: 0x00c0, 0x15c4: 0x00c0, 0x15c5: 0x00c0,
	0x15c6: 0x00c0, 0x15c7: 0x00c0, 0x15c8: 0x00c0, 0x15c9: 0x00c0,
	0x15d0: 0x00c0, 0x15d1: 0x00c0,
	0x15d2: 0x00c0, 0x15d3: 0x00c0, 0x15d4: 0x00c0, 0x15d5: 0x00c0, 0x15d6: 0x00c0, 0x15d7: 0x00c0,
	0x15d8: 0x00c0, 0x15d9: 0x00c0, 0x15da: 0x0080,
	0x15de: 0x0080, 0x15df: 0x0080, 0x15e0: 0x0080, 0x15e1: 0x0080, 0x15e2: 0x0080, 0x15e3: 0x0080,
	0x15e4: 0x0080, 0x15e5: 0x0080, 0x15e6: 0x0080, 0x15e7: 0x0080, 0x15e8: 0x0080, 0x15e9: 0x0080,
	0x15ea: 0x0080, 0x15eb: 0x0080, 0x15ec: 0x0080, 0x15ed: 0x0080, 0x15ee: 0x0080, 0x15ef: 0x0080,
	0x15f0: 0x0080, 0x15f1: 0x0080, 0x15f2: 0x0080, 0x15f3: 0x0080, 0x15f4: 0x0080, 0x15f5: 0x0080,
	0x15f6: 0x0080, 0x15f7: 0x0080, 0x15f8: 0x0080, 0x15f9: 0x0080, 0x15fa: 0x0080, 0x15fb: 0x0080,
	0x15fc: 0x0080, 0x15fd: 0x0080, 0x15fe: 0x0080, 0x15ff: 0x0080,
	// Block 0x58, offset 0x1600
	0x1600: 0x00c0, 0x1601: 0x00c0, 0x1602: 0x00c0, 0x1603: 0x00c0, 0x1604: 0x00c0, 0x1605: 0x00c0,
	0x1606: 0x00c0, 0x1607: 0x00c0, 0x1608: 0x00c0, 0x1609: 0x00c0, 0x160a: 0x00c0, 0x160b: 0x00c0,
	0x160c: 0x00c0, 0x160d: 0x00c0, 0x160e: 0x00c0, 0x160f: 0x00c0, 0x1610: 0x00c0, 0x1611: 0x00c0,
	0x1612: 0x00c0, 0x1613: 0x00c0, 0x1614: 0x00c0, 0x1615: 0x00c0, 0x1616: 0x00c0, 0x1617: 0x00c3,
	0x1618: 0x00c3, 0x1619: 0x00c0, 0x161a: 0x00c0, 0x161b: 0x00c3,
	0x161e: 0x0080, 0x161f: 0x0080, 0x1620: 0x00c0, 0x1621: 0x00c0, 0x1622: 0x00c0, 0x1623: 0x00c0,
	0x1624: 0x00c0, 0x1625: 0x00c0, 0x1626: 0x00c0, 0x1627: 0x00c0, 0x1628: 0x00c0, 0x1629: 0x00c0,
	0x162a: 0x00c0, 0x162b: 0x00c0, 0x162c: 0x00c0, 0x162d: 0x00c0, 0x162e: 0x00c0, 0x162f: 0x00c0,
	0x1630: 0x00c0, 0x1631: 0x00c0, 0x1632: 0x00c0, 0x1633: 0x00c0, 0x1634: 0x00c0, 0x1635: 0x00c0,
	0x1636: 0x00c0, 0x1637: 0x00c0, 0x1638: 0x00c0, 0x1639: 0x00c0, 0x163a: 0x00c0, 0x163b: 0x00c0,
	0x163c: 0x00c0, 0x163d: 0x00c0, 0x163e: 0x00c0, 0x163f: 0x00c0,
	// Block 0x59, offset 0x1640
	0x1640: 0x00c0, 0x1641: 0x00c0, 0x1642: 0x00c0, 0x1643: 0x00c0, 0x1644: 0x00c0, 0x1645: 0x00c0,
	0x1646: 0x00c0, 0x1647: 0x00c0, 0x1648: 0x00c0, 0x1649: 0x00c0, 0x164a: 0x00c0, 0x164b: 0x00c0,
	0x164c: 0x00c0, 0x164d: 0x00c0, 0x164e: 0x00c0, 0x164f: 0x00c0, 0x1650: 0x00c0, 0x1651: 0x00c0,
	0x1652: 0x00c0, 0x1653: 0x00c0, 0x1654: 0x00c0, 0x1655: 0x00c0, 0x1656: 0x00c3, 0x1657: 0x00c0,
	0x1658: 0x00c3, 0x1659: 0x00c3, 0x165a: 0x00c3, 0x165b: 0x00c3, 0x165c: 0x00c3, 0x165d: 0x00c3,
	0x165e: 0x00c3, 0x1660: 0x00c6, 0x1661: 0x00c0, 0x1662: 0x00c3, 0x1663: 0x00c0,
	0x1664: 0x00c0, 0x1665: 0x00c3, 0x1666: 0x00c3, 0x1667: 0x00c3, 0x1668: 0x00c3, 0x1669: 0x00c3,
	0x166a: 0x00c3, 0x166b: 0x00c3, 0x166c: 0x00c3, 0x166d: 0x00c0, 0x166e: 0x00c0, 0x166f: 0x00c0,
	0x1670: 0x00c0, 0x1671: 0x00c0, 0x1672: 0x00c0, 0x1673: 0x00c3, 0x1674: 0x00c3, 0x1675: 0x00c3,
	0x1676: 0x00c3, 0x1677: 0x00c3, 0x1678: 0x00c3, 0x1679: 0x00c3, 0x167a: 0x00c3, 0x167b: 0x00c3,
	0x167c: 0x00c3, 0x167f: 0x00c3,
	// Block 0x5a, offset 0x1680
	0x1680: 0x00c0, 0x1681: 0x00c0, 0x1682: 0x00c0, 0x1683: 0x00c0, 0x1684: 0x00c0, 0x1685: 0x00c0,
	0x1686: 0x00c0, 0x1687: 0x00c0, 0x1688: 0x00c0, 0x1689: 0x00c0,
	0x1690: 0x00c0, 0x1691: 0x00c0,
	0x1692: 0x00c0, 0x1693: 0x00c0, 0x1694: 0x00c0, 0x1695: 0x00c0, 0x1696: 0x00c0, 0x1697: 0x00c0,
	0x1698: 0x00c0, 0x1699: 0x00c0,
	0x16a0: 0x0080, 0x16a1: 0x0080, 0x16a2: 0x0080, 0x16a3: 0x0080,
	0x16a4: 0x0080, 0x16a5: 0x0080, 0x16a6: 0x0080, 0x16a7: 0x00c0, 0x16a8: 0x0080, 0x16a9: 0x0080,
	0x16aa: 0x0080, 0x16ab: 0x0080, 0x16ac: 0x0080, 0x16ad: 0x0080,
	0x16b0: 0x00c3, 0x16b1: 0x00c3, 0x16b2: 0x00c3, 0x16b3: 0x00c3, 0x16b4: 0x00c3, 0x16b5: 0x00c3,
	0x16b6: 0x00c3, 0x16b7: 0x00c3, 0x16b8: 0x00c3, 0x16b9: 0x00c3, 0x16ba: 0x00c3, 0x16bb: 0x00c3,
	0x16bc: 0x00c3, 0x16bd: 0x00c3, 0x16be: 0x0083, 0x16bf: 0x00c3,
	// Block 0x5b, offset 0x16c0
	0x16c0: 0x00c3,
	// Block 0x5c, offset 0x1700
	0x1700: 0x00c3, 0x1701: 0x00c3, 0x1702: 0x00c3, 0x1703: 0x00c3, 0x1704: 0x00c0, 0x1705: 0x00c0,
	0x1706: 0x00c0, 0x1707: 0x00c0, 0x1708: 0x00c0, 0x1709: 0x00c0, 0x170a: 0x00c0, 0x170b: 0x00c0,
	0x170c: 0x00c0, 0x170d: 0x00c0, 0x170e: 0x00c0, 0x170f: 0x00c0, 0x1710: 0x00c0, 0x1711: 0x00c0,
	0x1712: 0x00c0, 0x1713: 0x00c0, 0x1714: 0x00c0, 0x1715: 0x00c0, 0x1716: 0x00c0, 0x1717: 0x00c0,
	0x1718: 0x00c0, 0x1719: 0x00c0, 0x171a: 0x00c0, 0x171b: 0x00c0, 0x171c: 0x00c0, 0x171d: 0x00c0,
	0x171e: 0x00c0, 0x171f: 0x00c0, 0x1720: 0x00c0, 0x1721: 0x00c0, 0x1722: 0x00c0, 0x1723: 0x00c0,
	0x1724: 0x00c0, 0x1725: 0x00c0, 0x1726: 0x00c0, 0x1727: 0x00c0, 0x1728: 0x00c0, 0x1729: 0x00c0,
	0x172a: 0x00c0, 0x172b: 0x00c0, 0x172c: 0x00c0, 0x172d: 0x00c0, 0x172e: 0x00c0, 0x172f: 0x00c0,
	0x1730: 0x00c0, 0x1731: 0x00c0, 0x1732: 0x00c0, 0x1733: 0x00c0, 0x1734: 0x00c3, 0x1735: 0x00c0,
	0x1736: 0x00c3, 0x1737: 0x00c3, 0x1738: 0x00c3, 0x1739: 0x00c3, 0x173a: 0x00c3, 0x173b: 0x00c0,
	0x173c: 0x00c3, 0x173d: 0x00c0, 0x173e: 0x00c0, 0x173f: 0x00c0,
	// Block 0x5d, offset 0x1740
	0x1740: 0x00c0, 0x1741: 0x00c0, 0x1742: 0x00c3, 0x1743: 0x00c0, 0x1744: 0x00c5, 0x1745: 0x00c0,
	0x1746: 0x00c0, 0x1747: 0x00c0, 0x1748: 0x00c0, 0x1749: 0x00c0, 0x174a: 0x00c0, 0x174b: 0x00c0,
	0x1750: 0x00c0, 0x1751: 0x00c0,
	0x1752: 0x00c0, 0x1753: 0x00c0, 0x1754: 0x00c0, 0x1755: 0x00c0, 0x1756: 0x00c0, 0x1757: 0x00c0,
	0x1758: 0x00c0, 0x1759: 0x00c0, 0x175a: 0x0080, 0x175b: 0x0080, 0x175c: 0x0080, 0x175d: 0x0080,
	0x175e: 0x0080, 0x175f: 0x0080, 0x1760: 0x0080, 0x1761: 0x0080, 0x1762: 0x0080, 0x1763: 0x0080,
	0x1764: 0x0080, 0x1765: 0x0080, 0x1766: 0x0080, 0x1767: 0x0080, 0x1768: 0x0080, 0x1769: 0x0080,
	0x176a: 0x0080, 0x176b: 0x00c3, 0x176c: 0x00c3, 0x176d: 0x00c3, 0x176e: 0x00c3, 0x176f: 0x00c3,
	0x1770: 0x00c3, 0x1771: 0x00c3, 0x1772: 0x00c3, 0x1773: 0x00c3, 0x1774: 0x0080, 0x1775: 0x0080,
	0x1776: 0x0080, 0x1777: 0x0080, 0x1778: 0x0080, 0x1779: 0x0080, 0x177a: 0x0080, 0x177b: 0x0080,
	0x177c: 0x0080,
	// Block 0x5e, offset 0x1780
	0x1780: 0x00c3, 0x1781: 0x00c3, 0x1782: 0x00c0, 0x1783: 0x00c0, 0x1784: 0x00c0, 0x1785: 0x00c0,
	0x1786: 0x00c0, 0x1787: 0x00c0, 0x1788: 0x00c0, 0x1789: 0x00c0, 0x178a: 0x00c0, 0x178b: 0x00c0,
	0x178c: 0x00c0, 0x178d: 0x00c0, 0x178e: 0x00c0, 0x178f: 0x00c0, 0x1790: 0x00c0, 0x1791: 0x00c0,
	0x1792: 0x00c0, 0x1793: 0x00c0, 0x1794: 0x00c0, 0x1795: 0x00c0, 0x1796: 0x00c0, 0x1797: 0x00c0,
	0x1798: 0x00c0, 0x1799: 0x00c0, 0x179a: 0x00c0, 0x179b: 0x00c0, 0x179c: 0x00c0, 0x179d: 0x00c0,
	0x179e: 0x00c0, 0x179f: 0x00c0, 0x17a0: 0x00c0, 0x17a1: 0x00c0, 0x17a2: 0x00c3, 0x17a3: 0x00c3,
	0x17a4: 0x00c3, 0x17a5: 0x00c3, 0x17a6: 0x00c0, 0x17a7: 0x00c0, 0x17a8: 0x00c3, 0x17a9: 0x00c3,
	0x17aa: 0x00c5, 0x17ab: 0x00c6, 0x17ac: 0x00c3, 0x17ad: 0x00c3, 0x17ae: 0x00c0, 0x17af: 0x00c0,
	0x17b0: 0x00c0, 0x17b1: 0x00c0, 0x17b2: 0x00c0, 0x17b3: 0x00c0, 0x17b4: 0x00c0, 0x17b5: 0x00c0,
	0x17b6: 0x00c0, 0x17b7: 0x00c0, 0x17b8: 0x00c0, 0x17b9: 0x00c0, 0x17ba: 0x00c0, 0x17bb: 0x00c0,
	0x17bc: 0x00c0, 0x17bd: 0x00c0, 0x17be: 0x00c0, 0x17bf: 0x00c0,
	// Block 0x5f, offset 0x17c0
	0x17c0: 0x00c0, 0x17c1: 0x00c0, 0x17c2: 0x00c0, 0x17c3: 0x00c0, 0x17c4: 0x00c0, 0x17c5: 0x00c0,
	0x17c6: 0x00c0, 0x17c7: 0x00c0, 0x17c8: 0x00c0, 0x17c9: 0x00c0, 0x17ca: 0x00c0, 0x17cb: 0x00c0,
	0x17cc: 0x00c0, 0x17cd: 0x00c0, 0x17ce: 0x00c0, 0x17cf: 0x00c0, 0x17d0: 0x00c0, 0x17d1: 0x00c0,
	0x17d2: 0x00c0, 0x17d3: 0x00c0, 0x17d4: 0x00c0, 0x17d5: 0x00c0, 0x17d6: 0x00c0, 0x17d7: 0x00c0,
	0x17d8: 0x00c0, 0x17d9: 0x00c0, 0x17da: 0x00c0, 0x17db: 0x00c0, 0x17dc: 0x00c0, 0x17dd: 0x00c0,
	0x17de: 0x00c0, 0x17df: 0x00c0, 0x17e0: 0x00c0, 0x17e1: 0x00c0, 0x17e2: 0x00c0, 0x17e3: 0x00c0,
	0x17e4: 0x00c0, 0x17e5: 0x00c0, 0x17e6: 0x00c3, 0x17e7: 0x00c0, 0x17e8: 0x00c3, 0x17e9: 0x00c3,
	0x17ea: 0x00c0, 0x17eb: 0x00c0, 0x17ec: 0x00c0, 0x17ed: 0x00c3, 0x17ee: 0x00c0, 0x17ef: 0x00c3,
	0x17f0: 0x00c3, 0x17f1: 0x00c3, 0x17f2: 0x00c5, 0x17f3: 0x00c5,
	0x17fc: 0x0080, 0x17fd: 0x0080, 0x17fe: 0x0080, 0x17ff: 0x0080,
	// Block 0x60, offset 0x1800
	0x1800: 0x00c0, 0x1801: 0x00c0, 0x1802: 0x00c0, 0x1803: 0x00c0, 0x1804: 0x00c0, 0x1805: 0x00c0,
	0x1806: 0x00c0, 0x1807: 0x00c0, 0x1808: 0x00c0, 0x1809: 0x00c0, 0x180a: 0x00c0, 0x180b: 0x00c0,
	0x180c: 0x00c0, 0x180d: 0x00c0, 0x180e: 0x00c0, 0x180f: 0x00c0, 0x1810: 0x00c0, 0x1811: 0x00c0,
	0x1812: 0x00c0, 0x1813: 0x00c0, 0x1814: 0x00c0, 0x1815: 0x00c0, 0x1816: 0x00c0, 0x1817: 0x00c0,
	0x1818: 0x00c0, 0x1819: 0x00c0, 0x181a: 0x00c0, 0x181b: 0x00c0, 0x181c: 0x00c0, 0x181d: 0x00c0,
	0x181e: 0x00c0, 0x181f: 0x00c0, 0x1820: 0x00c0, 0x1821: 0x00c0, 0x1822: 0x00c0, 0x1823: 0x00c0,
	0x1824: 0x00c0, 0x1825: 0x00c0, 0x1826: 0x00c0, 0x1827: 0x00c0, 0x1828: 0x00c0, 0x1829: 0x00c0,
	0x182a: 0x00c0, 0x182b: 0x00c0, 0x182c: 0x00c3, 0x182d: 0x00c3, 0x182e: 0x00c3, 0x182f: 0x00c3,
	0x1830: 0x00c3, 0x1831: 0x00c3, 0x1832: 0x00c3, 0x1833: 0x00c3, 0x1834: 0x00c0, 0x1835: 0x00c0,
	0x1836: 0x00c3, 0x1837: 0x00c3, 0x183b: 0x0080,
	0x183c: 0x0080, 0x183d: 0x0080, 0x183e: 0x0080, 0x183f: 0x0080,
	// Block 0x61, offset 0x1840
	0x1840: 0x00c0, 0x1841: 0x00c0, 0x1842: 0x00c0, 0x1843: 0x00c0, 0x1844: 0x00c0, 0x1845: 0x00c0,
	0x1846: 0x00c0, 0x1847: 0x00c0, 0x1848: 0x00c0, 0x1849: 0x00c0,
	0x184d: 0x00c0, 0x184e: 0x00c0, 0x184f: 0x00c0, 0x1850: 0x00c0, 0x1851: 0x00c0,
	0x1852: 0x00c0, 0x1853: 0x00c0, 0x1854: 0x00c0, 0x1855: 0x00c0, 0x1856: 0x00c0, 0x1857: 0x00c0,
	0x1858: 0x00c0, 0x1859: 0x00c0, 0x185a: 0x00c0, 0x185b: 0x00c0, 0x185c: 0x00c0, 0x185d: 0x00c0,
	0x185e: 0x00c0, 0x185f: 0x00c0, 0x1860: 0x00c0, 0x1861: 0x00c0, 0x1862: 0x00c0, 0x1863: 0x00c0,
	0x1864: 0x00c0, 0x1865: 0x00c0, 0x1866: 0x00c0, 0x1867: 0x00c0, 0x1868: 0x00c0, 0x1869: 0x00c0,
	0x186a: 0x00c0, 0x186b: 0x00c0, 0x186c: 0x00c0, 0x186d: 0x00c0, 0x186e: 0x00c0, 0x186f: 0x00c0,
	0x1870: 0x00c0, 0x1871: 0x00c0, 0x1872: 0x00c0, 0x1873: 0x00c0, 0x1874: 0x00c0, 0x1875: 0x00c0,
	0x1876: 0x00c0, 0x1877: 0x00c0, 0x1878: 0x00c0, 0x1879: 0x00c0, 0x187a: 0x00c0, 0x187b: 0x00c0,
	0x187c: 0x00c0, 0x187d: 0x00c0, 0x187e: 0x0080, 0x187f: 0x0080,
	// Block 0x62, offset 0x1880
	0x1880: 0x00c0, 0x1881: 0x00c0, 0x1882: 0x00c0, 0x1883: 0x00c0, 0x1884: 0x00c0, 0x1885: 0x00c0,
	0x1886: 0x00c0, 0x1887: 0x00c0, 0x1888: 0x00c0,
	0x1890: 0x00c0, 0x1891: 0x00c0,
	0x1892: 0x00c0, 0x1893: 0x00c0, 0x1894: 0x00c0, 0x1895: 0x00c0, 0x1896: 0x00c0, 0x1897: 0x00c0,
	0x1898: 0x00c0, 0x1899: 0x00c0, 0x189a: 0x00c0, 0x189b: 0x00c0, 0x189c: 0x00c0, 0x189d: 0x00c0,
	0x189e: 0x00c0, 0x189f: 0x00c0, 0x18a0: 0x00c0, 0x18a1: 0x00c0, 0x18a2: 0x00c0, 0x18a3: 0x00c0,
	0x18a4: 0x00c0, 0x18a5: 0x00c0, 0x18a6: 0x00c0, 0x18a7: 0x00c0, 0x18a8: 0x00c0, 0x18a9: 0x00c0,
	0x18aa: 0x00c0, 0x18ab: 0x00c0, 0x18ac: 0x00c0, 0x18ad: 0x00c0, 0x18ae: 0x00c0, 0x18af: 0x00c0,
	0x18b0: 0x00c0, 0x18b1: 0x00c0, 0x18b2: 0x00c0, 0x18b3: 0x00c0, 0x18b4: 0x00c0, 0x18b5: 0x00c0,
	0x18b6: 0x00c0, 0x18b7: 0x00c0, 0x18b8: 0x00c0, 0x18b9: 0x00c0, 0x18ba: 0x00c0,
	0x18bd: 0x00c0, 0x18be: 0x00c0, 0x18bf: 0x00c0,
	// Block 0x63, offset 0x18c0
	0x18c0: 0x0080, 0x18c1: 0x0080, 0x18c2: 0x0080, 0x18c3: 0x0080, 0x18c4: 0x0080, 0x18c5: 0x0080,
	0x18c6: 0x0080, 0x18c7: 0x0080,
	0x18d0: 0x00c3, 0x18d1: 0x00c3,
	0x18d2: 0x00c3, 0x18d3: 0x0080, 0x18d4: 0x00c3, 0x18d5: 0x00c3, 0x18d6: 0x00c3, 0x18d7: 0x00c3,
	0x18d8: 0x00c3, 0x18d9: 0x00c3, 0x18da: 0x00c3, 0x18db: 0x00c3, 0x18dc: 0x00c3, 0x18dd: 0x00c3,
	0x18de: 0x00c3, 0x18df: 0x00c3, 0x18e0: 0x00c3, 0x18e1: 0x00c0, 0x18e2: 0x00c3, 0x18e3: 0x00c3,
	0x18e4: 0x00c3, 0x18e5: 0x00c3, 0x18e6: 0x00c3, 0x18e7: 0x00c3, 0x18e8: 0x00c3, 0x18e9: 0x00c0,
	0x18ea: 0x00c0, 0x18eb: 0x00c0, 0x18ec: 0x00c0, 0x18ed: 0x00c3, 0x18ee: 0x00c0, 0x18ef: 0x00c0,
	0x18f0: 0x00c0, 0x18f1: 0x00c0, 0x18f2: 0x00c0, 0x18f3: 0x00c0, 0x18f4: 0x00c3, 0x18f5: 0x00c0,
	0x18f6: 0x00c0, 0x18f7: 0x00c0, 0x18f8: 0x00c3, 0x18f9: 0x00c3, 0x18fa: 0x00c0,
	// Block 0x64, offset 0x1900
	0x1900: 0x00c0, 0x1901: 0x00c0, 0x1902: 0x00c0, 0x1903: 0x00c0, 0x1904: 0x00c0, 0x1905: 0x00c0,
	0x1906: 0x00c0, 0x1907: 0x00c0, 0x1908: 0x00c0, 0x1909: 0x00c0, 0x190a: 0x00c0, 0x190b: 0x00c0,
	0x190c: 0x00c0, 0x190d: 0x00c0, 0x190e: 0x00c0, 0x190f: 0x00c0, 0x1910: 0x00c0, 0x1911: 0x00c0,
	0x1912: 0x00c0, 0x1913: 0x00c0, 0x1914: 0x00c0, 0x1915: 0x00c0, 0x1916: 0x00c0, 0x1917: 0x00c0,
	0x1918: 0x00c0, 0x1919: 0x00c0, 0x191a: 0x00c0, 0x191b: 0x00c0, 0x191c: 0x00c0, 0x191d: 0x00c0,
	0x191e: 0x00c0, 0x191f: 0x00c0, 0x1920: 0x00c0, 0x1921: 0x00c0, 0x1922: 0x00c0, 0x1923: 0x00c0,
	0x1924: 0x00c0, 0x1925: 0x00c0, 0x1926: 0x00c8, 0x1927: 0x00c8, 0x1928: 0x00c8, 0x1929: 0x00c8,
	0x192a: 0x00c8, 0x192b: 0x00c0, 0x192c: 0x0080, 0x192d: 0x0080, 0x192e: 0x0080, 0x192f: 0x00c0,
	0x1930: 0x0080, 0x1931: 0x0080, 0x1932: 0x0080, 0x1933: 0x0080, 0x1934: 0x0080, 0x1935: 0x0080,
	0x1936: 0x0080, 0x1937: 0x0080, 0x1938: 0x0080, 0x1939: 0x0080, 0x193a: 0x0080, 0x193b: 0x00c0,
	0x193c: 0x0080, 0x193d: 0x0080, 0x193e: 0x0080, 0x193f: 0x0080,
	// Block 0x65, offset 0x1940
	0x1940: 0x0080, 0x1941: 0x0080, 0x1942: 0x0080, 0x1943: 0x0080, 0x1944: 0x0080, 0x1945: 0x0080,
	0x1946: 0x0080, 0x1947: 0x0080, 0x1948: 0x0080, 0x1949: 0x0080, 0x194a: 0x0080, 0x194b: 0x0080,
	0x194c: 0x0080, 0x194d: 0x0080, 0x194e: 0x00c0, 0x194f: 0x0080, 0x1950: 0x0080, 0x1951: 0x0080,
	0x1952: 0x0080, 0x1953: 0x0080, 0x1954: 0x0080, 0x1955: 0x0080, 0x1956: 0x0080, 0x1957: 0x0080,
	0x1958: 0x0080, 0x1959: 0x0080, 0x195a: 0x0080, 0x195b: 0x0080, 0x195c: 0x0080, 0x195d: 0x0088,
	0x195e: 0x0088, 0x195f: 0x0088, 0x1960: 0x0088, 0x1961: 0x0088, 0x1962: 0x0080, 0x1963: 0x0080,
	0x1964: 0x0080, 0x1965: 0x0080, 0x1966: 0x0088, 0x1967: 0x0088, 0x1968: 0x0088, 0x1969: 0x0088,
	0x196a: 0x0088, 0x196b: 0x00c0, 0x196c: 0x00c0, 0x196d: 0x00c0, 0x196e: 0x00c0, 0x196f: 0x00c0,
	0x1970: 0x00c0, 0x1971: 0x00c0, 0x1972: 0x00c0, 0x1973: 0x00c0, 0x1974: 0x00c0, 0x1975: 0x00c0,
	0x1976: 0x00c0, 0x1977: 0x00c0, 0x1978: 0x0080, 0x1979: 0x00c0, 0x197a: 0x00c0, 0x197b: 0x00c0,
	0x197c: 0x00c0, 0x197d: 0x00c0, 0x197e: 0x00c0, 0x197f: 0x00c0,
	// Block 0x66, offset 0x1980
	0x1980: 0x00c0, 0x1981: 0x00c0, 0x1982: 0x00c0, 0x1983: 0x00c0, 0x1984: 0x00c0, 0x1985: 0x00c0,
	0x1986: 0x00c0, 0x1987: 0x00c0, 0x1988: 0x00c0, 0x1989: 0x00c0, 0x198a: 0x00c0, 0x198b: 0x00c0,
	0x198c: 0x00c0, 0x198d: 0x00c0, 0x198e: 0x00c0, 0x198f: 0x00c0, 0x1990: 0x00c0, 0x1991: 0x00c0,
	0x1992: 0x00c0, 0x1993: 0x00c0, 0x1994: 0x00c0, 0x1995: 0x00c0, 0x1996: 0x00c0, 0x1997: 0x00c0,
	0x1998: 0x00c0, 0x1999: 0x00c0, 0x199a: 0x00c0, 0x199b: 0x0080, 0x199c: 0x0080, 0x199d: 0x0080,
	0x199e: 0x0080, 0x199f: 0x0080, 0x19a0: 0x0080, 0x19a1: 0x0080, 0x19a2: 0x0080, 0x19a3: 0x0080,
	0x19a4: 0x0080, 0x19a5: 0x0080, 0x19a6: 0x0080, 0x19a7: 0x0080, 0x19a8: 0x0080, 0x19a9: 0x0080,
	0x19aa: 0x0080, 0x19ab: 0x0080, 0x19ac: 0x0080, 0x19ad: 0x0080, 0x19ae: 0x0080, 0x19af: 0x0080,
	0x19b0: 0x0080, 0x19b1: 0x0080, 0x19b2: 0x0080, 0x19b3: 0x0080, 0x19b4: 0x0080, 0x19b5: 0x0080,
	0x19b6: 0x0080, 0x19b7: 0x0080, 0x19b8: 0x0080, 0x19b9: 0x0080, 0x19ba: 0x0080, 0x19bb: 0x0080,
	0x19bc: 0x0080, 0x19bd: 0x0080, 0x19be: 0x0080, 0x19bf: 0x0088,
	// Block 0x67, offset 0x19c0
	0x19c0: 0x00c3, 0x19c1: 0x00c3, 0x19c2: 0x00c3, 0x19c3: 0x00c3, 0x19c4: 0x00c3, 0x19c5: 0x00c3,
	0x19c6: 0x00c3, 0x19c7: 0x00c3, 0x19c8: 0x00c3, 0x19c9: 0x00c3, 0x19ca: 0x00c3, 0x19cb: 0x00c3,
	0x19cc: 0x00c3, 0x19cd: 0x00c3, 0x19ce: 0x00c3, 0x19cf: 0x00c3, 0x19d0: 0x00c3, 0x19d1: 0x00c3,
	0x19d2: 0x00c3, 0x19d3: 0x00c3, 0x19d4: 0x00c3, 0x19d5: 0x00c3, 0x19d6: 0x00c3, 0x19d7: 0x00c3,
	0x19d8: 0x00c3, 0x19d9: 0x00c3, 0x19da: 0x00c3, 0x19db: 0x00c3, 0x19dc: 0x00c3, 0x19dd: 0x00c3,
	0x19de: 0x00c3, 0x19df: 0x00c3, 0x19e0: 0x00c3, 0x19e1: 0x00c3, 0x19e2: 0x00c3, 0x19e3: 0x00c3,
	0x19e4: 0x00c3, 0x19e5: 0x00c3, 0x19e6: 0x00c3, 0x19e7: 0x00c3, 0x19e8: 0x00c3, 0x19e9: 0x00c3,
	0x19ea: 0x00c3, 0x19eb: 0x00c3, 0x19ec: 0x00c3, 0x19ed: 0x00c3, 0x19ee: 0x00c3, 0x19ef: 0x00c3,
	0x19f0: 0x00c3, 0x19f1: 0x00c3, 0x19f2: 0x00c3, 0x19f3: 0x00c3, 0x19f4: 0x00c3, 0x19f5: 0x00c3,
	0x19f6: 0x00c3, 0x19f7: 0x00c3, 0x19f8: 0x00c3, 0x19f9: 0x00c3, 0x19fb: 0x00c3,
	0x19fc: 0x00c3, 0x19fd: 0x00c3, 0x19fe: 0x00c3, 0x19ff: 0x00c3,
	// Block 0x68, offset 0x1a00
	0x1a00: 0x00c0, 0x1a01: 0x00c0, 0x1a02: 0x00c0, 0x1a03: 0x00c0, 0x1a04: 0x00c0, 0x1a05: 0x00c0,
	0x1a06: 0x00c0, 0x1a07: 0x00c0, 0x1a08: 0x00c0, 0x1a09: 0x00c0, 0x1a0a: 0x00c0, 0x1a0b: 0x00c0,
	0x1a0c: 0x00c0, 0x1a0d: 0x00c0, 0x1a0e: 0x00c0, 0x1a0f: 0x00c0, 0x1a10: 0x00c0, 0x1a11: 0x00c0,
	0x1a12: 0x00c0, 0x1a13: 0x00c0, 0x1a14: 0x00c0, 0x1a15: 0x00c0, 0x1a16: 0x00c0, 0x1a17: 0x00c0,
	0x1a18: 0x00c0, 0x1a19: 0x00c0, 0x1a1a: 0x0080, 0x1a1b: 0x0080, 0x1a1c: 0x00c0, 0x1a1d: 0x00c0,
	0x1a1e: 0x00c0, 0x1a1f: 0x00c0, 0x1a20: 0x00c0, 0x1a21: 0x00c0, 0x1a22: 0x00c0, 0x1a23: 0x00c0,
	0x1a24: 0x00c0, 0x1a25: 0x00c0, 0x1a26: 0x00c0, 0x1a27: 0x00c0, 0x1a28: 0x00c0, 0x1a29: 0x00c0,
	0x1a2a: 0x00c0, 0x1a2b: 0x00c0, 0x1a2c: 0x00c0, 0x1a2d: 0x00c0, 0x1a2e: 0x00c0, 0x1a2f: 0x00c0,
	0x1a30: 0x00c0, 0x1a31: 0x00c0, 0x1a32: 0x00c0, 0x1a33: 0x00c0, 0x1a34: 0x00c0, 0x1a35: 0x00c0,
	0x1a36: 0x00c0, 0x1a37: 0x00c0, 0x1a38: 0x00c0, 0x1a39: 0x00c0, 0x1a3a: 0x00c0, 0x1a3b: 0x00c0,
	0x1a3c: 0x00c0, 0x1a3d: 0x00c0, 0x1a3e: 0x00c0, 0x1a3f: 0x00c0,
	// Block 0x69, offset 0x1a40
	0x1a40: 0x00c8, 0x1a41: 0x00c8, 0x1a42: 0x00c8, 0x1a43: 0x00c8, 0x1a44: 0x00c8, 0x1a45: 0x00c8,
	0x1a46: 0x00c8, 0x1a47: 0x00c8, 0x1a48: 0x00c8, 0x1a49: 0x00c8, 0x1a4a: 0x00c8, 0x1a4b: 0x00c8,
	0x1a4c: 0x00c8, 0x1a4d: 0x00c8, 0x1a4e: 0x00c8, 0x1a4f: 0x00c8, 0x1a50: 0x00c8, 0x1a51: 0x00c8,
	0x1a52: 0x00c8, 0x1a53: 0x00c8, 0x1a54: 0x00c8, 0x1a55: 0x00c8,
	0x1a58: 0x00c8, 0x1a59: 0x00c8, 0x1a5a: 0x00c8, 0x1a5b: 0x00c8, 0x1a5c: 0x00c8, 0x1a5d: 0x00c8,
	0x1a60: 0x00c8, 0x1a61: 0x00c8, 0x1a62: 0x00c8, 0x1a63: 0x00c8,
	0x1a64: 0x00c8, 0x1a65: 0x00c8, 0x1a66: 0x00c8, 0x1a67: 0x00c8, 0x1a68: 0x00c8, 0x1a69: 0x00c8,
	0x1a6a: 0x00c8, 0x1a6b: 0x00c8, 0x1a6c: 0x00c8, 0x1a6d: 0x00c8, 0x1a6e: 0x00c8, 0x1a6f: 0x00c8,
	0x1a70: 0x00c8, 0x1a71: 0x00c8, 0x1a72: 0x00c8, 0x1a73: 0x00c8, 0x1a74: 0x00c8, 0x1a75: 0x00c8,
	0x1a76: 0x00c8, 0x1a77: 0x00c8, 0x1a78: 0x00c8, 0x1a79: 0x00c8, 0x1a7a: 0x00c8, 0x1a7b: 0x00c8,
	0x1a7c: 0x00c8, 0x1a7d: 0x00c8, 0x1a7e: 0x00c8, 0x1a7f: 0x00c8,
	// Block 0x6a, offset 0x1a80
	0x1a80: 0x00c8, 0x1a81: 0x00c8, 0x1a82: 0x00c8, 0x1a83: 0x00c8, 0x1a84: 0x00c8, 0x1a85: 0x00c8,
	0x1a88: 0x00c8, 0x1a89: 0x00c8, 0x1a8a: 0x00c8, 0x1a8b: 0x00c8,
	0x1a8c: 0x00c8, 0x1a8d: 0x00c8, 0x1a90: 0x00c8, 0x1a91: 0x00c8,
	0x1a92: 0x00c8, 0x1a93: 0x00c8, 0x1a94: 0x00c8, 0x1a95: 0x00c8, 0x1a96: 0x00c8, 0x1a97: 0x00c8,
	0x1a99: 0x00c8, 0x1a9b: 0x00c8, 0x1a9d: 0x00c8,
	0x1a9f: 0x00c8, 0x1aa0: 0x00c8, 0x1aa1: 0x00c8, 0x1aa2: 0x00c8, 0x1aa3: 0x00c8,
	0x1aa4: 0x00c8, 0x1aa5: 0x00c8, 0x1aa6: 0x00c8, 0x1aa7: 0x00c8, 0x1aa8: 0x00c8, 0x1aa9: 0x00c8,
	0x1aaa: 0x00c8, 0x1aab: 0x00c8, 0x1aac: 0x00c8, 0x1aad: 0x00c8, 0x1aae: 0x00c8, 0x1aaf: 0x00c8,
	0x1ab0: 0x00c8, 0x1ab1: 0x0088, 0x1ab2: 0x00c8, 0x1ab3: 0x0088, 0x1ab4: 0x00c8, 0x1ab5: 0x0088,
	0x1ab6: 0x00c8, 0x1ab7: 0x0088, 0x1ab8: 0x00c8, 0x1ab9: 0x0088, 0x1aba: 0x00c8, 0x1abb: 0x0088,
	0x1abc: 0x00c8, 0x1abd: 0x0088,
	// Block 0x6b, offset 0x1ac0
	0x1ac0: 0x00c8, 0x1ac1: 0x00c8, 0x1ac2: 0x00c8, 0x1ac3: 0x00c8, 0x1ac4: 0x00c8, 0x1ac5: 0x00c8,
	0x1ac6: 0x00c8, 0x1ac7: 0x00c8, 0x1ac8: 0x0088, 0x1ac9: 0x0088, 0x1aca: 0x0088, 0x1acb: 0x0088,
	0x1acc: 0x0088, 0x1acd: 0x0088, 0x1ace: 0x0088, 0x1acf: 0x0088, 0x1ad0: 0x00c8, 0x1ad1: 0x00c8,
	0x1ad2: 0x00c8, 0x1ad3: 0x00c8, 0x1ad4: 0x00c8, 0x1ad5: 0x00c8, 0x1ad6: 0x00c8, 0x1ad7: 0x00c8,
	0x1ad8: 0x0088, 0x1ad9: 0x0088, 0x1ada: 0x0088, 0x1adb: 0x0088, 0x1adc: 0x0088, 0x1add: 0x0088,
	0x1ade: 0x0088, 0x1adf: 0x0088, 0x1ae0: 0x00c8, 0x1ae1: 0x00c8, 0x1ae2: 0x00c8, 0x1ae3: 0x00c8,
	0x1ae4: 0x00c8, 0x1ae5: 0x00c8, 0x1ae6: 0x00c8, 0x1ae7: 0x00c8, 0x1ae8: 0x0088, 0x1ae9: 0x0088,
	0x1aea: 0x0088, 0x1aeb: 0x0088, 0x1aec: 0x0088, 0x1aed: 0x0088, 0x1aee: 0x0088, 0x1aef: 0x0088,
	0x1af0: 0x00c8, 0x1af1: 0x00c8, 0x1af2: 0x00c8, 0x1af3: 0x00c8, 0x1af4: 0x00c8,
	0x1af6: 0x00c8, 0x1af7: 0x00c8, 0x1af8: 0x00c8, 0x1af9: 0x00c8, 0x1afa: 0x00c8, 0x1afb: 0x0088,
	0x1afc: 0x0088, 0x1afd: 0x0088, 0x1afe: 0x0088, 0x1aff: 0x0088,
	// Block 0x6c, offset 0x1b00
	0x1b00: 0x0088, 0x1b01: 0x0088, 0x1b02: 0x00c8, 0x1b03: 0x00c8, 0x1b04: 0x00c8,
	0x1b06: 0x00c8, 0x1b07: 0x00c8, 0x1b08: 0x00c8, 0x1b09: 0x0088, 0x1b0a: 0x00c8, 0x1b0b: 0x0088,
	0x1b0c: 0x0088, 0x1b0d: 0x0088, 0x1b0e: 0x0088, 0x1b0f: 0x0088, 0x1b10: 0x00c8, 0x1b11: 0x00c8,
	0x1b12: 0x00c8, 0x1b13: 0x0088, 0x1b16: 0x00c8, 0x1b17: 0x00c8,
	0x1b18: 0x00c8, 0x1b19: 0x00c8, 0x1b1a: 0x00c8, 0x1b1b: 0x0088, 0x1b1d: 0x0088,
	0x1b1e: 0x0088, 0x1b1f: 0x0088, 0x1b20: 0x00c8, 0x1b21: 0x00c8, 0x1b22: 0x00c8, 0x1b23: 0x0088,
	0x1b24: 0x00c8, 0x1b25: 0x00c8, 0x1b26: 0x00c8, 0x1b27: 0x00c8, 0x1b28: 0x00c8, 0x1b29: 0x00c8,
	0x1b2a: 0x00c8, 0x1b2b: 0x0088, 0x1b2c: 0x00c8, 0x1b2d: 0x0088, 0x1b2e: 0x0088, 0x1b2f: 0x0088,
	0x1b32: 0x00c8, 0x1b33: 0x00c8, 0x1b34: 0x00c8,
	0x1b36: 0x00c8, 0x1b37: 0x00c8, 0x1b38: 0x00c8, 0x1b39: 0x0088, 0x1b3a: 0x00c8, 0x1b3b: 0x0088,
	0x1b3c: 0x0088, 0x1b3d: 0x0088, 0x1b3e: 0x0088,
	// Block 0x6d, offset 0x1b40
	0x1b40: 0x0080, 0x1b41: 0x0080, 0x1b42: 0x0080, 0x1b43: 0x0080, 0x1b44: 0x0080, 0x1b45: 0x0080,
	0x1b46: 0x0080, 0x1b47: 0x0080, 0x1b48: 0x0080, 0x1b49: 0x0080, 0x1b4a: 0x0080, 0x1b4b: 0x0040,
	0x1b4c: 0x004d, 0x1b4d: 0x004e, 0x1b4e: 0x0040, 0x1b4f: 0x0040, 0x1b50: 0x0080, 0x1b51: 0x0080,
	0x1b52: 0x0080, 0x1b53: 0x0080, 0x1b54: 0x0080, 0x1b55: 0x0080, 0x1b56: 0x0080, 0x1b57: 0x0080,
	0x1b58: 0x0080, 0x1b59: 0x0080, 0x1b5a: 0x0080, 0x1b5b: 0x0080, 0x1b5c: 0x0080, 0x1b5d: 0x0080,
	0x1b5e: 0x0080, 0x1b5f: 0x0080, 0x1b60: 0x0080, 0x1b61: 0x0080, 0x1b62: 0x0080, 0x1b63: 0x0080,
	0x1b64: 0x0080, 0x1b65: 0x0080, 0x1b66: 0x0080, 0x1b67: 0x0080, 0x1b68: 0x0040, 0x1b69: 0x0040,
	0x1b6a: 0x0040, 0x1b6b: 0x0040, 0x1b6c: 0x0040, 0x1b6d: 0x0040, 0x1b6e: 0x0040, 0x1b6f: 0x0080,
	0x1b70: 0x0080, 0x1b71: 0x0080, 0x1b72: 0x0080, 0x1b73: 0x0080, 0x1b74: 0x0080, 0x1b75: 0x0080,
	0x1b76: 0x0080, 0x1b77: 0x0080, 0x1b78: 0x0080, 0x1b79: 0x0080, 0x1b7a: 0x0080, 0x1b7b: 0x0080,
	0x1b7c: 0x0080, 0x1b7d: 0x0080, 0x1b7e: 0x0080, 0x1b7f: 0x0080,
	// Block 0x6e, offset 0x1b80
	0x1b80: 0x0080, 0x1b81: 0x0080, 0x1b82: 0x0080, 0x1b83: 0x0080, 0x1b84: 0x0080, 0x1b85: 0x0080,
	0x1b86: 0x0080, 0x1b87: 0x0080, 0x1b88: 0x0080, 0x1b89: 0x0080, 0x1b8a: 0x0080, 0x1b8b: 0x0080,
	0x1b8c: 0x0080, 0x1b8d: 0x0080, 0x1b8e: 0x0080, 0x1b8f: 0x0080, 0x1b90: 0x0080, 0x1b91: 0x0080,
	0x1b92: 0x0080, 0x1b93: 0x0080, 0x1b94: 0x0080, 0x1b95: 0x0080, 0x1b96: 0x0080, 0x1b97: 0x0080,
	0x1b98: 0x0080, 0x1b99: 0x0080, 0x1b9a: 0x0080, 0x1b9b: 0x0080, 0x1b9c: 0x0080, 0x1b9d: 0x0080,
	0x1b9e: 0x0080, 0x1b9f: 0x0080, 0x1ba0: 0x0040, 0x1ba1: 0x0040, 0x1ba2: 0x0040, 0x1ba3: 0x0040,
	0x1ba4: 0x0040, 0x1ba6: 0x0040, 0x1ba7: 0x0040, 0x1ba8: 0x0040, 0x1ba9: 0x0040,
	0x1baa: 0x0040, 0x1bab: 0x0040, 0x1bac: 0x0040, 0x1bad: 0x0040, 0x1bae: 0x0040, 0x1baf: 0x0040,
	0x1bb0: 0x0080, 0x1bb1: 0x0080, 0x1bb4: 0x0080, 0x1bb5: 0x0080,
	0x1bb6: 0x0080, 0x1bb7: 0x0080, 0x1bb8: 0x0080, 0x1bb9: 0x0080, 0x1bba: 0x0080, 0x1bbb: 0x0080,
	0x1bbc: 0x0080, 0x1bbd: 0x0080, 0x1bbe: 0x0080, 0x1bbf: 0x0080,
	// Block 0x6f, offset 0x1bc0
	0x1bc0: 0x0080, 0x1bc1: 0x0080, 0x1bc2: 0x0080, 0x1bc3: 0x0080, 0x1bc4: 0x0080, 0x1bc5: 0x0080,
	0x1bc6: 0x0080, 0x1bc7: 0x0080, 0x1bc8: 0x0080, 0x1bc9: 0x0080, 0x1bca: 0x0080, 0x1bcb: 0x0080,
	0x1bcc: 0x0080, 0x1bcd: 0x0080, 0x1bce: 0x0080, 0x1bd0: 0x0080, 0x1bd1: 0x0080,
	0x1bd2: 0x0080, 0x1bd3: 0x0080, 0x1bd4: 0x0080, 0x1bd5: 0x0080, 0x1bd6: 0x0080, 0x1bd7: 0x0080,
	0x1bd8: 0x0080, 0x1bd9: 0x0080, 0x1bda: 0x0080, 0x1bdb: 0x0080, 0x1bdc: 0x0080,
	0x1be0: 0x0080, 0x1be1: 0x0080, 0x1be2: 0x0080, 0x1be3: 0x0080,
	0x1be4: 0x0080, 0x1be5: 0x0080, 0x1be6: 0x0080, 0x1be7: 0x0080, 0x1be8: 0x0080, 0x1be9: 0x0080,
	0x1bea: 0x0080, 0x1beb: 0x0080, 0x1bec: 0x0080, 0x1bed: 0x0080, 0x1bee: 0x0080, 0x1bef: 0x0080,
	0x1bf0: 0x0080, 0x1bf1: 0x0080, 0x1bf2: 0x0080, 0x1bf3: 0x0080, 0x1bf4: 0x0080, 0x1bf5: 0x0080,
	0x1bf6: 0x0080, 0x1bf7: 0x0080, 0x1bf8: 0x0080, 0x1bf9: 0x0080, 0x1bfa: 0x0080, 0x1bfb: 0x0080,
	0x1bfc: 0x0080, 0x1bfd: 0x0080, 0x1bfe: 0x0080, 0x1bff: 0x0080,
	// Block 0x70, offset 0x1c00
	0x1c10: 0x00c3, 0x1c11: 0x00c3,
	0x1c12: 0x00c3, 0x1c13: 0x00c3, 0x1c14: 0x00c3, 0x1c15: 0x00c3, 0x1c16: 0x00c3, 0x1c17: 0x00c3,
	0x1c18: 0x00c3, 0x1c19: 0x00c3, 0x1c1a: 0x00c3, 0x1c1b: 0x00c3, 0x1c1c: 0x00c3, 0x1c1d: 0x0083,
	0x1c1e: 0x0083, 0x1c1f: 0x0083, 0x1c20: 0x0083, 0x1c21: 0x00c3, 0x1c22: 0x0083, 0x1c23: 0x0083,
	0x1c24: 0x0083, 0x1c25: 0x00c3, 0x1c26: 0x00c3, 0x1c27: 0x00c3, 0x1c28: 0x00c3, 0x1c29: 0x00c3,
	0x1c2a: 0x00c3, 0x1c2b: 0x00c3, 0x1c2c: 0x00c3, 0x1c2d: 0x00c3, 0x1c2e: 0x00c3, 0x1c2f: 0x00c3,
	0x1c30: 0x00c3,
	// Block 0x71, offset 0x1c40
	0x1c40: 0x0080, 0x1c41: 0x0080, 0x1c42: 0x0080, 0x1c43: 0x0080, 0x1c44: 0x0080, 0x1c45: 0x0080,
	0x1c46: 0x0080, 0x1c47: 0x0080, 0x1c48: 0x0080, 0x1c49: 0x0080, 0x1c4a: 0x0080, 0x1c4b: 0x0080,
	0x1c4c: 0x0080, 0x1c4d: 0x0080, 0x1c4e: 0x0080, 0x1c4f: 0x0080, 0x1c50: 0x0080, 0x1c51: 0x0080,
	0x1c52: 0x0080, 0x1c53: 0x0080, 0x1c54: 0x0080, 0x1c55: 0x0080, 0x1c56: 0x0080, 0x1c57: 0x0080,
	0x1c58: 0x0080, 0x1c59: 0x0080, 0x1c5a: 0x0080, 0x1c5b: 0x0080, 0x1c5c: 0x0080, 0x1c5d: 0x0080,
	0x1c5e: 0x0080, 0x1c5f: 0x0080, 0x1c60: 0x0080, 0x1c61: 0x0080, 0x1c62: 0x0080, 0x1c63: 0x0080,
	0x1c64: 0x0080, 0x1c65: 0x0080, 0x1c66: 0x0088, 0x1c67: 0x0080, 0x1c68: 0x0080, 0x1c69: 0x0080,
	0x1c6a: 0x0080, 0x1c6b: 0x0080, 0x1c6c: 0x0080, 0x1c6d: 0x0080, 0x1c6e: 0x0080, 0x1c6f: 0x0080,
	0x1c70: 0x0080, 0x1c71: 0x0080, 0x1c72: 0x00c0, 0x1c73: 0x0080, 0x1c74: 0x0080, 0x1c75: 0x0080,
	0x1c76: 0x0080, 0x1c77: 0x0080, 0x1c78: 0x0080, 0x1c79: 0x0080, 0x1c7a: 0x0080, 0x1c7b: 0x0080,
	0x1c7c: 0x0080, 0x1c7d: 0x0080, 0x1c7e: 0x0080, 0x1c7f: 0x0080,
	// Block 0x72, offset 0x1c80
	0x1c80: 0x0080, 0x1c81: 0x0080, 0x1c82: 0x0080, 0x1c83: 0x0080, 0x1c84: 0x0080, 0x1c85: 0x0080,
	0x1c86: 0x0080, 0x1c87: 0x0080, 0x1c88: 0x0080, 0x1c89: 0x0080, 0x1c8a: 0x0080, 0x1c8b: 0x0080,
	0x1c8c: 0x0080, 0x1c8d: 0x0080, 0x1c8e: 0x00c0, 0x1c8f: 0x0080, 0x1c90: 0x0080, 0x1c91: 0x0080,
	0x1c92: 0x0080, 0x1c93: 0x0080, 0x1c94: 0x0080, 0x1c95: 0x0080, 0x1c96: 0x0080, 0x1c97: 0x0080,
	0x1c98: 0x0080, 0x1c99: 0x0080, 0x1c9a: 0x0080, 0x1c9b: 0x0080, 0x1c9c: 0x0080, 0x1c9d: 0x0080,
	0x1c9e: 0x0080, 0x1c9f: 0x0080, 0x1ca0: 0x0080, 0x1ca1: 0x0080, 0x1ca2: 0x0080, 0x1ca3: 0x0080,
	0x1ca4: 0x0080, 0x1ca5: 0x0080, 0x1ca6: 0x0080, 0x1ca7: 0x0080, 0x1ca8: 0x0080, 0x1ca9: 0x0080,
	0x1caa: 0x0080, 0x1cab: 0x0080, 0x1cac: 0x0080, 0x1cad: 0x0080, 0x1cae: 0x0080, 0x1caf: 0x0080,
	0x1cb0: 0x0080, 0x1cb1: 0x0080, 0x1cb2: 0x0080, 0x1cb3: 0x0080, 0x1cb4: 0x0080, 0x1cb5: 0x0080,
	0x1cb6: 0x0080, 0x1cb7: 0x0080, 0x1cb8: 0x0080, 0x1cb9: 0x0080, 0x1cba: 0x0080, 0x1cbb: 0x0080,
	0x1cbc: 0x0080, 0x1cbd: 0x0080, 0x1cbe: 0x0080, 0x1cbf: 0x0080,
	// Block 0x73, offset 0x1cc0
	0x1cc0: 0x0080, 0x1cc1: 0x0080, 0x1cc2: 0x0080, 0x1cc3: 0x00c0, 0x1cc4: 0x00c0, 0x1cc5: 0x0080,
	0x1cc6: 0x0080, 0x1cc7: 0x0080, 0x1cc8: 0x0080, 0x1cc9: 0x0080, 0x1cca: 0x0080, 0x1ccb: 0x0080,
	0x1cd0: 0x0080, 0x1cd1: 0x0080,
	0x1cd2: 0x0080, 0x1cd3: 0x0080, 0x1cd4: 0x0080, 0x1cd5: 0x0080, 0x1cd6: 0x0080, 0x1cd7: 0x0080,
	0x1cd8: 0x0080, 0x1cd9: 0x0080, 0x1cda: 0x0080, 0x1cdb: 0x0080, 0x1cdc: 0x0080, 0x1cdd: 0x0080,
	0x1cde: 0x0080, 0x1cdf: 0x0080, 0x1ce0: 0x0080, 0x1ce1: 0x0080, 0x1ce2: 0x0080, 0x1ce3: 0x0080,
	0x1ce4: 0x0080, 0x1ce5: 0x0080, 0x1ce6: 0x0080, 0x1ce7: 0x0080, 0x1ce8: 0x0080, 0x1ce9: 0x0080,
	0x1cea: 0x0080, 0x1ceb: 0x0080, 0x1cec: 0x0080, 0x1ced: 0x0080, 0x1cee: 0x0080, 0x1cef: 0x0080,
	0x1cf0: 0x0080, 0x1cf1: 0x0080, 0x1cf2: 0x0080, 0x1cf3: 0x0080, 0x1cf4: 0x0080, 0x1cf5: 0x0080,
	0x1cf6: 0x0080, 0x1cf7: 0x0080, 0x1cf8: 0x0080, 0x1cf9: 0x0080, 0x1cfa: 0x0080, 0x1cfb: 0x0080,
	0x1cfc: 0x0080, 0x1cfd: 0x0080, 0x1cfe: 0x0080, 0x1cff: 0x0080,
	// Block 0x74, offset 0x1d00
	0x1d00: 0x0080, 0x1d01: 0x0080, 0x1d02: 0x0080, 0x1d03: 0x0080, 0x1d04: 0x0080, 0x1d05: 0x0080,
	0x1d06: 0x0080, 0x1d07: 0x0080, 0x1d08: 0x0080, 0x1d09: 0x0080, 0x1d0a: 0x0080, 0x1d0b: 0x0080,
	0x1d0c: 0x0080, 0x1d0d: 0x0080, 0x1d0e: 0x0080, 0x1d0f: 0x0080, 0x1d10: 0x0080, 0x1d11: 0x0080,
	0x1d12: 0x0080, 0x1d13: 0x0080, 0x1d14: 0x0080, 0x1d15: 0x0080, 0x1d16: 0x0080, 0x1d17: 0x0080,
	0x1d18: 0x0080, 0x1d19: 0x0080, 0x1d1a: 0x0080, 0x1d1b: 0x0080, 0x1d1c: 0x0080, 0x1d1d: 0x0080,
	0x1d1e: 0x0080, 0x1d1f: 0x0080, 0x1d20: 0x0080, 0x1d21: 0x0080, 0x1d22: 0x0080, 0x1d23: 0x0080,
	0x1d24: 0x0080, 0x1d25: 0x0080, 0x1d26: 0x0080, 0x1d27: 0x0080, 0x1d28: 0x0080, 0x1d29: 0x0080,
	0x1d2a: 0x0080, 0x1d2b: 0x0080, 0x1d2c: 0x0080, 0x1d2d: 0x0080, 0x1d2e: 0x0080, 0x1d2f: 0x0080,
	0x1d30: 0x0080, 0x1d31: 0x0080, 0x1d32: 0x0080, 0x1d33: 0x0080, 0x1d34: 0x0080, 0x1d35: 0x0080,
	0x1d36: 0x0080, 0x1d37: 0x0080, 0x1d38: 0x0080, 0x1d39: 0x0080, 0x1d3a: 0x0080, 0x1d3b: 0x0080,
	0x1d3c: 0x0080, 0x1d3d: 0x0080, 0x1d3e: 0x0080, 0x1d3f: 0x0080,
	// Block 0x75, offset 0x1d40
	0x1d40: 0x0080, 0x1d41: 0x0080, 0x1d42: 0x0080, 0x1d43: 0x0080, 0x1d44: 0x0080, 0x1d45: 0x0080,
	0x1d46: 0x0080, 0x1d47: 0x0080, 0x1d48: 0x0080, 0x1d49: 0x0080, 0x1d4a: 0x0080, 0x1d4b: 0x0080,
	0x1d4c: 0x0080, 0x1d4d: 0x0080, 0x1d4e: 0x0080, 0x1d4f: 0x0080, 0x1d50: 0x0080, 0x1d51: 0x0080,
	0x1d52: 0x0080, 0x1d53: 0x0080, 0x1d54: 0x0080, 0x1d55: 0x0080, 0x1d56: 0x0080, 0x1d57: 0x0080,
	0x1d58: 0x0080, 0x1d59: 0x0080, 0x1d5a: 0x0080, 0x1d5b: 0x0080, 0x1d5c: 0x0080, 0x1d5d: 0x0080,
	0x1d5e: 0x0080, 0x1d5f: 0x0080, 0x1d60: 0x0080, 0x1d61: 0x0080, 0x1d62: 0x0080, 0x1d63: 0x0080,
	0x1d64: 0x0080, 0x1d65: 0x0080, 0x1d66: 0x0080,
	// Block 0x76, offset 0x1d80
	0x1d80: 0x0080, 0x1d81: 0x0080, 0x1d82: 0x0080, 0x1d83: 0x0080, 0x1d84: 0x0080, 0x1d85: 0x0080,
	0x1d86: 0x0080, 0x1d87: 0x0080, 0x1d88: 0x0080, 0x1d89: 0x0080, 0x1d8a: 0x0080,
	0x1da0: 0x0080, 0x1da1: 0x0080, 0x1da2: 0x0080, 0x1da3: 0x0080,
	0x1da4: 0x0080, 0x1da5: 0x0080, 0x1da6: 0x0080, 0x1da7: 0x0080, 0x1da8: 0x0080, 0x1da9: 0x0080,
	0x1daa: 0x0080, 0x1dab: 0x0080, 0x1dac: 0x0080, 0x1dad: 0x0080, 0x1dae: 0x0080, 0x1daf: 0x0080,
	0x1db0: 0x0080, 0x1db1: 0x0080, 0x1db2: 0x0080, 0x1db3: 0x0080, 0x1db4: 0x0080, 0x1db5: 0x0080,
	0x1db6: 0x0080, 0x1db7: 0x0080, 0x1db8: 0x0080, 0x1db9: 0x0080, 0x1dba: 0x0080, 0x1dbb: 0x0080,
	0x1dbc: 0x0080, 0x1dbd: 0x0080, 0x1dbe: 0x0080, 0x1dbf: 0x0080,
	// Block 0x77, offset 0x1dc0
	0x1dc0: 0x0080, 0x1dc1: 0x0080, 0x1dc2: 0x0080, 0x1dc3: 0x0080, 0x1dc4: 0x0080, 0x1dc5: 0x0080,
	0x1dc6: 0x0080, 0x1dc7: 0x0080, 0x1dc8: 0x0080, 0x1dc9: 0x0080, 0x1dca: 0x0080, 0x1dcb: 0x0080,
	0x1dcc: 0x0080, 0x1dcd: 0x0080, 0x1dce: 0x0080, 0x1dcf: 0x0080, 0x1dd0: 0x0080, 0x1dd1: 0x0080,
	0x1dd2: 0x0080, 0x1dd3: 0x0080, 0x1dd4: 0x0080, 0x1dd5: 0x0080, 0x1dd6: 0x0080, 0x1dd7: 0x0080,
	0x1dd8: 0x0080, 0x1dd9: 0x0080, 0x1dda: 0x0080, 0x1ddb: 0x0080, 0x1ddc: 0x0080, 0x1ddd: 0x0080,
	0x1dde: 0x0080, 0x1ddf: 0x0080, 0x1de0: 0x0080, 0x1de1: 0x0080, 0x1de2: 0x0080, 0x1de3: 0x0080,
	0x1de4: 0x0080, 0x1de5: 0x0080, 0x1de6: 0x0080, 0x1de7: 0x0080, 0x1de8: 0x0080, 0x1de9: 0x0080,
	0x1dea: 0x0080, 0x1deb: 0x0080, 0x1dec: 0x0080, 0x1ded: 0x0080, 0x1dee: 0x0080, 0x1def: 0x0080,
	0x1df0: 0x0080, 0x1df1: 0x0080, 0x1df2: 0x0080, 0x1df3: 0x0080,
	0x1df6: 0x0080, 0x1df7: 0x0080, 0x1df8: 0x0080, 0x1df9: 0x0080, 0x1dfa: 0x0080, 0x1dfb: 0x0080,
	0x1dfc: 0x0080, 0x1dfd: 0x0080, 0x1dfe: 0x0080, 0x1dff: 0x0080,
	// Block 0x78, offset 0x1e00
	0x1e00: 0x0080, 0x1e01: 0x0080, 0x1e02: 0x0080, 0x1e03: 0x0080, 0x1e04: 0x0080, 0x1e05: 0x0080,
	0x1e06: 0x0080, 0x1e07: 0x0080, 0x1e08: 0x0080, 0x1e09: 0x0080, 0x1e0a: 0x0080, 0x1e0b: 0x0080,
	0x1e0c: 0x0080, 0x1e0d: 0x0080, 0x1e0e: 0x0080, 0x1e0f: 0x0080, 0x1e10: 0x0080, 0x1e11: 0x0080,
	0x1e12: 0x0080, 0x1e13: 0x0080, 0x1e14: 0x0080, 0x1e15: 0x0080, 0x1e17: 0x0080,
	0x1e18: 0x0080, 0x1e19: 0x0080, 0x1e1a: 0x0080, 0x1e1b: 0x0080, 0x1e1c: 0x0080, 0x1e1d: 0x0080,
	0x1e1e: 0x0080, 0x1e1f: 0x0080, 0x1e20: 0x0080, 0x1e21: 0x0080, 0x1e22: 0x0080, 0x1e23: 0x0080,
	0x1e24: 0x0080, 0x1e25: 0x0080, 0x1e26: 0x0080, 0x1e27: 0x0080, 0x1e28: 0x0080, 0x1e29: 0x0080,
	0x1e2a: 0x0080, 0x1e2b: 0x0080, 0x1e2c: 0x0080, 0x1e2d: 0x0080, 0x1e2e: 0x0080, 0x1e2f: 0x0080,
	0x1e30: 0x0080, 0x1e31: 0x0080, 0x1e32: 0x0080, 0x1e33: 0x0080, 0x1e34: 0x0080, 0x1e35: 0x0080,
	0x1e36: 0x0080, 0x1e37: 0x0080, 0x1e38: 0x0080, 0x1e39: 0x0080, 0x1e3a: 0x0080, 0x1e3b: 0x0080,
	0x1e3c: 0x0080, 0x1e3d: 0x0080, 0x1e3e: 0x0080, 0x1e3f: 0x0080,
	// Block 0x79, offset 0x1e40
	0x1e40: 0x00c0, 0x1e41: 0x00c0, 0x1e42: 0x00c0, 0x1e43: 0x00c0, 0x1e44: 0x00c0, 0x1e45: 0x00c0,
	0x1e46: 0x00c0, 0x1e47: 0x00c0, 0x1e48: 0x00c0, 0x1e49: 0x00c0, 0x1e4a: 0x00c0, 0x1e4b: 0x00c0,
	0x1e4c: 0x00c0, 0x1e4d: 0x00c0, 0x1e4e: 0x00c0, 0x1e4f: 0x00c0, 0x1e50: 0x00c0, 0x1e51: 0x00c0,
	0x1e52: 0x00c0, 0x1e53: 0x00c0, 0x1e54: 0x00c0, 0x1e55: 0x00c0, 0x1e56: 0x00c0, 0x1e57: 0x00c0,
	0x1e58: 0x00c0, 0x1e59: 0x00c0, 0x1e5a: 0x00c0, 0x1e5b: 0x00c0, 0x1e5c: 0x00c0, 0x1e5d: 0x00c0,
	0x1e5e: 0x00c0, 0x1e5f: 0x00c0, 0x1e60: 0x00c0, 0x1e61: 0x00c0, 0x1e62: 0x00c0, 0x1e63: 0x00c0,
	0x1e64: 0x00c0, 0x1e65: 0x00c0, 0x1e66: 0x00c0, 0x1e67: 0x00c0, 0x1e68: 0x00c0, 0x1e69: 0x00c0,
	0x1e6a: 0x00c0, 0x1e6b: 0x00c0, 0x1e6c: 0x00c0, 0x1e6d: 0x00c0, 0x1e6e: 0x00c0,
	0x1e70: 0x00c0, 0x1e71: 0x00c0, 0x1e72: 0x00c0, 0x1e73: 0x00c0, 0x1e74: 0x00c0, 0x1e75: 0x00c0,
	0x1e76: 0x00c0, 0x1e77: 0x00c0, 0x1e78: 0x00c0, 0x1e79: 0x00c0, 0x1e7a: 0x00c0, 0x1e7b: 0x00c0,
	0x1e7c: 0x00c0, 0x1e7d: 0x00c0, 0x1e7e: 0x00c0, 0x1e7f: 0x00c0,
	// Block 0x7a, offset 0x1e80
	0x1e80: 0x00c0, 0x1e81: 0x00c0, 0x1e82: 0x00c0, 0x1e83: 0x00c0, 0x1e84: 0x00c0, 0x1e85: 0x00c0,
	0x1e86: 0x00c0, 0x1e87: 0x00c0, 0x1e88: 0x00c0, 0x1e89: 0x00c0, 0x1e8a: 0x00c0, 0x1e8b: 0x00c0,
	0x1e8c: 0x00c0, 0x1e8d: 0x00c0, 0x1e8e: 0x00c0, 0x1e8f: 0x00c0, 0x1e90: 0x00c0, 0x1e91: 0x00c0,
	0x1e92: 0x00c0, 0x1e93: 0x00c0, 0x1e94: 0x00c0, 0x1e95: 0x00c0, 0x1e96: 0x00c0, 0x1e97: 0x00c0,
	0x1e98: 0x00c0, 0x1e99: 0x00c0, 0x1e9a: 0x00c0, 0x1e9b: 0x00c0, 0x1e9c: 0x00c0, 0x1e9d: 0x00c0,
	0x1e9e: 0x00c0, 0x1ea0: 0x00c0, 0x1ea1: 0x00c0, 0x1ea2: 0x00c0, 0x1ea3: 0x00c0,
	0x1ea4: 0x00c0, 0x1ea5: 0x00c0, 0x1ea6: 0x00c0, 0x1ea7: 0x00c0, 0x1ea8: 0x00c0, 0x1ea9: 0x00c0,
	0x1eaa: 0x00c0, 0x1eab: 0x00c0, 0x1eac: 0x00c0, 0x1ead: 0x00c0, 0x1eae: 0x00c0, 0x1eaf: 0x00c0,
	0x1eb0: 0x00c0, 0x1eb1: 0x00c0, 0x1eb2: 0x00c0, 0x1eb3: 0x00c0, 0x1eb4: 0x00c0, 0x1eb5: 0x00c0,
	0x1eb6: 0x00c0, 0x1eb7: 0x00c0, 0x1eb8: 0x00c0, 0x1eb9: 0x00c0, 0x1eba: 0x00c0, 0x1ebb: 0x00c0,
	0x1ebc: 0x0080, 0x1ebd: 0x0080, 0x1ebe: 0x00c0, 0x1ebf: 0x00c0,
	// Block 0x7b, offset 0x1ec0
	0x1ec0: 0x00c0, 0x1ec1: 0x00c0, 0x1ec2: 0x00c0, 0x1ec3: 0x00c0, 0x1ec4: 0x00c0, 0x1ec5: 0x00c0,
	0x1ec6: 0x00c0, 0x1ec7: 0x00c0, 0x1ec8: 0x00c0, 0x1ec9: 0x00c0, 0x1eca: 0x00c0, 0x1ecb: 0x00c0,
	0x1ecc: 0x00c0, 0x1ecd: 0x00c0, 0x1ece: 0x00c0, 0x1ecf: 0x00c0, 0x1ed0: 0x00c0, 0x1ed1: 0x00c0,
	0x1ed2: 0x00c0, 0x1ed3: 0x00c0, 0x1ed4: 0x00c0, 0x1ed5: 0x00c0, 0x1ed6: 0x00c0, 0x1ed7: 0x00c0,
	0x1ed8: 0x00c0, 0x1ed9: 0x00c0, 0x1eda: 0x00c0, 0x1edb: 0x00c0, 0x1edc: 0x00c0, 0x1edd: 0x00c0,
	0x1ede: 0x00c0, 0x1edf: 0x00c0, 0x1ee0: 0x00c0, 0x1ee1: 0x00c0, 0x1ee2: 0x00c0, 0x1ee3: 0x00c0,
	0x1ee4: 0x00c0, 0x1ee5: 0x0080, 0x1ee6: 0x0080, 0x1ee7: 0x0080, 0x1ee8: 0x0080, 0x1ee9: 0x0080,
	0x1eea: 0x0080, 0x1eeb: 0x00c0, 0x1eec: 0x00c0, 0x1eed: 0x00c0, 0x1eee: 0x00c0, 0x1eef: 0x00c3,
	0x1ef0: 0x00c3, 0x1ef1: 0x00c3, 0x1ef2: 0x00c0, 0x1ef3: 0x00c0,
	0x1ef9: 0x0080, 0x1efa: 0x0080, 0x1efb: 0x0080,
	0x1efc: 0x0080, 0x1efd: 0x0080, 0x1efe: 0x0080, 0x1eff: 0x0080,
	// Block 0x7c, offset 0x1f00
	0x1f00: 0x00c0, 0x1f01: 0x00c0, 0x1f02: 0x00c0, 0x1f03: 0x00c0, 0x1f04: 0x00c0, 0x1f05: 0x00c0,
	0x1f06: 0x00c0, 0x1f07: 0x00c0, 0x1f08: 0x00c0, 0x1f09: 0x00c0, 0x1f0a: 0x00c0, 0x1f0b: 0x00c0,
	0x1f0c: 0x00c0, 0x1f0d: 0x00c0, 0x1f0e: 0x00c0, 0x1f0f: 0x00c0, 0x1f10: 0x00c0, 0x1f11: 0x00c0,
	0x1f12: 0x00c0, 0x1f13: 0x00c0, 0x1f14: 0x00c0, 0x1f15: 0x00c0, 0x1f16: 0x00c0, 0x1f17: 0x00c0,
	0x1f18: 0x00c0, 0x1f19: 0x00c0, 0x1f1a: 0x00c0, 0x1f1b: 0x00c0, 0x1f1c: 0x00c0, 0x1f1d: 0x00c0,
	0x1f1e: 0x00c0, 0x1f1f: 0x00c0, 0x1f20: 0x00c0, 0x1f21: 0x00c0, 0x1f22: 0x00c0, 0x1f23: 0x00c0,
	0x1f24: 0x00c0, 0x1f25: 0x00c0, 0x1f27: 0x00c0,
	0x1f2d: 0x00c0,
	0x1f30: 0x00c0, 0x1f31: 0x00c0, 0x1f32: 0x00c0, 0x1f33: 0x00c0, 0x1f34: 0x00c0, 0x1f35: 0x00c0,
	0x1f36: 0x00c0, 0x1f37: 0x00c0, 0x1f38: 0x00c0, 0x1f39: 0x00c0, 0x1f3a: 0x00c0, 0x1f3b: 0x00c0,
	0x1f3c: 0x00c0, 0x1f3d: 0x00c0, 0x1f3e: 0x00c0, 0x1f3f: 0x00c0,
	// Block 0x7d, offset 0x1f40
	0x1f40: 0x00c0, 0x1f41: 0x00c0, 0x1f42: 0x00c0, 0x1f43: 0x00c0, 0x1f44: 0x00c0, 0x1f45: 0x00c0,
	0x1f46: 0x00c0, 0x1f47: 0x00c0, 0x1f48: 0x00c0, 0x1f49: 0x00c0, 0x1f4a: 0x00c0, 0x1f4b: 0x00c0,
	0x1f4c: 0x00c0, 0x1f4d: 0x00c0, 0x1f4e: 0x00c0, 0x1f4f: 0x00c0, 0x1f50: 0x00c0, 0x1f51: 0x00c0,
	0x1f52: 0x00c0, 0x1f53: 0x00c0, 0x1f54: 0x00c0, 0x1f55: 0x00c0, 0x1f56: 0x00c0, 0x1f57: 0x00c0,
	0x1f58: 0x00c0, 0x1f59: 0x00c0, 0x1f5a: 0x00c0, 0x1f5b: 0x00c0, 0x1f5c: 0x00c0, 0x1f5d: 0x00c0,
	0x1f5e: 0x00c0, 0x1f5f: 0x00c0, 0x1f60: 0x00c0, 0x1f61: 0x00c0, 0x1f62: 0x00c0, 0x1f63: 0x00c0,
	0x1f64: 0x00c0, 0x1f65: 0x00c0, 0x1f66: 0x00c0, 0x1f67: 0x00c0,
	0x1f6f: 0x0080,
	0x1f70: 0x0080,
	0x1f7f: 0x00c6,
	// Block 0x7e, offset 0x1f80
	0x1f80: 0x00c0, 0x1f81: 0x00c0, 0x1f82: 0x00c0, 0x1f83: 0x00c0, 0x1f84: 0x00c0, 0x1f85: 0x00c0,
	0x1f86: 0x00c0, 0x1f87: 0x00c0, 0x1f88: 0x00c0, 0x1f89: 0x00c0, 0x1f8a: 0x00c0, 0x1f8b: 0x00c0,
	0x1f8c: 0x00c0, 0x1f8d: 0x00c0, 0x1f8e: 0x00c0, 0x1f8f: 0x00c0, 0x1f90: 0x00c0, 0x1f91: 0x00c0,
	0x1f92: 0x00c0, 0x1f93: 0x00c0, 0x1f94: 0x00c0, 0x1f95: 0x00c0, 0x1f96: 0x00c0,
	0x1fa0: 0x00c0, 0x1fa1: 0x00c0, 0x1fa2: 0x00c0, 0x1fa3: 0x00c0,
	0x1fa4: 0x00c0, 0x1fa5: 0x00c0, 0x1fa6: 0x00c0, 0x1fa8: 0x00c0, 0x1fa9: 0x00c0,
	0x1faa: 0x00c0, 0x1fab: 0x00c0, 0x1fac: 0x00c0, 0x1fad: 0x00c0, 0x1fae: 0x00c0,
	0x1fb0: 0x00c0, 0x1fb1: 0x00c0, 0x1fb2: 0x00c0, 0x1fb3: 0x00c0, 0x1fb4: 0x00c0, 0x1fb5: 0x00c0,
	0x1fb6: 0x00c0, 0x1fb8: 0x00c0, 0x1fb9: 0x00c0, 0x1fba: 0x00c0, 0x1fbb: 0x00c0,
	0x1fbc: 0x00c0, 0x1fbd: 0x00c0, 0x1fbe: 0x00c0,
	// Block 0x7f, offset 0x1fc0
	0x1fc0: 0x00c0, 0x1fc1: 0x00c0, 0x1fc2: 0x00c0, 0x1fc3: 0x00c0, 0x1fc4: 0x00c0, 0x1fc5: 0x00c0,
	0x1fc6: 0x00c0, 0x1fc8: 0x00c0, 0x1fc9: 0x00c0, 0x1fca: 0x00c0, 0x1fcb: 0x00c0,
	0x1fcc: 0x00c0, 0x1fcd: 0x00c0, 0x1fce: 0x00c0, 0x1fd0: 0x00c0, 0x1fd1: 0x00c0,
	0x1fd2: 0x00c0, 0x1fd3: 0x00c0, 0x1fd4: 0x00c0, 0x1fd5: 0x00c0, 0x1fd6: 0x00c0,
	0x1fd8: 0x00c0, 0x1fd9: 0x00c0, 0x1fda: 0x00c0, 0x1fdb: 0x00c0, 0x1fdc: 0x00c0, 0x1fdd: 0x00c0,
	0x1fde: 0x00c0, 0x1fe0: 0x00c3, 0x1fe1: 0x00c3, 0x1fe2: 0x00c3, 0x1fe3: 0x00c3,
	0x1fe4: 0x00c3, 0x1fe5: 0x00c3, 0x1fe6: 0x00c3, 0x1fe7: 0x00c3, 0x1fe8: 0x00c3, 0x1fe9: 0x00c3,
	0x1fea: 0x00c3, 0x1feb: 0x00c3, 0x1fec: 0x00c3, 0x1fed: 0x00c3, 0x1fee: 0x00c3, 0x1fef: 0x00c3,
	0x1ff0: 0x00c3, 0x1ff1: 0x00c3, 0x1ff2: 0x00c3, 0x1ff3: 0x00c3, 0x1ff4: 0x00c3, 0x1ff5: 0x00c3,
	0x1ff6: 0x00c3, 0x1ff7: 0x00c3, 0x1ff8: 0x00c3, 0x1ff9: 0x00c3, 0x1ffa: 0x00c3, 0x1ffb: 0x00c3,
	0x1ffc: 0x00c3, 0x1ffd: 0x00c3, 0x1ffe: 0x00c3, 0x1fff: 0x00c3,
	// Block 0x80, offset 0x2000
	0x2000: 0x0080, 0x2001: 0x0080, 0x2002: 0x0080, 0x2003: 0x0080, 0x2004: 0x0080, 0x2005: 0x0080,
	0x2006: 0x0080, 0x2007: 0x0080, 0x2008: 0x0080, 0x2009: 0x0080, 0x200a: 0x0080, 0x200b: 0x0080,
	0x200c: 0x0080, 0x200d: 0x0080, 0x200e: 0x0080, 0x200f: 0x0080, 0x2010: 0x0080, 0x2011: 0x0080,
	0x2012: 0x0080, 0x2013: 0x0080, 0x2014: 0x0080, 0x2015: 0x0080, 0x2016: 0x0080, 0x2017: 0x0080,
	0x2018: 0x0080, 0x2019: 0x0080, 0x201a: 0x0080, 0x201b: 0x0080, 0x201c: 0x0080, 0x201d: 0x0080,
	0x201e: 0x0080, 0x201f: 0x0080, 0x2020: 0x0080, 0x2021: 0x0080, 0x2022: 0x0080, 0x2023: 0x0080,
	0x2024: 0x0080, 0x2025: 0x0080, 0x2026: 0x0080, 0x2027: 0x0080, 0x2028: 0x0080, 0x2029: 0x0080,
	0x202a: 0x0080, 0x202b: 0x0080, 0x202c: 0x0080, 0x202d: 0x0080, 0x202e: 0x0080, 0x202f: 0x00c0,
	0x2030: 0x0080, 0x2031: 0x0080, 0x2032: 0x0080, 0x2033: 0x0080, 0x2034: 0x0080, 0x2035: 0x0080,
	0x2036: 0x0080, 0x2037: 0x0080, 0x2038: 0x0080, 0x2039: 0x0080, 0x203a: 0x0080, 0x203b: 0x0080,
	0x203c: 0x0080, 0x203d: 0x0080, 0x203e: 0x0080, 0x203f: 0x0080,
	// Block 0x81, offset 0x2040
	0x2040: 0x0080, 0x2041: 0x0080, 0x2042: 0x0080, 0x2043: 0x0080, 0x2044: 0x0080, 0x2045: 0x0080,
	0x2046: 0x0080, 0x2047: 0x0080, 0x2048: 0x0080, 0x2049: 0x0080, 0x204a: 0x0080, 0x204b: 0x0080,
	0x204c: 0x0080, 0x204d: 0x0080, 0x204e: 0x0080, 0x204f: 0x0080, 0x2050: 0x0080, 0x2051: 0x0080,
	0x2052: 0x0080,
	// Block 0x82, offset 0x2080
	0x2080: 0x008c, 0x2081: 0x008c, 0x2082: 0x008c, 0x2083: 0x008c, 0x2084: 0x008c, 0x2085: 0x008c,
	0x2086: 0x008c, 0x2087: 0x008c, 0x2088: 0x008c, 0x2089: 0x008c, 0x208a: 0x008c, 0x208b: 0x008c,
	0x208c: 0x008c, 0x208d: 0x008c, 0x208e: 0x008c, 0x208f: 0x008c, 0x2090: 0x008c, 0x2091: 0x008c,
	0x2092: 0x008c, 0x2093: 0x008c, 0x2094: 0x008c, 0x2095: 0x008c, 0x2096: 0x008c, 0x2097: 0x008c,
	0x2098: 0x008c, 0x2099: 0x008c, 0x209b: 0x008c, 0x209c: 0x008c, 0x209d: 0x008c,
	0x209e: 0x008c, 0x209f: 0x008c, 0x20a0: 0x008c, 0x20a1: 0x008c, 0x20a2: 0x008c, 0x20a3: 0x008c,
	0x20a4: 0x008c, 0x20a5: 0x008c, 0x20a6: 0x008c, 0x20a7: 0x008c, 0x20a8: 0x008c, 0x20a9: 0x008c,
	0x20aa: 0x008c, 0x20ab: 0x008c, 0x20ac: 0x008c, 0x20ad: 0x008c, 0x20ae: 0x008c, 0x20af: 0x008c,
	0x20b0: 0x008c, 0x20b1: 0x008c, 0x20b2: 0x008c, 0x20b3: 0x008c, 0x20b4: 0x008c, 0x20b5: 0x008c,
	0x20b6: 0x008c, 0x20b7: 0x008c, 0x20b8: 0x008c, 0x20b9: 0x008c, 0x20ba: 0x008c, 0x20bb: 0x008c,
	0x20bc: 0x008c, 0x20bd: 0x008c, 0x20be: 0x008c, 0x20bf: 0x008c,
	// Block 0x83, offset 0x20c0
	0x20c0: 0x008c, 0x20c1: 0x008c, 0x20c2: 0x008c, 0x20c3: 0x008c, 0x20c4: 0x008c, 0x20c5: 0x008c,
	0x20c6: 0x008c, 0x20c7: 0x008c, 0x20c8: 0x008c, 0x20c9: 0x008c, 0x20ca: 0x008c, 0x20cb: 0x008c,
	0x20cc: 0x008c, 0x20cd: 0x008c, 0x20ce: 0x008c, 0x20cf: 0x008c, 0x20d0: 0x008c, 0x20d1: 0x008c,
	0x20d2: 0x008c, 0x20d3: 0x008c, 0x20d4: 0x008c, 0x20d5: 0x008c, 0x20d6: 0x008c, 0x20d7: 0x008c,
	0x20d8: 0x008c, 0x20d9: 0x008c, 0x20da: 0x008c, 0x20db: 0x008c, 0x20dc: 0x008c, 0x20dd: 0x008c,
	0x20de: 0x008c, 0x20df: 0x008c, 0x20e0: 0x008c, 0x20e1: 0x008c, 0x20e2: 0x008c, 0x20e3: 0x008c,
	0x20e4: 0x008c, 0x20e5: 0x008c, 0x20e6: 0x008c, 0x20e7: 0x008c, 0x20e8: 0x008c, 0x20e9: 0x008c,
	0x20ea: 0x008c, 0x20eb: 0x008c, 0x20ec: 0x008c, 0x20ed: 0x008c, 0x20ee: 0x008c, 0x20ef: 0x008c,
	0x20f0: 0x008c, 0x20f1: 0x008c, 0x20f2: 0x008c, 0x20f3: 0x008c,
	// Block 0x84, offset 0x2100
	0x2100: 0x008c, 0x2101: 0x008c, 0x2102: 0x008c, 0x2103: 0x008c, 0x2104: 0x008c, 0x2105: 0x008c,
	0x2106: 0x008c, 0x2107: 0x008c, 0x2108: 0x008c, 0x2109: 0x008c, 0x210a: 0x008c, 0x210b: 0x008c,
	0x210c: 0x008c, 0x210d: 0x008c, 0x210e: 0x008c, 0x210f: 0x008c, 0x2110: 0x008c, 0x2111: 0x008c,
	0x2112: 0x008c, 0x2113: 0x008c, 0x2114: 0x008c, 0x2115: 0x008c, 0x2116: 0x008c, 0x2117: 0x008c,
	0x2118: 0x008c, 0x2119: 0x008c, 0x211a: 0x008c, 0x211b: 0x008c, 0x211c: 0x008c, 0x211d: 0x008c,
	0x211e: 0x008c, 0x211f: 0x008c, 0x2120: 0x008c, 0x2121: 0x008c, 0x2122: 0x008c, 0x2123: 0x008c,
	0x2124: 0x008c, 0x2125: 0x008c, 0x2126: 0x008c, 0x2127: 0x008c, 0x2128: 0x008c, 0x2129: 0x008c,
	0x212a: 0x008c, 0x212b: 0x008c, 0x212c: 0x008c, 0x212d: 0x008c, 0x212e: 0x008c, 0x212f: 0x008c,
	0x2130: 0x008c, 0x2131: 0x008c, 0x2132: 0x008c, 0x2133: 0x008c, 0x2134: 0x008c, 0x2135: 0x008c,
	0x2136: 0x008c, 0x2137: 0x008c, 0x2138: 0x008c, 0x2139: 0x008c, 0x213a: 0x008c, 0x213b: 0x008c,
	0x213c: 0x008c, 0x213d: 0x008c, 0x213e: 0x008c, 0x213f: 0x008c,
	// Block 0x85, offset 0x2140
	0x2140: 0x008c, 0x2141: 0x008c, 0x2142: 0x008c, 0x2143: 0x008c, 0x2144: 0x008c, 0x2145: 0x008c,
	0x2146: 0x008c, 0x2147: 0x008c, 0x2148: 0x008c, 0x2149: 0x008c, 0x214a: 0x008c, 0x214b: 0x008c,
	0x214c: 0x008c, 0x214d: 0x008c, 0x214e: 0x008c, 0x214f: 0x008c, 0x2150: 0x008c, 0x2151: 0x008c,
	0x2152: 0x008c, 0x2153: 0x008c, 0x2154: 0x008c, 0x2155: 0x008c,
	0x2170: 0x0080, 0x2171: 0x0080, 0x2172: 0x0080, 0x2173: 0x0080, 0x2174: 0x0080, 0x2175: 0x0080,
	0x2176: 0x0080, 0x2177: 0x0080, 0x2178: 0x0080, 0x2179: 0x0080, 0x217a: 0x0080, 0x217b: 0x0080,
	// Block 0x86, offset 0x2180
	0x2180: 0x0080, 0x2181: 0x0080, 0x2182: 0x0080, 0x2183: 0x0080, 0x2184: 0x0080, 0x2185: 0x00cc,
	0x2186: 0x00c0, 0x2187: 0x00cc, 0x2188: 0x0080, 0x2189: 0x0080, 0x218a: 0x0080, 0x218b: 0x0080,
	0x218c: 0x0080, 0x218d: 0x0080, 0x218e: 0x0080, 0x218f: 0x0080, 0x2190: 0x0080, 0x2191: 0x0080,
	0x2192: 0x0080, 0x2193: 0x0080, 0x2194: 0x0080, 0x2195: 0x0080, 0x2196: 0x0080, 0x2197: 0x0080,
	0x2198: 0x0080, 0x2199: 0x0080, 0x219a: 0x0080, 0x219b: 0x0080, 0x219c: 0x0080, 0x219d: 0x0080,
	0x219e: 0x0080, 0x219f: 0x0080, 0x21a0: 0x0080, 0x21a1: 0x008c, 0x21a2: 0x008c, 0x21a3: 0x008c,
	0x21a4: 0x008c, 0x21a5: 0x008c, 0x21a6: 0x008c, 0x21a7: 0x008c, 0x21a8: 0x008c, 0x21a9: 0x008c,
	0x21aa: 0x00c3, 0x21ab: 0x00c3, 0x21ac: 0x00c3, 0x21ad: 0x00c3, 0x21ae: 0x0040, 0x21af: 0x0040,
	0x21b0: 0x0080, 0x21b1: 0x0040, 0x21b2: 0x0040, 0x21b3: 0x0040, 0x21b4: 0x0040, 0x21b5: 0x0040,
	0x21b6: 0x0080, 0x21b7: 0x0080, 0x21b8: 0x008c, 0x21b9: 0x008c, 0x21ba: 0x008c, 0x21bb: 0x0040,
	0x21bc: 0x00c0, 0x21bd: 0x0080, 0x21be: 0x0080, 0x21bf: 0x0080,
	// Block 0x87, offset 0x21c0
	0x21c1: 0x00cc, 0x21c2: 0x00cc, 0x21c3: 0x00cc, 0x21c4: 0x00cc, 0x21c5: 0x00cc,
	0x21c6: 0x00cc, 0x21c7: 0x00cc, 0x21c8: 0x00cc, 0x21c9: 0x00cc, 0x21ca: 0x00cc, 0x21cb: 0x00cc,
	0x21cc: 0x00cc, 0x21cd: 0x00cc, 0x21ce: 0x00cc, 0x21cf: 0x00cc, 0x21d0: 0x00cc, 0x21d1: 0x00cc,
	0x21d2: 0x00cc, 0x21d3: 0x00cc, 0x21d4: 0x00cc, 0x21d5: 0x00cc, 0x21d6: 0x00cc, 0x21d7: 0x00cc,
	0x21d8: 0x00cc, 0x21d9: 0x00cc, 0x21da: 0x00cc, 0x21db: 0x00cc, 0x21dc: 0x00cc, 0x21dd: 0x00cc,
	0x21de: 0x00cc, 0x21df: 0x00cc, 0x21e0: 0x00cc, 0x21e1: 0x00cc, 0x21e2: 0x00cc, 0x21e3: 0x00cc,
	0x21e4: 0x00cc, 0x21e5: 0x00cc, 0x21e6: 0x00cc, 0x21e7: 0x00cc, 0x21e8: 0x00cc, 0x21e9: 0x00cc,
	0x21ea: 0x00cc, 0x21eb: 0x00cc, 0x21ec: 0x00cc, 0x21ed: 0x00cc, 0x21ee: 0x00cc, 0x21ef: 0x00cc,
	0x21f0: 0x00cc, 0x21f1: 0x00cc, 0x21f2: 0x00cc, 0x21f3: 0x00cc, 0x21f4: 0x00cc, 0x21f5: 0x00cc,
	0x21f6: 0x00cc, 0x21f7: 0x00cc, 0x21f8: 0x00cc, 0x21f9: 0x00cc, 0x21fa: 0x00cc, 0x21fb: 0x00cc,
	0x21fc: 0x00cc, 0x21fd: 0x00cc, 0x21fe: 0x00cc, 0x21ff: 0x00cc,
	// Block 0x88, offset 0x2200
	0x2200: 0x00cc, 0x2201: 0x00cc, 0x2202: 0x00cc, 0x2203: 0x00cc, 0x2204: 0x00cc, 0x2205: 0x00cc,
	0x2206: 0x00cc, 0x2207: 0x00cc, 0x2208: 0x00cc, 0x2209: 0x00cc, 0x220a: 0x00cc, 0x220b: 0x00cc,
	0x220c: 0x00cc, 0x220d: 0x00cc, 0x220e: 0x00cc, 0x220f: 0x00cc, 0x2210: 0x00cc, 0x2211: 0x00cc,
	0x2212: 0x00cc, 0x2213: 0x00cc, 0x2214: 0x00cc, 0x2215: 0x00cc, 0x2216: 0x00cc,
	0x2219: 0x00c3, 0x221a: 0x00c3, 0x221b: 0x0080, 0x221c: 0x0080, 0x221d: 0x00cc,
	0x221e: 0x00cc, 0x221f: 0x008c, 0x2220: 0x0080, 0x2221: 0x00cc, 0x2222: 0x00cc, 0x2223: 0x00cc,
	0x2224: 0x00cc, 0x2225: 0x00cc, 0x2226: 0x00cc, 0x2227: 0x00cc, 0x2228: 0x00cc, 0x2229: 0x00cc,
	0x222a: 0x00cc, 0x222b: 0x00cc, 0x222c: 0x00cc, 0x222d: 0x00cc, 0x222e: 0x00cc, 0x222f: 0x00cc,
	0x2230: 0x00cc, 0x2231: 0x00cc, 0x2232: 0x00cc, 0x2233: 0x00cc, 0x2234: 0x00cc, 0x2235: 0x00cc,
	0x2236: 0x00cc, 0x2237: 0x00cc, 0x2238: 0x00cc, 0x2239: 0x00cc, 0x223a: 0x00cc, 0x223b: 0x00cc,
	0x223c: 0x00cc, 0x223d: 0x00cc, 0x223e: 0x00cc, 0x223f: 0x00cc,
	// Block 0x89, offset 0x2240
	0x2240: 0x00cc, 0x2241: 0x00cc, 0x2242: 0x00cc, 0x2243: 0x00cc, 0x2244: 0x00cc, 0x2245: 0x00cc,
	0x2246: 0x00cc, 0x2247: 0x00cc, 0x2248: 0x00cc, 0x2249: 0x00cc, 0x224a: 0x00cc, 0x224b: 0x00cc,
	0x224c: 0x00cc, 0x224d: 0x00cc, 0x224e: 0x00cc, 0x224f: 0x00cc, 0x2250: 0x00cc, 0x2251: 0x00cc,
	0x2252: 0x00cc, 0x2253: 0x00cc, 0x2254: 0x00cc, 0x2255: 0x00cc, 0x2256: 0x00cc, 0x2257: 0x00cc,
	0x2258: 0x00cc, 0x2259: 0x00cc, 0x225a: 0x00cc, 0x225b: 0x00cc, 0x225c: 0x00cc, 0x225d: 0x00cc,
	0x225e: 0x00cc, 0x225f: 0x00cc, 0x2260: 0x00cc, 0x2261: 0x00cc, 0x2262: 0x00cc, 0x2263: 0x00cc,
	0x2264: 0x00cc, 0x2265: 0x00cc, 0x2266: 0x00cc, 0x2267: 0x00cc, 0x2268: 0x00cc, 0x2269: 0x00cc,
	0x226a: 0x00cc, 0x226b: 0x00cc, 0x226c: 0x00cc, 0x226d: 0x00cc, 0x226e: 0x00cc, 0x226f: 0x00cc,
	0x2270: 0x00cc, 0x2271: 0x00cc, 0x2272: 0x00cc, 0x2273: 0x00cc, 0x2274: 0x00cc, 0x2275: 0x00cc,
	0x2276: 0x00cc, 0x2277: 0x00cc, 0x2278: 0x00cc, 0x2279: 0x00cc, 0x227a: 0x00cc, 0x227b: 0x00d2,
	0x227c: 0x00c0, 0x227d: 0x00cc, 0x227e: 0x00cc, 0x227f: 0x008c,
	// Block 0x8a, offset 0x2280
	0x2285: 0x00c0,
	0x2286: 0x00c0, 0x2287: 0x00c0, 0x2288: 0x00c0, 0x2289: 0x00c0, 0x228a: 0x00c0, 0x228b: 0x00c0,
	0x228c: 0x00c0, 0x228d: 0x00c0, 0x228e: 0x00c0, 0x228f: 0x00c0, 0x2290: 0x00c0, 0x2291: 0x00c0,
	0x2292: 0x00c0, 0x2293: 0x00c0, 0x2294: 0x00c0, 0x2295: 0x00c0, 0x2296: 0x00c0, 0x2297: 0x00c0,
	0x2298: 0x00c0, 0x2299: 0x00c0, 0x229a: 0x00c0, 0x229b: 0x00c0, 0x229c: 0x00c0, 0x229d: 0x00c0,
	0x229e: 0x00c0, 0x229f: 0x00c0, 0x22a0: 0x00c0, 0x22a1: 0x00c0, 0x22a2: 0x00c0, 0x22a3: 0x00c0,
	0x22a4: 0x00c0, 0x22a5: 0x00c0, 0x22a6: 0x00c0, 0x22a7: 0x00c0, 0x22a8: 0x00c0, 0x22a9: 0x00c0,
	0x22aa: 0x00c0, 0x22ab: 0x00c0, 0x22ac: 0x00c0, 0x22ad: 0x00c0, 0x22ae: 0x00c0, 0x22af: 0x00c0,
	0x22b1: 0x0080, 0x22b2: 0x0080, 0x22b3: 0x0080, 0x22b4: 0x0080, 0x22b5: 0x0080,
	0x22b6: 0x0080, 0x22b7: 0x0080, 0x22b8: 0x0080, 0x22b9: 0x0080, 0x22ba: 0x0080, 0x22bb: 0x0080,
	0x22bc: 0x0080, 0x22bd: 0x0080, 0x22be: 0x0080, 0x22bf: 0x0080,
	// Block 0x8b, offset 0x22c0
	0x22c0: 0x0080, 0x22c1: 0x0080, 0x22c2: 0x0080, 0x22c3: 0x0080, 0x22c4: 0x0080, 0x22c5: 0x0080,
	0x22c6: 0x0080, 0x22c7: 0x0080, 0x22c8: 0x0080, 0x22c9: 0x0080, 0x22ca: 0x0080, 0x22cb: 0x0080,
	0x22cc: 0x0080, 0x22cd: 0x0080, 0x22ce: 0x0080, 0x22cf: 0x0080, 0x22d0: 0x0080, 0x22d1: 0x0080,
	0x22d2: 0x0080, 0x22d3: 0x0080, 0x22d4: 0x0080, 0x22d5: 0x0080, 0x22d6: 0x0080, 0x22d7: 0x0080,
	0x22d8: 0x0080, 0x22d9: 0x0080, 0x22da: 0x0080, 0x22db: 0x0080, 0x22dc: 0x0080, 0x22dd: 0x0080,
	0x22de: 0x0080, 0x22df: 0x0080, 0x22e0: 0x0080, 0x22e1: 0x0080, 0x22e2: 0x0080, 0x22e3: 0x0080,
	0x22e4: 0x0040, 0x22e5: 0x0080, 0x22e6: 0x0080, 0x22e7: 0x0080, 0x22e8: 0x0080, 0x22e9: 0x0080,
	0x22ea: 0x0080, 0x22eb: 0x0080, 0x22ec: 0x0080, 0x22ed: 0x0080, 0x22ee: 0x0080, 0x22ef: 0x0080,
	0x22f0: 0x0080, 0x22f1: 0x0080, 0x22f2: 0x0080, 0x22f3: 0x0080, 0x22f4: 0x0080, 0x22f5: 0x0080,
	0x22f6: 0x0080, 0x22f7: 0x0080, 0x22f8: 0x0080, 0x22f9: 0x0080, 0x22fa: 0x0080, 0x22fb: 0x0080,
	0x22fc: 0x0080, 0x22fd: 0x0080, 0x22fe: 0x0080, 0x22ff: 0x0080,
	// Block 0x8c, offset 0x2300
	0x2300: 0x0080, 0x2301: 0x0080, 0x2302: 0x0080, 0x2303: 0x0080, 0x2304: 0x0080, 0x2305: 0x0080,
	0x2306: 0x0080, 0x2307: 0x0080, 0x2308: 0x0080, 0x2309: 0x0080, 0x230a: 0x0080, 0x230b: 0x0080,
	0x230c: 0x0080, 0x230d: 0x0080, 0x230e: 0x0080, 0x2310: 0x0080, 0x2311: 0x0080,
	0x2312: 0x0080, 0x2313: 0x0080, 0x2314: 0x0080, 0x2315: 0x0080, 0x2316: 0x0080, 0x2317: 0x0080,
	0x2318: 0x0080, 0x2319: 0x0080, 0x231a: 0x0080, 0x231b: 0x0080, 0x231c: 0x0080, 0x231d: 0x0080,
	0x231e: 0x0080, 0x231f: 0x0080, 0x2320: 0x00c0, 0x2321: 0x00c0, 0x2322: 0x00c0, 0x2323: 0x00c0,
	0x2324: 0x00c0, 0x2325: 0x00c0, 0x2326: 0x00c0, 0x2327: 0x00c0, 0x2328: 0x00c0, 0x2329: 0x00c0,
	0x232a: 0x00c0, 0x232b: 0x00c0, 0x232c: 0x00c0, 0x232d: 0x00c0, 0x232e: 0x00c0, 0x232f: 0x00c0,
	0x2330: 0x00c0, 0x2331: 0x00c0, 0x2332: 0x00c0, 0x2333: 0x00c0, 0x2334: 0x00c0, 0x2335: 0x00c0,
	0x2336: 0x00c0, 0x2337: 0x00c0, 0x2338: 0x00c0, 0x2339: 0x00c0, 0x233a: 0x00c0, 0x233b: 0x00c0,
	0x233c: 0x00c0, 0x233d: 0x00c0, 0x233e: 0x00c0, 0x233f: 0x00c0,
	// Block 0x8d, offset 0x2340
	0x2340: 0x0080, 0x2341: 0x0080, 0x2342: 0x0080, 0x2343: 0x0080, 0x2344: 0x0080, 0x2345: 0x0080,
	0x2346: 0x0080, 0x2347: 0x0080, 0x2348: 0x0080, 0x2349: 0x0080, 0x234a: 0x0080, 0x234b: 0x0080,
	0x234c: 0x0080, 0x234d: 0x0080, 0x234e: 0x0080, 0x234f: 0x0080, 0x2350: 0x0080, 0x2351: 0x0080,
	0x2352: 0x0080, 0x2353: 0x0080, 0x2354: 0x0080, 0x2355: 0x0080, 0x2356: 0x0080, 0x2357: 0x0080,
	0x2358: 0x0080, 0x2359: 0x0080, 0x235a: 0x0080, 0x235b: 0x0080, 0x235c: 0x0080, 0x235d: 0x0080,
	0x235e: 0x0080, 0x235f: 0x0080, 0x2360: 0x0080, 0x2361: 0x0080, 0x2362: 0x0080, 0x2363: 0x0080,
	0x2370: 0x00cc, 0x2371: 0x00cc, 0x2372: 0x00cc, 0x2373: 0x00cc, 0x2374: 0x00cc, 0x2375: 0x00cc,
	0x2376: 0x00cc, 0x2377: 0x00cc, 0x2378: 0x00cc, 0x2379: 0x00cc, 0x237a: 0x00cc, 0x237b: 0x00cc,
	0x237c: 0x00cc, 0x237d: 0x00cc, 0x237e: 0x00cc, 0x237f: 0x00cc,
	// Block 0x8e, offset 0x2380
	0x2380: 0x0080, 0x2381: 0x0080, 0x2382: 0x0080, 0x2383: 0x0080, 0x2384: 0x0080, 0x2385: 0x0080,
	0x2386: 0x0080, 0x2387: 0x0080, 0x2388: 0x0080, 0x2389: 0x0080, 0x238a: 0x0080, 0x238b: 0x0080,
	0x238c: 0x0080, 0x238d: 0x0080, 0x238e: 0x0080, 0x238f: 0x0080, 0x2390: 0x0080, 0x2391: 0x0080,
	0x2392: 0x0080, 0x2393: 0x0080, 0x2394: 0x0080, 0x2395: 0x0080, 0x2396: 0x0080, 0x2397: 0x0080,
	0x2398: 0x0080, 0x2399: 0x0080, 0x239a: 0x0080, 0x239b: 0x0080, 0x239c: 0x0080, 0x239d: 0x0080,
	0x239e: 0x0080, 0x23a0: 0x0080, 0x23a1: 0x0080, 0x23a2: 0x0080, 0x23a3: 0x0080,
	0x23a4: 0x0080, 0x23a5: 0x0080, 0x23a6: 0x0080, 0x23a7: 0x0080, 0x23a8: 0x0080, 0x23a9: 0x0080,
	0x23aa: 0x0080, 0x23ab: 0x0080, 0x23ac: 0x0080, 0x23ad: 0x0080, 0x23ae: 0x0080, 0x23af: 0x0080,
	0x23b0: 0x0080, 0x23b1: 0x0080, 0x23b2: 0x0080, 0x23b3: 0x0080, 0x23b4: 0x0080, 0x23b5: 0x0080,
	0x23b6: 0x0080, 0x23b7: 0x0080, 0x23b8: 0x0080, 0x23b9: 0x0080, 0x23ba: 0x0080, 0x23bb: 0x0080,
	0x23bc: 0x0080, 0x23bd: 0x0080, 0x23be: 0x0080, 0x23bf: 0x0080,
	// Block 0x8f, offset 0x23c0
	0x23c0: 0x0080, 0x23c1: 0x0080, 0x23c2: 0x0080, 0x23c3: 0x0080, 0x23c4: 0x0080, 0x23c5: 0x0080,
	0x23c6: 0x0080, 0x23c7: 0x0080, 0x23c8: 0x0080, 0x23c9: 0x0080, 0x23ca: 0x0080, 0x23cb: 0x0080,
	0x23cc: 0x0080, 0x23cd: 0x0080, 0x23ce: 0x0080, 0x23cf: 0x0080, 0x23d0: 0x008c, 0x23d1: 0x008c,
	0x23d2: 0x008c, 0x23d3: 0x008c, 0x23d4: 0x008c, 0x23d5: 0x008c, 0x23d6: 0x008c, 0x23d7: 0x008c,
	0x23d8: 0x008c, 0x23d9: 0x008c, 0x23da: 0x008c, 0x23db: 0x008c, 0x23dc: 0x008c, 0x23dd: 0x008c,
	0x23de: 0x008c, 0x23df: 0x008c, 0x23e0: 0x008c, 0x23e1: 0x008c, 0x23e2: 0x008c, 0x23e3: 0x008c,
	0x23e4: 0x008c, 0x23e5: 0x008c, 0x23e6: 0x008c, 0x23e7: 0x008c, 0x23e8: 0x008c, 0x23e9: 0x008c,
	0x23ea: 0x008c, 0x23eb: 0x008c, 0x23ec: 0x008c, 0x23ed: 0x008c, 0x23ee: 0x008c, 0x23ef: 0x008c,
	0x23f0: 0x008c, 0x23f1: 0x008c, 0x23f2: 0x008c, 0x23f3: 0x008c, 0x23f4: 0x008c, 0x23f5: 0x008c,
	0x23f6: 0x008c, 0x23f7: 0x008c, 0x23f8: 0x008c, 0x23f9: 0x008c, 0x23fa: 0x008c, 0x23fb: 0x008c,
	0x23fc: 0x008c, 0x23fd: 0x008c, 0x23fe: 0x008c, 0x23ff: 0x0080,
	// Block 0x90, offset 0x2400
	0x2400: 0x008c, 0x2401: 0x008c, 0x2402: 0x008c, 0x2403: 0x008c, 0x2404: 0x008c, 0x2405: 0x008c,
	0x2406: 0x008c, 0x2407: 0x008c, 0x2408: 0x008c, 0x2409: 0x008c, 0x240a: 0x008c, 0x240b: 0x008c,
	0x240c: 0x008c, 0x240d: 0x008c, 0x240e: 0x008c, 0x240f: 0x008c, 0x2410: 0x008c, 0x2411: 0x008c,
	0x2412: 0x008c, 0x2413: 0x008c, 0x2414: 0x008c, 0x2415: 0x008c, 0x2416: 0x008c, 0x2417: 0x008c,
	0x2418: 0x0080, 0x2419: 0x0080, 0x241a: 0x0080, 0x241b: 0x0080, 0x241c: 0x0080, 0x241d: 0x0080,
	0x241e: 0x0080, 0x241f: 0x0080, 0x2420: 0x0080, 0x2421: 0x0080, 0x2422: 0x0080, 0x2423: 0x0080,
	0x2424: 0x0080, 0x2425: 0x0080, 0x2426: 0x0080, 0x2427: 0x0080, 0x2428: 0x0080, 0x2429: 0x0080,
	0x242a: 0x0080, 0x242b: 0x0080, 0x242c: 0x0080, 0x242d: 0x0080, 0x242e: 0x0080, 0x242f: 0x0080,
	0x2430: 0x0080, 0x2431: 0x0080, 0x2432: 0x0080, 0x2433: 0x0080, 0x2434: 0x0080, 0x2435: 0x0080,
	0x2436: 0x0080, 0x2437: 0x0080, 0x2438: 0x0080, 0x2439: 0x0080, 0x243a: 0x0080, 0x243b: 0x0080,
	0x243c: 0x0080, 0x243d: 0x0080, 0x243e: 0x0080, 0x243f: 0x0080,
	// Block 0x91, offset 0x2440
	0x2440: 0x00cc, 0x2441: 0x00cc, 0x2442: 0x00cc, 0x2443: 0x00cc, 0x2444: 0x00cc, 0x2445: 0x00cc,
	0x2446: 0x00cc, 0x2447: 0x00cc, 0x2448: 0x00cc, 0x2449: 0x00cc, 0x244a: 0x00cc, 0x244b: 0x00cc,
	0x244c: 0x00cc, 0x244d: 0x00cc, 0x244e: 0x00cc, 0x244f: 0x00cc, 0x2450: 0x00cc, 0x2451: 0x00cc,
	0x2452: 0x00cc, 0x2453: 0x00cc, 0x2454: 0x00cc, 0x2455: 0x00cc, 0x2456: 0x00cc, 0x2457: 0x00cc,
	0x2458: 0x00cc, 0x2459: 0x00cc, 0x245a: 0x00cc, 0x245b: 0x00cc, 0x245c: 0x00cc, 0x245d: 0x00cc,
	0x245e: 0x00cc, 0x245f: 0x00cc, 0x2460: 0x00cc, 0x2461: 0x00cc, 0x2462: 0x00cc, 0x2463: 0x00cc,
	0x2464: 0x00cc, 0x2465: 0x00cc, 0x2466: 0x00cc, 0x2467: 0x00cc, 0x2468: 0x00cc, 0x2469: 0x00cc,
	0x246a: 0x00cc, 0x246b: 0x00cc, 0x246c: 0x00cc, 0x246d: 0x00cc, 0x246e: 0x00cc, 0x246f: 0x00cc,
	0x2470: 0x00cc, 0x2471: 0x00cc, 0x2472: 0x00cc, 0x2473: 0x00cc, 0x2474: 0x00cc, 0x2475: 0x00cc,
	0x2476: 0x00cc, 0x2477: 0x00cc, 0x2478: 0x00cc, 0x2479: 0x00cc, 0x247a: 0x00cc, 0x247b: 0x00cc,
	0x247c: 0x00cc, 0x247d: 0x00cc, 0x247e: 0x00cc, 0x247f: 0x00cc,
	// Block 0x92, offset 0x2480
	0x2480: 0x00cc, 0x2481: 0x00cc, 0x2482: 0x00cc, 0x2483: 0x00cc, 0x2484: 0x00cc, 0x2485: 0x00cc,
	0x2486: 0x00cc, 0x2487: 0x00cc, 0x2488: 0x00cc, 0x2489: 0x00cc, 0x248a: 0x00cc, 0x248b: 0x00cc,
	0x248c: 0x00cc, 0x248d: 0x00cc, 0x248e: 0x00cc, 0x248f: 0x00cc, 0x2490: 0x00cc, 0x2491: 0x00cc,
	0x2492: 0x00cc, 0x2493: 0x00cc, 0x2494: 0x00cc, 0x2495: 0x00cc, 0x2496: 0x00cc, 0x2497: 0x00cc,
	0x2498: 0x00cc, 0x2499: 0x00cc, 0x249a: 0x00cc, 0x249b: 0x00cc, 0x249c: 0x00cc, 0x249d: 0x00cc,
	0x249e: 0x00cc, 0x249f: 0x00cc, 0x24a0: 0x00cc, 0x24a1: 0x00cc, 0x24a2: 0x00cc, 0x24a3: 0x00cc,
	0x24a4: 0x00cc, 0x24a5: 0x00cc, 0x24a6: 0x00cc, 0x24a7: 0x00cc, 0x24a8: 0x00cc, 0x24a9: 0x00cc,
	0x24aa: 0x00cc, 0x24ab: 0x00cc, 0x24ac: 0x00cc, 0x24ad: 0x00cc, 0x24ae: 0x00cc, 0x24af: 0x00cc,
	0x24b0: 0x00cc, 0x24b1: 0x00cc, 0x24b2: 0x00cc, 0x24b3: 0x00cc, 0x24b4: 0x00cc, 0x24b5: 0x00cc,
	0x24b6: 0x00cc, 0x24b7: 0x00cc, 0x24b8: 0x00cc, 0x24b9: 0x00cc, 0x24ba: 0x00cc, 0x24bb: 0x00cc,
	0x24bc: 0x00cc,
	// Block 0x93, offset 0x24c0
	0x24c0: 0x00c0, 0x24c1: 0x00c0, 0x24c2: 0x00c0, 0x24c3: 0x00c0, 0x24c4: 0x00c0, 0x24c5: 0x00c0,
	0x24c6: 0x00c0, 0x24c7: 0x00c0, 0x24c8: 0x00c0, 0x24c9: 0x00c0, 0x24ca: 0x00c0, 0x24cb: 0x00c0,
	0x24cc: 0x00c0, 0x24d0: 0x0080, 0x24d1: 0x0080,
	0x24d2: 0x0080, 0x24d3: 0x0080, 0x24d4: 0x0080, 0x24d5: 0x0080, 0x24d6: 0x0080, 0x24d7: 0x0080,
	0x24d8: 0x0080, 0x24d9: 0x0080, 0x24da: 0x0080, 0x24db: 0x0080, 0x24dc: 0x0080, 0x24dd: 0x0080,
	0x24de: 0x0080, 0x24df: 0x0080, 0x24e0: 0x0080, 0x24e1: 0x0080, 0x24e2: 0x0080, 0x24e3: 0x0080,
	0x24e4: 0x0080, 0x24e5: 0x0080, 0x24e6: 0x0080, 0x24e7: 0x0080, 0x24e8: 0x0080, 0x24e9: 0x0080,
	0x24ea: 0x0080, 0x24eb: 0x0080, 0x24ec: 0x0080, 0x24ed: 0x0080, 0x24ee: 0x0080, 0x24ef: 0x0080,
	0x24f0: 0x0080, 0x24f1: 0x0080, 0x24f2: 0x0080, 0x24f3: 0x0080, 0x24f4: 0x0080, 0x24f5: 0x0080,
	0x24f6: 0x0080, 0x24f7: 0x0080, 0x24f8: 0x0080, 0x24f9: 0x0080, 0x24fa: 0x0080, 0x24fb: 0x0080,
	0x24fc: 0x0080, 0x24fd: 0x0080, 0x24fe: 0x0080, 0x24ff: 0x0080,
	// Block 0x94, offset 0x2500
	0x2500: 0x0080, 0x2501: 0x0080, 0x2502: 0x0080, 0x2503: 0x0080, 0x2504: 0x0080, 0x2505: 0x0080,
	0x2506: 0x0080,
	0x2510: 0x00c0, 0x2511: 0x00c0,
	0x2512: 0x00c0, 0x2513: 0x00c0, 0x2514: 0x00c0, 0x2515: 0x00c0, 0x2516: 0x00c0, 0x2517: 0x00c0,
	0x2518: 0x00c0, 0x2519: 0x00c0, 0x251a: 0x00c0, 0x251b: 0x00c0, 0x251c: 0x00c0, 0x251d: 0x00c0,
	0x251e: 0x00c0, 0x251f: 0x00c0, 0x2520: 0x00c0, 0x2521: 0x00c0, 0x2522: 0x00c0, 0x2523: 0x00c0,
	0x2524: 0x00c0, 0x2525: 0x00c0, 0x2526: 0x00c0, 0x2527: 0x00c0, 0x2528: 0x00c0, 0x2529: 0x00c0,
	0x252a: 0x00c0, 0x252b: 0x00c0, 0x252c: 0x00c0, 0x252d: 0x00c0, 0x252e: 0x00c0, 0x252f: 0x00c0,
	0x2530: 0x00c0, 0x2531: 0x00c0, 0x2532: 0x00c0, 0x2533: 0x00c0, 0x2534: 0x00c0, 0x2535: 0x00c0,
	0x2536: 0x00c0, 0x2537: 0x00c0, 0x2538: 0x00c0, 0x2539: 0x00c0, 0x253a: 0x00c0, 0x253b: 0x00c0,
	0x253c: 0x00c0, 0x253d: 0x00c0, 0x253e: 0x0080, 0x253f: 0x0080,
	// Block 0x95, offset 0x2540
	0x2540: 0x00c0, 0x2541: 0x00c0, 0x2542: 0x00c0, 0x2543: 0x00c0, 0x2544: 0x00c0, 0x2545: 0x00c0,
	0x2546: 0x00c0, 0x2547: 0x00c0, 0x2548: 0x00c0, 0x2549: 0x00c0, 0x254a: 0x00c0, 0x254b: 0x00c0,
	0x254c: 0x00c0, 0x254d: 0x0080, 0x254e: 0x0080, 0x254f: 0x0080, 0x2550: 0x00c0, 0x2551: 0x00c0,
	0x2552: 0x00c0, 0x2553: 0x00c0, 0x2554: 0x00c0, 0x2555: 0x00c0, 0x2556: 0x00c0, 0x2557: 0x00c0,
	0x2558: 0x00c0, 0x2559: 0x00c0, 0x255a: 0x00c0, 0x255b: 0x00c0, 0x255c: 0x00c0, 0x255d: 0x00c0,
	0x255e: 0x00c0, 0x255f: 0x00c0, 0x2560: 0x00c0, 0x2561: 0x00c0, 0x2562: 0x00c0, 0x2563: 0x00c0,
	0x2564: 0x00c0, 0x2565: 0x00c0, 0x2566: 0x00c0, 0x2567: 0x00c0, 0x2568: 0x00c0, 0x2569: 0x00c0,
	0x256a: 0x00c0, 0x256b: 0x00c0,
	// Block 0x96, offset 0x2580
	0x2580: 0x00c0, 0x2581: 0x00c0, 0x2582: 0x00c0, 0x2583: 0x00c0, 0x2584: 0x00c0, 0x2585: 0x00c0,
	0x2586: 0x00c0, 0x2587: 0x00c0, 0x2588: 0x00c0, 0x2589: 0x00c0, 0x258a: 0x00c0, 0x258b: 0x00c0,
	0x258c: 0x00c0, 0x258d: 0x00c0, 0x258e: 0x00c0, 0x258f: 0x00c0, 0x2590: 0x00c0, 0x2591: 0x00c0,
	0x2592: 0x00c0, 0x2593: 0x00c0, 0x2594: 0x00c0, 0x2595: 0x00c0, 0x2596: 0x00c0, 0x2597: 0x00c0,
	0x2598: 0x00c0, 0x2599: 0x00c0, 0x259a: 0x00c0, 0x259b: 0x00c0, 0x259c: 0x00c0, 0x259d: 0x00c0,
	0x259e: 0x00c0, 0x259f: 0x00c0, 0x25a0: 0x00c0, 0x25a1: 0x00c0, 0x25a2: 0x00c0, 0x25a3: 0x00c0,
	0x25a4: 0x00c0, 0x25a5: 0x00c0, 0x25a6: 0x00c0, 0x25a7: 0x00c0, 0x25a8: 0x00c0, 0x25a9: 0x00c0,
	0x25aa: 0x00c0, 0x25ab: 0x00c0, 0x25ac: 0x00c0, 0x25ad: 0x00c0, 0x25ae: 0x00c0, 0x25af: 0x00c3,
	0x25b0: 0x0083, 0x25b1: 0x0083, 0x25b2: 0x0083, 0x25b3: 0x0080, 0x25b4: 0x00c3, 0x25b5: 0x00c3,
	0x25b6: 0x00c3, 0x25b7: 0x00c3, 0x25b8: 0x00c3, 0x25b9: 0x00c3, 0x25ba: 0x00c3, 0x25bb: 0x00c3,
	0x25bc: 0x00c3, 0x25bd: 0x00c3, 0x25be: 0x0080, 0x25bf: 0x00c0,
	// Block 0x97, offset 0x25c0
	0x25c0: 0x00c0, 0x25c1: 0x00c0, 0x25c2: 0x00c0, 0x25c3: 0x00c0, 0x25c4: 0x00c0, 0x25c5: 0x00c0,
	0x25c6: 0x00c0, 0x25c7: 0x00c0, 0x25c8: 0x00c0, 0x25c9: 0x00c0, 0x25ca: 0x00c0, 0x25cb: 0x00c0,
	0x25cc: 0x00c0, 0x25cd: 0x00c0, 0x25ce: 0x00c0, 0x25cf: 0x00c0, 0x25d0: 0x00c0, 0x25d1: 0x00c0,
	0x25d2: 0x00c0, 0x25d3: 0x00c0, 0x25d4: 0x00c0, 0x25d5: 0x00c0, 0x25d6: 0x00c0, 0x25d7: 0x00c0,
	0x25d8: 0x00c0, 0x25d9: 0x00c0, 0x25da: 0x00c0, 0x25db: 0x00c0, 0x25dc: 0x0080, 0x25dd: 0x0080,
	0x25de: 0x00c3, 0x25df: 0x00c3, 0x25e0: 0x00c0, 0x25e1: 0x00c0, 0x25e2: 0x00c0, 0x25e3: 0x00c0,
	0x25e4: 0x00c0, 0x25e5: 0x00c0, 0x25e6: 0x00c0, 0x25e7: 0x00c0, 0x25e8: 0x00c0, 0x25e9: 0x00c0,
	0x25ea: 0x00c0, 0x25eb: 0x00c0, 0x25ec: 0x00c0, 0x25ed: 0x00c0, 0x25ee: 0x00c0, 0x25ef: 0x00c0,
	0x25f0: 0x00c0, 0x25f1: 0x00c0, 0x25f2: 0x00c0, 0x25f3: 0x00c0, 0x25f4: 0x00c0, 0x25f5: 0x00c0,
	0x25f6: 0x00c0, 0x25f7: 0x00c0, 0x25f8: 0x00c0, 0x25f9: 0x00c0, 0x25fa: 0x00c0, 0x25fb: 0x00c0,
	0x25fc: 0x00c0, 0x25fd: 0x00c0, 0x25fe: 0x00c0, 0x25ff: 0x00c0,
	// Block 0x98, offset 0x2600
	0x2600: 0x00c0, 0x2601: 0x00c0, 0x2602: 0x00c0, 0x2603: 0x00c0, 0x2604: 0x00c0, 0x2605: 0x00c0,
	0x2606: 0x00c0, 0x2607: 0x00c0, 0x2608: 0x00c0, 0x2609: 0x00c0, 0x260a: 0x00c0, 0x260b: 0x00c0,
	0x260c: 0x00c0, 0x260d: 0x00c0, 0x260e: 0x00c0, 0x260f: 0x00c0, 0x2610: 0x00c0, 0x2611: 0x00c0,
	0x2612: 0x00c0, 0x2613: 0x00c0, 0x2614: 0x00c0, 0x2615: 0x00c0, 0x2616: 0x00c0, 0x2617: 0x00c0,
	0x2618: 0x00c0, 0x2619: 0x00c0, 0x261a: 0x00c0, 0x261b: 0x00c0, 0x261c: 0x00c0, 0x261d: 0x00c0,
	0x261e: 0x00c0, 0x261f: 0x00c0, 0x2620: 0x00c0, 0x2621: 0x00c0, 0x2622: 0x00c0, 0x2623: 0x00c0,
	0x2624: 0x00c0, 0x2625: 0x00c0, 0x2626: 0x0080, 0x2627: 0x0080, 0x2628: 0x0080, 0x2629: 0x0080,
	0x262a: 0x0080, 0x262b: 0x0080, 0x262c: 0x0080, 0x262d: 0x0080, 0x262e: 0x0080, 0x262f: 0x0080,
	0x2630: 0x00c3, 0x2631: 0x00c3, 0x2632: 0x0080, 0x2633: 0x0080, 0x2634: 0x0080, 0x2635: 0x0080,
	0x2636: 0x0080, 0x2637: 0x0080,
	// Block 0x99, offset 0x2640
	0x2640: 0x0080, 0x2641: 0x0080, 0x2642: 0x0080, 0x2643: 0x0080, 0x2644: 0x0080, 0x2645: 0x0080,
	0x2646: 0x0080, 0x2647: 0x0080, 0x2648: 0x0080, 0x2649: 0x0080, 0x264a: 0x0080, 0x264b: 0x0080,
	0x264c: 0x0080, 0x264d: 0x0080, 0x264e: 0x0080, 0x264f: 0x0080, 0x2650: 0x0080, 0x2651: 0x0080,
	0x2652: 0x0080, 0x2653: 0x0080, 0x2654: 0x0080, 0x2655: 0x0080, 0x2656: 0x0080, 0x2657: 0x00c0,
	0x2658: 0x00c0, 0x2659: 0x00c0, 0x265a: 0x00c0, 0x265b: 0x00c0, 0x265c: 0x00c0, 0x265d: 0x00c0,
	0x265e: 0x00c0, 0x265f: 0x00c0, 0x2660: 0x0080, 0x2661: 0x0080, 0x2662: 0x00c0, 0x2663: 0x00c0,
	0x2664: 0x00c0, 0x2665: 0x00c0, 0x2666: 0x00c0, 0x2667: 0x00c0, 0x2668: 0x00c0, 0x2669: 0x00c0,
	0x266a: 0x00c0, 0x266b: 0x00c0, 0x266c: 0x00c0, 0x266d: 0x00c0, 0x266e: 0x00c0, 0x266f: 0x00c0,
	0x2670: 0x00c0, 0x2671: 0x00c0, 0x2672: 0x00c0, 0x2673: 0x00c0, 0x2674: 0x00c0, 0x2675: 0x00c0,
	0x2676: 0x00c0, 0x2677: 0x00c0, 0x2678: 0x00c0, 0x2679: 0x00c0, 0x267a: 0x00c0, 0x267b: 0x00c0,
	0x267c: 0x00c0, 0x267d: 0x00c0, 0x267e: 0x00c0, 0x267f: 0x00c0,
	// Block 0x9a, offset 0x2680
	0x2680: 0x00c0, 0x2681: 0x00c0, 0x2682: 0x00c0, 0x2683: 0x00c0, 0x2684: 0x00c0, 0x2685: 0x00c0,
	0x2686: 0x00c0, 0x2687: 0x00c0, 0x2688: 0x00c0, 0x2689: 0x00c0, 0x268a: 0x00c0, 0x268b: 0x00c0,
	0x268c: 0x00c0, 0x268d: 0x00c0, 0x268e: 0x00c0, 0x268f: 0x00c0, 0x2690: 0x00c0, 0x2691: 0x00c0,
	0x2692: 0x00c0, 0x2693: 0x00c0, 0x2694: 0x00c0, 0x2695: 0x00c0, 0x2696: 0x00c0, 0x2697: 0x00c0,
	0x2698: 0x00c0, 0x2699: 0x00c0, 0x269a: 0x00c0, 0x269b: 0x00c0, 0x269c: 0x00c0, 0x269d: 0x00c0,
	0x269e: 0x00c0, 0x269f: 0x00c0, 0x26a0: 0x00c0, 0x26a1: 0x00c0, 0x26a2: 0x00c0, 0x26a3: 0x00c0,
	0x26a4: 0x00c0, 0x26a5: 0x00c0, 0x26a6: 0x00c0, 0x26a7: 0x00c0, 0x26a8: 0x00c0, 0x26a9: 0x00c0,
	0x26aa: 0x00c0, 0x26ab: 0x00c0, 0x26ac: 0x00c0, 0x26ad: 0x00c0, 0x26ae: 0x00c0, 0x26af: 0x00c0,
	0x26b0: 0x0080, 0x26b1: 0x00c0, 0x26b2: 0x00c0, 0x26b3: 0x00c0, 0x26b4: 0x00c0, 0x26b5: 0x00c0,
	0x26b6: 0x00c0, 0x26b7: 0x00c0, 0x26b8: 0x00c0, 0x26b9: 0x00c0, 0x26ba: 0x00c0, 0x26bb: 0x00c0,
	0x26bc: 0x00c0, 0x26bd: 0x00c0, 0x26be: 0x00c0, 0x26bf: 0x00c0,
	// Block 0x9b, offset 0x26c0
	0x26c0: 0x00c0, 0x26c1: 0x00c0, 0x26c2: 0x00c0, 0x26c3: 0x00c0, 0x26c4: 0x00c0, 0x26c5: 0x00c0,
	0x26c6: 0x00c0, 0x26c7: 0x00c0, 0x26c8: 0x00c0, 0x26c9: 0x0080, 0x26ca: 0x0080, 0x26cb: 0x00c0,
	0x26cc: 0x00c0, 0x26cd: 0x00c0, 0x26ce: 0x00c0, 0x26cf: 0x00c0, 0x26d0: 0x00c0, 0x26d1: 0x00c0,
	0x26d2: 0x00c0, 0x26d3: 0x00c0, 0x26d4: 0x00c0, 0x26d5: 0x00c0, 0x26d6: 0x00c0, 0x26d7: 0x00c0,
	0x26d8: 0x00c0, 0x26d9: 0x00c0, 0x26da: 0x00c0, 0x26db: 0x00c0, 0x26dc: 0x00c0, 0x26dd: 0x00c0,
	0x26de: 0x00c0, 0x26df: 0x00c0, 0x26e0: 0x00c0, 0x26e1: 0x00c0, 0x26e2: 0x00c0, 0x26e3: 0x00c0,
	0x26e4: 0x00c0, 0x26e5: 0x00c0, 0x26e6: 0x00c0, 0x26e7: 0x00c0, 0x26e8: 0x00c0, 0x26e9: 0x00c0,
	0x26ea: 0x00c0, 0x26eb: 0x00c0, 0x26ec: 0x00c0, 0x26ed: 0x00c0, 0x26ee: 0x00c0, 0x26ef: 0x00c0,
	0x26f0: 0x00c0, 0x26f1: 0x00c0, 0x26f2: 0x00c0, 0x26f3: 0x00c0, 0x26f4: 0x00c0, 0x26f5: 0x00c0,
	0x26f6: 0x00c0, 0x26f7: 0x00c0, 0x26f8: 0x00c0, 0x26f9: 0x00c0, 0x26fa: 0x00c0, 0x26fb: 0x00c0,
	0x26fc: 0x00c0, 0x26fd: 0x00c0, 0x26fe: 0x00c0, 0x26ff: 0x00c0,
	// Block 0x9c, offset 0x2700
	0x2702: 0x00c0, 0x2703: 0x00c0, 0x2704: 0x00c0, 0x2705: 0x00c0,
	0x2706: 0x00c0, 0x2707: 0x00c0, 0x2708: 0x00c0, 0x2709: 0x00c0, 0x270a: 0x00c0,
	0x2735: 0x00c0,
	0x2736: 0x00c0, 0x2737: 0x00c0, 0x2738: 0x0080, 0x2739: 0x0080, 0x273a: 0x00c0, 0x273b: 0x00c0,
	0x273c: 0x00c0, 0x273d: 0x00c0, 0x273e: 0x00c0, 0x273f: 0x00c0,
	// Block 0x9d, offset 0x2740
	0x2740: 0x00c0, 0x2741: 0x00c0, 0x2742: 0x00c3, 0x2743: 0x00c0, 0x2744: 0x00c0, 0x2745: 0x00c0,
	0x2746: 0x00c6, 0x2747: 0x00c0, 0x2748: 0x00c0, 0x2749: 0x00c0, 0x274a: 0x00c0, 0x274b: 0x00c3,
	0x274c: 0x00c0, 0x274d: 0x00c0, 0x274e: 0x00c0, 0x274f: 0x00c0, 0x2750: 0x00c0, 0x2751: 0x00c0,
	0x2752: 0x00c0, 0x2753: 0x00c0, 0x2754: 0x00c0, 0x2755: 0x00c0, 0x2756: 0x00c0, 0x2757: 0x00c0,
	0x2758: 0x00c0, 0x2759: 0x00c0, 0x275a: 0x00c0, 0x275b: 0x00c0, 0x275c: 0x00c0, 0x275d: 0x00c0,
	0x275e: 0x00c0, 0x275f: 0x00c0, 0x2760: 0x00c0, 0x2761: 0x00c0, 0x2762: 0x00c0, 0x2763: 0x00c0,
	0x2764: 0x00c0, 0x2765: 0x00c3, 0x2766: 0x00c3, 0x2767: 0x00c0, 0x2768: 0x0080, 0x2769: 0x0080,
	0x276a: 0x0080, 0x276b: 0x0080, 0x276c: 0x00c6,
	0x2770: 0x0080, 0x2771: 0x0080, 0x2772: 0x0080, 0x2773: 0x0080, 0x2774: 0x0080, 0x2775: 0x0080,
	0x2776: 0x0080, 0x2777: 0x0080, 0x2778: 0x0080, 0x2779: 0x0080,
	// Block 0x9e, offset 0x2780
	0x2780: 0x00c2, 0x2781: 0x00c2, 0x2782: 0x00c2, 0x2783: 0x00c2, 0x2784: 0x00c2, 0x2785: 0x00c2,
	0x2786: 0x00c2, 0x2787: 0x00c2, 0x2788: 0x00c2, 0x2789: 0x00c2, 0x278a: 0x00c2, 0x278b: 0x00c2,
	0x278c: 0x00c2, 0x278d: 0x00c2, 0x278e: 0x00c2, 0x278f: 0x00c2, 0x2790: 0x00c2, 0x2791: 0x00c2,
	0x2792: 0x00c2, 0x2793: 0x00c2, 0x2794: 0x00c2, 0x2795: 0x00c2, 0x2796: 0x00c2, 0x2797: 0x00c2,
	0x2798: 0x00c2, 0x2799: 0x00c2, 0x279a: 0x00c2, 0x279b: 0x00c2, 0x279c: 0x00c2, 0x279d: 0x00c2,
	0x279e: 0x00c2, 0x279f: 0x00c2, 0x27a0: 0x00c2, 0x27a1: 0x00c2, 0x27a2: 0x00c2, 0x27a3: 0x00c2,
	0x27a4: 0x00c2, 0x27a5: 0x00c2, 0x27a6: 0x00c2, 0x27a7: 0x00c2, 0x27a8: 0x00c2, 0x27a9: 0x00c2,
	0x27aa: 0x00c2, 0x27ab: 0x00c2, 0x27ac: 0x00c2, 0x27ad: 0x00c2, 0x27ae: 0x00c2, 0x27af: 0x00c2,
	0x27b0: 0x00c2, 0x27b1: 0x00c2, 0x27b2: 0x00c1, 0x27b3: 0x00c0, 0x27b4: 0x0080, 0x27b5: 0x0080,
	0x27b6: 0x0080, 0x27b7: 0x0080,
	// Block 0x9f, offset 0x27c0
	0x27c0: 0x00c0, 0x27c1: 0x00c0, 0x27c2: 0x00c0, 0x27c3: 0x00c0, 0x27c4: 0x00c6, 0x27c5: 0x00c3,
	0x27ce: 0x0080, 0x27cf: 0x0080, 0x27d0: 0x00c0, 0x27d1: 0x00c0,
	0x27d2: 0x00c0, 0x27d3: 0x00c0, 0x27d4: 0x00c0, 0x27d5: 0x00c0, 0x27d6: 0x00c0, 0x27d7: 0x00c0,
	0x27d8: 0x00c0, 0x27d9: 0x00c0,
	0x27e0: 0x00c3, 0x27e1: 0x00c3, 0x27e2: 0x00c3, 0x27e3: 0x00c3,
	0x27e4: 0x00c3, 0x27e5: 0x00c3, 0x27e6: 0x00c3, 0x27e7: 0x00c3, 0x27e8: 0x00c3, 0x27e9: 0x00c3,
	0x27ea: 0x00c3, 0x27eb: 0x00c3, 0x27ec: 0x00c3, 0x27ed: 0x00c3, 0x27ee: 0x00c3, 0x27ef: 0x00c3,
	0x27f0: 0x00c3, 0x27f1: 0x00c3, 0x27f2: 0x00c0, 0x27f3: 0x00c0, 0x27f4: 0x00c0, 0x27f5: 0x00c0,
	0x27f6: 0x00c0, 0x27f7: 0x00c0, 0x27f8: 0x0080, 0x27f9: 0x0080, 0x27fa: 0x0080, 0x27fb: 0x00c0,
	0x27fc: 0x0080, 0x27fd: 0x00c0, 0x27fe: 0x00c0, 0x27ff: 0x00c3,
	// Block 0xa0, offset 0x2800
	0x2800: 0x00c0, 0x2801: 0x00c0, 0x2802: 0x00c0, 0x2803: 0x00c0, 0x2804: 0x00c0, 0x2805: 0x00c0,
	0x2806: 0x00c0, 0x2807: 0x00c0, 0x2808: 0x00c0, 0x2809: 0x00c0, 0x280a: 0x00c0, 0x280b: 0x00c0,
	0x280c: 0x00c0, 0x280d: 0x00c0, 0x280e: 0x00c0, 0x280f: 0x00c0, 0x2810: 0x00c0, 0x2811: 0x00c0,
	0x2812: 0x00c0, 0x2813: 0x00c0, 0x2814: 0x00c0, 0x2815: 0x00c0, 0x2816: 0x00c0, 0x2817: 0x00c0,
	0x2818: 0x00c0, 0x2819: 0x00c0, 0x281a: 0x00c0, 0x281b: 0x00c0, 0x281c: 0x00c0, 0x281d: 0x00c0,
	0x281e: 0x00c0, 0x281f: 0x00c0, 0x2820: 0x00c0, 0x2821: 0x00c0, 0x2822: 0x00c0, 0x2823: 0x00c0,
	0x2824: 0x00c0, 0x2825: 0x00c0, 0x2826: 0x00c3, 0x2827: 0x00c3, 0x2828: 0x00c3, 0x2829: 0x00c3,
	0x282a: 0x00c3, 0x282b: 0x00c3, 0x282c: 0x00c3, 0x282d: 0x00c3, 0x282e: 0x0080, 0x282f: 0x0080,
	0x2830: 0x00c0, 0x2831: 0x00c0, 0x2832: 0x00c0, 0x2833: 0x00c0, 0x2834: 0x00c0, 0x2835: 0x00c0,
	0x2836: 0x00c0, 0x2837: 0x00c0, 0x2838: 0x00c0, 0x2839: 0x00c0, 0x283a: 0x00c0, 0x283b: 0x00c0,
	0x283c: 0x00c0, 0x283d: 0x00c0, 0x283e: 0x00c0, 0x283f: 0x00c0,
	// Block 0xa1, offset 0x2840
	0x2840: 0x00c0, 0x2841: 0x00c0, 0x2842: 0x00c0, 0x2843: 0x00c0, 0x2844: 0x00c0, 0x2845: 0x00c0,
	0x2846: 0x00c0, 0x2847: 0x00c3, 0x2848: 0x00c3, 0x2849: 0x00c3, 0x284a: 0x00c3, 0x284b: 0x00c3,
	0x284c: 0x00c3, 0x284d: 0x00c3, 0x284e: 0x00c3, 0x284f: 0x00c3, 0x2850: 0x00c3, 0x2851: 0x00c3,
	0x2852: 0x00c0, 0x2853: 0x00c5,
	0x285f: 0x0080, 0x2860: 0x0040, 0x2861: 0x0040, 0x2862: 0x0040, 0x2863: 0x0040,
	0x2864: 0x0040, 0x2865: 0x0040, 0x2866: 0x0040, 0x2867: 0x0040, 0x2868: 0x0040, 0x2869: 0x0040,
	0x286a: 0x0040, 0x286b: 0x0040, 0x286c: 0x0040, 0x286d: 0x0040, 0x286e: 0x0040, 0x286f: 0x0040,
	0x2870: 0x0040, 0x2871: 0x0040, 0x2872: 0x0040, 0x2873: 0x0040, 0x2874: 0x0040, 0x2875: 0x0040,
	0x2876: 0x0040, 0x2877: 0x0040, 0x2878: 0x0040, 0x2879: 0x0040, 0x287a: 0x0040, 0x287b: 0x0040,
	0x287c: 0x0040,
	// Block 0xa2, offset 0x2880
	0x2880: 0x00c3, 0x2881: 0x00c3, 0x2882: 0x00c3, 0x2883: 0x00c0, 0x2884: 0x00c0, 0x2885: 0x00c0,
	0x2886: 0x00c0, 0x2887: 0x00c0, 0x2888: 0x00c0, 0x2889: 0x00c0, 0x288a: 0x00c0, 0x288b: 0x00c0,
	0x288c: 0x00c0, 0x288d: 0x00c0, 0x288e: 0x00c0, 0x288f: 0x00c0, 0x2890: 0x00c0, 0x2891: 0x00c0,
	0x2892: 0x00c0, 0x2893: 0x00c0, 0x2894: 0x00c0, 0x2895: 0x00c0, 0x2896: 0x00c0, 0x2897: 0x00c0,
	0x2898: 0x00c0, 0x2899: 0x00c0, 0x289a: 0x00c0, 0x289b: 0x00c0, 0x289c: 0x00c0, 0x289d: 0x00c0,
	0x289e: 0x00c0, 0x289f: 0x00c0, 0x28a0: 0x00c0, 0x28a1: 0x00c0, 0x28a2: 0x00c0, 0x28a3: 0x00c0,
	0x28a4: 0x00c0, 0x28a5: 0x00c0, 0x28a6: 0x00c0, 0x28a7: 0x00c0, 0x28a8: 0x00c0, 0x28a9: 0x00c0,
	0x28aa: 0x00c0, 0x28ab: 0x00c0, 0x28ac: 0x00c0, 0x28ad: 0x00c0, 0x28ae: 0x00c0, 0x28af: 0x00c0,
	0x28b0: 0x00c0, 0x28b1: 0x00c0, 0x28b2: 0x00c0, 0x28b3: 0x00c3, 0x28b4: 0x00c0, 0x28b5: 0x00c0,
	0x28b6: 0x00c3, 0x28b7: 0x00c3, 0x28b8: 0x00c3, 0x28b9: 0x00c3, 0x28ba: 0x00c0, 0x28bb: 0x00c0,
	0x28bc: 0x00c3, 0x28bd: 0x00c3, 0x28be: 0x00c0, 0x28bf: 0x00c0,
	// Block 0xa3, offset 0x28c0
	0x28c0: 0x00c5, 0x28c1: 0x0080, 0x28c2: 0x0080, 0x28c3: 0x0080, 0x28c4: 0x0080, 0x28c5: 0x0080,
	0x28c6: 0x0080, 0x28c7: 0x0080, 0x28c8: 0x0080, 0x28c9: 0x0080, 0x28ca: 0x0080, 0x28cb: 0x0080,
	0x28cc: 0x0080, 0x28cd: 0x0080, 0x28cf: 0x00c0, 0x28d0: 0x00c0, 0x28d1: 0x00c0,
	0x28d2: 0x00c0, 0x28d3: 0x00c0, 0x28d4: 0x00c0, 0x28d5: 0x00c0, 0x28d6: 0x00c0, 0x28d7: 0x00c0,
	0x28d8: 0x00c0, 0x28d9: 0x00c0,
	0x28de: 0x0080, 0x28df: 0x0080, 0x28e0: 0x00c0, 0x28e1: 0x00c0, 0x28e2: 0x00c0, 0x28e3: 0x00c0,
	0x28e4: 0x00c0, 0x28e5: 0x00c3, 0x28e6: 0x00c0, 0x28e7: 0x00c0, 0x28e8: 0x00c0, 0x28e9: 0x00c0,
	0x28ea: 0x00c0, 0x28eb: 0x00c0, 0x28ec: 0x00c0, 0x28ed: 0x00c0, 0x28ee: 0x00c0, 0x28ef: 0x00c0,
	0x28f0: 0x00c0, 0x28f1: 0x00c0, 0x28f2: 0x00c0, 0x28f3: 0x00c0, 0x28f4: 0x00c0, 0x28f5: 0x00c0,
	0x28f6: 0x00c0, 0x28f7: 0x00c0, 0x28f8: 0x00c0, 0x28f9: 0x00c0, 0x28fa: 0x00c0, 0x28fb: 0x00c0,
	0x28fc: 0x00c0, 0x28fd: 0x00c0, 0x28fe: 0x00c0,
	// Block 0xa4, offset 0x2900
	0x2900: 0x00c0, 0x2901: 0x00c0, 0x2902: 0x00c0, 0x2903: 0x00c0, 0x2904: 0x00c0, 0x2905: 0x00c0,
	0x2906: 0x00c0, 0x2907: 0x00c0, 0x2908: 0x00c0, 0x2909: 0x00c0, 0x290a: 0x00c0, 0x290b: 0x00c0,
	0x290c: 0x00c0, 0x290d: 0x00c0, 0x290e: 0x00c0, 0x290f: 0x00c0, 0x2910: 0x00c0, 0x2911: 0x00c0,
	0x2912: 0x00c0, 0x2913: 0x00c0, 0x2914: 0x00c0, 0x2915: 0x00c0, 0x2916: 0x00c0, 0x2917: 0x00c0,
	0x2918: 0x00c0, 0x2919: 0x00c0, 0x291a: 0x00c0, 0x291b: 0x00c0, 0x291c: 0x00c0, 0x291d: 0x00c0,
	0x291e: 0x00c0, 0x291f: 0x00c0, 0x2920: 0x00c0, 0x2921: 0x00c0, 0x2922: 0x00c0, 0x2923: 0x00c0,
	0x2924: 0x00c0, 0x2925: 0x00c0, 0x2926: 0x00c0, 0x2927: 0x00c0, 0x2928: 0x00c0, 0x2929: 0x00c3,
	0x292a: 0x00c3, 0x292b: 0x00c3, 0x292c: 0x00c3, 0x292d: 0x00c3, 0x292e: 0x00c3, 0x292f: 0x00c0,
	0x2930: 0x00c0, 0x2931: 0x00c3, 0x2932: 0x00c3, 0x2933: 0x00c0, 0x2934: 0x00c0, 0x2935: 0x00c3,
	0x2936: 0x00c3,
	// Block 0xa5, offset 0x2940
	0x2940: 0x00c0, 0x2941: 0x00c0, 0x2942: 0x00c0, 0x2943: 0x00c3, 0x2944: 0x00c0, 0x2945: 0x00c0,
	0x2946: 0x00c0, 0x2947: 0x00c0, 0x2948: 0x00c0, 0x2949: 0x00c0, 0x294a: 0x00c0, 0x294b: 0x00c0,
	0x294c: 0x00c3, 0x294d: 0x00c0, 0x2950: 0x00c0, 0x2951: 0x00c0,
	0x2952: 0x00c0, 0x2953: 0x00c0, 0x2954: 0x00c0, 0x2955: 0x00c0, 0x2956: 0x00c0, 0x2957: 0x00c0,
	0x2958: 0x00c0, 0x2959: 0x00c0, 0x295c: 0x0080, 0x295d: 0x0080,
	0x295e: 0x0080, 0x295f: 0x0080, 0x2960: 0x00c0, 0x2961: 0x00c0, 0x2962: 0x00c0, 0x2963: 0x00c0,
	0x2964: 0x00c0, 0x2965: 0x00c0, 0x2966: 0x00c0, 0x2967: 0x00c0, 0x2968: 0x00c0, 0x2969: 0x00c0,
	0x296a: 0x00c0, 0x296b: 0x00c0, 0x296c: 0x00c0, 0x296d: 0x00c0, 0x296e: 0x00c0, 0x296f: 0x00c0,
	0x2970: 0x00c0, 0x2971: 0x00c0, 0x2972: 0x00c0, 0x2973: 0x00c0, 0x2974: 0x00c0, 0x2975: 0x00c0,
	0x2976: 0x00c0, 0x2977: 0x0080, 0x2978: 0x0080, 0x2979: 0x0080, 0x297a: 0x00c0, 0x297b: 0x00c0,
	0x297c: 0x00c3, 0x297d: 0x00c0, 0x297e: 0x00c0, 0x297f: 0x00c0,
	// Block 0xa6, offset 0x2980
	0x2980: 0x00c0, 0x2981: 0x00c0, 0x2982: 0x00c0, 0x2983: 0x00c0, 0x2984: 0x00c0, 0x2985: 0x00c0,
	0x2986: 0x00c0, 0x2987: 0x00c0, 0x2988: 0x00c0, 0x2989: 0x00c0, 0x298a: 0x00c0, 0x298b: 0x00c0,
	0x298c: 0x00c0, 0x298d: 0x00c0, 0x298e: 0x00c0, 0x298f: 0x00c0, 0x2990: 0x00c0, 0x2991: 0x00c0,
	0x2992: 0x00c0, 0x2993: 0x00c0, 0x2994: 0x00c0, 0x2995: 0x00c0, 0x2996: 0x00c0, 0x2997: 0x00c0,
	0x2998: 0x00c0, 0x2999: 0x00c0, 0x299a: 0x00c0, 0x299b: 0x00c0, 0x299c: 0x00c0, 0x299d: 0x00c0,
	0x299e: 0x00c0, 0x299f: 0x00c0, 0x29a0: 0x00c0, 0x29a1: 0x00c0, 0x29a2: 0x00c0, 0x29a3: 0x00c0,
	0x29a4: 0x00c0, 0x29a5: 0x00c0, 0x29a6: 0x00c0, 0x29a7: 0x00c0, 0x29a8: 0x00c0, 0x29a9: 0x00c0,
	0x29aa: 0x00c0, 0x29ab: 0x00c0, 0x29ac: 0x00c0, 0x29ad: 0x00c0, 0x29ae: 0x00c0, 0x29af: 0x00c0,
	0x29b0: 0x00c3, 0x29b1: 0x00c0, 0x29b2: 0x00c3, 0x29b3: 0x00c3, 0x29b4: 0x00c3, 0x29b5: 0x00c0,
	0x29b6: 0x00c0, 0x29b7: 0x00c3, 0x29b8: 0x00c3, 0x29b9: 0x00c0, 0x29ba: 0x00c0, 0x29bb: 0x00c0,
	0x29bc: 0x00c0, 0x29bd: 0x00c0, 0x29be: 0x00c3, 0x29bf: 0x00c3,
	// Block 0xa7, offset 0x29c0
	0x29c0: 0x00c0, 0x29c1: 0x00c3, 0x29c2: 0x00c0,
	0x29db: 0x00c0, 0x29dc: 0x00c0, 0x29dd: 0x00c0,
	0x29de: 0x0080, 0x29df: 0x0080, 0x29e0: 0x00c0, 0x29e1: 0x00c0, 0x29e2: 0x00c0, 0x29e3: 0x00c0,
	0x29e4: 0x00c0, 0x29e5: 0x00c0, 0x29e6: 0x00c0, 0x29e7: 0x00c0, 0x29e8: 0x00c0, 0x29e9: 0x00c0,
	0x29ea: 0x00c0, 0x29eb: 0x00c0, 0x29ec: 0x00c3, 0x29ed: 0x00c3, 0x29ee: 0x00c0, 0x29ef: 0x00c0,
	0x29f0: 0x0080, 0x29f1: 0x0080, 0x29f2: 0x00c0, 0x29f3: 0x00c0, 0x29f4: 0x00c0, 0x29f5: 0x00c0,
	0x29f6: 0x00c6,
	// Block 0xa8, offset 0x2a00
	0x2a01: 0x00c0, 0x2a02: 0x00c0, 0x2a03: 0x00c0, 0x2a04: 0x00c0, 0x2a05: 0x00c0,
	0x2a06: 0x00c0, 0x2a09: 0x00c0, 0x2a0a: 0x00c0, 0x2a0b: 0x00c0,
	0x2a0c: 0x00c0, 0x2a0d: 0x00c0, 0x2a0e: 0x00c0, 0x2a11: 0x00c0,
	0x2a12: 0x00c0, 0x2a13: 0x00c0, 0x2a14: 0x00c0, 0x2a15: 0x00c0, 0x2a16: 0x00c0,
	0x2a20: 0x00c0, 0x2a21: 0x00c0, 0x2a22: 0x00c0, 0x2a23: 0x00c0,
	0x2a24: 0x00c0, 0x2a25: 0x00c0, 0x2a26: 0x00c0, 0x2a28: 0x00c0, 0x2a29: 0x00c0,
	0x2a2a: 0x00c0, 0x2a2b: 0x00c0, 0x2a2c: 0x00c0, 0x2a2d: 0x00c0, 0x2a2e: 0x00c0,
	0x2a30: 0x00c0, 0x2a31: 0x00c0, 0x2a32: 0x00c0, 0x2a33: 0x00c0, 0x2a34: 0x00c0, 0x2a35: 0x00c0,
	0x2a36: 0x00c0, 0x2a37: 0x00c0, 0x2a38: 0x00c0, 0x2a39: 0x00c0, 0x2a3a: 0x00c0, 0x2a3b: 0x00c0,
	0x2a3c: 0x00c0, 0x2a3d: 0x00c0, 0x2a3e: 0x00c0, 0x2a3f: 0x00c0,
	// Block 0xa9, offset 0x2a40
	0x2a40: 0x00c0, 0x2a41: 0x00c0, 0x2a42: 0x00c0, 0x2a43: 0x00c0, 0x2a44: 0x00c0, 0x2a45: 0x00c0,
	0x2a46: 0x00c0, 0x2a47: 0x00c0, 0x2a48: 0x00c0, 0x2a49: 0x00c0, 0x2a4a: 0x00c0, 0x2a4b: 0x00c0,
	0x2a4c: 0x00c0, 0x2a4d: 0x00c0, 0x2a4e: 0x00c0, 0x2a4f: 0x00c0, 0x2a50: 0x00c0, 0x2a51: 0x00c0,
	0x2a52: 0x00c0, 0x2a53: 0x00c0, 0x2a54: 0x00c0, 0x2a55: 0x00c0, 0x2a56: 0x00c0, 0x2a57: 0x00c0,
	0x2a58: 0x00c0, 0x2a59: 0x00c0, 0x2a5a: 0x00c0, 0x2a5b: 0x0080, 0x2a5c: 0x0080, 0x2a5d: 0x0080,
	0x2a5e: 0x0080, 0x2a5f: 0x0080, 0x2a60: 0x00c0, 0x2a61: 0x00c0, 0x2a62: 0x00c0, 0x2a63: 0x00c0,
	0x2a64: 0x00c0, 0x2a65: 0x00c8, 0x2a66: 0x00c0, 0x2a67: 0x00c0, 0x2a68: 0x00c0, 0x2a69: 0x0080,
	0x2a6a: 0x0080, 0x2a6b: 0x0080,
	0x2a70: 0x00c0, 0x2a71: 0x00c0, 0x2a72: 0x00c0, 0x2a73: 0x00c0, 0x2a74: 0x00c0, 0x2a75: 0x00c0,
	0x2a76: 0x00c0, 0x2a77: 0x00c0, 0x2a78: 0x00c0, 0x2a79: 0x00c0, 0x2a7a: 0x00c0, 0x2a7b: 0x00c0,
	0x2a7c: 0x00c0, 0x2a7d: 0x00c0, 0x2a7e: 0x00c0, 0x2a7f: 0x00c0,
	// Block 0xaa, offset 0x2a80
	0x2a80: 0x00c0, 0x2a81: 0x00c0, 0x2a82: 0x00c0, 0x2a83: 0x00c0, 0x2a84: 0x00c0, 0x2a85: 0x00c0,
	0x2a86: 0x00c0, 0x2a87: 0x00c0, 0x2a88: 0x00c0, 0x2a89: 0x00c0, 0x2a8a: 0x00c0, 0x2a8b: 0x00c0,
	0x2a8c: 0x00c0, 0x2a8d: 0x00c0, 0x2a8e: 0x00c0, 0x2a8f: 0x00c0, 0x2a90: 0x00c0, 0x2a91: 0x00c0,
	0x2a92: 0x00c0, 0x2a93: 0x00c0, 0x2a94: 0x00c0, 0x2a95: 0x00c0, 0x2a96: 0x00c0, 0x2a97: 0x00c0,
	0x2a98: 0x00c0, 0x2a99: 0x00c0, 0x2a9a: 0x00c0, 0x2a9b: 0x00c0, 0x2a9c: 0x00c0, 0x2a9d: 0x00c0,
	0x2a9e: 0x00c0, 0x2a9f: 0x00c0, 0x2aa0: 0x00c0, 0x2aa1: 0x00c0, 0x2aa2: 0x00c0, 0x2aa3: 0x00c0,
	0x2aa4: 0x00c0, 0x2aa5: 0x00c3, 0x2aa6: 0x00c0, 0x2aa7: 0x00c0, 0x2aa8: 0x00c3, 0x2aa9: 0x00c0,
	0x2aaa: 0x00c0, 0x2aab: 0x0080, 0x2aac: 0x00c0, 0x2aad: 0x00c6,
	0x2ab0: 0x00c0, 0x2ab1: 0x00c0, 0x2ab2: 0x00c0, 0x2ab3: 0x00c0, 0x2ab4: 0x00c0, 0x2ab5: 0x00c0,
	0x2ab6: 0x00c0, 0x2ab7: 0x00c0, 0x2ab8: 0x00c0, 0x2ab9: 0x00c0,
	// Block 0xab, offset 0x2ac0
	0x2ac0: 0x00c0, 0x2ac1: 0x00c0, 0x2ac2: 0x00c0, 0x2ac3: 0x00c0, 0x2ac4: 0x00c0, 0x2ac5: 0x00c0,
	0x2ac6: 0x00c0, 0x2ac7: 0x00c0, 0x2ac8: 0x00c0, 0x2ac9: 0x00c0, 0x2aca: 0x00c0, 0x2acb: 0x00c0,
	0x2acc: 0x00c0, 0x2acd: 0x00c0, 0x2ace: 0x00c0, 0x2acf: 0x00c0, 0x2ad0: 0x00c0, 0x2ad1: 0x00c0,
	0x2ad2: 0x00c0, 0x2ad3: 0x00c0, 0x2ad4: 0x00c0, 0x2ad5: 0x00c0, 0x2ad6: 0x00c0, 0x2ad7: 0x00c0,
	0x2ad8: 0x00c0, 0x2ad9: 0x00c0, 0x2ada: 0x00c0, 0x2adb: 0x00c0, 0x2adc: 0x00c0, 0x2add: 0x00c0,
	0x2ade: 0x00c0, 0x2adf: 0x00c0, 0x2ae0: 0x00c0, 0x2ae1: 0x00c0, 0x2ae2: 0x00c0, 0x2ae3: 0x00c0,
	0x2af0: 0x0040, 0x2af1: 0x0040, 0x2af2: 0x0040, 0x2af3: 0x0040, 0x2af4: 0x0040, 0x2af5: 0x0040,
	0x2af6: 0x0040, 0x2af7: 0x0040, 0x2af8: 0x0040, 0x2af9: 0x0040, 0x2afa: 0x0040, 0x2afb: 0x0040,
	0x2afc: 0x0040, 0x2afd: 0x0040, 0x2afe: 0x0040, 0x2aff: 0x0040,
	// Block 0xac, offset 0x2b00
	0x2b00: 0x0040, 0x2b01: 0x0040, 0x2b02: 0x0040, 0x2b03: 0x0040, 0x2b04: 0x0040, 0x2b05: 0x0040,
	0x2b06: 0x0040, 0x2b0b: 0x0040,
	0x2b0c: 0x0040, 0x2b0d: 0x0040, 0x2b0e: 0x0040, 0x2b0f: 0x0040, 0x2b10: 0x0040, 0x2b11: 0x0040,
	0x2b12: 0x0040, 0x2b13: 0x0040, 0x2b14: 0x0040, 0x2b15: 0x0040, 0x2b16: 0x0040, 0x2b17: 0x0040,
	0x2b18: 0x0040, 0x2b19: 0x0040, 0x2b1a: 0x0040, 0x2b1b: 0x0040, 0x2b1c: 0x0040, 0x2b1d: 0x0040,
	0x2b1e: 0x0040, 0x2b1f: 0x0040, 0x2b20: 0x0040, 0x2b21: 0x0040, 0x2b22: 0x0040, 0x2b23: 0x0040,
	0x2b24: 0x0040, 0x2b25: 0x0040, 0x2b26: 0x0040, 0x2b27: 0x0040, 0x2b28: 0x0040, 0x2b29: 0x0040,
	0x2b2a: 0x0040, 0x2b2b: 0x0040, 0x2b2c: 0x0040, 0x2b2d: 0x0040, 0x2b2e: 0x0040, 0x2b2f: 0x0040,
	0x2b30: 0x0040, 0x2b31: 0x0040, 0x2b32: 0x0040, 0x2b33: 0x0040, 0x2b34: 0x0040, 0x2b35: 0x0040,
	0x2b36: 0x0040, 0x2b37: 0x0040, 0x2b38: 0x0040, 0x2b39: 0x0040, 0x2b3a: 0x0040, 0x2b3b: 0x0040,
	// Block 0xad, offset 0x2b40
	0x2b40: 0x008c, 0x2b41: 0x008c, 0x2b42: 0x008c, 0x2b43: 0x008c, 0x2b44: 0x008c, 0x2b45: 0x008c,
	0x2b46: 0x008c, 0x2b47: 0x008c, 0x2b48: 0x008c, 0x2b49: 0x008c, 0x2b4a: 0x008c, 0x2b4b: 0x008c,
	0x2b4c: 0x008c, 0x2b4d: 0x008c, 0x2b4e: 0x00cc, 0x2b4f: 0x00cc, 0x2b50: 0x008c, 0x2b51: 0x00cc,
	0x2b52: 0x008c, 0x2b53: 0x00cc, 0x2b54: 0x00cc, 0x2b55: 0x008c, 0x2b56: 0x008c, 0x2b57: 0x008c,
	0x2b58: 0x008c, 0x2b59: 0x008c, 0x2b5a: 0x008c, 0x2b5b: 0x008c, 0x2b5c: 0x008c, 0x2b5d: 0x008c,
	0x2b5e: 0x008c, 0x2b5f: 0x00cc, 0x2b60: 0x008c, 0x2b61: 0x00cc, 0x2b62: 0x008c, 0x2b63: 0x00cc,
	0x2b64: 0x00cc, 0x2b65: 0x008c, 0x2b66: 0x008c, 0x2b67: 0x00cc, 0x2b68: 0x00cc, 0x2b69: 0x00cc,
	0x2b6a: 0x008c, 0x2b6b: 0x008c, 0x2b6c: 0x008c, 0x2b6d: 0x008c, 0x2b6e: 0x008c, 0x2b6f: 0x008c,
	0x2b70: 0x008c, 0x2b71: 0x008c, 0x2b72: 0x008c, 0x2b73: 0x008c, 0x2b74: 0x008c, 0x2b75: 0x008c,
	0x2b76: 0x008c, 0x2b77: 0x008c, 0x2b78: 0x008c, 0x2b79: 0x008c, 0x2b7a: 0x008c, 0x2b7b: 0x008c,
	0x2b7c: 0x008c, 0x2b7d: 0x008c, 0x2b7e: 0x008c, 0x2b7f: 0x008c,
	// Block 0xae, offset 0x2b80
	0x2b80: 0x008c, 0x2b81: 0x008c, 0x2b82: 0x008c, 0x2b83: 0x008c, 0x2b84: 0x008c, 0x2b85: 0x008c,
	0x2b86: 0x008c, 0x2b87: 0x008c, 0x2b88: 0x008c, 0x2b89: 0x008c, 0x2b8a: 0x008c, 0x2b8b: 0x008c,
	0x2b8c: 0x008c, 0x2b8d: 0x008c, 0x2b8e: 0x008c, 0x2b8f: 0x008c, 0x2b90: 0x008c, 0x2b91: 0x008c,
	0x2b92: 0x008c, 0x2b93: 0x008c, 0x2b94: 0x008c, 0x2b95: 0x008c, 0x2b96: 0x008c, 0x2b97: 0x008c,
	0x2b98: 0x008c, 0x2b99: 0x008c, 0x2b9a: 0x008c, 0x2b9b: 0x008c, 0x2b9c: 0x008c, 0x2b9d: 0x008c,
	0x2b9e: 0x008c, 0x2b9f: 0x008c, 0x2ba0: 0x008c, 0x2ba1: 0x008c, 0x2ba2: 0x008c, 0x2ba3: 0x008c,
	0x2ba4: 0x008c, 0x2ba5: 0x008c, 0x2ba6: 0x008c, 0x2ba7: 0x008c, 0x2ba8: 0x008c, 0x2ba9: 0x008c,
	0x2baa: 0x008c, 0x2bab: 0x008c, 0x2bac: 0x008c, 0x2bad: 0x008c,
	0x2bb0: 0x008c, 0x2bb1: 0x008c, 0x2bb2: 0x008c, 0x2bb3: 0x008c, 0x2bb4: 0x008c, 0x2bb5: 0x008c,
	0x2bb6: 0x008c, 0x2bb7: 0x008c, 0x2bb8: 0x008c, 0x2bb9: 0x008c, 0x2bba: 0x008c, 0x2bbb: 0x008c,
	0x2bbc: 0x008c, 0x2bbd: 0x008c, 0x2bbe: 0x008c, 0x2bbf: 0x008c,
	// Block 0xaf, offset 0x2bc0
	0x2bc0: 0x008c, 0x2bc1: 0x008c, 0x2bc2: 0x008c, 0x2bc3: 0x008c, 0x2bc4: 0x008c, 0x2bc5: 0x008c,
	0x2bc6: 0x008c, 0x2bc7: 0x008c, 0x2bc8: 0x008c, 0x2bc9: 0x008c, 0x2bca: 0x008c, 0x2bcb: 0x008c,
	0x2bcc: 0x008c, 0x2bcd: 0x008c, 0x2bce: 0x008c, 0x2bcf: 0x008c, 0x2bd0: 0x008c, 0x2bd1: 0x008c,
	0x2bd2: 0x008c, 0x2bd3: 0x008c, 0x2bd4: 0x008c, 0x2bd5: 0x008c, 0x2bd6: 0x008c, 0x2bd7: 0x008c,
	0x2bd8: 0x008c, 0x2bd9: 0x008c,
	// Block 0xb0, offset 0x2c00
	0x2c00: 0x0080, 0x2c01: 0x0080, 0x2c02: 0x0080, 0x2c03: 0x0080, 0x2c04: 0x0080, 0x2c05: 0x0080,
	0x2c06: 0x0080,
	0x2c13: 0x0080, 0x2c14: 0x0080, 0x2c15: 0x0080, 0x2c16: 0x0080, 0x2c17: 0x0080,
	0x2c1d: 0x008a,
	0x2c1e: 0x00cb, 0x2c1f: 0x008a, 0x2c20: 0x008a, 0x2c21: 0x008a, 0x2c22: 0x008a, 0x2c23: 0x008a,
	0x2c24: 0x008a, 0x2c25: 0x008a, 0x2c26: 0x008a, 0x2c27: 0x008a, 0x2c28: 0x008a, 0x2c29: 0x008a,
	0x2c2a: 0x008a, 0x2c2b: 0x008a, 0x2c2c: 0x008a, 0x2c2d: 0x008a, 0x2c2e: 0x008a, 0x2c2f: 0x008a,
	0x2c30: 0x008a, 0x2c31: 0x008a, 0x2c32: 0x008a, 0x2c33: 0x008a, 0x2c34: 0x008a, 0x2c35: 0x008a,
	0x2c36: 0x008a, 0x2c38: 0x008a, 0x2c39: 0x008a, 0x2c3a: 0x008a, 0x2c3b: 0x008a,
	0x2c3c: 0x008a, 0x2c3e: 0x008a,
	// Block 0xb1, offset 0x2c40
	0x2c40: 0x008a, 0x2c41: 0x008a, 0x2c43: 0x008a, 0x2c44: 0x008a,
	0x2c46: 0x008a, 0x2c47: 0x008a, 0x2c48: 0x008a, 0x2c49: 0x008a, 0x2c4a: 0x008a, 0x2c4b: 0x008a,
	0x2c4c: 0x008a, 0x2c4d: 0x008a, 0x2c4e: 0x008a, 0x2c4f: 0x008a, 0x2c50: 0x0080, 0x2c51: 0x0080,
	0x2c52: 0x0080, 0x2c53: 0x0080, 0x2c54: 0x0080, 0x2c55: 0x0080, 0x2c56: 0x0080, 0x2c57: 0x0080,
	0x2c58: 0x0080, 0x2c59: 0x0080, 0x2c5a: 0x0080, 0x2c5b: 0x0080, 0x2c5c: 0x0080, 0x2c5d: 0x0080,
	0x2c5e: 0x0080, 0x2c5f: 0x0080, 0x2c60: 0x0080, 0x2c61: 0x0080, 0x2c62: 0x0080, 0x2c63: 0x0080,
	0x2c64: 0x0080, 0x2c65: 0x0080, 0x2c66: 0x0080, 0x2c67: 0x0080, 0x2c68: 0x0080, 0x2c69: 0x0080,
	0x2c6a: 0x0080, 0x2c6b: 0x0080, 0x2c6c: 0x0080, 0x2c6d: 0x0080, 0x2c6e: 0x0080, 0x2c6f: 0x0080,
	0x2c70: 0x0080, 0x2c71: 0x0080, 0x2c72: 0x0080, 0x2c73: 0x0080, 0x2c74: 0x0080, 0x2c75: 0x0080,
	0x2c76: 0x0080, 0x2c77: 0x0080, 0x2c78: 0x0080, 0x2c79: 0x0080, 0x2c7a: 0x0080, 0x2c7b: 0x0080,
	0x2c7c: 0x0080, 0x2c7d: 0x0080, 0x2c7e: 0x0080, 0x2c7f: 0x0080,
	// Block 0xb2, offset 0x2c80
	0x2c80: 0x0080, 0x2c81: 0x0080,
	0x2c93: 0x0080, 0x2c94: 0x0080, 0x2c95: 0x0080, 0x2c96: 0x0080, 0x2c97: 0x0080,
	0x2c98: 0x0080, 0x2c99: 0x0080, 0x2c9a: 0x0080, 0x2c9b: 0x0080, 0x2c9c: 0x0080, 0x2c9d: 0x0080,
	0x2c9e: 0x0080, 0x2c9f: 0x0080, 0x2ca0: 0x0080, 0x2ca1: 0x0080, 0x2ca2: 0x0080, 0x2ca3: 0x0080,
	0x2ca4: 0x0080, 0x2ca5: 0x0080, 0x2ca6: 0x0080, 0x2ca7: 0x0080, 0x2ca8: 0x0080, 0x2ca9: 0x0080,
	0x2caa: 0x0080, 0x2cab: 0x0080, 0x2cac: 0x0080, 0x2cad: 0x0080, 0x2cae: 0x0080, 0x2caf: 0x0080,
	0x2cb0: 0x0080, 0x2cb1: 0x0080, 0x2cb2: 0x0080, 0x2cb3: 0x0080, 0x2cb4: 0x0080, 0x2cb5: 0x0080,
	0x2cb6: 0x0080, 0x2cb7: 0x0080, 0x2cb8: 0x0080, 0x2cb9: 0x0080, 0x2cba: 0x0080, 0x2cbb: 0x0080,
	0x2cbc: 0x0080, 0x2cbd: 0x0080, 0x2cbe: 0x0080, 0x2cbf: 0x0080,
	// Block 0xb3, offset 0x2cc0
	0x2cd0: 0x0080, 0x2cd1: 0x0080,
	0x2cd2: 0x0080, 0x2cd3: 0x0080, 0x2cd4: 0x0080, 0x2cd5: 0x0080, 0x2cd6: 0x0080, 0x2cd7: 0x0080,
	0x2cd8: 0x0080, 0x2cd9: 0x0080, 0x2cda: 0x0080, 0x2cdb: 0x0080, 0x2cdc: 0x0080, 0x2cdd: 0x0080,
	0x2cde: 0x0080, 0x2cdf: 0x0080, 0x2ce0: 0x0080, 0x2ce1: 0x0080, 0x2ce2: 0x0080, 0x2ce3: 0x0080,
	0x2ce4: 0x0080, 0x2ce5: 0x0080, 0x2ce6: 0x0080, 0x2ce7: 0x0080, 0x2ce8: 0x0080, 0x2ce9: 0x0080,
	0x2cea: 0x0080, 0x2ceb: 0x0080, 0x2cec: 0x0080, 0x2ced: 0x0080, 0x2cee: 0x0080, 0x2cef: 0x0080,
	0x2cf0: 0x0080, 0x2cf1: 0x0080, 0x2cf2: 0x0080, 0x2cf3: 0x0080, 0x2cf4: 0x0080, 0x2cf5: 0x0080,
	0x2cf6: 0x0080, 0x2cf7: 0x0080, 0x2cf8: 0x0080, 0x2cf9: 0x0080, 0x2cfa: 0x0080, 0x2cfb: 0x0080,
	0x2cfc: 0x0080, 0x2cfd: 0x0080, 0x2cfe: 0x0080, 0x2cff: 0x0080,
	// Block 0xb4, offset 0x2d00
	0x2d00: 0x0080, 0x2d01: 0x0080, 0x2d02: 0x0080, 0x2d03: 0x0080, 0x2d04: 0x0080, 0x2d05: 0x0080,
	0x2d06: 0x0080, 0x2d07: 0x0080, 0x2d08: 0x0080, 0x2d09: 0x0080, 0x2d0a: 0x0080, 0x2d0b: 0x0080,
	0x2d0c: 0x0080, 0x2d0d: 0x0080, 0x2d0e: 0x0080, 0x2d0f: 0x0080,
	0x2d12: 0x0080, 0x2d13: 0x0080, 0x2d14: 0x0080, 0x2d15: 0x0080, 0x2d16: 0x0080, 0x2d17: 0x0080,
	0x2d18: 0x0080, 0x2d19: 0x0080, 0x2d1a: 0x0080, 0x2d1b: 0x0080, 0x2d1c: 0x0080, 0x2d1d: 0x0080,
	0x2d1e: 0x0080, 0x2d1f: 0x0080, 0x2d20: 0x0080, 0x2d21: 0x0080, 0x2d22: 0x0080, 0x2d23: 0x0080,
	0x2d24: 0x0080, 0x2d25: 0x0080, 0x2d26: 0x0080, 0x2d27: 0x0080, 0x2d28: 0x0080, 0x2d29: 0x0080,
	0x2d2a: 0x0080, 0x2d2b: 0x0080, 0x2d2c: 0x0080, 0x2d2d: 0x0080, 0x2d2e: 0x0080, 0x2d2f: 0x0080,
	0x2d30: 0x0080, 0x2d31: 0x0080, 0x2d32: 0x0080, 0x2d33: 0x0080, 0x2d34: 0x0080, 0x2d35: 0x0080,
	0x2d36: 0x0080, 0x2d37: 0x0080, 0x2d38: 0x0080, 0x2d39: 0x0080, 0x2d3a: 0x0080, 0x2d3b: 0x0080,
	0x2d3c: 0x0080, 0x2d3d: 0x0080, 0x2d3e: 0x0080, 0x2d3f: 0x0080,
	// Block 0xb5, offset 0x2d40
	0x2d40: 0x0080, 0x2d41: 0x0080, 0x2d42: 0x0080, 0x2d43: 0x0080, 0x2d44: 0x0080, 0x2d45: 0x0080,
	0x2d46: 0x0080, 0x2d47: 0x0080,
	0x2d70: 0x0080, 0x2d71: 0x0080, 0x2d72: 0x0080, 0x2d73: 0x0080, 0x2d74: 0x0080, 0x2d75: 0x0080,
	0x2d76: 0x0080, 0x2d77: 0x0080, 0x2d78: 0x0080, 0x2d79: 0x0080, 0x2d7a: 0x0080, 0x2d7b: 0x0080,
	0x2d7c: 0x0080, 0x2d7d: 0x0080,
	// Block 0xb6, offset 0x2d80
	0x2d80: 0x0040, 0x2d81: 0x0040, 0x2d82: 0x0040, 0x2d83: 0x0040, 0x2d84: 0x0040, 0x2d85: 0x0040,
	0x2d86: 0x0040, 0x2d87: 0x0040, 0x2d88: 0x0040, 0x2d89: 0x0040, 0x2d8a: 0x0040, 0x2d8b: 0x0040,
	0x2d8c: 0x0040, 0x2d8d: 0x0040, 0x2d8e: 0x0040, 0x2d8f: 0x0040, 0x2d90: 0x0080, 0x2d91: 0x0080,
	0x2d92: 0x0080, 0x2d93: 0x0080, 0x2d94: 0x0080, 0x2d95: 0x0080, 0x2d96: 0x0080, 0x2d97: 0x0080,
	0x2d98: 0x0080, 0x2d99: 0x0080,
	0x2da0: 0x00c3, 0x2da1: 0x00c3, 0x2da2: 0x00c3, 0x2da3: 0x00c3,
	0x2da4: 0x00c3, 0x2da5: 0x00c3, 0x2da6: 0x00c3, 0x2da7: 0x00c3, 0x2da8: 0x00c3, 0x2da9: 0x00c3,
	0x2daa: 0x00c3, 0x2dab: 0x00c3, 0x2dac: 0x00c3, 0x2dad: 0x00c3, 0x2dae: 0x00c3, 0x2daf: 0x00c3,
	0x2db0: 0x0080, 0x2db1: 0x0080, 0x2db2: 0x0080, 0x2db3: 0x0080, 0x2db4: 0x0080, 0x2db5: 0x0080,
	0x2db6: 0x0080, 0x2db7: 0x0080, 0x2db8: 0x0080, 0x2db9: 0x0080, 0x2dba: 0x0080, 0x2dbb: 0x0080,
	0x2dbc: 0x0080, 0x2dbd: 0x0080, 0x2dbe: 0x0080, 0x2dbf: 0x0080,
	// Block 0xb7, offset 0x2dc0
	0x2dc0: 0x0080, 0x2dc1: 0x0080, 0x2dc2: 0x0080, 0x2dc3: 0x0080, 0x2dc4: 0x0080, 0x2dc5: 0x0080,
	0x2dc6: 0x0080, 0x2dc7: 0x0080, 0x2dc8: 0x0080, 0x2dc9: 0x0080, 0x2dca: 0x0080, 0x2dcb: 0x0080,
	0x2dcc: 0x0080, 0x2dcd: 0x0080, 0x2dce: 0x0080, 0x2dcf: 0x0080, 0x2dd0: 0x0080, 0x2dd1: 0x0080,
	0x2dd2: 0x0080, 0x2dd4: 0x0080, 0x2dd5: 0x0080, 0x2dd6: 0x0080, 0x2dd7: 0x0080,
	0x2dd8: 0x0080, 0x2dd9: 0x0080, 0x2dda: 0x0080, 0x2ddb: 0x0080, 0x2ddc: 0x0080, 0x2ddd: 0x0080,
	0x2dde: 0x0080, 0x2ddf: 0x0080, 0x2de0: 0x0080, 0x2de1: 0x0080, 0x2de2: 0x0080, 0x2de3: 0x0080,
	0x2de4: 0x0080, 0x2de5: 0x0080, 0x2de6: 0x0080, 0x2de8: 0x0080, 0x2de9: 0x0080,
	0x2dea: 0x0080, 0x2deb: 0x0080,
	0x2df0: 0x0080, 0x2df1: 0x0080, 0x2df2: 0x0080, 0x2df3: 0x00c0, 0x2df4: 0x0080,
	0x2df6: 0x0080, 0x2df7: 0x0080, 0x2df8: 0x0080, 0x2df9: 0x0080, 0x2dfa: 0x0080, 0x2dfb: 0x0080,
	0x2dfc: 0x0080, 0x2dfd: 0x0080, 0x2dfe: 0x0080, 0x2dff: 0x0080,
	// Block 0xb8, offset 0x2e00
	0x2e00: 0x0080, 0x2e01: 0x0080, 0x2e02: 0x0080, 0x2e03: 0x0080, 0x2e04: 0x0080, 0x2e05: 0x0080,
	0x2e06: 0x0080, 0x2e07: 0x0080, 0x2e08: 0x0080, 0x2e09: 0x0080, 0x2e0a: 0x0080, 0x2e0b: 0x0080,
	0x2e0c: 0x0080, 0x2e0d: 0x0080, 0x2e0e: 0x0080, 0x2e0f: 0x0080, 0x2e10: 0x0080, 0x2e11: 0x0080,
	0x2e12: 0x0080, 0x2e13: 0x0080, 0x2e14: 0x0080, 0x2e15: 0x0080, 0x2e16: 0x0080, 0x2e17: 0x0080,
	0x2e18: 0x0080, 0x2e19: 0x0080, 0x2e1a: 0x0080, 0x2e1b: 0x0080, 0x2e1c: 0x0080, 0x2e1d: 0x0080,
	0x2e1e: 0x0080, 0x2e1f: 0x0080, 0x2e20: 0x0080, 0x2e21: 0x0080, 0x2e22: 0x0080, 0x2e23: 0x0080,
	0x2e24: 0x0080, 0x2e25: 0x0080, 0x2e26: 0x0080, 0x2e27: 0x0080, 0x2e28: 0x0080, 0x2e29: 0x0080,
	0x2e2a: 0x0080, 0x2e2b: 0x0080, 0x2e2c: 0x0080, 0x2e2d: 0x0080, 0x2e2e: 0x0080, 0x2e2f: 0x0080,
	0x2e30: 0x0080, 0x2e31: 0x0080, 0x2e32: 0x0080, 0x2e33: 0x0080, 0x2e34: 0x0080, 0x2e35: 0x0080,
	0x2e36: 0x0080, 0x2e37: 0x0080, 0x2e38: 0x0080, 0x2e39: 0x0080, 0x2e3a: 0x0080, 0x2e3b: 0x0080,
	0x2e3c: 0x0080, 0x2e3f: 0x0040,
	// Block 0xb9, offset 0x2e40
	0x2e41: 0x0080, 0x2e42: 0x0080, 0x2e43: 0x0080, 0x2e44: 0x0080, 0x2e45: 0x0080,
	0x2e46: 0x0080, 0x2e47: 0x0080, 0x2e48: 0x0080, 0x2e49: 0x0080, 0x2e4a: 0x0080, 0x2e4b: 0x0080,
	0x2e4c: 0x0080, 0x2e4d: 0x0080, 0x2e4e: 0x0080, 0x2e4f: 0x0080, 0x2e50: 0x0080, 0x2e51: 0x0080,
	0x2e52: 0x0080, 0x2e53: 0x0080, 0x2e54: 0x0080, 0x2e55: 0x0080, 0x2e56: 0x0080, 0x2e57: 0x0080,
	0x2e58: 0x0080, 0x2e59: 0x0080, 0x2e5a: 0x0080, 0x2e5b: 0x0080, 0x2e5c: 0x0080, 0x2e5d: 0x0080,
	0x2e5e: 0x0080, 0x2e5f: 0x0080, 0x2e60: 0x0080, 0x2e61: 0x0080, 0x2e62: 0x0080, 0x2e63: 0x0080,
	0x2e64: 0x0080, 0x2e65: 0x0080, 0x2e66: 0x0080, 0x2e67: 0x0080, 0x2e68: 0x0080, 0x2e69: 0x0080,
	0x2e6a: 0x0080, 0x2e6b: 0x0080, 0x2e6c: 0x0080, 0x2e6d: 0x0080, 0x2e6e: 0x0080, 0x2e6f: 0x0080,
	0x2e70: 0x0080, 0x2e71: 0x0080, 0x2e72: 0x0080, 0x2e73: 0x0080, 0x2e74: 0x0080, 0x2e75: 0x0080,
	0x2e76: 0x0080, 0x2e77: 0x0080, 0x2e78: 0x0080, 0x2e79: 0x0080, 0x2e7a: 0x0080, 0x2e7b: 0x0080,
	0x2e7c: 0x0080, 0x2e7d: 0x0080, 0x2e7e: 0x0080, 0x2e7f: 0x0080,
	// Block 0xba, offset 0x2e80
	0x2e80: 0x0080, 0x2e81: 0x0080, 0x2e82: 0x0080, 0x2e83: 0x0080, 0x2e84: 0x0080, 0x2e85: 0x0080,
	0x2e86: 0x0080, 0x2e87: 0x0080, 0x2e88: 0x0080, 0x2e89: 0x0080, 0x2e8a: 0x0080, 0x2e8b: 0x0080,
	0x2e8c: 0x0080, 0x2e8d: 0x0080, 0x2e8e: 0x0080, 0x2e8f: 0x0080, 0x2e90: 0x0080, 0x2e91: 0x0080,
	0x2e92: 0x0080, 0x2e93: 0x0080, 0x2e94: 0x0080, 0x2e95: 0x0080, 0x2e96: 0x0080, 0x2e97: 0x0080,
	0x2e98: 0x0080, 0x2e99: 0x0080, 0x2e9a: 0x0080, 0x2e9b: 0x0080, 0x2e9c: 0x0080, 0x2e9d: 0x0080,
	0x2e9e: 0x0080, 0x2e9f: 0x0080, 0x2ea0: 0x0080, 0x2ea1: 0x0080, 0x2ea2: 0x0080, 0x2ea3: 0x0080,
	0x2ea4: 0x0080, 0x2ea5: 0x0080, 0x2ea6: 0x008c, 0x2ea7: 0x008c, 0x2ea8: 0x008c, 0x2ea9: 0x008c,
	0x2eaa: 0x008c, 0x2eab: 0x008c, 0x2eac: 0x008c, 0x2ead: 0x008c, 0x2eae: 0x008c, 0x2eaf: 0x008c,
	0x2eb0: 0x0080, 0x2eb1: 0x008c, 0x2eb2: 0x008c, 0x2eb3: 0x008c, 0x2eb4: 0x008c, 0x2eb5: 0x008c,
	0x2eb6: 0x008c, 0x2eb7: 0x008c, 0x2eb8: 0x008c, 0x2eb9: 0x008c, 0x2eba: 0x008c, 0x2ebb: 0x008c,
	0x2ebc: 0x008c, 0x2ebd: 0x008c, 0x2ebe: 0x008c, 0x2ebf: 0x008c,
	// Block 0xbb, offset 0x2ec0
	0x2ec0: 0x008c, 0x2ec1: 0x008c, 0x2ec2: 0x008c, 0x2ec3: 0x008c, 0x2ec4: 0x008c, 0x2ec5: 0x008c,
	0x2ec6: 0x008c, 0x2ec7: 0x008c, 0x2ec8: 0x008c, 0x2ec9: 0x008c, 0x2eca: 0x008c, 0x2ecb: 0x008c,
	0x2ecc: 0x008c, 0x2ecd: 0x008c, 0x2ece: 0x008c, 0x2ecf: 0x008c, 0x2ed0: 0x008c, 0x2ed1: 0x008c,
	0x2ed2: 0x008c, 0x2ed3: 0x008c, 0x2ed4: 0x008c, 0x2ed5: 0x008c, 0x2ed6: 0x008c, 0x2ed7: 0x008c,
	0x2ed8: 0x008c, 0x2ed9: 0x008c, 0x2eda: 0x008c, 0x2edb: 0x008c, 0x2edc: 0x008c, 0x2edd: 0x008c,
	0x2ede: 0x0080, 0x2edf: 0x0080, 0x2ee0: 0x0040, 0x2ee1: 0x0080, 0x2ee2: 0x0080, 0x2ee3: 0x0080,
	0x2ee4: 0x0080, 0x2ee5: 0x0080, 0x2ee6: 0x0080, 0x2ee7: 0x0080, 0x2ee8: 0x0080, 0x2ee9: 0x0080,
	0x2eea: 0x0080, 0x2eeb: 0x0080, 0x2eec: 0x0080, 0x2eed: 0x0080, 0x2eee: 0x0080, 0x2eef: 0x0080,
	0x2ef0: 0x0080, 0x2ef1: 0x0080, 0x2ef2: 0x0080, 0x2ef3: 0x0080, 0x2ef4: 0x0080, 0x2ef5: 0x0080,
	0x2ef6: 0x0080, 0x2ef7: 0x0080, 0x2ef8: 0x0080, 0x2ef9: 0x0080, 0x2efa: 0x0080, 0x2efb: 0x0080,
	0x2efc: 0x0080, 0x2efd: 0x0080, 0x2efe: 0x0080,
	// Block 0xbc, offset 0x2f00
	0x2f02: 0x0080, 0x2f03: 0x0080, 0x2f04: 0x0080, 0x2f05: 0x0080,
	0x2f06: 0x0080, 0x2f07: 0x0080, 0x2f0a: 0x0080, 0x2f0b: 0x0080,
	0x2f0c: 0x0080, 0x2f0d: 0x0080, 0x2f0e: 0x0080, 0x2f0f: 0x0080,
	0x2f12: 0x0080, 0x2f13: 0x0080, 0x2f14: 0x0080, 0x2f15: 0x0080, 0x2f16: 0x0080, 0x2f17: 0x0080,
	0x2f1a: 0x0080, 0x2f1b: 0x0080, 0x2f1c: 0x0080,
	0x2f20: 0x0080, 0x2f21: 0x0080, 0x2f22: 0x0080, 0x2f23: 0x0080,
	0x2f24: 0x0080, 0x2f25: 0x0080, 0x2f26: 0x0080, 0x2f28: 0x0080, 0x2f29: 0x0080,
	0x2f2a: 0x0080, 0x2f2b: 0x0080, 0x2f2c: 0x0080, 0x2f2d: 0x0080, 0x2f2e: 0x0080,
	0x2f39: 0x0040, 0x2f3a: 0x0040, 0x2f3b: 0x0040,
	0x2f3c: 0x0080, 0x2f3d: 0x0080,
	// Block 0xbd, offset 0x2f40
	0x2f40: 0x00c0, 0x2f41: 0x00c0, 0x2f42: 0x00c0, 0x2f43: 0x00c0, 0x2f44: 0x00c0, 0x2f45: 0x00c0,
	0x2f46: 0x00c0, 0x2f47: 0x00c0, 0x2f48: 0x00c0, 0x2f49: 0x00c0, 0x2f4a: 0x00c0, 0x2f4b: 0x00c0,
	0x2f4d: 0x00c0, 0x2f4e: 0x00c0, 0x2f4f: 0x00c0, 0x2f50: 0x00c0, 0x2f51: 0x00c0,
	0x2f52: 0x00c0, 0x2f53: 0x00c0, 0x2f54: 0x00c0, 0x2f55: 0x00c0, 0x2f56: 0x00c0, 0x2f57: 0x00c0,
	0x2f58: 0x00c0, 0x2f59: 0x00c0, 0x2f5a: 0x00c0, 0x2f5b: 0x00c0, 0x2f5c: 0x00c0, 0x2f5d: 0x00c0,
	0x2f5e: 0x00c0, 0x2f5f: 0x00c0, 0x2f60: 0x00c0, 0x2f61: 0x00c0, 0x2f62: 0x00c0, 0x2f63: 0x00c0,
	0x2f64: 0x00c0, 0x2f65: 0x00c0, 0x2f66: 0x00c0, 0x2f68: 0x00c0, 0x2f69: 0x00c0,
	0x2f6a: 0x00c0, 0x2f6b: 0x00c0, 0x2f6c: 0x00c0, 0x2f6d: 0x00c0, 0x2f6e: 0x00c0, 0x2f6f: 0x00c0,
	0x2f70: 0x00c0, 0x2f71: 0x00c0, 0x2f72: 0x00c0, 0x2f73: 0x00c0, 0x2f74: 0x00c0, 0x2f75: 0x00c0,
	0x2f76: 0x00c0, 0x2f77: 0x00c0, 0x2f78: 0x00c0, 0x2f79: 0x00c0, 0x2f7a: 0x00c0,
	0x2f7c: 0x00c0, 0x2f7d: 0x00c0, 0x2f7f: 0x00c0,
	// Block 0xbe, offset 0x2f80
	0x2f80: 0x00c0, 0x2f81: 0x00c0, 0x2f82: 0x00c0, 0x2f83: 0x00c0, 0x2f84: 0x00c0, 0x2f85: 0x00c0,
	0x2f86: 0x00c0, 0x2f87: 0x00c0, 0x2f88: 0x00c0, 0x2f89: 0x00c0, 0x2f8a: 0x00c0, 0x2f8b: 0x00c0,
	0x2f8c: 0x00c0, 0x2f8d: 0x00c0, 0x2f90: 0x00c0, 0x2f91: 0x00c0,
	0x2f92: 0x00c0, 0x2f93: 0x00c0, 0x2f94: 0x00c0, 0x2f95: 0x00c0, 0x2f96: 0x00c0, 0x2f97: 0x00c0,
	0x2f98: 0x00c0, 0x2f99: 0x00c0, 0x2f9a: 0x00c0, 0x2f9b: 0x00c0, 0x2f9c: 0x00c0, 0x2f9d: 0x00c0,
	// Block 0xbf, offset 0x2fc0
	0x2fc0: 0x00c0, 0x2fc1: 0x00c0, 0x2fc2: 0x00c0, 0x2fc3: 0x00c0, 0x2fc4: 0x00c0, 0x2fc5: 0x00c0,
	0x2fc6: 0x00c0, 0x2fc7: 0x00c0, 0x2fc8: 0x00c0, 0x2fc9: 0x00c0, 0x2fca: 0x00c0, 0x2fcb: 0x00c0,
	0x2fcc: 0x00c0, 0x2fcd: 0x00c0, 0x2fce: 0x00c0, 0x2fcf: 0x00c0, 0x2fd0: 0x00c0, 0x2fd1: 0x00c0,
	0x2fd2: 0x00c0, 0x2fd3: 0x00c0, 0x2fd4: 0x00c0, 0x2fd5: 0x00c0, 0x2fd6: 0x00c0, 0x2fd7: 0x00c0,
	0x2fd8: 0x00c0, 0x2fd9: 0x00c0, 0x2fda: 0x00c0, 0x2fdb: 0x00c0, 0x2fdc: 0x00c0, 0x2fdd: 0x00c0,
	0x2fde: 0x00c0, 0x2fdf: 0x00c0, 0x2fe0: 0x00c0, 0x2fe1: 0x00c0, 0x2fe2: 0x00c0, 0x2fe3: 0x00c0,
	0x2fe4: 0x00c0, 0x2fe5: 0x00c0, 0x2fe6: 0x00c0, 0x2fe7: 0x00c0, 0x2fe8: 0x00c0, 0x2fe9: 0x00c0,
	0x2fea: 0x00c0, 0x2feb: 0x00c0, 0x2fec: 0x00c0, 0x2fed: 0x00c0, 0x2fee: 0x00c0, 0x2fef: 0x00c0,
	0x2ff0: 0x00c0, 0x2ff1: 0x00c0, 0x2ff2: 0x00c0, 0x2ff3: 0x00c0, 0x2ff4: 0x00c0, 0x2ff5: 0x00c0,
	0x2ff6: 0x00c0, 0x2ff7: 0x00c0, 0x2ff8: 0x00c0, 0x2ff9: 0x00c0, 0x2ffa: 0x00c0,
	// Block 0xc0, offset 0x3000
	0x3000: 0x0080, 0x3001: 0x0080, 0x3002: 0x0080,
	0x3007: 0x0080, 0x3008: 0x0080, 0x3009: 0x0080, 0x300a: 0x0080, 0x300b: 0x0080,
	0x300c: 0x0080, 0x300d: 0x0080, 0x300e: 0x0080, 0x300f: 0x0080, 0x3010: 0x0080, 0x3011: 0x0080,
	0x3012: 0x0080, 0x3013: 0x0080, 0x3014: 0x0080, 0x3015: 0x0080, 0x3016: 0x0080, 0x3017: 0x0080,
	0x3018: 0x0080, 0x3019: 0x0080, 0x301a: 0x0080, 0x301b: 0x0080, 0x301c: 0x0080, 0x301d: 0x0080,
	0x301e: 0x0080, 0x301f: 0x0080, 0x3020: 0x0080, 0x3021: 0x0080, 0x3022: 0x0080, 0x3023: 0x0080,
	0x3024: 0x0080, 0x3025: 0x0080, 0x3026: 0x0080, 0x3027: 0x0080, 0x3028: 0x0080, 0x3029: 0x0080,
	0x302a: 0x0080, 0x302b: 0x0080, 0x302c: 0x0080, 0x302d: 0x0080, 0x302e: 0x0080, 0x302f: 0x0080,
	0x3030: 0x0080, 0x3031: 0x0080, 0x3032: 0x0080, 0x3033: 0x0080,
	0x3037: 0x0080, 0x3038: 0x0080, 0x3039: 0x0080, 0x303a: 0x0080, 0x303b: 0x0080,
	0x303c: 0x0080, 0x303d: 0x0080, 0x303e: 0x0080, 0x303f: 0x0080,
	// Block 0xc1, offset 0x3040
	0x3040: 0x0088, 0x3041: 0x0088, 0x3042: 0x0088, 0x3043: 0x0088, 0x3044: 0x0088, 0x3045: 0x0088,
	0x3046: 0x0088, 0x3047: 0x0088, 0x3048: 0x0088, 0x3049: 0x0088, 0x304a: 0x0088, 0x304b: 0x0088,
	0x304c: 0x0088, 0x304d: 0x0088, 0x304e: 0x0088, 0x304f: 0x0088, 0x3050: 0x0088, 0x3051: 0x0088,
	0x3052: 0x0088, 0x3053: 0x0088, 0x3054: 0x0088, 0x3055: 0x0088, 0x3056: 0x0088, 0x3057: 0x0088,
	0x3058: 0x0088, 0x3059: 0x0088, 0x305a: 0x0088, 0x305b: 0x0088, 0x305c: 0x0088, 0x305d: 0x0088,
	0x305e: 0x0088, 0x305f: 0x0088, 0x3060: 0x0088, 0x3061: 0x0088, 0x3062: 0x0088, 0x3063: 0x0088,
	0x3064: 0x0088, 0x3065: 0x0088, 0x3066: 0x0088, 0x3067: 0x0088, 0x3068: 0x0088, 0x3069: 0x0088,
	0x306a: 0x0088, 0x306b: 0x0088, 0x306c: 0x0088, 0x306d: 0x0088, 0x306e: 0x0088, 0x306f: 0x0088,
	0x3070: 0x0088, 0x3071: 0x0088, 0x3072: 0x0088, 0x3073: 0x0088, 0x3074: 0x0088, 0x3075: 0x0088,
	0x3076: 0x0088, 0x3077: 0x0088, 0x3078: 0x0088, 0x3079: 0x0088, 0x307a: 0x0088, 0x307b: 0x0088,
	0x307c: 0x0088, 0x307d: 0x0088, 0x307e: 0x0088, 0x307f: 0x0088,
	// Block 0xc2, offset 0x3080
	0x3080: 0x0088, 0x3081: 0x0088, 0x3082: 0x0088, 0x3083: 0x0088, 0x3084: 0x0088, 0x3085: 0x0088,
	0x3086: 0x0088, 0x3087: 0x0088, 0x3088: 0x0088, 0x3089: 0x0088, 0x308a: 0x0088, 0x308b: 0x0088,
	0x308c: 0x0088, 0x308d: 0x0088, 0x308e: 0x0088, 0x3090: 0x0080, 0x3091: 0x0080,
	0x3092: 0x0080, 0x3093: 0x0080, 0x3094: 0x0080, 0x3095: 0x0080, 0x3096: 0x0080, 0x3097: 0x0080,
	0x3098: 0x0080, 0x3099: 0x0080, 0x309a: 0x0080, 0x309b: 0x0080, 0x309c: 0x0080,
	0x30a0: 0x0088,
	// Block 0xc3, offset 0x30c0
	0x30d0: 0x0080, 0x30d1: 0x0080,
	0x30d2: 0x0080, 0x30d3: 0x0080, 0x30d4: 0x0080, 0x30d5: 0x0080, 0x30d6: 0x0080, 0x30d7: 0x0080,
	0x30d8: 0x0080, 0x30d9: 0x0080, 0x30da: 0x0080, 0x30db: 0x0080, 0x30dc: 0x0080, 0x30dd: 0x0080,
	0x30de: 0x0080, 0x30df: 0x0080, 0x30e0: 0x0080, 0x30e1: 0x0080, 0x30e2: 0x0080, 0x30e3: 0x0080,
	0x30e4: 0x0080, 0x30e5: 0x0080, 0x30e6: 0x0080, 0x30e7: 0x0080, 0x30e8: 0x0080, 0x30e9: 0x0080,
	0x30ea: 0x0080, 0x30eb: 0x0080, 0x30ec: 0x0080, 0x30ed: 0x0080, 0x30ee: 0x0080, 0x30ef: 0x0080,
	0x30f0: 0x0080, 0x30f1: 0x0080, 0x30f2: 0x0080, 0x30f3: 0x0080, 0x30f4: 0x0080, 0x30f5: 0x0080,
	0x30f6: 0x0080, 0x30f7: 0x0080, 0x30f8: 0x0080, 0x30f9: 0x0080, 0x30fa: 0x0080, 0x30fb: 0x0080,
	0x30fc: 0x0080, 0x30fd: 0x00c3,
	// Block 0xc4, offset 0x3100
	0x3100: 0x00c0, 0x3101: 0x00c0, 0x3102: 0x00c0, 0x3103: 0x00c0, 0x3104: 0x00c0, 0x3105: 0x00c0,
	0x3106: 0x00c0, 0x3107: 0x00c0, 0x3108: 0x00c0, 0x3109: 0x00c0, 0x310a: 0x00c0, 0x310b: 0x00c0,
	0x310c: 0x00c0, 0x310d: 0x00c0, 0x310e: 0x00c0, 0x310f: 0x00c0, 0x3110: 0x00c0, 0x3111: 0x00c0,
	0x3112: 0x00c0, 0x3113: 0x00c0, 0x3114: 0x00c0, 0x3115: 0x00c0, 0x3116: 0x00c0, 0x3117: 0x00c0,
	0x3118: 0x00c0, 0x3119: 0x00c0, 0x311a: 0x00c0, 0x311b: 0x00c0, 0x311c: 0x00c0,
	0x3120: 0x00c0, 0x3121: 0x00c0, 0x3122: 0x00c0, 0x3123: 0x00c0,
	0x3124: 0x00c0, 0x3125: 0x00c0, 0x3126: 0x00c0, 0x3127: 0x00c0, 0x3128: 0x00c0, 0x3129: 0x00c0,
	0x312a: 0x00c0, 0x312b: 0x00c0, 0x312c: 0x00c0, 0x312d: 0x00c0, 0x312e: 0x00c0, 0x312f: 0x00c0,
	0x3130: 0x00c0, 0x3131: 0x00c0, 0x3132: 0x00c0, 0x3133: 0x00c0, 0x3134: 0x00c0, 0x3135: 0x00c0,
	0x3136: 0x00c0, 0x3137: 0x00c0, 0x3138: 0x00c0, 0x3139: 0x00c0, 0x313a: 0x00c0, 0x313b: 0x00c0,
	0x313c: 0x00c0, 0x313d: 0x00c0, 0x313e: 0x00c0, 0x313f: 0x00c0,
	// Block 0xc5, offset 0x3140
	0x3140: 0x00c0, 0x3141: 0x00c0, 0x3142: 0x00c0, 0x3143: 0x00c0, 0x3144: 0x00c0, 0x3145: 0x00c0,
	0x3146: 0x00c0, 0x3147: 0x00c0, 0x3148: 0x00c0, 0x3149: 0x00c0, 0x314a: 0x00c0, 0x314b: 0x00c0,
	0x314c: 0x00c0, 0x314d: 0x00c0, 0x314e: 0x00c0, 0x314f: 0x00c0, 0x3150: 0x00c0,
	0x3160: 0x00c3, 0x3161: 0x0080, 0x3162: 0x0080, 0x3163: 0x0080,
	0x3164: 0x0080, 0x3165: 0x0080, 0x3166: 0x0080, 0x3167: 0x0080, 0x3168: 0x0080, 0x3169: 0x0080,
	0x316a: 0x0080, 0x316b: 0x0080, 0x316c: 0x0080, 0x316d: 0x0080, 0x316e: 0x0080, 0x316f: 0x0080,
	0x3170: 0x0080, 0x3171: 0x0080, 0x3172: 0x0080, 0x3173: 0x0080, 0x3174: 0x0080, 0x3175: 0x0080,
	0x3176: 0x0080, 0x3177: 0x0080, 0x3178: 0x0080, 0x3179: 0x0080, 0x317a: 0x0080, 0x317b: 0x0080,
	// Block 0xc6, offset 0x3180
	0x3180: 0x00c0, 0x3181: 0x00c0, 0x3182: 0x00c0, 0x3183: 0x00c0, 0x3184: 0x00c0, 0x3185: 0x00c0,
	0x3186: 0x00c0, 0x3187: 0x00c0, 0x3188: 0x00c0, 0x3189: 0x00c0, 0x318a: 0x00c0, 0x318b: 0x00c0,
	0x318c: 0x00c0, 0x318d: 0x00c0, 0x318e: 0x00c0, 0x318f: 0x00c0, 0x3190: 0x00c0, 0x3191: 0x00c0,
	0x3192: 0x00c0, 0x3193: 0x00c0, 0x3194: 0x00c0, 0x3195: 0x00c0, 0x3196: 0x00c0, 0x3197: 0x00c0,
	0x3198: 0x00c0, 0x3199: 0x00c0, 0x319a: 0x00c0, 0x319b: 0x00c0, 0x319c: 0x00c0, 0x319d: 0x00c0,
	0x319e: 0x00c0, 0x319f: 0x00c0, 0x31a0: 0x0080, 0x31a1: 0x0080, 0x31a2: 0x0080, 0x31a3: 0x0080,
	0x31ad: 0x00c0, 0x31ae: 0x00c0, 0x31af: 0x00c0,
	0x31b0: 0x00c0, 0x31b1: 0x00c0, 0x31b2: 0x00c0, 0x31b3: 0x00c0, 0x31b4: 0x00c0, 0x31b5: 0x00c0,
	0x31b6: 0x00c0, 0x31b7: 0x00c0, 0x31b8: 0x00c0, 0x31b9: 0x00c0, 0x31ba: 0x00c0, 0x31bb: 0x00c0,
	0x31bc: 0x00c0, 0x31bd: 0x00c0, 0x31be: 0x00c0, 0x31bf: 0x00c0,
	// Block 0xc7, offset 0x31c0
	0x31c0: 0x00c0, 0x31c1: 0x0080, 0x31c2: 0x00c0, 0x31c3: 0x00c0, 0x31c4: 0x00c0, 0x31c5: 0x00c0,
	0x31c6: 0x00c0, 0x31c7: 0x00c0, 0x31c8: 0x00c0, 0x31c9: 0x00c0, 0x31ca: 0x0080,
	0x31d0: 0x00c0, 0x31d1: 0x00c0,
	0x31d2: 0x00c0, 0x31d3: 0x00c0, 0x31d4: 0x00c0, 0x31d5: 0x00c0, 0x31d6: 0x00c0, 0x31d7: 0x00c0,
	0x31d8: 0x00c0, 0x31d9: 0x00c0, 0x31da: 0x00c0, 0x31db: 0x00c0, 0x31dc: 0x00c0, 0x31dd: 0x00c0,
	0x31de: 0x00c0, 0x31df: 0x00c0, 0x31e0: 0x00c0, 0x31e1: 0x00c0, 0x31e2: 0x00c0, 0x31e3: 0x00c0,
	0x31e4: 0x00c0, 0x31e5: 0x00c0, 0x31e6: 0x00c0, 0x31e7: 0x00c0, 0x31e8: 0x00c0, 0x31e9: 0x00c0,
	0x31ea: 0x00c0, 0x31eb: 0x00c0, 0x31ec: 0x00c0, 0x31ed: 0x00c0, 0x31ee: 0x00c0, 0x31ef: 0x00c0,
	0x31f0: 0x00c0, 0x31f1: 0x00c0, 0x31f2: 0x00c0, 0x31f3: 0x00c0, 0x31f4: 0x00c0, 0x31f5: 0x00c0,
	0x31f6: 0x00c3, 0x31f7: 0x00c3, 0x31f8: 0x00c3, 0x31f9: 0x00c3, 0x31fa: 0x00c3,
	// Block 0xc8, offset 0x3200
	0x3200: 0x00c0, 0x3201: 0x00c0, 0x3202: 0x00c0, 0x3203: 0x00c0, 0x3204: 0x00c0, 0x3205: 0x00c0,
	0x3206: 0x00c0, 0x3207: 0x00c0, 0x3208: 0x00c0, 0x3209: 0x00c0, 0x320a: 0x00c0, 0x320b: 0x00c0,
	0x320c: 0x00c0, 0x320d: 0x00c0, 0x320e: 0x00c0, 0x320f: 0x00c0, 0x3210: 0x00c0, 0x3211: 0x00c0,
	0x3212: 0x00c0, 0x3213: 0x00c0, 0x3214: 0x00c0, 0x3215: 0x00c0, 0x3216: 0x00c0, 0x3217: 0x00c0,
	0x3218: 0x00c0, 0x3219: 0x00c0, 0x321a: 0x00c0, 0x321b: 0x00c0, 0x321c: 0x00c0, 0x321d: 0x00c0,
	0x321f: 0x0080, 0x3220: 0x00c0, 0x3221: 0x00c0, 0x3222: 0x00c0, 0x3223: 0x00c0,
	0x3224: 0x00c0, 0x3225: 0x00c0, 0x3226: 0x00c0, 0x3227: 0x00c0, 0x3228: 0x00c0, 0x3229: 0x00c0,
	0x322a: 0x00c0, 0x322b: 0x00c0, 0x322c: 0x00c0, 0x322d: 0x00c0, 0x322e: 0x00c0, 0x322f: 0x00c0,
	0x3230: 0x00c0, 0x3231: 0x00c0, 0x3232: 0x00c0, 0x3233: 0x00c0, 0x3234: 0x00c0, 0x3235: 0x00c0,
	0x3236: 0x00c0, 0x3237: 0x00c0, 0x3238: 0x00c0, 0x3239: 0x00c0, 0x323a: 0x00c0, 0x323b: 0x00c0,
	0x323c: 0x00c0, 0x323d: 0x00c0, 0x323e: 0x00c0, 0x323f: 0x00c0,
	// Block 0xc9, offset 0x3240
	0x3240: 0x00c0, 0x3241: 0x00c0, 0x3242: 0x00c0, 0x3243: 0x00c0,
	0x3248: 0x00c0, 0x3249: 0x00c0, 0x324a: 0x00c0, 0x324b: 0x00c0,
	0x324c: 0x00c0, 0x324d: 0x00c0, 0x324e: 0x00c0, 0x324f: 0x00c0, 0x3250: 0x0080, 0x3251: 0x0080,
	0x3252: 0x0080, 0x3253: 0x0080, 0x3254: 0x0080, 0x3255: 0x0080,
	// Block 0xca, offset 0x3280
	0x3280: 0x00c0, 0x3281: 0x00c0, 0x3282: 0x00c0, 0x3283: 0x00c0, 0x3284: 0x00c0, 0x3285: 0x00c0,
	0x3286: 0x00c0, 0x3287: 0x00c0, 0x3288: 0x00c0, 0x3289: 0x00c0, 0x328a: 0x00c0, 0x328b: 0x00c0,
	0x328c: 0x00c0, 0x328d: 0x00c0, 0x328e: 0x00c0, 0x328f: 0x00c0, 0x3290: 0x00c0, 0x3291: 0x00c0,
	0x3292: 0x00c0, 0x3293: 0x00c0, 0x3294: 0x00c0, 0x3295: 0x00c0, 0x3296: 0x00c0, 0x3297: 0x00c0,
	0x3298: 0x00c0, 0x3299: 0x00c0, 0x329a: 0x00c0, 0x329b: 0x00c0, 0x329c: 0x00c0, 0x329d: 0x00c0,
	0x32a0: 0x00c0, 0x32a1: 0x00c0, 0x32a2: 0x00c0, 0x32a3: 0x00c0,
	0x32a4: 0x00c0, 0x32a5: 0x00c0, 0x32a6: 0x00c0, 0x32a7: 0x00c0, 0x32a8: 0x00c0, 0x32a9: 0x00c0,
	0x32b0: 0x00c0, 0x32b1: 0x00c0, 0x32b2: 0x00c0, 0x32b3: 0x00c0, 0x32b4: 0x00c0, 0x32b5: 0x00c0,
	0x32b6: 0x00c0, 0x32b7: 0x00c0, 0x32b8: 0x00c0, 0x32b9: 0x00c0, 0x32ba: 0x00c0, 0x32bb: 0x00c0,
	0x32bc: 0x00c0, 0x32bd: 0x00c0, 0x32be: 0x00c0, 0x32bf: 0x00c0,
	// Block 0xcb, offset 0x32c0
	0x32c0: 0x00c0, 0x32c1: 0x00c0, 0x32c2: 0x00c0, 0x32c3: 0x00c0, 0x32c4: 0x00c0, 0x32c5: 0x00c0,
	0x32c6: 0x00c0, 0x32c7: 0x00c0, 0x32c8: 0x00c0, 0x32c9: 0x00c0, 0x32ca: 0x00c0, 0x32cb: 0x00c0,
	0x32cc: 0x00c0, 0x32cd: 0x00c0, 0x32ce: 0x00c0, 0x32cf: 0x00c0, 0x32d0: 0x00c0, 0x32d1: 0x00c0,
	0x32d2: 0x00c0, 0x32d3: 0x00c0,
	0x32d8: 0x00c0, 0x32d9: 0x00c0, 0x32da: 0x00c0, 0x32db: 0x00c0, 0x32dc: 0x00c0, 0x32dd: 0x00c0,
	0x32de: 0x00c0, 0x32df: 0x00c0, 0x32e0: 0x00c0, 0x32e1: 0x00c0, 0x32e2: 0x00c0, 0x32e3: 0x00c0,
	0x32e4: 0x00c0, 0x32e5: 0x00c0, 0x32e6: 0x00c0, 0x32e7: 0x00c0, 0x32e8: 0x00c0, 0x32e9: 0x00c0,
	0x32ea: 0x00c0, 0x32eb: 0x00c0, 0x32ec: 0x00c0, 0x32ed: 0x00c0, 0x32ee: 0x00c0, 0x32ef: 0x00c0,
	0x32f0: 0x00c0, 0x32f1: 0x00c0, 0x32f2: 0x00c0, 0x32f3: 0x00c0, 0x32f4: 0x00c0, 0x32f5: 0x00c0,
	0x32f6: 0x00c0, 0x32f7: 0x00c0, 0x32f8: 0x00c0, 0x32f9: 0x00c0, 0x32fa: 0x00c0, 0x32fb: 0x00c0,
	// Block 0xcc, offset 0x3300
	0x3300: 0x00c0, 0x3301: 0x00c0, 0x3302: 0x00c0, 0x3303: 0x00c0, 0x3304: 0x00c0, 0x3305: 0x00c0,
	0x3306: 0x00c0, 0x3307: 0x00c0, 0x3308: 0x00c0, 0x3309: 0x00c0, 0x330a: 0x00c0, 0x330b: 0x00c0,
	0x330c: 0x00c0, 0x330d: 0x00c0, 0x330e: 0x00c0, 0x330f: 0x00c0, 0x3310: 0x00c0, 0x3311: 0x00c0,
	0x3312: 0x00c0, 0x3313: 0x00c0, 0x3314: 0x00c0, 0x3315: 0x00c0, 0x3316: 0x00c0, 0x3317: 0x00c0,
	0x3318: 0x00c0, 0x3319: 0x00c0, 0x331a: 0x00c0, 0x331b: 0x00c0, 0x331c: 0x00c0, 0x331d: 0x00c0,
	0x331e: 0x00c0, 0x331f: 0x00c0, 0x3320: 0x00c0, 0x3321: 0x00c0, 0x3322: 0x00c0, 0x3323: 0x00c0,
	0x3324: 0x00c0, 0x3325: 0x00c0, 0x3326: 0x00c0, 0x3327: 0x00c0,
	0x3330: 0x00c0, 0x3331: 0x00c0, 0x3332: 0x00c0, 0x3333: 0x00c0, 0x3334: 0x00c0, 0x3335: 0x00c0,
	0x3336: 0x00c0, 0x3337: 0x00c0, 0x3338: 0x00c0, 0x3339: 0x00c0, 0x333a: 0x00c0, 0x333b: 0x00c0,
	0x333c: 0x00c0, 0x333d: 0x00c0, 0x333e: 0x00c0, 0x333f: 0x00c0,
	// Block 0xcd, offset 0x3340
	0x3340: 0x00c0, 0x3341: 0x00c0, 0x3342: 0x00c0, 0x3343: 0x00c0, 0x3344: 0x00c0, 0x3345: 0x00c0,
	0x3346: 0x00c0, 0x3347: 0x00c0, 0x3348: 0x00c0, 0x3349: 0x00c0, 0x334a: 0x00c0, 0x334b: 0x00c0,
	0x334c: 0x00c0, 0x334d: 0x00c0, 0x334e: 0x00c0, 0x334f: 0x00c0, 0x3350: 0x00c0, 0x3351: 0x00c0,
	0x3352: 0x00c0, 0x3353: 0x00c0, 0x3354: 0x00c0, 0x3355: 0x00c0, 0x3356: 0x00c0, 0x3357: 0x00c0,
	0x3358: 0x00c0, 0x3359: 0x00c0, 0x335a: 0x00c0, 0x335b: 0x00c0, 0x335c: 0x00c0, 0x335d: 0x00c0,
	0x335e: 0x00c0, 0x335f: 0x00c0, 0x3360: 0x00c0, 0x3361: 0x00c0, 0x3362: 0x00c0, 0x3363: 0x00c0,
	0x336f: 0x0080,
	// Block 0xce, offset 0x3380
	0x3380: 0x00c0, 0x3381: 0x00c0, 0x3382: 0x00c0, 0x3383: 0x00c0, 0x3384: 0x00c0, 0x3385: 0x00c0,
	0x3386: 0x00c0, 0x3387: 0x00c0, 0x3388: 0x00c0, 0x3389: 0x00c0, 0x338a: 0x00c0, 0x338b: 0x00c0,
	0x338c: 0x00c0, 0x338d: 0x00c0, 0x338e: 0x00c0, 0x338f: 0x00c0, 0x3390: 0x00c0, 0x3391: 0x00c0,
	0x3392: 0x00c0, 0x3393: 0x00c0, 0x3394: 0x00c0, 0x3395: 0x00c0, 0x3396: 0x00c0, 0x3397: 0x00c0,
	0x3398: 0x00c0, 0x3399: 0x00c0, 0x339a: 0x00c0, 0x339b: 0x00c0, 0x339c: 0x00c0, 0x339d: 0x00c0,
	0x339e: 0x00c0, 0x339f: 0x00c0, 0x33a0: 0x00c0, 0x33a1: 0x00c0, 0x33a2: 0x00c0, 0x33a3: 0x00c0,
	0x33a4: 0x00c0, 0x33a5: 0x00c0, 0x33a6: 0x00c0, 0x33a7: 0x00c0, 0x33a8: 0x00c0, 0x33a9: 0x00c0,
	0x33aa: 0x00c0, 0x33ab: 0x00c0, 0x33ac: 0x00c0, 0x33ad: 0x00c0, 0x33ae: 0x00c0, 0x33af: 0x00c0,
	0x33b0: 0x00c0, 0x33b1: 0x00c0, 0x33b2: 0x00c0, 0x33b3: 0x00c0, 0x33b4: 0x00c0, 0x33b5: 0x00c0,
	0x33b6: 0x00c0,
	// Block 0xcf, offset 0x33c0
	0x33c0: 0x00c0, 0x33c1: 0x00c0, 0x33c2: 0x00c0, 0x33c3: 0x00c0, 0x33c4: 0x00c0, 0x33c5: 0x00c0,
	0x33c6: 0x00c0, 0x33c7: 0x00c0, 0x33c8: 0x00c0, 0x33c9: 0x00c0, 0x33ca: 0x00c0, 0x33cb: 0x00c0,
	0x33cc: 0x00c0, 0x33cd: 0x00c0, 0x33ce: 0x00c0, 0x33cf: 0x00c0, 0x33d0: 0x00c0, 0x33d1: 0x00c0,
	0x33d2: 0x00c0, 0x33d3: 0x00c0, 0x33d4: 0x00c0, 0x33d5: 0x00c0,
	0x33e0: 0x00c0, 0x33e1: 0x00c0, 0x33e2: 0x00c0, 0x33e3: 0x00c0,
	0x33e4: 0x00c0, 0x33e5: 0x00c0, 0x33e6: 0x00c0, 0x33e7: 0x00c0,
	// Block 0xd0, offset 0x3400
	0x3400: 0x00c0, 0x3401: 0x00c0, 0x3402: 0x00c0, 0x3403: 0x00c0, 0x3404: 0x00c0, 0x3405: 0x00c0,
	0x3408: 0x00c0, 0x340a: 0x00c0, 0x340b: 0x00c0,
	0x340c: 0x00c0, 0x340d: 0x00c0, 0x340e: 0x00c0, 0x340f: 0x00c0, 0x3410: 0x00c0, 0x3411: 0x00c0,
	0x3412: 0x00c0, 0x3413: 0x00c0, 0x3414: 0x00c0, 0x3415: 0x00c0, 0x3416: 0x00c0, 0x3417: 0x00c0,
	0x3418: 0x00c0, 0x3419: 0x00c0, 0x341a: 0x00c0, 0x341b: 0x00c0, 0x341c: 0x00c0, 0x341d: 0x00c0,
	0x341e: 0x00c0, 0x341f: 0x00c0, 0x3420: 0x00c0, 0x3421: 0x00c0, 0x3422: 0x00c0, 0x3423: 0x00c0,
	0x3424: 0x00c0, 0x3425: 0x00c0, 0x3426: 0x00c0, 0x3427: 0x00c0, 0x3428: 0x00c0, 0x3429: 0x00c0,
	0x342a: 0x00c0, 0x342b: 0x00c0, 0x342c: 0x00c0, 0x342d: 0x00c0, 0x342e: 0x00c0, 0x342f: 0x00c0,
	0x3430: 0x00c0, 0x3431: 0x00c0, 0x3432: 0x00c0, 0x3433: 0x00c0, 0x3434: 0x00c0, 0x3435: 0x00c0,
	0x3437: 0x00c0, 0x3438: 0x00c0,
	0x343c: 0x00c0, 0x343f: 0x00c0,
	// Block 0xd1, offset 0x3440
	0x3440: 0x00c0, 0x3441: 0x00c0, 0x3442: 0x00c0, 0x3443: 0x00c0, 0x3444: 0x00c0, 0x3445: 0x00c0,
	0x3446: 0x00c0, 0x3447: 0x00c0, 0x3448: 0x00c0, 0x3449: 0x00c0, 0x344a: 0x00c0, 0x344b: 0x00c0,
	0x344c: 0x00c0, 0x344d: 0x00c0, 0x344e: 0x00c0, 0x344f: 0x00c0, 0x3450: 0x00c0, 0x3451: 0x00c0,
	0x3452: 0x00c0, 0x3453: 0x00c0, 0x3454: 0x00c0, 0x3455: 0x00c0, 0x3457: 0x0080,
	0x3458: 0x0080, 0x3459: 0x0080, 0x345a: 0x0080, 0x345b: 0x0080, 0x345c: 0x0080, 0x345d: 0x0080,
	0x345e: 0x0080, 0x345f: 0x0080, 0x3460: 0x00c0, 0x3461: 0x00c0, 0x3462: 0x00c0, 0x3463: 0x00c0,
	0x3464: 0x00c0, 0x3465: 0x00c0, 0x3466: 0x00c0, 0x3467: 0x00c0, 0x3468: 0x00c0, 0x3469: 0x00c0,
	0x346a: 0x00c0, 0x346b: 0x00c0, 0x346c: 0x00c0, 0x346d: 0x00c0, 0x346e: 0x00c0, 0x346f: 0x00c0,
	0x3470: 0x00c0, 0x3471: 0x00c0, 0x3472: 0x00c0, 0x3473: 0x00c0, 0x3474: 0x00c0, 0x3475: 0x00c0,
	0x3476: 0x00c0, 0x3477: 0x0080, 0x3478: 0x0080, 0x3479: 0x0080, 0x347a: 0x0080, 0x347b: 0x0080,
	0x347c: 0x0080, 0x347d: 0x0080, 0x347e: 0x0080, 0x347f: 0x0080,
	// Block 0xd2, offset 0x3480
	0x3480: 0x00c0, 0x3481: 0x00c0, 0x3482: 0x00c0, 0x3483: 0x00c0, 0x3484: 0x00c0, 0x3485: 0x00c0,
	0x3486: 0x00c0, 0x3487: 0x00c0, 0x3488: 0x00c0, 0x3489: 0x00c0, 0x348a: 0x00c0, 0x348b: 0x00c0,
	0x348c: 0x00c0, 0x348d: 0x00c0, 0x348e: 0x00c0, 0x348f: 0x00c0, 0x3490: 0x00c0, 0x3491: 0x00c0,
	0x3492: 0x00c0, 0x3493: 0x00c0, 0x3494: 0x00c0, 0x3495: 0x00c0, 0x3496: 0x00c0, 0x3497: 0x00c0,
	0x3498: 0x00c0, 0x3499: 0x00c0, 0x349a: 0x00c0, 0x349b: 0x00c0, 0x349c: 0x00c0, 0x349d: 0x00c0,
	0x349e: 0x00c0,
	0x34a7: 0x0080, 0x34a8: 0x0080, 0x34a9: 0x0080,
	0x34aa: 0x0080, 0x34ab: 0x0080, 0x34ac: 0x0080, 0x34ad: 0x0080, 0x34ae: 0x0080, 0x34af: 0x0080,
	// Block 0xd3, offset 0x34c0
	0x34e0: 0x00c0, 0x34e1: 0x00c0, 0x34e2: 0x00c0, 0x34e3: 0x00c0,
	0x34e4: 0x00c0, 0x34e5: 0x00c0, 0x34e6: 0x00c0, 0x34e7: 0x00c0, 0x34e8: 0x00c0, 0x34e9: 0x00c0,
	0x34ea: 0x00c0, 0x34eb: 0x00c0, 0x34ec: 0x00c0, 0x34ed: 0x00c0, 0x34ee: 0x00c0, 0x34ef: 0x00c0,
	0x34f0: 0x00c0, 0x34f1: 0x00c0, 0x34f2: 0x00c0, 0x34f4: 0x00c0, 0x34f5: 0x00c0,
	0x34fb: 0x0080,
	0x34fc: 0x0080, 0x34fd: 0x0080, 0x34fe: 0x0080, 0x34ff: 0x0080,
	// Block 0xd4, offset 0x3500
	0x3500: 0x00c0, 0x3501: 0x00c0, 0x3502: 0x00c0, 0x3503: 0x00c0, 0x3504: 0x00c0, 0x3505: 0x00c0,
	0x3506: 0x00c0, 0x3507: 0x00c0, 0x3508: 0x00c0, 0x3509: 0x00c0, 0x350a: 0x00c0, 0x350b: 0x00c0,
	0x350c: 0x00c0, 0x350d: 0x00c0, 0x350e: 0x00c0, 0x350f: 0x00c0, 0x3510: 0x00c0, 0x3511: 0x00c0,
	0x3512: 0x00c0, 0x3513: 0x00c0, 0x3514: 0x00c0, 0x3515: 0x00c0, 0x3516: 0x0080, 0x3517: 0x0080,
	0x3518: 0x0080, 0x3519: 0x0080, 0x351a: 0x0080, 0x351b: 0x0080,
	0x351f: 0x0080, 0x3520: 0x00c0, 0x3521: 0x00c0, 0x3522: 0x00c0, 0x3523: 0x00c0,
	0x3524: 0x00c0, 0x3525: 0x00c0, 0x3526: 0x00c0, 0x3527: 0x00c0, 0x3528: 0x00c0, 0x3529: 0x00c0,
	0x352a: 0x00c0, 0x352b: 0x00c0, 0x352c: 0x00c0, 0x352d: 0x00c0, 0x352e: 0x00c0, 0x352f: 0x00c0,
	0x3530: 0x00c0, 0x3531: 0x00c0, 0x3532: 0x00c0, 0x3533: 0x00c0, 0x3534: 0x00c0, 0x3535: 0x00c0,
	0x3536: 0x00c0, 0x3537: 0x00c0, 0x3538: 0x00c0, 0x3539: 0x00c0,
	0x353f: 0x0080,
	// Block 0xd5, offset 0x3540
	0x3540: 0x00c0, 0x3541: 0x00c0, 0x3542: 0x00c0, 0x3543: 0x00c0, 0x3544: 0x00c0, 0x3545: 0x00c0,
	0x3546: 0x00c0, 0x3547: 0x00c0, 0x3548: 0x00c0, 0x3549: 0x00c0, 0x354a: 0x00c0, 0x354b: 0x00c0,
	0x354c: 0x00c0, 0x354d: 0x00c0, 0x354e: 0x00c0, 0x354f: 0x00c0, 0x3550: 0x00c0, 0x3551: 0x00c0,
	0x3552: 0x00c0, 0x3553: 0x00c0, 0x3554: 0x00c0, 0x3555: 0x00c0, 0x3556: 0x00c0, 0x3557: 0x00c0,
	0x3558: 0x00c0, 0x3559: 0x00c0, 0x355a: 0x00c0, 0x355b: 0x00c0, 0x355c: 0x00c0, 0x355d: 0x00c0,
	0x355e: 0x00c0, 0x355f: 0x00c0, 0x3560: 0x00c0, 0x3561: 0x00c0, 0x3562: 0x00c0, 0x3563: 0x00c0,
	0x3564: 0x00c0, 0x3565: 0x00c0, 0x3566: 0x00c0, 0x3567: 0x00c0, 0x3568: 0x00c0, 0x3569: 0x00c0,
	0x356a: 0x00c0, 0x356b: 0x00c0, 0x356c: 0x00c0, 0x356d: 0x00c0, 0x356e: 0x00c0, 0x356f: 0x00c0,
	0x3570: 0x00c0, 0x3571: 0x00c0, 0x3572: 0x00c0, 0x3573: 0x00c0, 0x3574: 0x00c0, 0x3575: 0x00c0,
	0x3576: 0x00c0, 0x3577: 0x00c0,
	0x357c: 0x0080, 0x357d: 0x0080, 0x357e: 0x00c0, 0x357f: 0x00c0,
	// Block 0xd6, offset 0x3580
	0x3580: 0x00c0, 0x3581: 0x00c3, 0x3582: 0x00c3, 0x3583: 0x00c3, 0x3585: 0x00c3,
	0x3586: 0x00c3,
	0x358c: 0x00c3, 0x358d: 0x00c3, 0x358e: 0x00c3, 0x358f: 0x00c3, 0x3590: 0x00c0, 0x3591: 0x00c0,
	0x3592: 0x00c0, 0x3593: 0x00c0, 0x3595: 0x00c0, 0x3596: 0x00c0, 0x3597: 0x00c0,
	0x3599: 0x00c0, 0x359a: 0x00c0, 0x359b: 0x00c0, 0x359c: 0x00c0, 0x359d: 0x00c0,
	0x359e: 0x00c0, 0x359f: 0x00c0, 0x35a0: 0x00c0, 0x35a1: 0x00c0, 0x35a2: 0x00c0, 0x35a3: 0x00c0,
	0x35a4: 0x00c0, 0x35a5: 0x00c0, 0x35a6: 0x00c0, 0x35a7: 0x00c0, 0x35a8: 0x00c0, 0x35a9: 0x00c0,
	0x35aa: 0x00c0, 0x35ab: 0x00c0, 0x35ac: 0x00c0, 0x35ad: 0x00c0, 0x35ae: 0x00c0, 0x35af: 0x00c0,
	0x35b0: 0x00c0, 0x35b1: 0x00c0, 0x35b2: 0x00c0, 0x35b3: 0x00c0, 0x35b4: 0x00c0, 0x35b5: 0x00c0,
	0x35b8: 0x00c3, 0x35b9: 0x00c3, 0x35ba: 0x00c3,
	0x35bf: 0x00c6,
	// Block 0xd7, offset 0x35c0
	0x35c0: 0x0080, 0x35c1: 0x0080, 0x35c2: 0x0080, 0x35c3: 0x0080, 0x35c4: 0x0080, 0x35c5: 0x0080,
	0x35c6: 0x0080, 0x35c7: 0x0080, 0x35c8: 0x0080,
	0x35d0: 0x0080, 0x35d1: 0x0080,
	0x35d2: 0x0080, 0x35d3: 0x0080, 0x35d4: 0x0080, 0x35d5: 0x0080, 0x35d6: 0x0080, 0x35d7: 0x0080,
	0x35d8: 0x0080,
	0x35e0: 0x00c0, 0x35e1: 0x00c0, 0x35e2: 0x00c0, 0x35e3: 0x00c0,
	0x35e4: 0x00c0, 0x35e5: 0x00c0, 0x35e6: 0x00c0, 0x35e7: 0x00c0, 0x35e8: 0x00c0, 0x35e9: 0x00c0,
	0x35ea: 0x00c0, 0x35eb: 0x00c0, 0x35ec: 0x00c0, 0x35ed: 0x00c0, 0x35ee: 0x00c0, 0x35ef: 0x00c0,
	0x35f0: 0x00c0, 0x35f1: 0x00c0, 0x35f2: 0x00c0, 0x35f3: 0x00c0, 0x35f4: 0x00c0, 0x35f5: 0x00c0,
	0x35f6: 0x00c0, 0x35f7: 0x00c0, 0x35f8: 0x00c0, 0x35f9: 0x00c0, 0x35fa: 0x00c0, 0x35fb: 0x00c0,
	0x35fc: 0x00c0, 0x35fd: 0x0080, 0x35fe: 0x0080, 0x35ff: 0x0080,
	// Block 0xd8, offset 0x3600
	0x3600: 0x00c0, 0x3601: 0x00c0, 0x3602: 0x00c0, 0x3603: 0x00c0, 0x3604: 0x00c0, 0x3605: 0x00c0,
	0x3606: 0x00c0, 0x3607: 0x00c0, 0x3608: 0x00c0, 0x3609: 0x00c0, 0x360a: 0x00c0, 0x360b: 0x00c0,
	0x360c: 0x00c0, 0x360d: 0x00c0, 0x360e: 0x00c0, 0x360f: 0x00c0, 0x3610: 0x00c0, 0x3611: 0x00c0,
	0x3612: 0x00c0, 0x3613: 0x00c0, 0x3614: 0x00c0, 0x3615: 0x00c0, 0x3616: 0x00c0, 0x3617: 0x00c0,
	0x3618: 0x00c0, 0x3619: 0x00c0, 0x361a: 0x00c0, 0x361b: 0x00c0, 0x361c: 0x00c0, 0x361d: 0x0080,
	0x361e: 0x0080, 0x361f: 0x0080,
	// Block 0xd9, offset 0x3640
	0x3640: 0x00c2, 0x3641: 0x00c2, 0x3642: 0x00c2, 0x3643: 0x00c2, 0x3644: 0x00c2, 0x3645: 0x00c4,
	0x3646: 0x00c0, 0x3647: 0x00c4, 0x3648: 0x0080, 0x3649: 0x00c4, 0x364a: 0x00c4, 0x364b: 0x00c0,
	0x364c: 0x00c0, 0x364d: 0x00c1, 0x364e: 0x00c4, 0x364f: 0x00c4, 0x3650: 0x00c4, 0x3651: 0x00c4,
	0x3652: 0x00c4, 0x3653: 0x00c2, 0x3654: 0x00c2, 0x3655: 0x00c2, 0x3656: 0x00c2, 0x3657: 0x00c1,
	0x3658: 0x00c2, 0x3659: 0x00c2, 0x365a: 0x00c2, 0x365b: 0x00c2, 0x365c: 0x00c2, 0x365d: 0x00c4,
	0x365e: 0x00c2, 0x365f: 0x00c2, 0x3660: 0x00c2, 0x3661: 0x00c4, 0x3662: 0x00c0, 0x3663: 0x00c0,
	0x3664: 0x00c4, 0x3665: 0x00c3, 0x3666: 0x00c3,
	0x366b: 0x0082, 0x366c: 0x0082, 0x366d: 0x0082, 0x366e: 0x0082, 0x366f: 0x0084,
	0x3670: 0x0080, 0x3671: 0x0080, 0x3672: 0x0080, 0x3673: 0x0080, 0x3674: 0x0080, 0x3675: 0x0080,
	0x3676: 0x0080,
	// Block 0xda, offset 0x3680
	0x3680: 0x00c0, 0x3681: 0x00c0, 0x3682: 0x00c0, 0x3683: 0x00c0, 0x3684: 0x00c0, 0x3685: 0x00c0,
	0x3686: 0x00c0, 0x3687: 0x00c0, 0x3688: 0x00c0, 0x3689: 0x00c0, 0x368a: 0x00c0, 0x368b: 0x00c0,
	0x368c: 0x00c0, 0x368d: 0x00c0, 0x368e: 0x00c0, 0x368f: 0x00c0, 0x3690: 0x00c0, 0x3691: 0x00c0,
	0x3692: 0x00c0, 0x3693: 0x00c0, 0x3694: 0x00c0, 0x3695: 0x00c0, 0x3696: 0x00c0, 0x3697: 0x00c0,
	0x3698: 0x00c0, 0x3699: 0x00c0, 0x369a: 0x00c0, 0x369b: 0x00c0, 0x369c: 0x00c0, 0x369d: 0x00c0,
	0x369e: 0x00c0, 0x369f: 0x00c0, 0x36a0: 0x00c0, 0x36a1: 0x00c0, 0x36a2: 0x00c0, 0x36a3: 0x00c0,
	0x36a4: 0x00c0, 0x36a5: 0x00c0, 0x36a6: 0x00c0, 0x36a7: 0x00c0, 0x36a8: 0x00c0, 0x36a9: 0x00c0,
	0x36aa: 0x00c0, 0x36ab: 0x00c0, 0x36ac: 0x00c0, 0x36ad: 0x00c0, 0x36ae: 0x00c0, 0x36af: 0x00c0,
	0x36b0: 0x00c0, 0x36b1: 0x00c0, 0x36b2: 0x00c0, 0x36b3: 0x00c0, 0x36b4: 0x00c0, 0x36b5: 0x00c0,
	0x36b9: 0x0080, 0x36ba: 0x0080, 0x36bb: 0x0080,
	0x36bc: 0x0080, 0x36bd: 0x0080, 0x36be: 0x0080, 0x36bf: 0x0080,
	// Block 0xdb, offset 0x36c0
	0x36c0: 0x00c0, 0x36c1: 0x00c0, 0x36c2: 0x00c0, 0x36c3: 0x00c0, 0x36c4: 0x00c0, 0x36c5: 0x00c0,
	0x36c6: 0x00c0, 0x36c7: 0x00c0, 0x36c8: 0x00c0, 0x36c9: 0x00c0, 0x36ca: 0x00c0, 0x36cb: 0x00c0,
	0x36cc: 0x00c0, 0x36cd: 0x00c0, 0x36ce: 0x00c0, 0x36cf: 0x00c0, 0x36d0: 0x00c0, 0x36d1: 0x00c0,
	0x36d2: 0x00c0, 0x36d3: 0x00c0, 0x36d4: 0x00c0, 0x36d5: 0x00c0,
	0x36d8: 0x0080, 0x36d9: 0x0080, 0x36da: 0x0080, 0x36db: 0x0080, 0x36dc: 0x0080, 0x36dd: 0x0080,
	0x36de: 0x0080, 0x36df: 0x0080, 0x36e0: 0x00c0, 0x36e1: 0x00c0, 0x36e2: 0x00c0, 0x36e3: 0x00c0,
	0x36e4: 0x00c0, 0x36e5: 0x00c0, 0x36e6: 0x00c0, 0x36e7: 0x00c0, 0x36e8: 0x00c0, 0x36e9: 0x00c0,
	0x36ea: 0x00c0, 0x36eb: 0x00c0, 0x36ec: 0x00c0, 0x36ed: 0x00c0, 0x36ee: 0x00c0, 0x36ef: 0x00c0,
	0x36f0: 0x00c0, 0x36f1: 0x00c0, 0x36f2: 0x00c0,
	0x36f8: 0x0080, 0x36f9: 0x0080, 0x36fa: 0x0080, 0x36fb: 0x0080,
	0x36fc: 0x0080, 0x36fd: 0x0080, 0x36fe: 0x0080, 0x36ff: 0x0080,
	// Block 0xdc, offset 0x3700
	0x3700: 0x00c2, 0x3701: 0x00c4, 0x3702: 0x00c2, 0x3703: 0x00c4, 0x3704: 0x00c4, 0x3705: 0x00c4,
	0x3706: 0x00c2, 0x3707: 0x00c2, 0x3708: 0x00c2, 0x3709: 0x00c4, 0x370a: 0x00c2, 0x370b: 0x00c2,
	0x370c: 0x00c4, 0x370d: 0x00c2, 0x370e: 0x00c4, 0x370f: 0x00c4, 0x3710: 0x00c2, 0x3711: 0x00c4,
	0x3719: 0x0080, 0x371a: 0x0080, 0x371b: 0x0080, 0x371c: 0x0080,
	0x3729: 0x0084,
	0x372a: 0x0084, 0x372b: 0x0084, 0x372c: 0x0084, 0x372d: 0x0082, 0x372e: 0x0082, 0x372f: 0x0080,
	// Block 0xdd, offset 0x3740
	0x3740: 0x00c0, 0x3741: 0x00c0, 0x3742: 0x00c0, 0x3743: 0x00c0, 0x3744: 0x00c0, 0x3745: 0x00c0,
	0x3746: 0x00c0, 0x3747: 0x00c0, 0x3748: 0x00c0,
	// Block 0xde, offset 0x3780
	0x3780: 0x00c0, 0x3781: 0x00c0, 0x3782: 0x00c0, 0x3783: 0x00c0, 0x3784: 0x00c0, 0x3785: 0x00c0,
	0x3786: 0x00c0, 0x3787: 0x00c0, 0x3788: 0x00c0, 0x3789: 0x00c0, 0x378a: 0x00c0, 0x378b: 0x00c0,
	0x378c: 0x00c0, 0x378d: 0x00c0, 0x378e: 0x00c0, 0x378f: 0x00c0, 0x3790: 0x00c0, 0x3791: 0x00c0,
	0x3792: 0x00c0, 0x3793: 0x00c0, 0x3794: 0x00c0, 0x3795: 0x00c0, 0x3796: 0x00c0, 0x3797: 0x00c0,
	0x3798: 0x00c0, 0x3799: 0x00c0, 0x379a: 0x00c0, 0x379b: 0x00c0, 0x379c: 0x00c0, 0x379d: 0x00c0,
	0x379e: 0x00c0, 0x379f: 0x00c0, 0x37a0: 0x00c0, 0x37a1: 0x00c0, 0x37a2: 0x00c0, 0x37a3: 0x00c0,
	0x37a4: 0x00c0, 0x37a5: 0x00c0, 0x37a6: 0x00c0, 0x37a7: 0x00c0, 0x37a8: 0x00c0, 0x37a9: 0x00c0,
	0x37aa: 0x00c0, 0x37ab: 0x00c0, 0x37ac: 0x00c0, 0x37ad: 0x00c0, 0x37ae: 0x00c0, 0x37af: 0x00c0,
	0x37b0: 0x00c0, 0x37b1: 0x00c0, 0x37b2: 0x00c0,
	// Block 0xdf, offset 0x37c0
	0x37c0: 0x00c0, 0x37c1: 0x00c0, 0x37c2: 0x00c0, 0x37c3: 0x00c0, 0x37c4: 0x00c0, 0x37c5: 0x00c0,
	0x37c6: 0x00c0, 0x37c7: 0x00c0, 0x37c8: 0x00c0, 0x37c9: 0x00c0, 0x37ca: 0x00c0, 0x37cb: 0x00c0,
	0x37cc: 0x00c0, 0x37cd: 0x00c0, 0x37ce: 0x00c0, 0x37cf: 0x00c0, 0x37d0: 0x00c0, 0x37d1: 0x00c0,
	0x37d2: 0x00c0, 0x37d3: 0x00c0, 0x37d4: 0x00c0, 0x37d5: 0x00c0, 0x37d6: 0x00c0, 0x37d7: 0x00c0,
	0x37d8: 0x00c0, 0x37d9: 0x00c0, 0x37da: 0x00c0, 0x37db: 0x00c0, 0x37dc: 0x00c0, 0x37dd: 0x00c0,
	0x37de: 0x00c0, 0x37df: 0x00c0, 0x37e0: 0x00c0, 0x37e1: 0x00c0, 0x37e2: 0x00c0, 0x37e3: 0x00c0,
	0x37e4: 0x00c0, 0x37e5: 0x00c0, 0x37e6: 0x00c0, 0x37e7: 0x00c0, 0x37e8: 0x00c0, 0x37e9: 0x00c0,
	0x37ea: 0x00c0, 0x37eb: 0x00c0, 0x37ec: 0x00c0, 0x37ed: 0x00c0, 0x37ee: 0x00c0, 0x37ef: 0x00c0,
	0x37f0: 0x00c0, 0x37f1: 0x00c0, 0x37f2: 0x00c0,
	0x37fa: 0x0080, 0x37fb: 0x0080,
	0x37fc: 0x0080, 0x37fd: 0x0080, 0x37fe: 0x0080, 0x37ff: 0x0080,
	// Block 0xe0, offset 0x3800
	0x3800: 0x00c1, 0x3801: 0x00c2, 0x3802: 0x00c2, 0x3803: 0x00c2, 0x3804: 0x00c2, 0x3805: 0x00c2,
	0x3806: 0x00c2, 0x3807: 0x00c2, 0x3808: 0x00c2, 0x3809: 0x00c2, 0x380a: 0x00c2, 0x380b: 0x00c2,
	0x380c: 0x00c2, 0x380d: 0x00c2, 0x380e: 0x00c2, 0x380f: 0x00c2, 0x3810: 0x00c2, 0x3811: 0x00c2,
	0x3812: 0x00c2, 0x3813: 0x00c2, 0x3814: 0x00c2, 0x3815: 0x00c2, 0x3816: 0x00c2, 0x3817: 0x00c2,
	0x3818: 0x00c2, 0x3819: 0x00c2, 0x381a: 0x00c2, 0x381b: 0x00c2, 0x381c: 0x00c2, 0x381d: 0x00c2,
	0x381e: 0x00c2, 0x381f: 0x00c2, 0x3820: 0x00c2, 0x3821: 0x00c2, 0x3822: 0x00c4, 0x3823: 0x00c2,
	0x3824: 0x00c3, 0x3825: 0x00c3, 0x3826: 0x00c3, 0x3827: 0x00c3,
	0x3830: 0x00c0, 0x3831: 0x00c0, 0x3832: 0x00c0, 0x3833: 0x00c0, 0x3834: 0x00c0, 0x3835: 0x00c0,
	0x3836: 0x00c0, 0x3837: 0x00c0, 0x3838: 0x00c0, 0x3839: 0x00c0,
	// Block 0xe1, offset 0x3840
	0x3860: 0x0080, 0x3861: 0x0080, 0x3862: 0x0080, 0x3863: 0x0080,
	0x3864: 0x0080, 0x3865: 0x0080, 0x3866: 0x0080, 0x3867: 0x0080, 0x3868: 0x0080, 0x3869: 0x0080,
	0x386a: 0x0080, 0x386b: 0x0080, 0x386c: 0x0080, 0x386d: 0x0080, 0x386e: 0x0080, 0x386f: 0x0080,
	0x3870: 0x0080, 0x3871: 0x0080, 0x3872: 0x0080, 0x3873: 0x0080, 0x3874: 0x0080, 0x3875: 0x0080,
	0x3876: 0x0080, 0x3877: 0x0080, 0x3878: 0x0080, 0x3879: 0x0080, 0x387a: 0x0080, 0x387b: 0x0080,
	0x387c: 0x0080, 0x387d: 0x0080, 0x387e: 0x0080,
	// Block 0xe2, offset 0x3880
	0x3880: 0x00c0, 0x3881: 0x00c0, 0x3882: 0x00c0, 0x3883: 0x00c0, 0x3884: 0x00c0, 0x3885: 0x00c0,
	0x3886: 0x00c0, 0x3887: 0x00c0, 0x3888: 0x00c0, 0x3889: 0x00c0, 0x388a: 0x00c0, 0x388b: 0x00c0,
	0x388c: 0x00c0, 0x388d: 0x00c0, 0x388e: 0x00c0, 0x388f: 0x00c0, 0x3890: 0x00c0, 0x3891: 0x00c0,
	0x3892: 0x00c0, 0x3893: 0x00c0, 0x3894: 0x00c0, 0x3895: 0x00c0, 0x3896: 0x00c0, 0x3897: 0x00c0,
	0x3898: 0x00c0, 0x3899: 0x00c0, 0x389a: 0x00c0, 0x389b: 0x00c0, 0x389c: 0x00c0, 0x389d: 0x00c0,
	0x389e: 0x00c0, 0x389f: 0x00c0, 0x38a0: 0x00c0, 0x38a1: 0x00c0, 0x38a2: 0x00c0, 0x38a3: 0x00c0,
	0x38a4: 0x00c0, 0x38a5: 0x00c0, 0x38a6: 0x00c0, 0x38a7: 0x00c0, 0x38a8: 0x00c0, 0x38a9: 0x00c0,
	0x38ab: 0x00c3, 0x38ac: 0x00c3, 0x38ad: 0x0080,
	0x38b0: 0x00c0, 0x38b1: 0x00c0,
	// Block 0xe3, offset 0x38c0
	0x38c0: 0x00c0, 0x38c1: 0x00c0, 0x38c2: 0x00c0, 0x38c3: 0x00c0, 0x38c4: 0x00c0, 0x38c5: 0x00c0,
	0x38c6: 0x00c0, 0x38c7: 0x00c0, 0x38c8: 0x00c0, 0x38c9: 0x00c0, 0x38ca: 0x00c0, 0x38cb: 0x00c0,
	0x38cc: 0x00c0, 0x38cd: 0x00c0, 0x38ce: 0x00c0, 0x38cf: 0x00c0, 0x38d0: 0x00c0, 0x38d1: 0x00c0,
	0x38d2: 0x00c0, 0x38d3: 0x00c0, 0x38d4: 0x00c0, 0x38d5: 0x00c0, 0x38d6: 0x00c0, 0x38d7: 0x00c0,
	0x38d8: 0x00c0, 0x38d9: 0x00c0, 0x38da: 0x00c0, 0x38db: 0x00c0, 0x38dc: 0x00c0, 0x38dd: 0x0080,
	0x38de: 0x0080, 0x38df: 0x0080, 0x38e0: 0x0080, 0x38e1: 0x0080, 0x38e2: 0x0080, 0x38e3: 0x0080,
	0x38e4: 0x0080, 0x38e5: 0x0080, 0x38e6: 0x0080, 0x38e7: 0x00c0,
	0x38f0: 0x00c2, 0x38f1: 0x00c2, 0x38f2: 0x00c2, 0x38f3: 0x00c4, 0x38f4: 0x00c2, 0x38f5: 0x00c2,
	0x38f6: 0x00c2, 0x38f7: 0x00c2, 0x38f8: 0x00c2, 0x38f9: 0x00c2, 0x38fa: 0x00c2, 0x38fb: 0x00c2,
	0x38fc: 0x00c2, 0x38fd: 0x00c2, 0x38fe: 0x00c2, 0x38ff: 0x00c2,
	// Block 0xe4, offset 0x3900
	0x3900: 0x00c2, 0x3901: 0x00c2, 0x3902: 0x00c2, 0x3903: 0x00c2, 0x3904: 0x00c2, 0x3905: 0x00c0,
	0x3906: 0x00c3, 0x3907: 0x00c3, 0x3908: 0x00c3, 0x3909: 0x00c3, 0x390a: 0x00c3, 0x390b: 0x00c3,
	0x390c: 0x00c3, 0x390d: 0x00c3, 0x390e: 0x00c3, 0x390f: 0x00c3, 0x3910: 0x00c3, 0x3911: 0x0082,
	0x3912: 0x0082, 0x3913: 0x0082, 0x3914: 0x0084, 0x3915: 0x0080, 0x3916: 0x0080, 0x3917: 0x0080,
	0x3918: 0x0080, 0x3919: 0x0080,
	// Block 0xe5, offset 0x3940
	0x3970: 0x00c2, 0x3971: 0x00c0, 0x3972: 0x00c2, 0x3973: 0x00c2, 0x3974: 0x00c4, 0x3975: 0x00c4,
	0x3976: 0x00c4, 0x3977: 0x00c0, 0x3978: 0x00c2, 0x3979: 0x00c4, 0x397a: 0x00c4, 0x397b: 0x00c2,
	0x397c: 0x00c2, 0x397d: 0x00c4, 0x397e: 0x00c2, 0x397f: 0x00c2,
	// Block 0xe6, offset 0x3980
	0x3980: 0x00c0, 0x3981: 0x00c2, 0x3982: 0x00c4, 0x3983: 0x00c4, 0x3984: 0x00c2, 0x3985: 0x0080,
	0x3986: 0x0080, 0x3987: 0x0080, 0x3988: 0x0080, 0x3989: 0x0084, 0x398a: 0x0082, 0x398b: 0x0081,
	0x39a0: 0x00c0, 0x39a1: 0x00c0, 0x39a2: 0x00c0, 0x39a3: 0x00c0,
	0x39a4: 0x00c0, 0x39a5: 0x00c0, 0x39a6: 0x00c0, 0x39a7: 0x00c0, 0x39a8: 0x00c0, 0x39a9: 0x00c0,
	0x39aa: 0x00c0, 0x39ab: 0x00c0, 0x39ac: 0x00c0, 0x39ad: 0x00c0, 0x39ae: 0x00c0, 0x39af: 0x00c0,
	0x39b0: 0x00c0, 0x39b1: 0x00c0, 0x39b2: 0x00c0, 0x39b3: 0x00c0, 0x39b4: 0x00c0, 0x39b5: 0x00c0,
	0x39b6: 0x00c0,
	// Block 0xe7, offset 0x39c0
	0x39c0: 0x00c0, 0x39c1: 0x00c3, 0x39c2: 0x00c0, 0x39c3: 0x00c0, 0x39c4: 0x00c0, 0x39c5: 0x00c0,
	0x39c6: 0x00c0, 0x39c7: 0x00c0, 0x39c8: 0x00c0, 0x39c9: 0x00c0, 0x39ca: 0x00c0, 0x39cb: 0x00c0,
	0x39cc: 0x00c0, 0x39cd: 0x00c0, 0x39ce: 0x00c0, 0x39cf: 0x00c0, 0x39d0: 0x00c0, 0x39d1: 0x00c0,
	0x39d2: 0x00c0, 0x39d3: 0x00c0, 0x39d4: 0x00c0, 0x39d5: 0x00c0, 0x39d6: 0x00c0, 0x39d7: 0x00c0,
	0x39d8: 0x00c0, 0x39d9: 0x00c0, 0x39da: 0x00c0, 0x39db: 0x00c0, 0x39dc: 0x00c0, 0x39dd: 0x00c0,
	0x39de: 0x00c0, 0x39df: 0x00c0, 0x39e0: 0x00c0, 0x39e1: 0x00c0, 0x39e2: 0x00c0, 0x39e3: 0x00c0,
	0x39e4: 0x00c0, 0x39e5: 0x00c0, 0x39e6: 0x00c0, 0x39e7: 0x00c0, 0x39e8: 0x00c0, 0x39e9: 0x00c0,
	0x39ea: 0x00c0, 0x39eb: 0x00c0, 0x39ec: 0x00c0, 0x39ed: 0x00c0, 0x39ee: 0x00c0, 0x39ef: 0x00c0,
	0x39f0: 0x00c0, 0x39f1: 0x00c0, 0x39f2: 0x00c0, 0x39f3: 0x00c0, 0x39f4: 0x00c0, 0x39f5: 0x00c0,
	0x39f6: 0x00c0, 0x39f7: 0x00c0, 0x39f8: 0x00c3, 0x39f9: 0x00c3, 0x39fa: 0x00c3, 0x39fb: 0x00c3,
	0x39fc: 0x00c3, 0x39fd: 0x00c3, 0x39fe: 0x00c3, 0x39ff: 0x00c3,
	// Block 0xe8, offset 0x3a00
	0x3a00: 0x00c3, 0x3a01: 0x00c3, 0x3a02: 0x00c3, 0x3a03: 0x00c3, 0x3a04: 0x00c3, 0x3a05: 0x00c3,
	0x3a06: 0x00c6, 0x3a07: 0x0080, 0x3a08: 0x0080, 0x3a09: 0x0080, 0x3a0a: 0x0080, 0x3a0b: 0x0080,
	0x3a0c: 0x0080, 0x3a0d: 0x0080,
	0x3a12: 0x0080, 0x3a13: 0x0080, 0x3a14: 0x0080, 0x3a15: 0x0080, 0x3a16: 0x0080, 0x3a17: 0x0080,
	0x3a18: 0x0080, 0x3a19: 0x0080, 0x3a1a: 0x0080, 0x3a1b: 0x0080, 0x3a1c: 0x0080, 0x3a1d: 0x0080,
	0x3a1e: 0x0080, 0x3a1f: 0x0080, 0x3a20: 0x0080, 0x3a21: 0x0080, 0x3a22: 0x0080, 0x3a23: 0x0080,
	0x3a24: 0x0080, 0x3a25: 0x0080, 0x3a26: 0x00c0, 0x3a27: 0x00c0, 0x3a28: 0x00c0, 0x3a29: 0x00c0,
	0x3a2a: 0x00c0, 0x3a2b: 0x00c0, 0x3a2c: 0x00c0, 0x3a2d: 0x00c0, 0x3a2e: 0x00c0, 0x3a2f: 0x00c0,
	0x3a3f: 0x00c6,
	// Block 0xe9, offset 0x3a40
	0x3a40: 0x00c3, 0x3a41: 0x00c3, 0x3a42: 0x00c0, 0x3a43: 0x00c0, 0x3a44: 0x00c0, 0x3a45: 0x00c0,
	0x3a46: 0x00c0, 0x3a47: 0x00c0, 0x3a48: 0x00c0, 0x3a49: 0x00c0, 0x3a4a: 0x00c0, 0x3a4b: 0x00c0,
	0x3a4c: 0x00c0, 0x3a4d: 0x00c0, 0x3a4e: 0x00c0, 0x3a4f: 0x00c0, 0x3a50: 0x00c0, 0x3a51: 0x00c0,
	0x3a52: 0x00c0, 0x3a53: 0x00c0, 0x3a54: 0x00c0, 0x3a55: 0x00c0, 0x3a56: 0x00c0, 0x3a57: 0x00c0,
	0x3a58: 0x00c0, 0x3a59: 0x00c0, 0x3a5a: 0x00c0, 0x3a5b: 0x00c0, 0x3a5c: 0x00c0, 0x3a5d: 0x00c0,
	0x3a5e: 0x00c0, 0x3a5f: 0x00c0, 0x3a60: 0x00c0, 0x3a61: 0x00c0, 0x3a62: 0x00c0, 0x3a63: 0x00c0,
	0x3a64: 0x00c0, 0x3a65: 0x00c0, 0x3a66: 0x00c0, 0x3a67: 0x00c0, 0x3a68: 0x00c0, 0x3a69: 0x00c0,
	0x3a6a: 0x00c0, 0x3a6b: 0x00c0, 0x3a6c: 0x00c0, 0x3a6d: 0x00c0, 0x3a6e: 0x00c0, 0x3a6f: 0x00c0,
	0x3a70: 0x00c0, 0x3a71: 0x00c0, 0x3a72: 0x00c0, 0x3a73: 0x00c3, 0x3a74: 0x00c3, 0x3a75: 0x00c3,
	0x3a76: 0x00c3, 0x3a77: 0x00c0, 0x3a78: 0x00c0, 0x3a79: 0x00c6, 0x3a7a: 0x00c3, 0x3a7b: 0x0080,
	0x3a7c: 0x0080, 0x3a7d: 0x0040, 0x3a7e: 0x0080, 0x3a7f: 0x0080,
	// Block 0xea, offset 0x3a80
	0x3a80: 0x0080, 0x3a81: 0x0080,
	0x3a8d: 0x0040, 0x3a90: 0x00c0, 0x3a91: 0x00c0,
	0x3a92: 0x00c0, 0x3a93: 0x00c0, 0x3a94: 0x00c0, 0x3a95: 0x00c0, 0x3a96: 0x00c0, 0x3a97: 0x00c0,
	0x3a98: 0x00c0, 0x3a99: 0x00c0, 0x3a9a: 0x00c0, 0x3a9b: 0x00c0, 0x3a9c: 0x00c0, 0x3a9d: 0x00c0,
	0x3a9e: 0x00c0, 0x3a9f: 0x00c0, 0x3aa0: 0x00c0, 0x3aa1: 0x00c0, 0x3aa2: 0x00c0, 0x3aa3: 0x00c0,
	0x3aa4: 0x00c0, 0x3aa5: 0x00c0, 0x3aa6: 0x00c0, 0x3aa7: 0x00c0, 0x3aa8: 0x00c0,
	0x3ab0: 0x00c0, 0x3ab1: 0x00c0, 0x3ab2: 0x00c0, 0x3ab3: 0x00c0, 0x3ab4: 0x00c0, 0x3ab5: 0x00c0,
	0x3ab6: 0x00c0, 0x3ab7: 0x00c0, 0x3ab8: 0x00c0, 0x3ab9: 0x00c0,
	// Block 0xeb, offset 0x3ac0
	0x3ac0: 0x00c3, 0x3ac1: 0x00c3, 0x3ac2: 0x00c3, 0x3ac3: 0x00c0, 0x3ac4: 0x00c0, 0x3ac5: 0x00c0,
	0x3ac6: 0x00c0, 0x3ac7: 0x00c0, 0x3ac8: 0x00c0, 0x3ac9: 0x00c0, 0x3aca: 0x00c0, 0x3acb: 0x00c0,
	0x3acc: 0x00c0, 0x3acd: 0x00c0, 0x3ace: 0x00c0, 0x3acf: 0x00c0, 0x3ad0: 0x00c0, 0x3ad1: 0x00c0,
	0x3ad2: 0x00c0, 0x3ad3: 0x00c0, 0x3ad4: 0x00c0, 0x3ad5: 0x00c0, 0x3ad6: 0x00c0, 0x3ad7: 0x00c0,
	0x3ad8: 0x00c0, 0x3ad9: 0x00c0, 0x3ada: 0x00c0, 0x3adb: 0x00c0, 0x3adc: 0x00c0, 0x3add: 0x00c0,
	0x3ade: 0x00c0, 0x3adf: 0x00c0, 0x3ae0: 0x00c0, 0x3ae1: 0x00c0, 0x3ae2: 0x00c0, 0x3ae3: 0x00c0,
	0x3ae4: 0x00c0, 0x3ae5: 0x00c0, 0x3ae6: 0x00c0, 0x3ae7: 0x00c3, 0x3ae8: 0x00c3, 0x3ae9: 0x00c3,
	0x3aea: 0x00c3, 0x3aeb: 0x00c3, 0x3aec: 0x00c0, 0x3aed: 0x00c3, 0x3aee: 0x00c3, 0x3aef: 0x00c3,
	0x3af0: 0x00c3, 0x3af1: 0x00c3, 0x3af2: 0x00c3, 0x3af3: 0x00c6, 0x3af4: 0x00c6,
	0x3af6: 0x00c0, 0x3af7: 0x00c0, 0x3af8: 0x00c0, 0x3af9: 0x00c0, 0x3afa: 0x00c0, 0x3afb: 0x00c0,
	0x3afc: 0x00c0, 0x3afd: 0x00c0, 0x3afe: 0x00c0, 0x3aff: 0x00c0,
	// Block 0xec, offset 0x3b00
	0x3b00: 0x0080, 0x3b01: 0x0080, 0x3b02: 0x0080, 0x3b03: 0x0080, 0x3b04: 0x00c0, 0x3b05: 0x00c0,
	0x3b06: 0x00c0, 0x3b07: 0x00c0,
	0x3b10: 0x00c0, 0x3b11: 0x00c0,
	0x3b12: 0x00c0, 0x3b13: 0x00c0, 0x3b14: 0x00c0, 0x3b15: 0x00c0, 0x3b16: 0x00c0, 0x3b17: 0x00c0,
	0x3b18: 0x00c0, 0x3b19: 0x00c0, 0x3b1a: 0x00c0, 0x3b1b: 0x00c0, 0x3b1c: 0x00c0, 0x3b1d: 0x00c0,
	0x3b1e: 0x00c0, 0x3b1f: 0x00c0, 0x3b20: 0x00c0, 0x3b21: 0x00c0, 0x3b22: 0x00c0, 0x3b23: 0x00c0,
	0x3b24: 0x00c0, 0x3b25: 0x00c0, 0x3b26: 0x00c0, 0x3b27: 0x00c0, 0x3b28: 0x00c0, 0x3b29: 0x00c0,
	0x3b2a: 0x00c0, 0x3b2b: 0x00c0, 0x3b2c: 0x00c0, 0x3b2d: 0x00c0, 0x3b2e: 0x00c0, 0x3b2f: 0x00c0,
	0x3b30: 0x00c0, 0x3b31: 0x00c0, 0x3b32: 0x00c0, 0x3b33: 0x00c3, 0x3b34: 0x0080, 0x3b35: 0x0080,
	0x3b36: 0x00c0,
	// Block 0xed, offset 0x3b40
	0x3b40: 0x00c3, 0x3b41: 0x00c3, 0x3b42: 0x00c0, 0x3b43: 0x00c0, 0x3b44: 0x00c0, 0x3b45: 0x00c0,
	0x3b46: 0x00c0, 0x3b47: 0x00c0, 0x3b48: 0x00c0, 0x3b49: 0x00c0, 0x3b4a: 0x00c0, 0x3b4b: 0x00c0,
	0x3b4c: 0x00c0, 0x3b4d: 0x00c0, 0x3b4e: 0x00c0, 0x3b4f: 0x00c0, 0x3b50: 0x00c0, 0x3b51: 0x00c0,
	0x3b52: 0x00c0, 0x3b53: 0x00c0, 0x3b54: 0x00c0, 0x3b55: 0x00c0, 0x3b56: 0x00c0, 0x3b57: 0x00c0,
	0x3b58: 0x00c0, 0x3b59: 0x00c0, 0x3b5a: 0x00c0, 0x3b5b: 0x00c0, 0x3b5c: 0x00c0, 0x3b5d: 0x00c0,
	0x3b5e: 0x00c0, 0x3b5f: 0x00c0, 0x3b60: 0x00c0, 0x3b61: 0x00c0, 0x3b62: 0x00c0, 0x3b63: 0x00c0,
	0x3b64: 0x00c0, 0x3b65: 0x00c0, 0x3b66: 0x00c0, 0x3b67: 0x00c0, 0x3b68: 0x00c0, 0x3b69: 0x00c0,
	0x3b6a: 0x00c0, 0x3b6b: 0x00c0, 0x3b6c: 0x00c0, 0x3b6d: 0x00c0, 0x3b6e: 0x00c0, 0x3b6f: 0x00c0,
	0x3b70: 0x00c0, 0x3b71: 0x00c0, 0x3b72: 0x00c0, 0x3b73: 0x00c0, 0x3b74: 0x00c0, 0x3b75: 0x00c0,
	0x3b76: 0x00c3, 0x3b77: 0x00c3, 0x3b78: 0x00c3, 0x3b79: 0x00c3, 0x3b7a: 0x00c3, 0x3b7b: 0x00c3,
	0x3b7c: 0x00c3, 0x3b7d: 0x00c3, 0x3b7e: 0x00c3, 0x3b7f: 0x00c0,
	// Block 0xee, offset 0x3b80
	0x3b80: 0x00c5, 0x3b81: 0x00c0, 0x3b82: 0x00c0, 0x3b83: 0x00c0, 0x3b84: 0x00c0, 0x3b85: 0x0080,
	0x3b86: 0x0080, 0x3b87: 0x0080, 0x3b88: 0x0080, 0x3b89: 0x00c3, 0x3b8a: 0x00c3, 0x3b8b: 0x00c3,
	0x3b8c: 0x00c3, 0x3b8d: 0x0080, 0x3b8e: 0x00c0, 0x3b8f: 0x00c3, 0x3b90: 0x00c0, 0x3b91: 0x00c0,
	0x3b92: 0x00c0, 0x3b93: 0x00c0, 0x3b94: 0x00c0, 0x3b95: 0x00c0, 0x3b96: 0x00c0, 0x3b97: 0x00c0,
	0x3b98: 0x00c0, 0x3b99: 0x00c0, 0x3b9a: 0x00c0, 0x3b9b: 0x0080, 0x3b9c: 0x00c0, 0x3b9d: 0x0080,
	0x3b9e: 0x0080, 0x3b9f: 0x0080, 0x3ba1: 0x0080, 0x3ba2: 0x0080, 0x3ba3: 0x0080,
	0x3ba4: 0x0080, 0x3ba5: 0x0080, 0x3ba6: 0x0080, 0x3ba7: 0x0080, 0x3ba8: 0x0080, 0x3ba9: 0x0080,
	0x3baa: 0x0080, 0x3bab: 0x0080, 0x3bac: 0x0080, 0x3bad: 0x0080, 0x3bae: 0x0080, 0x3baf: 0x0080,
	0x3bb0: 0x0080, 0x3bb1: 0x0080, 0x3bb2: 0x0080, 0x3bb3: 0x0080, 0x3bb4: 0x0080,
	// Block 0xef, offset 0x3bc0
	0x3bc0: 0x00c0, 0x3bc1: 0x00c0, 0x3bc2: 0x00c0, 0x3bc3: 0x00c0, 0x3bc4: 0x00c0, 0x3bc5: 0x00c0,
	0x3bc6: 0x00c0, 0x3bc7: 0x00c0, 0x3bc8: 0x00c0, 0x3bc9: 0x00c0, 0x3bca: 0x00c0, 0x3bcb: 0x00c0,
	0x3bcc: 0x00c0, 0x3bcd: 0x00c0, 0x3bce: 0x00c0, 0x3bcf: 0x00c0, 0x3bd0: 0x00c0, 0x3bd1: 0x00c0,
	0x3bd3: 0x00c0, 0x3bd4: 0x00c0, 0x3bd5: 0x00c0, 0x3bd6: 0x00c0, 0x3bd7: 0x00c0,
	0x3bd8: 0x00c0, 0x3bd9: 0x00c0, 0x3bda: 0x00c0, 0x3bdb: 0x00c0, 0x3bdc: 0x00c0, 0x3bdd: 0x00c0,
	0x3bde: 0x00c0, 0x3bdf: 0x00c0, 0x3be0: 0x00c0, 0x3be1: 0x00c0, 0x3be2: 0x00c0, 0x3be3: 0x00c0,
	0x3be4: 0x00c0, 0x3be5: 0x00c0, 0x3be6: 0x00c0, 0x3be7: 0x00c0, 0x3be8: 0x00c0, 0x3be9: 0x00c0,
	0x3bea: 0x00c0, 0x3beb: 0x00c0, 0x3bec: 0x00c0, 0x3bed: 0x00c0, 0x3bee: 0x00c0, 0x3bef: 0x00c3,
	0x3bf0: 0x00c3, 0x3bf1: 0x00c3, 0x3bf2: 0x00c0, 0x3bf3: 0x00c0, 0x3bf4: 0x00c3, 0x3bf5: 0x00c5,
	0x3bf6: 0x00c3, 0x3bf7: 0x00c3, 0x3bf8: 0x0080, 0x3bf9: 0x0080, 0x3bfa: 0x0080, 0x3bfb: 0x0080,
	0x3bfc: 0x0080, 0x3bfd: 0x0080, 0x3bfe: 0x00c3,
	// Block 0xf0, offset 0x3c00
	0x3c00: 0x00c0, 0x3c01: 0x00c0, 0x3c02: 0x00c0, 0x3c03: 0x00c0, 0x3c04: 0x00c0, 0x3c05: 0x00c0,
	0x3c06: 0x00c0, 0x3c08: 0x00c0, 0x3c0a: 0x00c0, 0x3c0b: 0x00c0,
	0x3c0c: 0x00c0, 0x3c0d: 0x00c0, 0x3c0f: 0x00c0, 0x3c10: 0x00c0, 0x3c11: 0x00c0,
	0x3c12: 0x00c0, 0x3c13: 0x00c0, 0x3c14: 0x00c0, 0x3c15: 0x00c0, 0x3c16: 0x00c0, 0x3c17: 0x00c0,
	0x3c18: 0x00c0, 0x3c19: 0x00c0, 0x3c1a: 0x00c0, 0x3c1b: 0x00c0, 0x3c1c: 0x00c0, 0x3c1d: 0x00c0,
	0x3c1f: 0x00c0, 0x3c20: 0x00c0, 0x3c21: 0x00c0, 0x3c22: 0x00c0, 0x3c23: 0x00c0,
	0x3c24: 0x00c0, 0x3c25: 0x00c0, 0x3c26: 0x00c0, 0x3c27: 0x00c0, 0x3c28: 0x00c0, 0x3c29: 0x0080,
	0x3c30: 0x00c0, 0x3c31: 0x00c0, 0x3c32: 0x00c0, 0x3c33: 0x00c0, 0x3c34: 0x00c0, 0x3c35: 0x00c0,
	0x3c36: 0x00c0, 0x3c37: 0x00c0, 0x3c38: 0x00c0, 0x3c39: 0x00c0, 0x3c3a: 0x00c0, 0x3c3b: 0x00c0,
	0x3c3c: 0x00c0, 0x3c3d: 0x00c0, 0x3c3e: 0x00c0, 0x3c3f: 0x00c0,
	// Block 0xf1, offset 0x3c40
	0x3c40: 0x00c0, 0x3c41: 0x00c0, 0x3c42: 0x00c0, 0x3c43: 0x00c0, 0x3c44: 0x00c0, 0x3c45: 0x00c0,
	0x3c46: 0x00c0, 0x3c47: 0x00c0, 0x3c48: 0x00c0, 0x3c49: 0x00c0, 0x3c4a: 0x00c0, 0x3c4b: 0x00c0,
	0x3c4c: 0x00c0, 0x3c4d: 0x00c0, 0x3c4e: 0x00c0, 0x3c4f: 0x00c0, 0x3c50: 0x00c0, 0x3c51: 0x00c0,
	0x3c52: 0x00c0, 0x3c53: 0x00c0, 0x3c54: 0x00c0, 0x3c55: 0x00c0, 0x3c56: 0x00c0, 0x3c57: 0x00c0,
	0x3c58: 0x00c0, 0x3c59: 0x00c0, 0x3c5a: 0x00c0, 0x3c5b: 0x00c0, 0x3c5c: 0x00c0, 0x3c5d: 0x00c0,
	0x3c5e: 0x00c0, 0x3c5f: 0x00c3, 0x3c60: 0x00c0, 0x3c61: 0x00c0, 0x3c62: 0x00c0, 0x3c63: 0x00c3,
	0x3c64: 0x00c3, 0x3c65: 0x00c3, 0x3c66: 0x00c3, 0x3c67: 0x00c3, 0x3c68: 0x00c3, 0x3c69: 0x00c3,
	0x3c6a: 0x00c6,
	0x3c70: 0x00c0, 0x3c71: 0x00c0, 0x3c72: 0x00c0, 0x3c73: 0x00c0, 0x3c74: 0x00c0, 0x3c75: 0x00c0,
	0x3c76: 0x00c0, 0x3c77: 0x00c0, 0x3c78: 0x00c0, 0x3c79: 0x00c0,
	// Block 0xf2, offset 0x3c80
	0x3c80: 0x00c3, 0x3c81: 0x00c3, 0x3c82: 0x00c0, 0x3c83: 0x00c0, 0x3c85: 0x00c0,
	0x3c86: 0x00c0, 0x3c87: 0x00c0, 0x3c88: 0x00c0, 0x3c89: 0x00c0, 0x3c8a: 0x00c0, 0x3c8b: 0x00c0,
	0x3c8c: 0x00c0, 0x3c8f: 0x00c0, 0x3c90: 0x00c0,
	0x3c93: 0x00c0, 0x3c94: 0x00c0, 0x3c95: 0x00c0, 0x3c96: 0x00c0, 0x3c97: 0x00c0,
	0x3c98: 0x00c0, 0x3c99: 0x00c0, 0x3c9a: 0x00c0, 0x3c9b: 0x00c0, 0x3c9c: 0x00c0, 0x3c9d: 0x00c0,
	0x3c9e: 0x00c0, 0x3c9f: 0x00c0, 0x3ca0: 0x00c0, 0x3ca1: 0x00c0, 0x3ca2: 0x00c0, 0x3ca3: 0x00c0,
	0x3ca4: 0x00c0, 0x3ca5: 0x00c0, 0x3ca6: 0x00c0, 0x3ca7: 0x00c0, 0x3ca8: 0x00c0,
	0x3caa: 0x00c0, 0x3cab: 0x00c0, 0x3cac: 0x00c0, 0x3cad: 0x00c0, 0x3cae: 0x00c0, 0x3caf: 0x00c0,
	0x3cb0: 0x00c0, 0x3cb2: 0x00c0, 0x3cb3: 0x00c0, 0x3cb5: 0x00c0,
	0x3cb6: 0x00c0, 0x3cb7: 0x00c0, 0x3cb8: 0x00c0, 0x3cb9: 0x00c0, 0x3cbb: 0x00c3,
	0x3cbc: 0x00c3, 0x3cbd: 0x00c0, 0x3cbe: 0x00c0, 0x3cbf: 0x00c0,
	// Block 0xf3, offset 0x3cc0
	0x3cc0: 0x00c3, 0x3cc1: 0x00c0, 0x3cc2: 0x00c0, 0x3cc3: 0x00c0, 0x3cc4: 0x00c0,
	0x3cc7: 0x00c0, 0x3cc8: 0x00c0, 0x3ccb: 0x00c0,
	0x3ccc: 0x00c0, 0x3ccd: 0x00c5, 0x3cd0: 0x00c0,
	0x3cd7: 0x00c0,
	0x3cdd: 0x00c0,
	0x3cde: 0x00c0, 0x3cdf: 0x00c0, 0x3ce0: 0x00c0, 0x3ce1: 0x00c0, 0x3ce2: 0x00c0, 0x3ce3: 0x00c0,
	0x3ce6: 0x00c3, 0x3ce7: 0x00c3, 0x3ce8: 0x00c3, 0x3ce9: 0x00c3,
	0x3cea: 0x00c3, 0x3ceb: 0x00c3, 0x3cec: 0x00c3,
	0x3cf0: 0x00c3, 0x3cf1: 0x00c3, 0x3cf2: 0x00c3, 0x3cf3: 0x00c3, 0x3cf4: 0x00c3,
	// Block 0xf4, offset 0x3d00
	0x3d00: 0x00c0, 0x3d01: 0x00c0, 0x3d02: 0x00c0, 0x3d03: 0x00c0, 0x3d04: 0x00c0, 0x3d05: 0x00c0,
	0x3d06: 0x00c0, 0x3d07: 0x00c0, 0x3d08: 0x00c0, 0x3d09: 0x00c0, 0x3d0a: 0x00c0, 0x3d0b: 0x00c0,
	0x3d0c: 0x00c0, 0x3d0d: 0x00c0, 0x3d0e: 0x00c0, 0x3d0f: 0x00c0, 0x3d10: 0x00c0, 0x3d11: 0x00c0,
	0x3d12: 0x00c0, 0x3d13: 0x00c0, 0x3d14: 0x00c0, 0x3d15: 0x00c0, 0x3d16: 0x00c0, 0x3d17: 0x00c0,
	0x3d18: 0x00c0, 0x3d19: 0x00c0, 0x3d1a: 0x00c0, 0x3d1b: 0x00c0, 0x3d1c: 0x00c0, 0x3d1d: 0x00c0,
	0x3d1e: 0x00c0, 0x3d1f: 0x00c0, 0x3d20: 0x00c0, 0x3d21: 0x00c0, 0x3d22: 0x00c0, 0x3d23: 0x00c0,
	0x3d24: 0x00c0, 0x3d25: 0x00c0, 0x3d26: 0x00c0, 0x3d27: 0x00c0, 0x3d28: 0x00c0, 0x3d29: 0x00c0,
	0x3d2a: 0x00c0, 0x3d2b: 0x00c0, 0x3d2c: 0x00c0, 0x3d2d: 0x00c0, 0x3d2e: 0x00c0, 0x3d2f: 0x00c0,
	0x3d30: 0x00c0, 0x3d31: 0x00c0, 0x3d32: 0x00c0, 0x3d33: 0x00c0, 0x3d34: 0x00c0, 0x3d35: 0x00c0,
	0x3d36: 0x00c0, 0x3d37: 0x00c0, 0x3d38: 0x00c3, 0x3d39: 0x00c3, 0x3d3a: 0x00c3, 0x3d3b: 0x00c3,
	0x3d3c: 0x00c3, 0x3d3d: 0x00c3, 0x3d3e: 0x00c3, 0x3d3f: 0x00c3,
	// Block 0xf5, offset 0x3d40
	0x3d40: 0x00c0, 0x3d41: 0x00c0, 0x3d42: 0x00c6, 0x3d43: 0x00c3, 0x3d44: 0x00c3, 0x3d45: 0x00c0,
	0x3d46: 0x00c3, 0x3d47: 0x00c0, 0x3d48: 0x00c0, 0x3d49: 0x00c0, 0x3d4a: 0x00c0, 0x3d4b: 0x0080,
	0x3d4c: 0x0080, 0x3d4d: 0x0080, 0x3d4e: 0x0080, 0x3d4f: 0x0080, 0x3d50: 0x00c0, 0x3d51: 0x00c0,
	0x3d52: 0x00c0, 0x3d53: 0x00c0, 0x3d54: 0x00c0, 0x3d55: 0x00c0, 0x3d56: 0x00c0, 0x3d57: 0x00c0,
	0x3d58: 0x00c0, 0x3d59: 0x00c0, 0x3d5a: 0x0080, 0x3d5b: 0x0080, 0x3d5d: 0x0080,
	0x3d5e: 0x00c3, 0x3d5f: 0x00c0, 0x3d60: 0x00c0, 0x3d61: 0x00c0,
	// Block 0xf6, offset 0x3d80
	0x3d80: 0x00c0, 0x3d81: 0x00c0, 0x3d82: 0x00c0, 0x3d83: 0x00c0, 0x3d84: 0x00c0, 0x3d85: 0x00c0,
	0x3d86: 0x00c0, 0x3d87: 0x00c0, 0x3d88: 0x00c0, 0x3d89: 0x00c0, 0x3d8a: 0x00c0, 0x3d8b: 0x00c0,
	0x3d8c: 0x00c0, 0x3d8d: 0x00c0, 0x3d8e: 0x00c0, 0x3d8f: 0x00c0, 0x3d90: 0x00c0, 0x3d91: 0x00c0,
	0x3d92: 0x00c0, 0x3d93: 0x00c0, 0x3d94: 0x00c0, 0x3d95: 0x00c0, 0x3d96: 0x00c0, 0x3d97: 0x00c0,
	0x3d98: 0x00c0, 0x3d99: 0x00c0, 0x3d9a: 0x00c0, 0x3d9b: 0x00c0, 0x3d9c: 0x00c0, 0x3d9d: 0x00c0,
	0x3d9e: 0x00c0, 0x3d9f: 0x00c0, 0x3da0: 0x00c0, 0x3da1: 0x00c0, 0x3da2: 0x00c0, 0x3da3: 0x00c0,
	0x3da4: 0x00c0, 0x3da5: 0x00c0, 0x3da6: 0x00c0, 0x3da7: 0x00c0, 0x3da8: 0x00c0, 0x3da9: 0x00c0,
	0x3daa: 0x00c0, 0x3dab: 0x00c0, 0x3dac: 0x00c0, 0x3dad: 0x00c0, 0x3dae: 0x00c0, 0x3daf: 0x00c0,
	0x3db0: 0x00c0, 0x3db1: 0x00c0, 0x3db2: 0x00c0, 0x3db3: 0x00c3, 0x3db4: 0x00c3, 0x3db5: 0x00c3,
	0x3db6: 0x00c3, 0x3db7: 0x00c3, 0x3db8: 0x00c3, 0x3db9: 0x00c0, 0x3dba: 0x00c3, 0x3dbb: 0x00c0,
	0x3dbc: 0x00c0, 0x3dbd: 0x00c0, 0x3dbe: 0x00c0, 0x3dbf: 0x00c3,
	// Block 0xf7, offset 0x3dc0
	0x3dc0: 0x00c3, 0x3dc1: 0x00c0, 0x3dc2: 0x00c6, 0x3dc3: 0x00c3, 0x3dc4: 0x00c0, 0x3dc5: 0x00c0,
	0x3dc6: 0x0080, 0x3dc7: 0x00c0,
	0x3dd0: 0x00c0, 0x3dd1: 0x00c0,
	0x3dd2: 0x00c0, 0x3dd3: 0x00c0, 0x3dd4: 0x00c0, 0x3dd5: 0x00c0, 0x3dd6: 0x00c0, 0x3dd7: 0x00c0,
	0x3dd8: 0x00c0, 0x3dd9: 0x00c0,
	// Block 0xf8, offset 0x3e00
	0x3e00: 0x00c0, 0x3e01: 0x00c0, 0x3e02: 0x00c0, 0x3e03: 0x00c0, 0x3e04: 0x00c0, 0x3e05: 0x00c0,
	0x3e06: 0x00c0, 0x3e07: 0x00c0, 0x3e08: 0x00c0, 0x3e09: 0x00c0, 0x3e0a: 0x00c0, 0x3e0b: 0x00c0,
	0x3e0c: 0x00c0, 0x3e0d: 0x00c0, 0x3e0e: 0x00c0, 0x3e0f: 0x00c0, 0x3e10: 0x00c0, 0x3e11: 0x00c0,
	0x3e12: 0x00c0, 0x3e13: 0x00c0, 0x3e14: 0x00c0, 0x3e15: 0x00c0, 0x3e16: 0x00c0, 0x3e17: 0x00c0,
	0x3e18: 0x00c0, 0x3e19: 0x00c0, 0x3e1a: 0x00c0, 0x3e1b: 0x00c0, 0x3e1c: 0x00c0, 0x3e1d: 0x00c0,
	0x3e1e: 0x00c0, 0x3e1f: 0x00c0, 0x3e20: 0x00c0, 0x3e21: 0x00c0, 0x3e22: 0x00c0, 0x3e23: 0x00c0,
	0x3e24: 0x00c0, 0x3e25: 0x00c0, 0x3e26: 0x00c0, 0x3e27: 0x00c0, 0x3e28: 0x00c0, 0x3e29: 0x00c0,
	0x3e2a: 0x00c0, 0x3e2b: 0x00c0, 0x3e2c: 0x00c0, 0x3e2d: 0x00c0, 0x3e2e: 0x00c0, 0x3e2f: 0x00c0,
	0x3e30: 0x00c0, 0x3e31: 0x00c0, 0x3e32: 0x00c3, 0x3e33: 0x00c3, 0x3e34: 0x00c3, 0x3e35: 0x00c3,
	0x3e38: 0x00c0, 0x3e39: 0x00c0, 0x3e3a: 0x00c0, 0x3e3b: 0x00c0,
	0x3e3c: 0x00c3, 0x3e3d: 0x00c3, 0x3e3e: 0x00c0, 0x3e3f: 0x00c6,
	// Block 0xf9, offset 0x3e40
	0x3e40: 0x00c3, 0x3e41: 0x0080, 0x3e42: 0x0080, 0x3e43: 0x0080, 0x3e44: 0x0080, 0x3e45: 0x0080,
	0x3e46: 0x0080, 0x3e47: 0x0080, 0x3e48: 0x0080, 0x3e49: 0x0080, 0x3e4a: 0x0080, 0x3e4b: 0x0080,
	0x3e4c: 0x0080, 0x3e4d: 0x0080, 0x3e4e: 0x0080, 0x3e4f: 0x0080, 0x3e50: 0x0080, 0x3e51: 0x0080,
	0x3e52: 0x0080, 0x3e53: 0x0080, 0x3e54: 0x0080, 0x3e55: 0x0080, 0x3e56: 0x0080, 0x3e57: 0x0080,
	0x3e58: 0x00c0, 0x3e59: 0x00c0, 0x3e5a: 0x00c0, 0x3e5b: 0x00c0, 0x3e5c: 0x00c3, 0x3e5d: 0x00c3,
	// Block 0xfa, offset 0x3e80
	0x3e80: 0x00c0, 0x3e81: 0x00c0, 0x3e82: 0x00c0, 0x3e83: 0x00c0, 0x3e84: 0x00c0, 0x3e85: 0x00c0,
	0x3e86: 0x00c0, 0x3e87: 0x00c0, 0x3e88: 0x00c0, 0x3e89: 0x00c0, 0x3e8a: 0x00c0, 0x3e8b: 0x00c0,
	0x3e8c: 0x00c0, 0x3e8d: 0x00c0, 0x3e8e: 0x00c0, 0x3e8f: 0x00c0, 0x3e90: 0x00c0, 0x3e91: 0x00c0,
	0x3e92: 0x00c0, 0x3e93: 0x00c0, 0x3e94: 0x00c0, 0x3e95: 0x00c0, 0x3e96: 0x00c0, 0x3e97: 0x00c0,
	0x3e98: 0x00c0, 0x3e99: 0x00c0, 0x3e9a: 0x00c0, 0x3e9b: 0x00c0, 0x3e9c: 0x00c0, 0x3e9d: 0x00c0,
	0x3e9e: 0x00c0, 0x3e9f: 0x00c0, 0x3ea0: 0x00c0, 0x3ea1: 0x00c0, 0x3ea2: 0x00c0, 0x3ea3: 0x00c0,
	0x3ea4: 0x00c0, 0x3ea5: 0x00c0, 0x3ea6: 0x00c0, 0x3ea7: 0x00c0, 0x3ea8: 0x00c0, 0x3ea9: 0x00c0,
	0x3eaa: 0x00c0, 0x3eab: 0x00c0, 0x3eac: 0x00c0, 0x3ead: 0x00c0, 0x3eae: 0x00c0, 0x3eaf: 0x00c0,
	0x3eb0: 0x00c0, 0x3eb1: 0x00c0, 0x3eb2: 0x00c0, 0x3eb3: 0x00c3, 0x3eb4: 0x00c3, 0x3eb5: 0x00c3,
	0x3eb6: 0x00c3, 0x3eb7: 0x00c3, 0x3eb8: 0x00c3, 0x3eb9: 0x00c3, 0x3eba: 0x00c3, 0x3ebb: 0x00c0,
	0x3ebc: 0x00c0, 0x3ebd: 0x00c3, 0x3ebe: 0x00c0, 0x3ebf: 0x00c6,
	// Block 0xfb, offset 0x3ec0
	0x3ec0: 0x00c3, 0x3ec1: 0x0080, 0x3ec2: 0x0080, 0x3ec3: 0x0080, 0x3ec4: 0x00c0,
	0x3ed0: 0x00c0, 0x3ed1: 0x00c0,
	0x3ed2: 0x00c0, 0x3ed3: 0x00c0, 0x3ed4: 0x00c0, 0x3ed5: 0x00c0, 0x3ed6: 0x00c0, 0x3ed7: 0x00c0,
	0x3ed8: 0x00c0, 0x3ed9: 0x00c0,
	0x3ee0: 0x0080, 0x3ee1: 0x0080, 0x3ee2: 0x0080, 0x3ee3: 0x0080,
	0x3ee4: 0x0080, 0x3ee5: 0x0080, 0x3ee6: 0x0080, 0x3ee7: 0x0080, 0x3ee8: 0x0080, 0x3ee9: 0x0080,
	0x3eea: 0x0080, 0x3eeb: 0x0080, 0x3eec: 0x0080,
	// Block 0xfc, offset 0x3f00
	0x3f00: 0x00c0, 0x3f01: 0x00c0, 0x3f02: 0x00c0, 0x3f03: 0x00c0, 0x3f04: 0x00c0, 0x3f05: 0x00c0,
	0x3f06: 0x00c0, 0x3f07: 0x00c0, 0x3f08: 0x00c0, 0x3f09: 0x00c0, 0x3f0a: 0x00c0, 0x3f0b: 0x00c0,
	0x3f0c: 0x00c0, 0x3f0d: 0x00c0, 0x3f0e: 0x00c0, 0x3f0f: 0x00c0, 0x3f10: 0x00c0, 0x3f11: 0x00c0,
	0x3f12: 0x00c0, 0x3f13: 0x00c0, 0x3f14: 0x00c0, 0x3f15: 0x00c0, 0x3f16: 0x00c0, 0x3f17: 0x00c0,
	0x3f18: 0x00c0, 0x3f19: 0x00c0, 0x3f1a: 0x00c0, 0x3f1b: 0x00c0, 0x3f1c: 0x00c0, 0x3f1d: 0x00c0,
	0x3f1e: 0x00c0, 0x3f1f: 0x00c0, 0x3f20: 0x00c0, 0x3f21: 0x00c0, 0x3f22: 0x00c0, 0x3f23: 0x00c0,
	0x3f24: 0x00c0, 0x3f25: 0x00c0, 0x3f26: 0x00c0, 0x3f27: 0x00c0, 0x3f28: 0x00c0, 0x3f29: 0x00c0,
	0x3f2a: 0x00c0, 0x3f2b: 0x00c3, 0x3f2c: 0x00c0, 0x3f2d: 0x00c3, 0x3f2e: 0x00c0, 0x3f2f: 0x00c0,
	0x3f30: 0x00c3, 0x3f31: 0x00c3, 0x3f32: 0x00c3, 0x3f33: 0x00c3, 0x3f34: 0x00c3, 0x3f35: 0x00c3,
	0x3f36: 0x00c5, 0x3f37: 0x00c3, 0x3f38: 0x00c0,
	// Block 0xfd, offset 0x3f40
	0x3f40: 0x00c0, 0x3f41: 0x00c0, 0x3f42: 0x00c0, 0x3f43: 0x00c0, 0x3f44: 0x00c0, 0x3f45: 0x00c0,
	0x3f46: 0x00c0, 0x3f47: 0x00c0, 0x3f48: 0x00c0, 0x3f49: 0x00c0,
	// Block 0xfe, offset 0x3f80
	0x3f80: 0x00c0, 0x3f81: 0x00c0, 0x3f82: 0x00c0, 0x3f83: 0x00c0, 0x3f84: 0x00c0, 0x3f85: 0x00c0,
	0x3f86: 0x00c0, 0x3f87: 0x00c0, 0x3f88: 0x00c0, 0x3f89: 0x00c0, 0x3f8a: 0x00c0, 0x3f8b: 0x00c0,
	0x3f8c: 0x00c0, 0x3f8d: 0x00c0, 0x3f8e: 0x00c0, 0x3f8f: 0x00c0, 0x3f90: 0x00c0, 0x3f91: 0x00c0,
	0x3f92: 0x00c0, 0x3f93: 0x00c0, 0x3f94: 0x00c0, 0x3f95: 0x00c0, 0x3f96: 0x00c0, 0x3f97: 0x00c0,
	0x3f98: 0x00c0, 0x3f99: 0x00c0, 0x3f9a: 0x00c0, 0x3f9d: 0x00c3,
	0x3f9e: 0x00c3, 0x3f9f: 0x00c3, 0x3fa0: 0x00c0, 0x3fa1: 0x00c0, 0x3fa2: 0x00c3, 0x3fa3: 0x00c3,
	0x3fa4: 0x00c3, 0x3fa5: 0x00c3, 0x3fa6: 0x00c0, 0x3fa7: 0x00c3, 0x3fa8: 0x00c3, 0x3fa9: 0x00c3,
	0x3faa: 0x00c3, 0x3fab: 0x00c6,
	0x3fb0: 0x00c0, 0x3fb1: 0x00c0, 0x3fb2: 0x00c0, 0x3fb3: 0x00c0, 0x3fb4: 0x00c0, 0x3fb5: 0x00c0,
	0x3fb6: 0x00c0, 0x3fb7: 0x00c0, 0x3fb8: 0x00c0, 0x3fb9: 0x00c0, 0x3fba: 0x0080, 0x3fbb: 0x0080,
	0x3fbc: 0x0080, 0x3fbd: 0x0080, 0x3fbe: 0x0080, 0x3fbf: 0x0080,
	// Block 0xff, offset 0x3fc0
	0x3fc0: 0x00c0, 0x3fc1: 0x00c0, 0x3fc2: 0x00c0, 0x3fc3: 0x00c0, 0x3fc4: 0x00c0, 0x3fc5: 0x00c0,
	0x3fc6: 0x00c0, 0x3fc7: 0x00c0, 0x3fc8: 0x00c0, 0x3fc9: 0x00c0, 0x3fca: 0x00c0, 0x3fcb: 0x00c0,
	0x3fcc: 0x00c0, 0x3fcd: 0x00c0, 0x3fce: 0x00c0, 0x3fcf: 0x00c0, 0x3fd0: 0x00c0, 0x3fd1: 0x00c0,
	0x3fd2: 0x00c0, 0x3fd3: 0x00c0, 0x3fd4: 0x00c0, 0x3fd5: 0x00c0, 0x3fd6: 0x00c0, 0x3fd7: 0x00c0,
	0x3fd8: 0x00c0, 0x3fd9: 0x00c0, 0x3fda: 0x00c0, 0x3fdb: 0x00c0, 0x3fdc: 0x00c0, 0x3fdd: 0x00c0,
	0x3fde: 0x00c0, 0x3fdf: 0x00c0, 0x3fe0: 0x00c0, 0x3fe1: 0x00c0, 0x3fe2: 0x00c0, 0x3fe3: 0x00c0,
	0x3fe4: 0x00c0, 0x3fe5: 0x00c0, 0x3fe6: 0x00c0, 0x3fe7: 0x00c0, 0x3fe8: 0x00c0, 0x3fe9: 0x00c0,
	0x3fea: 0x00c0, 0x3feb: 0x00c0, 0x3fec: 0x00c0, 0x3fed: 0x00c0, 0x3fee: 0x00c0, 0x3fef: 0x00c3,
	0x3ff0: 0x00c3, 0x3ff1: 0x00c3, 0x3ff2: 0x00c3, 0x3ff3: 0x00c3, 0x3ff4: 0x00c3, 0x3ff5: 0x00c3,
	0x3ff6: 0x00c3, 0x3ff7: 0x00c3, 0x3ff8: 0x00c0, 0x3ff9: 0x00c6, 0x3ffa: 0x00c3, 0x3ffb: 0x0080,
	// Block 0x100, offset 0x4000
	0x4020: 0x00c0, 0x4021: 0x00c0, 0x4022: 0x00c0, 0x4023: 0x00c0,
	0x4024: 0x00c0, 0x4025: 0x00c0, 0x4026: 0x00c0, 0x4027: 0x00c0, 0x4028: 0x00c0, 0x4029: 0x00c0,
	0x402a: 0x00c0, 0x402b: 0x00c0, 0x402c: 0x00c0, 0x402d: 0x00c0, 0x402e: 0x00c0, 0x402f: 0x00c0,
	0x4030: 0x00c0, 0x4031: 0x00c0, 0x4032: 0x00c0, 0x4033: 0x00c0, 0x4034: 0x00c0, 0x4035: 0x00c0,
	0x4036: 0x00c0, 0x4037: 0x00c0, 0x4038: 0x00c0, 0x4039: 0x00c0, 0x403a: 0x00c0, 0x403b: 0x00c0,
	0x403c: 0x00c0, 0x403d: 0x00c0, 0x403e: 0x00c0, 0x403f: 0x00c0,
	// Block 0x101, offset 0x4040
	0x4040: 0x00c0, 0x4041: 0x00c0, 0x4042: 0x00c0, 0x4043: 0x00c0, 0x4044: 0x00c0, 0x4045: 0x00c0,
	0x4046: 0x00c0, 0x4047: 0x00c0, 0x4048: 0x00c0, 0x4049: 0x00c0, 0x404a: 0x00c0, 0x404b: 0x00c0,
	0x404c: 0x00c0, 0x404d: 0x00c0, 0x404e: 0x00c0, 0x404f: 0x00c0, 0x4050: 0x00c0, 0x4051: 0x00c0,
	0x4052: 0x00c0, 0x4053: 0x00c0, 0x4054: 0x00c0, 0x4055: 0x00c0, 0x4056: 0x00c0, 0x4057: 0x00c0,
	0x4058: 0x00c0, 0x4059: 0x00c0, 0x405a: 0x00c0, 0x405b: 0x00c0, 0x405c: 0x00c0, 0x405d: 0x00c0,
	0x405e: 0x00c0, 0x405f: 0x00c0, 0x4060: 0x00c0, 0x4061: 0x00c0, 0x4062: 0x00c0, 0x4063: 0x00c0,
	0x4064: 0x00c0, 0x4065: 0x00c0, 0x4066: 0x00c0, 0x4067: 0x00c0, 0x4068: 0x00c0, 0x4069: 0x00c0,
	0x406a: 0x0080, 0x406b: 0x0080, 0x406c: 0x0080, 0x406d: 0x0080, 0x406e: 0x0080, 0x406f: 0x0080,
	0x4070: 0x0080, 0x4071: 0x0080, 0x4072: 0x0080,
	0x407f: 0x00c0,
	// Block 0x102, offset 0x4080
	0x4080: 0x00c0, 0x4081: 0x00c0, 0x4082: 0x00c0, 0x4083: 0x00c0, 0x4084: 0x00c0, 0x4085: 0x00c0,
	0x4086: 0x00c0, 0x4089: 0x00c0,
	0x408c: 0x00c0, 0x408d: 0x00c0, 0x408e: 0x00c0, 0x408f: 0x00c0, 0x4090: 0x00c0, 0x4091: 0x00c0,
	0x4092: 0x00c0, 0x4093: 0x00c0, 0x4095: 0x00c0, 0x4096: 0x00c0,
	0x4098: 0x00c0, 0x4099: 0x00c0, 0x409a: 0x00c0, 0x409b: 0x00c0, 0x409c: 0x00c0, 0x409d: 0x00c0,
	0x409e: 0x00c0, 0x409f: 0x00c0, 0x40a0: 0x00c0, 0x40a1: 0x00c0, 0x40a2: 0x00c0, 0x40a3: 0x00c0,
	0x40a4: 0x00c0, 0x40a5: 0x00c0, 0x40a6: 0x00c0, 0x40a7: 0x00c0, 0x40a8: 0x00c0, 0x40a9: 0x00c0,
	0x40aa: 0x00c0, 0x40ab: 0x00c0, 0x40ac: 0x00c0, 0x40ad: 0x00c0, 0x40ae: 0x00c0, 0x40af: 0x00c0,
	0x40b0: 0x00c0, 0x40b1: 0x00c0, 0x40b2: 0x00c0, 0x40b3: 0x00c0, 0x40b4: 0x00c0, 0x40b5: 0x00c0,
	0x40b7: 0x00c0, 0x40b8: 0x00c0, 0x40bb: 0x00c3,
	0x40bc: 0x00c3, 0x40bd: 0x00c5, 0x40be: 0x00c6, 0x40bf: 0x00c0,
	// Block 0x103, offset 0x40c0
	0x40c0: 0x00c0, 0x40c1: 0x00c0, 0x40c2: 0x00c0, 0x40c3: 0x00c3, 0x40c4: 0x0080, 0x40c5: 0x0080,
	0x40c6: 0x0080,
	0x40d0: 0x00c0, 0x40d1: 0x00c0,
	0x40d2: 0x00c0, 0x40d3: 0x00c0, 0x40d4: 0x00c0, 0x40d5: 0x00c0, 0x40d6: 0x00c0, 0x40d7: 0x00c0,
	0x40d8: 0x00c0, 0x40d9: 0x00c0,
	// Block 0x104, offset 0x4100
	0x4120: 0x00c0, 0x4121: 0x00c0, 0x4122: 0x00c0, 0x4123: 0x00c0,
	0x4124: 0x00c0, 0x4125: 0x00c0, 0x4126: 0x00c0, 0x4127: 0x00c0,
	0x412a: 0x00c0, 0x412b: 0x00c0, 0x412c: 0x00c0, 0x412d: 0x00c0, 0x412e: 0x00c0, 0x412f: 0x00c0,
	0x4130: 0x00c0, 0x4131: 0x00c0, 0x4132: 0x00c0, 0x4133: 0x00c0, 0x4134: 0x00c0, 0x4135: 0x00c0,
	0x4136: 0x00c0, 0x4137: 0x00c0, 0x4138: 0x00c0, 0x4139: 0x00c0, 0x413a: 0x00c0, 0x413b: 0x00c0,
	0x413c: 0x00c0, 0x413d: 0x00c0, 0x413e: 0x00c0, 0x413f: 0x00c0,
	// Block 0x105, offset 0x4140
	0x4140: 0x00c0, 0x4141: 0x00c0, 0x4142: 0x00c0, 0x4143: 0x00c0, 0x4144: 0x00c0, 0x4145: 0x00c0,
	0x4146: 0x00c0, 0x4147: 0x00c0, 0x4148: 0x00c0, 0x4149: 0x00c0, 0x414a: 0x00c0, 0x414b: 0x00c0,
	0x414c: 0x00c0, 0x414d: 0x00c0, 0x414e: 0x00c0, 0x414f: 0x00c0, 0x4150: 0x00c0, 0x4151: 0x00c0,
	0x4152: 0x00c0, 0x4153: 0x00c0, 0x4154: 0x00c3, 0x4155: 0x00c3, 0x4156: 0x00c3, 0x4157: 0x00c3,
	0x415a: 0x00c3, 0x415b: 0x00c3, 0x415c: 0x00c0, 0x415d: 0x00c0,
	0x415e: 0x00c0, 0x415f: 0x00c0, 0x4160: 0x00c6, 0x4161: 0x00c0, 0x4162: 0x0080, 0x4163: 0x00c0,
	0x4164: 0x00c0,
	// Block 0x106, offset 0x4180
	0x4180: 0x00c0, 0x4181: 0x00c3, 0x4182: 0x00c3, 0x4183: 0x00c3, 0x4184: 0x00c3, 0x4185: 0x00c3,
	0x4186: 0x00c3, 0x4187: 0x00c3, 0x4188: 0x00c3, 0x4189: 0x00c3, 0x418a: 0x00c3, 0x418b: 0x00c0,
	0x418c: 0x00c0, 0x418d: 0x00c0, 0x418e: 0x00c0, 0x418f: 0x00c0, 0x4190: 0x00c0, 0x4191: 0x00c0,
	0x4192: 0x00c0, 0x4193: 0x00c0, 0x4194: 0x00c0, 0x4195: 0x00c0, 0x4196: 0x00c0, 0x4197: 0x00c0,
	0x4198: 0x00c0, 0x4199: 0x00c0, 0x419a: 0x00c0, 0x419b: 0x00c0, 0x419c: 0x00c0, 0x419d: 0x00c0,
	0x419e: 0x00c0, 0x419f: 0x00c0, 0x41a0: 0x00c0, 0x41a1: 0x00c0, 0x41a2: 0x00c0, 0x41a3: 0x00c0,
	0x41a4: 0x00c0, 0x41a5: 0x00c0, 0x41a6: 0x00c0, 0x41a7: 0x00c0, 0x41a8: 0x00c0, 0x41a9: 0x00c0,
	0x41aa: 0x00c0, 0x41ab: 0x00c0, 0x41ac: 0x00c0, 0x41ad: 0x00c0, 0x41ae: 0x00c0, 0x41af: 0x00c0,
	0x41b0: 0x00c0, 0x41b1: 0x00c0, 0x41b2: 0x00c0, 0x41b3: 0x00c3, 0x41b4: 0x00c6, 0x41b5: 0x00c3,
	0x41b6: 0x00c3, 0x41b7: 0x00c3, 0x41b8: 0x00c3, 0x41b9: 0x00c0, 0x41ba: 0x00c0, 0x41bb: 0x00c3,
	0x41bc: 0x00c3, 0x41bd: 0x00c3, 0x41be: 0x00c3, 0x41bf: 0x0080,
	// Block 0x107, offset 0x41c0
	0x41c0: 0x0080, 0x41c1: 0x0080, 0x41c2: 0x0080, 0x41c3: 0x0080, 0x41c4: 0x0080, 0x41c5: 0x0080,
	0x41c6: 0x0080, 0x41c7: 0x00c6,
	0x41d0: 0x00c0, 0x41d1: 0x00c3,
	0x41d2: 0x00c3, 0x41d3: 0x00c3, 0x41d4: 0x00c3, 0x41d5: 0x00c3, 0x41d6: 0x00c3, 0x41d7: 0x00c0,
	0x41d8: 0x00c0, 0x41d9: 0x00c3, 0x41da: 0x00c3, 0x41db: 0x00c3, 0x41dc: 0x00c0, 0x41dd: 0x00c0,
	0x41de: 0x00c0, 0x41df: 0x00c0, 0x41e0: 0x00c0, 0x41e1: 0x00c0, 0x41e2: 0x00c0, 0x41e3: 0x00c0,
	0x41e4: 0x00c0, 0x41e5: 0x00c0, 0x41e6: 0x00c0, 0x41e7: 0x00c0, 0x41e8: 0x00c0, 0x41e9: 0x00c0,
	0x41ea: 0x00c0, 0x41eb: 0x00c0, 0x41ec: 0x00c0, 0x41ed: 0x00c0, 0x41ee: 0x00c0, 0x41ef: 0x00c0,
	0x41f0: 0x00c0, 0x41f1: 0x00c0, 0x41f2: 0x00c0, 0x41f3: 0x00c0, 0x41f4: 0x00c0, 0x41f5: 0x00c0,
	0x41f6: 0x00c0, 0x41f7: 0x00c0, 0x41f8: 0x00c0, 0x41f9: 0x00c0, 0x41fa: 0x00c0, 0x41fb: 0x00c0,
	0x41fc: 0x00c0, 0x41fd: 0x00c0, 0x41fe: 0x00c0, 0x41ff: 0x00c0,
	// Block 0x108, offset 0x4200
	0x4200: 0x00c0, 0x4201: 0x00c0, 0x4202: 0x00c0, 0x4203: 0x00c0, 0x4204: 0x00c0, 0x4205: 0x00c0,
	0x4206: 0x00c0, 0x4207: 0x00c0, 0x4208: 0x00c0, 0x4209: 0x00c0, 0x420a: 0x00c3, 0x420b: 0x00c3,
	0x420c: 0x00c3, 0x420d: 0x00c3, 0x420e: 0x00c3, 0x420f: 0x00c3, 0x4210: 0x00c3, 0x4211: 0x00c3,
	0x4212: 0x00c3, 0x4213: 0x00c3, 0x4214: 0x00c3, 0x4215: 0x00c3, 0x4216: 0x00c3, 0x4217: 0x00c0,
	0x4218: 0x00c3, 0x4219: 0x00c6, 0x421a: 0x0080, 0x421b: 0x0080, 0x421c: 0x0080, 0x421d: 0x00c0,
	0x421e: 0x0080, 0x421f: 0x0080, 0x4220: 0x0080, 0x4221: 0x0080, 0x4222: 0x0080,
	// Block 0x109, offset 0x4240
	0x4240: 0x00c0, 0x4241: 0x00c0, 0x4242: 0x00c0, 0x4243: 0x00c0, 0x4244: 0x00c0, 0x4245: 0x00c0,
	0x4246: 0x00c0, 0x4247: 0x00c0, 0x4248: 0x00c0, 0x4249: 0x00c0, 0x424a: 0x00c0, 0x424b: 0x00c0,
	0x424c: 0x00c0, 0x424d: 0x00c0, 0x424e: 0x00c0, 0x424f: 0x00c0, 0x4250: 0x00c0, 0x4251: 0x00c0,
	0x4252: 0x00c0, 0x4253: 0x00c0, 0x4254: 0x00c0, 0x4255: 0x00c0, 0x4256: 0x00c0, 0x4257: 0x00c0,
	0x4258: 0x00c0, 0x4259: 0x00c0, 0x425a: 0x00c0, 0x425b: 0x00c0, 0x425c: 0x00c0, 0x425d: 0x00c0,
	0x425e: 0x00c0, 0x425f: 0x00c0, 0x4260: 0x00c0, 0x4261: 0x00c0, 0x4262: 0x00c0, 0x4263: 0x00c0,
	0x4264: 0x00c0, 0x4265: 0x00c0, 0x4266: 0x00c0, 0x4267: 0x00c0, 0x4268: 0x00c0, 0x4269: 0x00c0,
	0x426a: 0x00c0, 0x426b: 0x00c0, 0x426c: 0x00c0, 0x426d: 0x00c0, 0x426e: 0x00c0, 0x426f: 0x00c0,
	0x4270: 0x00c0, 0x4271: 0x00c0, 0x4272: 0x00c0, 0x4273: 0x00c0, 0x4274: 0x00c0, 0x4275: 0x00c0,
	0x4276: 0x00c0, 0x4277: 0x00c0, 0x4278: 0x00c0,
	// Block 0x10a, offset 0x4280
	0x4280: 0x00c0, 0x4281: 0x00c0, 0x4282: 0x00c0, 0x4283: 0x00c0, 0x4284: 0x00c0, 0x4285: 0x00c0,
	0x4286: 0x00c0, 0x4287: 0x00c0, 0x4288: 0x00c0, 0x428a: 0x00c0, 0x428b: 0x00c0,
	0x428c: 0x00c0, 0x428d: 0x00c0, 0x428e: 0x00c0, 0x428f: 0x00c0, 0x4290: 0x00c0, 0x4291: 0x00c0,
	0x4292: 0x00c0, 0x4293: 0x00c0, 0x4294: 0x00c0, 0x4295: 0x00c0, 0x4296: 0x00c0, 0x4297: 0x00c0,
	0x4298: 0x00c0, 0x4299: 0x00c0, 0x429a: 0x00c0, 0x429b: 0x00c0, 0x429c: 0x00c0, 0x429d: 0x00c0,
	0x429e: 0x00c0, 0x429f: 0x00c0, 0x42a0: 0x00c0, 0x42a1: 0x00c0, 0x42a2: 0x00c0, 0x42a3: 0x00c0,
	0x42a4: 0x00c0, 0x42a5: 0x00c0, 0x42a6: 0x00c0, 0x42a7: 0x00c0, 0x42a8: 0x00c0, 0x42a9: 0x00c0,
	0x42aa: 0x00c0, 0x42ab: 0x00c0, 0x42ac: 0x00c0, 0x42ad: 0x00c0, 0x42ae: 0x00c0, 0x42af: 0x00c0,
	0x42b0: 0x00c3, 0x42b1: 0x00c3, 0x42b2: 0x00c3, 0x42b3: 0x00c3, 0x42b4: 0x00c3, 0x42b5: 0x00c3,
	0x42b6: 0x00c3, 0x42b8: 0x00c3, 0x42b9: 0x00c3, 0x42ba: 0x00c3, 0x42bb: 0x00c3,
	0x42bc: 0x00c3, 0x42bd: 0x00c3, 0x42be: 0x00c0, 0x42bf: 0x00c6,
	// Block 0x10b, offset 0x42c0
	0x42c0: 0x00c0, 0x42c1: 0x0080, 0x42c2: 0x0080, 0x42c3: 0x0080, 0x42c4: 0x0080, 0x42c5: 0x0080,
	0x42d0: 0x00c0, 0x42d1: 0x00c0,
	0x42d2: 0x00c0, 0x42d3: 0x00c0, 0x42d4: 0x00c0, 0x42d5: 0x00c0, 0x42d6: 0x00c0, 0x42d7: 0x00c0,
	0x42d8: 0x00c0, 0x42d9: 0x00c0, 0x42da: 0x0080, 0x42db: 0x0080, 0x42dc: 0x0080, 0x42dd: 0x0080,
	0x42de: 0x0080, 0x42df: 0x0080, 0x42e0: 0x0080, 0x42e1: 0x0080, 0x42e2: 0x0080, 0x42e3: 0x0080,
	0x42e4: 0x0080, 0x42e5: 0x0080, 0x42e6: 0x0080, 0x42e7: 0x0080, 0x42e8: 0x0080, 0x42e9: 0x0080,
	0x42ea: 0x0080, 0x42eb: 0x0080, 0x42ec: 0x0080,
	0x42f0: 0x0080, 0x42f1: 0x0080, 0x42f2: 0x00c0, 0x42f3: 0x00c0, 0x42f4: 0x00c0, 0x42f5: 0x00c0,
	0x42f6: 0x00c0, 0x42f7: 0x00c0, 0x42f8: 0x00c0, 0x42f9: 0x00c0, 0x42fa: 0x00c0, 0x42fb: 0x00c0,
	0x42fc: 0x00c0, 0x42fd: 0x00c0, 0x42fe: 0x00c0, 0x42ff: 0x00c0,
	// Block 0x10c, offset 0x4300
	0x4300: 0x00c0, 0x4301: 0x00c0, 0x4302: 0x00c0, 0x4303: 0x00c0, 0x4304: 0x00c0, 0x4305: 0x00c0,
	0x4306: 0x00c0, 0x4307: 0x00c0, 0x4308: 0x00c0, 0x4309: 0x00c0, 0x430a: 0x00c0, 0x430b: 0x00c0,
	0x430c: 0x00c0, 0x430d: 0x00c0, 0x430e: 0x00c0, 0x430f: 0x00c0,
	0x4312: 0x00c3, 0x4313: 0x00c3, 0x4314: 0x00c3, 0x4315: 0x00c3, 0x4316: 0x00c3, 0x4317: 0x00c3,
	0x4318: 0x00c3, 0x4319: 0x00c3, 0x431a: 0x00c3, 0x431b: 0x00c3, 0x431c: 0x00c3, 0x431d: 0x00c3,
	0x431e: 0x00c3, 0x431f: 0x00c3, 0x4320: 0x00c3, 0x4321: 0x00c3, 0x4322: 0x00c3, 0x4323: 0x00c3,
	0x4324: 0x00c3, 0x4325: 0x00c3, 0x4326: 0x00c3, 0x4327: 0x00c3, 0x4329: 0x00c0,
	0x432a: 0x00c3, 0x432b: 0x00c3, 0x432c: 0x00c3, 0x432d: 0x00c3, 0x432e: 0x00c3, 0x432f: 0x00c3,
	0x4330: 0x00c3, 0x4331: 0x00c0, 0x4332: 0x00c3, 0x4333: 0x00c3, 0x4334: 0x00c0, 0x4335: 0x00c3,
	0x4336: 0x00c3,
	// Block 0x10d, offset 0x4340
	0x4340: 0x00c0, 0x4341: 0x00c0, 0x4342: 0x00c0, 0x4343: 0x00c0, 0x4344: 0x00c0, 0x4345: 0x00c0,
	0x4346: 0x00c0, 0x4348: 0x00c0, 0x4349: 0x00c0, 0x434b: 0x00c0,
	0x434c: 0x00c0, 0x434d: 0x00c0, 0x434e: 0x00c0, 0x434f: 0x00c0, 0x4350: 0x00c0, 0x4351: 0x00c0,
	0x4352: 0x00c0, 0x4353: 0x00c0, 0x4354: 0x00c0, 0x4355: 0x00c0, 0x4356: 0x00c0, 0x4357: 0x00c0,
	0x4358: 0x00c0, 0x4359: 0x00c0, 0x435a: 0x00c0, 0x435b: 0x00c0, 0x435c: 0x00c0, 0x435d: 0x00c0,
	0x435e: 0x00c0, 0x435f: 0x00c0, 0x4360: 0x00c0, 0x4361: 0x00c0, 0x4362: 0x00c0, 0x4363: 0x00c0,
	0x4364: 0x00c0, 0x4365: 0x00c0, 0x4366: 0x00c0, 0x4367: 0x00c0, 0x4368: 0x00c0, 0x4369: 0x00c0,
	0x436a: 0x00c0, 0x436b: 0x00c0, 0x436c: 0x00c0, 0x436d: 0x00c0, 0x436e: 0x00c0, 0x436f: 0x00c0,
	0x4370: 0x00c0, 0x4371: 0x00c3, 0x4372: 0x00c3, 0x4373: 0x00c3, 0x4374: 0x00c3, 0x4375: 0x00c3,
	0x4376: 0x00c3, 0x437a: 0x00c3,
	0x437c: 0x00c3, 0x437d: 0x00c3, 0x437f: 0x00c3,
	// Block 0x10e, offset 0x4380
	0x4380: 0x00c3, 0x4381: 0x00c3, 0x4382: 0x00c3, 0x4383: 0x00c3, 0x4384: 0x00c6, 0x4385: 0x00c6,
	0x4386: 0x00c0, 0x4387: 0x00c3,
	0x4390: 0x00c0, 0x4391: 0x00c0,
	0x4392: 0x00c0, 0x4393: 0x00c0, 0x4394: 0x00c0, 0x4395: 0x00c0, 0x4396: 0x00c0, 0x4397: 0x00c0,
	0x4398: 0x00c0, 0x4399: 0x00c0,
	0x43a0: 0x00c0, 0x43a1: 0x00c0, 0x43a2: 0x00c0, 0x43a3: 0x00c0,
	0x43a4: 0x00c0, 0x43a5: 0x00c0, 0x43a7: 0x00c0, 0x43a8: 0x00c0,
	0x43aa: 0x00c0, 0x43ab: 0x00c0, 0x43ac: 0x00c0, 0x43ad: 0x00c0, 0x43ae: 0x00c0, 0x43af: 0x00c0,
	0x43b0: 0x00c0, 0x43b1: 0x00c0, 0x43b2: 0x00c0, 0x43b3: 0x00c0, 0x43b4: 0x00c0, 0x43b5: 0x00c0,
	0x43b6: 0x00c0, 0x43b7: 0x00c0, 0x43b8: 0x00c0, 0x43b9: 0x00c0, 0x43ba: 0x00c0, 0x43bb: 0x00c0,
	0x43bc: 0x00c0, 0x43bd: 0x00c0, 0x43be: 0x00c0, 0x43bf: 0x00c0,
	// Block 0x10f, offset 0x43c0
	0x43c0: 0x00c0, 0x43c1: 0x00c0, 0x43c2: 0x00c0, 0x43c3: 0x00c0, 0x43c4: 0x00c0, 0x43c5: 0x00c0,
	0x43c6: 0x00c0, 0x43c7: 0x00c0, 0x43c8: 0x00c0, 0x43c9: 0x00c0, 0x43ca: 0x00c0, 0x43cb: 0x00c0,
	0x43cc: 0x00c0, 0x43cd: 0x00c0, 0x43ce: 0x00c0, 0x43d0: 0x00c3, 0x43d1: 0x00c3,
	0x43d3: 0x00c0, 0x43d4: 0x00c0, 0x43d5: 0x00c3, 0x43d6: 0x00c0, 0x43d7: 0x00c6,
	0x43d8: 0x00c0,
	0x43e0: 0x00c0, 0x43e1: 0x00c0, 0x43e2: 0x00c0, 0x43e3: 0x00c0,
	0x43e4: 0x00c0, 0x43e5: 0x00c0, 0x43e6: 0x00c0, 0x43e7: 0x00c0, 0x43e8: 0x00c0, 0x43e9: 0x00c0,
	// Block 0x110, offset 0x4400
	0x4420: 0x00c0, 0x4421: 0x00c0, 0x4422: 0x00c0, 0x4423: 0x00c0,
	0x4424: 0x00c0, 0x4425: 0x00c0, 0x4426: 0x00c0, 0x4427: 0x00c0, 0x4428: 0x00c0, 0x4429: 0x00c0,
	0x442a: 0x00c0, 0x442b: 0x00c0, 0x442c: 0x00c0, 0x442d: 0x00c0, 0x442e: 0x00c0, 0x442f: 0x00c0,
	0x4430: 0x00c0, 0x4431: 0x00c0, 0x4432: 0x00c0, 0x4433: 0x00c3, 0x4434: 0x00c3, 0x4435: 0x00c0,
	0x4436: 0x00c0, 0x4437: 0x0080, 0x4438: 0x0080,
	// Block 0x111, offset 0x4440
	0x4470: 0x00c0,
	// Block 0x112, offset 0x4480
	0x4480: 0x0080, 0x4481: 0x0080, 0x4482: 0x0080, 0x4483: 0x0080, 0x4484: 0x0080, 0x4485: 0x0080,
	0x4486: 0x0080, 0x4487: 0x0080, 0x4488: 0x0080, 0x4489: 0x0080, 0x448a: 0x0080, 0x448b: 0x0080,
	0x448c: 0x0080, 0x448d: 0x0080, 0x448e: 0x0080, 0x448f: 0x0080, 0x4490: 0x0080, 0x4491: 0x0080,
	0x4492: 0x0080, 0x4493: 0x0080, 0x4494: 0x0080, 0x4495: 0x0080, 0x4496: 0x0080, 0x4497: 0x0080,
	0x4498: 0x0080, 0x4499: 0x0080, 0x449a: 0x0080, 0x449b: 0x0080, 0x449c: 0x0080, 0x449d: 0x0080,
	0x449e: 0x0080, 0x449f: 0x0080, 0x44a0: 0x0080, 0x44a1: 0x0080, 0x44a2: 0x0080, 0x44a3: 0x0080,
	0x44a4: 0x0080, 0x44a5: 0x0080, 0x44a6: 0x0080, 0x44a7: 0x0080, 0x44a8: 0x0080, 0x44a9: 0x0080,
	0x44aa: 0x0080, 0x44ab: 0x0080, 0x44ac: 0x0080, 0x44ad: 0x0080, 0x44ae: 0x0080, 0x44af: 0x0080,
	0x44b0: 0x0080, 0x44b1: 0x0080,
	0x44bf: 0x0080,
	// Block 0x113, offset 0x44c0
	0x44c0: 0x00c0, 0x44c1: 0x00c0, 0x44c2: 0x00c0, 0x44c3: 0x00c0, 0x44c4: 0x00c0, 0x44c5: 0x00c0,
	0x44c6: 0x00c0, 0x44c7: 0x00c0, 0x44c8: 0x00c0, 0x44c9: 0x00c0, 0x44ca: 0x00c0, 0x44cb: 0x00c0,
	0x44cc: 0x00c0, 0x44cd: 0x00c0, 0x44ce: 0x00c0, 0x44cf: 0x00c0, 0x44d0: 0x00c0, 0x44d1: 0x00c0,
	0x44d2: 0x00c0, 0x44d3: 0x00c0, 0x44d4: 0x00c0, 0x44d5: 0x00c0, 0x44d6: 0x00c0, 0x44d7: 0x00c0,
	0x44d8: 0x00c0, 0x44d9: 0x00c0,
	// Block 0x114, offset 0x4500
	0x4500: 0x0080, 0x4501: 0x0080, 0x4502: 0x0080, 0x4503: 0x0080, 0x4504: 0x0080, 0x4505: 0x0080,
	0x4506: 0x0080, 0x4507: 0x0080, 0x4508: 0x0080, 0x4509: 0x0080, 0x450a: 0x0080, 0x450b: 0x0080,
	0x450c: 0x0080, 0x450d: 0x0080, 0x450e: 0x0080, 0x450f: 0x0080, 0x4510: 0x0080, 0x4511: 0x0080,
	0x4512: 0x0080, 0x4513: 0x0080, 0x4514: 0x0080, 0x4515: 0x0080, 0x4516: 0x0080, 0x4517: 0x0080,
	0x4518: 0x0080, 0x4519: 0x0080, 0x451a: 0x0080, 0x451b: 0x0080, 0x451c: 0x0080, 0x451d: 0x0080,
	0x451e: 0x0080, 0x451f: 0x0080, 0x4520: 0x0080, 0x4521: 0x0080, 0x4522: 0x0080, 0x4523: 0x0080,
	0x4524: 0x0080, 0x4525: 0x0080, 0x4526: 0x0080, 0x4527: 0x0080, 0x4528: 0x0080, 0x4529: 0x0080,
	0x452a: 0x0080, 0x452b: 0x0080, 0x452c: 0x0080, 0x452d: 0x0080, 0x452e: 0x0080,
	0x4530: 0x0080, 0x4531: 0x0080, 0x4532: 0x0080, 0x4533: 0x0080, 0x4534: 0x0080,
	// Block 0x115, offset 0x4540
	0x4540: 0x00c0, 0x4541: 0x00c0, 0x4542: 0x00c0, 0x4543: 0x00c0,
	// Block 0x116, offset 0x4580
	0x4580: 0x00c0, 0x4581: 0x00c0, 0x4582: 0x00c0, 0x4583: 0x00c0, 0x4584: 0x00c0, 0x4585: 0x00c0,
	0x4586: 0x00c0, 0x4587: 0x00c0, 0x4588: 0x00c0, 0x4589: 0x00c0, 0x458a: 0x00c0, 0x458b: 0x00c0,
	0x458c: 0x00c0, 0x458d: 0x00c0, 0x458e: 0x00c0, 0x458f: 0x00c0, 0x4590: 0x00c0, 0x4591: 0x00c0,
	0x4592: 0x00c0, 0x4593: 0x00c0, 0x4594: 0x00c0, 0x4595: 0x00c0, 0x4596: 0x00c0, 0x4597: 0x00c0,
	0x4598: 0x00c0, 0x4599: 0x00c0, 0x459a: 0x00c0, 0x459b: 0x00c0, 0x459c: 0x00c0, 0x459d: 0x00c0,
	0x459e: 0x00c0, 0x459f: 0x00c0, 0x45a0: 0x00c0, 0x45a1: 0x00c0, 0x45a2: 0x00c0, 0x45a3: 0x00c0,
	0x45a4: 0x00c0, 0x45a5: 0x00c0, 0x45a6: 0x00c0, 0x45a7: 0x00c0, 0x45a8: 0x00c0, 0x45a9: 0x00c0,
	0x45aa: 0x00c0, 0x45ab: 0x00c0, 0x45ac: 0x00c0, 0x45ad: 0x00c0, 0x45ae: 0x00c0,
	0x45b0: 0x0040, 0x45b1: 0x0040, 0x45b2: 0x0040, 0x45b3: 0x0040, 0x45b4: 0x0040, 0x45b5: 0x0040,
	0x45b6: 0x0040, 0x45b7: 0x0040, 0x45b8: 0x0040,
	// Block 0x117, offset 0x45c0
	0x45c0: 0x00c0, 0x45c1: 0x00c0, 0x45c2: 0x00c0, 0x45c3: 0x00c0, 0x45c4: 0x00c0, 0x45c5: 0x00c0,
	0x45c6: 0x00c0,
	// Block 0x118, offset 0x4600
	0x4600: 0x00c0, 0x4601: 0x00c0, 0x4602: 0x00c0, 0x4603: 0x00c0, 0x4604: 0x00c0, 0x4605: 0x00c0,
	0x4606: 0x00c0, 0x4607: 0x00c0, 0x4608: 0x00c0, 0x4609: 0x00c0, 0x460a: 0x00c0, 0x460b: 0x00c0,
	0x460c: 0x00c0, 0x460d: 0x00c0, 0x460e: 0x00c0, 0x460f: 0x00c0, 0x4610: 0x00c0, 0x4611: 0x00c0,
	0x4612: 0x00c0, 0x4613: 0x00c0, 0x4614: 0x00c0, 0x4615: 0x00c0, 0x4616: 0x00c0, 0x4617: 0x00c0,
	0x4618: 0x00c0, 0x4619: 0x00c0, 0x461a: 0x00c0, 0x461b: 0x00c0, 0x461c: 0x00c0, 0x461d: 0x00c0,
	0x461e: 0x00c0, 0x4620: 0x00c0, 0x4621: 0x00c0, 0x4622: 0x00c0, 0x4623: 0x00c0,
	0x4624: 0x00c0, 0x4625: 0x00c0, 0x4626: 0x00c0, 0x4627: 0x00c0, 0x4628: 0x00c0, 0x4629: 0x00c0,
	0x462e: 0x0080, 0x462f: 0x0080,
	// Block 0x119, offset 0x4640
	0x4650: 0x00c0, 0x4651: 0x00c0,
	0x4652: 0x00c0, 0x4653: 0x00c0, 0x4654: 0x00c0, 0x4655: 0x00c0, 0x4656: 0x00c0, 0x4657: 0x00c0,
	0x4658: 0x00c0, 0x4659: 0x00c0, 0x465a: 0x00c0, 0x465b: 0x00c0, 0x465c: 0x00c0, 0x465d: 0x00c0,
	0x465e: 0x00c0, 0x465f: 0x00c0, 0x4660: 0x00c0, 0x4661: 0x00c0, 0x4662: 0x00c0, 0x4663: 0x00c0,
	0x4664: 0x00c0, 0x4665: 0x00c0, 0x4666: 0x00c0, 0x4667: 0x00c0, 0x4668: 0x00c0, 0x4669: 0x00c0,
	0x466a: 0x00c0, 0x466b: 0x00c0, 0x466c: 0x00c0, 0x466d: 0x00c0,
	0x4670: 0x00c3, 0x4671: 0x00c3, 0x4672: 0x00c3, 0x4673: 0x00c3, 0x4674: 0x00c3, 0x4675: 0x0080,
	// Block 0x11a, offset 0x4680
	0x4680: 0x00c0, 0x4681: 0x00c0, 0x4682: 0x00c0, 0x4683: 0x00c0, 0x4684: 0x00c0, 0x4685: 0x00c0,
	0x4686: 0x00c0, 0x4687: 0x00c0, 0x4688: 0x00c0, 0x4689: 0x00c0, 0x468a: 0x00c0, 0x468b: 0x00c0,
	0x468c: 0x00c0, 0x468d: 0x00c0, 0x468e: 0x00c0, 0x468f: 0x00c0, 0x4690: 0x00c0, 0x4691: 0x00c0,
	0x4692: 0x00c0, 0x4693: 0x00c0, 0x4694: 0x00c0, 0x4695: 0x00c0, 0x4696: 0x00c0, 0x4697: 0x00c0,
	0x4698: 0x00c0, 0x4699: 0x00c0, 0x469a: 0x00c0, 0x469b: 0x00c0, 0x469c: 0x00c0, 0x469d: 0x00c0,
	0x469e: 0x00c0, 0x469f: 0x00c0, 0x46a0: 0x00c0, 0x46a1: 0x00c0, 0x46a2: 0x00c0, 0x46a3: 0x00c0,
	0x46a4: 0x00c0, 0x46a5: 0x00c0, 0x46a6: 0x00c0, 0x46a7: 0x00c0, 0x46a8: 0x00c0, 0x46a9: 0x00c0,
	0x46aa: 0x00c0, 0x46ab: 0x00c0, 0x46ac: 0x00c0, 0x46ad: 0x00c0, 0x46ae: 0x00c0, 0x46af: 0x00c0,
	0x46b0: 0x00c3, 0x46b1: 0x00c3, 0x46b2: 0x00c3, 0x46b3: 0x00c3, 0x46b4: 0x00c3, 0x46b5: 0x00c3,
	0x46b6: 0x00c3, 0x46b7: 0x0080, 0x46b8: 0x0080, 0x46b9: 0x0080, 0x46ba: 0x0080, 0x46bb: 0x0080,
	0x46bc: 0x0080, 0x46bd: 0x0080, 0x46be: 0x0080, 0x46bf: 0x0080,
	// Block 0x11b, offset 0x46c0
	0x46c0: 0x00c0, 0x46c1: 0x00c0, 0x46c2: 0x00c0, 0x46c3: 0x00c0, 0x46c4: 0x0080, 0x46c5: 0x0080,
	0x46d0: 0x00c0, 0x46d1: 0x00c0,
	0x46d2: 0x00c0, 0x46d3: 0x00c0, 0x46d4: 0x00c0, 0x46d5: 0x00c0, 0x46d6: 0x00c0, 0x46d7: 0x00c0,
	0x46d8: 0x00c0, 0x46d9: 0x00c0, 0x46db: 0x0080, 0x46dc: 0x0080, 0x46dd: 0x0080,
	0x46de: 0x0080, 0x46df: 0x0080, 0x46e0: 0x0080, 0x46e1: 0x0080, 0x46e3: 0x00c0,
	0x46e4: 0x00c0, 0x46e5: 0x00c0, 0x46e6: 0x00c0, 0x46e7: 0x00c0, 0x46e8: 0x00c0, 0x46e9: 0x00c0,
	0x46ea: 0x00c0, 0x46eb: 0x00c0, 0x46ec: 0x00c0, 0x46ed: 0x00c0, 0x46ee: 0x00c0, 0x46ef: 0x00c0,
	0x46f0: 0x00c0, 0x46f1: 0x00c0, 0x46f2: 0x00c0, 0x46f3: 0x00c0, 0x46f4: 0x00c0, 0x46f5: 0x00c0,
	0x46f6: 0x00c0, 0x46f7: 0x00c0,
	0x46fd: 0x00c0, 0x46fe: 0x00c0, 0x46ff: 0x00c0,
	// Block 0x11c, offset 0x4700
	0x4700: 0x00c0, 0x4701: 0x00c0, 0x4702: 0x00c0, 0x4703: 0x00c0, 0x4704: 0x00c0, 0x4705: 0x00c0,
	0x4706: 0x00c0, 0x4707: 0x00c0, 0x4708: 0x00c0, 0x4709: 0x00c0, 0x470a: 0x00c0, 0x470b: 0x00c0,
	0x470c: 0x00c0, 0x470d: 0x00c0, 0x470e: 0x00c0, 0x470f: 0x00c0,
	// Block 0x11d, offset 0x4740
	0x4740: 0x0080, 0x4741: 0x0080, 0x4742: 0x0080, 0x4743: 0x0080, 0x4744: 0x0080, 0x4745: 0x0080,
	0x4746: 0x0080, 0x4747: 0x0080, 0x4748: 0x0080, 0x4749: 0x0080, 0x474a: 0x0080, 0x474b: 0x0080,
	0x474c: 0x0080, 0x474d: 0x0080, 0x474e: 0x0080, 0x474f: 0x0080, 0x4750: 0x0080, 0x4751: 0x0080,
	0x4752: 0x0080, 0x4753: 0x0080, 0x4754: 0x0080, 0x4755: 0x0080, 0x4756: 0x0080, 0x4757: 0x0080,
	0x4758: 0x0080, 0x4759: 0x0080, 0x475a: 0x0080,
	// Block 0x11e, offset 0x4780
	0x4780: 0x00c0, 0x4781: 0x00c0, 0x4782: 0x00c0, 0x4783: 0x00c0, 0x4784: 0x00c0, 0x4785: 0x00c0,
	0x4786: 0x00c0, 0x4787: 0x00c0, 0x4788: 0x00c0, 0x4789: 0x00c0, 0x478a: 0x00c0,
	0x478f: 0x00c3, 0x4790: 0x00c0, 0x4791: 0x00c0,
	0x4792: 0x00c0, 0x4793: 0x00c0, 0x4794: 0x00c0, 0x4795: 0x00c0, 0x4796: 0x00c0, 0x4797: 0x00c0,
	0x4798: 0x00c0, 0x4799: 0x00c0, 0x479a: 0x00c0, 0x479b: 0x00c0, 0x479c: 0x00c0, 0x479d: 0x00c0,
	0x479e: 0x00c0, 0x479f: 0x00c0, 0x47a0: 0x00c0, 0x47a1: 0x00c0, 0x47a2: 0x00c0, 0x47a3: 0x00c0,
	0x47a4: 0x00c0, 0x47a5: 0x00c0, 0x47a6: 0x00c0, 0x47a7: 0x00c0, 0x47a8: 0x00c0, 0x47a9: 0x00c0,
	0x47aa: 0x00c0, 0x47ab: 0x00c0, 0x47ac: 0x00c0, 0x47ad: 0x00c0, 0x47ae: 0x00c0, 0x47af: 0x00c0,
	0x47b0: 0x00c0, 0x47b1: 0x00c0, 0x47b2: 0x00c0, 0x47b3: 0x00c0, 0x47b4: 0x00c0, 0x47b5: 0x00c0,
	0x47b6: 0x00c0, 0x47b7: 0x00c0, 0x47b8: 0x00c0, 0x47b9: 0x00c0, 0x47ba: 0x00c0, 0x47bb: 0x00c0,
	0x47bc: 0x00c0, 0x47bd: 0x00c0, 0x47be: 0x00c0, 0x47bf: 0x00c0,
	// Block 0x11f, offset 0x47c0
	0x47c0: 0x00c0, 0x47c1: 0x00c0, 0x47c2: 0x00c0, 0x47c3: 0x00c0, 0x47c4: 0x00c0, 0x47c5: 0x00c0,
	0x47c6: 0x00c0, 0x47c7: 0x00c0,
	0x47cf: 0x00c3, 0x47d0: 0x00c3, 0x47d1: 0x00c3,
	0x47d2: 0x00c3, 0x47d3: 0x00c0, 0x47d4: 0x00c0, 0x47d5: 0x00c0, 0x47d6: 0x00c0, 0x47d7: 0x00c0,
	0x47d8: 0x00c0, 0x47d9: 0x00c0, 0x47da: 0x00c0, 0x47db: 0x00c0, 0x47dc: 0x00c0, 0x47dd: 0x00c0,
	0x47de: 0x00c0, 0x47df: 0x00c0,
	// Block 0x120, offset 0x4800
	0x4820: 0x00c0, 0x4821: 0x00c0, 0x4822: 0x0080, 0x4823: 0x00c0,
	0x4824: 0x00c3,
	0x4830: 0x00cc, 0x4831: 0x00cc,
	// Block 0x121, offset 0x4840
	0x4840: 0x00c0, 0x4841: 0x00c0, 0x4842: 0x00c0, 0x4843: 0x00c0, 0x4844: 0x00c0, 0x4845: 0x00c0,
	0x4846: 0x00c0, 0x4847: 0x00c0, 0x4848: 0x00c0, 0x4849: 0x00c0, 0x484a: 0x00c0, 0x484b: 0x00c0,
	0x484c: 0x00c0, 0x484d: 0x00c0, 0x484e: 0x00c0, 0x484f: 0x00c0, 0x4850: 0x00c0, 0x4851: 0x00c0,
	0x4852: 0x00c0, 0x4853: 0x00c0, 0x4854: 0x00c0, 0x4855: 0x00c0, 0x4856: 0x00c0, 0x4857: 0x00c0,
	0x4858: 0x00c0, 0x4859: 0x00c0, 0x485a: 0x00c0, 0x485b: 0x00c0, 0x485c: 0x00c0, 0x485d: 0x00c0,
	0x485e: 0x00c0, 0x485f: 0x00c0, 0x4860: 0x00c0, 0x4861: 0x00c0, 0x4862: 0x00c0, 0x4863: 0x00c0,
	0x4864: 0x00c0, 0x4865: 0x00c0, 0x4866: 0x00c0, 0x4867: 0x00c0, 0x4868: 0x00c0, 0x4869: 0x00c0,
	0x486a: 0x00c0, 0x486b: 0x00c0, 0x486c: 0x00c0, 0x486d: 0x00c0, 0x486e: 0x00c0, 0x486f: 0x00c0,
	0x4870: 0x00c0, 0x4871: 0x00c0, 0x4872: 0x00c0, 0x4873: 0x00c0, 0x4874: 0x00c0, 0x4875: 0x00c0,
	0x4876: 0x00c0, 0x4877: 0x00c0,
	// Block 0x122, offset 0x4880
	0x4880: 0x00c0, 0x4881: 0x00c0, 0x4882: 0x00c0, 0x4883: 0x00c0, 0x4884: 0x00c0, 0x4885: 0x00c0,
	0x4886: 0x00c0, 0x4887: 0x00c0, 0x4888: 0x00c0, 0x4889: 0x00c0, 0x488a: 0x00c0, 0x488b: 0x00c0,
	0x488c: 0x00c0, 0x488d: 0x00c0, 0x488e: 0x00c0, 0x488f: 0x00c0, 0x4890: 0x00c0, 0x4891: 0x00c0,
	0x4892: 0x00c0, 0x4893: 0x00c0, 0x4894: 0x00c0, 0x4895: 0x00c0,
	// Block 0x123, offset 0x48c0
	0x48c0: 0x00cc, 0x48c1: 0x00cc, 0x48c2: 0x00cc, 0x48c3: 0x00cc, 0x48c4: 0x00cc, 0x48c5: 0x00cc,
	0x48c6: 0x00cc, 0x48c7: 0x00cc, 0x48c8: 0x00cc, 0x48c9: 0x00cc, 0x48ca: 0x00cc, 0x48cb: 0x00cc,
	0x48cc: 0x00cc, 0x48cd: 0x00cc, 0x48ce: 0x00cc, 0x48cf: 0x00cc, 0x48d0: 0x00cc, 0x48d1: 0x00cc,
	0x48d2: 0x00cc, 0x48d3: 0x00cc, 0x48d4: 0x00cc, 0x48d5: 0x00cc, 0x48d6: 0x00cc, 0x48d7: 0x00cc,
	0x48d8: 0x00cc, 0x48d9: 0x00cc, 0x48da: 0x00cc, 0x48db: 0x00cc, 0x48dc: 0x00cc, 0x48dd: 0x00cc,
	0x48de: 0x00cc,
	// Block 0x124, offset 0x4900
	0x4910: 0x00cc, 0x4911: 0x00cc,
	0x4912: 0x00cc,
	0x4924: 0x00cc, 0x4925: 0x00cc, 0x4926: 0x00cc, 0x4927: 0x00cc,
	0x4930: 0x00c0, 0x4931: 0x00c0, 0x4932: 0x00c0, 0x4933: 0x00c0, 0x4934: 0x00c0, 0x4935: 0x00c0,
	0x4936: 0x00c0, 0x4937: 0x00c0, 0x4938: 0x00c0, 0x4939: 0x00c0, 0x493a: 0x00c0, 0x493b: 0x00c0,
	0x493c: 0x00c0, 0x493d: 0x00c0, 0x493e: 0x00c0, 0x493f: 0x00c0,
	// Block 0x125, offset 0x4940
	0x4940: 0x00c0, 0x4941: 0x00c0, 0x4942: 0x00c0, 0x4943: 0x00c0, 0x4944: 0x00c0, 0x4945: 0x00c0,
	0x4946: 0x00c0, 0x4947: 0x00c0, 0x4948: 0x00c0, 0x4949: 0x00c0, 0x494a: 0x00c0, 0x494b: 0x00c0,
	0x494c: 0x00c0, 0x494d: 0x00c0, 0x494e: 0x00c0, 0x494f: 0x00c0, 0x4950: 0x00c0, 0x4951: 0x00c0,
	0x4952: 0x00c0, 0x4953: 0x00c0, 0x4954: 0x00c0, 0x4955: 0x00c0, 0x4956: 0x00c0, 0x4957: 0x00c0,
	0x4958: 0x00c0, 0x4959: 0x00c0, 0x495a: 0x00c0, 0x495b: 0x00c0, 0x495c: 0x00c0, 0x495d: 0x00c0,
	0x495e: 0x00c0, 0x495f: 0x00c0, 0x4960: 0x00c0, 0x4961: 0x00c0, 0x4962: 0x00c0, 0x4963: 0x00c0,
	0x4964: 0x00c0, 0x4965: 0x00c0, 0x4966: 0x00c0, 0x4967: 0x00c0, 0x4968: 0x00c0, 0x4969: 0x00c0,
	0x496a: 0x00c0, 0x496b: 0x00c0, 0x496c: 0x00c0, 0x496d: 0x00c0, 0x496e: 0x00c0, 0x496f: 0x00c0,
	0x4970: 0x00c0, 0x4971: 0x00c0, 0x4972: 0x00c0, 0x4973: 0x00c0, 0x4974: 0x00c0, 0x4975: 0x00c0,
	0x4976: 0x00c0, 0x4977: 0x00c0, 0x4978: 0x00c0, 0x4979: 0x00c0, 0x497a: 0x00c0, 0x497b: 0x00c0,
	// Block 0x126, offset 0x4980
	0x4980: 0x00c0, 0x4981: 0x00c0, 0x4982: 0x00c0, 0x4983: 0x00c0, 0x4984: 0x00c0, 0x4985: 0x00c0,
	0x4986: 0x00c0, 0x4987: 0x00c0, 0x4988: 0x00c0, 0x4989: 0x00c0, 0x498a: 0x00c0, 0x498b: 0x00c0,
	0x498c: 0x00c0, 0x498d: 0x00c0, 0x498e: 0x00c0, 0x498f: 0x00c0, 0x4990: 0x00c0, 0x4991: 0x00c0,
	0x4992: 0x00c0, 0x4993: 0x00c0, 0x4994: 0x00c0, 0x4995: 0x00c0, 0x4996: 0x00c0, 0x4997: 0x00c0,
	0x4998: 0x00c0, 0x4999: 0x00c0, 0x499a: 0x00c0, 0x499b: 0x00c0, 0x499c: 0x00c0, 0x499d: 0x00c0,
	0x499e: 0x00c0, 0x499f: 0x00c0, 0x49a0: 0x00c0, 0x49a1: 0x00c0, 0x49a2: 0x00c0, 0x49a3: 0x00c0,
	0x49a4: 0x00c0, 0x49a5: 0x00c0, 0x49a6: 0x00c0, 0x49a7: 0x00c0, 0x49a8: 0x00c0, 0x49a9: 0x00c0,
	0x49aa: 0x00c0,
	0x49b0: 0x00c0, 0x49b1: 0x00c0, 0x49b2: 0x00c0, 0x49b3: 0x00c0, 0x49b4: 0x00c0, 0x49b5: 0x00c0,
	0x49b6: 0x00c0, 0x49b7: 0x00c0, 0x49b8: 0x00c0, 0x49b9: 0x00c0, 0x49ba: 0x00c0, 0x49bb: 0x00c0,
	0x49bc: 0x00c0,
	// Block 0x127, offset 0x49c0
	0x49c0: 0x00c0, 0x49c1: 0x00c0, 0x49c2: 0x00c0, 0x49c3: 0x00c0, 0x49c4: 0x00c0, 0x49c5: 0x00c0,
	0x49c6: 0x00c0, 0x49c7: 0x00c0, 0x49c8: 0x00c0,
	0x49d0: 0x00c0, 0x49d1: 0x00c0,
	0x49d2: 0x00c0, 0x49d3: 0x00c0, 0x49d4: 0x00c0, 0x49d5: 0x00c0, 0x49d6: 0x00c0, 0x49d7: 0x00c0,
	0x49d8: 0x00c0, 0x49d9: 0x00c0, 0x49dc: 0x0080, 0x49dd: 0x00c3,
	0x49de: 0x00c3, 0x49df: 0x0080, 0x49e0: 0x0040, 0x49e1: 0x0040, 0x49e2: 0x0040, 0x49e3: 0x0040,
	// Block 0x128, offset 0x4a00
	0x4a00: 0x0080, 0x4a01: 0x0080, 0x4a02: 0x0080, 0x4a03: 0x0080, 0x4a04: 0x0080, 0x4a05: 0x0080,
	0x4a06: 0x0080, 0x4a07: 0x0080, 0x4a08: 0x0080, 0x4a09: 0x0080, 0x4a0a: 0x0080, 0x4a0b: 0x0080,
	0x4a0c: 0x0080, 0x4a0d: 0x0080, 0x4a0e: 0x0080, 0x4a0f: 0x0080, 0x4a10: 0x0080, 0x4a11: 0x0080,
	0x4a12: 0x0080, 0x4a13: 0x0080, 0x4a14: 0x0080, 0x4a15: 0x0080, 0x4a16: 0x0080, 0x4a17: 0x0080,
	0x4a18: 0x0080, 0x4a19: 0x0080, 0x4a1a: 0x0080, 0x4a1b: 0x0080, 0x4a1c: 0x0080, 0x4a1d: 0x0080,
	0x4a1e: 0x0080, 0x4a1f: 0x0080, 0x4a20: 0x0080, 0x4a21: 0x0080, 0x4a22: 0x0080, 0x4a23: 0x0080,
	0x4a24: 0x0080, 0x4a25: 0x0080, 0x4a26: 0x0080, 0x4a27: 0x0080, 0x4a28: 0x0080, 0x4a29: 0x0080,
	0x4a2a: 0x0080, 0x4a2b: 0x0080, 0x4a2c: 0x0080, 0x4a2d: 0x0080, 0x4a2e: 0x0080, 0x4a2f: 0x0080,
	0x4a30: 0x0080, 0x4a31: 0x0080, 0x4a32: 0x0080, 0x4a33: 0x0080, 0x4a34: 0x0080, 0x4a35: 0x0080,
	// Block 0x129, offset 0x4a40
	0x4a40: 0x0080, 0x4a41: 0x0080, 0x4a42: 0x0080, 0x4a43: 0x0080, 0x4a44: 0x0080, 0x4a45: 0x0080,
	0x4a46: 0x0080, 0x4a47: 0x0080, 0x4a48: 0x0080, 0x4a49: 0x0080, 0x4a4a: 0x0080, 0x4a4b: 0x0080,
	0x4a4c: 0x0080, 0x4a4d: 0x0080, 0x4a4e: 0x0080, 0x4a4f: 0x0080, 0x4a50: 0x0080, 0x4a51: 0x0080,
	0x4a52: 0x0080, 0x4a53: 0x0080, 0x4a54: 0x0080, 0x4a55: 0x0080, 0x4a56: 0x0080, 0x4a57: 0x0080,
	0x4a58: 0x0080, 0x4a59: 0x0080, 0x4a5a: 0x0080, 0x4a5b: 0x0080, 0x4a5c: 0x0080, 0x4a5d: 0x0080,
	0x4a5e: 0x0080, 0x4a5f: 0x0080, 0x4a60: 0x0080, 0x4a61: 0x0080, 0x4a62: 0x0080, 0x4a63: 0x0080,
	0x4a64: 0x0080, 0x4a65: 0x0080, 0x4a66: 0x0080, 0x4a69: 0x0080,
	0x4a6a: 0x0080, 0x4a6b: 0x0080, 0x4a6c: 0x0080, 0x4a6d: 0x0080, 0x4a6e: 0x0080, 0x4a6f: 0x0080,
	0x4a70: 0x0080, 0x4a71: 0x0080, 0x4a72: 0x0080, 0x4a73: 0x0080, 0x4a74: 0x0080, 0x4a75: 0x0080,
	0x4a76: 0x0080, 0x4a77: 0x0080, 0x4a78: 0x0080, 0x4a79: 0x0080, 0x4a7a: 0x0080, 0x4a7b: 0x0080,
	0x4a7c: 0x0080, 0x4a7d: 0x0080, 0x4a7e: 0x0080, 0x4a7f: 0x0080,
	// Block 0x12a, offset 0x4a80
	0x4a80: 0x0080, 0x4a81: 0x0080, 0x4a82: 0x0080, 0x4a83: 0x0080, 0x4a84: 0x0080, 0x4a85: 0x0080,
	0x4a86: 0x0080, 0x4a87: 0x0080, 0x4a88: 0x0080, 0x4a89: 0x0080, 0x4a8a: 0x0080, 0x4a8b: 0x0080,
	0x4a8c: 0x0080, 0x4a8d: 0x0080, 0x4a8e: 0x0080, 0x4a8f: 0x0080, 0x4a90: 0x0080, 0x4a91: 0x0080,
	0x4a92: 0x0080, 0x4a93: 0x0080, 0x4a94: 0x0080, 0x4a95: 0x0080, 0x4a96: 0x0080, 0x4a97: 0x0080,
	0x4a98: 0x0080, 0x4a99: 0x0080, 0x4a9a: 0x0080, 0x4a9b: 0x0080, 0x4a9c: 0x0080, 0x4a9d: 0x0080,
	0x4a9e: 0x0080, 0x4a9f: 0x0080, 0x4aa0: 0x0080, 0x4aa1: 0x0080, 0x4aa2: 0x0080, 0x4aa3: 0x0080,
	0x4aa4: 0x0080, 0x4aa5: 0x00c0, 0x4aa6: 0x00c0, 0x4aa7: 0x00c3, 0x4aa8: 0x00c3, 0x4aa9: 0x00c3,
	0x4aaa: 0x0080, 0x4aab: 0x0080, 0x4aac: 0x0080, 0x4aad: 0x00c0, 0x4aae: 0x00c0, 0x4aaf: 0x00c0,
	0x4ab0: 0x00c0, 0x4ab1: 0x00c0, 0x4ab2: 0x00c0, 0x4ab3: 0x0040, 0x4ab4: 0x0040, 0x4ab5: 0x0040,
	0x4ab6: 0x0040, 0x4ab7: 0x0040, 0x4ab8: 0x0040, 0x4ab9: 0x0040, 0x4aba: 0x0040, 0x4abb: 0x00c3,
	0x4abc: 0x00c3, 0x4abd: 0x00c3, 0x4abe: 0x00c3, 0x4abf: 0x00c3,
	// Block 0x12b, offset 0x4ac0
	0x4ac0: 0x00c3, 0x4ac1: 0x00c3, 0x4ac2: 0x00c3, 0x4ac3: 0x0080, 0x4ac4: 0x0080, 0x4ac5: 0x00c3,
	0x4ac6: 0x00c3, 0x4ac7: 0x00c3, 0x4ac8: 0x00c3, 0x4ac9: 0x00c3, 0x4aca: 0x00c3, 0x4acb: 0x00c3,
	0x4acc: 0x0080, 0x4acd: 0x0080, 0x4ace: 0x0080, 0x4acf: 0x0080, 0x4ad0: 0x0080, 0x4ad1: 0x0080,
	0x4ad2: 0x0080, 0x4ad3: 0x0080, 0x4ad4: 0x0080, 0x4ad5: 0x0080, 0x4ad6: 0x0080, 0x4ad7: 0x0080,
	0x4ad8: 0x0080, 0x4ad9: 0x0080, 0x4ada: 0x0080, 0x4adb: 0x0080, 0x4adc: 0x0080, 0x4add: 0x0080,
	0x4ade: 0x0080, 0x4adf: 0x0080, 0x4ae0: 0x0080, 0x4ae1: 0x0080, 0x4ae2: 0x0080, 0x4ae3: 0x0080,
	0x4ae4: 0x0080, 0x4ae5: 0x0080, 0x4ae6: 0x0080, 0x4ae7: 0x0080, 0x4ae8: 0x0080, 0x4ae9: 0x0080,
	0x4aea: 0x00c3, 0x4aeb: 0x00c3, 0x4aec: 0x00c3, 0x4aed: 0x00c3, 0x4aee: 0x0080, 0x4aef: 0x0080,
	0x4af0: 0x0080, 0x4af1: 0x0080, 0x4af2: 0x0080, 0x4af3: 0x0080, 0x4af4: 0x0080, 0x4af5: 0x0080,
	0x4af6: 0x0080, 0x4af7: 0x0080, 0x4af8: 0x0080, 0x4af9: 0x0080, 0x4afa: 0x0080, 0x4afb: 0x0080,
	0x4afc: 0x0080, 0x4afd: 0x0080, 0x4afe: 0x0080, 0x4aff: 0x0080,
	// Block 0x12c, offset 0x4b00
	0x4b00: 0x0080, 0x4b01: 0x0080, 0x4b02: 0x0080, 0x4b03: 0x0080, 0x4b04: 0x0080, 0x4b05: 0x0080,
	0x4b06: 0x0080, 0x4b07: 0x0080, 0x4b08: 0x0080, 0x4b09: 0x0080, 0x4b0a: 0x0080, 0x4b0b: 0x0080,
	0x4b0c: 0x0080, 0x4b0d: 0x0080, 0x4b0e: 0x0080, 0x4b0f: 0x0080, 0x4b10: 0x0080, 0x4b11: 0x0080,
	0x4b12: 0x0080, 0x4b13: 0x0080, 0x4b14: 0x0080, 0x4b15: 0x0080, 0x4b16: 0x0080, 0x4b17: 0x0080,
	0x4b18: 0x0080, 0x4b19: 0x0080, 0x4b1a: 0x0080, 0x4b1b: 0x0080, 0x4b1c: 0x0080, 0x4b1d: 0x0080,
	0x4b1e: 0x0080, 0x4b1f: 0x0080, 0x4b20: 0x0080, 0x4b21: 0x0080, 0x4b22: 0x0080, 0x4b23: 0x0080,
	0x4b24: 0x0080, 0x4b25: 0x0080, 0x4b26: 0x0080, 0x4b27: 0x0080, 0x4b28: 0x0080,
	// Block 0x12d, offset 0x4b40
	0x4b40: 0x0088, 0x4b41: 0x0088, 0x4b42: 0x00c9, 0x4b43: 0x00c9, 0x4b44: 0x00c9, 0x4b45: 0x0088,
	// Block 0x12e, offset 0x4b80
	0x4ba0: 0x0080, 0x4ba1: 0x0080, 0x4ba2: 0x0080, 0x4ba3: 0x0080,
	0x4ba4: 0x0080, 0x4ba5: 0x0080, 0x4ba6: 0x0080, 0x4ba7: 0x0080, 0x4ba8: 0x0080, 0x4ba9: 0x0080,
	0x4baa: 0x0080, 0x4bab: 0x0080, 0x4bac: 0x0080, 0x4bad: 0x0080, 0x4bae: 0x0080, 0x4baf: 0x0080,
	0x4bb0: 0x0080, 0x4bb1: 0x0080, 0x4bb2: 0x0080, 0x4bb3: 0x0080,
	// Block 0x12f, offset 0x4bc0
	0x4bc0: 0x0080, 0x4bc1: 0x0080, 0x4bc2: 0x0080, 0x4bc3: 0x0080, 0x4bc4: 0x0080, 0x4bc5: 0x0080,
	0x4bc6: 0x0080, 0x4bc7: 0x0080, 0x4bc8: 0x0080, 0x4bc9: 0x0080, 0x4bca: 0x0080, 0x4bcb: 0x0080,
	0x4bcc: 0x0080, 0x4bcd: 0x0080, 0x4bce: 0x0080, 0x4bcf: 0x0080, 0x4bd0: 0x0080, 0x4bd1: 0x0080,
	0x4bd2: 0x0080, 0x4bd3: 0x0080, 0x4bd4: 0x0080, 0x4bd5: 0x0080, 0x4bd6: 0x0080,
	0x4be0: 0x0080, 0x4be1: 0x0080, 0x4be2: 0x0080, 0x4be3: 0x0080,
	0x4be4: 0x0080, 0x4be5: 0x0080, 0x4be6: 0x0080, 0x4be7: 0x0080, 0x4be8: 0x0080, 0x4be9: 0x0080,
	0x4bea: 0x0080, 0x4beb: 0x0080, 0x4bec: 0x0080, 0x4bed: 0x0080, 0x4bee: 0x0080, 0x4bef: 0x0080,
	0x4bf0: 0x0080, 0x4bf1: 0x0080, 0x4bf2: 0x0080, 0x4bf3: 0x0080, 0x4bf4: 0x0080, 0x4bf5: 0x0080,
	0x4bf6: 0x0080, 0x4bf7: 0x0080, 0x4bf8: 0x0080,
	// Block 0x130, offset 0x4c00
	0x4c00: 0x0080, 0x4c01: 0x0080, 0x4c02: 0x0080, 0x4c03: 0x0080, 0x4c04: 0x0080, 0x4c05: 0x0080,
	0x4c06: 0x0080, 0x4c07: 0x0080, 0x4c08: 0x0080, 0x4c09: 0x0080, 0x4c0a: 0x0080, 0x4c0b: 0x0080,
	0x4c0c: 0x0080, 0x4c0d: 0x0080, 0x4c0e: 0x0080, 0x4c0f: 0x0080, 0x4c10: 0x0080, 0x4c11: 0x0080,
	0x4c12: 0x0080, 0x4c13: 0x0080, 0x4c14: 0x0080, 0x4c16: 0x0080, 0x4c17: 0x0080,
	0x4c18: 0x0080, 0x4c19: 0x0080, 0x4c1a: 0x0080, 0x4c1b: 0x0080, 0x4c1c: 0x0080, 0x4c1d: 0x0080,
	0x4c1e: 0x0080, 0x4c1f: 0x0080, 0x4c20: 0x0080, 0x4c21: 0x0080, 0x4c22: 0x0080, 0x4c23: 0x0080,
	0x4c24: 0x0080, 0x4c25: 0x0080, 0x4c26: 0x0080, 0x4c27: 0x0080, 0x4c28: 0x0080, 0x4c29: 0x0080,
	0x4c2a: 0x0080, 0x4c2b: 0x0080, 0x4c2c: 0x0080, 0x4c2d: 0x0080, 0x4c2e: 0x0080, 0x4c2f: 0x0080,
	0x4c30: 0x0080, 0x4c31: 0x0080, 0x4c32: 0x0080, 0x4c33: 0x0080, 0x4c34: 0x0080, 0x4c35: 0x0080,
	0x4c36: 0x0080, 0x4c37: 0x0080, 0x4c38: 0x0080, 0x4c39: 0x0080, 0x4c3a: 0x0080, 0x4c3b: 0x0080,
	0x4c3c: 0x0080, 0x4c3d: 0x0080, 0x4c3e: 0x0080, 0x4c3f: 0x0080,
	// Block 0x131, offset 0x4c40
	0x4c40: 0x0080, 0x4c41: 0x0080, 0x4c42: 0x0080, 0x4c43: 0x0080, 0x4c44: 0x0080, 0x4c45: 0x0080,
	0x4c46: 0x0080, 0x4c47: 0x0080, 0x4c48: 0x0080, 0x4c49: 0x0080, 0x4c4a: 0x0080, 0x4c4b: 0x0080,
	0x4c4c: 0x0080, 0x4c4d: 0x0080, 0x4c4e: 0x0080, 0x4c4f: 0x0080, 0x4c50: 0x0080, 0x4c51: 0x0080,
	0x4c52: 0x0080, 0x4c53: 0x0080, 0x4c54: 0x0080, 0x4c55: 0x0080, 0x4c56: 0x0080, 0x4c57: 0x0080,
	0x4c58: 0x0080, 0x4c59: 0x0080, 0x4c5a: 0x0080, 0x4c5b: 0x0080, 0x4c5c: 0x0080,
	0x4c5e: 0x0080, 0x4c5f: 0x0080, 0x4c62: 0x0080,
	0x4c65: 0x0080, 0x4c66: 0x0080, 0x4c69: 0x0080,
	0x4c6a: 0x0080, 0x4c6b: 0x0080, 0x4c6c: 0x0080, 0x4c6e: 0x0080, 0x4c6f: 0x0080,
	0x4c70: 0x0080, 0x4c71: 0x0080, 0x4c72: 0x0080, 0x4c73: 0x0080, 0x4c74: 0x0080, 0x4c75: 0x0080,
	0x4c76: 0x0080, 0x4c77: 0x0080, 0x4c78: 0x0080, 0x4c79: 0x0080, 0x4c7b: 0x0080,
	0x4c7d: 0x0080, 0x4c7e: 0x0080, 0x4c7f: 0x0080,
	// Block 0x132, offset 0x4c80
	0x4c80: 0x0080, 0x4c81: 0x0080, 0x4c82: 0x0080, 0x4c83: 0x0080, 0x4c85: 0x0080,
	0x4c86: 0x0080, 0x4c87: 0x0080, 0x4c88: 0x0080, 0x4c89: 0x0080, 0x4c8a: 0x0080, 0x4c8b: 0x0080,
	0x4c8c: 0x0080, 0x4c8d: 0x0080, 0x4c8e: 0x0080, 0x4c8f: 0x0080, 0x4c90: 0x0080, 0x4c91: 0x0080,
	0x4c92: 0x0080, 0x4c93: 0x0080, 0x4c94: 0x0080, 0x4c95: 0x0080, 0x4c96: 0x0080, 0x4c97: 0x0080,
	0x4c98: 0x0080, 0x4c99: 0x0080, 0x4c9a: 0x0080, 0x4c9b: 0x0080, 0x4c9c: 0x0080, 0x4c9d: 0x0080,
	0x4c9e: 0x0080, 0x4c9f: 0x0080, 0x4ca0: 0x0080, 0x4ca1: 0x0080, 0x4ca2: 0x0080, 0x4ca3: 0x0080,
	0x4ca4: 0x0080, 0x4ca5: 0x0080, 0x4ca6: 0x0080, 0x4ca7: 0x0080, 0x4ca8: 0x0080, 0x4ca9: 0x0080,
	0x4caa: 0x0080, 0x4cab: 0x0080, 0x4cac: 0x0080, 0x4cad: 0x0080, 0x4cae: 0x0080, 0x4caf: 0x0080,
	0x4cb0: 0x0080, 0x4cb1: 0x0080, 0x4cb2: 0x0080, 0x4cb3: 0x0080, 0x4cb4: 0x0080, 0x4cb5: 0x0080,
	0x4cb6: 0x0080, 0x4cb7: 0x0080, 0x4cb8: 0x0080, 0x4cb9: 0x0080, 0x4cba: 0x0080, 0x4cbb: 0x0080,
	0x4cbc: 0x0080, 0x4cbd: 0x0080, 0x4cbe: 0x0080, 0x4cbf: 0x0080,
	// Block 0x133, offset 0x4cc0
	0x4cc0: 0x0080, 0x4cc1: 0x0080, 0x4cc2: 0x0080, 0x4cc3: 0x0080, 0x4cc4: 0x0080, 0x4cc5: 0x0080,
	0x4cc7: 0x0080, 0x4cc8: 0x0080, 0x4cc9: 0x0080, 0x4cca: 0x0080,
	0x4ccd: 0x0080, 0x4cce: 0x0080, 0x4ccf: 0x0080, 0x4cd0: 0x0080, 0x4cd1: 0x0080,
	0x4cd2: 0x0080, 0x4cd3: 0x0080, 0x4cd4: 0x0080, 0x4cd6: 0x0080, 0x4cd7: 0x0080,
	0x4cd8: 0x0080, 0x4cd9: 0x0080, 0x4cda: 0x0080, 0x4cdb: 0x0080, 0x4cdc: 0x0080,
	0x4cde: 0x0080, 0x4cdf: 0x0080, 0x4ce0: 0x0080, 0x4ce1: 0x0080, 0x4ce2: 0x0080, 0x4ce3: 0x0080,
	0x4ce4: 0x0080, 0x4ce5: 0x0080, 0x4ce6: 0x0080, 0x4ce7: 0x0080, 0x4ce8: 0x0080, 0x4ce9: 0x0080,
	0x4cea: 0x0080, 0x4ceb: 0x0080, 0x4cec: 0x0080, 0x4ced: 0x0080, 0x4cee: 0x0080, 0x4cef: 0x0080,
	0x4cf0: 0x0080, 0x4cf1: 0x0080, 0x4cf2: 0x0080, 0x4cf3: 0x0080, 0x4cf4: 0x0080, 0x4cf5: 0x0080,
	0x4cf6: 0x0080, 0x4cf7: 0x0080, 0x4cf8: 0x0080, 0x4cf9: 0x0080, 0x4cfb: 0x0080,
	0x4cfc: 0x0080, 0x4cfd: 0x0080, 0x4cfe: 0x0080,
	// Block 0x134, offset 0x4d00
	0x4d00: 0x0080, 0x4d01: 0x0080, 0x4d02: 0x0080, 0x4d03: 0x0080, 0x4d04: 0x0080,
	0x4d06: 0x0080, 0x4d0a: 0x0080, 0x4d0b: 0x0080,
	0x4d0c: 0x0080, 0x4d0d: 0x0080, 0x4d0e: 0x0080, 0x4d0f: 0x0080, 0x4d10: 0x0080,
	0x4d12: 0x0080, 0x4d13: 0x0080, 0x4d14: 0x0080, 0x4d15: 0x0080, 0x4d16: 0x0080, 0x4d17: 0x0080,
	0x4d18: 0x0080, 0x4d19: 0x0080, 0x4d1a: 0x0080, 0x4d1b: 0x0080, 0x4d1c: 0x0080, 0x4d1d: 0x0080,
	0x4d1e: 0x0080, 0x4d1f: 0x0080, 0x4d20: 0x0080, 0x4d21: 0x0080, 0x4d22: 0x0080, 0x4d23: 0x0080,
	0x4d24: 0x0080, 0x4d25: 0x0080, 0x4d26: 0x0080, 0x4d27: 0x0080, 0x4d28: 0x0080, 0x4d29: 0x0080,
	0x4d2a: 0x0080, 0x4d2b: 0x0080, 0x4d2c: 0x0080, 0x4d2d: 0x0080, 0x4d2e: 0x0080, 0x4d2f: 0x0080,
	0x4d30: 0x0080, 0x4d31: 0x0080, 0x4d32: 0x0080, 0x4d33: 0x0080, 0x4d34: 0x0080, 0x4d35: 0x0080,
	0x4d36: 0x0080, 0x4d37: 0x0080, 0x4d38: 0x0080, 0x4d39: 0x0080, 0x4d3a: 0x0080, 0x4d3b: 0x0080,
	0x4d3c: 0x0080, 0x4d3d: 0x0080, 0x4d3e: 0x0080, 0x4d3f: 0x0080,
	// Block 0x135, offset 0x4d40
	0x4d40: 0x0080, 0x4d41: 0x0080, 0x4d42: 0x0080, 0x4d43: 0x0080, 0x4d44: 0x0080, 0x4d45: 0x0080,
	0x4d46: 0x0080, 0x4d47: 0x0080, 0x4d48: 0x0080, 0x4d49: 0x0080, 0x4d4a: 0x0080, 0x4d4b: 0x0080,
	0x4d4c: 0x0080, 0x4d4d: 0x0080, 0x4d4e: 0x0080, 0x4d4f: 0x0080, 0x4d50: 0x0080, 0x4d51: 0x0080,
	0x4d52: 0x0080, 0x4d53: 0x0080, 0x4d54: 0x0080, 0x4d55: 0x0080, 0x4d56: 0x0080, 0x4d57: 0x0080,
	0x4d58: 0x0080, 0x4d59: 0x0080, 0x4d5a: 0x0080, 0x4d5b: 0x0080, 0x4d5c: 0x0080, 0x4d5d: 0x0080,
	0x4d5e: 0x0080, 0x4d5f: 0x0080, 0x4d60: 0x0080, 0x4d61: 0x0080, 0x4d62: 0x0080, 0x4d63: 0x0080,
	0x4d64: 0x0080, 0x4d65: 0x0080, 0x4d68: 0x0080, 0x4d69: 0x0080,
	0x4d6a: 0x0080, 0x4d6b: 0x0080, 0x4d6c: 0x0080, 0x4d6d: 0x0080, 0x4d6e: 0x0080, 0x4d6f: 0x0080,
	0x4d70: 0x0080, 0x4d71: 0x0080, 0x4d72: 0x0080, 0x4d73: 0x0080, 0x4d74: 0x0080, 0x4d75: 0x0080,
	0x4d76: 0x0080, 0x4d77: 0x0080, 0x4d78: 0x0080, 0x4d79: 0x0080, 0x4d7a: 0x0080, 0x4d7b: 0x0080,
	0x4d7c: 0x0080, 0x4d7d: 0x0080, 0x4d7e: 0x0080, 0x4d7f: 0x0080,
	// Block 0x136, offset 0x4d80
	0x4d80: 0x0080, 0x4d81: 0x0080, 0x4d82: 0x0080, 0x4d83: 0x0080, 0x4d84: 0x0080, 0x4d85: 0x0080,
	0x4d86: 0x0080, 0x4d87: 0x0080, 0x4d88: 0x0080, 0x4d89: 0x0080, 0x4d8a: 0x0080, 0x4d8b: 0x0080,
	0x4d8e: 0x0080, 0x4d8f: 0x0080, 0x4d90: 0x0080, 0x4d91: 0x0080,
	0x4d92: 0x0080, 0x4d93: 0x0080, 0x4d94: 0x0080, 0x4d95: 0x0080, 0x4d96: 0x0080, 0x4d97: 0x0080,
	0x4d98: 0x0080, 0x4d99: 0x0080, 0x4d9a: 0x0080, 0x4d9b: 0x0080, 0x4d9c: 0x0080, 0x4d9d: 0x0080,
	0x4d9e: 0x0080, 0x4d9f: 0x0080, 0x4da0: 0x0080, 0x4da1: 0x0080, 0x4da2: 0x0080, 0x4da3: 0x0080,
	0x4da4: 0x0080, 0x4da5: 0x0080, 0x4da6: 0x0080, 0x4da7: 0x0080, 0x4da8: 0x0080, 0x4da9: 0x0080,
	0x4daa: 0x0080, 0x4dab: 0x0080, 0x4dac: 0x0080, 0x4dad: 0x0080, 0x4dae: 0x0080, 0x4daf: 0x0080,
	0x4db0: 0x0080, 0x4db1: 0x0080, 0x4db2: 0x0080, 0x4db3: 0x0080, 0x4db4: 0x0080, 0x4db5: 0x0080,
	0x4db6: 0x0080, 0x4db7: 0x0080, 0x4db8: 0x0080, 0x4db9: 0x0080, 0x4dba: 0x0080, 0x4dbb: 0x0080,
	0x4dbc: 0x0080, 0x4dbd: 0x0080, 0x4dbe: 0x0080, 0x4dbf: 0x0080,
	// Block 0x137, offset 0x4dc0
	0x4dc0: 0x00c3, 0x4dc1: 0x00c3, 0x4dc2: 0x00c3, 0x4dc3: 0x00c3, 0x4dc4: 0x00c3, 0x4dc5: 0x00c3,
	0x4dc6: 0x00c3, 0x4dc7: 0x00c3, 0x4dc8: 0x00c3, 0x4dc9: 0x00c3, 0x4dca: 0x00c3, 0x4dcb: 0x00c3,
	0x4dcc: 0x00c3, 0x4dcd: 0x00c3, 0x4dce: 0x00c3, 0x4dcf: 0x00c3, 0x4dd0: 0x00c3, 0x4dd1: 0x00c3,
	0x4dd2: 0x00c3, 0x4dd3: 0x00c3, 0x4dd4: 0x00c3, 0x4dd5: 0x00c3, 0x4dd6: 0x00c3, 0x4dd7: 0x00c3,
	0x4dd8: 0x00c3, 0x4dd9: 0x00c3, 0x4dda: 0x00c3, 0x4ddb: 0x00c3, 0x4ddc: 0x00c3, 0x4ddd: 0x00c3,
	0x4dde: 0x00c3, 0x4ddf: 0x00c3, 0x4de0: 0x00c3, 0x4de1: 0x00c3, 0x4de2: 0x00c3, 0x4de3: 0x00c3,
	0x4de4: 0x00c3, 0x4de5: 0x00c3, 0x4de6: 0x00c3, 0x4de7: 0x00c3, 0x4de8: 0x00c3, 0x4de9: 0x00c3,
	0x4dea: 0x00c3, 0x4deb: 0x00c3, 0x4dec: 0x00c3, 0x4ded: 0x00c3, 0x4dee: 0x00c3, 0x4def: 0x00c3,
	0x4df0: 0x00c3, 0x4df1: 0x00c3, 0x4df2: 0x00c3, 0x4df3: 0x00c3, 0x4df4: 0x00c3, 0x4df5: 0x00c3,
	0x4df6: 0x00c3, 0x4df7: 0x0080, 0x4df8: 0x0080, 0x4df9: 0x0080, 0x4dfa: 0x0080, 0x4dfb: 0x00c3,
	0x4dfc: 0x00c3, 0x4dfd: 0x00c3, 0x4dfe: 0x00c3, 0x4dff: 0x00c3,
	// Block 0x138, offset 0x4e00
	0x4e00: 0x00c3, 0x4e01: 0x00c3, 0x4e02: 0x00c3, 0x4e03: 0x00c3, 0x4e04: 0x00c3, 0x4e05: 0x00c3,
	0x4e06: 0x00c3, 0x4e07: 0x00c3, 0x4e08: 0x00c3, 0x4e09: 0x00c3, 0x4e0a: 0x00c3, 0x4e0b: 0x00c3,
	0x4e0c: 0x00c3, 0x4e0d: 0x00c3, 0x4e0e: 0x00c3, 0x4e0f: 0x00c3, 0x4e10: 0x00c3, 0x4e11: 0x00c3,
	0x4e12: 0x00c3, 0x4e13: 0x00c3, 0x4e14: 0x00c3, 0x4e15: 0x00c3, 0x4e16: 0x00c3, 0x4e17: 0x00c3,
	0x4e18: 0x00c3, 0x4e19: 0x00c3, 0x4e1a: 0x00c3, 0x4e1b: 0x00c3, 0x4e1c: 0x00c3, 0x4e1d: 0x00c3,
	0x4e1e: 0x00c3, 0x4e1f: 0x00c3, 0x4e20: 0x00c3, 0x4e21: 0x00c3, 0x4e22: 0x00c3, 0x4e23: 0x00c3,
	0x4e24: 0x00c3, 0x4e25: 0x00c3, 0x4e26: 0x00c3, 0x4e27: 0x00c3, 0x4e28: 0x00c3, 0x4e29: 0x00c3,
	0x4e2a: 0x00c3, 0x4e2b: 0x00c3, 0x4e2c: 0x00c3, 0x4e2d: 0x0080, 0x4e2e: 0x0080, 0x4e2f: 0x0080,
	0x4e30: 0x0080, 0x4e31: 0x0080, 0x4e32: 0x0080, 0x4e33: 0x0080, 0x4e34: 0x0080, 0x4e35: 0x00c3,
	0x4e36: 0x0080, 0x4e37: 0x0080, 0x4e38: 0x0080, 0x4e39: 0x0080, 0x4e3a: 0x0080, 0x4e3b: 0x0080,
	0x4e3c: 0x0080, 0x4e3d: 0x0080, 0x4e3e: 0x0080, 0x4e3f: 0x0080,
	// Block 0x139, offset 0x4e40
	0x4e40: 0x0080, 0x4e41: 0x0080, 0x4e42: 0x0080, 0x4e43: 0x0080, 0x4e44: 0x00c3, 0x4e45: 0x0080,
	0x4e46: 0x0080, 0x4e47: 0x0080, 0x4e48: 0x0080, 0x4e49: 0x0080, 0x4e4a: 0x0080, 0x4e4b: 0x0080,
	0x4e5b: 0x00c3, 0x4e5c: 0x00c3, 0x4e5d: 0x00c3,
	0x4e5e: 0x00c3, 0x4e5f: 0x00c3, 0x4e61: 0x00c3, 0x4e62: 0x00c3, 0x4e63: 0x00c3,
	0x4e64: 0x00c3, 0x4e65: 0x00c3, 0x4e66: 0x00c3, 0x4e67: 0x00c3, 0x4e68: 0x00c3, 0x4e69: 0x00c3,
	0x4e6a: 0x00c3, 0x4e6b: 0x00c3, 0x4e6c: 0x00c3, 0x4e6d: 0x00c3, 0x4e6e: 0x00c3, 0x4e6f: 0x00c3,
	// Block 0x13a, offset 0x4e80
	0x4e80: 0x00c3, 0x4e81: 0x00c3, 0x4e82: 0x00c3, 0x4e83: 0x00c3, 0x4e84: 0x00c3, 0x4e85: 0x00c3,
	0x4e86: 0x00c3, 0x4e88: 0x00c3, 0x4e89: 0x00c3, 0x4e8a: 0x00c3, 0x4e8b: 0x00c3,
	0x4e8c: 0x00c3, 0x4e8d: 0x00c3, 0x4e8e: 0x00c3, 0x4e8f: 0x00c3, 0x4e90: 0x00c3, 0x4e91: 0x00c3,
	0x4e92: 0x00c3, 0x4e93: 0x00c3, 0x4e94: 0x00c3, 0x4e95: 0x00c3, 0x4e96: 0x00c3, 0x4e97: 0x00c3,
	0x4e98: 0x00c3, 0x4e9b: 0x00c3, 0x4e9c: 0x00c3, 0x4e9d: 0x00c3,
	0x4e9e: 0x00c3, 0x4e9f: 0x00c3, 0x4ea0: 0x00c3, 0x4ea1: 0x00c3, 0x4ea3: 0x00c3,
	0x4ea4: 0x00c3, 0x4ea6: 0x00c3, 0x4ea7: 0x00c3, 0x4ea8: 0x00c3, 0x4ea9: 0x00c3,
	0x4eaa: 0x00c3,
	// Block 0x13b, offset 0x4ec0
	0x4ec0: 0x00c0, 0x4ec1: 0x00c0, 0x4ec2: 0x00c0, 0x4ec3: 0x00c0, 0x4ec4: 0x00c0, 0x4ec5: 0x00c0,
	0x4ec6: 0x00c0, 0x4ec7: 0x00c0, 0x4ec8: 0x00c0, 0x4ec9: 0x00c0, 0x4eca: 0x00c0, 0x4ecb: 0x00c0,
	0x4ecc: 0x00c0, 0x4ecd: 0x00c0, 0x4ece: 0x00c0, 0x4ecf: 0x00c0, 0x4ed0: 0x00c0, 0x4ed1: 0x00c0,
	0x4ed2: 0x00c0, 0x4ed3: 0x00c0, 0x4ed4: 0x00c0, 0x4ed5: 0x00c0, 0x4ed6: 0x00c0, 0x4ed7: 0x00c0,
	0x4ed8: 0x00c0, 0x4ed9: 0x00c0, 0x4eda: 0x00c0, 0x4edb: 0x00c0, 0x4edc: 0x00c0, 0x4edd: 0x00c0,
	0x4ede: 0x00c0, 0x4edf: 0x00c0, 0x4ee0: 0x00c0, 0x4ee1: 0x00c0, 0x4ee2: 0x00c0, 0x4ee3: 0x00c0,
	0x4ee4: 0x00c0, 0x4ee5: 0x00c0, 0x4ee6: 0x00c0, 0x4ee7: 0x00c0, 0x4ee8: 0x00c0, 0x4ee9: 0x00c0,
	0x4eea: 0x00c0, 0x4eeb: 0x00c0, 0x4eec: 0x00c0,
	0x4ef0: 0x00c3, 0x4ef1: 0x00c3, 0x4ef2: 0x00c3, 0x4ef3: 0x00c3, 0x4ef4: 0x00c3, 0x4ef5: 0x00c3,
	0x4ef6: 0x00c3, 0x4ef7: 0x00c0, 0x4ef8: 0x00c0, 0x4ef9: 0x00c0, 0x4efa: 0x00c0, 0x4efb: 0x00c0,
	0x4efc: 0x00c0, 0x4efd: 0x00c0,
	// Block 0x13c, offset 0x4f00
	0x4f00: 0x00c0, 0x4f01: 0x00c0, 0x4f02: 0x00c0, 0x4f03: 0x00c0, 0x4f04: 0x00c0, 0x4f05: 0x00c0,
	0x4f06: 0x00c0, 0x4f07: 0x00c0, 0x4f08: 0x00c0, 0x4f09: 0x00c0,
	0x4f0e: 0x00c0, 0x4f0f: 0x0080,
	// Block 0x13d, offset 0x4f40
	0x4f40: 0x00c0, 0x4f41: 0x00c0, 0x4f42: 0x00c0, 0x4f43: 0x00c0, 0x4f44: 0x00c0, 0x4f45: 0x00c0,
	0x4f46: 0x00c0, 0x4f47: 0x00c0, 0x4f48: 0x00c0, 0x4f49: 0x00c0, 0x4f4a: 0x00c0, 0x4f4b: 0x00c0,
	0x4f4c: 0x00c0, 0x4f4d: 0x00c0, 0x4f4e: 0x00c0, 0x4f4f: 0x00c0, 0x4f50: 0x00c0, 0x4f51: 0x00c0,
	0x4f52: 0x00c0, 0x4f53: 0x00c0, 0x4f54: 0x00c0, 0x4f55: 0x00c0, 0x4f56: 0x00c0, 0x4f57: 0x00c0,
	0x4f58: 0x00c0, 0x4f59: 0x00c0, 0x4f5a: 0x00c0, 0x4f5b: 0x00c0, 0x4f5c: 0x00c0, 0x4f5d: 0x00c0,
	0x4f5e: 0x00c0, 0x4f5f: 0x00c0, 0x4f60: 0x00c0, 0x4f61: 0x00c0, 0x4f62: 0x00c0, 0x4f63: 0x00c0,
	0x4f64: 0x00c0, 0x4f65: 0x00c0, 0x4f66: 0x00c0, 0x4f67: 0x00c0, 0x4f68: 0x00c0, 0x4f69: 0x00c0,
	0x4f6a: 0x00c0, 0x4f6b: 0x00c0, 0x4f6c: 0x00c3, 0x4f6d: 0x00c3, 0x4f6e: 0x00c3, 0x4f6f: 0x00c3,
	0x4f70: 0x00c0, 0x4f71: 0x00c0, 0x4f72: 0x00c0, 0x4f73: 0x00c0, 0x4f74: 0x00c0, 0x4f75: 0x00c0,
	0x4f76: 0x00c0, 0x4f77: 0x00c0, 0x4f78: 0x00c0, 0x4f79: 0x00c0,
	0x4f7f: 0x0080,
	// Block 0x13e, offset 0x4f80
	0x4f80: 0x00c0, 0x4f81: 0x00c0, 0x4f82: 0x00c0, 0x4f83: 0x00c0, 0x4f84: 0x00c0,
	0x4f87: 0x0080, 0x4f88: 0x0080, 0x4f89: 0x0080, 0x4f8a: 0x0080, 0x4f8b: 0x0080,
	0x4f8c: 0x0080, 0x4f8d: 0x0080, 0x4f8e: 0x0080, 0x4f8f: 0x0080, 0x4f90: 0x00c3, 0x4f91: 0x00c3,
	0x4f92: 0x00c3, 0x4f93: 0x00c3, 0x4f94: 0x00c3, 0x4f95: 0x00c3, 0x4f96: 0x00c3,
	// Block 0x13f, offset 0x4fc0
	0x4fc0: 0x00c2, 0x4fc1: 0x00c2, 0x4fc2: 0x00c2, 0x4fc3: 0x00c2, 0x4fc4: 0x00c2, 0x4fc5: 0x00c2,
	0x4fc6: 0x00c2, 0x4fc7: 0x00c2, 0x4fc8: 0x00c2, 0x4fc9: 0x00c2, 0x4fca: 0x00c2, 0x4fcb: 0x00c2,
	0x4fcc: 0x00c2, 0x4fcd: 0x00c2, 0x4fce: 0x00c2, 0x4fcf: 0x00c2, 0x4fd0: 0x00c2, 0x4fd1: 0x00c2,
	0x4fd2: 0x00c2, 0x4fd3: 0x00c2, 0x4fd4: 0x00c2, 0x4fd5: 0x00c2, 0x4fd6: 0x00c2, 0x4fd7: 0x00c2,
	0x4fd8: 0x00c2, 0x4fd9: 0x00c2, 0x4fda: 0x00c2, 0x4fdb: 0x00c2, 0x4fdc: 0x00c2, 0x4fdd: 0x00c2,
	0x4fde: 0x00c2, 0x4fdf: 0x00c2, 0x4fe0: 0x00c2, 0x4fe1: 0x00c2, 0x4fe2: 0x00c2, 0x4fe3: 0x00c2,
	0x4fe4: 0x00c2, 0x4fe5: 0x00c2, 0x4fe6: 0x00c2, 0x4fe7: 0x00c2, 0x4fe8: 0x00c2, 0x4fe9: 0x00c2,
	0x4fea: 0x00c2, 0x4feb: 0x00c2, 0x4fec: 0x00c2, 0x4fed: 0x00c2, 0x4fee: 0x00c2, 0x4fef: 0x00c2,
	0x4ff0: 0x00c2, 0x4ff1: 0x00c2, 0x4ff2: 0x00c2, 0x4ff3: 0x00c2, 0x4ff4: 0x00c2, 0x4ff5: 0x00c2,
	0x4ff6: 0x00c2, 0x4ff7: 0x00c2, 0x4ff8: 0x00c2, 0x4ff9: 0x00c2, 0x4ffa: 0x00c2, 0x4ffb: 0x00c2,
	0x4ffc: 0x00c2, 0x4ffd: 0x00c2, 0x4ffe: 0x00c2, 0x4fff: 0x00c2,
	// Block 0x140, offset 0x5000
	0x5000: 0x00c2, 0x5001: 0x00c2, 0x5002: 0x00c2, 0x5003: 0x00c2, 0x5004: 0x00c3, 0x5005: 0x00c3,
	0x5006: 0x00c3, 0x5007: 0x00c3, 0x5008: 0x00c3, 0x5009: 0x00c3, 0x500a: 0x00c3, 0x500b: 0x00c3,
	0x5010: 0x00c0, 0x5011: 0x00c0,
	0x5012: 0x00c0, 0x5013: 0x00c0, 0x5014: 0x00c0, 0x5015: 0x00c0, 0x5016: 0x00c0, 0x5017: 0x00c0,
	0x5018: 0x00c0, 0x5019: 0x00c0,
	0x501e: 0x0080, 0x501f: 0x0080,
	// Block 0x141, offset 0x5040
	0x5071: 0x0080, 0x5072: 0x0080, 0x5073: 0x0080, 0x5074: 0x0080, 0x5075: 0x0080,
	0x5076: 0x0080, 0x5077: 0x0080, 0x5078: 0x0080, 0x5079: 0x0080, 0x507a: 0x0080, 0x507b: 0x0080,
	0x507c: 0x0080, 0x507d: 0x0080, 0x507e: 0x0080, 0x507f: 0x0080,
	// Block 0x142, offset 0x5080
	0x5080: 0x0080, 0x5081: 0x0080, 0x5082: 0x0080, 0x5083: 0x0080, 0x5084: 0x0080, 0x5085: 0x0080,
	0x5086: 0x0080, 0x5087: 0x0080, 0x5088: 0x0080, 0x5089: 0x0080, 0x508a: 0x0080, 0x508b: 0x0080,
	0x508c: 0x0080, 0x508d: 0x0080, 0x508e: 0x0080, 0x508f: 0x0080, 0x5090: 0x0080, 0x5091: 0x0080,
	0x5092: 0x0080, 0x5093: 0x0080, 0x5094: 0x0080, 0x5095: 0x0080, 0x5096: 0x0080, 0x5097: 0x0080,
	0x5098: 0x0080, 0x5099: 0x0080, 0x509a: 0x0080, 0x509b: 0x0080, 0x509c: 0x0080, 0x509d: 0x0080,
	0x509e: 0x0080, 0x509f: 0x0080, 0x50a0: 0x0080, 0x50a1: 0x0080, 0x50a2: 0x0080, 0x50a3: 0x0080,
	0x50a4: 0x0080, 0x50a5: 0x0080, 0x50a6: 0x0080, 0x50a7: 0x0080, 0x50a8: 0x0080, 0x50a9: 0x0080,
	0x50aa: 0x0080, 0x50ab: 0x0080, 0x50ac: 0x0080, 0x50ad: 0x0080, 0x50ae: 0x0080, 0x50af: 0x0080,
	0x50b0: 0x0080, 0x50b1: 0x0080, 0x50b2: 0x0080, 0x50b3: 0x0080, 0x50b4: 0x0080,
	// Block 0x143, offset 0x50c0
	0x50c1: 0x0080, 0x50c2: 0x0080, 0x50c3: 0x0080, 0x50c4: 0x0080, 0x50c5: 0x0080,
	0x50c6: 0x0080, 0x50c7: 0x0080, 0x50c8: 0x0080, 0x50c9: 0x0080, 0x50ca: 0x0080, 0x50cb: 0x0080,
	0x50cc: 0x0080, 0x50cd: 0x0080, 0x50ce: 0x0080, 0x50cf: 0x0080, 0x50d0: 0x0080, 0x50d1: 0x0080,
	0x50d2: 0x0080, 0x50d3: 0x0080, 0x50d4: 0x0080, 0x50d5: 0x0080, 0x50d6: 0x0080, 0x50d7: 0x0080,
	0x50d8: 0x0080, 0x50d9: 0x0080, 0x50da: 0x0080, 0x50db: 0x0080, 0x50dc: 0x0080, 0x50dd: 0x0080,
	0x50de: 0x0080, 0x50df: 0x0080, 0x50e0: 0x0080, 0x50e1: 0x0080, 0x50e2: 0x0080, 0x50e3: 0x0080,
	0x50e4: 0x0080, 0x50e5: 0x0080, 0x50e6: 0x0080, 0x50e7: 0x0080, 0x50e8: 0x0080, 0x50e9: 0x0080,
	0x50ea: 0x0080, 0x50eb: 0x0080, 0x50ec: 0x0080, 0x50ed: 0x0080, 0x50ee: 0x0080, 0x50ef: 0x0080,
	0x50f0: 0x0080, 0x50f1: 0x0080, 0x50f2: 0x0080, 0x50f3: 0x0080, 0x50f4: 0x0080, 0x50f5: 0x0080,
	0x50f6: 0x0080, 0x50f7: 0x0080, 0x50f8: 0x0080, 0x50f9: 0x0080, 0x50fa: 0x0080, 0x50fb: 0x0080,
	0x50fc: 0x0080, 0x50fd: 0x0080,
	// Block 0x144, offset 0x5100
	0x5100: 0x0080, 0x5101: 0x0080, 0x5102: 0x0080, 0x5103: 0x0080, 0x5105: 0x0080,
	0x5106: 0x0080, 0x5107: 0x0080, 0x5108: 0x0080, 0x5109: 0x0080, 0x510a: 0x0080, 0x510b: 0x0080,
	0x510c: 0x0080, 0x510d: 0x0080, 0x510e: 0x0080, 0x510f: 0x0080, 0x5110: 0x0080, 0x5111: 0x0080,
	0x5112: 0x0080, 0x5113: 0x0080, 0x5114: 0x0080, 0x5115: 0x0080, 0x5116: 0x0080, 0x5117: 0x0080,
	0x5118: 0x0080, 0x5119: 0x0080, 0x511a: 0x0080, 0x511b: 0x0080, 0x511c: 0x0080, 0x511d: 0x0080,
	0x511e: 0x0080, 0x511f: 0x0080, 0x5121: 0x0080, 0x5122: 0x0080,
	0x5124: 0x0080, 0x5127: 0x0080, 0x5129: 0x0080,
	0x512a: 0x0080, 0x512b: 0x0080, 0x512c: 0x0080, 0x512d: 0x0080, 0x512e: 0x0080, 0x512f: 0x0080,
	0x5130: 0x0080, 0x5131: 0x0080, 0x5132: 0x0080, 0x5134: 0x0080, 0x5135: 0x0080,
	0x5136: 0x0080, 0x5137: 0x0080, 0x5139: 0x0080, 0x513b: 0x0080,
	// Block 0x145, offset 0x5140
	0x5142: 0x0080,
	0x5147: 0x0080, 0x5149: 0x0080, 0x514b: 0x0080,
	0x514d: 0x0080, 0x514e: 0x0080, 0x514f: 0x0080, 0x5151: 0x0080,
	0x5152: 0x0080, 0x5154: 0x0080, 0x5157: 0x0080,
	0x5159: 0x0080, 0x515b: 0x0080, 0x515d: 0x0080,
	0x515f: 0x0080, 0x5161: 0x0080, 0x5162: 0x0080,
	0x5164: 0x0080, 0x5167: 0x0080, 0x5168: 0x0080, 0x5169: 0x0080,
	0x516a: 0x0080, 0x516c: 0x0080, 0x516d: 0x0080, 0x516e: 0x0080, 0x516f: 0x0080,
	0x5170: 0x0080, 0x5171: 0x0080, 0x5172: 0x0080, 0x5174: 0x0080, 0x5175: 0x0080,
	0x5176: 0x0080, 0x5177: 0x0080, 0x5179: 0x0080, 0x517a: 0x0080, 0x517b: 0x0080,
	0x517c: 0x0080, 0x517e: 0x0080,
	// Block 0x146, offset 0x5180
	0x5180: 0x0080, 0x5181: 0x0080, 0x5182: 0x0080, 0x5183: 0x0080, 0x5184: 0x0080, 0x5185: 0x0080,
	0x5186: 0x0080, 0x5187: 0x0080, 0x5188: 0x0080, 0x5189: 0x0080, 0x518b: 0x0080,
	0x518c: 0x0080, 0x518d: 0x0080, 0x518e: 0x0080, 0x518f: 0x0080, 0x5190: 0x0080, 0x5191: 0x0080,
	0x5192: 0x0080, 0x5193: 0x0080, 0x5194: 0x0080, 0x5195: 0x0080, 0x5196: 0x0080, 0x5197: 0x0080,
	0x5198: 0x0080, 0x5199: 0x0080, 0x519a: 0x0080, 0x519b: 0x0080,
	0x51a1: 0x0080, 0x51a2: 0x0080, 0x51a3: 0x0080,
	0x51a5: 0x0080, 0x51a6: 0x0080, 0x51a7: 0x0080, 0x51a8: 0x0080, 0x51a9: 0x0080,
	0x51ab: 0x0080, 0x51ac: 0x0080, 0x51ad: 0x0080, 0x51ae: 0x0080, 0x51af: 0x0080,
	0x51b0: 0x0080, 0x51b1: 0x0080, 0x51b2: 0x0080, 0x51b3: 0x0080, 0x51b4: 0x0080, 0x51b5: 0x0080,
	0x51b6: 0x0080, 0x51b7: 0x0080, 0x51b8: 0x0080, 0x51b9: 0x0080, 0x51ba: 0x0080, 0x51bb: 0x0080,
	// Block 0x147, offset 0x51c0
	0x51f0: 0x0080, 0x51f1: 0x0080,
	// Block 0x148, offset 0x5200
	0x5200: 0x0080, 0x5201: 0x0080, 0x5202: 0x0080, 0x5203: 0x0080, 0x5204: 0x0080, 0x5205: 0x0080,
	0x5206: 0x0080, 0x5207: 0x0080, 0x5208: 0x0080, 0x5209: 0x0080, 0x520a: 0x0080, 0x520b: 0x0080,
	0x520c: 0x0080, 0x520d: 0x0080, 0x520e: 0x0080, 0x520f: 0x0080, 0x5210: 0x0080, 0x5211: 0x0080,
	0x5212: 0x0080, 0x5213: 0x0080, 0x5214: 0x0080, 0x5215: 0x0080, 0x5216: 0x0080, 0x5217: 0x0080,
	0x5218: 0x0080, 0x5219: 0x0080, 0x521a: 0x0080, 0x521b: 0x0080, 0x521c: 0x0080, 0x521d: 0x0080,
	0x521e: 0x0080, 0x521f: 0x0080, 0x5220: 0x0080, 0x5221: 0x0080, 0x5222: 0x0080, 0x5223: 0x0080,
	0x5224: 0x0080, 0x5225: 0x0080, 0x5226: 0x0080, 0x5227: 0x0080, 0x5228: 0x0080, 0x5229: 0x0080,
	0x522a: 0x0080, 0x522b: 0x0080,
	0x5230: 0x0080, 0x5231: 0x0080, 0x5232: 0x0080, 0x5233: 0x0080, 0x5234: 0x0080, 0x5235: 0x0080,
	0x5236: 0x0080, 0x5237: 0x0080, 0x5238: 0x0080, 0x5239: 0x0080, 0x523a: 0x0080, 0x523b: 0x0080,
	0x523c: 0x0080, 0x523d: 0x0080, 0x523e: 0x0080, 0x523f: 0x0080,
	// Block 0x149, offset 0x5240
	0x5240: 0x0080, 0x5241: 0x0080, 0x5242: 0x0080, 0x5243: 0x0080, 0x5244: 0x0080, 0x5245: 0x0080,
	0x5246: 0x0080, 0x5247: 0x0080, 0x5248: 0x0080, 0x5249: 0x0080, 0x524a: 0x0080, 0x524b: 0x0080,
	0x524c: 0x0080, 0x524d: 0x0080, 0x524e: 0x0080, 0x524f: 0x0080, 0x5250: 0x0080, 0x5251: 0x0080,
	0x5252: 0x0080, 0x5253: 0x0080,
	0x5260: 0x0080, 0x5261: 0x0080, 0x5262: 0x0080, 0x5263: 0x0080,
	0x5264: 0x0080, 0x5265: 0x0080, 0x5266: 0x0080, 0x5267: 0x0080, 0x5268: 0x0080, 0x5269: 0x0080,
	0x526a: 0x0080, 0x526b: 0x0080, 0x526c: 0x0080, 0x526d: 0x0080, 0x526e: 0x0080,
	0x5271: 0x0080, 0x5272: 0x0080, 0x5273: 0x0080, 0x5274: 0x0080, 0x5275: 0x0080,
	0x5276: 0x0080, 0x5277: 0x0080, 0x5278: 0x0080, 0x5279: 0x0080, 0x527a: 0x0080, 0x527b: 0x0080,
	0x527c: 0x0080, 0x527d: 0x0080, 0x527e: 0x0080, 0x527f: 0x0080,
	// Block 0x14a, offset 0x5280
	0x5281: 0x0080, 0x5282: 0x0080, 0x5283: 0x0080, 0x5284: 0x0080, 0x5285: 0x0080,
	0x5286: 0x0080, 0x5287: 0x0080, 0x5288: 0x0080, 0x5289: 0x0080, 0x528a: 0x0080, 0x528b: 0x0080,
	0x528c: 0x0080, 0x528d: 0x0080, 0x528e: 0x0080, 0x528f: 0x0080, 0x5291: 0x0080,
	0x5292: 0x0080, 0x5293: 0x0080, 0x5294: 0x0080, 0x5295: 0x0080, 0x5296: 0x0080, 0x5297: 0x0080,
	0x5298: 0x0080, 0x5299: 0x0080, 0x529a: 0x0080, 0x529b: 0x0080, 0x529c: 0x0080, 0x529d: 0x0080,
	0x529e: 0x0080, 0x529f: 0x0080, 0x52a0: 0x0080, 0x52a1: 0x0080, 0x52a2: 0x0080, 0x52a3: 0x0080,
	0x52a4: 0x0080, 0x52a5: 0x0080, 0x52a6: 0x0080, 0x52a7: 0x0080, 0x52a8: 0x0080, 0x52a9: 0x0080,
	0x52aa: 0x0080, 0x52ab: 0x0080, 0x52ac: 0x0080, 0x52ad: 0x0080, 0x52ae: 0x0080, 0x52af: 0x0080,
	0x52b0: 0x0080, 0x52b1: 0x0080, 0x52b2: 0x0080, 0x52b3: 0x0080, 0x52b4: 0x0080, 0x52b5: 0x0080,
	// Block 0x14b, offset 0x52c0
	0x52c0: 0x0080, 0x52c1: 0x0080, 0x52c2: 0x0080, 0x52c3: 0x0080, 0x52c4: 0x0080, 0x52c5: 0x0080,
	0x52c6: 0x0080, 0x52c7: 0x0080, 0x52c8: 0x0080, 0x52c9: 0x0080, 0x52ca: 0x0080, 0x52cb: 0x0080,
	0x52cc: 0x0080, 0x52cd: 0x0080, 0x52ce: 0x0080, 0x52cf: 0x0080, 0x52d0: 0x0080, 0x52d1: 0x0080,
	0x52d2: 0x0080, 0x52d3: 0x0080, 0x52d4: 0x0080, 0x52d5: 0x0080, 0x52d6: 0x0080, 0x52d7: 0x0080,
	0x52d8: 0x0080, 0x52d9: 0x0080, 0x52da: 0x0080, 0x52db: 0x0080, 0x52dc: 0x0080, 0x52dd: 0x0080,
	0x52de: 0x0080, 0x52df: 0x0080, 0x52e0: 0x0080, 0x52e1: 0x0080, 0x52e2: 0x0080, 0x52e3: 0x0080,
	0x52e4: 0x0080, 0x52e5: 0x0080, 0x52e6: 0x0080, 0x52e7: 0x0080, 0x52e8: 0x0080, 0x52e9: 0x0080,
	0x52ea: 0x0080, 0x52eb: 0x0080, 0x52ec: 0x0080, 0x52ed: 0x0080,
	// Block 0x14c, offset 0x5300
	0x5326: 0x0080, 0x5327: 0x0080, 0x5328: 0x0080, 0x5329: 0x0080,
	0x532a: 0x0080, 0x532b: 0x0080, 0x532c: 0x0080, 0x532d: 0x0080, 0x532e: 0x0080, 0x532f: 0x0080,
	0x5330: 0x0080, 0x5331: 0x0080, 0x5332: 0x0080, 0x5333: 0x0080, 0x5334: 0x0080, 0x5335: 0x0080,
	0x5336: 0x0080, 0x5337: 0x0080, 0x5338: 0x0080, 0x5339: 0x0080, 0x533a: 0x0080, 0x533b: 0x0080,
	0x533c: 0x0080, 0x533d: 0x0080, 0x533e: 0x0080, 0x533f: 0x0080,
	// Block 0x14d, offset 0x5340
	0x5340: 0x008c, 0x5341: 0x0080, 0x5342: 0x0080,
	0x5350: 0x0080, 0x5351: 0x0080,
	0x5352: 0x0080, 0x5353: 0x0080, 0x5354: 0x0080, 0x5355: 0x0080, 0x5356: 0x0080, 0x5357: 0x0080,
	0x5358: 0x0080, 0x5359: 0x0080, 0x535a: 0x0080, 0x535b: 0x0080, 0x535c: 0x0080, 0x535d: 0x0080,
	0x535e: 0x0080, 0x535f: 0x0080, 0x5360: 0x0080, 0x5361: 0x0080, 0x5362: 0x0080, 0x5363: 0x0080,
	0x5364: 0x0080, 0x5365: 0x0080, 0x5366: 0x0080, 0x5367: 0x0080, 0x5368: 0x0080, 0x5369: 0x0080,
	0x536a: 0x0080, 0x536b: 0x0080, 0x536c: 0x0080, 0x536d: 0x0080, 0x536e: 0x0080, 0x536f: 0x0080,
	0x5370: 0x0080, 0x5371: 0x0080, 0x5372: 0x0080, 0x5373: 0x0080, 0x5374: 0x0080, 0x5375: 0x0080,
	0x5376: 0x0080, 0x5377: 0x0080, 0x5378: 0x0080, 0x5379: 0x0080, 0x537a: 0x0080, 0x537b: 0x0080,
	// Block 0x14e, offset 0x5380
	0x5380: 0x0080, 0x5381: 0x0080, 0x5382: 0x0080, 0x5383: 0x0080, 0x5384: 0x0080, 0x5385: 0x0080,
	0x5386: 0x0080, 0x5387: 0x0080, 0x5388: 0x0080,
	0x5390: 0x0080, 0x5391: 0x0080,
	0x53a0: 0x0080, 0x53a1: 0x0080, 0x53a2: 0x0080, 0x53a3: 0x0080,
	0x53a4: 0x0080, 0x53a5: 0x0080,
	// Block 0x14f, offset 0x53c0
	0x53c0: 0x0080, 0x53c1: 0x0080, 0x53c2: 0x0080, 0x53c3: 0x0080, 0x53c4: 0x0080, 0x53c5: 0x0080,
	0x53c6: 0x0080, 0x53c7: 0x0080, 0x53c8: 0x0080, 0x53c9: 0x0080, 0x53ca: 0x0080, 0x53cb: 0x0080,
	0x53cc: 0x0080, 0x53cd: 0x0080, 0x53ce: 0x0080, 0x53cf: 0x0080, 0x53d0: 0x0080, 0x53d1: 0x0080,
	0x53d2: 0x0080, 0x53d3: 0x0080, 0x53d4: 0x0080, 0x53d5: 0x0080, 0x53d6: 0x0080, 0x53d7: 0x0080,
	0x53e0: 0x0080, 0x53e1: 0x0080, 0x53e2: 0x0080, 0x53e3: 0x0080,
	0x53e4: 0x0080, 0x53e5: 0x0080, 0x53e6: 0x0080, 0x53e7: 0x0080, 0x53e8: 0x0080, 0x53e9: 0x0080,
	0x53ea: 0x0080, 0x53eb: 0x0080, 0x53ec: 0x0080,
	0x53f0: 0x0080, 0x53f1: 0x0080, 0x53f2: 0x0080, 0x53f3: 0x0080, 0x53f4: 0x0080, 0x53f5: 0x0080,
	0x53f6: 0x0080, 0x53f7: 0x0080, 0x53f8: 0x0080, 0x53f9: 0x0080, 0x53fa: 0x0080, 0x53fb: 0x0080,
	0x53fc: 0x0080,
	// Block 0x150, offset 0x5400
	0x5400: 0x0080, 0x5401: 0x0080, 0x5402: 0x0080, 0x5403: 0x0080, 0x5404: 0x0080, 0x5405: 0x0080,
	0x5406: 0x0080, 0x5407: 0x0080, 0x5408: 0x0080, 0x5409: 0x0080, 0x540a: 0x0080, 0x540b: 0x0080,
	0x540c: 0x0080, 0x540d: 0x0080, 0x540e: 0x0080, 0x540f: 0x0080, 0x5410: 0x0080, 0x5411: 0x0080,
	0x5412: 0x0080, 0x5413: 0x0080, 0x5414: 0x0080, 0x5415: 0x0080, 0x5416: 0x0080, 0x5417: 0x0080,
	0x5418: 0x0080, 0x5419: 0x0080, 0x541a: 0x0080, 0x541b: 0x0080, 0x541c: 0x0080, 0x541d: 0x0080,
	0x541e: 0x0080, 0x541f: 0x0080, 0x5420: 0x0080, 0x5421: 0x0080, 0x5422: 0x0080, 0x5423: 0x0080,
	0x5424: 0x0080, 0x5425: 0x0080, 0x5426: 0x0080, 0x5427: 0x0080, 0x5428: 0x0080, 0x5429: 0x0080,
	0x542a: 0x0080, 0x542b: 0x0080, 0x542c: 0x0080, 0x542d: 0x0080, 0x542e: 0x0080, 0x542f: 0x0080,
	0x5430: 0x0080, 0x5431: 0x0080, 0x5432: 0x0080, 0x5433: 0x0080,
	// Block 0x151, offset 0x5440
	0x5440: 0x0080, 0x5441: 0x0080, 0x5442: 0x0080, 0x5443: 0x0080, 0x5444: 0x0080, 0x5445: 0x0080,
	0x5446: 0x0080, 0x5447: 0x0080, 0x5448: 0x0080, 0x5449: 0x0080, 0x544a: 0x0080, 0x544b: 0x0080,
	0x544c: 0x0080, 0x544d: 0x0080, 0x544e: 0x0080, 0x544f: 0x0080, 0x5450: 0x0080, 0x5451: 0x0080,
	0x5452: 0x0080, 0x5453: 0x0080, 0x5454: 0x0080, 0x5455: 0x0080, 0x5456: 0x0080, 0x5457: 0x0080,
	0x5458: 0x0080,
	0x5460: 0x0080, 0x5461: 0x0080, 0x5462: 0x0080, 0x5463: 0x0080,
	0x5464: 0x0080, 0x5465: 0x0080, 0x5466: 0x0080, 0x5467: 0x0080, 0x5468: 0x0080, 0x5469: 0x0080,
	0x546a: 0x0080, 0x546b: 0x0080,
	// Block 0x152, offset 0x5480
	0x5480: 0x0080, 0x5481: 0x0080, 0x5482: 0x0080, 0x5483: 0x0080, 0x5484: 0x0080, 0x5485: 0x0080,
	0x5486: 0x0080, 0x5487: 0x0080, 0x5488: 0x0080, 0x5489: 0x0080, 0x548a: 0x0080, 0x548b: 0x0080,
	0x5490: 0x0080, 0x5491: 0x0080,
	0x5492: 0x0080, 0x5493: 0x0080, 0x5494: 0x0080, 0x5495: 0x0080, 0x5496: 0x0080, 0x5497: 0x0080,
	0x5498: 0x0080, 0x5499: 0x0080, 0x549a: 0x0080, 0x549b: 0x0080, 0x549c: 0x0080, 0x549d: 0x0080,
	0x549e: 0x0080, 0x549f: 0x0080, 0x54a0: 0x0080, 0x54a1: 0x0080, 0x54a2: 0x0080, 0x54a3: 0x0080,
	0x54a4: 0x0080, 0x54a5: 0x0080, 0x54a6: 0x0080, 0x54a7: 0x0080, 0x54a8: 0x0080, 0x54a9: 0x0080,
	0x54aa: 0x0080, 0x54ab: 0x0080, 0x54ac: 0x0080, 0x54ad: 0x0080, 0x54ae: 0x0080, 0x54af: 0x0080,
	0x54b0: 0x0080, 0x54b1: 0x0080, 0x54b2: 0x0080, 0x54b3: 0x0080, 0x54b4: 0x0080, 0x54b5: 0x0080,
	0x54b6: 0x0080, 0x54b7: 0x0080, 0x54b8: 0x0080, 0x54b9: 0x0080, 0x54ba: 0x0080, 0x54bb: 0x0080,
	0x54bc: 0x0080, 0x54bd: 0x0080, 0x54be: 0x0080, 0x54bf: 0x0080,
	// Block 0x153, offset 0x54c0
	0x54c0: 0x0080, 0x54c1: 0x0080, 0x54c2: 0x0080, 0x54c3: 0x0080, 0x54c4: 0x0080, 0x54c5: 0x0080,
	0x54c6: 0x0080, 0x54c7: 0x0080,
	0x54d0: 0x0080, 0x54d1: 0x0080,
	0x54d2: 0x0080, 0x54d3: 0x0080, 0x54d4: 0x0080, 0x54d5: 0x0080, 0x54d6: 0x0080, 0x54d7: 0x0080,
	0x54d8: 0x0080, 0x54d9: 0x0080,
	0x54e0: 0x0080, 0x54e1: 0x0080, 0x54e2: 0x0080, 0x54e3: 0x0080,
	0x54e4: 0x0080, 0x54e5: 0x0080, 0x54e6: 0x0080, 0x54e7: 0x0080, 0x54e8: 0x0080, 0x54e9: 0x0080,
	0x54ea: 0x0080, 0x54eb: 0x0080, 0x54ec: 0x0080, 0x54ed: 0x0080, 0x54ee: 0x0080, 0x54ef: 0x0080,
	0x54f0: 0x0080, 0x54f1: 0x0080, 0x54f2: 0x0080, 0x54f3: 0x0080, 0x54f4: 0x0080, 0x54f5: 0x0080,
	0x54f6: 0x0080, 0x54f7: 0x0080, 0x54f8: 0x0080, 0x54f9: 0x0080, 0x54fa: 0x0080, 0x54fb: 0x0080,
	0x54fc: 0x0080, 0x54fd: 0x0080, 0x54fe: 0x0080, 0x54ff: 0x0080,
	// Block 0x154, offset 0x5500
	0x5500: 0x0080, 0x5501: 0x0080, 0x5502: 0x0080, 0x5503: 0x0080, 0x5504: 0x0080, 0x5505: 0x0080,
	0x5506: 0x0080, 0x5507: 0x0080,
	0x5510: 0x0080, 0x5511: 0x0080,
	0x5512: 0x0080, 0x5513: 0x0080, 0x5514: 0x0080, 0x5515: 0x0080, 0x5516: 0x0080, 0x5517: 0x0080,
	0x5518: 0x0080, 0x5519: 0x0080, 0x551a: 0x0080, 0x551b: 0x0080, 0x551c: 0x0080, 0x551d: 0x0080,
	0x551e: 0x0080, 0x551f: 0x0080, 0x5520: 0x0080, 0x5521: 0x0080, 0x5522: 0x0080, 0x5523: 0x0080,
	0x5524: 0x0080, 0x5525: 0x0080, 0x5526: 0x0080, 0x5527: 0x0080, 0x5528: 0x0080, 0x5529: 0x0080,
	0x552a: 0x0080, 0x552b: 0x0080, 0x552c: 0x0080, 0x552d: 0x0080,
	0x5530: 0x0080, 0x5531: 0x0080,
	// Block 0x155, offset 0x5540
	0x5540: 0x0080, 0x5541: 0x0080, 0x5542: 0x0080, 0x5543: 0x0080, 0x5544: 0x0080, 0x5545: 0x0080,
	0x5546: 0x0080, 0x5547: 0x0080, 0x5548: 0x0080, 0x5549: 0x0080, 0x554a: 0x0080, 0x554b: 0x0080,
	0x554c: 0x0080, 0x554d: 0x0080, 0x554e: 0x0080, 0x554f: 0x0080, 0x5550: 0x0080, 0x5551: 0x0080,
	0x5552: 0x0080, 0x5553: 0x0080, 0x5554: 0x0080, 0x5555: 0x0080, 0x5556: 0x0080, 0x5557: 0x0080,
	0x5558: 0x0080, 0x5559: 0x0080, 0x555a: 0x0080, 0x555b: 0x0080, 0x555c: 0x0080, 0x555d: 0x0080,
	0x555e: 0x0080, 0x555f: 0x0080, 0x5560: 0x0080, 0x5561: 0x0080, 0x5562: 0x0080, 0x5563: 0x0080,
	0x5564: 0x0080, 0x5565: 0x0080, 0x5566: 0x0080, 0x5567: 0x0080, 0x5568: 0x0080, 0x5569: 0x0080,
	0x556a: 0x0080, 0x556b: 0x0080, 0x556c: 0x0080, 0x556d: 0x0080, 0x556e: 0x0080, 0x556f: 0x0080,
	0x5570: 0x0080, 0x5571: 0x0080, 0x5572: 0x0080, 0x5573: 0x0080, 0x5574: 0x0080, 0x5575: 0x0080,
	0x5576: 0x0080, 0x5577: 0x0080, 0x5578: 0x0080, 0x557a: 0x0080, 0x557b: 0x0080,
	0x557c: 0x0080, 0x557d: 0x0080, 0x557e: 0x0080, 0x557f: 0x0080,
	// Block 0x156, offset 0x5580
	0x5580: 0x0080, 0x5581: 0x0080, 0x5582: 0x0080, 0x5583: 0x0080, 0x5584: 0x0080, 0x5585: 0x0080,
	0x5586: 0x0080, 0x5587: 0x0080, 0x5588: 0x0080, 0x5589: 0x0080, 0x558a: 0x0080, 0x558b: 0x0080,
	0x558d: 0x0080, 0x558e: 0x0080, 0x558f: 0x0080, 0x5590: 0x0080, 0x5591: 0x0080,
	0x5592: 0x0080, 0x5593: 0x0080, 0x5594: 0x0080, 0x5595: 0x0080, 0x5596: 0x0080, 0x5597: 0x0080,
	0x5598: 0x0080, 0x5599: 0x0080, 0x559a: 0x0080, 0x559b: 0x0080, 0x559c: 0x0080, 0x559d: 0x0080,
	0x559e: 0x0080, 0x559f: 0x0080, 0x55a0: 0x0080, 0x55a1: 0x0080, 0x55a2: 0x0080, 0x55a3: 0x0080,
	0x55a4: 0x0080, 0x55a5: 0x0080, 0x55a6: 0x0080, 0x55a7: 0x0080, 0x55a8: 0x0080, 0x55a9: 0x0080,
	0x55aa: 0x0080, 0x55ab: 0x0080, 0x55ac: 0x0080, 0x55ad: 0x0080, 0x55ae: 0x0080, 0x55af: 0x0080,
	0x55b0: 0x0080, 0x55b1: 0x0080, 0x55b2: 0x0080, 0x55b3: 0x0080, 0x55b4: 0x0080, 0x55b5: 0x0080,
	0x55b6: 0x0080, 0x55b7: 0x0080, 0x55b8: 0x0080, 0x55b9: 0x0080, 0x55ba: 0x0080, 0x55bb: 0x0080,
	0x55bc: 0x0080, 0x55bd: 0x0080, 0x55be: 0x0080, 0x55bf: 0x0080,
	// Block 0x157, offset 0x55c0
	0x55c0: 0x0080, 0x55c1: 0x0080, 0x55c2: 0x0080, 0x55c3: 0x0080, 0x55c4: 0x0080, 0x55c5: 0x0080,
	0x55c6: 0x0080, 0x55c7: 0x0080, 0x55c8: 0x0080, 0x55c9: 0x0080, 0x55ca: 0x0080, 0x55cb: 0x0080,
	0x55cc: 0x0080, 0x55cd: 0x0080, 0x55ce: 0x0080, 0x55cf: 0x0080, 0x55d0: 0x0080, 0x55d1: 0x0080,
	0x55d2: 0x0080, 0x55d3: 0x0080,
	0x55e0: 0x0080, 0x55e1: 0x0080, 0x55e2: 0x0080, 0x55e3: 0x0080,
	0x55e4: 0x0080, 0x55e5: 0x0080, 0x55e6: 0x0080, 0x55e7: 0x0080, 0x55e8: 0x0080, 0x55e9: 0x0080,
	0x55ea: 0x0080, 0x55eb: 0x0080, 0x55ec: 0x0080, 0x55ed: 0x0080,
	0x55f0: 0x0080, 0x55f1: 0x0080, 0x55f2: 0x0080, 0x55f3: 0x0080, 0x55f4: 0x0080,
	0x55f8: 0x0080, 0x55f9: 0x0080, 0x55fa: 0x0080,
	// Block 0x158, offset 0x5600
	0x5600: 0x0080, 0x5601: 0x0080, 0x5602: 0x0080, 0x5603: 0x0080, 0x5604: 0x0080, 0x5605: 0x0080,
	0x5606: 0x0080,
	0x5610: 0x0080, 0x5611: 0x0080,
	0x5612: 0x0080, 0x5613: 0x0080, 0x5614: 0x0080, 0x5615: 0x0080, 0x5616: 0x0080, 0x5617: 0x0080,
	0x5618: 0x0080, 0x5619: 0x0080, 0x561a: 0x0080, 0x561b: 0x0080, 0x561c: 0x0080, 0x561d: 0x0080,
	0x561e: 0x0080, 0x561f: 0x0080, 0x5620: 0x0080, 0x5621: 0x0080, 0x5622: 0x0080, 0x5623: 0x0080,
	0x5624: 0x0080, 0x5625: 0x0080, 0x5626: 0x0080, 0x5627: 0x0080, 0x5628: 0x0080,
	0x5630: 0x0080, 0x5631: 0x0080, 0x5632: 0x0080, 0x5633: 0x0080, 0x5634: 0x0080, 0x5635: 0x0080,
	0x5636: 0x0080,
	// Block 0x159, offset 0x5640
	0x5640: 0x0080, 0x5641: 0x0080, 0x5642: 0x0080,
	0x5650: 0x0080, 0x5651: 0x0080,
	0x5652: 0x0080, 0x5653: 0x0080, 0x5654: 0x0080, 0x5655: 0x0080, 0x5656: 0x0080,
	// Block 0x15a, offset 0x5680
	0x5680: 0x0080, 0x5681: 0x0080, 0x5682: 0x0080, 0x5683: 0x0080, 0x5684: 0x0080, 0x5685: 0x0080,
	0x5686: 0x0080, 0x5687: 0x0080, 0x5688: 0x0080, 0x5689: 0x0080, 0x568a: 0x0080, 0x568b: 0x0080,
	0x568c: 0x0080, 0x568d: 0x0080, 0x568e: 0x0080, 0x568f: 0x0080, 0x5690: 0x0080, 0x5691: 0x0080,
	0x5692: 0x0080, 0x5694: 0x0080, 0x5695: 0x0080, 0x5696: 0x0080, 0x5697: 0x0080,
	0x5698: 0x0080, 0x5699: 0x0080, 0x569a: 0x0080, 0x569b: 0x0080, 0x569c: 0x0080, 0x569d: 0x0080,
	0x569e: 0x0080, 0x569f: 0x0080, 0x56a0: 0x0080, 0x56a1: 0x0080, 0x56a2: 0x0080, 0x56a3: 0x0080,
	0x56a4: 0x0080, 0x56a5: 0x0080, 0x56a6: 0x0080, 0x56a7: 0x0080, 0x56a8: 0x0080, 0x56a9: 0x0080,
	0x56aa: 0x0080, 0x56ab: 0x0080, 0x56ac: 0x0080, 0x56ad: 0x0080, 0x56ae: 0x0080, 0x56af: 0x0080,
	0x56b0: 0x0080, 0x56b1: 0x0080, 0x56b2: 0x0080, 0x56b3: 0x0080, 0x56b4: 0x0080, 0x56b5: 0x0080,
	0x56b6: 0x0080, 0x56b7: 0x0080, 0x56b8: 0x0080, 0x56b9: 0x0080, 0x56ba: 0x0080, 0x56bb: 0x0080,
	0x56bc: 0x0080, 0x56bd: 0x0080, 0x56be: 0x0080, 0x56bf: 0x0080,
	// Block 0x15b, offset 0x56c0
	0x56c0: 0x0080, 0x56c1: 0x0080, 0x56c2: 0x0080, 0x56c3: 0x0080, 0x56c4: 0x0080, 0x56c5: 0x0080,
	0x56c6: 0x0080, 0x56c7: 0x0080, 0x56c8: 0x0080, 0x56c9: 0x0080, 0x56ca: 0x0080,
	0x56f0: 0x0080, 0x56f1: 0x0080, 0x56f2: 0x0080, 0x56f3: 0x0080, 0x56f4: 0x0080, 0x56f5: 0x0080,
	0x56f6: 0x0080, 0x56f7: 0x0080, 0x56f8: 0x0080, 0x56f9: 0x0080,
	// Block 0x15c, offset 0x5700
	0x5700: 0x00cc, 0x5701: 0x00cc, 0x5702: 0x00cc, 0x5703: 0x00cc, 0x5704: 0x00cc, 0x5705: 0x00cc,
	0x5706: 0x00cc, 0x5707: 0x00cc, 0x5708: 0x00cc, 0x5709: 0x00cc, 0x570a: 0x00cc, 0x570b: 0x00cc,
	0x570c: 0x00cc, 0x570d: 0x00cc, 0x570e: 0x00cc, 0x570f: 0x00cc, 0x5710: 0x00cc, 0x5711: 0x00cc,
	0x5712: 0x00cc, 0x5713: 0x00cc, 0x5714: 0x00cc, 0x5715: 0x00cc, 0x5716: 0x00cc, 0x5717: 0x00cc,
	0x5718: 0x00cc, 0x5719: 0x00cc, 0x571a: 0x00cc, 0x571b: 0x00cc, 0x571c: 0x00cc, 0x571d: 0x00cc,
	// Block 0x15d, offset 0x5740
	0x5740: 0x00cc, 0x5741: 0x00cc, 0x5742: 0x00cc, 0x5743: 0x00cc, 0x5744: 0x00cc, 0x5745: 0x00cc,
	0x5746: 0x00cc, 0x5747: 0x00cc, 0x5748: 0x00cc, 0x5749: 0x00cc, 0x574a: 0x00cc, 0x574b: 0x00cc,
	0x574c: 0x00cc, 0x574d: 0x00cc, 0x574e: 0x00cc, 0x574f: 0x00cc, 0x5750: 0x00cc, 0x5751: 0x00cc,
	0x5752: 0x00cc, 0x5753: 0x00cc, 0x5754: 0x00cc, 0x5755: 0x00cc, 0x5756: 0x00cc, 0x5757: 0x00cc,
	0x5758: 0x00cc, 0x5759: 0x00cc, 0x575a: 0x00cc, 0x575b: 0x00cc, 0x575c: 0x00cc, 0x575d: 0x00cc,
	0x575e: 0x00cc, 0x575f: 0x00cc, 0x5760: 0x00cc, 0x5761: 0x00cc, 0x5762: 0x00cc, 0x5763: 0x00cc,
	0x5764: 0x00cc, 0x5765: 0x00cc, 0x5766: 0x00cc, 0x5767: 0x00cc, 0x5768: 0x00cc, 0x5769: 0x00cc,
	0x576a: 0x00cc, 0x576b: 0x00cc, 0x576c: 0x00cc, 0x576d: 0x00cc, 0x576e: 0x00cc, 0x576f: 0x00cc,
	0x5770: 0x00cc, 0x5771: 0x00cc, 0x5772: 0x00cc, 0x5773: 0x00cc, 0x5774: 0x00cc,
	// Block 0x15e, offset 0x5780
	0x5780: 0x00cc, 0x5781: 0x00cc, 0x5782: 0x00cc, 0x5783: 0x00cc, 0x5784: 0x00cc, 0x5785: 0x00cc,
	0x5786: 0x00cc, 0x5787: 0x00cc, 0x5788: 0x00cc, 0x5789: 0x00cc, 0x578a: 0x00cc, 0x578b: 0x00cc,
	0x578c: 0x00cc, 0x578d: 0x00cc, 0x578e: 0x00cc, 0x578f: 0x00cc, 0x5790: 0x00cc, 0x5791: 0x00cc,
	0x5792: 0x00cc, 0x5793: 0x00cc, 0x5794: 0x00cc, 0x5795: 0x00cc, 0x5796: 0x00cc, 0x5797: 0x00cc,
	0x5798: 0x00cc, 0x5799: 0x00cc, 0x579a: 0x00cc, 0x579b: 0x00cc, 0x579c: 0x00cc, 0x579d: 0x00cc,
	0x57a0: 0x00cc, 0x57a1: 0x00cc, 0x57a2: 0x00cc, 0x57a3: 0x00cc,
	0x57a4: 0x00cc, 0x57a5: 0x00cc, 0x57a6: 0x00cc, 0x57a7: 0x00cc, 0x57a8: 0x00cc, 0x57a9: 0x00cc,
	0x57aa: 0x00cc, 0x57ab: 0x00cc, 0x57ac: 0x00cc, 0x57ad: 0x00cc, 0x57ae: 0x00cc, 0x57af: 0x00cc,
	0x57b0: 0x00cc, 0x57b1: 0x00cc, 0x57b2: 0x00cc, 0x57b3: 0x00cc, 0x57b4: 0x00cc, 0x57b5: 0x00cc,
	0x57b6: 0x00cc, 0x57b7: 0x00cc, 0x57b8: 0x00cc, 0x57b9: 0x00cc, 0x57ba: 0x00cc, 0x57bb: 0x00cc,
	0x57bc: 0x00cc, 0x57bd: 0x00cc, 0x57be: 0x00cc, 0x57bf: 0x00cc,
	// Block 0x15f, offset 0x57c0
	0x57c0: 0x00cc, 0x57c1: 0x00cc, 0x57c2: 0x00cc, 0x57c3: 0x00cc, 0x57c4: 0x00cc, 0x57c5: 0x00cc,
	0x57c6: 0x00cc, 0x57c7: 0x00cc, 0x57c8: 0x00cc, 0x57c9: 0x00cc, 0x57ca: 0x00cc, 0x57cb: 0x00cc,
	0x57cc: 0x00cc, 0x57cd: 0x00cc, 0x57ce: 0x00cc, 0x57cf: 0x00cc, 0x57d0: 0x00cc, 0x57d1: 0x00cc,
	0x57d2: 0x00cc, 0x57d3: 0x00cc, 0x57d4: 0x00cc, 0x57d5: 0x00cc, 0x57d6: 0x00cc, 0x57d7: 0x00cc,
	0x57d8: 0x00cc, 0x57d9: 0x00cc, 0x57da: 0x00cc, 0x57db: 0x00cc, 0x57dc: 0x00cc, 0x57dd: 0x00cc,
	0x57de: 0x00cc, 0x57df: 0x00cc, 0x57e0: 0x00cc, 0x57e1: 0x00cc,
	0x57f0: 0x00cc, 0x57f1: 0x00cc, 0x57f2: 0x00cc, 0x57f3: 0x00cc, 0x57f4: 0x00cc, 0x57f5: 0x00cc,
	0x57f6: 0x00cc, 0x57f7: 0x00cc, 0x57f8: 0x00cc, 0x57f9: 0x00cc, 0x57fa: 0x00cc, 0x57fb: 0x00cc,
	0x57fc: 0x00cc, 0x57fd: 0x00cc, 0x57fe: 0x00cc, 0x57ff: 0x00cc,
	// Block 0x160, offset 0x5800
	0x5800: 0x00cc, 0x5801: 0x00cc, 0x5802: 0x00cc, 0x5803: 0x00cc, 0x5804: 0x00cc, 0x5805: 0x00cc,
	0x5806: 0x00cc, 0x5807: 0x00cc, 0x5808: 0x00cc, 0x5809: 0x00cc, 0x580a: 0x00cc, 0x580b: 0x00cc,
	0x580c: 0x00cc, 0x580d: 0x00cc, 0x580e: 0x00cc, 0x580f: 0x00cc, 0x5810: 0x00cc, 0x5811: 0x00cc,
	0x5812: 0x00cc, 0x5813: 0x00cc, 0x5814: 0x00cc, 0x5815: 0x00cc, 0x5816: 0x00cc, 0x5817: 0x00cc,
	0x5818: 0x00cc, 0x5819: 0x00cc, 0x581a: 0x00cc, 0x581b: 0x00cc, 0x581c: 0x00cc, 0x581d: 0x00cc,
	0x581e: 0x00cc, 0x581f: 0x00cc, 0x5820: 0x00cc,
	// Block 0x161, offset 0x5840
	0x5840: 0x008c, 0x5841: 0x008c, 0x5842: 0x008c, 0x5843: 0x008c, 0x5844: 0x008c, 0x5845: 0x008c,
	0x5846: 0x008c, 0x5847: 0x008c, 0x5848: 0x008c, 0x5849: 0x008c, 0x584a: 0x008c, 0x584b: 0x008c,
	0x584c: 0x008c, 0x584d: 0x008c, 0x584e: 0x008c, 0x584f: 0x008c, 0x5850: 0x008c, 0x5851: 0x008c,
	0x5852: 0x008c, 0x5853: 0x008c, 0x5854: 0x008c, 0x5855: 0x008c, 0x5856: 0x008c, 0x5857: 0x008c,
	0x5858: 0x008c, 0x5859: 0x008c, 0x585a: 0x008c, 0x585b: 0x008c, 0x585c: 0x008c, 0x585d: 0x008c,
	// Block 0x162, offset 0x5880
	0x5880: 0x00cc, 0x5881: 0x00cc, 0x5882: 0x00cc, 0x5883: 0x00cc, 0x5884: 0x00cc, 0x5885: 0x00cc,
	0x5886: 0x00cc, 0x5887: 0x00cc, 0x5888: 0x00cc, 0x5889: 0x00cc, 0x588a: 0x00cc,
	// Block 0x163, offset 0x58c0
	0x58c1: 0x0040,
	0x58e0: 0x0040, 0x58e1: 0x0040, 0x58e2: 0x0040, 0x58e3: 0x0040,
	0x58e4: 0x0040, 0x58e5: 0x0040, 0x58e6: 0x0040, 0x58e7: 0x0040, 0x58e8: 0x0040, 0x58e9: 0x0040,
	0x58ea: 0x0040, 0x58eb: 0x0040, 0x58ec: 0x0040, 0x58ed: 0x0040, 0x58ee: 0x0040, 0x58ef: 0x0040,
	0x58f0: 0x0040, 0x58f1: 0x0040, 0x58f2: 0x0040, 0x58f3: 0x0040, 0x58f4: 0x0040, 0x58f5: 0x0040,
	0x58f6: 0x0040, 0x58f7: 0x0040, 0x58f8: 0x0040, 0x58f9: 0x0040, 0x58fa: 0x0040, 0x58fb: 0x0040,
	0x58fc: 0x0040, 0x58fd: 0x0040, 0x58fe: 0x0040, 0x58ff: 0x0040,
	// Block 0x164, offset 0x5900
	0x5900: 0x0040, 0x5901: 0x0040, 0x5902: 0x0040, 0x5903: 0x0040, 0x5904: 0x0040, 0x5905: 0x0040,
	0x5906: 0x0040, 0x5907: 0x0040, 0x5908: 0x0040, 0x5909: 0x0040, 0x590a: 0x0040, 0x590b: 0x0040,
	0x590c: 0x0040, 0x590d: 0x0040, 0x590e: 0x0040, 0x590f: 0x0040, 0x5910: 0x0040, 0x5911: 0x0040,
	0x5912: 0x0040, 0x5913: 0x0040, 0x5914: 0x0040, 0x5915: 0x0040, 0x5916: 0x0040, 0x5917: 0x0040,
	0x5918: 0x0040, 0x5919: 0x0040, 0x591a: 0x0040, 0x591b: 0x0040, 0x591c: 0x0040, 0x591d: 0x0040,
	0x591e: 0x0040, 0x591f: 0x0040, 0x5920: 0x0040, 0x5921: 0x0040, 0x5922: 0x0040, 0x5923: 0x0040,
	0x5924: 0x0040, 0x5925: 0x0040, 0x5926: 0x0040, 0x5927: 0x0040, 0x5928: 0x0040, 0x5929: 0x0040,
	0x592a: 0x0040, 0x592b: 0x0040, 0x592c: 0x0040, 0x592d: 0x0040, 0x592e: 0x0040, 0x592f: 0x0040,
	// Block 0x165, offset 0x5940
	0x5940: 0x0040, 0x5941: 0x0040, 0x5942: 0x0040, 0x5943: 0x0040, 0x5944: 0x0040, 0x5945: 0x0040,
	0x5946: 0x0040, 0x5947: 0x0040, 0x5948: 0x0040, 0x5949: 0x0040, 0x594a: 0x0040, 0x594b: 0x0040,
	0x594c: 0x0040, 0x594d: 0x0040, 0x594e: 0x0040, 0x594f: 0x0040, 0x5950: 0x0040, 0x5951: 0x0040,
	0x5952: 0x0040, 0x5953: 0x0040, 0x5954: 0x0040, 0x5955: 0x0040, 0x5956: 0x0040, 0x5957: 0x0040,
	0x5958: 0x0040, 0x5959: 0x0040, 0x595a: 0x0040, 0x595b: 0x0040, 0x595c: 0x0040, 0x595d: 0x0040,
	0x595e: 0x0040, 0x595f: 0x0040, 0x5960: 0x0040, 0x5961: 0x0040, 0x5962: 0x0040, 0x5963: 0x0040,
	0x5964: 0x0040, 0x5965: 0x0040, 0x5966: 0x0040, 0x5967: 0x0040, 0x5968: 0x0040, 0x5969: 0x0040,
	0x596a: 0x0040, 0x596b: 0x0040, 0x596c: 0x0040, 0x596d: 0x0040, 0x596e: 0x0040, 0x596f: 0x0040,
	0x5970: 0x0040, 0x5971: 0x0040, 0x5972: 0x0040, 0x5973: 0x0040, 0x5974: 0x0040, 0x5975: 0x0040,
	0x5976: 0x0040, 0x5977: 0x0040, 0x5978: 0x0040, 0x5979: 0x0040, 0x597a: 0x0040, 0x597b: 0x0040,
	0x597c: 0x0040, 0x597d: 0x0040,
}

// derivedPropertiesIndex: 38 blocks, 2432 entries, 4864 bytes
// Block 0 is the zero block.
var derivedPropertiesIndex = [2432]uint16{
	// Block 0x0, offset 0x0
	// Block 0x1, offset 0x40
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xc2: 0x01, 0xc3: 0x02, 0xc4: 0x03, 0xc5: 0x04, 0xc6: 0x05, 0xc7: 0x06,
	0xc8: 0x05, 0xc9: 0x05, 0xca: 0x07, 0xcb: 0x08, 0xcc: 0x09, 0xcd: 0x0a, 0xce: 0x0b, 0xcf: 0x0c,
	0xd0: 0x05, 0xd1: 0x05, 0xd2: 0x0d, 0xd3: 0x05, 0xd4: 0x0e, 0xd5: 0x0f, 0xd6: 0x10, 0xd7: 0x11,
	0xd8: 0x12, 0xd9: 0x13, 0xda: 0x14, 0xdb: 0x15, 0xdc: 0x16, 0xdd: 0x17, 0xde: 0x18, 0xdf: 0x19,
	0xe0: 0x02, 0xe1: 0x03, 0xe2: 0x04, 0xe3: 0x05, 0xe4: 0x06, 0xe5: 0x07, 0xe6: 0x07, 0xe7: 0x07,
	0xe8: 0x07, 0xe9: 0x08, 0xea: 0x09, 0xeb: 0x0a, 0xec: 0x0a, 0xed: 0x0b, 0xee: 0x0c, 0xef: 0x0d,
	0xf0: 0x1f, 0xf3: 0x22, 0xf4: 0x23,
	// Block 0x4, offset 0x100
	0x120: 0x1a, 0x121: 0x1b, 0x122: 0x1c, 0x123: 0x1d, 0x124: 0x1e, 0x125: 0x1f, 0x126: 0x20, 0x127: 0x21,
	0x128: 0x22, 0x129: 0x23, 0x12a: 0x24, 0x12b: 0x25, 0x12c: 0x26, 0x12d: 0x27, 0x12e: 0x28, 0x12f: 0x29,
	0x130: 0x2a, 0x131: 0x2b, 0x132: 0x2c, 0x133: 0x2d, 0x134: 0x2e, 0x135: 0x2f, 0x136: 0x30, 0x137: 0x31,
	0x138: 0x32, 0x139: 0x33, 0x13a: 0x34, 0x13b: 0x35, 0x13c: 0x36, 0x13d: 0x37, 0x13e: 0x38, 0x13f: 0x39,
	// Block 0x5, offset 0x140
	0x140: 0x3a, 0x141: 0x3b, 0x142: 0x3c, 0x143: 0x3d, 0x144: 0x3e, 0x145: 0x3e, 0x146: 0x3e, 0x147: 0x3e,
	0x148: 0x05, 0x149: 0x3f, 0x14a: 0x40, 0x14b: 0x41, 0x14c: 0x42, 0x14d: 0x43, 0x14e: 0x44, 0x14f: 0x45,
	0x150: 0x46, 0x151: 0x05, 0x152: 0x05, 0x153: 0x05, 0x154: 0x05, 0x155: 0x05, 0x156: 0x05, 0x157: 0x05,
	0x158: 0x05, 0x159: 0x47, 0x15a: 0x48, 0x15b: 0x49, 0x15c: 0x4a, 0x15d: 0x4b, 0x15e: 0x4c, 0x15f: 0x4d,
	0x160: 0x4e, 0x161: 0x4f, 0x162: 0x50, 0x163: 0x51, 0x164: 0x52, 0x165: 0x53, 0x166: 0x54, 0x167: 0x55,
	0x168: 0x56, 0x169: 0x57, 0x16a: 0x58, 0x16b: 0x59, 0x16c: 0x5a, 0x16d: 0x5b, 0x16e: 0x5c, 0x16f: 0x5d,
	0x170: 0x5e, 0x171: 0x5f, 0x172: 0x60, 0x173: 0x61, 0x174: 0x62, 0x175: 0x63, 0x176: 0x64, 0x177: 0x65,
	0x178: 0x05, 0x179: 0x05, 0x17a: 0x66, 0x17b: 0x05, 0x17c: 0x67, 0x17d: 0x68, 0x17e: 0x69, 0x17f: 0x6a,
	// Block 0x6, offset 0x180
	0x180: 0x6b, 0x181: 0x6c, 0x182: 0x6d, 0x183: 0x6e, 0x184: 0x6f, 0x185: 0x70, 0x186: 0x71, 0x187: 0x72,
	0x188: 0x72, 0x189: 0x72, 0x18a: 0x72, 0x18b: 0x72, 0x18c: 0x72, 0x18d: 0x72, 0x18e: 0x72, 0x18f: 0x72,
	0x190: 0x73, 0x191: 0x74, 0x192: 0x72, 0x193: 0x72, 0x194: 0x72, 0x195: 0x72, 0x196: 0x72, 0x197: 0x72,
	0x198: 0x72, 0x199: 0x72, 0x19a: 0x72, 0x19b: 0x72, 0x19c: 0x72, 0x19d: 0x72, 0x19e: 0x72, 0x19f: 0x72,
	0x1a0: 0x72, 0x1a1: 0x72, 0x1a2: 0x72, 0x1a3: 0x72, 0x1a4: 0x72, 0x1a5: 0x72, 0x1a6: 0x72, 0x1a7: 0x72,
	0x1a8: 0x72, 0x1a9: 0x72, 0x1aa: 0x72, 0x1ab: 0x72, 0x1ac: 0x72, 0x1ad: 0x75, 0x1ae: 0x76, 0x1af: 0x72,
	0x1b0: 0x77, 0x1b1: 0x78, 0x1b2: 0x05, 0x1b3: 0x79, 0x1b4: 0x7a, 0x1b5: 0x7b, 0x1b6: 0x7c, 0x1b7: 0x7d,
	0x1b8: 0x7e, 0x1b9: 0x7f, 0x1ba: 0x80, 0x1bb: 0x81, 0x1bc: 0x82, 0x1bd: 0x82, 0x1be: 0x82, 0x1bf: 0x83,
	// Block 0x7, offset 0x1c0
	0x1c0: 0x84, 0x1c1: 0x85, 0x1c2: 0x86, 0x1c3: 0x87, 0x1c4: 0x88, 0x1c5: 0x89, 0x1c6: 0x8a, 0x1c7: 0x8b,
	0x1c8: 0x8c, 0x1c9: 0x72, 0x1ca: 0x72, 0x1cb: 0x8d, 0x1cc: 0x82, 0x1cd: 0x8e, 0x1ce: 0x72, 0x1cf: 0x72,
	0x1d0: 0x8f, 0x1d1: 0x8f, 0x1d2: 0x8f, 0x1d3: 0x8f, 0x1d4: 0x8f, 0x1d5: 0x8f, 0x1d6: 0x8f, 0x1d7: 0x8f,
	0x1d8: 0x8f, 0x1d9: 0x8f, 0x1da: 0x8f, 0x1db: 0x8f, 0x1dc: 0x8f, 0x1dd: 0x8f, 0x1de: 0x8f, 0x1df: 0x8f,
	0x1e0: 0x8f, 0x1e1: 0x8f, 0x1e2: 0x8f, 0x1e3: 0x8f, 0x1e4: 0x8f, 0x1e5: 0x8f, 0x1e6: 0x8f, 0x1e7: 0x8f,
	0x1e8: 0x8f, 0x1e9: 0x8f, 0x1ea: 0x8f, 0x1eb: 0x8f, 0x1ec: 0x8f, 0x1ed: 0x8f, 0x1ee: 0x8f, 0x1ef: 0x8f,
	0x1f0: 0x8f, 0x1f1: 0x8f, 0x1f2: 0x8f, 0x1f3: 0x8f, 0x1f4: 0x8f, 0x1f5: 0x8f, 0x1f6: 0x8f, 0x1f7: 0x8f,
	0x1f8: 0x8f, 0x1f9: 0x8f, 0x1fa: 0x8f, 0x1fb: 0x8f, 0x1fc: 0x8f, 0x1fd: 0x8f, 0x1fe: 0x8f, 0x1ff: 0x8f,
	// Block 0x8, offset 0x200
	0x200: 0x8f, 0x201: 0x8f, 0x202: 0x8f, 0x203: 0x8f, 0x204: 0x8f, 0x205: 0x8f, 0x206: 0x8f, 0x207: 0x8f,
	0x208: 0x8f, 0x209: 0x8f, 0x20a: 0x8f, 0x20b: 0x8f, 0x20c: 0x8f, 0x20d: 0x8f, 0x20e: 0x8f, 0x20f: 0x8f,
	0x210: 0x8f, 0x211: 0x8f, 0x212: 0x8f, 0x213: 0x8f, 0x214: 0x8f, 0x215: 0x8f, 0x216: 0x8f, 0x217: 0x8f,
	0x218: 0x8f, 0x219: 0x8f, 0x21a: 0x8f, 0x21b: 0x8f, 0x21c: 0x8f, 0x21d: 0x8f, 0x21e: 0x8f, 0x21f: 0x8f,
	0x220: 0x8f, 0x221: 0x8f, 0x222: 0x8f, 0x223: 0x8f, 0x224: 0x8f, 0x225: 0x8f, 0x226: 0x8f, 0x227: 0x8f,
	0x228: 0x8f, 0x229: 0x8f, 0x22a: 0x8f, 0x22b: 0x8f, 0x22c: 0x8f, 0x22d: 0x8f, 0x22e: 0x8f, 0x22f: 0x8f,
	0x230: 0x8f, 0x231: 0x8f, 0x232: 0x8f, 0x233: 0x8f, 0x234: 0x8f, 0x235: 0x8f, 0x236: 0x8f, 0x237: 0x72,
	0x238: 0x8f, 0x239: 0x8f, 0x23a: 0x8f, 0x23b: 0x8f, 0x23c: 0x8f, 0x23d: 0x8f, 0x23e: 0x8f, 0x23f: 0x8f,
	// Block 0x9, offset 0x240
	0x240: 0x8f, 0x241: 0x8f, 0x242: 0x8f, 0x243: 0x8f, 0x244: 0x8f, 0x245: 0x8f, 0x246: 0x8f, 0x247: 0x8f,
	0x248: 0x8f, 0x249: 0x8f, 0x24a: 0x8f, 0x24b: 0x8f, 0x24c: 0x8f, 0x24d: 0x8f, 0x24e: 0x8f, 0x24f: 0x8f,
	0x250: 0x8f, 0x251: 0x8f, 0x252: 0x8f, 0x253: 0x8f, 0x254: 0x8f, 0x255: 0x8f, 0x256: 0x8f, 0x257: 0x8f,
	0x258: 0x8f, 0x259: 0x8f, 0x25a: 0x8f, 0x25b: 0x8f, 0x25c: 0x8f, 0x25d: 0x8f, 0x25e: 0x8f, 0x25f: 0x8f,
	0x260: 0x8f, 0x261: 0x8f, 0x262: 0x8f, 0x263: 0x8f, 0x264: 0x8f, 0x265: 0x8f, 0x266: 0x8f, 0x267: 0x8f,
	0x268: 0x8f, 0x269: 0x8f, 0x26a: 0x8f, 0x26b: 0x8f, 0x26c: 0x8f, 0x26d: 0x8f, 0x26e: 0x8f, 0x26f: 0x8f,
	0x270: 0x8f, 0x271: 0x8f, 0x272: 0x8f, 0x273: 0x8f, 0x274: 0x8f, 0x275: 0x8f, 0x276: 0x8f, 0x277: 0x8f,
	0x278: 0x8f, 0x279: 0x8f, 0x27a: 0x8f, 0x27b: 0x8f, 0x27c: 0x8f, 0x27d: 0x8f, 0x27e: 0x8f, 0x27f: 0x8f,
	// Block 0xa, offset 0x280
	0x280: 0x8f, 0x281: 0x8f, 0x282: 0x8f, 0x283: 0x8f, 0x284: 0x8f, 0x285: 0x8f, 0x286: 0x8f, 0x287: 0x8f,
	0x288: 0x8f, 0x289: 0x8f, 0x28a: 0x8f, 0x28b: 0x8f, 0x28c: 0x8f, 0x28d: 0x8f, 0x28e: 0x8f, 0x28f: 0x8f,
	0x290: 0x8f, 0x291: 0x8f, 0x292: 0x8f, 0x293: 0x8f, 0x294: 0x8f, 0x295: 0x8f, 0x296: 0x8f, 0x297: 0x8f,
	0x298: 0x8f, 0x299: 0x8f, 0x29a: 0x8f, 0x29b: 0x8f, 0x29c: 0x8f, 0x29d: 0x8f, 0x29e: 0x8f, 0x29f: 0x8f,
	0x2a0: 0x8f, 0x2a1: 0x8f, 0x2a2: 0x8f, 0x2a3: 0x8f, 0x2a4: 0x8f, 0x2a5: 0x8f, 0x2a6: 0x8f, 0x2a7: 0x8f,
	0x2a8: 0x8f, 0x2a9: 0x8f, 0x2aa: 0x8f, 0x2ab: 0x8f, 0x2ac: 0x8f, 0x2ad: 0x8f, 0x2ae: 0x8f, 0x2af: 0x8f,
	0x2b0: 0x8f, 0x2b1: 0x8f, 0x2b2: 0x8f, 0x2b3: 0x8f, 0x2b4: 0x8f, 0x2b5: 0x8f, 0x2b6: 0x8f, 0x2b7: 0x8f,
	0x2b8: 0x8f, 0x2b9: 0x8f, 0x2ba: 0x8f, 0x2bb: 0x8f, 0x2bc: 0x8f, 0x2bd: 0x8f, 0x2be: 0x8f, 0x2bf: 0x90,
	// Block 0xb, offset 0x2c0
	0x2c0: 0x05, 0x2c1: 0x05, 0x2c2: 0x05, 0x2c3: 0x05, 0x2c4: 0x05, 0x2c5: 0x05, 0x2c6: 0x05, 0x2c7: 0x05,
	0x2c8: 0x05, 0x2c9: 0x05, 0x2ca: 0x05, 0x2cb: 0x05, 0x2cc: 0x05, 0x2cd: 0x05, 0x2ce: 0x05, 0x2cf: 0x05,
	0x2d0: 0x05, 0x2d1: 0x05, 0x2d2: 0x91, 0x2d3: 0x92, 0x2d4: 0x05, 0x2d5: 0x05, 0x2d6: 0x05, 0x2d7: 0x05,
	0x2d8: 0x93, 0x2d9: 0x94, 0x2da: 0x95, 0x2db: 0x96, 0x2dc: 0x97, 0x2dd: 0x98, 0x2de: 0x99, 0x2df: 0x9a,
	0x2e0: 0x9b, 0x2e1: 0x9c, 0x2e2: 0x05, 0x2e3: 0x9d, 0x2e4: 0x9e, 0x2e5: 0x9f, 0x2e6: 0xa0, 0x2e7: 0xa1,
	0x2e8: 0xa2, 0x2e9: 0xa3, 0x2ea: 0xa4, 0x2eb: 0xa5, 0x2ec: 0xa6, 0x2ed: 0xa7, 0x2ee: 0x05, 0x2ef: 0xa8,
	0x2f0: 0x05, 0x2f1: 0x05, 0x2f2: 0x05, 0x2f3: 0x05, 0x2f4: 0x05, 0x2f5: 0x05, 0x2f6: 0x05, 0x2f7: 0x05,
	0x2f8: 0x05, 0x2f9: 0x05, 0x2fa: 0x05, 0x2fb: 0x05, 0x2fc: 0x05, 0x2fd: 0x05, 0x2fe: 0x05, 0x2ff: 0x05,
	// Block 0xc, offset 0x300
	0x300: 0x05, 0x301: 0x05, 0x302: 0x05, 0x303: 0x05, 0x304: 0x05, 0x305: 0x05, 0x306: 0x05, 0x307: 0x05,
	0x308: 0x05, 0x309: 0x05, 0x30a: 0x05, 0x30b: 0x05, 0x30c: 0x05, 0x30d: 0x05, 0x30e: 0x05, 0x30f: 0x05,
	0x310: 0x05, 0x311: 0x05, 0x312: 0x05, 0x313: 0x05, 0x314: 0x05, 0x315: 0x05, 0x316: 0x05, 0x317: 0x05,
	0x318: 0x05, 0x319: 0x05, 0x31a: 0x05, 0x31b: 0x05, 0x31c: 0x05, 0x31d: 0x05, 0x31e: 0x05, 0x31f: 0x05,
	0x320: 0x05, 0x321: 0x05, 0x322: 0x05, 0x323: 0x05, 0x324: 0x05, 0x325: 0x05, 0x326: 0x05, 0x327: 0x05,
	0x328: 0x05, 0x329: 0x05, 0x32a: 0x05, 0x32b: 0x05, 0x32c: 0x05, 0x32d: 0x05, 0x32e: 0x05, 0x32f: 0x05,
	0x330: 0x05, 0x331: 0x05, 0x332: 0x05, 0x333: 0x05, 0x334: 0x05, 0x335: 0x05, 0x336: 0x05, 0x337: 0x05,
	0x338: 0x05, 0x339: 0x05, 0x33a: 0x05, 0x33b: 0x05, 0x33c: 0x05, 0x33d: 0x05, 0x33e: 0x05, 0x33f: 0x05,
	// Block 0xd, offset 0x340
	0x340: 0x05, 0x341: 0x05, 0x342: 0x05, 0x343: 0x05, 0x344: 0x05, 0x345: 0x05, 0x346: 0x05, 0x347: 0x05,
	0x348: 0x05, 0x349: 0x05, 0x34a: 0x05, 0x34b: 0x05, 0x34c: 0x05, 0x34d: 0x05, 0x34e: 0x05, 0x34f: 0x05,
	0x350: 0x05, 0x351: 0x05, 0x352: 0x05, 0x353: 0x05, 0x354: 0x05, 0x355: 0x05, 0x356: 0x05, 0x357: 0x05,
	0x358: 0x05, 0x359: 0x05, 0x35a: 0x05, 0x35b: 0x05, 0x35c: 0x05, 0x35d: 0x05, 0x35e: 0xa9, 0x35f: 0xaa,
	// Block 0xe, offset 0x380
	0x380: 0x3e, 0x381: 0x3e, 0x382: 0x3e, 0x383: 0x3e, 0x384: 0x3e, 0x385: 0x3e, 0x386: 0x3e, 0x387: 0x3e,
	0x388: 0x3e, 0x389: 0x3e, 0x38a: 0x3e, 0x38b: 0x3e, 0x38c: 0x3e, 0x38d: 0x3e, 0x38e: 0x3e, 0x38f: 0x3e,
	0x390: 0x3e, 0x391: 0x3e, 0x392: 0x3e, 0x393: 0x3e, 0x394: 0x3e, 0x395: 0x3e, 0x396: 0x3e, 0x397: 0x3e,
	0x398: 0x3e, 0x399: 0x3e, 0x39a: 0x3e, 0x39b: 0x3e, 0x39c: 0x3e, 0x39d: 0x3e, 0x39e: 0x3e, 0x39f: 0x3e,
	0x3a0: 0x3e, 0x3a1: 0x3e, 0x3a2: 0x3e, 0x3a3: 0x3e, 0x3a4: 0x3e, 0x3a5: 0x3e, 0x3a6: 0x3e, 0x3a7: 0x3e,
	0x3a8: 0x3e, 0x3a9: 0x3e, 0x3aa: 0x3e, 0x3ab: 0x3e, 0x3ac: 0x3e, 0x3ad: 0x3e, 0x3ae: 0x3e, 0x3af: 0x3e,
	0x3b0: 0x3e, 0x3b1: 0x3e, 0x3b2: 0x3e, 0x3b3: 0x3e, 0x3b4: 0x3e, 0x3b5: 0x3e, 0x3b6: 0x3e, 0x3b7: 0x3e,
	0x3b8: 0x3e, 0x3b9: 0x3e, 0x3ba: 0x3e, 0x3bb: 0x3e, 0x3bc: 0x3e, 0x3bd: 0x3e, 0x3be: 0x3e, 0x3bf: 0x3e,
	// Block 0xf, offset 0x3c0
	0x3c0: 0x3e, 0x3c1: 0x3e, 0x3c2: 0x3e, 0x3c3: 0x3e, 0x3c4: 0x3e, 0x3c5: 0x3e, 0x3c6: 0x3e, 0x3c7: 0x3e,
	0x3c8: 0x3e, 0x3c9: 0x3e, 0x3ca: 0x3e, 0x3cb: 0x3e, 0x3cc: 0x3e, 0x3cd: 0x3e, 0x3ce: 0x3e, 0x3cf: 0x3e,
	0x3d0: 0x3e, 0x3d1: 0x3e, 0x3d2: 0x3e, 0x3d3: 0x3e, 0x3d4: 0x3e, 0x3d5: 0x3e, 0x3d6: 0x3e, 0x3d7: 0x3e,
	0x3d8: 0x3e, 0x3d9: 0x3e, 0x3da: 0x3e, 0x3db: 0x3e, 0x3dc: 0x3e, 0x3dd: 0x3e, 0x3de: 0x3e, 0x3df: 0x3e,
	0x3e0: 0x3e, 0x3e1: 0x3e, 0x3e2: 0x3e, 0x3e3: 0x3e, 0x3e4: 0x82, 0x3e5: 0x82, 0x3e6: 0x82, 0x3e7: 0x82,
	0x3e8: 0xab, 0x3e9: 0xac, 0x3ea: 0x82, 0x3eb: 0xad, 0x3ec: 0xae, 0x3ed: 0xaf, 0x3ee: 0x72, 0x3ef: 0xb0,
	0x3f0: 0x72, 0x3f1: 0x72, 0x3f2: 0x72, 0x3f3: 0x72, 0x3f4: 0x72, 0x3f5: 0xb1, 0x3f6: 0xb2, 0x3f7: 0xb3,
	0x3f8: 0xb4, 0x3f9: 0xb5, 0x3fa: 0x72, 0x3fb: 0xb6, 0x3fc: 0xb7, 0x3fd: 0xb8, 0x3fe: 0xb9, 0x3ff: 0xba,
	// Block 0x10, offset 0x400
	0x400: 0xbb, 0x401: 0xbc, 0x402: 0x05, 0x403: 0xbd, 0x404: 0xbe, 0x405: 0xbf, 0x406: 0xc0, 0x407: 0xc1,
	0x40a: 0xc2, 0x40b: 0xc3, 0x40c: 0xc4, 0x40d: 0xc5, 0x40e: 0xc6, 0x40f: 0xc7,
	0x410: 0x05, 0x411: 0x05, 0x412: 0xc8, 0x413: 0xc9, 0x414: 0xca, 0x415: 0xcb,
	0x418: 0x05, 0x419: 0x05, 0x41a: 0x05, 0x41b: 0x05, 0x41c: 0xcc, 0x41d: 0xcd,
	0x420: 0xce, 0x421: 0xcf, 0x422: 0xd0, 0x423: 0xd1, 0x424: 0xd2, 0x426: 0xd3, 0x427: 0xb2,
	0x428: 0xd4, 0x429: 0xd5, 0x42a: 0xd6, 0x42b: 0xd7, 0x42c: 0xd8, 0x42d: 0xd9, 0x42e: 0xda,
	0x430: 0x05, 0x431: 0xdb, 0x432: 0xdc, 0x433: 0xdd, 0x434: 0xde,
	0x439: 0xdf, 0x43a: 0xe0, 0x43c: 0xe1, 0x43d: 0xe2, 0x43e: 0xe3, 0x43f: 0xe4,
	// Block 0x11, offset 0x440
	0x440: 0xe5, 0x441: 0xe6, 0x442: 0xe7, 0x443: 0xe8, 0x444: 0xe9, 0x445: 0xea, 0x446: 0xeb, 0x447: 0xec,
	0x448: 0xed, 0x44a: 0xee, 0x44b: 0xef, 0x44c: 0xf0, 0x44d: 0xf1,
	0x450: 0xf2, 0x451: 0xf3, 0x452: 0xf4, 0x453: 0xf5, 0x456: 0xf6, 0x457: 0xf7,
	0x458: 0xf8, 0x459: 0xf9, 0x45a: 0xfa, 0x45b: 0xfb, 0x45c: 0xfc,
	0x460: 0xfd, 0x462: 0xfe, 0x463: 0xff, 0x464: 0x100, 0x465: 0x101, 0x466: 0x102, 0x467: 0x103,
	0x468: 0x104, 0x469: 0x105, 0x46a: 0x106, 0x46b: 0x107,
	0x470: 0x108, 0x471: 0x109, 0x472: 0x10a, 0x474: 0x10b, 0x475: 0x10c, 0x476: 0x10d,
	0x47b: 0x10e, 0x47e: 0x10f, 0x47f: 0x110,
	// Block 0x12, offset 0x480
	0x480: 0x05, 0x481: 0x05, 0x482: 0x05, 0x483: 0x05, 0x484: 0x05, 0x485: 0x05, 0x486: 0x05, 0x487: 0x05,
	0x488: 0x05, 0x489: 0x05, 0x48a: 0x05, 0x48b: 0x05, 0x48c: 0x05, 0x48d: 0x05, 0x48e: 0x111,
	0x490: 0x72, 0x491: 0x112, 0x492: 0x05, 0x493: 0x05, 0x494: 0x05, 0x495: 0x113,
	// Block 0x13, offset 0x4c0
	0x4c0: 0x05, 0x4c1: 0x05, 0x4c2: 0x05, 0x4c3: 0x05, 0x4c4: 0x05, 0x4c5: 0x05, 0x4c6: 0x05, 0x4c7: 0x05,
	0x4c8: 0x05, 0x4c9: 0x05, 0x4ca: 0x05, 0x4cb: 0x05, 0x4cc: 0x05, 0x4cd: 0x05, 0x4ce: 0x05, 0x4cf: 0x05,
	0x4d0: 0x114,
	// Block 0x14, offset 0x500
	0x510: 0x05, 0x511: 0x05, 0x512: 0x05, 0x513: 0x05, 0x514: 0x05, 0x515: 0x05, 0x516: 0x05, 0x517: 0x05,
	0x518: 0x05, 0x519: 0x115,
	// Block 0x15, offset 0x540
	0x560: 0x05, 0x561: 0x05, 0x562: 0x05, 0x563: 0x05, 0x564: 0x05, 0x565: 0x05, 0x566: 0x05, 0x567: 0x05,
	0x568: 0x107, 0x569: 0x116, 0x56b: 0x117, 0x56c: 0x118, 0x56d: 0x119, 0x56e: 0x11a,
	0x579: 0x05, 0x57a: 0x11b, 0x57c: 0x05, 0x57d: 0x11c, 0x57e: 0x11d, 0x57f: 0x11e,
	// Block 0x16, offset 0x580
	0x580: 0x05, 0x581: 0x05, 0x582: 0x05, 0x583: 0x05, 0x584: 0x05, 0x585: 0x05, 0x586: 0x05, 0x587: 0x05,
	0x588: 0x05, 0x589: 0x05, 0x58a: 0x05, 0x58b: 0x05, 0x58c: 0x05, 0x58d: 0x05, 0x58e: 0x05, 0x58f: 0x05,
	0x590: 0x05, 0x591: 0x05, 0x592: 0x05, 0x593: 0x05, 0x594: 0x05, 0x595: 0x05, 0x596: 0x05, 0x597: 0x05,
	0x598: 0x05, 0x599: 0x05, 0x59a: 0x05, 0x59b: 0x05, 0x59c: 0x05, 0x59d: 0x05, 0x59e: 0x05, 0x59f: 0x11f,
	0x5a0: 0x05, 0x5a1: 0x05, 0x5a2: 0x05, 0x5a3: 0x05, 0x5a4: 0x05, 0x5a5: 0x05, 0x5a6: 0x05, 0x5a7: 0x05,
	0x5a8: 0x05, 0x5a9: 0x05, 0x5aa: 0x05, 0x5ab: 0x05, 0x5ac: 0x05, 0x5ad: 0x05, 0x5ae: 0x05, 0x5af: 0x05,
	0x5b0: 0x05, 0x5b1: 0x05, 0x5b2: 0x05, 0x5b3: 0x120, 0x5b4: 0xdb,
	// Block 0x17, offset 0x5c0
	0x5c0: 0x8f, 0x5c1: 0x8f, 0x5c2: 0x8f, 0x5c3: 0x8f, 0x5c4: 0x121, 0x5c5: 0x122, 0x5c6: 0x05, 0x5c7: 0x05,
	0x5c8: 0x05, 0x5c9: 0x05, 0x5ca: 0x05, 0x5cb: 0x123,
	0x5f0: 0x05, 0x5f1: 0x124, 0x5f2: 0x125,
	// Block 0x18, offset 0x600
	0x600: 0x72, 0x601: 0x72, 0x602: 0x72, 0x603: 0x126, 0x604: 0x127, 0x605: 0x128, 0x606: 0x129, 0x607: 0x12a,
	0x608: 0xbf, 0x609: 0x12b, 0x60b: 0x12c, 0x60c: 0x72, 0x60d: 0x12d,
	0x610: 0x72, 0x611: 0x12e, 0x612: 0x12f, 0x613: 0x130, 0x614: 0x131, 0x615: 0x132, 0x616: 0x72, 0x617: 0x72,
	0x618: 0x72, 0x619: 0x72, 0x61a: 0x133, 0x61b: 0x72, 0x61c: 0x72, 0x61d: 0x72, 0x61e: 0x72, 0x61f: 0x134,
	0x620: 0x72, 0x621: 0x72, 0x622: 0x72, 0x623: 0x72, 0x624: 0x72, 0x625: 0x72, 0x626: 0x72, 0x627: 0x72,
	0x628: 0x135, 0x629: 0x136, 0x62a: 0x137,
	// Block 0x19, offset 0x640
	0x640: 0x138, 0x644: 0x139, 0x645: 0x13a,
	0x64b: 0x13b,
	0x660: 0x05, 0x661: 0x05, 0x662: 0x05, 0x663: 0x13c, 0x664: 0x13d, 0x665: 0x13e,
	0x671: 0x13f, 0x672: 0x140, 0x674: 0x141,
	0x678: 0x142, 0x679: 0x143, 0x67a: 0x144, 0x67b: 0x145,
	// Block 0x1a, offset 0x680
	0x680: 0x146, 0x681: 0x72, 0x682: 0x147, 0x683: 0x148, 0x684: 0x72, 0x685: 0x72, 0x686: 0x149, 0x687: 0x14a,
	0x688: 0x14b, 0x689: 0x14c, 0x68c: 0x72, 0x68d: 0x72, 0x68e: 0x72, 0x68f: 0x72,
	0x690: 0x72, 0x691: 0x72, 0x692: 0x72, 0x693: 0x72, 0x694: 0x72, 0x695: 0x72, 0x696: 0x72, 0x697: 0x72,
	0x698: 0x72, 0x699: 0x72, 0x69a: 0x72, 0x69b: 0x14d, 0x69c: 0x72, 0x69d: 0x14e, 0x69e: 0x72, 0x69f: 0x14f,
	0x6a0: 0x150, 0x6a1: 0x151, 0x6a2: 0x152, 0x6a4: 0x72, 0x6a5: 0x153, 0x6a6: 0x72, 0x6a7: 0x154,
	0x6a8: 0x72, 0x6a9: 0x155, 0x6aa: 0x156, 0x6ab: 0x157, 0x6ac: 0x72, 0x6ad: 0x72, 0x6ae: 0x158, 0x6af: 0x159,
	// Block 0x1b, offset 0x6c0
	0x6c0: 0x8f, 0x6c1: 0x8f, 0x6c2: 0x8f, 0x6c3: 0x8f, 0x6c4: 0x8f, 0x6c5: 0x8f, 0x6c6: 0x8f, 0x6c7: 0x8f,
	0x6c8: 0x8f, 0x6c9: 0x8f, 0x6ca: 0x8f, 0x6cb: 0x8f, 0x6cc: 0x8f, 0x6cd: 0x8f, 0x6ce: 0x8f, 0x6cf: 0x8f,
	0x6d0: 0x8f, 0x6d1: 0x8f, 0x6d2: 0x8f, 0x6d3: 0x8f, 0x6d4: 0x8f, 0x6d5: 0x8f, 0x6d6: 0x8f, 0x6d7: 0x8f,
	0x6d8: 0x8f, 0x6d9: 0x8f, 0x6da: 0x8f, 0x6db: 0x15a, 0x6dc: 0x8f, 0x6dd: 0x8f, 0x6de: 0x8f, 0x6df: 0x8f,
	0x6e0: 0x8f, 0x6e1: 0x8f, 0x6e2: 0x8f, 0x6e3: 0x8f, 0x6e4: 0x8f, 0x6e5: 0x8f, 0x6e6: 0x8f, 0x6e7: 0x8f,
	0x6e8: 0x8f, 0x6e9: 0x8f, 0x6ea: 0x8f, 0x6eb: 0x8f, 0x6ec: 0x8f, 0x6ed: 0x8f, 0x6ee: 0x8f, 0x6ef: 0x8f,
	0x6f0: 0x8f, 0x6f1: 0x8f, 0x6f2: 0x8f, 0x6f3: 0x8f, 0x6f4: 0x8f, 0x6f5: 0x8f, 0x6f6: 0x8f, 0x6f7: 0x8f,
	0x6f8: 0x8f, 0x6f9: 0x8f, 0x6fa: 0x8f, 0x6fb: 0x8f, 0x6fc: 0x8f, 0x6fd: 0x8f, 0x6fe: 0x8f, 0x6ff: 0x8f,
	// Block 0x1c, offset 0x700
	0x700: 0x8f, 0x701: 0x8f, 0x702: 0x8f, 0x703: 0x8f, 0x704: 0x8f, 0x705: 0x8f, 0x706: 0x8f, 0x707: 0x8f,
	0x708: 0x8f, 0x709: 0x8f, 0x70a: 0x8f, 0x70b: 0x8f, 0x70c: 0x8f, 0x70d: 0x8f, 0x70e: 0x8f, 0x70f: 0x8f,
	0x710: 0x8f, 0x711: 0x8f, 0x712: 0x8f, 0x713: 0x8f, 0x714: 0x8f, 0x715: 0x8f, 0x716: 0x8f, 0x717: 0x8f,
	0x718: 0x8f, 0x719: 0x8f, 0x71a: 0x8f, 0x71b: 0x8f, 0x71c: 0x15b, 0x71d: 0x8f, 0x71e: 0x8f, 0x71f: 0x8f,
	0x720: 0x15c, 0x721: 0x8f, 0x722: 0x8f, 0x723: 0x8f, 0x724: 0x8f, 0x725: 0x8f, 0x726: 0x8f, 0x727: 0x8f,
	0x728: 0x8f, 0x729: 0x8f, 0x72a: 0x8f, 0x72b: 0x8f, 0x72c: 0x8f, 0x72d: 0x8f, 0x72e: 0x8f, 0x72f: 0x8f,
	0x730: 0x8f, 0x731: 0x8f, 0x732: 0x8f, 0x733: 0x8f, 0x734: 0x8f, 0x735: 0x8f, 0x736: 0x8f, 0x737: 0x8f,
	0x738: 0x8f, 0x739: 0x8f, 0x73a: 0x8f, 0x73b: 0x8f, 0x73c: 0x8f, 0x73d: 0x8f, 0x73e: 0x8f, 0x73f: 0x8f,
	// Block 0x1d, offset 0x740
	0x740: 0x8f, 0x741: 0x8f, 0x742: 0x8f, 0x743: 0x8f, 0x744: 0x8f, 0x745: 0x8f, 0x746: 0x8f, 0x747: 0x8f,
	0x748: 0x8f, 0x749: 0x8f, 0x74a: 0x8f, 0x74b: 0x8f, 0x74c: 0x8f, 0x74d: 0x8f, 0x74e: 0x8f, 0x74f: 0x8f,
	0x750: 0x8f, 0x751: 0x8f, 0x752: 0x8f, 0x753: 0x8f, 0x754: 0x8f, 0x755: 0x8f, 0x756: 0x8f, 0x757: 0x8f,
	0x758: 0x8f, 0x759: 0x8f, 0x75a: 0x8f, 0x75b: 0x8f, 0x75c: 0x8f, 0x75d: 0x8f, 0x75e: 0x8f, 0x75f: 0x8f,
	0x760: 0x8f, 0x761: 0x8f, 0x762: 0x8f, 0x763: 0x8f, 0x764: 0x8f, 0x765: 0x8f, 0x766: 0x8f, 0x767: 0x8f,
	0x768: 0x8f, 0x769: 0x8f, 0x76a: 0x8f, 0x76b: 0x8f, 0x76c: 0x8f, 0x76d: 0x8f, 0x76e: 0x8f, 0x76f: 0x8f,
	0x770: 0x8f, 0x771: 0x8f, 0x772: 0x8f, 0x773: 0x8f, 0x774: 0x8f, 0x775: 0x8f, 0x776: 0x8f, 0x777: 0x8f,
	0x778: 0x8f, 0x779: 0x8f, 0x77a: 0x15d, 0x77b: 0x8f, 0x77c: 0x8f, 0x77d: 0x8f, 0x77e: 0x8f, 0x77f: 0x8f,
	// Block 0x1e, offset 0x780
	0x780: 0x8f, 0x781: 0x8f, 0x782: 0x8f, 0x783: 0x8f, 0x784: 0x8f, 0x785: 0x8f, 0x786: 0x8f, 0x787: 0x8f,
	0x788: 0x8f, 0x789: 0x8f, 0x78a: 0x8f, 0x78b: 0x8f, 0x78c: 0x8f, 0x78d: 0x8f, 0x78e: 0x8f, 0x78f: 0x8f,
	0x790: 0x8f, 0x791: 0x8f, 0x792: 0x8f, 0x793: 0x8f, 0x794: 0x8f, 0x795: 0x8f, 0x796: 0x8f, 0x797: 0x8f,
	0x798: 0x8f, 0x799: 0x8f, 0x79a: 0x8f, 0x79b: 0x8f, 0x79c: 0x8f, 0x79d: 0x8f, 0x79e: 0x8f, 0x79f: 0x8f,
	0x7a0: 0x8f, 0x7a1: 0x8f, 0x7a2: 0x8f, 0x7a3: 0x8f, 0x7a4: 0x8f, 0x7a5: 0x8f, 0x7a6: 0x8f, 0x7a7: 0x8f,
	0x7a8: 0x8f, 0x7a9: 0x8f, 0x7aa: 0x8f, 0x7ab: 0x8f, 0x7ac: 0x8f, 0x7ad: 0x8f, 0x7ae: 0x8f, 0x7af: 0x15e,
	// Block 0x1f, offset 0x7c0
	0x7e0: 0x82, 0x7e1: 0x82, 0x7e2: 0x82, 0x7e3: 0x82, 0x7e4: 0x82, 0x7e5: 0x82, 0x7e6: 0x82, 0x7e7: 0x82,
	0x7e8: 0x15f,
	// Block 0x20, offset 0x800
	0x800: 0x8f, 0x801: 0x8f, 0x802: 0x8f, 0x803: 0x8f, 0x804: 0x8f, 0x805: 0x8f, 0x806: 0x8f, 0x807: 0x8f,
	0x808: 0x8f, 0x809: 0x8f, 0x80a: 0x8f, 0x80b: 0x8f, 0x80c: 0x8f, 0x80d: 0x160,
	// Block 0x21, offset 0x840
	0x850: 0x0e, 0x851: 0x0f, 0x852: 0x10, 0x853: 0x11, 0x854: 0x12, 0x856: 0x13, 0x857: 0x0a,
	0x858: 0x14, 0x85b: 0x15, 0x85d: 0x16, 0x85e: 0x17, 0x85f: 0x18,
	0x860: 0x07, 0x861: 0x07, 0x862: 0x07, 0x863: 0x07, 0x864: 0x07, 0x865: 0x07, 0x866: 0x07, 0x867: 0x07,
	0x868: 0x07, 0x869: 0x07, 0x86a: 0x19, 0x86b: 0x1a, 0x86c: 0x1b, 0x86d: 0x07, 0x86e: 0x1c, 0x86f: 0x1d,
	0x870: 0x07, 0x871: 0x1e,
	// Block 0x22, offset 0x880
	0x880: 0x161, 0x881: 0x3e, 0x884: 0x3e, 0x885: 0x3e, 0x886: 0x3e, 0x887: 0x162,
	// Block 0x23, offset 0x8c0
	0x8c0: 0x3e, 0x8c1: 0x3e, 0x8c2: 0x3e, 0x8c3: 0x3e, 0x8c4: 0x3e, 0x8c5: 0x3e, 0x8c6: 0x3e, 0x8c7: 0x3e,
	0x8c8: 0x3e, 0x8c9: 0x3e, 0x8ca: 0x3e, 0x8cb: 0x3e, 0x8cc: 0x3e, 0x8cd: 0x3e, 0x8ce: 0x3e, 0x8cf: 0x3e,
	0x8d0: 0x3e, 0x8d1: 0x3e, 0x8d2: 0x3e, 0x8d3: 0x3e, 0x8d4: 0x3e, 0x8d5: 0x3e, 0x8d6: 0x3e, 0x8d7: 0x3e,
	0x8d8: 0x3e, 0x8d9: 0x3e, 0x8da: 0x3e, 0x8db: 0x3e, 0x8dc: 0x3e, 0x8dd: 0x3e, 0x8de: 0x3e, 0x8df: 0x3e,
	0x8e0: 0x3e, 0x8e1: 0x3e, 0x8e2: 0x3e, 0x8e3: 0x3e, 0x8e4: 0x3e, 0x8e5: 0x3e, 0x8e6: 0x3e, 0x8e7: 0x3e,
	0x8e8: 0x3e, 0x8e9: 0x3e, 0x8ea: 0x3e, 0x8eb: 0x3e, 0x8ec: 0x3e, 0x8ed: 0x3e, 0x8ee: 0x3e, 0x8ef: 0x3e,
	0x8f0: 0x3e, 0x8f1: 0x3e, 0x8f2: 0x3e, 0x8f3: 0x3e, 0x8f4: 0x3e, 0x8f5: 0x3e, 0x8f6: 0x3e, 0x8f7: 0x3e,
	0x8f8: 0x3e, 0x8f9: 0x3e, 0x8fa: 0x3e, 0x8fb: 0x3e, 0x8fc: 0x3e, 0x8fd: 0x3e, 0x8fe: 0x3e, 0x8ff: 0x163,
	// Block 0x24, offset 0x900
	0x920: 0x20,
	0x930: 0x0c, 0x931: 0x0c, 0x932: 0x0c, 0x933: 0x0c, 0x934: 0x0c, 0x935: 0x0c, 0x936: 0x0c, 0x937: 0x0c,
	0x938: 0x0c, 0x939: 0x0c, 0x93a: 0x0c, 0x93b: 0x0c, 0x93c: 0x0c, 0x93d: 0x0c, 0x93e: 0x0c, 0x93f: 0x21,
	// Block 0x25, offset 0x940
	0x940: 0x0c, 0x941: 0x0c, 0x942: 0x0c, 0x943: 0x0c, 0x944: 0x0c, 0x945: 0x0c, 0x946: 0x0c, 0x947: 0x0c,
	0x948: 0x0c, 0x949: 0x0c, 0x94a: 0x0c, 0x94b: 0x0c, 0x94c: 0x0c, 0x94d: 0x0c, 0x94e: 0x0c, 0x94f: 0x21,
}

// Total table size 27776 bytes (27KiB); checksum: 811C9DC5
