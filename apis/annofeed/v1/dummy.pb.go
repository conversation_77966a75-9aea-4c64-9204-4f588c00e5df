// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: annofeed/v1/dummy.proto

package annofeed

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DummyReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParamFileV2Ref *ParamFileV2 `protobuf:"bytes,1,opt,name=ParamFileV2Ref,proto3" json:"ParamFileV2Ref,omitempty"`
}

func (x *DummyReply) Reset() {
	*x = DummyReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_dummy_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DummyReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DummyReply) ProtoMessage() {}

func (x *DummyReply) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_dummy_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DummyReply.ProtoReflect.Descriptor instead.
func (*DummyReply) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_dummy_proto_rawDescGZIP(), []int{0}
}

func (x *DummyReply) GetParamFileV2Ref() *ParamFileV2 {
	if x != nil {
		return x.ParamFileV2Ref
	}
	return nil
}

var File_annofeed_v1_dummy_proto protoreflect.FileDescriptor

var file_annofeed_v1_dummy_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x75,
	0x6d, 0x6d, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x66,
	0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4e, 0x0a, 0x0a, 0x44, 0x75,
	0x6d, 0x6d, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x40, 0x0a, 0x0e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x46, 0x69, 0x6c, 0x65, 0x56, 0x32, 0x52, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x46, 0x69, 0x6c, 0x65, 0x56, 0x32, 0x52, 0x0e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x46, 0x69, 0x6c, 0x65, 0x56, 0x32, 0x52, 0x65, 0x66, 0x32, 0x54, 0x0a, 0x05, 0x44, 0x75,
	0x6d, 0x6d, 0x79, 0x12, 0x4b, 0x0a, 0x05, 0x44, 0x75, 0x6d, 0x6d, 0x79, 0x12, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x17, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x75, 0x6d, 0x6d, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x11, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x0b, 0x12, 0x09, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x75, 0x6d, 0x6d, 0x79,
	0x42, 0x4a, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x39, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64,
	0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_annofeed_v1_dummy_proto_rawDescOnce sync.Once
	file_annofeed_v1_dummy_proto_rawDescData = file_annofeed_v1_dummy_proto_rawDesc
)

func file_annofeed_v1_dummy_proto_rawDescGZIP() []byte {
	file_annofeed_v1_dummy_proto_rawDescOnce.Do(func() {
		file_annofeed_v1_dummy_proto_rawDescData = protoimpl.X.CompressGZIP(file_annofeed_v1_dummy_proto_rawDescData)
	})
	return file_annofeed_v1_dummy_proto_rawDescData
}

var file_annofeed_v1_dummy_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_annofeed_v1_dummy_proto_goTypes = []interface{}{
	(*DummyReply)(nil),    // 0: annofeed.v1.DummyReply
	(*ParamFileV2)(nil),   // 1: annofeed.v1.ParamFileV2
	(*emptypb.Empty)(nil), // 2: google.protobuf.Empty
}
var file_annofeed_v1_dummy_proto_depIdxs = []int32{
	1, // 0: annofeed.v1.DummyReply.ParamFileV2Ref:type_name -> annofeed.v1.ParamFileV2
	2, // 1: annofeed.v1.Dummy.Dummy:input_type -> google.protobuf.Empty
	0, // 2: annofeed.v1.Dummy.Dummy:output_type -> annofeed.v1.DummyReply
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_annofeed_v1_dummy_proto_init() }
func file_annofeed_v1_dummy_proto_init() {
	if File_annofeed_v1_dummy_proto != nil {
		return
	}
	file_annofeed_v1_param_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_annofeed_v1_dummy_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DummyReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_annofeed_v1_dummy_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_annofeed_v1_dummy_proto_goTypes,
		DependencyIndexes: file_annofeed_v1_dummy_proto_depIdxs,
		MessageInfos:      file_annofeed_v1_dummy_proto_msgTypes,
	}.Build()
	File_annofeed_v1_dummy_proto = out.File
	file_annofeed_v1_dummy_proto_rawDesc = nil
	file_annofeed_v1_dummy_proto_goTypes = nil
	file_annofeed_v1_dummy_proto_depIdxs = nil
}
