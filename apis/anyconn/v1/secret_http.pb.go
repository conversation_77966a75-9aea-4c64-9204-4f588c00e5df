// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: anyconn/v1/secret.proto

package anyconn

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationSecretsCreateSecret = "/anyconn.v1.Secrets/CreateSecret"
const OperationSecretsDeleteSecret = "/anyconn.v1.Secrets/DeleteSecret"
const OperationSecretsGetSecret = "/anyconn.v1.Secrets/GetSecret"
const OperationSecretsListSecret = "/anyconn.v1.Secrets/ListSecret"
const OperationSecretsUpdateSecret = "/anyconn.v1.Secrets/UpdateSecret"

type SecretsHTTPServer interface {
	CreateSecret(context.Context, *CreateSecretRequest) (*Secret, error)
	DeleteSecret(context.Context, *DeleteSecretRequest) (*emptypb.Empty, error)
	GetSecret(context.Context, *GetSecretRequest) (*Secret, error)
	ListSecret(context.Context, *ListSecretRequest) (*ListSecretReply, error)
	UpdateSecret(context.Context, *UpdateSecretRequest) (*Secret, error)
}

func RegisterSecretsHTTPServer(s *http.Server, srv SecretsHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/secrets", _Secrets_CreateSecret0_HTTP_Handler(srv))
	r.PATCH("/v1/secrets/{secret.uid}", _Secrets_UpdateSecret0_HTTP_Handler(srv))
	r.DELETE("/v1/secrets/{uid}", _Secrets_DeleteSecret0_HTTP_Handler(srv))
	r.GET("/v1/secrets/{uid}", _Secrets_GetSecret0_HTTP_Handler(srv))
	r.GET("/v1/secrets", _Secrets_ListSecret0_HTTP_Handler(srv))
}

func _Secrets_CreateSecret0_HTTP_Handler(srv SecretsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateSecretRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSecretsCreateSecret)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateSecret(ctx, req.(*CreateSecretRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Secret)
		return ctx.Result(200, reply)
	}
}

func _Secrets_UpdateSecret0_HTTP_Handler(srv SecretsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateSecretRequest
		if err := ctx.Bind(&in.Secret); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSecretsUpdateSecret)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateSecret(ctx, req.(*UpdateSecretRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Secret)
		return ctx.Result(200, reply)
	}
}

func _Secrets_DeleteSecret0_HTTP_Handler(srv SecretsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteSecretRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSecretsDeleteSecret)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteSecret(ctx, req.(*DeleteSecretRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Secrets_GetSecret0_HTTP_Handler(srv SecretsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSecretRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSecretsGetSecret)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSecret(ctx, req.(*GetSecretRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Secret)
		return ctx.Result(200, reply)
	}
}

func _Secrets_ListSecret0_HTTP_Handler(srv SecretsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListSecretRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSecretsListSecret)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListSecret(ctx, req.(*ListSecretRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListSecretReply)
		return ctx.Result(200, reply)
	}
}

type SecretsHTTPClient interface {
	CreateSecret(ctx context.Context, req *CreateSecretRequest, opts ...http.CallOption) (rsp *Secret, err error)
	DeleteSecret(ctx context.Context, req *DeleteSecretRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetSecret(ctx context.Context, req *GetSecretRequest, opts ...http.CallOption) (rsp *Secret, err error)
	ListSecret(ctx context.Context, req *ListSecretRequest, opts ...http.CallOption) (rsp *ListSecretReply, err error)
	UpdateSecret(ctx context.Context, req *UpdateSecretRequest, opts ...http.CallOption) (rsp *Secret, err error)
}

type SecretsHTTPClientImpl struct {
	cc *http.Client
}

func NewSecretsHTTPClient(client *http.Client) SecretsHTTPClient {
	return &SecretsHTTPClientImpl{client}
}

func (c *SecretsHTTPClientImpl) CreateSecret(ctx context.Context, in *CreateSecretRequest, opts ...http.CallOption) (*Secret, error) {
	var out Secret
	pattern := "/v1/secrets"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSecretsCreateSecret))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SecretsHTTPClientImpl) DeleteSecret(ctx context.Context, in *DeleteSecretRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/secrets/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSecretsDeleteSecret))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SecretsHTTPClientImpl) GetSecret(ctx context.Context, in *GetSecretRequest, opts ...http.CallOption) (*Secret, error) {
	var out Secret
	pattern := "/v1/secrets/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSecretsGetSecret))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SecretsHTTPClientImpl) ListSecret(ctx context.Context, in *ListSecretRequest, opts ...http.CallOption) (*ListSecretReply, error) {
	var out ListSecretReply
	pattern := "/v1/secrets"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSecretsListSecret))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SecretsHTTPClientImpl) UpdateSecret(ctx context.Context, in *UpdateSecretRequest, opts ...http.CallOption) (*Secret, error) {
	var out Secret
	pattern := "/v1/secrets/{secret.uid}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSecretsUpdateSecret))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in.Secret, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
