// source: annostat/v1/Production.proto
package biz

import (
	"time"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
)

type Production struct {
	ID           int64      `json:"id"`
	LotID        int64      `json:"lot_id" gorm:"default:null"`
	Phase        int32      `json:"phase" gorm:"default:null"`
	ExecutorUid  string     `json:"executor_uid" gorm:"default:null"` // the user which the job is assigned to
	ExecteamUid  string     `json:"execteam_uid" gorm:"default:null"` // the team which the executor belongs to
	Jobs         int32      `json:"jobs" gorm:"default:null"`
	Elems        int32      `json:"elems" gorm:"default:null"`
	Ins          int32      `json:"ins" gorm:"default:null"`
	Ins2d        int32      `json:"ins_2d" gorm:"default:null;column:ins_2d"`
	Ins3d        int32      `json:"ins_3d" gorm:"default:null;column:ins_3d"`
	InsByClass   InsByClass `json:"ins_by_class" gorm:"default:null"`
	PeriodStart  time.Time  `json:"period_start" gorm:"default:null"`
	Duration     int32      `json:"duration" gorm:"default:null"`
	WorkDuration int32      `json:"work_duration" gorm:"default:null"`
	CreatedAt    time.Time  `json:"created_at" gorm:"default:null"`
	WorkFrom     time.Time  `json:"work_from" gorm:"default:null"`
	WorkTo       time.Time  `json:"work_to" gorm:"default:null"`
	ExecutorCnt  int32      `json:"executor_cnt" gorm:"default:null"`
}

func (o *Production) GetID() int64 { return o.ID }

const ProdutionTableName = "productions"

var (
	production_ = field.RegObject(&Production{})
	// ProductionUpdatableFlds = field.NewModel(Production_)

	// field name in snake case
	ProductionSfldLotID        = field.Sname(&production_.LotID)
	ProductionSfldPhase        = field.Sname(&production_.Phase)
	ProductionSfldIns          = field.Sname(&production_.Ins)
	ProductionSfldIns2d        = field.Sname(&production_.Ins2d)
	ProductionSfldIns3d        = field.Sname(&production_.Ins3d)
	ProductionSfldElems        = field.Sname(&production_.Elems)
	ProductionSfldJobs         = field.Sname(&production_.Jobs)
	ProductionSfldExecutorUid  = field.Sname(&production_.ExecutorUid)
	ProductionSfldExecteamUid  = field.Sname(&production_.ExecteamUid)
	ProductionSfldDuration     = field.Sname(&production_.Duration)
	ProductionSfldWorkDuration = field.Sname(&production_.WorkDuration)
	ProductionSfldPeriodStart  = field.Sname(&production_.PeriodStart)
	ProductionSfldWorkFrom     = field.Sname(&production_.WorkFrom)
	ProductionSfldWorkTo       = field.Sname(&production_.WorkTo)
	ProductionSfldExecutorCnt  = field.Sname(&production_.ExecutorCnt)
)

type ProductionListFilter struct {
	TimeRange

	IDs    []int64
	LotIDs []int64
	Phases []int32

	ExecutorUids []string
	ExecteamUids []string

	Duration int32

	BizgranteeUid  string // to check kam's grants
	SpecgranteeUid string // to check pm's grants
}

func (o *ProductionListFilter) Apply(tx Tx) Tx {
	if o == nil {
		return tx
	}

	tbl := "productions."
	tx = ApplyFieldFilter(tx, tbl+"id", o.IDs)
	tx = ApplyFieldFilter(tx, tbl+ProductionSfldLotID, o.LotIDs)
	tx = ApplyFieldFilter(tx, tbl+ProductionSfldPhase, o.Phases)
	tx = ApplyFieldFilter(tx, tbl+ProductionSfldExecutorUid, o.ExecutorUids)
	tx = ApplyFieldFilter(tx, tbl+ProductionSfldExecteamUid, o.ExecteamUids)
	tx = ApplyFieldFilter(tx, tbl+ProductionSfldDuration, o.Duration)
	tx = ApplyTimeRangeFilter(tx, tbl+ProductionSfldPeriodStart, &o.TimeRange)

	if o.BizgranteeUid != "" { // to check kam's grants
		f := &JoinFilter{
			LeftModel:    &Production{},
			LeftJoinFld:  ProductionSfldLotID,
			RightModel:   &Lot{},
			RightJoinFld: LotSfldID,
		}
		tx = f.Apply(tx)

		f = &JoinFilter{
			LeftModel:    &Lot{},
			LeftJoinFld:  LotSfldOrgUid,
			RightModel:   &Bizgrant{},
			RightJoinFld: BizgrantSfldOrgUid.String(),
			Filter:       &BizgrantFilter{GranteeUid: o.BizgranteeUid},
		}
		tx = f.Apply(tx)
	}

	if o.SpecgranteeUid != "" { // to check pm's grants
		f := &JoinFilter{
			LeftModel:    &Production{},
			LeftJoinFld:  ProductionSfldLotID,
			RightModel:   &Lot{},
			RightJoinFld: LotSfldID,
		}
		tx = f.Apply(tx)

		f = &JoinFilter{
			LeftModel:    &Lot{},
			LeftJoinFld:  LotSfldOrgUid,
			RightModel:   &Specgrant{},
			RightJoinFld: SpecgrantSfldOrgUid.String(),
			Filter: &SpecgrantFilter{
				GranteeUid: o.BizgranteeUid,
				ItemType:   anno.Specgrant_ItemType_AnnoLot.String(), // AnnoLot
			},
		}
		tx = f.Apply(tx)
	}

	return tx
}

type ProductionsRepo repo.GenericRepo[Production]

// type ProductionsBiz struct {
// 	repo   ProductionsRepo
// 	bgtask BackgroundTask
// 	log    *log.Helper
// }

// func NewProductionsBiz(repo ProductionsRepo, bgtask BackgroundTask, logger log.Logger) *ProductionsBiz {
// 	return &ProductionsBiz{repo: repo, bgtask: bgtask, log: log.NewHelper(logger)}
// }
