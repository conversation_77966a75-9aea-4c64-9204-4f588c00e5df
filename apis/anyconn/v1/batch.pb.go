// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anyconn/v1/batch.proto

package anyconn

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Source_ParseErrorHandler_Error_Enum int32

const (
	Source_ParseErrorHandler_Error_unspecified  Source_ParseErrorHandler_Error_Enum = 0
	Source_ParseErrorHandler_Error_file_unknown Source_ParseErrorHandler_Error_Enum = 1
	Source_ParseErrorHandler_Error_file_missing Source_ParseErrorHandler_Error_Enum = 2
)

// Enum value maps for Source_ParseErrorHandler_Error_Enum.
var (
	Source_ParseErrorHandler_Error_Enum_name = map[int32]string{
		0: "unspecified",
		1: "file_unknown",
		2: "file_missing",
	}
	Source_ParseErrorHandler_Error_Enum_value = map[string]int32{
		"unspecified":  0,
		"file_unknown": 1,
		"file_missing": 2,
	}
)

func (x Source_ParseErrorHandler_Error_Enum) Enum() *Source_ParseErrorHandler_Error_Enum {
	p := new(Source_ParseErrorHandler_Error_Enum)
	*p = x
	return p
}

func (x Source_ParseErrorHandler_Error_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Source_ParseErrorHandler_Error_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anyconn_v1_batch_proto_enumTypes[0].Descriptor()
}

func (Source_ParseErrorHandler_Error_Enum) Type() protoreflect.EnumType {
	return &file_anyconn_v1_batch_proto_enumTypes[0]
}

func (x Source_ParseErrorHandler_Error_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Source_ParseErrorHandler_Error_Enum.Descriptor instead.
func (Source_ParseErrorHandler_Error_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{0, 1, 0, 0}
}

type Source_ParseErrorHandler_Handler_Enum int32

const (
	Source_ParseErrorHandler_Handler_unspecified Source_ParseErrorHandler_Handler_Enum = 0
	Source_ParseErrorHandler_Handler_fail        Source_ParseErrorHandler_Handler_Enum = 1 // if a rawdata like image has error, fail the total data workflow
	Source_ParseErrorHandler_Handler_ignore      Source_ParseErrorHandler_Handler_Enum = 2 // if a rawdata like image has error, just ignore this error
)

// Enum value maps for Source_ParseErrorHandler_Handler_Enum.
var (
	Source_ParseErrorHandler_Handler_Enum_name = map[int32]string{
		0: "unspecified",
		1: "fail",
		2: "ignore",
	}
	Source_ParseErrorHandler_Handler_Enum_value = map[string]int32{
		"unspecified": 0,
		"fail":        1,
		"ignore":      2,
	}
)

func (x Source_ParseErrorHandler_Handler_Enum) Enum() *Source_ParseErrorHandler_Handler_Enum {
	p := new(Source_ParseErrorHandler_Handler_Enum)
	*p = x
	return p
}

func (x Source_ParseErrorHandler_Handler_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Source_ParseErrorHandler_Handler_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anyconn_v1_batch_proto_enumTypes[1].Descriptor()
}

func (Source_ParseErrorHandler_Handler_Enum) Type() protoreflect.EnumType {
	return &file_anyconn_v1_batch_proto_enumTypes[1]
}

func (x Source_ParseErrorHandler_Handler_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Source_ParseErrorHandler_Handler_Enum.Descriptor instead.
func (Source_ParseErrorHandler_Handler_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{0, 1, 1, 0}
}

type Batch_State_Enum int32

const (
	Batch_State_unspecified Batch_State_Enum = 0
	// parsing the data specified in the source
	Batch_State_initializing Batch_State_Enum = 1
	// waiting for the batch related lot to be started
	Batch_State_waiting  Batch_State_Enum = 2
	Batch_State_ongoing  Batch_State_Enum = 3
	Batch_State_finished Batch_State_Enum = 4
	Batch_State_canceled Batch_State_Enum = 5
	// error occurred when parsing data specified by the source
	Batch_State_failed Batch_State_Enum = 6
)

// Enum value maps for Batch_State_Enum.
var (
	Batch_State_Enum_name = map[int32]string{
		0: "unspecified",
		1: "initializing",
		2: "waiting",
		3: "ongoing",
		4: "finished",
		5: "canceled",
		6: "failed",
	}
	Batch_State_Enum_value = map[string]int32{
		"unspecified":  0,
		"initializing": 1,
		"waiting":      2,
		"ongoing":      3,
		"finished":     4,
		"canceled":     5,
		"failed":       6,
	}
)

func (x Batch_State_Enum) Enum() *Batch_State_Enum {
	p := new(Batch_State_Enum)
	*p = x
	return p
}

func (x Batch_State_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Batch_State_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anyconn_v1_batch_proto_enumTypes[2].Descriptor()
}

func (Batch_State_Enum) Type() protoreflect.EnumType {
	return &file_anyconn_v1_batch_proto_enumTypes[2]
}

func (x Batch_State_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Batch_State_Enum.Descriptor instead.
func (Batch_State_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{7, 0, 0}
}

type Source struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// package file (.zip) URIs
	Uris []string `protobuf:"bytes,1,rep,name=uris,proto3" json:"uris,omitempty"`
	// access config when the files are hosted in a third-party platform
	Proprietary *Source_Proprietary `protobuf:"bytes,2,opt,name=proprietary,proto3" json:"proprietary,omitempty"`
	// folder layout style within package files if not conform to Konvery standard
	Style string `protobuf:"bytes,3,opt,name=style,proto3" json:"style,omitempty"`
	// // element type
	// Element.Type.Enum elem_type = 4;
	// if source contains consecutive frames
	IsFrameSeries bool `protobuf:"varint,5,opt,name=is_frame_series,json=isFrameSeries,proto3" json:"is_frame_series,omitempty"`
	// size of the unpacked data in GB
	PlainSizeGb int32 `protobuf:"varint,6,opt,name=plain_size_gb,json=plainSizeGb,proto3" json:"plain_size_gb,omitempty"`
	// define parser error handlers; it will fail the parser if no handler is specified.
	// max size is count(RawdataErrorAction.Error) x count(Rawdata.Type).
	ErrorHandlers []*Source_ParseErrorHandler `protobuf:"bytes,7,rep,name=error_handlers,json=errorHandlers,proto3" json:"error_handlers,omitempty"`
}

func (x *Source) Reset() {
	*x = Source{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_batch_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Source) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source) ProtoMessage() {}

func (x *Source) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_batch_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source.ProtoReflect.Descriptor instead.
func (*Source) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{0}
}

func (x *Source) GetUris() []string {
	if x != nil {
		return x.Uris
	}
	return nil
}

func (x *Source) GetProprietary() *Source_Proprietary {
	if x != nil {
		return x.Proprietary
	}
	return nil
}

func (x *Source) GetStyle() string {
	if x != nil {
		return x.Style
	}
	return ""
}

func (x *Source) GetIsFrameSeries() bool {
	if x != nil {
		return x.IsFrameSeries
	}
	return false
}

func (x *Source) GetPlainSizeGb() int32 {
	if x != nil {
		return x.PlainSizeGb
	}
	return 0
}

func (x *Source) GetErrorHandlers() []*Source_ParseErrorHandler {
	if x != nil {
		return x.ErrorHandlers
	}
	return nil
}

type CreateBatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory in update-requests
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// mandatory in create-requests;
	// pattern: ^[\\p{Han}\\w\\d-]{0,20}$
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// batch's organization; if empty, this is the requestor's organization
	OrgUid string `protobuf:"bytes,3,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// files attached to the batch
	Source *Source `protobuf:"bytes,4,opt,name=source,proto3" json:"source,omitempty"`
}

func (x *CreateBatchRequest) Reset() {
	*x = CreateBatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_batch_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBatchRequest) ProtoMessage() {}

func (x *CreateBatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_batch_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBatchRequest.ProtoReflect.Descriptor instead.
func (*CreateBatchRequest) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{1}
}

func (x *CreateBatchRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *CreateBatchRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateBatchRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *CreateBatchRequest) GetSource() *Source {
	if x != nil {
		return x.Source
	}
	return nil
}

type UpdateBatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update contents
	Batch *Batch `protobuf:"bytes,1,opt,name=batch,proto3" json:"batch,omitempty"`
	// name of fileds to be updated
	Fields []string `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *UpdateBatchRequest) Reset() {
	*x = UpdateBatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_batch_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBatchRequest) ProtoMessage() {}

func (x *UpdateBatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_batch_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBatchRequest.ProtoReflect.Descriptor instead.
func (*UpdateBatchRequest) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateBatchRequest) GetBatch() *Batch {
	if x != nil {
		return x.Batch
	}
	return nil
}

func (x *UpdateBatchRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type DeleteBatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// batch UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *DeleteBatchRequest) Reset() {
	*x = DeleteBatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_batch_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteBatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBatchRequest) ProtoMessage() {}

func (x *DeleteBatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_batch_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBatchRequest.ProtoReflect.Descriptor instead.
func (*DeleteBatchRequest) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteBatchRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetBatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// batch UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *GetBatchRequest) Reset() {
	*x = GetBatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_batch_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBatchRequest) ProtoMessage() {}

func (x *GetBatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_batch_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBatchRequest.ProtoReflect.Descriptor instead.
func (*GetBatchRequest) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{4}
}

func (x *GetBatchRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type ListBatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page   int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Pagesz int32 `protobuf:"varint,2,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// filter by orgnization
	OrgUid string `protobuf:"bytes,3,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// filter by creator
	CreatorUid string `protobuf:"bytes,4,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	// filter by name pattern
	NamePattern string `protobuf:"bytes,5,opt,name=name_pattern,json=namePattern,proto3" json:"name_pattern,omitempty"`
	// filter by batch state
	States []Batch_State_Enum `protobuf:"varint,6,rep,packed,name=states,proto3,enum=anyconn.v1.Batch_State_Enum" json:"states,omitempty"`
	// include batch's orgnization in the reply
	WithOrg bool `protobuf:"varint,7,opt,name=with_org,json=withOrg,proto3" json:"with_org,omitempty"`
}

func (x *ListBatchRequest) Reset() {
	*x = ListBatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_batch_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBatchRequest) ProtoMessage() {}

func (x *ListBatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_batch_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBatchRequest.ProtoReflect.Descriptor instead.
func (*ListBatchRequest) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{5}
}

func (x *ListBatchRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListBatchRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListBatchRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *ListBatchRequest) GetCreatorUid() string {
	if x != nil {
		return x.CreatorUid
	}
	return ""
}

func (x *ListBatchRequest) GetNamePattern() string {
	if x != nil {
		return x.NamePattern
	}
	return ""
}

func (x *ListBatchRequest) GetStates() []Batch_State_Enum {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *ListBatchRequest) GetWithOrg() bool {
	if x != nil {
		return x.WithOrg
	}
	return false
}

type ListBatchReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total   int32    `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Batches []*Batch `protobuf:"bytes,2,rep,name=batches,proto3" json:"batches,omitempty"`
}

func (x *ListBatchReply) Reset() {
	*x = ListBatchReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_batch_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBatchReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBatchReply) ProtoMessage() {}

func (x *ListBatchReply) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_batch_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBatchReply.ProtoReflect.Descriptor instead.
func (*ListBatchReply) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{6}
}

func (x *ListBatchReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListBatchReply) GetBatches() []*Batch {
	if x != nil {
		return x.Batches
	}
	return nil
}

type Batch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// batch UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// batch name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// UID of the organization which the batch belongs to
	OrgUid string `protobuf:"bytes,3,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// files attached to the batch
	Source *Source `protobuf:"bytes,4,opt,name=source,proto3" json:"source,omitempty"`
	// UID of the data associated with the batch
	DataUid string `protobuf:"bytes,5,opt,name=data_uid,json=dataUid,proto3" json:"data_uid,omitempty"`
	// creator UID
	CreatorUid string `protobuf:"bytes,6,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	// number of elements in the batch
	Size int32 `protobuf:"varint,10,opt,name=size,proto3" json:"size,omitempty"`
	// batch state
	State Batch_State_Enum `protobuf:"varint,11,opt,name=state,proto3,enum=anyconn.v1.Batch_State_Enum" json:"state,omitempty"`
	// annotated object count (include interpolated ones); only available after lot is finished
	InsTotal int32 `protobuf:"varint,12,opt,name=ins_total,json=insTotal,proto3" json:"ins_total,omitempty"`
	// annotation result file URL
	AnnoResultUrl string `protobuf:"bytes,13,opt,name=anno_result_url,json=annoResultUrl,proto3" json:"anno_result_url,omitempty"`
	// when state is failed, this field will contain detailed error message
	Error string `protobuf:"bytes,14,opt,name=error,proto3" json:"error,omitempty"`
	// batch creation time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// data validation summary
	DataSummary *DataValidationSummary `protobuf:"bytes,16,opt,name=data_summary,json=dataSummary,proto3" json:"data_summary,omitempty"`
}

func (x *Batch) Reset() {
	*x = Batch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_batch_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Batch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Batch) ProtoMessage() {}

func (x *Batch) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_batch_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Batch.ProtoReflect.Descriptor instead.
func (*Batch) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{7}
}

func (x *Batch) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Batch) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Batch) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *Batch) GetSource() *Source {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *Batch) GetDataUid() string {
	if x != nil {
		return x.DataUid
	}
	return ""
}

func (x *Batch) GetCreatorUid() string {
	if x != nil {
		return x.CreatorUid
	}
	return ""
}

func (x *Batch) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Batch) GetState() Batch_State_Enum {
	if x != nil {
		return x.State
	}
	return Batch_State_unspecified
}

func (x *Batch) GetInsTotal() int32 {
	if x != nil {
		return x.InsTotal
	}
	return 0
}

func (x *Batch) GetAnnoResultUrl() string {
	if x != nil {
		return x.AnnoResultUrl
	}
	return ""
}

func (x *Batch) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *Batch) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Batch) GetDataSummary() *DataValidationSummary {
	if x != nil {
		return x.DataSummary
	}
	return nil
}

type SetBatchAnnoResultRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// batch UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// URL to the result
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *SetBatchAnnoResultRequest) Reset() {
	*x = SetBatchAnnoResultRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_batch_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetBatchAnnoResultRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetBatchAnnoResultRequest) ProtoMessage() {}

func (x *SetBatchAnnoResultRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_batch_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetBatchAnnoResultRequest.ProtoReflect.Descriptor instead.
func (*SetBatchAnnoResultRequest) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{8}
}

func (x *SetBatchAnnoResultRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *SetBatchAnnoResultRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type GetBatchAnnoResultReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the estimated time when the result is ready;
	// valid only when the batch is in the finished state
	WillReadyAt *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=will_ready_at,json=willReadyAt,proto3" json:"will_ready_at,omitempty"`
	// URL to the result if available
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *GetBatchAnnoResultReply) Reset() {
	*x = GetBatchAnnoResultReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_batch_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBatchAnnoResultReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBatchAnnoResultReply) ProtoMessage() {}

func (x *GetBatchAnnoResultReply) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_batch_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBatchAnnoResultReply.ProtoReflect.Descriptor instead.
func (*GetBatchAnnoResultReply) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{9}
}

func (x *GetBatchAnnoResultReply) GetWillReadyAt() *timestamppb.Timestamp {
	if x != nil {
		return x.WillReadyAt
	}
	return nil
}

func (x *GetBatchAnnoResultReply) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type DataValidationSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of validation errors found, includes those unsaved errors
	TotalErrors int32 `protobuf:"varint,1,opt,name=total_errors,json=totalErrors,proto3" json:"total_errors,omitempty"`
	// validation errors; only the first 10 errors are saved
	Errors []*DataValidationSummary_Error `protobuf:"bytes,2,rep,name=errors,proto3" json:"errors,omitempty"`
}

func (x *DataValidationSummary) Reset() {
	*x = DataValidationSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_batch_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataValidationSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataValidationSummary) ProtoMessage() {}

func (x *DataValidationSummary) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_batch_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataValidationSummary.ProtoReflect.Descriptor instead.
func (*DataValidationSummary) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{10}
}

func (x *DataValidationSummary) GetTotalErrors() int32 {
	if x != nil {
		return x.TotalErrors
	}
	return 0
}

func (x *DataValidationSummary) GetErrors() []*DataValidationSummary_Error {
	if x != nil {
		return x.Errors
	}
	return nil
}

type Source_Proprietary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 3rd-party file host service type
	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	// 3rd-party file host service access config
	Config string `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *Source_Proprietary) Reset() {
	*x = Source_Proprietary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_batch_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Source_Proprietary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source_Proprietary) ProtoMessage() {}

func (x *Source_Proprietary) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_batch_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source_Proprietary.ProtoReflect.Descriptor instead.
func (*Source_Proprietary) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Source_Proprietary) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Source_Proprietary) GetConfig() string {
	if x != nil {
		return x.Config
	}
	return ""
}

type Source_ParseErrorHandler struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// anyconn.v1.Rawdata.Type.Enum rawdata_type = 1; // image or pcd
	Error   Source_ParseErrorHandler_Error_Enum   `protobuf:"varint,2,opt,name=error,proto3,enum=anyconn.v1.Source_ParseErrorHandler_Error_Enum" json:"error,omitempty"`
	Handler Source_ParseErrorHandler_Handler_Enum `protobuf:"varint,3,opt,name=handler,proto3,enum=anyconn.v1.Source_ParseErrorHandler_Handler_Enum" json:"handler,omitempty"`
}

func (x *Source_ParseErrorHandler) Reset() {
	*x = Source_ParseErrorHandler{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_batch_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Source_ParseErrorHandler) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source_ParseErrorHandler) ProtoMessage() {}

func (x *Source_ParseErrorHandler) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_batch_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source_ParseErrorHandler.ProtoReflect.Descriptor instead.
func (*Source_ParseErrorHandler) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Source_ParseErrorHandler) GetError() Source_ParseErrorHandler_Error_Enum {
	if x != nil {
		return x.Error
	}
	return Source_ParseErrorHandler_Error_unspecified
}

func (x *Source_ParseErrorHandler) GetHandler() Source_ParseErrorHandler_Handler_Enum {
	if x != nil {
		return x.Handler
	}
	return Source_ParseErrorHandler_Handler_unspecified
}

type Source_ParseErrorHandler_Error struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Source_ParseErrorHandler_Error) Reset() {
	*x = Source_ParseErrorHandler_Error{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_batch_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Source_ParseErrorHandler_Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source_ParseErrorHandler_Error) ProtoMessage() {}

func (x *Source_ParseErrorHandler_Error) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_batch_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source_ParseErrorHandler_Error.ProtoReflect.Descriptor instead.
func (*Source_ParseErrorHandler_Error) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{0, 1, 0}
}

type Source_ParseErrorHandler_Handler struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Source_ParseErrorHandler_Handler) Reset() {
	*x = Source_ParseErrorHandler_Handler{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_batch_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Source_ParseErrorHandler_Handler) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source_ParseErrorHandler_Handler) ProtoMessage() {}

func (x *Source_ParseErrorHandler_Handler) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_batch_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source_ParseErrorHandler_Handler.ProtoReflect.Descriptor instead.
func (*Source_ParseErrorHandler_Handler) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{0, 1, 1}
}

type Batch_State struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Batch_State) Reset() {
	*x = Batch_State{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_batch_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Batch_State) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Batch_State) ProtoMessage() {}

func (x *Batch_State) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_batch_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Batch_State.ProtoReflect.Descriptor instead.
func (*Batch_State) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{7, 0}
}

type DataValidationSummary_Error struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Error       Source_ParseErrorHandler_Error_Enum `protobuf:"varint,1,opt,name=error,proto3,enum=anyconn.v1.Source_ParseErrorHandler_Error_Enum" json:"error,omitempty"`
	ElemIndex   int32                               `protobuf:"varint,2,opt,name=elem_index,json=elemIndex,proto3" json:"elem_index,omitempty"`
	RawdataName string                              `protobuf:"bytes,3,opt,name=rawdata_name,json=rawdataName,proto3" json:"rawdata_name,omitempty"`
}

func (x *DataValidationSummary_Error) Reset() {
	*x = DataValidationSummary_Error{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_batch_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataValidationSummary_Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataValidationSummary_Error) ProtoMessage() {}

func (x *DataValidationSummary_Error) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_batch_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataValidationSummary_Error.ProtoReflect.Descriptor instead.
func (*DataValidationSummary_Error) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_batch_proto_rawDescGZIP(), []int{10, 0}
}

func (x *DataValidationSummary_Error) GetError() Source_ParseErrorHandler_Error_Enum {
	if x != nil {
		return x.Error
	}
	return Source_ParseErrorHandler_Error_unspecified
}

func (x *DataValidationSummary_Error) GetElemIndex() int32 {
	if x != nil {
		return x.ElemIndex
	}
	return 0
}

func (x *DataValidationSummary_Error) GetRawdataName() string {
	if x != nil {
		return x.RawdataName
	}
	return ""
}

var File_anyconn_v1_batch_proto protoreflect.FileDescriptor

var file_anyconn_v1_batch_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x61, 0x74,
	0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e,
	0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x93, 0x05, 0x0a, 0x06, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x72, 0x69, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x04, 0x75, 0x72, 0x69, 0x73, 0x12, 0x40, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x70, 0x72,
	0x69, 0x65, 0x74, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61,
	0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x50, 0x72, 0x6f, 0x70, 0x72, 0x69, 0x65, 0x74, 0x61, 0x72, 0x79, 0x52, 0x0b, 0x70, 0x72,
	0x6f, 0x70, 0x72, 0x69, 0x65, 0x74, 0x61, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x79,
	0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x12,
	0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x46, 0x72, 0x61, 0x6d,
	0x65, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x69, 0x6e,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x67, 0x62, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x53, 0x69, 0x7a, 0x65, 0x47, 0x62, 0x12, 0x4b, 0x0a, 0x0e, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x65, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x52, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x73, 0x1a, 0x4e, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x70,
	0x72, 0x69, 0x65, 0x74, 0x61, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x3a, 0x13, 0xba, 0x47, 0x10, 0xba, 0x01, 0x04, 0x74, 0x79, 0x70, 0x65, 0xba,
	0x01, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0xa7, 0x02, 0x0a, 0x11, 0x50, 0x61, 0x72,
	0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x12, 0x45,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e,
	0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x72, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x4b, 0x0a, 0x07, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x65,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x2e, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x07, 0x68, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x72, 0x1a, 0x44, 0x0a, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x3b, 0x0a, 0x04, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x10, 0x02, 0x1a, 0x38, 0x0a, 0x07, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x72, 0x22, 0x2d, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04,
	0x66, 0x61, 0x69, 0x6c, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x10, 0x02, 0x3a, 0x0a, 0xba, 0x47, 0x07, 0xba, 0x01, 0x04, 0x75, 0x72, 0x69, 0x73, 0x22, 0xa3,
	0x01, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x31, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0xfa, 0x42, 0x1a, 0x72, 0x18, 0x32, 0x16, 0x5e, 0x5b,
	0x5c, 0x70, 0x7b, 0x48, 0x61, 0x6e, 0x7d, 0x5c, 0x77, 0x5c, 0x64, 0x2d, 0x5d, 0x7b, 0x30, 0x2c,
	0x32, 0x30, 0x7d, 0x24, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72,
	0x67, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x67,
	0x55, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x3a,
	0x03, 0xba, 0x47, 0x00, 0x22, 0x6b, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x05, 0x62, 0x61,
	0x74, 0x63, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x6e, 0x79, 0x63,
	0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x05, 0x62, 0x61,
	0x74, 0x63, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x3a, 0x14, 0xba, 0x47, 0x11,
	0xba, 0x01, 0x05, 0x62, 0x61, 0x74, 0x63, 0x68, 0xba, 0x01, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x22, 0x26, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x23, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0xec,
	0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73,
	0x7a, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x12,
	0x17, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x61, 0x6d,
	0x65, 0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x34, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61,
	0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x6f, 0x72, 0x67, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x77, 0x69, 0x74, 0x68, 0x4f, 0x72, 0x67, 0x22, 0x53, 0x0a,
	0x0e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2b, 0x0a, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x73, 0x22, 0xfe, 0x04, 0x0a, 0x05, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x6e,
	0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x55,
	0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x55, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69,
	0x6e, 0x73, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x69, 0x6e, 0x73, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x6e, 0x6e, 0x6f,
	0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x61, 0x6e, 0x6e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x55, 0x72, 0x6c,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x49, 0x0a, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52,
	0x0b, 0x64, 0x61, 0x74, 0x61, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x1a, 0x74, 0x0a, 0x05,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x6b, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a,
	0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x10,
	0x0a, 0x0c, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x69, 0x6e, 0x67, 0x10, 0x01,
	0x12, 0x0b, 0x0a, 0x07, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x10, 0x02, 0x12, 0x0b, 0x0a,
	0x07, 0x6f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x66, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x63, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x65, 0x64, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x10, 0x06, 0x3a, 0x2f, 0xba, 0x47, 0x2c, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0xba, 0x01,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0xba, 0x01, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x22, 0x3f, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x41,
	0x6e, 0x6e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x75, 0x72, 0x6c, 0x22, 0x6b, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x3e, 0x0a, 0x0d, 0x77, 0x69, 0x6c, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x79, 0x5f, 0x61, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0b, 0x77, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x61, 0x64, 0x79, 0x41, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x22, 0x8e, 0x02, 0x0a, 0x15, 0x44, 0x61, 0x74, 0x61, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x3f,
	0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x1a,
	0x90, 0x01, 0x0a, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x45, 0x0a, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f,
	0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x50, 0x61, 0x72,
	0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6c, 0x65, 0x6d, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x6c, 0x65, 0x6d, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12,
	0x21, 0x0a, 0x0c, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x4e, 0x61,
	0x6d, 0x65, 0x32, 0xc5, 0x06, 0x0a, 0x07, 0x42, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x12, 0x58,
	0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x1e, 0x2e,
	0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x11, 0x2e,
	0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x3a, 0x01, 0x2a, 0x22, 0x0b, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x12, 0x68, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x1e, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x11, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x20, 0x3a, 0x05, 0x62, 0x61, 0x74, 0x63, 0x68, 0x32, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x62,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x2f, 0x7b, 0x62, 0x61, 0x74, 0x63, 0x68, 0x2e, 0x75, 0x69,
	0x64, 0x7d, 0x12, 0x67, 0x0a, 0x0b, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x12, 0x1b, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01,
	0x2a, 0x1a, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x2f, 0x7b,
	0x75, 0x69, 0x64, 0x7d, 0x2f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x12, 0x60, 0x0a, 0x0b, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x1e, 0x2e, 0x61, 0x6e, 0x79,
	0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x2a, 0x11, 0x2f, 0x76, 0x31, 0x2f,
	0x62, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x55, 0x0a,
	0x08, 0x47, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x1b, 0x2e, 0x61, 0x6e, 0x79, 0x63,
	0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x11, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x13, 0x12, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x2f, 0x7b,
	0x75, 0x69, 0x64, 0x7d, 0x12, 0x5a, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x12, 0x1c, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1a, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x13, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x0d, 0x12, 0x0b, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73,
	0x12, 0x7b, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x25, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x1a, 0x20, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x61,
	0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2d, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7b, 0x0a,
	0x0d, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1b,
	0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x6e,
	0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x61, 0x6e, 0x79, 0x63,
	0x6f, 0x6e, 0x6e, 0x2d, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x47, 0x0a, 0x0a, 0x61, 0x6e,
	0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x37, 0x67, 0x69, 0x74, 0x6c,
	0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f,
	0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73,
	0x2f, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e, 0x79, 0x63,
	0x6f, 0x6e, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_anyconn_v1_batch_proto_rawDescOnce sync.Once
	file_anyconn_v1_batch_proto_rawDescData = file_anyconn_v1_batch_proto_rawDesc
)

func file_anyconn_v1_batch_proto_rawDescGZIP() []byte {
	file_anyconn_v1_batch_proto_rawDescOnce.Do(func() {
		file_anyconn_v1_batch_proto_rawDescData = protoimpl.X.CompressGZIP(file_anyconn_v1_batch_proto_rawDescData)
	})
	return file_anyconn_v1_batch_proto_rawDescData
}

var file_anyconn_v1_batch_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_anyconn_v1_batch_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_anyconn_v1_batch_proto_goTypes = []interface{}{
	(Source_ParseErrorHandler_Error_Enum)(0),   // 0: anyconn.v1.Source.ParseErrorHandler.Error.Enum
	(Source_ParseErrorHandler_Handler_Enum)(0), // 1: anyconn.v1.Source.ParseErrorHandler.Handler.Enum
	(Batch_State_Enum)(0),                      // 2: anyconn.v1.Batch.State.Enum
	(*Source)(nil),                             // 3: anyconn.v1.Source
	(*CreateBatchRequest)(nil),                 // 4: anyconn.v1.CreateBatchRequest
	(*UpdateBatchRequest)(nil),                 // 5: anyconn.v1.UpdateBatchRequest
	(*DeleteBatchRequest)(nil),                 // 6: anyconn.v1.DeleteBatchRequest
	(*GetBatchRequest)(nil),                    // 7: anyconn.v1.GetBatchRequest
	(*ListBatchRequest)(nil),                   // 8: anyconn.v1.ListBatchRequest
	(*ListBatchReply)(nil),                     // 9: anyconn.v1.ListBatchReply
	(*Batch)(nil),                              // 10: anyconn.v1.Batch
	(*SetBatchAnnoResultRequest)(nil),          // 11: anyconn.v1.SetBatchAnnoResultRequest
	(*GetBatchAnnoResultReply)(nil),            // 12: anyconn.v1.GetBatchAnnoResultReply
	(*DataValidationSummary)(nil),              // 13: anyconn.v1.DataValidationSummary
	(*Source_Proprietary)(nil),                 // 14: anyconn.v1.Source.Proprietary
	(*Source_ParseErrorHandler)(nil),           // 15: anyconn.v1.Source.ParseErrorHandler
	(*Source_ParseErrorHandler_Error)(nil),     // 16: anyconn.v1.Source.ParseErrorHandler.Error
	(*Source_ParseErrorHandler_Handler)(nil),   // 17: anyconn.v1.Source.ParseErrorHandler.Handler
	(*Batch_State)(nil),                        // 18: anyconn.v1.Batch.State
	(*DataValidationSummary_Error)(nil),        // 19: anyconn.v1.DataValidationSummary.Error
	(*timestamppb.Timestamp)(nil),              // 20: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),                      // 21: google.protobuf.Empty
}
var file_anyconn_v1_batch_proto_depIdxs = []int32{
	14, // 0: anyconn.v1.Source.proprietary:type_name -> anyconn.v1.Source.Proprietary
	15, // 1: anyconn.v1.Source.error_handlers:type_name -> anyconn.v1.Source.ParseErrorHandler
	3,  // 2: anyconn.v1.CreateBatchRequest.source:type_name -> anyconn.v1.Source
	10, // 3: anyconn.v1.UpdateBatchRequest.batch:type_name -> anyconn.v1.Batch
	2,  // 4: anyconn.v1.ListBatchRequest.states:type_name -> anyconn.v1.Batch.State.Enum
	10, // 5: anyconn.v1.ListBatchReply.batches:type_name -> anyconn.v1.Batch
	3,  // 6: anyconn.v1.Batch.source:type_name -> anyconn.v1.Source
	2,  // 7: anyconn.v1.Batch.state:type_name -> anyconn.v1.Batch.State.Enum
	20, // 8: anyconn.v1.Batch.created_at:type_name -> google.protobuf.Timestamp
	13, // 9: anyconn.v1.Batch.data_summary:type_name -> anyconn.v1.DataValidationSummary
	20, // 10: anyconn.v1.GetBatchAnnoResultReply.will_ready_at:type_name -> google.protobuf.Timestamp
	19, // 11: anyconn.v1.DataValidationSummary.errors:type_name -> anyconn.v1.DataValidationSummary.Error
	0,  // 12: anyconn.v1.Source.ParseErrorHandler.error:type_name -> anyconn.v1.Source.ParseErrorHandler.Error.Enum
	1,  // 13: anyconn.v1.Source.ParseErrorHandler.handler:type_name -> anyconn.v1.Source.ParseErrorHandler.Handler.Enum
	0,  // 14: anyconn.v1.DataValidationSummary.Error.error:type_name -> anyconn.v1.Source.ParseErrorHandler.Error.Enum
	4,  // 15: anyconn.v1.Batches.CreateBatch:input_type -> anyconn.v1.CreateBatchRequest
	5,  // 16: anyconn.v1.Batches.UpdateBatch:input_type -> anyconn.v1.UpdateBatchRequest
	7,  // 17: anyconn.v1.Batches.CancelBatch:input_type -> anyconn.v1.GetBatchRequest
	6,  // 18: anyconn.v1.Batches.DeleteBatch:input_type -> anyconn.v1.DeleteBatchRequest
	7,  // 19: anyconn.v1.Batches.GetBatch:input_type -> anyconn.v1.GetBatchRequest
	8,  // 20: anyconn.v1.Batches.ListBatch:input_type -> anyconn.v1.ListBatchRequest
	11, // 21: anyconn.v1.Batches.SetAnnoResult:input_type -> anyconn.v1.SetBatchAnnoResultRequest
	7,  // 22: anyconn.v1.Batches.GetAnnoResult:input_type -> anyconn.v1.GetBatchRequest
	10, // 23: anyconn.v1.Batches.CreateBatch:output_type -> anyconn.v1.Batch
	10, // 24: anyconn.v1.Batches.UpdateBatch:output_type -> anyconn.v1.Batch
	21, // 25: anyconn.v1.Batches.CancelBatch:output_type -> google.protobuf.Empty
	21, // 26: anyconn.v1.Batches.DeleteBatch:output_type -> google.protobuf.Empty
	10, // 27: anyconn.v1.Batches.GetBatch:output_type -> anyconn.v1.Batch
	9,  // 28: anyconn.v1.Batches.ListBatch:output_type -> anyconn.v1.ListBatchReply
	21, // 29: anyconn.v1.Batches.SetAnnoResult:output_type -> google.protobuf.Empty
	12, // 30: anyconn.v1.Batches.GetAnnoResult:output_type -> anyconn.v1.GetBatchAnnoResultReply
	23, // [23:31] is the sub-list for method output_type
	15, // [15:23] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_anyconn_v1_batch_proto_init() }
func file_anyconn_v1_batch_proto_init() {
	if File_anyconn_v1_batch_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_anyconn_v1_batch_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Source); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_batch_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_batch_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_batch_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteBatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_batch_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_batch_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_batch_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBatchReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_batch_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Batch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_batch_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetBatchAnnoResultRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_batch_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBatchAnnoResultReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_batch_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataValidationSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_batch_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Source_Proprietary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_batch_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Source_ParseErrorHandler); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_batch_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Source_ParseErrorHandler_Error); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_batch_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Source_ParseErrorHandler_Handler); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_batch_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Batch_State); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_batch_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataValidationSummary_Error); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anyconn_v1_batch_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_anyconn_v1_batch_proto_goTypes,
		DependencyIndexes: file_anyconn_v1_batch_proto_depIdxs,
		EnumInfos:         file_anyconn_v1_batch_proto_enumTypes,
		MessageInfos:      file_anyconn_v1_batch_proto_msgTypes,
	}.Build()
	File_anyconn_v1_batch_proto = out.File
	file_anyconn_v1_batch_proto_rawDesc = nil
	file_anyconn_v1_batch_proto_goTypes = nil
	file_anyconn_v1_batch_proto_depIdxs = nil
}
