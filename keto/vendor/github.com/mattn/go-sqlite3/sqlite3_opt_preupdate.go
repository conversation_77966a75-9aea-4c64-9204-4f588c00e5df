// Copyright (C) 2019 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
// Copyright (C) 2018 segment.com <<EMAIL>>
//
// Use of this source code is governed by an MIT-style
// license that can be found in the LICENSE file.

// +build cgo

package sqlite3

// SQLitePreUpdateData represents all of the data available during a
// pre-update hook call.
type SQLitePreUpdateData struct {
	Conn         *SQLiteConn
	Op           int
	DatabaseName string
	TableName    string
	OldRowID     int64
	NewRowID     int64
}
