import os
import json
import shutil
import zipfile
import sys

sys.path.append('.')
from customer.common import tools

mac_file = '.DS_Store'


def run(input_dir):
    try:
        # unzip file
        unzip_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_unzip')
        result_dir = os.path.join(os.path.dirname(input_dir), 'result')
        if os.path.exists(unzip_dir):
            shutil.rmtree(unzip_dir)
        with zipfile.ZipFile(input_dir, 'r') as zip_ref:
            zip_ref.extractall(unzip_dir)

        try:
            os.mkdir(result_dir)
        except FileExistsError:
            # shutil.rmtree(result_dir)
            # os.mkdir(result_dir)
            print('folder exists')

        file_counter = 0

        for ds in os.listdir(unzip_dir):
            if ds != mac_file:
                # ds_mwjxtdg96wcrnjzvalsk
                ds_dir = os.path.join(unzip_dir, ds)
                for num_folder in os.listdir(ds_dir):
                    if num_folder != mac_file:
                        # 2309155517
                        num_folder_dir = os.path.join(ds_dir, num_folder)
                        for agu in os.listdir(num_folder_dir):
                            if agu != mac_file:
                                # agu
                                agu_dir = os.path.join(num_folder_dir, agu)
                                for segment in os.listdir(agu_dir):
                                    if segment != mac_file:
                                        # segment1
                                        segment_dir = os.path.join(agu_dir, segment)
                                        for label in os.listdir(segment_dir):
                                            if label != mac_file:
                                                # label
                                                label_dir = os.path.join(segment_dir, label)
                                                for json_file in os.listdir(label_dir):
                                                    if json_file != mac_file:
                                                        file_counter += 1
                                                        # 1690276969901000_hopper_90.json
                                                        # load json file
                                                        json_dir = os.path.join(label_dir, json_file)
                                                        json_data = json.load(open(json_dir))

                                                        # build result list
                                                        if 'lidar' in json_data:
                                                            result_list = []
                                                            for lidar in json_data['lidar']:
                                                                # build result string
                                                                result_string = (str(lidar['annotation']['data']['position']['x'])
                                                                                 + ' ' + str(lidar['annotation']['data']['position']['y'])
                                                                                 + ' ' + str(lidar['annotation']['data']['position']['z'])
                                                                                 + ' ' + str(lidar['annotation']['data']['dimension']['l'])
                                                                                 + ' ' + str(lidar['annotation']['data']['dimension']['w'])
                                                                                 + ' ' + str(lidar['annotation']['data']['dimension']['h'])
                                                                                 + ' ' + str(lidar['annotation']['data']['rotation']['z'])
                                                                                 + ' ')
                                                                # temp convert 109 to 105
                                                                if lidar['class'] == 'hopper_109':
                                                                    result_string += 'hopper_105'
                                                                else:
                                                                    result_string += lidar['class']
                                                                result_list.append(result_string)

                                                            with open(result_dir + '/' + json_file.split('.')[0] + '.txt', 'w') as f:
                                                                for result in result_list:
                                                                    f.write(f"{result}\n")

                                                            os.remove(json_dir)

        trans_result = {
            "code": 0,
            "output": unzip_dir,
            "err_msg": '成功',
            "summary": {
                '转换文件数': file_counter,
            }
        }

    except Exception as e:
        trans_result = {
            "code": 1,
            "err_msg": e,
            "err_file": json_dir
        }

    for key in trans_result:
        print(key + ': ' + str(trans_result[key]))

    print(trans_result)
    return trans_result


if __name__ == "__main__":
    args = tools.get_args()
    # args_input = '/Users/<USER>/Downloads/第一批3D挖装运导出结果/AGU-阿瓜斯卡连特斯3D挖装运_230920a30f3_验收_已通过_JSON标注结果文件_20230925_2540.zip'
    run(args.input)
