FROM golang:1.20.1-alpine3.16 AS builder

RUN apk -U --no-cache add build-base git gcc bash

WORKDIR /go/src/github.com/ory/keto

ADD go.mod go.mod
ADD go.sum go.sum

ADD proto/go.mod proto/go.mod
ADD proto/go.sum proto/go.sum

ENV CGO_ENABLED 1

RUN go mod download

ADD . .

RUN go build -buildvcs=false -tags sqlite -o /usr/bin/keto .

FROM alpine:3.17.2

RUN addgroup -S ory; \
    adduser -S ory -G ory -D  -h /home/<USER>/bin/nologin; \
    chown -R ory:ory /home/<USER>

RUN apk --no-cache --latest upgrade
RUN apk --no-cache --upgrade --latest add ca-certificates

COPY --from=builder /usr/bin/keto /usr/bin/keto

# By creating the sqlite folder as the ory user, the mounted volume will be owned by ory:ory, which
# is required for read/write of SQLite.
RUN mkdir -p /var/lib/sqlite
RUN chown ory:ory /var/lib/sqlite
VOLUME /var/lib/sqlite

# Exposing the ory home directory to simplify passing in the configuration.
VOLUME /home/<USER>

EXPOSE 4466 4467

USER ory

ENTRYPOINT ["keto"]

CMD ["serve"]
