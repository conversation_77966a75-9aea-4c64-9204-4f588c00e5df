# annotate

A Go package for applying multiple sets of annotations to a region of text.

**[Documentation on Sourcegraph](https://sourcegraph.com/github.com/sourcegraph/annotate)**

[![Build Status](https://travis-ci.org/sourcegraph/annotate.png?branch=master)](https://travis-ci.org/sourcegraph/annotate)
[![status](https://sourcegraph.com/api/repos/github.com/sourcegraph/annotate/badges/status.png)](https://sourcegraph.com/github.com/sourcegraph/annotate)
[![authors](https://sourcegraph.com/api/repos/github.com/sourcegraph/annotate/badges/authors.png)](https://sourcegraph.com/github.com/sourcegraph/annotate)
[![Total views](https://sourcegraph.com/api/repos/github.com/sourcegraph/annotate/counters/views.png)](https://sourcegraph.com/github.com/sourcegraph/annotate)
