package common

import (
	"reflect"
	"sync"
)

var (
	typesMap     = map[string]reflect.Type{}
	typesMapLock sync.RWMutex
)

func RegTypeOf(v any) string {
	t := reflect.TypeOf(v)
	name := NameOf(t)
	// if name == "" {
	// 	panic("value is of an undefined type")
	// }
	RegType(name, t)
	return name
}

func RegType(name string, t reflect.Type) {
	if name == "" {
		return
	}

	typesMapLock.RLock()
	if typesMap[name] != nil {
		typesMapLock.RUnlock()
		return
	}
	typesMapLock.RUnlock()

	typesMapLock.Lock()
	defer typesMapLock.Unlock()
	if typesMap[name] == nil {
		typesMap[name] = t
	}
}

func NameOf(t reflect.Type) string {
	prefix := ""
	if t.Kind() == reflect.Pointer {
		prefix = "*"
		t = t.Elem()
	}
	if t.Name() == "" {
		return ""
	}
	return prefix + t.PkgPath() + "." + t.Name()
}

func GetTypeNameOf(v any) string {
	t := reflect.TypeOf(v)
	return NameOf(t)
}

func GetType(name string) reflect.Type {
	if name == "" {
		return nil
	}

	typesMapLock.RLock()
	defer typesMapLock.RUnlock()
	return typesMap[name]
}

// func NewValueOfType(name string) any {
// 	t := GetType(name)
// 	if t == nil {
// 		return nil
// 	}
// 	return NewValueOf(t)
// }

// // NewValueOf returns an object of type t.
// // If t is a pointer type, an object of the element type of t is created,
// // and the pointer to the object is returned.
// func NewValueOf(t reflect.Type) reflect.Value {
// 	isPointerType := t.Kind() == reflect.Pointer
// 	if isPointerType {
// 		t = t.Elem()
// 	}
// 	v := reflect.New(t)
// 	if !isPointerType {
// 		v = v.Elem()
// 	}

// 	return v
// }

// NewValueOf returns a pointer to an object of type t, if t is not a pointer type,
// or a pointer to an object of the element type of t, if t is a pointer type.
func NewValuePointerOf(t reflect.Type) reflect.Value {
	if t.Kind() == reflect.Pointer {
		t = t.Elem()
	}
	return reflect.New(t)
}
