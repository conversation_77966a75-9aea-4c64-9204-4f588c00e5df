package preparedata

import (
	"testing"

	"annofeed/internal/biz"
	"annofeed/workflow/common"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"

	"github.com/stretchr/testify/assert"
)

func Test_validate(t *testing.T) {
	cases := []struct {
		name      string
		group     *RawdataValidationGroup
		elemIdx   int32
		fileName  string
		idxInClip int

		expected []*anno.DataValidationSummary_Error
	}{
		{
			name: "unknown_file",
			group: &RawdataValidationGroup{
				BaseNames: map[string]int32{
					"image_0.jpg": 0,
					"image_1.jpg": 0,
				},
				CurElemIdx: 1,
			},
			elemIdx:  1,
			fileName: "image_0_0.jpg",
			expected: []*anno.DataValidationSummary_Error{
				{
					Error:       anno.Source_ParseErrorHandler_Error_file_unknown,
					ElemIndex:   1,
					RawdataName: "image_0_0.jpg",
				},
			},
		},
		{
			name: "missing_files",
			group: &RawdataValidationGroup{
				BaseNames: map[string]int32{
					"image_0.jpg": 1,
					"image_1.jpg": 0,
				},
				CurElemIdx: 1,
			},
			elemIdx:  2,
			fileName: "image_1_1.jpg",
			expected: []*anno.DataValidationSummary_Error{
				{
					Error:       anno.Source_ParseErrorHandler_Error_file_missing,
					ElemIndex:   1,
					RawdataName: "image_1.jpg",
				},
				{
					Error:       anno.Source_ParseErrorHandler_Error_file_unknown,
					ElemIndex:   2,
					RawdataName: "image_1_1.jpg",
				},
			},
		},
		{
			name: "missing lidar.pcd in the 1st element of a 4D fusion clip",
			group: &RawdataValidationGroup{
				BaseNames: map[string]int32{
					"image_0.jpg": 0,
				},
				DataType: biz.DataTypeFusion4D,
			},
			elemIdx:  1,
			fileName: "image_0.jpg",
			expected: []*anno.DataValidationSummary_Error{
				{
					Error:       anno.Source_ParseErrorHandler_Error_file_missing,
					RawdataName: common.LidarPCD,
				},
			},
		},
		{
			name: "non-1st element of a 4D fusion clip does not have a lidar.pcd",
			group: &RawdataValidationGroup{
				BaseNames: map[string]int32{
					common.LidarPCD: 0,
					"image_0.jpg":   1,
				},
				DataType:   biz.DataTypeFusion4D,
				CurElemIdx: 1,
				IdxInClip:  1,
			},
			elemIdx:  2,
			fileName: "image_0.jpg",
			expected: []*anno.DataValidationSummary_Error{},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			got := c.group.validate(c.elemIdx, c.fileName, c.idxInClip)
			for i := range c.expected {
				assert.Equal(t, c.expected[i], got[i])
			}
		})
	}
}
