// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: annofeed/v1/file.proto

package annofeed

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationFilesCreateFile = "/annofeed.v1.Files/CreateFile"
const OperationFilesDeleteFile = "/annofeed.v1.Files/DeleteFile"
const OperationFilesFinishFileUpload = "/annofeed.v1.Files/FinishFileUpload"
const OperationFilesGetFile = "/annofeed.v1.Files/GetFile"
const OperationFilesGetFileUploadURLs = "/annofeed.v1.Files/GetFileUploadURLs"
const OperationFilesListFile = "/annofeed.v1.Files/ListFile"
const OperationFilesShareFile = "/annofeed.v1.Files/ShareFile"
const OperationFilesUpdateFile = "/annofeed.v1.Files/UpdateFile"

type FilesHTTPServer interface {
	// CreateFile create a file record and presigned URLs for upload
	CreateFile(context.Context, *CreateFileRequest) (*CreateFileReply, error)
	// DeleteFile delete a file record. Ongoing multipart upload will be aborted.
	DeleteFile(context.Context, *DeleteFileRequest) (*emptypb.Empty, error)
	// FinishFileUpload mark the file upload as completed
	FinishFileUpload(context.Context, *FinishFileUploadRequest) (*emptypb.Empty, error)
	GetFile(context.Context, *GetFileRequest) (*File, error)
	GetFileUploadURLs(context.Context, *GetFileUploadURLsRequest) (*GetFileUploadURLsReply, error)
	ListFile(context.Context, *ListFileRequest) (*ListFileReply, error)
	// ShareFile get a presigned URL to download the file
	ShareFile(context.Context, *ShareFileRequest) (*ShareFileReply, error)
	UpdateFile(context.Context, *UpdateFileRequest) (*File, error)
}

func RegisterFilesHTTPServer(s *http.Server, srv FilesHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/files", _Files_CreateFile0_HTTP_Handler(srv))
	r.PATCH("/v1/files/{file.uid}", _Files_UpdateFile0_HTTP_Handler(srv))
	r.PUT("/v1/files/{uid}/finish-upload", _Files_FinishFileUpload0_HTTP_Handler(srv))
	r.GET("/v1/files/{uid}/upload-urls", _Files_GetFileUploadURLs0_HTTP_Handler(srv))
	r.DELETE("/v1/files/{uid}", _Files_DeleteFile0_HTTP_Handler(srv))
	r.GET("/v1/files/{uid}", _Files_GetFile0_HTTP_Handler(srv))
	r.GET("/v1/files", _Files_ListFile0_HTTP_Handler(srv))
	r.PUT("/v1/files/{uid}/share", _Files_ShareFile0_HTTP_Handler(srv))
}

func _Files_CreateFile0_HTTP_Handler(srv FilesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateFileRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFilesCreateFile)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateFile(ctx, req.(*CreateFileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateFileReply)
		return ctx.Result(200, reply)
	}
}

func _Files_UpdateFile0_HTTP_Handler(srv FilesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateFileRequest
		if err := ctx.Bind(&in.File); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFilesUpdateFile)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateFile(ctx, req.(*UpdateFileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*File)
		return ctx.Result(200, reply)
	}
}

func _Files_FinishFileUpload0_HTTP_Handler(srv FilesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FinishFileUploadRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFilesFinishFileUpload)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FinishFileUpload(ctx, req.(*FinishFileUploadRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Files_GetFileUploadURLs0_HTTP_Handler(srv FilesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetFileUploadURLsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFilesGetFileUploadURLs)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetFileUploadURLs(ctx, req.(*GetFileUploadURLsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetFileUploadURLsReply)
		return ctx.Result(200, reply)
	}
}

func _Files_DeleteFile0_HTTP_Handler(srv FilesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteFileRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFilesDeleteFile)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteFile(ctx, req.(*DeleteFileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Files_GetFile0_HTTP_Handler(srv FilesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetFileRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFilesGetFile)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetFile(ctx, req.(*GetFileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*File)
		return ctx.Result(200, reply)
	}
}

func _Files_ListFile0_HTTP_Handler(srv FilesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListFileRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFilesListFile)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListFile(ctx, req.(*ListFileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListFileReply)
		return ctx.Result(200, reply)
	}
}

func _Files_ShareFile0_HTTP_Handler(srv FilesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ShareFileRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFilesShareFile)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ShareFile(ctx, req.(*ShareFileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ShareFileReply)
		return ctx.Result(200, reply)
	}
}

type FilesHTTPClient interface {
	CreateFile(ctx context.Context, req *CreateFileRequest, opts ...http.CallOption) (rsp *CreateFileReply, err error)
	DeleteFile(ctx context.Context, req *DeleteFileRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	FinishFileUpload(ctx context.Context, req *FinishFileUploadRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetFile(ctx context.Context, req *GetFileRequest, opts ...http.CallOption) (rsp *File, err error)
	GetFileUploadURLs(ctx context.Context, req *GetFileUploadURLsRequest, opts ...http.CallOption) (rsp *GetFileUploadURLsReply, err error)
	ListFile(ctx context.Context, req *ListFileRequest, opts ...http.CallOption) (rsp *ListFileReply, err error)
	ShareFile(ctx context.Context, req *ShareFileRequest, opts ...http.CallOption) (rsp *ShareFileReply, err error)
	UpdateFile(ctx context.Context, req *UpdateFileRequest, opts ...http.CallOption) (rsp *File, err error)
}

type FilesHTTPClientImpl struct {
	cc *http.Client
}

func NewFilesHTTPClient(client *http.Client) FilesHTTPClient {
	return &FilesHTTPClientImpl{client}
}

func (c *FilesHTTPClientImpl) CreateFile(ctx context.Context, in *CreateFileRequest, opts ...http.CallOption) (*CreateFileReply, error) {
	var out CreateFileReply
	pattern := "/v1/files"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFilesCreateFile))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FilesHTTPClientImpl) DeleteFile(ctx context.Context, in *DeleteFileRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/files/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationFilesDeleteFile))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FilesHTTPClientImpl) FinishFileUpload(ctx context.Context, in *FinishFileUploadRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/files/{uid}/finish-upload"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFilesFinishFileUpload))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FilesHTTPClientImpl) GetFile(ctx context.Context, in *GetFileRequest, opts ...http.CallOption) (*File, error) {
	var out File
	pattern := "/v1/files/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationFilesGetFile))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FilesHTTPClientImpl) GetFileUploadURLs(ctx context.Context, in *GetFileUploadURLsRequest, opts ...http.CallOption) (*GetFileUploadURLsReply, error) {
	var out GetFileUploadURLsReply
	pattern := "/v1/files/{uid}/upload-urls"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationFilesGetFileUploadURLs))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FilesHTTPClientImpl) ListFile(ctx context.Context, in *ListFileRequest, opts ...http.CallOption) (*ListFileReply, error) {
	var out ListFileReply
	pattern := "/v1/files"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationFilesListFile))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FilesHTTPClientImpl) ShareFile(ctx context.Context, in *ShareFileRequest, opts ...http.CallOption) (*ShareFileReply, error) {
	var out ShareFileReply
	pattern := "/v1/files/{uid}/share"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFilesShareFile))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FilesHTTPClientImpl) UpdateFile(ctx context.Context, in *UpdateFileRequest, opts ...http.CallOption) (*File, error) {
	var out File
	pattern := "/v1/files/{file.uid}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFilesUpdateFile))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in.File, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
