package server

import (
	"fmt"
	stdhttp "net/http"
	"path"
	"regexp"
	"strings"
	"time"

	"annofeed/api/client"
	"annofeed/internal/biz"
	"annofeed/internal/conf"
	"annofeed/internal/signer"
	"annofeed/workflow/common"

	"gitlab.rp.konvery.work/platform/apis/middleware"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/ktime"
	"gitlab.rp.konvery.work/platform/pkg/s3"
	"gitlab.rp.konvery.work/platform/pkg/upload"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/gosimple/slug"
	"github.com/samber/lo"
)

func setupRouter(svr *http.Server, rdbiz *biz.RawdataBiz, filebz *biz.FilesBiz, fs *conf.FileServer, s3cli *s3.Client, logger log.Logger) {
	filters := []http.FilterFunc{
		middleware.HTTPLogFilter(logger),
		middleware.HTTPAuthFilter(logger),
		middleware.HTTPLoadUserFilter(nil),
	}
	route := svr.Route("/v1", filters...)
	route.POST("/upload/", genUploadHanlder(false, fs, filebz, logger))
	route.POST("/avatar/", genUploadHanlder(true, fs, filebz, logger))
	route.GET(biz.FileURLPathPrefix+"{share_id}", genFileRouteHanlder(fs, filebz, s3cli, logger))

	if fs.PublicRawdata {
		filters = []http.FilterFunc{middleware.HTTPLogFilter(logger)}
	}
	svr.Route("/v1").GET(biz.RawdataURLPathPrefix+"{uid}", genRawdataRouteHanlder(rdbiz, false, logger), filters...)
	svr.Route("/v1").GET(biz.EmbeddingURLPathPrefix+"{uid}", genRawdataRouteHanlder(rdbiz, true, logger), filters...)

	handler := stdhttp.StripPrefix("/v1", stdhttp.FileServer(stdhttp.Dir(fs.BaseDir)))
	handler = middleware.HTTPLogFilter(logger)(handler)
	svr.HandlePrefix("/v1/res/", handler)
	svr.HandlePrefix("/v1/avatar/", handler)
	svr.HandlePrefix("/v1/data/", handler)
	svr.HandlePrefix("/v1/upload/", handler)
}

func genUploadHanlder(isAvatar bool, fs *conf.FileServer, filebz *biz.FilesBiz, logger log.Logger) http.HandlerFunc {
	// classExp := regexp.MustCompile(`^[a-z0-9-_]{2,32}$`)
	// nameExp := regexp.MustCompile(`^[a-z0-9-_]{2,32}\.(jpg|png|jpeg)$`)

	avatarURLBase := fs.AvatarUrl
	if fs.Storage == upload.StorageTypeLocalFS {
		avatarURLBase = fs.SvcUrl
	}

	h := func(ctx http.Context) (err error) {
		req := ctx.Request()
		q := req.URL.Query()
		name := q.Get("name")
		if name == "" {
			return errors.NewErrEmptyField(errors.WithMessage("name"))
		}
		ext := strings.ToLower(path.Ext(name))
		slugName := slug.Make(name[:len(name)-len(ext)]) + ext

		key, uri, url := "", "", ""
		if isAvatar {
			key, uri, err = uploadAvatar(ctx, name, slugName)
			if err == nil {
				if fs.Cloudfront != nil && fs.Cloudfront.Enabled {
					res, err := signer.SignGetURL(ctx, uri, 0)
					if err != nil {
						return fmt.Errorf("failed to assemble url: %w", err)
					}
					url = res.URL
				} else {
					url = avatarURLBase + "/" + key
				}
			}
		} else {
			key, uri, err = uploadFile(ctx, filebz, name, slugName)
		}

		if err != nil {
			log.NewHelper(logger).WithContext(ctx).Infow("msg", "failed to upload a file", "path", key, "error", err)
			return err
		}

		log.NewHelper(logger).WithContext(ctx).Infow("msg", "upload a file", "size", req.ContentLength, "uri", uri)
		return ctx.JSON(200, map[string]any{"uri": uri, "url": url})
	}
	return middleware.HTTPLogErrorHandler(logger, h)
}

var (
	classExp = regexp.MustCompile(`^[a-z0-9_-]{3,32}$`)
	nameExp  = regexp.MustCompile(`^[a-z0-9_-]{3,32}\.(jpg|png|webp|jpeg)$`)
)

func uploadAvatar(ctx http.Context, name, slugName string) (key, uri string, err error) {
	req := ctx.Request()
	if req.ContentLength > 50*1024 {
		return "", "", errors.NewErrBigFile(errors.WithMessage("too big file"))
	}
	if !lo.Contains([]string{".png", ".jpg", ".jpeg", ".webp", ".avif", ".gif", ".svg"}, path.Ext(slugName)) {
		return "", "", errors.NewErrInvalidFileType(errors.WithMessage("invalid file format"))
	}

	uid := kid.StringID(kid.NewID())
	date := ktime.Today(ktime.DateStyleShortPath)

	q := req.URL.Query()
	typ := q.Get("type")
	switch typ {
	case "prj", "user":
		key = path.Join("avatar", typ, date, uid, slugName)
	case "res":
		class := q.Get("class")
		if !classExp.MatchString(class) {
			return "", "", errors.NewErrInvalidField(errors.WithFields("class"))
		}
		name = strings.ToLower(name)
		if !nameExp.MatchString(name) {
			return "", "", errors.NewErrInvalidField(errors.WithFields("name"))
		}
		key = path.Join("res", class, name)
	default:
		return "", "", errors.NewErrInvalidFileType(errors.WithFields("type"))
	}

	opts := &upload.Options{Public: true}
	resp, err := upload.Upload(ctx, key, req.Body, opts)
	if err != nil {
		return "", "", errors.NewErrServerError(errors.WithMessage("failed to upload avatar"))
	}
	if !strings.HasPrefix(resp.MIME, "image/") {
		// TODO: delete the file from storage
		return "", "", errors.NewErrInvalidFileType(errors.WithMessage("invalid file format"))
	}
	return key, resp.URI, nil
}

func uploadFile(ctx http.Context, filebz *biz.FilesBiz, name, slugName string) (key, uri string, err error) {
	req := ctx.Request()
	q := req.URL.Query()
	orgUid := q.Get("org_uid")
	if orgUid == "" {
		orgUid = biz.UserFromCtx(ctx).GetOrgUid()
	}
	if orgUid == "" {
		return "", "", errors.NewErrEmptyField(errors.WithFields("org_uid"))
	}
	if !client.IsAllowed(ctx, "", biz.PermCreate, biz.PermClsFile, client.GroupScope(orgUid)) {
		return "", "", errors.NewErrForbidden()
	}

	uid := kid.StringID(kid.NewID())
	key = biz.MakeUploadFileKey(uid, slugName)

	resp, err := upload.Upload(ctx, key, req.Body, nil)
	if err != nil {
		return "", "", fmt.Errorf("failed to upload file: %w", err)
	}

	uri = resp.URI
	f := &biz.File{
		ID:         kid.ParseID(uid),
		Name:       name,
		URI:        uri,
		MIME:       resp.MIME,
		Size:       resp.Size,
		Sha256:     resp.Sha256,
		State:      biz.FileStateUploaded,
		OrgUid:     orgUid,
		CreatorUid: biz.UserFromCtx(ctx).GetUid(),
	}
	_, err = filebz.Create(ctx, f)
	if err != nil {
		return "", "", err
	}
	return
}

// redirect the web client to the actual location of a rawdata file
func genRawdataRouteHanlder(rdbiz *biz.RawdataBiz, isEmbedding bool, logger log.Logger) http.HandlerFunc {
	h := func(ctx http.Context) (err error) {
		defer func() {
			if err != nil {
				if errT, ok := errors.AsT[*errors.Error](err); !ok {
					err = errors.NewErrServerError().WithCause(err)
				} else {
					err = errT
				}
			}
		}()

		uid := ctx.Vars().Get("uid")
		rawdata, err := rdbiz.GetByUid(ctx, uid)
		if err != nil {
			return err
		}

		if rawdata.Format == common.RawdataFormatFilelist {
			filelist, err := rdbiz.FilelistReply(ctx, rawdata)
			if err != nil {
				return err
			}
			return ctx.JSON(200, filelist)
		}

		url := ""
		if isEmbedding {
			url, err = rdbiz.EmbeddingURL(ctx, rawdata)
		} else {
			url, err = rdbiz.DataURL(ctx, rawdata)
		}
		if err != nil {
			return err
		}

		fmt.Println("---> location 302")
		log.NewHelper(logger).WithContext(ctx).Debugw("msg", "request is redirected", "url", url)
		ctx.Response().Header().Add("Location", url)
		return ctx.String(stdhttp.StatusFound, "")
	}
	return middleware.HTTPLogErrorHandler(logger, h)
}

// generate a redirection to the actual location of a file
func genFileRouteHanlder(fs *conf.FileServer, filebiz *biz.FilesBiz, s3cli *s3.Client,
	logger log.Logger) http.HandlerFunc {
	h := func(ctx http.Context) (err error) {
		defer func() {
			if err != nil {
				var e *errors.Error
				if !errors.As(err, &e) {
					err = errors.NewErrServerError().WithCause(err)
				}
			}
		}()

		sid := ctx.Vars().Get("share_id")
		data, err := filebiz.GetByShareID(ctx, sid)
		if err != nil {
			return err
		}
		if !client.IsAllowed(ctx, "", biz.PermGet, biz.PermClsFile, data.GetUid()) {
			return errors.NewErrForbidden()
		}

		uri, err := upload.ParseURI(data.URI)
		if err != nil {
			return fmt.Errorf("failed to parse file URI %v: %w", data.URI, err)
		}
		url := ""
		if uri.Scheme == upload.StorageTypeS3 {
			res, err := signer.SignGetURL(ctx, data.URI, 8*time.Hour)
			if err != nil {
				return err
			}
			url = res.URL
		} else {
			url = fs.SvcUrl + uri.Path
		}

		log.NewHelper(logger).WithContext(ctx).Debugw("msg", "request is redirected", "url", url)
		ctx.Response().Header().Add("Location", url)
		return ctx.String(stdhttp.StatusFound, "")
	}
	return middleware.HTTPLogErrorHandler(logger, h)
}
