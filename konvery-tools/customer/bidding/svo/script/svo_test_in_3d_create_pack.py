from PIL import Image
import json
import os
import shutil
import sys
from pathlib import Path

import numpy as np
import pandas as pd
import yaml

sys.path.append('.')

from customer.common import tools
from scipy.spatial.transform import Rotation as R

camera_name_mapping = [
    "front_camera",
    "left_camera",
    "back_camera",
    "right_camera"
]


def load_camera_config(config_path):
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)


def load_ego_state(csv_path):
    return pd.read_csv(csv_path)


def create_target_structure(base_dir, collection_name, element_name):
    data_dir = Path(base_dir) / f"data/{collection_name}/{element_name}"
    data_dir.mkdir(parents=True, exist_ok=True)
    return data_dir


def convert_camera_files(source_dir, target_dir, frame_name, camera_config):
    camera_mapping = {
        "Ego_001_FrontCamera": "front_camera",
        "Ego_001_LeftCamera": "left_camera",
        "Ego_001_RightCamera": "right_camera",
        "Ego_001_BackCamera": "back_camera",
        "Ego_001_FrontLidar": "lidar"
    }

    for src_subdir, camera_name in camera_mapping.items():
        src_path = Path(source_dir) / src_subdir
        if not src_path.exists():
            continue

        matched_files = [file for file in src_path.iterdir() if file.stem == frame_name]
        if matched_files:
            file_extension = matched_files[0].suffix  # 保持原始后缀
            dst_file = target_dir / f"{camera_name}{file_extension}"
            shutil.copy(matched_files[0], dst_file)
        else:
            # print(f"No file found for frame '{frame_name}' in directory '{src_path}'. Creating placeholder image.")
            try:
                width, height = get_camera_image_size(camera_config, camera_name)
                placeholder_path = target_dir / f"{camera_name}.jpg"
                create_placeholder_image(placeholder_path, width, height)
            except Exception as e:
                print(f"Error creating placeholder image for {src_subdir}: {e}")


def get_camera_image_size(camera_config, ego_camera_name):
    for camera_name, camera_info in camera_config.items():
        # Map internal camera name to external camera name
        if camera_name == ego_camera_name:
            intrinsic_params = camera_info['camera_intrinsics']
            if not intrinsic_params:
                raise ValueError(f"Camera '{camera_name}' does not have 'camera_intrinsics' key.")

            image_size_x = intrinsic_params['image_size_x']
            image_size_y = intrinsic_params['image_size_y']
            if not image_size_x or not image_size_y:
                raise ValueError(f"Camera '{camera_name}' does not have valid 'image_size_x' or 'image_size_y' "
                                 f"parameters.")
            return image_size_x, image_size_y


def create_placeholder_image(target_path, width, height, color=(128, 128, 128)):
    """生成一个占位图片"""
    placeholder_image = Image.new('RGB', (width, height), color=color)
    placeholder_image.save(target_path)
    # print(f"Placeholder image created at: {target_path}")


def euler_to_quaternion(roll, pitch, yaw):
    return R.from_euler('xyz', [roll, pitch, yaw], degrees=False).as_quat()


def convert_pose_to_matrix(x, y, z, qx, qy, qz, qw):
    """ Converts pose (x, y, z, qx, qy, qz, qw) to a 4x4 transformation matrix """
    T = np.eye(4)
    T[:3, :3] = R.from_quat([qx, qy, qz, qw]).as_matrix()
    T[:3, 3] = [x, y, z]
    return T


def create_params_json(target_dir, camera_config, ego_state, frame_name):
    if ego_state is None:
        return
    formatted_timestamps = ego_state['Timestamp'].apply(lambda x: f"{x:.2f}")
    pose_row = ego_state[formatted_timestamps == frame_name]
    if pose_row.empty:
        # print(f"No matching row found for frame_name: {frame_name}")
        return

    row = pose_row.iloc[0].to_list()
    x, y, z = row[1], row[2], row[3]
    roll, pitch, yaw = row[4], row[5], row[6]
    qx, qy, qz, qw = euler_to_quaternion(roll, pitch, yaw)

    pose_matrix = convert_pose_to_matrix(x, y, z, qx, qy, qz, qw)
    params = {
        "meta": {"version": "v2"},
        "lidar": {
            "viewpoint": [0, 0, 0, 0, 0, 0, 1],
            "pose": pose_matrix.flatten().tolist(),
            "transform": [],
            "timestamp": float(pose_row.iloc[0]['Timestamp'])
        },
        "cameras": {}
    }

    lidar_to_imu_extrinsics = camera_config['lidar_to_imu_extrinsics']
    # 获取 LiDAR 到 IMU 的变换矩阵
    lidar_to_imu_matrix = np.eye(4)
    lidar_to_imu_matrix[:3, :3] = np.array([
        [lidar_to_imu_extrinsics['rotation_matrix']['r11'], lidar_to_imu_extrinsics['rotation_matrix']['r12'], lidar_to_imu_extrinsics['rotation_matrix']['r13']],
        [lidar_to_imu_extrinsics['rotation_matrix']['r21'], lidar_to_imu_extrinsics['rotation_matrix']['r22'], lidar_to_imu_extrinsics['rotation_matrix']['r23']],
        [lidar_to_imu_extrinsics['rotation_matrix']['r31'], lidar_to_imu_extrinsics['rotation_matrix']['r32'], lidar_to_imu_extrinsics['rotation_matrix']['r33']]
    ])
    lidar_to_imu_matrix[:3, 3] = [
        lidar_to_imu_extrinsics['translation_vector']['tx'],
        lidar_to_imu_extrinsics['translation_vector']['ty'],
        lidar_to_imu_extrinsics['translation_vector']['tz']
    ]

    # Add camera parameters from YAML
    for camera_name, camera_info in camera_config.items():
        # Map internal camera name to external camera name
        if camera_name not in camera_name_mapping:
            continue

        intrinsic_params = camera_info['camera_intrinsics']
        extrinsic_params = camera_info['camera_to_imu_extrinsics']

        # Extract intrinsic parameters
        intrinsic_matrix = [
            intrinsic_params['focal_length_x'], 0, intrinsic_params['principal_point_x'],
            0, intrinsic_params['focal_length_y'], intrinsic_params['principal_point_y'],
            0, 0, 1
        ]

        # Extract distortion coefficients
        distortion_coeffs = [
            0,  # Camera model (0: pinhole)
            intrinsic_params['distortion_coefficients']['k1'],
            intrinsic_params['distortion_coefficients']['k2'],
            intrinsic_params['distortion_coefficients']['p1'],
            intrinsic_params['distortion_coefficients']['p2'],
            intrinsic_params['distortion_coefficients']['k3']
        ]

        # Extract extrinsic parameters (rotation matrix and translation vector)
        camera_to_imu_matrix = np.eye(4)
        camera_to_imu_matrix[:3, :3] = np.array([
            [extrinsic_params['rotation_matrix']['r11'], extrinsic_params['rotation_matrix']['r12'],
             extrinsic_params['rotation_matrix']['r13']],
            [extrinsic_params['rotation_matrix']['r21'], extrinsic_params['rotation_matrix']['r22'],
             extrinsic_params['rotation_matrix']['r23']],
            [extrinsic_params['rotation_matrix']['r31'], extrinsic_params['rotation_matrix']['r32'],
             extrinsic_params['rotation_matrix']['r33']]
        ])
        camera_to_imu_matrix[:3, 3] = [
            extrinsic_params['translation_vector']['tx'],
            extrinsic_params['translation_vector']['ty'],
            extrinsic_params['translation_vector']['tz']
        ]

        # 计算 LiDAR 到相机的变换：T(lidar->camera) = T(camera->IMU)^-1 @ T(lidar->IMU)
        camera_to_lidar_matrix = np.linalg.inv(camera_to_imu_matrix) @ lidar_to_imu_matrix

        # Add camera parameters to the params dictionary
        params['cameras'][camera_name] = {
            "extrinsic": camera_to_lidar_matrix.flatten().tolist(),
            "intrinsic": intrinsic_matrix,
            "distortion": distortion_coeffs
        }
    with open(target_dir / "params.json", 'w') as f:
        json.dump(params, f, indent=4)


def run(input_dir, output_dir):
    input_dir = os.path.join(input_dir, 'trial_annotation_data')
    if not os.path.exists(input_dir):
        print('Input dir does not exist:', input_dir)
        return
    camera_config_path = os.path.join(input_dir, 'multi_camera_config.yaml')
    if not os.path.exists(camera_config_path):
        print('camera_config_path does not exist: ', camera_config_path)
        return
    camera_config = load_camera_config(camera_config_path)

    for collection in os.listdir(input_dir):
        if collection == ".DS_Store" or collection == "multi_camera_config.yaml":
            continue
        collection_path = os.path.join(input_dir, collection)
        if not os.path.isdir(collection_path):
            # print('collection path does not exist: ', collection_path)
            return
        ego_state_path = os.path.join(collection_path, "ego_state.csv")
        if not os.path.exists(ego_state_path):
            # print('ego_state path does not exist: ', ego_state_path)
            return
        ego_state = load_ego_state(ego_state_path)

        for elem_dir in os.listdir(collection_path):
            if elem_dir == ".DS_Store" or elem_dir == "ego_state.csv":
                continue
            elem_dir_path = os.path.join(collection_path, elem_dir)
            if not os.path.exists(elem_dir_path):
                print('elem_dir_path does not exist: ', elem_dir_path)
                return
            for elem_file in os.listdir(elem_dir_path):
                if elem_file == ".DS_Store":
                    continue
                frame_name = Path(elem_file).stem
                lidar_path = 'Ego_001_FrontLidar'
                pcd_file = Path(collection_path) / f"{lidar_path}" / f"{frame_name}.pcd"
                if not os.path.isfile(pcd_file):
                    # print('pcd_path does not exist: ', pcd_file)
                    continue
                data_dir = create_target_structure(output_dir, collection, frame_name)
                convert_camera_files(collection_path, data_dir, frame_name, camera_config)
                create_params_json(data_dir, camera_config, ego_state, frame_name)


'''
源数据格式如下
├── README.md
└── trial_annotation_data
    ├── ego_accept_merge
    │ ├── Ego_001_BackCamera
    │ │ └── 1249.65.jpg
    │ ├── Ego_001_FrontCamera
    │ │ └── 1249.65.jpg
    │ ├── Ego_001_FrontLidar
    │ │ └── 1249.65.pcd
    │ ├── Ego_001_LeftCamera
    │ │ └── 1249.65.jpg
    │ ├── Ego_001_RightCamera
    │ │ └── 1249.65.jpg
    │ └── ego_state.csv
    └── multi_camera_config.yaml
'''
if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
