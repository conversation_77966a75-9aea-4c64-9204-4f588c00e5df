package client

import (
	"context"

	annov1 "gitlab.rp.konvery.work/platform/apis/anno/v1"

	"github.com/go-kratos/kratos/v2/errors"
	"google.golang.org/grpc"
)

type (
	Job     = annov1.Job
	JobAnno = annov1.JobAnno
	Lot     = annov1.Lot
	Order   = annov1.Order

	Anno interface {
		annov1.JobsClient
		annov1.LotsClient
		annov1.OrdersClient
	}
)

func NewAnno(conn *grpc.ClientConn) Anno {
	return struct {
		annov1.JobsClient
		annov1.LotsClient
		annov1.OrdersClient
	}{
		JobsClient:   annov1.NewJobsClient(conn),
		LotsClient:   annov1.NewLotsClient(conn),
		OrdersClient: annov1.NewOrdersClient(conn),
	}
}

var annosvc Anno

func InitAnno(service *Service) {
	annosvc = NewAnno(newGrpcConn(service))
}

func SetAnnoSvc(anno Anno) {
	annosvc = anno
}

func CreateLot(ctx context.Context, req *annov1.CreateLotRequest) (*Lot, error) {
	lot, err := annosvc.CreateLot(ctx, req)
	if err != nil {
		return nil, errors.FromError(err)
	}
	return lot, nil
}

func CloneLot(ctx context.Context, req *annov1.CloneLotRequest) (*Lot, error) {
	lot, err := annosvc.CloneLot(ctx, req)
	if err != nil {
		return nil, errors.FromError(err)
	}
	return lot, nil
}

func GetLot(ctx context.Context, uid string) (*Lot, error) {
	lot, err := annosvc.GetLot(ctx, &annov1.GetLotRequest{Uid: uid})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return lot, nil
}

func GetJob(ctx context.Context, uid string, expand bool) (*Job, error) {
	reply, err := annosvc.GetJob(ctx, &annov1.GetJobRequest{Uid: uid, Expand: expand})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return reply, nil
}

func ListJob(ctx context.Context, req *annov1.ListJobRequest) (*annov1.ListJobReply, error) {
	rsp, err := annosvc.ListJob(ctx, req)
	if err != nil {
		return nil, errors.FromError(err)
	}
	return rsp, nil
}

func CreateOrder(ctx context.Context, req *annov1.CreateOrderRequest) (*Order, error) {
	order, err := annosvc.CreateOrder(ctx, req)
	if err != nil {
		return nil, errors.FromError(err)
	}
	return order, nil
}

func GetOrder(ctx context.Context, uid string) (*Order, error) {
	order, err := annosvc.GetOrder(ctx, &annov1.GetOrderRequest{Uid: uid})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return order, nil
}

func SetOrderAnnoResult(ctx context.Context, uid, url string) error {
	_, err := annosvc.SetAnnoResult(ctx, &annov1.SetOrderAnnoResultRequest{Uid: uid, Url: url})
	if err != nil {
		return errors.FromError(err)
	}
	return nil
}

func JobAnnoFromJob(j *Job) *JobAnno {
	return &JobAnno{
		JobIndex:          j.IdxInLot,
		ElementAnnos:      j.Annotations,
		NeedInterpolation: j.NeedInterpolation,
		InsCnt:            j.InsCnt,
	}
}

func UpdateOrderState(ctx context.Context, uid string, state annov1.Order_State_Enum, dataSize int32, errMsg string) (*Order, error) {
	order, err := annosvc.UpdateOrder(ctx, &annov1.UpdateOrderRequest{
		Order: &annov1.Order{
			Uid:   uid,
			State: state,
			Size:  dataSize,
			Error: errMsg,
		},
		Fields: []string{"state", "size", "error"},
	})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return order, nil
}

// GetVisibleLots returns the uids of visible lots by executorUid. This API is used in `annostat` service.
func GetVisibleLots(ctx context.Context, executorUid string, lotStates []annov1.Lot_State_Enum, countOnly bool, claimable bool) (*annov1.GetVisibleLotsReply, error) {
	reply, err := annosvc.GetVisibleLots(ctx, &annov1.GetVisibleLotsRequest{
		ExecutorUid: executorUid,
		States:      lotStates,
		CountOnly:   countOnly,
		Claimable:   claimable,
	})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return reply, nil
}

// SetLotAnnoResult sets the annotation result in lot. This API is used in `annout` service.
func SetLotAnnoResult(ctx context.Context, uid, url string) error {
	_, err := annosvc.SetLotAnnoResult(ctx, &annov1.SetLotAnnoResultRequest{Uid: uid, Url: url})
	if err != nil {
		return errors.FromError(err)
	}
	return nil
}

func AllowDownloadAnnos(ctx context.Context, uid string, allow bool) error {
	_, err := annosvc.AllowDownloadAnnos(ctx, &annov1.AllowDownloadAnnosRequest{Uid: uid, Allow: allow})
	if err != nil {
		return errors.FromError(err)
	}
	return nil
}
