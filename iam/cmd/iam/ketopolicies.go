package main

import (
	"context"
	"fmt"
	"time"

	"iam/internal/biz"
	"iam/pkg/keto"

	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/kid"
)

// CreateAllPolicies recreates keto tuples for all users/teams.
func (o *ketoMgr) CreateAllPolicies() error {
	err := o.CreateTuplesForOrgs()
	if err != nil {
		return err
	}
	return o.CreateTuplesForUsers()
}

func (o *ketoMgr) CreateTuplesForOrgs() error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	count, err := o.teamsRepo.Count(ctx, &biz.TeamListFilter{ParentID: new(int64)})
	cancel()
	if err != nil {
		return fmt.Errorf("failed to query orgnization count: %w", err)
	}
	o.log.Infof("CreateTuplesForOrgs: find %v orgnizations", count)

	pager := repo.Pager{Pagesz: 1000}
	for n := 0; ; {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		groups, token, err := o.teamsRepo.List(ctx, &biz.TeamListFilter{ParentID: new(int64)}, pager)
		cancel()
		if err != nil {
			return fmt.Errorf("failed to query orgnization list: %w", err)
		}

		for _, t := range groups {
			ctx, cancel = context.WithTimeout(context.Background(), 10*time.Second)
			err = o.CreateTuplesForOrg(ctx, t)
			cancel()
			if err != nil {
				return fmt.Errorf("failed to add orgnization tuples: %w", err)
			}
		}

		n += len(groups)
		pager.PageToken = token
		o.log.Infof("CreateTuplesForOrgs: finished %v orgs", n)
		if token == "" {
			break
		}
	}
	o.log.Infof("CreateTuplesForOrgs: finished")
	return nil
}

func (o *ketoMgr) CreateTuplesForOrg(ctx context.Context, team *biz.Team) error {
	return keto.DefaultAccessMgr().CreateGroup(ctx, team.GetUid(), "", "")
}

func (o *ketoMgr) CreateTuplesForUsers() error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	count, err := o.usersRepo.Count(ctx, &biz.UserListFilter{})
	cancel()
	if err != nil {
		return fmt.Errorf("failed to query user count: %w", err)
	}
	o.log.Infof("CreateTupleForUsers: find %v users", count)

	pager := repo.Pager{Pagesz: 1000}
	for n := 0; ; {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		users, token, err := o.usersRepo.List(ctx, nil, pager)
		cancel()
		if err != nil {
			return fmt.Errorf("failed to query user list: %w", err)
		}

		for _, u := range users {
			ctx, cancel = context.WithTimeout(context.Background(), 10*time.Second)
			err = o.CreateTuplesForUser(ctx, u)
			cancel()
			if err != nil {
				return fmt.Errorf("failed to add user tuples: %w", err)
			}
		}

		n += len(users)
		pager.PageToken = token
		o.log.Infof("CreateTupleForUsers: finished %v users", n)
		if token == "" {
			break
		}
	}
	o.log.Infof("CreateTupleForUsers: finished")
	return nil
}

func (o *ketoMgr) CreateTuplesForUser(ctx context.Context, user *biz.User) error {
	if biz.IsSysRole(user.Role) {
		err := keto.DefaultAccessMgr().AddSysGroupMembers(ctx, user.Role, user.GetUid())
		if err != nil {
			return err
		}
	} else if user.OrgID > 0 {
		err := keto.DefaultAccessMgr().AddGroupMembers(ctx, kid.StringID(user.OrgID), user.Role, true, []string{user.GetUid()})
		if err != nil {
			return err
		}
	}
	return keto.DefaultAccessMgr().CreateUsers(ctx, user.GetUid())
}
