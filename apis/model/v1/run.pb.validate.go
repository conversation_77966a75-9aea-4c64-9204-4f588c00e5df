// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: model/v1/run.proto

package model

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateRunRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateRunRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRunRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRunRequestMultiError, or nil if none found.
func (m *CreateRunRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRunRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetDataset()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRunRequestValidationError{
					field:  "Dataset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRunRequestValidationError{
					field:  "Dataset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDataset()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRunRequestValidationError{
				field:  "Dataset",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Target

	// no validation rules for MaxCredits

	// no validation rules for BaseModel

	if all {
		switch v := interface{}(m.GetPipeline()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRunRequestValidationError{
					field:  "Pipeline",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRunRequestValidationError{
					field:  "Pipeline",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPipeline()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRunRequestValidationError{
				field:  "Pipeline",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ModelUid

	// no validation rules for Artifacts

	if len(errors) > 0 {
		return CreateRunRequestMultiError(errors)
	}

	return nil
}

// CreateRunRequestMultiError is an error wrapping multiple validation errors
// returned by CreateRunRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateRunRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRunRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRunRequestMultiError) AllErrors() []error { return m }

// CreateRunRequestValidationError is the validation error returned by
// CreateRunRequest.Validate if the designated constraints aren't met.
type CreateRunRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRunRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRunRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRunRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRunRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRunRequestValidationError) ErrorName() string { return "CreateRunRequestValidationError" }

// Error satisfies the builtin error interface
func (e CreateRunRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRunRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRunRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRunRequestValidationError{}

// Validate checks the field values on UpdateRunRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateRunRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateRunRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateRunRequestMultiError, or nil if none found.
func (m *UpdateRunRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateRunRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRunRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRunRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRunRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateRunRequestMultiError(errors)
	}

	return nil
}

// UpdateRunRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateRunRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateRunRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateRunRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateRunRequestMultiError) AllErrors() []error { return m }

// UpdateRunRequestValidationError is the validation error returned by
// UpdateRunRequest.Validate if the designated constraints aren't met.
type UpdateRunRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateRunRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateRunRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateRunRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateRunRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateRunRequestValidationError) ErrorName() string { return "UpdateRunRequestValidationError" }

// Error satisfies the builtin error interface
func (e UpdateRunRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateRunRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateRunRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateRunRequestValidationError{}

// Validate checks the field values on Run with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Run) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Run with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in RunMultiError, or nil if none found.
func (m *Run) ValidateAll() error {
	return m.validate(true)
}

func (m *Run) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_Run_Uid_Pattern.MatchString(m.GetUid()) {
		err := RunValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Name

	// no validation rules for Type

	if !_Run_OperatorUid_Pattern.MatchString(m.GetOperatorUid()) {
		err := RunValidationError{
			field:  "OperatorUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|^$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDataset()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RunValidationError{
					field:  "Dataset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RunValidationError{
					field:  "Dataset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDataset()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RunValidationError{
				field:  "Dataset",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Target

	// no validation rules for MaxCredits

	// no validation rules for BaseModel

	if all {
		switch v := interface{}(m.GetPipeline()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RunValidationError{
					field:  "Pipeline",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RunValidationError{
					field:  "Pipeline",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPipeline()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RunValidationError{
				field:  "Pipeline",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for State

	// no validation rules for FailReason

	if !_Run_ModelUid_Pattern.MatchString(m.GetModelUid()) {
		err := RunValidationError{
			field:  "ModelUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|^$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetStartedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RunValidationError{
					field:  "StartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RunValidationError{
					field:  "StartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RunValidationError{
				field:  "StartedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RunValidationError{
					field:  "EndedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RunValidationError{
					field:  "EndedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RunValidationError{
				field:  "EndedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RunValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RunValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RunValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RunValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RunValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RunValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Artifacts

	if len(errors) > 0 {
		return RunMultiError(errors)
	}

	return nil
}

// RunMultiError is an error wrapping multiple validation errors returned by
// Run.ValidateAll() if the designated constraints aren't met.
type RunMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RunMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RunMultiError) AllErrors() []error { return m }

// RunValidationError is the validation error returned by Run.Validate if the
// designated constraints aren't met.
type RunValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RunValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RunValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RunValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RunValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RunValidationError) ErrorName() string { return "RunValidationError" }

// Error satisfies the builtin error interface
func (e RunValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRun.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RunValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RunValidationError{}

var _Run_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

var _Run_OperatorUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|^$")

var _Run_ModelUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|^$")

// Validate checks the field values on RunFilter with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RunFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RunFilter with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RunFilterMultiError, or nil
// if none found.
func (m *RunFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *RunFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_RunFilter_OperatorUid_Pattern.MatchString(m.GetOperatorUid()) {
		err := RunFilterValidationError{
			field:  "OperatorUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|^$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Type

	// no validation rules for NamePattern

	// no validation rules for BaseModelPattern

	if len(errors) > 0 {
		return RunFilterMultiError(errors)
	}

	return nil
}

// RunFilterMultiError is an error wrapping multiple validation errors returned
// by RunFilter.ValidateAll() if the designated constraints aren't met.
type RunFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RunFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RunFilterMultiError) AllErrors() []error { return m }

// RunFilterValidationError is the validation error returned by
// RunFilter.Validate if the designated constraints aren't met.
type RunFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RunFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RunFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RunFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RunFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RunFilterValidationError) ErrorName() string { return "RunFilterValidationError" }

// Error satisfies the builtin error interface
func (e RunFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRunFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RunFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RunFilterValidationError{}

var _RunFilter_OperatorUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|^$")

// Validate checks the field values on GetRunRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetRunRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRunRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetRunRequestMultiError, or
// nil if none found.
func (m *GetRunRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRunRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetRunRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := GetRunRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetRunRequestMultiError(errors)
	}

	return nil
}

// GetRunRequestMultiError is an error wrapping multiple validation errors
// returned by GetRunRequest.ValidateAll() if the designated constraints
// aren't met.
type GetRunRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRunRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRunRequestMultiError) AllErrors() []error { return m }

// GetRunRequestValidationError is the validation error returned by
// GetRunRequest.Validate if the designated constraints aren't met.
type GetRunRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRunRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRunRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRunRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRunRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRunRequestValidationError) ErrorName() string { return "GetRunRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetRunRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRunRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRunRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRunRequestValidationError{}

var _GetRunRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on DeleteRunRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteRunRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteRunRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteRunRequestMultiError, or nil if none found.
func (m *DeleteRunRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteRunRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_DeleteRunRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := DeleteRunRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|byfilter\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteRunRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteRunRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteRunRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteRunRequestMultiError(errors)
	}

	return nil
}

// DeleteRunRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteRunRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteRunRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteRunRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteRunRequestMultiError) AllErrors() []error { return m }

// DeleteRunRequestValidationError is the validation error returned by
// DeleteRunRequest.Validate if the designated constraints aren't met.
type DeleteRunRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteRunRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteRunRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteRunRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteRunRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteRunRequestValidationError) ErrorName() string { return "DeleteRunRequestValidationError" }

// Error satisfies the builtin error interface
func (e DeleteRunRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteRunRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteRunRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteRunRequestValidationError{}

var _DeleteRunRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|byfilter")

// Validate checks the field values on ListRunRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListRunRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListRunRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListRunRequestMultiError,
// or nil if none found.
func (m *ListRunRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListRunRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListRunRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListRunRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListRunRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListRunRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListRunRequestMultiError(errors)
	}

	return nil
}

// ListRunRequestMultiError is an error wrapping multiple validation errors
// returned by ListRunRequest.ValidateAll() if the designated constraints
// aren't met.
type ListRunRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListRunRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListRunRequestMultiError) AllErrors() []error { return m }

// ListRunRequestValidationError is the validation error returned by
// ListRunRequest.Validate if the designated constraints aren't met.
type ListRunRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListRunRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListRunRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListRunRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListRunRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListRunRequestValidationError) ErrorName() string { return "ListRunRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListRunRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListRunRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListRunRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListRunRequestValidationError{}

// Validate checks the field values on ListRunReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListRunReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListRunReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListRunReplyMultiError, or
// nil if none found.
func (m *ListRunReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListRunReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRuns() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListRunReplyValidationError{
						field:  fmt.Sprintf("Runs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListRunReplyValidationError{
						field:  fmt.Sprintf("Runs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListRunReplyValidationError{
					field:  fmt.Sprintf("Runs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for Total

	if len(errors) > 0 {
		return ListRunReplyMultiError(errors)
	}

	return nil
}

// ListRunReplyMultiError is an error wrapping multiple validation errors
// returned by ListRunReply.ValidateAll() if the designated constraints aren't met.
type ListRunReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListRunReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListRunReplyMultiError) AllErrors() []error { return m }

// ListRunReplyValidationError is the validation error returned by
// ListRunReply.Validate if the designated constraints aren't met.
type ListRunReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListRunReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListRunReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListRunReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListRunReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListRunReplyValidationError) ErrorName() string { return "ListRunReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListRunReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListRunReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListRunReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListRunReplyValidationError{}

// Validate checks the field values on StartRunRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *StartRunRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartRunRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StartRunRequestMultiError, or nil if none found.
func (m *StartRunRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StartRunRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_StartRunRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := StartRunRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := StartRunRequest_Option_Enum_name[int32(m.GetOption())]; !ok {
		err := StartRunRequestValidationError{
			field:  "Option",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return StartRunRequestMultiError(errors)
	}

	return nil
}

// StartRunRequestMultiError is an error wrapping multiple validation errors
// returned by StartRunRequest.ValidateAll() if the designated constraints
// aren't met.
type StartRunRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartRunRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartRunRequestMultiError) AllErrors() []error { return m }

// StartRunRequestValidationError is the validation error returned by
// StartRunRequest.Validate if the designated constraints aren't met.
type StartRunRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartRunRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartRunRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartRunRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartRunRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartRunRequestValidationError) ErrorName() string { return "StartRunRequestValidationError" }

// Error satisfies the builtin error interface
func (e StartRunRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartRunRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartRunRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartRunRequestValidationError{}

var _StartRunRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on FinishRunRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FinishRunRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FinishRunRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FinishRunRequestMultiError, or nil if none found.
func (m *FinishRunRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FinishRunRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_FinishRunRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := FinishRunRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for FailReason

	if len(errors) > 0 {
		return FinishRunRequestMultiError(errors)
	}

	return nil
}

// FinishRunRequestMultiError is an error wrapping multiple validation errors
// returned by FinishRunRequest.ValidateAll() if the designated constraints
// aren't met.
type FinishRunRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FinishRunRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FinishRunRequestMultiError) AllErrors() []error { return m }

// FinishRunRequestValidationError is the validation error returned by
// FinishRunRequest.Validate if the designated constraints aren't met.
type FinishRunRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FinishRunRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FinishRunRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FinishRunRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FinishRunRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FinishRunRequestValidationError) ErrorName() string { return "FinishRunRequestValidationError" }

// Error satisfies the builtin error interface
func (e FinishRunRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFinishRunRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FinishRunRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FinishRunRequestValidationError{}

var _FinishRunRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on Run_Dataset with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Run_Dataset) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Run_Dataset with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Run_DatasetMultiError, or
// nil if none found.
func (m *Run_Dataset) ValidateAll() error {
	return m.validate(true)
}

func (m *Run_Dataset) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUris() {
		_, _ = idx, item

		if _, err := url.Parse(item); err != nil {
			err = Run_DatasetValidationError{
				field:  fmt.Sprintf("Uris[%v]", idx),
				reason: "value must be a valid URI",
				cause:  err,
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for SizeGb

	if len(errors) > 0 {
		return Run_DatasetMultiError(errors)
	}

	return nil
}

// Run_DatasetMultiError is an error wrapping multiple validation errors
// returned by Run_Dataset.ValidateAll() if the designated constraints aren't met.
type Run_DatasetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Run_DatasetMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Run_DatasetMultiError) AllErrors() []error { return m }

// Run_DatasetValidationError is the validation error returned by
// Run_Dataset.Validate if the designated constraints aren't met.
type Run_DatasetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Run_DatasetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Run_DatasetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Run_DatasetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Run_DatasetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Run_DatasetValidationError) ErrorName() string { return "Run_DatasetValidationError" }

// Error satisfies the builtin error interface
func (e Run_DatasetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRun_Dataset.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Run_DatasetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Run_DatasetValidationError{}

// Validate checks the field values on Run_Pipeline with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Run_Pipeline) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Run_Pipeline with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Run_PipelineMultiError, or
// nil if none found.
func (m *Run_Pipeline) ValidateAll() error {
	return m.validate(true)
}

func (m *Run_Pipeline) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Image

	if len(errors) > 0 {
		return Run_PipelineMultiError(errors)
	}

	return nil
}

// Run_PipelineMultiError is an error wrapping multiple validation errors
// returned by Run_Pipeline.ValidateAll() if the designated constraints aren't met.
type Run_PipelineMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Run_PipelineMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Run_PipelineMultiError) AllErrors() []error { return m }

// Run_PipelineValidationError is the validation error returned by
// Run_Pipeline.Validate if the designated constraints aren't met.
type Run_PipelineValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Run_PipelineValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Run_PipelineValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Run_PipelineValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Run_PipelineValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Run_PipelineValidationError) ErrorName() string { return "Run_PipelineValidationError" }

// Error satisfies the builtin error interface
func (e Run_PipelineValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRun_Pipeline.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Run_PipelineValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Run_PipelineValidationError{}

// Validate checks the field values on Run_State with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Run_State) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Run_State with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Run_StateMultiError, or nil
// if none found.
func (m *Run_State) ValidateAll() error {
	return m.validate(true)
}

func (m *Run_State) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Run_StateMultiError(errors)
	}

	return nil
}

// Run_StateMultiError is an error wrapping multiple validation errors returned
// by Run_State.ValidateAll() if the designated constraints aren't met.
type Run_StateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Run_StateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Run_StateMultiError) AllErrors() []error { return m }

// Run_StateValidationError is the validation error returned by
// Run_State.Validate if the designated constraints aren't met.
type Run_StateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Run_StateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Run_StateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Run_StateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Run_StateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Run_StateValidationError) ErrorName() string { return "Run_StateValidationError" }

// Error satisfies the builtin error interface
func (e Run_StateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRun_State.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Run_StateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Run_StateValidationError{}

// Validate checks the field values on StartRunRequest_Option with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StartRunRequest_Option) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartRunRequest_Option with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StartRunRequest_OptionMultiError, or nil if none found.
func (m *StartRunRequest_Option) ValidateAll() error {
	return m.validate(true)
}

func (m *StartRunRequest_Option) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return StartRunRequest_OptionMultiError(errors)
	}

	return nil
}

// StartRunRequest_OptionMultiError is an error wrapping multiple validation
// errors returned by StartRunRequest_Option.ValidateAll() if the designated
// constraints aren't met.
type StartRunRequest_OptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartRunRequest_OptionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartRunRequest_OptionMultiError) AllErrors() []error { return m }

// StartRunRequest_OptionValidationError is the validation error returned by
// StartRunRequest_Option.Validate if the designated constraints aren't met.
type StartRunRequest_OptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartRunRequest_OptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartRunRequest_OptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartRunRequest_OptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartRunRequest_OptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartRunRequest_OptionValidationError) ErrorName() string {
	return "StartRunRequest_OptionValidationError"
}

// Error satisfies the builtin error interface
func (e StartRunRequest_OptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartRunRequest_Option.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartRunRequest_OptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartRunRequest_OptionValidationError{}
