import os
import sys
sys.path.append('.')
from customer.common import tools


def run(input_dir, output_dir, track_id):
    label_dirs = tools.find_dir_by_pre_name(input_dir, 'label')
    for label_dir in label_dirs:
        clip_name = os.path.basename(os.path.dirname(label_dir))
        out_sub_dir = os.path.join(output_dir, clip_name)
        os.makedirs(out_sub_dir)

        json_files = tools.get_json_files(label_dir)
        for json_file in json_files:
            results = []
            json_data = tools.get_json_data(json_file)
            width = json_data['label_meta']['source_info']['width']
            height = json_data['label_meta']['source_info']['height']
            for label in json_data['labels']:
                anno = label['annotation']['data']
                label_width = anno['width']
                label_height = anno['height']
                center_x = anno['x'] + label_width / 2
                center_y = anno['y'] + label_height / 2
                if track_id:
                    track_id = label['attrs']['track_id'][0]
                    results.append(
                        f"{label['class']} {float(center_x) / width:.6f} {float(center_y) / height:.6f} {float(label_width) / width:.6f} {float(label_height) / height:.6f} {track_id}"
                    )
                else:
                    results.append(
                        f"{label['class']} {float(center_x) / width:.6f} {float(center_y) / height:.6f} {float(label_width) / width:.6f} {float(label_height) / height:.6f}"
                    )
            out_file = os.path.join(out_sub_dir, json_file.name.replace(".json", ".txt"))
            with open(out_file, 'w') as f:
                f.write("\n".join(results))


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out, tools.is_true(args.txt))
