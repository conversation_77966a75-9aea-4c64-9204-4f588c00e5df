package qcraft

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestConvertEulerAngles(t *testing.T) {
	q, err := convertEulerAngles([]float64{72.449, -20.594, 12.574, -0.312, 0.009, 0.0100})
	require.NoError(t, err)
	assert.InDeltaSlice(t, []float64{72.449, -20.594, 12.574,
		0.004240067641654379, 0.0052221134504812725, -0.1553422916846638, 0.9878378023601149}, q, 1e-9)
}
