package preparedata

import (
	"bufio"
	sha2 "crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"image"
	_ "image/jpeg"
	_ "image/png"
	"io"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"

	"annofeed/internal/biz"
	"annofeed/workflow/common"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/log"
	_ "golang.org/x/image/webp"
)

type rawdataParser struct {
	rawdatabiz *biz.RawdataBiz
	log        *log.Helper
}

func newRawdataParser(rawdatabiz *biz.RawdataBiz, logger log.Logger) *rawdataParser {
	return &rawdataParser{rawdatabiz: rawdatabiz, log: log.NewHelper(logger)}
}

func (o *rawdataParser) parseImage(basedir, subdir, name string) (*biz.Rawdata, error) {
	fpath := filepath.Join(subdir, name)
	f, err := os.Open(filepath.Join(basedir, fpath))
	if err != nil {
		return nil, fmt.Errorf("failed to open file %v: %w", fpath, err)
	}
	defer f.Close()
	imgCfg, format, err := image.DecodeConfig(f)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image %v: %w", fpath, err)
	}

	camName := name[:len(name)-len(path.Ext(name))]
	meta := &anno.Rawdata_Meta{
		Image: &anno.Rawdata_ImageMeta{
			Width:  int32(imgCfg.Width),
			Height: int32(imgCfg.Height),
			Camera: camName,
		},
	}
	data, err := json.Marshal(meta)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal image cfg %v: %w", fpath, err)
	}
	return &biz.Rawdata{
		Type:    biz.RawdataTypeImage,
		Format:  common.SupportedImageFormat[format],
		CamName: camName,
		Info:    data,
	}, nil
}

func (o *rawdataParser) parsePCD(basedir, subdir, name string) (*biz.Rawdata, error) {
	fpath := filepath.Join(subdir, name)
	f, err := os.Open(filepath.Join(basedir, fpath))
	if err != nil {
		return nil, fmt.Errorf("failed to open file %v: %w", fpath, err)
	}
	defer f.Close()

	// format of extrinsic param is [x,y,z,qx,qy,qz,qw]
	viewpoint, _ := biz.ExtrinsicToRawdataParam([]float64{0, 0, 0, 0, 0, 0, 1})
	paramData, err := biz.LoadElemParamData(basedir, subdir, common.ParamsFile)
	if err != nil && !errors.Is(err, os.ErrNotExist) {
		return nil, fmt.Errorf("failed to load elem params: %w", err)
	}
	if paramData == nil {
		paramData = new(biz.ElemParamData)
	}

	lidarViewpoint, err := paramData.ElemParam.GetLidarViewpoint(name)
	if err != nil {
		return nil, fmt.Errorf("failed to get lidar viewpoint")
	}
	if lidarViewpoint != nil {
		viewpoint = lidarViewpoint
	}

	var data []byte
	rb := bufio.NewReader(f)
loop:
	for {
		line, _, err := rb.ReadLine()
		if err != nil {
			return nil, fmt.Errorf("failed to read line %v: %w", fpath, err)
		}
		args := strings.Fields(string(line))
		if len(args) < 2 {
			return nil, fmt.Errorf("invalid PCD file %v: header field have no value", fpath)
		}
		switch args[0] {
		case "POINTS":
			points, err := strconv.Atoi(args[1])
			if err != nil {
				return nil, fmt.Errorf("failed to convert pcd points count %v: %w", fpath, err)
			}
			data, err = json.Marshal(&anno.Rawdata_Meta{
				Pcd: &anno.Rawdata_PCDMeta{
					Points:    int32(points),
					Viewpoint: viewpoint,
					Pose:      paramData.ElemParam.GetLidarPose(),
				},
			})
			if err != nil {
				return nil, fmt.Errorf("failed to marshal pcd cfg %v: %w", fpath, err)
			}
		case "DATA":
			break loop
		}
	}
	return &biz.Rawdata{
		Type:   biz.RawdataTypePointcloud,
		Format: common.RawdataFormatPCD,
		Info:   data,
		Idx:    1, // put it the first in the element's rawdatas
	}, nil
}

func (o *rawdataParser) fakePCD(basedir, subdir, name string) (*biz.Rawdata, error) {
	// format of extrinsic param is [x,y,z,qx,qy,qz,qw]
	viewpoint, _ := biz.ExtrinsicToRawdataParam([]float64{0, 0, 0, 0, 0, 0, 1})
	paramData, err := biz.LoadElemParamData(basedir, subdir, common.ParamsFile)
	if err != nil && !errors.Is(err, os.ErrNotExist) {
		return nil, fmt.Errorf("failed to load elem params: %w", err)
	}
	if paramData == nil {
		paramData = new(biz.ElemParamData)
	}

	lidarViewpoint, err := paramData.ElemParam.GetLidarViewpoint(name)
	if err != nil {
		return nil, fmt.Errorf("failed to get lidar viewpoint")
	}
	if lidarViewpoint != nil {
		viewpoint = lidarViewpoint
	}

	pose := paramData.ElemParam.GetLidarPose()
	data, err := json.Marshal(&anno.Rawdata_Meta{Pcd: &anno.Rawdata_PCDMeta{Viewpoint: viewpoint, Pose: pose}})
	if err != nil {
		return nil, fmt.Errorf("failed to marshal pcd cfg %v: %w", path.Join(subdir, common.LidarPCD), err)
	}

	return &biz.Rawdata{
		Type:   biz.RawdataTypePointcloud,
		Format: common.RawdataFormatPCD,
		Info:   data,
		Idx:    1, // put it the first in the element's rawdatas
	}, nil
}

func (o *rawdataParser) parseElemParams(basedir, subdir, name string) (*biz.Rawdata, error) {
	// verify the format
	paramData, err := biz.LoadElemParamData(basedir, subdir, name)
	if err != nil {
		fmt.Println("failed parse elem params: ", err)
		return nil, err
	}

	return &biz.Rawdata{
		Type:   biz.RawdataTypeParams,
		Format: common.RawdataFormatJSON,
		Info:   paramData.RawData,
	}, nil
}

func (o *rawdataParser) parseElemAnnos(basedir, subdir, name string) (*biz.Rawdata, error) {
	fpath := filepath.Join(subdir, name)
	_, annos, err := LoadJSONFile[anno.ExportAnnos](basedir, fpath)
	if err != nil || annos == nil {
		return nil, err
	}
	var data []byte
	if annos != nil && len(annos.ElementAnnos) > 0 {
		data, err = json.Marshal(annos.ElementAnnos[0])
		if err != nil {
			return nil, err
		}
	}

	return &biz.Rawdata{
		Type:   biz.RawdataTypeAnnos,
		Format: common.RawdataFormatJSON,
		Info:   data,
	}, nil
}

func (o *rawdataParser) StatFile(fpath string) (size int64, sha256 string, err error) {
	f, err := os.Open(fpath)
	if err != nil {
		return 0, "", fmt.Errorf("failed to open file %v: %w", fpath, err)
	}
	defer f.Close()

	hash := sha2.New()
	buf := make([]byte, 8*1024)
	for {
		n, err := f.Read(buf)
		if n > 0 {
			hash.Write(buf[:n])
			size += int64(n)
		}
		if err == io.EOF {
			err = nil
			break
		}
		if err != nil {
			return 0, "", fmt.Errorf("failed to read file %v: %w", fpath, err)
		}
	}
	sha256 = hex.EncodeToString(hash.Sum(nil))
	return
}
