// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/specgrant.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateSpecgrantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateSpecgrantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSpecgrantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSpecgrantRequestMultiError, or nil if none found.
func (m *CreateSpecgrantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSpecgrantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GranteeUid

	if _, ok := _CreateSpecgrantRequest_ItemType_NotInLookup[m.GetItemType()]; ok {
		err := CreateSpecgrantRequestValidationError{
			field:  "ItemType",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := Specgrant_ItemType_Enum_name[int32(m.GetItemType())]; !ok {
		err := CreateSpecgrantRequestValidationError{
			field:  "ItemType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ItemUid

	if len(errors) > 0 {
		return CreateSpecgrantRequestMultiError(errors)
	}

	return nil
}

// CreateSpecgrantRequestMultiError is an error wrapping multiple validation
// errors returned by CreateSpecgrantRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateSpecgrantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSpecgrantRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSpecgrantRequestMultiError) AllErrors() []error { return m }

// CreateSpecgrantRequestValidationError is the validation error returned by
// CreateSpecgrantRequest.Validate if the designated constraints aren't met.
type CreateSpecgrantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSpecgrantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSpecgrantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSpecgrantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSpecgrantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSpecgrantRequestValidationError) ErrorName() string {
	return "CreateSpecgrantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateSpecgrantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSpecgrantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSpecgrantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSpecgrantRequestValidationError{}

var _CreateSpecgrantRequest_ItemType_NotInLookup = map[Specgrant_ItemType_Enum]struct{}{
	0: {},
}

// Validate checks the field values on Specgrant with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Specgrant) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Specgrant with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SpecgrantMultiError, or nil
// if none found.
func (m *Specgrant) ValidateAll() error {
	return m.validate(true)
}

func (m *Specgrant) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GrantorUid

	// no validation rules for GranteeUid

	if _, ok := _Specgrant_ItemType_NotInLookup[m.GetItemType()]; ok {
		err := SpecgrantValidationError{
			field:  "ItemType",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := Specgrant_ItemType_Enum_name[int32(m.GetItemType())]; !ok {
		err := SpecgrantValidationError{
			field:  "ItemType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ItemUid

	// no validation rules for OrgUid

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SpecgrantValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SpecgrantValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SpecgrantValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SpecgrantMultiError(errors)
	}

	return nil
}

// SpecgrantMultiError is an error wrapping multiple validation errors returned
// by Specgrant.ValidateAll() if the designated constraints aren't met.
type SpecgrantMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SpecgrantMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SpecgrantMultiError) AllErrors() []error { return m }

// SpecgrantValidationError is the validation error returned by
// Specgrant.Validate if the designated constraints aren't met.
type SpecgrantValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SpecgrantValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SpecgrantValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SpecgrantValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SpecgrantValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SpecgrantValidationError) ErrorName() string { return "SpecgrantValidationError" }

// Error satisfies the builtin error interface
func (e SpecgrantValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSpecgrant.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SpecgrantValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SpecgrantValidationError{}

var _Specgrant_ItemType_NotInLookup = map[Specgrant_ItemType_Enum]struct{}{
	0: {},
}

// Validate checks the field values on SpecgrantFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SpecgrantFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SpecgrantFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SpecgrantFilterMultiError, or nil if none found.
func (m *SpecgrantFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *SpecgrantFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GrantorUid

	// no validation rules for GranteeUid

	// no validation rules for OrgUid

	// no validation rules for ItemType

	for idx, item := range m.GetItemUids() {
		_, _ = idx, item

		if !_SpecgrantFilter_ItemUids_Pattern.MatchString(item) {
			err := SpecgrantFilterValidationError{
				field:  fmt.Sprintf("ItemUids[%v]", idx),
				reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return SpecgrantFilterMultiError(errors)
	}

	return nil
}

// SpecgrantFilterMultiError is an error wrapping multiple validation errors
// returned by SpecgrantFilter.ValidateAll() if the designated constraints
// aren't met.
type SpecgrantFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SpecgrantFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SpecgrantFilterMultiError) AllErrors() []error { return m }

// SpecgrantFilterValidationError is the validation error returned by
// SpecgrantFilter.Validate if the designated constraints aren't met.
type SpecgrantFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SpecgrantFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SpecgrantFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SpecgrantFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SpecgrantFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SpecgrantFilterValidationError) ErrorName() string { return "SpecgrantFilterValidationError" }

// Error satisfies the builtin error interface
func (e SpecgrantFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSpecgrantFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SpecgrantFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SpecgrantFilterValidationError{}

var _SpecgrantFilter_ItemUids_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on DeleteSpecgrantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteSpecgrantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteSpecgrantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteSpecgrantRequestMultiError, or nil if none found.
func (m *DeleteSpecgrantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteSpecgrantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFilter() == nil {
		err := DeleteSpecgrantRequestValidationError{
			field:  "Filter",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteSpecgrantRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteSpecgrantRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteSpecgrantRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteSpecgrantRequestMultiError(errors)
	}

	return nil
}

// DeleteSpecgrantRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteSpecgrantRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteSpecgrantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteSpecgrantRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteSpecgrantRequestMultiError) AllErrors() []error { return m }

// DeleteSpecgrantRequestValidationError is the validation error returned by
// DeleteSpecgrantRequest.Validate if the designated constraints aren't met.
type DeleteSpecgrantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteSpecgrantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteSpecgrantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteSpecgrantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteSpecgrantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteSpecgrantRequestValidationError) ErrorName() string {
	return "DeleteSpecgrantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteSpecgrantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteSpecgrantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteSpecgrantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteSpecgrantRequestValidationError{}

// Validate checks the field values on ListSpecgrantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListSpecgrantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSpecgrantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSpecgrantRequestMultiError, or nil if none found.
func (m *ListSpecgrantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSpecgrantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListSpecgrantRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListSpecgrantRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListSpecgrantRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListSpecgrantRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListSpecgrantRequestMultiError(errors)
	}

	return nil
}

// ListSpecgrantRequestMultiError is an error wrapping multiple validation
// errors returned by ListSpecgrantRequest.ValidateAll() if the designated
// constraints aren't met.
type ListSpecgrantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSpecgrantRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSpecgrantRequestMultiError) AllErrors() []error { return m }

// ListSpecgrantRequestValidationError is the validation error returned by
// ListSpecgrantRequest.Validate if the designated constraints aren't met.
type ListSpecgrantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSpecgrantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSpecgrantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSpecgrantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSpecgrantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSpecgrantRequestValidationError) ErrorName() string {
	return "ListSpecgrantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListSpecgrantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSpecgrantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSpecgrantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSpecgrantRequestValidationError{}

// Validate checks the field values on ListSpecgrantReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListSpecgrantReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSpecgrantReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSpecgrantReplyMultiError, or nil if none found.
func (m *ListSpecgrantReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSpecgrantReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetGrants() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListSpecgrantReplyValidationError{
						field:  fmt.Sprintf("Grants[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListSpecgrantReplyValidationError{
						field:  fmt.Sprintf("Grants[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListSpecgrantReplyValidationError{
					field:  fmt.Sprintf("Grants[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if len(errors) > 0 {
		return ListSpecgrantReplyMultiError(errors)
	}

	return nil
}

// ListSpecgrantReplyMultiError is an error wrapping multiple validation errors
// returned by ListSpecgrantReply.ValidateAll() if the designated constraints
// aren't met.
type ListSpecgrantReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSpecgrantReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSpecgrantReplyMultiError) AllErrors() []error { return m }

// ListSpecgrantReplyValidationError is the validation error returned by
// ListSpecgrantReply.Validate if the designated constraints aren't met.
type ListSpecgrantReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSpecgrantReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSpecgrantReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSpecgrantReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSpecgrantReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSpecgrantReplyValidationError) ErrorName() string {
	return "ListSpecgrantReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListSpecgrantReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSpecgrantReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSpecgrantReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSpecgrantReplyValidationError{}

// Validate checks the field values on Specgrant_ItemType with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Specgrant_ItemType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Specgrant_ItemType with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Specgrant_ItemTypeMultiError, or nil if none found.
func (m *Specgrant_ItemType) ValidateAll() error {
	return m.validate(true)
}

func (m *Specgrant_ItemType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Specgrant_ItemTypeMultiError(errors)
	}

	return nil
}

// Specgrant_ItemTypeMultiError is an error wrapping multiple validation errors
// returned by Specgrant_ItemType.ValidateAll() if the designated constraints
// aren't met.
type Specgrant_ItemTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Specgrant_ItemTypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Specgrant_ItemTypeMultiError) AllErrors() []error { return m }

// Specgrant_ItemTypeValidationError is the validation error returned by
// Specgrant_ItemType.Validate if the designated constraints aren't met.
type Specgrant_ItemTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Specgrant_ItemTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Specgrant_ItemTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Specgrant_ItemTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Specgrant_ItemTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Specgrant_ItemTypeValidationError) ErrorName() string {
	return "Specgrant_ItemTypeValidationError"
}

// Error satisfies the builtin error interface
func (e Specgrant_ItemTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSpecgrant_ItemType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Specgrant_ItemTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Specgrant_ItemTypeValidationError{}
