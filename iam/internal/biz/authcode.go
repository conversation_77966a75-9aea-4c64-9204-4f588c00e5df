package biz

import (
	"time"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
)

const (
	AuthcodeStatusValid   = 0
	AuthcodeStatusUsed    = 1
	AuthcodeStatusExpired = 2
)

type Authcode struct {
	ID        int64     `json:"id" gorm:"default:null"`
	UserID    int64     `json:"user_id" gorm:"default:null"`
	Sequence  int       `json:"sequence" gorm:"default:null"`
	Channel   string    `json:"channel" gorm:"default:null"`
	Receiver  string    `json:"receiver" gorm:"default:null"`
	Code      string    `json:"code" gorm:"default:null"`
	Status    int       `json:"status" gorm:"default:null"`
	ExpiredAt time.Time `json:"expired_at" gorm:"default:null"`
	CreatedAt time.Time `json:"created_at" gorm:"default:null"`
	UpdatedAt time.Time `json:"updated_at" gorm:"default:null"`
}

func (o *Authcode) GetID() int64 { return o.ID }
func (o *Authcode) IsValid() bool {
	return o.Status == AuthcodeStatusValid && time.Now().Before(o.ExpiredAt)
}

const AuthcodeTableName = "autocodes"

var (
	authcode_             = field.RegObject(&Authcode{})
	AuthcodeUpdatableFlds = field.NewModel(AuthcodeTableName, authcode_,
		"UserID", "Sequence", "Channel", "Receiver", "Code", "Status", "ExpiredAt")

	Authcode_UserID    = field.Sname(&authcode_.UserID)
	Authcode_Sequence  = field.Sname(&authcode_.Sequence)
	Authcode_Channel   = field.Sname(&authcode_.Channel)
	Authcode_Receiver  = field.Sname(&authcode_.Receiver)
	Authcode_Code      = field.Sname(&authcode_.Code)
	Authcode_Status    = field.Sname(&authcode_.Status)
	Authcode_ExpiredAt = field.Sname(&authcode_.ExpiredAt)
)
