syntax = "proto3";

package anno.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
// import "google/protobuf/field_mask.proto";
import "google/api/field_behavior.proto";
import "openapi/v3/annotations.proto";
// import "anno/v1/type.proto";
import "validate/validate.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anno/v1;anno";
option java_multiple_files = true;
option java_package = "anno.v1";

service Projects {
  rpc CreateProject (CreateProjectRequest) returns (Project) {
    option (google.api.http) = {
      post: "/v1/projects"
      body: "*"
    };
  }

  rpc UpdateProject (UpdateProjectRequest) returns (Project) {
    option (google.api.http) = {
      patch: "/v1/projects/{project.uid}"
      body: "project"
    };
  }

  rpc DeleteProject (DeleteProjectRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/projects/{uid}"
    };
  }

  rpc GetProject (GetProjectRequest) returns (Project) {
    option (google.api.http) = {
      get: "/v1/projects/{uid}"
    };
  }

  rpc ListProject (ListProjectRequest) returns (ListProjectReply) {
    option (google.api.http) = {
      get: "/v1/projects"
    };
  }
}

message CreateProjectRequest {
  // mandatory in update-requests
  string uid = 1;
  // mandatory in create-requests
  string name = 2;
  string desc = 3;
  string avatar = 4;
}
//message CreateProjectReply {}

message UpdateProjectRequest {
  option (openapi.v3.schema) = {
    required: ["project", "fields"]
  };

  CreateProjectRequest project = 1;
  repeated string fields = 2;
}
//message UpdateProjectReply {}

message DeleteProjectRequest {
  string uid = 1;
}
//message DeleteProjectReply {}

message GetProjectRequest {
  string uid = 1;
}
//message GetProjectReply {}

message ListProjectRequest {
  int32 page = 1;
  int32 pagesz = 2 [(validate.rules).int32 = {gte:0, lte: 100}];

  // filter by orgnization
  string org_uid = 3;
  // filter by creator
  string creator_uid = 4;
  // filter by name pattern
  string name_pattern = 5;
}

message ListProjectReply {
  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated Project projects = 2;
}

message Project {
  option (openapi.v3.schema) = {
    required: ["uid", "name", "created_at"]
  };

  string uid = 1;
  string name = 2;
  string desc = 3;
  string avatar = 4;

  google.protobuf.Timestamp updated_at = 14 [(google.api.field_behavior) = OUTPUT_ONLY];
  google.protobuf.Timestamp created_at = 15 [(google.api.field_behavior) = OUTPUT_ONLY];
}
