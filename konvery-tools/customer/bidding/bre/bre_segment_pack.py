import os
import sys
import numpy as np
import shutil
from collections import Counter
import open3d as o3d

sys.path.append(".")
from customer.common import tools


def customer_code(parsing_dir, lidar_dir):
    """客户给的参考代码，不实际使用，仅供参考"""
    lidar_name = 'hesai90'
    pc_range = [[-100.0, -100.0, -5.0], [100.0, 100.0, 5.0]]

    ### 2 gen & save xyzl pcd file
    parsing_files = os.listdir(parsing_dir)
    lidar_files = os.listdir(lidar_dir)
    parsing_files = sorted(parsing_files)
    for frame_id, file in enumerate(parsing_files):
        parsing_path = os.path.join(parsing_dir, file)
        with open(parsing_path, "r") as f:
            input_lines = f.readlines()
        labels = input_lines[0].strip().split()
        labels = [int(l) for l in labels]
        ### keep 7 classes format
        # parsings = np.array([float(n) for n in labels]).reshape(-1, 1)
        ### keep 8 classes format
        parsings = np.array([float(n + 1) if n > 4 else float(n) for n in labels]).reshape(-1, 1)

        lidar_path = os.path.join(lidar_dir, file.replace(".txt", ".bin"))
        points = np.fromfile(lidar_path, dtype=np.float16).reshape(-1, 5).astype(np.float32)  # xyzit\
        assert points.shape[0] == parsings.shape[0]

        ### save xyzl pcd file
        xyzl = np.hstack((points[:, 0:3], parsings))
        valid_xyzl = []
        for i in range(xyzl.shape[0]):
            if pc_range[0][0] <= xyzl[i, 0] <= pc_range[1][0] and \
                    pc_range[0][1] <= xyzl[i, 1] <= pc_range[1][1] and \
                    pc_range[0][2] <= xyzl[i, 2] <= pc_range[1][2]:
                valid_xyzl.append(xyzl[i, :])
        valid_xyzl = np.array(valid_xyzl)  ### save xyzl pcd file


category_mapping = {
    0: "obj",
    1: "ground",
    2: "curb",
    3: "vege",
    4: "fence",
    5: "fence",
    6: "unknown",
    7: "noise",
    8: "wallcolumn"
}


HEADER = '''
VERSION .7
FIELDS x y z intensity 
SIZE 4 4 4 4
TYPE F F F F
COUNT 1 1 1 1 
WIDTH {}
HEIGHT 1
POINTS {}
VIEWPOINT 0 0 0 1 0 0 0
DATA ascii
'''

cameras = ["spherical_right_forward", "spherical_left_forward", "spherical_right_backward", "spherical_backward",
           "spherical_left_backward", "onsemi_narrow", "onsemi_obstacle"]

def bin2pcd(bin_file, pcd_file):
    points = np.fromfile(bin_file, dtype=np.float16).reshape(-1, 5).astype(np.float32)  # xyzit
    with open(pcd_file, 'w') as f:
        f.write(HEADER.format(len(points), len(points)))
        np.savetxt(f, points[:, :4], delimiter=' ', fmt='%f %f %f %f')
    return len(points)


def construct_config(param_dir, output_file, range):
    config = {
        "camera": {camera: camera for camera in cameras},
        "data_type": "pointcloud",
        "sensor_params": {},
        "range": range
    }
    # for camera in cameras:
    #     intrinsic_file = os.path.join(param_dir, f'{camera}_intrinsics.yaml')
    #     extrinsic_file = os.path.join(param_dir, f'{camera}_extrinsics.yaml')
    #     with open(intrinsic_file) as stream:
    #         param = yaml.safe_load(stream)
    #         intrinsic = param['K']
    #     with open(extrinsic_file) as stream:
    #         param = yaml.safe_load(stream)
    #         translation = param['transform']['translation']
    #         rotation = param['transform']['rotation']
    #         extrinsic = tools.pose_to_mat([translation['x'], translation['y'], translation['z'], rotation['x'], rotation['y'], rotation['z'], rotation['w']])
    #     config['sensor_params'][camera] = {
    #         "camera_model": "pinhole",
    #         "extrinsic": extrinsic.tolist(),
    #         "fx": intrinsic[0] / 2,
    #         "fy": intrinsic[4] / 2,
    #         "cx": intrinsic[2] / 2,
    #         "cy": intrinsic[5] / 2,
    #         "k1": 0,
    #         "k2": 0,
    #         "k3": 0,
    #         "p1": 0,
    #         "p2": 0
    #     }
    tools.write_json_file(config, output_file)


def construct_pre_label_by_bin(pre_label_file, output_file, point_num):
    with open(pre_label_file) as f:
        input_lines = f.readlines()
    labels = input_lines[0].strip().split()
    labels = [int(l) for l in labels]
    labels = [n + 1 if n > 4 else n for n in labels]
    assert point_num == len(labels), "pcd points number must equal to labels number"


    statistic = {}
    counter = Counter(labels)
    for category in counter:
        statistic[category_mapping[category]] = {
            "num": counter.get(category),
            "instances": {}
        }
    segmentation = {
        "result": [
            [category_mapping.get(l) for l in labels],
            [0] * point_num
        ],
        "statistic": statistic
    }
    pre_label_data = {"label_meta": {"mark_status": 0}, "segmentation": segmentation}
    tools.write_json_file(pre_label_data, output_file)


def construct_pre_label_by_pcd(pcd_file, output_file):
    # 读取PCD文件
    pcd = o3d.t.io.read_point_cloud(pcd_file)
    # 获取标签字段
    labels = []
    for label in pcd.point.label:
        labels.append(label.item())
    labels = [n + 1 if n > 4 else n for n in labels]

    statistic = {}
    counter = Counter(labels)
    for category in counter:
        statistic[category_mapping[category]] = {
            "num": counter.get(category),
            "instances": {}
        }
    segmentation = {
        "result": [
            [category_mapping.get(l) for l in labels],
            [0] * len(labels)
        ],
        "statistic": statistic
    }
    pre_label_data = {"label_meta": {"mark_status": 0}, "segmentation": segmentation}
    tools.write_json_file(pre_label_data, output_file)


def process_by_bin(item, input_dir, out_lidar_dir, out_camera_dir, out_pre_label_dir):
    bin_file = item['hesai90']
    point_num = bin2pcd(os.path.join(input_dir, 'bin', bin_file),
                        os.path.join(out_lidar_dir, bin_file.replace('.bin', '.pcd')),
                        fields='xyzl')

    pre_label_file = os.path.join(input_dir, 'parsing', bin_file.replace('.bin', '.txt'))
    out_pre_label_file = os.path.join(out_pre_label_dir, bin_file.replace('.bin', '.json'))
    construct_pre_label_by_bin(pre_label_file, out_pre_label_file, point_num)

    for camera in cameras:
        shutil.copyfile(
            os.path.join(input_dir, camera, item[camera]),
            os.path.join(out_camera_dir, camera, bin_file.replace('.bin', '.jpg'))
        )


def process_by_pcd(item, input_dir, out_lidar_dir, out_camera_dir, out_pre_label_dir):
    pcd_file = item['hesai90']
    out_pre_label_file = os.path.join(out_pre_label_dir, pcd_file.replace('.pcd', '.json'))
    construct_pre_label_by_pcd(os.path.join(input_dir, 'pcd', pcd_file), out_pre_label_file)

    shutil.copyfile(
        os.path.join(input_dir, 'pcd', pcd_file),
        os.path.join(out_lidar_dir, pcd_file)
    )
    for camera in cameras:
        shutil.copyfile(
            os.path.join(input_dir, camera, item[camera]),
            os.path.join(out_camera_dir, camera, pcd_file.replace('.pcd', '.jpg'))
        )


def run(input_dir, output_dir):

    tools.check_dir(output_dir)

    out_camera_dir = os.path.join(output_dir, 'camera')
    os.mkdir(out_camera_dir)
    for camera in cameras:
        os.mkdir(os.path.join(out_camera_dir, camera))
    out_lidar_dir = os.path.join(output_dir, 'lidar')
    os.mkdir(out_lidar_dir)
    out_pre_label_dir = os.path.join(output_dir, 'pre_label')
    os.mkdir(out_pre_label_dir)

    record_name = os.path.basename(input_dir)
    record_json = os.path.join(input_dir, f'{record_name}.record.json')
    record_data = tools.get_json_data(record_json)

    range_conf = {}

    for i in range(len(record_data.get('images', [])) + len(record_data.get('frames', []))):
        if 'images' in record_data:
            item = record_data['images'][i]
            process_by_bin(item, input_dir, out_lidar_dir, out_camera_dir, out_pre_label_dir)
        elif 'frames' in record_data:
            item = record_data['frames'][i]
            process_by_pcd(item, input_dir, out_lidar_dir, out_camera_dir, out_pre_label_dir)
        else:
            raise Exception("不支持的配置文件格式")
        range_conf[item['hesai90'].rsplit('.', 1)[0]] = {
            "box": [
                {
                  "name": "main",
                  "x": [-100, 100],
                  "y": [-100, 100],
                  "z": [-5, 5]
                }
            ]
        }
    construct_config(os.path.join(input_dir, 'param'), os.path.join(output_dir, 'config.json'), range_conf)


if __name__ == '__main__':
    args = tools.get_args()
    # tools.check_dir(args.out)
    for record in tools.listdir(os.path.join(args.input, 'records'), full_path=True):
        out_record = os.path.join(args.out, os.path.basename(record))
        run(os.path.join(args.input, 'records', record), out_record)
