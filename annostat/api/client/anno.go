package client

import (
	"annostat/internal/conf"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/apis/client"
)

type (
	Job  = anno.Job
	Anno = client.Anno
)

func initAnno(c *conf.Bootstrap) {
	client.InitAnno(&client.Service{Addr: c.Rpc.Anno.Addr})
}

var (
	SetAnnoSvc     = client.SetAnnoSvc
	GetLot         = client.GetLot
	GetJob         = client.GetJob
	ListJob        = client.ListJob
	GetVisibleLots = client.GetVisibleLots
)
