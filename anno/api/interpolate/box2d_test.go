package interpolate

import (
	"testing"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"

	"github.com/stretchr/testify/assert"
)

func TestBox2dInterpolate(t *testing.T) {
	o1 := &anno.Object{
		Uuid:    "1111",
		TrackId: "car-1",
		Label: &anno.Object_Label{
			Name:  "car",
			Attrs: []*anno.AttrAndValues{},
			Widget: &anno.Object_Widget{
				Name: anno.WidgetName_box2d,
				Data: []float64{200, 200, 100, 100, 100},
			},
		},
	}
	o2 := cloneObject(o1)
	o2.Label.Widget.Data = []float64{400, 400, 200, 200, 100}

	cases := []struct {
		o1       *anno.Object
		o2       *anno.Object
		span     float64
		distance float64
		exp      []float64
	}{
		{o1, nil, 10, 0, o1.Label.Widget.Data},
		{o1, nil, 10, 5, o1.Label.Widget.Data},
		{o1, o1, 5, 1, o1.Label.Widget.Data},
		{o1, o1, 5, 3, o1.Label.Widget.Data},
		{o1, o1, 5, 5, o1.Label.Widget.Data},
		{o1, o2, 1, 0, o1.Label.Widget.Data},
		{o1, o2, 1, 1, o2.Label.Widget.Data},
		{o1, o2, 10, 0, o1.Label.Widget.Data},
		{o1, o2, 10, 1, []float64{220, 220, 110, 110, 100}},
		{o1, o2, 10, 2, []float64{240, 240, 120, 120, 100}},
		{o1, o2, 10, 5, []float64{300, 300, 150, 150, 100}},
		{o1, o2, 10, 9, []float64{380, 380, 190, 190, 100}},
		{o1, o2, 10, 10, o2.Label.Widget.Data},
	}

	for i, c := range cases {
		o := box2dInterpolate(c.o1, c.o2, c.span, c.distance)
		assert.EqualValues(t, c.exp, o.Label.Widget.Data, "case-%v", i+1)
	}
}
