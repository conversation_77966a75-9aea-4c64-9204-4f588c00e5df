import os
import sys
import re
import json
import shutil
import numpy as np

sys.path.append('.')
from customer.common import tools


def parse_block(block):
    lines = block.strip().split("\n")
    new_lines = ['{']
    for idx in range(1, len(lines) - 1):
        line = lines[idx].strip()
        next_line = lines[idx+1].strip()
        # 忽略空行和注释
        if not line or line.startswith("//") or line.startswith("#"):
            continue
        if ':' in line:
            key, value = line.split(':')
            if key == 'model_type':
                new_lines.append(f'"{key}":"{value}"')
            else:
                if next_line == '}':
                    new_lines.append(f'"{key}":{value}')
                else:
                    new_lines.append(f'"{key}":{value},')
        elif line == '}':
            if next_line == '}':
                new_lines.append('}')
            else:
                new_lines.append('},')
        else:
            field = line.split(' ')[0]
            new_lines.append(f'"{field}": {{')
    new_lines.append('}')
    result = json.loads('\n'.join(new_lines))
    return result


def parse_cfg(cfg_file):
    with open(cfg_file, "r") as f:
        content = f.read()
    blocks = re.findall(r"config\s*(\{.*?\n\})", content, re.DOTALL)
    camera_params = dict()
    for block in blocks:
        params = parse_block(block)
        camera_params[params['camera_dev']] = params
    return camera_params


def construct_config(camera_params):
    config_params = dict()
    for camera_name, params in camera_params.items():
        position = camera_params[camera_name]['parameters']['extrinsic']['sensor_to_cam']['position']
        orientation = camera_params[camera_name]['parameters']['extrinsic']['sensor_to_cam']['orientation']
        extrinsic = np.linalg.inv(tools.pose_to_mat(
            [position['x'], position['y'], position['z'], orientation['qx'], orientation['qy'], orientation['qz'], orientation['qw']]
        ))
        intrinsic = camera_params[camera_name]['parameters']['intrinsic']
        config_params[camera_name] = {
            "camera_model": "pinhole" if intrinsic['model_type'].strip() == "PINHOLE" else "fisheye",
            "extrinsic": extrinsic.tolist(),
            "fx": intrinsic['f_x'],
            "fy": intrinsic['f_y'],
            "cx": intrinsic['o_x'],
            "cy": intrinsic['o_y'],
            "k1": intrinsic['k_1'],
            "k2": intrinsic['k_2'],
        }
        if config_params[camera_name]['camera_model'] == 'pinhole':
            config_params[camera_name]["p1"] = intrinsic['p_1']
            config_params[camera_name]["p2"] = intrinsic['p_2']
            config_params[camera_name]["k3"] = 0
        else:
            config_params[camera_name]["k3"] = intrinsic['k_3']
            config_params[camera_name]["k4"] = intrinsic['k_4']

    configs = {
        "data_type": "fusion_pointcloud",
        "sensor_params": config_params,
        "camera": {key: key for key in config_params.keys()}
    }
    return configs


def run(input_dir, output_dir):
    out_sub_dir = os.path.join(output_dir, os.path.basename(input_dir))
    os.mkdir(out_sub_dir)
    out_lidar_dir = os.path.join(out_sub_dir, "lidar")
    os.mkdir(out_lidar_dir)
    out_cameras_dir = os.path.join(out_sub_dir, "camera")
    os.mkdir(out_cameras_dir)
    pcds = sorted(tools.get_file_by_extension(input_dir, '.pcd'), key=lambda x: x.name)
    for idx, pcd in enumerate(pcds):
        camera_dir = pcd.path.rsplit('.')[0]
        if idx == 0:
            for file in tools.listdir(camera_dir):
                if file.endswith('.cfg'):
                    camera_params = parse_cfg(os.path.join(camera_dir, file))
                    config = construct_config(camera_params)
                    tools.write_json_file(config, os.path.join(out_sub_dir, 'config.json'), indent=4)
                else:
                    os.mkdir(os.path.join(out_cameras_dir, file.split('_', 1)[1].split('.')[0]))
        shutil.copyfile(pcd, os.path.join(out_lidar_dir, pcd.name))
        for file in tools.listdir(camera_dir):
            if file.endswith('.jpg'):
                src = os.path.join(camera_dir, file)
                dst = os.path.join(out_cameras_dir, file.split('_', 1)[1].split('.')[0], pcd.name.replace('.pcd', '.jpg'))
                shutil.copyfile(src, dst)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)

