import json
import os
import sys
from datetime import datetime

import numpy as np
from scipy.spatial.transform import Rotation as R

sys.path.append(".")
from customer.common import tools
import helper

ROOT = "/Users/<USER>/work/data/Guangqiyan"
RAW_DATA_DIR = "raw/downloaded/20231012/LNABLAB39N5506120-10/drive-south_main_line-afternoon-cloudy-smooth-dry-expressway-asphalt-1230show1-same_time/20231012-15-15-12/10hz30s/samples"
RAW_DATA_PATH = os.path.join(ROOT, RAW_DATA_DIR)
POSE_PATH = os.path.join(RAW_DATA_PATH, "pcd_merge_frames_pose")
PCD_PATH = os.path.join(RAW_DATA_PATH, "pcd_merge")
RAW_PARAM_DIR = "raw/downloaded/20231012/LNABLAB39N5506120-10/ego_010-20230920/ego_010/calib"
RAW_PARAM_PATH = os.path.join(ROOT, RAW_PARAM_DIR)
INTRINSIC_EXTRINSIC_PATH = os.path.join(RAW_PARAM_PATH, "ASEva_extrinsic_intrinsic")
DISTORTION_PATH = os.path.join(RAW_PARAM_PATH, "gq_calib")

version = datetime.now().strftime("%Y%m%d")
OUTPUT = os.path.join(ROOT, "output", version, "data")

poses = {}  # timestamp -> data
base_pose_timestamp = ""
base_pose = {}
first_frame = ""
pcd_path = ""


CAMERA_MAP = {
    "E": "cam_back_right",
    "F": "cam_front_right",
    "G": "cam_front_left",
    "H": "cam_back_left",
    "I": "cam_front_30",
    "J": "cam_front_60",
    "K": "cam_front_120",
    "L": "cam_back",
}
camera_extrinsic = {}  # camera -> data
camera_intrinsic = {}
camera_distortion = {}
camera_titles = {}


def parse_pcd():
    global pcd_path, base_pose_timestamp
    for file in os.listdir(PCD_PATH):
        if file.endswith(".pcd"):
            pcd_path = os.path.join(PCD_PATH, file)
            base_pose_timestamp = file.split("_")[-1][:-len(".pcd")]
            print("pcd_path", pcd_path)
            print("base_pose_timestamp", base_pose_timestamp)
            break


def parse_pose():
    global base_pose, base_pose_timestamp
    pose_files = os.listdir(POSE_PATH)
    pose_files = [x for x in pose_files if x.endswith(".txt")]
    pose_files.sort()
    for file in pose_files:
        timestamp = file.split("_")[-1][:-len(".txt")]
        filepath = os.path.join(POSE_PATH, file)
        with open(filepath, "r") as fr:
            lines = fr.readlines()
            columns = lines[0].strip()  # lidar_frame x y z roll pitch yaw match_score
            data = lines[1].strip().split(" ")
            # print(split_data)
            poses[timestamp] = {
                "lidar_frame": float(data[0]),
                "x": float(data[1]),
                "y": float(data[2]),
                "z": float(data[3]),
                "roll": float(data[4]),
                "pitch": float(data[5]),
                "yaw": float(data[6]),
                "match_score": float(data[7]),
                "lidar_frame_time": float(data[8])
            }
            if timestamp == base_pose_timestamp:
                base_pose = poses[timestamp]
                print("base_pose", base_pose)


def get_pose(p):
    return [p["x"], p["y"], p["z"], p["yaw"], -p["pitch"], p["roll"]]


def pose_to_mat(pose):
    # pose is like [x, y, z, yaw, pitch, roll] or [x, y, z, qx, qy, qy, qw]
    if len(pose) == 6:
        rotation = R.from_euler("ZYX", pose[3:], degrees=True)
    elif len(pose) == 7:
        rotation = R.from_quat(pose[3:])
    else:
        raise Exception("wrong pose" + str(pose))
    mat = rotation.as_matrix()
    mat44 = np.zeros((4, 4))
    mat44[:3, :3] = mat
    mat44[0, 3] = pose[0]
    mat44[1, 3] = pose[1]
    mat44[2, 3] = pose[2]
    mat44[3, 3] = 1
    return mat44


def mat_to_pose(mat):
    if len(mat) != 4 and len(mat[0]) != 4:
        raise Exception("mat should have 4*4 elements")
    rotation = R.from_matrix(mat[:3, :3])
    quat = rotation.as_quat()
    position = mat[:3, 3]
    return position.tolist() + quat.tolist()


def parse_params():
    intrinsic_extrinsic_files = os.listdir(INTRINSIC_EXTRINSIC_PATH)
    intrinsic_extrinsic_files = [f for f in intrinsic_extrinsic_files if f.endswith(".json")]
    for file in intrinsic_extrinsic_files:
        filename = file[:-len(".json")]
        camera = CAMERA_MAP[filename]
        filepath = os.path.join(INTRINSIC_EXTRINSIC_PATH, file)
        with open(filepath, "r") as fr:
            data = json.load(fr)
            camera_intrinsic[camera] = data["intrinsic"]
            camera_extrinsic[camera] = data["extrinsic"]
    # don't need to process distortion
    dist_file = os.path.join(DISTORTION_PATH, "gq_calib.json")
    with open(dist_file, "r") as fr:
        data = json.load(fr)
        camera_data = data["camera"]
        for cam_data in camera_data:
            camera_id = cam_data["id"]
            if camera_id in CAMERA_MAP:
                camera = CAMERA_MAP[camera_id]
                camera_titles[camera] = cam_data["position"]
            # cam_config = cam_data["config"]
            # dist_type = 0
            # data = [cam_config["k1"], cam_config["k2"], cam_config["pq"], cam_config["p2"]]
            # if cam_config["fish"] == 1:
            #     dist_type = 2
            #     data = [cam_config["k1"], cam_config["k2"], cam_config["k3"], cam_config["k4"]]
            # camera_distortion[camera] = {
            #     "type": dist_type,
            #     "data": data,
            # }


def get_cam_transforms(camera, pose_transform):
    extrinsic = camera_extrinsic[camera]
    assert len(extrinsic) == 16, Exception("wrong extrinsic")
    extrinsic_mat = np.array(extrinsic).reshape((4, 4))
    # extrinsic_mat = np.linalg.inv(extrinsic_mat)
    rot = np.array([0, -1, 0, 0, 0, 0, -1, 0, 1, 0, 0, 0, 0, 0, 0, 1]).reshape(4, 4)
    extrinsic_mat = rot.dot(extrinsic_mat)

    intrinsic = camera_intrinsic[camera]
    assert len(intrinsic) == 9, Exception("wrong intrinsic")
    intrinsic_data = [intrinsic[0], intrinsic[4], intrinsic[2], intrinsic[5]]  # [K[0][0], K[1][1], K[0][2], K[1][2]]

    # distortion = camera_distortion[camera]
    # assert len(distortion) == 4, Exception("wrong distortion")
    # distortion_data = [0] + distortion[:4]
    # if distortion["type"] == 2:
    #     distortion_data[0] = 2

    return {
        "name": camera,
        "title": camera_titles[camera],
        "transforms": [
            {
                "type": 0,  # matrix
                "column_cnt": 4,
                "data": pose_transform.flatten().tolist(),
            },
            {
                "type": 0,  # matrix
                "column_cnt": 4,
                "data": extrinsic_mat.flatten().tolist(),
            },
            {
                "type": 2,  # intrinsic
                "column_cnt": 1,
                "data": intrinsic_data,
            },
            # {
            #     "type": 3,  # distortion
            #     "column_cnt": 1,
            #     "data": distortion_data,
            # }
        ]
    }


def parse_files_and_write_params(copy_file=False):
    global pcd_path, first_frame
    raw_data_dirs = os.listdir(RAW_DATA_PATH)
    cameras = [d for d in raw_data_dirs if d.startswith("undistorted_")]
    assert len(cameras) == 8, Exception("wrong cameras")
    # parse files
    frames = set()
    camera_names = set()
    for camera in cameras:
        print("Processing camera", camera)
        camera_name = camera[len("undistorted_"):]
        camera_names.add(camera_name)
        camera_path = os.path.join(RAW_DATA_PATH, camera)
        images = os.listdir(camera_path)
        images = [x for x in images if x.endswith(".jpeg")]
        images.sort()
        for img in images:
            # img name is like "xlab_010_cam_back_2023_10_12_15_11_51_1697094916425.jpeg"
            frame = img.split("_")[-1][:-len(".jpeg")]
            frames.add(frame)
            if first_frame == "":
                first_frame = frame
            if copy_file:
                img_path = os.path.join(camera_path, img)
                dst_path = os.path.join(OUTPUT, frame, camera_name + ".jpg")
                # print(img_path, dst_path)
                tools.copy_file_v2(img_path, dst_path)
    # write params
    result = {
        "lidars": {"lidar": {"name": "lidar"}},
        "cameras": {}
    }
    frames = list(frames)
    frames.sort()
    camera_names = list(camera_names)
    camera_names.sort()
    for frame in frames:
        pose = poses[frame]
        pose_mat = pose_to_mat(get_pose(pose))
        base_pose_mat = pose_to_mat(get_pose(base_pose))
        relative_pose_mat = np.linalg.inv(base_pose_mat).dot(pose_mat)
        relative_pose = mat_to_pose(relative_pose_mat)
        result["lidars"]["lidar"]["pose"] = {"pose": relative_pose}
        pose_transform = np.linalg.inv(pose_mat).dot(base_pose_mat)
        for camera in camera_names:
            result["cameras"][camera] = get_cam_transforms(camera, pose_transform)
        param_file_path = os.path.join(OUTPUT, frame, "params.json")
        with open(param_file_path, "w", encoding='utf-8') as fw:
            json.dump(result, fw, ensure_ascii=False)
    # write pcd
    if pcd_path == "" and first_frame == "":
        raise Exception("No pcd file found")
    elif copy_file:
        print("Copying pcd to the first frame {}".format(first_frame))
        dst_path = os.path.join(OUTPUT, first_frame, "lidar.pcd")
        tools.copy_file_v2(pcd_path, dst_path)


if __name__ == "__main__":
    parse_pcd()
    parse_pose()
    parse_params()
    parse_files_and_write_params(copy_file=True)

