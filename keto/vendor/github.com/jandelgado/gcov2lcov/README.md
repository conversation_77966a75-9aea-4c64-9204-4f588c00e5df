# gcov2lcov

[![Build Status](https://github.com/jandelgado/gcov2lcov/workflows/run%20tests/badge.svg)](https://github.com/jandelgado/gcov2lcov/actions?workflow=run%20tests)
[![Coverage Status](https://coveralls.io/repos/github/jandelgado/gcov2lcov/badge.svg?branch=master)](https://coveralls.io/github/jandelgado/gcov2lcov?branch=master)
[![Go Report Card](https://goreportcard.com/badge/github.com/jandelgado/gcov2lcov)](https://goreportcard.com/report/github.com/jandelgado/gcov2lcov)

Convert golang test coverage to lcov format (which can for example be uploaded
to coveralls).

See [gcov2lcov-action](https://github.com/jandelgado/gcov2lcov-action)
for a github action which uses this tool.

## Credits

This tool is based on [covfmt](https://github.com/ricallinson/covfmt) and
uses some parts of [goveralls](https://github.com/mattn/goveralls).

## Installation

```
$ go get -u github.com/jandelgado/gcov2lcov
```

## Usage

```
Usage of ./gcov2lcov:
  -infile string
    	go coverage file to read, default: <stdin>
  -outfile string
    	lcov file to write, default: <stdout>
  -use-absolute-source-path
    	use absolute paths for source file in lcov output, default: false
```

### Example

```sh
$ go test -coverprofile=coverage.out && \
gcov2lcov -infile=coverage.out -outfile=coverage.lcov
```

## Build and Test

* `make test`  to run tests
* `make build` to build binary in `bin/` directory

## Tracefile format reference

The following desription is taken from the [geninfo
manpage](http://ltp.sourceforge.net/coverage/lcov/geninfo.1.php) of the [lcov
homepage](http://ltp.sourceforge.net/coverage/lcov/):

```
A tracefile is made up of several human-readable lines of text, divided into sections. If available, a tracefile begins with the testname which is stored in the following format:

TN:<test name>

For each source file referenced in the .da file, there is a section containing filename and coverage data:

SF:<absolute path to the source file>

Following is a list of line numbers for each function name found in the source file:

FN:<line number of function start>,<function name>

Next, there is a list of execution counts for each instrumented function:

FNDA:<execution count>,<function name>

This list is followed by two lines containing the number of functions found and hit:

FNF:<number of functions found> FNH:<number of function hit>

Branch coverage information is stored which one line per branch:

BRDA:<line number>,<block number>,<branch number>,<taken>

Block number and branch number are gcc internal IDs for the branch. Taken is either '-' if the basic block containing the branch was never executed or a number indicating how often that branch was taken.

Branch coverage summaries are stored in two lines:

BRF:<number of branches found> BRH:<number of branches hit>

Then there is a list of execution counts for each instrumented line (i.e. a line which resulted in executable code):

DA:<line number>,<execution count>[,<checksum>]

Note that there may be an optional checksum present for each instrumented line. The current geninfo implementation uses an MD5 hash as checksumming algorithm.

At the end of a section, there is a summary about how many lines were found and how many were actually instrumented:

LH:<number of lines with a non-zero execution count> LF:<number of instrumented lines>

Each sections ends with:

end_of_record

In addition to the main source code file there are sections for all #included files which also contain executable code.

Note that the absolute path of a source file is generated by interpreting the contents of the respective .bb file (see gcov (1) for more information on this file type). Relative filenames are prefixed with the directory in which the .bb file is found.

Note also that symbolic links to the .bb file will be resolved so that the actual file path is used instead of the path to a link. This approach is necessary for the mechanism to work with the /proc/gcov files.

```

## Author

Jan Delgado

## License

MIT
