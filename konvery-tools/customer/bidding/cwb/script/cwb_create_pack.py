import os
import shutil
import sys

sys.path.append('.')
from customer.common import tools

code = 0
package_index = 0
pcd_image_map = {}
structure_data = {}
camera_para_map = {}
camera_dict = {"front": {"name": "FRAME_CAMERA360_FRONT", "camera_name": "前视"},
               "rear": {"name": "FRAME_CAMERA360_REAR", "camera_name": "后视"}}
category_test_in_map = {}


def create_structure_json_file(json_data):
    global structure_data
    remove_list = ["token", "prev", "next", "elem", "recordname", "conf", "timestamp"]
    pcd_name = json_data["recordname"].split("/")[-1]
    structure_data[pcd_name] = {"timestamp": json_data["timestamp"], "recordname": pcd_name}
    for key in json_data.keys():
        if key in remove_list or json_data[key] == "":
            continue
        # keep only valid camera files
        structure_data[pcd_name][key] = json_data[key].split("/")[-1]
    structure_data[pcd_name]["elem"] = json_data["elem"]
    structure_data[pcd_name]["conf"] = json_data["conf"]


def get_code_msg(msg_code):
    msg = "失败"
    if 0 == msg_code:
        msg = "成功"
    return msg


def create_test_in_config(output_dir, pcd_list):
    test_in_config_filename = output_dir + os.sep + "config.json"
    cameras = {}
    sensor_params = {}
    config = {
        "camera": cameras,
        "data_type": "single_fusion_pointcloud",
        "sensor_params": sensor_params,
    }
    for cam in camera_para_map.keys():
        cam = cam.lower()
        cameras[cam] = camera_dict[cam]["camera_name"]

    # for pcd in pcd_list:
    #     pcd = pcd.replace(".pcd", "")
    #     sensor_params[pcd] = {}
    #     for cam in camera_dict.keys():
    #         # add camera parameters here
    #         sensor_params[pcd][cam] = {}
    #         extrinsic = pcd_image_map[pcd][cam]["extrinsic"]
    #         intrinsic = pcd_image_map[pcd][cam]["intrinsic"]
    #         distortion = pcd_image_map[pcd][cam]["distortion"]
    #
    #         sensor_params[pcd][cam] = {
    #             "camera_model": "pinhole",
    #             "extrinsic": extrinsic.tolist(),
    #             "fx": intrinsic[0],
    #             "fy": intrinsic[1],
    #             "cx": intrinsic[2],
    #             "cy": intrinsic[3],
    #             "k1": distortion[0],
    #             "k2": distortion[1],
    #             "k3": distortion[2],
    #             "p1": distortion[3],
    #             "p2": distortion[4]
    #         }
    with open(test_in_config_filename, "w") as f:
        tools.write_json_to_file(config, f, indent=4)


def copy_to_new_dir(input_dir, pcd_list, output_dir):
    """
        拷贝原始文件到分包后的目录
    """
    global code

    pre_label_dir = output_dir + os.sep + "pre_label"
    camera_dir = output_dir + os.sep + "camera"
    lidar_dir = output_dir + os.sep + "lidar"

    tools.check_dir(camera_dir)
    tools.check_dir(lidar_dir)
    tools.check_dir(pre_label_dir)

    for cam in camera_para_map.keys():
        tools.check_dir(camera_dir + os.sep + cam)

    for pcd in pcd_list:
        if pcd.name not in pcd_image_map.keys():
            code = 1
            print(f"!!!ERROR: {pcd.name} is not found in pcd_image_map")
            continue

        src_pcd = pcd.path
        dst_pcd = lidar_dir + os.sep + pcd.name
        add_pre_label(pcd.name, pre_label_dir)

        for cam in camera_para_map.keys():
            src_img = input_dir + os.sep + pcd_image_map[pcd.name][cam]
            dst_img = camera_dir + os.sep + cam + os.sep + pcd.name.replace(".pcd", ".jpg")
            shutil.copy2(src_img, dst_img)

        shutil.copy(src_pcd, dst_pcd)

    # create_test_in_config(output_dir)


def add_pre_label(pcd_name, pre_label_dir):
    lidar = []
    pre_label_data = {
        "label_meta": {"mark_status": 0, "global": {}},
        "lidar": lidar
    }

    for elem in structure_data[pcd_name]["elem"]:
        # "token": "0",
        # "sub-token": "null",
        # "flag": "False",
        # "class": "CYCLIST",
        # "sparse": 0,
        # "position": {
        #     "x": "-6.9702053",
        #     "y": "13.675761",
        #     "z": "-1.5323235"
        # },
        # "size": {
        #     "length": "1.4644296",
        #     "width": "0.4936741",
        #     "height": "1.014062"
        # },
        # "yaw": "1.322579"

        annotation = {"type": "volume",
                      "data": {"position": {"x": 0.0, "y": 0.0, "z": 0.0}, "dimension": {"l": 0, "w": 0, "h": 0},
                               "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}}
        annotation["data"]["position"] = {"x": float(elem["position"]["x"]), "y": float(elem["position"]["y"]),
                                          "z": float(elem["position"]["z"])}
        annotation["data"]["dimension"] = {"l": float(elem["size"]["length"]), "w": float(elem["size"]["width"]),
                                           "h": float(elem["size"]["height"])}
        annotation["data"]["rotation"] = {"x": 0, "y": 0, "z": float(elem["yaw"])}
        box = {"class": category_test_in_map[elem["class"].lower()],
               "attrs": {"flag": [elem["flag"]]},
               "track_id": int(elem["token"]),
               "group_id": 0,
               "annotation": annotation
               }
        lidar.append(box)
    pre_label_file = pre_label_dir + os.sep + pcd_name.replace(".pcd", ".json")
    tools.write_json_file(data=pre_label_data, file=pre_label_file, indent=4)


def get_camera_map(camera_para_file):
    global camera_para_map
    data = tools.get_json_data(camera_para_file)

    for cam in camera_dict.keys():
        name = camera_dict[cam]["name"]
        camera_para_map[cam] = {}
        for elem in data["connectedFrameTransContainer"]["connectedFrameTrans"]:
            if elem["targetFrame"] == name:
                camera_para_map[cam]["extrinsic"] = elem["trans"]
                break
        camera_para_map[cam]["intrinsic"] = data["cameraCalibrationIntrinsicMap"]["cameraParamMap"][name][
            "intrisicParam"]
        camera_para_map[cam]["distortion"] = data["cameraCalibrationIntrinsicMap"]["cameraParamMap"][name][
            "distortionParam"]


def create_pack(input_dir, output_dir, pack_size):
    """
        对原始文件进行分包
    """
    global package_index
    global pcd_image_map
    global camera_para_map
    camera_para_file = input_dir + os.sep + "params.json"
    get_camera_map(camera_para_file)
    label_dir = input_dir + os.sep + "label"
    for elem in tools.get_json_files(label_dir):
        data = tools.get_json_data(elem.path)
        create_structure_json_file(data)
        pcd_name = data["recordname"].split("/")[-1]
        pcd_image_map[pcd_name] = {"front": data["imagePath_front"],
                                   "rear": data["imagePath_rear"],
                                   "pre_label": data["elem"]
                                   }

    sub_pcd_list = []
    pcd_input_dir = input_dir + os.sep + "processed_pointcloud"
    pcd_list = tools.get_file_by_extension(pcd_input_dir, ".pcd")
    total = len(pcd_list)

    for index, pcd in enumerate(pcd_list, start=1):
        sub_pcd_list.append(pcd)

        if index % pack_size == 0 or index == total:
            package_index += 1
            seg_output_dir = output_dir + os.sep + "segment_" + str(package_index)
            tools.check_dir(seg_output_dir)

            # copy data
            copy_to_new_dir(input_dir, sub_pcd_list, seg_output_dir)
            sub_pcd_list = []

    print(f"Total pcd {total}, pack size: {pack_size}, package count: {package_index}")


def get_category_mapping(test_in_config):
    global category_test_in_map
    data = tools.get_json_data(test_in_config)
    for elem_type in data["properties"][0]["class"]:
        category_test_in_map[elem_type["pname"].lower()] = elem_type["pname"]


def run(input_dir, output_dir, test_in_config, pack_size):
    """
       主函数
    """
    get_category_mapping(test_in_config)

    input_root = tools.unzip(input_dir)
    tools.check_dir(output_dir)

    structure_json = output_dir + os.sep + "保留这个文件-导出时会用到.json"
    create_pack(input_root, output_dir, pack_size=pack_size)

    tools.write_json_file(data=structure_data, file=structure_json, indent=4)
    trans_result = {"code": code, "output": output_dir, "err_msg": get_code_msg(code)}

    print(trans_result)
    return trans_result


"""
    项目： CWB项目 带预标
    对接平台：云测新平台 
    功能： 将客户的文件, 标注结果转换成云测的格式， 并按数量分包
    输出： 分包后的数据
    开发： Justin
    问题反馈：https://qcjmmm41aj.feishu.cn/sheets/C1tfspMz1h6OMZtg5gLcN8S8nmf?from=from_copylink
"""
if __name__ == "__main__":

    args = tools.get_args()

    if args.input == "../../input/test":
        args.input = "/Users/<USER>/Downloads/Convert/CWB/ZBJ069_rover_sample_2023-08-08_15_03_55_267561023"
        args.out = "/Users/<USER>/Downloads/cwb-23d-pack-output"
        args.pcd = "/Users/<USER>/Project/konvery-tools/customer/bidding/cwb/config/cwb-test-in-23d-config.json"

    run(input_dir=args.input, output_dir=args.out, test_in_config=args.pcd, pack_size=40)
