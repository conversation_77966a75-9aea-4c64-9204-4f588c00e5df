// Copyright © 2022 Ory Corp
// SPDX-License-Identifier: Apache-2.0

package check_test

import (
	"context"
	"os"
	"strings"
	"testing"

	"github.com/gofrs/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/goleak"

	"github.com/ory/keto/internal/check"
	"github.com/ory/keto/internal/check/checkgroup"
	"github.com/ory/keto/internal/driver/config"
	"github.com/ory/keto/internal/namespace"
	"github.com/ory/keto/internal/schema"
)

func TestSubjectExpansion(t *testing.T) {
	var rawns = `
	import { Namespace, SubjectSet, Context } from '@ory/keto-namespace-types';
	
	class IamRole implements Namespace {
	  related: {
		perms: SubjectSet<IamRole, "perms">[]
	  }
	}`

	nss, err := schema.Parse(rawns)
	require.True(t, err == nil)
	namespaces := make([]*namespace.Namespace, len(nss))
	for i, ns := range nss {
		ns := ns
		namespaces[i] = &ns
	}

	reg := newDepsProvider(t, namespaces)
	reg.Logger().Logger.SetLevel(logrus.InfoLevel)

	insertFixtures(t, reg.RelationTupleManager(), []string{
		"IamRole:a#perms@IamRole:b#perms",
		"IamRole:b#perms@IamRole:c#perms",
		"IamRole:c#perms@IamRole:d#perms",
		"IamRole:d#perms@IamRole:e#perms",
		"IamRole:e#perms@get",
	})

	testCases := []struct {
		query         string
		expected      checkgroup.Result
		expectedPaths []path
	}{{
		query:    "IamRole:d#perms@get",
		expected: checkgroup.ResultIsMember,
	}, {
		query:    "IamRole:c#perms@get",
		expected: checkgroup.ResultIsMember,
	}, {
		query:    "IamRole:b#perms@get",
		expected: checkgroup.ResultIsMember,
	}, {
		query:    "IamRole:a#perms@get",
		expected: checkgroup.ResultIsMember,
	}}

	t.Run("suite=testcases", func(t *testing.T) {
		ctx := context.Background()
		reg.Config(ctx).Set(config.KeyLimitMaxReadDepth, 10)
		e := check.NewEngine(reg)
		defer goleak.VerifyNone(t, goleak.IgnoreCurrent())

		for _, tc := range testCases {
			t.Run("case="+tc.query, func(t *testing.T) {
				rt := tupleFromString(t, tc.query)

				res := e.CheckRelationTuple(ctx, rt, 100)
				require.NoError(t, res.Err)
				t.Logf("tree:\n%s", res.Tree)
				assert.Equal(t, tc.expected.Membership, res.Membership)

				if len(tc.expectedPaths) > 0 {
					for _, path := range tc.expectedPaths {
						assertPath(t, path, res.Tree)
					}
				}
			})
		}

		e.UseCache(true)
		for _, tc := range testCases {
			t.Run("case=use_cache:"+tc.query, func(t *testing.T) {
				rt := tupleFromString(t, tc.query)

				res := e.CheckRelationTuple(ctx, rt, 100)
				require.NoError(t, res.Err)
				t.Logf("tree:\n%s", res.Tree)
				assert.Equal(t, tc.expected.Membership, res.Membership)

				if len(tc.expectedPaths) > 0 {
					for _, path := range tc.expectedPaths {
						assertPath(t, path, res.Tree)
					}
				}
			})
		}
	})
}

func (p persister) LoadSysObjNameMap(ctx context.Context, filter func(string) bool) (map[uuid.UUID]string, error) {
	names := map[uuid.UUID]string{}
	for _, v := range uuidMapping {
		if strings.HasPrefix(v, "sys/") {
			names[toUUID(v)] = v
		}
	}
	return names, nil
}

func TestUsersetRewrites2(t *testing.T) {
	nss, err := schema.Parse(rawns)
	require.True(t, err == nil)
	namespaces := make([]*namespace.Namespace, len(nss))
	for i, ns := range nss {
		ns := ns
		namespaces[i] = &ns
	}

	reg := newDepsProvider(t, namespaces)
	reg.Logger().Logger.SetLevel(logrus.TraceLevel)

	insertFixtures(t, reg.RelationTupleManager(), []string{
		"IamUser:sample#policies@IamPolicy:sys/admin",
		"IamRole:admin#perms@IamRole:iam.owner#perms",
		"IamRole:iam.owner#perms@IamRole:IamUser.owner#perms",
		"IamRole:IamUser.owner#perms@IamRole:IamUser.editor#perms",
		"IamRole:IamUser.editor#<EMAIL>",

		"IamPolicy:sys/admin#roles@IamRole:admin",
		"IamPolicy:sys/admin#users@IamGroup:sys/root#members",
		"IamGroup:sys/root#members@IamUser:aaaa",
		"IamGroup:sys/root#members@IamUser:bbbb",
	})

	testCases := []struct {
		query         string
		expected      checkgroup.Result
		expectedPaths []path
	}{{
		query:    "IamUser:sample#getPrivacy@IamUser:aaaa",
		expected: checkgroup.ResultIsMember,
	}, {
		query:    "IamUser:sample#getPrivacy@IamUser:bbbb",
		expected: checkgroup.ResultIsMember,
	}, {
		query:    "IamUser:sample#getPrivacy@IamUser:cccc",
		expected: checkgroup.ResultNotMember,
	}}

	t.Run("suite=testcases", func(t *testing.T) {
		ctx := context.Background()
		reg.Config(ctx).Set(config.KeyLimitMaxReadDepth, 10)
		e := check.NewEngine(reg)
		defer goleak.VerifyNone(t, goleak.IgnoreCurrent())

		for _, tc := range testCases {
			t.Run("case="+tc.query, func(t *testing.T) {
				rt := tupleFromString(t, tc.query)

				res := e.CheckRelationTuple(ctx, rt, 100)
				require.NoError(t, res.Err)
				t.Logf("tree:\n%s", res.Tree)
				assert.Equal(t, tc.expected.Membership, res.Membership)

				if len(tc.expectedPaths) > 0 {
					for _, path := range tc.expectedPaths {
						assertPath(t, path, res.Tree)
					}
				}
			})
		}
	})

	t.Run("suite=testcases-use_cache", func(t *testing.T) {
		os.Setenv("DUMP_CACHE", "true")
		// os.Setenv("SHOW_DB_QUERY", "true")
		os.Setenv("SHOW_OBJ_NAME", "true")
		ctx := context.Background()
		reg.Config(ctx).Set(config.KeyLimitMaxReadDepth, 10)
		e := check.NewEngine(reg)
		e.UseCache(true)
		defer goleak.VerifyNone(t, goleak.IgnoreCurrent())

		for _, tc := range testCases {
			t.Run("case="+tc.query, func(t *testing.T) {
				rt := tupleFromString(t, tc.query)

				res := e.CheckRelationTuple(ctx, rt, 100)
				require.NoError(t, res.Err)
				t.Logf("tree:\n%s", res.Tree)
				assert.Equal(t, tc.expected.Membership, res.Membership)

				if len(tc.expectedPaths) > 0 {
					for _, path := range tc.expectedPaths {
						assertPath(t, path, res.Tree)
					}
				}
			})
		}
	})
}

var rawns = `
import { Namespace, SubjectSet, Context } from '@ory/keto-namespace-types';

class IamPerm implements Namespace {}

// IamRole ID:
// for global roles: <role-name>
// for org roles: <org-uid>.<role-name>
class IamRole implements Namespace {
  related: {
    parents: IamGroup[]
    policies: IamPolicy[]
    perms: (IamPerm | SubjectSet<IamRole, "perms">)[]
  }

  permits = {
    has_perm: (ctx: Context, perm: string): boolean => this.related.perms.includes(perm),

    check: (ctx: Context, perm: string): boolean => this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),

    get: (ctx: Context): boolean => this.permits.check(ctx, "IamRole.get"),
    list: (ctx: Context): boolean => this.permits.check(ctx, "IamRole.list"),
    update: (ctx: Context): boolean => this.permits.check(ctx, "IamRole.update"),
    delete: (ctx: Context): boolean => this.permits.check(ctx, "IamRole.delete"),
    create: (ctx: Context): boolean => this.permits.check(ctx, "IamRole.create"),
    getPolicy: (ctx: Context): boolean => this.permits.check(ctx, "IamRole.getPolicy"),
    setPolicy: (ctx: Context): boolean => this.permits.check(ctx, "IamRole.setPolicy"),
  }
}

// for system policies: sys/<name>
// for per resource policies: <resource-ns>/<resource-name>.<role-name>
class IamPolicy implements Namespace {
  related: {
    roles: IamRole[] // only one role is allowed
    users: (IamUser | SubjectSet<IamGroup, "members">)[]
  }

  permits = {
    // check describes if the policy binds the subject and the permission
    check: (ctx: Context, perm: string): boolean => this.related.users.includes(ctx.subject) &&
      this.related.roles.traverse((p) => p.permits.has_perm(ctx, perm)),
  }
}

// IamGroup ID: <group-uid>
class IamGroup implements Namespace {
  related: {
    parents: IamGroup[]
    policies: IamPolicy[]
    members: (IamUser | SubjectSet<IamGroup, "members">)[]
  }

  permits = {
    check: (ctx: Context, perm: string): boolean => this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),

    get: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.get"),
    list: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.list"),
    update: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.update"),
    delete: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.delete"),
    create: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.create"),
    getPolicy: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.getPolicy"),
    setPolicy: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.setPolicy"),

    listMember: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.listMember"),
    addMember: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.addMember"),
    deleteMember: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.deleteMember"),
    setMemberRole: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.setMemberRole"),

    stat: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.stat"),
  }
}

// IamUser ID: <user-uid>
class IamUser implements Namespace {
  related: {
    parents: IamGroup[]
    policies: IamPolicy[]
    owners: IamUser[] // one is always the owner of oneself
  }

  permits = {
    check: (ctx: Context, perm: string): boolean => this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.owners.includes(ctx.subject),
    get: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.get"),
    list: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.list"),
    update: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.update"),
    delete: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.delete"),
    create: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.create"),
    getPolicy: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.getPolicy"),
    setPolicy: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.setPolicy"),
    getPrivacy: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.getPrivacy"),

    stat: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.stat"),
  }
}
`
