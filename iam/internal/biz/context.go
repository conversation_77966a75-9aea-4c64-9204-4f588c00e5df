package biz

import (
	context "context"

	"github.com/samber/lo"
)

type userContextKey struct{}

// ContextUser holds the user information in the context.
type ContextUser struct {
	// the effective user
	*User
	// the real user in an assume situation
	AssumeBy *User
}

func (o *ContextUser) IsEmpty() bool   { return o == nil || o.User == nil || o.User.ID == 0 }
func (o *ContextUser) RealUser() *User { return lo.Ternary(o.AssumeBy != nil, o.AssumeBy, o.User) }

// NewCtxWithUser wraps a user, and an optionally assume-by user in the context.
func NewCtxWithUser(ctx context.Context, user *User, assumeBy ...*User) context.Context {
	var by *User
	if len(assumeBy) > 0 {
		by = assumeBy[0]
	}
	return context.WithValue(ctx, userContextKey{}, &ContextUser{User: user, AssumeBy: by})
}

// UserFromCtx retrieves the user from the context.
func UserFromCtx(ctx context.Context) *ContextUser {
	user, _ := ctx.Value(userContextKey{}).(*ContextUser)
	return user
}
