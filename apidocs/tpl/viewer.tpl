
<!--
Copyright 2018 Stoplight, Inc.

Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.

Origin: https://github.com/stoplightio/elements/blob/main/examples/bootstrap/index.html
-->

<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Elements in Twitter Bootstrap</title>

    <!-- Elements: Web Component
      https://unpkg.com/@stoplight/elements/web-components.min.js
      https://unpkg.com/@stoplight/elements/styles.min.css
     -->
    <script src="/res/web-components.min.js"></script>
    <link rel="stylesheet" href="/res/styles.min.css">

    <!-- Twitter Bootstrap: https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css -->
    <link rel="stylesheet" href="/res/bootstrap.min.css">
    <!-- Twitter Bootstrap: Sticky Footer Example: https://getbootstrap.com/docs/4.5/examples/sticky-footer-navbar/sticky-footer-navbar.css -->
    <link rel="stylesheet" href="/res/sticky-footer-navbar.css">

    <style>
      body {
        display: flex;
        flex-direction: column;
        height: 100vh;
      }

      main {
        flex: 1 0 0;
        overflow: hidden;
      }
    </style>
  </head>

  <body>

    <header>
      <!-- Fixed navbar -->
      <nav class="navbar navbar-expand-md navbar-dark bg-dark">
        <a class="navbar-brand" href="#">Fixed navbar</a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
          <ul class="navbar-nav mr-auto">
            <li class="nav-item active">
              <a class="nav-link" href="#">Home <span class="sr-only">/apidocs/view</span></a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#">Link</a>
            </li>
            <li class="nav-item">
              <a class="nav-link disabled" href="#">Disabled</a>
            </li>
          </ul>
        </div>
      </nav>
    </header>

    <!-- Begin page content -->
    <main role="main">
      <elements-api
        apiDescriptionUrl="{{ .docUrl }}"
        router="hash"
      />
    </main>

    <footer class="footer">
      <div class="container">
        <span class="text-muted">Awesome footer goes here.</span>
      </div>
    </footer>
  </body>
</html>
