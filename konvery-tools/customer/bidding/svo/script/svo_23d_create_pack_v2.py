import os
import numpy as np
import math
import pandas as pd
import sys
import shutil
import yaml
sys.path.append('.')
from customer.common import tools
from customer.common import ply2pcd


camera_names = [
    'CAMERA_FRONT_CENTER',
    'CAMERA_FRONT_CENTER_TELE',
    'CAMERA_FRONT_LEFT',
    'CAMERA_FRONT_RIGHT',
    'CAMERA_REAR_CENTER',
    'CAMERA_REAR_LEFT',
    'CAMERA_REAR_RIGHT'
]


def read_yaml_file(file):
    with open(file, 'r') as fr:
        data = yaml.safe_load(fr)
        return data


def get_nearest_frame(src, dsts):
    distance = math.inf
    target = None
    if isinstance(src, str):
        src = float(src)
    for dst in dsts:
        if abs(dst-src) < distance:
            distance = abs(dst-src)
            target = dst
    return target


def construct_pose(pose_file, frame_timestamps):
    poses = dict()
    df = pd.read_csv(pose_file)
    for frame_timestamp in frame_timestamps:
        target_timestamp = get_nearest_frame(frame_timestamp, df['timestamp'])
        target_pose = df[df['timestamp'] == target_timestamp]
        rotation = target_pose['pose_rotation_quaternion']
        rotation = np.array(rotation.values[0].strip('[]').split(','), dtype=np.float32)
        translation = target_pose['pose_translation']
        translation = np.array(translation.values[0].strip('[]').split(','), dtype=np.float32)
        pose_mat = tools.pose_to_mat(rotation.tolist() + translation.tolist())
        poses[str(frame_timestamp)] = pose_mat.T.flatten().tolist()
    return poses


def construct_config(input_dir):
    params_dirs = [os.path.dirname(_) for _ in tools.find_dir_by_pre_name(os.path.join(input_dir, 'params'), 'extrinsic')]
    assert len(params_dirs) == 1, 'find more than one params dir'
    params_dir = params_dirs[0]
    sensor_params = dict()
    for camera_name in camera_names:
        intrinsic_yaml_file = os.path.join(params_dir, 'intrinsics', f'{camera_name}_intrinsic.yaml')
        extrinsic_yaml_file = os.path.join(params_dir, 'extrinsics', f'{camera_name}_extrinsic.yaml')

        intrinsic_data = read_yaml_file(intrinsic_yaml_file)
        extrinsic_data = read_yaml_file(extrinsic_yaml_file)
        extrinsic = tools.pose_to_mat(
            np.array([
                extrinsic_data['x'],
                extrinsic_data['y'],
                extrinsic_data['z'],
                extrinsic_data['qx'],
                extrinsic_data['qy'],
                extrinsic_data['qz'],
                extrinsic_data['qw']
            ])
        )
        assert intrinsic_data['model'] == 'pinhole', f'{camera_name} model not support'
        sensor_params[camera_name] = {
            'camera_model': intrinsic_data['model'],
            'extrinsic': np.linalg.inv(extrinsic).tolist(),
            'fx': intrinsic_data['fx'],
            'fy': intrinsic_data['fy'],
            'cx': intrinsic_data['cx'],
            'cy': intrinsic_data['cy'],
            'k1': intrinsic_data['k1'],
            'k2': intrinsic_data['k2'],
            'p1': intrinsic_data['p1'],
            'p2': intrinsic_data['p2'],
            'k3': intrinsic_data['k3'],
            'k4': intrinsic_data['k4'],
            'k5': intrinsic_data['k5'],
            'k6': intrinsic_data['k6'],
        }
    config = {
        "data_type": "fusion_pointcloud",
        "sensor_params": sensor_params,
    }
    return config


def process_sub_dir(sub_dir, output_dir):
    out_clip_dir = os.path.join(output_dir, os.path.basename(sub_dir))
    os.mkdir(out_clip_dir)
    out_lidar_dir = os.path.join(out_clip_dir, 'lidar')
    os.mkdir(out_lidar_dir)
    out_cameras_dir = os.path.join(out_clip_dir, 'camera')
    os.mkdir(out_cameras_dir)
    for camera_name in camera_names:
        os.mkdir(os.path.join(out_cameras_dir, camera_name))
    extract_file = os.path.join(sub_dir, 'extract_result.json')
    extract_datas = tools.get_json_data(extract_file)
    plys = tools.get_file_by_extension(os.path.join(sub_dir, 'processed', 'pandar128'), '.ply')
    assert {i['PANDAR128'] for i in extract_datas['matched']} == {i.name for i in plys}, "pcd and extract file not match"
    for extract_data in extract_datas['matched']:
        ply_src_path = os.path.join(sub_dir, 'PROCESSED', 'PANDAR128', extract_data['PANDAR128'])
        pcd_dst_path = os.path.join(out_lidar_dir, extract_data['PANDAR128'].replace('.ply', '.pcd'))
        ply2pcd.convert_ply_to_pcd(ply_src_path, pcd_dst_path)
        for camera_name in camera_names:
            src_img = os.path.join(sub_dir, camera_name, extract_data[camera_name])
            dst_img = os.path.join(out_cameras_dir, camera_name, extract_data['PANDAR128'].replace('.ply', '.jpg'))
            shutil.copyfile(src_img, dst_img)
    return [i['PANDAR128'].rsplit('.', 1)[0] for i in extract_datas['matched']]


def run(input_dir, output_dir):
    config = construct_config(input_dir)
    sub_dirs = [os.path.dirname(_) for _ in tools.find_dir_by_pre_name(os.path.join(input_dir, 'data'), 'PROCESSED')]
    for sub_dir in sub_dirs:
        pose_file = os.path.join(sub_dir, 'sycn', 'odom.csv')
        frame_timestamps = process_sub_dir(sub_dir, output_dir)
        poses = construct_pose(pose_file, frame_timestamps)
        config['poses'] = poses
        tools.write_json_file(config, os.path.join(output_dir, os.path.basename(sub_dir), 'config.json'), indent=4, ensure_ascii=False)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
