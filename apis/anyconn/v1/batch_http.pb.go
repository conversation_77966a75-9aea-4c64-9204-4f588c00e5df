// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: anyconn/v1/batch.proto

package anyconn

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationBatchesCancelBatch = "/anyconn.v1.Batches/CancelBatch"
const OperationBatchesCreateBatch = "/anyconn.v1.Batches/CreateBatch"
const OperationBatchesDeleteBatch = "/anyconn.v1.Batches/DeleteBatch"
const OperationBatchesGetAnnoResult = "/anyconn.v1.Batches/GetAnnoResult"
const OperationBatchesGetBatch = "/anyconn.v1.Batches/GetBatch"
const OperationBatchesListBatch = "/anyconn.v1.Batches/ListBatch"
const OperationBatchesSetAnnoResult = "/anyconn.v1.Batches/SetAnnoResult"
const OperationBatchesUpdateBatch = "/anyconn.v1.Batches/UpdateBatch"

type BatchesHTTPServer interface {
	// CancelBatch only platform admin can cancel batches
	CancelBatch(context.Context, *GetBatchRequest) (*emptypb.Empty, error)
	CreateBatch(context.Context, *CreateBatchRequest) (*Batch, error)
	DeleteBatch(context.Context, *DeleteBatchRequest) (*emptypb.Empty, error)
	GetAnnoResult(context.Context, *GetBatchRequest) (*GetBatchAnnoResultReply, error)
	GetBatch(context.Context, *GetBatchRequest) (*Batch, error)
	ListBatch(context.Context, *ListBatchRequest) (*ListBatchReply, error)
	SetAnnoResult(context.Context, *SetBatchAnnoResultRequest) (*emptypb.Empty, error)
	UpdateBatch(context.Context, *UpdateBatchRequest) (*Batch, error)
}

func RegisterBatchesHTTPServer(s *http.Server, srv BatchesHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/batches", _Batches_CreateBatch0_HTTP_Handler(srv))
	r.PATCH("/v1/batches/{batch.uid}", _Batches_UpdateBatch0_HTTP_Handler(srv))
	r.PUT("/v1/batches/{uid}/cancel", _Batches_CancelBatch0_HTTP_Handler(srv))
	r.DELETE("/v1/batches/{uid}", _Batches_DeleteBatch0_HTTP_Handler(srv))
	r.GET("/v1/batches/{uid}", _Batches_GetBatch0_HTTP_Handler(srv))
	r.GET("/v1/batches", _Batches_ListBatch0_HTTP_Handler(srv))
	r.PUT("/v1/batches/{uid}/anyconn-result", _Batches_SetAnnoResult0_HTTP_Handler(srv))
	r.GET("/v1/batches/{uid}/anyconn-result", _Batches_GetAnnoResult0_HTTP_Handler(srv))
}

func _Batches_CreateBatch0_HTTP_Handler(srv BatchesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateBatchRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBatchesCreateBatch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateBatch(ctx, req.(*CreateBatchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Batch)
		return ctx.Result(200, reply)
	}
}

func _Batches_UpdateBatch0_HTTP_Handler(srv BatchesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateBatchRequest
		if err := ctx.Bind(&in.Batch); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBatchesUpdateBatch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateBatch(ctx, req.(*UpdateBatchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Batch)
		return ctx.Result(200, reply)
	}
}

func _Batches_CancelBatch0_HTTP_Handler(srv BatchesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetBatchRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBatchesCancelBatch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CancelBatch(ctx, req.(*GetBatchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Batches_DeleteBatch0_HTTP_Handler(srv BatchesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteBatchRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBatchesDeleteBatch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteBatch(ctx, req.(*DeleteBatchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Batches_GetBatch0_HTTP_Handler(srv BatchesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetBatchRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBatchesGetBatch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetBatch(ctx, req.(*GetBatchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Batch)
		return ctx.Result(200, reply)
	}
}

func _Batches_ListBatch0_HTTP_Handler(srv BatchesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListBatchRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBatchesListBatch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListBatch(ctx, req.(*ListBatchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListBatchReply)
		return ctx.Result(200, reply)
	}
}

func _Batches_SetAnnoResult0_HTTP_Handler(srv BatchesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetBatchAnnoResultRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBatchesSetAnnoResult)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetAnnoResult(ctx, req.(*SetBatchAnnoResultRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Batches_GetAnnoResult0_HTTP_Handler(srv BatchesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetBatchRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBatchesGetAnnoResult)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAnnoResult(ctx, req.(*GetBatchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetBatchAnnoResultReply)
		return ctx.Result(200, reply)
	}
}

type BatchesHTTPClient interface {
	CancelBatch(ctx context.Context, req *GetBatchRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CreateBatch(ctx context.Context, req *CreateBatchRequest, opts ...http.CallOption) (rsp *Batch, err error)
	DeleteBatch(ctx context.Context, req *DeleteBatchRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetAnnoResult(ctx context.Context, req *GetBatchRequest, opts ...http.CallOption) (rsp *GetBatchAnnoResultReply, err error)
	GetBatch(ctx context.Context, req *GetBatchRequest, opts ...http.CallOption) (rsp *Batch, err error)
	ListBatch(ctx context.Context, req *ListBatchRequest, opts ...http.CallOption) (rsp *ListBatchReply, err error)
	SetAnnoResult(ctx context.Context, req *SetBatchAnnoResultRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateBatch(ctx context.Context, req *UpdateBatchRequest, opts ...http.CallOption) (rsp *Batch, err error)
}

type BatchesHTTPClientImpl struct {
	cc *http.Client
}

func NewBatchesHTTPClient(client *http.Client) BatchesHTTPClient {
	return &BatchesHTTPClientImpl{client}
}

func (c *BatchesHTTPClientImpl) CancelBatch(ctx context.Context, in *GetBatchRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/batches/{uid}/cancel"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBatchesCancelBatch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BatchesHTTPClientImpl) CreateBatch(ctx context.Context, in *CreateBatchRequest, opts ...http.CallOption) (*Batch, error) {
	var out Batch
	pattern := "/v1/batches"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBatchesCreateBatch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BatchesHTTPClientImpl) DeleteBatch(ctx context.Context, in *DeleteBatchRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/batches/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBatchesDeleteBatch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BatchesHTTPClientImpl) GetAnnoResult(ctx context.Context, in *GetBatchRequest, opts ...http.CallOption) (*GetBatchAnnoResultReply, error) {
	var out GetBatchAnnoResultReply
	pattern := "/v1/batches/{uid}/anyconn-result"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBatchesGetAnnoResult))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BatchesHTTPClientImpl) GetBatch(ctx context.Context, in *GetBatchRequest, opts ...http.CallOption) (*Batch, error) {
	var out Batch
	pattern := "/v1/batches/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBatchesGetBatch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BatchesHTTPClientImpl) ListBatch(ctx context.Context, in *ListBatchRequest, opts ...http.CallOption) (*ListBatchReply, error) {
	var out ListBatchReply
	pattern := "/v1/batches"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBatchesListBatch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BatchesHTTPClientImpl) SetAnnoResult(ctx context.Context, in *SetBatchAnnoResultRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/batches/{uid}/anyconn-result"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBatchesSetAnnoResult))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BatchesHTTPClientImpl) UpdateBatch(ctx context.Context, in *UpdateBatchRequest, opts ...http.CallOption) (*Batch, error) {
	var out Batch
	pattern := "/v1/batches/{batch.uid}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBatchesUpdateBatch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in.Batch, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
