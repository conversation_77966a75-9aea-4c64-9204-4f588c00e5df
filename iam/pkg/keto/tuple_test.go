package keto

import (
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestParseTuple(t *testing.T) {
	for _, tc := range []struct {
		tuple    *RelationTuple
		expected string
	}{
		{ // full tuple
			tuple: &RelationTuple{
				Namespace: "n",
				Object:    "o",
				Relation:  "r",
				Subject:   NewSubjectId("s"),
			},
			expected: "n:o#r@s",
		},
		{ // skip '#' in subject set when relation is empty
			tuple: &RelationTuple{
				Namespace: "groups",
				Object:    "dev",
				Relation:  "member",
				Subject:   NewSubjectSet("users", "user", ""),
			},
			expected: "groups:dev#member@users:user",
		},
	} {
		t.Run("case="+tc.expected, func(t *testing.T) {
			assert.Equal(t, tc.expected, FormatTuple(tc.tuple))
		})
	}

	for _, tc := range []struct {
		enc      string
		err      string
		expected *RelationTuple
	}{
		{
			enc: "n:o#r@s",
			expected: &RelationTuple{
				Namespace: "n",
				Object:    "o",
				Relation:  "r",
				Subject:   NewSubjectId("s"),
			},
		},
		{
			enc: "n:o#r@n:o#r",
			expected: &RelationTuple{
				Namespace: "n",
				Object:    "o",
				Relation:  "r",
				Subject:   NewSubjectSet("n", "o", "r"),
			},
		},
		{
			enc: "n:o#r@n:o#...",
			expected: &RelationTuple{
				Namespace: "n",
				Object:    "o",
				Relation:  "r",
				Subject:   NewSubjectSet("n", "o", "..."),
			},
		},
		{
			enc: "n:o#r@n:o#",
			expected: &RelationTuple{
				Namespace: "n",
				Object:    "o",
				Relation:  "r",
				Subject:   NewSubjectSet("n", "o", ""),
			},
		},
		{
			enc: "n:o#r@n:o",
			expected: &RelationTuple{
				Namespace: "n",
				Object:    "o",
				Relation:  "r",
				Subject:   NewSubjectSet("n", "o", ""),
			},
		},
		{
			enc: "n:o#r@(n:o#r)",
			expected: &RelationTuple{
				Namespace: "n",
				Object:    "o",
				Relation:  "r",
				Subject:   NewSubjectSet("n", "o", "r"),
			},
		},
		{
			enc: "#dev:@ory#:working:@projects:keto#awesome",
			expected: &RelationTuple{
				Namespace: "#dev",
				Object:    "@ory",
				Relation:  ":working:",
				Subject:   NewSubjectSet("projects", "keto", "awesome"),
			},
		},
		{
			enc: "no-colon#in@this",
			err: "malformed input",
		},
		{
			enc: "no:hash-in@this",
			err: "malformed input",
		},
		{
			enc: "no:at#in-this",
			err: "malformed input",
		},
	} {
		t.Run(fmt.Sprintf("string=%s", tc.enc), func(t *testing.T) {
			actual, err := ParseTuple(tc.enc)
			if err != nil {
				assert.True(t, strings.HasPrefix(err.Error(), tc.err))
			}
			assert.Equal(t, tc.expected, actual)
		})
	}
}
