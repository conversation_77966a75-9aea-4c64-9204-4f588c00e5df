from PIL import Image
import json
import os
import shutil
import sys
from pathlib import Path

import numpy as np
import pandas as pd
import yaml

sys.path.append('../../svo/script')

from customer.common import tools, pypcd
from scipy.spatial.transform import Rotation as R
from customer.common.bin2pcd.pcdtool.all import write_pcd


def convert_pose_to_matrix(x, y, z, qx, qy, qz, qw):
    """ Converts pose (x, y, z, qx, qy, qz, qw) to a 4x4 transformation matrix """
    T = np.eye(4)
    T[:3, :3] = R.from_quat([qx, qy, qz, qw]).as_matrix()
    T[:3, 3] = [x, y, z]
    return T


def euler_to_quaternion(roll, pitch, yaw):
    return R.from_euler('xyz', [roll, pitch, yaw], degrees=False).as_quat()


def listdir(input_dir):
    """wraps for os.listdir but dose not contains mac os system file"""
    mac_file = ['.DS_Store', '__MACOSX']
    return sorted([os.path.join(input_dir, f) for f in os.listdir(input_dir) if f not in mac_file])


def run(input_dir, output_dir):
    if not os.path.exists(input_dir):
        print('Input dir does not exist:', input_dir)
        return

    new_pc_data = []
    sub_dirs = listdir(input_dir)
    pcd_prefix = 'center_128_lidar_scan_data_'
    for folder in sub_dirs:
        # print(folder)
        frame_path = os.path.join(input_dir, folder)
        # print(frame_path)
        if not os.path.isdir(frame_path):
            # print(frame_path, 'is not a directory')
            continue

        frame_name = os.path.basename(frame_path)
        params_file = os.path.join(frame_path, "frame_infos.json")
        pcd_file = os.path.join(frame_path, pcd_prefix + frame_name + ".pcd")
        # print(pcd_file)
        if not params_file or not pcd_file:
            continue

        params = tools.get_json_data(params_file)
        # print(params)
        car_pose = params.get("car_pose", {})
        center = car_pose.get("center", {})
        rotation = car_pose.get("rotation", {})
        attitude = car_pose.get("attitude", {})  # 直接使用四元数

        x, y, z = center.get("x", 0), center.get("y", 0), center.get("z", 0)
        # roll, pitch, yaw = np.radians(rotation.get("x", 0)), np.radians(rotation.get("y", 0)), np.radians(rotation.get("z", 0))
        # qx, qy, qz, qw = euler_to_quaternion(roll, pitch, yaw)
        # print(qx, qy, qz, qw)
        qw, qx, qy, qz = (
            attitude.get("w", 1),
            attitude.get("x", 0),
            attitude.get("y", 0),
            attitude.get("z", 0),
        )
        # print(qx, qy, qz, qw)


        T = convert_pose_to_matrix(x, y, z, qx, qy, qz, qw)
        print(T.flatten().tolist())
        return

        pcd = pypcd.point_cloud_from_path(str(pcd_file))
        for row in pcd.pc_data:
            # print(row)
            point = np.array([row[0], row[1], row[2], 1])

            # newPoint = T @ point
            # newPoint[3] = row[3]
            # new_pc_data.append(newPoint)

            transformed_point = T @ point
            new_pc_data.append([transformed_point[0], transformed_point[1], transformed_point[2], row[3]])

    output_file_name = os.path.basename(input_dir) + '_' + pcd_prefix.rstrip('_') + ".pcd"
    print(output_file_name)
    output_file = os.path.join(output_dir, output_file_name)
    write_pcd(new_pc_data, output_file, True)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
