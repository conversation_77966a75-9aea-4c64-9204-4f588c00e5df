// source: gitlab.rp.konvery.work/platform/apis/iam/v1/user.proto
package service

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"sort"
	"strings"
	"time"

	"iam/internal/biz"
	"iam/internal/server/middleware"
	"iam/pkg/keto"

	"gitlab.rp.konvery.work/platform/apis/iam/v1"
	"gitlab.rp.konvery.work/platform/apis/types"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"

	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var apiGenders = map[int]iam.User_Gender_Enum{
	biz.GenderMale:   iam.User_Gender_male,
	biz.GenderFemale: iam.User_Gender_female,
}
var bizGenders = map[iam.User_Gender_Enum]int{
	iam.User_Gender_male:   biz.GenderMale,
	iam.User_Gender_female: biz.GenderFemale,
}

func FromBizGender(val int) iam.User_Gender_Enum {
	return apiGenders[val]
}

func ToBizGender(v iam.User_Gender_Enum) int {
	return bizGenders[v]
}

func ParseGender(s string) (any, error) {
	if v, ok := iam.User_Gender_Enum_value[s]; ok {
		return int(v), nil
	}
	return nil, fmt.Errorf("invalid gender value: %v", s)
}

func FromBizUser(d *biz.User) *iam.User {
	if d == nil {
		return nil
	}
	orgUid := ""
	if d.OrgID != 0 {
		orgUid = kid.StringID(d.OrgID)
	}
	orgType := iam.Team_Type_unspecified
	if d.Org != nil {
		orgType = iam.Team_Type_Enum(d.Org.Type)
	}
	birth := ""
	if !d.Birthday.IsZero() {
		birth = d.Birthday.Format(time.RFC3339)
	}

	o := &iam.User{
		Uid:       d.GetUid(),
		Name:      d.Name,
		Phone:     d.Phone,
		Email:     d.Email,
		Avatar:    d.Avatar,
		Role:      d.Role,
		Birthday:  birth,
		Gender:    FromBizGender(d.Gender),
		Province:  d.Province,
		City:      d.City,
		Imperfect: d.IsImperfect(),
		CreatedAt: timestamppb.New(d.CreatedAt),
		OrgUid:    orgUid,
		OrgType:   orgType,
		Tags:      d.Tags,
		// Hierarchy: strings.Join(d.GetHierarchy(), "/"),
	}
	return o
}

func ToBizUser(d *iam.CreateUserRequest) *biz.User {
	if d == nil {
		return nil
	}
	birth := time.Time{}
	if d.Birthday != "" {
		birth, _ = time.Parse(time.RFC3339, d.Birthday)
	}
	o := &biz.User{
		ID:       kid.ParseID(d.Uid),
		Name:     d.Name,
		Phone:    d.Phone,
		Email:    d.Email,
		Avatar:   d.Avatar,
		Role:     d.Role,
		Province: d.Province,
		City:     d.City,
		Birthday: birth,
		Gender:   ToBizGender(d.Gender),
	}
	return o
}

func ClearPrivacy(u *biz.User) {
	u.Phone = ""
	u.Birthday = time.Time{}
	u.Province = ""
	u.City = ""
}

func clearSysInfo(ctx context.Context, users ...*biz.User) {
	op := biz.UserFromCtx(ctx)
	if biz.IsPrivilegedUser(op.User) {
		return
	}
	for _, u := range users {
		if u == nil {
			continue
		}
		u.Tags = lo.Filter(u.Tags, func(v string, _ int) bool { return !biz.IsSysTag(v) })
	}
}

type UsersService struct {
	iam.UnimplementedUsersServer
	bz       *biz.UsersBiz
	teambz   *biz.TeamsBiz
	permbz   *biz.PermsBiz
	roleRepo biz.RolesRepo
	grantbz  *biz.BizgrantsBiz
	log      *log.Helper
}

func NewUsersService(bz *biz.UsersBiz, teambz *biz.TeamsBiz, permbz *biz.PermsBiz,
	roleRepo biz.RolesRepo, grantbz *biz.BizgrantsBiz,
	logger log.Logger) *UsersService {
	return &UsersService{bz: bz, teambz: teambz, permbz: permbz, roleRepo: roleRepo, grantbz: grantbz,
		log: log.NewHelper(logger)}
}

func (o *UsersService) checkNewUserRole(op *biz.User, req *iam.CreateUserRequest) error {
	if req.Role == "" {
		req.Role = biz.TeamRoleMember
	}
	if !(biz.IsPrivileged(req.Role) || req.Role == biz.TeamRoleMember) {
		return errors.NewErrInvalidField(errors.WithFields("role"))
	}
	if !op.IsRoot() && biz.IsPrivileged(req.Role) {
		return errors.NewErrForbidden(errors.WithMessage("only super users can create privileged users"))
	}
	return nil
}

func (o *UsersService) CreateUser(ctx context.Context, req *iam.CreateUserRequest) (*iam.User, error) {
	if req.Name == "" || req.Phone == "" {
		return nil, errors.NewErrEmptyFieldEither(errors.WithFields("name", "phone"))
	}
	if ValidateEmailPhoneNumber(req.Email, req.Phone) != nil {
		return nil, errors.NewErrInvalidField(errors.WithFields("email", "phone"))
	}

	// check permissions
	op := biz.UserFromCtx(ctx)
	if err := o.checkNewUserRole(op.User, req); err != nil {
		return nil, err
	}
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermCreate, keto.MakeUserName("")) {
		return nil, errors.NewErrForbidden()
	}

	user, err := o.bz.Create(ctx, ToBizUser(req))
	return FromBizUser(user), err
}

func (o *UsersService) BatchCreateUsers(ctx context.Context, req *iam.BatchCreateUsersRequest) (*iam.ListUserReply, error) {
	if len(req.Users) > 100 {
		return nil, errors.NewErrUnsupportedField(errors.WithMessage("too many users"))
	}
	if len(req.Users) == 0 {
		return nil, errors.NewErrEmptyField(errors.WithFields("users"))
	}
	op := biz.UserFromCtx(ctx)
	for _, user := range req.Users {
		if err := o.checkNewUserRole(op.User, user); err != nil {
			return nil, err
		}
		if user.Name == "" || user.Phone == "" {
			return nil, errors.NewErrEmptyFieldEither(errors.WithFields("name", "phone"))
		}
		if ValidateEmailPhoneNumber(user.Email, user.Phone) != nil {
			return nil, errors.NewErrInvalidField(errors.WithFields("email", "phone"))
		}
	}

	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermCreate, keto.MakeUserName("")) {
		return nil, errors.NewErrForbidden()
	}

	users, err := o.bz.BatchCreateUsers(ctx, kslice.Map(ToBizUser, req.Users))
	if err != nil {
		return nil, err
	}
	return &iam.ListUserReply{Total: int32(len(users)), Users: kslice.Map(FromBizUser, users)}, err
}

func (o *UsersService) UpdateUser(ctx context.Context, req *iam.UpdateUserRequest) (*iam.User, error) {
	if len(req.Fields) == 0 {
		return nil, errors.NewErrEmptyField(errors.WithFields("fields"))
	}
	if lo.Contains(req.Fields, biz.User_Email) || lo.Contains(req.Fields, biz.User_Phone) {
		return nil, errors.NewErrForbidden(errors.WithMessage("cannot change email or phone"))
	}

	op := biz.UserFromCtx(ctx)
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermUpdate, keto.MakeUserName(req.User.Uid)) {
		return nil, errors.NewErrForbidden()
	}

	target, err := o.bz.Get(ctx, req.User.Uid)
	if err != nil {
		return nil, err
	}

	// only allow to update system roles here
	if lo.Contains(req.Fields, biz.User_Role) {
		// TODO: if req.User.Role is "member" or "", try to fill his org role with team_members table
		if !lo.Contains(biz.SysRoles(), op.Role) ||
			(req.User.Role != biz.TeamRoleMember && !lo.Contains(biz.SysRoles(), req.User.Role)) {
			fmt.Println("---> update role err 1")
			return nil, errors.NewErrForbidden(errors.WithMessage("update role is not allowed"))
		}
		fmt.Println("---> op.Role: ", op.Role)
		allowed := false
		switch op.Role {
		case biz.SysRoleRoot, biz.SysRoleSuperAdmin:
			fmt.Println("---> root")
			allowed = true
		case biz.SysRoleAdmin, biz.SysRoleService:
			fmt.Println("---> service, admin, superadmin")
			fmt.Println(lo.Contains(biz.SysRoleAdmins(), req.User.Role))
			allowed = !lo.Contains(biz.SysRoleAdmins(), req.User.Role)
		default:
			allowed = req.User.Role == biz.TeamRoleMember
		}
		if !allowed {
			fmt.Println("---> update role err 2")
			return nil, errors.NewErrForbidden(errors.WithMessage("update role is not allowed"))
		}
		if biz.IsSysRole(target.Role) {
			fmt.Println("---> del old role keto: ", target.Role)
			err := keto.DefaultAccessMgr().DeleteSysGroupMembers(ctx, target.Role, target.GetUid())
			if err != nil {
				return nil, err
			}
		}
		if biz.IsSysRole(req.User.Role) {
			fmt.Println("---> add new role keto: ", req.User.Role)
			err := keto.DefaultAccessMgr().AddSysGroupMembers(ctx, req.User.Role, target.GetUid())
			if err != nil {
				return nil, err
			}
		}
	}

	// user := &biz.User{Uid: req.Uid}
	// if err = util.MapToStruct(req.Fields, user, map[string]util.FieldParser{"gender": ParseGender}); err != nil {
	// 	return nil, perrors.NewErrBadRequest("unsupported fields")
	// }
	user, err := o.bz.Update(ctx, ToBizUser(req.User), field.NewMask(req.Fields...))
	user.Org = target.Org
	clearSysInfo(ctx, user)
	return FromBizUser(user), err
}

func (o *UsersService) DeleteUser(ctx context.Context, req *iam.DeleteUserRequest) (*emptypb.Empty, error) {
	op := biz.UserFromCtx(ctx)
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermDelete, keto.MakeUserName(req.Uid)) {
		return nil, errors.NewErrForbidden()
	}

	return &emptypb.Empty{}, o.bz.Delete(ctx, req.Uid)
}

func (o *UsersService) GetUser(ctx context.Context, req *iam.GetUserRequest) (*iam.User, error) {
	user, err := o.bz.Get(ctx, req.Uid)

	op := biz.UserFromCtx(ctx)
	if user != nil && user.ID != op.ID && !biz.IsPrivilegedUser(op.User) &&
		!keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermGetPrivacy, keto.MakeUserName(req.Uid)) {
		ClearPrivacy(user)
	}
	clearSysInfo(ctx, user)
	return FromBizUser(user), err
}

func (o *UsersService) GetMe(ctx context.Context, _ *emptypb.Empty) (*iam.User, error) {
	op := biz.UserFromCtx(ctx)
	if op == nil {
		return nil, errors.NewErrUnauthorized()
	}
	clearSysInfo(ctx, op.User)
	return FromBizUser(op.User), nil
}

func (o *UsersService) GetMe2(ctx context.Context, _ *emptypb.Empty) (*iam.UserContext, error) {
	op := biz.UserFromCtx(ctx)
	if op == nil {
		return nil, errors.NewErrUnauthorized()
	}

	clearSysInfo(ctx, op.User, op.AssumeBy)
	return &iam.UserContext{
		User:     FromBizUser(op.User),
		AssumeBy: FromBizUser(op.AssumeBy),
	}, nil
}

// // TODO: obsolete this API as it is meaningless now.
// func (o *UsersService) GetUserHierarchy(ctx context.Context, req *v1.GetUserHierarchyRequest) (*v1.GetUserHierarchyReply, error) {
// 	if req.LevelsUp < 0 {
// 		return nil, perrors.NewErrBadRequest("negative levels")
// 	}
// 	user, err := o.bz.Get(ctx, req.Uid)
// 	if err != nil {
// 		return nil, err
// 	}
// 	var teams []*biz.Team
// 	if user.Org != nil {
// 		n, hcnt := int(req.LevelsUp), len(user.Org.Hierarchy)
// 		if n == 0 || n > hcnt {
// 			n = hcnt
// 		}
// 		teams = make([]*biz.Team, 0, n+1)
// 		for i := hcnt - n + 1; i < hcnt; i++ {
// 			team, err := o.teambz.GetByID(ctx, user.Org.Hierarchy[i])
// 			if err != nil {
// 				return nil, err
// 			}
// 			teams = append(teams, team)
// 		}
// 		teams = append(teams, user.Org)
// 	}
// 	return &v1.GetUserHierarchyReply{Teams: util.Map(FromBizTeam, teams)}, nil
// }

func (o *UsersService) ListUser(ctx context.Context, req *iam.ListUserRequest) (*iam.ListUserReply, error) {
	op := biz.UserFromCtx(ctx)
	if !biz.IsPrivilegedUser(op.User) &&
		!keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermList, keto.MakeUserName("")) {
		return nil, errors.NewErrForbidden()
	}

	filter := &biz.UserListFilter{
		Pagesz: int(req.Pagesz),
		Page:   int(req.Page),
		IDs:    biz.GetIDs(req.Uids),
		OrgID:  kid.ParseID(req.OrgUid),
		Roles:  req.Roles,
		Phones: req.Phones,
		Emails: req.Emails,
		Tags:   req.Tags,

		NamePattern: req.NamePattern,
	}
	var cnt int64
	if filter.Page == 0 {
		var err error
		cnt, err = o.bz.Count(ctx, filter)
		if err != nil {
			return nil, err
		}
	}
	users, err := o.bz.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	clearSysInfo(ctx, users...)
	if !op.IsPrivileged() {
		for _, u := range users {
			ClearPrivacy(u)
		}
	}

	var orgs []*biz.Team
	if req.WithOrg {
		teamMap := make(map[int64]*biz.Team, len(users))
		orgs = make([]*biz.Team, len(users))
		for i, u := range users {
			if u.OrgID == 0 {
				continue
			}
			tid := u.OrgID
			t := teamMap[tid]
			if t == nil {
				t, err = o.teambz.GetByID(ctx, tid)
				if err != nil {
					return nil, err
				}
				teamMap[tid] = t
			}
			orgs[i] = t
			u.Org = t
		}
	}
	return &iam.ListUserReply{
		Total: int32(cnt),
		Users: kslice.Map(FromBizUser, users),
		Orgs:  kslice.Map(ToBaseUser, orgs),
	}, err
}

func (o *UsersService) Login(ctx context.Context, req *iam.LoginRequest) (*iam.LoginReply, error) {
	r, err := o.bz.Login(ctx, req)
	if err != nil {
		return nil, err
	}
	// 登录成功后，更新最后登录时间
	err = o.bz.UpdateLastLoginTime(ctx, r.User.ID)
	if err != nil {
		// 记录错误但不影响登录流程
		o.log.Error(ctx, "failed to update last login time", err, "user_id", r.User.ID)
	}
	o.setCookie(ctx, r.Token, r.User)
	feperm, err := o.getFeperm(ctx, r.User)
	if err != nil {
		return nil, err
	}
	clearSysInfo(ctx, r.User)
	return &iam.LoginReply{
		User:       FromBizUser(r.User),
		Token:      r.Token,
		ExpireTime: timestamppb.New(r.ExpiresAt),
		Feperm:     feperm,
	}, nil
}

func (o *UsersService) setCookie(ctx context.Context, token string, user *biz.User) {
	c := middleware.Cookies{
		Token: token,
		Shunt: o.bz.GetRoutingCfg(ctx, user),
	}
	middleware.SetCookie(ctx, c)
}

func (o *UsersService) Logout(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	// actual implementation is in internal/server/middleware/cookie.go: SetCookieFilter
	return nil, nil
}

func (o *UsersService) SendAuthCode(ctx context.Context, req *iam.SendAuthCodeRequest) (*iam.SendAuthCodeReply, error) {
	// TODO: check req.Purpose

	req.Receiver = strings.TrimSpace(req.Receiver)
	if err := ValidateIdentity(biz.AuthChannelToIDType(req.Channel.String()), req.Receiver); err != nil {
		return nil, errors.NewErrInvalidField(errors.WithFields("channel", "receiver"))
	}
	return o.bz.SendAuthCode(ctx, req)
}

func (o *UsersService) GetPerms(ctx context.Context, req *iam.GetPermsRequest) (*iam.GetPermsReply, error) {
	cls, _ := keto.ParseObjectName(req.Resource)
	obj := req.Resource
	if req.Scope != "" {
		// we're check permissions in a specific scope
		obj = keto.MakeObjectName(cls, req.Scope)
	}

	perms := req.Perms
	if len(perms) == 0 {
		// query available permissions for this resource type
		if cls == "" {
			return nil, errors.NewErrInvalidField(errors.WithFields("resource"))
		}
		pp, _, err := o.permbz.List(ctx, &biz.PermListFilter{Class: cls}, biz.Pager{Pagesz: 100})
		if err != nil {
			return nil, err
		}
		perms = lo.Map(pp, func(v *biz.Perm, _ int) string { return v.Name })
	}

	// check the permissions one by one
	rp := make([]string, 0, len(perms))
	for _, perm := range perms {
		perm := keto.PermName(perm)
		allowed, err := keto.DefaultAccessMgr().AllowE(ctx, req.Uid, perm, obj)
		if err != nil {
			return nil, err
		}
		if allowed {
			rp = append(rp, perm)
		}
	}
	return &iam.GetPermsReply{Perms: rp}, nil
}

func (o *UsersService) IsAllowed(ctx context.Context, req *iam.IsAllowedRequest) (*iam.IsAllowedReply, error) {
	// in := make([]*keto.AccessRequest, len(req.Actions))
	// for _, act := range req.Actions {
	// 	in = append(in, &keto.AccessRequest{
	// 		Subject: req.Uid,
	// 		Perm:    keto.PermName(act.Perm),
	// 		Object:  act.Resource,
	// 	})
	// }
	// allowed, err := keto.DefaultAccessMgr().Check(ctx, in)
	spew.Dump("---> check is allowed", req)
	allowed, err := keto.DefaultAccessMgr().AllowE(ctx, req.Uid, keto.PermName(req.Action.Perm), req.Action.Resource)
	if err != nil {
		return nil, err
	}
	return &iam.IsAllowedReply{Allowed: allowed}, nil
}

func (o *UsersService) GetMyFeperm(ctx context.Context, _ *emptypb.Empty) (*iam.Feperm, error) {
	user := biz.UserFromCtx(ctx)
	return o.getFeperm(ctx, user.User)
}

func (o *UsersService) getFeperm(ctx context.Context, user *biz.User) (*iam.Feperm, error) {
	perms, err := o.roleRepo.GetFeperm(ctx, user.Role)
	if err != nil && !errors.IsNotFound(err) {
		return nil, err
	}
	if len(perms) == 0 {
		return &iam.Feperm{}, nil
	}

	// get team type
	teamType := iam.Team_Type_unspecified
	if user.Org == nil && user.OrgID > 0 {
		user.Org, err = o.teambz.GetByID(ctx, user.OrgID)
		if err != nil {
			return nil, fmt.Errorf("failed to get org: %w", err)
		}
	}
	if user.Org != nil {
		teamType = iam.Team_Type_Enum(user.Org.Type)
	}

	var match, looseMatch *iam.FepermItem
	for _, p := range perms {
		if p.TeamType == teamType {
			match = p
		}
		if p.TeamType == iam.Team_Type_unspecified {
			looseMatch = p
		}
	}
	if match == nil {
		match = looseMatch
	}
	if match == nil {
		return &iam.Feperm{}, nil
	}

	return match.Perm, nil
}

func (o *UsersService) AddTag(ctx context.Context, req *types.TagRequest) (*types.TagList, error) {
	op := biz.UserFromCtx(ctx)
	if !biz.IsPrivilegedUser(op.User) {
		if lo.SomeBy(req.Tags, func(v string) bool { return biz.IsSysTag(strings.ToLower(v)) }) ||
			!keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermUpdate, keto.MakeUserName(req.Uid)) {
			return nil, errors.NewErrForbidden()
		}
	}

	user, err := o.bz.Get(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if lo.Every(user.Tags, req.Tags) {
		clearSysInfo(ctx, user)
		return &types.TagList{Tags: user.Tags}, nil
	}
	user.Tags = lo.Union(user.Tags, req.Tags)
	sort.Strings(user.Tags)
	_, err = o.bz.Update(ctx, user, field.NewMask(biz.User_Tags))
	if err != nil {
		return nil, err
	}
	clearSysInfo(ctx, user) // update will refill the tags
	return &types.TagList{Tags: user.Tags}, nil
}

func (o *UsersService) DeleteTag(ctx context.Context, req *types.TagRequest) (*types.TagList, error) {
	op := biz.UserFromCtx(ctx)
	if !biz.IsPrivilegedUser(op.User) {
		if lo.SomeBy(req.Tags, func(v string) bool { return biz.IsSysTag(strings.ToLower(v)) }) ||
			!keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermUpdate, keto.MakeUserName(req.Uid)) {
			return nil, errors.NewErrForbidden()
		}
	}

	user, err := o.bz.Get(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if lo.None(user.Tags, req.Tags) {
		clearSysInfo(ctx, user)
		return &types.TagList{Tags: user.Tags}, nil
	}
	user.Tags = lo.Without(user.Tags, req.Tags...)
	_, err = o.bz.Update(ctx, user, field.NewMask(biz.User_Tags))
	if err != nil {
		return nil, err
	}
	clearSysInfo(ctx, user) // update will refill the tags
	return &types.TagList{Tags: user.Tags}, nil
}
