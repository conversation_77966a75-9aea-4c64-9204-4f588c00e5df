syntax = "proto3";

package iam.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/iam/v1;iam";
option java_multiple_files = true;
option java_package = "iam.v1";

service Bizgrants {
  rpc CreateBizgrant (CreateBizgrantRequest) returns (CreateBizgrantReply) {
    option (google.api.http) = {
      post: "/v1/bizgrants"
      body: "*"
    };
  }

  rpc DeleteBizgrant (DeleteBizgrantRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/bizgrants"
    };
  }

  // rpc GetBizgrant (GetBizgrantRequest) returns (GetBizgrantReply) {
  //   option (google.api.http) = {
  //     get: "/v1/bizgrants/{uid}"
  //   };
  // }

  rpc ListBizgrant (ListBizgrantRequest) returns (ListBizgrantReply) {
    option (google.api.http) = {
      get: "/v1/bizgrants"
    };
  }
}

message CreateBizgrantRequest {
  option (openapi.v3.schema) = {
    required: ["grantee_uid", "org_uid", "biz"]
  };

  // grantee uid
  string grantee_uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // uid of the organization whose business permissions are granted
  string org_uid = 2 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // business scope granted
  repeated BizType.Enum bizz = 3 [(validate.rules).repeated.items.enum = {
                                      defined_only: true, not_in: [0]}];
}

message CreateBizgrantReply {
  option (openapi.v3.schema) = {
    required: ["grants"]
  };

  repeated Bizgrant grants = 1;
}

message BizType {
  enum Enum {
    unspecified = 0;
    anno = 1;
  }
}

message Bizgrant {
  option (openapi.v3.schema) = {
    required: ["grantor_uid", "grantee_uid", "org_uid", "biz", "created_at"]
  };

  // grantor uid
  string grantor_uid = 1;
  // grantee uid
  string grantee_uid = 2;
  // uid of the organization whose business permissions are granted
  string org_uid = 3;
  // business scope granted
  BizType.Enum biz = 4;

  google.protobuf.Timestamp created_at = 15;
}

message BizgrantFilter {
  // grantor uid
  string grantor_uid = 1;
  // grantee uid
  string grantee_uid = 2;
  // uid of the orgnization whose business permissions are granted
  string org_uid = 3;
  // business scope granted
  BizType.Enum biz = 4 [(validate.rules).enum = {defined_only: true}];
}

message DeleteBizgrantRequest {
  BizgrantFilter filter = 1 [(validate.rules).message.required = true];
}

message ListBizgrantRequest {
  int32 pagesz = 1 [(validate.rules).int32 = {gte:0, lte: 100}];
  // An opaque pagination token returned from a previous call.
  // An empty token denotes the first page.
  string page_token = 2;

  BizgrantFilter filter = 3;
}

message ListBizgrantReply {
  option (openapi.v3.schema) = {
    required: ["grants", "next_page_token"]
  };

  repeated Bizgrant grants = 1;
  // An opaque pagination token, if not empty, to be used to fetch the next page of results
  string next_page_token = 2;
}
