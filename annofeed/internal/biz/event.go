package biz

import (
	"context"
	"encoding/json"
	"fmt"

	"annofeed/internal/mq"
	"gitlab.rp.konvery.work/platform/pkg/log"

	"github.com/go-kratos/kratos/v2/errors"
)

const (
	EvtSubtypeCreate      = "create"
	EvtSubtypeBatchCreate = "batchCreate"
	EvtSubtypeStart       = "start"
	EvtSubtypeStop        = "stop"
	EvtSubtypeDelete      = "delete"
)

type EventsBiz struct {
	bizgrantRepo BizgrantsRepo
	bgtask       BackgroundTask
	log          *log.Helper
}

func NewEventsBiz(
	bizgrantRepo BizgrantsRepo,
	bgtask BackgroundTask,
	logger log.Logger,
) *EventsBiz {
	o := &EventsBiz{
		bizgrantRepo: bizgrantRepo,
		bgtask:       bgtask,
		log:          log.NewHelper(logger),
	}
	o.init()
	return o
}

func (o *EventsBiz) init() {
	mq.RegEventHandler("AnnoBizgrant", EvtSubtypeCreate, o.createBizgrant)
	mq.RegEventHandler("AnnoBizgrant", EvtSubtypeDelete, o.deleteBizgrant)
}

func (o *EventsBiz) createBizgrant(ctx context.Context, ev *mq.Event) error {
	body, _ := ev.Body.(string)
	data := &Bizgrant{}
	if err := json.Unmarshal([]byte(body), data); err != nil {
		return fmt.Errorf("failed to unmarshal event body: %w", err)
	}
	_, err := o.bizgrantRepo.Create(ctx, data)
	if err != nil {
		if errors.IsConflict(err) {
			return nil
		}
		return fmt.Errorf("failed to create Bizgrant: %w", err)
	}
	return nil
}

func (o *EventsBiz) deleteBizgrant(ctx context.Context, ev *mq.Event) error {
	body, _ := ev.Body.(string)
	bizgrantIDs := []int64{}
	if err := json.Unmarshal([]byte(body), &bizgrantIDs); err != nil {
		return fmt.Errorf("failed to unmarshal event body: %w", err)
	}
	if len(bizgrantIDs) == 0 {
		return nil
	}

	if err := o.bizgrantRepo.DeleteByIDs(ctx, bizgrantIDs); err != nil {
		return fmt.Errorf("failed to delete Bizgrant: %w", err)
	}
	return nil
}
