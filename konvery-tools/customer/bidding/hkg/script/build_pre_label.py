import os
import shutil
import json

mac_file = ['.DS_Store', '__MACOSX']


root_dir = '/Users/<USER>/Downloads/yushua'
result_dir = '/Users/<USER>/Downloads/result'
try:
    os.mkdir(result_dir)
except FileExistsError:
    shutil.rmtree(result_dir)
    os.mkdir(result_dir)

for number_folder in os.listdir(root_dir):
    if number_folder not in mac_file:
        result_dir_1 = os.path.join(result_dir, number_folder)
        os.mkdir(result_dir_1)
        number_folder_dir = os.path.join(root_dir, number_folder)
        for dashed_num_folder in os.listdir(number_folder_dir):
            if dashed_num_folder not in mac_file:
                result_dir_2 = os.path.join(result_dir_1, dashed_num_folder)
                os.mkdir(result_dir_2)
                dashed_num_folder_dir = os.path.join(number_folder_dir, dashed_num_folder)
                bev_json_dir = os.path.join(dashed_num_folder_dir, 'bev_json')

                for pre_label in os.listdir(bev_json_dir):
                    if pre_label not in mac_file:
                        pre_label_dir = os.path.join(bev_json_dir, pre_label)
                        json_data = json.load(open(pre_label_dir))

                        pre_annotation = {}

                        label_meta = {
                            'mark_status': 0,
                            'global': {}
                        }
                        lidar_list = []
                        for annotation in json_data['annotations']:
                            lidar_list_content = {
                                'class': annotation['label'],
                                'attrs': {
                                    'occlusion': [annotation['attributes']['occlusion']],
                                    'ignore': [annotation['attributes']['ignore']],
                                    'is_static': [annotation['attributes']['is_static']],
                                },
                                'track_id': annotation['trackId'],
                                'group_id': 0,
                                'annotation': {
                                    'type': 'volume',
                                    'data': {
                                        'position': annotation['motion_center'],
                                        'dimension': {
                                            'l': annotation['size']['x'],
                                            'w': annotation['size']['y'],
                                            'h': annotation['size']['z']
                                        },
                                        'rotation': {
                                            'x': 0,
                                            'y': 0,
                                            'z': annotation['yaw'],
                                        }
                                    }
                                }
                            }
                            lidar_list.append(lidar_list_content)

                        pre_annotation['label_meta'] = label_meta
                        pre_annotation['lidar'] = lidar_list

                        with open(os.path.join(result_dir_2, pre_label), 'w') as f:
                            json.dump(pre_annotation, f,
                                      indent=4,
                                      ensure_ascii=False)