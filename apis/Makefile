GOHOSTOS:=$(shell go env GOHOSTOS)
GOPATH:=$(shell go env GOPATH)
VERSION=$(shell git describe --tags --always)

MODS := types iam anno annofeed annout annostat anyconn model

.PHONY: init
# init env
init:
#v1.34.2
	go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.32.0
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@v1.3.0
	go install github.com/go-kratos/kratos/cmd/protoc-gen-go-http/v2@latest
	go install github.com/google/gnostic/cmd/protoc-gen-openapi@latest
	go install github.com/envoyproxy/protoc-gen-validate@latest

.PHONY: $(MODS)
$(MODS): FOLDER=$@
$(MODS): PROTO_FILES=$(shell find $(FOLDER) -name *.proto)
$(MODS):
	protoc --proto_path=. \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:. \
 	       --go-http_out=paths=source_relative:. \
 	       --go-grpc_out=paths=source_relative:. \
 	       --validate_out=paths=source_relative,lang=go:. \
	       --openapi_out=fq_schema_naming=true,naming=proto,enum_type=string,default_response=false:. \
	       $(PROTO_FILES)
	@sed -i "s:^    /v1/:    /$(FOLDER)/v1/:" openapi.yaml
	@test -f ./$(FOLDER)/patch-openapi.sh && ./$(FOLDER)/patch-openapi.sh || true
	@mv openapi.yaml $(FOLDER)/openapi.yaml

.PHONY: all
# generate all
all:
	# make init
	$(foreach mod,$(MODS),\
		make $(mod) \
	&&) true

test: all
	@git diff -I '^// -?[[:space:]]protoc|_HTTP_Handler\(srv' --exit-code || (echo commit the changes before proceeding && false)
	@go test ./...


# show help
help:
	@echo ''
	@echo 'Usage:'
	@echo ' make [target]'
	@echo ''
	@echo 'Targets:'
	@awk '/^[a-zA-Z\-\_0-9]+:/ { \
	helpMessage = match(lastLine, /^# (.*)/); \
		if (helpMessage) { \
			helpCommand = substr($$1, 0, index($$1, ":")); \
			helpMessage = substr(lastLine, RSTART + 2, RLENGTH); \
			printf "\033[36m%-22s\033[0m %s\n", helpCommand,helpMessage; \
		} \
	} \
	{ lastLine = $$0 }' $(MAKEFILE_LIST)

.DEFAULT_GOAL := help
