package biz

import (
	time "time"

	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
)

type TeamMember struct {
	ID        int64     `json:"id" gorm:"default:null"`
	TeamID    int64     `json:"team_id" gorm:"default:null"`
	UserID    int64     `json:"user_id" gorm:"default:null"`
	Role      string    `json:"role" gorm:"default:null"`
	Status    int       `json:"status" gorm:"default:null"`
	CreatedAt time.Time `json:"created_at" gorm:"default:null"`
}

const TeamMemberTableName = "team_members"

var (
	teamMember_             = field.RegObject(&TeamMember{})
	TeamMemberUpdatableFlds = field.NewModel(TeamMemberTableName, teamMember_)

	TeamMember_TeamID = field.Sname(&teamMember_.TeamID)
	TeamMember_UserID = field.Sname(&teamMember_.UserID)
	TeamMember_Role   = field.Sname(&teamMember_.Role)
	TeamMember_Status = field.Sname(&teamMember_.Status)
)

const (
	MemberStatusActive = 0
	// MemberStatusDeleted = 1
)

const (
	TeamRoleOwner   = "owner"
	TeamRoleManager = "manager"
	TeamRoleMember  = "member"

	SysRoleRoot       = "root"
	SysRoleAdmin      = "admin"
	SysRoleService    = "service"
	SysRoleSubAdmin   = "subAdmin"
	SysRoleInspector  = "inspector"
	SysRoleKAM        = "kam"
	SysRolePM         = "pm"
	SysRoleSuperAdmin = "superadmin"
	SysRoleDemo       = "demo"
)

func TeamRoles() []string       { return []string{TeamRoleOwner, TeamRoleManager, TeamRoleMember} }
func TeamEditorRoles() []string { return []string{TeamRoleOwner, TeamRoleManager} }
func SysRoles() []string {
	return []string{SysRoleRoot, SysRoleAdmin, SysRoleService, SysRoleSubAdmin,
		SysRoleInspector, SysRoleKAM, SysRolePM, SysRoleSuperAdmin, SysRoleDemo}
}
func SysRoleAdmins() []string {
	return []string{SysRoleRoot, SysRoleAdmin, SysRoleService, SysRoleSuperAdmin}
}

func IsSysRole(role string) bool    { return lo.Contains(SysRoles(), role) }
func IsPrivileged(role string) bool { return lo.Contains(SysRoleAdmins(), role) }
func IsPrivilegedUser(u *User) bool { return IsPrivileged(u.Role) }

func IsTeamEditorRole(role string) bool { return lo.Contains(TeamEditorRoles(), role) }
func CanManageOrg(u *User, orgID int64) bool {
	return IsPrivilegedUser(u) || (u.OrgID == orgID && IsTeamEditorRole(u.Role))
}
