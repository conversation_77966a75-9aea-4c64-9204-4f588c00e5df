# Default values for the service.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: artifactory.rp.konvery.work/docker/kon-apidocs
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""
  svcCommand: ["./apidocs", "start"]

imagePullSecrets: []
#  - name: "sansheng-artifactory-token"
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations:
    eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/non-prod-workload-sansheng
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "sansheng-kon-apidocs"

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

secretDBName: "sansheng-role-secret"
secretS3: "sansheng-iam-s3"

service:
  type: ClusterIP
  port: 8080
  grpcPort: 3721
  healthzPath: /healthz

hostName: apidocs.np.konvery.work

# not needed any more because we are behind a API gateway
ingress:
  enabled: false
  className: "ingress-nginx-internal"
  annotations:
    # nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    external-dns-annotation: ingress-nginx-internal
    nginx.ingress.kubernetes.io/proxy-body-size: 1024m
    nginx.ingress.kubernetes.io/enable-cors: "true"
  hosts:
    # todo: should change to the actual host when make deployment
    - host: apidocs.np.konvery.work
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls:
    - secretName: wildcard-np-konvery-work
      hosts:
        - apidocs.np.konvery.work

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

database:
  DATABASE: sansheng-xx

envVars: {}

## @section Persistence parameters
##

## Enable persistence using Persistent Volume Claims
## ref: https://kubernetes.io/docs/user-guide/persistent-volumes/
##
persistence:
  ## @param persistence.enabled If true, use a Persistent Volume Claim. If false, use emptyDir.
  ##
  enabled: true
  ## @param persistence.storageClass Persistent Volume Storage Class
  ## If defined, storageClassName: <storageClass>
  ## If set to "-", storageClassName: "", which disables dynamic provisioning
  ## If undefined (the default) or set to null, no storageClassName spec is
  ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
  ##   GKE, AWS & OpenStack)
  ##
  storageClass: ""
  ##
  ## @param persistence.annotations [object] Annotations for the PVC
  ##
  annotations: {}
  ## @param persistence.accessModes Persistent Volume Access Modes
  ##
  accessModes:
    - ReadWriteOnce
  ## @param persistence.size PVC Storage Request for etcd data volume
  ##
  size: 4Gi
  ## @param persistence.selector [object] Selector to match an existing Persistent Volume
  ## ref: https://kubernetes.io/docs/concepts/storage/persistent-volumes/#selector
  ##
  selector: {}

global:
  # Global StorageClass for Persistent Volume(s)
  storageClass: ""

## persistentVolumeClaimRetentionPolicy
## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#persistentvolumeclaim-retention
## @param persistentVolumeClaimRetentionPolicy.enabled Controls if and how PVCs are deleted during the lifecycle of a StatefulSet
## @param persistentVolumeClaimRetentionPolicy.whenScaled Volume retention behavior when the replica count of the StatefulSet is reduced
## @param persistentVolumeClaimRetentionPolicy.whenDeleted Volume retention behavior that applies when the StatefulSet is deleted
persistentVolumeClaimRetentionPolicy:
  enabled: false
  whenScaled: Retain
  whenDeleted: Retain
