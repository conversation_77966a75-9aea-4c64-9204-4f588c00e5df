// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: jaeger-idl/proto/api_v2/sampling.proto

package api_v2

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type SamplingStrategyType int32

const (
	SamplingStrategyType_PROBABILISTIC SamplingStrategyType = 0
	SamplingStrategyType_RATE_LIMITING SamplingStrategyType = 1
)

var SamplingStrategyType_name = map[int32]string{
	0: "PROBABILISTIC",
	1: "RATE_LIMITING",
}

var SamplingStrategyType_value = map[string]int32{
	"PROBABILISTIC": 0,
	"RATE_LIMITING": 1,
}

func (x SamplingStrategyType) String() string {
	return proto.EnumName(SamplingStrategyType_name, int32(x))
}

func (SamplingStrategyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ae32d90db01957f7, []int{0}
}

type ProbabilisticSamplingStrategy struct {
	SamplingRate         float64  `protobuf:"fixed64,1,opt,name=samplingRate,proto3" json:"samplingRate,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProbabilisticSamplingStrategy) Reset()         { *m = ProbabilisticSamplingStrategy{} }
func (m *ProbabilisticSamplingStrategy) String() string { return proto.CompactTextString(m) }
func (*ProbabilisticSamplingStrategy) ProtoMessage()    {}
func (*ProbabilisticSamplingStrategy) Descriptor() ([]byte, []int) {
	return fileDescriptor_ae32d90db01957f7, []int{0}
}
func (m *ProbabilisticSamplingStrategy) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ProbabilisticSamplingStrategy) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ProbabilisticSamplingStrategy.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ProbabilisticSamplingStrategy) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProbabilisticSamplingStrategy.Merge(m, src)
}
func (m *ProbabilisticSamplingStrategy) XXX_Size() int {
	return m.Size()
}
func (m *ProbabilisticSamplingStrategy) XXX_DiscardUnknown() {
	xxx_messageInfo_ProbabilisticSamplingStrategy.DiscardUnknown(m)
}

var xxx_messageInfo_ProbabilisticSamplingStrategy proto.InternalMessageInfo

func (m *ProbabilisticSamplingStrategy) GetSamplingRate() float64 {
	if m != nil {
		return m.SamplingRate
	}
	return 0
}

type RateLimitingSamplingStrategy struct {
	MaxTracesPerSecond   int32    `protobuf:"varint,1,opt,name=maxTracesPerSecond,proto3" json:"maxTracesPerSecond,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RateLimitingSamplingStrategy) Reset()         { *m = RateLimitingSamplingStrategy{} }
func (m *RateLimitingSamplingStrategy) String() string { return proto.CompactTextString(m) }
func (*RateLimitingSamplingStrategy) ProtoMessage()    {}
func (*RateLimitingSamplingStrategy) Descriptor() ([]byte, []int) {
	return fileDescriptor_ae32d90db01957f7, []int{1}
}
func (m *RateLimitingSamplingStrategy) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RateLimitingSamplingStrategy) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RateLimitingSamplingStrategy.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RateLimitingSamplingStrategy) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RateLimitingSamplingStrategy.Merge(m, src)
}
func (m *RateLimitingSamplingStrategy) XXX_Size() int {
	return m.Size()
}
func (m *RateLimitingSamplingStrategy) XXX_DiscardUnknown() {
	xxx_messageInfo_RateLimitingSamplingStrategy.DiscardUnknown(m)
}

var xxx_messageInfo_RateLimitingSamplingStrategy proto.InternalMessageInfo

func (m *RateLimitingSamplingStrategy) GetMaxTracesPerSecond() int32 {
	if m != nil {
		return m.MaxTracesPerSecond
	}
	return 0
}

type OperationSamplingStrategy struct {
	Operation             string                         `protobuf:"bytes,1,opt,name=operation,proto3" json:"operation,omitempty"`
	ProbabilisticSampling *ProbabilisticSamplingStrategy `protobuf:"bytes,2,opt,name=probabilisticSampling,proto3" json:"probabilisticSampling,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                       `json:"-"`
	XXX_unrecognized      []byte                         `json:"-"`
	XXX_sizecache         int32                          `json:"-"`
}

func (m *OperationSamplingStrategy) Reset()         { *m = OperationSamplingStrategy{} }
func (m *OperationSamplingStrategy) String() string { return proto.CompactTextString(m) }
func (*OperationSamplingStrategy) ProtoMessage()    {}
func (*OperationSamplingStrategy) Descriptor() ([]byte, []int) {
	return fileDescriptor_ae32d90db01957f7, []int{2}
}
func (m *OperationSamplingStrategy) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OperationSamplingStrategy) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OperationSamplingStrategy.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *OperationSamplingStrategy) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OperationSamplingStrategy.Merge(m, src)
}
func (m *OperationSamplingStrategy) XXX_Size() int {
	return m.Size()
}
func (m *OperationSamplingStrategy) XXX_DiscardUnknown() {
	xxx_messageInfo_OperationSamplingStrategy.DiscardUnknown(m)
}

var xxx_messageInfo_OperationSamplingStrategy proto.InternalMessageInfo

func (m *OperationSamplingStrategy) GetOperation() string {
	if m != nil {
		return m.Operation
	}
	return ""
}

func (m *OperationSamplingStrategy) GetProbabilisticSampling() *ProbabilisticSamplingStrategy {
	if m != nil {
		return m.ProbabilisticSampling
	}
	return nil
}

type PerOperationSamplingStrategies struct {
	DefaultSamplingProbability       float64                      `protobuf:"fixed64,1,opt,name=defaultSamplingProbability,proto3" json:"defaultSamplingProbability,omitempty"`
	DefaultLowerBoundTracesPerSecond float64                      `protobuf:"fixed64,2,opt,name=defaultLowerBoundTracesPerSecond,proto3" json:"defaultLowerBoundTracesPerSecond,omitempty"`
	PerOperationStrategies           []*OperationSamplingStrategy `protobuf:"bytes,3,rep,name=perOperationStrategies,proto3" json:"perOperationStrategies,omitempty"`
	DefaultUpperBoundTracesPerSecond float64                      `protobuf:"fixed64,4,opt,name=defaultUpperBoundTracesPerSecond,proto3" json:"defaultUpperBoundTracesPerSecond,omitempty"`
	XXX_NoUnkeyedLiteral             struct{}                     `json:"-"`
	XXX_unrecognized                 []byte                       `json:"-"`
	XXX_sizecache                    int32                        `json:"-"`
}

func (m *PerOperationSamplingStrategies) Reset()         { *m = PerOperationSamplingStrategies{} }
func (m *PerOperationSamplingStrategies) String() string { return proto.CompactTextString(m) }
func (*PerOperationSamplingStrategies) ProtoMessage()    {}
func (*PerOperationSamplingStrategies) Descriptor() ([]byte, []int) {
	return fileDescriptor_ae32d90db01957f7, []int{3}
}
func (m *PerOperationSamplingStrategies) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PerOperationSamplingStrategies) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PerOperationSamplingStrategies.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PerOperationSamplingStrategies) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PerOperationSamplingStrategies.Merge(m, src)
}
func (m *PerOperationSamplingStrategies) XXX_Size() int {
	return m.Size()
}
func (m *PerOperationSamplingStrategies) XXX_DiscardUnknown() {
	xxx_messageInfo_PerOperationSamplingStrategies.DiscardUnknown(m)
}

var xxx_messageInfo_PerOperationSamplingStrategies proto.InternalMessageInfo

func (m *PerOperationSamplingStrategies) GetDefaultSamplingProbability() float64 {
	if m != nil {
		return m.DefaultSamplingProbability
	}
	return 0
}

func (m *PerOperationSamplingStrategies) GetDefaultLowerBoundTracesPerSecond() float64 {
	if m != nil {
		return m.DefaultLowerBoundTracesPerSecond
	}
	return 0
}

func (m *PerOperationSamplingStrategies) GetPerOperationStrategies() []*OperationSamplingStrategy {
	if m != nil {
		return m.PerOperationStrategies
	}
	return nil
}

func (m *PerOperationSamplingStrategies) GetDefaultUpperBoundTracesPerSecond() float64 {
	if m != nil {
		return m.DefaultUpperBoundTracesPerSecond
	}
	return 0
}

type SamplingStrategyResponse struct {
	StrategyType          SamplingStrategyType            `protobuf:"varint,1,opt,name=strategyType,proto3,enum=jaeger.api_v2.SamplingStrategyType" json:"strategyType,omitempty"`
	ProbabilisticSampling *ProbabilisticSamplingStrategy  `protobuf:"bytes,2,opt,name=probabilisticSampling,proto3" json:"probabilisticSampling,omitempty"`
	RateLimitingSampling  *RateLimitingSamplingStrategy   `protobuf:"bytes,3,opt,name=rateLimitingSampling,proto3" json:"rateLimitingSampling,omitempty"`
	OperationSampling     *PerOperationSamplingStrategies `protobuf:"bytes,4,opt,name=operationSampling,proto3" json:"operationSampling,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                        `json:"-"`
	XXX_unrecognized      []byte                          `json:"-"`
	XXX_sizecache         int32                           `json:"-"`
}

func (m *SamplingStrategyResponse) Reset()         { *m = SamplingStrategyResponse{} }
func (m *SamplingStrategyResponse) String() string { return proto.CompactTextString(m) }
func (*SamplingStrategyResponse) ProtoMessage()    {}
func (*SamplingStrategyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ae32d90db01957f7, []int{4}
}
func (m *SamplingStrategyResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SamplingStrategyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SamplingStrategyResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SamplingStrategyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SamplingStrategyResponse.Merge(m, src)
}
func (m *SamplingStrategyResponse) XXX_Size() int {
	return m.Size()
}
func (m *SamplingStrategyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SamplingStrategyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SamplingStrategyResponse proto.InternalMessageInfo

func (m *SamplingStrategyResponse) GetStrategyType() SamplingStrategyType {
	if m != nil {
		return m.StrategyType
	}
	return SamplingStrategyType_PROBABILISTIC
}

func (m *SamplingStrategyResponse) GetProbabilisticSampling() *ProbabilisticSamplingStrategy {
	if m != nil {
		return m.ProbabilisticSampling
	}
	return nil
}

func (m *SamplingStrategyResponse) GetRateLimitingSampling() *RateLimitingSamplingStrategy {
	if m != nil {
		return m.RateLimitingSampling
	}
	return nil
}

func (m *SamplingStrategyResponse) GetOperationSampling() *PerOperationSamplingStrategies {
	if m != nil {
		return m.OperationSampling
	}
	return nil
}

type SamplingStrategyParameters struct {
	ServiceName          string   `protobuf:"bytes,1,opt,name=serviceName,proto3" json:"serviceName,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SamplingStrategyParameters) Reset()         { *m = SamplingStrategyParameters{} }
func (m *SamplingStrategyParameters) String() string { return proto.CompactTextString(m) }
func (*SamplingStrategyParameters) ProtoMessage()    {}
func (*SamplingStrategyParameters) Descriptor() ([]byte, []int) {
	return fileDescriptor_ae32d90db01957f7, []int{5}
}
func (m *SamplingStrategyParameters) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SamplingStrategyParameters) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SamplingStrategyParameters.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SamplingStrategyParameters) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SamplingStrategyParameters.Merge(m, src)
}
func (m *SamplingStrategyParameters) XXX_Size() int {
	return m.Size()
}
func (m *SamplingStrategyParameters) XXX_DiscardUnknown() {
	xxx_messageInfo_SamplingStrategyParameters.DiscardUnknown(m)
}

var xxx_messageInfo_SamplingStrategyParameters proto.InternalMessageInfo

func (m *SamplingStrategyParameters) GetServiceName() string {
	if m != nil {
		return m.ServiceName
	}
	return ""
}

func init() {
	proto.RegisterEnum("jaeger.api_v2.SamplingStrategyType", SamplingStrategyType_name, SamplingStrategyType_value)
	proto.RegisterType((*ProbabilisticSamplingStrategy)(nil), "jaeger.api_v2.ProbabilisticSamplingStrategy")
	proto.RegisterType((*RateLimitingSamplingStrategy)(nil), "jaeger.api_v2.RateLimitingSamplingStrategy")
	proto.RegisterType((*OperationSamplingStrategy)(nil), "jaeger.api_v2.OperationSamplingStrategy")
	proto.RegisterType((*PerOperationSamplingStrategies)(nil), "jaeger.api_v2.PerOperationSamplingStrategies")
	proto.RegisterType((*SamplingStrategyResponse)(nil), "jaeger.api_v2.SamplingStrategyResponse")
	proto.RegisterType((*SamplingStrategyParameters)(nil), "jaeger.api_v2.SamplingStrategyParameters")
}

func init() {
	proto.RegisterFile("jaeger-idl/proto/api_v2/sampling.proto", fileDescriptor_ae32d90db01957f7)
}

var fileDescriptor_ae32d90db01957f7 = []byte{
	// 577 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x54, 0xcf, 0x6f, 0x12, 0x41,
	0x14, 0x76, 0x40, 0x9b, 0xf4, 0xd1, 0x6a, 0x3b, 0x56, 0x5d, 0x09, 0x25, 0x64, 0x9b, 0x28, 0x56,
	0x81, 0x64, 0xbd, 0x19, 0xd3, 0xa4, 0x34, 0x86, 0xac, 0xa1, 0x94, 0x2c, 0x78, 0xd1, 0x03, 0x0e,
	0xf0, 0xdc, 0x8c, 0x81, 0x9d, 0xcd, 0xec, 0xb4, 0xca, 0xd5, 0xc4, 0xab, 0x17, 0xcf, 0x5e, 0xfc,
	0x6b, 0x3c, 0x9a, 0x78, 0xf3, 0x64, 0x88, 0x7f, 0x88, 0xd9, 0x5f, 0x2d, 0x2c, 0x0b, 0xdc, 0x3c,
	0xed, 0xe6, 0xbd, 0x6f, 0xbe, 0xef, 0x7b, 0x6f, 0xde, 0x3c, 0x78, 0xf0, 0x9e, 0xa1, 0x8d, 0xb2,
	0xc2, 0x87, 0xa3, 0x9a, 0x2b, 0x85, 0x12, 0x35, 0xe6, 0xf2, 0xde, 0x85, 0x51, 0xf3, 0xd8, 0xd8,
	0x1d, 0x71, 0xc7, 0xae, 0x06, 0x51, 0xba, 0x1d, 0xe2, 0xaa, 0x61, 0x36, 0xbf, 0x67, 0x0b, 0x5b,
	0x84, 0x78, 0xff, 0x2f, 0x04, 0xe5, 0x0b, 0xb6, 0x10, 0xf6, 0x08, 0x7d, 0x8a, 0x1a, 0x73, 0x1c,
	0xa1, 0x98, 0xe2, 0xc2, 0xf1, 0xc2, 0xac, 0x7e, 0x02, 0xfb, 0x6d, 0x29, 0xfa, 0xac, 0xcf, 0x47,
	0xdc, 0x53, 0x7c, 0xd0, 0x89, 0x14, 0x3a, 0x4a, 0x32, 0x85, 0xf6, 0x84, 0xea, 0xb0, 0x15, 0xab,
	0x5a, 0x4c, 0xa1, 0x46, 0x4a, 0xa4, 0x4c, 0xac, 0xb9, 0x98, 0xde, 0x82, 0x82, 0xff, 0x6d, 0xf2,
	0x31, 0x57, 0xfe, 0xd9, 0x24, 0x47, 0x15, 0xe8, 0x98, 0x7d, 0xec, 0x4a, 0x36, 0x40, 0xaf, 0x8d,
	0xb2, 0x83, 0x03, 0xe1, 0x0c, 0x03, 0xa6, 0x1b, 0x56, 0x4a, 0x46, 0xff, 0x46, 0xe0, 0xfe, 0x99,
	0x8b, 0x32, 0x70, 0xba, 0xc0, 0x56, 0x80, 0x4d, 0x11, 0x27, 0x03, 0x92, 0x4d, 0xeb, 0x2a, 0x40,
	0xfb, 0x70, 0xc7, 0x4d, 0x2b, 0x48, 0xcb, 0x94, 0x48, 0x39, 0x67, 0x3c, 0xa9, 0xce, 0xf5, 0xac,
	0xba, 0xb2, 0x78, 0x2b, 0x9d, 0x4a, 0xff, 0x9d, 0x81, 0x62, 0x1b, 0xe5, 0x32, 0x8b, 0x1c, 0x3d,
	0x7a, 0x04, 0xf9, 0x21, 0xbe, 0x63, 0xe7, 0x23, 0x15, 0x27, 0x2f, 0x95, 0xd4, 0x24, 0x6a, 0xe2,
	0x0a, 0x04, 0x7d, 0x09, 0xa5, 0x28, 0xdb, 0x14, 0x1f, 0x50, 0xd6, 0xc5, 0xb9, 0x33, 0x4c, 0x36,
	0x30, 0x13, 0xb0, 0xac, 0xc5, 0xd1, 0xb7, 0x70, 0xd7, 0x9d, 0x75, 0x7b, 0xe9, 0x52, 0xcb, 0x96,
	0xb2, 0xe5, 0x9c, 0x51, 0x4e, 0xf4, 0x64, 0x69, 0xeb, 0xad, 0x25, 0x3c, 0x33, 0x6e, 0x5f, 0xb9,
	0xee, 0x12, 0xb7, 0xd7, 0xe7, 0xdc, 0x2e, 0xc5, 0xe9, 0x9f, 0xb3, 0xa0, 0x2d, 0x08, 0xa3, 0xe7,
	0x0a, 0xc7, 0x43, 0xda, 0x80, 0x2d, 0x2f, 0x8a, 0x75, 0x27, 0x6e, 0x38, 0x8d, 0x37, 0x8d, 0x83,
	0x44, 0x01, 0xc9, 0xe3, 0x3e, 0xd4, 0x9a, 0x3b, 0xf8, 0x3f, 0xc6, 0x84, 0xf6, 0x60, 0x4f, 0xa6,
	0x3c, 0x0b, 0x2d, 0x1b, 0x48, 0x3c, 0x4e, 0x48, 0xac, 0x7a, 0x41, 0x56, 0x2a, 0x11, 0x7d, 0x03,
	0xbb, 0x22, 0x79, 0x57, 0x41, 0x9f, 0x73, 0x46, 0x25, 0x59, 0xc0, 0xca, 0x71, 0xb5, 0x16, 0x79,
	0xf4, 0x23, 0xc8, 0x27, 0x6d, 0xb4, 0x99, 0x64, 0x63, 0x54, 0x28, 0x3d, 0x5a, 0x82, 0x9c, 0x87,
	0xf2, 0x82, 0x0f, 0xb0, 0xc5, 0xc6, 0x18, 0x3d, 0xc3, 0xd9, 0xd0, 0xe1, 0x73, 0xd8, 0x4b, 0xbb,
	0x07, 0xba, 0x0b, 0xdb, 0x6d, 0xeb, 0xac, 0x7e, 0x5c, 0x37, 0x9b, 0x66, 0xa7, 0x6b, 0x9e, 0xec,
	0x5c, 0xf3, 0x43, 0xd6, 0x71, 0xf7, 0x45, 0xaf, 0x69, 0x9e, 0x9a, 0x5d, 0xb3, 0xd5, 0xd8, 0x21,
	0xc6, 0x77, 0x02, 0xb7, 0xe2, 0xe3, 0xa7, 0xcc, 0x61, 0x36, 0x4a, 0xfa, 0x85, 0xc0, 0xed, 0x06,
	0xaa, 0x85, 0x85, 0xf0, 0x68, 0xcd, 0xf5, 0x5f, 0xd9, 0xce, 0x3f, 0x5c, 0x03, 0x8d, 0x07, 0x4d,
	0x3f, 0xf8, 0xf4, 0xeb, 0xef, 0xd7, 0xcc, 0xbe, 0xae, 0x05, 0x7b, 0x73, 0x66, 0xf5, 0xc6, 0xc8,
	0x67, 0xe4, 0xb0, 0x5e, 0xf9, 0x31, 0x2d, 0x92, 0x9f, 0xd3, 0x22, 0xf9, 0x33, 0x2d, 0x12, 0xb8,
	0xc7, 0x45, 0xc4, 0xae, 0x24, 0x1b, 0xf8, 0x5b, 0x3a, 0x14, 0x79, 0xbd, 0x11, 0x7e, 0xfb, 0x1b,
	0xc1, 0xca, 0x7d, 0xfa, 0x2f, 0x00, 0x00, 0xff, 0xff, 0x60, 0xd2, 0xcd, 0xa7, 0xdf, 0x05, 0x00,
	0x00,
}

func (m *ProbabilisticSamplingStrategy) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProbabilisticSamplingStrategy) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ProbabilisticSamplingStrategy) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.SamplingRate != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.SamplingRate))))
		i--
		dAtA[i] = 0x9
	}
	return len(dAtA) - i, nil
}

func (m *RateLimitingSamplingStrategy) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RateLimitingSamplingStrategy) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RateLimitingSamplingStrategy) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.MaxTracesPerSecond != 0 {
		i = encodeVarintSampling(dAtA, i, uint64(m.MaxTracesPerSecond))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *OperationSamplingStrategy) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OperationSamplingStrategy) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *OperationSamplingStrategy) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.ProbabilisticSampling != nil {
		{
			size, err := m.ProbabilisticSampling.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSampling(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.Operation) > 0 {
		i -= len(m.Operation)
		copy(dAtA[i:], m.Operation)
		i = encodeVarintSampling(dAtA, i, uint64(len(m.Operation)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PerOperationSamplingStrategies) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PerOperationSamplingStrategies) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PerOperationSamplingStrategies) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.DefaultUpperBoundTracesPerSecond != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.DefaultUpperBoundTracesPerSecond))))
		i--
		dAtA[i] = 0x21
	}
	if len(m.PerOperationStrategies) > 0 {
		for iNdEx := len(m.PerOperationStrategies) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.PerOperationStrategies[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintSampling(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.DefaultLowerBoundTracesPerSecond != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.DefaultLowerBoundTracesPerSecond))))
		i--
		dAtA[i] = 0x11
	}
	if m.DefaultSamplingProbability != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.DefaultSamplingProbability))))
		i--
		dAtA[i] = 0x9
	}
	return len(dAtA) - i, nil
}

func (m *SamplingStrategyResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SamplingStrategyResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SamplingStrategyResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.OperationSampling != nil {
		{
			size, err := m.OperationSampling.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSampling(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.RateLimitingSampling != nil {
		{
			size, err := m.RateLimitingSampling.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSampling(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.ProbabilisticSampling != nil {
		{
			size, err := m.ProbabilisticSampling.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSampling(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.StrategyType != 0 {
		i = encodeVarintSampling(dAtA, i, uint64(m.StrategyType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *SamplingStrategyParameters) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SamplingStrategyParameters) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SamplingStrategyParameters) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.ServiceName) > 0 {
		i -= len(m.ServiceName)
		copy(dAtA[i:], m.ServiceName)
		i = encodeVarintSampling(dAtA, i, uint64(len(m.ServiceName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintSampling(dAtA []byte, offset int, v uint64) int {
	offset -= sovSampling(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *ProbabilisticSamplingStrategy) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.SamplingRate != 0 {
		n += 9
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *RateLimitingSamplingStrategy) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.MaxTracesPerSecond != 0 {
		n += 1 + sovSampling(uint64(m.MaxTracesPerSecond))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *OperationSamplingStrategy) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Operation)
	if l > 0 {
		n += 1 + l + sovSampling(uint64(l))
	}
	if m.ProbabilisticSampling != nil {
		l = m.ProbabilisticSampling.Size()
		n += 1 + l + sovSampling(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PerOperationSamplingStrategies) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DefaultSamplingProbability != 0 {
		n += 9
	}
	if m.DefaultLowerBoundTracesPerSecond != 0 {
		n += 9
	}
	if len(m.PerOperationStrategies) > 0 {
		for _, e := range m.PerOperationStrategies {
			l = e.Size()
			n += 1 + l + sovSampling(uint64(l))
		}
	}
	if m.DefaultUpperBoundTracesPerSecond != 0 {
		n += 9
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SamplingStrategyResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StrategyType != 0 {
		n += 1 + sovSampling(uint64(m.StrategyType))
	}
	if m.ProbabilisticSampling != nil {
		l = m.ProbabilisticSampling.Size()
		n += 1 + l + sovSampling(uint64(l))
	}
	if m.RateLimitingSampling != nil {
		l = m.RateLimitingSampling.Size()
		n += 1 + l + sovSampling(uint64(l))
	}
	if m.OperationSampling != nil {
		l = m.OperationSampling.Size()
		n += 1 + l + sovSampling(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SamplingStrategyParameters) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ServiceName)
	if l > 0 {
		n += 1 + l + sovSampling(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovSampling(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozSampling(x uint64) (n int) {
	return sovSampling(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *ProbabilisticSamplingStrategy) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSampling
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ProbabilisticSamplingStrategy: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ProbabilisticSamplingStrategy: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field SamplingRate", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.SamplingRate = float64(math.Float64frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipSampling(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSampling
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RateLimitingSamplingStrategy) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSampling
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RateLimitingSamplingStrategy: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RateLimitingSamplingStrategy: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxTracesPerSecond", wireType)
			}
			m.MaxTracesPerSecond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSampling
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxTracesPerSecond |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSampling(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSampling
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OperationSamplingStrategy) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSampling
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OperationSamplingStrategy: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OperationSamplingStrategy: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Operation", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSampling
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSampling
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSampling
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Operation = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProbabilisticSampling", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSampling
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSampling
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSampling
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ProbabilisticSampling == nil {
				m.ProbabilisticSampling = &ProbabilisticSamplingStrategy{}
			}
			if err := m.ProbabilisticSampling.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSampling(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSampling
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PerOperationSamplingStrategies) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSampling
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PerOperationSamplingStrategies: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PerOperationSamplingStrategies: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field DefaultSamplingProbability", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.DefaultSamplingProbability = float64(math.Float64frombits(v))
		case 2:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field DefaultLowerBoundTracesPerSecond", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.DefaultLowerBoundTracesPerSecond = float64(math.Float64frombits(v))
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PerOperationStrategies", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSampling
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSampling
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSampling
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PerOperationStrategies = append(m.PerOperationStrategies, &OperationSamplingStrategy{})
			if err := m.PerOperationStrategies[len(m.PerOperationStrategies)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field DefaultUpperBoundTracesPerSecond", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.DefaultUpperBoundTracesPerSecond = float64(math.Float64frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipSampling(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSampling
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SamplingStrategyResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSampling
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SamplingStrategyResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SamplingStrategyResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StrategyType", wireType)
			}
			m.StrategyType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSampling
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StrategyType |= SamplingStrategyType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProbabilisticSampling", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSampling
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSampling
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSampling
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ProbabilisticSampling == nil {
				m.ProbabilisticSampling = &ProbabilisticSamplingStrategy{}
			}
			if err := m.ProbabilisticSampling.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RateLimitingSampling", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSampling
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSampling
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSampling
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RateLimitingSampling == nil {
				m.RateLimitingSampling = &RateLimitingSamplingStrategy{}
			}
			if err := m.RateLimitingSampling.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OperationSampling", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSampling
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSampling
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSampling
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.OperationSampling == nil {
				m.OperationSampling = &PerOperationSamplingStrategies{}
			}
			if err := m.OperationSampling.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSampling(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSampling
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SamplingStrategyParameters) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSampling
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SamplingStrategyParameters: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SamplingStrategyParameters: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ServiceName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSampling
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSampling
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSampling
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ServiceName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSampling(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSampling
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipSampling(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowSampling
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSampling
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSampling
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthSampling
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupSampling
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthSampling
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthSampling        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowSampling          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupSampling = fmt.Errorf("proto: unexpected end of group")
)
