package biz

// "annostat/internal/protoconv"

type JobState int32

const (
	JobStateUnknown   JobState = -1
	JobStateUnstart   JobState = 0 // phase "unstart"
	JobStateDoing     JobState = 1 // phase "doing"
	JobStateChecking  JobState = 2 // phase background "checking"
	JobStateCommitted JobState = 3 // phase "committed"
	JobStateFinished  JobState = 4 // job "finished"
)

func (o JobState) String() string { return JobStateNames[o] }
func (o JobState) Value() int32   { return int32(o) }

var JobStateNames = map[JobState]string{
	JobStateUnstart:   "unstart",
	JobStateDoing:     "doing",
	JobStateChecking:  "checking",
	JobStateCommitted: "committed",
	JobStateFinished:  "finished",
}
var jobStateValues = map[string]JobState{}

func ToJobState(name string) JobState {
	if v, ok := jobStateValues[name]; ok {
		return v
	}
	return JobStateUnknown
}

func init() {
	for k, v := range JobStateNames {
		jobStateValues[v] = k
	}
}

const (
	ForceActionPrefix = "force_"

	JobActionClaim        = "claim"                              // claim a job for oneself
	JobActionAssign       = "assign"                             // assign a job to a user
	JobActionSubmit       = "submit"                             // submit annotations
	JobActionGiveup       = "giveup"                             // job executor give up a job
	JobActionAccept       = "accept"                             // reviewer accept job annotations
	JobActionReject       = "reject"                             // reviewer reject job annotations
	JobActionForceReject  = ForceActionPrefix + JobActionReject  //
	JobActionRecycle      = "recycle"                            // reviewer reject job annotations and put it back to the initial stage
	JobActionForceRecycle = ForceActionPrefix + JobActionRecycle //
	JobActionTimeout      = "timeout"                            // assignment timeout
	JobActionEnd          = "end"                                // termination
	// JobActionCascadeTerm  = "cascade_term"                       // cascaded termination from lot
	// JobActionFix          = "fix"                                // reviewer fix job annotations and accept
)

func IsJobActionReject(act string) bool { return act == JobActionReject || act == JobActionForceReject }
func IsJobActionRecycle(act string) bool {
	return act == JobActionRecycle || act == JobActionForceRecycle
}

// func FromBizLotState(state LotState) v1.Lot_State {
// 	return LotStateConv.ToProto(state)
// }

// func ToBizLotState(state v1.Lot_State) LotState {
// 	return LotStateConv.FromProto(state)
// }

// var LotStateConv = protoconv.NewProtoEnumConverter(v1.Lot_State_value, func(v string) LotState { return LotState(v) },
// 	v1.Lot_ongoing, LotStateOngoing)

// func init() {
// 	// ensure conversion between proto values and biz values work well
// 	LotStateConv.MustConv(false,
// 		LotStateUnspecified,
// 		LotStateUnstart,
// 		LotStateInitializing,
// 		LotStateOngoing,
// 		LotStateFinished,
// 		LotStatePaused,
// 		LotStateCanceled,
// 	)
// }
