# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
paths:
    /annostat/v1/lots/count:
        get:
            tags:
                - Stats
            description: count lots
            operationId: Stats_GetLotCount
            parameters:
                - name: filter.time_range.from
                  in: query
                  description: time is within [from, to)
                  schema:
                    type: string
                    format: date-time
                - name: filter.time_range.to
                  in: query
                  schema:
                    type: string
                    format: date-time
                - name: filter.lot_types
                  in: query
                  description: filter lot by type
                  schema:
                    type: array
                    items:
                        enum:
                            - unspecified
                            - annotate
                            - segment
                        type: string
                        format: enum
                - name: filter.data_types
                  in: query
                  description: filter lot by data type
                  schema:
                    type: array
                    items:
                        enum:
                            - unspecified
                            - image
                            - pointcloud
                            - fusion2d
                            - fusion3d
                        type: string
                        format: enum
                - name: filter.lot_uids
                  in: query
                  description: filter lot by uid
                  schema:
                    type: array
                    items:
                        type: string
                - name: filter.owner_orgs
                  in: query
                  description: filter lot by owner organization
                  schema:
                    type: array
                    items:
                        type: string
                - name: filter.executor_orgs
                  in: query
                  description: filter lot by executor organization
                  schema:
                    type: array
                    items:
                        type: string
                - name: filter.executor_uids
                  in: query
                  description: filter lot by executor uid
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annostat.v1.GetLotCountReply'
    /annostat/v1/lots/ongoing:
        get:
            tags:
                - Stats
            description: get ongoing lots
            operationId: Stats_GetOngoingLots
            parameters:
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annostat.v1.GetOngoingLotsReply'
    /annostat/v1/lots/{uid}/labels:
        get:
            tags:
                - Stats
            description: lot statistics by label
            operationId: Stats_GetLotLabelStat
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annostat.v1.GetLotLabelStatReply'
    /annostat/v1/lots/{uid}/phases/{phase}/stat-by-exec:
        get:
            tags:
                - Stats
            description: lot statistics by executor
            operationId: Stats_GetLotStatByExecutor
            parameters:
                - name: uid
                  in: path
                  description: lot uid
                  required: true
                  schema:
                    type: string
                - name: phase
                  in: path
                  description: phase number, start from 1
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: by_execteam
                  in: query
                  description: true to stats by execution teams, false to stats by individuals
                  schema:
                    type: boolean
                - name: time_range.from
                  in: query
                  description: time is within [from, to)
                  schema:
                    type: string
                    format: date-time
                - name: time_range.to
                  in: query
                  schema:
                    type: string
                    format: date-time
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annostat.v1.GetLotStatByExecutorReply'
    /annostat/v1/lots/{uid}/status:
        get:
            tags:
                - Stats
            description: lot statistics by phase
            operationId: Stats_GetLotStatus
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annostat.v1.GetLotStatusReply'
    /annostat/v1/orders/conversion:
        get:
            tags:
                - Stats
            description: order-to-lot conversion statistics
            operationId: Stats_GetOrderConversion
            parameters:
                - name: filter.time_range.from
                  in: query
                  description: time is within [from, to)
                  schema:
                    type: string
                    format: date-time
                - name: filter.time_range.to
                  in: query
                  schema:
                    type: string
                    format: date-time
                - name: filter.owner_orgs
                  in: query
                  description: filter by owner organization
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annostat.v1.GetOrderConversionReply'
    /annostat/v1/orders/count:
        get:
            tags:
                - Stats
            description: count orders
            operationId: Stats_GetOrderCount
            parameters:
                - name: filter.time_range.from
                  in: query
                  description: time is within [from, to)
                  schema:
                    type: string
                    format: date-time
                - name: filter.time_range.to
                  in: query
                  schema:
                    type: string
                    format: date-time
                - name: filter.owner_orgs
                  in: query
                  description: filter by owner organization
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annostat.v1.GetOrderCountReply'
    /annostat/v1/prod-by-time:
        get:
            tags:
                - Stats
            description: get production by time
            operationId: Stats_ProductionByTime
            parameters:
                - name: filter.time_range.from
                  in: query
                  description: time is within [from, to)
                  schema:
                    type: string
                    format: date-time
                - name: filter.time_range.to
                  in: query
                  schema:
                    type: string
                    format: date-time
                - name: filter.lot_types
                  in: query
                  description: filter lot by type
                  schema:
                    type: array
                    items:
                        enum:
                            - unspecified
                            - annotate
                            - segment
                        type: string
                        format: enum
                - name: filter.data_types
                  in: query
                  description: filter lot by data type
                  schema:
                    type: array
                    items:
                        enum:
                            - unspecified
                            - image
                            - pointcloud
                            - fusion2d
                            - fusion3d
                        type: string
                        format: enum
                - name: filter.lot_uids
                  in: query
                  description: filter lot by uid
                  schema:
                    type: array
                    items:
                        type: string
                - name: filter.owner_orgs
                  in: query
                  description: filter lot by owner organization
                  schema:
                    type: array
                    items:
                        type: string
                - name: filter.executor_orgs
                  in: query
                  description: filter lot by executor organization
                  schema:
                    type: array
                    items:
                        type: string
                - name: filter.executor_uids
                  in: query
                  description: filter lot by executor uid
                  schema:
                    type: array
                    items:
                        type: string
                - name: time_unit
                  in: query
                  description: group results by time_unit (in seconds)
                  schema:
                    type: integer
                    format: int32
                - name: items
                  in: query
                  description: 'items to count: anno2d, anno3d, elem, job'
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annostat.v1.ProductionByTimeReply'
    /annostat/v1/version:
        get:
            tags:
                - Configs
            operationId: Configs_GetVersion
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annostat.v1.GetVersionReply'
components:
    schemas:
        annostat.v1.ExecutorCounter:
            required:
                - executor
                - jobs
                - elems
                - ins
                - ins_by_widget
                - jobs_per_hour
                - elems_per_hour
                - elem_accuracy
                - ins_accuracy
            type: object
            properties:
                executor:
                    allOf:
                        - $ref: '#/components/schemas/iam.v1.BaseUser'
                    description: executor can be a user or a team
                jobs:
                    type: integer
                    description: number of jobs submitted
                    format: int32
                elems:
                    type: integer
                    description: number of elements submitted
                    format: int32
                ins:
                    type: integer
                    description: number of annotations submitted
                    format: int32
                ins_by_widget:
                    type: object
                    additionalProperties:
                        type: integer
                        format: int32
                    description: 'ins by widget: anno2d/anno3d/...'
                jobs_per_hour:
                    type: number
                    description: number of jobs submitted per hour
                    format: float
                elems_per_hour:
                    type: number
                    description: number of elements submitted per hour
                    format: float
                elem_accuracy:
                    type: number
                    description: 'element accuracy: [0, 1]'
                    format: float
                ins_accuracy:
                    type: number
                    description: 'ins accuracy: [0, 1]'
                    format: float
        annostat.v1.GetLotCountReply:
            required:
                - total
                - ongoing
                - paused
                - finished
            type: object
            properties:
                total:
                    type: integer
                    format: int32
                ongoing:
                    type: integer
                    format: int32
                paused:
                    type: integer
                    format: int32
                finished:
                    type: integer
                    format: int32
        annostat.v1.GetLotLabelStatReply:
            type: object
            properties:
                cuboids:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/annostat.v1.GetLotLabelStatReply_Cuboid'
                    description: cuboid statistics, indexed by cuboid name
        annostat.v1.GetLotLabelStatReply_Cuboid:
            required:
                - ins_cnt
                - scales
            type: object
            properties:
                ins_cnt:
                    type: integer
                    description: number of cuboid objects with this label
                    format: int32
                scales:
                    type: array
                    items:
                        type: number
                        format: double
                    description: 'statistical cuboid scale on each axis: x, y, z'
        annostat.v1.GetLotStatByExecutorReply:
            required:
                - total
                - counters
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                counters:
                    type: array
                    items:
                        $ref: '#/components/schemas/annostat.v1.ExecutorCounter'
                    description: executor statistics
        annostat.v1.GetLotStatusReply:
            required:
                - total_elems
                - phases
            type: object
            properties:
                total_elems:
                    type: integer
                    format: int32
                phases:
                    type: array
                    items:
                        $ref: '#/components/schemas/annostat.v1.GetLotStatusReply_Phase'
                    description: status of each phase
        annostat.v1.GetLotStatusReply_Phase:
            required:
                - elems_to_work
                - estimated_days_left
            type: object
            properties:
                elems_to_work:
                    type: integer
                    description: number of elements in and before this phase
                    format: int32
                estimated_days_left:
                    type: number
                    description: |-
                        estimated number of days needed to close this phase (days needed to close prior phases are not included);
                         -1 means estimation is unavailable
                    format: float
        annostat.v1.GetOngoingLotsReply:
            required:
                - total
                - lots
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                lots:
                    type: array
                    items:
                        $ref: '#/components/schemas/annostat.v1.GetOngoingLotsReply_Lot'
        annostat.v1.GetOngoingLotsReply_Lot:
            required:
                - uid
                - name
                - data_type
                - data_size
                - phases
                - exp_end_time
                - created_at
            type: object
            properties:
                uid:
                    type: string
                name:
                    type: string
                data_type:
                    enum:
                        - unspecified
                        - image
                        - pointcloud
                        - fusion2d
                        - fusion3d
                    type: string
                    format: enum
                data_size:
                    type: integer
                    description: number of elements in the lot
                    format: int32
                phases:
                    type: array
                    items:
                        $ref: '#/components/schemas/annostat.v1.GetOngoingLotsReply_Phase'
                exp_end_time:
                    type: string
                    description: expected end time
                    format: date-time
                created_at:
                    type: string
                    format: date-time
        annostat.v1.GetOngoingLotsReply_Phase:
            required:
                - number
                - name
                - type
                - estimated_days_left
            type: object
            properties:
                number:
                    type: integer
                    description: phase number, starts from 1
                    format: int32
                name:
                    type: string
                type:
                    enum:
                        - unspecified
                        - label
                        - review
                    type: string
                    format: enum
                estimated_days_left:
                    type: number
                    description: |-
                        estimated number of days needed to close this phase (days needed to close prior phases are not included);
                         -1 means estimation is unavailable
                    format: float
        annostat.v1.GetOrderConversionReply:
            required:
                - orders
                - lots
            type: object
            properties:
                orders:
                    type: integer
                    format: int32
                lots:
                    type: integer
                    format: int32
        annostat.v1.GetOrderCountReply:
            required:
                - total
                - ongoing
                - paused
                - finished
            type: object
            properties:
                total:
                    type: integer
                    format: int32
                ongoing:
                    type: integer
                    format: int32
                paused:
                    type: integer
                    format: int32
                finished:
                    type: integer
                    format: int32
        annostat.v1.GetVersionReply:
            type: object
            properties:
                version:
                    type: string
        annostat.v1.ProductionByTimeReply:
            required:
                - units
            type: object
            properties:
                units:
                    type: array
                    items:
                        $ref: '#/components/schemas/annostat.v1.ProductionByTimeReply_Unit'
                    description: items and count in each time unit
        annostat.v1.ProductionByTimeReply_Unit:
            required:
                - time_range
                - items
            type: object
            properties:
                time_range:
                    allOf:
                        - $ref: '#/components/schemas/annostat.v1.TimeRange'
                    description: time range of this unit
                items:
                    type: object
                    additionalProperties:
                        type: number
                        format: float
                    description: item name and count
        annostat.v1.TimeRange:
            required:
                - from
                - to
            type: object
            properties:
                from:
                    type: string
                    description: time is within [from, to)
                    format: date-time
                to:
                    type: string
                    format: date-time
        iam.v1.BaseUser:
            required:
                - uid
                - name
                - avatar
            type: object
            properties:
                uid:
                    type: string
                    description: user/team uid
                name:
                    type: string
                    description: user/team name
                avatar:
                    type: string
                    description: user/team avatar url
tags:
    - name: Configs
    - name: Stats
