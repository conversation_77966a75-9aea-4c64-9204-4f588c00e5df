// source: gitlab.rp.konvery.work/platform/apis/iam/v1/role.proto
package biz

import (
	"context"
	"time"

	"iam/pkg/keto"

	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type Perm struct {
	ID        int64     `json:"id" gorm:"default:null"`
	Name      string    `json:"name" gorm:"default:null"`
	Class     string    `json:"class" gorm:"default:null"`
	CreatedAt time.Time `json:"created_at" gorm:"default:null"`
}

func (o *Perm) GetID() int64   { return o.ID }
func (o *Perm) GetUid() string { return o.Name }
func (o *Perm) UidFld() string { return Perm_Name }

const PermTableName = "perms"

var (
	perm_             = field.RegObject(&Perm{})
	PermUpdatableFlds = field.NewModel(PermTableName, perm_)

	Perm_Name  = field.Sname(&perm_.Name)
	Perm_Class = field.Sname(&perm_.Class)
)

type PermListFilter struct {
	Class       string
	NamePattern string
}

func (o *PermListFilter) Apply(tx Tx) Tx {
	if tx == nil {
		return nil
	}

	tbl := PermTableName + "."
	tx = repo.ApplyFieldFilter(tx, tbl+Perm_Class, o.Class)
	tx = repo.ApplyPatternFilter(tx, tbl+Perm_Name, o.NamePattern)
	return tx
}

type PermsRepo interface {
	repo.GenericRepo[Perm]

	Delete(context.Context, []string) error
	ListPermClass(context.Context) ([]string, error)
}

type PermsBiz struct {
	repo PermsRepo
	log  *log.Helper
}

func NewPermsBiz(repo PermsRepo, logger log.Logger) *PermsBiz {
	return &PermsBiz{repo: repo, log: log.NewHelper(logger)}
}

func (o *PermsBiz) Add(ctx context.Context, perms []string) ([]*Perm, error) {
	return o.repo.BatchCreate(ctx, lo.Map(perms, func(v string, _ int) *Perm {
		return &Perm{Name: v, Class: keto.PermClass(v)}
	}))
}

func (o *PermsBiz) Delete(ctx context.Context, perms []string) error {
	return o.repo.Delete(ctx, perms)
}

func (o *PermsBiz) List(ctx context.Context, filter *PermListFilter, pager Pager) ([]*Perm, PageToken, error) {
	return o.repo.List(ctx, filter, pager)
}

func (o *PermsBiz) ListPermClass(ctx context.Context) ([]string, error) {
	return o.repo.ListPermClass(ctx)
}
