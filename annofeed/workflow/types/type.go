package types

import (
	"context"
)

type Heartbeat func(ctx context.Context, details ...interface{})

type Event struct {
	Event     string         `json:"event,omitempty"`
	LotID     int64          `json:"lot_id,omitempty"`
	JobID     int64          `json:"job_id,omitempty"`
	JobAction string         `json:"job_action,omitempty"`
	Extra     map[string]any `json:"extra,omitempty"`
	TraceID   string         `json:"trace_id,omitempty"`
}
