package serial

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"reflect"

	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

type Map[K comparable, V any] map[K]V

func NewMap[K comparable, V any](e Map[K, V]) Map[K, V] {
	return Map[K, V](e)
}

// Scan scan value into the object, implements sql.Scanner interface
func (o *Map[K, V]) Scan(value interface{}) error {
	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("failed to unmarshal value: %#v", value)
	}

	return json.Unmarshal(bytes, o)
}

// Value return json value, implement driver.Valuer interface
func (o Map[K, V]) Value() (driver.Value, error) {
	if v := reflect.ValueOf(o); v.IsNil() {
		return nil, nil
	}
	return json.Marshal(o)
}

// GormDataType gorm common data type
func (Map[K, V]) GormDataType() string {
	return "serializable_map"
}

// GormDBDataType gorm db data type
func (Map[K, V]) GormDBDataType(db *gorm.DB, field *schema.Field) string {
	switch db.Dialector.Name() {
	case "sqlite", "mysql":
		return "JSON"
	case "postgres":
		return "JSONB"
	}
	return ""
}
