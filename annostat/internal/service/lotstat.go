package service

import (
	"context"

	"annostat/internal/biz"
	"gitlab.rp.konvery.work/platform/pkg/kid"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/apis/annostat/v1"
)

func (o *StatsService) GetLotLabelStat(ctx context.Context, req *annostat.GetLotStatusRequest) (
	*annostat.GetLotLabelStatReply, error) {
	filter := biz.NewAndFilters(&biz.JobstatListFilter{LotID: kid.ParseID(req.Uid)},
		biz.NewEqualFilter(biz.JobstatSfldJobID, 0))
	stat, err := o.jobstatRepo.GetByFilter(ctx, filter)
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, err
		}
		err = nil
		stat = &biz.Jobstat{}
	}

	cuboids := make(map[string]*annostat.GetLotLabelStatReply_Cuboid, len(stat.Cuboids))
	for k, c := range stat.Cuboids {
		if c.InsCnt <= 0 {
			continue
		}
		cuboids[k] = &annostat.GetLotLabelStatReply_Cuboid{
			InsCnt: int32(c.InsCnt),
			Scales: lo.Map(c.Scales[:], func(v float64, _ int) float64 { return v / float64(c.InsCnt) }),
		}
	}
	return &annostat.GetLotLabelStatReply{Cuboids: cuboids}, nil
}
