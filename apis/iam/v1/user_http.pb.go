// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: iam/v1/user.proto

package iam

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	types "gitlab.rp.konvery.work/platform/apis/types"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationUsersAddTag = "/iam.v1.Users/AddTag"
const OperationUsersAssumeUser = "/iam.v1.Users/AssumeUser"
const OperationUsersBatchCreateUsers = "/iam.v1.Users/BatchCreateUsers"
const OperationUsersCreateUser = "/iam.v1.Users/CreateUser"
const OperationUsersDeleteTag = "/iam.v1.Users/DeleteTag"
const OperationUsersDeleteUser = "/iam.v1.Users/DeleteUser"
const OperationUsersGetMe = "/iam.v1.Users/GetMe"
const OperationUsersGetMe2 = "/iam.v1.Users/GetMe2"
const OperationUsersGetMyFeperm = "/iam.v1.Users/GetMyFeperm"
const OperationUsersGetPerms = "/iam.v1.Users/GetPerms"
const OperationUsersGetUser = "/iam.v1.Users/GetUser"
const OperationUsersIsAllowed = "/iam.v1.Users/IsAllowed"
const OperationUsersListUser = "/iam.v1.Users/ListUser"
const OperationUsersLogin = "/iam.v1.Users/Login"
const OperationUsersLogout = "/iam.v1.Users/Logout"
const OperationUsersRefreshToken = "/iam.v1.Users/RefreshToken"
const OperationUsersSendAuthCode = "/iam.v1.Users/SendAuthCode"
const OperationUsersUpdateUser = "/iam.v1.Users/UpdateUser"

type UsersHTTPServer interface {
	AddTag(context.Context, *types.TagRequest) (*types.TagList, error)
	AssumeUser(context.Context, *AssumeUserRequest) (*LoginReply, error)
	BatchCreateUsers(context.Context, *BatchCreateUsersRequest) (*ListUserReply, error)
	CreateUser(context.Context, *CreateUserRequest) (*User, error)
	DeleteTag(context.Context, *types.TagRequest) (*types.TagList, error)
	DeleteUser(context.Context, *DeleteUserRequest) (*emptypb.Empty, error)
	// GetMe retrieve my info
	GetMe(context.Context, *emptypb.Empty) (*User, error)
	// GetMe2 retrieve my info; also returns the real user in an assume context
	GetMe2(context.Context, *emptypb.Empty) (*UserContext, error)
	// GetMyFeperm get my front-end permissions
	GetMyFeperm(context.Context, *emptypb.Empty) (*Feperm, error)
	// GetPerms get the permissions granted on a resource
	GetPerms(context.Context, *GetPermsRequest) (*GetPermsReply, error)
	GetUser(context.Context, *GetUserRequest) (*User, error)
	// IsAllowed check if the user is allowed to perform the actions
	IsAllowed(context.Context, *IsAllowedRequest) (*IsAllowedReply, error)
	ListUser(context.Context, *ListUserRequest) (*ListUserReply, error)
	Login(context.Context, *LoginRequest) (*LoginReply, error)
	// Logout clear HTTP cookies
	Logout(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	RefreshToken(context.Context, *emptypb.Empty) (*LoginReply, error)
	SendAuthCode(context.Context, *SendAuthCodeRequest) (*SendAuthCodeReply, error)
	UpdateUser(context.Context, *UpdateUserRequest) (*User, error)
}

func RegisterUsersHTTPServer(s *http.Server, srv UsersHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/users", _Users_CreateUser0_HTTP_Handler(srv))
	r.POST("/v1/users/batch", _Users_BatchCreateUsers0_HTTP_Handler(srv))
	r.PATCH("/v1/users/{user.uid}", _Users_UpdateUser0_HTTP_Handler(srv))
	r.DELETE("/v1/users/{uid}", _Users_DeleteUser0_HTTP_Handler(srv))
	r.GET("/v1/users/me", _Users_GetMe0_HTTP_Handler(srv))
	r.GET("/v1/users/me2", _Users_GetMe20_HTTP_Handler(srv))
	r.GET("/v1/users/me/feperm", _Users_GetMyFeperm0_HTTP_Handler(srv))
	r.GET("/v1/users/{uid}", _Users_GetUser0_HTTP_Handler(srv))
	r.GET("/v1/users", _Users_ListUser0_HTTP_Handler(srv))
	r.PUT("/v1/users/{uid}/tag", _Users_AddTag0_HTTP_Handler(srv))
	r.DELETE("/v1/users/{uid}/tag", _Users_DeleteTag0_HTTP_Handler(srv))
	r.POST("/v1/users/login", _Users_Login0_HTTP_Handler(srv))
	r.POST("/v1/users/logout", _Users_Logout0_HTTP_Handler(srv))
	r.POST("/v1/users/send-auth-code", _Users_SendAuthCode0_HTTP_Handler(srv))
	r.GET("/v1/users/{uid}/assume", _Users_AssumeUser0_HTTP_Handler(srv))
	r.GET("/v1/users/token/refresh", _Users_RefreshToken0_HTTP_Handler(srv))
	r.GET("/v1/users/{uid}/is-allowed", _Users_IsAllowed0_HTTP_Handler(srv))
	r.GET("/v1/users/{uid}/perms", _Users_GetPerms0_HTTP_Handler(srv))
}

func _Users_CreateUser0_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersCreateUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUser(ctx, req.(*CreateUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*User)
		return ctx.Result(200, reply)
	}
}

func _Users_BatchCreateUsers0_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BatchCreateUsersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersBatchCreateUsers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BatchCreateUsers(ctx, req.(*BatchCreateUsersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListUserReply)
		return ctx.Result(200, reply)
	}
}

func _Users_UpdateUser0_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateUserRequest
		if err := ctx.Bind(&in.User); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersUpdateUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUser(ctx, req.(*UpdateUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*User)
		return ctx.Result(200, reply)
	}
}

func _Users_DeleteUser0_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteUserRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersDeleteUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteUser(ctx, req.(*DeleteUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Users_GetMe0_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersGetMe)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetMe(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*User)
		return ctx.Result(200, reply)
	}
}

func _Users_GetMe20_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersGetMe2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetMe2(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserContext)
		return ctx.Result(200, reply)
	}
}

func _Users_GetMyFeperm0_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersGetMyFeperm)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetMyFeperm(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Feperm)
		return ctx.Result(200, reply)
	}
}

func _Users_GetUser0_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersGetUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUser(ctx, req.(*GetUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*User)
		return ctx.Result(200, reply)
	}
}

func _Users_ListUser0_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListUserRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersListUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUser(ctx, req.(*ListUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListUserReply)
		return ctx.Result(200, reply)
	}
}

func _Users_AddTag0_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in types.TagRequest
		if err := ctx.Bind(&in.Tags); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersAddTag)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddTag(ctx, req.(*types.TagRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*types.TagList)
		return ctx.Result(200, reply)
	}
}

func _Users_DeleteTag0_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in types.TagRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersDeleteTag)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteTag(ctx, req.(*types.TagRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*types.TagList)
		return ctx.Result(200, reply)
	}
}

func _Users_Login0_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LoginRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersLogin)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Login(ctx, req.(*LoginRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LoginReply)
		return ctx.Result(200, reply)
	}
}

func _Users_Logout0_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersLogout)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Logout(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Users_SendAuthCode0_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SendAuthCodeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersSendAuthCode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SendAuthCode(ctx, req.(*SendAuthCodeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SendAuthCodeReply)
		return ctx.Result(200, reply)
	}
}

func _Users_AssumeUser0_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AssumeUserRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersAssumeUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AssumeUser(ctx, req.(*AssumeUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LoginReply)
		return ctx.Result(200, reply)
	}
}

func _Users_RefreshToken0_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersRefreshToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RefreshToken(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LoginReply)
		return ctx.Result(200, reply)
	}
}

func _Users_IsAllowed0_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IsAllowedRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersIsAllowed)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IsAllowed(ctx, req.(*IsAllowedRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IsAllowedReply)
		return ctx.Result(200, reply)
	}
}

func _Users_GetPerms0_HTTP_Handler(srv UsersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetPermsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUsersGetPerms)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPerms(ctx, req.(*GetPermsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetPermsReply)
		return ctx.Result(200, reply)
	}
}

type UsersHTTPClient interface {
	AddTag(ctx context.Context, req *types.TagRequest, opts ...http.CallOption) (rsp *types.TagList, err error)
	AssumeUser(ctx context.Context, req *AssumeUserRequest, opts ...http.CallOption) (rsp *LoginReply, err error)
	BatchCreateUsers(ctx context.Context, req *BatchCreateUsersRequest, opts ...http.CallOption) (rsp *ListUserReply, err error)
	CreateUser(ctx context.Context, req *CreateUserRequest, opts ...http.CallOption) (rsp *User, err error)
	DeleteTag(ctx context.Context, req *types.TagRequest, opts ...http.CallOption) (rsp *types.TagList, err error)
	DeleteUser(ctx context.Context, req *DeleteUserRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetMe(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *User, err error)
	GetMe2(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *UserContext, err error)
	GetMyFeperm(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *Feperm, err error)
	GetPerms(ctx context.Context, req *GetPermsRequest, opts ...http.CallOption) (rsp *GetPermsReply, err error)
	GetUser(ctx context.Context, req *GetUserRequest, opts ...http.CallOption) (rsp *User, err error)
	IsAllowed(ctx context.Context, req *IsAllowedRequest, opts ...http.CallOption) (rsp *IsAllowedReply, err error)
	ListUser(ctx context.Context, req *ListUserRequest, opts ...http.CallOption) (rsp *ListUserReply, err error)
	Login(ctx context.Context, req *LoginRequest, opts ...http.CallOption) (rsp *LoginReply, err error)
	Logout(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	RefreshToken(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *LoginReply, err error)
	SendAuthCode(ctx context.Context, req *SendAuthCodeRequest, opts ...http.CallOption) (rsp *SendAuthCodeReply, err error)
	UpdateUser(ctx context.Context, req *UpdateUserRequest, opts ...http.CallOption) (rsp *User, err error)
}

type UsersHTTPClientImpl struct {
	cc *http.Client
}

func NewUsersHTTPClient(client *http.Client) UsersHTTPClient {
	return &UsersHTTPClientImpl{client}
}

func (c *UsersHTTPClientImpl) AddTag(ctx context.Context, in *types.TagRequest, opts ...http.CallOption) (*types.TagList, error) {
	var out types.TagList
	pattern := "/v1/users/{uid}/tag"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUsersAddTag))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in.Tags, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UsersHTTPClientImpl) AssumeUser(ctx context.Context, in *AssumeUserRequest, opts ...http.CallOption) (*LoginReply, error) {
	var out LoginReply
	pattern := "/v1/users/{uid}/assume"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUsersAssumeUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UsersHTTPClientImpl) BatchCreateUsers(ctx context.Context, in *BatchCreateUsersRequest, opts ...http.CallOption) (*ListUserReply, error) {
	var out ListUserReply
	pattern := "/v1/users/batch"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUsersBatchCreateUsers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UsersHTTPClientImpl) CreateUser(ctx context.Context, in *CreateUserRequest, opts ...http.CallOption) (*User, error) {
	var out User
	pattern := "/v1/users"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUsersCreateUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UsersHTTPClientImpl) DeleteTag(ctx context.Context, in *types.TagRequest, opts ...http.CallOption) (*types.TagList, error) {
	var out types.TagList
	pattern := "/v1/users/{uid}/tag"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUsersDeleteTag))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UsersHTTPClientImpl) DeleteUser(ctx context.Context, in *DeleteUserRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/users/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUsersDeleteUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UsersHTTPClientImpl) GetMe(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*User, error) {
	var out User
	pattern := "/v1/users/me"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUsersGetMe))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UsersHTTPClientImpl) GetMe2(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*UserContext, error) {
	var out UserContext
	pattern := "/v1/users/me2"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUsersGetMe2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UsersHTTPClientImpl) GetMyFeperm(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*Feperm, error) {
	var out Feperm
	pattern := "/v1/users/me/feperm"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUsersGetMyFeperm))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UsersHTTPClientImpl) GetPerms(ctx context.Context, in *GetPermsRequest, opts ...http.CallOption) (*GetPermsReply, error) {
	var out GetPermsReply
	pattern := "/v1/users/{uid}/perms"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUsersGetPerms))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UsersHTTPClientImpl) GetUser(ctx context.Context, in *GetUserRequest, opts ...http.CallOption) (*User, error) {
	var out User
	pattern := "/v1/users/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUsersGetUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UsersHTTPClientImpl) IsAllowed(ctx context.Context, in *IsAllowedRequest, opts ...http.CallOption) (*IsAllowedReply, error) {
	var out IsAllowedReply
	pattern := "/v1/users/{uid}/is-allowed"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUsersIsAllowed))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UsersHTTPClientImpl) ListUser(ctx context.Context, in *ListUserRequest, opts ...http.CallOption) (*ListUserReply, error) {
	var out ListUserReply
	pattern := "/v1/users"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUsersListUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UsersHTTPClientImpl) Login(ctx context.Context, in *LoginRequest, opts ...http.CallOption) (*LoginReply, error) {
	var out LoginReply
	pattern := "/v1/users/login"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUsersLogin))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UsersHTTPClientImpl) Logout(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/users/logout"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUsersLogout))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UsersHTTPClientImpl) RefreshToken(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*LoginReply, error) {
	var out LoginReply
	pattern := "/v1/users/token/refresh"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUsersRefreshToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UsersHTTPClientImpl) SendAuthCode(ctx context.Context, in *SendAuthCodeRequest, opts ...http.CallOption) (*SendAuthCodeReply, error) {
	var out SendAuthCodeReply
	pattern := "/v1/users/send-auth-code"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUsersSendAuthCode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UsersHTTPClientImpl) UpdateUser(ctx context.Context, in *UpdateUserRequest, opts ...http.CallOption) (*User, error) {
	var out User
	pattern := "/v1/users/{user.uid}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUsersUpdateUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in.User, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
