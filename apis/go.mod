module gitlab.rp.konvery.work/platform/apis

go 1.22

require (
	github.com/envoyproxy/protoc-gen-validate v1.0.2
	github.com/go-kratos/kratos/contrib/metrics/prometheus/v2 v2.0.0-20231113102135-421dbc7dae0f
	github.com/go-kratos/kratos/v2 v2.7.1
	github.com/google/gnostic v0.6.9
	github.com/gorilla/handlers v1.5.2
	github.com/iancoleman/strcase v0.3.0
	github.com/prometheus/client_golang v1.17.0
	github.com/samber/lo v1.38.1
	github.com/stretchr/testify v1.8.4
	gitlab.rp.konvery.work/platform/pkg v0.2.3
	google.golang.org/genproto/googleapis/api v0.0.0-20231016165738-49dd2c1f3d0b
	google.golang.org/grpc v1.59.0
	google.golang.org/protobuf v1.31.0
)

require (
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/felixge/httpsnoop v1.0.3 // indirect
	github.com/go-kratos/aegis v0.2.0 // indirect
	github.com/go-logr/logr v1.3.0 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-playground/form/v4 v4.2.1 // indirect
	github.com/golang/protobuf v1.5.3 // indirect
	github.com/google/gnostic-models v0.6.9-0.20230804172637-c7be7c783f49 // indirect
	github.com/google/uuid v1.5.0 // indirect
	github.com/gorilla/mux v1.8.0 // indirect
	github.com/matoous/go-nanoid/v2 v2.0.0 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.4 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/client_model v0.4.1-0.20230718164431-9a2bf3000d16 // indirect
	github.com/prometheus/common v0.44.0 // indirect
	github.com/prometheus/procfs v0.11.1 // indirect
	gitlab.rp.konvery.work/pkg/mq v0.0.2 // indirect
	go.opentelemetry.io/otel v1.20.0 // indirect
	go.opentelemetry.io/otel/metric v1.20.0 // indirect
	go.opentelemetry.io/otel/trace v1.20.0 // indirect
	go.temporal.io/sdk v1.24.0 // indirect
	golang.org/x/exp v0.0.0-20230905200255-921286631fa9 // indirect
	golang.org/x/net v0.18.0 // indirect
	golang.org/x/sync v0.5.0 // indirect
	golang.org/x/sys v0.16.0 // indirect
	golang.org/x/text v0.14.0 // indirect
	google.golang.org/genproto v0.0.0-20231016165738-49dd2c1f3d0b // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20231030173426-d783a09b4405 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/gorm v1.25.5 // indirect
)

// fix [Import of gnostic-models and gnostic generated code leads to panic #397](https://github.com/google/gnostic/issues/397)
replace github.com/google/gnostic => github.com/google/gnostic v0.6.10-0.20230825194252-836f55b2639b
