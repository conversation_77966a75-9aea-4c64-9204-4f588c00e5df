syntax = "proto3";

package anno.v1;

import "google/protobuf/timestamp.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";
import "anno/v1/widget.proto";
import "iam/v1/type.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anno/v1;anno";
option java_multiple_files = true;
option java_package = "anno.v1";

// 一帧数据，包含一个或多个待标注文件
message Element {
  option (openapi.v3.schema) = {
    required: ["index", "name", "type", "datas"]
  };

  message Type {
    enum Enum {
      unspecified = 0;
      // one image in an element
      image = 1;
      // one point-cloud file in an element
      pointcloud = 2;
      // multiple image files in an element
      fusion2d = 3;
      // multiple image files and a point-cloud file in an element
      fusion3d = 4;
      // a multi-frame fusion point-cloud file and related pictures in a frame series
      fusion4d = 5;
    }
  }

  // element index in the lot dataset
  int32 index = 1;
  // name of the element: innermost-folder/frame-index
  string name = 2;
  Type.Enum type = 3 [(validate.rules).enum = {defined_only: true, not_in: [0]}];
  repeated Rawdata datas = 4;

  // 标注结果；
  // 仅用于结果导出，或在导入时用于传输已经有标注信息的数据；
  // 标注过程中，请使用 Job 里的相关字段
  ElementAnno anno = 5;
}

// 与待标注文件相关的参数或变换矩阵
message RawdataParam {
  option (openapi.v3.schema) = {
    required: ["type", "column_cnt", "data"]
  };

  message Type {
    enum Enum {
      // matrix transformer; row first: r00,r01,r02,...,r10,r11,r12,...
      matrix = 0;
      // extrinsic params: x,y,z,qx,qy,qz,qw
      extrinsic = 1;
      // intrinsic params: fx,fy,cx,cy
      intrinsic = 2;
      // distortion params: distortion_type,k1,k2,...
      distortion = 3;
    }
  }
  message DistortionType {
    enum Enum {
      // Brown-Conrady model (radtan): k1,k2,p1,p2,[k3,[k4,k5,k6]]
      pinhole = 0;
      // Omnidirectional:
      mei = 1;
      // KB8(fisheye) model: k1,k2,k3,k4
      kb = 2;
      // Scaramuzza: affine_parameters(ac,ad,ae,cx,cy), number-of-inv_poly_parameters, inv_poly_parameters(p0,p1,p2,...)
      ocam = 3;
      // Bosch Fisheye model: same as ocam
      bosch_fisheye = -1001;
    }
  }

  Type.Enum type = 1 [(validate.rules).enum = {defined_only: true}];
  // number of columns in the data
  int32 column_cnt = 2;
  // if type is distortion, the 1st number is the distortion model type (DistortionType)
  repeated double data = 3;

  // message Other {
  //   // reference frame offset for omnidirectional model
  //   double xi = 1;
  // }
  // // other parameters
  // Other other = 4;
}

// 待标注文件，比如一张图片、点云的一帧
message Rawdata {
  option (openapi.v3.schema) = {
    required: ["name", "title", "type", "format", "url", "size", "sha256", "meta", "ins", "transform"]
  };

  message ImageMeta {
    option (openapi.v3.schema) = {
      required: ["width", "height"]
    };

    int32 width = 1;
    int32 height = 2;
    // camera name
    string camera = 3;
  }
  message PCDMeta {
    option (openapi.v3.schema) = {
      required: ["points"]
    };

    // number of points in the pcd file
    int32 points = 1;
    // viewpoint of the point cloud in the point cloud's coordinate system
    RawdataParam viewpoint = 2;

    // origin point of the point cloud in world coordinate system when point cloud is not in world coordinate system,
    // in the form of [x,y,z,qx,qy,qz,qw]
    repeated double pose = 3;
  }
  message Meta {
    ImageMeta image = 1;
    PCDMeta pcd = 2;
  }

  message Type {
    enum Enum {
      unspecified = 0;
      image = 1;
      pointcloud = 2;
    }
  }

  message Format {
    enum Enum {
      unspecified = 0;
      json = 1;
      png = 2;
      jpg = 3;
      pcd = 4;
      // indicate that GET rawdata request will return types.Filelist instead of the rawdata itself
      filelist = 5;
      webp = 6;
    }
  }

  message Embedding {
    // // file/inline
    // string type = 1;

    // embedding file URL
    string url = 2;
  }

  // file pathname
  string name = 1;
  // data type
  Type.Enum type = 2 [(validate.rules).enum = {defined_only: true, not_in: [0]}];
  // data format
  Format.Enum format = 3 [(validate.rules).enum = {defined_only: true, not_in: [0]}];
  string url = 4;
  // number of bytes in the file
  double size = 5;
  // SHA-256 of the file
  string sha256 = 6;
  // display name (sensor name)
  string title = 7;
  // 变换参数，可以将世界坐标系下的点映射到图片上
  repeated RawdataParam transform = 8;

  // metadata of rawdata
  Meta meta = 9;

  // 识别出的物体列表；
  // 仅用于结果导出，或在导入时用于传输已经有标注信息的数据；
  // 标注过程中，请使用 Job 里的相关字段
  repeated Object ins = 10;

  // original pathname of the rawdata
  string orig_name = 11;

  // file embedding
  Embedding embedding = 12;
}

//message Voxel {
//  option (openapi.v3.schema) = {
//    required: ["x", "y", "z"]
//  };
//
//  double x = 1;
//  double y = 2;
//  double z = 3;
//}

message Direction {
  option (openapi.v3.schema) = {
    required: ["toward"]
  };

  // origin point; if empty, it is the widget center point;
  // x, y if 2D; x, y, z if 3D
  repeated double origin = 1;
  // toward point;
  // x, y if 2D; x, y, z if 3D
  repeated double toward = 2;
}

message AttrAndValues {
  option (openapi.v3.schema) = {
    required: ["name", "values"]
  };

  // name of the attribute
  string name = 1;
  // values of the attribute
  repeated string values = 2;
}

message Object {
  option (openapi.v3.schema) = {
    required: ["uuid", "label"]
  };

  message Widget {
    option (openapi.v3.schema) = {
      required: ["name", "data"]
    };

    // widget name
    WidgetName.Enum name = 1 [(validate.rules).enum = {defined_only: true, not_in: [0]}];
    // characteristic values of geometric shapes, or bitmap origin (left, top)
    repeated double data = 2;
    // gaps within the widget if any
    repeated Widget gaps = 3;

    // bitmap file URL or data URI, e.g. data:image/png;base64,<base64-encoded-file-content>
    // pointcloud file URL or data URI, e.g. data:application/pcd;base64,<base64-encoded-file-content>
    string uri = 4;
    // forward direction. if origin is null, then the center point of the widget is implied.
    Direction forward = 5;
    // number of points within the object (in 3D segmentation tasks)
    int32 point_cnt = 6;
    // class ID in RawdataAnno.Segmentation; zero if the task is not a segmentation task
    int32 seg_class_id = 7;
    // line type used to draw the widget
    WidgetLineType.Enum line_type = 8 [(validate.rules).enum = {defined_only: true}];
    // related parent lines
    repeated string parent_lines = 9;
  }
  message Label {
    option (openapi.v3.schema) = {
      required: ["name"]
    };

    // label name
    string name = 1;
    // widget will be null if compound is not null
    Widget widget = 2;
    // attributes associated with the object
    repeated AttrAndValues attrs = 3;
  }

  // UUID of the object
  string uuid = 1;
  // 一个任务包中，相同的 track_id 表示同一个实体对象。
  // 命名可以采用 Job.Uid + Label.name + index 的模式，例如：“xxx-car-1”
  string track_id = 2;
  Label label = 3;
  // 在连续帧标注中，表示该物体从下一帧开始将不复存在
  bool vanish_later = 4;
  // // 在连续帧标注中，表示该物体是否是插值帧
  // bool interpolated = 5;
  reserved 5;

  message Compound {
    option (openapi.v3.schema) = {
      required: ["parts"]
    };
    // referenced component object UUIDs
    repeated string parts = 1;
  }
  // it is a compounded object if not null
  Compound compound = 6;

  message Source {
    enum Enum {
      unspecified = 0;
      // by a labeler
      manual = 1;
      // by a model
      prelabel = 2;
      // by interpolation (frame series)
      interpolation = 3;
      // by projection (3D to 2D)
      projection = 4;
    }
  }
  // how the object is created
  Source.Enum source = 7 [(validate.rules).enum = {defined_only: true}];
}

//////////////////
// annotations
//////////////////

// annotations of a rawdata
message RawdataAnno {
  option (openapi.v3.schema) = {
    required: ["name", "objects", "attrs"]
  };

  // name of the rawdata
  string name = 1;
  repeated Object objects = 2;
  repeated AttrAndValues attrs = 3;
  // segmentation result of the rawdata; null if the task is not a segmentation task
  Segmentation segmentation = 4;
  // original pathname of the rawdata
  string orig_name = 5;
  // url of the rawdata, only available during exporting anno results
  string url = 6;

  message Metadata {
    // rawdata type
    Rawdata.Type.Enum type = 1;
    // camera name if the rawdata is an image (ImageMeta.camera)
    string camera = 2;
  }
  // metadata
  Metadata metadata = 7;
}

message Segmentation {
  message Class {
    option (openapi.v3.schema) = {
      required: ["id", "name"]
    };

    // serial number, start from 1
    int32 id = 1;
    // it is Object.uuid for instance segmentation; it is label name for semantic/panoptic segmentation
    string name = 2;
  }

  message RLE {
    option (openapi.v3.schema) = {
      required: ["runs"]
    };

    // number of consecutive points having the same class ID
    repeated int32 runs = 1;
    // Class.id of the points at the corresponding index in runs;
    // if empty, it implies a sequence of 0 and 1, starting with 0: [0, 1, 0, 1, ...]
    repeated int32 vals = 2;
  }

  // classes definition; if empty, class name is RawdataAnno.Object.uuid with the matching seg_class_id
  repeated Class classes = 1;

  // unpacked RLE; either rle or rle_pack is set, not both
  RLE rle = 2;
  // packed RLE; serialize RLE using JSON, then compress using gzip, then encode in base64;
  // format: data:application/json;gzip;base64,[base64-encoded-content]
  //      or http://packed-RLE-file-url (file format: RLE;json;gzip\ngzip-encoded-content)
  string rle_pack = 3;
}

// annotations of an element (frame)
message ElementAnno {
  option (openapi.v3.schema) = {
    required: ["index", "name", "ins_cnt", "attrs", "rawdata_annos"]
  };

  message Metadata {
    message Executor {
      option (openapi.v3.schema) = {
        required: ["user", "submit_at"]
      };

      iam.v1.BaseUser user = 1;
      google.protobuf.Timestamp submit_at = 2;
      int32 phase = 3;
    }

    // executor of each phase
    repeated Executor executors = 1;
  }

  // element index in the lot dataset
  int32 index = 1;
  // name of the element: innermost-folder/frame-index
  string name = 2;
  // 相应位置的元素对应 Element 的 datas 相应位置的结果
  repeated RawdataAnno rawdata_annos = 3;
  repeated AttrAndValues attrs = 4;

  // number of objects annotated in the element
  int32 ins_cnt = 5;
  // metadata of the element annos
  Metadata metadata = 6;
  Segmentation3d segmentation3d = 7;
}

message Segmentation3d{
  Segmentation3dResult result = 1;
  repeated Segmentation3dStatistic statistic = 2;
}
message Segmentation3dResult{
  repeated string category = 1;
  repeated int32 instance_id = 2;
}

message Segmentation3dStatistic{
  option (openapi.v3.schema) = {
    required: ["category_name", "num"]
  };
  string category_name = 1;
  int32 num = 2;
  repeated Segmentation3dInstance instances = 3;
}
message Segmentation3dInstance{
  option (openapi.v3.schema) = {
    required: ["instance_id", "num"]
  };
  int32 instance_id = 1;
  int32 num = 2;
}

// annotations
message ExportAnnos {
  option (openapi.v3.schema) = {
    required: ["element_annos"]
  };

  repeated ElementAnno element_annos = 1;
  string job_uid = 2;
}
