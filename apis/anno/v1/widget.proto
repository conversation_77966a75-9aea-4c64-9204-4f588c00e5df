syntax = "proto3";

package anno.v1;

option go_package = "gitlab.rp.konvery.work/platform/apis/anno/v1;anno";
option java_multiple_files = true;
option java_package = "anno.v1";

message WidgetName {
  enum Enum {
    unspecified = 0;
    // 2D矩形框
    box2d = 1;
    // 平面伪3D矩形框
    pscuboid = 2;
    // 3D矩形框
    cuboid = 3;
    // 2D多边形框
    poly2d = 4;
    // 3D多边形框
    poly3d = 5;
    // 2D 折线（含直线）
    line2d = 6;
    // 3D 折线（含直线）
    line3d = 7;
    // 2D 点
    point2d = 8;
    // 3D 点
    point3d = 9;
    // 位图
    bitmap = 10;
  }
}

message WidgetLineType {
  enum Enum {
    line = 0;
    // Catmull–Rom spline
    crspline = 1;
    // center line
    centerline = 2;
  }
}

/*

// 2D矩形框
Widget {
    name: "box2d"
    data: [5]double[x, y, width, height, alpha]
            // (x,y)：中心点坐标，单位：pixel
            // width, height: 宽和高，单位：pixel
            // alpha: 以(x,y)为中心、从12点钟位置开始的顺时针方向的旋转角度，
            //        单位：radian
}

// 平面伪3D矩形框
Widget {
    name: "pscuboid"
    data: [10]double[x1, y1, width1, height1, x2, y2, width2, height2, alpha]
            // (x1,y1)：实框中心点坐标，单位：pixel
            // (x2,y2)：虚框中心点坐标，单位：pixel
            // width1, height1: 宽和高，单位：pixel
            // width2, height2: 宽和高，单位：pixel
            // alpha: 以(x,y)为中心、从12点钟位置开始的顺时针方向的旋转角度，
            //        单位：radian
}

// 3D矩形框
Widget {
    name: "cuboid"
    // rotation in quaternion
    data: [10]double[x, y, z, sx, sy, sz, qx, qy, qz, qw]
            // (x,y,z)：中心点坐标，单位：m
            // (sx,sy,sz): x, y, z 轴上的长度，单位：m
            // (qx,qy,qz,qw): rotation as a unit quaternion as in the SciPy convention
}

// 2D多边形框
Widget {
    name: "poly2d"
    data: []double[x1, y1, x2, y2, ...] // 顶点坐标(x,y)列表
}

// 3D多边形框
Widget {
    name: "poly3d"
    data: []double[x1, y1, z1, x2, y2, z2, ...] // 顶点坐标(x,y,z)列表
}

// 2D 折线（含直线）
Widget {
    name: "line2d"
    data: []double[x1, y1, x2, y2, ...] // 顶点(x,y)坐标
}

// 3D 折线（含直线）
Widget {
    name: "line3d"
    data: []double[x1, y1, z1, x2, y2, z2, ...] // 顶点(x,y,z)坐标
}

// 2D 点
Widget {
    name: "point2d"
    data: [2]double[x, y] // 点坐标
}

// 3D 点
Widget {
    name: "point3d"
    data: [3]double[x, y, z] // 点坐标
}

*/
