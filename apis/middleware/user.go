package middleware

import (
	"context"
	"encoding/base64"
	"fmt"
	stdhttp "net/http"

	"github.com/go-kratos/kratos/v2/encoding"
	"github.com/go-kratos/kratos/v2/encoding/json"
	"github.com/go-kratos/kratos/v2/metadata"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/http"
	"gitlab.rp.konvery.work/platform/apis/client"
	"gitlab.rp.konvery.work/platform/apis/iam/v1"
	"gitlab.rp.konvery.work/platform/apis/middleware/authfilter"
	"gitlab.rp.konvery.work/platform/pkg/errors"
)

var enc = encoding.GetCodec("json")

func init() {
	json.MarshalOptions.UseProtoNames = true
}

// LoadUser is a middleware to load user.
func LoadUser(noauth []authfilter.NoauthFilter) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			if md, ok := metadata.FromServerContext(ctx); ok {
				var user, assumeBy *iam.User
				if userStr := md.Get(client.HeaderUser); userStr != "" {
					bs, err := base64.StdEncoding.DecodeString(userStr)
					if err != nil {
						return nil, fmt.Errorf("failed to decode user info: %w", err)
					}
					uc, err := parseUserCtx(bs)
					if err != nil {
						return nil, fmt.Errorf("failed to unmarshal user info: %w", err)
					}
					user, assumeBy = uc.User, uc.AssumeBy
				} else if uid := md.Get(client.HeaderUserUid); uid != "" {
					ctx = client.NewCtxWithRPCIdentity(ctx, &client.RPCIdentity{Uid: uid})
					user, err = client.GetUser(ctx, uid)
					if err != nil {
						return nil, err
					}
				}

				ctx, _ = setCtxUser(ctx, user, assumeBy)
			}

			if tr, ok := transport.FromServerContext(ctx); ok {
				if client.UserFromCtx(ctx).IsEmpty() && !authfilter.SkipAuth(noauth, tr.Operation()) {
					return nil, errors.NewErrUnauthorized()
				}
			}
			return handler(ctx, req)
		}
	}
}

// HTTPLoadUserFilter is a HTTP middleware to load user.
func HTTPLoadUserFilter(noauth []authfilter.NoauthFilter) http.FilterFunc {
	return func(h stdhttp.Handler) stdhttp.Handler {
		return Handler(func(w stdhttp.ResponseWriter, r *stdhttp.Request) {
			ctx := r.Context()
			var user, assumeBy *iam.User
			if userStr := r.Header.Get(client.HeaderUser); userStr != "" {
				bs, err := base64.StdEncoding.DecodeString(userStr)
				if err != nil {
					writeErr(w, errors.NewErrServerError().WithCause(fmt.Errorf("failed to decode user info")))
					return
				}
				uc, err := parseUserCtx(bs)
				if err != nil {
					writeErr(w, errors.NewErrServerError().WithCause(fmt.Errorf("failed to unmarshal user info: %w", err)))
					return
				}
				user, assumeBy = uc.User, uc.AssumeBy
			} else if uid := r.Header.Get(client.HeaderUserUid); uid != "" {
				var err error
				ctx = client.NewCtxWithRPCIdentity(ctx, &client.RPCIdentity{Uid: uid})
				user, err = client.GetUser(ctx, uid)
				if err != nil {
					writeErr(w, errors.NewErrServerError().WithCause(fmt.Errorf("failed to get user info: %w", err)))
					return
				}
			}

			ctx, newctx := setCtxUser(ctx, user, assumeBy)
			if newctx {
				r = r.WithContext(ctx)
			}

			if client.UserFromCtx(ctx).IsEmpty() && !authfilter.SkipAuth(noauth, r.URL.Path) {
				writeErr(w, errors.NewErrUnauthorized())
				return
			}

			h.ServeHTTP(w, r)
		})
	}
}

func parseUserCtx(bs []byte) (*iam.UserContext, error) {
	uc := &iam.UserContext{}
	if err := enc.Unmarshal(bs, &uc); err != nil {
		return nil, err
	}
	if !uc.IsEmpty() {
		return uc, nil
	}

	// compatible with old user context
	u := &iam.User{}
	if err := enc.Unmarshal(bs, &u); err != nil {
		return nil, err
	}
	if u.Uid == "" {
		return uc, nil
	}
	return &iam.UserContext{User: u}, nil
}

func setCtxUser(ctx context.Context, user, assumeBy *iam.User) (ctx_ context.Context, newctx bool) {
	if user.GetUid() == "" {
		return ctx, false
	}
	uc := client.UserFromCtx(ctx)
	if uc == nil {
		return client.NewCtxWithUser(ctx, user, assumeBy), true
	}
	// fill into the slot reserved by a previous middleware, e.g. Log
	uc.User, uc.AssumeBy = user, assumeBy
	return ctx, false
}

type Handler func(w stdhttp.ResponseWriter, r *stdhttp.Request)

func (o Handler) ServeHTTP(w stdhttp.ResponseWriter, r *stdhttp.Request) {
	o(w, r)
}

func writeErr(w stdhttp.ResponseWriter, err *errors.Error) {
	w.WriteHeader(int(err.Code))
	data, _ := enc.Marshal(err)
	w.Write(data)
}
