package middleware

import (
	"io"
	stdhttp "net/http"
	"time"

	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/gorilla/handlers"
	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

// HTTPLogFilter returns a http.FilterFunc that logs the request.
func HTTPLogFilter(logger log.Logger) http.FilterFunc {
	return func(h stdhttp.Handler) stdhttp.Handler {
		return handlers.CustomLoggingHandler(nil, h,
			func(_ io.Writer, params handlers.LogFormatterParams) {
				r := params.Request
				d := time.Since(params.TimeStamp).Seconds()
				log.Log(logger, r.Context(), log.LevelInfo, "API call "+lo.Ternary(params.StatusCode < 400, "ok", "failed"),
					"method", r.Method, "uri", r.<PERSON>questUR<PERSON>,
					"content-length", params.Size, "code", params.StatusCode, "latency", d)
			})
	}
}

// HTTPLogErrorHandler returns a http.HandlerFunc that logs when the request fails.
func HTTPLogErrorHandler(logger log.Logger, h http.HandlerFunc) http.HandlerFunc {
	return func(ctx http.Context) (err error) {
		ts := time.Now()
		err = h(ctx)
		if err == nil {
			return
		}

		r := ctx.Request()
		d := time.Since(ts).Seconds()
		log.Log(logger, ctx, log.LevelError, "API call failed",
			"method", r.Method, "uri", r.RequestURI, "error", err, "latency", d)
		return
	}
}
