// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"annout/internal/conf"
	"annout/internal/server"
	"annout/internal/service"
	"annout/workflow"
	"annout/workflow/reap"
	"github.com/go-kratos/kratos/v2/log"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(string2 string, confServer *conf.Server, data *conf.Data, logger log.Logger) (*App, func(), error) {
	configsService := service.NewConfigsService()
	backgroundTask := workflow.NewWorkflowStarter()
	reapersService := service.NewReapersService(backgroundTask)
	grpcServer := server.NewGRPCServer(confServer, configsService, reapersService, logger)
	httpServer := server.NewHTTPServer(confServer, configsService, reapersService, logger)
	activities := reap.NewActivities(logger)
	app := newApp(logger, grpcServer, httpServer, activities)
	return app, func() {
	}, nil
}
