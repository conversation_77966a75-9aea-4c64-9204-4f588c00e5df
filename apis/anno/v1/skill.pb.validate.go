// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/skill.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UpdateSkillRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateSkillRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSkillRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateSkillRequestMultiError, or nil if none found.
func (m *UpdateSkillRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSkillRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSkill()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSkillRequestValidationError{
					field:  "Skill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSkillRequestValidationError{
					field:  "Skill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSkill()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSkillRequestValidationError{
				field:  "Skill",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateSkillRequestMultiError(errors)
	}

	return nil
}

// UpdateSkillRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateSkillRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateSkillRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSkillRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSkillRequestMultiError) AllErrors() []error { return m }

// UpdateSkillRequestValidationError is the validation error returned by
// UpdateSkillRequest.Validate if the designated constraints aren't met.
type UpdateSkillRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSkillRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSkillRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSkillRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSkillRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSkillRequestValidationError) ErrorName() string {
	return "UpdateSkillRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateSkillRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSkillRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSkillRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSkillRequestValidationError{}

// Validate checks the field values on DeleteSkillRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteSkillRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteSkillRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteSkillRequestMultiError, or nil if none found.
func (m *DeleteSkillRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteSkillRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return DeleteSkillRequestMultiError(errors)
	}

	return nil
}

// DeleteSkillRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteSkillRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteSkillRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteSkillRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteSkillRequestMultiError) AllErrors() []error { return m }

// DeleteSkillRequestValidationError is the validation error returned by
// DeleteSkillRequest.Validate if the designated constraints aren't met.
type DeleteSkillRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteSkillRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteSkillRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteSkillRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteSkillRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteSkillRequestValidationError) ErrorName() string {
	return "DeleteSkillRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteSkillRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteSkillRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteSkillRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteSkillRequestValidationError{}

// Validate checks the field values on GetSkillRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetSkillRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSkillRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSkillRequestMultiError, or nil if none found.
func (m *GetSkillRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSkillRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return GetSkillRequestMultiError(errors)
	}

	return nil
}

// GetSkillRequestMultiError is an error wrapping multiple validation errors
// returned by GetSkillRequest.ValidateAll() if the designated constraints
// aren't met.
type GetSkillRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSkillRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSkillRequestMultiError) AllErrors() []error { return m }

// GetSkillRequestValidationError is the validation error returned by
// GetSkillRequest.Validate if the designated constraints aren't met.
type GetSkillRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSkillRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSkillRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSkillRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSkillRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSkillRequestValidationError) ErrorName() string { return "GetSkillRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetSkillRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSkillRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSkillRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSkillRequestValidationError{}

// Validate checks the field values on ListSkillRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListSkillRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSkillRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSkillRequestMultiError, or nil if none found.
func (m *ListSkillRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSkillRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListSkillRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := Skill_Type_Enum_name[int32(m.GetType())]; !ok {
		err := ListSkillRequestValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for NamePattern

	if len(errors) > 0 {
		return ListSkillRequestMultiError(errors)
	}

	return nil
}

// ListSkillRequestMultiError is an error wrapping multiple validation errors
// returned by ListSkillRequest.ValidateAll() if the designated constraints
// aren't met.
type ListSkillRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSkillRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSkillRequestMultiError) AllErrors() []error { return m }

// ListSkillRequestValidationError is the validation error returned by
// ListSkillRequest.Validate if the designated constraints aren't met.
type ListSkillRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSkillRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSkillRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSkillRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSkillRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSkillRequestValidationError) ErrorName() string { return "ListSkillRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListSkillRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSkillRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSkillRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSkillRequestValidationError{}

// Validate checks the field values on ListSkillReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListSkillReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSkillReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListSkillReplyMultiError,
// or nil if none found.
func (m *ListSkillReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSkillReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetSkills() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListSkillReplyValidationError{
						field:  fmt.Sprintf("Skills[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListSkillReplyValidationError{
						field:  fmt.Sprintf("Skills[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListSkillReplyValidationError{
					field:  fmt.Sprintf("Skills[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListSkillReplyMultiError(errors)
	}

	return nil
}

// ListSkillReplyMultiError is an error wrapping multiple validation errors
// returned by ListSkillReply.ValidateAll() if the designated constraints
// aren't met.
type ListSkillReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSkillReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSkillReplyMultiError) AllErrors() []error { return m }

// ListSkillReplyValidationError is the validation error returned by
// ListSkillReply.Validate if the designated constraints aren't met.
type ListSkillReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSkillReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSkillReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSkillReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSkillReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSkillReplyValidationError) ErrorName() string { return "ListSkillReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListSkillReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSkillReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSkillReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSkillReplyValidationError{}

// Validate checks the field values on Skill with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Skill) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Skill with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SkillMultiError, or nil if none found.
func (m *Skill) ValidateAll() error {
	return m.validate(true)
}

func (m *Skill) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := Skill_Type_Enum_name[int32(m.GetType())]; !ok {
		err := SkillValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Name

	// no validation rules for MaxLevel

	// no validation rules for Langs

	if len(errors) > 0 {
		return SkillMultiError(errors)
	}

	return nil
}

// SkillMultiError is an error wrapping multiple validation errors returned by
// Skill.ValidateAll() if the designated constraints aren't met.
type SkillMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SkillMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SkillMultiError) AllErrors() []error { return m }

// SkillValidationError is the validation error returned by Skill.Validate if
// the designated constraints aren't met.
type SkillValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SkillValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SkillValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SkillValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SkillValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SkillValidationError) ErrorName() string { return "SkillValidationError" }

// Error satisfies the builtin error interface
func (e SkillValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSkill.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SkillValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SkillValidationError{}

// Validate checks the field values on UserSkill with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserSkill) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserSkill with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserSkillMultiError, or nil
// if none found.
func (m *UserSkill) ValidateAll() error {
	return m.validate(true)
}

func (m *UserSkill) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if _, ok := Skill_Type_Enum_name[int32(m.GetType())]; !ok {
		err := UserSkillValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Name

	// no validation rules for Level

	if len(errors) > 0 {
		return UserSkillMultiError(errors)
	}

	return nil
}

// UserSkillMultiError is an error wrapping multiple validation errors returned
// by UserSkill.ValidateAll() if the designated constraints aren't met.
type UserSkillMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserSkillMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserSkillMultiError) AllErrors() []error { return m }

// UserSkillValidationError is the validation error returned by
// UserSkill.Validate if the designated constraints aren't met.
type UserSkillValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserSkillValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserSkillValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserSkillValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserSkillValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserSkillValidationError) ErrorName() string { return "UserSkillValidationError" }

// Error satisfies the builtin error interface
func (e UserSkillValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserSkill.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserSkillValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserSkillValidationError{}

// Validate checks the field values on GetUserSkillRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserSkillRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserSkillRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserSkillRequestMultiError, or nil if none found.
func (m *GetUserSkillRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserSkillRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if all {
		switch v := interface{}(m.GetScope()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserSkillRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserSkillRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScope()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserSkillRequestValidationError{
				field:  "Scope",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserSkillRequestMultiError(errors)
	}

	return nil
}

// GetUserSkillRequestMultiError is an error wrapping multiple validation
// errors returned by GetUserSkillRequest.ValidateAll() if the designated
// constraints aren't met.
type GetUserSkillRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserSkillRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserSkillRequestMultiError) AllErrors() []error { return m }

// GetUserSkillRequestValidationError is the validation error returned by
// GetUserSkillRequest.Validate if the designated constraints aren't met.
type GetUserSkillRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserSkillRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserSkillRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserSkillRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserSkillRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserSkillRequestValidationError) ErrorName() string {
	return "GetUserSkillRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserSkillRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserSkillRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserSkillRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserSkillRequestValidationError{}

// Validate checks the field values on GetUserSkillReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetUserSkillReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserSkillReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserSkillReplyMultiError, or nil if none found.
func (m *GetUserSkillReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserSkillReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetSkills() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUserSkillReplyValidationError{
						field:  fmt.Sprintf("Skills[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUserSkillReplyValidationError{
						field:  fmt.Sprintf("Skills[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUserSkillReplyValidationError{
					field:  fmt.Sprintf("Skills[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetUserSkillReplyMultiError(errors)
	}

	return nil
}

// GetUserSkillReplyMultiError is an error wrapping multiple validation errors
// returned by GetUserSkillReply.ValidateAll() if the designated constraints
// aren't met.
type GetUserSkillReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserSkillReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserSkillReplyMultiError) AllErrors() []error { return m }

// GetUserSkillReplyValidationError is the validation error returned by
// GetUserSkillReply.Validate if the designated constraints aren't met.
type GetUserSkillReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserSkillReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserSkillReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserSkillReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserSkillReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserSkillReplyValidationError) ErrorName() string {
	return "GetUserSkillReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserSkillReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserSkillReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserSkillReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserSkillReplyValidationError{}

// Validate checks the field values on ListUsersSkillRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListUsersSkillRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUsersSkillRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListUsersSkillRequestMultiError, or nil if none found.
func (m *ListUsersSkillRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUsersSkillRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetScope()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListUsersSkillRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListUsersSkillRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScope()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListUsersSkillRequestValidationError{
				field:  "Scope",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TeamUid

	for idx, item := range m.GetSkills() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUsersSkillRequestValidationError{
						field:  fmt.Sprintf("Skills[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUsersSkillRequestValidationError{
						field:  fmt.Sprintf("Skills[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUsersSkillRequestValidationError{
					field:  fmt.Sprintf("Skills[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListUsersSkillRequestMultiError(errors)
	}

	return nil
}

// ListUsersSkillRequestMultiError is an error wrapping multiple validation
// errors returned by ListUsersSkillRequest.ValidateAll() if the designated
// constraints aren't met.
type ListUsersSkillRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUsersSkillRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUsersSkillRequestMultiError) AllErrors() []error { return m }

// ListUsersSkillRequestValidationError is the validation error returned by
// ListUsersSkillRequest.Validate if the designated constraints aren't met.
type ListUsersSkillRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUsersSkillRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUsersSkillRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUsersSkillRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUsersSkillRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUsersSkillRequestValidationError) ErrorName() string {
	return "ListUsersSkillRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListUsersSkillRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUsersSkillRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUsersSkillRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUsersSkillRequestValidationError{}

// Validate checks the field values on ListUsersSkillReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListUsersSkillReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUsersSkillReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListUsersSkillReplyMultiError, or nil if none found.
func (m *ListUsersSkillReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUsersSkillReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUsersSkillReplyValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUsersSkillReplyValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUsersSkillReplyValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListUsersSkillReplyMultiError(errors)
	}

	return nil
}

// ListUsersSkillReplyMultiError is an error wrapping multiple validation
// errors returned by ListUsersSkillReply.ValidateAll() if the designated
// constraints aren't met.
type ListUsersSkillReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUsersSkillReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUsersSkillReplyMultiError) AllErrors() []error { return m }

// ListUsersSkillReplyValidationError is the validation error returned by
// ListUsersSkillReply.Validate if the designated constraints aren't met.
type ListUsersSkillReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUsersSkillReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUsersSkillReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUsersSkillReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUsersSkillReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUsersSkillReplyValidationError) ErrorName() string {
	return "ListUsersSkillReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListUsersSkillReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUsersSkillReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUsersSkillReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUsersSkillReplyValidationError{}

// Validate checks the field values on AddUsersSkillRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddUsersSkillRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddUsersSkillRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddUsersSkillRequestMultiError, or nil if none found.
func (m *AddUsersSkillRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddUsersSkillRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetScope()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddUsersSkillRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddUsersSkillRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScope()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddUsersSkillRequestValidationError{
				field:  "Scope",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSkills() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AddUsersSkillRequestValidationError{
						field:  fmt.Sprintf("Skills[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AddUsersSkillRequestValidationError{
						field:  fmt.Sprintf("Skills[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AddUsersSkillRequestValidationError{
					field:  fmt.Sprintf("Skills[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AddUsersSkillRequestMultiError(errors)
	}

	return nil
}

// AddUsersSkillRequestMultiError is an error wrapping multiple validation
// errors returned by AddUsersSkillRequest.ValidateAll() if the designated
// constraints aren't met.
type AddUsersSkillRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddUsersSkillRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddUsersSkillRequestMultiError) AllErrors() []error { return m }

// AddUsersSkillRequestValidationError is the validation error returned by
// AddUsersSkillRequest.Validate if the designated constraints aren't met.
type AddUsersSkillRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddUsersSkillRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddUsersSkillRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddUsersSkillRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddUsersSkillRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddUsersSkillRequestValidationError) ErrorName() string {
	return "AddUsersSkillRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddUsersSkillRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddUsersSkillRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddUsersSkillRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddUsersSkillRequestValidationError{}

// Validate checks the field values on DeleteUsersSkillRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteUsersSkillRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteUsersSkillRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteUsersSkillRequestMultiError, or nil if none found.
func (m *DeleteUsersSkillRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteUsersSkillRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetScope()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteUsersSkillRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteUsersSkillRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScope()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteUsersSkillRequestValidationError{
				field:  "Scope",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DeleteUsersSkillRequestValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DeleteUsersSkillRequestValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DeleteUsersSkillRequestValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DeleteUsersSkillRequestMultiError(errors)
	}

	return nil
}

// DeleteUsersSkillRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteUsersSkillRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteUsersSkillRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteUsersSkillRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteUsersSkillRequestMultiError) AllErrors() []error { return m }

// DeleteUsersSkillRequestValidationError is the validation error returned by
// DeleteUsersSkillRequest.Validate if the designated constraints aren't met.
type DeleteUsersSkillRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteUsersSkillRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteUsersSkillRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteUsersSkillRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteUsersSkillRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteUsersSkillRequestValidationError) ErrorName() string {
	return "DeleteUsersSkillRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteUsersSkillRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteUsersSkillRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteUsersSkillRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteUsersSkillRequestValidationError{}

// Validate checks the field values on AddTeamSkillRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddTeamSkillRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddTeamSkillRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddTeamSkillRequestMultiError, or nil if none found.
func (m *AddTeamSkillRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddTeamSkillRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if all {
		switch v := interface{}(m.GetScope()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddTeamSkillRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddTeamSkillRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScope()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddTeamSkillRequestValidationError{
				field:  "Scope",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSkills() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AddTeamSkillRequestValidationError{
						field:  fmt.Sprintf("Skills[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AddTeamSkillRequestValidationError{
						field:  fmt.Sprintf("Skills[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AddTeamSkillRequestValidationError{
					field:  fmt.Sprintf("Skills[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AddTeamSkillRequestMultiError(errors)
	}

	return nil
}

// AddTeamSkillRequestMultiError is an error wrapping multiple validation
// errors returned by AddTeamSkillRequest.ValidateAll() if the designated
// constraints aren't met.
type AddTeamSkillRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddTeamSkillRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddTeamSkillRequestMultiError) AllErrors() []error { return m }

// AddTeamSkillRequestValidationError is the validation error returned by
// AddTeamSkillRequest.Validate if the designated constraints aren't met.
type AddTeamSkillRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddTeamSkillRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddTeamSkillRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddTeamSkillRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddTeamSkillRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddTeamSkillRequestValidationError) ErrorName() string {
	return "AddTeamSkillRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddTeamSkillRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddTeamSkillRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddTeamSkillRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddTeamSkillRequestValidationError{}

// Validate checks the field values on DeleteTeamSkillRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteTeamSkillRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteTeamSkillRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteTeamSkillRequestMultiError, or nil if none found.
func (m *DeleteTeamSkillRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteTeamSkillRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if all {
		switch v := interface{}(m.GetScope()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteTeamSkillRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteTeamSkillRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScope()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteTeamSkillRequestValidationError{
				field:  "Scope",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSkills() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DeleteTeamSkillRequestValidationError{
						field:  fmt.Sprintf("Skills[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DeleteTeamSkillRequestValidationError{
						field:  fmt.Sprintf("Skills[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DeleteTeamSkillRequestValidationError{
					field:  fmt.Sprintf("Skills[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DeleteTeamSkillRequestMultiError(errors)
	}

	return nil
}

// DeleteTeamSkillRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteTeamSkillRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteTeamSkillRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteTeamSkillRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteTeamSkillRequestMultiError) AllErrors() []error { return m }

// DeleteTeamSkillRequestValidationError is the validation error returned by
// DeleteTeamSkillRequest.Validate if the designated constraints aren't met.
type DeleteTeamSkillRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteTeamSkillRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteTeamSkillRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteTeamSkillRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteTeamSkillRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteTeamSkillRequestValidationError) ErrorName() string {
	return "DeleteTeamSkillRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteTeamSkillRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteTeamSkillRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteTeamSkillRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteTeamSkillRequestValidationError{}

// Validate checks the field values on Skill_Type with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Skill_Type) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Skill_Type with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Skill_TypeMultiError, or
// nil if none found.
func (m *Skill_Type) ValidateAll() error {
	return m.validate(true)
}

func (m *Skill_Type) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Skill_TypeMultiError(errors)
	}

	return nil
}

// Skill_TypeMultiError is an error wrapping multiple validation errors
// returned by Skill_Type.ValidateAll() if the designated constraints aren't met.
type Skill_TypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Skill_TypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Skill_TypeMultiError) AllErrors() []error { return m }

// Skill_TypeValidationError is the validation error returned by
// Skill_Type.Validate if the designated constraints aren't met.
type Skill_TypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Skill_TypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Skill_TypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Skill_TypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Skill_TypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Skill_TypeValidationError) ErrorName() string { return "Skill_TypeValidationError" }

// Error satisfies the builtin error interface
func (e Skill_TypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSkill_Type.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Skill_TypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Skill_TypeValidationError{}

// Validate checks the field values on DeleteUsersSkillRequest_UserSkill with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeleteUsersSkillRequest_UserSkill) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteUsersSkillRequest_UserSkill
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeleteUsersSkillRequest_UserSkillMultiError, or nil if none found.
func (m *DeleteUsersSkillRequest_UserSkill) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteUsersSkillRequest_UserSkill) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Type

	// no validation rules for Name

	if len(errors) > 0 {
		return DeleteUsersSkillRequest_UserSkillMultiError(errors)
	}

	return nil
}

// DeleteUsersSkillRequest_UserSkillMultiError is an error wrapping multiple
// validation errors returned by
// DeleteUsersSkillRequest_UserSkill.ValidateAll() if the designated
// constraints aren't met.
type DeleteUsersSkillRequest_UserSkillMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteUsersSkillRequest_UserSkillMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteUsersSkillRequest_UserSkillMultiError) AllErrors() []error { return m }

// DeleteUsersSkillRequest_UserSkillValidationError is the validation error
// returned by DeleteUsersSkillRequest_UserSkill.Validate if the designated
// constraints aren't met.
type DeleteUsersSkillRequest_UserSkillValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteUsersSkillRequest_UserSkillValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteUsersSkillRequest_UserSkillValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteUsersSkillRequest_UserSkillValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteUsersSkillRequest_UserSkillValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteUsersSkillRequest_UserSkillValidationError) ErrorName() string {
	return "DeleteUsersSkillRequest_UserSkillValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteUsersSkillRequest_UserSkillValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteUsersSkillRequest_UserSkill.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteUsersSkillRequest_UserSkillValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteUsersSkillRequest_UserSkillValidationError{}
