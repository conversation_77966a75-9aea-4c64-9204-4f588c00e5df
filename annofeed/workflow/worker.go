package workflow

import (
	"context"
	"fmt"
	"os"
	"time"

	"annofeed/internal/biz"
	"annofeed/internal/conf"
	"annofeed/workflow/preparedata"
	"annofeed/workflow/types"

	"github.com/google/wire"
	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/pkg/kparser"
	"gitlab.rp.konvery.work/platform/pkg/kutil"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"gitlab.rp.konvery.work/platform/pkg/wf"
	"gitlab.rp.konvery.work/platform/pkg/wf/ktwf"
	"gitlab.rp.konvery.work/platform/pkg/wf/tracing"
	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/worker"
)

var ProviderSet = wire.NewSet(NewWorkflowStarter,
	preparedata.NewActivities,
)

// StartWorker starts a normal temporal worker.
func StartWorker(activities ...any) {
	// register workflows and activities
	startWorker(false, activities, []any{})
}

var (
	cfg    *conf.Temporal
	defcli client.Client
)

// Init initializes a normal temporal worker.
func Init(c *conf.Temporal, logger log.Logger) {
	cfg = c
	var err error
	defcli, err = client.Dial(client.Options{
		HostPort:           cfg.Addr,
		Namespace:          cfg.Namespace,
		Logger:             log.NewTemporalLogger(logger),
		ContextPropagators: tracing.GetContextPropagators(),
	})
	if err != nil {
		panic(fmt.Errorf("failed to create client: %v", err))
	}
}

// InitKtwf initializes a ktwf worker.
func InitKtwf(t *conf.Temporal, k *conf.Ktwf, logger log.Logger) {
	image := lo.Ternary(k.Master.Image != "", k.Master.Image, os.Getenv("image"))
	sa := lo.Ternary(k.Master.ServiceAccount != "", k.Master.ServiceAccount, os.Getenv("serviceAccountName"))
	command := k.Master.Command
	if env := os.Getenv("command"); len(command) == 0 && env != "" {
		cmd, err := kparser.ParseJSON[[]string]([]byte(env))
		kutil.Assert(err, "failed to parse command")
		command = *cmd
	}
	ktwf.Init(&ktwf.Config{
		Config: wf.Config{
			Logger:    logger,
			Addr:      t.Addr,
			Taskq:     t.TaskQueue,
			Namespace: t.Namespace,
		},
		StorageClass: k.Master.StorageClass,
		Master: ktwf.MasterCfg{
			Image:          image,
			Command:        command,
			Args:           k.Master.Args,
			ServiceAccount: sa,
		},
		Worker: ktwf.WorkerCfg{
			Namespace:      k.Worker.Namespace,
			ServiceAccount: k.Worker.ServiceAccount,
		},
	})
}

// startWorker starts a normal temporal worker.
func startWorker(session bool, activities []any, wfs []any) {
	w := worker.New(defcli, cfg.TaskQueue, worker.Options{EnableSessionWorker: session})
	lo.ForEach(activities, func(a any, _ int) { w.RegisterActivity(a) })
	lo.ForEach(wfs, func(wf any, _ int) { w.RegisterWorkflow(wf) })
	err := w.Run(worker.InterruptCh())
	if err != nil {
		panic(fmt.Errorf("worker exited unexpectedly: %w", err))
	}
}

func StartCronJob(ctx context.Context, schedule, name string, wffunc any, args ...any) error {
	retrypolicy := &temporal.RetryPolicy{
		InitialInterval:    time.Second,
		BackoffCoefficient: 2.0,
		MaximumInterval:    time.Second * 100,
		MaximumAttempts:    10,
	}
	options := client.StartWorkflowOptions{
		ID:           name,
		TaskQueue:    cfg.TaskQueue,
		CronSchedule: schedule,
		RetryPolicy:  retrypolicy,
	}
	_, err := defcli.ExecuteWorkflow(ctx, options, wffunc)
	if err != nil {
		return fmt.Errorf("failed to StartCronJob %v: %w", name, err)
	}
	return nil
}

type workflowStarter struct{}

func NewWorkflowStarter() biz.BackgroundTask {
	return &workflowStarter{}
}

// PrepareData starts a prepare data workflow.
func (o *workflowStarter) PrepareData(ctx context.Context, data *biz.Data) error {
	return preparedata.StartWorkflow(ctx, data)
}

// StartWorkflow starts a workflow.
func (o *workflowStarter) StartWorkflow(ctx context.Context, wfid string, wffunc any, args ...any) error {
	retrypolicy := &temporal.RetryPolicy{
		InitialInterval:    time.Second,
		BackoffCoefficient: 1.0,
		MaximumInterval:    time.Second * 100,
		MaximumAttempts:    20,
	}
	options := client.StartWorkflowOptions{
		ID:          wfid,
		TaskQueue:   cfg.TaskQueue,
		RetryPolicy: retrypolicy,
	}
	_, err := defcli.ExecuteWorkflow(ctx, options, wffunc, args...)
	if err != nil {
		return fmt.Errorf("failed to StartWorkflow: %w", err)
	}
	return nil
}

// CancelWorkflow cancels a workflow.
func (o *workflowStarter) CancelWorkflow(ctx context.Context, wfid string) error {
	return defcli.CancelWorkflow(ctx, wfid, "")
}

const MainSignalName = "MainChannel"

// SignalEvent sends an event to a workflow.
func (o *workflowStarter) SignalEvent(ctx context.Context, wfid string, ev *types.Event) error {
	err := defcli.SignalWorkflow(ctx, wfid, "", MainSignalName, ev)
	if err != nil {
		return fmt.Errorf("failed to signal workflow %v: %w", wfid, err)
	}
	return nil
}
