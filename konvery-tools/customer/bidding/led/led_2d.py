import json
import sys

sys.path.append(".")
from customer.common import tools

line_count = 0
file_count = 0
err_files = {}
file_box_dict = {}
current_file = ""
object_count = 0
type_dict = {
    "box": "2Dbox",
    "point": "2Dkeypoints",
    "polygon": "2Dpolygon",
}

feature_point_dict = {
    "AngularPoint-Right-rear": "RR",
    "AngularPoint-Left-rear": "LR",
    "AngularPoint-Right-front": "RF",
    "AngularPoint-Left-front": "LF"
}

box_type_list = ["Vehicle", "Person", "Cyclist", "StaticTarget"]


def get_label(label):
    mark_label = label["class"]

    if mark_label == "RegionalTarget":
        mark_label = "Freespace"
    elif mark_label in feature_point_dict.keys():
        mark_label = feature_point_dict[mark_label]
    else:
        for item in box_type_list:
            if item in label["attrs"].keys():
                mark_label = label["attrs"][item][0]
                break

    return mark_label


def convert(data, name=None):
    global current_file
    global err_files
    annotations = []

    for label in data["labels"]:
        mark_type = type_dict[label["annotation"]["type"]]
        anno_data = label["annotation"]["data"]
        region = []
        attributes = []
        group_id = None

        if "group_id" in label.keys():
            group_id = label["group_id"]

        # get region
        if mark_type == "2Dbox":
            width = anno_data["width"]
            height = anno_data["height"]

            region = [anno_data["x"] + width/2, anno_data["y"] + height/2, width, height]
        else:
            if isinstance(anno_data, list):
                for item in anno_data:
                    region.append([item["x"], item["y"]])
            else:
                region = [[anno_data["x"], anno_data["y"]]]

        mark_label = get_label(label)

        # get attributes
        for attr in label["attrs"].keys():
            if attr in box_type_list:
                continue
            attributes.append(attr)
            attributes.append(label["attrs"][attr][0])

        anno = {
            "markType": mark_type,
            "label": mark_label,
            "region": region,
            "attributes": attributes,
            "subNote": [],
            "groupId": group_id
        }

        annotations.append(anno)

    new_data = {
        "fileUrl": name,
        "subNote": [],
        "annotations": annotations
    }

    return new_data


def run(input_dir, output_dir):
    global file_count
    global line_count
    global current_file
    global file_box_dict

    file_count = 0
    invalid_file_count = 0
    invalid_file_list = []

    tools.check_dir(output_dir)
    input_dir = tools.unzip(input_dir)

    for f in tools.get_json_files(input_dir):

        if f.name.endswith('.json') and not f.name.startswith("."):

            convert_data = {}
            output_file_name = ""

            with open(f.path) as json_file:
                data = json.load(json_file)
                output_file_name = f.path.replace(input_dir, "").replace("label", "")
                current_file = f.name
                file_box_dict[current_file] = 0
                if data["label_meta"]["mark_status"] == 0:
                    file_count += 1
                    convert_data = convert(data, f.name.replace(".json", ".png"))
                else:
                    invalid_file_count += 1
                    invalid_file_list.append(f.path)
                    continue

            output_file_name = output_dir + output_file_name

            with open(output_file_name, "w") as outfile:
                json.dump(convert_data, outfile, indent=4)

    err_title = "错误信息"
    err_type_list = ["无效值"]
    return tools.check_error(output_dir, err_files, err_title, file_count, object_count, err_type_list, is_zip=False)


"""
    项目： LED 2D项目
    对接平台：云测新平台 
    功能： 云测导出的数据转换成客户格式
    输入： json
    输出： json
    云测3D输出格式： http://label-docs.testin.cn/dataset/pointcloud/
"""
if __name__ == "__main__":
    args = tools.get_args()
    args.input = "../../../input/test/2d"
    run(args.input, args.out)
