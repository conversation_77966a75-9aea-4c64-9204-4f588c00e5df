package ast

import (
	"bytes"
	"strings"
)

type FunctionLiteral struct {
	TokenAble
	Parameters []*Identifier
	Block      *BlockStatement
}

var _ Expression = &FunctionLiteral{}

func (fl *FunctionLiteral) expressionNode() {}

func (fl *FunctionLiteral) String() string {
	var out bytes.Buffer

	params := []string{}
	for _, p := range fl.Parameters {
		params = append(params, p.String())
	}

	out.WriteString(fl.TokenLiteral())
	out.WriteString("(")
	out.WriteString(strings.Join(params, ", "))
	out.WriteString(") ")
	out.WriteString(fl.Block.String())

	return out.String()
}
