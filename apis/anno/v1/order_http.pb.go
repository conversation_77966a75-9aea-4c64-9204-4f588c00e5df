// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: anno/v1/order.proto

package anno

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationOrdersCancelOrder = "/anno.v1.Orders/CancelOrder"
const OperationOrdersCreateOrder = "/anno.v1.Orders/CreateOrder"
const OperationOrdersDeleteOrder = "/anno.v1.Orders/DeleteOrder"
const OperationOrdersExportOrderAnnos = "/anno.v1.Orders/ExportOrderAnnos"
const OperationOrdersGetAnnoResult = "/anno.v1.Orders/GetAnnoResult"
const OperationOrdersGetOrder = "/anno.v1.Orders/GetOrder"
const OperationOrdersListOrder = "/anno.v1.Orders/ListOrder"
const OperationOrdersSetAnnoResult = "/anno.v1.Orders/SetAnnoResult"
const OperationOrdersUpdateOrder = "/anno.v1.Orders/UpdateOrder"

type OrdersHTTPServer interface {
	// CancelOrder only platform admin can cancel orders
	CancelOrder(context.Context, *GetOrderRequest) (*emptypb.Empty, error)
	CreateOrder(context.Context, *CreateOrderRequest) (*Order, error)
	DeleteOrder(context.Context, *DeleteOrderRequest) (*emptypb.Empty, error)
	ExportOrderAnnos(context.Context, *ExportOrderAnnosRequest) (*emptypb.Empty, error)
	GetAnnoResult(context.Context, *GetOrderRequest) (*GetOrderAnnoResultReply, error)
	GetOrder(context.Context, *GetOrderRequest) (*Order, error)
	ListOrder(context.Context, *ListOrderRequest) (*ListOrderReply, error)
	SetAnnoResult(context.Context, *SetOrderAnnoResultRequest) (*emptypb.Empty, error)
	UpdateOrder(context.Context, *UpdateOrderRequest) (*Order, error)
}

func RegisterOrdersHTTPServer(s *http.Server, srv OrdersHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/orders", _Orders_CreateOrder0_HTTP_Handler(srv))
	r.PATCH("/v1/orders/{order.uid}", _Orders_UpdateOrder0_HTTP_Handler(srv))
	r.PUT("/v1/orders/{uid}/cancel", _Orders_CancelOrder0_HTTP_Handler(srv))
	r.DELETE("/v1/orders/{uid}", _Orders_DeleteOrder0_HTTP_Handler(srv))
	r.GET("/v1/orders/{uid}", _Orders_GetOrder0_HTTP_Handler(srv))
	r.GET("/v1/orders", _Orders_ListOrder0_HTTP_Handler(srv))
	r.PUT("/v1/orders/{uid}/anno-result", _Orders_SetAnnoResult0_HTTP_Handler(srv))
	r.GET("/v1/orders/{uid}/anno-result", _Orders_GetAnnoResult0_HTTP_Handler(srv))
	r.PUT("/v1/orders/{uid}/export-annos", _Orders_ExportOrderAnnos0_HTTP_Handler(srv))
}

func _Orders_CreateOrder0_HTTP_Handler(srv OrdersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrderRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOrdersCreateOrder)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrder(ctx, req.(*CreateOrderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Order)
		return ctx.Result(200, reply)
	}
}

func _Orders_UpdateOrder0_HTTP_Handler(srv OrdersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateOrderRequest
		if err := ctx.Bind(&in.Order); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOrdersUpdateOrder)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateOrder(ctx, req.(*UpdateOrderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Order)
		return ctx.Result(200, reply)
	}
}

func _Orders_CancelOrder0_HTTP_Handler(srv OrdersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetOrderRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOrdersCancelOrder)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CancelOrder(ctx, req.(*GetOrderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Orders_DeleteOrder0_HTTP_Handler(srv OrdersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteOrderRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOrdersDeleteOrder)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteOrder(ctx, req.(*DeleteOrderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Orders_GetOrder0_HTTP_Handler(srv OrdersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetOrderRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOrdersGetOrder)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOrder(ctx, req.(*GetOrderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Order)
		return ctx.Result(200, reply)
	}
}

func _Orders_ListOrder0_HTTP_Handler(srv OrdersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListOrderRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOrdersListOrder)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListOrder(ctx, req.(*ListOrderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListOrderReply)
		return ctx.Result(200, reply)
	}
}

func _Orders_SetAnnoResult0_HTTP_Handler(srv OrdersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetOrderAnnoResultRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOrdersSetAnnoResult)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetAnnoResult(ctx, req.(*SetOrderAnnoResultRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Orders_GetAnnoResult0_HTTP_Handler(srv OrdersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetOrderRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOrdersGetAnnoResult)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAnnoResult(ctx, req.(*GetOrderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetOrderAnnoResultReply)
		return ctx.Result(200, reply)
	}
}

func _Orders_ExportOrderAnnos0_HTTP_Handler(srv OrdersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExportOrderAnnosRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOrdersExportOrderAnnos)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExportOrderAnnos(ctx, req.(*ExportOrderAnnosRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type OrdersHTTPClient interface {
	CancelOrder(ctx context.Context, req *GetOrderRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CreateOrder(ctx context.Context, req *CreateOrderRequest, opts ...http.CallOption) (rsp *Order, err error)
	DeleteOrder(ctx context.Context, req *DeleteOrderRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	ExportOrderAnnos(ctx context.Context, req *ExportOrderAnnosRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetAnnoResult(ctx context.Context, req *GetOrderRequest, opts ...http.CallOption) (rsp *GetOrderAnnoResultReply, err error)
	GetOrder(ctx context.Context, req *GetOrderRequest, opts ...http.CallOption) (rsp *Order, err error)
	ListOrder(ctx context.Context, req *ListOrderRequest, opts ...http.CallOption) (rsp *ListOrderReply, err error)
	SetAnnoResult(ctx context.Context, req *SetOrderAnnoResultRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateOrder(ctx context.Context, req *UpdateOrderRequest, opts ...http.CallOption) (rsp *Order, err error)
}

type OrdersHTTPClientImpl struct {
	cc *http.Client
}

func NewOrdersHTTPClient(client *http.Client) OrdersHTTPClient {
	return &OrdersHTTPClientImpl{client}
}

func (c *OrdersHTTPClientImpl) CancelOrder(ctx context.Context, in *GetOrderRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/orders/{uid}/cancel"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOrdersCancelOrder))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OrdersHTTPClientImpl) CreateOrder(ctx context.Context, in *CreateOrderRequest, opts ...http.CallOption) (*Order, error) {
	var out Order
	pattern := "/v1/orders"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOrdersCreateOrder))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OrdersHTTPClientImpl) DeleteOrder(ctx context.Context, in *DeleteOrderRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/orders/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOrdersDeleteOrder))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OrdersHTTPClientImpl) ExportOrderAnnos(ctx context.Context, in *ExportOrderAnnosRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/orders/{uid}/export-annos"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOrdersExportOrderAnnos))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OrdersHTTPClientImpl) GetAnnoResult(ctx context.Context, in *GetOrderRequest, opts ...http.CallOption) (*GetOrderAnnoResultReply, error) {
	var out GetOrderAnnoResultReply
	pattern := "/v1/orders/{uid}/anno-result"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOrdersGetAnnoResult))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OrdersHTTPClientImpl) GetOrder(ctx context.Context, in *GetOrderRequest, opts ...http.CallOption) (*Order, error) {
	var out Order
	pattern := "/v1/orders/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOrdersGetOrder))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OrdersHTTPClientImpl) ListOrder(ctx context.Context, in *ListOrderRequest, opts ...http.CallOption) (*ListOrderReply, error) {
	var out ListOrderReply
	pattern := "/v1/orders"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOrdersListOrder))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OrdersHTTPClientImpl) SetAnnoResult(ctx context.Context, in *SetOrderAnnoResultRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/orders/{uid}/anno-result"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOrdersSetAnnoResult))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OrdersHTTPClientImpl) UpdateOrder(ctx context.Context, in *UpdateOrderRequest, opts ...http.CallOption) (*Order, error) {
	var out Order
	pattern := "/v1/orders/{order.uid}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOrdersUpdateOrder))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in.Order, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
