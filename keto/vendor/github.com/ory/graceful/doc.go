/*
 * Copyright © 2015-2018 Aeneas Rekkas <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * <AUTHOR> Rekkas <<EMAIL>>
 * @copyright 	2015-2018 Aeneas Rekkas <<EMAIL>>
 * @license 	Apache-2.0
 */

// Package graceful contains best practice http server configurations and helpers for Go 1.8's http graceful shutdown feature.
package graceful
