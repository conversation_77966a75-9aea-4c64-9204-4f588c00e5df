import datetime
import sys
import os
from glob import glob

import jsonschema
import numpy as np
from scipy.spatial.transform import Rotation as R

sys.path.append(".")
from customer.common import tools


non_speed_group_category = {
    "prohibition",
    "warning",
    "indicator",
    "other"
}

all_categories = {
    "maximum_speed",
    "minimum_speed",
    "release_speed",
    "supplementary",
    "elect_speed"
    "max_mini_speed",
    "ramp_speed",
    "zone_speed",
    "car_speed",
    "bus_speed",
    "ruck_speed",
    "prohibition",
    "warning",
    "indicator",
    "other"
}


# 二级 限速类
sub_limit_seed_category = {
    "maximum_speed",
    "minimum_speed",
    "release_speed",
    "supplementary"
}


def process_json_file(input_path, output_path):
    data = tools.get_json_data(input_path)
    # print(data)
    timestamp = os.path.splitext(os.path.basename(input_path))[0]
    transformed = {
        "version": "1.0.0",
        "project_name": "2D_traffic_sign",
        "rule_version": "1.0.0",
        "producer_id": "",
        "timestamp": timestamp,
        "car_id": "",
        "clip_id": "",
        "calib_path": "",
        "labeled_data": []
    }
    source_info = data.get("label_meta", {}).get("source_info", {})
    image_width = source_info.get("width", 0)
    image_height = source_info.get("height", 0)
    camera_info = {
        "name": "front_short_camera",
        "timestamp": timestamp,
        "path": "",
        "image_height": image_height,
        "image_width": image_width
    }

    groups = []
    for label in data.get("labels", []):
        group_category = label.get("class", '')
        # 兼容异常标注数据
        if group_category == "maximum_spee":
            group_category = "maximum_speed"
        group = {
            "group_id": "0",
            "group_category": group_category,
            "group_box": {"x1": 222, "y1": 222, "x2": 222, "y2": 222},
            "objects": []
        }
        # 一级分类为:禁令类、警示类、指示类、其他时,object为空
        if group_category not in non_speed_group_category:
            attrs = label.get("attrs", {})
            sub_category = ''
            for sub_class in sub_limit_seed_category:
                if sub_class not in attrs:
                    continue
                sub_category = sub_class

            # 非限速牌值为-1
            limit_number = "-1"
            if sub_category in sub_limit_seed_category:
                limit_number = attrs.get(sub_category, [])
                if limit_number:
                    limit_number = str(limit_number[0])
            # print(limit_number)
            annotations = label.get("annotations", {})
            anno_data = annotations.get("data", {})
            obj = {
                "attributes": {
                    "obj_id": label.get("object_id", "0"),
                    "sub_category": sub_category,
                    "supplementary_name": "zone",
                    "limit_number": limit_number,
                    "occlusion": attrs.get("occlusio", ["0"])[0],
                    "truncated": attrs.get("truncated", ["0"])[0],
                    "orientation": attrs.get("orientation", ["0"])[0]
                },
                "box": {
                    "x1": anno_data.get("x", 0),
                    "y1": anno_data.get("y", 0),
                    "x2": anno_data.get("x", 0) + anno_data.get("width", 0),
                    "y2": anno_data.get("y", 0) + anno_data.get("height", 0)
                }
            }
            group["objects"].append(obj)

        groups.append(group)

    transformed["labeled_data"].append({"camera": camera_info, "groups": groups})
    #
    # tools.write_json_file(processed_data, output_path, indent=4, ensure_ascii=False)


def run(input_dir, output_dir):
    last_path_level = os.path.basename(os.path.normpath(input_dir))
    output_dir = os.path.join(output_dir, last_path_level)
    for dirpath, dirnames, filenames in os.walk(input_dir):
        if 'label' in dirnames:
            label_dir = os.path.join(dirpath, 'label')
            relative_path = os.path.relpath(label_dir, input_dir)
            output_label_dir = os.path.join(str(output_dir), relative_path)
            json_files = sorted([f for f in os.listdir(label_dir) if f.endswith('.json')])
            for filename in json_files:
                # print('---', filename)
                input_file = os.path.join(label_dir, filename)
                output_file = os.path.join(output_label_dir, filename)
                process_json_file(input_file, output_file)


if __name__ == "__main__":
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
