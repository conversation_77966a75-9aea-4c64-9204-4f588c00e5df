# Keto 项目权限系统分析

本文档基于项目中的实际定义（`namespaces.keto.ts` 和 `tuples.txt`）分析 Keto 权限系统的实现。

## 1. 命名空间定义

项目定义了以下主要命名空间：

### 1.1 IAM 相关命名空间

1. **IamRole**
   ```typescript
   class IamRole implements Namespace {
     related: {
       parents: IamGroup[]
       policies: IamPolicy[]
       perms: (IamPerm | SubjectSet<IamRole, "perms">)[]
     }
   }
   ```
   - 用于定义角色及其权限
   - 支持通过父组、策略和直接权限继承
   - ID 格式：
     - 全局角色：`<role-name>`
     - 组织角色：`<org-uid>.<role-name>`

2. **IamPolicy**
   ```typescript
   class IamPolicy implements Namespace {
     related: {
       roles: IamRole[]
       users: (IamUser | SubjectSet<IamGroup, "members">)[]
     }
   }
   ```
   - 用于定义策略
   - ID 格式：
     - 系统策略：`sys/<name>`
     - 资源策略：`<resource-ns>/<resource-name>.<role-name>`

3. **IamGroup**
   ```typescript
   class IamGroup implements Namespace {
     related: {
       parents: IamGroup[]
       policies: IamPolicy[]
       members: (IamUser | SubjectSet<IamGroup, "members">)[]
     }
   }
   ```
   - 用于定义用户组
   - ID 格式：`<group-uid>`

4. **IamUser**
   ```typescript
   class IamUser implements Namespace {
     related: {
       parents: IamGroup[]
       policies: IamPolicy[]
       owners: IamUser[]
     }
   }
   ```
   - 用于定义用户
   - ID 格式：`<user-uid>`

### 1.2 业务相关命名空间

1. **AnnoLot**
   ```typescript
   class AnnoLot implements Namespace {
     related: {
       parents: (IamGroup|AnnoLot)[]
       policies: IamPolicy[]
     }
   }
   ```
   - 用于标注批次管理
   - ID 格式：
     - 顶层批次：`<lot-uid>`
     - 分阶段批次：`<lot-uid>.phase-<index>`

2. **AnnoJob**
   ```typescript
   class AnnoJob implements Namespace {
     related: {
       parents: AnnoLot[]
       policies: IamPolicy[]
     }
   }
   ```
   - 用于标注任务管理
   - ID 格式：
     - 顶层任务：`<job-uid>`
     - 子任务：`<job-uid>.<subtype>`

## 2. 权限继承机制

### 2.1 全局角色继承

在 `tuples.txt` 中定义了全局角色的继承关系：

```text
# 根角色继承
IamRole:root#perms@IamRole:admin#perms
IamRole:root#policies@IamPolicy:sys/admin

# 管理员角色权限
IamRole:admin#perms@IamRole:iam.owner#perms
IamRole:admin#perms@IamRole:anno.owner#perms
IamRole:admin#perms@IamRole:anno.regulator#perms
IamRole:admin#perms@IamRole:annofeed.owner#perms
IamRole:admin#perms@IamRole:fe.owner#perms
IamRole:admin#policies@IamPolicy:sys/admin
```

### 2.2 业务角色继承

1. **标注系统角色**
   ```text
   IamRole:anno.viewer#perms@IamRole:AnnoLot.viewer#perms
   IamRole:anno.viewer#perms@IamRole:AnnoJob.viewer#perms
   IamRole:anno.viewer#perms@IamRole:AnnoOrder.viewer#perms
   ```

2. **团队角色**
   ```text
   IamRole:member#perms@IamRole:iam.viewer#perms
   IamRole:member#perms@IamRole:anno.viewer#perms
   IamRole:member#perms@IamRole:annofeed.viewer#perms
   ```

## 3. 权限检查流程

### 3.1 基本检查流程

1. **直接权限检查**
   ```typescript
   check: (ctx: Context, perm: string): boolean =>
     this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
     this.related.policies.traverse((p) => p.permits.check(ctx, perm))
   ```

2. **全局权限检查**
   ```typescript
   check: (ctx: Context, perm: string): boolean =>
     IamRole:"*".permits.check(ctx, perm) ||
     this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
     this.related.policies.traverse((p) => p.permits.check(ctx, perm))
   ```

### 3.2 具体权限示例

1. **用户权限检查**
   ```typescript
   check: (ctx: Context, perm: string): boolean => 
     this == ctx.subject ||
     IamUser:"*".permits.check(ctx, perm) ||
     this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
     this.related.policies.traverse((p) => p.permits.check(ctx, perm)) ||
     this.related.owners.includes(ctx.subject)
   ```

2. **标注批次权限检查**
   ```typescript
   check: (ctx: Context, perm: string): boolean =>
     AnnoLot:"*".permits.check(ctx, perm) ||
     this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
     this.related.policies.traverse((p) => p.permits.check(ctx, perm))
   ```

## 4. 数据转换流程

### 4.1 UUID 转换

当关系元组被写入数据库时：

1. 使用 `uuid.NewV5()` 生成确定性 UUID
2. 基于 NetworkID 和原始字符串生成 UUID
3. 确保相同的字符串 ID 生成相同的 UUID

### 4.2 关系元组格式

1. **直接用户权限**
   ```text
   IamUser:user1#policies@IamPolicy:sys/admin
   ```

2. **组权限**
   ```text
   IamGroup:group1#members@IamUser:user1
   ```

3. **角色权限**
   ```text
   IamRole:admin#perms@IamRole:iam.owner#perms
   ```

## 5. 系统特点

1. **灵活的权限继承**
   - 支持多级继承
   - 支持组和角色权限
   - 支持策略继承

2. **细粒度的权限控制**
   - 支持命名空间级别的权限
   - 支持对象级别的权限
   - 支持关系级别的权限

3. **可扩展的权限模型**
   - 支持自定义命名空间
   - 支持自定义关系定义
   - 支持复杂的主体集定义

4. **性能优化**
   - 使用 UUID 确保数据一致性
   - 支持缓存机制
   - 支持分页查询

5. **安全性**
   - 严格的类型检查
   - 权限验证
   - 访问控制 