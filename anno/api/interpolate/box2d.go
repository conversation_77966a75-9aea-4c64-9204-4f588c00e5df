package interpolate

import (
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
)

func init() {
	RegisterInterpolator("box2d", box2dInterpolate)
}

// Interpolator generates a new object using linear interpolation.
// span indicates the distance between o1 and o2.
// distance indicates the distance from o1 to the new object.
// if o2 is nil, a copy of o1 is returned.
func box2dInterpolate(o1, o2 *anno.Object, span, distance float64) *anno.Object {
	o := cloneObject(o1)
	if o2 == nil {
		return o
	}

	// x, y, width, height, alpha
	data := o.Label.Widget.Data
	for i := range data {
		d1 := o1.Label.Widget.Data[i]
		d2 := o2.Label.Widget.Data[i]
		data[i] = d1 + (d2-d1)*distance/span
	}
	return o
}
