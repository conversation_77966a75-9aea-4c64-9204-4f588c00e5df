package jobstat

import (
	"time"

	"annostat/workflow/common"

	"gitlab.rp.konvery.work/platform/pkg/wf/tracing"
	"gitlab.rp.konvery.work/platform/pkg/wf/wfutil"
	"go.temporal.io/sdk/workflow"
)

func GetWorkflowID(lotUid string) string {
	return "annostat-jobstat-" + lotUid
}

func JobstatWorkflow(ctx workflow.Context) (err error) {
	defer func() { err = wfutil.RecreateNonRetryableError(err) }()

	var a *Activities
	retry := wfutil.RetryPolicy(10000)
	traceID := workflow.GetInfo(ctx).WorkflowExecution.ID
	tracing.PrintTraceID(ctx, traceID)
	ctx = tracing.WithCustomTraceID(ctx, traceID)
	sigChan := workflow.GetSignalChannel(ctx, common.DefaultSignalName)

	for {
		ev := &common.Event{}
		if ok, _ := sigChan.ReceiveWithTimeout(ctx, time.Nanosecond, ev); !ok {
			// Before completing the Workflow or using Continue-As-New, make sure to do an asynchronous drain
			// on the Signal channel. Otherwise, the Signals will be lost.
			// ReceiveAsync will cause some signals redelivered when there are multiple signals to handle.
			// ReceiveWithTimeout (AwaitWithTimeout + ReceiveAsync) will avoid it.
			return
		}

		tracing.PrintTraceID(tracing.WithCustomTraceID(ctx, ev.TraceID), traceID)
		switch ev.Event {
		case common.EvtJobsubmit:
			err = wfutil.ExecActivity(ctx, a.OnJobsubmit, "", time.Minute, 0, retry, ev)
		}
	}
}
