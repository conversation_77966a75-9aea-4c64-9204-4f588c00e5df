package preparedata

import (
	"context"
	"fmt"
	"os"
	"path"
	"path/filepath"
	"strings"

	"annofeed/internal/biz"
	"annofeed/internal/data/serial"
	"annofeed/workflow/common"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/kfs"
	"gitlab.rp.konvery.work/platform/pkg/wf/wfutil"
	"go.temporal.io/sdk/activity"
)

func (o *Activities) PrepareDataValidateRawdatas(ctx context.Context, basedir string, data *biz.Data) (err error) {
	fmt.Println("---> prepare data validate rawdatas")
	var allSummaryErrors []*anno.DataValidationSummary_Error
	data, err = o.databiz.GetByID(ctx, data.ID)
	if err != nil {
		return fixError(err, "")
	}
	fmt.Println(data.Size)

	defer func() {
		if len(allSummaryErrors) > 0 || data.Summary.E != nil { // update summary
			totalErrors := len(allSummaryErrors)
			if totalErrors > 0 {
				o.log.Info(ctx, "data validation error", "data_id", data.ID, "all_items", allSummaryErrors)
			}
			if totalErrors > biz.MaxSummaryItems {
				allSummaryErrors = allSummaryErrors[:biz.MaxSummaryItems]
			}
			data.Summary = *serial.New(&anno.DataValidationSummary{
				TotalErrors: int32(totalErrors),
				Errors:      allSummaryErrors,
			})
			o.databiz.Update(ctx, data, field.NewMask(biz.DataSfldSummary.String())) // ignore error
		}
		if err != nil { // convert error to non-retriable error
			err = wfutil.NewNonRetryableError("validation error", err)
		}
	}()

	if data.BaseOn > 0 {
		return nil
	}

	elem := &struct { // fields should be exported as it will be attached to heartbeat
		Path      string
		Index     int32
		ClipName  string
		IdxInClip int
	}{Index: -1, IdxInClip: -1}
	startIdx := wfutil.GetActivtityStartIndex(ctx, elem)
	i := -1

	changeElem := func(dir string) {
		elem.Path = dir
		elem.Index++
		elem.IdxInClip++
		if clip := filepath.Dir(dir); clip != elem.ClipName {
			elem.ClipName = clip
			elem.IdxInClip = 0
		}
	}

	rawdataValGroup := &RawdataValidationGroup{
		BaseNames: make(map[string]int32),
		IdxInClip: -1,
		DataType:  data.Type,
	}
	err = kfs.IterateDir(basedir, func(entry os.DirEntry, subdir string, depth int) error {
		// skip directories and processed files
		name := entry.Name()
		if i++; entry.IsDir() || i < startIdx || subdir == "" ||
			path.Base(subdir) == common.MetaDir ||
			strings.HasPrefix(subdir, common.AnnosPrefix) ||
			strings.HasSuffix(name, common.OrigExt) ||
			name == common.ParamsFile ||
			name == common.AnnosFile {
			return nil
		}

		if elem.Path != subdir {
			changeElem(subdir)
		}
		summaryErrors := rawdataValGroup.validate(elem.Index, name, elem.IdxInClip)
		allSummaryErrors = append(allSummaryErrors, summaryErrors...)
		if err := validateWithErrorHandlers(summaryErrors, data.Source.E.ErrorHandlers); err != nil {
			return err
		}

		activity.RecordHeartbeat(ctx, i, elem) // Report progress.
		return nil
	})

	if err == nil { // check the last elem
		summaryErrors := rawdataValGroup.checkMissingFiles()
		allSummaryErrors = append(allSummaryErrors, summaryErrors...)
		if err := validateWithErrorHandlers(summaryErrors, data.Source.E.ErrorHandlers); err != nil {
			return err
		}
	}

	return err
}

type RawdataValidationGroup struct {
	BaseNames  map[string]int32 // file names -> element index
	CurElemIdx int32
	IdxInClip  int
	DataType   string
}

func (group *RawdataValidationGroup) validate(elemIdx int32, name string, idxInClip int) (
	summaryErrors []*anno.DataValidationSummary_Error) {
	if elemIdx == 0 { // save the 1st elem as standard
		group.BaseNames[name] = elemIdx
		return nil
	}

	if elemIdx != group.CurElemIdx {
		// switch to the next elem
		// And we can also check the previous elem.
		// for example:	when switching to the 3rd elem, we can check the 2nd elem with base elem
		summaryErrors = group.checkMissingFiles()
		group.CurElemIdx = elemIdx
		group.IdxInClip = idxInClip
	}

	if _, ok := group.BaseNames[name]; !ok {
		summaryErrors = append(summaryErrors, &anno.DataValidationSummary_Error{
			Error:       anno.Source_ParseErrorHandler_Error_file_unknown,
			ElemIndex:   elemIdx,
			RawdataName: name,
		})
		return summaryErrors
	}
	group.BaseNames[name] = elemIdx // indicate this file exists in BaseNames
	return summaryErrors
}

func (group *RawdataValidationGroup) checkMissingFiles() (summaryErrors []*anno.DataValidationSummary_Error) {
	lastElemIdx := group.CurElemIdx
	lidarIdx, ok := group.BaseNames[common.LidarPCD]
	// first element in a fusion4d clip must have a lidar.pcd
	if group.IdxInClip == 0 && group.DataType == biz.DataTypeFusion4D && (!ok || lidarIdx != lastElemIdx) {
		summaryErrors = append(summaryErrors, &anno.DataValidationSummary_Error{
			Error:       anno.Source_ParseErrorHandler_Error_file_missing,
			ElemIndex:   lastElemIdx,
			RawdataName: common.LidarPCD,
		})
	}

	for name, elemIdx := range group.BaseNames {
		if elemIdx != lastElemIdx &&
			// elements, except for the first one, in a fusion4d clip do not have a lidar.pcd
			!(group.IdxInClip > 0 && group.DataType == biz.DataTypeFusion4D && name == common.LidarPCD) {
			summaryErrors = append(summaryErrors, &anno.DataValidationSummary_Error{
				Error:       anno.Source_ParseErrorHandler_Error_file_missing,
				ElemIndex:   lastElemIdx,
				RawdataName: name,
			})
		}
	}
	return summaryErrors
}

func validateWithErrorHandlers(summaryErrors []*anno.DataValidationSummary_Error, errorHandlers []*anno.Source_ParseErrorHandler) error {
	if len(summaryErrors) == 0 {
		return nil
	}
	if len(errorHandlers) == 0 {
		return fmt.Errorf("fail the workflow if no handler is specified")
	}

	for _, summaryErr := range summaryErrors {
		var rawdataType = anno.Rawdata_Type_image
		if summaryErr.RawdataName == common.LidarPCD {
			rawdataType = anno.Rawdata_Type_pointcloud
		}

		for _, handler := range errorHandlers {
			if handler.RawdataType == 0 || handler.RawdataType == rawdataType {
				if handler.Error == 0 || handler.Error == summaryErr.Error {
					if handler.Handler == anno.Source_ParseErrorHandler_Handler_fail {
						return fmt.Errorf("fail the workflow as the handler required")
					}
				}
			}
		}
	}

	return nil
}
