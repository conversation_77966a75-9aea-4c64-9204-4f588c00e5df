// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: iam/v1/policy.proto

package iam

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Policies_CreatePolicy_FullMethodName        = "/iam.v1.Policies/CreatePolicy"
	Policies_UpdatePolicy_FullMethodName        = "/iam.v1.Policies/UpdatePolicy"
	Policies_DeletePolicy_FullMethodName        = "/iam.v1.Policies/DeletePolicy"
	Policies_GetPolicy_FullMethodName           = "/iam.v1.Policies/GetPolicy"
	Policies_GetAttachedPolicies_FullMethodName = "/iam.v1.Policies/GetAttachedPolicies"
	Policies_CreateResource_FullMethodName      = "/iam.v1.Policies/CreateResource"
	Policies_DeleteResource_FullMethodName      = "/iam.v1.Policies/DeleteResource"
	Policies_RevokeUsersRole_FullMethodName     = "/iam.v1.Policies/RevokeUsersRole"
)

// PoliciesClient is the client API for Policies service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PoliciesClient interface {
	// Create and attach a policy to a resource
	CreatePolicy(ctx context.Context, in *CreatePolicyRequest, opts ...grpc.CallOption) (*Policy, error)
	UpdatePolicy(ctx context.Context, in *UpdatePolicyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// detach and delete the policy
	DeletePolicy(ctx context.Context, in *DeletePolicyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetPolicy(ctx context.Context, in *GetPolicyRequest, opts ...grpc.CallOption) (*Policy, error)
	// get policies attached to a resource
	GetAttachedPolicies(ctx context.Context, in *GetAttachedPoliciesRequest, opts ...grpc.CallOption) (*GetAttachedPoliciesReply, error)
	// [rpc only] intialize access policies for a resource
	CreateResource(ctx context.Context, in *CreateResourceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// [rpc only] clear access policies for a resource
	DeleteResource(ctx context.Context, in *DeleteResourceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// [rpc only] revoke users' role on a resource
	RevokeUsersRole(ctx context.Context, in *RevokeUsersRoleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type policiesClient struct {
	cc grpc.ClientConnInterface
}

func NewPoliciesClient(cc grpc.ClientConnInterface) PoliciesClient {
	return &policiesClient{cc}
}

func (c *policiesClient) CreatePolicy(ctx context.Context, in *CreatePolicyRequest, opts ...grpc.CallOption) (*Policy, error) {
	out := new(Policy)
	err := c.cc.Invoke(ctx, Policies_CreatePolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *policiesClient) UpdatePolicy(ctx context.Context, in *UpdatePolicyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Policies_UpdatePolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *policiesClient) DeletePolicy(ctx context.Context, in *DeletePolicyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Policies_DeletePolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *policiesClient) GetPolicy(ctx context.Context, in *GetPolicyRequest, opts ...grpc.CallOption) (*Policy, error) {
	out := new(Policy)
	err := c.cc.Invoke(ctx, Policies_GetPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *policiesClient) GetAttachedPolicies(ctx context.Context, in *GetAttachedPoliciesRequest, opts ...grpc.CallOption) (*GetAttachedPoliciesReply, error) {
	out := new(GetAttachedPoliciesReply)
	err := c.cc.Invoke(ctx, Policies_GetAttachedPolicies_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *policiesClient) CreateResource(ctx context.Context, in *CreateResourceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Policies_CreateResource_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *policiesClient) DeleteResource(ctx context.Context, in *DeleteResourceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Policies_DeleteResource_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *policiesClient) RevokeUsersRole(ctx context.Context, in *RevokeUsersRoleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Policies_RevokeUsersRole_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PoliciesServer is the server API for Policies service.
// All implementations must embed UnimplementedPoliciesServer
// for forward compatibility
type PoliciesServer interface {
	// Create and attach a policy to a resource
	CreatePolicy(context.Context, *CreatePolicyRequest) (*Policy, error)
	UpdatePolicy(context.Context, *UpdatePolicyRequest) (*emptypb.Empty, error)
	// detach and delete the policy
	DeletePolicy(context.Context, *DeletePolicyRequest) (*emptypb.Empty, error)
	GetPolicy(context.Context, *GetPolicyRequest) (*Policy, error)
	// get policies attached to a resource
	GetAttachedPolicies(context.Context, *GetAttachedPoliciesRequest) (*GetAttachedPoliciesReply, error)
	// [rpc only] intialize access policies for a resource
	CreateResource(context.Context, *CreateResourceRequest) (*emptypb.Empty, error)
	// [rpc only] clear access policies for a resource
	DeleteResource(context.Context, *DeleteResourceRequest) (*emptypb.Empty, error)
	// [rpc only] revoke users' role on a resource
	RevokeUsersRole(context.Context, *RevokeUsersRoleRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedPoliciesServer()
}

// UnimplementedPoliciesServer must be embedded to have forward compatible implementations.
type UnimplementedPoliciesServer struct {
}

func (UnimplementedPoliciesServer) CreatePolicy(context.Context, *CreatePolicyRequest) (*Policy, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePolicy not implemented")
}
func (UnimplementedPoliciesServer) UpdatePolicy(context.Context, *UpdatePolicyRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePolicy not implemented")
}
func (UnimplementedPoliciesServer) DeletePolicy(context.Context, *DeletePolicyRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePolicy not implemented")
}
func (UnimplementedPoliciesServer) GetPolicy(context.Context, *GetPolicyRequest) (*Policy, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPolicy not implemented")
}
func (UnimplementedPoliciesServer) GetAttachedPolicies(context.Context, *GetAttachedPoliciesRequest) (*GetAttachedPoliciesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAttachedPolicies not implemented")
}
func (UnimplementedPoliciesServer) CreateResource(context.Context, *CreateResourceRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateResource not implemented")
}
func (UnimplementedPoliciesServer) DeleteResource(context.Context, *DeleteResourceRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteResource not implemented")
}
func (UnimplementedPoliciesServer) RevokeUsersRole(context.Context, *RevokeUsersRoleRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RevokeUsersRole not implemented")
}
func (UnimplementedPoliciesServer) mustEmbedUnimplementedPoliciesServer() {}

// UnsafePoliciesServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PoliciesServer will
// result in compilation errors.
type UnsafePoliciesServer interface {
	mustEmbedUnimplementedPoliciesServer()
}

func RegisterPoliciesServer(s grpc.ServiceRegistrar, srv PoliciesServer) {
	s.RegisterService(&Policies_ServiceDesc, srv)
}

func _Policies_CreatePolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PoliciesServer).CreatePolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Policies_CreatePolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PoliciesServer).CreatePolicy(ctx, req.(*CreatePolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Policies_UpdatePolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PoliciesServer).UpdatePolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Policies_UpdatePolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PoliciesServer).UpdatePolicy(ctx, req.(*UpdatePolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Policies_DeletePolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PoliciesServer).DeletePolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Policies_DeletePolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PoliciesServer).DeletePolicy(ctx, req.(*DeletePolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Policies_GetPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PoliciesServer).GetPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Policies_GetPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PoliciesServer).GetPolicy(ctx, req.(*GetPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Policies_GetAttachedPolicies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAttachedPoliciesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PoliciesServer).GetAttachedPolicies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Policies_GetAttachedPolicies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PoliciesServer).GetAttachedPolicies(ctx, req.(*GetAttachedPoliciesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Policies_CreateResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateResourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PoliciesServer).CreateResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Policies_CreateResource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PoliciesServer).CreateResource(ctx, req.(*CreateResourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Policies_DeleteResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteResourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PoliciesServer).DeleteResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Policies_DeleteResource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PoliciesServer).DeleteResource(ctx, req.(*DeleteResourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Policies_RevokeUsersRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevokeUsersRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PoliciesServer).RevokeUsersRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Policies_RevokeUsersRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PoliciesServer).RevokeUsersRole(ctx, req.(*RevokeUsersRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Policies_ServiceDesc is the grpc.ServiceDesc for Policies service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Policies_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "iam.v1.Policies",
	HandlerType: (*PoliciesServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePolicy",
			Handler:    _Policies_CreatePolicy_Handler,
		},
		{
			MethodName: "UpdatePolicy",
			Handler:    _Policies_UpdatePolicy_Handler,
		},
		{
			MethodName: "DeletePolicy",
			Handler:    _Policies_DeletePolicy_Handler,
		},
		{
			MethodName: "GetPolicy",
			Handler:    _Policies_GetPolicy_Handler,
		},
		{
			MethodName: "GetAttachedPolicies",
			Handler:    _Policies_GetAttachedPolicies_Handler,
		},
		{
			MethodName: "CreateResource",
			Handler:    _Policies_CreateResource_Handler,
		},
		{
			MethodName: "DeleteResource",
			Handler:    _Policies_DeleteResource_Handler,
		},
		{
			MethodName: "RevokeUsersRole",
			Handler:    _Policies_RevokeUsersRole_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "iam/v1/policy.proto",
}
