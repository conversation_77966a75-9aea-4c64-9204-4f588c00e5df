syntax = "proto3";

package annofeed.v1;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";
import "anno/v1/elemanno.proto";
import "anno/v1/order.proto";
import "types/filelist.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/annofeed/v1;annofeed";
option java_multiple_files = true;
option java_package = "annofeed.v1";

service Datas {
  rpc CreateData (CreateDataRequest) returns (Data) {
    option (google.api.http) = {
      post: "/v1/datas"
      body: "*"
    };
  }

  rpc UpdateData (UpdateDataRequest) returns (Data) {
    option (google.api.http) = {
      patch: "/v1/datas/{data.uid}"
      body: "data"
    };
  }

  rpc DeleteData (DeleteDataRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/datas/{uid}"
    };
  }

  rpc GetData (GetDataRequest) returns (Data) {
    option (google.api.http) = {
      get: "/v1/datas/{uid}"
    };
  }

  rpc ListData (ListDataRequest) returns (ListDataReply) {
    option (google.api.http) = {
      get: "/v1/datas"
    };
  }

  // Fetch a list of elements
  rpc GetDataElements(GetDataElementsRequest) returns (GetDataElementsReply) {
    option (google.api.http) = {
      get: "/v1/datas/{uid}/elements"
    };
  }

  // Find a list of elements (only names and indexes are returned)
  rpc FindDataElements(FindDataElementsRequest) returns (GetDataElementsReply) {
    option (google.api.http) = {
      get: "/v1/datas/{uid}/find-elements"
    };
  }

  // Get data meta
  rpc GetDataMeta(GetDataMetaRequest) returns (GetDataMetaReply) {
    option (google.api.http) = {
      get: "/v1/datas/{uid}/meta"
    };
  }

  // Set rawdata embedding.
  rpc SetRawdataEmbedding (SetRawdataEmbeddingRequest) returns (SetRawdataEmbeddingReply) {
    option (google.api.http) = {
      // put: "/v1/datas/{uid}/rawdatas/{rawdata_name=**}/embedding" // cannot handle leading/trailing '/' in rawdata_name
      put: "/v1/datas/{uid}/rawdata-embedding"
      body: "*"
    };
  }

  // Get data validation summary
  rpc GetDataValidationSummary(GetDataRequest) returns (anno.v1.DataValidationSummary) {}

  // Parse data
  rpc ParseData(ParseDataRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/datas/{uid}/parse"
      body: "*"
    };
  }
}

message CreateDataRequest {
  option (openapi.v3.schema) = {
    required: ["type", "source", "org_uid"]
  };

  // data UID (only needed in update-requests)
  string uid = 1;
  // data name (mandatory in create-requests)
  string name = 2;
  // data description
  string desc = 3;
  // data type
  anno.v1.Element.Type.Enum type = 4 [(validate.rules).enum = {defined_only: true}];
  // based on data UID
  string base_on_uid = 5;
  // source files which the data originates from
  anno.v1.Source source = 6;
  // UID of the organization which the data belongs to
  string org_uid = 7 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // UID of the order which caused the data
  string order_uid = 8;
}

message UpdateDataRequest {
  option (openapi.v3.schema) = {
    required: ["data", "fields"]
  };

  // update contents
  CreateDataRequest data = 1;
  // name of fields to be updated
  repeated string fields = 2;
}
//message UpdateDataReply {}

message DeleteDataRequest {
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
}
//message DeleteDataReply {}

message GetDataRequest {
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // if true, only return the simple info: name, desc, state, etc.
  bool simple = 2;
}
//message GetDataReply {}

message ListDataRequest {
  int32 page = 1;
  int32 pagesz = 2 [(validate.rules).int32 = {gte:0, lte: 100}];

  // filter by organization
  string org_uid = 3;
  // filter by creator
  string creator_uid = 4;
  // filter by name pattern
  string name_pattern = 5;
  // filter by order
  string order_uid = 6;
  // filter by data state
  repeated Data.State.Enum states = 7;
}

message ListDataReply {
  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated Data datas = 2;
}

message Data {
  option (openapi.v3.schema) = {
    required: ["uid", "name", "type", "source", "org_uid", "state", "created_at"]
  };

  message State {
    enum Enum {
      unspecified = 0;
      raw = 1;
      fetching = 2;
      processing = 3;
      ready = 4;
      abandoned = 5;
      disabled = 6;
      failed = 7;
    }
  }

  // data UID
  string uid = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
  // data name
  string name = 2;
  // data description
  string desc = 3;
  // data type
  anno.v1.Element.Type.Enum type = 4 [(validate.rules).enum = {defined_only: true}];
  // based on data UID
  string base_on_uid = 5;
  // source files which the data originates from
  anno.v1.Source source = 6;
  // UID of the organization which the data belongs to
  string org_uid = 7 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // UID of the order which caused the data
  string order_uid = 8;

  // number of elements in the data
  int32 size = 10 [(google.api.field_behavior) = OUTPUT_ONLY];
  // data state: fetching, processing, ready, failed
  State.Enum state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
  // when state is failed, this field will contain the error message
  string error = 12;
  // UID of the creator
  string creator_uid = 13;

  // data creation time
  google.protobuf.Timestamp created_at = 15 [(google.api.field_behavior) = OUTPUT_ONLY];
  // data validation summary
  anno.v1.DataValidationSummary summary = 16 [(google.api.field_behavior) = OUTPUT_ONLY];
}

message GetDataElementsRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "start_idx", "count"]
  };

  // data UID
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // element start index
  int32 start_idx = 2 [(validate.rules).int32.gte = 0];
  // element count
  int32 count = 3;
}

message GetDataElementsReply {
  repeated anno.v1.Element elements = 1;
}

message FindDataElementsRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "start_idx", "count"]
  };

  // data UID
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // element start index
  int32 start_idx = 2 [(validate.rules).int32.gte = 0];
  // element count
  int32 count = 3;
  // search elements by the pattern of their names
  string name_pattern = 4;
}

message GetDataMetaRequest {
  // data UID
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // whether to return metafiles in response
  bool with_metafiles = 2;
}

message GetDataMetaReply {
  map<string, string> metadata = 1;
  types.Filelist metafiles = 2;
}

message SetRawdataEmbeddingRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "rawdata_name", "embedding_uri"]
  };

  // data UID
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // rawdata name
  string rawdata_name = 2 [(validate.rules).string.min_len = 1];
  // embedding file URI
  string embedding_uri = 3 [(validate.rules).string.uri_ref = true];
}

message SetRawdataEmbeddingReply {
  // embedding file access URL
  string embedding_url = 1;
}

message ParseDataRequest {
  option (openapi.v3.schema) = {
    required: ["uid"]
  };

  enum Option {
      // 只解析未曾解析的数据
      unspecified = 0;
      // 无论上次解析结果如何，都清除之前的解析结果并重新解析数据
      force = 1;
      // 解析未曾解析的或者失败的数据（清除之前的解析结果）
      reparse_failed = 2;
  }

  // data UID
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // parse option will not break current data parsing workflow if any
  Option option = 2 [(validate.rules).enum = {defined_only: true}];
}
