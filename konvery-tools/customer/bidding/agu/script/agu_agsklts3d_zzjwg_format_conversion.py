import os
import glob
import json
import numpy as np
import math
import shutil
import zipfile
import sys

sys.path.append('.')
from customer.common import tools


label_map = {
    '': 0,
    'ground': 1,
    'person': 2,
    'loader': 3,
    'self': 4,
    'materialA': 5,
    'materialB': 6,
    'dumper': 7,
    'obstacle': 8,
    'other': 9
}


def run(input_dir):
    file_counter = 0
    # unzip file
    unzip_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_unzip')
    result_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_result')
    if os.path.exists(unzip_dir):
        shutil.rmtree(unzip_dir)
    with zipfile.ZipFile(input_dir, 'r') as zip_ref:
        zip_ref.extractall(unzip_dir)

    try:
        os.mkdir(result_dir)
    except FileExistsError:
        shutil.rmtree(result_dir)
        os.mkdir(result_dir)

    for pcd_dir in glob.glob(os.path.join(unzip_dir, '**', '*.pcd'), recursive=True):
        file_name = os.path.basename(pcd_dir)
        if os.path.basename(os.path.dirname(pcd_dir)) == 'lidar':
            # load pcd file
            with open(pcd_dir, 'r') as file:
                lines = [line.rstrip() for line in file]
            # Find the line number where the data starts
            data_start = lines.index('DATA ascii') + 1
            # Extract the point data
            points_data = [line.split(' ') for line in lines[data_start:]]
            # Convert the list of points to a NumPy array
            pcd_array = np.array(points_data).astype(float)

            # load corresponding json file
            json_dir = os.path.join(os.path.dirname(os.path.dirname(pcd_dir)), 'label', file_name.split('.pcd')[0] + '.json')
            json_data = json.load(open(json_dir))
            label_list = json_data['segmentation']['result'][0]

            # get space dimensions
            x_min, y_min, z_min, x_max, y_max, z_max = 0, 0, 0, 0, 0, 0
            for line in pcd_array:
                if line[0] < x_min:
                    x_min = line[0]
                if line[1] < y_min:
                    y_min = line[1]
                if line[2] < z_min:
                    z_min = line[2]
                if line[0] > x_max:
                    x_max = line[0]
                if line[1] > y_max:
                    y_max = line[1]
                if line[2] > z_max:
                    z_max = line[2]

            tag_dict = {}

            # iterate through pcd file and assign tag to each block list
            for index, line in enumerate(pcd_array):
                tag_key = (math.floor((line[2] - z_min) / 0.2), math.floor((line[1] - y_min) / 0.2), math.floor((line[0] - x_min) / 0.2))
                if tag_key not in tag_dict:
                    tag_dict[tag_key] = [label_map[label_list[index]]]
                else:
                    tag_dict[tag_key].append(label_map[label_list[index]])

            # build npy result
            result_list = []
            for key in tag_dict:
                major_tag = max(set(tag_dict[key]), key=tag_dict[key].count)
                result_list.append([key[0], key[1], key[2], int(major_tag)])

            result_array = np.array(result_list)
            np.save(os.path.join(result_dir, file_name.split('.pcd')[0] + '.npy'),
                    result_array)

    trans_result = {
        "code": 0,
        "output": result_dir,
        "err_msg": '成功',
        "summary": {
            '生成文件数': file_counter,
        }
    }

    return trans_result


if __name__ == "__main__":
    args = tools.get_args()
    run(args.input)

    # args_input = '/Users/<USER>/Downloads/agu/test.zip'
    # run(args_input)