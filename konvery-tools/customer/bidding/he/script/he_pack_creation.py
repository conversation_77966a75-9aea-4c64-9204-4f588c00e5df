import os
import shutil
import threading
import sys

sys.path.append(".")
from customer.common import tools

pack_count = 0
thread_size = 10
lidar_dir = "lidar"
camera_dir_list = ["front_cam", "left_cam", "right_cam"]

mutex = threading.Lock()

total_input_pcd = 0
total_process_pcd = 0
package_size = 5
code = 0
err_msg = "成功"


def copy_to_new_dir(input_dir, output_dir, pcd_list, pack_id):
    global total_process_pcd
    global code
    global err_msg

    pack_dir = output_dir + os.sep + input_dir.split(os.sep)[-1] + "_" + str(pack_id)
    out_lidar_dir = pack_dir + os.sep + lidar_dir
    out_camera_dir = [pack_dir + os.sep + "camera" + os.sep + cam for cam in camera_dir_list]
    tools.check_dir(out_lidar_dir)
    [tools.check_dir(cam) for cam in out_camera_dir]

    pcd_list_count = len(pcd_list)
    if pcd_list_count != package_size:
        print(pack_dir, pcd_list)

    mutex.acquire()
    total_process_pcd += pcd_list_count
    mutex.release()

    try:
        for pcd in pcd_list:
            pcd_file_name = os.path.join(input_dir + os.sep + lidar_dir, pcd)
            shutil.copyfile(pcd_file_name, out_lidar_dir + os.sep + pcd)
            file_name = pcd.replace("pcd", "jpg")
            for camera_file in camera_dir_list:
                camera_file_name = input_dir + os.sep + camera_file + os.sep + file_name
                out_camera_file_name = pack_dir + os.sep + "camera" + os.sep + camera_file + os.sep + file_name
                tools.shutil.copyfile(camera_file_name, out_camera_file_name)
    except Exception as e:
        code = 1
        err_msg = ".pcd对应的jpg文件不存在"
        print(e)

    tools.shutil.copyfile("script/config.json", pack_dir + os.sep + "config.json")


def compare_name(name):
    result = name.split(".")[0]

    if result:
        result = int(result)
    else:
        result = -1
    return result


def create_pack(input_dir, output_dir, pack_size):
    pcd_list = []
    pack_id = 0
    global pack_count
    global total_input_pcd
    global total_process_pcd

    lidar = input_dir + os.sep + lidar_dir
    file_list = os.listdir(lidar)

    for file in file_list:
        if file.startswith(".") or not file.endswith(".pcd"):
            file_list.remove(file)

    file_list.sort(key=compare_name)
    pcd_file_count = len(file_list)
    pcd_count = 0
    process_list = []

    for pcd_file in file_list:
        if not pcd_file.startswith(".") and pcd_file.endswith(".pcd"):
            process_list.append(pcd_file)
            mutex.acquire()
            total_input_pcd += 1
            mutex.release()

            pcd_list.append(pcd_file)
            pcd_count += 1
            if pcd_count % pack_size == 0 or pcd_count == pcd_file_count:
                pack_id += 1
                copy_to_new_dir(input_dir, output_dir, pcd_list, pack_id)
                pcd_list = []
                mutex.acquire()
                pack_count += 1
                mutex.release()

    if pcd_count != pcd_file_count:
        process_list.sort()
        file_list.sort()
        print(
            f"!!!!!!!Error : {input_dir}, processed :{pcd_count}, input pcd:{pcd_file_count}, {process_list},{file_list}")


def run(input_dir, output_dir, pack_size):
    global package_size
    global code
    global err_msg

    package_size = pack_size
    input_dir = tools.unzip(input_dir)
    sub_dir_list = []

    for directory in os.listdir(input_dir):
        directory = os.path.join(input_dir, directory)
        if os.path.isdir(directory):
            sub_dir_list.append(directory)

    threads_list = []
    sub_dir_count = len(sub_dir_list)

    for index, sub_dir in enumerate(sub_dir_list, start=1):
        thd = threading.Thread(target=create_pack, args=[sub_dir, output_dir, pack_size])
        threads_list.append(thd)
        thd.start()

        if index % thread_size == 0 or index == sub_dir_count:
            for thd in threads_list:
                thd.join()
            threads_list = []

    for th in threads_list:
        th.join()

    if total_process_pcd != total_input_pcd:
        code = 1
        err_msg = "失败，输入点云数量与输出点云数量不一致"

    trans_result = {"code": code, "output": output_dir, "err_msg": err_msg,
                    "summary": {"total_input_pcd": total_input_pcd, "total_process_pcd": total_process_pcd,
                                "pack_size": pack_size, "pack_count": pack_count}}
    print(trans_result)
    return trans_result


if __name__ == "__main__":
    """
    ——自定义目录
      ——segment_000 //序列目录，名称可自定义
        ——config.json // 配置文件，为纯点云标注时为可选文件，文件名为config.json
        ——lidar // 点云文件目录，目录名为lidar
          ——000.pcd // 名称可自定义
          ——001.pcd
        ——camera // 图像目录，目录名为camera,无图像则不含此目录。
          ——front_camera // 多视角摄像头名称，名称可自定义  
            ——000.jpg // 通过文件名与点云对应
            ——001.jpg
        ——pre_label // 可选，预导入目录，通过文件名与原始点云文件对应，目录名为pre_label
          ——000.json
          ——001.json
    """
    args = tools.get_args()

    # Notice: Program must run on notepad, it would report error on mobile hard disk

    args.input = "/Users/<USER>/Downloads/data"

    run(args.input, args.out, int(args.txt))
