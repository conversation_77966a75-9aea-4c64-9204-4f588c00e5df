syntax = "proto3";
package types;

import "openapi/v3/annotations.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/types;types";
option java_multiple_files = true;
option java_package = "types";

message Multilingual {
  option (openapi.v3.schema) = {
    required: ["langs"]
  };

  // language => text
  map<string, string> langs = 1;
}

message MultilingualItem {
  option (openapi.v3.schema) = {
    required: ["name", "langs"]
  };

  // item name
  string name = 1;
  // language => text
  map<string, string> langs = 2;
}

message DisplayItem {
  option (openapi.v3.schema) = {
    required: ["name", "display_name"]
  };

  // item name
  string name = 1;
  // item display name
  string display_name = 2;
}
