// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: anyconn/v1/webhookevt.proto

package anyconn

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationWebhookEvtsCreateWebhookEvt = "/anyconn.v1.WebhookEvts/CreateWebhookEvt"
const OperationWebhookEvtsDeleteWebhookEvt = "/anyconn.v1.WebhookEvts/DeleteWebhookEvt"
const OperationWebhookEvtsGetWebhookEvt = "/anyconn.v1.WebhookEvts/GetWebhookEvt"
const OperationWebhookEvtsListWebhookEvt = "/anyconn.v1.WebhookEvts/ListWebhookEvt"
const OperationWebhookEvtsUpdateWebhookEvt = "/anyconn.v1.WebhookEvts/UpdateWebhookEvt"

type WebhookEvtsHTTPServer interface {
	CreateWebhookEvt(context.Context, *CreateWebhookEvtRequest) (*WebhookEvt, error)
	DeleteWebhookEvt(context.Context, *DeleteWebhookEvtRequest) (*emptypb.Empty, error)
	GetWebhookEvt(context.Context, *GetWebhookEvtRequest) (*WebhookEvt, error)
	ListWebhookEvt(context.Context, *ListWebhookEvtRequest) (*ListWebhookEvtReply, error)
	UpdateWebhookEvt(context.Context, *UpdateWebhookEvtRequest) (*WebhookEvt, error)
}

func RegisterWebhookEvtsHTTPServer(s *http.Server, srv WebhookEvtsHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/webhookevts", _WebhookEvts_CreateWebhookEvt0_HTTP_Handler(srv))
	r.PATCH("/v1/webhookevts/{uid}", _WebhookEvts_UpdateWebhookEvt0_HTTP_Handler(srv))
	r.DELETE("/v1/webhookevts/{uid}", _WebhookEvts_DeleteWebhookEvt0_HTTP_Handler(srv))
	r.GET("/v1/webhookevts/{uid}", _WebhookEvts_GetWebhookEvt0_HTTP_Handler(srv))
	r.GET("/v1/webhookevts", _WebhookEvts_ListWebhookEvt0_HTTP_Handler(srv))
}

func _WebhookEvts_CreateWebhookEvt0_HTTP_Handler(srv WebhookEvtsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateWebhookEvtRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWebhookEvtsCreateWebhookEvt)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateWebhookEvt(ctx, req.(*CreateWebhookEvtRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WebhookEvt)
		return ctx.Result(200, reply)
	}
}

func _WebhookEvts_UpdateWebhookEvt0_HTTP_Handler(srv WebhookEvtsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateWebhookEvtRequest
		if err := ctx.Bind(&in.Evt); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWebhookEvtsUpdateWebhookEvt)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateWebhookEvt(ctx, req.(*UpdateWebhookEvtRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WebhookEvt)
		return ctx.Result(200, reply)
	}
}

func _WebhookEvts_DeleteWebhookEvt0_HTTP_Handler(srv WebhookEvtsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteWebhookEvtRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWebhookEvtsDeleteWebhookEvt)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteWebhookEvt(ctx, req.(*DeleteWebhookEvtRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _WebhookEvts_GetWebhookEvt0_HTTP_Handler(srv WebhookEvtsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetWebhookEvtRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWebhookEvtsGetWebhookEvt)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWebhookEvt(ctx, req.(*GetWebhookEvtRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WebhookEvt)
		return ctx.Result(200, reply)
	}
}

func _WebhookEvts_ListWebhookEvt0_HTTP_Handler(srv WebhookEvtsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListWebhookEvtRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWebhookEvtsListWebhookEvt)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListWebhookEvt(ctx, req.(*ListWebhookEvtRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListWebhookEvtReply)
		return ctx.Result(200, reply)
	}
}

type WebhookEvtsHTTPClient interface {
	CreateWebhookEvt(ctx context.Context, req *CreateWebhookEvtRequest, opts ...http.CallOption) (rsp *WebhookEvt, err error)
	DeleteWebhookEvt(ctx context.Context, req *DeleteWebhookEvtRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetWebhookEvt(ctx context.Context, req *GetWebhookEvtRequest, opts ...http.CallOption) (rsp *WebhookEvt, err error)
	ListWebhookEvt(ctx context.Context, req *ListWebhookEvtRequest, opts ...http.CallOption) (rsp *ListWebhookEvtReply, err error)
	UpdateWebhookEvt(ctx context.Context, req *UpdateWebhookEvtRequest, opts ...http.CallOption) (rsp *WebhookEvt, err error)
}

type WebhookEvtsHTTPClientImpl struct {
	cc *http.Client
}

func NewWebhookEvtsHTTPClient(client *http.Client) WebhookEvtsHTTPClient {
	return &WebhookEvtsHTTPClientImpl{client}
}

func (c *WebhookEvtsHTTPClientImpl) CreateWebhookEvt(ctx context.Context, in *CreateWebhookEvtRequest, opts ...http.CallOption) (*WebhookEvt, error) {
	var out WebhookEvt
	pattern := "/v1/webhookevts"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWebhookEvtsCreateWebhookEvt))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WebhookEvtsHTTPClientImpl) DeleteWebhookEvt(ctx context.Context, in *DeleteWebhookEvtRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/webhookevts/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWebhookEvtsDeleteWebhookEvt))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WebhookEvtsHTTPClientImpl) GetWebhookEvt(ctx context.Context, in *GetWebhookEvtRequest, opts ...http.CallOption) (*WebhookEvt, error) {
	var out WebhookEvt
	pattern := "/v1/webhookevts/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWebhookEvtsGetWebhookEvt))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WebhookEvtsHTTPClientImpl) ListWebhookEvt(ctx context.Context, in *ListWebhookEvtRequest, opts ...http.CallOption) (*ListWebhookEvtReply, error) {
	var out ListWebhookEvtReply
	pattern := "/v1/webhookevts"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWebhookEvtsListWebhookEvt))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WebhookEvtsHTTPClientImpl) UpdateWebhookEvt(ctx context.Context, in *UpdateWebhookEvtRequest, opts ...http.CallOption) (*WebhookEvt, error) {
	var out WebhookEvt
	pattern := "/v1/webhookevts/{uid}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWebhookEvtsUpdateWebhookEvt))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in.Evt, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
