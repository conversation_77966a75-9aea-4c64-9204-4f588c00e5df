// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: iam/v1/perm.proto

package iam

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Perms_EditPerms_FullMethodName     = "/iam.v1.Perms/EditPerms"
	Perms_ListPermClass_FullMethodName = "/iam.v1.Perms/ListPermClass"
	Perms_ListPerm_FullMethodName      = "/iam.v1.Perms/ListPerm"
)

// PermsClient is the client API for Perms service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PermsClient interface {
	EditPerms(ctx context.Context, in *EditPermsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// get permission class (resource type) list: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
	ListPermClass(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListPermClassReply, error)
	// get permission list
	ListPerm(ctx context.Context, in *ListPermRequest, opts ...grpc.CallOption) (*ListPermReply, error)
}

type permsClient struct {
	cc grpc.ClientConnInterface
}

func NewPermsClient(cc grpc.ClientConnInterface) PermsClient {
	return &permsClient{cc}
}

func (c *permsClient) EditPerms(ctx context.Context, in *EditPermsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Perms_EditPerms_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permsClient) ListPermClass(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListPermClassReply, error) {
	out := new(ListPermClassReply)
	err := c.cc.Invoke(ctx, Perms_ListPermClass_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permsClient) ListPerm(ctx context.Context, in *ListPermRequest, opts ...grpc.CallOption) (*ListPermReply, error) {
	out := new(ListPermReply)
	err := c.cc.Invoke(ctx, Perms_ListPerm_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PermsServer is the server API for Perms service.
// All implementations must embed UnimplementedPermsServer
// for forward compatibility
type PermsServer interface {
	EditPerms(context.Context, *EditPermsRequest) (*emptypb.Empty, error)
	// get permission class (resource type) list: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
	ListPermClass(context.Context, *emptypb.Empty) (*ListPermClassReply, error)
	// get permission list
	ListPerm(context.Context, *ListPermRequest) (*ListPermReply, error)
	mustEmbedUnimplementedPermsServer()
}

// UnimplementedPermsServer must be embedded to have forward compatible implementations.
type UnimplementedPermsServer struct {
}

func (UnimplementedPermsServer) EditPerms(context.Context, *EditPermsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditPerms not implemented")
}
func (UnimplementedPermsServer) ListPermClass(context.Context, *emptypb.Empty) (*ListPermClassReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPermClass not implemented")
}
func (UnimplementedPermsServer) ListPerm(context.Context, *ListPermRequest) (*ListPermReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPerm not implemented")
}
func (UnimplementedPermsServer) mustEmbedUnimplementedPermsServer() {}

// UnsafePermsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PermsServer will
// result in compilation errors.
type UnsafePermsServer interface {
	mustEmbedUnimplementedPermsServer()
}

func RegisterPermsServer(s grpc.ServiceRegistrar, srv PermsServer) {
	s.RegisterService(&Perms_ServiceDesc, srv)
}

func _Perms_EditPerms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditPermsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermsServer).EditPerms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Perms_EditPerms_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermsServer).EditPerms(ctx, req.(*EditPermsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Perms_ListPermClass_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermsServer).ListPermClass(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Perms_ListPermClass_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermsServer).ListPermClass(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Perms_ListPerm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPermRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermsServer).ListPerm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Perms_ListPerm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermsServer).ListPerm(ctx, req.(*ListPermRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Perms_ServiceDesc is the grpc.ServiceDesc for Perms service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Perms_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "iam.v1.Perms",
	HandlerType: (*PermsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "EditPerms",
			Handler:    _Perms_EditPerms_Handler,
		},
		{
			MethodName: "ListPermClass",
			Handler:    _Perms_ListPermClass_Handler,
		},
		{
			MethodName: "ListPerm",
			Handler:    _Perms_ListPerm_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "iam/v1/perm.proto",
}
