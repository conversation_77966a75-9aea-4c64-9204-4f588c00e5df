syntax = "proto3";

package anyconn.v1;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";
// import "iam/v1/type.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anyconn/v1;anyconn";
option java_multiple_files = true;
option java_package = "anyconn.v1";

service Batches {
  rpc CreateBatch (CreateBatchRequest) returns (Batch) {
    option (google.api.http) = {
      post: "/v1/batches"
      body: "*"
    };
  }

  rpc UpdateBatch (UpdateBatchRequest) returns (Batch) {
    option (google.api.http) = {
      patch: "/v1/batches/{batch.uid}"
      body: "batch"
    };
  }

  // only platform admin can cancel batches
  rpc CancelBatch (GetBatchRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/batches/{uid}/cancel"
      body: "*"
    };
  }

  rpc DeleteBatch (DeleteBatchRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/batches/{uid}"
    };
  }

  rpc GetBatch (GetBatchRequest) returns (Batch) {
    option (google.api.http) = {
      get: "/v1/batches/{uid}"
    };
  }

  rpc ListBatch (ListBatchRequest) returns (ListBatchReply) {
    option (google.api.http) = {
      get: "/v1/batches"
    };
  }

  rpc SetAnnoResult (SetBatchAnnoResultRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/batches/{uid}/anyconn-result"
      body: "*"
    };
  }

  rpc GetAnnoResult (GetBatchRequest) returns (GetBatchAnnoResultReply) {
    option (google.api.http) = {
      get: "/v1/batches/{uid}/anyconn-result"
    };
  }
}

message Source {
  option (openapi.v3.schema) = {
    required: ["uris"]
  };

  // message File {
  //   string uri = 1;
  // }
  // message OSS {
  //   string provider = 1;
  //   string access_key = 2;
  //   string secret_key = 3;
  //   string bucket = 4;
  //   repeated string dirs = 5;
  // }
  // OSS bucket = 2;

  message Proprietary {
    option (openapi.v3.schema) = {
      required: ["type", "config"]
    };

    // 3rd-party file host service type
    string type = 1;
    // 3rd-party file host service access config
    string config = 2;
  }

  message ParseErrorHandler {
    message Error {
      enum Enum {
        unspecified = 0;
        file_unknown = 1;
        file_missing = 2;
      }
    }

    message Handler {
      enum Enum {
        unspecified = 0;
        fail = 1; // if a rawdata like image has error, fail the total data workflow
        ignore = 2; // if a rawdata like image has error, just ignore this error
      }
    }

    // anyconn.v1.Rawdata.Type.Enum rawdata_type = 1; // image or pcd
    Error.Enum error = 2;
    Handler.Enum handler = 3;
  }

  // package file (.zip) URIs
  repeated string uris = 1;
  // access config when the files are hosted in a third-party platform
  Proprietary proprietary = 2;
  // folder layout style within package files if not conform to Konvery standard
  string style = 3;
  // // element type
  // Element.Type.Enum elem_type = 4;
  // if source contains consecutive frames
  bool is_frame_series = 5;
  // size of the unpacked data in GB
  int32 plain_size_gb = 6;
  // define parser error handlers; it will fail the parser if no handler is specified.
  // max size is count(RawdataErrorAction.Error) x count(Rawdata.Type).
  repeated ParseErrorHandler error_handlers = 7;
}

message CreateBatchRequest {
  option (openapi.v3.schema) = {
    required: []
  };

  // mandatory in update-requests
  string uid = 1;
  // mandatory in create-requests;
  // pattern: ^[\\p{Han}\\w\\d-]{0,20}$
  string name = 2 [(validate.rules).string = {pattern: "^[\\p{Han}\\w\\d-]{0,20}$"}];
  // batch's organization; if empty, this is the requestor's organization
  string org_uid = 3;
  // files attached to the batch
  Source source = 4;
}

message UpdateBatchRequest {
  option (openapi.v3.schema) = {
    required: ["batch", "fields"]
  };

  // update contents
  Batch batch = 1;
  // name of fileds to be updated
  repeated string fields = 2;
}

message DeleteBatchRequest {
  // batch UID
  string uid = 1;
}

message GetBatchRequest {
  // batch UID
  string uid = 1;
}

message ListBatchRequest {
  int32 page = 1;
  int32 pagesz = 2;

  // filter by orgnization
  string org_uid = 3;
  // filter by creator
  string creator_uid = 4;
  // filter by name pattern
  string name_pattern = 5;

  // filter by batch state
  repeated Batch.State.Enum states = 6;
  // include batch's orgnization in the reply
  bool with_org = 7;
}

message ListBatchReply {
  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated Batch batches = 2;
  // // the orgnization that the batch, at the corresponding position, belongs to.
  // repeated iam.v1.BaseUser orgs = 3;
}

message Batch {
  option (openapi.v3.schema) = {
    required: ["uid", "name", "org_uid", "state", "created_at"]
  };

  message State {
    enum Enum {
      unspecified = 0;
      // parsing the data specified in the source
      initializing = 1;
      // waiting for the batch related lot to be started
      waiting = 2;
      ongoing = 3;
      finished = 4;
      canceled = 5;
      // error occurred when parsing data specified by the source
      failed = 6;
    }
  }

  // batch UID
  string uid = 1;
  // batch name
  string name = 2;
  // UID of the organization which the batch belongs to
  string org_uid = 3;
  // files attached to the batch
  Source source = 4;

  // UID of the data associated with the batch
  string data_uid = 5;
  // creator UID
  string creator_uid = 6;

  // number of elements in the batch
  int32 size = 10;
  // batch state
  State.Enum state = 11;
  // annotated object count (include interpolated ones); only available after lot is finished
  int32 ins_total = 12;
  // annotation result file URL
  string anno_result_url = 13;
  // when state is failed, this field will contain detailed error message
  string error = 14;

  // batch creation time
  google.protobuf.Timestamp created_at = 15;
  // data validation summary
  DataValidationSummary data_summary = 16 [(google.api.field_behavior) = OUTPUT_ONLY];
}

message SetBatchAnnoResultRequest {
  // batch UID
  string uid = 1;
  // URL to the result
  string url = 2;
}

message GetBatchAnnoResultReply {
  // the estimated time when the result is ready;
  // valid only when the batch is in the finished state
  google.protobuf.Timestamp will_ready_at = 1;
  // URL to the result if available
  string url = 2;
}

// Note: the messages below should be defined in annofeed and imported in anyconn.
// To avoid recursively import, we define them in anyconn and import them in annofeed.

message DataValidationSummary {
  message Error {
    Source.ParseErrorHandler.Error.Enum error = 1;
    int32 elem_index = 2;
    string rawdata_name = 3;
  }

  // total number of validation errors found, includes those unsaved errors
  int32 total_errors = 1;
  // validation errors; only the first 10 errors are saved
  repeated Error errors = 2;

}
