package protoconv_test

import (
	"testing"

	"anno/internal/biz"
	"anno/internal/protoconv"

	"github.com/stretchr/testify/assert"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
)

func TestConvter(t *testing.T) {
	conv := protoconv.NewProtoEnumStringConverter(anno.Lot_State_Enum_value, anno.Lot_State_finished, biz.LotStateFinished.String())
	assert.Equal(t, conv.FromProto(anno.Lot_State_finished), biz.LotStateFinished.String())
	assert.Equal(t, conv.FromProto(anno.Lot_State_canceled), biz.LotStateCanceled.String())
	conv.MustConv(false, biz.LotStateFinished.String(), biz.LotStateCanceled.String())
}
