// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anno/v1/elemanno.proto

package anno

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	v1 "gitlab.rp.konvery.work/platform/apis/iam/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Element_Type_Enum int32

const (
	Element_Type_unspecified Element_Type_Enum = 0
	// one image in an element
	Element_Type_image Element_Type_Enum = 1
	// one point-cloud file in an element
	Element_Type_pointcloud Element_Type_Enum = 2
	// multiple image files in an element
	Element_Type_fusion2d Element_Type_Enum = 3
	// multiple image files and a point-cloud file in an element
	Element_Type_fusion3d Element_Type_Enum = 4
	// a multi-frame fusion point-cloud file and related pictures in a frame series
	Element_Type_fusion4d Element_Type_Enum = 5
)

// Enum value maps for Element_Type_Enum.
var (
	Element_Type_Enum_name = map[int32]string{
		0: "unspecified",
		1: "image",
		2: "pointcloud",
		3: "fusion2d",
		4: "fusion3d",
		5: "fusion4d",
	}
	Element_Type_Enum_value = map[string]int32{
		"unspecified": 0,
		"image":       1,
		"pointcloud":  2,
		"fusion2d":    3,
		"fusion3d":    4,
		"fusion4d":    5,
	}
)

func (x Element_Type_Enum) Enum() *Element_Type_Enum {
	p := new(Element_Type_Enum)
	*p = x
	return p
}

func (x Element_Type_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Element_Type_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_elemanno_proto_enumTypes[0].Descriptor()
}

func (Element_Type_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_elemanno_proto_enumTypes[0]
}

func (x Element_Type_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Element_Type_Enum.Descriptor instead.
func (Element_Type_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{0, 0, 0}
}

type RawdataParam_Type_Enum int32

const (
	// matrix transformer; row first: r00,r01,r02,...,r10,r11,r12,...
	RawdataParam_Type_matrix RawdataParam_Type_Enum = 0
	// extrinsic params: x,y,z,qx,qy,qz,qw
	RawdataParam_Type_extrinsic RawdataParam_Type_Enum = 1
	// intrinsic params: fx,fy,cx,cy
	RawdataParam_Type_intrinsic RawdataParam_Type_Enum = 2
	// distortion params: distortion_type,k1,k2,...
	RawdataParam_Type_distortion RawdataParam_Type_Enum = 3
)

// Enum value maps for RawdataParam_Type_Enum.
var (
	RawdataParam_Type_Enum_name = map[int32]string{
		0: "matrix",
		1: "extrinsic",
		2: "intrinsic",
		3: "distortion",
	}
	RawdataParam_Type_Enum_value = map[string]int32{
		"matrix":     0,
		"extrinsic":  1,
		"intrinsic":  2,
		"distortion": 3,
	}
)

func (x RawdataParam_Type_Enum) Enum() *RawdataParam_Type_Enum {
	p := new(RawdataParam_Type_Enum)
	*p = x
	return p
}

func (x RawdataParam_Type_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RawdataParam_Type_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_elemanno_proto_enumTypes[1].Descriptor()
}

func (RawdataParam_Type_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_elemanno_proto_enumTypes[1]
}

func (x RawdataParam_Type_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RawdataParam_Type_Enum.Descriptor instead.
func (RawdataParam_Type_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{1, 0, 0}
}

type RawdataParam_DistortionType_Enum int32

const (
	// Brown-Conrady model (radtan): k1,k2,p1,p2,[k3,[k4,k5,k6]]
	RawdataParam_DistortionType_pinhole RawdataParam_DistortionType_Enum = 0
	// Omnidirectional:
	RawdataParam_DistortionType_mei RawdataParam_DistortionType_Enum = 1
	// KB8(fisheye) model: k1,k2,k3,k4
	RawdataParam_DistortionType_kb RawdataParam_DistortionType_Enum = 2
	// Scaramuzza: affine_parameters(ac,ad,ae,cx,cy), number-of-inv_poly_parameters, inv_poly_parameters(p0,p1,p2,...)
	RawdataParam_DistortionType_ocam RawdataParam_DistortionType_Enum = 3
	// Bosch Fisheye model: same as ocam
	RawdataParam_DistortionType_bosch_fisheye RawdataParam_DistortionType_Enum = -1001
)

// Enum value maps for RawdataParam_DistortionType_Enum.
var (
	RawdataParam_DistortionType_Enum_name = map[int32]string{
		0:     "pinhole",
		1:     "mei",
		2:     "kb",
		3:     "ocam",
		-1001: "bosch_fisheye",
	}
	RawdataParam_DistortionType_Enum_value = map[string]int32{
		"pinhole":       0,
		"mei":           1,
		"kb":            2,
		"ocam":          3,
		"bosch_fisheye": -1001,
	}
)

func (x RawdataParam_DistortionType_Enum) Enum() *RawdataParam_DistortionType_Enum {
	p := new(RawdataParam_DistortionType_Enum)
	*p = x
	return p
}

func (x RawdataParam_DistortionType_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RawdataParam_DistortionType_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_elemanno_proto_enumTypes[2].Descriptor()
}

func (RawdataParam_DistortionType_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_elemanno_proto_enumTypes[2]
}

func (x RawdataParam_DistortionType_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RawdataParam_DistortionType_Enum.Descriptor instead.
func (RawdataParam_DistortionType_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{1, 1, 0}
}

type Rawdata_Type_Enum int32

const (
	Rawdata_Type_unspecified Rawdata_Type_Enum = 0
	Rawdata_Type_image       Rawdata_Type_Enum = 1
	Rawdata_Type_pointcloud  Rawdata_Type_Enum = 2
)

// Enum value maps for Rawdata_Type_Enum.
var (
	Rawdata_Type_Enum_name = map[int32]string{
		0: "unspecified",
		1: "image",
		2: "pointcloud",
	}
	Rawdata_Type_Enum_value = map[string]int32{
		"unspecified": 0,
		"image":       1,
		"pointcloud":  2,
	}
)

func (x Rawdata_Type_Enum) Enum() *Rawdata_Type_Enum {
	p := new(Rawdata_Type_Enum)
	*p = x
	return p
}

func (x Rawdata_Type_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Rawdata_Type_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_elemanno_proto_enumTypes[3].Descriptor()
}

func (Rawdata_Type_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_elemanno_proto_enumTypes[3]
}

func (x Rawdata_Type_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Rawdata_Type_Enum.Descriptor instead.
func (Rawdata_Type_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{2, 3, 0}
}

type Rawdata_Format_Enum int32

const (
	Rawdata_Format_unspecified Rawdata_Format_Enum = 0
	Rawdata_Format_json        Rawdata_Format_Enum = 1
	Rawdata_Format_png         Rawdata_Format_Enum = 2
	Rawdata_Format_jpg         Rawdata_Format_Enum = 3
	Rawdata_Format_pcd         Rawdata_Format_Enum = 4
	// indicate that GET rawdata request will return types.Filelist instead of the rawdata itself
	Rawdata_Format_filelist Rawdata_Format_Enum = 5
	Rawdata_Format_webp     Rawdata_Format_Enum = 6
)

// Enum value maps for Rawdata_Format_Enum.
var (
	Rawdata_Format_Enum_name = map[int32]string{
		0: "unspecified",
		1: "json",
		2: "png",
		3: "jpg",
		4: "pcd",
		5: "filelist",
		6: "webp",
	}
	Rawdata_Format_Enum_value = map[string]int32{
		"unspecified": 0,
		"json":        1,
		"png":         2,
		"jpg":         3,
		"pcd":         4,
		"filelist":    5,
		"webp":        6,
	}
)

func (x Rawdata_Format_Enum) Enum() *Rawdata_Format_Enum {
	p := new(Rawdata_Format_Enum)
	*p = x
	return p
}

func (x Rawdata_Format_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Rawdata_Format_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_elemanno_proto_enumTypes[4].Descriptor()
}

func (Rawdata_Format_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_elemanno_proto_enumTypes[4]
}

func (x Rawdata_Format_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Rawdata_Format_Enum.Descriptor instead.
func (Rawdata_Format_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{2, 4, 0}
}

type Object_Source_Enum int32

const (
	Object_Source_unspecified Object_Source_Enum = 0
	// by a labeler
	Object_Source_manual Object_Source_Enum = 1
	// by a model
	Object_Source_prelabel Object_Source_Enum = 2
	// by interpolation (frame series)
	Object_Source_interpolation Object_Source_Enum = 3
	// by projection (3D to 2D)
	Object_Source_projection Object_Source_Enum = 4
)

// Enum value maps for Object_Source_Enum.
var (
	Object_Source_Enum_name = map[int32]string{
		0: "unspecified",
		1: "manual",
		2: "prelabel",
		3: "interpolation",
		4: "projection",
	}
	Object_Source_Enum_value = map[string]int32{
		"unspecified":   0,
		"manual":        1,
		"prelabel":      2,
		"interpolation": 3,
		"projection":    4,
	}
)

func (x Object_Source_Enum) Enum() *Object_Source_Enum {
	p := new(Object_Source_Enum)
	*p = x
	return p
}

func (x Object_Source_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Object_Source_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_elemanno_proto_enumTypes[5].Descriptor()
}

func (Object_Source_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_elemanno_proto_enumTypes[5]
}

func (x Object_Source_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Object_Source_Enum.Descriptor instead.
func (Object_Source_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{5, 3, 0}
}

// 一帧数据，包含一个或多个待标注文件
type Element struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// element index in the lot dataset
	Index int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	// name of the element: innermost-folder/frame-index
	Name  string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type  Element_Type_Enum `protobuf:"varint,3,opt,name=type,proto3,enum=anno.v1.Element_Type_Enum" json:"type,omitempty"`
	Datas []*Rawdata        `protobuf:"bytes,4,rep,name=datas,proto3" json:"datas,omitempty"`
	// 标注结果；
	// 仅用于结果导出，或在导入时用于传输已经有标注信息的数据；
	// 标注过程中，请使用 Job 里的相关字段
	Anno *ElementAnno `protobuf:"bytes,5,opt,name=anno,proto3" json:"anno,omitempty"`
}

func (x *Element) Reset() {
	*x = Element{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Element) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Element) ProtoMessage() {}

func (x *Element) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Element.ProtoReflect.Descriptor instead.
func (*Element) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{0}
}

func (x *Element) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *Element) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Element) GetType() Element_Type_Enum {
	if x != nil {
		return x.Type
	}
	return Element_Type_unspecified
}

func (x *Element) GetDatas() []*Rawdata {
	if x != nil {
		return x.Datas
	}
	return nil
}

func (x *Element) GetAnno() *ElementAnno {
	if x != nil {
		return x.Anno
	}
	return nil
}

// 与待标注文件相关的参数或变换矩阵
type RawdataParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type RawdataParam_Type_Enum `protobuf:"varint,1,opt,name=type,proto3,enum=anno.v1.RawdataParam_Type_Enum" json:"type,omitempty"`
	// number of columns in the data
	ColumnCnt int32 `protobuf:"varint,2,opt,name=column_cnt,json=columnCnt,proto3" json:"column_cnt,omitempty"`
	// if type is distortion, the 1st number is the distortion model type (DistortionType)
	Data []float64 `protobuf:"fixed64,3,rep,packed,name=data,proto3" json:"data,omitempty"`
}

func (x *RawdataParam) Reset() {
	*x = RawdataParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RawdataParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawdataParam) ProtoMessage() {}

func (x *RawdataParam) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawdataParam.ProtoReflect.Descriptor instead.
func (*RawdataParam) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{1}
}

func (x *RawdataParam) GetType() RawdataParam_Type_Enum {
	if x != nil {
		return x.Type
	}
	return RawdataParam_Type_matrix
}

func (x *RawdataParam) GetColumnCnt() int32 {
	if x != nil {
		return x.ColumnCnt
	}
	return 0
}

func (x *RawdataParam) GetData() []float64 {
	if x != nil {
		return x.Data
	}
	return nil
}

// 待标注文件，比如一张图片、点云的一帧
type Rawdata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file pathname
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// data type
	Type Rawdata_Type_Enum `protobuf:"varint,2,opt,name=type,proto3,enum=anno.v1.Rawdata_Type_Enum" json:"type,omitempty"`
	// data format
	Format Rawdata_Format_Enum `protobuf:"varint,3,opt,name=format,proto3,enum=anno.v1.Rawdata_Format_Enum" json:"format,omitempty"`
	Url    string              `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	// number of bytes in the file
	Size float64 `protobuf:"fixed64,5,opt,name=size,proto3" json:"size,omitempty"`
	// SHA-256 of the file
	Sha256 string `protobuf:"bytes,6,opt,name=sha256,proto3" json:"sha256,omitempty"`
	// display name (sensor name)
	Title string `protobuf:"bytes,7,opt,name=title,proto3" json:"title,omitempty"`
	// 变换参数，可以将世界坐标系下的点映射到图片上
	Transform []*RawdataParam `protobuf:"bytes,8,rep,name=transform,proto3" json:"transform,omitempty"`
	// metadata of rawdata
	Meta *Rawdata_Meta `protobuf:"bytes,9,opt,name=meta,proto3" json:"meta,omitempty"`
	// 识别出的物体列表；
	// 仅用于结果导出，或在导入时用于传输已经有标注信息的数据；
	// 标注过程中，请使用 Job 里的相关字段
	Ins []*Object `protobuf:"bytes,10,rep,name=ins,proto3" json:"ins,omitempty"`
	// original pathname of the rawdata
	OrigName string `protobuf:"bytes,11,opt,name=orig_name,json=origName,proto3" json:"orig_name,omitempty"`
	// file embedding
	Embedding *Rawdata_Embedding `protobuf:"bytes,12,opt,name=embedding,proto3" json:"embedding,omitempty"`
}

func (x *Rawdata) Reset() {
	*x = Rawdata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rawdata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rawdata) ProtoMessage() {}

func (x *Rawdata) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rawdata.ProtoReflect.Descriptor instead.
func (*Rawdata) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{2}
}

func (x *Rawdata) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Rawdata) GetType() Rawdata_Type_Enum {
	if x != nil {
		return x.Type
	}
	return Rawdata_Type_unspecified
}

func (x *Rawdata) GetFormat() Rawdata_Format_Enum {
	if x != nil {
		return x.Format
	}
	return Rawdata_Format_unspecified
}

func (x *Rawdata) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Rawdata) GetSize() float64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Rawdata) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *Rawdata) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Rawdata) GetTransform() []*RawdataParam {
	if x != nil {
		return x.Transform
	}
	return nil
}

func (x *Rawdata) GetMeta() *Rawdata_Meta {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *Rawdata) GetIns() []*Object {
	if x != nil {
		return x.Ins
	}
	return nil
}

func (x *Rawdata) GetOrigName() string {
	if x != nil {
		return x.OrigName
	}
	return ""
}

func (x *Rawdata) GetEmbedding() *Rawdata_Embedding {
	if x != nil {
		return x.Embedding
	}
	return nil
}

type Direction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// origin point; if empty, it is the widget center point;
	// x, y if 2D; x, y, z if 3D
	Origin []float64 `protobuf:"fixed64,1,rep,packed,name=origin,proto3" json:"origin,omitempty"`
	// toward point;
	// x, y if 2D; x, y, z if 3D
	Toward []float64 `protobuf:"fixed64,2,rep,packed,name=toward,proto3" json:"toward,omitempty"`
}

func (x *Direction) Reset() {
	*x = Direction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Direction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Direction) ProtoMessage() {}

func (x *Direction) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Direction.ProtoReflect.Descriptor instead.
func (*Direction) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{3}
}

func (x *Direction) GetOrigin() []float64 {
	if x != nil {
		return x.Origin
	}
	return nil
}

func (x *Direction) GetToward() []float64 {
	if x != nil {
		return x.Toward
	}
	return nil
}

type AttrAndValues struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the attribute
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// values of the attribute
	Values []string `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *AttrAndValues) Reset() {
	*x = AttrAndValues{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttrAndValues) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttrAndValues) ProtoMessage() {}

func (x *AttrAndValues) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttrAndValues.ProtoReflect.Descriptor instead.
func (*AttrAndValues) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{4}
}

func (x *AttrAndValues) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AttrAndValues) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

type Object struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// UUID of the object
	Uuid string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	// 一个任务包中，相同的 track_id 表示同一个实体对象。
	// 命名可以采用 Job.Uid + Label.name + index 的模式，例如：“xxx-car-1”
	TrackId string        `protobuf:"bytes,2,opt,name=track_id,json=trackId,proto3" json:"track_id,omitempty"`
	Label   *Object_Label `protobuf:"bytes,3,opt,name=label,proto3" json:"label,omitempty"`
	// 在连续帧标注中，表示该物体从下一帧开始将不复存在
	VanishLater bool `protobuf:"varint,4,opt,name=vanish_later,json=vanishLater,proto3" json:"vanish_later,omitempty"`
	// it is a compounded object if not null
	Compound *Object_Compound `protobuf:"bytes,6,opt,name=compound,proto3" json:"compound,omitempty"`
	// how the object is created
	Source Object_Source_Enum `protobuf:"varint,7,opt,name=source,proto3,enum=anno.v1.Object_Source_Enum" json:"source,omitempty"`
}

func (x *Object) Reset() {
	*x = Object{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Object) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Object) ProtoMessage() {}

func (x *Object) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Object.ProtoReflect.Descriptor instead.
func (*Object) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{5}
}

func (x *Object) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *Object) GetTrackId() string {
	if x != nil {
		return x.TrackId
	}
	return ""
}

func (x *Object) GetLabel() *Object_Label {
	if x != nil {
		return x.Label
	}
	return nil
}

func (x *Object) GetVanishLater() bool {
	if x != nil {
		return x.VanishLater
	}
	return false
}

func (x *Object) GetCompound() *Object_Compound {
	if x != nil {
		return x.Compound
	}
	return nil
}

func (x *Object) GetSource() Object_Source_Enum {
	if x != nil {
		return x.Source
	}
	return Object_Source_unspecified
}

// annotations of a rawdata
type RawdataAnno struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the rawdata
	Name    string           `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Objects []*Object        `protobuf:"bytes,2,rep,name=objects,proto3" json:"objects,omitempty"`
	Attrs   []*AttrAndValues `protobuf:"bytes,3,rep,name=attrs,proto3" json:"attrs,omitempty"`
	// segmentation result of the rawdata; null if the task is not a segmentation task
	Segmentation *Segmentation `protobuf:"bytes,4,opt,name=segmentation,proto3" json:"segmentation,omitempty"`
	// original pathname of the rawdata
	OrigName string `protobuf:"bytes,5,opt,name=orig_name,json=origName,proto3" json:"orig_name,omitempty"`
	// url of the rawdata, only available during exporting anno results
	Url string `protobuf:"bytes,6,opt,name=url,proto3" json:"url,omitempty"`
	// metadata
	Metadata *RawdataAnno_Metadata `protobuf:"bytes,7,opt,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *RawdataAnno) Reset() {
	*x = RawdataAnno{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RawdataAnno) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawdataAnno) ProtoMessage() {}

func (x *RawdataAnno) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawdataAnno.ProtoReflect.Descriptor instead.
func (*RawdataAnno) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{6}
}

func (x *RawdataAnno) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RawdataAnno) GetObjects() []*Object {
	if x != nil {
		return x.Objects
	}
	return nil
}

func (x *RawdataAnno) GetAttrs() []*AttrAndValues {
	if x != nil {
		return x.Attrs
	}
	return nil
}

func (x *RawdataAnno) GetSegmentation() *Segmentation {
	if x != nil {
		return x.Segmentation
	}
	return nil
}

func (x *RawdataAnno) GetOrigName() string {
	if x != nil {
		return x.OrigName
	}
	return ""
}

func (x *RawdataAnno) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *RawdataAnno) GetMetadata() *RawdataAnno_Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type Segmentation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// classes definition; if empty, class name is RawdataAnno.Object.uuid with the matching seg_class_id
	Classes []*Segmentation_Class `protobuf:"bytes,1,rep,name=classes,proto3" json:"classes,omitempty"`
	// unpacked RLE; either rle or rle_pack is set, not both
	Rle *Segmentation_RLE `protobuf:"bytes,2,opt,name=rle,proto3" json:"rle,omitempty"`
	// packed RLE; serialize RLE using JSON, then compress using gzip, then encode in base64;
	// format: data:application/json;gzip;base64,[base64-encoded-content]
	//
	//	or http://packed-RLE-file-url (file format: RLE;json;gzip\ngzip-encoded-content)
	RlePack string `protobuf:"bytes,3,opt,name=rle_pack,json=rlePack,proto3" json:"rle_pack,omitempty"`
}

func (x *Segmentation) Reset() {
	*x = Segmentation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Segmentation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Segmentation) ProtoMessage() {}

func (x *Segmentation) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Segmentation.ProtoReflect.Descriptor instead.
func (*Segmentation) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{7}
}

func (x *Segmentation) GetClasses() []*Segmentation_Class {
	if x != nil {
		return x.Classes
	}
	return nil
}

func (x *Segmentation) GetRle() *Segmentation_RLE {
	if x != nil {
		return x.Rle
	}
	return nil
}

func (x *Segmentation) GetRlePack() string {
	if x != nil {
		return x.RlePack
	}
	return ""
}

// annotations of an element (frame)
type ElementAnno struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// element index in the lot dataset
	Index int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	// name of the element: innermost-folder/frame-index
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 相应位置的元素对应 Element 的 datas 相应位置的结果
	RawdataAnnos []*RawdataAnno   `protobuf:"bytes,3,rep,name=rawdata_annos,json=rawdataAnnos,proto3" json:"rawdata_annos,omitempty"`
	Attrs        []*AttrAndValues `protobuf:"bytes,4,rep,name=attrs,proto3" json:"attrs,omitempty"`
	// number of objects annotated in the element
	InsCnt int32 `protobuf:"varint,5,opt,name=ins_cnt,json=insCnt,proto3" json:"ins_cnt,omitempty"`
	// metadata of the element annos
	Metadata       *ElementAnno_Metadata `protobuf:"bytes,6,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Segmentation3D *Segmentation3D       `protobuf:"bytes,7,opt,name=segmentation3d,proto3" json:"segmentation3d,omitempty"`
}

func (x *ElementAnno) Reset() {
	*x = ElementAnno{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ElementAnno) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ElementAnno) ProtoMessage() {}

func (x *ElementAnno) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ElementAnno.ProtoReflect.Descriptor instead.
func (*ElementAnno) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{8}
}

func (x *ElementAnno) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *ElementAnno) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ElementAnno) GetRawdataAnnos() []*RawdataAnno {
	if x != nil {
		return x.RawdataAnnos
	}
	return nil
}

func (x *ElementAnno) GetAttrs() []*AttrAndValues {
	if x != nil {
		return x.Attrs
	}
	return nil
}

func (x *ElementAnno) GetInsCnt() int32 {
	if x != nil {
		return x.InsCnt
	}
	return 0
}

func (x *ElementAnno) GetMetadata() *ElementAnno_Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ElementAnno) GetSegmentation3D() *Segmentation3D {
	if x != nil {
		return x.Segmentation3D
	}
	return nil
}

type Segmentation3D struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *Segmentation3DResult      `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Statistic []*Segmentation3DStatistic `protobuf:"bytes,2,rep,name=statistic,proto3" json:"statistic,omitempty"`
}

func (x *Segmentation3D) Reset() {
	*x = Segmentation3D{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Segmentation3D) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Segmentation3D) ProtoMessage() {}

func (x *Segmentation3D) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Segmentation3D.ProtoReflect.Descriptor instead.
func (*Segmentation3D) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{9}
}

func (x *Segmentation3D) GetResult() *Segmentation3DResult {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *Segmentation3D) GetStatistic() []*Segmentation3DStatistic {
	if x != nil {
		return x.Statistic
	}
	return nil
}

type Segmentation3DResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category   []string `protobuf:"bytes,1,rep,name=category,proto3" json:"category,omitempty"`
	InstanceId []int32  `protobuf:"varint,2,rep,packed,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
}

func (x *Segmentation3DResult) Reset() {
	*x = Segmentation3DResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Segmentation3DResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Segmentation3DResult) ProtoMessage() {}

func (x *Segmentation3DResult) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Segmentation3DResult.ProtoReflect.Descriptor instead.
func (*Segmentation3DResult) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{10}
}

func (x *Segmentation3DResult) GetCategory() []string {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *Segmentation3DResult) GetInstanceId() []int32 {
	if x != nil {
		return x.InstanceId
	}
	return nil
}

type Segmentation3DStatistic struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CategoryName string                    `protobuf:"bytes,1,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
	Num          int32                     `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	Instances    []*Segmentation3DInstance `protobuf:"bytes,3,rep,name=instances,proto3" json:"instances,omitempty"`
}

func (x *Segmentation3DStatistic) Reset() {
	*x = Segmentation3DStatistic{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Segmentation3DStatistic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Segmentation3DStatistic) ProtoMessage() {}

func (x *Segmentation3DStatistic) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Segmentation3DStatistic.ProtoReflect.Descriptor instead.
func (*Segmentation3DStatistic) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{11}
}

func (x *Segmentation3DStatistic) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

func (x *Segmentation3DStatistic) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *Segmentation3DStatistic) GetInstances() []*Segmentation3DInstance {
	if x != nil {
		return x.Instances
	}
	return nil
}

type Segmentation3DInstance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstanceId int32 `protobuf:"varint,1,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
	Num        int32 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
}

func (x *Segmentation3DInstance) Reset() {
	*x = Segmentation3DInstance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Segmentation3DInstance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Segmentation3DInstance) ProtoMessage() {}

func (x *Segmentation3DInstance) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Segmentation3DInstance.ProtoReflect.Descriptor instead.
func (*Segmentation3DInstance) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{12}
}

func (x *Segmentation3DInstance) GetInstanceId() int32 {
	if x != nil {
		return x.InstanceId
	}
	return 0
}

func (x *Segmentation3DInstance) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

// annotations
type ExportAnnos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ElementAnnos []*ElementAnno `protobuf:"bytes,1,rep,name=element_annos,json=elementAnnos,proto3" json:"element_annos,omitempty"`
	JobUid       string         `protobuf:"bytes,2,opt,name=job_uid,json=jobUid,proto3" json:"job_uid,omitempty"`
}

func (x *ExportAnnos) Reset() {
	*x = ExportAnnos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportAnnos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportAnnos) ProtoMessage() {}

func (x *ExportAnnos) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportAnnos.ProtoReflect.Descriptor instead.
func (*ExportAnnos) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{13}
}

func (x *ExportAnnos) GetElementAnnos() []*ElementAnno {
	if x != nil {
		return x.ElementAnnos
	}
	return nil
}

func (x *ExportAnnos) GetJobUid() string {
	if x != nil {
		return x.JobUid
	}
	return ""
}

type Element_Type struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Element_Type) Reset() {
	*x = Element_Type{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Element_Type) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Element_Type) ProtoMessage() {}

func (x *Element_Type) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Element_Type.ProtoReflect.Descriptor instead.
func (*Element_Type) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{0, 0}
}

type RawdataParam_Type struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RawdataParam_Type) Reset() {
	*x = RawdataParam_Type{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RawdataParam_Type) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawdataParam_Type) ProtoMessage() {}

func (x *RawdataParam_Type) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawdataParam_Type.ProtoReflect.Descriptor instead.
func (*RawdataParam_Type) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{1, 0}
}

type RawdataParam_DistortionType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RawdataParam_DistortionType) Reset() {
	*x = RawdataParam_DistortionType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RawdataParam_DistortionType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawdataParam_DistortionType) ProtoMessage() {}

func (x *RawdataParam_DistortionType) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawdataParam_DistortionType.ProtoReflect.Descriptor instead.
func (*RawdataParam_DistortionType) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{1, 1}
}

type Rawdata_ImageMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width  int32 `protobuf:"varint,1,opt,name=width,proto3" json:"width,omitempty"`
	Height int32 `protobuf:"varint,2,opt,name=height,proto3" json:"height,omitempty"`
	// camera name
	Camera string `protobuf:"bytes,3,opt,name=camera,proto3" json:"camera,omitempty"`
}

func (x *Rawdata_ImageMeta) Reset() {
	*x = Rawdata_ImageMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rawdata_ImageMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rawdata_ImageMeta) ProtoMessage() {}

func (x *Rawdata_ImageMeta) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rawdata_ImageMeta.ProtoReflect.Descriptor instead.
func (*Rawdata_ImageMeta) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{2, 0}
}

func (x *Rawdata_ImageMeta) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *Rawdata_ImageMeta) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *Rawdata_ImageMeta) GetCamera() string {
	if x != nil {
		return x.Camera
	}
	return ""
}

type Rawdata_PCDMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// number of points in the pcd file
	Points int32 `protobuf:"varint,1,opt,name=points,proto3" json:"points,omitempty"`
	// viewpoint of the point cloud in the point cloud's coordinate system
	Viewpoint *RawdataParam `protobuf:"bytes,2,opt,name=viewpoint,proto3" json:"viewpoint,omitempty"`
	// origin point of the point cloud in world coordinate system when point cloud is not in world coordinate system,
	// in the form of [x,y,z,qx,qy,qz,qw]
	Pose []float64 `protobuf:"fixed64,3,rep,packed,name=pose,proto3" json:"pose,omitempty"`
}

func (x *Rawdata_PCDMeta) Reset() {
	*x = Rawdata_PCDMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rawdata_PCDMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rawdata_PCDMeta) ProtoMessage() {}

func (x *Rawdata_PCDMeta) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rawdata_PCDMeta.ProtoReflect.Descriptor instead.
func (*Rawdata_PCDMeta) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{2, 1}
}

func (x *Rawdata_PCDMeta) GetPoints() int32 {
	if x != nil {
		return x.Points
	}
	return 0
}

func (x *Rawdata_PCDMeta) GetViewpoint() *RawdataParam {
	if x != nil {
		return x.Viewpoint
	}
	return nil
}

func (x *Rawdata_PCDMeta) GetPose() []float64 {
	if x != nil {
		return x.Pose
	}
	return nil
}

type Rawdata_Meta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image *Rawdata_ImageMeta `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	Pcd   *Rawdata_PCDMeta   `protobuf:"bytes,2,opt,name=pcd,proto3" json:"pcd,omitempty"`
}

func (x *Rawdata_Meta) Reset() {
	*x = Rawdata_Meta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rawdata_Meta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rawdata_Meta) ProtoMessage() {}

func (x *Rawdata_Meta) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rawdata_Meta.ProtoReflect.Descriptor instead.
func (*Rawdata_Meta) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{2, 2}
}

func (x *Rawdata_Meta) GetImage() *Rawdata_ImageMeta {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *Rawdata_Meta) GetPcd() *Rawdata_PCDMeta {
	if x != nil {
		return x.Pcd
	}
	return nil
}

type Rawdata_Type struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Rawdata_Type) Reset() {
	*x = Rawdata_Type{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rawdata_Type) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rawdata_Type) ProtoMessage() {}

func (x *Rawdata_Type) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rawdata_Type.ProtoReflect.Descriptor instead.
func (*Rawdata_Type) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{2, 3}
}

type Rawdata_Format struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Rawdata_Format) Reset() {
	*x = Rawdata_Format{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rawdata_Format) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rawdata_Format) ProtoMessage() {}

func (x *Rawdata_Format) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rawdata_Format.ProtoReflect.Descriptor instead.
func (*Rawdata_Format) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{2, 4}
}

type Rawdata_Embedding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// embedding file URL
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *Rawdata_Embedding) Reset() {
	*x = Rawdata_Embedding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rawdata_Embedding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rawdata_Embedding) ProtoMessage() {}

func (x *Rawdata_Embedding) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rawdata_Embedding.ProtoReflect.Descriptor instead.
func (*Rawdata_Embedding) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{2, 5}
}

func (x *Rawdata_Embedding) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type Object_Widget struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// widget name
	Name WidgetName_Enum `protobuf:"varint,1,opt,name=name,proto3,enum=anno.v1.WidgetName_Enum" json:"name,omitempty"`
	// characteristic values of geometric shapes, or bitmap origin (left, top)
	Data []float64 `protobuf:"fixed64,2,rep,packed,name=data,proto3" json:"data,omitempty"`
	// gaps within the widget if any
	Gaps []*Object_Widget `protobuf:"bytes,3,rep,name=gaps,proto3" json:"gaps,omitempty"`
	// bitmap file URL or data URI, e.g. data:image/png;base64,<base64-encoded-file-content>
	// pointcloud file URL or data URI, e.g. data:application/pcd;base64,<base64-encoded-file-content>
	Uri string `protobuf:"bytes,4,opt,name=uri,proto3" json:"uri,omitempty"`
	// forward direction. if origin is null, then the center point of the widget is implied.
	Forward *Direction `protobuf:"bytes,5,opt,name=forward,proto3" json:"forward,omitempty"`
	// number of points within the object (in 3D segmentation tasks)
	PointCnt int32 `protobuf:"varint,6,opt,name=point_cnt,json=pointCnt,proto3" json:"point_cnt,omitempty"`
	// class ID in RawdataAnno.Segmentation; zero if the task is not a segmentation task
	SegClassId int32 `protobuf:"varint,7,opt,name=seg_class_id,json=segClassId,proto3" json:"seg_class_id,omitempty"`
	// line type used to draw the widget
	LineType WidgetLineType_Enum `protobuf:"varint,8,opt,name=line_type,json=lineType,proto3,enum=anno.v1.WidgetLineType_Enum" json:"line_type,omitempty"`
	// related parent lines
	ParentLines []string `protobuf:"bytes,9,rep,name=parent_lines,json=parentLines,proto3" json:"parent_lines,omitempty"`
}

func (x *Object_Widget) Reset() {
	*x = Object_Widget{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Object_Widget) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Object_Widget) ProtoMessage() {}

func (x *Object_Widget) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Object_Widget.ProtoReflect.Descriptor instead.
func (*Object_Widget) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{5, 0}
}

func (x *Object_Widget) GetName() WidgetName_Enum {
	if x != nil {
		return x.Name
	}
	return WidgetName_unspecified
}

func (x *Object_Widget) GetData() []float64 {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Object_Widget) GetGaps() []*Object_Widget {
	if x != nil {
		return x.Gaps
	}
	return nil
}

func (x *Object_Widget) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *Object_Widget) GetForward() *Direction {
	if x != nil {
		return x.Forward
	}
	return nil
}

func (x *Object_Widget) GetPointCnt() int32 {
	if x != nil {
		return x.PointCnt
	}
	return 0
}

func (x *Object_Widget) GetSegClassId() int32 {
	if x != nil {
		return x.SegClassId
	}
	return 0
}

func (x *Object_Widget) GetLineType() WidgetLineType_Enum {
	if x != nil {
		return x.LineType
	}
	return WidgetLineType_line
}

func (x *Object_Widget) GetParentLines() []string {
	if x != nil {
		return x.ParentLines
	}
	return nil
}

type Object_Label struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// label name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// widget will be null if compound is not null
	Widget *Object_Widget `protobuf:"bytes,2,opt,name=widget,proto3" json:"widget,omitempty"`
	// attributes associated with the object
	Attrs []*AttrAndValues `protobuf:"bytes,3,rep,name=attrs,proto3" json:"attrs,omitempty"`
}

func (x *Object_Label) Reset() {
	*x = Object_Label{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Object_Label) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Object_Label) ProtoMessage() {}

func (x *Object_Label) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Object_Label.ProtoReflect.Descriptor instead.
func (*Object_Label) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{5, 1}
}

func (x *Object_Label) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Object_Label) GetWidget() *Object_Widget {
	if x != nil {
		return x.Widget
	}
	return nil
}

func (x *Object_Label) GetAttrs() []*AttrAndValues {
	if x != nil {
		return x.Attrs
	}
	return nil
}

type Object_Compound struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// referenced component object UUIDs
	Parts []string `protobuf:"bytes,1,rep,name=parts,proto3" json:"parts,omitempty"`
}

func (x *Object_Compound) Reset() {
	*x = Object_Compound{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Object_Compound) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Object_Compound) ProtoMessage() {}

func (x *Object_Compound) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Object_Compound.ProtoReflect.Descriptor instead.
func (*Object_Compound) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{5, 2}
}

func (x *Object_Compound) GetParts() []string {
	if x != nil {
		return x.Parts
	}
	return nil
}

type Object_Source struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Object_Source) Reset() {
	*x = Object_Source{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Object_Source) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Object_Source) ProtoMessage() {}

func (x *Object_Source) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Object_Source.ProtoReflect.Descriptor instead.
func (*Object_Source) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{5, 3}
}

type RawdataAnno_Metadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rawdata type
	Type Rawdata_Type_Enum `protobuf:"varint,1,opt,name=type,proto3,enum=anno.v1.Rawdata_Type_Enum" json:"type,omitempty"`
	// camera name if the rawdata is an image (ImageMeta.camera)
	Camera string `protobuf:"bytes,2,opt,name=camera,proto3" json:"camera,omitempty"`
}

func (x *RawdataAnno_Metadata) Reset() {
	*x = RawdataAnno_Metadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RawdataAnno_Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawdataAnno_Metadata) ProtoMessage() {}

func (x *RawdataAnno_Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawdataAnno_Metadata.ProtoReflect.Descriptor instead.
func (*RawdataAnno_Metadata) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{6, 0}
}

func (x *RawdataAnno_Metadata) GetType() Rawdata_Type_Enum {
	if x != nil {
		return x.Type
	}
	return Rawdata_Type_unspecified
}

func (x *RawdataAnno_Metadata) GetCamera() string {
	if x != nil {
		return x.Camera
	}
	return ""
}

type Segmentation_Class struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// serial number, start from 1
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// it is Object.uuid for instance segmentation; it is label name for semantic/panoptic segmentation
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *Segmentation_Class) Reset() {
	*x = Segmentation_Class{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Segmentation_Class) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Segmentation_Class) ProtoMessage() {}

func (x *Segmentation_Class) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Segmentation_Class.ProtoReflect.Descriptor instead.
func (*Segmentation_Class) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{7, 0}
}

func (x *Segmentation_Class) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Segmentation_Class) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type Segmentation_RLE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// number of consecutive points having the same class ID
	Runs []int32 `protobuf:"varint,1,rep,packed,name=runs,proto3" json:"runs,omitempty"`
	// Class.id of the points at the corresponding index in runs;
	// if empty, it implies a sequence of 0 and 1, starting with 0: [0, 1, 0, 1, ...]
	Vals []int32 `protobuf:"varint,2,rep,packed,name=vals,proto3" json:"vals,omitempty"`
}

func (x *Segmentation_RLE) Reset() {
	*x = Segmentation_RLE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Segmentation_RLE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Segmentation_RLE) ProtoMessage() {}

func (x *Segmentation_RLE) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Segmentation_RLE.ProtoReflect.Descriptor instead.
func (*Segmentation_RLE) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{7, 1}
}

func (x *Segmentation_RLE) GetRuns() []int32 {
	if x != nil {
		return x.Runs
	}
	return nil
}

func (x *Segmentation_RLE) GetVals() []int32 {
	if x != nil {
		return x.Vals
	}
	return nil
}

type ElementAnno_Metadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// executor of each phase
	Executors []*ElementAnno_Metadata_Executor `protobuf:"bytes,1,rep,name=executors,proto3" json:"executors,omitempty"`
}

func (x *ElementAnno_Metadata) Reset() {
	*x = ElementAnno_Metadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ElementAnno_Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ElementAnno_Metadata) ProtoMessage() {}

func (x *ElementAnno_Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ElementAnno_Metadata.ProtoReflect.Descriptor instead.
func (*ElementAnno_Metadata) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ElementAnno_Metadata) GetExecutors() []*ElementAnno_Metadata_Executor {
	if x != nil {
		return x.Executors
	}
	return nil
}

type ElementAnno_Metadata_Executor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	User     *v1.BaseUser           `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	SubmitAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=submit_at,json=submitAt,proto3" json:"submit_at,omitempty"`
	Phase    int32                  `protobuf:"varint,3,opt,name=phase,proto3" json:"phase,omitempty"`
}

func (x *ElementAnno_Metadata_Executor) Reset() {
	*x = ElementAnno_Metadata_Executor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_elemanno_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ElementAnno_Metadata_Executor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ElementAnno_Metadata_Executor) ProtoMessage() {}

func (x *ElementAnno_Metadata_Executor) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_elemanno_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ElementAnno_Metadata_Executor.ProtoReflect.Descriptor instead.
func (*ElementAnno_Metadata_Executor) Descriptor() ([]byte, []int) {
	return file_anno_v1_elemanno_proto_rawDescGZIP(), []int{8, 0, 0}
}

func (x *ElementAnno_Metadata_Executor) GetUser() *v1.BaseUser {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *ElementAnno_Metadata_Executor) GetSubmitAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SubmitAt
	}
	return nil
}

func (x *ElementAnno_Metadata_Executor) GetPhase() int32 {
	if x != nil {
		return x.Phase
	}
	return 0
}

var File_anno_v1_elemanno_proto protoreflect.FileDescriptor

var file_anno_v1_elemanno_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x6e, 0x6e, 0x6f, 0x2f,
	0x76, 0x31, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x11, 0x69, 0x61, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xca, 0x02, 0x0a, 0x07, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e,
	0x75, 0x6d, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x05, 0x64, 0x61, 0x74, 0x61, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61,
	0x77, 0x64, 0x61, 0x74, 0x61, 0x52, 0x05, 0x64, 0x61, 0x74, 0x61, 0x73, 0x12, 0x28, 0x0a, 0x04,
	0x61, 0x6e, 0x6e, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6e, 0x6e, 0x6f,
	0x52, 0x04, 0x61, 0x6e, 0x6e, 0x6f, 0x1a, 0x64, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x22, 0x5c,
	0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x32, 0x64, 0x10, 0x03,
	0x12, 0x0c, 0x0a, 0x08, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x33, 0x64, 0x10, 0x04, 0x12, 0x0c,
	0x0a, 0x08, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x34, 0x64, 0x10, 0x05, 0x3a, 0x21, 0xba, 0x47,
	0x1e, 0xba, 0x01, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0xba, 0x01, 0x04, 0x74, 0x79, 0x70, 0x65, 0xba, 0x01, 0x05, 0x64, 0x61, 0x74, 0x61, 0x73, 0x22,
	0xc8, 0x02, 0x0a, 0x0c, 0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x12, 0x3d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x43, 0x6e, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x01, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x1a, 0x48, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x22, 0x40, 0x0a, 0x04, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x0a, 0x0a, 0x06, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x10, 0x00, 0x12, 0x0d,
	0x0a, 0x09, 0x65, 0x78, 0x74, 0x72, 0x69, 0x6e, 0x73, 0x69, 0x63, 0x10, 0x01, 0x12, 0x0d, 0x0a,
	0x09, 0x69, 0x6e, 0x74, 0x72, 0x69, 0x6e, 0x73, 0x69, 0x63, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a,
	0x64, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x03, 0x1a, 0x5c, 0x0a, 0x0e,
	0x44, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x4a,
	0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0b, 0x0a, 0x07, 0x70, 0x69, 0x6e, 0x68, 0x6f, 0x6c,
	0x65, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x6d, 0x65, 0x69, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02,
	0x6b, 0x62, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x6f, 0x63, 0x61, 0x6d, 0x10, 0x03, 0x12, 0x1a,
	0x0a, 0x0d, 0x62, 0x6f, 0x73, 0x63, 0x68, 0x5f, 0x66, 0x69, 0x73, 0x68, 0x65, 0x79, 0x65, 0x10,
	0x97, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x3a, 0x1e, 0xba, 0x47, 0x1b, 0xba,
	0x01, 0x04, 0x74, 0x79, 0x70, 0x65, 0xba, 0x01, 0x0a, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f,
	0x63, 0x6e, 0x74, 0xba, 0x01, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xa0, 0x08, 0x0a, 0x07, 0x52,
	0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e,
	0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x2e,
	0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x33, 0x0a, 0x09,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x77, 0x64, 0x61, 0x74,
	0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72,
	0x6d, 0x12, 0x29, 0x0a, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x77, 0x64, 0x61, 0x74,
	0x61, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x03,
	0x69, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x03, 0x69, 0x6e, 0x73, 0x12,
	0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x69, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x69, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x09,
	0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x77, 0x64, 0x61, 0x74,
	0x61, 0x2e, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x09, 0x65, 0x6d, 0x62,
	0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x1a, 0x67, 0x0a, 0x09, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x3a, 0x14, 0xba, 0x47, 0x11, 0xba, 0x01,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0xba, 0x01, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a,
	0x78, 0x0a, 0x07, 0x50, 0x43, 0x44, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x76, 0x69, 0x65, 0x77, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x09, 0x76, 0x69,
	0x65, 0x77, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x73, 0x65, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x01, 0x52, 0x04, 0x70, 0x6f, 0x73, 0x65, 0x3a, 0x0c, 0xba, 0x47, 0x09,
	0xba, 0x01, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x1a, 0x64, 0x0a, 0x04, 0x4d, 0x65, 0x74,
	0x61, 0x12, 0x30, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x77, 0x64, 0x61,
	0x74, 0x61, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x05, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x03, 0x70, 0x63, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x77, 0x64, 0x61,
	0x74, 0x61, 0x2e, 0x50, 0x43, 0x44, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x03, 0x70, 0x63, 0x64, 0x1a,
	0x3a, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x22, 0x32, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12,
	0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00,
	0x12, 0x09, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x10, 0x02, 0x1a, 0x5e, 0x0a, 0x06, 0x46,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x22, 0x54, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a,
	0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x08,
	0x0a, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x70, 0x6e, 0x67, 0x10,
	0x02, 0x12, 0x07, 0x0a, 0x03, 0x6a, 0x70, 0x67, 0x10, 0x03, 0x12, 0x07, 0x0a, 0x03, 0x70, 0x63,
	0x64, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x10,
	0x05, 0x12, 0x08, 0x0a, 0x04, 0x77, 0x65, 0x62, 0x70, 0x10, 0x06, 0x1a, 0x1d, 0x0a, 0x09, 0x45,
	0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x3a, 0x51, 0xba, 0x47, 0x4e, 0xba,
	0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0xba, 0x01,
	0x04, 0x74, 0x79, 0x70, 0x65, 0xba, 0x01, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0xba, 0x01,
	0x03, 0x75, 0x72, 0x6c, 0xba, 0x01, 0x04, 0x73, 0x69, 0x7a, 0x65, 0xba, 0x01, 0x06, 0x73, 0x68,
	0x61, 0x32, 0x35, 0x36, 0xba, 0x01, 0x04, 0x6d, 0x65, 0x74, 0x61, 0xba, 0x01, 0x03, 0x69, 0x6e,
	0x73, 0xba, 0x01, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0x49, 0x0a,
	0x09, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x03, 0x28, 0x01, 0x52, 0x06, 0x6f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f, 0x77, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x01, 0x52, 0x06, 0x74, 0x6f, 0x77, 0x61, 0x72, 0x64, 0x3a, 0x0c, 0xba, 0x47, 0x09, 0xba,
	0x01, 0x06, 0x74, 0x6f, 0x77, 0x61, 0x72, 0x64, 0x22, 0x50, 0x0a, 0x0d, 0x41, 0x74, 0x74, 0x72,
	0x41, 0x6e, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x3a, 0x13, 0xba, 0x47, 0x10, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0xba, 0x01, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0xac, 0x07, 0x0a, 0x06, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x61, 0x6e, 0x69, 0x73, 0x68, 0x5f, 0x6c, 0x61, 0x74, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x76, 0x61, 0x6e, 0x69, 0x73, 0x68, 0x4c,
	0x61, 0x74, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e, 0x64,
	0x52, 0x08, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x3d, 0x0a, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x1a, 0xfc, 0x02, 0x0a, 0x06, 0x57, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x12, 0x38, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x01, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x2a, 0x0a, 0x04, 0x67, 0x61, 0x70, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x2e, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x52, 0x04, 0x67, 0x61, 0x70, 0x73, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x72, 0x69, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x69,
	0x12, 0x2c, 0x0a, 0x07, 0x66, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x66, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x43, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0c, 0x73,
	0x65, 0x67, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x73, 0x65, 0x67, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12, 0x43, 0x0a,
	0x09, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x4c, 0x69, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x6e,
	0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x6e, 0x65, 0x73, 0x3a, 0x11, 0xba, 0x47, 0x0e, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0xba, 0x01, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x85, 0x01, 0x0a, 0x05, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x06, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x52, 0x06,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x12, 0x2c, 0x0a, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x74, 0x74, 0x72, 0x41, 0x6e, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x05, 0x61,
	0x74, 0x74, 0x72, 0x73, 0x3a, 0x0a, 0xba, 0x47, 0x07, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x1a, 0x2d, 0x0a, 0x08, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x61, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x70, 0x61, 0x72,
	0x74, 0x73, 0x3a, 0x0b, 0xba, 0x47, 0x08, 0xba, 0x01, 0x05, 0x70, 0x61, 0x72, 0x74, 0x73, 0x1a,
	0x5e, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x54, 0x0a, 0x04, 0x45, 0x6e, 0x75,
	0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x10, 0x01, 0x12, 0x0c,
	0x0a, 0x08, 0x70, 0x72, 0x65, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x03, 0x12,
	0x0e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x04, 0x3a,
	0x12, 0xba, 0x47, 0x0f, 0xba, 0x01, 0x04, 0x75, 0x75, 0x69, 0x64, 0xba, 0x01, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x4a, 0x04, 0x08, 0x05, 0x10, 0x06, 0x22, 0x91, 0x03, 0x0a, 0x0b, 0x52, 0x61,
	0x77, 0x64, 0x61, 0x74, 0x61, 0x41, 0x6e, 0x6e, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a,
	0x07, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52,
	0x07, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x2c, 0x0a, 0x05, 0x61, 0x74, 0x74, 0x72,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x41, 0x6e, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52,
	0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x12, 0x39, 0x0a, 0x0c, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x69, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x69, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c,
	0x12, 0x39, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x77,
	0x64, 0x61, 0x74, 0x61, 0x41, 0x6e, 0x6e, 0x6f, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x52, 0x0a, 0x08, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75,
	0x6d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x3a,
	0x1c, 0xba, 0x47, 0x19, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x07, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0xba, 0x01, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x22, 0x86, 0x02,
	0x0a, 0x0c, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35,
	0x0a, 0x07, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x07, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x65, 0x73, 0x12, 0x2b, 0x0a, 0x03, 0x72, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x4c, 0x45, 0x52, 0x03, 0x72,
	0x6c, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6c, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x1a, 0x3c, 0x0a,
	0x05, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x3a, 0x0f, 0xba, 0x47, 0x0c, 0xba,
	0x01, 0x02, 0x69, 0x64, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x1a, 0x39, 0x0a, 0x03, 0x52,
	0x4c, 0x45, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x75, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x04, 0x72, 0x75, 0x6e, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x76, 0x61, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x05, 0x52, 0x04, 0x76, 0x61, 0x6c, 0x73, 0x3a, 0x0a, 0xba, 0x47, 0x07, 0xba,
	0x01, 0x04, 0x72, 0x75, 0x6e, 0x73, 0x22, 0xd8, 0x04, 0x0a, 0x0b, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x39, 0x0a, 0x0d, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x6e, 0x6f,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x0c, 0x72,
	0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x12, 0x2c, 0x0a, 0x05, 0x61,
	0x74, 0x74, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x41, 0x6e, 0x64, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x52, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x6e, 0x73,
	0x5f, 0x63, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x43,
	0x6e, 0x74, 0x12, 0x39, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3f, 0x0a,
	0x0e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x33, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x33, 0x64, 0x52, 0x0e,
	0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x33, 0x64, 0x1a, 0xea,
	0x01, 0x0a, 0x08, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x44, 0x0a, 0x09, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x41, 0x6e, 0x6e, 0x6f, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72,
	0x73, 0x1a, 0x97, 0x01, 0x0a, 0x08, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x12, 0x24,
	0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04,
	0x75, 0x73, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x5f, 0x61,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x08, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x68,
	0x61, 0x73, 0x65, 0x3a, 0x16, 0xba, 0x47, 0x13, 0xba, 0x01, 0x04, 0x75, 0x73, 0x65, 0x72, 0xba,
	0x01, 0x09, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x5f, 0x61, 0x74, 0x3a, 0x34, 0xba, 0x47, 0x31,
	0xba, 0x01, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba,
	0x01, 0x07, 0x69, 0x6e, 0x73, 0x5f, 0x63, 0x6e, 0x74, 0xba, 0x01, 0x05, 0x61, 0x74, 0x74, 0x72,
	0x73, 0xba, 0x01, 0x0d, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x6e, 0x6f,
	0x73, 0x22, 0x87, 0x01, 0x0a, 0x0e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x33, 0x64, 0x12, 0x35, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x33, 0x64, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3e, 0x0a, 0x09, 0x73,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x33, 0x64, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x22, 0x53, 0x0a, 0x14, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x33, 0x64, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12,
	0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x05, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64,
	0x22, 0xaa, 0x01, 0x0a, 0x17, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x33, 0x64, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x6e, 0x75, 0x6d, 0x12, 0x3d, 0x0a, 0x09, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x33, 0x64, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x09, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x3a, 0x19, 0xba, 0x47, 0x16, 0xba, 0x01, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0x64, 0x0a,
	0x16, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x33, 0x64, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x3a, 0x17, 0xba, 0x47, 0x14, 0xba,
	0x01, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0xba, 0x01, 0x03,
	0x6e, 0x75, 0x6d, 0x22, 0x76, 0x0a, 0x0b, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x6e, 0x6e,
	0x6f, 0x73, 0x12, 0x39, 0x0a, 0x0d, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6e,
	0x6e, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x52,
	0x0c, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x12, 0x17, 0x0a,
	0x07, 0x6a, 0x6f, 0x62, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6a, 0x6f, 0x62, 0x55, 0x69, 0x64, 0x3a, 0x13, 0xba, 0x47, 0x10, 0xba, 0x01, 0x0d, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x42, 0x3e, 0x0a, 0x07, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x31, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62,
	0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b,
	0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e, 0x6e, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_anno_v1_elemanno_proto_rawDescOnce sync.Once
	file_anno_v1_elemanno_proto_rawDescData = file_anno_v1_elemanno_proto_rawDesc
)

func file_anno_v1_elemanno_proto_rawDescGZIP() []byte {
	file_anno_v1_elemanno_proto_rawDescOnce.Do(func() {
		file_anno_v1_elemanno_proto_rawDescData = protoimpl.X.CompressGZIP(file_anno_v1_elemanno_proto_rawDescData)
	})
	return file_anno_v1_elemanno_proto_rawDescData
}

var file_anno_v1_elemanno_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_anno_v1_elemanno_proto_msgTypes = make([]protoimpl.MessageInfo, 32)
var file_anno_v1_elemanno_proto_goTypes = []interface{}{
	(Element_Type_Enum)(0),                // 0: anno.v1.Element.Type.Enum
	(RawdataParam_Type_Enum)(0),           // 1: anno.v1.RawdataParam.Type.Enum
	(RawdataParam_DistortionType_Enum)(0), // 2: anno.v1.RawdataParam.DistortionType.Enum
	(Rawdata_Type_Enum)(0),                // 3: anno.v1.Rawdata.Type.Enum
	(Rawdata_Format_Enum)(0),              // 4: anno.v1.Rawdata.Format.Enum
	(Object_Source_Enum)(0),               // 5: anno.v1.Object.Source.Enum
	(*Element)(nil),                       // 6: anno.v1.Element
	(*RawdataParam)(nil),                  // 7: anno.v1.RawdataParam
	(*Rawdata)(nil),                       // 8: anno.v1.Rawdata
	(*Direction)(nil),                     // 9: anno.v1.Direction
	(*AttrAndValues)(nil),                 // 10: anno.v1.AttrAndValues
	(*Object)(nil),                        // 11: anno.v1.Object
	(*RawdataAnno)(nil),                   // 12: anno.v1.RawdataAnno
	(*Segmentation)(nil),                  // 13: anno.v1.Segmentation
	(*ElementAnno)(nil),                   // 14: anno.v1.ElementAnno
	(*Segmentation3D)(nil),                // 15: anno.v1.Segmentation3d
	(*Segmentation3DResult)(nil),          // 16: anno.v1.Segmentation3dResult
	(*Segmentation3DStatistic)(nil),       // 17: anno.v1.Segmentation3dStatistic
	(*Segmentation3DInstance)(nil),        // 18: anno.v1.Segmentation3dInstance
	(*ExportAnnos)(nil),                   // 19: anno.v1.ExportAnnos
	(*Element_Type)(nil),                  // 20: anno.v1.Element.Type
	(*RawdataParam_Type)(nil),             // 21: anno.v1.RawdataParam.Type
	(*RawdataParam_DistortionType)(nil),   // 22: anno.v1.RawdataParam.DistortionType
	(*Rawdata_ImageMeta)(nil),             // 23: anno.v1.Rawdata.ImageMeta
	(*Rawdata_PCDMeta)(nil),               // 24: anno.v1.Rawdata.PCDMeta
	(*Rawdata_Meta)(nil),                  // 25: anno.v1.Rawdata.Meta
	(*Rawdata_Type)(nil),                  // 26: anno.v1.Rawdata.Type
	(*Rawdata_Format)(nil),                // 27: anno.v1.Rawdata.Format
	(*Rawdata_Embedding)(nil),             // 28: anno.v1.Rawdata.Embedding
	(*Object_Widget)(nil),                 // 29: anno.v1.Object.Widget
	(*Object_Label)(nil),                  // 30: anno.v1.Object.Label
	(*Object_Compound)(nil),               // 31: anno.v1.Object.Compound
	(*Object_Source)(nil),                 // 32: anno.v1.Object.Source
	(*RawdataAnno_Metadata)(nil),          // 33: anno.v1.RawdataAnno.Metadata
	(*Segmentation_Class)(nil),            // 34: anno.v1.Segmentation.Class
	(*Segmentation_RLE)(nil),              // 35: anno.v1.Segmentation.RLE
	(*ElementAnno_Metadata)(nil),          // 36: anno.v1.ElementAnno.Metadata
	(*ElementAnno_Metadata_Executor)(nil), // 37: anno.v1.ElementAnno.Metadata.Executor
	(WidgetName_Enum)(0),                  // 38: anno.v1.WidgetName.Enum
	(WidgetLineType_Enum)(0),              // 39: anno.v1.WidgetLineType.Enum
	(*v1.BaseUser)(nil),                   // 40: iam.v1.BaseUser
	(*timestamppb.Timestamp)(nil),         // 41: google.protobuf.Timestamp
}
var file_anno_v1_elemanno_proto_depIdxs = []int32{
	0,  // 0: anno.v1.Element.type:type_name -> anno.v1.Element.Type.Enum
	8,  // 1: anno.v1.Element.datas:type_name -> anno.v1.Rawdata
	14, // 2: anno.v1.Element.anno:type_name -> anno.v1.ElementAnno
	1,  // 3: anno.v1.RawdataParam.type:type_name -> anno.v1.RawdataParam.Type.Enum
	3,  // 4: anno.v1.Rawdata.type:type_name -> anno.v1.Rawdata.Type.Enum
	4,  // 5: anno.v1.Rawdata.format:type_name -> anno.v1.Rawdata.Format.Enum
	7,  // 6: anno.v1.Rawdata.transform:type_name -> anno.v1.RawdataParam
	25, // 7: anno.v1.Rawdata.meta:type_name -> anno.v1.Rawdata.Meta
	11, // 8: anno.v1.Rawdata.ins:type_name -> anno.v1.Object
	28, // 9: anno.v1.Rawdata.embedding:type_name -> anno.v1.Rawdata.Embedding
	30, // 10: anno.v1.Object.label:type_name -> anno.v1.Object.Label
	31, // 11: anno.v1.Object.compound:type_name -> anno.v1.Object.Compound
	5,  // 12: anno.v1.Object.source:type_name -> anno.v1.Object.Source.Enum
	11, // 13: anno.v1.RawdataAnno.objects:type_name -> anno.v1.Object
	10, // 14: anno.v1.RawdataAnno.attrs:type_name -> anno.v1.AttrAndValues
	13, // 15: anno.v1.RawdataAnno.segmentation:type_name -> anno.v1.Segmentation
	33, // 16: anno.v1.RawdataAnno.metadata:type_name -> anno.v1.RawdataAnno.Metadata
	34, // 17: anno.v1.Segmentation.classes:type_name -> anno.v1.Segmentation.Class
	35, // 18: anno.v1.Segmentation.rle:type_name -> anno.v1.Segmentation.RLE
	12, // 19: anno.v1.ElementAnno.rawdata_annos:type_name -> anno.v1.RawdataAnno
	10, // 20: anno.v1.ElementAnno.attrs:type_name -> anno.v1.AttrAndValues
	36, // 21: anno.v1.ElementAnno.metadata:type_name -> anno.v1.ElementAnno.Metadata
	15, // 22: anno.v1.ElementAnno.segmentation3d:type_name -> anno.v1.Segmentation3d
	16, // 23: anno.v1.Segmentation3d.result:type_name -> anno.v1.Segmentation3dResult
	17, // 24: anno.v1.Segmentation3d.statistic:type_name -> anno.v1.Segmentation3dStatistic
	18, // 25: anno.v1.Segmentation3dStatistic.instances:type_name -> anno.v1.Segmentation3dInstance
	14, // 26: anno.v1.ExportAnnos.element_annos:type_name -> anno.v1.ElementAnno
	7,  // 27: anno.v1.Rawdata.PCDMeta.viewpoint:type_name -> anno.v1.RawdataParam
	23, // 28: anno.v1.Rawdata.Meta.image:type_name -> anno.v1.Rawdata.ImageMeta
	24, // 29: anno.v1.Rawdata.Meta.pcd:type_name -> anno.v1.Rawdata.PCDMeta
	38, // 30: anno.v1.Object.Widget.name:type_name -> anno.v1.WidgetName.Enum
	29, // 31: anno.v1.Object.Widget.gaps:type_name -> anno.v1.Object.Widget
	9,  // 32: anno.v1.Object.Widget.forward:type_name -> anno.v1.Direction
	39, // 33: anno.v1.Object.Widget.line_type:type_name -> anno.v1.WidgetLineType.Enum
	29, // 34: anno.v1.Object.Label.widget:type_name -> anno.v1.Object.Widget
	10, // 35: anno.v1.Object.Label.attrs:type_name -> anno.v1.AttrAndValues
	3,  // 36: anno.v1.RawdataAnno.Metadata.type:type_name -> anno.v1.Rawdata.Type.Enum
	37, // 37: anno.v1.ElementAnno.Metadata.executors:type_name -> anno.v1.ElementAnno.Metadata.Executor
	40, // 38: anno.v1.ElementAnno.Metadata.Executor.user:type_name -> iam.v1.BaseUser
	41, // 39: anno.v1.ElementAnno.Metadata.Executor.submit_at:type_name -> google.protobuf.Timestamp
	40, // [40:40] is the sub-list for method output_type
	40, // [40:40] is the sub-list for method input_type
	40, // [40:40] is the sub-list for extension type_name
	40, // [40:40] is the sub-list for extension extendee
	0,  // [0:40] is the sub-list for field type_name
}

func init() { file_anno_v1_elemanno_proto_init() }
func file_anno_v1_elemanno_proto_init() {
	if File_anno_v1_elemanno_proto != nil {
		return
	}
	file_anno_v1_widget_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_anno_v1_elemanno_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Element); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RawdataParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rawdata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Direction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttrAndValues); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Object); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RawdataAnno); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Segmentation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ElementAnno); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Segmentation3D); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Segmentation3DResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Segmentation3DStatistic); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Segmentation3DInstance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportAnnos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Element_Type); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RawdataParam_Type); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RawdataParam_DistortionType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rawdata_ImageMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rawdata_PCDMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rawdata_Meta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rawdata_Type); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rawdata_Format); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rawdata_Embedding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Object_Widget); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Object_Label); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Object_Compound); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Object_Source); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RawdataAnno_Metadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Segmentation_Class); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Segmentation_RLE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ElementAnno_Metadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_elemanno_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ElementAnno_Metadata_Executor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anno_v1_elemanno_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   32,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_anno_v1_elemanno_proto_goTypes,
		DependencyIndexes: file_anno_v1_elemanno_proto_depIdxs,
		EnumInfos:         file_anno_v1_elemanno_proto_enumTypes,
		MessageInfos:      file_anno_v1_elemanno_proto_msgTypes,
	}.Build()
	File_anno_v1_elemanno_proto = out.File
	file_anno_v1_elemanno_proto_rawDesc = nil
	file_anno_v1_elemanno_proto_goTypes = nil
	file_anno_v1_elemanno_proto_depIdxs = nil
}
