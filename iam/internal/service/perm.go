// source: gitlab.rp.konvery.work/platform/apis/iam/v1/role.proto
package service

import (
	"context"

	"iam/internal/biz"

	"gitlab.rp.konvery.work/platform/apis/iam/v1"
	"gitlab.rp.konvery.work/platform/pkg/errors"

	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/emptypb"
)

type PermsService struct {
	iam.UnimplementedPermsServer
	bz *biz.PermsBiz
}

func NewPermsService(bz *biz.PermsBiz) *PermsService {
	return &PermsService{bz: bz}
}

func (o *PermsService) EditPerms(ctx context.Context, req *iam.EditPermsRequest) (_ *emptypb.Empty, err error) {
	if !biz.UserFromCtx(ctx).IsPrivileged() {
		return nil, errors.NewErrForbidden()
	}

	switch req.Action {
	case iam.EditAction_add:
		_, err = o.bz.Add(ctx, req.Perms)
	case iam.EditAction_delete:
		err = o.bz.Delete(ctx, req.Perms)
	}
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, err
}

func (o *PermsService) ListPermClass(ctx context.Context, _ *emptypb.Empty) (*iam.ListPermClassReply, error) {
	classes, err := o.bz.ListPermClass(ctx)
	if err != nil {
		return nil, err
	}
	return &iam.ListPermClassReply{Classes: classes}, nil
}

func (o *PermsService) ListPerm(ctx context.Context, req *iam.ListPermRequest) (*iam.ListPermReply, error) {
	pager := biz.Pager{
		Pagesz:    int(req.Pagesz),
		PageToken: biz.PageToken(req.PageToken),
	}
	filter := &biz.PermListFilter{
		Class:       req.Class,
		NamePattern: req.NamePattern,
	}

	// var cnt int
	// if pager.Page == 0 {
	// 	var err error
	// 	cnt, err = o.bz.Count(ctx, filter)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// }
	datas, nextPageToken, err := o.bz.List(ctx, filter, pager)
	if err != nil {
		return nil, err
	}
	return &iam.ListPermReply{
		NextPageToken: nextPageToken.String(),
		Perms: lo.Map(datas, func(v *biz.Perm, _ int) string {
			return v.Name
		})}, nil
}
