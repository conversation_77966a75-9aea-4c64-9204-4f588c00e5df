大卓送标、回标接口对接V3.820250314​
注意事项：
• 内外参都是Clip维度，仅需下载一份数据处理即可，无需重复下载​
同理，其他Clip维度的数据文件，也仅需按ClipID下载第一行，后续可能其他行对应数据会置为
空
• 多线程生成txt文件，能保证同一个clipId的frames相对顺序，但不能保证同一个clips的
frames是在txt文件是邻近行，不同clip的frames会交叉出现。​
• 送标接口文件内容可能为ZIP也可能为TXT，请兼容两个文件形式​
• lidar_slam后续版本config.json包含lidar2imu，详见文档​ LidarSLAM的pose副本，优先取
Config的lidar2imu进行拼接点云，次选送标数据的pose和extrinsics。​
• 请使用JSONSchemaDraft4进行校验​
• 3DLane回标模式由单Clip切换为Clip+Frame模式，回标key需由3d_lane_annotated_info切
换为3d_lane_clip_annotated_info和3d_lane_frame_annotated_info。历史数据不拆帧也需要
将Clip维度JSON外层Key切换3d_lane_clip_annotated_info。​
• 注意：从3DLane切换为Parking3DLane的任务，除了回标JSONKey需要调整外，回标时需
将annotateType从2调整为31​
一、版本历史
版本​ 时间​ 作者​ 内容​
v1.x​ 2024-
01-04​
v2.0​ 2024-
01-22​
新增静态元素标注（BevLane）​
v2.1​ 2024-
05-06​
新增KeyPoints标注项目​
新增3D鱼眼城市物体检测​
新增3D鱼眼高速物体检测​
v2.2​ 2024-
08-23​
新增泊车项目​
v2.3​ 泊车项目Header新增isKeyFrame​
2024-
08-26​
v2.4​ 2024-
09-12​
3DLane新增Camera6-10及对应的timestamp、stitch_config​
v2.5​ 2024-
10-08​
新增GOP静态目标物检测​
v2.6​ 2024-
10-12​
AUTO4DLane调整​
v2.7​ 2024-
10-15​
新增三个无需去畸变2D项目，相机遮挡、环境识别、泊车FSline​
新增四个去畸变项目：红绿灯（关键帧or连续帧）、路牌、光斑​
v2.8​ 2024-
10-16​
回标结果文档调整，区分三种回标模式（CLIP、FRAME、CLIP+FRAME）​
v2.9​ 2024-
10-23​
增加"关键帧"筛选逻辑​
v3.0​ 2024-
10-31​
增加获取JSONSchemaAPI​
v3.1​ 2024-
12-23​
GOP新增combineLidar（mc_lidar列保留，对应值可能为空）​
v3.2​ 2025-
01-09​
新增标注项目23DObjectComplement​
v3.3​ 2025-
01-14​
新增标注项目Parking3DLane​
v3.4​ 2025-
01-21​
ParkingMovableObjectDetection调整，新增preLabelResult、
extrinsics去除lidar2imu​
调整未上线，请兼容两种形式​
v3.5​ 2025-
02-25​
City4DLane调整。​
v3.6​ 2025-
02-26​
ParkingFreeSpace（ParkingOCC）送标数据增加两部分PMODGTJSON
和GOPGTJSON​
v3.7​ 2025-
02-28​
• 新增行车OCC（3DDrivingSegmentation）​
• 以下项目追加mc_clip_sideview_lidar（Clip维度补盲雷达拼接点云，
一个Clip一个）​
◦ 3DLane​
◦ Parking3DLane​
◦ parking_surround_space_detection​
◦ parking_road_sign_mark​
◦ driving_free_space​
◦ GOP静态目标物检测​
◦ CITY_4D_LANE​
• 以下项目追加mc_frame_sideview_lidar（Frame维度补盲雷达拼接
点云，一帧一个）​
注意兼容。​
v3.8​ 2025-
03-14​
City4DLane增加优化后外参回传方式​
v3.9​ 2025-
05-07​
新增GOP补标项目：gop_det_complement​
二、概述
API对接包含两方：​
1. 标注供应商
a. 提供送标接口
b. 提供获取标注结果接口（部分数据完成标注能够拉取结果，能够持续拉取）​
2. 大卓标注需求方
a. 用供应商提供的送标接口发送送标数据
b. 用供应商提供的获取标注结果接口获取标注结果集（根据送标接口返回的id或者其他参数能拉
取对应的送标批次结果集）​
说明：
大卓需求方会要求送标文件格式及标注结果文件格式、送标接口及获取标注结果接口。
三、送标与结果获取API​
1、送标API​
1.1、API​
1.1.1、请求方式及Header​
URL​ https://host:port/xxx​
method​ POST（表单请求）​
RequestHeader​ "Content-Type":"multipart/form-data"​
"Authorization":"ZdriveAI<accessKey>:<signature>"//签名计算⽅法⻅下⽂​
ResponseHeader​ "Content-Type":"application/json"​
1.1.2、入参​
参数名​ 类型​ 取值​
name​ 字符串​ 标注任务名称(2d_lane\等)（相同annotateType固定值）​
annotateT
ype​
整数​ 取值为：1-9​
值为1代表送标任务类型为2DLane-每帧两个前视摄像头-连续帧​
值为2代表送标任务类型为3DLane-一个CLip一个Lidar-单ClipPcd​
值为3代表送标任务类型为23DObject（23D高速物体检测）-每帧一个
Lidar7个相机-连续帧(一个Clip40帧）​
值为4代表送标任务类型为3DBox-每帧一个Lidar7个相机-连续帧（每个
Clip>=3帧）​
值为5代表送标任务类型为2D红绿灯关键帧-每帧一个前摄-单帧​
值为6代表送标任务类型为2D红绿灯连续帧-每帧一个前摄-连续帧​
值为7代表送标任务类型为23DCityObjectDetection（23D城区物体检测）
-每帧一个Lidar7个相机-连续帧​
值为8代表送标任务类型M23DCityObjectDetection-每帧一个Lidar7个
相机-连续帧​
值为9代表送标任务类型Only3DCityObjectDetection（城区物体检测仅
3D）-每帧一个Lidar7个相机-连续帧（回标见*******）​
值为10代表送标任务类型23DObjectDetection（高速物体检测仅3D）-每
帧一个Lidar7个相机-连续帧​
（回标见*******）​
值为11代表送标任务类型BevLane（Bev车道线标注）-每帧2个Camera+
Clip一个大图（同一个Clip只需要下载一个大图即可）​
标注结果见2.2.3Bev车道线标注​
送标文件表头见1.2.7​
12、值为12代表送标任务类型CameraBevLaneCombine（基于图片的
BevLane车道线标注）（一个Clip两个大图）​
13、值为13代表送标任务类型CameraSLAMLaneCombine（基于图片的
BevLane车道线标注）（一个Clip1个大图），见（1.2.10）​
14、值为14代表送标任务类型KeyPoints（一个clip40帧，每帧camera0和1
两个相机；），送标文件header见（1.2.11），回标见********​
15、值为15代表送标任务类型3d_city_object_detection_with_fish_eye
每帧一个Lidar7个相机-连续帧(一个Clip小于等于40帧）​
送标见1.2.12​
16、值为16代表送标任务类型
3d_highway_object_detection_with_fish_eye每帧一个Lidar7个相机-连
续帧(一个Clip小于等于40帧）​
送标见1.2.13​
17、值为17代表送标任务类型parking_movable_object_detection每帧一
个lidar和6个Camera​
18、值为18代表送标任务类型auto_4d_lane每个Clip仅有一个脱敏的
Pose​
19、值为19代表送标任务类型parking_surround_space_detection​
每个Clip仅有一个拼接后大点云及10帧的图片（每帧7相机）​
20、值为20代表送标任务类型parking_road_sign_mark​
每个Clip仅有一个拼接后大点云及10帧的图片（每帧7相机）​
21、值为21代表送标任务类型parking_free_space​
每个Clip仅有一个拼接后大点云及10帧的图片（每帧7相机）以及另外一个
预标注结果的json​
22、值为22代表送标任务类型driving_free_space​
23、值为23代表送标任务类型GOP静态目标物检测​
24->相机遮挡camera_2d_blockage1.2.21​
25->环境识别camera_2d_environment_identification1.2.22​
26->泊车FSlinecamera_2d_fsline1.2.23​
27->路牌camera_2d_traffic_sign​
28->光斑camera_2d_spot_detection
29->CITY_4D_LANE​
30->D23_OBJECT_COMPLEMENT​
31->PARKING_D3_LANE​
32->3DDrivingSegmentation​
33->GOPDETCOMPLEMENT​
dataType​ 字符串​ 与annotateType对应，表明该送标任务对应送标的数据类型​
取值为："fusion3d"or"image"or"pointcloud"​
"fusion3d"：每帧1个点云+7个相机的照⽚​
"image":每帧一张或多张图片​
"pointcloud":每个Clip一个多帧融合点云​
timestamp​ 整数​ 送标时间1697081971​
uploadFile​ 送标文件流（文
件的字节数组）​
具体解析见下​
annotateId​ 字符串​ 该送标任务的唯一ID​
markProje
ctId​
字符串​ 可选-不参与签名校验。​
sendLabel
ClipCount​
整数​ 送标的Clip数量-用于双方校对​
sendLabel
FrameCou
nt​
整数​ 送标的Frame数量-用于双方校对-默认值为0​
fileMd5​ 字符串​ 送标文件MD5（Base64编码）（当文件类型为ZIP，则为ZIP文件的MD5）​
customCo
nfig​
JSON字符串​ 自定义配置​
fileConten
tType​
字符串​ "ZIP"、"TXT"（非必填，默认情况下请以TXT处理）​
1.1.3、Signature计算方法​
StringToSign = "POST" + "\n" +
"annotateType=xxx" + "\n" +
"dataType=xxx" + "\n" +
"timestamp=xxx" + "\n" +
"annotateId=xxx" + "\n" +
"sendLabelClipCount=xxx" + "\n" +
"sendLabelFrameCount=xxx" + "\n" +
"fileMd5=xxx";
Signature=Base64( HMAC-SHA1( UTF-8-Encoding-Of(secretKey),
UTF-8-Encoding-Of(StringToSign)
1
2
3
4
5
6
7
8
9
10
11
12
));
13
描述：先拼装StringToSign，然后使⽤HMAC-SHA1计算secret_key和StringToSign的摘要，最后
对摘要进⾏base64编码即可得到最终的签名 ​
注意：拼接顺序不可变
1.1.4、请求及响应示例​
RequestBody​ "uploadFile":"送标文件流",​
"timestamp":1777777777(送标时间)​
"annotateType":1,​
"dataType":"fusion3d",​
"annotateId":"13213",​
"sendLabelClipCount":1,​
"sendLabelFrameCount":40,​
"fileMd5":"11111"​
ResponseBody​
{
"code":0,
"message":"操作成功",
"data": {
"annotateId": "13213"
}
}
1
2
3
4
5
6
7
1.2、送标文件流（TXT文本orZIP压缩文件）​
使⽤⽆BOM的UTF-8编码，包含⼀个表头⾏和若⼲数据⾏，⾏与⾏之间采⽤换⾏符('\n')分隔；
每⾏包含相等数量的列，列与列之间使⽤制表符'\t'分隔。​
每行代表一个Clip的一帧，每帧可能包含多个Camera或一个Lidar。例如，某次送标20个
Clips，800Frames，则会先按Clip的采集时间进行排序，遍历Clips时，再按Frames的采集时间排
序，构建送标文件行。
强调：本身送标文件内数据行的数据其实是有序的，升序排列，送标文件从上到下，采集时间从
老到新。当然也新增了timestamp列（LidarCollectTime），也可以按照这个进行升序排列。​
备注：线下试标只会直接提供原始数据。
建议处理方式：按ClipId对数据行进行分组，保留数据行之间的相对位置，数据行按采集时间升序。​
更新：
因送标数量较多的情况下，送标文件Txt会较大，会压缩为ZIP进行传输：​
Txt压缩后文件结构：​
示例ZIP文件：​
374_56_13_1_0_114721418811160
06312.zip
3.50KB
1.2.1、以23DObject融合（高速物体检测）为例​
参考文件：
​ ​
3_23d_object_15635989820497945113.txt
testlidar2imu.txt
表头：
clip frame params lidar camera0 camera1
camera2 camera3 camera4 camera5 camera6
37ee65f2a8e24aae90b2688d07e66770 ff5944283ccc11eeba870242ac110006
{"camera0":{"extrinsic":[[0.02063983,-0.99963073,0.0176848,0.032489],
[0.00155673,-0.01765644,-0.999844,-0.741099],
[0.99978488,0.02066389,0.00119164,-0.927762],[0,0,0,1]],"intrinsic":
[[1854.41,0,1888.36],[0,1853.57,1066.09],[0,0,1]],"distcoeff":
[[-0.287803,0.0871568,5.56998e-
7,0.000393049,-0.0122187]],"width":3840,"height":2160},"camera1":{"extrinsic":
[[-0.02453704,-0.99968552,0.00519043,-0.0282021],
[0.03767286,-0.00611296,-0.99927137,-0.684901],
[0.99898906,-0.02432348,0.03781077,-0.977548],[0,0,0,1]],"intrinsic":
1
2
[[7655.26,0,1932.64],[0,7667.3,688.026],[0,0,1]],"distcoeff":
[[-0.486463,7.22143,0.0114237,0.00437047,-65.6133]],"width":3840,"height":2160
},"camera2":{"extrinsic":[[0.75095676,-0.66025662,0.0112361,-0.117116],
[0.01666858,0.0019432,-0.999859,-1.13147],
[0.66014212,0.75103746,0.0124649,-1.44742],[0,0,0,1]],"intrinsic":
[[1106.21,0,955.44],[0,1103.39,640.263],[0,0,1]],"distcoeff":
[[-0.337859,0.133493,-0.0000754106,0.000846691,-0.0275559]],"width":1920,"heig
ht":1280},"camera3":{"extrinsic":[[0.64829349,0.76117867,0.0151819,-1.56241],
[0.00472666,0.01584187,-0.999819,-1.32022],
[-0.76133672,0.64827177,0.00664364,0.23768],[0,0,0,1]],"intrinsic":
[[1115.58,0,980.21],[0,1114.45,640.876],[0,0,1]],"distcoeff":
[[-0.346379,0.148701,0.000260434,-0.00030326,-0.0358272]],"width":1920,"height
":1280},"camera4":{"extrinsic":[[-0.77981367,-0.62572839,-0.0183264,0.134654],
[0.00671711,0.02096919,-0.999716,-1.1424],
[0.62590853,-0.77978246,-0.0120696,-1.2894],[0,0,0,1]],"intrinsic":
[[1113.39,0,958.311],[0,1114.95,644.528],[0,0,1]],"distcoeff":
[[-0.341939,0.138198,0.000127178,0.000584062,-0.0297029]],"width":1920,"height
":1280},"camera5":{"extrinsic":
[[-0.661337,0.750084,0.0027526999999999986,1.5361899999999997],
[-0.021316699999999997,-0.015125999999999995,-0.9996590000000001,-1.3362600000
000002],[-0.749786,-0.6611699999999999,0.0259927,0.2607279999999998],
[0,0,0,1]],"intrinsic":[[1106.21,0,955.44],[0,1107.39,640.263],
[0,0,1]],"distcoeff":
[[-0.337859,0.133493,-0.0000754106,0.000846691,-0.0275559]],"width":1920,"heig
ht":1280},"camera6":{"extrinsic":[[0.01164609,0.99981744,0.0151853,0.0171096],
[-0.01363762,0.01534369,-0.999789,-1.31744],
[-0.99984027,0.01143643,0.0138138,-2.51142],[0,0,0,1]],"intrinsic":
[[1919.04,0,981.044],[0,1918.5,639.135],[0,0,1]],"distcoeff":
[[-0.491649,0.263609,-0.0002304,-0.00114529,-0.113226]],"width":1920,"height":
1280}} https://1*************/dataManager/frameLidar/download?
frame_lidar_id=aaaf39f63ccb11ee978d0242ac110006 http:/sssss/download?
frame_camera_id=aa9b59363ccb11ee978d0242ac110006 http:/sssss/download?
frame_camera_id=aa9771683ccb11ee978d0242ac110006 http:/sssss/download?
frame_camera_id=aa9470f83ccb11ee978d0242ac110006 http:/sssss/download?
frame_camera_id=aa975b563ccb11ee978d0242ac110006 http:/sssss/download?
frame_camera_id=aa94529e3ccb11ee978d0242ac110006 http:/sssss/download?
自动换行结果：
clip frame params lidar camera0 camera1
camera2 camera3 camera4 camera5 camera6
37ee65f2a8e24aae90b2688d07e66770 ff5944283ccc11eeba870242ac110006
{"camera0":{"extrinsic":[[0.02063983,-0.99963073,0.0176848,0.032489],
[0.00155673,-0.01765644,-0.999844,-0.741099],
[0.99978488,0.02066389,0.00119164,-0.927762],[0,0,0,1]],"intrinsic":
1
2
[[1854.41,0,1888.36],[0,1853.57,1066.09],[0,0,1]],"distcoeff":
[[-0.287803,0.0871568,5.56998e-
7,0.000393049,-0.0122187]],"width":3840,"height":2160},"camera1":{"extrinsic":
[[-0.02453704,-0.99968552,0.00519043,-0.0282021],
[0.03767286,-0.00611296,-0.99927137,-0.684901],
[0.99898906,-0.02432348,0.03781077,-0.977548],[0,0,0,1]],"intrinsic":
[[7655.26,0,1932.64],[0,7667.3,688.026],[0,0,1]],"distcoeff":
[[-0.486463,7.22143,0.0114237,0.00437047,-65.6133]],"width":3840,"height":2160
},"camera2":{"extrinsic":[[0.75095676,-0.66025662,0.0112361,-0.117116],
[0.01666858,0.0019432,-0.999859,-1.13147],
[0.66014212,0.75103746,0.0124649,-1.44742],[0,0,0,1]],"intrinsic":
[[1106.21,0,955.44],[0,1103.39,640.263],[0,0,1]],"distcoeff":
[[-0.337859,0.133493,-0.0000754106,0.000846691,-0.0275559]],"width":1920,"heig
ht":1280},"camera3":{"extrinsic":[[0.64829349,0.76117867,0.0151819,-1.56241],
[0.00472666,0.01584187,-0.999819,-1.32022],
[-0.76133672,0.64827177,0.00664364,0.23768],[0,0,0,1]],"intrinsic":
[[1115.58,0,980.21],[0,1114.45,640.876],[0,0,1]],"distcoeff":
[[-0.346379,0.148701,0.000260434,-0.00030326,-0.0358272]],"width":1920,"height
":1280},"camera4":{"extrinsic":[[-0.77981367,-0.62572839,-0.0183264,0.134654],
[0.00671711,0.02096919,-0.999716,-1.1424],
[0.62590853,-0.77978246,-0.0120696,-1.2894],[0,0,0,1]],"intrinsic":
[[1113.39,0,958.311],[0,1114.95,644.528],[0,0,1]],"distcoeff":
[[-0.341939,0.138198,0.000127178,0.000584062,-0.0297029]],"width":1920,"height
":1280},"camera5":{"extrinsic":
[[-0.661337,0.750084,0.0027526999999999986,1.5361899999999997],
[-0.021316699999999997,-0.015125999999999995,-0.9996590000000001,-1.3362600000
000002],[-0.749786,-0.6611699999999999,0.0259927,0.2607279999999998],
[0,0,0,1]],"intrinsic":[[1106.21,0,955.44],[0,1107.39,640.263],
[0,0,1]],"distcoeff":
[[-0.337859,0.133493,-0.0000754106,0.000846691,-0.0275559]],"width":1920,"heig
ht":1280},"camera6":{"extrinsic":[[0.01164609,0.99981744,0.0151853,0.0171096],
[-0.01363762,0.01534369,-0.999789,-1.31744],
[-0.99984027,0.01143643,0.0138138,-2.51142],[0,0,0,1]],"intrinsic":
[[1919.04,0,981.044],[0,1918.5,639.135],[0,0,1]],"distcoeff":
[[-0.491649,0.263609,-0.0002304,-0.00114529,-0.113226]],"width":1920,"height":
1280}} https://1*************/dataManager/frameLidar/download?
frame_lidar_id=aaaf39f63ccb11ee978d0242ac110006 http:/sssss/download?
frame_camera_id=aa9b59363ccb11ee978d0242ac110006 http:/sssss/download?
frame_camera_id=aa9771683ccb11ee978d0242ac110006 http:/sssss/download?
frame_camera_id=aa9470f83ccb11ee978d0242ac110006 http:/sssss/download?
frame_camera_id=aa975b563ccb11ee978d0242ac110006 http:/sssss/download?
frame_camera_id=aa94529e3ccb11ee978d0242ac110006 http:/sssss/download?
1.2.2、表头解释​
新：
clip frame params lidar camera0 camera1
camera2 camera3 camera4 camera5 camera6 pose
timestamp lidar2imu preLabelResult
1
表头解释：
• 激光雷达lidar​
◦ "lidar"-激光雷达​
◦ 对应送标文件行内容是：lidar的下载链接（需要鉴权）​
◦ 其他：
▪ lidar0:mainlidar​
▪ lidar1:leftlidar​
▪ lidar2:frontlidar​
▪ lidar3:rightlidar​
▪ lidar4:rearlidar​
• 摄像头Camera​
◦ "camera0"-frontwide摄像头​
◦ "camera1"-frontmain摄像头​
◦ "camera2"-leftfront摄像头​
◦ "camera3"-leftrear摄像头​
◦ "camera4"-rightfront摄像头
◦ "camera5"-rightrear摄像头​
◦ "camera6"-rearmain摄像头​
◦ "camera7"-fisheye_left摄像头​
◦ "camera8"-fisheye_rear摄像头
◦ "camera9"-fisheye_front摄像头​
◦ "camera10"-fisheye_right摄像头​
◦ 对应送标文件行内容是：camera的下载链接（需要鉴权）​
• params是相机内外参​
◦ 包含所有相机的内外参数，外参为lidar-to-cam，pinhole模型​
◦ extrinsic外参，4x4矩阵​
◦ intrinsic内参，3x3矩阵​
◦ 是否需要额外去畸变根据项目决定，不要依赖于里面的distcoeff畸变参数​
▪ distortion_model,​
• equidistant/equi鱼眼​
• radtan针孔​
{
"camera0":{
"extrinsic":[
[
0.0315702,
-0.998611,
-0.042186,
-0.0285775
],
[
-0.0125354,
0.0418082,
-0.999049,
-0.673792
],
[
0.999422,
0.0320687,
-0.0111981,
-1.10643
],
[
0,
0,
0,
1
]
],
"intrinsic":[
[
1826.6133871245559,
0,
1915.2191771492762
],
[
0,
1834.7941837797914,
1095.4703106732586
],
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
[
0,
0,
1
]
],
"distcoeff":[
[
-0.01383916323767457,
-0.019271422009210497,
0.02783647356426087,
-0.010405287579907621
]
],
"width":3840,
"height":2160,
"distortion_model":"equi"
},
"camera1":{
"extrinsic":[
[
-0.00714031,
-0.99892,
-0.045896,
0.0585296
],
[
0.00421229,
0.0458667,
-0.998938,
-0.723766
],
[
0.999966,
-0.00732599,
0.00387996,
-0.97494
],
[
0,
0,
0,
1
]
],
"intrinsic":[
[
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
7211.91848020694,
0,
2045.012613728529
],
[
0,
7251.8799949429595,
1023.8694786133906
],
[
0,
0,
1
]
],
"distcoeff":[
[
-0.23731118209876492,
-1.6945238916099126,
0.0016362551340936332,
0.0013264118640054217,
14.14587817593321
]
],
"width":3840,
"height":2160,
"distortion_model":"radtan"
}
}
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
• clip​
◦ 是Frame归属的ClipID​
• Frame​
◦ 是FrameID​
• Clip与Frame关系​
◦ Clip​
▪ Frame​
• FrameCamera​
• FrameLidar​
• timestamp​
◦ 含义
◦ 含义
▪ 每一帧的lidarcollecttime​
◦ 值示例
▪ 1689298334199101​
• Pose​
◦ 位置信息
▪ PoseJSON可以用于计算静止物体的绝对位置，可以提高标注的效率和精准度。​
◦ 使用方式
▪ localization里的timestamp，用lidar_collect的时间去找timestamp，LidarTime(16位
/1000000)=localization的timestamp，找离timestamp最近的那个，一般来说相差不会
超过5毫秒​
◦ JSON的下载链接​
每个数据行都有Pose的下载链接，同一个Clip的Pose是相同的，所以需要每个Clip只
需要下载第一个PoseJson解析里面每帧的timestamp及其他信息即可​
[
{
"timestamp":1689471123.053,
"pose":{
"position":{
"x":1.6490251836,
"y":2.0063902484,
"z":3.307000000000208
},
"orientation":{
"qx":0.0016591738260832825,
"qy":0.011822287975195087,
"qz":-0.162737862813475,
"qw":0.2245085592299923
},
"linear_velocity":{
"x":0.0011573000000013688,
"y":0.0011573002000023688,
"z":-0.0011573010000023688
},
"linear_acceleration":{
"x":-0.12462053696074665,
"y":0.31169531434061853,
"z":0.04141787504757519
},
"angular_velocity":{
"x":-0.001164734539538676,
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
"y":0.0015101873167637659,
"z":0.00011470325703366996
},
"heading":0.2281704775035213,
"linear_acceleration_vrf":{
"x":-0.2245056318969727,
"y":-0.01810714093017578,
"z":0.041209898498535155
},
"angular_velocity_vrf":{
"x":-0.001285096579000839,
"y":-0.000454532532776805,
"z":0.0000541633189095948
},
"euler_angles":{
"x":-0.0007618834160471571,
"y":0.028190239199191493,
"z":-1.2016258492913753
}
}
}
]
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
• lidar2imu​
◦ 文件下载链接，同其他需要鉴权
frame_id: imu
child_frame_id: main_lidar
transform:
rotation:
x: -0.004625
y: -0.001118
z: 0.690515
w: 0.723301
translation:
x: 0.088070
y: 1.139114
z: 1.827921
1
2
3
4
5
6
7
8
9
10
11
12
13
14
• preLabelResult​
◦ 预标注结果下载链接
◦ 每个Clip只需要下载第一个即可​
• extrinsics：ZIP文件，所有内参​
◦ lidar2camera​
◦ lidar2imu​
◦ lidar2lidar​
• intrinsics：ZIp文件，所有外参​
1.2.3、3DLane表头示例​
clip frame params lidar config camera0
camera0_timestamp camera6 camera6_timestamp camera7
camera7_timestamp camera8 camera8_timestamp camera9
camera9_timestamp camera10 camera10_timestamp pose
timestamp mc_clip_sideview_lidar
1
1、Clip列为ClipId，唯一标识一个大点云​
3、Params为内外参（内外参同一个Clip是一样的，因为一个Clip还有小于等于40帧的camera0，
同一个clip有40个数据行，所以每个Clip只需要取第一个数据行的内外参作为该CLip的内外参即
可）
4、Lidar为大点云的下载链接（取相同ClipId的第一个下载即可，较大，无需重复下载）​
5、Camera0为FrontWide摄像头的下载链接​
6、Pose和Timestamp见1.2.1（Pose也每个Clip仅需要下载第一个即可）​
7、每个camrea后接对应的采集时间​
example：​
clip frame params lidar config camera0
camera0_timestamp camera6 camera6_timestamp camera7
camera7_timestamp camera8 camera8_timestamp camera9
camera9_timestamp camera10 camera10_timestamp pose
timestamp
e8bf1e78f99f35708bf05e18d9256eda e8bf1e78f99f35708bf05e18d9256eda
{"camera0": {"extrinsic": [[-0.029168818323205392, -0.9995149736253626,
-0.010908599191400692, -0.026271251961146887], [-0.013042992714993262,
0.011292901642231066, -0.9998511642807323, -0.4728702860727063],
[0.9994893998330672, -0.029022196181365978, -0.013366067115866189,
1
2
-0.3654719245536414], [0.0, 0.0, 0.0, 1.0]], "intrinsic":
[[1852.4148865342538, 0, 1923.3190363579638], [0, 1852.4148865342538,
1077.3587703166233], [0, 0, 1]], "distcoeff": [[-0.020369304099528723,
0.0011859723931988662, 0.0, 0.0]], "width": 3840, "height": 2160,
"distortion_model": "fisheye"}, "camera1": {"extrinsic": [[8.789723209579048e-
05, -0.9999137155997779, -0.013135967018968573, 0.015599005883866188],
[0.00616667098419399, 0.013136259287713668, -0.9998946998863923,
-0.5085236906776793], [0.9999809820406834, 6.882789842066474e-06,
0.00616729353751827, -0.3761425210372349], [0.0, 0.0, 0.0, 1.0]],
"intrinsic": [[7410.880044979921, 0, 1902.647506970213], [0,
7410.880044979921, 1062.5877777462647], [0, 0, 1]], "distcoeff":
[[0.0001438660708580407, 0.08004684834437571, 0.0, 0.0]], "width": 3840,
"height": 2160, "distortion_model": "fisheye"}, "camera2": {"extrinsic":
[[0.7338320626775862, -0.679295660812005, -0.006921631913139603,
0.19456676322847105], [-0.0038862704772110726, 0.005990889953932126,
-0.9999745027446142, -0.8017327157841586], [0.6793198073721517,
0.7338402513078315, 0.0017563804602571966, -1.0929912877683214], [0.0, 0.0,
0.0, 1.0]], "intrinsic": [[1114.8315774226508, 0, 960.7783624691106], [0,
1114.8315774226508, 602.4203825015728], [0, 0, 1]], "distcoeff":
[[-0.025688974601599166, -0.024946354665274215, 0.0, 0.0]], "width": 1920,
"height": 1280, "distortion_model": "fisheye"}, "camera3": {"extrinsic":
[[0.7692262094571358, 0.6389171825549937, -0.008710483350291684,
-1.3264893316441808], [-0.059813227227748574, 0.05842685408505923,
-0.9964982090151117, -1.004415950394524], [-0.6361709019853147,
0.7670535421714129, 0.08315916611823007, -0.0626393951118773], [0.0, 0.0,
0.0, 1.0]], "intrinsic": [[1113.6963712712, 0, 961.3164185216709], [0,
1113.6963712712, 603.7850693763796], [0, 0, 1]], "distcoeff":
[[-0.02656837358584862, -0.024595069964136664, 0.0, 0.0]], "width": 1920,
"height": 1280, "distortion_model": "fisheye"}, "camera4": {"extrinsic":
[[-0.7557346334563142, -0.6548221746671965, 0.008548880556763387,
-0.19738490406243045], [0.05926503586781372, -0.0813871090775875,
-0.9949189886616797, -0.8929008115517162], [0.6521907844474936,
-0.7513880875021348, 0.10031511671551065, -0.9891270594750959], [0.0, 0.0,
0.0, 1.0]], "intrinsic": [[1108.0886883559656, 0, 956.3594035868961], [0,
1108.0886883559656, 599.7826725386543], [0, 0, 1]], "distcoeff":
[[-0.026493484144774995, -0.024747799369182335, 0.0, 0.0]], "width": 1920,
"height": 1280, "distortion_model": "fisheye"}, "camera5": {"extrinsic":
[[-0.7418536242082376, 0.6704245612580785, 0.013568637036030836,
1.3354591090295027], [-0.04580705705310394, -0.030479264900131073,
-0.9984852166833924, -0.9759070142424873], [-0.668995451235086,
-0.7413514160457647, 0.053321329256716156, -0.09598543483846457], [0.0, 0.0,
0.0, 1.0]], "intrinsic": [[1113.24004157915, 0, 960.8972893526814], [0,
1113.24004157915, 600.506446107682], [0, 0, 1]], "distcoeff":
[[-0.02520512837914536, -0.025237510864915113, 0.0, 0.0]], "width": 1920,
"height": 1280, "distortion_model": "fisheye"}, "camera6": {"extrinsic":
[[0.03168788846972276, 0.9994454575369024, -0.01023010914811212,
-0.005582642570808659], [-0.006203434977184341, -0.010038389800091085,
-0.9999303716382983, -0.9071940788797618], [-0.9994785616104103,
0.03174914391087258, 0.005881899532671708, -2.6146545688566736], [0.0, 0.0,
0.0, 1.0]], "intrinsic": [[1111.808668199756, 0, 960.4848174027945], [0,
1111.808668199756, 605.5212888848077], [0, 0, 1]], "distcoeff":
[[-0.025829314777532253, -0.02528362552915135, 0.0, 0.0]], "width": 1920,
"height": 1280, "distortion_model": "fisheye"}, "camera7": {"extrinsic":
[[0.9978998225686861, -0.049851112582341414, -0.04136194738750477,
-0.46311659531051574], [-0.06091005415448207, -0.5048448793443917,
-0.861058426067967, -0.09810954047154972], [0.02204335319959401,
0.8617694090498044, -0.50682104948953, -1.3235412959212953], [0.0, 0.0, 0.0,
1.0]], "intrinsic": [[572.1771051350621, 0, 962.8454170064136], [0,
572.238735944139, 755.421433524873], [0, 0, 1]], "distcoeff":
[[0.015104168285096291, -0.0061479351608860625, -0.0007320686648522558,
-5.8833291365005755e-05]], "width": 1536, "height": 1920, "distortion_model":
"fisheye"}, "camera8": {"extrinsic": [[-0.0033319135228588865,
0.9999315999563995, 0.011211322888573738, -0.02740378200549618],
[0.18170462165817863, 0.011630138254383636, -0.9832843791865312,
-0.4073201208977074], [-0.9833475117273315, -0.0012390693360716516,
-0.18173094366905712, -2.7549494418607554], [0.0, 0.0, 0.0, 1.0]],
"intrinsic": [[563.6942542738898, 0, 961.652350932664], [0,
563.9775270702067, 747.5765752324043], [0, 0, 1]], "distcoeff":
[[0.01801651350153593, -0.007929945081446134, 0.0003269646065622156,
-0.0002343620936079627]], "width": 1536, "height": 1920, "distortion_model":
"fisheye"}, "camera9": {"extrinsic": [[-0.02303989472578771,
-0.9997344868083116, -0.00034516249300509024, -0.01405198349251332],
[-0.22955389343474866, 0.005626331047838051, -0.973279689713028,
-0.6771334006638658], [0.9730232131146578, -0.022345028195598693,
-0.22962257392287266, -2.5004531216379315], [0.0, 0.0, 0.0, 1.0]],
"intrinsic": [[567.8364522739613, 0, 966.8806540902376], [0,
568.7361209378345, 750.2275197171581], [0, 0, 1]], "distcoeff":
[[0.018599859698221255, -0.007216969090888559, -0.0005012515814924863,
-4.906123184957169e-05]], "width": 1536, "height": 1920, "distortion_model":
"fisheye"}, "camera10": {"extrinsic": [[-0.9999784774377649,
-0.0006186885118498869, 0.006531606676363728, 0.4196878472679045],
[-0.006031004355025867, 0.4786207437553013, -0.878001031111919,
-0.14994172364648092], [-0.0025829532940169985, -0.8780215264283945,
-0.4786141739236658, -1.3227618868448456], [0.0, 0.0, 0.0, 1.0]],
"intrinsic": [[564.7735527253236, 0, 967.2712277598115], [0,
565.8977615404226, 745.319796078611], [0, 0, 1]], "distcoeff":
[[0.017832453098290003, -0.005396435560731018, -0.0013868465245521645,
0.00011104339308450818]], "width": 1536, "height": 1920, "distortion_model":
"fisheye"}} https://big-data-resource-l2plus.obs.cn-east-
3.myhuaweicloud.com:443/clips/20231127/LVTDB24B8ND409768/L2+/clip_170105228200
0/bevLane/lidar0_stitch_68543efca1fb3612b2483518a228b054.png?
AccessKeyId=478SULZPVA3PMRBQEUHH&Expires=1726736322&Signature=GAjp+9CK4WaDfHgs
7l3tZDgnH2A= https://big-data-resource-l2plus.obs.cn-east-
3.myhuaweicloud.com:443/clips/20231127/LVTDB24B8ND409768/L2+/clip_170105228200
0/bevLane/lidar0_stitch_config_68543efca1fb3612b2483518a228b054.json?
AccessKeyId=478SULZPVA3PMRBQEUHH&Expires=1726736322&Signature=arK/JlbXRNN0dq0P
nZDEagSJxDw= https://zdrive-
bigdata.mychery.com/manager/dataManager/frameCamera/download?
frame_camera_id=98a86e6810743675b10caa0f8bcd26b8 1702029592402223
https://zdrive-bigdata.mychery.com/manager/dataManager/frameCamera/download?
frame_camera_id=2e724633a9e03b3693ea2a9217c98b2b 1702029592403322
https://zdrive-
bigdata.mychery.com/manager/dataManager/v1/clip/downloadLocalization/e8bf1e78f
99f35708bf05e18d9256eda 1702029592400155
*******、lidarslamconfig​
详细介绍见​ LidarSLAM的pose副本​
1. 拼接的点云 correct.pcd 的坐标系是原点平移到clip里面 pose.json (localization.json)中
第一帧pose后的UTM坐标，注意只有平移没有旋转，这里的平移(x,y,z)记为 OFFSET 。​
2. 优化输出的 config.json (correct_localization.json)里面的每帧key-
value localization_pose 对应的是运动补偿后单帧lidar时刻对应的RTK在UTM中的pose，
这个pose是lidarslam优化输出的pose,记为 T_optrtk2utm , 标定文件lidar2imu.yaml中可以
拿到lidar外参 T_lidar2imu ​
3. 位置转换计算：
单帧lidar运动补偿到T_camera+20ms后的点齐次坐标记为：
P_lidar_motion_compensation
lidarSLAM输出的 correct.pcd 点云补位齐次坐标记为： P_lidar_slam ​
P_lidar_slam=(T_optrtk2utm-OFFSET)@T_lidar2imu@
P_lidar_motion_compensation​
4. 拼接点云上的标注结果投影图像：
参考上面的计算，逆过程可以把点云中的标注转换到单帧lidar坐标系。记拼接点云中的标注为
P_labeling （这个 P_labeling 标注的坐标系同 P_lidar_slam 一样）。记单帧lidar坐标
系中的标注为 P_labeling_one_frame 。​
标定参数extrinsic中lidar2cam里面有lidar到camera的外参，标定参数intrinsic中有camera的内
参，使用这些参数可以把标注结果投影到图像。记投影图像的像素坐标标注为 P_labeling_uv 。​
P_labeling_one_frame= np.linalg.inv(T_lidar2imu) @ np.linalg.inv(T_optrtk2utm-
OFFSET)@P_labeling​
P_labeling_uv = intrinsic @ lidar2cam@ P_labeling_one_frame​
送标Header里config示例文件：​
lidar0_stitch_config_27afe08c37b83382a10304d706f4a953.json
因送标数量较多的情况下，送标文件Txt会较大，会压缩为ZIP进行传输：​
Txt压缩后文件结构：​
示例ZIP文件：​
374_56_13_1_0_114721418811160
06312.zip
3.50KB
1.2.4、2DLane车道线表头示例​
clip frame camera0 camera1
1
1.2.5、23DObject/3DBox/23DCityObjectDetection/Only3DCityObject
Detection表头示例​
clip frame params lidar camera0 camera1
camera2 camera3 camera4 camera5 camera6
pose timestamp
1
1.2.6、(5）2D红绿灯关键帧/（6）2D红绿灯连续帧表头示例​
clip frame camera0 camera1
1
1.2.7、Bev车道线标注表头示例​
clip frame combineCamera combineCamera0 combineCamera1
combineCameraConfig combineCamera0Config combineCamera1Config
camera0 camera1
1
1、Combine：clip frame combineCamera combineCameraConfig camera0
camera1
2、Camera0：clip frame combineCamera0 combineCamera0Config camera0
camera1
3、Camera1：clip frame combineCamera1 combineCamera1Config camera0
camera1
1
2
3
4、Camera0 && Camera1：clip frame combineCamera0 combineCamera1
combineCamera0Config combineCamera1Config camera0 camera1
4
备注：combineCamera每个Clip（ClipId列为唯一标识符）只需要下载一次即可，无需重复下载​
• combineCamera​
◦ 由Camera0和camera1的时序拼接点云​
• combineCamera0​
◦ 由Camera0的时序拼接点云​
• combineCamera1​
◦ 由Camera1的时序拼接点云​
• combineCameraConfig ​
◦ 配置
• combineCamera0Config ​
◦ 配置
• combineCamera1Config ​
◦ 配置
送标时，只会送标combineCamera或者（combineCamera0、combineCamera1）​
1.2.8、2D-Lane-with-label标注表头示例​
clip frame combineCamera combineCamera combineCameraHeightPt
camera0 camera1
1
备注：combineCamera每个Clip（ClipId列为唯一标识符）只需要下载一次即可，无需重复下载​
• combineCamera​
◦ 由Camera0和camera1的时序拼接点云​
• combineCamera0​
◦ 由Camera0的时序拼接点云​
• combineCamera1​
◦ 由Camera1的时序拼接点云​
• combineCameraConfig ​
◦ 配置
• combineCamera0Config ​
◦ 配置
• combineCamera1Config ​
◦ 配置
送标时，只会送标combineCamera或者（combineCamera0、combineCamera1）​
1.2.9、CameraBevLaneCombine标注表头示例​
clip combineCamera combineCamera0 combineCamera1
1
优先标注：combineCamera0、combineCamera1同时可以标注的​
情况：
1、同时有combineCamera0 combineCamera1，无combineCamera​
2、只有combineCamera ​
1.2.10、CameraSlamLaneCombine标注表头示例​
clip frame combineCamera combineCameraConfig combineCameraHeightPt
camera0 camera1
1
1.2.11、KeyPoints表头示例​
clip frame camera0 camera1
1
1.2.12、3DCityObjectDetectionWithFishEye表头示例​
clip frame params lidar camera0 camera1
camera6 camera7 camera8 camera9 camera10
pose timestamp preLabelResult
1
1.2.13、3DHighWayObjectDetectionWithFishEye表头示例​
clip frame params lidar camera0 camera1
camera6 camera7 camera8 camera9 camera10
1
pose timestamp preLabelResult
1.2.14、PARKING_MOVABLE_OBJECT_DETECTION泊车目标物检测表头示例：​
old：​
clip frame params lidar camera0 camera6
camera7 camera8 camera9 camera10 pose
timestamp lidar2imu preLabelResult
1
new：（去除lidar2im以extrinsics替代，增加pvb共用预标注结果preLabelResult ）​
clip frame params lidar camera0 camera6
camera7 camera8 camera9 camera10 pose
timestamp extrinsics preLabelResult
1
• extrinsics见1.2.2，解压后对应路径为：lidar2imu/lidar2imu.yaml​
• preLabelResult：JSON文件下载链接（值可能未空，预标注结果未必存在）​
pvb对应的以下标注类型，都使用同一个标注结果JSON文件。格式固定​
3d_city_object_detection_with_fish_eye 15​
3d_highway_object_detection_with_fish_eye 16​
parking_movable_object_detection 17​
Only3DCityObjectDetection 9​
23DObjectDetection 10​
示例文件：
clip_1723858879600.json
49.35MB
1.2.15、AUTO_4D_LANE（自动4DLane标注）​
clip frame lidar0 camera0 camera0_timestamp
camera6 camera6_timestamp tag pose timestamp
extrinsics intrinsics
1
• tag：clip标签，多个标签以英文","逗号分隔​
• extrinsics：ZIP文件，所有内参，见1.2.2​
• intrinsics：ZIp文件，所有外参，见1.2.2​
示例：
clip frame lidar0 camera0 camera0_timestamp
camera6 camera6_timestamp tag pose timestamp
extrinsics intrinsics
15fbec48f3163bf393fbf5c30a07cb2b d45006fd9ada3a6fa8051965fd664c99
https://zdrive-bigdata.mychery.com/manager/dataManager/frameLidar/download?
frame_lidar_id=1118bbbeaff53f278c5b368b806e0ee8 https://zdrive-
bigdata.mychery.com/manager/dataManager/frameCamera/download?
frame_camera_id=30237aec900039daa780c4675e3c0980 1717043623415541
https://zdrive-bigdata.mychery.com/manager/dataManager/frameCamera/download?
frame_camera_id=ad653f471e333d658106ad2c01f855ad 1717043623415766
city_road https://zdrive-
bigdata.mychery.com/manager/dataManager/v1/clip/downloadLocalization/15fbec48f
3163bf393fbf5c30a07cb2b 1717043623399430 https://zdrive-ai-big-data-
platform.obs.cn-east-
3.myhuaweicloud.com:443/dataManager/calibration/LNNAJDDS6PD000629/clip_1717043
623299/extrinsics.zip?
AccessKeyId=478SULZPVA3PMRBQEUHH&Expires=1729317287&Signature=KSWObH6%2BU%2Ftj
ar89lgSxGEokwG8%3D https://zdrive-ai-big-data-platform.obs.cn-east-
3.myhuaweicloud.com:443/dataManager/calibration/LNNAJDDS6PD000629/clip_1717043
623299/intrinsics.zip?
AccessKeyId=478SULZPVA3PMRBQEUHH&Expires=1729317287&Signature=UNY%2BoDGVI6spKL
3H8%2FKsGKm7Yko%3D
1
2
357_58_13_1_0_1506398356478968035.txt
若请求参数的fileContentType为"ZIP"，则送标表单接口的uploadFile为ZIP文件格式，​
Txt压缩后：​
1.2.16、环视泊车车位检测parking_surround_space_detection​
表头：
clip frame params combineLidar config lidar0 camera0
camera0_timestamp camera6 camera6_timestamp
camera7 camera7_timestamp camera8 camera8_timestamp
camera9 camera9_timestamp camera10 camera10_timestamp
pose timestamp extrinsics intrinsics mc_clip_sideview_lidar
1
Clip和Frame分别为CLip唯一ID，和Frame唯一ID，前者需要回标回传，后者用于
corrected_pose匹配对应的Pose。​
• Clip:clipId​
• Frame:frameId​
• params：内外参，格式见1.2.2​
• combineLidar:clip拼接大点云，下载链接​
• lidar0：主激光雷达，下载链接​
• camera{X}_timestmap:采集时间戳​
• Config见下面的改动项​
• pose：原Pose信息，格式见1.2.2，下载链接​
• extrinsics：ZIP文件，所有内参，见1.2.2​
• intrinsics：ZIp文件，所有外参，见1.2.2​
改动项：
1、lidar为Clip拼接大点云，每个Clip下载其中一个即可（PCD文件），对应combineLidar的列值​
2、config说明参考*******​
示例（待更新）：
clip frame params combineLidar lidar0
lidar1 lidar2 lidar3 lidar4 camera0
camera6 camera7 camera8 camera9 camera10
isKeyFrame corrected_pose pose timestamp
extrinsics intrinsics
1
a2c1581eb64831b2aaf50ffa9ba12f35 4168e816c45534e7808c15e4a0661a75
{"camera0": {"extrinsic": [[-0.029168818323205392, -0.9995149736253626,
-0.010908599191400692, -0.026271251961146887], [-0.013042992714993262,
0.011292901642231066, -0.9998511642807323, -0.4728702860727063],
[0.9994893998330672, -0.029022196181365978, -0.013366067115866189,
-0.3654719245536414], [0.0, 0.0, 0.0, 1.0]], "intrinsic":
[[1852.4148865342538, 0, 1923.3190363579638], [0, 1852.4148865342538,
1077.3587703166233], [0, 0, 1]], "distcoeff": [[-0.020369304099528723,
0.0011859723931988662, 0.0, 0.0]], "width": 3840, "height": 2160,
"distortion_model": "fisheye"}, "camera1": {"extrinsic": [[8.789723209579048e-
05, -0.9999137155997779, -0.013135967018968573, 0.015599005883866188],
[0.00616667098419399, 0.013136259287713668, -0.9998946998863923,
-0.5085236906776793], [0.9999809820406834, 6.882789842066474e-06,
0.00616729353751827, -0.3761425210372349], [0.0, 0.0, 0.0, 1.0]],
"intrinsic": [[7410.880044979921, 0, 1902.647506970213], [0,
7410.880044979921, 1062.5877777462647], [0, 0, 1]], "distcoeff":
[[0.0001438660708580407, 0.08004684834437571, 0.0, 0.0]], "width": 3840,
"height": 2160, "distortion_model": "fisheye"}, "camera2": {"extrinsic":
[[0.7338320626775862, -0.679295660812005, -0.006921631913139603,
0.19456676322847105], [-0.0038862704772110726, 0.005990889953932126,
-0.9999745027446142, -0.8017327157841586], [0.6793198073721517,
0.7338402513078315, 0.0017563804602571966, -1.0929912877683214], [0.0, 0.0,
0.0, 1.0]], "intrinsic": [[1114.8315774226508, 0, 960.7783624691106], [0,
1114.8315774226508, 602.4203825015728], [0, 0, 1]], "distcoeff":
[[-0.025688974601599166, -0.024946354665274215, 0.0, 0.0]], "width": 1920,
"height": 1280, "distortion_model": "fisheye"}, "camera3": {"extrinsic":
[[0.7692262094571358, 0.6389171825549937, -0.008710483350291684,
-1.3264893316441808], [-0.059813227227748574, 0.05842685408505923,
-0.9964982090151117, -1.004415950394524], [-0.6361709019853147,
0.7670535421714129, 0.08315916611823007, -0.0626393951118773], [0.0, 0.0,
0.0, 1.0]], "intrinsic": [[1113.6963712712, 0, 961.3164185216709], [0,
1113.6963712712, 603.7850693763796], [0, 0, 1]], "distcoeff":
[[-0.02656837358584862, -0.024595069964136664, 0.0, 0.0]], "width": 1920,
"height": 1280, "distortion_model": "fisheye"}, "camera4": {"extrinsic":
[[-0.7557346334563142, -0.6548221746671965, 0.008548880556763387,
-0.19738490406243045], [0.05926503586781372, -0.0813871090775875,
-0.9949189886616797, -0.8929008115517162], [0.6521907844474936,
-0.7513880875021348, 0.10031511671551065, -0.9891270594750959], [0.0, 0.0,
0.0, 1.0]], "intrinsic": [[1108.0886883559656, 0, 956.3594035868961], [0,
1108.0886883559656, 599.7826725386543], [0, 0, 1]], "distcoeff":
[[-0.026493484144774995, -0.024747799369182335, 0.0, 0.0]], "width": 1920,
"height": 1280, "distortion_model": "fisheye"}, "camera5": {"extrinsic":
[[-0.7418536242082376, 0.6704245612580785, 0.013568637036030836,
1.3354591090295027], [-0.04580705705310394, -0.030479264900131073,
-0.9984852166833924, -0.9759070142424873], [-0.668995451235086,
-0.7413514160457647, 0.053321329256716156, -0.09598543483846457], [0.0, 0.0,
0.0, 1.0]], "intrinsic": [[1113.24004157915, 0, 960.8972893526814], [0,
2
1113.24004157915, 600.506446107682], [0, 0, 1]], "distcoeff":
[[-0.02520512837914536, -0.025237510864915113, 0.0, 0.0]], "width": 1920,
"height": 1280, "distortion_model": "fisheye"}, "camera6": {"extrinsic":
[[0.03168788846972276, 0.9994454575369024, -0.01023010914811212,
-0.005582642570808659], [-0.006203434977184341, -0.010038389800091085,
-0.9999303716382983, -0.9071940788797618], [-0.9994785616104103,
0.03174914391087258, 0.005881899532671708, -2.6146545688566736], [0.0, 0.0,
0.0, 1.0]], "intrinsic": [[1111.808668199756, 0, 960.4848174027945], [0,
1111.808668199756, 605.5212888848077], [0, 0, 1]], "distcoeff":
[[-0.025829314777532253, -0.02528362552915135, 0.0, 0.0]], "width": 1920,
"height": 1280, "distortion_model": "fisheye"}, "camera7": {"extrinsic":
[[0.9978998225686861, -0.049851112582341414, -0.04136194738750477,
-0.46311659531051574], [-0.06091005415448207, -0.5048448793443917,
-0.861058426067967, -0.09810954047154972], [0.02204335319959401,
0.8617694090498044, -0.50682104948953, -1.3235412959212953], [0.0, 0.0, 0.0,
1.0]], "intrinsic": [[572.1771051350621, 0, 962.8454170064136], [0,
572.238735944139, 755.421433524873], [0, 0, 1]], "distcoeff":
[[0.015104168285096291, -0.0061479351608860625, -0.0007320686648522558,
-5.8833291365005755e-05]], "width": 1536, "height": 1920, "distortion_model":
"fisheye"}, "camera8": {"extrinsic": [[-0.0033319135228588865,
0.9999315999563995, 0.011211322888573738, -0.02740378200549618],
[0.18170462165817863, 0.011630138254383636, -0.9832843791865312,
-0.4073201208977074], [-0.9833475117273315, -0.0012390693360716516,
-0.18173094366905712, -2.7549494418607554], [0.0, 0.0, 0.0, 1.0]],
"intrinsic": [[563.6942542738898, 0, 961.652350932664], [0,
563.9775270702067, 747.5765752324043], [0, 0, 1]], "distcoeff":
[[0.01801651350153593, -0.007929945081446134, 0.0003269646065622156,
-0.0002343620936079627]], "width": 1536, "height": 1920, "distortion_model":
"fisheye"}, "camera9": {"extrinsic": [[-0.02303989472578771,
-0.9997344868083116, -0.00034516249300509024, -0.01405198349251332],
[-0.22955389343474866, 0.005626331047838051, -0.973279689713028,
-0.6771334006638658], [0.9730232131146578, -0.022345028195598693,
-0.22962257392287266, -2.5004531216379315], [0.0, 0.0, 0.0, 1.0]],
"intrinsic": [[567.8364522739613, 0, 966.8806540902376], [0,
568.7361209378345, 750.2275197171581], [0, 0, 1]], "distcoeff":
[[0.018599859698221255, -0.007216969090888559, -0.0005012515814924863,
-4.906123184957169e-05]], "width": 1536, "height": 1920, "distortion_model":
"fisheye"}, "camera10": {"extrinsic": [[-0.9999784774377649,
-0.0006186885118498869, 0.006531606676363728, 0.4196878472679045],
[-0.006031004355025867, 0.4786207437553013, -0.878001031111919,
-0.14994172364648092], [-0.0025829532940169985, -0.8780215264283945,
-0.4786141739236658, -1.3227618868448456], [0.0, 0.0, 0.0, 1.0]],
"intrinsic": [[564.7735527253236, 0, 967.2712277598115], [0,
565.8977615404226, 745.319796078611], [0, 0, 1]], "distcoeff":
[[0.017832453098290003, -0.005396435560731018, -0.0013868465245521645,
0.00011104339308450818]], "width": 1536, "height": 1920, "distortion_model":
"fisheye"}} https://big-data-resource-l2plus.obs.cn-east-
3.myhuaweicloud.com:443/clips/20231127/LVTDB24B8ND409768/L2%2B/clip_1701052282
000/parking/combine_parking_d206f8237e25380ca0d590e1e27589fa.pcd?
AccessKeyId=478SULZPVA3PMRBQEUHH&Expires=1725514505&Signature=Sa2aY1MUTgb1rvR2
XwVd%2FELG2us%3D https://zdrive-
bigdata.mychery.com/manager/dataManager/frameLidar/download?
frame_lidar_id=94c7a45c13873352b3c38f4e50dc094e https://zdrive-
bigdata.mychery.com/manager/dataManager/frameLidar/download?
frame_lidar_id=79cc31b2413c3d59bc15414881a95ec2 https://zdrive-
bigdata.mychery.com/manager/dataManager/frameLidar/download?
frame_lidar_id=b3d54b44ca2a3339899fdbe3f9232127 https://zdrive-
bigdata.mychery.com/manager/dataManager/frameLidar/download?
frame_lidar_id=13db6b86c71a36f19c39d609b96e1a7f https://zdrive-
bigdata.mychery.com/manager/dataManager/frameLidar/download?
frame_lidar_id=9dd87801226811ee8e70a33e1de7b53c https://zdrive-
bigdata.mychery.com/manager/dataManager/frameCamera/download?
frame_camera_id=6dacff07dccb30959e7bb0f16ab8ef4b https://zdrive-
bigdata.mychery.com/manager/dataManager/frameCamera/download?
frame_camera_id=8846c1d2ed373a9c96cb58f42680f6f1 https://zdrive-
bigdata.mychery.com/manager/dataManager/frameCamera/download?
frame_camera_id=a367752ec2b63287be47a8e1335f3bdb https://zdrive-
bigdata.mychery.com/manager/dataManager/frameCamera/download?
frame_camera_id=ddbdf0d89fc13dcc88d7f48e545da587 https://zdrive-
bigdata.mychery.com/manager/dataManager/frameCamera/download?
frame_camera_id=4a10a9bea9c33b1da80619c142df406a https://zdrive-
bigdata.mychery.com/manager/dataManager/frameCamera/download?
frame_camera_id=4e18e21fcf813b21a7ead096b9b643d6 false
https://big-data-resource-l2plus.obs.cn-east-
3.myhuaweicloud.com:443/clips/20231127/LVTDB24B8ND409768/L2%2B/clip_1701052282
000/parking/corrected_localization_1b47a670e36d396c91ba4d5da1b1b25d.json?
AccessKeyId=478SULZPVA3PMRBQEUHH&Expires=1725514507&Signature=I77S5NR4wYfG%2Bp
7jRcFnxhXEVsw%3D https://zdrive-
bigdata.mychery.com/manager/dataManager/v1/clip/downloadLocalization/a2c1581eb
64831b2aaf50ffa9ba12f35 1714374690701383 https://zdrive-ai-big-
data-platform.obs.cn-east-
3.myhuaweicloud.com:443/dataManager/calibration/LNNAJDDS6PD000629/clip_1714374
676500/extrinsics.zip?
AccessKeyId=478SULZPVA3PMRBQEUHH&Expires=1725514498&Signature=rZ9Cd6wzNJ6FZi3M
iupzru0r3zU%3D https://zdrive-ai-big-data-platform.obs.cn-east-
3.myhuaweicloud.com:443/dataManager/calibration/LNNAJDDS6PD000629/clip_1714374
676500/intrinsics.zip?
AccessKeyId=478SULZPVA3PMRBQEUHH&Expires=1725514497&Signature=SgH2g8xTQYi1%2Bk
5V8DW45F449B4%3D
374_56_13_1_0_11472141881116006312.txt
因送标数量较多的情况下，送标文件Txt会较大，会压缩为ZIP进行传输：​
Txt压缩后文件结构：​
374_56_13_1_0_114721418811160
06312.zip
3.50KB
1.2.17、泊车路面标识parking_road_sign_mark​
同1.2.16
clip frame params combineLidar config lidar0 camera0
camera0_timestamp camera6 camera6_timestamp
camera7 camera7_timestamp camera8 camera8_timestamp
camera9 camera9_timestamp camera10 camera10_timestamp
pose timestamp extrinsics intrinsics mc_clip_sideview_lidar
1
表头解释同：1.2.16、parking_surround_space_detection​
1.2.18、泊车可行驶区域parking_free_space​
clip frame params config mc_lidar camera0
camera0_timestamp camera1 camera1_timestamp camera2
camera2_timestamp camera3 camera3_timestamp camera4
camera4_timestamp camera5 camera5_timestamp camera6
camera6_timestamp pose timestamp extrinsics intrinsics pmodGt
gopGt mc_frame_sideview_lidar
1
部分表头解释参考：1.2.16、parking_surround_space_detection​
PMODGTExample（非必然有值）：​
​ ​
clip_1735032848600-PVB.json
GOPGTExample（非必然有值）：​
​ ​
clip_1735032848600-GOP.json
两个GT文件每个Clip仅下载一次即可。​
🏖️ 注意：这个项目送标的pcd为加运动补偿的单帧pcd，需要供应商侧使用config.json中的
pose进行点云拼接，拼接时注意❗要建立拼接点云和单帧点云的索引或者对应关系，保证拼
接点云里面每个点的标注内容能拆回到单帧。
1.2.19、（22）行车可行驶区域driving_free_space​
同1.2.16​
clip frame params combineLidar config lidar0 camera0
camera0_timestamp camera6 camera6_timestamp
camera7 camera7_timestamp camera8 camera8_timestamp
camera9 camera9_timestamp camera10 camera10_timestamp
pose timestamp extrinsics intrinsics mc_clip_sideview_lidar
1
表头解释同：1.2.16、parking_surround_space_detection​
1.2.20、（23）GOP静态目标物检测​
clip frame params combineLidar config mc_lidar camera0
camera0_timestamp camera1 camera1_timestamp
camera2 camera2_timestamp camera3 camera3_timestamp
camera4 camera4_timestamp camera5 camera5_timestamp
camera6 camera6_timestamp pose timestamp extrinsics
intrinsics mc_clip_sideview_lidar
1
每个Clip1个Config，仅需下载第一个，其中mc_lidar、camera0-6包含近200帧。​
1.2.21、（24）相机遮挡camera_blockage​
clip frame camera0 camera1 camera2 camera3 camera4
camera5 camera6 camera7 camera8 camera9 camera10
1
每帧10个图片，4个fisheyecamera,7个周视相机，相机编号见1.2.2​
1.2.22、（25）环境识别environment_identification​
clip frame camera0 camera1 camera2 camera3 camera4
camera5 camera6 camera7 camera8 camera9 camera10
1
每帧10个图片，4个fisheyecamera,7个周视相机，相机编号见1.2.2​
1.2.23、（26）parking_fsline_2d​
clip frame camera7 camera8 camera9 camera10
1
每帧4个图片，4个fisheyecamera,相机编号见1.2.2​
1.2.24、（27）路牌TRAFFIC_SIGN​
clip frame camera0 camera1
1
1.2.25、（28）光斑SPOT_DETECTION​
clip frame camera0 camera1
1
1.2.26、（29）City4DLane​
clip frame params combineLidar config mc_lidar camera0
camera0_timestamp camera1 camera1_timestamp
camera6 camera6_timestamp camera7 camera7_timestamp
camera8 camera8_timestamp camera9 camera9_timestamp
camera10 camera10_timestamp pose timestamp extrinsics
intrinsics mc_clip_sideview_lidar
1
combineLidar、pose、config、extrinsics intrinsics每个Clip仅需下载一次即可，无需重复下载​
1.2.27、（30）23DObjectComplement​
clip frame params lidar camera1 gt
1
新参数解释：
Gt真值，每个Clip仅需要下载一份即可。​
数据形式：下载链接
样例文件：
​ ​
clip_1691111147800.json
1.2.28、 (31）Parking3DLane表头示例​
同3DLane，见1.2.23​
1.2.29、 (32）行车OCC（3DDrivingSegmentation）表头示例​
clip frame params config mc_lidar camera0
camera0_timestamp camera1 camera1_timestamp camera2
camera2_timestamp camera3 camera3_timestamp camera4
camera4_timestamp camera5 camera5_timestamp camera6
camera6_timestamp camera7 camera7_timestamp camera8
camera8_timestamp camera9 camera9_timestamp camera10
camera10_timestamp pose timestamp extrinsics intrinsics
cityGt highwayGt gopGt mc_frame_sideview_lidar
1
cityGt和highwayGt非同时存在，三种GTJSON结构有细微区别，注意适配。​
mc_frame_sideview_lidar按需求非必然存在。​
cityGt:​
highwayGt:​
gopGt:​
1.2.30、（33）GOPDETCOMPLEMENT​
代码块​
clip frame params combineLidar config mc_lidar camera0
camera0_timestamp camera1 camera1_timestamp camera2
camera2_timestamp camera3 camera3_timestamp camera4
camera4_timestamp camera5 camera5_timestamp camera6
camera6_timestamp pose timestamp extrinsics intrinsics
mc_clip_sideview_lidar gop_gt
1.3、大卓鉴权、JPG或Lidar下载API​
标注供应商获取到送标文件后，需要解析送标文件行内容，先通过大卓鉴权接口获取Token再去
下载JPG和PCD，同时需要保证下载机制有异步重试机制，作废机制。​
下载资源，访问条件：
1、Token​
2、确保出口IP已在我方配置。​
注意：若Txt内URL的域名为zdrive-portal.mychery.com、zdrive-bigdata.mychery.com才需
要增加Token！因为部分大文件可能会直接走华为云的接口访问，额外增加Token会访问失败。​
1
1.3.1、获取Token接口​
URL​ https://host:port/operationManager/token​
method​ POST​
RequestHeader​ "Content-Type":"application/x-www-form-urlencoded"​
Or​
"Content-Type":"multipart/form-data"​
ResponseHeader​ "Content-Type":"application/json"​
入参​ username（用户名）​
password（密码）​
回参​ Code(业务操作码，0是正常）​
Message（提示信息）​
Data(数据值）
expireAt:与yyyy-MM-ddHH:mm:ss过期​
expireTime:有效时间（秒）​
token：（token值）​
RequestBody​ username:"Test"​
password:"Test"​
ResponseBody​
{
"code":0,
"message":"操作成功",
"data":{
"expireAt":"2023-07-11 12:34:56",
"expireTime":3600,
"token":"token 值"
}
}
1
2
3
4
5
6
7
8
9
1.3.2、获取LidarPCD文件​
URL​ https://host:port/dataManager/frameLidar/download?frame_lidar_id=xxxxx​
method​ Get​
RequestHeader​ Key：Authorization​
Value："Bearer"+token​
示例："Authorization":"BearereyJ0eX111111111111111"​
Response
Header​
"Content-Disposition":"attachment;
filename=lidar0_1686460162420925_58604f740c2f11ee98e49fb417e3b72f.pcd"​
入参​ frame_lidar_id (Lidar帧id）​
示例：PCDDownloadURL，需在我方将出口IP配置上才可以访问​
1.3.3、获取CameraJPG文件​
URL​ https://host:port/dataManager/frameCamera/download?frame_camera_id=xxxxx​
method​ Get​
RequestHeader​ Key：Authorization​
Value："Bearer"+token​
示例："Authorization":"BearereyJ0eX111111111111111"​
Response
Header​
"Content-Disposition":"attachment;
filename=camera1_1686460162420925_58604f740c2f11ee98e49fb417e3b72f.jpg"​
入参​ frame_camera_id(Camrea帧id）​
示例：JPGDownloadURL，需在我方将出口IP配置上才可以访问​
1.3.4、获取PoseJson文件Or获取大点云PCD​
同1.3.1、1.3.2都是下载URL，除了URL不一样，其他都一样。​
示例：PoseDownloadURL，需在我方将出口IP配置上才可以访问​
2、标注结果获取API​
2.1、API​
2.1.1、入参​
参数名​ 类型​ 含义​
annotateId​ 字符串​ 关联的送标任务的AnnotateId​
2.1.2、回参​
参数名​ 类型​ 含义​ 取值​
annotateId​ 字符串​ 接口入参的annotateId​
clipElements​ 整数​ 本批次Clip的数量​
frameElement
s​
整数​ 本批次总帧数或者多帧融合点云的数量​
state​ 字符串​ 表明当前该送标任务的状态​ "labeling"-标注中​
"finished"-已完成​
"abnormal"-异常​
annotateType​ 整数​ 标注任务类型​ 对应送标时的AnnotateType，原
样返回​
clipFinished​ 整数​ 已完成标注的Clip数量​
frameFinished​ 整数​ 已完成标注的Frame数量​
zipDownload
Url​
字符串​ 下载标注结果Zip包的url​ 1、试标阶段返回全量数据​
2、待招标通过后再沟通确认​
注意：该URL必须不需要授权即可
访问下载压缩包，且要确保没有
CDNCache，有Cache会导致获
取Zip包不是最新的。​
timestamp​ 整数​ 标注结果的更新时间戳；如果两次请求
返回的时间戳没有变化，则意味着没有
新的数据，⽆需重新下载​
1697081971​
2.1.3、请求及响应示例​
URL​ https://host:port/<供应商自定义URL>/{annotateId}?timestamp=1689154713​
method​ GET​
RequestHeader​ "Authorization":"ZdriveAI<accessKey>:<signature>"//签名计算⽅法⻅下⽂​
URLParams​ //unixtime，与请求接收时间相差应在[-600秒，5秒]之内​
timestamp=1689154713​
ResponseHeader​ "Content-Type":"application/json"​
ResponseBody​
{
"code":0,
"message":"操作成功",
"data":{
"annotateId":"13213",
"state":"labeling",
"annotateType":1,
"clipElements":1000,
"frameElements":123123,
"clipFinished":123,
"frameFinished":123,
"zipDownloadUrl": "http://xxxx/xxx.zip",
"timestamp": 1689154713,
}
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
2.1.4、Signature计算方法​
StringToSign = "GET" + "\n" +
"timestamp=xxx" + "\n" +
"annotateId=xxx";
Signature=Base64( HMAC-SHA1( UTF-8-Encoding-Of(secretKey),
UTF-8-Encoding-Of(StringToSign)
));
1
2
3
4
5
6
7
描述：先拼装StringToSign，然后使⽤HMAC-SHA1计算secretKey和StringToSign的摘要，最后对
摘要进⾏base64编码即可得到最终的签名 ​
注意：拼接顺序不可变
四、标注结果
本接口规范只要求除算法要求标注结果annotated_info外的格式，具体算法标注结果格式要求会由
各算法团队给出。
红色部分大体是按照2.2.1、2.2.2要求的格式，如果有字段新增等请按算法团队提供的规则执
行。
{
"2d_object_annotated_info":{
"annotator":"标注员",
"annotation_qa":"质检员",
"annotation_date":"2022-08-05T15:44:40",
"check_no":"提验的日期，如 20231212，该值不可变更，一旦赋值必须保持唯一。",
"frame_id":"对应帧数据行的 FrameId",
"annotated_info":{}
}
1
2
3
4
5
6
7
8
9
4.1、线的类型​
类型​ 英⽂​
单虚线​ single_dash​
单实线​ single_solid​
胖虚线​ dense_wide_dash​
双实线​ double_solid​
双虚线​ double_dash​
左虚右实线​ left_dash_right_solid​
左实右虚线​ left_solid_right_dash​
路沿线​ road_edge​
其他类型​ others​
4.2、回标模式​
回标模式​ 标注类型​
CLIP​ AUTO_4D_LANE​
D3_LANE​
BEV_LANE_COMBINE​
CAMERA_BEV_LANE_COMBINE​
FRAME​ D2_LANE​
D3_BOX​
D2_TRAFFIC_LIGHT_KEY_FRAME​
D2_TRAFFIC_LIGHT_CONTINUOUS_FRAME​
M2_D3_CITY_OBJECT_DETECTION​
KEY_POINTS​
D3_CITY_OBJECT_DETECTION_WITH_FISH_EYE​
D3_HIGHWAY_OBJECT_DETECTION_WITH_FISH_EYE​
PARKING_MOVABLE_OBJECT_DETECTION​
CAMERA_BLOCKAGE​
ENVIRONMENT_IDENTIFICATION​
PARKING_FSLINE_2D​
CLIP+FRAME​ CAMERA_SLAM_LANE_COMBINE​
PARKING_SURROUND_SPACE_DETECTION​
PARKING_ROAD_SIGN_MARK​
DRIVING_FREE_SPACE​
GOP_OBJECT_DETECTION​
CITY_4D_LANE​
Others​ PARKING_FREE_SPACE
D3_DRIVING_SEGMENTATION​
4.2.1、单Frame​
1.每个批次的结果会打成⼀个zip包​
2.zip包内为每个帧的标注结果，⽂件路径为clipId+"/"+frameId+.json后缀:​
38d432c00bberraf888f096fckkkk480/38d432c00bberraf888f096fckkkk480.json
...
1
2
每个frame⽂件格式为JSON格式​
*******、2D物体检测/3D物体检测任务/2D⻋道线任务/3DBox任务/交通灯（连续帧
和关键帧/KeyPoints/3D鱼眼城市物体检测/3D鱼眼高速物体检测任务​
*******.1、23DObject高速物体检测​
标注数据入库红线 ​
{
"2d_object_annotated_info":{
"annotator":"标注员",
"annotation_qa":"质检员",
"annotation_date":"2022-08-05T15:44:40",
"check_no":"提验的日期，如 20231212，该值不可变更，一旦赋值必须保持唯一。",
"frame_id":"对应帧数据行的 FrameId",
"annotated_info":{
"camera0":{
"objects":[
{
"track_id":1,
"bbox":[
561.3565,
703.1243,
132.1052,
183.1258
],
"category":"car",
"lane_id":[
1
],
"is_crop":false,
"tire_line":[
[
-100,
-100
],
[
630.817610062893,
583.3962264150944
],
[
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
631.2903225806451,
701.774193548387
],
[
615.1612903225806,
729.6774193548387
]
],
"signal":"others"
}
]
}
}
},
"3d_object_annotated_info":{
"annotator":"标注员",
"annotation_qa":"质检员",
"annotation_date":"2022-08-05T15:44:40",
"check_no":"提验的日期，如 20231212，该值不可变更，一旦赋值必须保持唯一。",
"frame_id":"对应帧数据行的 FrameId",
"annotated_info":{
"3d_object_detection_info":{
"basic_info":{
"annotation_date":"2022-08-05T15:44:40"
},
"ext_info":{
"exception_flag":true
},
"3d_object_detection_anns_info":[
{
"size":[
2.1,
4.7,
1.8
],
"obj_center_pos":[
47.67774994506254,
1.0511472161155395,
-1.1871771747806774
],
"obj_rotation":[
0.9994878429621736,
4.885906248585606e-16,
9.55778573003732e-16,
0.03200080890885854
],
"category":"car",
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
"track_id":1,
"is_double_image":false,
"group_id": -1,
"is_group": false,
"num_lidar_pts":112,
"num_radar_pts":-1
},
{
"size":[
1.8,
4.6,
1.5
],
"obj_center_pos":[
-47.35474974162991,
-11.65969219370768,
-1.1378283150076802
],
"obj_rotation":[
0.9246076116489002,
-2.286960300198134e-16,
-3.6884522721598043e-16,
-0.3809209425601545
],
"category":"car",
"track_id":2,
"object_id":1,
"group_id": -1,
"is_group": false,
"is_double_image":true,
"num_lidar_pts":212,
"num_radar_pts":-1
}
]
}
}
}
}
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
//camera0-camera6,如果无目标物需为"cameraX":{"objects":[]}​
//无目标物，为"3d_object_detection_anns_info":[]​
*******.1.1、2D-box-L2格式要求​
标注内容红线：
• 标注结果json中必须有字段：track_id,bbox,category,is_crop,lane_id,​
• tire_line字段在 类
别"car","truck","pickup_truck","construction_vehicle","bus","motorcycle","tricycle","bicycle"类
别必须存在,类别"unknown"时,视情况而定​
• signal字段在 类别"car","truck","pickup_truck","construction_vehicle","bus","motorcycle",类别
必须存在,类别"unknown"时,视情况而定​
track_i
d​
bbox​ category​ is_crop​ lane_id​ tire_line​ signal​
格式​ int​ list；长
度必须
为4,成
员类型
为float​
str​ bool​ list;成
员类型
为int ​
list;长度必须为4,
成员float或者int,​
str​
值的范
围​
>0​ "person","an
imal","traffic
_cone","car",
"pickup_tru
ck","truck","
construction
_vehicle","b
us","motorc
ycle","tricycl
e","bicycle","
unknown"​
True/Fa
lse​
"left_turn",
"right_turn",
"brake",
"normal",
"unknown"​
字段说
明​
物体标
号，同
一物体
前后帧
应一
致，2d
框和3d
框应一
致​
2d框位
置
[x_c,y_
c,w,h]​
2d框中
心点坐
标，目
标框宽
度与高
度​
物体类别​ 是否遮
挡​
车道线
标号​
障碍物接地点[x,y]​ 转向灯​
特殊案
例​
成员是int时,只有
[-100,-100],表示不
可见​
*******.1.2、3D-box-L2格式要求​
标注内容红线：
• 标注结果json中必须有字段：track_id,obj_center_pos,size,obj_rotation,category,
is_double_image,num_lidar_pts,num_radar_pts,exception_flag​
• track_id与2dbox中同一物体的track_id必须一致​
track
_id​
obj_c
enter
_pos​
category​ size​ obj_r
otatio
n​
num_
lidar_
pts​
num_
radar
_pts​
is_do
uble_
imag
e​
excep
tion_f
lag​
is_gr
oup​
group
_id​
格式​ int​ list；
长度
必须
为3,
成员
类型
为
float​
str​ list；
长度
必须
为3,
成员
类型
为
float​
list；
长度
必须
为4,
成员
类型
为
float​
int​ int​ bool​ bool​ bool​ int​
值的
范围​
>0​ "person","
animal","t
raffic_con
e","car",
"pickup_t
ruck","tru
ck","const
ruction_v
ehicle","b
us","moto
rcycle","tr
icycle","bi
cycle","un
known"​
>0​ >5​ -1​ True/
False​
True/
False​
默认
值
Flase​
默认
值 -1​
字段
说明​
物体
标
号，
同一
物体
中心
点x、
y、z​
物体类别​ 物体
长
度、
宽
四元
数XY
ZW​
物体
激光
雷达
物体
毫米
波雷
达点
物体
是否
有重
影​
点云
是否
异常​
物体
前后
帧应
一
致，
2d框
和3d
框应
一致​
度、
高度​
点个
数​
个
数，
默认
值-1​
画面
里点
云出
现重
影、
点云
在地
平线
下、
点云
实际
是玻
璃上
的镜
像​
特殊
案例​
*******.2、2DLane​
需同时返回Camera0、Camera1的标注结果​
{
"2d_lane_annotated_info": {
"annotator":"标注员",
"annotation_qa":"质检员",
"check_no":"clipId",
"annotation_date":"2022-08-05T15:44:40", 定义：导出的时间
"frame_id":"对应帧数据行的 FrameId",
"annotated_info":{
"camera0":{
"lines":[
{
"line_key_points":[
[
1458,
652.9
]
],
"dash_line_start_end":[
],
"type":"single_solid",
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
"color":"white",
"has_fishbone":false
},
{
"line_key_points":[
[
1462.3,
656.9
]
],
"dash_line_start_end":[
],
"type":"road_edge",
"color":"yellow",
"has_fishbone":false
},
{
"line_key_points":[
[
1455.3,
603.1
]
],
"dash_line_start_end":[
[
[
1220.5,
710.7
],
true,
[
1212.6,
702.7
],
true
]
],
"type":"single_dash",
"color":"yellow",
"has_fishbone":false
},
{
"line_key_points":[
[
1472.4,
608.1
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
]
],
"dash_line_start_end":[
[
[
1233.3,
710.5
],
true,
[
1229,
702.9
],
true
]
],
"type":"single_dash",
"color":"yellow",
"has_fishbone":false
}
],
"num_lanes_left_right":[
1,
1
]
},
"camera1":{
"lines":[
{
"line_key_points":[
[
1458,
652.9
]
],
"dash_line_start_end":[
],
"type":"single_solid",
"color":"white",
"has_fishbone":false
},
{
"line_key_points":[
[
1462.3,
656.9
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
]
],
"dash_line_start_end":[
],
"type":"road_edge",
"color":"yellow",
"has_fishbone":false
},
{
"line_key_points":[
[
1455.3,
603.1
]
],
"dash_line_start_end":[
[
[
1220.5,
710.7
],
true,
[
1212.6,
702.7
],
true
]
],
"type":"single_dash",
"color":"yellow",
"has_fishbone":false
},
{
"line_key_points":[
[
1472.4,
608.1
],
[
1233.3,
710.5
],
[
1229,
702.9
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
]
],
"dash_line_start_end":[
[
[
1233.3,
710.5
],
true,
[
1229,
702.9
],
true
]
],
"type":"single_dash",
"color":"yellow",
"has_fishbone":false
}
],
"num_lanes_left_right":[
1,
1
]
}
}
}
}
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
//camera0-camera1,如果无目标物需为"cameraX":{"lines":[]}​
*******.2.1、2D-Lane格式要求​
标注内容红线：
• 标注结果json中必须有字段：lines，line_key_points，dash_line_start_end，type，color，
has_fishbone，num_lanes_left_right​
lines​ line_key_
points​
dash_line_start
_end​
type color​ has_fishb
one​
num_lan
es_left_ri
ght​
格式​ list​ list；且必
须两个以
上​
list​ str​ str​ bool​ list，且只
有两个值,
每个值都
是int​
层级​ In
annotate
d_info​
Inline​ Inline​ Inline​ Inline​ Inline​ In
annotate
d_info​
值的范围​ lines，
line_key_
points，
dash_line
_start_en
d，type，
color，
has_fishb
one​
'single_d
ash','singl
e_solid','d
ense_wid
e_dash','d
ouble_sol
id',​
'double_d
ash','left_
dash_righ
t_solid','le
ft_solid_r
ight_dash
','road_ed
ge','others
','stop_la
ne','road_
guide_lan
e','virtual
_road_ed
ge'​
'yellow','
white','ot
hers'​
True/Fals
e​
特殊案例​ • 如果type是
single_solid
，
double_soli
d，
road_edge
，others，
则
dash_line_st
art_end不可
有点；反
之，必须有
点​
road_edg
e的颜色只
能
是'yellow',
'others'​
值为
[0,0]，或
者包含-1
可能会有
问题​
• 点必须在线上，也必须在图像中
• 虚线的dash_line_start_end必须是一一对应的​
• 必须由近及远标注
• 车道数量num_lanes_left_right的参数前后两帧如果变化可能会有问题；line_key_points，
dash_line_start_end，type，color前后两帧发生变化可能会有问题（做了排序）​
• 一变多多变一的车道线会有一个交界处，其中下面车道线的结束点和上面车道线的起点≤2像素​
*******.3、3DBox​
{
"3d_box_annotated_info":{
"annotator":"标注员",
"annotation_date":"2022-08-05T15:44:40",
"annotation_qa":"质检员",
"frame_id":"对应帧数据行的 FrameId",
"check_no":"提验的日期，如 20231212，该值不可变更，一旦赋值必须保持唯一。",
"annotated_info":{
"3d_object_detection_info":{
"basic_info":{
"annotation_date":"2022-08-05T15:44:40"
},
"ext_info":{
"exception_flag":true
},
"3d_object_detection_anns_info":[
{
"size":[
4.6,
1.8,
1.5
],
"obj_center_pos":[
47.67774994506254,
1.0511472161155395,
-1.1871771747806774
],
"obj_rotation":[
0.9994878429621736,
4.885906248585606e-16,
9.55778573003732e-16,
0.03200080890885854
],
"category":"car",
"object_id":1,
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
"group_id":1,
"is_group": false,
"is_double_image":false,
"num_lidar_pts":112,
"num_radar_pts":-1
},
{
"size":[
7,
2.8,
3
],
"obj_center_pos":[
-47.35474974162991,
-11.65969219370768,
-1.1378283150076802
],
"obj_rotation":[
0.9246076116489002,
-2.286960300198134e-16,
-3.6884522721598043e-16,
-0.3809209425601545
],
"category":"truck",
"object_id":2,
"group_id":2,
"is_group": false,
"is_double_image":true,
"num_lidar_pts":212,
"num_radar_pts":-1
},
{
"size":[
3,
2.8,
3
],
"obj_center_pos":[
-47.35474974162991,
-11.65969219370768,
-1.1378283150076802
],
"obj_rotation":[
0.9246076116489002,
-2.286960300198134e-16,
-3.6884522721598043e-16,
-0.3809209425601545
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
],
"category":"truck",
"object_id":11,
"group_id":2,
"is_group": false,
"is_double_image":true,
"num_lidar_pts":212,
"num_radar_pts":-1
}
]
}
}
}
}
83
84
85
86
87
88
89
90
91
92
93
94
95
96
//无目标物，为"3d_object_detection_anns_info":[]​
*******.3.1、部分字段顺序要求​
• size：lwh​
• obj_center_pos：xyz​
• obj_rotation：xyzw​
• 群体is_group的属性，要求是只有行人、交通锥、障碍物、摩托车、自行车这五类可能是true，其
他默认false（必须有这个字段）​
• is_double_image，is_group,group_id三个字段也是要求每个框都有，is_double_image，
is_group值为false/true；group_id默认和object_id一致，除非是同一车因头身方向不一致分两个
框标时group_id按规则要求来设置​
*******.4、(5) 2D红绿灯关键帧D2_TRAFFIC_LIGHT_KEY_FRAME​
*******.4.1格式要求：​
id​ trac
k_id​
x​ y​ widt
h​
heig
ht​
bbox
_cla
ss​
colo
r​
shap
e​
have
_nu
mbe
r​
boa
der_
type
boa
der_
quali
ty​
shap
e_qu
ality​
co
td
w
含义​ 唯一
编码​
跟踪
id​
左上
角点
x坐
标​
左上
角点
y坐
标​
框的
宽​
框的
高​
框属
性​
颜色​ 类型​ 数字
属性​
边框
类型​
边框
质量​
类型
质量​
倒
时
值
格式​ str​ int​ float​ float​ float​ float​ str​ str​ str​ str​ str​ str​ str​ st
层级​
值的
范围​
>0​ "non
_rela
ted",
"atte
ntio
n_cu
rrent
",​
"atte
ntio
n_fu
ture"
,
"no_
atte
ntio
n",
"con
fuse
d"​
"0",
"1",
"2",
"3",
"4",
"5"​
"0",
"1",
"2",
"3",..
.,
"21"​
"0",
"1"​
"0",
"1",
"2",
"3"​
100,"
50","
0"​
100,"
50","
0"​
"-
"-
"-
"0
1"
",.
98
99
特殊
案例​
-3
A,
支
真
-2
障
字
-1
值
无
字
0-
为
示
字
*******.4.2json示例：​
{
"2d_traffic_light_key_frame_annotated_info": {
"annotator":"标注员",
"annotation_qa":"质检员",
"check_no":"clipId",
"annotation_date":"2022-08-05T15:44:40",
"frame_id":"对应帧数据行的 FrameId",
"annotated_info":{
"camera0":{
"width": 1408,
"height": 792,
"objects": [
{
"id": "411fdc78",
"track_id": 1,
"x": 2812.1956,
"y": 246.6994,
"width": 138.4377,
"height": 290.7783,
"bbox_class": "non_related",
"color": "",
"shape": "",
"have_number": "",
"boader_type": "",
"boader_quality":"",
"shape_quality":"",
"countdown":"",
"visiable": ["camera0","camera1"]
},
{
"id": "UCOHelww",
"track_id": 1,
"X": 2812.1956,
"y": 246.6994,
"width": 138.4377,
"height": 290.7783,
"bbox_class": "non_related",
"color": "",
"shape": "",
"have_number": "",
"boader_type": "",
"boader_quality":"",
"shape_quality":"",
"countdown":"",
"visiable": ["camera0","camera1"]
}
]
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
},
"camera1":{
"width": 1408,
"height": 792,
"objects": [
{
"id": "411fdc79",
"track_id": 1,
"x": 2812.1956,
"y": 246.6994,
"width": 138.4377,
"height": 290.7783,
"bbox_class": "non_related",
"color": "",
"shape": "",
"have_number": "",
"boader_type": "",
"boader_quality":"",
"shape_quality":"",
"countdown":"",
"visiable": ["camera0","camera1"]
},
{
"id": "UCOHelw9",
"track_id": 1,
"X": 2812.1956,
"y": 246.6994,
"width": 138.4377,
"height": 290.7783,
"bbox_class": "non_related",
"color": "",
"shape": "",
"have_number": "",
"boader_type": "",
"boader_quality":"",
"shape_quality":"",
"countdown":"",
"visiable": ["camera0","camera1"]
}
]
}
}
}
}
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
*******.5、(6)2D红绿灯连续帧D2_TRAFFIC_LIGHT_CONTINUOUS_FRAME​
{
"2d_traffic_light_continuous_frame_annotated_info": {
"annotator":"标注员",
"annotation_qa":"质检员",
"check_no":"clipId",
"annotation_date":"2022-08-05T15:44:40",
"frame_id":"对应帧数据行的 FrameId",
"annotated_info":{
"camera0":{
"width": 1408,
"height": 792,
"objects": [
{
"id": "411fdc78",
"track_id": 1,
"x": 2812.1956,
"y": 246.6994,
"width": 138.4377,
"height": 290.7783,
"bbox_class": "non_related",
"color": "",
"shape": "",
"have_number": "",
"boader_type": "",
"boader_quality":"",
"shape_quality":"",
"countdown":"",
"visiable": ["camera0","camera1"]
},
{
"id": "UCOHelww",
"track_id": 1,
"X": 2812.1956,
"y": 246.6994,
"width": 138.4377,
"height": 290.7783,
"bbox_class": "non_related",
"color": "",
"shape": "",
"have_number": "",
"boader_type": "",
"boader_quality":"",
"shape_quality":"",
"countdown":"",
"visiable": ["camera0","camera1"]
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
]
},
"camera1":{
"width": 1408,
"height": 792,
"objects": [
{
"id": "411fdc79",
"track_id": 1,
"x": 2812.1956,
"y": 246.6994,
"width": 138.4377,
"height": 290.7783,
"bbox_class": "non_related",
"color": "",
"shape": "",
"have_number": "",
"boader_type": "",
"boader_quality":"",
"shape_quality":"",
"countdown":"",
"visiable": ["camera0","camera1"]
},
{
"id": "UCOHelw9",
"track_id": 1,
"X": 2812.1956,
"y": 246.6994,
"width": 138.4377,
"height": 290.7783,
"bbox_class": "non_related",
"color": "",
"shape": "",
"have_number": "",
"boader_type": "",
"boader_quality":"",
"shape_quality":"",
"countdown":"",
"visiable": ["camera0","camera1"]
}
]
}
}
}
}
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
*******.6、23DCityObjectDetection23D城区物体检测​
标注数据入库红线 ​
{
"2d_city_object_detection_annotated_info":{
"annotator":"标注员",
"annotation_qa":"质检员",
"annotation_date":"2022-08-05T15:44:40",
"check_no":"提验的日期，如 20231212，该值不可变更，一旦赋值必须保持唯一。",
"frame_id":"对应帧数据行的 FrameId",
"semantic_version":"V123123",
"annotated_info":{
"camera0":{
"objects":[
{
"track_id":1,
"bbox":[
561.3565,
703.1243,
132.1052,
183.1258
],
"category":"car",
"subcategory":"",
"lane_id":[
1
],
"is_crop":false,
"tire_line":[
[
-100,
-100
],
[
630.817610062893,
583.3962264150944
],
[
631.2903225806451,
701.774193548387
],
[
615.1612903225806,
729.6774193548387
]
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
],
"signal":"others"
}
]
}
}
},
"3d_city_object_detection_annotated_info":{
"annotator":"标注员",
"annotation_qa":"质检员",
"annotation_date":"2022-08-05T15:44:40",
"check_no":"提验的日期，如 20231212，该值不可变更，一旦赋值必须保持唯一。",
"frame_id":"对应帧数据行的 FrameId",
"semantic_version":"V123123",
"annotated_info":{
"3d_object_detection_info":{
"basic_info":{
"annotation_date":"2022-08-05T15:44:40"
},
"ext_info":{
"exception_flag":true
},
"3d_object_detection_anns_info":[
{
"size":[
2.1,
4.7,
1.8
],
"obj_center_pos":[
47.67774994506254,
1.0511472161155395,
-1.1871771747806774
],
"obj_rotation":[
0.9994878429621736,
4.885906248585606e-16,
9.55778573003732e-16,
0.03200080890885854
],
"category":"car",
"subcategory":"",
"track_id":1,
"is_double_image":false,
"group_id": -1,
"is_group": false,
"num_lidar_pts":112,
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
"num_radar_pts":-1
},
{
"size":[
1.8,
4.6,
1.5
],
"obj_center_pos":[
-47.35474974162991,
-11.65969219370768,
-1.1378283150076802
],
"obj_rotation":[
0.9246076116489002,
-2.286960300198134e-16,
-3.6884522721598043e-16,
-0.3809209425601545
],
"category":"car",
"subcategory":"",
"track_id":2,
"group_id": -1,
"is_group": false,
"is_double_image":true,
"num_lidar_pts":212,
"num_radar_pts":-1
}
]
}
}
}
}
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
2.2.1.1.6.1、2D-box格式要求​
标注内容红线：
• 标注结果json中必须有字段：track_id,group_id，bbox,category,subcategory，is_crop,
lane_id,is_group​
• tire_line字段在 类
别"car","truck","pickup_truck","construction_vehicle","bus","motorcycle","tricycle","bi
cycle"类别必须存在,类别"unknown"时,视情况而定​
• signal字段在 类
别"car","truck","pickup_truck","construction_vehicle","bus","motorcycle",类别必须存在,
类别"unknown"时,视情况而定​
track
_id​
grou
p_id​
bbox​ category​ subc
atego
ry​
is_cr
op​
lane_
id​
tire_line​ signal​ is_gr
oup​
格式​ int​ int​ list；
长度
必须
为4,
成员
类型
为
float​
str​ str​ bool​ list;
成员
类型
为int ​
list;长度必须
为4,成员float
或者int,​
str​ bool​
值的
范围​
>0​ >0​ "car","pic
kup_truc
k","truck",
"construc
tion_vehi
cle","bus",
"recreatio
nal_vehic
le","moto
rcycle","tr
icycle","bi
cycle","pe
rson","ani
mal","traf
fic_cone",
"barrier","
traffic_w
arning","u
nknown"​
"car",
"pick
up_tr
uck","
truck
","con
struct
ion_v
ehicl
e","b
us","r
ecrea
tiona
l_veh
icle","
moto
rcycl
e","tri
cycle
","bic
ycle",
"pers
on","
anim
al","tr
True/
False​
-3到
10
和-10
0​
"left_tur
n",
"right_t
urn",
"brake",
"normal
",
"unkno
wn",​
"double
_flash",​
""​
True/
False​
affic_
cone"
,"barr
ier","t
raffic
_war
ning",
"unk
nown
"​
van,p
ickup
_bed
_truc
k,trail
er,bo
x_tru
ck,no
rm_b
ox_tr
uck,s
p_bo
x_tru
ck,cr
ane,c
emen
t_mix
er,wa
terin
g_car
t,spe
cial_c
lothe
s​
""​
字段
说明​
物体
标
号，
同一
物体
前后
帧应
一
致，
每个
框都
有，
大部
分的
值默
认为
track
2d框
位置
[x_c,y
_c,w,
h]​
2d框
中心
点坐
物体类别:
小型车，
皮卡，卡
车，工程
车，公交
车，房
车，摩托
车，自行
车，行
一级
属性​
二级
属
性：​
小型
车：
是否
遮挡​
车道
线标
号​
障碍物接地点
[x,y]​
转向灯​
行人、交
通锥、障
碍物等
给""​
未知、三
轮车可能
有信号灯
所有
框都
要求
有这
个字
段​
2d框
和3d
框应
一致​
_id的
值​
标，
目标
框宽
度与
高度​
人，动
物，交通
锥，障碍
物，未知​
面包
车；​
卡
车：
车斗
型卡
车、
平板
卡
车、
箱式
货
车、
标准
箱
式、
非标
准箱
式​
工程
车：
吊
车、
水泥
搅拌
车、
洒水
车​
行
人：
特殊
服饰​
2024.
03.06
规则
更
新，
无需
标注
二级
属
性，
但仍
保留
正常
给"left_t
urn",
"right_t
urn",
"brake",
"normal
",
"unkno
wn","do
uble_fla
sh"；没
有信号灯
时给""​
subc
atego
ry这
个字
段​
特殊
案例​
box_t
ruck
因规
则更
新后
续会
删除​
成员是int时,只
有[-100,-100],
表示不可见​
2.2.1.1.6.2、3D-box格式要求​
标注内容红线：
• 标注结果json中必须有字段：track_id,group_id，obj_center_pos,size,obj_rotation,
category,subcategory，is_double_image,num_lidar_pts,num_radar_pts,
exception_flag​
• track_id与2dbox中同一物体的track_id必须一致​
trac
k_id​
grou
p_id​
obj_
cent
er_p
os​
cate
gory​
subc
ateg
ory​
size​ obj_
rotat
ion​
num
_lida
r_pt
s​
num
_rad
ar_p
ts​
is_d
oubl
e_im
age​
exce
ptio
n_fla
g​
is_gr
oup​
Sem
Ver​
格式​ int​ int​ list
；长
度必
须为
3,成
员类
型为
float​
str​ str​ list
；长
度必
须为
3,成
员类
型为
float​
list
；长
度必
须为
4,成
员类
型为
float​
int​ int​ bool​ bool​ bool​ str​
值的
范围​
>0​ >0​ "car"
,"pic
kup_
truc
k","tr
uck",
"car"
,"pic
kup_
truc
k","tr
uck",
>0​ >0​ -1​ True
/Fals
e​
True
/Fals
e​
True
/Fals
e​
"con
struc
tion
_veh
icle",
"bus
","re
creat
ional
_veh
icle",
"mot
orcy
cle","
tricy
cle","
bicy
cle","
pers
on","
ani
mal",
"traf
fic_c
one",
"bar
rier",
"traf
fic_
warn
ing",
"unk
now
n"​
"con
struc
tion
_veh
icle",
"bus
","re
creat
ional
_veh
icle",
"mot
orcy
cle","
tricy
cle","
bicy
cle","
pers
on","
ani
mal",
"traf
fic_c
one",
"bar
rier",
"traf
fic_
warn
ing",
"unk
now
n"​
van,
pick
up_
bed_
truc
k,tra
iler,b
ox_t
ruck,
nor
m_b
ox_t
ruck,
sp_b
ox_t
ruck,
cran
e,ce
men
t_mi
xer,
wate
ring
_car
t,spe
cial_
clot
hes​
字段
说明​
物体
标
号，
同一
物体
前后
帧应
一
致，
2d框
和3d
框应
一致​
每个
框都
有，
大部
分的
值默
认为
trac
k_id
的值​
物体
中心
点
x、
y、z​
物体
类别:
小型
车，
皮
卡，
卡
车，
工程
车，
公交
车，
房
车，
摩托
车，
自行
车，
行
人，
动
物，
交通
锥，
障碍
物，
未知​
一级
属性​
二级
属
性：​
小型
车：
面包
车；​
卡
车：
车斗
型卡
车、
平板
卡
车、
箱式
货
车、
标准
箱
式、
非标
准箱
式​
物体
长
度、
宽
度、
高度​
四元
数X
YZ
W​
物体
激光
雷达
点个
数​
物体
毫米
波雷
达点
个
数，
默认
值-1​
物体
是否
有重
影​
点云
是否
异常​
画面
里点
云出
现重
影、
点云
在地
平线
下、
点云
实际
是玻
璃上
的镜
像​
所有
框都
要求
有这
个字
段​
clip
维度
的字
段，
规则
版本
号​
工程
车：
吊
车、
水泥
搅拌
车、
洒水
车​
行
人：
特殊
服饰​
特殊
案例​
box_
truc
k因
规则
更新
后续
会删
除​
• 静止车辆方向保持不变
• 70米范围内无错误，包括漏标、不贴合、方向可见的不正确、重影标注不正确、track_id错误等​
• 同一个ID大小保持一致（误差不超过0.05米）​
• 四周和地线贴合要求不超过1个像素，上边缘贴合要求不超过2个像素；车辆噪点/后视镜不标注，
以实际车的边缘贴合。
后视镜不标
*******.7、M23DCityObjectDetection（M23D城区物体检测）​
标注数据入库红线 ​
*******.7.1、示例JSON​
{
"m2_3d_city_object_detection_annotated_info":{
"annotator":"标注员",
"annotation_qa":"质检员",
"annotation_date":"2022-08-05T15:44:40",
"check_no":"提验的日期，如 20231212，该值不可变更，一旦赋值必须保持唯一。",
"frame_id":"对应帧数据行的 FrameId",
"semantic_version":"V123123",
"annotated_info":{
"3d_object_detection_info":{
"basic_info":{
"annotation_date":"2022-08-05T15:44:40"
},
"ext_info":{
"exception_flag":true
},
"3d_object_detection_anns_info":[
{
"size":[
2.1,
4.7,
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
1.8
],
"obj_center_pos":[
47.67774994506254,
1.0511472161155395,
-1.1871771747806774
],
"obj_rotation":[
0.9994878429621736,
4.885906248585606e-16,
9.55778573003732e-16,
0.03200080890885854
],
"category":"car",
"subcategory":"",
"track_id":1,
"group_id": -1,
"is_double_image":false,
"is_group": false,
"num_lidar_pts":112,
"num_radar_pts":-1
},
{
"size":[
1.8,
4.6,
1.5
],
"obj_center_pos":[
-47.35474974162991,
-11.65969219370768,
-1.1378283150076802
],
"obj_rotation":[
0.9246076116489002,
-2.286960300198134e-16,
-3.6884522721598043e-16,
-0.3809209425601545
],
"category":"car",
"subcategory":"",
"track_id":2,
"group_id": -1,
"is_group": false,
"is_double_image":true,
"num_lidar_pts":212,
"num_radar_pts":-1
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
}
]
}
}
}
}
69
70
71
72
73
74
*******.7.2、3D-box格式要求​
• 标注结果json中必须有字段：track_id,group_id，obj_center_pos,category,subcategory,
size,obj_rotation,num_lidar_pts,num_radar_pts,is_double_image,exception_flag，
is_group，SemVer​
trac
k_id​
grou
p_id​
obj_
cent
er_p
os​
cate
gory​
subc
ateg
ory​
size​ obj_
rotat
ion​
num
_lida
r_pt
s​
num
_rad
ar_p
ts​
is_d
oubl
e_im
age​
exce
ptio
n_fla
g​
is_gr
oup​
Sem
Ver​
格式​ int​ int​ list
；长
度必
须为
3,成
员类
型为
float​
str​ str​ list
；长
度必
须为
3,成
员类
型为
float​
list
；长
度必
须为
4,成
员类
型为
float​
int​ int​ bool​ bool​ bool​ str​
值的
范围​
>0​ >0​ "car"
,"pic
kup_
truc
k","tr
uck",
"con
struc
tion
_veh
icle",
"bus
","re
creat
"car"
,"pic
kup_
truc
k","tr
uck",
"con
struc
tion
_veh
icle",
"bus
","re
creat
>0​ >0​ -1​ True
/Fals
e​
True
/Fals
e​
True
/Fals
e​
ional
_veh
icle",
"mot
orcy
cle","
tricy
cle","
bicy
cle","
pers
on","
ani
mal",
"traf
fic_c
one",
"bar
rier",
"traf
fic_
warn
ing",
"unk
now
n"​
ional
_veh
icle",
"mot
orcy
cle","
tricy
cle","
bicy
cle","
pers
on","
ani
mal",
"traf
fic_c
one",
"bar
rier",
"traf
fic_
warn
ing",
"unk
now
n"​
van,
pick
up_
bed_
truc
k,tra
iler,b
ox_t
ruck,
nor
m_b
ox_t
ruck,
sp_b
ox_t
ruck,
cran
e,ce
men
t_mi
xer,
wate
ring
_car
t,spe
cial_
clot
hes​
字段
说明​
物体
标
号，
同一
物体
前后
帧应
一
致，
2d框
和3d
框应
一致​
每个
框都
有，
大部
分的
值默
认为
trac
k_id
的值​
物体
中心
点
x、
y、z​
物体
类别:
小型
车，
皮
卡，
卡
车，
工程
车，
公交
车，
房
车，
摩托
车，
自行
车，
行
人，
动
物，
交通
锥，
障碍
物，
未知​
一级
属性​
二级
属
性：​
小型
车：
面包
车；​
卡
车：
车斗
型卡
车、
平板
卡
车、
箱式
货
车、
标准
箱
式、
非标
准箱
式​
工程
车：
吊
车、
水泥
搅拌
车、
物体
长
度、
宽
度、
高度​
四元
数X
YZ
W​
物体
激光
雷达
点个
数​
物体
毫米
波雷
达点
个
数，
默认
值-1​
物体
是否
有重
影​
点云
是否
异常​
画面
里点
云出
现重
影、
点云
在地
平线
下、
点云
实际
是玻
璃上
的镜
像​
所有
框都
要求
有这
个字
段​
clip
维度
的字
段，
规则
版本
号​
洒水
车​
行
人：
特殊
服饰​
特殊
案例​
box_
truc
k因
规则
更新
后续
会删
除​
• 静止车辆方向保持不变
• 70米范围内无错误，包括漏标、不贴合、方向可见的不正确、重影标注不正确、track_id错误等​
• 同一个ID大小保持一致（误差不超过0.05米）​
• 四周和地线贴合要求不超过1个像素，上边缘贴合要求不超过2个像素；车辆噪点/后视镜不标注，
以实际车的边缘贴合。
后视镜不标
*******.8、Only3DCityObjectDetection（城区物体检测仅3D）​
标注数据入库红线 ​
*******.8.1、示例JSON​
{
"only_3d_city_object_detection_annotated_info":{
"annotator":"标注员",
"annotation_qa":"质检员",
"annotation_date":"2022-08-05T15:44:40",
"check_no":"提验的日期，如 20231212，该值不可变更，一旦赋值必须保持唯一。",
"frame_id":"对应帧数据行的 FrameId",
"semantic_version":"V123123",
"annotated_info":{
"3d_object_detection_info":{
"basic_info":{
"annotation_date":"2022-08-05T15:44:40"
},
"ext_info":{
"exception_flag":true
},
"3d_object_detection_anns_info":[
{
"size":[
2.1,
4.7,
1.8
],
"obj_center_pos":[
47.67774994506254,
1.0511472161155395,
-1.1871771747806774
],
"obj_rotation":[
0.9994878429621736,
4.885906248585606e-16,
9.55778573003732e-16,
0.03200080890885854
],
"category":"car",
"subcategory":"car",
"track_id":1,
"group_id":1,
"is_double_image":false,
"is_group":true,
"lane_id":[
2
],
"is_crop":false,
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
"signal":"normal",
"num_lidar_pts":112,
"num_radar_pts":-1
},
{
"size":[
1.8,
4.6,
1.5
],
"obj_center_pos":[
-47.35474974162991,
-11.65969219370768,
-1.1378283150076802
],
"obj_rotation":[
0.9246076116489002,
-2.286960300198134e-16,
-3.6884522721598043e-16,
-0.3809209425601545
],
"category":"car",
"subcategory":"car",
"track_id":2,
"group_id": -1,
"is_double_image":false,
"is_group":true,
"lane_id":[
2
],
"is_crop":false,
"signal":"normal",
"num_lidar_pts":212,
"num_radar_pts":-1
}
]
}
}
}
}
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
*******.8.2、3D-box-L2格式要求​
• 标注结果json中必须有字段：track_id,group_id，obj_center_pos,category,subcategory,
size,obj_rotation,num_lidar_pts,num_radar_pts,is_double_image,exception_flag，
is_group，SemVer,is_crop，lane_id，signal​
trac
k_id​
grou
p_id​
obj_
cent
er_p
os​
cate
gory​
subc
ateg
ory​
size​ obj_
rotat
ion​
num
_lida
r_pt
s​
num
_rad
ar_p
ts​
is_d
oubl
e_im
age​
exce
ptio
n_fla
g​
is_gr
oup​
Sem
Ver​
is
op
格式​ int​ int​ list
；长
度必
须为
3,成
员类
型为
float​
str​ str​ list
；长
度必
须为
3,成
员类
型为
float​
list
；长
度必
须为
4,成
员类
型为
float​
int​ int​ bool​ bool​ bool​ str​ bo
值的
范围​
>0​ >0​ "car"
,"pic
kup_
truc
k","tr
uck",
"con
struc
tion
_veh
icle",
"bus
","re
creat
ional
_veh
icle",
"mot
orcy
cle","
tricy
cle","
bicy
cle","
pers
on","
ani
"car"
,"pic
kup_
truc
k","tr
uck",
"con
struc
tion
_veh
icle",
"bus
","re
creat
ional
_veh
icle",
"mot
orcy
cle","
tricy
cle","
bicy
cle","
pers
on","
ani
>0​ >0​ -1​ True
/Fals
e​
True
/Fals
e​
True
/Fals
e​
Tr
/F
e​
mal",
"traf
fic_c
one",
"bar
rier",
"traf
fic_
warn
ing",
"unk
now
n"​
mal",
"traf
fic_c
one",
"bar
rier",
"traf
fic_
warn
ing",
"unk
now
n"​
van,
pick
up_
bed_
truc
k,tra
iler,b
ox_t
ruck,
nor
m_b
ox_t
ruck,
sp_b
ox_t
ruck,
cran
e,ce
men
t_mi
xer,
wate
ring
_car
t,spe
cial_
clot
hes​
""​
字段
说明​
物体
标
每个
框都
物体
中心
物体
类别:
一级
属性​
物体
长
四元
数X
物体
激光
物体
毫米
物体
是否
点云
是否
所有
框都
clip
维度
是
遮
号，
同一
物体
前后
帧应
一
致，
2d框
和3d
框应
一致​
有，
大部
分的
值默
认为
trac
k_id
的值​
点
x、
y、z​
小型
车，
皮
卡，
卡
车，
工程
车，
公交
车，
房
车，
摩托
车，
自行
车，
行
人，
动
物，
交通
锥，
障碍
物，
未知​
二级
属
性：​
小型
车：
面包
车；​
卡
车：
车斗
型卡
车、
平板
卡
车、
箱式
货
车、
标准
箱
式、
非标
准箱
式​
工程
车：
吊
车、
水泥
搅拌
车、
洒水
车​
行
人：
特殊
服饰​
2024
.03.0
6规
则更
新，
无需
度、
宽
度、
高度​
YZ
W​
雷达
点个
数​
波雷
达点
个
数，
默认
值-1​
有重
影​
异常​
画面
里点
云出
现重
影、
点云
在地
平线
下、
点云
实际
是玻
璃上
的镜
像​
要求
有这
个字
段​
的字
段，
规则
版本
号​
标注
二级
属
性，
但仍
保留
subc
ateg
ory
这个
字段​
特殊
案例​
box_
truc
k因
规则
更新
后续
会删
除​
• 静止车辆方向保持不变
• 70米范围内无错误，包括漏标、不贴合、方向可见的不正确、重影标注不正确、track_id错误等​
• 同一个ID大小保持一致（误差不超过0.05米）​
• 四周和地线贴合要求不超过1个像素，上边缘贴合要求不超过2个像素；车辆噪点/后视镜不标注，
以实际车的边缘贴合。
后视镜不标
*******.9、23DObjectDetection（高速物体检测仅3D）​
*******.9.1、示例JSON​
{
"3d_object_detection_annotated_info":{
"annotator":"标注员",
"annotation_qa":"质检员",
"annotation_date":"2022-08-05T15:44:40",
"check_no":"提验的日期，如 20231212，该值不可变更，一旦赋值必须保持唯一。",
"frame_id":"对应帧数据行的 FrameId",
"semantic_version":"V123123",
"annotated_info":{
"3d_object_detection_info":{
"basic_info":{
1
2
3
4
5
6
7
8
9
10
11
"annotation_date":"2022-08-05T15:44:40"
},
"ext_info":{
"exception_flag":true
},
"3d_object_detection_anns_info":[
{
"size":[
4.6,
1.8,
1.5
],
"obj_center_pos":[
47.67774994506254,
1.0511472161155395,
-1.1871771747806774
],
"obj_rotation":[
0.9994878429621736,
4.885906248585606e-16,
9.55778573003732e-16,
0.03200080890885854
],
"category":"car",
"subcategory":"van",
"track_id":1,
"group_id":1,
"is_double_image":false,
"is_group":true,
"lane_id":[
2
],
"is_crop":false,
"signal":"normal",
"num_lidar_pts":112,
"num_radar_pts":-1
},
{
"size":[
7,
2.8,
3
],
"obj_center_pos":[
-47.35474974162991,
-11.65969219370768,
-1.1378283150076802
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
],
"obj_rotation":[
0.9246076116489002,
-2.286960300198134e-16,
-3.6884522721598043e-16,
-0.3809209425601545
],
"category":"truck",
"subcategory":"truck",
"track_id":2,
"group_id":2,
"is_double_image":true,
"is_group":false,
"lane_id":[
2
],
"is_crop":false,
"signal":"normal",
"num_lidar_pts":212,
"num_radar_pts":-1
},
{
"size":[
3,
2.8,
3
],
"obj_center_pos":[
-47.35474974162991,
-11.65969219370768,
-1.1378283150076802
],
"obj_rotation":[
0.9246076116489002,
-2.286960300198134e-16,
-3.6884522721598043e-16,
-0.3809209425601545
],
"category":"truck",
"subcategory":"truck",
"track_id":3,
"group_id":2,
"is_double_image":false,
"is_group":false,
"lane_id":[
2
],
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
"is_crop":false,
"signal":"normal",
"num_lidar_pts":212,
"num_radar_pts":-1
}
]
}
}
}
}
106
107
108
109
110
111
112
113
114
115
*******.9.2、3DBox要求​
• 标注结果json中必须有字段：track_id,group_id，obj_center_pos,category,subcategory,
size,obj_rotation,num_lidar_pts,num_radar_pts,is_double_image,exception_flag，
is_group，SemVer,is_crop，lane_id，signal​
trac
k_id​
grou
p_id​
obj_
cent
er_p
os​
cate
gory​
subc
ateg
ory​
size​ obj_
rotat
ion​
num
_lida
r_pt
s​
num
_rad
ar_p
ts​
is_d
oubl
e_im
age​
exce
ptio
n_fla
g​
is_gr
oup​
Sem
Ver​
is
op
格式​ int​ int​ list
；长
度必
须为
3,成
员类
型为
float​
str​ str​ list
；长
度必
须为
3,成
员类
型为
float​
list
；长
度必
须为
4,成
员类
型为
float​
int​ int​ bool​ bool​ bool​ str​ bo
值的
范围​
>0​ >0​ "car"
,"pic
kup_
truc
k","tr
uck",
"con
struc
tion
_veh
icle",
"car"
,"pic
kup_
truc
k","tr
uck",
"con
struc
tion
_veh
icle",
>0​ >0​ -1​ True
/Fals
e​
True
/Fals
e​
True
/Fals
e​
Tr
/F
e​
"bus
","re
creat
ional
_veh
icle",
"mot
orcy
cle","
tricy
cle","
bicy
cle","
pers
on","
ani
mal",
"traf
fic_c
one",
"bar
rier",
"traf
fic_
warn
ing",
"unk
now
n"​
"bus
","re
creat
ional
_veh
icle",
"mot
orcy
cle","
tricy
cle","
bicy
cle","
pers
on","
ani
mal",
"traf
fic_c
one",
"bar
rier",
"traf
fic_
warn
ing",
"unk
now
n"​
van,
pick
up_
bed_
truc
k,tra
iler,b
ox_t
ruck,
nor
m_b
ox_t
ruck,
sp_b
ox_t
ruck,
cran
e,ce
men
t_mi
xer,
wate
ring
_car
t,spe
cial_
clot
hes​
""​
字段
说明​
物体
标
号，
同一
物体
前后
帧应
一
致，
2d框
和3d
框应
一致​
每个
框都
有，
大部
分的
值默
认为
trac
k_id
的值​
物体
中心
点
x、
y、z​
物体
类别:
小型
车，
皮
卡，
卡
车，
工程
车，
公交
车，
房
车，
摩托
车，
自行
车，
行
人，
动
物，
交通
锥，
障碍
物，
未知​
一级
属性​
二级
属
性：​
小型
车：
面包
车；​
卡
车：
车斗
型卡
车、
平板
卡
车、
箱式
货
车、
标准
箱
式、
非标
准箱
式​
工程
车：
吊
车、
物体
长
度、
宽
度、
高度​
四元
数X
YZ
W​
物体
激光
雷达
点个
数​
物体
毫米
波雷
达点
个
数，
默认
值-1​
物体
是否
有重
影​
点云
是否
异常​
画面
里点
云出
现重
影、
点云
在地
平线
下、
点云
实际
是玻
璃上
的镜
像​
所有
框都
要求
有这
个字
段​
clip
维度
的字
段，
规则
版本
号​
是
遮
水泥
搅拌
车、
洒水
车​
行
人：
特殊
服饰​
2024
.03.0
6规
则更
新，
无需
标注
二级
属
性，
但仍
保留
subc
ateg
ory
这个
字段​
特殊
案例​
box_
truc
k因
规则
更新
后续
会删
除​
• 静止车辆方向保持不变
• 70米范围内无错误，包括漏标、不贴合、方向可见的不正确、重影标注不正确、track_id错误等​
• 同一个ID大小保持一致（误差不超过0.05米）​
• 四周和地线贴合要求不超过1个像素，上边缘贴合要求不超过2个像素；车辆噪点/后视镜不标注，
以实际车的边缘贴合。
后视镜不标
*******.10、KeyPoints​
*******0.1、示例JSON​
{
"KeyPoints_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2022-08-05T15:44:40",
"clip_id": "对应帧数据行的 clip",
"check_no": "clipId",
"annotated_info": {
"camera0": {
"keypoints": [
{
"position": [
11,
33
],
"type": "divide",
"occlusion": false
},
{
"position": [
11,
33
],
"type": "merge",
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
"occlusion": true
}
]
},
"camera1": {
"keypoins": []
}
}
}
}
25
26
27
28
29
30
31
32
33
34
*******.10.2、格式要求​
keypoints​ position​ type occlusion​
格式​ list​ list；且必须
3个以上​
str​ bool​
层级​ In
annotated_i
nfo​
Inkeypoints​ Inkeypoints​ Inkeypoints​
值的范围​ position,typ
e,occlusion​
divide,merg
e,dash_solid​
True/False​
*******.11、3DCityObjectDetectionWithFishEye​
*******.11.1、示例JSON​
{
"3d_city_object_detection_with_fish_eye_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2024-05-06T00:54:27",
"check_no": "clipId",
"clip_id": "对应帧数据行的 clipId",
"frame_id": "对应帧数据行的 FrameId",
"semantic_version": "v20241019",
"annotated_info": {
"3d_object_detection_info": {
"basic_info": {
"annotation_date": "2024-05-06T00:54:27"
},
1
2
3
4
5
6
7
8
9
10
11
12
13
14
"ext_info": {
"exception_flag": true,
"vru_track_id_valid": false
},
"3d_object_detection_anns_info": [
{
"size": [
4.6,
1.8,
1.5
],
"obj_center_pos": [
47.67774994506254,
1.0511472161155395,
-1.1871771747806774
],
"obj_rotation": [
0.9994878429621736,
4.885906248585606e-16,
9.55778573003732e-16,
0.03200080890885854
],
"category": "car",
"subcategory": "car",
"track_id": 1,
"group_id": 1,
"is_double_image": true,
"is_group": false,
"lane_id": [
2
],
"is_crop": false,
"signal": "normal",
"num_lidar_pts": 112,
"num_radar_pts": -1,
"is_E0X_shaped_vehicle": false,
"open_status": "",
"is_cyclist": false
},
{
"size": [
7,
2.8,
3
],
"obj_center_pos": [
-47.35474974162991,
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
-11.65969219370768,
-1.1378283150076802
],
"obj_rotation": [
0.9246076116489002,
-2.286960300198134e-16,
-3.6884522721598043e-16,
-0.3809209425601545
],
"category": "truck",
"subcategory": "truck",
"track_id": 2,
"group_id": 2,
"is_double_image": false,
"is_group": true,
"lane_id": [
2
],
"is_crop": false,
"signal": "normal",
"num_lidar_pts": 212,
"num_radar_pts": -1,
"is_E0X_shaped_vehicle": false,
"open_status": "",
"is_cyclist": false
},
{
"size": [
3,
2.8,
3
],
"obj_center_pos": [
-47.35474974162991,
-11.65969219370768,
-1.1378283150076802
],
"obj_rotation": [
0.9246076116489002,
-2.286960300198134e-16,
-3.6884522721598043e-16,
-0.3809209425601545
],
"category": "truck",
"subcategory": "truck",
"track_id": "new_id",
"group_id": 2,
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
"is_double_image": false,
"is_group": true,
"lane_id": [
2
],
"is_crop": false,
"signal": "normal",
"num_lidar_pts": 212,
"num_radar_pts": -1,
"is_E0X_shaped_vehicle": false,
"open_status": "",
"is_cyclist": false
}
]
}
}
}
}
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
*******.11.2、格式要求​
• 标注结果json中必须有字段：track_id,group_id，obj_center_pos,category,subcategory,
size,obj_rotation,num_lidar_pts,num_radar_pts,is_double_image,exception_flag，
is_group，SemVer,is_crop，lane_id，signal，is_E0X_shaped_vehicle​
帧属性​ 3D属性​
字段​ exce
ptio
n_fla
g​
Sem
Ver​
cate
gory​
subc
ateg
ory​
is_E
0X_s
hape
d_ve
hicle​
trac
k_id​
grou
p_id​
obj_
cent
er_p
os​
size​ obj_
rotat
ion​
num
_lida
r_pt
s​
num
_rad
ar_p
ts​
is_d
oubl
e_im
age​
is
ou
格式​ bool​ str​ str​ str​ bool​ int​ int​ list
；长
度必
须为
3,成
员类
型为
float​
list
；长
度必
须为
3,成
员类
型为
float​
list
；长
度必
须为
4,成
员类
型为
float​
int​ int​ bool​ bo
>0​ >0​ >0​ >0​ -1​
值的
范围​
True
/Fals
e​
"car"
,"pic
kup_
truc
k","tr
uck",
"con
struc
tion
_veh
icle",
"bus
","re
creat
ional
_veh
icle",
"mot
orcy
cle","
tricy
cle","
bicy
cle","
pers
on","
ani
mal",
"traf
fic_c
one",
"bar
rier",
"traf
fic_
warn
ing",
"unk
now
n","
warn
ing_
post
","ga
te","
"car"
,"pic
kup_
truc
k","tr
uck",
"con
struc
tion
_veh
icle",
"bus
","re
creat
ional
_veh
icle",
"mot
orcy
cle","
tricy
cle","
bicy
cle","
pers
on","
ani
mal",
"traf
fic_c
one",
"bar
rier",
"traf
fic_
warn
ing",
"unk
now
n",""​
True
/Fals
e​
True
/Fals
e​
Tr
/F
e​
barri
er_g
ate",
"con
struc
tion
_sig
n","s
peci
al_v
ehicl
e"​
字段
说明​
点云
是否
异常​
画面
里点
云出
现重
影、
点云
在地
平线
下、
点云
实际
是玻
璃上
的镜
像​
clip
维度
的字
段，
规则
版本
号​
物体
类别:
小型
车，
皮
卡，
卡
车，
工程
车，
公交
车，
房
车，
摩托
车，
自行
车，
行
人，
动
物，
交通
锥，
障碍
物，
未
知，
警示
柱，
闸
机，
一级
属性​
2024
.03.0
6规
则更
新，
无需
标注
二级
属
性，
但仍
保留
subc
ateg
ory
这个
字段​
是否
为异
形
车；​
输出
时所
有框
都带
这个
属
性，
默认
false
，只
有当
cate
goy
是
truc
k和
cons
truct
ion_
vehi
cle
时，
才可
能输
出
true​
物体
标
号，
同一
物体
前后
帧应
一
致，
2d框
和3d
框应
一致​
每个
框都
有，
大部
分的
值默
认为
trac
k_id
的值​
物体
中心
点，
顺序
为
x、
y、z​
物体
长
度、
宽
度、
高
度，
顺序
为
lwh​
四元
数顺
序为
XY
ZW​
物体
激光
雷达
点个
数​
物体
毫米
波雷
达点
个
数，
默认
值-1​
物体
是否
有重
影​
所
框
要
有
个
段
道
闸，
施工
标志
牌，
特殊
车辆​
特殊
案例​
*******.12、3DHighwayObjectDetectionWithFishEye​
*******.12.1、示例JSON​
{
"3d_highway_object_detection_with_fish_eye_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2024-05-06T00:54:27",
"check_no": "clipId",
"clip_id": "对应帧数据行的 clipId",
"frame_id": "对应帧数据行的 FrameId",
"semantic_version": "v20241019",
"annotated_info": {
"3d_object_detection_info": {
"basic_info": {
"annotation_date": "2024-05-06T00:54:27"
},
"ext_info": {
"exception_flag": true,
"vru_track_id_valid": false
},
"3d_object_detection_anns_info": [
{
"size": [
4.6,
1.8,
1.5
],
"obj_center_pos": [
47.67774994506254,
1.0511472161155395,
-1.1871771747806774
],
"obj_rotation": [
0.9994878429621736,
4.885906248585606e-16,
9.55778573003732e-16,
0.03200080890885854
],
"category": "car",
"subcategory": "car",
"track_id": 1,
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
"group_id": 1,
"is_double_image": true,
"is_group": false,
"lane_id": [
2
],
"is_crop": false,
"signal": "normal",
"num_lidar_pts": 112,
"num_radar_pts": -1,
"is_E0X_shaped_vehicle": false,
"open_status": "",
"is_cyclist": false
},
{
"size": [
7,
2.8,
3
],
"obj_center_pos": [
-47.35474974162991,
-11.65969219370768,
-1.1378283150076802
],
"obj_rotation": [
0.9246076116489002,
-2.286960300198134e-16,
-3.6884522721598043e-16,
-0.3809209425601545
],
"category": "truck",
"subcategory": "truck",
"track_id": 2,
"group_id": 2,
"is_double_image": false,
"is_group": true,
"lane_id": [
2
],
"is_crop": false,
"signal": "normal",
"num_lidar_pts": 212,
"num_radar_pts": -1,
"is_E0X_shaped_vehicle": false,
"open_status": "",
"is_cyclist": false
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
},
{
"size": [
3,
2.8,
3
],
"obj_center_pos": [
-47.35474974162991,
-11.65969219370768,
-1.1378283150076802
],
"obj_rotation": [
0.9246076116489002,
-2.286960300198134e-16,
-3.6884522721598043e-16,
-0.3809209425601545
],
"category": "truck",
"subcategory": "truck",
"track_id": "new_id",
"group_id": 2,
"is_double_image": false,
"is_group": true,
"lane_id": [
2
],
"is_crop": false,
"signal": "normal",
"num_lidar_pts": 212,
"num_radar_pts": -1,
"is_E0X_shaped_vehicle": false,
"open_status": "",
"is_cyclist": false
}
]
}
}
}
}
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
*******2.2、格式要求​
• 标注结果json中必须有字段：track_id,group_id，obj_center_pos,category,subcategory,
size,obj_rotation,num_lidar_pts,num_radar_pts,is_double_image,exception_flag，
is_group，SemVer,is_crop，lane_id，signal，is_E0X_shaped_vehicle​
帧属性​ 3D属性​
字段​ exce
ptio
n_fla
g​
Sem
Ver​
cate
gory​
subc
ateg
ory​
is_E
0X_s
hape
d_ve
hicle​
trac
k_id​
grou
p_id​
obj_
cent
er_p
os​
size​ obj_
rotat
ion​
num
_lida
r_pt
s​
num
_rad
ar_p
ts​
is_d
oubl
e_im
age​
is
ou
格式​ bool​ str​ str​ str​ bool​ int​ int​ list
；长
度必
须为
3,成
员类
型为
float​
list
；长
度必
须为
3,成
员类
型为
float​
list
；长
度必
须为
4,成
员类
型为
float​
int​ int​ bool​ bo
值的
范围​
True
/Fals
e​
"car"
,"pic
kup_
truc
k","tr
uck",
"con
struc
tion
_veh
icle",
"bus
","re
creat
ional
_veh
icle",
"mot
orcy
cle","
tricy
cle","
bicy
cle","
pers
on","
"car"
,"pic
kup_
truc
k","tr
uck",
"con
struc
tion
_veh
icle",
"bus
","re
creat
ional
_veh
icle",
"mot
orcy
cle","
tricy
cle","
bicy
cle","
pers
on","
True
/Fals
e​
>0​ >0​ >0​ >0​ -1​ True
/Fals
e​
Tr
/F
e​
ani
mal",
"traf
fic_c
one",
"bar
rier",
"traf
fic_
warn
ing",
"unk
now
n","
warn
ing_
post
","ga
te","
barri
er_g
ate",
"con
struc
tion
_sig
n","s
peci
al_v
ehicl
e"​
ani
mal",
"traf
fic_c
one",
"bar
rier",
"traf
fic_
warn
ing",
"unk
now
n",""​
字段
说明​
点云
是否
异常​
画面
里点
云出
现重
影、
点云
在地
平线
下、
点云
clip
维度
的字
段，
规则
版本
号​
物体
类别:
小型
车，
皮
卡，
卡
车，
工程
车，
公交
车，
房
一级
属性​
2024
.03.0
6规
则更
新，
无需
标注
二级
属
性，
但仍
是否
为异
形
车；​
输出
时所
有框
都带
这个
属
性，
默认
false
物体
标
号，
同一
物体
前后
帧应
一
致，
2d框
和3d
框应
一致​
每个
框都
有，
大部
分的
值默
认为
trac
k_id
的值​
物体
中心
点，
顺序
为
x、
y、z​
物体
长
度、
宽
度、
高
度，
顺序
为
lwh​
四元
数顺
序为
XY
ZW​
物体
激光
雷达
点个
数​
物体
毫米
波雷
达点
个
数，
默认
值-1​
物体
是否
有重
影​
所
框
要
有
个
段
实际
是玻
璃上
的镜
像​
车，
摩托
车，
自行
车，
行
人，
动
物，
交通
锥，
障碍
物，
未
知，
警示
柱，
闸
机，
道
闸，
施工
标志
牌，
特殊
车辆​
保留
subc
ateg
ory
这个
字段​
，只
有当
cate
goy
是
truc
k和
cons
truct
ion_
vehi
cle
时，
才可
能输
出
true​
特殊
案例​
*******.13、PARKING_MOVABLE_OBJECT_DETECTION泊车目标物检测​
*******.13.1、示例JSON​
{
"parking_movable_object_detection_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2024-05-06T00:54:27",
"check_no": "clipId",
"clip_id": "对应帧数据行的 clipId",
"frame_id": "对应帧数据行的 FrameId",
"semantic_version": "v20241019",
"annotated_info": {
"3d_object_detection_info": {
"basic_info": {
"annotation_date": "2024-05-06T00:54:27"
},
"ext_info": {
"exception_flag": true,
"vru_track_id_valid": false
},
"3d_object_detection_anns_info": [
{
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
"size": [
4.6,
1.8,
1.5
],
"obj_center_pos": [
47.67774994506254,
1.0511472161155395,
-1.1871771747806774
],
"obj_rotation": [
0.9994878429621736,
4.885906248585606e-16,
9.55778573003732e-16,
0.03200080890885854
],
"category": "car",
"subcategory": "car",
"track_id": 1,
"group_id": 1,
"is_double_image": true,
"is_group": false,
"lane_id": [
2
],
"is_crop": false,
"signal": "normal",
"num_lidar_pts": 112,
"num_radar_pts": -1,
"is_E0X_shaped_vehicle": false,
"open_status": "",
"is_cyclist": false
},
{
"size": [
7,
2.8,
3
],
"obj_center_pos": [
-47.35474974162991,
-11.65969219370768,
-1.1378283150076802
],
"obj_rotation": [
0.9246076116489002,
-2.286960300198134e-16,
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
-3.6884522721598043e-16,
-0.3809209425601545
],
"category": "truck",
"subcategory": "truck",
"track_id": 2,
"group_id": 2,
"is_double_image": false,
"is_group": true,
"lane_id": [
2
],
"is_crop": false,
"signal": "normal",
"num_lidar_pts": 212,
"num_radar_pts": -1,
"is_E0X_shaped_vehicle": false,
"open_status": "",
"is_cyclist": false
},
{
"size": [
3,
2.8,
3
],
"obj_center_pos": [
-47.35474974162991,
-11.65969219370768,
-1.1378283150076802
],
"obj_rotation": [
0.9246076116489002,
-2.286960300198134e-16,
-3.6884522721598043e-16,
-0.3809209425601545
],
"category": "truck",
"subcategory": "truck",
"track_id": "new_id",
"group_id": 2,
"is_double_image": false,
"is_group": true,
"lane_id": [
2
],
"is_crop": false,
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
"signal": "normal",
"num_lidar_pts": 212,
"num_radar_pts": -1,
"is_E0X_shaped_vehicle": false,
"open_status": "",
"is_cyclist": false
}
]
}
}
}
}
115
116
117
118
119
120
121
122
123
124
125
126
*******.13.2、格式要求​
• 标注结果json中必须有字段：track_id,group_id，obj_center_pos,category,subcategory,
size,obj_rotation,num_lidar_pts,num_radar_pts,is_double_image,exception_flag，
is_group，SemVer,is_crop，lane_id，signal​
• ，is_E0X_shaped_vehicle​
帧属性​ 3D属性​
字段​ exce
ptio
n_fla
g​
Sem
Ver​
cate
gory​
subc
ateg
ory​
is_E
0X_s
hape
d_ve
hicle​
trac
k_id​
grou
p_id​
obj_
cent
er_p
os​
size​ obj_
rotat
ion​
num
_lida
r_pt
s​
num
_rad
ar_p
ts​
is_d
oubl
e_im
age​
is
ou
格式​ bool​ str​ str​ str​ bool​ int​ int​ list
；长
度必
须为
3,成
员类
型为
float​
list
；长
度必
须为
3,成
员类
型为
float​
list
；长
度必
须为
4,成
员类
型为
float​
int​ int​ bool​ bo
值的
范围​
True
/Fals
e​
"car"
,"pic
kup_
truc
k","tr
"car"
,"pic
kup_
truc
k","tr
True
/Fals
e​
>0​ >0​ >0​ >0​ -1​ True
/Fals
e​
Tr
/F
e​
uck",
"con
struc
tion
_veh
icle",
"bus
","re
creat
ional
_veh
icle",
"mot
orcy
cle","
tricy
cle","
bicy
cle","
pers
on","
ani
mal",
"traf
fic_c
one",
"bar
rier",
"traf
fic_
warn
ing",
"unk
now
n","
warn
ing_
post
","ga
te","
barri
er_g
ate",
"con
struc
uck",
"con
struc
tion
_veh
icle",
"bus
","re
creat
ional
_veh
icle",
"mot
orcy
cle","
tricy
cle","
bicy
cle","
pers
on","
ani
mal",
"traf
fic_c
one",
"bar
rier",
"traf
fic_
warn
ing",
"unk
now
n",""​
tion
_sig
n","s
peci
al_v
ehicl
e"​
字段
说明​
点云
是否
异常​
画面
里点
云出
现重
影、
点云
在地
平线
下、
点云
实际
是玻
璃上
的镜
像​
clip
维度
的字
段，
规则
版本
号​
物体
类别:
小型
车，
皮
卡，
卡
车，
工程
车，
公交
车，
房
车，
摩托
车，
自行
车，
行
人，
动
物，
交通
锥，
障碍
物，
未
知，
警示
柱，
闸
机，
道
闸，
施工
标志
牌，
一级
属性​
2024
.03.0
6规
则更
新，
无需
标注
二级
属
性，
但仍
保留
subc
ateg
ory
这个
字段​
是否
为异
形
车；​
输出
时所
有框
都带
这个
属
性，
默认
false
，只
有当
cate
goy
是
truc
k和
cons
truct
ion_
vehi
cle
时，
才可
能输
出
true​
物体
标
号，
同一
物体
前后
帧应
一
致，
2d框
和3d
框应
一致​
每个
框都
有，
大部
分的
值默
认为
trac
k_id
的值​
物体
中心
点，
顺序
为
x、
y、z​
物体
长
度、
宽
度、
高
度，
顺序
为
lwh​
四元
数顺
序为
XY
ZW​
物体
激光
雷达
点个
数​
物体
毫米
波雷
达点
个
数，
默认
值-1​
物体
是否
有重
影​
所
框
要
有
个
段
特殊
车辆​
特殊
案例​
*******、（24）相机遮挡CAMERA_BLOCKAGE​
*******.1、示例JSON：​
{
"camera_2d_blockage_annotated_info": {
"annotator":"标注员",
"annotation_qa":"质检员",
"check_no":"clipId",
"annotation_date":"2022-08-05T15:44:40", 定义：导出的时间
"frame_id":"对应帧数据行的 FrameId",
"annotated_info":{
"camera0":{
"result": [
{
"sourceID": "",
"id": "SfNKjUPS",
"filterLabel": "full_blocked"
}
]
},
"camera1":{
"result": [
{
"sourceID": "",
"id": "SfNKjUPS",
"filterLabel": "full_blocked"
}
]
},
"camera2":{
"result": [
{
"sourceID": "",
"id": "SfNKjUPS",
"filterLabel": "full_blocked"
}
]
},
"camera3":{
"result": [
{
"sourceID": "",
"id": "SfNKjUPS",
"filterLabel": "full_blocked"
}
]
},
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
"camera4":{
"result": [
{
"sourceID": "",
"id": "SfNKjUPS",
"filterLabel": "full_blocked"
}
]
},
"camera5":{
"result": [
{
"sourceID": "",
"id": "SfNKjUPS",
"filterLabel": "full_blocked"
}
]
},
"camera6":{
"result": [
{
"sourceID": "",
"id": "SfNKjUPS",
"filterLabel": "full_blocked"
}
]
},
"camera7":{
"result": [
{
"sourceID": "",
"id": "SfNKjUPS",
"filterLabel": "full_blocked"
}
]
},
"camera8":{
"result": [
{
"sourceID": "",
"id": "SfNKjUPS",
"filterLabel": "full_blocked"
}
]
},
"camera9":{
"result": [
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
{
"sourceID": "",
"id": "SfNKjUPS",
"filterLabel": "full_blocked"
}
]
},
"camera10":{
"result": [
{
"sourceID": "",
"id": "SfNKjUPS",
"filterLabel": "full_blocked"
}
]
}
}
}
}
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
*******.2、格式要求：​
filterLabel​
含义​ 标注结果​
格式​ str​
层级​
值的范围​ clear清晰，
condensatio
n水雾遮挡，
full_blocked
全遮挡，
partial_bloc
ked部分遮挡​
特殊案例​
*******、（25）环境识别ENVIRONMENT_IDENTIFICATION​
*******.1示例JSON​
{
1
"camera_2d_environment_identification_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"check_no": "clipId",
"clip_id": "对应帧数据行的 ClipId",
"annotation_date": "2022-08-05T15:44:40", 定义：导出的时间
"frame_id": "对应帧数据行的 FrameId",
"annotated_info": {
"camera0": {
"width": 3840,//图片实际的宽​
"height": 2160,//图片实际的高​
"result": {
"period": "daytime",
"sky": "blue",
"ground": "dry",
"location": "citystreet"
}
}
}
}
}
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
*******.1格式要求：​
*******、（26）泊车FSlinePARKING_FSLINE_2D​
{
"camera_2d_fsline_annotated_info": {
"annotator":"标注员",
"annotation_qa":"质检员",
1
2
3
4
"check_no":"clipId",
"annotation_date":"2022-08-05T15:44:40", 定义：导出的时间
"frame_id":"对应帧数据行的 FrameId",
"annotated_info":{
}
}
}
5
6
7
8
9
10
11
*******、（27）路牌2d_traffic_sign​
*******.1示例Json​
{
"camera_2d_traffic_sign_annotated_info": {
"annotator":"标注员",
"annotation_qa":"质检员",
"check_no":"clipId",
"annotation_date":"2022-08-05T15:44:40",
"frame_id":"对应帧数据行的 FrameId",
"annotated_info":{
"camera0":{
"width": 1408,
"height": 792,
"objects": [
{
"id": "411fdc78",
"track_id": 1,
"x": 2812.1956,
"y": 246.6994,
"width": 138.4377,
"height": 290.7783,
"bbox_class": "non_related",
"label": "",
"visiable": ["camera0"],
"valid": true
},
{
"id": "UCOHelww",
"track_id": 1,
"x": 2812.1956,
"y": 246.6994,
"width": 138.4377,
"height": 290.7783,
"bbox_class": "non_related",
"label": "",
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
"visiable": ["camera0"],
"valid": true
}
]
},
"camera1":{
"width": 1408,
"height": 792,
"objects": [
{
"id": "411fdc77",
"track_id": 1,
"x": 2812.1956,
"y": 246.6994,
"width": 138.4377,
"height": 290.7783,
"bbox_class": "non_related",
"label": "",
"visiable": ["camera1"],
"valid": true
},
{
"id": "UCOHelw7",
"track_id": 1,
"x": 2812.1956,
"y": 246.6994,
"width": 138.4377,
"height": 290.7783,
"bbox_class": "non_related",
"label": "",
"visiable": ["camera1"],
"valid": true
}
]
}
}
}
}
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
*******.1格式要求​
4.2.1.7、（28）光斑2d_spot_detection​
4.2.1.7.1示例Json​
光斑-大卓回标json格式-3.json
4.2.1.7.2格式要求​
4.2.1.8、（30）23DObjectComplement​
4.2.1.8.1、示例JSON：​
{
"2d_object_complement_annotated_info":{
"annotator":"标注员",
"annotation_qa":"质检员",
"annotation_date":"2022-08-05T15:44:40",
"check_no":"提验的日期，如 20231212，该值不可变更，一旦赋值必须保持唯一。",
"frame_id":"对应帧数据行的 FrameId",
"annotated_info":{
"camera0":{
"objects":[
{
"track_id":1,
"bbox":[
561.3565,
703.1243,
132.1052,
183.1258
],
"category":"car",
"lane_id":[
1
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
],
"is_crop":false,
"tire_line":[
[
-100,
-100
],
[
630.817610062893,
583.3962264150944
],
[
631.2903225806451,
701.774193548387
],
[
615.1612903225806,
729.6774193548387
]
],
"signal":"others"
}
]
}
}
},
"3d_object_complement_annotated_info":{
"annotator":"标注员",
"annotation_qa":"质检员",
"annotation_date":"2022-08-05T15:44:40",
"check_no":"提验的日期，如 20231212，该值不可变更，一旦赋值必须保持唯一。",
"frame_id":"对应帧数据行的 FrameId",
"annotated_info":{
"3d_object_detection_info":{
"basic_info":{
"annotation_date":"2022-08-05T15:44:40"
},
"ext_info":{
"exception_flag":true
},
"3d_object_detection_anns_info":[
{
"size":[
2.1,
4.7,
1.8
],
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
"obj_center_pos":[
47.67774994506254,
1.0511472161155395,
-1.1871771747806774
],
"obj_rotation":[
0.9994878429621736,
4.885906248585606e-16,
9.55778573003732e-16,
0.03200080890885854
],
"category":"car",
"track_id":1,
"is_double_image":false,
"group_id": -1,
"is_group": false,
"num_lidar_pts":112,
"num_radar_pts":-1
},
{
"size":[
1.8,
4.6,
1.5
],
"obj_center_pos":[
-47.35474974162991,
-11.65969219370768,
-1.1378283150076802
],
"obj_rotation":[
0.9246076116489002,
-2.286960300198134e-16,
-3.6884522721598043e-16,
-0.3809209425601545
],
"category":"car",
"track_id":2,
"object_id":1,
"group_id": -1,
"is_group": false,
"is_double_image":true,
"num_lidar_pts":212,
"num_radar_pts":-1
}
]
}
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
}
}
}
116
117
118
4.2.2、单Clip​
1、每个批次的结果会打成⼀个zip包​
2.zip包内为每个clip的标注结果，⽂件路径为clipId+.json后缀:​
38d432c00bberraf888f096fckkkk480.json
....
1
2
3、每个clip⽂件格式为JSON格式​
*******、Bev车道线标注​
每个Clip包含1-2个Clip维度大图标注结果+40个Frame维度标注结果​
1、每个批次的结果会打成⼀个zip包​
2、zip内包含一个Clip维度大图的标注结果​
2.1、拼接大图​
Clip拼接大图的标注结果，⽂件路径为clipId+.json后缀​
clip_1.json
clip_2.json
clip_3.json
clip_4.json
....
....
1
2
3
4
5
6
每个Clip、Frame⽂件格式均为合法JSON格式​
3、标注结果格式​
3.1、Clip维度大图标注结果格式​
Bev车道线标注的主体是点云，点的坐标是X、Y、Z；且一个clip可能针对cam_0和cam_1各生成1个
点云文件（合计2个），或者直接生成1个combine的点云文件，因此标注结果中要不是camera0和
camera1中有结果，要不是combine中结果。​
{
1
"bev_lane_combine_annotated_info":{
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2022-08-05T15:44:40",
"clip_id": "对应帧数据行的 clip",
"check_no": "clipId",
"annotated_info":{
"camera0":{
"keypoints":[
{"position":[11,22,33],
"type":"divide",
"Occlusion":false
},
{"position":[11,22,33],
"type":"merge",
"Occlusion":true
}
],
"lines":[
{
"line_key_points":[
[
43.35594401056317,
17.367683198169637,
17.367683198169637
],true,
[
47.1199606956959,
17.367683198169637,
17.434382247924802
],true
],
"dash_line_start_end":[
[
[
52.502145902186165,
17.50360946655273,
17.50360946655273
],
true,
[
51.79040182705955,
17.50360946655273,
17.50360946655273
],
true
]
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
],
"type":"single_dash",
"color":"yellow",
"has_fishbone":false
}
]
},
"camera1":{
"lines":[]
},
"combine":{
"lines":[]
}
}
}
}
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
*******、CameraBev车道线标注​
每个Clip一个JSON，该JSON内包含1-2个Clip维度大图标注结果​
1、每个批次的结果会打成⼀个zip包​
2、zip内包含一个Clip维度大图的标注结果​
2.1、拼接大图​
Clip拼接大图的标注结果，⽂件路径为clipId+.json后缀​
clip_1.json
clip_2.json
clip_3.json
clip_4.json
....
....
1
2
3
4
5
6
每个Clip⽂件格式均为合法JSON格式​
3、标注结果格式​
3.1、Clip维度大图标注结果格式​
默认值：（即使无结果也需要返回个lines空数据）​
{
"camera0": {
"lines": [
1
2
3
]
},
"camera1": {
"lines": [
]
},
"combine": {
"lines": [
]
}
}
4
5
6
7
8
9
10
11
12
13
14
15
16
17
CameraBev车道线的标注主体是图片，点只有X和Y；且一个clip针对cam_0和cam_1各生成1个图
片文件（合计2个）。​
{
"camera_bev_lane_combine_annotated_info":{
"annotator":"标注员",
"annotation_qa":"质检员",
"annotation_date":"2022-08-05T15:44:40",
"clip_id":"对应帧数据行的 clip",
"check_no":"对应帧数据行的 clip",
"annotated_info":{
"camera0":{
"lines":[
{
"line_key_points":[
[
43.35594401056317,
17.367683198169637
],true,
[
47.1199606956959,
17.367683198169637
],true
],
"dash_line_start_end":[
[
[
52.502145902186165,
17.50360946655273
],
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
true,
[
51.79040182705955,
17.50360946655273
],
true
]
],
"type":"single_dash",
"color":"yellow",
"has_fishbone":false
}
]
},
"camera1":{
"lines":[]
},
"combine":{
"lines":[]
}
}
}
}
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
线的类型【type】​ 颜色【color】​ 是否有鱼骨线
【has_fishbone】​
线的关键点
【line_ke
y_points
】​
虚线起止点
【dash_li
ne_start_
end】​
单虚线​ single_das
h​
黄/白/其他​ yellow/wh
ite/others​
是/否​ true/false​ 起止点是否
真实可见，
用
true/false
表示​
单实线​ single_soli
d​
黄/白/其他​ yellow/wh
ite/others​
是/否​ true/false​
胖虚线​ dense_wid
e_dash​
黄/白/其他​ yellow/wh
ite/others​
是/否​ true/false​ 起止点是否
真实可见，
用
true/false
表示​
双实线​ double_so
lid​
黄/白/其他​ yellow/wh
ite/others​
是/否​ true/false​
双虚线​ double_da
sh​
黄/白/其他​ yellow/wh
ite/others​
是/否​ true/false​ 起止点是否
真实可见，
用
true/false
表示​
左虚右实线​ left_dash_
right_solid​
黄/白/其他​ yellow/wh
ite/others​
是/否​ true/false​ 起止点是否
真实可见，
用
true/false
表示​
左实右虚线​ left_solid_
right_dash​
黄/白/其他​ yellow/wh
ite/others​
是/否​ true/false​ 起止点是否
真实可见，
用
true/false
表示​
路沿线​ road_edge​ 黄/其他​ yellow/oth
ers​
是/否​ true/false​
停止线​ stop_lane​ 黄/白/其他​ yellow/wh
ite/others​
是/否​ true/false​
路口导向线​ road_guid
e_lane​
黄/白/其他​ yellow/wh
ite/others​
是/否​ true/false​
其他类型​ others​ 黄/白/其他​ yellow/wh
ite/others​
是/否​ true/false​
*******、2DLanewithlabel​
每个clip包含40帧camera0的图片和40帧camera1的图片的标注结果。​
1、每个批次的结果会打成⼀个zip包​
2、zip内需包括Camera0、Camera1的标注结果​
3、标注结果格式​
3.1、Clip维度大图标注结果格式​
同*******、2DLane。​
lines​ line_key_
points​
dash_line_start
_end​
type color​ has_fishb
one​
num_lan
es_left_ri
ght​
格式​ list​ list；且必
须两个以
上​
list​ str​ str​ bool​ list，且只
有两个值,
每个值都
是int​
层级​ In
annotate
d_info​
Inline​ Inline​ Inline​ Inline​ Inline​ In
annotate
d_info​
值的范围​ lines，
line_key_
points，
dash_line
_start_en
d，type，
color，
has_fishb
one​
'single_d
ash','singl
e_solid','d
ense_wid
e_dash','d
ouble_sol
id',​
'double_d
ash','left_
dash_righ
t_solid','le
ft_solid_r
ight_dash
','road_ed
ge','others
','stop_lan
e','road_g
uide_lane
'​
'yellow','
white','ot
hers'​
True/Fals
e​
-1到10​
特殊案例​ road_edg
e的颜色只
能
是'yellow',
'others'​
值为
[0,0]，或
者包含-1
可能会有
问题​
• 如果type是
single_solid
，
double_soli
d，
road_edge
，others，
则
dash_line_st
art_end不可
有点；反
之，必须有
点​
4.2.3、Clip+Frame复合​
*******、标注结果zip文件结构及内容​
*******.1、每个批次的结果会打成⼀个zip包​
类似项目CamSlam回标ZIP示例​
04eca0f46b98400bb1e727cb4d26a
a14.zip
14.89KB
*******.2、zip包内为标注结果JSON文件​
*******.2.1、其中每个clip的标注结果，⽂件路径为clipId+.json后缀:​
38d432c00bberraf888f096fckkkk480.json
....
1
2
*******.2.2、其中每个frame的标注结果，⽂件路径为clipId+\+frameId+.json后缀:​
38d432c00bberraf888f096fckkkk480\xxxxxxxxxxxx.json
aaaaaa\bbbbb.json
....
1
2
3
semantic_version为标注规则版本，需按业务设置给定值​
*******.3、Clip标注结果​
{
"xxx_clip_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2022-08-05T15:44:40",
"clip_id": "对应帧数据行的 clip",
"check_no": "clipId",
"semantic_version":"",
"annotated_info": {}
}
}
1
2
3
4
5
6
7
8
9
10
11
*******.4、Frame标注结果​
{
"xxx_frame_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
1
2
3
4
"annotation_date": "2022-08-05T15:44:40",
"clip_id": "对应帧数据行的 clip",
"frame_id":"数据行的 frame",
"check_no": "复用clip_id",
"annotation_date": "2024-05-06T00:54:27",
"check_no": "clipId",
"semantic_version": "v20240507",
"annotated_info": {}
}
}
5
6
7
8
9
10
11
12
13
14
*******、CameraSLAMLaneCombine
*******.1、每个clip⽂件格式为JSON格式​
{
"camera_slam_lane_combine_clip_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2022-08-05T15:44:40",
"clip_id": "对应帧数据行的 clip",
"check_no": "clipId",
"annotated_info": {
"keypoints": [
{
"position": [
11,
33
],
"type": "divide",
"occlusion": false
},
{
"position": [
11,
33
],
"type": "merge",
"occlusion": true
}
],
"lines": [
{
"line_key_points": [
[
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
43.35594401056317,
17.367683198169637
],
true,
[
47.1199606956959,
17.434382247924802
],
true
],
"dash_line_start_end": [
[
[
52.502145902186165,
17.50360946655273
],
true,
[
51.79040182705955,
17.50360946655273
],
true
]
],
"type": "single_dash",
"color": "yellow",
"has_fishbone": false
}
]
}
}
}
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
*******.2、Frame标注结果​
{
"camera_slam_lane_combine_frame_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"check_no": "clipId",
"annotation_date": "2022-08-05T15:44:40定义：导出的时间",
"frame_id": "对应帧数据行的 FrameId",
"annotated_info": {
"camera0": {
"keypoints": [
1
2
3
4
5
6
7
8
9
10
{
"position": [
11,
33
],
"type": "divide",
"occlusion": false
},
{
"position": [
11,
33
],
"type": "merge",
"occlusion": true
}
],
"lines": [
{
"line_key_points": [
[
1472.4,
608.1
]
],
"dash_line_start_end": [
[
[
1233.3,
710.5
],
true,
[
1229,
702.9
],
true
]
],
"type": "single_dash",
"color": "yellow",
"has_fishbone": false
}
],
"num_lanes_left_right": [
1,
1
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
]
},
"camera1": {
"keypoints": [
{
"position": [
11,
33
],
"type": [
"divide"
],
"Occlusion": false
},
{
"position": [
11,
33
],
"type": [
"merge"
],
"Occlusion": true
}
],
"lines": [
{
"line_key_points": [
[
1472.4,
608.1
],
[
1233.3,
710.5
],
[
1229,
702.9
]
],
"dash_line_start_end": [
[
[
1233.3,
710.5
],
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
true,
[
1229,
702.9
],
true
]
],
"type": "single_dash",
"color": "yellow",
"has_fishbone": false
}
],
"num_lanes_left_right": [
1,
1
]
}
}
}
}
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
大图的结果:​
lines​ line_k
ey_poi
nts​
dash_line_
start_end​
type color​ has_fi
shbon
e​
keypoi
nts​
positi
on​
type occlus
ion​
格式​ list​ list；
且必须
2个以
上​
list；且必
须3个以上​
str​ str​ bool​ list​ list；
且必须
2个以
上​
str​ bool​
层级​ In
annot
ated_i
nfo​
Inline​ Inline​ Inline​ Inline​ Inline​ In
annot
ated_i
nfo​
In
keypoi
nts​
In
keypoi
nts​
In
keypoi
nts​
值的范
围​
lines
，
line_k
ey_poi
nts，
dash_l
'single
_dash',
'single
_solid',
'dense
_wide
'yello
w','whi
te','oth
ers'​
True/F
alse​
positi
on,typ
e,occl
usion​
divide,
merge
,dash_
solid​
True/F
alse​
ine_st
art_en
d，
type，
color
，
has_fi
shbon
e​
_dash',
'doubl
e_soli
d',​
'doubl
e_das
h','left
_dash
_right
_solid',
'left_s
olid_ri
ght_d
ash','r
oad_e
dge','o
thers'​
特殊案
例​
• 如果
type是
single_
solid，
double
_solid
，
road_e
dge，
others
，则
dash_li
ne_star
t_end
不可有
点；反
之，必
须有点​
road_
edge
的颜色
只能
是'yell
ow','ot
hers'​
单帧小图的结果:​
lines​ line_
key_
point
s​
dash_line
_start_en
d​
type color​ has_f
ishbo
ne​
keyp
oints​
positi
on​
type occlu
sion​
num_
lanes
_left_
right​
格式​ list​ list；
且必
须2个
以上​
list；且必
须3个以上​
str​ str​ bool​ list​ list；
且必
须2个
以上​
str​ bool​ list，
且只
有两
个值,
每个
值都
是int​
层级​ In
annot
ated_
info​
In
line​
Inline​ In
line​
In
line​
In
line​
In
annot
ated_
info​
In
keyp
oints​
In
keyp
oints​
In
keyp
oints​
In
annot
ated_
info​
值的
范围​
lines
，
line_
key_
point
s，
dash
_line
_start
_end
，
type
，
color
，
has_f
ishbo
ne​
'singl
e_das
h','sin
gle_s
olid','
dens
e_wi
de_d
ash','
doubl
e_soli
d',​
'doub
le_da
sh','le
ft_da
sh_ri
ght_s
olid','l
eft_s
olid_r
ight_
dash',
'road
_edg
e','oth
ers'​
'yello
w','w
hite','
other
s'​
True/
False​
positi
on,ty
pe,oc
clusio
n​
divid
e,mer
ge,da
sh_so
lid​
True/
False​
特殊
案例​
road_
edge
的颜
值为
[0,0]
，或
• 如果
type是
single
_solid
，
doubl
e_soli
d，
road_
edge
，
others
，则
dash_
line_s
tart_e
nd不
可有
点；反
之，必
须有点​
色只
能
是'yel
low','
other
s'​
者包
含-1
可能
会有
问题​
*******、环视泊车车位检测parking_surround_space_detection​
*******.1、Clip示例JSON​
若无标注结果annotated_info的空结构：​
{
"flags": [
],
"parking_space": [
]
}
1
2
3
4
5
6
7
8
{
"parking_surround_space_detection_clip_annotated_info": {
1
2
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2022-08-05T15:44:40",
"clip_id": "对应帧数据行的 clip",
"check_no": "复用clip_id",
"semantic_version":"V20240826",
"annotated_info": {
"parking_space": [
{
"parks": [
{
"parks_key_points": [
[
1.1366173613769799,
4.291387376400225,
-1.8462184768841752
],
[
2.2957938990229683,
4.279394770113074,
-1.8462184768841752
],
[
3.4549704366689564,
4.267402163825922,
-1.8462184768841752
],
[
3.418769204346444,
7.15272838016239,
-1.8462184768841752
],
[
3.3825679720239314,
10.038054596498858,
-1.8462184768841752
],
[
2.283207489605726,
10.069312444584662,
-1.8462184768841752
],
[
1.1838470071875207,
10.100570292670467,
-1.8462184768841752
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
],
[
1.1602321842822503,
7.195978834535346,
-1.8462184768841752
]
],
"parks_points_visible":[
[
"camera0"
],
[
"camera7"
],
[
"camera7",
"camera8"
],
[
"camera9"
],
[
"camera10"
],
[
"camera0"
],
[
"camera1"
],
[
"camera6"
]
],
"usable": "free",
"type": "vertical",
"track_id": 1,
"special_parking":"normal",
"texture":"bricklawn",
"lock": {
"lock_key_points": [],
"status": ""
},
"lever": {
"lever_key_points": []
}
}
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
]
},
{
"parks": [
{
"parks_key_points": [
[
1.1366173613769799,
4.291387376400225,
-1.8462184768841752
],
[
2.2957938990229683,
4.279394770113074,
-1.8462184768841752
],
[
3.4549704366689564,
4.267402163825922,
-1.8462184768841752
],
[
3.418769204346444,
7.15272838016239,
-1.8462184768841752
],
[
3.3825679720239314,
10.038054596498858,
-1.8462184768841752
],
[
2.283207489605726,
10.069312444584662,
-1.8462184768841752
],
[
1.1838470071875207,
10.100570292670467,
-1.8462184768841752
],
[
1.1602321842822503,
7.195978834535346,
-1.8462184768841752
]
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
],
"parks_points_visible":[
[
"camera0"
],
[
"camera7"
],
[
"camera7",
"camera8"
],
[
"camera9"
],
[
"camera10"
],
[
"camera0"
],
[
"camera1"
],
[
"camera6"
]
],
"usable": "free",
"type": "vertical",
"track_id": 1,
"special_parking":"normal",
"texture":"bricklawn",
"lock": {
"lock_key_points": [],
"status": ""
},
"lever": {
"lever_key_points": []
}
}
]
}
]
}
}
}
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
*******.2、Frame示例JSON​
若无标注结果annotated_info的空结构：​
{
"flags": [
],
"parking_space": [
]
}
1
2
3
4
5
6
7
8
{
"parking_surround_space_detection_frame_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2022-08-05T15:44:40",
"clip_id": "对应帧数据行的 clip",
"check_no": "复用clip_id",
"semantic_version":"V20240826",
"annotated_info": {
"parking_space": [
{
"parks": [
{
"parks_key_points": [
[
1.1366173613769799,
4.291387376400225,
-1.8462184768841752
],
[
2.2957938990229683,
4.279394770113074,
-1.8462184768841752
],
[
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
3.4549704366689564,
4.267402163825922,
-1.8462184768841752
],
[
3.418769204346444,
7.15272838016239,
-1.8462184768841752
],
[
3.3825679720239314,
10.038054596498858,
-1.8462184768841752
],
[
2.283207489605726,
10.069312444584662,
-1.8462184768841752
],
[
1.1838470071875207,
10.100570292670467,
-1.8462184768841752
],
[
1.1602321842822503,
7.195978834535346,
-1.8462184768841752
]
],
"parks_points_visible":[
[
"camera0"
],
[
"camera7"
],
[
"camera7",
"camera8"
],
[
"camera9"
],
[
"camera10"
],
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
[
"camera0"
],
[
"camera1"
],
[
"camera6"
]
],
"usable": "free",
"type": "vertical",
"track_id": 1,
"special_parking":"normal",
"texture":"bricklawn",
"lock": {
"lock_key_points": [],
"status": ""
},
"lever": {
"lever_key_points": []
}
}
]
},
{
"parks": [
{
"parks_key_points": [
[
1.1366173613769799,
4.291387376400225,
-1.8462184768841752
],
[
2.2957938990229683,
4.279394770113074,
-1.8462184768841752
],
[
3.4549704366689564,
4.267402163825922,
-1.8462184768841752
],
[
3.418769204346444,
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
7.15272838016239,
-1.8462184768841752
],
[
3.3825679720239314,
10.038054596498858,
-1.8462184768841752
],
[
2.283207489605726,
10.069312444584662,
-1.8462184768841752
],
[
1.1838470071875207,
10.100570292670467,
-1.8462184768841752
],
[
1.1602321842822503,
7.195978834535346,
-1.8462184768841752
]
],
"parks_points_visible":[
[
"camera0"
],
[
"camera7"
],
[
"camera7",
"camera8"
],
[
"camera9"
],
[
"camera10"
],
[
"camera0"
],
[
"camera1"
],
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
[
"camera6"
]
],
"usable": "free",
"type": "vertical",
"track_id": 1,
"special_parking":"normal",
"texture":"bricklawn",
"lock": {
"lock_key_points": [],
"status": ""
},
"lever": {
"lever_key_points": []
}
}
]
}
]
}
}
}
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
*******.3、格式要求​
ext_i
nfo​
sem
antic
_ver
sion​
park
s​
trac
k_id​
usab
le​
type park
s_ke
y_po
ints​
park
s_po
ints_
visib
le​
车位
纹理
（te
xtur
e）​
spec
ial_p
arki
ng​
lock​ lock
_key
_poi
nts​
statu
s​
le
r​
格式​ 投影
是否
准确​
str​ list​ int​ str​ str​ list​ list​ list​ str​ list​ list​ int暂
定​
lis
层级​ list​ In
park
ing_
spac
e​
In
park
s​
In
park
s​
In
park
s​
In
park
s​
In
park
s​
In
park
s​
In
park
s​
In
park
ing_
spac
e​
In
lock​
In
lock​
In
pa
in
sp
e​
＞0​
值的
范围​
In
ann
otat
ed_i
nfo​
free,
occu
py,u
nkn
own​
verti
cal,h
orizo
ntal,
slant
,mec
hani
cal_
slot,
unk
now
n​
8个
点；
float
，包
括
x,y,z,
visib
le​
cam
era0
、6-
10
和""​
line/
brick
lawn
/cor
ner/
othe
rs​
'acc
essi
ble_
park
ing_
spac
e','lo
ng_p
arki
ng_s
pace'
,'tan
dem
_par
king',
'min
i_pa
rkin
g_sp
ace','
wom
en’
s_pa
rkin
g_sp
ace','
'​
4个
点；
float​
包括
x,y,z​
0(关
闭）,
1(打
开)​
特殊
案例​
"ca
mer
a0":
{"pr
oject
ion":
}​
"ca
mer
a6":
{"pr
oject
ion":
}​
"ca
mer
a7":
车位​ 工具
自动
生成
即可​
“fre
e"->
可
泊，
"occ
upy"
->不
可泊
，"u
nkn
own
"->
无法
判断​
多帧
拼接
时默
认输
出""
，拆
到单
帧才
开始
标注
该属
性​
如：
"poi
nts_
visib
le":[​
line
线
型，
brick
lawn
地砖
型，
corn
er角
点
型，
othe
rs其
他​
acce
ssibl
e_pa
rkin
g_sp
ace
无障
碍车
位，
long
_par
king
_spa
ce大
型车
位，
tand
地
锁；
如无
地锁
则保
留
key​
如无
地锁
则保
留
key​
如无
地锁
则保
留
key​
限
杆
{"pr
oject
ion":
}​
"ca
mer
a8":
{"pr
oject
ion":
}​
"ca
mer
a9":
{"pr
oject
ion":
}​
"ca
mer
a10"
:
{"pr
oject
ion":
}​
该成
员的
值
为：
"corr
ect"
，"i
ncor
rect"
，"u
nkn
own
",​
""​
[​
[""],​
[""]​
]]
em_
park
ing
子母
车
位，
mini
_par
king
_spa
ces
迷你
车
位，
wom
en’
s_pa
rkin
g_sp
ace
女性
友好
车位​
*******、泊车路面标识parking_road_sign_mark​
*******.1、Clip示例JSON​
若无标注结果annotated_info的空结构：​
{
"land_mark": [
]
}
1
2
3
4
5
{
"parking_road_sign_mark_clip_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2024-05-06T00:54:27",
"clip_id": "对应帧数据行的 clip",
"check_no": "clipId",
"semantic_version": "v20241230",
"annotated_info": {
"ext_info": {
"camera0": {
"projection": "correct"
},
"camera6": {
"projection": "incorrect"
},
"camera7": {
"projection": "unknown"
},
"camera8": {
"projection": "correct"
},
"camera9": {
"projection": "incorrect"
},
"camera10": {
"projection": "unknown"
}
},
"land_mark": [
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
{
"type": "straight_left",
"worn_status": "clear",
"bbox": [
[
[
-24.597306729965915,
-15.434656297817597,
-1.8917511641203855
],
[
-24.549893497090206,
-14.455544842156652,
-1.8872655457482166
]
],
[
[
-24.549893497090206,
-14.455544842156652,
-1.8872655457482166
],
[
-21.554037891027903,
-14.60055898849593,
-1.9002375807544079
]
],
[
[
-21.554037891027903,
-14.60055898849593,
-1.9002375807544079
],
[
-21.60145112390361,
-15.579670444156875,
-1.9047231991265767
]
],
[
[
-21.60145112390361,
-15.579670444156875,
-1.9047231991265767
],
[
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
-24.597306729965915,
-15.434656297817597,
-1.8917511641203855
]
]
],
"bbox_visible": [
""
],
"center_points": [
[
[
-24.58789988725534,
-15.240399401569437,
-1.8908612120136123
],
[
-23.6656504691795,
-14.498346553824415,
-1.8910943122244204
]
],
[
[
-24.58789988725534,
-15.240399401569437,
-1.8908612120136123
],
[
-21.590941206484093,
-15.362634399167602,
-1.903728888555295
]
]
],
"points_visible": [
[
[
""
],
[
""
]
],
[
[
""
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
],
[
""
]
]
],
"passable": [
[],
[]
]
},
{
"type": "forbidden_left",
"worn_status": "clear",
"bbox": [
[
[
1,
2,
3
],
[
4,
5,
6
]
],
[
[
4,
5,
6
],
[
7,
8,
9
]
],
[
[
7,
8,
9
],
[
10,
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
11,
12
]
],
[
[
10,
11,
12
],
[
1,
2,
3
]
]
],
"bbox_visible": [
"camera1"
],
"center_points": [
[
[
1,
2,
3
]
],
[
[
1,
2,
3
],
[
7,
8,
9
]
],
[
[
1,
2,
3
],
[
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
7,
8,
9
]
]
],
"points_visible": [
[
[
"camera0",
"camera7"
]
],
[
[
"camera0"
],
[
"camera0"
]
],
[
[
"camera0"
],
[
"camera0"
]
]
],
"passable": []
},
{
"type": "intersection",
"worn_status": "clear",
"bbox": [
[
[
-19.68243397759422,
-1.1007637765589795,
-1.8604830566946462
],
[
-13.298094248814357,
-1.6591012881587348,
-1.8893188267928596
]
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
],
[
[
-13.298094248814357,
-1.6591012881587348,
-1.8893188267928596
],
[
-12.977401113152748,
3.8705189160475513,
-1.8642028028679853
]
],
[
[
-12.977401113152748,
3.8705189160475513,
-1.8642028028679853
],
[
-19.190179330162422,
4.288481655783489,
-1.8367411615845102
]
],
[
[
-19.190179330162422,
4.288481655783489,
-1.8367411615845102
],
[
-19.68243397759422,
-1.1007637765589795,
-1.8604830566946462
]
]
],
"bbox_visible": [
""
],
"center_points": [
[
[
-16.243396856783455,
1.539857402988849,
-1.861956774775487
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
]
]
],
"points_visible": [
[
[
""
],
[
""
]
],
[
[
""
],
[
""
]
],
[
[
""
],
[
""
]
],
[
[
""
],
[
""
]
]
],
"passable": [
[
1
],
[
1
],
[
1
],
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
[
0
]
]
}
]
}
}
}
360
361
362
363
364
365
366
367
368
*******.2、Frame示例JSON​
若无标注结果annotated_info的空结构：​
{
"land_mark": [
]
}
1
2
3
4
5
{
"parking_road_sign_mark_frame_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2024-05-06T00:54:27",
"clip_id": "对应帧数据行的 clip",
"check_no": "clipId",
"semantic_version": "v20241230",
"annotated_info": {
"ext_info": {
"camera0": {
"projection": "correct"
},
"camera6": {
"projection": "incorrect"
},
"camera7": {
"projection": "unknown"
},
"camera8": {
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
"projection": "correct"
},
"camera9": {
"projection": "incorrect"
},
"camera10": {
"projection": "unknown"
}
},
"land_mark": [
{
"type": "straight_left",
"worn_status": "clear",
"bbox": [
[
[
-24.597306729965915,
-15.434656297817597,
-1.8917511641203855
],
[
-24.549893497090206,
-14.455544842156652,
-1.8872655457482166
]
],
[
[
-24.549893497090206,
-14.455544842156652,
-1.8872655457482166
],
[
-21.554037891027903,
-14.60055898849593,
-1.9002375807544079
]
],
[
[
-21.554037891027903,
-14.60055898849593,
-1.9002375807544079
],
[
-21.60145112390361,
-15.579670444156875,
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
-1.9047231991265767
]
],
[
[
-21.60145112390361,
-15.579670444156875,
-1.9047231991265767
],
[
-24.597306729965915,
-15.434656297817597,
-1.8917511641203855
]
]
],
"bbox_visible": [
""
],
"center_points": [
[
[
-24.58789988725534,
-15.240399401569437,
-1.8908612120136123
],
[
-23.6656504691795,
-14.498346553824415,
-1.8910943122244204
]
],
[
[
-24.58789988725534,
-15.240399401569437,
-1.8908612120136123
],
[
-21.590941206484093,
-15.362634399167602,
-1.903728888555295
]
]
],
"points_visible": [
[
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
[
""
],
[
""
]
],
[
[
""
],
[
""
]
]
],
"passable": [
[],
[]
]
},
{
"type": "forbidden_left",
"worn_status": "clear",
"bbox": [
[
[
1,
2,
3
],
[
4,
5,
6
]
],
[
[
4,
5,
6
],
[
7,
8,
9
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
]
],
[
[
7,
8,
9
],
[
10,
11,
12
]
],
[
[
10,
11,
12
],
[
1,
2,
3
]
]
],
"bbox_visible": [
"camera1"
],
"center_points": [
[
[
1,
2,
3
]
],
[
[
1,
2,
3
],
[
7,
8,
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
9
]
],
[
[
1,
2,
3
],
[
7,
8,
9
]
]
],
"points_visible": [
[
[
"camera0",
"camera7"
]
],
[
[
"camera0"
],
[
"camera0"
]
],
[
[
"camera0"
],
[
"camera0"
]
]
],
"passable": []
},
{
"type": "intersection",
"worn_status": "clear",
"bbox": [
[
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
[
-19.68243397759422,
-1.1007637765589795,
-1.8604830566946462
],
[
-13.298094248814357,
-1.6591012881587348,
-1.8893188267928596
]
],
[
[
-13.298094248814357,
-1.6591012881587348,
-1.8893188267928596
],
[
-12.977401113152748,
3.8705189160475513,
-1.8642028028679853
]
],
[
[
-12.977401113152748,
3.8705189160475513,
-1.8642028028679853
],
[
-19.190179330162422,
4.288481655783489,
-1.8367411615845102
]
],
[
[
-19.190179330162422,
4.288481655783489,
-1.8367411615845102
],
[
-19.68243397759422,
-1.1007637765589795,
-1.8604830566946462
]
]
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
],
"bbox_visible": [
""
],
"center_points": [
[
[
-16.243396856783455,
1.539857402988849,
-1.861956774775487
]
]
],
"points_visible": [
[
[
""
],
[
""
]
],
[
[
""
],
[
""
]
],
[
[
""
],
[
""
]
],
[
[
""
],
[
""
]
]
],
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
"passable": [
[
1
],
[
1
],
[
1
],
[
0
]
]
}
]
}
}
}
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
*******.3、格式要求​
ext_in
fo​
sema
ntic_
versi
on​
land
_ma
rk​
type cente
r_poi
nts​
point
s_visi
ble​
bbox​ bbox
_visi
ble​
worn
_stat
us​
pass
able​
is_sh
aped
_mar
k​
含义​ 投影是
否准确​
版本
号​
路面
标识​
类型​ 中心
点​
点在
相机
中的
可见
性​
外接
矩形
框​
框在
相机
中的
可见
性​
磨损
程度​
是否
可通
行​
是否
异形
标志​
格式​ list​ list​ str​ list​ list​ list​ list​ str​ int​ bool​
层级​ In
annot
ated_i
nfo​
In
ann
otat
ed_i
nfo​
Inland_mark​ In
land_
mark​
In
land_
mark​
In
land_
mark​
In
land_
mark​
In
land_
mark​
In
land_
mark​
In
land_
mark​
值的
范围​
"came
ra0":
{"proj
在标
注范
围更
type,
cent
er_p
'straight','strai
ght_left','strai
ght_right','str
值为
float
，包
值为
str，
包括
值为
float
，包
值为
str,ca
mera
'clear
','slig
ht_w
0,1,2​ true
，
false​
ection
":}​
"came
ra6":
{"proj
ection
":}​
"came
ra7":
{"proj
ection
":}​
"came
ra8":
{"proj
ection
":}​
"came
ra9":
{"proj
ection
":}​
"came
ra10":
{"proj
ection
":}​
该成员
的值
为："c
orrect
"，"in
correc
t"，"u
nkno
wn",""​
新时
会改
动，
正常
都是
默认
值​
oints
,bbo
x,wo
rn_st
atus,
pass
able​
aight_u_turn',
'straight_left_
right','straight
_left_u_turn','
left','left_u_tu
rn','left_c_tur
n','right','right
_u_turn','right
_c_turn','left_
right','u_turn','
no_stop','cros
s','no_left_tur
n','no_right_t
urn','no_turn','
crosswalk','st
op_line','spee
d_bump','dec
eleration_dia
mond_shape
d','deceleratio
n_inverted_tr
iangle','stop_s
ign','intersecti
on','bus_lanes
','pillar','non_
motorized_ve
hicle_sign','si
dewalk_sign','
center_circle'​
括
x,y,z​
came
ra0、
6-10​
括
x,y,z​
0、6-
10​
orn','
heav
y_wo
rn','u
nkno
wn'​
含
义：
清
晰，
轻微
磨
损，
严重
磨
损，
无法
判断​
0代表
不可
通
行，1
代表
可通
行，2
代表
不确
定​
特殊
案例​
多帧
拼接
时默
认输
出""
多帧
拼接
时默
认输
出""
当类
别
是'int
ersec
tion'
，拆
到单
帧才
开始
标注
该属
性​
如："
point
s_visi
ble":
[​
[
[""],​
[""]​
]]​
，拆
到单
帧才
开始
标注
该属
性​
如："
point
s_visi
ble":
[​
[
[""],​
[""]​
]]​
才需
要判
断
pass
able
；其
他类
别默
认输
出
为"pa
ssabl
e":[]​
*******、DrivingFreeSpace行车可行驶区域​
*******.1、Clip示例JSON​
若无标注结果annotated_info的空结构：​
{
"ext_info":{},
"free_space": []
}
1
2
3
4
{
"driving_free_space_clip_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2024-05-06T00:54:27",
"clip_id": "对应帧数据行的 clip",
"check_no": "clipId",
"semantic_version": "v20240507",
"annotated_info": {
"ext_info": {
"camera0": {
"projection": "correct"
},
"camera6": {
"projection": "incorrect"
},
"camera7": {
"projection": "unknown"
},
"camera8": {
"projection": "correct"
},
"camera9": {
"projection": "incorrect"
},
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
"camera10": {
"projection": "unknown"
}
},
"free_space": [
{
"category": "person",
"points": [
[
1.11,
2,
3
],
[
2.22,
3,
4
],
[
1.11,
2,
3
],
[
2.22,
3,
4
]
]
},
{
"category": "free_space",
"points": [
[
1.11,
2,
3
],
[
2.2222,
3,
4
],
[
1.1111,
2,
3
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
],
[
2.2222,
3,
4
]
]
},
{
"category": "unknown",
"points": [
[
1.2222,
2,
3
],
[
2.222,
3,
4
],
[
1.333,
2,
3
],
[
2.2222,
3,
4
]
]
}
]
}
}
}
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
*******.2、Frame示例JSON​
若无标注结果annotated_info的空结构：​
{
"ext_info":{},
1
2
"free_space": []
}
3
4
{
"driving_free_space_frame_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2024-05-06T00:54:27",
"clip_id": "对应帧数据行的 clip",
"frame_id": "数据行的 frame",
"check_no": "clipId",
"semantic_version": "v20240507",
"annotated_info": {
"ext_info": {
"camera0": {
"projection": "correct"
},
"camera6": {
"projection": "incorrect"
},
"camera7": {
"projection": "unknown"
},
"camera8": {
"projection": "correct"
},
"camera9": {
"projection": "incorrect"
},
"camera10": {
"projection": "unknown"
}
},
"free_space": [
{
"category": "person",
"points": [
[
1.11,
2,
3
],
[
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
2.22,
3,
4
],
[
1.11,
2,
3
],
[
2.22,
3,
4
]
]
},
{
"category": "free_space",
"points": [
[
1.11,
2,
3
],
[
2.2222,
3,
4
],
[
1.1111,
2,
3
],
[
2.2222,
3,
4
]
]
},
{
"category": "unknown",
"points": [
[
1.2222,
2,
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
3
],
[
2.222,
3,
4
],
[
1.333,
2,
3
],
[
2.2222,
3,
4
]
]
}
]
}
}
}
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
*******、GOP静态目标物​
*******.1、Clip示例JSON​
若无标注结果annotated_info的空结构：​
{
"gop_object_detection_clip_annotated_info": {
"annotator": "标注员",
"annotation_qa": "标注QA",
"annotation_date": "2024-05-06T00:54:27", // 标注时间，请按格式转换​
"check_no": "对应帧数据行的 clipId",
"clip_id": "对应帧数据行的 clipId",
"semantic_version": "v20240507",
"annotated_info": {
"3d_object_detection_info": {
"basic_info": {
"annotation_date": "2024-05-06T00:54:27"// 标注时间，请按格
式转换​
},
"ext_info": {
"exception_flag": false
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
},
"3d_object_detection_anns_info": []
}
}
}
}
16
17
18
19
20
21
{
"gop_object_detection_clip_annotated_info": {
"annotator": "标注员",
"annotation_qa": "标注QA",
"annotation_date": "2024-05-06T00:54:27",// 标注时间，请按格式转换​
"check_no": "对应帧数据行的 clipId",
"clip_id": "对应帧数据行的 clipId",
"semantic_version": "v20240507",
"annotated_info": {
"3d_object_detection_info": {
"basic_info": {
"annotation_date": "2024-05-06T00:54:27"// 标注时间，请按格
式转换​
},
"ext_info": {
"exception_flag": false
},
"3d_object_detection_anns_info": [
{
"size": [
4.302217521462755,
1.8,
1.5139830367586726
],
"obj_center_pos": [
72.94021089966282,
3.696936231664625,
-1.165984623301676
],
"obj_rotation": [
0.0012672878488797833,
-0.0007546878638219518,
-0.0014036121169581398,
0.9999979271482323
],
"category": "car",
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
"subcategory": "car",
"track_id": 1,
"group_id": 1,
"is_double_image": false,
"is_group": false,
"num_lidar_pts": 27,
"num_radar_pts": -1,
"lane_id": [
1
],
"is_crop": false,
"signal": "normal"
},
{
"size": [
4.778677786906435,
1.9474448596105933,
1.6714912413811058
],
"obj_center_pos": [
1.2642261275331408,
3.4894973909212337,
-1.2476808886321433
],
"obj_rotation": [
-0.002093905940891015,
0.0011792803505163202,
0.0072522469323475,
0.9999708144592011
],
"category": "car",
"subcategory": "car",
"track_id": 2,
"group_id": 2,
"is_double_image": false,
"is_group": false,
"num_lidar_pts": 5734,
"num_radar_pts": -1,
"lane_id": [
1
],
"is_crop": true,
"signal": "unknown"
},
{
"size": [
12,
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
2.5,
3.5
],
"obj_center_pos": [
-56.77242330902409,
40.11005092285775,
-0.970341732491503
],
"obj_rotation": [
0.0014138189858667548,
-0.0026026541860189943,
0.7265867567049812,
0.6870683461548782
],
"category": "bus",
"subcategory": "bus",
"track_id": 8,
"group_id": 8,
"is_double_image": false,
"is_group": false,
"num_lidar_pts": 194,
"num_radar_pts": -1,
"lane_id": [
-100
],
"is_crop": true,
"signal": "unknown"
},
{
"size": [
12,
2.7189871591716464,
3.5
],
"obj_center_pos": [
-60.65684474414331,
40.34174168164523,
-0.9258173206386808
],
"obj_rotation": [
0.002593709474106762,
0.0014301622637757838,
-0.6916251386977924,
0.7222505762049086
],
"category": "bus",
"subcategory": "bus",
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
"track_id": 9,
"group_id": 9,
"is_double_image": false,
"is_group": false,
"num_lidar_pts": 56,
"num_radar_pts": -1,
"lane_id": [
-100
],
"is_crop": true,
"signal": "unknown"
}
]
}
}
}
}
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
*******.2、Frame示例JSON​
若无标注结果annotated_info的空结构：​
{
"gop_object_detection_frame_annotated_info": {
"annotator": "标注员",
"annotation_qa": "标注QA",
"annotation_date": "2024-05-06T00:54:27",// 标注时间，请按格式转换​
"check_no": "对应帧数据行的 clipId",
"clip_id": "对应帧数据行的 clipId",
"check_no":"对应帧数据行的 Frame ID",
"semantic_version": "v20240507",
"annotated_info": {
"3d_object_detection_info": {
"basic_info": {
"annotation_date": "2024-05-06T00:54:27"// 标注时间，请按格
式转换​
},
"ext_info": {
"exception_flag": false
},
"3d_object_detection_anns_info": []
}
}
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
}
22
{
"gop_object_detection_frame_annotated_info": {
"annotator": "标注员",
"annotation_qa": "标注QA",
"annotation_date": "2024-05-06T00:54:27",// 标注时间，请按格式转换​
"check_no": "对应帧数据行的 clipId",
"clip_id": "对应帧数据行的 clipId",
"check_no":"对应帧数据行的 Frame ID",
"semantic_version": "v20240507",
"annotated_info": {
"3d_object_detection_info": {
"basic_info": {
"annotation_date": "2024-05-06T00:54:27"// 标注时间，请按格
式转换​
},
"ext_info": {
"exception_flag": false
},
"3d_object_detection_anns_info": [
{
"size": [
4.302217521462755,
1.8,
1.5139830367586726
],
"obj_center_pos": [
72.94021089966282,
3.696936231664625,
-1.165984623301676
],
"obj_rotation": [
0.0012672878488797833,
-0.0007546878638219518,
-0.0014036121169581398,
0.9999979271482323
],
"category": "car",
"subcategory": "car",
"track_id": 1,
"group_id": 1,
"is_double_image": false,
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
"is_group": false,
"num_lidar_pts": 27,
"num_radar_pts": -1,
"lane_id": [
1
],
"is_crop": false,
"signal": "normal"
},
{
"size": [
4.778677786906435,
1.9474448596105933,
1.6714912413811058
],
"obj_center_pos": [
1.2642261275331408,
3.4894973909212337,
-1.2476808886321433
],
"obj_rotation": [
-0.002093905940891015,
0.0011792803505163202,
0.0072522469323475,
0.9999708144592011
],
"category": "car",
"subcategory": "car",
"track_id": 2,
"group_id": 2,
"is_double_image": false,
"is_group": false,
"num_lidar_pts": 5734,
"num_radar_pts": -1,
"lane_id": [
1
],
"is_crop": true,
"signal": "unknown"
},
{
"size": [
12,
2.5,
3.5
],
"obj_center_pos": [
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
-56.77242330902409,
40.11005092285775,
-0.970341732491503
],
"obj_rotation": [
0.0014138189858667548,
-0.0026026541860189943,
0.7265867567049812,
0.6870683461548782
],
"category": "bus",
"subcategory": "bus",
"track_id": 8,
"group_id": 8,
"is_double_image": false,
"is_group": false,
"num_lidar_pts": 194,
"num_radar_pts": -1,
"lane_id": [
-100
],
"is_crop": true,
"signal": "unknown"
},
{
"size": [
12,
2.7189871591716464,
3.5
],
"obj_center_pos": [
-60.65684474414331,
40.34174168164523,
-0.9258173206386808
],
"obj_rotation": [
0.002593709474106762,
0.0014301622637757838,
-0.6916251386977924,
0.7222505762049086
],
"category": "bus",
"subcategory": "bus",
"track_id": 9,
"group_id": 9,
"is_double_image": false,
"is_group": false,
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
"num_lidar_pts": 56,
"num_radar_pts": -1,
"lane_id": [
-100
],
"is_crop": true,
"signal": "unknown"
}
]
}
}
}
}
135
136
137
138
139
140
141
142
143
144
145
146
147
*******、CITY_4D_LANE​
*******.1、Clip示例JSON​
*******.1.1、若无标注结果annotated_info的空结构​
{
"city_4d_lane_clip_annotated_info": {
"annotator": "标注员",
"annotation_qa": "标注QA",
"annotation_date": "2024-05-06T00:54:27",// 标注时间，请按格式转换​
"check_no": "对应帧数据行的 clipId",
"clip_id": "对应帧数据行的 clipId",
"check_no":"对应帧数据行的 Frame ID",
"semantic_version": "v20240507",
"filter": [
]
"ext_info": {
"optimized_calibration_url": "http://xxx/sss/extrinsics.zip"
},
"annotated_info": {
"keypoints": [],
"lines": [],
"road_marking": [],
"guide_lines": [],
"roadedge":[]
}
}
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
完整实例
city_4d_lane_clip_annotated_info.json
pose结果示例：​
{
"poses": {
"sample_1722340351300193": {
"timestamp": 1722340351324842,
"name": "sample_1722340351300193",
"position": {
"x": 333512.263939424,
"y": 3467710.74518867,
"z": 4.408584994768713
},
"orientation": {
"qx": 0.0167974152290696,
"qy": 0.0062067053559419,
"qz": 0.852520590542123,
"qw": 0.522386797643237
}
}
}
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
*******.1.2、回传优化后相机外参​
{
"city_4d_lane_clip_annotated_info": {
"ext_info": {
"optimized_calibration_url": "http://xxx/sss/extrinsics.zip"
}
}
}
1
2
3
4
5
6
7
要求：
1、仅包含lidar2camera文件夹​
2、需完整包含11个相机lidar2xx.yaml文件，即使没有部分相机没有调整外参，也需要包含原本的
yaml。​
3、外参文件格式与送标提供的外参文件格式一致。​
4、zip文件层级及文件内容请按约定实现​
5、调整后外参zip下载URL为可授权访问，无需额外的鉴权，请确保下载链接稳定性。​
内部文件结构：
Zip样例文件：​
extrinsics.zip
4.59KB
*******.2、Frame示例JSON​
若无标注结果annotated_info的空结构：​
{
"city_4d_lane_frame_annotated_info": {
"annotator": "标注员",
"annotation_qa": "标注QA",
"annotation_date": "2024-05-06T00:54:27",// 标注时间，请按格式转换​
"check_no": "对应帧数据行的 clipId",
"clip_id": "对应帧数据行的 clipId",
"check_no":"对应帧数据行的 Frame ID",
"semantic_version": "v20240507",
"annotated_info": {
"keypoints": [],
"lines": [],
"road_marking": [],
"guide_lines": []
}
}
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
{
"city_4d_lane_frame_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2024-10-11T15:46:41",
"clip_id": "fffd7bf712173f6d882e0589f754cc98",
"check_no": "fffd7bf712173f6d882e0589f754cc98",
"frame_id": "",
"annotated_info": {
"keypoints": [
{
"position": [
11,
22,
33
],
"type": "divide",
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
"occlusion": true
},
{
"position": [
11,
22,
33
],
"type": "merge",
"occlusion": true
}
],
"road_marking": [
{
"type": "straight",
"worn_status": "clear",
"road_marking_id": "12",
"center_points": [
[
-458.3775259740583,
-5493.141575330933,
1.8494458389000967
],
[
-458.1116066671079,
-5499.************,
1.904452449036618
]
],
"points": [
[
-457.6136886638065,
-5499.************,
1.904452449036618
],
[
-458.7080404585201,
-5499.************,
1.904452449036618
],
[
-458.8880330711769,
-5493.156159346676,
1.8494458389000967
],
[
-457.7936812764633,
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
-5493.124896228313,
1.8494458389000967
],
[
-457.6136886638065,
-5499.************,
1.904452449036618
]
]
}
],
"guide_lines": [
{
"guide_line_id": "1419057194716306",
"predguide_line": [
"1419057194716523"
],
"succguide_line": [
"1419057194716422"
],
"left_line_id": [],
"right_line_id": [
"1419057194716307"
],
"points": [
[
4.735618615406565,
-7.53541045030579,
-0.18502279134464406
],
[
-3.0563812137115747,
-7.197876436170191,
-0.1357281573483533
],
[
-10.847526899538934,
-6.838621675036848,
-0.07815532433154893
],
[
-12.633159767021425,
-6.757287297397852,
-0.06819593126646328
],
[
-14.415939468191937,
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
-6.67326996056363,
-0.05831324957094086
],
[
-16.201052639517002,
-6.588757106103003,
-0.048425965636127444
],
[
-17.969421677524224,
-6.502994853071868,
-0.03867556100665226
],
[
-19.745394147059415,
-6.424199603497982,
-0.028724679461713976
],
[
-21.531147496541962,
-6.340509577188641,
-0.018815410289291457
],
[
-23.309888113231864,
-6.255204373970628,
-0.008987068699792822
],
[
-25.06280834041536,
-6.167459397576749,
0.0006191065716514288
],
[
-26.83488921634853,
-6.078497891314328,
0.010324720440104684
],
[
-28.6165354057448,
-5.9903171779587865,
0.02010997777211987
],
[
-30.366194691159762,
-5.90073618106544,
0.029655071463142946
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
],
[
-32.156590867671184,
-5.81147438660264,
0.03947438002078307
],
[
-33.954649939900264,
-5.721956993453205,
0.049338448161702075
],
[
-35.727254086057656,
-5.6371811563149095,
0.05391699290689722
],
[
-37.49848155071959,
-5.551538705360144,
0.05921938569841512
],
[
-39.29178213526029,
-5.460164242424071,
0.06449905258847988
],
[
-41.06877191120293,
-5.372665039263666,
0.06978865192980965
],
[
-42.86967377481051,
-5.2882567839697,
0.07523068991021287
],
[
-44.643227948457934,
-5.196011127438396,
0.08041648155407977
],
[
-46.40577826742083,
-5.102061602752656,
0.08552676548057381
],
[
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
-48.204200144158676,
-5.000863475725055,
0.09063946035044879
],
[
-49.99883903353475,
-4.897592164576054,
0.09569788001652846
],
[
-51.77977819275111,
-4.804136965889484,
0.10088955086784779
],
[
-53.56151539448183,
-4.713545733131468,
0.10613886790421834
],
[
-55.35765839961823,
-4.61857790639624,
0.11136125195360869
],
[
-57.13078265992226,
-4.527725279796869,
0.1165718806094942
],
[
-58.90965395217063,
-4.436677637510002,
0.12180129124033723
],
[
-60.71613546588924,
-4.3473240695893764,
0.12717102262086932
],
[
-62.49584607849829,
-4.25457077100873,
0.132371247130056
],
[
-64.3033497215365,
-4.159171734005213,
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
0.1376298889799159
],
[
-66.08767784642987,
-4.061255667824298,
0.15879882679172308
],
[
-67.88069199840538,
-3.96193651529029,
0.16287529753807561
],
[
-69.68328173924237,
-3.8622654071077704,
0.16697706309674665
],
[
-71.50208893918898,
-3.7658859114162624,
0.17119844857062194
],
[
-73.28370988694951,
-3.6608777730725706,
0.1751241982244487
],
[
-75.0606247629039,
-3.5622512605041265,
0.17916013395670305
],
[
-76.85216970019974,
-3.4645310076884925,
0.18326323455036597
],
[
-78.62961288436782,
-3.3557071229442954,
0.18709955661437228
],
[
-80.44043751218123,
-3.2511055604554713,
0.19113167925029373
],
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
[
-82.26440941996407,
-3.1486648274585605,
0.19525075062272723
],
[
-84.06799064693041,
-3.054368899669498,
0.19946201200280989
],
[
-85.86591982026584,
-2.9536120453849435,
0.2327907039067707
],
[
-87.67629811639199,
-2.852407983969897,
0.23563291451998136
],
[
-89.46464489970822,
-2.7525520492345095,
0.23844285123458864
],
[
-91.25488471606513,
-2.6432778048329055,
0.24107077608413086
],
[
-93.06399215431884,
-2.535798310767859,
0.24378492476394964
],
[
-94.88741474691778,
-2.429981202352792,
0.240445842657234
],
[
-97.52227057004347,
-2.2652183626778424,
0.24250768528364297
],
[
-100.31374507991131,
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
-2.0999666056595743,
0.24488151521394208
],
[
-102.10941790539073,
-1.9879727945663035,
0.24629264508585003
],
[
-104.72623740707058,
-1.8215420343913138,
0.24828345680196318
],
[
-107.47679549717577,
-1.6415210752747953,
0.2502725045057268
],
[
-110.04657640121877,
-1.4678290337324142,
0.25201879782289094
],
[
-112.98145927680889,
-1.2837879615835845,
0.25430489544926527
],
[
-116.61147233372321,
-1.0600428199395537,
0.25721157532522554
],
[
-119.01361128670396,
-0.9115716661326587,
0.25912672287832894
],
[
-120.91536975116469,
-0.7940708049573004,
0.26064380517791186
],
[
-123.79093808785547,
-0.6059980471618474,
0.27884130700238696
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
],
[
-125.58359482279047,
-0.487706845626235,
0.2788326701566355
],
[
-127.39350291609298,
-0.3685416290536523,
0.2788294834474341
],
[
-130.13132101553492,
-0.1893887580372393,
0.2788478217674557
],
[
-132.7954059439944,
-0.01499280845746398,
0.27886424467711013
],
[
-135.5334395871032,
0.16925169713795185,
0.2787763016680218
],
[
-138.1761923196027,
0.3483373452909291,
0.27866519808045354
],
[
-140.9049139780691,
0.528937273658812,
0.2786407261667634
],
[
-143.55821064463817,
0.7137444308027625,
0.27842437281348253
],
[
-146.32353629276622,
0.9062695414759219,
0.28613045317684627
],
[
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
410
411
412
413
414
415
416
417
418
419
420
421
422
423
424
425
426
427
428
429
430
431
432
433
434
435
436
437
438
439
440
-148.91263017232995,
1.0849086381495,
0.28340022115415753
],
[
-151.27569513273193,
1.252405992243439,
0.28081526476266827
],
[
-152.47250694129616,
1.3307865005917847,
0.26978485542313013
],
[
-154.2537543794606,
1.460809137672186,
0.2651173775252351
],
[
-156.70852392585948,
1.634110972750932,
0.25881170962105315
],
[
-157.77713740919717,
1.7122228010557592,
0.25600924979430406
],
[
-159.5455273538828,
1.8357250420376658,
0.25149561206210347
],
[
-161.31188012752682,
1.9552436415106058,
0.24706985920608648
],
[
-163.05088066775352,
2.085761398077011,
0.24243604615167325
],
[
-164.81236738309963,
2.2073227581568062,
441
442
443
444
445
446
447
448
449
450
451
452
453
454
455
456
457
458
459
460
461
462
463
464
465
466
467
468
469
470
471
472
473
474
475
476
477
478
479
480
481
482
483
484
485
486
487
0.23797142810312621
],
[
-166.5618368863361,
2.332629854325205,
0.23343879090992647
],
[
-168.29599881643662,
2.457258411683142,
0.22893681879177663
],
[
-170.0482715475955,
2.5804653028026223,
0.20961941512732984
],
[
-171.7799447843572,
2.703968368470669,
0.20378643807045727
],
[
-173.51086051145103,
2.8287241370417178,
0.19792684433247487
],
[
-175.2362686250708,
2.9598964364267886,
0.19193380677159322
],
[
-176.96251802926417,
3.087771149352193,
0.1860128823105054
],
[
-178.66460511437617,
3.2072965214028955,
0.18032125154064538
],
[
-180.3865755298175,
3.334472317714244,
0.17442352934487193
],
488
489
490
491
492
493
494
495
496
497
498
499
500
501
502
503
504
505
506
507
508
509
510
511
512
513
514
515
516
517
518
519
520
521
522
523
524
525
526
527
528
529
530
531
532
533
534
[
-182.08719972678227,
3.4674633438698947,
0.1807872832859898
],
[
-183.80188084184192,
3.5927668204531074,
0.17433862230906882
],
[
-185.4917714589974,
3.7191416663117707,
0.1679185367035183
],
[
-187.18030027701752,
3.848165155388415,
0.16144193828167896
],
[
-188.87587989703752,
3.9779040864668787,
0.15493433305252946
],
[
-190.55416411685292,
4.104599700309336,
0.148531681312015
],
[
-192.24217496957863,
4.236204055137932,
0.14199829966108268
],
[
-193.9395671985112,
4.363247077912092,
0.13554731066917025
],
[
-195.59544890018879,
4.488904946949333,
0.12921546929830185
],
[
-197.27555507363286,
535
536
537
538
539
540
541
542
543
544
545
546
547
548
549
550
551
552
553
554
555
556
557
558
559
560
561
562
563
564
565
566
567
568
569
570
571
572
573
574
575
576
577
578
579
580
581
4.617640755139291,
0.12276319505747058
],
[
-198.96239783091005,
4.747812896966934,
0.11626441295497969
],
[
-200.60336133517558,
4.876331539824605,
0.10990006682848819
],
[
-202.26106582628563,
5.005187748000026,
0.13649890391749242
],
[
-203.90199313429184,
5.130882266908884,
0.12980003709299037
],
[
-205.53520810359623,
5.254539436660707,
0.12316588097324743
],
[
-207.17738412250765,
5.3880788260139525,
0.1162839276642309
],
[
-208.79750267253257,
5.518895294051617,
0.10951575510634992
],
[
-210.40714798111003,
5.642943228594959,
0.08460710905974267
],
[
-212.01738369115628,
5.774105003569275,
0.0770607205327769
582
583
584
585
586
587
588
589
590
591
592
593
594
595
596
597
598
599
600
601
602
603
604
605
606
607
608
609
610
611
612
613
614
615
616
617
618
619
620
621
622
623
624
625
626
627
628
],
[
-213.6387262659846,
5.896917551290244,
0.06967474471759072
],
[
-215.25763594696764,
6.030178315937519,
0.062055735667640555
],
[
-216.85329426254611,
6.15789407864213,
0.0655366246597735
],
[
-218.4642011487158,
6.278510247822851,
0.05755739159792572
],
[
-220.05656050599646,
6.412679634988308,
0.04932992549646453
],
[
-221.62733154103626,
6.531885604839772,
0.04151319162055067
],
[
-223.20984204194974,
6.658340951427817,
0.033493308632179364
],
[
-224.78305365284905,
6.786868580151349,
0.02545646900685572
],
[
-226.3530888572568,
6.921728685032576,
0.017285811872620016
],
[
629
630
631
632
633
634
635
636
637
638
639
640
641
642
643
644
645
646
647
648
649
650
651
652
653
654
655
656
657
658
659
660
661
662
663
664
665
666
667
668
669
670
671
672
673
674
675
-227.9199621132575,
7.049749763216823,
0.009281095335440526
],
[
-229.48396803904325,
7.177623802796006,
-0.0043539370157610335
],
[
-237.34588789602276,
7.822599456179887,
-0.04840136161698272
],
[
-245.2044178812066,
8.515516921877861,
-0.09355267073003848
]
],
"continue_id": 12
},
{
"guide_line_id": "1419057194716307",
"predguide_line": [
"1419057194716524"
],
"succguide_line": [
"1419057194716423"
],
"left_line_id": [
"1419057194716306"
],
"right_line_id": [
"1419057194716308"
],
"continue_id": 12,
"points": [
[
4.895637919194996,
-3.7356915078125894,
-0.2711651328438025
],
[
-4.754404725157656,
-3.301755099091679,
-0.21047186707595245
676
677
678
679
680
681
682
683
684
685
686
687
688
689
690
691
692
693
694
695
696
697
698
699
700
701
702
703
704
705
706
707
708
709
710
711
712
713
714
715
716
717
718
719
720
721
722
],
[
-14.40399613219779,
-2.8562781722284853,
-0.14089117569215226
],
[
-17.411287089111283,
-2.7202604827471077,
-0.12409716607733401
],
[
-20.4006407140987,
-2.5843586516566575,
-0.10741835727060334
],
[
-24.88185974082444,
-2.3538671187125146,
-0.08299435696214541
],
[
-29.373653309361544,
-2.1238888683728874,
-0.05848987245988013
],
[
-32.41182990861125,
-1.9795142686925828,
-0.04167381536539061
],
[
-35.44027062470559,
-1.8352415673434734,
-0.019585482271772925
],
[
-39.93776735756546,
-1.6103852153755724,
-0.006262406975792167
],
[
-44.43532735633198,
-1.3855346515774727,
0.007061026156687689
],
[
723
724
725
726
727
728
729
730
731
732
733
734
735
736
737
738
739
740
741
742
743
744
745
746
747
748
749
750
751
752
753
754
755
756
757
758
759
760
761
762
763
764
765
766
767
768
769
-47.455604507937096,
-1.2164874011650681,
0.015664541745584515
],
[
-50.47410408518044,
-1.0473782317712903,
0.024259920945816305
],
[
-54.961478074779734,
-0.817781139165163,
0.037453122964924646
],
[
-59.44916604628088,
-0.5882032043300569,
0.05064791871988028
],
[
-62.448639948270284,
-0.43121755961328745,
0.059399629883246874
],
[
-65.44173651578603,
-0.2741147270426154,
0.08182956909780703
],
[
-69.94038342765998,
-0.02415128517895937,
0.09204210755132358
],
[
-74.44107337144669,
0.22574700275436044,
0.10226281267450243
],
[
-77.4539875681512,
0.401099578011781,
0.1089456743838273
],
[
-80.46285341179464,
0.5764993415214121,
770
771
772
773
774
775
776
777
778
779
780
781
782
783
784
785
786
787
788
789
790
791
792
793
794
795
796
797
798
799
800
801
802
803
804
805
806
807
808
809
810
811
812
813
814
815
816
0.11561397131288054
],
[
-84.94022820109967,
0.8239112324081361,
0.15527251974558087
],
[
-89.41253365867306,
1.0713964165188372,
0.16234401735439086
],
[
-92.45181685313582,
1.2530996617861092,
0.16688114141593413
],
[
-95.49034378526267,
1.4348098910413682,
0.16300467972721489
],
[
-99.95712757844012,
1.706998746842146,
0.16664516975444066
],
[
-104.4158538322663,
1.9792463327758014,
0.17026790131354463
],
[
-107.45601997349877,
2.1836140835657716,
0.17235659978472828
],
[
-110.50355156743899,
2.3879457907751203,
0.1744611722229399
],
[
-114.86961162119405,
2.6485493844375014,
0.17813047363160628
],
817
818
819
820
821
822
823
824
825
826
827
828
829
830
831
832
833
834
835
836
837
838
839
840
841
842
843
844
845
846
847
848
849
850
851
852
853
854
855
856
857
858
859
860
861
862
863
[
-119.23007625888567,
2.908873523119837,
0.181793962474055
],
[
-122.27068113192217,
3.106263082008809,
0.18402562679359313
],
[
-125.30048412352335,
3.3030472937971354,
0.19909499273574038
],
[
-129.8838787386194,
3.6114936815574765,
0.19894723330965558
],
[
-134.47024116071407,
3.9199431985616684,
0.1988034933340339
],
[
-137.51148272503633,
4.127377687487751,
0.19864747674772776
],
[
-140.54898258298635,
4.3348137703724205,
0.1984862766130986
],
[
-145.0071892197593,
4.6456183437258005,
0.2074712827309506
],
[
-149.46594129642472,
4.956420772708952,
0.20270336942119016
],
[
-152.49648420908488,
864
865
866
867
868
869
870
871
872
873
874
875
876
877
878
879
880
881
882
883
884
885
886
887
888
889
890
891
892
893
894
895
896
897
898
899
900
901
902
903
904
905
906
907
908
909
910
5.1659504398703575,
0.18720806885417485
],
[
-155.52349678898463,
5.375445449259132,
0.1795229914442933
],
[
-160.00552378199063,
5.695555347949266,
0.16793044547622404
],
[
-164.48821157228667,
6.0156553783454,
0.15633741865643724
],
[
-167.51865361788077,
6.229420464951545,
0.14855683018381782
],
[
-170.54421718697995,
6.44314754428342,
0.12251692543194714
],
[
-175.01316417730413,
6.773473283741623,
0.1072047678232213
],
[
-179.48064057790907,
7.10377804050222,
0.09189569103665107
],
[
-182.51092993526254,
7.329280209261924,
0.09327731332201505
],
[
-185.53760639572283,
7.554715605452657,
0.08179899376413591
911
912
913
914
915
916
917
918
919
920
921
922
923
924
925
926
927
928
929
930
931
932
933
934
935
936
937
938
939
940
941
942
943
944
945
946
947
948
949
950
951
952
953
954
955
956
957
],
[
-190.01199953566538,
7.895747858565301,
0.06465618586933797
],
[
-194.48565522418357,
8.236773374024779,
0.0475150939303246
],
[
-197.52208986866754,
8.47133198613301,
0.035811457414760106
],
[
-200.55808818741934,
8.705886020325124,
0.024108849473136296
],
[
-205.0176215034444,
9.04926167242229,
0.03720872628573613
],
[
-209.47106783970958,
9.392517801839858,
0.01897935123490413
],
[
-212.59008342702873,
9.643621514551342,
-0.013394079638596956
],
[
-215.7073671791004,
9.894764286931604,
-0.01579762654547423
],
[
-220.08260581846116,
10.24595256568864,
-0.038006329805321926
],
[
958
959
960
961
962
963
964
965
966
967
968
969
970
971
972
973
974
975
976
977
978
979
980
981
982
983
984
985
986
987
988
989
990
991
992
993
994
995
996
997
998
999
1000
1001
1002
1003
1004
-224.45813119842205,
10.597146401181817,
-0.060216091151025886
],
[
-227.4930009592208,
10.847205363679677,
-0.07576808786531952
],
[
-236.17039731610566,
11.559537485707551,
-0.13108165870202626
],
[
-244.84602997498587,
12.297379789873958,
-0.18029605828965511
]
]
}
],
"lines": [
{
"line_id": "1419057194715732",
"subtype":"",
"line_key_points": [
[
149.99670548678841,
-14.825044851750135,
-1.3226210135473444
],
[
144.7283068097313,
-14.935055521782488,
-1.274014632021295
],
[
142.93606749631,
-14.988178243394941,
-1.2571829283086196
],
[
135.80657419236377,
-15.100425599142909,
-1.1920976862702135
],
1005
1006
1007
1008
1009
1010
1011
1012
1013
1014
1015
1016
1017
1018
1019
1020
1021
1022
1023
1024
1025
1026
1027
1028
1029
1030
1031
1032
1033
1034
1035
1036
1037
1038
1039
1040
1041
1042
1043
1044
1045
1046
1047
1048
1049
1050
1051
[
124.14393890905194,
-14.686457668431103,
-1.0969143730641457
],
[
105.2305926270783,
-14.122844802681357,
-0.8777705137852578
],
[
94.43086303432938,
-13.801579733844846,
-0.7949731597713532
],
[
85.40035509853624,
-13.51997912209481,
-0.7260081663483291
],
[
80.06722855276894,
-13.330639257561415,
-0.685756568798011
],
[
76.42029324453324,
-13.209366085007787,
-0.6580616292383841
],
[
69.2464029204566,
-12.682756661903113,
-0.6095463819588964
],
[
58.55861696321517,
-12.277064366266131,
-0.5294240770246033
],
[
47.80576872464735,
-11.86351310275495,
-0.4183639089887876
],
[
37.11912705714349,
1052
1053
1054
1055
1056
1057
1058
1059
1060
1061
1062
1063
1064
1065
1066
1067
1068
1069
1070
1071
1072
1073
1074
1075
1076
1077
1078
1079
1080
1081
1082
1083
1084
1085
1086
1087
1088
1089
1090
1091
1092
1093
1094
1095
1096
1097
1098
-11.446769695263356,
-0.3470394228644871
],
[
26.376916255103424,
-11.026783056557178,
-0.2753661608639435
],
[
16.67345354659483,
-10.638378773350269,
-0.21080898047985386
],
[
4.785975651699118,
-9.956260651815683,
-0.13125147980048713
],
[
4.633867378288414,
-9.951551909092814,
-0.13024714543827898
]
],
"group_id":1,
"predline": [],
"succline": [
"1419057194715757"
],
"type": "road_edge",
"diversion_line": false,
"color": "others",
"has_fishbone": false,
"dash_line_start_end": []
},
{
"line_id": "1419057194715734",
"line_key_points": [
[
150.08367727522273,
-10.307478883303702,
-1.4087006535332804
],
[
144.5532875767094,
-10.188899007160217,
-1.3620972427188738
1099
1100
1101
1102
1103
1104
1105
1106
1107
1108
1109
1110
1111
1112
1113
1114
1115
1116
1117
1118
1119
1120
1121
1122
1123
1124
1125
1126
1127
1128
1129
1130
1131
1132
1133
1134
1135
1136
1137
1138
1139
1140
1141
1142
1143
1144
1145
],
[
135.5361655728193,
-10.0062608695589,
-1.28590977390561
],
[
129.47897174721584,
-9.872186371590942,
-1.2349463957516056
],
[
120.46175339666661,
-9.692891118116677,
-1.158694947674281
],
[
114.4612068865681,
-9.554335046093911,
-1.1083164933161518
],
[
105.46610469708685,
-9.324421029072255,
-0.9790619008164878
],
[
99.4233498176327,
-9.139589859638363,
-0.9328394852171762
],
[
90.39967895130394,
-8.852563077583909,
-0.8640434564586332
],
[
84.37665029836353,
-8.640367189887911,
-0.8185509487687286
],
[
75.35722328402335,
-8.332350096665323,
-0.7502246276462747
],
[
1146
1147
1148
1149
1150
1151
1152
1153
1154
1155
1156
1157
1158
1159
1160
1161
1162
1163
1164
1165
1166
1167
1168
1169
1170
1171
1172
1173
1174
1175
1176
1177
1178
1179
1180
1181
1182
1183
1184
1185
1186
1187
1188
1189
1190
1191
1192
69.27666712796781,
-8.119091453030705,
-0.7042776520915366
],
[
60.28944142779801,
-7.782837237231433,
-0.6368026246322964
],
[
54.223888331791386,
-7.538796444423497,
-0.5916171879508676
],
[
45.23597678274382,
-7.18811782123521,
-0.49517213428312434
],
[
39.19977519544773,
-6.961279655341059,
-0.45470982296393725
],
[
30.186256948276423,
-6.595711839385331,
-0.3948410161431326
],
[
24.175211991067044,
-6.357145467307419,
-0.3548076110434337
],
[
15.190784453239758,
-6.020986597985029,
-0.29516161875817115
],
[
9.161763586977031,
-5.769887785427272,
-0.25679502162312673
],
[
4.817090172087774,
-5.600825265981257,
1193
1194
1195
1196
1197
1198
1199
1200
1201
1202
1203
1204
1205
1206
1207
1208
1209
1210
1211
1212
1213
1214
1215
1216
1217
1218
1219
1220
1221
1222
1223
1224
1225
1226
1227
1228
1229
1230
1231
1232
1233
1234
1235
1236
1237
1238
1239
-0.22888121504534453
]
],
"predline": [],
"succline": [
"1419057194715759"
],
"type": "single_dash",
"subtype":"",
"color": "white",
"has_fishbone": false,
"diversion_line": false,
"dash_line_start_end": [
[
[
135.5361655728193,
-10.0062608695589,
-1.28590977390561
],
[
129.47897174721584,
-9.872186371590942,
-1.2349463957516056
]
],
[
[
120.46175339666661,
-9.692891118116677,
-1.158694947674281
],
[
114.4612068865681,
-9.554335046093911,
-1.1083164933161518
]
],
[
[
105.46610469708685,
-9.324421029072255,
-0.9790619008164878
],
[
99.4233498176327,
-9.139589859638363,
-0.9328394852171762
1240
1241
1242
1243
1244
1245
1246
1247
1248
1249
1250
1251
1252
1253
1254
1255
1256
1257
1258
1259
1260
1261
1262
1263
1264
1265
1266
1267
1268
1269
1270
1271
1272
1273
1274
1275
1276
1277
1278
1279
1280
1281
1282
1283
1284
1285
1286
]
],
[
[
90.39967895130394,
-8.852563077583909,
-0.8640434564586332
],
[
84.37665029836353,
-8.640367189887911,
-0.8185509487687286
]
],
[
[
75.35722328402335,
-8.332350096665323,
-0.7502246276462747
],
[
69.27666712796781,
-8.119091453030705,
-0.7042776520915366
]
],
[
[
60.28944142779801,
-7.782837237231433,
-0.6368026246322964
],
[
54.223888331791386,
-7.538796444423497,
-0.5916171879508676
]
],
[
[
45.23597678274382,
-7.18811782123521,
-0.49517213428312434
],
[
39.19977519544773,
-6.961279655341059,
1287
1288
1289
1290
1291
1292
1293
1294
1295
1296
1297
1298
1299
1300
1301
1302
1303
1304
1305
1306
1307
1308
1309
1310
1311
1312
1313
1314
1315
1316
1317
1318
1319
1320
1321
1322
1323
1324
1325
1326
1327
1328
1329
1330
1331
1332
1333
-0.45470982296393725
]
],
[
[
30.186256948276423,
-6.595711839385331,
-0.3948410161431326
],
[
24.175211991067044,
-6.357145467307419,
-0.3548076110434337
]
],
[
[
15.190784453239758,
-6.020986597985029,
-0.29516161875817115
],
[
9.161763586977031,
-5.769887785427272,
-0.25679502162312673
]
]
]
}
]
}
}
}
1334
1335
1336
1337
1338
1339
1340
1341
1342
1343
1344
1345
1346
1347
1348
1349
1350
1351
1352
1353
1354
1355
1356
1357
1358
1359
1360
1361
1362
1363
1364
1365
1366
*******.3、格式要求​
lines​ line_
id​
pred
line​
succ
line​
line_
key_
poin
ts​
dash
_line
_sta
rt_e
nd​
type​ subt
ype​
colo
r​
has_
fish
bon
e​
dive
rsio
n_li
ne​
keyp
oint
s​
posi
tion​
ty
含义​ 车道
线的
车道
线的
前继
车道
后继
车道
车道
线的
虚线
的起
车道
线的
基于
已有
车道
线的
是否
有鱼
是否
导流
合流
点分
坐标​ 类
中心
线​
中心
线的
id​
线中
线​
线中
线​
点​ 止点​ 类型​ 类型
的细
分类​
颜色​ 骨线​ 线​ 流点
和线
性转
换点​
格式​ list​ str​ list​ list​ list
；必
须有
2个
点，
且成
员是
xyz
且为
浮点
数​
list
；必
须有
两个
点，
且成
员是
xyz
且为
浮点
数​
str​ str​ str​ bool​
bool​
list​ list
；且
必须
3个
以上
且为
浮点
数​
st
层级​ In
ann
otat
ed_i
nfo​
In
lines​
In
lines​
In
lines​
In
lines​
In
lines​
In
lines​
In
lines​
In
lines​
In
lines​
In
lines​
In
ann
otat
ed_i
nfo​
In
keyp
oints​
In
ke
oi
值的
范围​
lines
，
line_
key_
poin
ts，
dash
_line
_star
t_en
d，
type
，
colo
r，
has_
fishb
one​
'sing
le_d
ash','
singl
e_so
lid','
dens
e_wi
de_d
ash','
dou
ble_
solid
',​
'dou
ble_
dash
','left
_das
h_ri
ght_
solid
','left
默
认""​
当前
只有
路沿
有细
分
类：
"flat
_cur
b","r
oad_
edge
_sto
ne","
traffi
c_ba
rrier"
,"wal
l","is
olati
on_
'yell
ow','
whit
e','re
d','bl
ue','o
ther
s'​
True
/Fals
e​
True
/Fals
e​
posit
ion,t
ype​
di
e,
rg
da
_s
d​
_soli
d_ri
ght_
dash
','roa
d_ed
ge','o
ther
s','vir
tual
_roa
d_ed
ge','s
top_
lane',
'roa
d_gu
ide_l
ane','
bott
s_do
ts','u
nkn
own',
'reve
rsibl
e_la
ne'​
barri
er","
colu
mn",
"wat
er_b
arrie
r","s
afety
_bar
rier",
"sto
ne"​
特殊
案例​
• 如
果
t
y
p
e
是
s
i
n
g
l
e
_
d
a
s
h
，
d
o
u
b
l
e
road
_edg
e的
颜色
只能
是'ot
hers'​
判断
是否
为导
流线​
_
d
a
s
h
，
d
e
n
s
e
_
w
i
d
e
_
d
a
s
h
,l
e
f
t
_
d
a
s
h
_
r
i
g
h
t
_
s
o
li
d
,l
e
f
t
_
s
o
li
d
_
r
i
g
h
t
_
d
a
s
h
,
则
d
a
s
h
_
li
n
e
_
s
t
a
r
t
_
e
n
d
必
须
有
点
;
反
之
不
会
有
点
*******、3D⻋道线任务​
3D车道线标注主体是点云，点的坐标是X、Y、Z。​
3dlane回标对比.zip
112.39KB
*******.1、Clip示例JSON​
{
"3d_lane_clip_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2022-08-05T15:44:40",
"clip_id": "对应帧数据行的 clip",
"check_no": "clipId",
"semantic_version": "v20241104",
"annotated_info": {
"poses": {},
"flags": [
"virtual_road_edge"
],
"ext_info": {
"camera0": {
"projection": "correct"
},
"camera6": {
"projection": "incorrect"
},
"camera7": {
"projection": "unknown"
},
"camera8": {
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
"projection": "correct"
},
"camera9": {
"projection": "incorrect"
},
"camera10": {
"projection": "unknown"
}
},
"keypoints": [
{
"position": [
11,
22,
33
],
"type": "divide"，
"occlusion": true
},
{
"position": [
11,
22,
33
],
"type": "merge"，
"occlusion": true
}
],
"lines": [
{
"line_id": "1",
"predline": [
"1",
"2"
],
"succline": [
"3",
"4"
],
"line_key_points": [
[
43.35594401056317,
17.367683198169637,
17.367683198169637
],
[
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
47.1199606956959,
17.367683198169637,
17.434382247924802
]
],
"dash_line_start_end": [
[
[
52.502145902186165,
17.50360946655273,
17.50360946655273
],
true,
[
51.79040182705955,
17.50360946655273,
17.50360946655273
],
true
]
],
"type": "single_dash",
"color": "yellow",
"has_fishbone": false,
"diversion_line": false
}
],
"road_marking": [
{
"points": [
[
43.35594401056317,
17.367683198169637,
17.367683198169637
],
[
47.1199606956959,
17.367683198169637,
17.434382247924802
]
],
"center_points": [
[
43.35594401056317,
17.367683198169637,
17.367683198169637
],
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
[
47.1199606956959,
17.367683198169637,
17.434382247924802
]
],
"type": "right",
"road_marking_id": "1",
"worn_status": "clear",
"is_shaped_mark":false
}
]
}
}
}
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
*******.2、Frame示例JSON​
{
"3d_lane_frame_annotated_info": {
"annotator": "标注员",
"annotation_qa": "标注QA",
"annotation_date": "2024-05-06T00:54:27", // 标注时间，请按格式转换​
"check_no": "对应帧数据行的 clipId",
"frame_id": "",
"clip_id": "对应帧数据行的 clipId",
"semantic_version": "v20241104",
"annotated_info": {
"flags": [
"virtual_road_edge"
],
"flags": [
"virtual_road_edge"
],
"ext_info": {
"camera0": {
"projection": "correct"
},
"camera6": {
"projection": "incorrect"
},
"camera7": {
"projection": "unknown"
},
"camera8": {
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
"projection": "correct"
},
"camera9": {
"projection": "incorrect"
},
"camera10": {
"projection": "unknown"
}
},
"keypoints": [
{
"position": [
11,
22,
33
],
"type": "divide"，
"occlusion": true
},
{
"position": [
11,
22,
33
],
"type": "merge"，
"occlusion": true
}
],
"lines": [
{
"line_id": "1",
"predline": [
"1",
"2"
],
"succline": [
"3",
"4"
],
"line_key_points": [
[
43.35594401056317,
17.367683198169637,
17.367683198169637
],
[
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
47.1199606956959,
17.367683198169637,
17.434382247924802
]
],
"dash_line_start_end": [
[
[
52.502145902186165,
17.50360946655273,
17.50360946655273
],
true,
[
51.79040182705955,
17.50360946655273,
17.50360946655273
],
true
]
],
"type": "single_dash",
"color": "yellow",
"has_fishbone": false,
"diversion_line": false
}
],
"road_marking": [
{
"points": [
[
43.35594401056317,
17.367683198169637,
17.367683198169637
],
[
47.1199606956959,
17.367683198169637,
17.434382247924802
]
],
"center_points": [
[
43.35594401056317,
17.367683198169637,
17.367683198169637
],
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
[
47.1199606956959,
17.367683198169637,
17.434382247924802
]
],
"type": "right",
"road_marking_id": "1",
"worn_status": "clear",
"is_shaped_mark":false
}
]
}
}
}
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
pose结果示例：​
{
"poses": {
"sample_1722340351300193": {
"timestamp": 1722340351324842,
"name": "sample_1722340351300193",
"position": {
"x": 333512.263939424,
"y": 3467710.74518867,
"z": 4.408584994768713
},
"orientation": {
"qx": 0.0167974152290696,
"qy": 0.0062067053559419,
"qz": 0.852520590542123,
"qw": 0.522386797643237
}
}
}
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
*******.3、3D-Lane格式要求​
和2dlane几乎相同，除了​
1没有num_lanes_left_right​
2所有点都是3个浮点数，而不是两个浮点数​
3新增keypoints，和lines同级​
ext_i
nfo
lines​ line_
id​
pred
line​
succ
line​
line_
key_
poin
ts​
dash
_line
_sta
rt_e
nd​
type​ colo
r​
has_
fish
bon
e​
dive
rsio
n_li
ne​
keyp
oint
s​
po
ti
含义​ 投影
是否
准确​
车道
线的
中心
线​
车道
线的
中心
线的
id​
前继
车道
线中
线​
后继
车道
线中
线​
车道
线的
点​
虚线
的起
止点​
虚线
点是
否可
见​
车道
线的
类型​
车道
线的
颜色​
是否
有鱼
骨线​
是否
导流
线​
合流
点分
流点
和线
性转
换点​
坐
格式​ list​ list​ str​ list​ list​ list
；必
须有
2个
点，
且成
员是
xyz
且为
浮点
数​
list
；必
须有
两个
点，
且成
员是
xyz
且为
浮点
数​
bool​ str​ str​ bool​ bool​ list​ lis
；
必
3个
以
且
浮
数
层级​ In
ann
otat
ed_i
nfo​
In
ann
otat
ed_i
nfo​
In
lines​
In
lines​
In
lines​
In
lines​
In
lines​
In
lines​
In
lines​
In
lines​
In
lines​
In
lines​
In
ann
otat
ed_i
nfo​
In
ke
oi
值的
范围​
"ca
mer
a0":
{"pr
oject
ion":
}​
"ca
mer
a6":
{"pr
oject
lines
，
line_
key_
poin
ts，
dash
_line
_star
t_en
d，
type
True
/Fals
e​
'sing
le_d
ash','
singl
e_so
lid','
dens
e_wi
de_d
ash','
dou
ble_
'yell
ow','
whit
e','re
d','bl
ue','o
ther
s'​
True
/Fals
e​
True
/Fals
e​
posit
ion,t
ype​
ion":
}​
"ca
mer
a7":
{"pr
oject
ion":
}​
"ca
mer
a8":
{"pr
oject
ion":
}​
"ca
mer
a9":
{"pr
oject
ion":
}​
"ca
mer
a10"
:
{"pr
oject
ion":
}​
该成
员的
值
为：
"corr
ect"
，"i
ncor
rect"
，"u
nkn
，
colo
r，
has_
fishb
one​
solid
',​
'dou
ble_
dash
','left
_das
h_ri
ght_
solid
','left
_soli
d_ri
ght_
dash
','roa
d_ed
ge','s
top_
lane',
'roa
d_gu
ide_l
ane','
othe
rs','b
otts_
dots'
,'unk
now
n','re
versi
ble_l
ane','
horiz
onta
l_lin
e','re
versi
ble_l
ane'​
own
",""​
特殊
案例​
如果
type
是
singl
e_da
sh，
dou
ble_
dash
，
dens
e_wi
de_d
起止
点是
否真
实可
见，
用
true/
false
表示​
road
_edg
e的
颜色
只能
是'ot
hers'​
判断
是否
有鱼
骨线​
判断
是否
为导
流线​
ash,l
eft_
dash
_rig
ht_s
olid,l
eft_s
olid_
right
_das
h,则
dash
_line
_star
t_en
d必
须有
点;反
之不
会有
点​
*******0、AUTO_4D_LANE​
*******0.1、Clip示例JSON​
{
"auto_4d_lane_clip_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2024-10-11T15:46:41",
"clip_id": "fffd7bf712173f6d882e0589f754cc98",
"check_no": "fffd7bf712173f6d882e0589f754cc98",
"frame_id": "",
"annotated_info": {
"keypoints": [
{
"position": [
11,
22,
33
],
"type": "divide",
"occlusion": true
},
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
{
"position": [
11,
22,
33
],
"type": "merge",
"occlusion": true
}
],
"road_marking": [
{
"type": "straight",
"worn_status": "clear",
"road_marking_id": "12",
"center_points": [
[
-458.3775259740583,
-5493.141575330933,
1.8494458389000967
],
[
-458.1116066671079,
-5499.************,
1.904452449036618
]
],
"points": [
[
-457.6136886638065,
-5499.************,
1.904452449036618
],
[
-458.7080404585201,
-5499.************,
1.904452449036618
],
[
-458.8880330711769,
-5493.156159346676,
1.8494458389000967
],
[
-457.7936812764633,
-5493.124896228313,
1.8494458389000967
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
],
[
-457.6136886638065,
-5499.************,
1.904452449036618
]
]
}
],
"guide_lines": [
{
"guide_line_id": "1419057194716306",
"predguide_line": [
"1419057194716523"
],
"succguide_line": [
"1419057194716422"
],
"left_line_id": [],
"right_line_id": [
"1419057194716307"
],
"points": [
[
4.735618615406565,
-7.53541045030579,
-0.18502279134464406
],
[
-245.2044178812066,
8.515516921877861,
-0.09355267073003848
]
],
"continue_id": 12
}
],
"lines": [
{
"line_id": "1419057194715732",
"line_key_points": [
[
4.633867378288414,
-9.951551909092814,
-0.13024714543827898
]
],
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
"group_id":1,
"predline": [],
"succline": [
"1419057194715757"
],
"type": "road_edge",
"diversion_line": false,
"color": "others",
"has_fishbone": false,
"dash_line_start_end": []
},
{
"line_id": "1419057194715734",
"line_key_points": [
[
[
15.190784453239758,
-6.020986597985029,
-0.29516161875817115
],
[
9.161763586977031,
-5.769887785427272,
-0.25679502162312673
]
]
]
}
]
}
}
}
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
*******0.2、Frame示例JSON​
{
"auto_4d_lane_frame_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2024-10-11T15:46:41",
"clip_id": "fffd7bf712173f6d882e0589f754cc98",
"check_no": "fffd7bf712173f6d882e0589f754cc98",
"frame_id": "",
"annotated_info": {
"keypoints": [
1
2
3
4
5
6
7
8
9
10
{
"position": [
11,
22,
33
],
"type": "divide",
"occlusion": true
},
{
"position": [
11,
22,
33
],
"type": "merge",
"occlusion": true
}
],
"road_marking": [
{
"type": "straight",
"worn_status": "clear",
"road_marking_id": "12",
"center_points": [
[
-458.3775259740583,
-5493.141575330933,
1.8494458389000967
],
[
-458.1116066671079,
-5499.************,
1.904452449036618
]
],
"points": [
[
-457.6136886638065,
-5499.************,
1.904452449036618
],
[
-458.7080404585201,
-5499.************,
1.904452449036618
],
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
[
-458.8880330711769,
-5493.156159346676,
1.8494458389000967
],
[
-457.7936812764633,
-5493.124896228313,
1.8494458389000967
],
[
-457.6136886638065,
-5499.************,
1.904452449036618
]
]
}
],
"guide_lines": [
{
"guide_line_id": "1419057194716306",
"predguide_line": [
"1419057194716523"
],
"succguide_line": [
"1419057194716422"
],
"left_line_id": [],
"right_line_id": [
"1419057194716307"
],
"points": [
[
4.735618615406565,
-7.53541045030579,
-0.18502279134464406
],
[
-245.2044178812066,
8.515516921877861,
-0.09355267073003848
]
],
"continue_id": 12
}
],
"lines": [
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
{
"line_id": "1419057194715732",
"line_key_points": [
[
4.633867378288414,
-9.951551909092814,
-0.13024714543827898
]
],
"group_id":1,
"predline": [],
"succline": [
"1419057194715757"
],
"type": "road_edge",
"diversion_line": false,
"color": "others",
"has_fishbone": false,
"dash_line_start_end": []
},
{
"line_id": "1419057194715734",
"line_key_points": [
[
[
15.190784453239758,
-6.020986597985029,
-0.29516161875817115
],
[
9.161763586977031,
-5.769887785427272,
-0.25679502162312673
]
]
]
}
]
}
}
}
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
*******0.2、格式要求​
lines​ type​ type​
line_
id​
pred
line​
succ
line​
line_
key_
poin
ts​
dash
_line
_sta
rt_e
nd​
colo
r​
has_
fish
bon
e​
keyp
oint
s​
posi
tion​
road
_ma
rkin
g​
po
ts
含义​ 车道
线的
中心
线​
车道
线的
中心
线的
id​
前继
车道
线中
线​
后继
车道
线中
线​
车道
线的
点​
虚线
的起
止点​
车道
线的
类型​
车道
线的
颜色​
是否
有鱼
骨线​
合流
点分
流点
和线
性转
换点​
坐标​ 类型​ 地面
标识​
坐
格式​ list​ str​ list​ list​ list
；必
须有
两个
点，
且成
员是
xyz
且为
浮点
数​
list
；必
须有
两个
点，
且成
员是
xyz
且为
浮点
数​
str​ str​ bool​ list​ list
；且
必须
3个
以上
且为
浮点
数​
str​ list​ lis
；
须
两
点
且
员
xy
且
浮
数
层级​ In
ann
otat
ed_i
nfo​
In
lines​
In
lines​
In
lines​
In
lines​
In
lines​
In
lines​
In
lines​
In
lines​
In
ann
otat
ed_i
nfo​
In
keyp
oints​
In
keyp
oints​
In
ann
otat
ed_i
nfo​
In
ro
_m
rk
g​
值的
范围​
lines
，
line_
key_
poin
ts，
dash
_line
_star
t_en
d，
type
，
colo
r，
'sing
le_d
ash','
singl
e_so
lid','
dens
e_wi
de_d
ash','
dou
ble_
solid
',​
'yell
ow','
whit
e','ot
hers',
'blue
','red'
,'unk
now
n'​
True
/Fals
e​
posit
ion,t
ype​
divid
e,me
rge,
dash
_soli
d​
poin
ts,ty
pe,r
oad_
mar
king
_id​
has_
fishb
one​
'dou
ble_
dash
','left
_das
h_ri
ght_
solid
','left
_soli
d_ri
ght_
dash
','roa
d_ed
ge','o
ther
s','vir
tual
_roa
d_ed
ge','s
top_
lane',
'roa
d_gu
ide_l
ane','
unk
now
n','b
otts
_dot
s'​
特殊
案例​
• 如
果
t
y
p
e
是
s
i
n
g
road
_edg
e的
颜色
只能
是'ot
hers'​
l
e
_
d
a
s
h
，
d
o
u
b
l
e
_
d
a
s
h
，
d
e
n
s
e
_
w
i
d
e
_
d
a
s
h
,l
e
f
t
_
d
a
s
h
_
r
i
g
h
t
_
s
o
li
d
,l
e
f
t
_
s
o
li
d
_
r
i
g
h
t
_
d
a
s
h
,
则
d
a
s
h
_
li
n
e
_
s
t
a
r
t
_
e
n
d
必
须
有
点
;
反
之
不
会
有
点
*******1、Parking3DLane​
与3DLane基本一致​
关键区别：
3d_lane_clip_annotated_info调整为parking_3d_lane_clip_annotated_info
3d_lane_frame_annotated_info调整为parking_3d_lane_frame_annotated_info​
*******2、GOP_DET_COMPLEMENT​
*******2.1、Clip示例JSON​
若无标注结果annotated_info的空结构：​
代码块​
{
"gop_det_complement_clip_annotated_info": {
"annotator": "标注员",
"annotation_qa": "标注QA",
"annotation_date": "2024-05-06T00:54:27", // 标注时间，请按格式转换​
"check_no": "对应帧数据行的 clipId",
"clip_id": "对应帧数据行的 clipId",
"semantic_version": "v20240507",
"annotated_info": {
}
}
}
代码块​
{
"gop_det_complement_clip_annotated_info": {
"annotator": "标注员",
1
2
3
4
5
6
7
8
9
10
11
12
1
2
3
"annotation_qa": "标注QA",
"annotation_date": "2024-05-06T00:54:27",
"check_no": "对应帧数据行的 clipId",
"clip_id": "对应帧数据行的 clipId",
"semantic_version": "v20240507",
"annotated_info": {
"3d_object_detection_info": {
"basic_info": {
"annotation_date": "2024-05-06T00:54:27"
},
"ext_info": {
"exception_flag": true,
"cross_lane_valid": {
"frame_id": true
}
},
"3d_object_detection_anns_info": [
{
"size": [
12,
2.7189871591716464,
3.5
],
"obj_center_pos": [
-60.65684474414331,
40.34174168164523,
-0.9258173206386808
],
"obj_rotation": [
0.002593709474106762,
0.0014301622637757838,
-0.6916251386977924,
0.7222505762049086
],
"category": "bus",
"subcategory": "bus",
"track_id": 9,
"group_id": 9,
"is_double_image": false,
"is_group": false,
"num_lidar_pts": 56,
"num_radar_pts": -1,
"cross_lane": {
"frame_id": "true"
},
"is_crop": true,
"signal": "unknown"
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
}
]
}
}
}
}
*******2.2、Frame示例JSON​
若无标注结果annotated_info的空结构：​
代码块​
{
"gop_det_complement_frame_annotated_info": {
"annotator": "标注员",
"annotation_qa": "标注QA",
"annotation_date": "2024-05-06T00:54:27",// 标注时间，请按格式转换​
"check_no": "对应帧数据行的 clipId",
"clip_id": "对应帧数据行的 clipId",
"check_no":"对应帧数据行的 Frame ID",
"semantic_version": "v20240507",
"annotated_info": {
}
}
}
代码块​
{
"gop_det_complement_frame_annotated_info": {
"annotator": "标注员",
"annotation_qa": "标注QA",
"annotation_date": "2024-05-06T00:54:27",
"check_no": "clipId",
"clip_id": "对应帧数据行的 clipId",
"frame_id": "对应帧数据行的 FrameId",
"semantic_version": "v20240507",
"annotated_info": {
"3d_object_detection_info": {
"basic_info": {
"annotation_date": "2024-05-06T00:54:27"
51
52
53
54
55
56
1
2
3
4
5
6
7
8
9
10
11
12
13
1
2
3
4
5
6
7
8
9
10
11
12
13
},
"ext_info": {
"exception_flag": true,
"cross_lane_valid": false
},
"3d_object_detection_anns_info": [
{
"size": [
4.302217521462755,
1.8,
1.5139830367586726
],
"obj_center_pos": [
72.94021089966282,
3.696936231664625,
-1.165984623301676
],
"obj_rotation": [
0.0012672878488797833,
-0.0007546878638219518,
-0.0014036121169581398,
0.9999979271482323
],
"category": "car",
"subcategory": "car",
"track_id": 1,
"group_id": 1,
"is_double_image": false,
"is_group": false,
"num_lidar_pts": 27,
"num_radar_pts": -1,
"cross_lane": "true",
"is_crop": false,
"signal": "normal"
},
{
"size": [
4.778677786906435,
1.9474448596105933,
1.6714912413811058
],
"obj_center_pos": [
1.2642261275331408,
3.4894973909212337,
-1.2476808886321433
],
"obj_rotation": [
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
-0.002093905940891015,
0.0011792803505163202,
0.0072522469323475,
0.9999708144592011
],
"category": "car",
"subcategory": "car",
"track_id": 2,
"group_id": 2,
"is_double_image": false,
"is_group": false,
"num_lidar_pts": 5734,
"num_radar_pts": -1,
"cross_lane": "true",
"is_crop": true,
"signal": "unknown"
},
{
"size": [
12,
2.5,
3.5
],
"obj_center_pos": [
-56.77242330902409,
40.11005092285775,
-0.970341732491503
]
4.2.4、OthersSpecial​
*******、（21）（PFS）ParkingFreeSpace​
PFS回标比较特殊，按帧回标，每帧对应一个Bin文件，该Bin文件不会被解析，因此需要每帧
还需要一个JSON文件用于存储该Bin文件的MetaData，如标注时间、标注员、质检员等。​
bin文件DZ的读取方式：​
import numpy as np
anno_bin = "sample_1725272449500025.bin"
anno = np.fromfile(anno_bin, dtype=np.int32)
示例ZIP文件结构要求如下：​
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
JSON文件示例：​
{
"parking_free_space_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2022-08-05T15:44:40",
"frame_id": "对应帧数据行的 FrameId",
"semantic_version": "V123123",
"annotated_info": {
"ext_info": {
"camera0": {
"projection": "correct"
},
"camera6": {
"projection": "incorrect"
},
"camera7": {
"projection": "unknown"
},
"camera8": {
"projection": "correct"
},
"camera9": {
"projection": "incorrect"
},
"camera10": {
"projection": "unknown"
}
}
} // 只放投影标签​
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
}
31
*******、（32）3DDrivingSegmentation​
回标比较特殊，按帧回标，每帧对应一个Bin文件，该Bin文件不会被解析，因此需要每帧还需
要一个JSON文件用于存储该Bin文件的MetaData，如标注时间、标注员、质检员等。​
bin文件DZ的读取方式：​
import numpy as np
anno_bin = "sample_1725272449500025.bin"
anno = np.fromfile(anno_bin, dtype=np.int32)
示例ZIP文件结构要求如下：​
JSON文件示例：​
{
"3d_driving_segmentation_annotated_info": {
"annotator": "标注员",
"annotation_qa": "质检员",
"annotation_date": "2022-08-05T15:44:40",
"frame_id": "对应帧数据行的 FrameId",
"semantic_version": "V123123",
"annotated_info": {
"ext_info": {
"camera0": {
"projection": "correct"
},
"camera6": {
"projection": "incorrect"
},
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
"camera7": {
"projection": "unknown"
},
"camera8": {
"projection": "correct"
},
"camera9": {
"projection": "incorrect"
},
"camera10": {
"projection": "unknown"
}
}
} // 只放投影标签​
}
}
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
4.3、JSONSchema及API​
请使用JSONSchemaDraft4进行校验​
因​ 标注结果JSONSchema 文档可能维护不及时，提供API获取大卓侧进行回标数据JSON格
式校验所用的JSONSchema定义文件。​
注意：该JSONSchema仅校验标注结果JSON文件中的annotated_info部分，即实际的标注结
果部分。
如[parking_free_space_annotated_info][annotated_info]部分，但也请
annotator\annotation_date等字段按格式要求。​
URL​ https://zdrive-
bigdata.mychery.com/manager/dataManager/annotatedRecord/jsonSchema/{annotat
eType}​
method​ Get​
RequestHeader​ Key：Authorization​
Value："Bearer"+token​
示例："Authorization":"BearereyJ0eX111111111111111"​
Response
Header​
"Content-Type":"application/json"​
参数解释​ annotateType：1:2DLane、2:3DLane、3:23DObject、4：3DBox​
见入参1.1.2定义​
ResponseBody​ 此处data内容与回标要求的高度一致，即回标各类JSON文件内容最外层Key去掉
"annotated_info"。​
如2---2DLane​
{
"code":0,
"message":"操作成功",
"data":{
"2d_lane": "https://zdrive-ai-big-data-
platform.obs.cn-east-
3.myhuaweicloud.com:443/Label/feedAnnotate/jsonSchemaValida
tion/2d_lane.json?
AccessKeyId=478SULZPVA3PMRBQEUHH&Expires=1730447953&Signatu
re=A74lZ4P1RU80OS0DW1B4RXb8%2Fhs%3D"
}
}
1
2
3
4
5
6
7
如19--PARKING_SURROUND_SPACE_DETECTION​
{
"code": 0,
"message": "操作成功",
"traceId": "f913d2633ae940d18f29c61747ee5661",
"data": {
"parking_surround_space_detection_clip":
"https://zdrive-ai-big-data-platform.obs.cn-east-
3.myhuaweicloud.com:443/Label/feedAnnotate/jsonSchemaValida
tion/parking_surround_space_detection_clip.json?
AccessKeyId=478SULZPVA3PMRBQEUHH&Expires=1730448151&Signatu
re=rJe83I0psUOBAuvW5Lvb83l1%2Byk%3D",
"parking_surround_space_detection_frame":
"https://zdrive-ai-big-data-platform.obs.cn-east-
3.myhuaweicloud.com:443/Label/feedAnnotate/jsonSchemaValida
tion/parking_surround_space_detection_frame.json?
AccessKeyId=478SULZPVA3PMRBQEUHH&Expires=1730448151&Signatu
re=qE5DhH%2B9qHe0lEV3kDgW7FXFlpI%3D"
}
}
1
2
3
4
5
6
7
8
9
如15--D3_CITY_OBJECT_DETECTION_WITH_FISH_EYE​
{
1
"code": 0,
"message": "操作成功",
"traceId": "1dc7d83522d446c29a220cdaa726ee05",
"data": {
"3d_city_object_detection_with_fish_eye":
"https://zdrive-ai-big-data-platform.obs.cn-east-
3.myhuaweicloud.com:443/Label/feedAnnotate/jsonSchemaValida
tion/3d_city_object_detection_with_fish_eye.json?
AccessKeyId=478SULZPVA3PMRBQEUHH&Expires=1730448193&Signatu
re=rXAV%2FOWr2poEwrqnI%2B1OmxifSFg%3D"
}
}
2
3
4
5
6
7
8
4.4、格式校验​
为了避免标注格式错误，会针对Clip所有Frame标注结果进行格式校验。​
4.4.1、标注时格式及模型校验接口​
URL​ https://zdrive-
bigdata.mychery.com/manager/dataManager/task/send/label/labelingValidation​
method​ POST​
RequestHeader​ Key：Authorization​
Value："Bearer"+token​
示例："Authorization":"BearereyJ0eX111111111111111"​
Response
Header​
"Content-Type":"application/json"​
参数解释​ annotateType：1:2DLane、2:3DLane、3:23DObject、4：3DBox​
其他1.1.2​
注：annotateType为3即23DObject需同时2d_object_annotated_info和
3d_object_annotated_info作为入参​
Request
Params​ {
"annotateId":"对应 uid or dataId",
"clipId":"clipId",
"annotateType":1,
"frames":[
1
2
3
4
5
{
"frameId":"frameId",
"annotatedInfo":{
"3d_object_detection_annotated_info":{},
"only_3d_city_object_detection_annotated_info":{},
"m2_3d_city_object_detection_annotated_info":{},
"2d_object_annotated_info":{
"camera0":{
"objects":[
]
}
},
"3d_object_annotated_info":{
"3d_object_detection_info":{
"basic_info":{
},
"ext_info":{
},
"3d_object_detection_anns_info":[
]
}
},
"3d_box_annotated_info":{
"3d_object_detection_info":{
"basic_info":{
},
"ext_info":{
},
"3d_object_detection_anns_info":[
]
}
},
"2d_lane_annotated_info":{
"camera0":{
"lines":[
],
"num_lanes_left_right":[
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
3,
0
]
},
"camera1":{
"lines":[
],
"num_lanes_left_right":[
3,
0
]
}
},
"3d_lane_annotated_info":{
"lines":[
{
"line_key_points":[
[
-33.620456870506956,
-5.404188940404152,
16.94198652258395
]
],
"dash_line_start_end":[
[
[
1320.5,
810.7,
0.1
],
[
1312.6,
802.7,
0.2
]
]
],
"type":"single_solid",
"color":"white",
"has_fishbone":false
}
]
}
}
}
]
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
}
98
ResponseBody​
注意：该返回不
固定​
{
"code": 0,
"message": "None",
"data": {
// [可选] 表示是否发现问题​
"hasError": true // or false,
// 发现的问题列表​
"errors":[
{
// frame ID
"frameId": "",
// 平台赋予的标注物的 ID，在一帧里唯一（此前没
有，需要在标注结果里新增）​
// "_id": "xx",
// 错误分类：undesired_label(多标)、
missing_label(漏标)、wrong_label(错标)​
// "type": "",
// 漏标的标注物的中心点坐标，2D 标注物里 z 为 0​
// "position": { x: 0, y: 0, z: 0 },
// 详细的错误描述​
"message": ""
}
]
}
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
4.4.2、查询质检模型结果接口​
URL​ https://zdrive-
bigdata.mychery.com/manager/dataManager/task/model/external/result?
annotateId=bugp5368bqh&clipId=c213aa48796645cdbddc224a6e4ab21e&annotateTy
pe=1​
method​ Get​
RequestHeader​ Key：Authorization​
Value："Bearer"+token​
示例："Authorization":"BearereyJ0eX111111111111111"​
"Content-Type":"application/json"​
Response
Header​
参数解释​ annotateType：1:2DLane、2:3DLane、3:23DObject、4：3DBox​
注：annotateType为3即23DObject需同时返回2d_object_annotated_info和
3d_object_annotated_info​
Request
Params​ {
"annotateId":"对应 uid or dataId",
"clipId":"clipId",
"annotateType":1
}
1
2
3
4
5
ResponseBody​
注意：该返回不
固定​
1、2DLane：​
{
"code":0,
"message":"操作成功",
"data":{
"hasError":true,
"clipCheckMsgs":null,
"errors":[
{
"frameId":"c1bd3ee43b7d11ee92a80242ac110002",
"message":"2 camera0 3 type road_edge |||
single_dash ---- [91.7, 1414.3]; 2 camera0 4 type
single_solid ||| single_dash ---- [3729.8, 1584]; 2
camera0 4 color white ||| yellow ---- [3729.8, 1584];
2 camera0 5 type road_edge ||| single_dash ----
[3839.4, 1439.4]; 2 camera0 5 color others ||| yellow --
-- [3839.4, 1439.4]; 2 camera1 num_lanes [1, 1] ||| [1,
2]; 2 camera1 5 type road_edge ||| single_dash ----
[3840, 1246.9]; 2 camera1 5 color others ||| yellow ---
- [3840, 1246.9]"
},
{
"frameId":"c1bd61583b7d11ee92a80242ac110002",
"message":"3 camera0 0 color white |||
yellow ---- [1356.3, 1442.8]; 3 camera0 1 color white
||| yellow ---- [2434.1, 1470.9]"
},
{
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
"frameId":"c1bfcdc63b7d11ee92a80242ac110002",
"message":"5 camera0 2 type single_solid
||| single_dash ---- [172.1, 1441.6]; 5 camera0 2 color
white ||| yellow ---- [172.1, 1441.6]; 5 camera1 0
color white ||| yellow ---- [689, 1762]; 5 camera1 1
color white ||| yellow ---- [2817.9, 1751.3]"
}
]
}
}
17
18
19
20
21
22
2、23DObject融合​
{
"code": 0,
"message": "操作成功",
"data": {
"hasError": true,
"clipCheckMsgs": null,
"clipTotalBoxesCount": null,
"errors": [],
"framesCheckMsgs": [
{
"frameId":
"b58355fe419111ee83890242ac110002",
"frameIndex": 0,
"frameBoxesCount": null,
"messages": null,
"errors": [
{
"camera": "camera3",
"trackId": 3,
"message": "类别错误"
},
{
"camera": "camera3",
"trackId": 3,
"message": "接地点顺序错误"
},
{
"camera": "camera4",
"trackId": 9,
"message": "2点接地点漏标注"
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
]
}
]
}
}
31
32
33
34
35
3D模型：​
1、9 Only3DCityObjectDetection​
2、1023DObjectDetection​
{
"code":0,
"message":"操作成功",
"data":{
"framesCheckMsgs":[
{
"frameId":"f9dae3048e6a11eea291fa163e3a9f58",
"errors":[
{
"trackId":4,
"message":"请检查是否类别错误"
}
]
}
]
}
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
未找到资源​ {​
"code":5,​
"message":"未找到资源",​
"data":null​
}​
请求参数错误​ {​
"code":3,​
"message":"请求参数错误",​
"data":null​
}​
任务初始化中​ {​
"code":50101,​
"message":"任务初始化中",​
"data":null​
}​
任务运行中​ {​
"code":50102,​
"message":"任务运行中",​
"data":null​
}​
任务失败​ {​
"code":50103,​
"message":"任务失败",​
"data":null​
}​
4.5、算法验收驳回、通过通知接口​
用于我方算法验收同学，驳回某个Clip时通知供应商，修改Clip状态，从结果集中剔除，避免被重复
拉取
4.5.1、API​
URL​ https://host:port/<供应商自定义URL>​
method​ POST​
RequestHeader​ "Authorization":"ZdriveAI<accessKey>:<signature>"//签名计算⽅法⻅下⽂​
Response
Header​
"Content-Type":"application/json"​
参数解释​ annotate_id：送标ID（String）​
clip_id：Clip唯一ID(String)​
annotate_type:标注任务类型(Integer)​
Operation:操作（String）​
确认：confirmed​
驳回：rejected​
Request
Params​ {
"annotate_type": 1,
"clip_id": "xxxxxx",
"timestamp": 1777777777,
"annotate_id": "11111111111111",
"operation": "confirmed" // confirmed or rejected
}
1
2
3
4
5
6
7
ResponseBody​
{
"code": 0,
"message": "",
"data": null
}
1
2
3
4
5
4.5.2、Signature计算方法​
SStringToSign = "POST" + "\n" +
"annotate_type=xxx" + "\n" +
"clip_id=xxx" + "\n" +
"timestamp=xxx" + "\n" +
"annotate_id=xxx" + "\n" +
"operation=xxx";
Signature=Base64( HMAC-SHA1( UTF-8-Encoding-Of(secretKey),
UTF-8-Encoding-Of(StringToSign)
));
1
2
3
4
5
6
7
8
9
10
11
描述：先拼装StringToSign，然后使⽤HMAC-SHA1计算secretKey和StringToSign的摘要，最后对
摘要进⾏base64编码即可得到最终的签名 ​
注意：拼接顺序不可变
五、（废弃）自动属性标注
5.1、提交自动属性标注任务​
URL​ https://zdrive-bigdata.mychery.com/manager/dataManager/task/model/autoLabel​
method​ POST​
RequestHeader​ Key：Authorization​
Value："Bearer"+token​
示例："Authorization":"BearereyJ0eX111111111111111"​
Response
Header​
"Content-Type":"application/json"​
参数解释​ annotateType：1:2DLane、2:3DLane、3:23DObject、4：3DBox​
其他1.1.2​
注1：annotateType为3即23DObject需同时2d_object_annotated_info和
3d_object_annotated_info作为入参​
注2：该Clip送标多少帧，则请求参数的Frames必须有相同数量的Frame（或多或少会提
示非法），会与送标记录做校验。即使该Frame无需标注无标注结果，也需要按导出格式
要求给定默认值{frame_id:"xx","annotated_info":{.....}}。​
注3：annotated_info内的最外层Key与回标返回一致，如2DLane标注项目则为
annotated_info.2d_lane_annotated_info​
若为3D城市鱼眼，则为
annotated_info.3d_city_object_detection_with_fish_eye_annotated_info​
Request
Params​ {
"annotate_id": "对应 uid or dataId",
"clip_id": "clipId",
"annotate_type": 1,
"frames": [
{
"frame_id": "frameId",
"annotated_info": {
"3d_city_object_detection_with_fish_eye_annotated_info": {
"3d_object_detection_info": {
"3d_object_detection_anns_info": []
}
},
"3d_highway_object_detection_with_fish_eye_annotated_info":
{
"3d_object_detection_info": {
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
"3d_object_detection_anns_info": []
}
},
"3d_object_detection_annotated_info": {
"3d_object_detection_info": {
"3d_object_detection_anns_info": []
}
},
"only_3d_city_object_detection_annotated_info": {
"3d_object_detection_info": {
"basic_info": {
"annotation_date": "2022-08-
05T15:44:40"
},
"ext_info": {
"exception_flag": true
},
"3d_object_detection_anns_info": [
{
"size": [
2.1,
4.7,
1.8
],
"obj_center_pos": [
47.67774994506254,
1.0511472161155395,
-1.1871771747806774
],
"obj_rotation": [
0.9994878429621736,
4.885906248585606e-16,
9.55778573003732e-16,
0.03200080890885854
],
"track_id": 1,
"group_id": 1,
"is_double_image": false,
"is_group": true,
"signal": "normal",
"num_lidar_pts": 112,
"num_radar_pts": -1
},
{
"size": [
1.8,
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
4.6,
1.5
],
"obj_center_pos": [
-47.35474974162991,
-11.65969219370768,
-1.1378283150076802
],
"obj_rotation": [
0.9246076116489002,
-2.286960300198134e-16,
-3.6884522721598043e-
16,
-0.3809209425601545
],
"track_id": 2,
"group_id": -1,
"is_double_image": false,
"is_group": true,
"signal": "normal",
"num_lidar_pts": 212,
"num_radar_pts": -1
}
]
}
},
"m2_3d_city_object_detection_annotated_info": {
},
"2d_object_annotated_info": {
"camera0": {
"objects": [
]
}
},
"3d_object_annotated_info": {
"3d_object_detection_info": {
"basic_info": {
},
"ext_info": {
},
"3d_object_detection_anns_info": [
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
]
}
},
"3d_box_annotated_info": {
"3d_object_detection_info": {
"basic_info": {
},
"ext_info": {
},
"3d_object_detection_anns_info": [
]
}
},
"2d_lane_annotated_info": {
"camera0": {
"lines": [
],
"num_lanes_left_right": [
3,
0
]
},
"camera1": {
"lines": [
],
"num_lanes_left_right": [
3,
0
]
}
},
"3d_lane_annotated_info": {
"lines": [
{
"line_key_points": [
[
-33.620456870506956,
-5.404188940404152,
16.94198652258395
]
],
"dash_line_start_end": [
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
[
[
1320.5,
810.7,
0.1
],
[
1312.6,
802.7,
0.2
]
]
],
"type": "single_solid",
"color": "white",
"has_fishbone": false
}
]
}
}
}
]
}
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
ResponseBody​
注意：该返回不
固定​
{
"code": 0,
"message": "None",
"data": {}
}
1
2
3
4
5
5.2、获取自动属性标注任务结果​
URL​ https://zdrive-bigdata.mychery.com/manager/dataManager/task/model/autoLabel?
annotateId=bugp5368bqh&clipId=c213aa48796645cdbddc224a6e4ab21e&annotateTy
pe=1​
method​ Get​
RequestHeader​ Key：Authorization​
Value："Bearer"+token​
示例："Authorization":"BearereyJ0eX111111111111111"​
Response
Header​
"Content-Type":"application/json"​
参数解释​ annotateType：1:2DLane、2:3DLane、3:23DObject、4：3DBox​
注：annotateType为3即23DObject需同时返回2d_object_annotated_info和
3d_object_annotated_info​
Request
Params​ {
"annotate_id":"对应 uid or dataId",
"clip_id":"clipId",
"annotate_type":1
}
1
2
3
4
5
ResponseBody​
注意：该返回不
固定​
1、2DLane：​
{
"code": 0,
"message": "操作成功",
"data": {
"annotate_id": "对应 uid or dataId",
"clip_id": "clipId",
"annotate_type": 1,
"frames": [
{
"frame_id": "frameId",
"annotated_info": {
"3d_object_detection_annotated_info": {
},
"only_3d_city_object_detection_annotated_info": {
},
"m2_3d_city_object_detection_annotated_info": {
},
"2d_object_annotated_info": {
"camera0": {
"objects": [
]
}
},
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
"3d_object_annotated_info": {
"3d_object_detection_info": {
"basic_info": {
},
"ext_info": {
},
"3d_object_detection_anns_info": [
]
}
},
"3d_box_annotated_info": {
"3d_object_detection_info": {
"basic_info": {
},
"ext_info": {
},
"3d_object_detection_anns_info": [
]
}
},
"2d_lane_annotated_info": {
"camera0": {
"lines": [
],
"num_lanes_left_right": [
3,
0
]
},
"camera1": {
"lines": [
],
"num_lanes_left_right": [
3,
0
]
}
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
},
"3d_lane_annotated_info": {
"lines": [
{
"line_key_points": [
[
-33.620456870506956,
-5.404188940404152,
16.94198652258395
]
],
"dash_line_start_end": [
[
[
1320.5,
810.7,
0.1
],
[
1312.6,
802.7,
0.2
]
]
],
"type": "single_solid",
"color": "white",
"has_fishbone": false
}
]
}
}
}
]
}
}
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
未找到资源​ {​
"code":5,​
"message":"未找到资源",​
"data":null​
}​
请求参数错误​ {​
"code":3,​
"message":"请求参数错误",​
"data":null​
}​
任务初始化中​ {​
"code":50101,​
"message":"任务初始化中",​
"data":null​
}​
任务运行中​ {​
"code":50102,​
"message":"任务运行中",​
"data":null​
}​
任务失败​ {​
"code":50103,​
"message":"任务失败",​
"data":null​
}​
5.3、自动属性标注任务完成通知接口​
供应商需按该接口进行开发，我方会配置供应商的接口url直接调用。​
5.3.1、请求方式及Header​
URL​ https://host:port/xxx​
method​ POST​
RequestHeader​ "Content-Type":"application/json"​
"Authorization":"ZdriveAI<accessKey>:<signature>"//签名计算⽅法⻅下⽂​
ResponseHeader​ "Content-Type":"application/json"​
5.3.2、入参​
参数名​ 类型​ 取值​
annotate_type​ 整数​
clip_id 字符串​
timestamp​ 整数​ 时间1697081971​
annotate_id​ 字符串​ 该送标任务的唯一ID​
5.3.3、Signature计算方法​
StringToSign = "POST" + "\n" +
"annotate_type=xxx" + "\n" +
"clip_id=xxx" + "\n" +
"timestamp=xxx" + "\n" +
"annotate_id=xxx";
Signature=Base64( HMAC-SHA1( UTF-8-Encoding-Of(secretKey),
UTF-8-Encoding-Of(StringToSign)
));
1
2
3
4
5
6
7
8
9
10
描述：先拼装StringToSign，然后使⽤HMAC-SHA1计算secret_key和StringToSign的摘要，最后
对摘要进⾏base64编码即可得到最终的签名 ​
注意：拼接顺序不可变
5.3.4、请求及响应示例​
RequestBody​
{
"annotate_type": 1,
"clip_id": "xxxxxx",
"timestamp": 1777777777,
"annotate_id": "11111111111111"
}
1
2
3
4
5
6
ResponseBody​
{
"code":0,
"message":"操作成功",
"data": null
1
2
3
4
}
5
六、关键帧筛选
6.1、筛选逻辑​
筛选逻辑略。
以parking_surround_space_detection的送标Txt为例，即送标文件Txt的Frame列为
FrameId，timestamp列为Frame的collect_time。​
clip frame params combineLidar config lidar0 camera0
camera0_timestamp camera6 camera6_timestamp
camera7 camera7_timestamp camera8 camera8_timestamp
camera9 camera9_timestamp camera10 camera10_timestamp
pose timestamp extrinsics intrinsics
1
6.2、代码示例​
6.2.1、Python​
def key_frame_selector_with_frequency(frames: list[dict], frequency: float):
frames.sort(key=lambda frame: frame["collect_time"])
if frequency == 2:
return filter_frame(frames, 10, [3, 7])
elif frequency == 0.5:
return filter_frame(frames, 20, [3])
elif frequency == 1:
return filter_frame(frames, 10, [3])
elif frequency == 10:
return frames
return None
def filter_frame(frames, rate, orders):
result = []
for i in range(0, len(frames), rate):
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
for order in orders:
upper = min(i + rate - 1, len(frames) - 1)
index = i + order - 1
if i <= index <= upper:
result.append(frames[index])
result.sort(key=lambda frame: frame["collect_time"])
return result
if __name__ == "__main__":
frames = [
{"collect_time": 1689297938399935, "frame_id":
"f79504fb226911ee8e71a33e1de7b53c"} ]
print(key_frame_selector_with_frequency(frames, 0.5))
17
18
19
20
21
22
23
24
25
26
27
28
29
6.2.2、Java​
@Data
public class Frame {
private String id;
private String collectTime;
}
public List<Frame> keyFrameSelectorWithFrequency(List<Frame> frames, double
frequency) {
frames.sort(Comparator.comparing(Frame::getCollectTime));
if (frequency == 2) {
return filterFrame(frames, 10, List.of(3, 7));
} else if (frequency == 0.5) {
return filterFrame(frames, 20, List.of(3));
} else if (frequency == 1) {
return filterFrame(frames, 10, List.of(3));
} else if (frequency == 10) {
return frames;
}
}
private List<Frame> filterFrame(List<Frame> frames, Integer rate,
List<Integer> orders) {
List<Frame> result = new ArrayList<>();
for (int i = 0; i < frames.size(); i += rate) {
for (Integer order : orders) {
int upper = Math.min(i + rate - 1, frames.size() - 1);
int index = i + order - 1;
if (i <= index && index <= upper) {
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
result.add(frames.get(index));
}
}
}
return result;
}
27
28
29
30
31
32
测试数据：
[{"collect_time":1689297918499922,"frame_id":"f79504d4226911ee8e71a33e1de7b53c
"},
{"collect_time":1689297918599768,"frame_id":"f7950542226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297918699622,"frame_id":"f7950582226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297918799484,"frame_id":"f7950561226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297918899342,"frame_id":"f795051e226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297918999200,"frame_id":"f795055b226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297919099077,"frame_id":"f79504f9226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297919199007,"frame_id":"f79504f7226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297919298974,"frame_id":"f79504f6226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297919398986,"frame_id":"f79504c1226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297919499051,"frame_id":"f79504c7226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297919599153,"frame_id":"f7950512226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297919699283,"frame_id":"f7950505226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297919799435,"frame_id":"f7950563226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297919899593,"frame_id":"f7950533226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297919999757,"frame_id":"f79504e4226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297920099905,"frame_id":"f79504d3226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297920200027,"frame_id":"f79504c8226911ee8e71a33e1de7b53c"
1
},
{"collect_time":1689297920300145,"frame_id":"f795052d226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297920400236,"frame_id":"f795056c226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297920500297,"frame_id":"f79504f1226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297920600324,"frame_id":"f795050e226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297920700317,"frame_id":"f7950527226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297920800281,"frame_id":"f7950540226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297920900215,"frame_id":"f7950583226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297921000134,"frame_id":"f7950506226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297921100036,"frame_id":"f7950555226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297921199922,"frame_id":"f795055d226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297921299827,"frame_id":"f79504cc226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297921399771,"frame_id":"f795057c226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297921499757,"frame_id":"f79504c5226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297921599776,"frame_id":"f79504fc226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297921699833,"frame_id":"f79504e7226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297921799921,"frame_id":"f7950524226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297921900024,"frame_id":"f795053a226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297922000132,"frame_id":"f7950509226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297922100244,"frame_id":"f7950515226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297922200349,"frame_id":"f795057f226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297922300440,"frame_id":"f79504ee226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297922400535,"frame_id":"f79504f0226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297922500632,"frame_id":"f79504c6226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297922600726,"frame_id":"f795056d226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297922700822,"frame_id":"f795052e226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297922800917,"frame_id":"f7950567226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297922901010,"frame_id":"f7950514226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297923001100,"frame_id":"f7950535226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297923101176,"frame_id":"f795053d226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297923201206,"frame_id":"f79504d1226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297923301178,"frame_id":"f795050c226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297923401110,"frame_id":"f79504c9226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297923501016,"frame_id":"f79504d8226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297923600899,"frame_id":"f79504de226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297923700764,"frame_id":"f79504ef226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297923800627,"frame_id":"f79504fa226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297923900481,"frame_id":"f7950537226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297924000341,"frame_id":"f795055e226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297924100207,"frame_id":"f7950581226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297924200090,"frame_id":"f7950516226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297924299993,"frame_id":"f7950545226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297924399913,"frame_id":"f7950507226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297924499848,"frame_id":"f79504c3226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297924599813,"frame_id":"f795055f226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297924699804,"frame_id":"f795054a226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297924799820,"frame_id":"f79504e0226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297924899862,"frame_id":"f79504dd226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297924999932,"frame_id":"f79504fe226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297925100022,"frame_id":"f7950541226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297925200122,"frame_id":"f7950569226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297925300230,"frame_id":"f79504ca226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297925400325,"frame_id":"f7950558226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297925500397,"frame_id":"f79504da226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297925600448,"frame_id":"f79504e5226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297925700463,"frame_id":"f7950501226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297925800447,"frame_id":"f795055a226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297925900411,"frame_id":"f7950525226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297926000361,"frame_id":"f7950586226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297926100303,"frame_id":"f7950504226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297926200236,"frame_id":"f79504d6226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297926300161,"frame_id":"f795057e226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297926400076,"frame_id":"f79504cd226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297926499988,"frame_id":"f79504e3226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297926599903,"frame_id":"f795056e226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297926699818,"frame_id":"f7950526226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297926799729,"frame_id":"f7950519226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297926899629,"frame_id":"f7950536226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297926999524,"frame_id":"f79504f2226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297927099423,"frame_id":"f7950574226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297927199361,"frame_id":"f795050d226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297927299357,"frame_id":"f79504cf226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297927399408,"frame_id":"f7950511226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297927499501,"frame_id":"f795051c226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297927599626,"frame_id":"f79504ed226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297927699778,"frame_id":"f795053f226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297927799939,"frame_id":"f7950538226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297927900101,"frame_id":"f795052f226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297928000260,"frame_id":"f795056a226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297928100407,"frame_id":"f7950532226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297928200538,"frame_id":"f795056f226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297928300651,"frame_id":"f7950500226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297928400757,"frame_id":"f79504eb226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297928500864,"frame_id":"f79504f5226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297928600974,"frame_id":"f7950571226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297928701084,"frame_id":"f7950556226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297928801189,"frame_id":"f7950575226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297928901292,"frame_id":"f795050f226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297929001386,"frame_id":"f7950518226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297929101461,"frame_id":"f7950547226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297929201506,"frame_id":"f795051d226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297929301506,"frame_id":"f79504dc226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297929401456,"frame_id":"f79504e6226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297929501366,"frame_id":"f7950523226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297929601241,"frame_id":"f7950503226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297929701099,"frame_id":"f7950565226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297929800954,"frame_id":"f795057d226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297929900802,"frame_id":"f7950573226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297930000648,"frame_id":"f7950539226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297930100488,"frame_id":"f79504e9226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297930200331,"frame_id":"f79504e8226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297930300185,"frame_id":"f7950531226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297930400086,"frame_id":"f79504f8226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297930500021,"frame_id":"f7950529226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297930599983,"frame_id":"f79504d7226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297930699952,"frame_id":"f79504ec226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297930799923,"frame_id":"f795054f226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297930899899,"frame_id":"f7950528226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297930999882,"frame_id":"f7950530226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297931099881,"frame_id":"f7950580226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297931199897,"frame_id":"f795055c226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297931299943,"frame_id":"f7950513226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297931400016,"frame_id":"f7950587226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297931500104,"frame_id":"f7950562226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297931600195,"frame_id":"f7950551226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297931700291,"frame_id":"f795050a226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297931800381,"frame_id":"f7950553226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297931900451,"frame_id":"f7950568226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297932000513,"frame_id":"f79504ea226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297932100554,"frame_id":"f795050b226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297932200579,"frame_id":"f795051f226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297932300579,"frame_id":"f7950557226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297932400562,"frame_id":"f7950508226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297932500530,"frame_id":"f7950549226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297932600483,"frame_id":"f7950502226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297932700436,"frame_id":"f7950585226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297932800397,"frame_id":"f7950522226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297932900362,"frame_id":"f7950572226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297933000322,"frame_id":"f79504df226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297933100266,"frame_id":"f7950520226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297933200200,"frame_id":"f79504cb226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297933300132,"frame_id":"f7950517226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297933400069,"frame_id":"f7950559226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297933500004,"frame_id":"f7950543226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297933599934,"frame_id":"f79504db226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297933699865,"frame_id":"f79504d2226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297933799822,"frame_id":"f7950550226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297933899800,"frame_id":"f7950578226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297933999812,"frame_id":"f79504c2226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297934099866,"frame_id":"f7950584226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297934199941,"frame_id":"f79504f3226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297934300029,"frame_id":"f7950579226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297934400111,"frame_id":"f795054b226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297934500187,"frame_id":"f795051a226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297934600255,"frame_id":"f79504ce226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297934700323,"frame_id":"f7950577226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297934800382,"frame_id":"f7950521226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297934900426,"frame_id":"f79504e2226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297935000439,"frame_id":"f7950552226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297935100422,"frame_id":"f795057b226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297935200386,"frame_id":"f7950510226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297935300341,"frame_id":"f795054d226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297935400288,"frame_id":"f795057a226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297935500229,"frame_id":"f7950546226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297935600170,"frame_id":"f7950560226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297935700106,"frame_id":"f7950534226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297935800026,"frame_id":"f7950554226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297935899936,"frame_id":"f79504e1226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297935999841,"frame_id":"f79504d0226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297936099761,"frame_id":"f79504f4226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297936199711,"frame_id":"f7950548226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297936299706,"frame_id":"f7950566226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297936399745,"frame_id":"f795052a226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297936499820,"frame_id":"f795051b226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297936599916,"frame_id":"f7950576226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297936700027,"frame_id":"f79504c4226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297936800146,"frame_id":"f795053b226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297936900259,"frame_id":"f79504c0226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297937000349,"frame_id":"f79504fd226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297937100418,"frame_id":"f795054e226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297937200459,"frame_id":"f795053e226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297937300467,"frame_id":"f795054c226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297937400441,"frame_id":"f795052b226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297937500386,"frame_id":"f79504d9226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297937600306,"frame_id":"f795056b226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297937700208,"frame_id":"f795052c226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297937800113,"frame_id":"f7950564226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297937900034,"frame_id":"f79504d5226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297937999981,"frame_id":"f7950544226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297938099953,"frame_id":"f795053c226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297938199941,"frame_id":"f7950570226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297938299934,"frame_id":"f79504ff226911ee8e71a33e1de7b53c"
},
{"collect_time":1689297938399935,"frame_id":"f79504fb226911ee8e71a33e1de7b53c"
}]
2
0.5HZ输出结果​
[{'collect_time': 1689297918699622, 'frame_id':
'f7950582226911ee8e71a33e1de7b53c'}, {'collect_time': 1689297920700317,
'frame_id': 'f7950527226911ee8e71a33e1de7b53c'}, {'collect_time':
1689297922700822, 'frame_id': 'f795052e226911ee8e71a33e1de7b53c'},
{'collect_time': 1689297924699804, 'frame_id':
1
'f795054a226911ee8e71a33e1de7b53c'}, {'collect_time': 1689297926699818,
'frame_id': 'f7950526226911ee8e71a33e1de7b53c'}, {'collect_time':
1689297928701084, 'frame_id': 'f7950556226911ee8e71a33e1de7b53c'},
{'collect_time': 1689297930699952, 'frame_id':
'f79504ec226911ee8e71a33e1de7b53c'}, {'collect_time': 1689297932700436,
'frame_id': 'f7950585226911ee8e71a33e1de7b53c'}, {'collect_time':
1689297934700323, 'frame_id': 'f7950577226911ee8e71a33e1de7b53c'},
{'collect_time': 1689297936700027, 'frame_id':
'f79504c4226911ee8e71a33e1de7b53c'}]
2
