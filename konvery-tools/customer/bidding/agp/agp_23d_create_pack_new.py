import os
import shutil
import sys
import numpy as np
import glob
import pandas as pd
import tempfile

sys.path.append('.')
from customer.common import tools


camera_names = ['CAM_FRONT_LEFT', 'CAM_FRONT', 'CAM_FRONT_RIGHT', 'CAM_BACK_LEFT', 'CAM_BACK', 'CAM_BACK_RIGHT']
calib_keys = ['P0', 'P1', 'P2', 'P3', 'P4', 'P5']


def construct_config(config_file_dir, out_config_file):
    sensor_params = {}
    for calib_file in tools.listdir(config_file_dir, sort=True, full_path=True):
        sensor_param = {}
        calib_name = os.path.splitext(os.path.basename(calib_file))[0]
        with open(calib_file, 'r') as f:
            calib_texts = f.readlines()
        calib_data = {}
        for calib_text in calib_texts:
            k = calib_text.split(':')[0].strip()
            if not (k in calib_keys or k.startswith('T_lidar2cam')):
                continue
            v = np.array(calib_text.split(':')[1].strip().split(), dtype=float).reshape(3, 4)
            calib_data[k] = v
        for idx in range(len(camera_names)):
            camera_name = camera_names[idx]
            calib_key = calib_keys[idx]
            intrinsic = calib_data[calib_key]
            extrinsic = calib_data[f'T_lidar2cam{calib_key[1]}']
            sensor_param[camera_name] = {
                "camera_model": 'pinhole',
                "extrinsic": extrinsic.tolist() + [[0, 0, 0, 1]],
                "fx": intrinsic[0][0],
                "fy": intrinsic[1][1],
                "cx": intrinsic[0][2],
                "cy": intrinsic[1][2],
                "k1": 0,
                "k2": 0,
                "k3": 0,
                "p1": 0,
                "p2": 0
            }
        sensor_params[calib_name] = sensor_param
    configs = {
        "data_type": "single_fusion_pointcloud",
        "sensor_params": sensor_params
    }
    tools.write_json_file(configs, out_config_file, ensure_ascii=False, indent=4)


def run(input_dir, output_dir):
    out_seg_dir = os.path.join(output_dir, os.path.basename(input_dir))
    config_file_dir = os.path.join(input_dir, 'calib')
    out_config_file = os.path.join(out_seg_dir, 'config.json')
    construct_config(config_file_dir, out_config_file)
    src_cam_dir = os.path.join(input_dir, 'camera')
    dst_cam_dir = os.path.join(out_seg_dir, 'camera')
    shutil.copytree(src_cam_dir, dst_cam_dir)
    src_lidar_dir = os.path.join(input_dir, 'lidar')
    dst_lidar_dir = os.path.join(out_seg_dir, 'lidar')
    shutil.copytree(src_lidar_dir, dst_lidar_dir)


"""
项目：agp
输入数据：kitti数据格式
kitti格式参考：https://blog.csdn.net/QFJIZHI/article/details/103682310
"""
if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
