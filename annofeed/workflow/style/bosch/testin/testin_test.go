package boschtestin

import (
	"context"
	"testing"

	"annofeed/internal/biz"

	"github.com/stretchr/testify/require"
)

func TestParseLabels(t *testing.T) {
	file := "" // params file
	if file == "" {
		return
	}

	params, err := ParseLabels(file)
	require.NoError(t, err)
	t.Logf("%+v", params)
}

func TestTransform(t *testing.T) {
	odir := "" // source data dir
	if odir == "" {
		return
	}

	// os.MkdirAll(ndir, 0766)
	dataType, err := Transform(context.Background(), nil, odir, 0, func(context.Context, ...interface{}) {})
	require.NoError(t, err)
	require.Equal(t, biz.DataTypeFusion3D, dataType)
}
