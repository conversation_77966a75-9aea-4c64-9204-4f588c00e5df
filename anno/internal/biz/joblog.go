package biz

import (
	"context"
	"time"

	"anno/internal/mq"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
)

type Joblog struct {
	ID            int64             `json:"id"`
	JobID         int64             `json:"job_id" gorm:"default:null"`
	OperatorUid   string            `json:"operator_uid" gorm:"default:null"`
	OpOrgUid      string            `json:"op_org_uid" gorm:"default:null"`
	Action        string            `json:"action" gorm:"default:null"`
	Details       JoblogDetailsWrap `json:"details" gorm:"default:null"`
	FromPhase     int32             `json:"from_phase" gorm:"default:null"`
	FromState     int32             `json:"from_state" gorm:"default:null"`
	ToPhase       int32             `json:"to_phase" gorm:"default:null"`
	ToState       int32             `json:"to_state" gorm:"default:null"`
	ToExecutorUid string            `json:"to_executor_uid" gorm:"default:null"` // the user which the job is assigned to
	ToExecteam    string            `json:"to_execteam" gorm:"default:null"`     // the team which the executor belongs to
	CreatedAt     time.Time         `json:"created_at" gorm:"default:null"`
}

func (o *Joblog) GetID() int64 {
	return o.ID
}

const JoblogTableName = "joblogs"

var (
	Joblog_             = field.RegObject(&Joblog{})
	JoblogUpdatableFlds = field.NewModel(JoblogTableName, Joblog_, "ToPhase", "ToState", "ToExecutorUid", "ToExecteam")

	JoblogSfldJobID         = field.Sname(&Joblog_.JobID)
	JoblogSfldOperatorUid   = field.Sname(&Joblog_.OperatorUid)
	JoblogSfldAction        = field.Sname(&Joblog_.Action)
	JoblogSfldDetails       = field.Sname(&Joblog_.Details)
	JoblogSfldFromPhase     = field.Sname(&Joblog_.FromPhase)
	JoblogSfldFromState     = field.Sname(&Joblog_.FromState)
	JoblogSfldToPhase       = field.Sname(&Joblog_.ToPhase)
	JoblogSfldToState       = field.Sname(&Joblog_.ToState)
	JoblogSfldToExecutorUid = field.Sname(&Joblog_.ToExecutorUid)
	JoblogSfldToExecteam    = field.Sname(&Joblog_.ToExecteam)
	JoblogSfldCreatedAt     = field.Sname(&Joblog_.CreatedAt)
)

type GiveupReason struct {
	Reason  string `json:"reason"`
	Details string `json:"details"`
}

type JoblogDetails struct {
	AddComments     []*AnnoComment    `json:"add_comments"`
	Resolves        []*ResolveComment `json:"resolves"`
	DeletedComments []*ResolveComment `json:"deleted_comments"`
	UpdatedComments []*AnnoComment    `json:"updated_comments"`
	GiveupReason    *GiveupReason     `json:"giveup_reason"`
}

type JoblogDetailsWrap = serial.Type[*JoblogDetails]

func PublishJoblog(ctx context.Context, jlog *Joblog) error {
	return mq.PublishEvt(ctx, EvtTypeAnnoJoblog, EvtSubtypeCreate, jlog)
}

type JoblogFilter struct {
	JobID     int64
	FromPhase int32
	Actions   []string
}

func (o *JoblogFilter) Apply(tx Tx) Tx {
	tx = repo.ApplyFieldFilter(tx, JoblogSfldJobID, o.JobID)
	tx = repo.ApplyFieldFilter(tx, JoblogSfldFromPhase, o.FromPhase)
	tx = repo.ApplyFieldFilter(tx, JoblogSfldAction, o.Actions)
	return tx
}
