package service

import (
	"anno/internal/biz"

	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func FromBizLot(d *biz.Lot) *anno.Lot {
	if d == nil {
		return nil
	}
	o := &anno.Lot{
		Uid:           d.GetUid(),
		Name:          d.Name,
		Desc:          d.Desc,
		State:         biz.FromBizLotState(d.State),
		Type:          anno.Lot_Type_Enum(anno.Lot_Type_Enum_value[d.Type]),
		DataType:      anno.Element_Type_Enum(anno.Element_Type_Enum_value[d.DataType]),
		Priority:      int32(d.Priority),
		Autostart:     d.Autostart,
		DataSize:      d.DataSize,
		Error:         d.<PERSON><PERSON>r.<PERSON>,
		OrgUid:        d.OrgUid,
		DataUid:       d.DataUid,
		IsFrameSeries: d.IsFrameSeries,
		JobSize:       d.JobSize,
		JobCount:      int32(d.JobCount),
		JobReady:      d.JobReady,
		InsCnt:        d.InsCnt,
		InsTotal:      d.InsTotal,
		CreatorUid:    d.CreatorUid,
		ExpEndTime:    timestamppb.New(d.ExpEndTime),
		UpdatedAt:     timestamppb.New(d.UpdatedAt),
		CreatedAt:     timestamppb.New(d.CreatedAt),

		CanExportAnnos: d.CanExportAnnos,
		Tags:           d.Tags,
	}
	if d.ExtraData.E != nil {
		o.AnnoResultUrl = d.ExtraData.E.AnnoResultUrl
	}
	if d.Ally != nil {
		o.Ontologies = d.Ally.Ontologies.E
		o.Instruction = d.Ally.Instruction
		o.Out = d.Ally.Out.E
		// o.RejectReasons = d.Ally.RejectReasons
		o.ToolCfg = d.Ally.ToolConfig.E
	}
	if d.OrderID > 0 {
		o.OrderUid = kid.StringID(d.OrderID)
	}
	if len(d.Phases) > 0 {
		o.Phases = fromBizLotphases(d.Phases)
	}
	return o
}

func fromBizLotphases(bizLotphases []*biz.Lotphase) []*anno.Lotphase {
	var pbLotphases []*anno.Lotphase

	lotphasesList := biz.LotphaseGroups(bizLotphases)
	for _, phases := range lotphasesList {
		if len(phases) == 0 {
			continue
		}
		if len(phases) == 1 { // there is only one execteam in this phase
			execteam := phases[0].Execteam
			var execteams []*anno.Lotphase_Execteam
			if execteam != "" {
				execteams = []*anno.Lotphase_Execteam{{
					Execteam:    phases[0].Execteam,
					Quota:       phases[0].Quota.E.GetQuota(),
					ClaimedJobs: phases[0].ClaimedJobs,
				}}
			}
			pbLotphases = append(pbLotphases, FromBizLotphase(phases[0], execteam, execteams))
		} else { // there is more than one execteam in this phase
			var execteamQuotas = lo.Map(phases, func(item *biz.Lotphase, _ int) *anno.Lotphase_Execteam {
				return &anno.Lotphase_Execteam{
					Execteam:    item.Execteam,
					Quota:       item.Quota.E.GetQuota(),
					ClaimedJobs: item.ClaimedJobs,
				}
			})
			pbLotphases = append(pbLotphases, FromBizLotphase(phases[0], "", execteamQuotas))
		}
	}
	return pbLotphases
}

func ToBizLot(d *anno.CreateLotRequest) *biz.Lot {
	if d == nil {
		return nil
	}
	subtypes := 0
	if d.Ontologies != nil {
		subtypes = len(d.Ontologies.Groups)
	}
	o := &biz.Lot{
		ID:            kid.ParseID(d.Uid),
		Name:          d.Name,
		Desc:          d.Desc,
		Type:          d.Type.String(),
		Priority:      d.Priority,
		Autostart:     d.Autostart,
		DataUid:       d.DataUid,
		OrgUid:        d.OrgUid,
		OrderID:       kid.ParseID(d.OrderUid),
		Subtypes:      int32(subtypes),
		PhaseCount:    len(d.Phases),
		IsFrameSeries: d.IsFrameSeries,
		JobSize:       d.JobSize,
		ExpEndTime:    d.ExpEndTime.AsTime(),
		Ally: &biz.Lotally{
			Out:         *serial.New(d.Out),
			Ontologies:  *serial.New(d.Ontologies),
			Instruction: d.Instruction,
			// RejectReasons: d.RejectReasons,
			ToolConfig: *serial.New(d.ToolCfg),
		},
		// Project: &biz.Project{Uid: d.ProjectUid},
	}

	for i, phase := range d.Phases {
		o.Phases = append(o.Phases, ToBizLotphaseSlice(phase, i)...)
	}

	return o
}

// func ToSimpleLot(d *biz.Lot) *anno.SimpleLot {
// 	if d == nil {
// 		return nil
// 	}
// 	return &anno.SimpleLot{
// 		Uid:  d.GetUid(),
// 		Name: d.Name,
// 	}
// }

func FromBizLotphase(d *biz.Lotphase, execteam string, execteams []*anno.Lotphase_Execteam) *anno.Lotphase {
	if d == nil {
		return nil
	}
	o := &anno.Lotphase{
		Number:        d.Number,
		Type:          anno.Lotphase_Type_Enum(anno.Lotphase_Type_Enum_value[d.Type]),
		Name:          d.Name,
		Editable:      d.Editable,
		SamplePercent: d.SamplePercent,
		MinSkillLevel: d.MinSkillLevel,
		Timeout:       d.Timeout,
		Execteams:     execteams,
		ClaimPolicy:   anno.Lotphase_ClaimPolicy_Enum(anno.Lotphase_ClaimPolicy_Enum_value[d.ClaimPolicy]),
	}
	return o
}

func ToBizLotphase(d *anno.Lotphase, sliceIdx int, execteam string, quota *anno.Lotphase_Quota) *biz.Lotphase {
	if d == nil {
		return nil
	}

	o := &biz.Lotphase{
		Number:        int32(sliceIdx + 1),
		Type:          d.Type.String(),
		Name:          d.Name,
		Editable:      d.Editable,
		SamplePercent: d.SamplePercent,
		MinSkillLevel: d.MinSkillLevel,
		Timeout:       d.Timeout,
		Execteam:      execteam,
		Quota:         *serial.New(&biz.ExecteamQuota{Quota: quota}),
		ClaimPolicy:   d.ClaimPolicy.String(),
	}
	return o
}

func ToBizLotphaseSlice(d *anno.Lotphase, sliceIdx int) []*biz.Lotphase {
	phase := make([]*biz.Lotphase, len(d.Execteams)+1)
	phase[0] = ToBizLotphase(d, sliceIdx, "", nil) // will be used to store phase-related data
	for i, execteamQuota := range d.Execteams {
		phase[i+1] = ToBizLotphase(d, sliceIdx, execteamQuota.Execteam, execteamQuota.Quota)
	}

	return phase
}

func buildCreateLotRequestFromLot(lot *biz.Lot) *anno.CreateLotRequest {
	return &anno.CreateLotRequest{
		Name:        lot.Name,
		Desc:        lot.Desc,
		ProjectUid:  kid.StringID(lot.ProjectID),
		Type:        anno.Lot_Type_Enum(anno.Lot_Type_Enum_value[lot.Type]),
		Priority:    lot.Priority,
		Autostart:   lot.Autostart,
		JobSize:     lot.JobSize,
		Ontologies:  lot.Ally.Ontologies.E,
		Phases:      fromBizLotphases(lot.Phases),
		Instruction: lot.Ally.Instruction,
		// OrgUid:        lot.OrgUid, // org_uid can be inferred in CreateLot
		DataUid:       lot.DataUid,
		Out:           lot.Ally.Out.E,
		ExpEndTime:    timestamppb.New(lot.ExpEndTime),
		IsFrameSeries: lot.IsFrameSeries,
		OrderUid:      kid.StringID(lot.OrderID),
		ToolCfg:       lot.Ally.ToolConfig.E,
	}
}
