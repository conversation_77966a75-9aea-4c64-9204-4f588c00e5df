{"noEdit": true, "task_label": "pic", "task_type": 1001, "dotLimitLine": "", "dotLimit": "", "template_name": "", "is_online": true, "per_type": false, "task_key": "", "template_key": "", "deny_reason": "", "postil_config": [{"name": "漏标", "value": 1, "status": 1}, {"name": "多标", "value": 2, "status": 1}, {"name": "错标", "value": 3, "status": 1}], "outside": 0, "is_ocr_auto_fill": 0, "is_open_camera_tag": 0, "selection_method": 0, "fuse_configure_method": 0, "merge_point_cloud": 0, "picture_quality": "75", "boxsize": [{"name": "32*32辅助框", "minWidth": "32", "minHeight": "32"}, {"name": "20*20辅助框", "minWidth": "20", "minHeight": "20"}, {"name": "2*2辅助框", "minWidth": "2", "minHeight": "2"}, {"name": "3*3辅助框", "minWidth": "3", "minHeight": "3"}], "sub_round": [], "dataclass_list": [], "mark_region": {"circle": []}, "properties": [{"toolName": "矩形", "toolType": "rect", "is_polygon_filled": 1, "is_open_magic": 0, "is_open_color": 1, "dotLimit": 0, "dotLimitLine": 0, "dotLimitPoint": 0, "is_dot_limit": 0, "is_dot_limit_line": 0, "is_point_dot_limit": 0, "toolColor": "#cccccc", "class": [{"ptitle": "机动车", "pname": "Vehicle", "color": "rgb(255, 255, 255)", "pcode": "1e0eb15e", "is_instance": 0, "attrs": [{"ptitle": "机动车", "pname": "Vehicle", "type": 0, "pvalue": [{"pname": "Car", "ptitle": "乘用车", "code": "c4e920e7"}, {"pname": "Bus", "ptitle": "客车", "code": "994bf1d2"}, {"pname": "SchoolBus", "ptitle": "校车", "code": "ae27cd2e"}, {"pname": "PatrolCar", "ptitle": "观光巡逻车", "code": "e850535c"}, {"pname": "<PERSON>", "ptitle": "面包车", "code": "4d66cf29"}, {"pname": "Truck", "ptitle": "货车", "code": "9f850718"}, {"pname": "Pickup", "ptitle": "皮卡", "code": "06e6fd53"}, {"pname": "VehicleTransporter", "ptitle": "车辆运输车", "code": "40138b79"}, {"pname": "EngineeringCar", "ptitle": "工程车", "code": "b7dc5122"}, {"pname": "PoliceCar", "ptitle": "警车", "code": "720b7b98"}, {"pname": "Ambulance", "ptitle": "救护车", "code": "9a579195"}, {"pname": "FireEngine", "ptitle": "消防车", "code": "4ea825d9"}, {"pname": "Tankers", "ptitle": "油罐车", "code": "9b2812de"}, {"pname": "SanitationTruck", "ptitle": "环卫车", "code": "df39d91c"}, {"pname": "Forklift", "ptitle": "叉车", "code": "6a12624a"}, {"pname": "UnmannedVehicle", "ptitle": "无人物流车", "code": "5050a07a"}, {"pname": "Tricycle", "ptitle": "三轮车", "code": "e6e832cf"}, {"pname": "UnknownCar", "ptitle": "未定类型车", "code": "fb3c4a8f"}], "default_value": ""}], "pre_type": false, "is_track_prop": false}, {"ptitle": "骑行者", "pname": "Cyclist", "color": "rgb(255, 255, 255)", "pcode": "a280d463", "is_instance": 0, "attrs": [{"ptitle": "骑行者", "pname": "Cyclist", "type": 0, "pvalue": [{"pname": "Motorlist", "ptitle": "摩托骑行者", "code": "9954abba"}, {"pname": "Cyclist", "ptitle": "骑行者", "code": "90e9a02a"}], "default_value": ""}, {"ptitle": "骑行者姿态", "pname": "pose", "type": 0, "pvalue": [{"pname": "Ride", "ptitle": "骑行", "code": "fd250a9c"}, {"pname": "<PERSON><PERSON>", "ptitle": "推行", "code": "cda90656"}], "default_value": ""}]}, {"ptitle": "人", "pname": "Person", "color": "rgb(255, 255, 255)", "pcode": "f49bbe72", "is_instance": 0, "attrs": [{"ptitle": "人", "pname": "Person", "type": 0, "pvalue": [{"pname": "Adult", "ptitle": "大人", "code": "d84c3ad1"}, {"pname": "Child", "ptitle": "小孩", "code": "ad88136f"}], "default_value": ""}, {"ptitle": "行人姿态", "pname": "pose", "type": 0, "pvalue": [{"pname": "Stand", "ptitle": "站立", "code": "acc8d7f7"}, {"pname": "Squat", "ptitle": "蹲坐", "code": "b35397ca"}, {"pname": "Lie", "ptitle": "躺姿", "code": "f70122f9"}], "default_value": ""}]}, {"ptitle": "静态目标", "pname": "StaticTarget", "color": "rgb(255, 255, 255)", "pcode": "f0ad6398", "is_instance": 0, "attrs": [{"ptitle": "静态目标", "pname": "StaticTarget", "type": 0, "pvalue": [{"pname": "TrafficCone", "ptitle": "交通锥", "code": "7e7586f9"}, {"pname": "TriReflector", "ptitle": "三角反光板", "code": "ffc2d20b"}, {"pname": "Roadwork", "ptitle": "施工牌", "code": "fae76aef"}], "default_value": ""}]}], "attrs": [], "amount": 1, "isIntro": false, "is_check_all": 0, "is_open_rect_rotate": 0, "fast_mark_edit_size": 0}, {"toolName": "多边形", "toolType": "polygon", "is_polygon_filled": 1, "is_open_magic": 0, "is_open_color": 1, "dotLimit": 0, "dotLimitLine": 0, "dotLimitPoint": 0, "is_dot_limit": 0, "is_dot_limit_line": 0, "is_point_dot_limit": 0, "toolColor": "#cccccc", "class": [{"ptitle": "区域目标", "pname": "RegionalTarget", "color": "rgb(172, 244, 247)", "pcode": "ba39a124", "is_instance": 0, "attrs": []}], "attrs": [], "amount": 1, "isIntro": false, "is_check_all": 0}, {"toolName": "特征点", "toolType": "point", "is_polygon_filled": 1, "is_open_magic": 0, "is_open_color": 1, "dotLimit": 0, "dotLimitLine": 0, "dotLimitPoint": 0, "is_dot_limit": 0, "is_dot_limit_line": 0, "is_point_dot_limit": 0, "toolColor": "#cccccc", "class": [{"ptitle": "边角点-右后方", "pname": "RR", "color": "rgb(131, 115, 232)", "pcode": "d4c48976", "is_instance": 0}, {"ptitle": "边角点-左后方", "pname": "LR", "color": "rgb(134, 234, 117)", "pcode": "e1ffb6c6", "is_instance": 0}, {"ptitle": "边角点-右前方", "pname": "RF", "color": "rgb(239, 176, 118)", "pcode": "d5580383", "is_instance": 0}, {"ptitle": "边角点-左前方", "pname": "LF", "color": "rgb(15, 145, 240)", "pcode": "db5f4ddd", "is_instance": 0}], "attrs": [], "amount": 1, "isIntro": false, "is_check_all": 0}], "attributeSeparation": 0, "properties2D": [], "global": {"attrs": [], "class": []}, "price": {"channel_price": {}, "customer_price": {}}, "train_desc": "", "train_num": "", "train_max_num": "", "train_rate": "", "package_type": 1, "submit_at": "", "update_time": "", "check_limit": "", "rotate_picture_export_type": 1, "is_train": 0, "fast_mark": 0, "is_body_orientation": 0, "is_rotate_picture": 0, "is_point": 0, "point_desc": "", "coco_desc": "", "is_ocr": 0, "is_track": 0, "is_semantic": 0, "segmentation_type": 0, "is_panoramic": 0, "is_class_type": 1, "boxsize3d": [], "group_type": 0, "assist": {}, "is_vsample_interval": 0, "vsample_interval": "", "isCutting": 0, "splitChannels": 0, "minNum": "", "maxNum": "", "isConversion": 0, "conversion_type": 0, "isPinyin": 0, "task_mode": 1, "cut_type": 0, "isAudioProps": 1, "isAddTime": 0, "addTime": "", "is_prop_check": 0, "is_escape": 0, "appropriate_types": 1, "props_save": 0, "labeling_method": 0, "add_attr": 0, "modify_corpus": 0, "is_pre_labeled": 0, "pro_type": 0, "num_of_task": 1, "num_of_pack": "", "articleTitle_type": 0, "article_titleArr": [], "corpus_type": 0, "text_title": [], "ocr_content_configure": [], "camera_properties": [], "textadd": false, "add_tag": 0, "tag_type": 0, "reference_tag": 0, "min_words_length": "", "max_words_length": "", "min_num_pieces": "", "max_num_pieces": "", "regStatus": 0, "is_expression": 0, "expression_content": "", "generate_mode": 0, "cue_word": [], "check_rule_name": 0, "translate": 0, "discrete": 0, "relationship_definition": [], "events_definition": [], "attribute_definition": [], "entity_premark": 0, "entity_library_id": "", "force_submit": 0, "cleaning_type": "1", "cleaningTypeArr": [], "type_list": [], "preset_type": "", "pass_total_limit": 0, "command_params": "", "boxConsistent": 0, "is_private": 1, "project_type": {"pic": [{"task_type": 1001, "title": "图像通用标注", "imgsrc": "/images/show_1001.png", "icon": "#icontongyongbiaozhu", "is_allow": 1}, {"task_type": 1002, "title": "OCR文字转写", "imgsrc": "/images/show_1002.png", "icon": "#iconwenzizhuanxie", "is_allow": 1}, {"task_type": 1003, "title": "REID目标跟踪", "imgsrc": "/images/show_1003.png", "icon": "#iconmubiaogenzong", "is_allow": 1}, {"task_type": 1004, "title": "图像语义分割", "imgsrc": "/images/show_1004.png", "icon": "#iconyuyifenge1", "is_allow": 1}, {"task_type": 1006, "title": "人体关键点", "imgsrc": "/images/show_1006.png", "icon": "#<PERSON>rentiguanjiandian", "is_allow": 1}], "point_cloud": [{"task_type": 902, "title": "点云", "imgsrc": "/images/show_902.png", "icon": "#icondianyun2d3dronghe", "is_allow": 1}], "audio": [{"task_type": 704, "title": "音频标注", "imgsrc": "/images/show_704.png", "icon": "#iconyin<PERSON><PERSON><PERSON>hu", "is_allow": 1}], "text": [{"task_type": 1111, "title": "意图及实体标注", "imgsrc": "/images/show_1111.png", "icon": "#iconyi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_allow": 1}, {"task_type": 1202, "title": "命名实体识别与关系标注", "imgsrc": "/images/show_1202.png", "icon": "#iconmingmingshitishibieyuguanxibiaozhu", "is_allow": 1}, {"task_type": 1103, "title": "文本生成", "imgsrc": "/images/show_1103.png", "icon": "#iconwenben", "is_allow": 1}, {"task_type": 1112, "title": "文章判断", "imgsrc": "/images/show_1112.png", "icon": "#icon<PERSON>zhangpanduan", "is_allow": 1}, {"task_type": 1113, "title": "相似文本判断", "des": "给定参考语料，从一组句子中选择单条或多条相似文本", "imgsrc": "/images/show_1113.png", "icon": "#icon<PERSON>zhangpanduan", "is_allow": 1}], "video": [{"task_type": 1201, "title": "视频标注", "imgsrc": "/images/show_1201.png", "icon": "#icons<PERSON><PERSON><PERSON><PERSON><PERSON>", "is_allow": 1}]}, "issues": [{"name": "漏标", "value": 1, "status": 1}, {"name": "多标", "value": 2, "status": 1}, {"name": "错标", "value": 3, "status": 1}]}