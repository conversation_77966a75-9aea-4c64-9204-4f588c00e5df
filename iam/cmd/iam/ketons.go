package main

import (
	"context"
	"fmt"
	"time"

	"iam/internal/biz"
	"iam/internal/conf"
	"iam/internal/data"
	"iam/pkg/keto"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
)

type ketoMgr struct {
	teamsRepo biz.TeamsRepo
	usersRepo biz.UsersRepo
	cleanup   func()
	log       *log.Helper
}

func newKetoMgr(bc *conf.Bootstrap, logger log.Logger) *ketoMgr {
	dataData, cleanup, err := data.NewData(bc.Data, logger)
	if err != nil {
		panic(fmt.Errorf("failed to create database client: %w", err))
	}
	keto.Init(&keto.Config{
		ReadAddr:  bc.Keto.ReadAddr,
		WriteAddr: bc.Keto.WriteAddr,
		Logger:    logger,
	})

	return &ketoMgr{
		teamsRepo: data.NewTeamsRepo(dataData, logger),
		usersRepo: data.NewUsersRepo(dataData, logger),
		cleanup:   cleanup,
		log:       log.New<PERSON>elper(logger),
	}
}

func (o *ketoMgr) Close() {
	o.cleanup()
}

// InitNamespace adds a namespace to keto.
func (o *ketoMgr) InitNamespace(ns string) error {
	err := o.AddNamespaceTupleForAdmins(ns)
	if err != nil {
		return err
	}
	err = o.AddNamespaceTupleForOrgs(ns)
	if err != nil {
		return err
	}
	return o.AddNamespaceTupleForUsers(ns)
}

func (o *ketoMgr) AddNamespaceTupleForAdmins(ns string) error {
	// tuple: <Namespace>:/#policies@IamPolicy:sys/admin
	tuple := &keto.RelationTuple{
		Namespace: ns,
		Object:    keto.GlobalScope,
		Relation:  keto.RelPolicies,
		Subject:   keto.NewSubjectSet(keto.PolicyNs, "sys/admin", ""),
	}
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	err := keto.DefaultKeto().Add(ctx, tuple)
	cancel()
	if err != nil {
		return fmt.Errorf("failed to add admin tuples: %w", err)
	}
	o.log.Infof("AddNamespaceTupleForAdmins: finished")
	return nil
}

func (o *ketoMgr) AddNamespaceTupleForOrgs(ns string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	count, err := o.teamsRepo.Count(ctx, &biz.TeamListFilter{ParentID: new(int64)})
	cancel()
	if err != nil {
		return fmt.Errorf("failed to query orgnization count: %w", err)
	}
	o.log.Infof("AddNamespaceTupleForOrgs: find %v orgnizations", count)

	pager := repo.Pager{Pagesz: 1000}
	for n := 0; ; {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		groups, token, err := o.teamsRepo.List(ctx, &biz.TeamListFilter{ParentID: new(int64)}, pager)
		cancel()
		if err != nil {
			return fmt.Errorf("failed to query orgnization list: %w", err)
		}

		// tuple: <Namespace>:IamGroup.<group-name>#parents@IamGroup:<group-name>
		tuples := lo.Map(groups, func(v *biz.Team, _ int) *keto.RelationTuple {
			uid := v.GetUid()
			return &keto.RelationTuple{
				Namespace: ns,
				Object:    keto.MakeScopeObj(keto.GroupNs, uid),
				Relation:  keto.RelParents,
				Subject:   keto.NewSubjectSet(keto.GroupNs, uid, ""),
			}
		})
		ctx, cancel = context.WithTimeout(context.Background(), 10*time.Second)
		err = keto.DefaultKeto().Add(ctx, tuples...)
		cancel()
		if err != nil {
			return fmt.Errorf("failed to add orgnization tuples: %w", err)
		}

		n += len(groups)
		pager.PageToken = token
		o.log.Infof("AddNamespaceTupleForOrgs: finished %v orgs", n)
		if token == "" {
			break
		}
	}
	o.log.Infof("AddNamespaceTupleForOrgs: finished")
	return nil
}

func (o *ketoMgr) AddNamespaceTupleForUsers(ns string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	count, err := o.usersRepo.Count(ctx, &biz.UserListFilter{})
	cancel()
	if err != nil {
		return fmt.Errorf("failed to query user count: %w", err)
	}
	o.log.Infof("AddNamespaceTupleForUsers: find %v users", count)

	pager := repo.Pager{Pagesz: 1000}
	for n := 0; ; {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		users, token, err := o.usersRepo.List(ctx, nil, pager)
		cancel()
		if err != nil {
			return fmt.Errorf("failed to query user list: %w", err)
		}

		// tuple: <Namespace>:IamUser.<user-name>#policies@IamPolicy:IamUser.<user-name>.owner
		tuples := lo.Map(users, func(v *biz.User, _ int) *keto.RelationTuple {
			obj := keto.MakeScopeObj(keto.UserNs, v.GetUid())
			return &keto.RelationTuple{
				Namespace: ns,
				Object:    obj,
				Relation:  keto.RelPolicies,
				Subject:   keto.NewSubjectSet(keto.PolicyNs, obj+".owner", ""),
			}
		})
		ctx, cancel = context.WithTimeout(context.Background(), 10*time.Second)
		err = keto.DefaultKeto().Add(ctx, tuples...)
		cancel()
		if err != nil {
			return fmt.Errorf("failed to add user tuples: %w", err)
		}
		n += len(users)
		pager.PageToken = token
		o.log.Infof("AddNamespaceTupleForUsers: finished %v users", n)
		if token == "" {
			break
		}
	}
	o.log.Infof("AddNamespaceTupleForUsers: finished")
	return nil
}
