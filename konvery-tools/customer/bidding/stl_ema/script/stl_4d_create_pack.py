import glob
import json
import os
import shutil
import sys
import numpy as np

sys.path.append('.')
from customer.common import tools


mapping_id_EP40 = {
    'camera-71': 'front_narrow',
    'camera-73': 'front_wide',
    'camera-75': 'back_left',
    'camera-76': 'front_left',
    'camera-77': 'back_right',
    'camera-78': 'front_right',
    'camera-83': 'back',
}

camera_ids = ["front_narrow", "front_wide", "front_left", "back_left", "front_right", "back_right", "back"]


def init_params_fromDir(params_root_path='params', calibration_type='mini-eye'):
    camera_params = {}
    if calibration_type == 'SensorCalibration':
        camera_ids_ = camera_ids
    for camera_name in camera_ids_:
        if calibration_type == 'SensorCalibration':
            camera_name_extrinsic = 'top_center_lidar-to-' + camera_name + '-extrinsic'
            camera_name_intrinsic = camera_name + '-intrinsic'
            with open(os.path.join(params_root_path, camera_name_extrinsic+'.json')) as f:
                json_dict_extrinsic = json.load(f)
            with open(os.path.join(params_root_path, camera_name_intrinsic+'.json')) as f:
                json_dict_intrinsic = json.load(f)
            extrinsic_mat = np.linalg.inv(np.array(json_dict_extrinsic[camera_name_extrinsic]['param']['sensor_calib']['data']))
            intrinsic_mat = np.array(json_dict_intrinsic[camera_name_intrinsic]['param']['cam_K']['data'])
            dist_mat = json_dict_intrinsic[camera_name_intrinsic]['param']['cam_dist']['data']
            camera_params[camera_name] = {
                "extrinsic": tools.mat_to_pose(extrinsic_mat),
                "intrinsic": [intrinsic_mat[0][0], intrinsic_mat[1][1], intrinsic_mat[0][2], intrinsic_mat[1][2]],
                "distortion": [0] + dist_mat[0]
            }
    return camera_params


def copy_with_orig(src, dst, orig_prefix=None):
    shutil.copyfile(src, dst)
    if orig_prefix:
        with open(dst + '.orig', 'w') as f:
            f.write(src.replace(orig_prefix, ''))


def construct_config_v2(camera_params, global_imu_pose, output_seg_dir):
    config = {
        "meta": {
            "version": "v2"
        },
        "lidar": {
            "viewpoint": global_imu_pose[os.path.basename(output_seg_dir)]
        },
        "cameras": camera_params,
    }

    with open(os.path.join(output_seg_dir, 'params.json'), 'w') as f:
        json.dump(config, f, indent=4)


def construct_config_v1(camera_params, global_imu_pose, output_seg_dir):
    camera_params_v1 = {}
    camera_pose_mat = tools.pose_to_mat(global_imu_pose[os.path.basename(output_seg_dir)])
    pose_transform = np.linalg.inv(camera_pose_mat).flatten().tolist()
    for camera_name, camera_param in camera_params.items():
        camera_params_v1[camera_name] = {
            "name": camera_name,
            "transforms": [
                {
                    "type": 0,
                    "column_cnt": 4,
                    "data": pose_transform
                },
                {
                    "type": 0,
                    "column_cnt": 4,
                    "data": np.linalg.inv(tools.pose_to_mat(camera_param['extrinsic'])).flatten().tolist()
                },
                {
                    "type": 2,
                    "column_cnt": 1,
                    "data": camera_param['intrinsic']
                },
                {
                    "type": 3,
                    "column_cnt": 1,
                    "data": camera_param['distortion']
                }
        ]
        }
    config = {
        "lidars": {
            "lidar": {
                "name": "lidar",
                "pose": {
                    "pose": global_imu_pose[os.path.basename(output_seg_dir)]
                }
            }
        },
        "cameras": camera_params_v1
    }

    with open(os.path.join(output_seg_dir, 'params.json'), 'w') as f:
        json.dump(config, f, indent=4)


def run(input_dir, output_dir):
    err_list = []
    prefix = os.path.dirname(input_dir)
    mcap = input_dir
    collection_out_dir = os.path.join(output_dir, os.path.basename(mcap))
    os.makedirs(collection_out_dir)

    # lidar_file = os.path.join(mcap, 'label_map.pcd')
    # global_imu_pose_file = os.path.join(mcap, 'global_imu_pose.txt')
    lidar_file = os.path.join(os.path.dirname(input_dir), 'global_map_iter0_roadcenter_range_300m.pcd')
    global_imu_pose_file = os.path.join(mcap, 'global_imu_pose_roadcenter.txt')

    param_dir = os.path.join(mcap, 'param')
    label_file = os.path.join(mcap, 'label_file')

    camera_params = init_params_fromDir(params_root_path=param_dir, calibration_type='SensorCalibration')
    with open(global_imu_pose_file) as f:
        poses = f.readlines()
    global_imu_pose = {}
    for pose in poses:
        pose_split = pose.split()
        global_imu_pose[pose_split[0]] = [float(i) for i in pose_split[1:]]

    seg_dirs = tools.get_sub_dir(label_file, name_only=False, sort=True)
    has_copy_lidar = False
    for seg_dir in enumerate(seg_dirs):
        seg_name = os.path.basename(seg_dir)
        seg_out_dir = os.path.join(collection_out_dir, seg_name)
        os.mkdir(seg_out_dir)
        # construct_config_v2(camera_params, global_imu_pose, seg_out_dir)
        try:
            construct_config_v1(camera_params, global_imu_pose, seg_out_dir)
        except KeyError as e:
            err_list.append(os.path.basename(seg_dir))
            os.rmdir(seg_out_dir)
            continue

        if not has_copy_lidar:
            copy_with_orig(lidar_file, os.path.join(seg_out_dir, 'lidar.pcd'), prefix)
            has_copy_lidar = True

        imgs = tools.get_file_by_extension(seg_dir, '.jpg')
        img_cnt = 0
        for img in imgs:
            cam_type = img.name.rsplit('-', 1)[0]
            if cam_type in mapping_id_EP40:
                copy_with_orig(img.path, os.path.join(seg_out_dir, mapping_id_EP40[cam_type] + '.jpg'), prefix)
                img_cnt += 1
        assert img_cnt == 7, f'missing camera({img_cnt}/7)'
    err_count = {}
    err_count[os.path.basename(input_dir)] = {
        'total_frame': len(seg_dirs),
        'error_num': len(err_list),
        'error_frame': err_list
    }
    return err_count

if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    err_counts = dict()
    for mcap in glob.glob(os.path.join(args.input, 'produce*', '*mcap')):
        err_count = run(mcap, os.path.join(args.out, os.path.basename(os.path.dirname(mcap))))
        err_counts.update(err_count)
    tools.write_json_file(err_counts, os.path.join(args.out, 'err_counts.json'), indent=4)
# python3 customer/bidding/stl_ema/script/stl_4d_create_pack.py --input /home/<USER>/data/hw/ADS_tag_nodecp25plgkfntl4n6flk90_2316 --out /home/<USER>/data/hw/ADS_tag_nodecp25plgkfntl4n6flk90_2316_out
# python3 customer/bidding/stl_ema/script/stl_4d_create_pack.py --input /home/<USER>/data/hw/ADS_tag_nodecp25pp8kfntvb8pn6of0_9883 --out /home/<USER>/data/hw/ADS_tag_nodecp25pp8kfntvb8pn6of0_9883_out