package biz

import (
	"context"
	"encoding/json"
	"path"
	"time"

	"annofeed/internal/conf"
	"annofeed/internal/data/serial"
	"annofeed/internal/signer"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/apis/types"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"gitlab.rp.konvery.work/platform/pkg/s3"
	"gitlab.rp.konvery.work/platform/pkg/upload"
)

const (
	RawdataTypeImage      = "image"
	RawdataTypePointcloud = "pointcloud"
	RawdataTypeParams     = "params"
	RawdataTypeAnnos      = "annos"
	RawdataTypeMeta       = "meta"

	RawdataURLPathPrefix   = "/rawdata/"
	EmbeddingURLPathPrefix = "/embedding/"
)

type PCDSegment struct {
	Points    int64  `json:"points"`
	Size      int64  `json:"size"`
	URI       string `json:"uri"`
	AccessURL string `json:"access_url"`
}

type Metafile struct {
	Name string `json:"name"`
	URI  string `json:"uri"`
}

type ExtraData struct {
	PCDSegments []*PCDSegment `json:"segments"`
	Metafiles   []*Metafile   `json:"metafiles"`
}
type ExtraDataType = serial.Type[*ExtraData]

type Rawdata struct {
	ID int64 `json:"id,omitempty"`
	// Uid           string    `json:"uid" gorm:"default:null"`
	DataID        int64     `json:"data_id,omitempty" gorm:"default:null"`
	Folder        string    `json:"folder" gorm:"default:null"`
	ElemIdxInData int32     `json:"elem_idx_in_data" gorm:"default:null"`
	Name          string    `json:"name" gorm:"default:null"`      // path
	OrigName      string    `json:"orig_name" gorm:"default:null"` // path
	Info          JSON      `json:"info" gorm:"default:null"`
	CamName       string    `json:"cam_name" gorm:"default:null"`
	Type          string    `json:"type" gorm:"default:null"`
	Format        string    `json:"format" gorm:"default:null"`
	Size          int64     `json:"size" gorm:"default:null"`
	Idx           int       `json:"idx" gorm:"default:null"` // for order purpose only
	URI           string    `json:"uri" gorm:"default:null"`
	AccessURL     string    `json:"access_url" gorm:"default:null"`
	URLExpires    time.Time `json:"url_expires" gorm:"default:null"` // expiration of URL
	EmbeddingURI  string    `json:"embedding_uri" gorm:"default:null"`
	Sha256        string    `json:"sha256" gorm:"default:null;column:sha256"`
	// Storage       string    `json:"storage" gorm:"default:null"` // s3/localfs
	// Key           string    `json:"key" gorm:"default:null"`     // key of s3 or file path
	ExtraData ExtraDataType `json:"extra_data" gorm:"default:null"`
	CreatedAt time.Time     `json:"created_at" gorm:"default:null"`
	UpdatedAt time.Time     `json:"updated_at" gorm:"default:null"`
	// DeletedAt gorm.DeletedAt `json:"-"`
	// Infop *RawdataInfo `json:"-" gorm:"-"`
}

// TableName returns the table name. Refers to https://gorm.io/zh_CN/docs/conventions.html.
func (Rawdata) TableName() string { return "rawdatas" }
func (o *Rawdata) GetID() int64   { return o.ID }
func (o *Rawdata) EnsureID() {
	if o.ID == 0 {
		o.ID = kid.NewID()
	}
}
func (o *Rawdata) GetUid() string { return kid.StringID(o.ID) }
func (o *Rawdata) UidFld() string { panic("code error") }
func (o *Rawdata) IsExpired(threshold time.Duration) bool {
	return o.URLExpires.IsZero() || time.Now().Add(threshold).After(o.URLExpires)
}

var (
	rawdata_             = field.RegObject(&Rawdata{})
	RawdataUpdatableFlds = field.NewModel(Rawdata{}.TableName(), rawdata_,
		"Format", "URI", "AccessURL", "URLExpires", "EmbeddingURI", "ExtraData")

	RawdataSfldDataID       = field.Sname(&rawdata_.DataID)
	RawdataSfldFolder       = field.Sname(&rawdata_.Folder)
	RawdataSfldName         = field.Sname(&rawdata_.Name)
	RawdataSfldType         = field.Sname(&rawdata_.Type)
	RawdataSfldFormat       = field.Sname(&rawdata_.Format)
	RawdataSfldURI          = field.Sname(&rawdata_.URI)
	RawdataSfldSize         = field.Sname(&rawdata_.Size)
	RawdataSfldIdx          = field.Sname(&rawdata_.Idx)
	RawdataSfldSha256       = field.Sname(&rawdata_.Sha256)
	RawdataSfldAccessURL    = field.Sname(&rawdata_.AccessURL)
	RawdataSfldURLExpires   = field.Sname(&rawdata_.URLExpires)
	RawdataSfldEmbeddingURI = field.Sname(&rawdata_.EmbeddingURI)
	RawdataSfldExtraData    = field.Sname(&rawdata_.ExtraData)

	RawdataSfldElemIdxInData = field.Sname(&rawdata_.ElemIdxInData)
)

// type RawdataImageMeta struct {
// 	Width  int32 `json:"width"`
// 	Height int32 `json:"height"`
// }

// type RawdataInfo struct {
// 	Image *RawdataImageMeta `json:"image"`
// }

type Element struct {
	DataID        int64  `json:"data_id,omitempty" gorm:"default:null"`
	Folder        string `json:"folder" gorm:"default:null"`
	ElemIdxInData int32  `json:"elem_idx_in_data" gorm:"default:null"`
}

type ElemAnno = anno.ElementAnno

func ConvertRawdata(rawdata *Rawdata) *anno.Rawdata {
	var props = &anno.Rawdata_Meta{}
	if rawdata.Type != RawdataTypeParams && len(rawdata.Info) > 2 {
		_ = json.Unmarshal(rawdata.Info, props)
	}
	var em *anno.Rawdata_Embedding
	if rawdata.EmbeddingURI != "" {
		em = &anno.Rawdata_Embedding{Url: rawdata.EmbeddingURI}
	}

	var title string
	switch rawdata.Type {
	case RawdataTypeImage:
		// rawdata.CamName is always not empty if rawdata.Type is image
		title = rawdata.CamName // equal to props.Image.Camera
	case RawdataTypePointcloud:
		title = path.Base(rawdata.Name)
		title = title[:len(title)-len(path.Ext(title))]
	}

	return &anno.Rawdata{
		Title:     title,
		Name:      rawdata.Name,
		OrigName:  rawdata.OrigName,
		Type:      anno.Rawdata_Type_Enum(anno.Rawdata_Type_Enum_value[rawdata.Type]),
		Format:    anno.Rawdata_Format_Enum(anno.Rawdata_Format_Enum_value[rawdata.Format]),
		Url:       rawdata.AccessURL,
		Size:      float64(rawdata.Size),
		Sha256:    rawdata.Sha256,
		Meta:      props,
		Embedding: em,
	}
}

type RawdataListFilter struct {
	DataID        int64
	Name          string
	FolderPattern string
	Sha256        string
	FromElemIdx   int32
	ElemCnt       int32
	Count         int
	Type          string
	NotTypeAnnos  bool
	OrderByIDDesc bool
}

func (o *RawdataListFilter) Apply(tx Tx) Tx {
	if tx == nil {
		return nil
	}

	tbl := Rawdata{}.TableName() + "."
	tx = repo.ApplyFieldFilter(tx, tbl+RawdataSfldDataID, o.DataID)
	tx = repo.ApplyFieldFilter(tx, tbl+RawdataSfldName, o.Name)
	tx = repo.ApplyPatternFilter(tx, tbl+RawdataSfldFolder, o.FolderPattern)
	tx = repo.ApplyFieldFilter(tx, tbl+RawdataSfldSha256, o.Sha256)
	if o.FromElemIdx > 0 {
		f := &repo.RelationFilter{
			Field:    tbl + RawdataSfldElemIdxInData,
			Relation: repo.FilterRelationGreaterEqualThan,
			Value:    o.FromElemIdx,
		}
		tx = f.Apply(tx)
	}
	if o.ElemCnt > 0 {
		f := &repo.RelationFilter{
			Field:    tbl + RawdataSfldElemIdxInData,
			Relation: repo.FilterRelationLessThan,
			Value:    o.FromElemIdx + o.ElemCnt,
		}
		tx = f.Apply(tx)
	}
	tx = repo.ApplyFieldFilter(tx, tbl+RawdataSfldType, o.Type)
	if o.NotTypeAnnos {
		f := repo.NewUnequalFilter(tbl+RawdataSfldType, RawdataTypeAnnos)
		tx = f.Apply(tx)
	}

	return tx
}

type RawdataDeleteFilter struct {
	ID     int64
	IDs    []int64
	DataID int64
}

type RawdataRepo interface {
	repo.GenericRepo[Rawdata]

	DeleteByFilter(context.Context, *RawdataDeleteFilter) error
	ListByFilter(context.Context, *RawdataListFilter) ([]*Rawdata, error)
	ListElement(context.Context, int64, Pager) ([]*Element, error)
}

type RawdataBiz struct {
	repo   RawdataRepo
	svcURL string
	s3cli  *s3.Client
	log    *log.Helper
}

func NewRawdataBiz(repo RawdataRepo, fscfg *conf.FileServer, s3cli *s3.Client, logger log.Logger) *RawdataBiz {
	return &RawdataBiz{
		repo:   repo,
		svcURL: fscfg.SvcUrl,
		s3cli:  s3cli,
		log:    log.NewHelper(logger),
	}
}

func (o *RawdataBiz) Create(ctx context.Context, rd *Rawdata) (*Rawdata, error) {
	return o.repo.Create(ctx, rd)
}

func (o *RawdataBiz) Update(ctx context.Context, p *Rawdata, fldMask *FieldMask) (*Rawdata, error) {
	return o.repo.Update(ctx, p, fldMask)
}

func (o *RawdataBiz) DeleteByFilter(ctx context.Context, filter *RawdataDeleteFilter) error {
	return o.repo.DeleteByFilter(ctx, filter)
}

func (o *RawdataBiz) List(ctx context.Context, filter *RawdataListFilter) ([]*Rawdata, error) {
	return o.repo.ListByFilter(ctx, filter)
}

func (o *RawdataBiz) ListElement(ctx context.Context, dataID int64, pager Pager) ([]*Element, error) {
	return o.repo.ListElement(ctx, dataID, pager)
}

func (o *RawdataBiz) DataRoute(data *Rawdata) string {
	if data.URI == "" {
		return ""
	}
	return o.svcURL + RawdataURLPathPrefix + data.GetUid()
}

func (o *RawdataBiz) EmbeddingRoute(data *Rawdata) string {
	if data.EmbeddingURI == "" {
		return ""
	}
	return o.svcURL + EmbeddingURLPathPrefix + data.GetUid()
}

func (o *RawdataBiz) GetByUid(ctx context.Context, uid string) (*Rawdata, error) {
	return o.repo.GetByID(ctx, kid.ParseID(uid))
}

func (o *RawdataBiz) DataURL(ctx context.Context, rawdata *Rawdata) (string, error) {
	if rawdata == nil || rawdata.URI == "" {
		return "", nil
	}
	uri, err := upload.ParseURI(rawdata.URI)
	if err != nil {
		return "", err
	}
	switch uri.Scheme {
	case upload.StorageTypeS3:
		if rawdata.IsExpired(time.Hour * 1) {
			// regenerate the URL
			res, err := signer.SignGetURL(ctx, rawdata.URI, 0)
			if err != nil {
				return "", err
			}
			rawdata.AccessURL = res.URL
			rawdata.URLExpires = res.ExpiresAt
			// update Rawdata URL and expires
			if conf.ShouldSaveAccessURL() {
				o.repo.Update(ctx, rawdata, field.NewMask(RawdataSfldAccessURL, RawdataSfldURLExpires))
			}
		}
	case upload.StorageTypeLocalFS:
		rawdata.AccessURL = o.svcURL + uri.Path
	default:
		// keep it as is
	}
	return rawdata.AccessURL, nil
}

func (o *RawdataBiz) EmbeddingURL(ctx context.Context, rawdata *Rawdata) (string, error) {
	if rawdata == nil || rawdata.EmbeddingURI == "" {
		return "", nil
	}
	uri, err := upload.ParseURI(rawdata.EmbeddingURI)
	if err != nil {
		return "", err
	}

	url := ""
	switch uri.Scheme {
	case upload.StorageTypeS3:
		// generate a presigned access URL
		res, err := signer.SignGetURL(ctx, rawdata.EmbeddingURI, 0)
		if err != nil {
			return "", err
		}
		url = res.URL
	case upload.StorageTypeLocalFS:
		url = o.svcURL + uri.Path
	default:
		url = rawdata.EmbeddingURI
	}
	return url, nil
}

func (o *RawdataBiz) FilelistReply(ctx context.Context, rawdata *Rawdata) (*types.Filelist, error) {
	extraData := rawdata.ExtraData.E
	uri, err := upload.ParseURI(extraData.PCDSegments[0].URI)
	if err != nil {
		return nil, err
	}
	switch uri.Scheme {
	case upload.StorageTypeS3:
		if rawdata.IsExpired(time.Hour * 1) { // regenerate the URL
			for _, segment := range extraData.PCDSegments {
				res, err := signer.SignGetURL(ctx, segment.URI, 0)
				if err != nil {
					return nil, err
				}
				rawdata.URLExpires = res.ExpiresAt // using the last expire time should be acceptable
				segment.AccessURL = res.URL
			}

			// update Rawdata expires and extra data
			if conf.ShouldSaveAccessURL() {
				o.repo.Update(ctx, rawdata, field.NewMask(RawdataSfldURLExpires, RawdataSfldExtraData))
			}
		}
	case upload.StorageTypeLocalFS:
		for _, segment := range extraData.PCDSegments {
			uri, err := upload.ParseURI(segment.URI)
			if err != nil {
				return nil, err
			}
			segment.AccessURL = o.svcURL + uri.Path
		}
	}

	filelist := &types.Filelist{}
	for _, segment := range extraData.PCDSegments {
		filelist.Files = append(filelist.Files, &types.Filelist_File{
			Url:  segment.AccessURL,
			Size: float64(segment.Size),
		})
	}
	return filelist, nil
}
