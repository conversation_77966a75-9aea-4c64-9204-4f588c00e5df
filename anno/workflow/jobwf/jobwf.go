package jobwf

import (
	"context"
	"time"

	"anno/internal/biz"
	"anno/workflow/common"

	"gitlab.rp.konvery.work/platform/pkg/wf"
	"gitlab.rp.konvery.work/platform/pkg/wf/tracing"
	"gitlab.rp.konvery.work/platform/pkg/wf/wfutil"
	"go.temporal.io/sdk/workflow"
)

func GetJobWfID(jobID int64) string { return "anno-job-" + (&biz.Job{ID: jobID}).GetUid() }

func SignalJobWf(ctx context.Context, jobID int64, ev *common.Event) error {
	opts := &wf.WfArgsOpts{
		Workflow:     JobWorkflow,
		WorkflowArgs: []any{jobID},
		Options: wf.StartWorkflowOptions{
			RetryPolicy: wfutil.RetryPolicy(100),
		},
	}
	wfID := GetJobWfID(jobID)
	_, err := wf.SignalWithStartWorkflow(ctx, wfID, "", ev, opts)
	return err
}

// JobWorkflow is the workflow function for jobs.
func JobWorkflow(ctx workflow.Context, jobID int64) (err error) {
	defer func() {
		err = wfutil.RecreateNonRetryableError(err)
	}()

	var a *Activities
	retry := wfutil.RetryPolicy(10000)
	wfID := GetJobWfID(jobID)
	ctx = tracing.WithCustomTraceID(ctx, wfID)
	lg := workflow.GetLogger(ctx)
	sigChan := wf.SignalChannel(ctx, "")

	for {
		ev := &common.Event{}
		if ok, _ := sigChan.ReceiveWithTimeout(ctx, time.Nanosecond, ev); !ok {
			// Before completing the Workflow or using Continue-As-New, make sure to do an asynchronous drain
			// on the Signal channel. Otherwise, the Signals will be lost.
			// ReceiveAsync will cause some signals redelivered when there are multiple signals to handle.
			// ReceiveWithTimeout (AwaitWithTimeout + ReceiveAsync) will avoid it.
			return
		}

		tracing.PrintTraceID(tracing.WithCustomTraceID(ctx, ev.TraceID), wfID)
		switch ev.Event {
		case common.EvtJobChecking:
			wfutil.ExecActivity(ctx, a.JobCheckAnnos, "", 30*time.Second, 0, retry, jobID)
			wfutil.ExecActivity(ctx, a.JobRollPhase, "", 30*time.Second, 0, retry, jobID, ev)
		case common.EvtJobDoing:
		case common.EvtJobChanged:
		default:
			lg.Error("unknown event", "event", ev.Event)
		}
	}
}
