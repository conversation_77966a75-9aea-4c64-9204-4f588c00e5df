package client

import (
	"anno/internal/conf"

	"gitlab.rp.konvery.work/platform/apis/client"
	"gitlab.rp.konvery.work/platform/apis/iam/v1"
)

type (
	User     = iam.User
	BaseUser = iam.BaseUser
	Team     = iam.Team
)

func initIAM(c *conf.Bootstrap) {
	svc := &client.Service{Addr: c.Rpc.Iam.Addr}
	if c.Rpc.Annofeed.Timeout != nil {
		svc.Timeout = c.Rpc.Annofeed.Timeout.AsDuration()
	}
	client.InitIAM(svc)
}

var (
	ListUsersInMap       = client.ListUsersInMap
	GetUser              = client.GetUser
	GetMe                = client.GetMe
	GetMe2               = client.GetMe2
	GetTeam              = client.GetTeam
	ListTeamsInMap       = client.ListTeamsInMap
	ListOrgTeams         = client.ListOrgTeams
	GetTeamMembers       = client.GetTeamMembers
	IsAllowed            = client.IsAllowed
	CreateAccessPolicies = client.CreateAccessPolicies
	DeleteAccessPolicies = client.DeleteAccessPolicies
	RevokeUsersRole      = client.RevokeUsersRole
	GetAttachedPolicies  = client.GetAttachedPolicies
	CreatePolicy         = client.CreatePolicy
	UpdatePolicy         = client.UpdatePolicy

	ToBaseUser = client.ToBaseUser
)
