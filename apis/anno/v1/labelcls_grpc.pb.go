// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: anno/v1/labelcls.proto

package anno

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Labelclz_CreateLabelcls_FullMethodName = "/anno.v1.Labelclz/CreateLabelcls"
	Labelclz_UpdateLabelcls_FullMethodName = "/anno.v1.Labelclz/UpdateLabelcls"
	Labelclz_DeleteLabelcls_FullMethodName = "/anno.v1.Labelclz/DeleteLabelcls"
	Labelclz_GetLabelcls_FullMethodName    = "/anno.v1.Labelclz/GetLabelcls"
	Labelclz_ListLabelcls_FullMethodName   = "/anno.v1.Labelclz/ListLabelcls"
)

// LabelclzClient is the client API for Labelclz service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LabelclzClient interface {
	CreateLabelcls(ctx context.Context, in *Labelcls, opts ...grpc.CallOption) (*Labelcls, error)
	UpdateLabelcls(ctx context.Context, in *Labelcls, opts ...grpc.CallOption) (*Labelcls, error)
	DeleteLabelcls(ctx context.Context, in *DeleteLabelclsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetLabelcls(ctx context.Context, in *GetLabelclsRequest, opts ...grpc.CallOption) (*Labelcls, error)
	ListLabelcls(ctx context.Context, in *ListLabelclsRequest, opts ...grpc.CallOption) (*ListLabelclsReply, error)
}

type labelclzClient struct {
	cc grpc.ClientConnInterface
}

func NewLabelclzClient(cc grpc.ClientConnInterface) LabelclzClient {
	return &labelclzClient{cc}
}

func (c *labelclzClient) CreateLabelcls(ctx context.Context, in *Labelcls, opts ...grpc.CallOption) (*Labelcls, error) {
	out := new(Labelcls)
	err := c.cc.Invoke(ctx, Labelclz_CreateLabelcls_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *labelclzClient) UpdateLabelcls(ctx context.Context, in *Labelcls, opts ...grpc.CallOption) (*Labelcls, error) {
	out := new(Labelcls)
	err := c.cc.Invoke(ctx, Labelclz_UpdateLabelcls_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *labelclzClient) DeleteLabelcls(ctx context.Context, in *DeleteLabelclsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Labelclz_DeleteLabelcls_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *labelclzClient) GetLabelcls(ctx context.Context, in *GetLabelclsRequest, opts ...grpc.CallOption) (*Labelcls, error) {
	out := new(Labelcls)
	err := c.cc.Invoke(ctx, Labelclz_GetLabelcls_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *labelclzClient) ListLabelcls(ctx context.Context, in *ListLabelclsRequest, opts ...grpc.CallOption) (*ListLabelclsReply, error) {
	out := new(ListLabelclsReply)
	err := c.cc.Invoke(ctx, Labelclz_ListLabelcls_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LabelclzServer is the server API for Labelclz service.
// All implementations must embed UnimplementedLabelclzServer
// for forward compatibility
type LabelclzServer interface {
	CreateLabelcls(context.Context, *Labelcls) (*Labelcls, error)
	UpdateLabelcls(context.Context, *Labelcls) (*Labelcls, error)
	DeleteLabelcls(context.Context, *DeleteLabelclsRequest) (*emptypb.Empty, error)
	GetLabelcls(context.Context, *GetLabelclsRequest) (*Labelcls, error)
	ListLabelcls(context.Context, *ListLabelclsRequest) (*ListLabelclsReply, error)
	mustEmbedUnimplementedLabelclzServer()
}

// UnimplementedLabelclzServer must be embedded to have forward compatible implementations.
type UnimplementedLabelclzServer struct {
}

func (UnimplementedLabelclzServer) CreateLabelcls(context.Context, *Labelcls) (*Labelcls, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLabelcls not implemented")
}
func (UnimplementedLabelclzServer) UpdateLabelcls(context.Context, *Labelcls) (*Labelcls, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLabelcls not implemented")
}
func (UnimplementedLabelclzServer) DeleteLabelcls(context.Context, *DeleteLabelclsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLabelcls not implemented")
}
func (UnimplementedLabelclzServer) GetLabelcls(context.Context, *GetLabelclsRequest) (*Labelcls, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLabelcls not implemented")
}
func (UnimplementedLabelclzServer) ListLabelcls(context.Context, *ListLabelclsRequest) (*ListLabelclsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLabelcls not implemented")
}
func (UnimplementedLabelclzServer) mustEmbedUnimplementedLabelclzServer() {}

// UnsafeLabelclzServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LabelclzServer will
// result in compilation errors.
type UnsafeLabelclzServer interface {
	mustEmbedUnimplementedLabelclzServer()
}

func RegisterLabelclzServer(s grpc.ServiceRegistrar, srv LabelclzServer) {
	s.RegisterService(&Labelclz_ServiceDesc, srv)
}

func _Labelclz_CreateLabelcls_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Labelcls)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LabelclzServer).CreateLabelcls(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Labelclz_CreateLabelcls_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LabelclzServer).CreateLabelcls(ctx, req.(*Labelcls))
	}
	return interceptor(ctx, in, info, handler)
}

func _Labelclz_UpdateLabelcls_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Labelcls)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LabelclzServer).UpdateLabelcls(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Labelclz_UpdateLabelcls_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LabelclzServer).UpdateLabelcls(ctx, req.(*Labelcls))
	}
	return interceptor(ctx, in, info, handler)
}

func _Labelclz_DeleteLabelcls_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLabelclsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LabelclzServer).DeleteLabelcls(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Labelclz_DeleteLabelcls_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LabelclzServer).DeleteLabelcls(ctx, req.(*DeleteLabelclsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Labelclz_GetLabelcls_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLabelclsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LabelclzServer).GetLabelcls(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Labelclz_GetLabelcls_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LabelclzServer).GetLabelcls(ctx, req.(*GetLabelclsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Labelclz_ListLabelcls_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLabelclsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LabelclzServer).ListLabelcls(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Labelclz_ListLabelcls_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LabelclzServer).ListLabelcls(ctx, req.(*ListLabelclsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Labelclz_ServiceDesc is the grpc.ServiceDesc for Labelclz service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Labelclz_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "anno.v1.Labelclz",
	HandlerType: (*LabelclzServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateLabelcls",
			Handler:    _Labelclz_CreateLabelcls_Handler,
		},
		{
			MethodName: "UpdateLabelcls",
			Handler:    _Labelclz_UpdateLabelcls_Handler,
		},
		{
			MethodName: "DeleteLabelcls",
			Handler:    _Labelclz_DeleteLabelcls_Handler,
		},
		{
			MethodName: "GetLabelcls",
			Handler:    _Labelclz_GetLabelcls_Handler,
		},
		{
			MethodName: "ListLabelcls",
			Handler:    _Labelclz_ListLabelcls_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "anno/v1/labelcls.proto",
}
