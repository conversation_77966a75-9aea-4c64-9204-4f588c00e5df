package main

import (
	"annofeed/internal/conf"
	"annofeed/internal/signer"
	"gitlab.rp.konvery.work/platform/pkg/download"
	"gitlab.rp.konvery.work/platform/pkg/upload"
)

func initFileServer(cfg *conf.FileServer) {
	if cfg.Storage == "" {
		cfg.Storage = upload.StorageTypeLocalFS
	}

	signer.Init(cfg)

	upload.Init(&upload.Config{
		Storage:      cfg.Storage,
		BaseDir:      cfg.BaseDir,
		Bucket:       cfg.Bucket,
		PublicBucket: cfg.PublicBucket,
	})
}

func initDownload(workdir string, cfg *conf.FileServer, profile download.ProfileProvider) {
	download.Init(&download.Config{
		WorkDir:       workdir,
		LocalUploader: upload.LocalFSConfig{BaseDir: cfg.BaseDir},
		Profile:       profile,
	})
}
