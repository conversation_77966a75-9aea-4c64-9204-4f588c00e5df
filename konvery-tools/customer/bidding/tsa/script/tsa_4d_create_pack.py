import os
import shutil
import sys
import uuid
import numpy as np
sys.path.append('.')
from customer.common import tools


cam_name = 'front_near'

category_mapping = {
    'c_right': 'c_right_arrow',
    'left': 'left_arrow',
    'right': 'right_arrow',
    'straight': 'straight_arrow',
    'straight_left': 'straight_left_arrow',
    'straight_right': 'straight_right_arrow',
    'stopline': 'stop_line',
    'pending_transfer_area_dash': 'pending_transfer_area_dash_lane'
}


def get_rel_id(obj, rel_type):
    if '0' in obj[rel_type]:
        obj[rel_type].remove('0')
    if obj[rel_type]:
        return [','.join(obj[rel_type]).replace('\uff0c', ',')]
    return []


def get_classification(obj):
    if isinstance(obj['class'], list):
        classification = category_mapping.get(obj['class'][0], obj['class'][0])
    else:
        classification = category_mapping.get(obj['class'], obj['class'])
    return classification


def construct_pre_label(pre_label_file, collection, element):
    pre_label_data = tools.get_json_data(pre_label_file)
    max_id = 0
    for obj in pre_label_data['task_lane_divider']:
        max_id = max(max_id, int(obj['LLID']))
    for obj in pre_label_data['task_lane_relation']:
        max_id = max(max_id, int(obj['LSID']))
    track_id = max_id + 1

    labels = []
    for obj in pre_label_data['task_lane_divider']:
        points = []
        for pt in obj['point']:
            points.extend(pt)
        label = {
            "uuid": str(uuid.uuid4()),
            "track_id": obj['LLID'],
            "label": {
                "name": 'class',
                "widget": {
                    "name": "line3d",
                    "data": points
                },
                "attrs": [
                    {"name": 'category', "values": [get_classification(obj)]},
                    {"name": 'color', "values": obj['color'] if isinstance(obj['color'], list) else [obj['color']]},
                    {"name": 'pre_id', "values": get_rel_id(obj, 'pre_id')},
                    {"name": 'suc_id', "values": get_rel_id(obj, 'suc_id')},
                ]
            },
            "source": "manual"
        }
        labels.append(label)
    for obj in pre_label_data['task_lane_relation']:
        points = []
        for pt in obj['point']:
            points.extend(pt)
        label = {
            "uuid": str(uuid.uuid4()),
            "track_id": obj['LSID'],
            "label": {
                "name": 'Lane_Segment',
                "widget": {
                    "name": "poly3d",
                    "data": points
                },
                "attrs": [
                    {"name": 'pre_id', "values": get_rel_id(obj, 'pre_id')},
                    {"name": 'suc_id', "values": get_rel_id(obj, 'suc_id')},
                    {"name": 'left_id', "values": get_rel_id(obj, 'left_lane')},
                    {"name": 'right_id', "values": get_rel_id(obj, 'right_lane')},
                ]
            },
            "source": "manual"
        }
        labels.append(label)
    for obj in pre_label_data['task_road_edge']:
        points = []
        for pt in obj['point']:
            points.extend(pt)
        label = {
            "uuid": str(uuid.uuid4()),
            "track_id": str(track_id),
            "label": {
                "name": 'road',
                "widget": {
                    "name": "line3d",
                    "data": points
                },
                "attrs": [
                    {"name": 'classification', "values": obj['class'] if isinstance(obj['class'], list) else [obj['class']]},
                ]
            },
            "source": "manual"
        }
        track_id += 1
        labels.append(label)
    for obj in pre_label_data['task_arrow_polygon']:
        points = []
        for pt in obj['point']:
            points.extend(pt)
        label = {
            "uuid": str(uuid.uuid4()),
            "track_id": str(track_id),
            "label": {
                "name": 'Groundmarking',
                "widget": {
                    "name": "poly3d",
                    "data": points
                },
                "attrs": [
                    {"name": 'classification', "values": obj['class'] if isinstance(obj['class'], list) else [obj['class']]},
                ]
            },
            "source": "manual"
        }
        track_id += 1
        labels.append(label)
    annos = {
        "element_annos": [
            {
                "name": f"{collection}/{element}",
                "rawdata_annos": [
                    {
                        "name": f"{collection}/{element}/lidar.pcd",
                        "objects": labels,
                        "attrs": []
                    }
                ],
                "attrs": []
            }
        ]
    }
    return annos


def run(input_dir, output_dir):
    imgs = tools.get_file_by_extension(input_dir, '.jpg')
    imgs.sort(key=lambda img: img.name)
    pre_label_file = [file for file in tools.listdir(input_dir, full_path=True) if file.endswith('.json') and os.path.basename(file).startswith('intensity')][0]
    pose_mats = np.load([file for file in tools.listdir(input_dir, full_path=True) if file.endswith('.npy')][0])
    pcd_file = [file for file in tools.listdir(input_dir, full_path=True) if file.endswith('.pcd')][0]
    pose_orider_path = [file for file in tools.listdir(input_dir, full_path=True) if file.endswith('.txt')][0]
    pose_file_names = tools.read_file(pose_orider_path)
    pose_file_names = [os.path.basename(pose_file_name.strip()) for pose_file_name in pose_file_names]
    file_name2pose_mat = dict(zip(pose_file_names, pose_mats))
    calibration_file = [file for file in tools.listdir(input_dir, full_path=True) if file.endswith('.json') and os.path.basename(file).startswith('calibration')][0]
    calibration_data = tools.get_json_data(calibration_file)
    out_data_dir = os.path.join(output_dir, 'data')
    os.mkdir(out_data_dir)
    out_annos_dir = os.path.join(output_dir, 'annos')
    os.mkdir(out_annos_dir)
    out_clip_dir = os.path.join(out_data_dir, os.path.basename(input_dir))
    os.mkdir(out_clip_dir)
    out_annos_clip_dir = os.path.join(out_annos_dir, os.path.basename(input_dir))
    os.mkdir(out_annos_clip_dir)

    for idx, img in enumerate(imgs):
        img_name = os.path.basename(img)
        img_timestamp = img_name.split('.')[0]
        out_frame_dir = os.path.join(out_clip_dir, img_timestamp)
        os.mkdir(out_frame_dir)
        out_annos_frame_dir = os.path.join(out_annos_clip_dir, img_timestamp)
        shutil.copyfile(img, os.path.join(out_frame_dir, 'cam.jpg'))
        if idx == 0:
            shutil.copy(pcd_file, os.path.join(out_frame_dir, 'lidar.pcd'))
            annos = construct_pre_label(pre_label_file,
                                        os.path.basename(os.path.dirname(os.path.dirname(img.path))),
                                        img.name.split('.')[0])
        else:
            annos = {
                "label_meta": {"mark_status": 0, "global": {}},
                "labels": []
            }
        tools.write_json_file(annos, os.path.join(out_annos_frame_dir, 'annos.json'), ensure_ascii=False, indent=4)
        pose_mat = file_name2pose_mat[img_name]
        params = {
            "meta": {
                "version": "v2"
            },
            "lidar": {
                "viewpoint": tools.mat_to_pose(pose_mat),
                "pose": tools.mat_to_pose(pose_mat),
            },
            "cameras": {
                "cam": {
                    "extrinsic": (np.array(calibration_data[cam_name]['extrinsics']['lidar2camera']) @ np.linalg.inv(pose_mat)).flatten().tolist(),
                    "intrinsic": [
                        calibration_data[cam_name]['intrinsics']['fx'], 0, calibration_data[cam_name]['intrinsics']['cx'],
                        0, calibration_data[cam_name]['intrinsics']['fy'], calibration_data[cam_name]['intrinsics']['cy'],
                        0, 0, 1
                    ],
                    "distortion": [
                        0, 0, 0, 0, 0, 0, 0, 0, 0
                        # calibration_data[cam_name]['distortions']['k1'],
                        # calibration_data[cam_name]['distortions']['k2'],
                        # calibration_data[cam_name]['distortions']['p1'],
                        # calibration_data[cam_name]['distortions']['p2'],
                        # calibration_data[cam_name]['distortions']['k3'],
                        # calibration_data[cam_name]['distortions']['k4'],
                        # calibration_data[cam_name]['distortions']['k5'],
                        # calibration_data[cam_name]['distortions']['k6'],
                    ]
                }
            }
        }
        tools.write_json_file(params, os.path.join(out_frame_dir, 'params.json'))


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
