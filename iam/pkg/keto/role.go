package keto

import (
	"context"
	"fmt"
	"strings"

	keto "github.com/ory/keto/proto/ory/keto/relation_tuples/v1alpha2"
)

const (
	RoleNs       = "IamRole"
	RoleRelPerms = "perms"
)

// role policy template
const (
	roleTpl       = "IamRole:<role-name>#policies@IamPolicy:sys/admin\n"
	roleViewerTpl = "IamRole:<role-name>#policies@IamPolicy:sys/public-view\n"
	roleParentTpl = "IamRole:<role-name>#parents@IamGroup:<parent-name>\n"
)

func init() {
	// validate templates
	s := roleTpl + roleViewerTpl + roleParentTpl
	_, err := ParseTuples(s)
	if err != nil {
		panic(fmt.Errorf("failed to parse resource policy template \n: %v\n: %w", s, err))
	}
}

func MakeRoleName(name string) string { return MakeObjectName(RoleNs, name) }

func (o *AccessMgr) CreateRole(ctx context.Context, name, parent string, perms []string) (err error) {
	tpl := roleTpl
	if parent == "" {
		// it's a global role
		tpl += roleViewerTpl
	} else {
		tpl += strings.ReplaceAll(roleParentTpl, "<parent-name>", parent)
	}
	tpl = strings.ReplaceAll(tpl, "<role-name>", name)
	tps, err := ParseTuples(tpl)
	if err != nil {
		return fmt.Errorf("failed to parse role policy template: %q: %w", tpl, err)
	}

	permtps, err := o.makeRolePermTuples(name, perms)
	if err != nil {
		return
	}
	return o.kt.Add(ctx, append(tps, permtps...)...)
}

func (o *AccessMgr) GetRole(ctx context.Context, name string) (perms []string, err error) {
	rsp, err := o.kt.List(ctx, &keto.ListRelationTuplesRequest{
		RelationQuery: NewQuery(RoleNs, name, RoleRelPerms, nil),
	})
	if err != nil {
		return
	}

	for _, t := range rsp.RelationTuples {
		perm := t.Subject.GetId()
		if perm == "" {
			sub := t.Subject.GetSet()
			perm = MakeObjectName(sub.Namespace, sub.Object)
		}
		perms = append(perms, perm)
	}
	return
}

// ExpandRolePerms walks the relation tree and collects the permissions.
// It may return a partial result if the depth of the relation tree is greater than the max search depth set in keto.
// Caution: The query may be costly if the query results in lots of items or involves many relation indirections.
func (o *AccessMgr) ExpandRolePerms(ctx context.Context, name string) (perms []string, err error) {
	rsp, err := o.kt.Expand(ctx, &keto.ExpandRequest{
		Subject: NewSubjectSet(RoleNs, name, RoleRelPerms),
	})
	if err != nil {
		return
	}
	WalkSubjectTree(rsp.Tree, func(t *keto.RelationTuple, isLeaf bool) error {
		if isLeaf && t.Subject.GetId() != "" {
			perms = append(perms, t.Subject.GetId())
		}
		return nil
	})
	return
}

func (o *AccessMgr) makeRolePermTuples(name string, perms []string) (tps []*RelationTuple, err error) {
	for _, perm := range perms {
		var subject *keto.Subject
		ns, obj := ParseObjectName(perm)
		if ns != "" {
			if ns != RoleNs {
				return nil, fmt.Errorf("want a role or permission, got %s", perm)
			}
			subject = NewSubjectSet(ns, obj, "")
		} else {
			subject = NewSubjectId(obj)
		}
		tps = append(tps, NewTuple(RoleNs, name, RoleRelPerms, subject))
	}
	return
}
func (o *AccessMgr) AddRolePerms(ctx context.Context, name string, perms []string) (err error) {
	tps, err := o.makeRolePermTuples(name, perms)
	if err != nil {
		return
	}
	return o.kt.Add(ctx, tps...)
}

func (o *AccessMgr) DeleteRolePerms(ctx context.Context, name string, perms []string) (err error) {
	tps, err := o.makeRolePermTuples(name, perms)
	if err != nil {
		return
	}
	return o.kt.Delete(ctx, tps...)
}

func (o *AccessMgr) DeleteRole(ctx context.Context, name string) (err error) {
	return o.DeleteObject(ctx, MakeObjectName(RoleNs, name))
}
