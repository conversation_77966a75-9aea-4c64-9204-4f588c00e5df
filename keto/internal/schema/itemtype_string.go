// Code generated by "stringer -type=itemType -trimprefix item -linecomment"; DO NOT EDIT.

package schema

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[itemError-0]
	_ = x[itemEOF-1]
	_ = x[itemIdentifier-2]
	_ = x[itemComment-3]
	_ = x[itemStringLiteral-4]
	_ = x[itemKeywordClass-5]
	_ = x[itemKeywordImplements-6]
	_ = x[itemKeywordThis-7]
	_ = x[itemKeywordCtx-8]
	_ = x[itemOperatorAnd-9]
	_ = x[itemOperatorOr-10]
	_ = x[itemOperatorNot-11]
	_ = x[itemOperatorAssign-12]
	_ = x[itemOperatorEqual-13]
	_ = x[itemOperatorArrow-14]
	_ = x[itemOperatorDot-15]
	_ = x[itemOperatorColon-16]
	_ = x[itemOperatorComma-17]
	_ = x[itemSemicolon-18]
	_ = x[itemTypeUnion-19]
	_ = x[itemParenLeft-20]
	_ = x[itemParenRight-21]
	_ = x[itemBraceLeft-22]
	_ = x[itemBraceRight-23]
	_ = x[itemBracketLeft-24]
	_ = x[itemBracketRight-25]
	_ = x[itemAngledLeft-26]
	_ = x[itemAngledRight-27]
}

const _itemType_name = "ErrorEOFIdentifierCommentStringLiteralKeywordClassKeywordImplementsKeywordThisKeywordCtx\"&&\"\"||\"\"!\"\"=\"\"==\"\"=>\"\".\"\":\"\",\"\";\"\"|\"\"(\"\")\"\"{\"\"}\"\"[\"\"]\"\"<\"\">\""

var _itemType_index = [...]uint8{0, 5, 8, 18, 25, 38, 50, 67, 78, 88, 92, 96, 99, 102, 106, 110, 113, 116, 119, 122, 125, 128, 131, 134, 137, 140, 143, 146, 149}

func (i itemType) String() string {
	if i < 0 || i >= itemType(len(_itemType_index)-1) {
		return "itemType(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _itemType_name[_itemType_index[i]:_itemType_index[i+1]]
}
