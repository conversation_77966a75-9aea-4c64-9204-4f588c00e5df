// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: iam/v1/perm.proto

package iam

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationPermsEditPerms = "/iam.v1.Perms/EditPerms"
const OperationPermsListPerm = "/iam.v1.Perms/ListPerm"
const OperationPermsListPermClass = "/iam.v1.Perms/ListPermClass"

type PermsHTTPServer interface {
	EditPerms(context.Context, *EditPermsRequest) (*emptypb.Empty, error)
	// ListPerm get permission list
	ListPerm(context.Context, *ListPermRequest) (*ListPermReply, error)
	// ListPermClass get permission class (resource type) list: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
	ListPermClass(context.Context, *emptypb.Empty) (*ListPermClassReply, error)
}

func RegisterPermsHTTPServer(s *http.Server, srv PermsHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/perms", _Perms_EditPerms0_HTTP_Handler(srv))
	r.GET("/v1/perms/classes", _Perms_ListPermClass0_HTTP_Handler(srv))
	r.GET("/v1/perms", _Perms_ListPerm0_HTTP_Handler(srv))
}

func _Perms_EditPerms0_HTTP_Handler(srv PermsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EditPermsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPermsEditPerms)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.EditPerms(ctx, req.(*EditPermsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Perms_ListPermClass0_HTTP_Handler(srv PermsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPermsListPermClass)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListPermClass(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListPermClassReply)
		return ctx.Result(200, reply)
	}
}

func _Perms_ListPerm0_HTTP_Handler(srv PermsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListPermRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPermsListPerm)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListPerm(ctx, req.(*ListPermRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListPermReply)
		return ctx.Result(200, reply)
	}
}

type PermsHTTPClient interface {
	EditPerms(ctx context.Context, req *EditPermsRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	ListPerm(ctx context.Context, req *ListPermRequest, opts ...http.CallOption) (rsp *ListPermReply, err error)
	ListPermClass(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *ListPermClassReply, err error)
}

type PermsHTTPClientImpl struct {
	cc *http.Client
}

func NewPermsHTTPClient(client *http.Client) PermsHTTPClient {
	return &PermsHTTPClientImpl{client}
}

func (c *PermsHTTPClientImpl) EditPerms(ctx context.Context, in *EditPermsRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/perms"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPermsEditPerms))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PermsHTTPClientImpl) ListPerm(ctx context.Context, in *ListPermRequest, opts ...http.CallOption) (*ListPermReply, error) {
	var out ListPermReply
	pattern := "/v1/perms"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPermsListPerm))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PermsHTTPClientImpl) ListPermClass(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*ListPermClassReply, error) {
	var out ListPermClassReply
	pattern := "/v1/perms/classes"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPermsListPermClass))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
