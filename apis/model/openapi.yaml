# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
paths:
    /model/v1/models:
        get:
            tags:
                - Models
            operationId: Models_List
            parameters:
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: |-
                    An opaque pagination token returned from a previous call.
                     An empty token denotes the first page.
                  schema:
                    type: string
                - name: filter.run_uid
                  in: query
                  description: run uid
                  schema:
                    type: string
                - name: filter.type
                  in: query
                  description: type
                  schema:
                    type: string
                - name: filter.name_pattern
                  in: query
                  description: name pattern
                  schema:
                    type: string
                - name: filter.version
                  in: query
                  description: model version
                  schema:
                    type: string
                - name: filter.base_pattern
                  in: query
                  description: base model pattern
                  schema:
                    type: string
                - name: filter.tags
                  in: query
                  description: attached tags
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/model.v1.ListModelReply'
        post:
            tags:
                - Models
            operationId: Models_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/model.v1.CreateModelRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/model.v1.Model'
    /model/v1/models/{uid}:
        get:
            tags:
                - Models
            operationId: Models_Get
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/model.v1.Model'
        delete:
            tags:
                - Models
            operationId: Models_Delete
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: filter.run_uid
                  in: query
                  description: run uid
                  schema:
                    type: string
                - name: filter.type
                  in: query
                  description: type
                  schema:
                    type: string
                - name: filter.name_pattern
                  in: query
                  description: name pattern
                  schema:
                    type: string
                - name: filter.version
                  in: query
                  description: model version
                  schema:
                    type: string
                - name: filter.base_pattern
                  in: query
                  description: base model pattern
                  schema:
                    type: string
                - name: filter.tags
                  in: query
                  description: attached tags
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content: {}
        patch:
            tags:
                - Models
            operationId: Models_Update
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: fields
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/model.v1.CreateModelRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/model.v1.Model'
    /model/v1/models/{uid}/tag:
        put:
            tags:
                - Models
            operationId: Models_AddTag
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            type: string
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/types.TagList'
        delete:
            tags:
                - Models
            operationId: Models_DeleteTag
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: tags
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/types.TagList'
    /model/v1/runs:
        get:
            tags:
                - Runs
            operationId: Runs_List
            parameters:
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: |-
                    An opaque pagination token returned from a previous call.
                     An empty token denotes the first page.
                  schema:
                    type: string
                - name: filter.operator_uid
                  in: query
                  description: operator uid
                  schema:
                    type: string
                - name: filter.type
                  in: query
                  description: type
                  schema:
                    type: string
                - name: filter.states
                  in: query
                  description: states
                  schema:
                    type: array
                    items:
                        enum:
                            - unspecified
                            - unstart
                            - running
                            - finished
                            - failed
                            - canceled
                        type: string
                        format: enum
                - name: filter.name_pattern
                  in: query
                  description: name pattern
                  schema:
                    type: string
                - name: filter.base_model_pattern
                  in: query
                  description: base model pattern
                  schema:
                    type: string
                - name: filter.tags
                  in: query
                  description: attached tags
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/model.v1.ListRunReply'
        post:
            tags:
                - Runs
            operationId: Runs_Create
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/model.v1.CreateRunRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/model.v1.Run'
    /model/v1/runs/{uid}:
        get:
            tags:
                - Runs
            operationId: Runs_Get
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/model.v1.Run'
        delete:
            tags:
                - Runs
            operationId: Runs_Delete
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: filter.operator_uid
                  in: query
                  description: operator uid
                  schema:
                    type: string
                - name: filter.type
                  in: query
                  description: type
                  schema:
                    type: string
                - name: filter.states
                  in: query
                  description: states
                  schema:
                    type: array
                    items:
                        enum:
                            - unspecified
                            - unstart
                            - running
                            - finished
                            - failed
                            - canceled
                        type: string
                        format: enum
                - name: filter.name_pattern
                  in: query
                  description: name pattern
                  schema:
                    type: string
                - name: filter.base_model_pattern
                  in: query
                  description: base model pattern
                  schema:
                    type: string
                - name: filter.tags
                  in: query
                  description: attached tags
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content: {}
        patch:
            tags:
                - Runs
            operationId: Runs_Update
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: fields
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/model.v1.CreateRunRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/model.v1.Run'
    /model/v1/runs/{uid}/cancel:
        put:
            tags:
                - Runs
            operationId: Runs_Cancel
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/model.v1.GetRunRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /model/v1/runs/{uid}/finish:
        put:
            tags:
                - Runs
            operationId: Runs_Finish
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/model.v1.FinishRunRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /model/v1/runs/{uid}/start:
        put:
            tags:
                - Runs
            operationId: Runs_Start
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/model.v1.StartRunRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /model/v1/runs/{uid}/tag:
        put:
            tags:
                - Runs
            operationId: Runs_AddTag
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            type: string
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/types.TagList'
        delete:
            tags:
                - Runs
            operationId: Runs_DeleteTag
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: tags
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/types.TagList'
    /model/v1/version:
        get:
            tags:
                - Configs
            operationId: Configs_GetVersion
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/model.v1.GetVersionReply'
components:
    schemas:
        model.v1.CreateModelRequest:
            type: object
            properties:
                name:
                    type: string
                    description: '[mandatory for create] model name'
                type:
                    type: string
                    description: '[mandatory for create] model type: od/segmentation'
                base:
                    type: string
                    description: base model name
                run_uid:
                    type: string
                    description: run uid
                uri:
                    type: string
                    description: uri
                version:
                    type: string
                    description: '[mandatory for create] version'
                bytes:
                    type: number
                    description: the size of the model in bytes
                    format: double
                metric:
                    type: object
                    additionalProperties:
                        type: number
                        format: double
                    description: 'metric of the model: precision/recall/accuracy/f1/mae/rmse/...'
                tags:
                    type: array
                    items:
                        type: string
                    description: tags attached to the model
        model.v1.CreateRunRequest:
            type: object
            properties:
                name:
                    type: string
                    description: '[mandatory for create] run name'
                type:
                    type: string
                    description: '[mandatory for create] model type: od/segmentation'
                dataset:
                    allOf:
                        - $ref: '#/components/schemas/model.v1.Run_Dataset'
                    description: dataset used for this run
                target:
                    type: string
                    description: define the conditions to stop training
                max_credits:
                    type: integer
                    description: maximum credits the run can consume
                    format: int32
                base_model:
                    type: string
                    description: base model name and version
                tags:
                    type: array
                    items:
                        type: string
                    description: tags attached to the run
                pipeline:
                    allOf:
                        - $ref: '#/components/schemas/model.v1.Run_Pipeline'
                    description: training pipeline image
                model_uid:
                    type: string
                    description: uid of the model uploaded by this run
                artifacts:
                    type: string
                    description: artifacts uploaded by this run
        model.v1.FinishRunRequest:
            type: object
            properties:
                uid:
                    type: string
                fail_reason:
                    type: string
                    description: fail reason when the run should be failed; empty means the run should be finished successfully
        model.v1.GetRunRequest:
            type: object
            properties:
                uid:
                    type: string
        model.v1.GetVersionReply:
            type: object
            properties:
                version:
                    type: string
        model.v1.ListModelReply:
            required:
                - models
                - next_page_token
            type: object
            properties:
                models:
                    type: array
                    items:
                        $ref: '#/components/schemas/model.v1.Model'
                next_page_token:
                    type: string
                    description: An opaque pagination token, if not empty, to be used to fetch the next page of results
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
        model.v1.ListRunReply:
            required:
                - runs
                - next_page_token
            type: object
            properties:
                runs:
                    type: array
                    items:
                        $ref: '#/components/schemas/model.v1.Run'
                next_page_token:
                    type: string
                    description: An opaque pagination token, if not empty, to be used to fetch the next page of results
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
        model.v1.Model:
            required:
                - uid
                - name
                - type
                - uri
                - version
                - created_at
            type: object
            properties:
                uid:
                    type: string
                    description: model uid
                name:
                    type: string
                    description: model name
                type:
                    type: string
                    description: 'model type: od/segmentation'
                base:
                    type: string
                    description: base model name
                run_uid:
                    type: string
                    description: run uid
                uri:
                    type: string
                    description: uri
                version:
                    type: string
                    description: version
                bytes:
                    type: number
                    description: the size of the model in bytes
                    format: double
                metric:
                    type: object
                    additionalProperties:
                        type: number
                        format: double
                    description: 'metric of the model: precision/recall/accuracy/f1/mae/rmse/...'
                tags:
                    type: array
                    items:
                        type: string
                    description: tags attached to the model
                updated_at:
                    type: string
                    format: date-time
                created_at:
                    type: string
                    format: date-time
        model.v1.Run:
            required:
                - uid
                - name
                - type
                - operator_uid
                - dataset
                - state
                - created_at
            type: object
            properties:
                uid:
                    type: string
                    description: run uid
                name:
                    type: string
                    description: run name
                type:
                    type: string
                    description: 'model type: od/segmentation'
                operator_uid:
                    type: string
                    description: operator uid
                dataset:
                    allOf:
                        - $ref: '#/components/schemas/model.v1.Run_Dataset'
                    description: dataset used for this run
                target:
                    type: string
                    description: define the conditions to stop training
                max_credits:
                    type: integer
                    description: maximum credits the run can consume
                    format: int32
                base_model:
                    type: string
                    description: base model name and version
                tags:
                    type: array
                    items:
                        type: string
                    description: tags attached to the run
                pipeline:
                    allOf:
                        - $ref: '#/components/schemas/model.v1.Run_Pipeline'
                    description: training pipeline
                state:
                    enum:
                        - unspecified
                        - unstart
                        - running
                        - finished
                        - failed
                        - canceled
                    type: string
                    description: state of the run
                    format: enum
                fail_reason:
                    type: string
                    description: fail reason
                model_uid:
                    type: string
                    description: uid of the model uploaded by this run
                started_at:
                    type: string
                    format: date-time
                ended_at:
                    type: string
                    format: date-time
                updated_at:
                    type: string
                    format: date-time
                created_at:
                    type: string
                    format: date-time
                artifacts:
                    type: string
                    description: artifacts uploaded by this run
        model.v1.Run_Dataset:
            required:
                - uris
                - size_gb
            type: object
            properties:
                uris:
                    type: array
                    items:
                        type: string
                    description: uris of the dataset
                size_gb:
                    type: integer
                    description: dataset size in giga bytes
                    format: int32
        model.v1.Run_Pipeline:
            type: object
            properties:
                image:
                    type: string
                command:
                    type: array
                    items:
                        type: string
        model.v1.StartRunRequest:
            type: object
            properties:
                uid:
                    type: string
                option:
                    enum:
                        - unspecified
                        - retry_nok
                    type: string
                    description: start option
                    format: enum
        types.TagList:
            required:
                - tags
            type: object
            properties:
                tags:
                    type: array
                    items:
                        type: string
tags:
    - name: Configs
    - name: Models
    - name: Runs
