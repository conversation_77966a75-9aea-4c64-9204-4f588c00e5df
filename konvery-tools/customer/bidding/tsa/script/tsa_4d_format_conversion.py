import os
import sys
import numpy as np
sys.path.append('.')
from customer.common import tools


def get_rel_id(label, rel_type):
    if '0' == tools.get_value_from_kw_attrs(label['label']['attrs'], rel_type):
        return []
    return tools.get_value_from_kw_attrs(label['label']['attrs'], rel_type).replace('，', ',').split(',')


def run(input_dir, output_dir):
    for clip_dir in tools.listdir(os.path.join(input_dir, 'annos'), full_path=True, sort=True):
        first_json_file = os.path.join(tools.listdir(clip_dir, full_path=True, sort=True)[0], 'annos.json')
        json_data = tools.get_json_data(first_json_file)
        task_lane_divider = []
        task_lane_relation = []
        task_road_edge = []
        task_arrow_polygon = []
        rawdata_annos = json_data['element_annos'][0]['rawdata_annos']
        for rawdata_anno in rawdata_annos:
            if not rawdata_anno['name'].endswith('lidar.pcd'):
                continue
            for idx, label in enumerate(rawdata_anno['objects'], start=1):
                if label['label']['name'] == 'Lane_Segment':
                    obj = {
                        "LSID": str(idx),
                        "point": np.array(label['label']['widget']['data']).reshape(-1, 3).tolist(),
                        "pre_id": get_rel_id(label, 'pre_id'),
                        "suc_id": get_rel_id(label, 'suc_id'),
                        "left_lane": get_rel_id(label, 'left_id'),
                        "right_lane": get_rel_id(label, 'right_id'),
                        "type": "polygon"
                    }
                    task_lane_relation.append(obj)
                elif label['label']['name'] == 'class':
                    obj = {
                        "LLID": str(idx),
                        "point": np.array(label['label']['widget']['data']).reshape(-1, 3).tolist(),
                        "class": tools.get_value_from_kw_attrs(label['label']['attrs'], 'category'),
                        "color": tools.get_value_from_kw_attrs(label['label']['attrs'], 'color'),
                        "pre_id": get_rel_id(label, 'pre_id'),
                        "suc_id": get_rel_id(label, 'suc_id'),
                        "type": "polyline"
                    }
                    if obj['class'] == 'wide_solid':
                        obj['class'] = 'wide_solid_lane'
                    elif obj['class'] == 'wide_dash':
                        obj['class'] = 'wide_dash_lane'
                    task_lane_divider.append(obj)
                elif label['label']['name'] == 'road':
                    obj = {
                        "point": np.array(label['label']['widget']['data']).reshape(-1, 3).tolist(),
                        "class": tools.get_value_from_kw_attrs(label['label']['attrs'], 'classification1'),
                        "type": "polyline"
                    }
                    if tools.get_value_from_kw_attrs(label['label']['attrs'], 'circular') == 'yes':
                        obj['point'] = obj['point'] + [obj['point'][0]]
                    task_road_edge.append(obj)
                elif label['label']['name'] == 'stop_line':
                    obj = {
                        "point": np.array(label['label']['widget']['data']).reshape(-1, 3).tolist(),
                        "class": tools.get_value_from_kw_attrs(label['label']['attrs'], 'polyline'),
                        "type": "polyline"
                    }
                    if obj['class'] == 'double_soild_stop_line':
                        obj['class'] = 'double_solid_stop_line'
                    task_arrow_polygon.append(obj)
                elif label['label']['name'] == 'Groundmarking':
                    obj = {
                        "point": np.array(label['label']['widget']['data']).reshape(-1, 3).tolist(),
                        "class": tools.get_value_from_kw_attrs(label['label']['attrs'], 'classification'),
                        "type": "polygon"
                    }
                    task_arrow_polygon.append(obj)
                else:
                    raise NotImplementedError(f'unsupported label class {label["class"]}')
        result = {
            "cam_folder": None,
            "data_folder": None,
            "file_name": None,
            "task_lane_divider": task_lane_divider,
            "task_lane_relation": task_lane_relation,
            "task_road_edge": task_road_edge,
            "task_arrow_polygon": task_arrow_polygon
        }
        tools.write_json_file(result, os.path.join(output_dir, f'{os.path.basename(clip_dir)}.json'), indent=4)


if __name__ == '__main__':
    input_dir = '/Users/<USER>/Downloads/TSA/tsa-4dda-fan-wei-shi-biao_lot-cgnmo3dhjnj_order-cgmfth84q5f_ins-0_241030-1651'
    output_dir = '/Users/<USER>/Downloads/TSA/tsa-4dda-fan-wei-shi-biao_lot-cgnmo3dhjnj_order-cgmfth84q5f_ins-0_241030-1651_out'
    run(input_dir, output_dir)
