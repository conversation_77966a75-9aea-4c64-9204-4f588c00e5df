# github.com/gobuffalo/envy Stands on the Shoulders of Giants

github.com/gobuffalo/envy does not try to reinvent the wheel! Instead, it uses the already great wheels developed by the Go community and puts them all together in the best way possible. Without these giants, this project would not be possible. Please make sure to check them out and thank them for all of their hard work.

Thank you to the following **GIANTS**:


* [github.com/davecgh/go-spew](https://godoc.org/github.com/davecgh/go-spew)

* [github.com/joho/godotenv](https://godoc.org/github.com/joho/godotenv)

* [github.com/kr/pretty](https://godoc.org/github.com/kr/pretty)

* [github.com/kr/pty](https://godoc.org/github.com/kr/pty)

* [github.com/kr/text](https://godoc.org/github.com/kr/text)

* [github.com/pmezard/go-difflib](https://godoc.org/github.com/pmezard/go-difflib)

* [github.com/rogpeppe/go-internal](https://godoc.org/github.com/rogpeppe/go-internal)

* [github.com/stretchr/objx](https://godoc.org/github.com/stretchr/objx)

* [github.com/stretchr/testify](https://godoc.org/github.com/stretchr/testify)

* [gopkg.in/check.v1](https://godoc.org/gopkg.in/check.v1)

* [gopkg.in/errgo.v2](https://godoc.org/gopkg.in/errgo.v2)

* [gopkg.in/yaml.v2](https://godoc.org/gopkg.in/yaml.v2)
