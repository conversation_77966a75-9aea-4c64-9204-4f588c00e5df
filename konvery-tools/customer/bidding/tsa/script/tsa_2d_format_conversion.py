import os
import sys

sys.path.append('.')
from customer.common import tools


def get_rel_id(label, rel_type):
    if '0' in label['attrs'][rel_type]:
        return []
    return label['attrs'][rel_type]


def run(input_dir, output_dir):
    json_files = tools.get_json_files(input_dir)
    for json_file in json_files:
        json_data = tools.get_json_data(json_file)
        task_lane_divider = []
        task_lane_relation = []
        task_road_edge = []
        task_arrow_polygon = []
        for idx, label in enumerate(json_data['labels'], start=1):
            if label['class'] == 'Lane_Segment':
                obj = {
                    "LSID": str(idx),
                    "point": [],
                    "pre_id": get_rel_id(label, 'pre_id'),
                    "suc_id": get_rel_id(label, 'suc_id'),
                    "left_lane": get_rel_id(label, 'left_id'),
                    "right_lane": get_rel_id(label, 'right_id'),
                    "type": "polygon",
                    "point_uv": [[pt['x'], pt['y']] for pt in label['annotation']['data']]
                }
                task_lane_relation.append(obj)
            elif label['class'] == 'class':
                obj = {
                    "LLID": str(idx),
                    "point": [],
                    "class": label['attrs']['category'],
                    "color": label['attrs']['color'][0],
                    "pre_id": get_rel_id(label, 'pre_id'),
                    "suc_id": get_rel_id(label, 'suc_id'),
                    "type": "polyline",
                    "point_uv": [[pt['x'], pt['y']] for pt in label['annotation']['data']]
                }
                task_lane_divider.append(obj)
            elif label['class'] == 'road':
                obj = {
                    "point": [],
                    "class": label['attrs']['classification'],
                    "type": "polyline",
                    "point_uv": [[pt['x'], pt['y']] for pt in label['annotation']['data']]
                }
                if label['attrs']['circular'][0] == 'yes':
                    obj['point_uv'] = obj['point_uv'] + [obj['point_uv'][0]]
                task_road_edge.append(obj)
            elif label['class'] == 'stop_line':
                obj = {
                    "point": [],
                    "class": label['attrs']['polyline'],
                    "type": "polyline",
                    "point_uv": [[pt['x'], pt['y']] for pt in label['annotation']['data']]
                }
                task_arrow_polygon.append(obj)
            elif label['class'] == 'Groundmarking':
                obj = {
                    "point": [],
                    "class": label['attrs']['classification'][0],
                    "type": "polygon",
                    "point_uv": [[pt['x'], pt['y']] for pt in label['annotation']['data']]
                }
                task_arrow_polygon.append(obj)
            else:
                raise NotImplementedError(f'unsupported label class {label["class"]}')
        result = {
            "cam_folder": None,
            "data_folder": None,
            "file_name": None,
            "task_lane_divider": task_lane_divider,
            "task_lane_relation": task_lane_relation,
            "task_road_edge": task_road_edge,
            "task_arrow_polygon": task_arrow_polygon
        }
        tools.write_json_file(result, os.path.join(output_dir, json_file.name))


if __name__ == '__main__':
    input_dir = '/Users/<USER>/Downloads/TSA/ds_xabbbyeqsdyhhdmbetqc'
    output_dir = '/Users/<USER>/Downloads/TSA/ds_xabbbyeqsdyhhdmbetqc_out'
    run(input_dir, output_dir)
