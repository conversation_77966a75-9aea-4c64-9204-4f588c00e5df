// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: iam/v1/bizgrant.proto

package iam

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationBizgrantsCreateBizgrant = "/iam.v1.Bizgrants/CreateBizgrant"
const OperationBizgrantsDeleteBizgrant = "/iam.v1.Bizgrants/DeleteBizgrant"
const OperationBizgrantsListBizgrant = "/iam.v1.Bizgrants/ListBizgrant"

type BizgrantsHTTPServer interface {
	CreateBizgrant(context.Context, *CreateBizgrantRequest) (*CreateBizgrantReply, error)
	DeleteBizgrant(context.Context, *DeleteBizgrantRequest) (*emptypb.Empty, error)
	ListBizgrant(context.Context, *ListBizgrantRequest) (*ListBizgrantReply, error)
}

func RegisterBizgrantsHTTPServer(s *http.Server, srv BizgrantsHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/bizgrants", _Bizgrants_CreateBizgrant0_HTTP_Handler(srv))
	r.DELETE("/v1/bizgrants", _Bizgrants_DeleteBizgrant0_HTTP_Handler(srv))
	r.GET("/v1/bizgrants", _Bizgrants_ListBizgrant0_HTTP_Handler(srv))
}

func _Bizgrants_CreateBizgrant0_HTTP_Handler(srv BizgrantsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateBizgrantRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBizgrantsCreateBizgrant)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateBizgrant(ctx, req.(*CreateBizgrantRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateBizgrantReply)
		return ctx.Result(200, reply)
	}
}

func _Bizgrants_DeleteBizgrant0_HTTP_Handler(srv BizgrantsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteBizgrantRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBizgrantsDeleteBizgrant)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteBizgrant(ctx, req.(*DeleteBizgrantRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Bizgrants_ListBizgrant0_HTTP_Handler(srv BizgrantsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListBizgrantRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBizgrantsListBizgrant)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListBizgrant(ctx, req.(*ListBizgrantRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListBizgrantReply)
		return ctx.Result(200, reply)
	}
}

type BizgrantsHTTPClient interface {
	CreateBizgrant(ctx context.Context, req *CreateBizgrantRequest, opts ...http.CallOption) (rsp *CreateBizgrantReply, err error)
	DeleteBizgrant(ctx context.Context, req *DeleteBizgrantRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	ListBizgrant(ctx context.Context, req *ListBizgrantRequest, opts ...http.CallOption) (rsp *ListBizgrantReply, err error)
}

type BizgrantsHTTPClientImpl struct {
	cc *http.Client
}

func NewBizgrantsHTTPClient(client *http.Client) BizgrantsHTTPClient {
	return &BizgrantsHTTPClientImpl{client}
}

func (c *BizgrantsHTTPClientImpl) CreateBizgrant(ctx context.Context, in *CreateBizgrantRequest, opts ...http.CallOption) (*CreateBizgrantReply, error) {
	var out CreateBizgrantReply
	pattern := "/v1/bizgrants"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBizgrantsCreateBizgrant))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BizgrantsHTTPClientImpl) DeleteBizgrant(ctx context.Context, in *DeleteBizgrantRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/bizgrants"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBizgrantsDeleteBizgrant))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BizgrantsHTTPClientImpl) ListBizgrant(ctx context.Context, in *ListBizgrantRequest, opts ...http.CallOption) (*ListBizgrantReply, error) {
	var out ListBizgrantReply
	pattern := "/v1/bizgrants"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBizgrantsListBizgrant))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
