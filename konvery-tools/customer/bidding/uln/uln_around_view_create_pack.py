import os
import json
import shutil
import numpy as np
import configparser
from customer.common import tools


camera_list = ['cam-B', 'cam-F', 'cam-L', 'cam-R']
camera_name_mapping = {
    'cam-B': '4back',
    'cam-F': '1front',
    'cam-L': '3left',
    'cam-R': '2right'
}
extrinsic_name_mapping = {
    'FRONT': 'cam-F',
    'REAR': 'cam-B',
    'LEFT': 'cam-L',
    'RIGHT': 'cam-R'
}


def get_extrinisc_mapping(file):
    extrinsic_mapping = {}
    with open(file) as f:
        lines = f.readlines()
    for idx in range(len(lines)):
        line = lines[idx]
        if line.strip().endswith('tcl:'):
            camera_type = extrinsic_name_mapping[line.split()[0]]
            arr = [i.split() for i in lines[idx+1: idx+5]]
            extrinsic_mapping[camera_type] = np.array(arr, dtype=np.float32)
    return extrinsic_mapping


def get_distortion_mapping(file):
    distortion_mapping = {}
    ini_config = configparser.ConfigParser()
    ini_config.read(file)
    for cam_name in extrinsic_name_mapping.keys():
        distortion = {}
        distortion['k1'] = float(ini_config[cam_name]['K1'])
        distortion['k3'] = float(ini_config[cam_name]['K3'])
        distortion['k5'] = float(ini_config[cam_name]['K5'])
        distortion['k7'] = float(ini_config[cam_name]['K7'])
        distortion['k9'] = float(ini_config[cam_name]['K9'])
        distortion_mapping[cam_name] = distortion
    return distortion_mapping


def construct_config_testin(params_dir, out_collection_dir):
    config = {
        "camera": {},
        "data_type": "fusion_pointcloud",
        "sensor_params": {}
    }
    for camera_name in camera_name_mapping:
        config['camera'][camera_name] = camera_name_mapping[camera_name][1:]
    files = tools.listdir(params_dir, full_path=True)
    extrinsic_md_file = [i for i in files if i.endswith('.md')][0]
    extrinsic_mapping = get_extrinisc_mapping(extrinsic_md_file)
    distortion_file = [i for i in files if os.path.basename(i).startswith('svperception')][0]
    distortion_mapping = get_distortion_mapping(distortion_file)
    camera_cfg_file = [i for i in files if i.endswith('cameraCfg.ini')][0]
    # 创建一个configparser对象
    ini_config = configparser.ConfigParser()
    # 读取INI文件
    ini_config.read(camera_cfg_file)
    for camera in camera_list:
        params = ini_config[camera_name_mapping[camera]]
        config['sensor_params'][camera] = {
            'camera_model': 'fisheye',
            'extrinsic': extrinsic_mapping[camera].tolist(),
            'fx': float(params['k1']),
            'fy': float(params['asp']) * float(params['k1']),
            'cx': float(params['cx']),
            'cy': float(params['cy']),
            'k1': distortion_mapping.get('k1', 0),
            'k2': distortion_mapping.get('k2', 0),
            'k3': distortion_mapping.get('k3', 0),
            'k4': distortion_mapping.get('k4', 0),
            'k5': distortion_mapping.get('k5', 0),
            'k6': distortion_mapping.get('k6', 0),
            'k7': distortion_mapping.get('k7', 0),
            'k8': distortion_mapping.get('k8', 0),
            'k9': distortion_mapping.get('k9', 0),
        }

    with open(os.path.join(out_collection_dir, 'config.json'), 'w') as f:
        json.dump(config, f, indent=4)


def run(input_dir, output_dir):
    tools.check_dir(output_dir)
    sub_dirs = tools.listdir(input_dir, full_path=True)
    params_dir = [i for i in sub_dirs if '参数' in i][0]
    sub_dirs.remove(params_dir)

    for collection_dir in sub_dirs:
        if not os.path.isdir(collection_dir):
            continue
        out_collection_dir = os.path.join(output_dir, os.path.basename(collection_dir))
        os.mkdir(out_collection_dir)

        construct_config_testin(params_dir, out_collection_dir)

        out_lidar_dir = os.path.join(out_collection_dir, 'lidar')
        os.mkdir(out_lidar_dir)
        out_cameras_dir = os.path.join(out_collection_dir, 'camera')
        os.mkdir(out_cameras_dir)
        for camera in camera_list:
            os.mkdir(os.path.join(out_cameras_dir, camera))
        for ele_dir in tools.listdir(collection_dir, full_path=True):
            ele_name = os.path.basename(ele_dir)
            for file in tools.listdir(ele_dir, full_path=True):
                if file.endswith('.pcd'):
                    shutil.copyfile(file, os.path.join(out_lidar_dir, f'{ele_name}.pcd'))
                else:
                    camera_type = os.path.basename(file).rsplit('_', 2)[1]
                    shutil.copyfile(file, os.path.join(out_cameras_dir, camera_type, f'{ele_name}.jpg'))


"""
项目：上汽-中海庭-环视 分包
"""
if __name__ == '__main__':
    args = tools.get_args()
    if args.input == '../../input/test':
        args.input = '/Users/<USER>/Downloads/uln/环视/环视点云分割+23D联合目标检测'
        args.out = '/Users/<USER>/Downloads/uln/环视/环视点云分割+23D联合目标检测_out'
    run(args.input, args.out)
