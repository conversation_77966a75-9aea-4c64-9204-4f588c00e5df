# Keto 权限系统策略（Policy）分析

## 1. 策略的基本概念

策略（Policy）是 Keto 权限系统中的一个重要概念，它提供了一种更灵活的权限管理方式。策略可以同时绑定多个角色和用户/用户组，用于实现更复杂的权限控制场景。

## 2. 策略的定义和结构

### 2.1 基本格式
```plaintext
# 策略定义示例
IamPolicy:sys/admin#roles@IamRole:admin
IamPolicy:sys/admin#users@IamGroup:sys/admin#members
```

### 2.2 策略组成部分
- 策略名称（如：sys/admin）
- 角色绑定（roles）
- 用户/用户组绑定（users）

## 3. 策略的作用

1. **批量管理**：可以一次性管理多个用户或角色的权限
2. **临时授权**：可以创建临时策略，设置过期时间
3. **灵活组合**：可以组合多个角色和用户组
4. **动态调整**：可以随时调整策略内容
5. **权限隔离**：不同策略之间相互独立

## 4. 策略与 RBAC 的区别

### 4.1 传统 RBAC
```
User -> Role -> Permission
```

### 4.2 Keto 策略系统
```
User -> Group -> Policy -> Role -> Permission
```

## 5. 策略使用场景

### 5.1 系统管理员策略
```plaintext
IamPolicy:sys/admin#roles@IamRole:admin
IamPolicy:sys/admin#users@IamGroup:sys/admin#members
```

### 5.2 项目管理员策略
```plaintext
IamPolicy:project1/admin#roles@IamRole:project.admin
IamPolicy:project1/admin#users@IamGroup:project1/admins#members
```

### 5.3 临时访问策略
```plaintext
IamPolicy:temp/access#roles@IamRole:viewer
IamPolicy:temp/access#users@IamUser:user123
```

## 6. 策略的实现

### 6.1 策略更新
```typescript
async function updatePolicy(policyId: string, roles: string[], users: string[]) {
  // 1. 更新角色绑定
  for (const role of roles) {
    await ketow.relationTuple.create({
      namespace: "IamPolicy",
      object: policyId,
      relation: "roles",
      subject: `IamRole:${role}`
    });
  }
  
  // 2. 更新用户绑定
  for (const user of users) {
    await ketow.relationTuple.create({
      namespace: "IamPolicy",
      object: policyId,
      relation: "users",
      subject: `IamUser:${user}`
    });
  }
}
```

### 6.2 策略创建
```typescript
async function createProjectPolicy(projectId: string) {
  // 1. 创建项目管理员策略
  await createPolicy(`${projectId}/admin`, {
    roles: ['project.admin'],
    users: [`IamGroup:${projectId}/admins#members`]
  });
  
  // 2. 创建项目成员策略
  await createPolicy(`${projectId}/member`, {
    roles: ['project.member'],
    users: [`IamGroup:${projectId}/members#members`]
  });
  
  // 3. 创建项目访客策略
  await createPolicy(`${projectId}/guest`, {
    roles: ['project.guest'],
    users: [`IamGroup:${projectId}/guests#members`]
  });
}
```

## 7. 策略权限检查

### 7.1 权限检查流程
```typescript
async function checkPolicyPermission(userId: string, policyId: string, permission: string) {
  // 1. 检查用户是否在策略中
  const isInPolicy = await checkUserInPolicy(userId, policyId);
  if (!isInPolicy) return false;
  
  // 2. 获取策略绑定的角色
  const roles = await getPolicyRoles(policyId);
  
  // 3. 检查角色是否有权限
  for (const role of roles) {
    if (await checkRolePermission(role, permission)) {
      return true;
    }
  }
  
  return false;
}
```

## 8. 策略的优势

1. **灵活性**：可以动态调整策略内容
2. **可扩展性**：支持添加新的角色和用户
3. **可维护性**：策略可以独立管理
4. **可重用性**：策略可以在不同场景下重用
5. **可审计性**：策略变更可以记录和追踪

## 9. 使用建议

1. 使用有意义的策略命名
2. 合理组织策略结构
3. 定期审查策略内容
4. 记录策略变更历史
5. 设置策略过期时间

## 10. 总结

策略（Policy）是 Keto 权限系统中的一个重要概念，它提供了一种更灵活的权限管理方式。通过策略，可以实现：
- 批量权限管理
- 临时权限授权
- 动态权限调整
- 复杂权限组合
- 权限隔离

这种设计使得权限系统不仅具有 RBAC 的基本功能，还提供了更灵活的权限管理方式，特别适合需要动态调整权限、临时授权或批量管理的场景。 