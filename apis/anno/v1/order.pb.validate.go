// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/order.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Source with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Source) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Source with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SourceMultiError, or nil if none found.
func (m *Source) ValidateAll() error {
	return m.validate(true)
}

func (m *Source) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetProprietary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SourceValidationError{
					field:  "Proprietary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SourceValidationError{
					field:  "Proprietary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProprietary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SourceValidationError{
				field:  "Proprietary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Style

	// no validation rules for ElemType

	// no validation rules for IsFrameSeries

	// no validation rules for PlainSizeGb

	for idx, item := range m.GetErrorHandlers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SourceValidationError{
						field:  fmt.Sprintf("ErrorHandlers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SourceValidationError{
						field:  fmt.Sprintf("ErrorHandlers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SourceValidationError{
					field:  fmt.Sprintf("ErrorHandlers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AutoParse

	// no validation rules for NamedUris

	if all {
		switch v := interface{}(m.GetConverter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SourceValidationError{
					field:  "Converter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SourceValidationError{
					field:  "Converter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConverter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SourceValidationError{
				field:  "Converter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Metadata

	if len(errors) > 0 {
		return SourceMultiError(errors)
	}

	return nil
}

// SourceMultiError is an error wrapping multiple validation errors returned by
// Source.ValidateAll() if the designated constraints aren't met.
type SourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SourceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SourceMultiError) AllErrors() []error { return m }

// SourceValidationError is the validation error returned by Source.Validate if
// the designated constraints aren't met.
type SourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SourceValidationError) ErrorName() string { return "SourceValidationError" }

// Error satisfies the builtin error interface
func (e SourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SourceValidationError{}

// Validate checks the field values on CreateOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateOrderRequestMultiError, or nil if none found.
func (m *CreateOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if !_CreateOrderRequest_Name_Pattern.MatchString(m.GetName()) {
		err := CreateOrderRequestValidationError{
			field:  "Name",
			reason: "value does not match regex pattern \"^[\\\\p{Han}\\\\w\\\\d_-]{0,256}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for OrgUid

	if all {
		switch v := interface{}(m.GetSource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOrderRequestValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOrderRequestValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOrderRequestValidationError{
				field:  "Source",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateOrderRequestMultiError(errors)
	}

	return nil
}

// CreateOrderRequestMultiError is an error wrapping multiple validation errors
// returned by CreateOrderRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOrderRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOrderRequestMultiError) AllErrors() []error { return m }

// CreateOrderRequestValidationError is the validation error returned by
// CreateOrderRequest.Validate if the designated constraints aren't met.
type CreateOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOrderRequestValidationError) ErrorName() string {
	return "CreateOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOrderRequestValidationError{}

var _CreateOrderRequest_Name_Pattern = regexp.MustCompile("^[\\p{Han}\\w\\d_-]{0,256}$")

// Validate checks the field values on UpdateOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateOrderRequestMultiError, or nil if none found.
func (m *UpdateOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetOrder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateOrderRequestValidationError{
					field:  "Order",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateOrderRequestValidationError{
					field:  "Order",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateOrderRequestValidationError{
				field:  "Order",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateOrderRequestMultiError(errors)
	}

	return nil
}

// UpdateOrderRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateOrderRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateOrderRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateOrderRequestMultiError) AllErrors() []error { return m }

// UpdateOrderRequestValidationError is the validation error returned by
// UpdateOrderRequest.Validate if the designated constraints aren't met.
type UpdateOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateOrderRequestValidationError) ErrorName() string {
	return "UpdateOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateOrderRequestValidationError{}

// Validate checks the field values on DeleteOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteOrderRequestMultiError, or nil if none found.
func (m *DeleteOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return DeleteOrderRequestMultiError(errors)
	}

	return nil
}

// DeleteOrderRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteOrderRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteOrderRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteOrderRequestMultiError) AllErrors() []error { return m }

// DeleteOrderRequestValidationError is the validation error returned by
// DeleteOrderRequest.Validate if the designated constraints aren't met.
type DeleteOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteOrderRequestValidationError) ErrorName() string {
	return "DeleteOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteOrderRequestValidationError{}

// Validate checks the field values on GetOrderRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrderRequestMultiError, or nil if none found.
func (m *GetOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return GetOrderRequestMultiError(errors)
	}

	return nil
}

// GetOrderRequestMultiError is an error wrapping multiple validation errors
// returned by GetOrderRequest.ValidateAll() if the designated constraints
// aren't met.
type GetOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderRequestMultiError) AllErrors() []error { return m }

// GetOrderRequestValidationError is the validation error returned by
// GetOrderRequest.Validate if the designated constraints aren't met.
type GetOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderRequestValidationError) ErrorName() string { return "GetOrderRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderRequestValidationError{}

// Validate checks the field values on ListOrderRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListOrderRequestMultiError, or nil if none found.
func (m *ListOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListOrderRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for OrgUid

	// no validation rules for CreatorUid

	// no validation rules for NamePattern

	for idx, item := range m.GetStates() {
		_, _ = idx, item

		if _, ok := _ListOrderRequest_States_NotInLookup[item]; ok {
			err := ListOrderRequestValidationError{
				field:  fmt.Sprintf("States[%v]", idx),
				reason: "value must not be in list [0]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if _, ok := Order_State_Enum_name[int32(item)]; !ok {
			err := ListOrderRequestValidationError{
				field:  fmt.Sprintf("States[%v]", idx),
				reason: "value must be one of the defined enum values",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for WithOrg

	if len(errors) > 0 {
		return ListOrderRequestMultiError(errors)
	}

	return nil
}

// ListOrderRequestMultiError is an error wrapping multiple validation errors
// returned by ListOrderRequest.ValidateAll() if the designated constraints
// aren't met.
type ListOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListOrderRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListOrderRequestMultiError) AllErrors() []error { return m }

// ListOrderRequestValidationError is the validation error returned by
// ListOrderRequest.Validate if the designated constraints aren't met.
type ListOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListOrderRequestValidationError) ErrorName() string { return "ListOrderRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListOrderRequestValidationError{}

var _ListOrderRequest_States_NotInLookup = map[Order_State_Enum]struct{}{
	0: {},
}

// Validate checks the field values on ListOrderReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListOrderReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListOrderReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListOrderReplyMultiError,
// or nil if none found.
func (m *ListOrderReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListOrderReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetOrders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListOrderReplyValidationError{
						field:  fmt.Sprintf("Orders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListOrderReplyValidationError{
						field:  fmt.Sprintf("Orders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListOrderReplyValidationError{
					field:  fmt.Sprintf("Orders[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetOrgs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListOrderReplyValidationError{
						field:  fmt.Sprintf("Orgs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListOrderReplyValidationError{
						field:  fmt.Sprintf("Orgs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListOrderReplyValidationError{
					field:  fmt.Sprintf("Orgs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListOrderReplyMultiError(errors)
	}

	return nil
}

// ListOrderReplyMultiError is an error wrapping multiple validation errors
// returned by ListOrderReply.ValidateAll() if the designated constraints
// aren't met.
type ListOrderReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListOrderReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListOrderReplyMultiError) AllErrors() []error { return m }

// ListOrderReplyValidationError is the validation error returned by
// ListOrderReply.Validate if the designated constraints aren't met.
type ListOrderReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListOrderReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListOrderReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListOrderReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListOrderReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListOrderReplyValidationError) ErrorName() string { return "ListOrderReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListOrderReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListOrderReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListOrderReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListOrderReplyValidationError{}

// Validate checks the field values on Order with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Order) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Order with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in OrderMultiError, or nil if none found.
func (m *Order) ValidateAll() error {
	return m.validate(true)
}

func (m *Order) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for OrgUid

	if all {
		switch v := interface{}(m.GetSource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderValidationError{
				field:  "Source",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DataUid

	// no validation rules for CreatorUid

	// no validation rules for Size

	if _, ok := Order_State_Enum_name[int32(m.GetState())]; !ok {
		err := OrderValidationError{
			field:  "State",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for InsTotal

	// no validation rules for AnnoResultUrl

	// no validation rules for Error

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDataSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "DataSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "DataSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDataSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderValidationError{
				field:  "DataSummary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CanExportAnnos

	if len(errors) > 0 {
		return OrderMultiError(errors)
	}

	return nil
}

// OrderMultiError is an error wrapping multiple validation errors returned by
// Order.ValidateAll() if the designated constraints aren't met.
type OrderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrderMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrderMultiError) AllErrors() []error { return m }

// OrderValidationError is the validation error returned by Order.Validate if
// the designated constraints aren't met.
type OrderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrderValidationError) ErrorName() string { return "OrderValidationError" }

// Error satisfies the builtin error interface
func (e OrderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrder.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrderValidationError{}

// Validate checks the field values on SetOrderAnnoResultRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetOrderAnnoResultRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetOrderAnnoResultRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetOrderAnnoResultRequestMultiError, or nil if none found.
func (m *SetOrderAnnoResultRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetOrderAnnoResultRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if _, err := url.Parse(m.GetUrl()); err != nil {
		err = SetOrderAnnoResultRequestValidationError{
			field:  "Url",
			reason: "value must be a valid URI",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SetOrderAnnoResultRequestMultiError(errors)
	}

	return nil
}

// SetOrderAnnoResultRequestMultiError is an error wrapping multiple validation
// errors returned by SetOrderAnnoResultRequest.ValidateAll() if the
// designated constraints aren't met.
type SetOrderAnnoResultRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetOrderAnnoResultRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetOrderAnnoResultRequestMultiError) AllErrors() []error { return m }

// SetOrderAnnoResultRequestValidationError is the validation error returned by
// SetOrderAnnoResultRequest.Validate if the designated constraints aren't met.
type SetOrderAnnoResultRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetOrderAnnoResultRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetOrderAnnoResultRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetOrderAnnoResultRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetOrderAnnoResultRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetOrderAnnoResultRequestValidationError) ErrorName() string {
	return "SetOrderAnnoResultRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SetOrderAnnoResultRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetOrderAnnoResultRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetOrderAnnoResultRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetOrderAnnoResultRequestValidationError{}

// Validate checks the field values on GetOrderAnnoResultReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrderAnnoResultReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderAnnoResultReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrderAnnoResultReplyMultiError, or nil if none found.
func (m *GetOrderAnnoResultReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderAnnoResultReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetWillReadyAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderAnnoResultReplyValidationError{
					field:  "WillReadyAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderAnnoResultReplyValidationError{
					field:  "WillReadyAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWillReadyAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderAnnoResultReplyValidationError{
				field:  "WillReadyAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Url

	if len(errors) > 0 {
		return GetOrderAnnoResultReplyMultiError(errors)
	}

	return nil
}

// GetOrderAnnoResultReplyMultiError is an error wrapping multiple validation
// errors returned by GetOrderAnnoResultReply.ValidateAll() if the designated
// constraints aren't met.
type GetOrderAnnoResultReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderAnnoResultReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderAnnoResultReplyMultiError) AllErrors() []error { return m }

// GetOrderAnnoResultReplyValidationError is the validation error returned by
// GetOrderAnnoResultReply.Validate if the designated constraints aren't met.
type GetOrderAnnoResultReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderAnnoResultReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderAnnoResultReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderAnnoResultReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderAnnoResultReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderAnnoResultReplyValidationError) ErrorName() string {
	return "GetOrderAnnoResultReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderAnnoResultReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderAnnoResultReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderAnnoResultReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderAnnoResultReplyValidationError{}

// Validate checks the field values on DataValidationSummary with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DataValidationSummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataValidationSummary with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DataValidationSummaryMultiError, or nil if none found.
func (m *DataValidationSummary) ValidateAll() error {
	return m.validate(true)
}

func (m *DataValidationSummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalErrors

	for idx, item := range m.GetErrors() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DataValidationSummaryValidationError{
						field:  fmt.Sprintf("Errors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DataValidationSummaryValidationError{
						field:  fmt.Sprintf("Errors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DataValidationSummaryValidationError{
					field:  fmt.Sprintf("Errors[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DataValidationSummaryMultiError(errors)
	}

	return nil
}

// DataValidationSummaryMultiError is an error wrapping multiple validation
// errors returned by DataValidationSummary.ValidateAll() if the designated
// constraints aren't met.
type DataValidationSummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataValidationSummaryMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataValidationSummaryMultiError) AllErrors() []error { return m }

// DataValidationSummaryValidationError is the validation error returned by
// DataValidationSummary.Validate if the designated constraints aren't met.
type DataValidationSummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataValidationSummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataValidationSummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataValidationSummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataValidationSummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataValidationSummaryValidationError) ErrorName() string {
	return "DataValidationSummaryValidationError"
}

// Error satisfies the builtin error interface
func (e DataValidationSummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataValidationSummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataValidationSummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataValidationSummaryValidationError{}

// Validate checks the field values on ExportOrderAnnosRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExportOrderAnnosRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExportOrderAnnosRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExportOrderAnnosRequestMultiError, or nil if none found.
func (m *ExportOrderAnnosRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ExportOrderAnnosRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Option

	if len(errors) > 0 {
		return ExportOrderAnnosRequestMultiError(errors)
	}

	return nil
}

// ExportOrderAnnosRequestMultiError is an error wrapping multiple validation
// errors returned by ExportOrderAnnosRequest.ValidateAll() if the designated
// constraints aren't met.
type ExportOrderAnnosRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExportOrderAnnosRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExportOrderAnnosRequestMultiError) AllErrors() []error { return m }

// ExportOrderAnnosRequestValidationError is the validation error returned by
// ExportOrderAnnosRequest.Validate if the designated constraints aren't met.
type ExportOrderAnnosRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExportOrderAnnosRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExportOrderAnnosRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExportOrderAnnosRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExportOrderAnnosRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExportOrderAnnosRequestValidationError) ErrorName() string {
	return "ExportOrderAnnosRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ExportOrderAnnosRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExportOrderAnnosRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExportOrderAnnosRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExportOrderAnnosRequestValidationError{}

// Validate checks the field values on Source_Proprietary with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Source_Proprietary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Source_Proprietary with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Source_ProprietaryMultiError, or nil if none found.
func (m *Source_Proprietary) ValidateAll() error {
	return m.validate(true)
}

func (m *Source_Proprietary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Config

	if len(errors) > 0 {
		return Source_ProprietaryMultiError(errors)
	}

	return nil
}

// Source_ProprietaryMultiError is an error wrapping multiple validation errors
// returned by Source_Proprietary.ValidateAll() if the designated constraints
// aren't met.
type Source_ProprietaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Source_ProprietaryMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Source_ProprietaryMultiError) AllErrors() []error { return m }

// Source_ProprietaryValidationError is the validation error returned by
// Source_Proprietary.Validate if the designated constraints aren't met.
type Source_ProprietaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Source_ProprietaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Source_ProprietaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Source_ProprietaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Source_ProprietaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Source_ProprietaryValidationError) ErrorName() string {
	return "Source_ProprietaryValidationError"
}

// Error satisfies the builtin error interface
func (e Source_ProprietaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSource_Proprietary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Source_ProprietaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Source_ProprietaryValidationError{}

// Validate checks the field values on Source_ParseErrorHandler with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Source_ParseErrorHandler) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Source_ParseErrorHandler with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Source_ParseErrorHandlerMultiError, or nil if none found.
func (m *Source_ParseErrorHandler) ValidateAll() error {
	return m.validate(true)
}

func (m *Source_ParseErrorHandler) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RawdataType

	// no validation rules for Error

	// no validation rules for Handler

	if len(errors) > 0 {
		return Source_ParseErrorHandlerMultiError(errors)
	}

	return nil
}

// Source_ParseErrorHandlerMultiError is an error wrapping multiple validation
// errors returned by Source_ParseErrorHandler.ValidateAll() if the designated
// constraints aren't met.
type Source_ParseErrorHandlerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Source_ParseErrorHandlerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Source_ParseErrorHandlerMultiError) AllErrors() []error { return m }

// Source_ParseErrorHandlerValidationError is the validation error returned by
// Source_ParseErrorHandler.Validate if the designated constraints aren't met.
type Source_ParseErrorHandlerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Source_ParseErrorHandlerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Source_ParseErrorHandlerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Source_ParseErrorHandlerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Source_ParseErrorHandlerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Source_ParseErrorHandlerValidationError) ErrorName() string {
	return "Source_ParseErrorHandlerValidationError"
}

// Error satisfies the builtin error interface
func (e Source_ParseErrorHandlerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSource_ParseErrorHandler.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Source_ParseErrorHandlerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Source_ParseErrorHandlerValidationError{}

// Validate checks the field values on Source_ParseErrorHandler_Error with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Source_ParseErrorHandler_Error) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Source_ParseErrorHandler_Error with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// Source_ParseErrorHandler_ErrorMultiError, or nil if none found.
func (m *Source_ParseErrorHandler_Error) ValidateAll() error {
	return m.validate(true)
}

func (m *Source_ParseErrorHandler_Error) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Source_ParseErrorHandler_ErrorMultiError(errors)
	}

	return nil
}

// Source_ParseErrorHandler_ErrorMultiError is an error wrapping multiple
// validation errors returned by Source_ParseErrorHandler_Error.ValidateAll()
// if the designated constraints aren't met.
type Source_ParseErrorHandler_ErrorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Source_ParseErrorHandler_ErrorMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Source_ParseErrorHandler_ErrorMultiError) AllErrors() []error { return m }

// Source_ParseErrorHandler_ErrorValidationError is the validation error
// returned by Source_ParseErrorHandler_Error.Validate if the designated
// constraints aren't met.
type Source_ParseErrorHandler_ErrorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Source_ParseErrorHandler_ErrorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Source_ParseErrorHandler_ErrorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Source_ParseErrorHandler_ErrorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Source_ParseErrorHandler_ErrorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Source_ParseErrorHandler_ErrorValidationError) ErrorName() string {
	return "Source_ParseErrorHandler_ErrorValidationError"
}

// Error satisfies the builtin error interface
func (e Source_ParseErrorHandler_ErrorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSource_ParseErrorHandler_Error.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Source_ParseErrorHandler_ErrorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Source_ParseErrorHandler_ErrorValidationError{}

// Validate checks the field values on Source_ParseErrorHandler_Handler with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *Source_ParseErrorHandler_Handler) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Source_ParseErrorHandler_Handler with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// Source_ParseErrorHandler_HandlerMultiError, or nil if none found.
func (m *Source_ParseErrorHandler_Handler) ValidateAll() error {
	return m.validate(true)
}

func (m *Source_ParseErrorHandler_Handler) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Source_ParseErrorHandler_HandlerMultiError(errors)
	}

	return nil
}

// Source_ParseErrorHandler_HandlerMultiError is an error wrapping multiple
// validation errors returned by
// Source_ParseErrorHandler_Handler.ValidateAll() if the designated
// constraints aren't met.
type Source_ParseErrorHandler_HandlerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Source_ParseErrorHandler_HandlerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Source_ParseErrorHandler_HandlerMultiError) AllErrors() []error { return m }

// Source_ParseErrorHandler_HandlerValidationError is the validation error
// returned by Source_ParseErrorHandler_Handler.Validate if the designated
// constraints aren't met.
type Source_ParseErrorHandler_HandlerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Source_ParseErrorHandler_HandlerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Source_ParseErrorHandler_HandlerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Source_ParseErrorHandler_HandlerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Source_ParseErrorHandler_HandlerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Source_ParseErrorHandler_HandlerValidationError) ErrorName() string {
	return "Source_ParseErrorHandler_HandlerValidationError"
}

// Error satisfies the builtin error interface
func (e Source_ParseErrorHandler_HandlerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSource_ParseErrorHandler_Handler.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Source_ParseErrorHandler_HandlerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Source_ParseErrorHandler_HandlerValidationError{}

// Validate checks the field values on Order_State with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Order_State) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Order_State with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Order_StateMultiError, or
// nil if none found.
func (m *Order_State) ValidateAll() error {
	return m.validate(true)
}

func (m *Order_State) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Order_StateMultiError(errors)
	}

	return nil
}

// Order_StateMultiError is an error wrapping multiple validation errors
// returned by Order_State.ValidateAll() if the designated constraints aren't met.
type Order_StateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Order_StateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Order_StateMultiError) AllErrors() []error { return m }

// Order_StateValidationError is the validation error returned by
// Order_State.Validate if the designated constraints aren't met.
type Order_StateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Order_StateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Order_StateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Order_StateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Order_StateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Order_StateValidationError) ErrorName() string { return "Order_StateValidationError" }

// Error satisfies the builtin error interface
func (e Order_StateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrder_State.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Order_StateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Order_StateValidationError{}

// Validate checks the field values on DataValidationSummary_Error with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DataValidationSummary_Error) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataValidationSummary_Error with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DataValidationSummary_ErrorMultiError, or nil if none found.
func (m *DataValidationSummary_Error) ValidateAll() error {
	return m.validate(true)
}

func (m *DataValidationSummary_Error) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Error

	// no validation rules for ElemIndex

	// no validation rules for RawdataName

	if len(errors) > 0 {
		return DataValidationSummary_ErrorMultiError(errors)
	}

	return nil
}

// DataValidationSummary_ErrorMultiError is an error wrapping multiple
// validation errors returned by DataValidationSummary_Error.ValidateAll() if
// the designated constraints aren't met.
type DataValidationSummary_ErrorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataValidationSummary_ErrorMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataValidationSummary_ErrorMultiError) AllErrors() []error { return m }

// DataValidationSummary_ErrorValidationError is the validation error returned
// by DataValidationSummary_Error.Validate if the designated constraints
// aren't met.
type DataValidationSummary_ErrorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataValidationSummary_ErrorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataValidationSummary_ErrorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataValidationSummary_ErrorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataValidationSummary_ErrorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataValidationSummary_ErrorValidationError) ErrorName() string {
	return "DataValidationSummary_ErrorValidationError"
}

// Error satisfies the builtin error interface
func (e DataValidationSummary_ErrorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataValidationSummary_Error.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataValidationSummary_ErrorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataValidationSummary_ErrorValidationError{}

// Validate checks the field values on ExportOrderAnnosRequest_Option with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExportOrderAnnosRequest_Option) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExportOrderAnnosRequest_Option with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ExportOrderAnnosRequest_OptionMultiError, or nil if none found.
func (m *ExportOrderAnnosRequest_Option) ValidateAll() error {
	return m.validate(true)
}

func (m *ExportOrderAnnosRequest_Option) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ExportOrderAnnosRequest_OptionMultiError(errors)
	}

	return nil
}

// ExportOrderAnnosRequest_OptionMultiError is an error wrapping multiple
// validation errors returned by ExportOrderAnnosRequest_Option.ValidateAll()
// if the designated constraints aren't met.
type ExportOrderAnnosRequest_OptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExportOrderAnnosRequest_OptionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExportOrderAnnosRequest_OptionMultiError) AllErrors() []error { return m }

// ExportOrderAnnosRequest_OptionValidationError is the validation error
// returned by ExportOrderAnnosRequest_Option.Validate if the designated
// constraints aren't met.
type ExportOrderAnnosRequest_OptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExportOrderAnnosRequest_OptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExportOrderAnnosRequest_OptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExportOrderAnnosRequest_OptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExportOrderAnnosRequest_OptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExportOrderAnnosRequest_OptionValidationError) ErrorName() string {
	return "ExportOrderAnnosRequest_OptionValidationError"
}

// Error satisfies the builtin error interface
func (e ExportOrderAnnosRequest_OptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExportOrderAnnosRequest_Option.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExportOrderAnnosRequest_OptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExportOrderAnnosRequest_OptionValidationError{}
