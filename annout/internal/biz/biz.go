package biz

import (
	"context"

	"annout/internal/data/serial"
	"gitlab.rp.konvery.work/platform/pkg/data/field"

	"github.com/go-kratos/kratos/v2/encoding"
	"github.com/go-kratos/kratos/v2/encoding/json"
	"github.com/google/wire"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// ProviderSet is biz providers.
var ProviderSet = wire.NewSet() // NewReapersBiz)

type JSON = datatypes.JSON
type DeletedAt = gorm.DeletedAt
type Tx = *gorm.DB
type FieldMask = field.Mask
type TxAction func(ctx context.Context, v any) error

func JSONCodec() encoding.Codec { return encoding.GetCodec("json") }

type StrMap = serial.Map[string, string]

func init() {
	json.MarshalOptions.EmitUnpopulated = true
	json.MarshalOptions.UseProtoNames = true
}

type Pager struct {
	Pagesz int
	Page   int
}

func (o *Pager) Offset() int { return o.Pagesz * o.Page }

type TokenPager struct {
	Pagesz    int
	PageToken string
}

type ValidValue[T any] struct {
	Value T
	Valid bool
}

func NewValidValue[T any](v T) ValidValue[T] {
	return ValidValue[T]{
		Value: v,
		Valid: true,
	}
}

func (o *ValidValue[T]) Set(v T) {
	o.Value = v
	o.Valid = true
}
