syntax = "proto3";

package iam.v1;

import "google/api/annotations.proto";
//import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "openapi/v3/annotations.proto";
import "iam/v1/type.proto";
import "validate/validate.proto";
import "types/tag.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/iam/v1;iam";
option java_multiple_files = true;
option java_package = "iam.v1";

service Users {
  rpc CreateUser (CreateUserRequest) returns (User) {
    option (google.api.http) = {
      post: "/v1/users"
      body: "*"
    };
  }

  rpc BatchCreateUsers (BatchCreateUsersRequest) returns (ListUserReply) {
    option (google.api.http) = {
      post: "/v1/users/batch"
      body: "*"
    };
  }

  rpc UpdateUser (UpdateUserRequest) returns (User) {
    option (google.api.http) = {
      patch: "/v1/users/{user.uid}"
      body: "user"
    };
  }

  rpc DeleteUser (DeleteUserRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/users/{uid}"
    };
  }

  // retrieve my info
  rpc GetMe (google.protobuf.Empty) returns (User) {
    option (google.api.http) = {
      get: "/v1/users/me"
    };
  }

  // retrieve my info; also returns the real user in an assume context
  rpc GetMe2 (google.protobuf.Empty) returns (UserContext) {
    option (google.api.http) = {
      get: "/v1/users/me2"
    };
  }

  // get my front-end permissions
  rpc GetMyFeperm (google.protobuf.Empty) returns (Feperm) {
    option (google.api.http) = {
      get: "/v1/users/me/feperm"
    };
  }

  rpc GetUser (GetUserRequest) returns (User) {
    option (google.api.http) = {
      get: "/v1/users/{uid}"
    };
  }

  // rpc GetUserHierarchy (GetUserHierarchyRequest) returns (GetUserHierarchyReply) {
  //   option (google.api.http) = {
  //     get: "/v1/users/{uid}/hierarchy"
  //   };
  // }

  rpc ListUser (ListUserRequest) returns (ListUserReply) {
    option (google.api.http) = {
      get: "/v1/users"
    };
  }

  rpc AddTag (types.TagRequest) returns (types.TagList) {
    option (google.api.http) = {
      put: "/v1/users/{uid}/tag"
      body: "tags"
    };
  }

  rpc DeleteTag (types.TagRequest) returns (types.TagList) {
    option (google.api.http) = {
      delete: "/v1/users/{uid}/tag"
    };
  }

  rpc Login (LoginRequest) returns (LoginReply) {
    option (google.api.http) = {
      post: "/v1/users/login"
      body: "*"
    };
  }

  // clear HTTP cookies
  rpc Logout (google.protobuf.Empty) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/users/logout"
      body: "*"
    };
  }

  rpc SendAuthCode (SendAuthCodeRequest) returns (SendAuthCodeReply) {
    option (google.api.http) = {
      post: "/v1/users/send-auth-code"
      body: "*"
    };
  }

  rpc AssumeUser (AssumeUserRequest) returns (LoginReply) {
    option (google.api.http) = {
      get: "/v1/users/{uid}/assume"
    };
  }

  rpc RefreshToken (google.protobuf.Empty) returns (LoginReply) {
    option (google.api.http) = {
      get: "/v1/users/token/refresh"
    };
  }

  // // set the user's default team
  // // user created resources will be put into ones default team
  // rpc SetDefaultTeam (SetDefaultTeamRequest) returns (google.protobuf.Empty) {
  //   option (google.api.http) = {
  //     put: "/v1/users/{uid}/default-team"
  //   };
  // }

  // check if the user is allowed to perform the actions
  rpc IsAllowed (IsAllowedRequest) returns (IsAllowedReply) {
    option (google.api.http) = {
      get: "/v1/users/{uid}/is-allowed"
    };
  }

  // get the permissions granted on a resource
  rpc GetPerms (GetPermsRequest) returns (GetPermsReply) {
    option (google.api.http) = {
      get: "/v1/users/{uid}/perms"
    };
  }
}

message CreateUserRequest {
  string uid = 1;
  // mandatory in create-user requests
  string name = 2;
  // phone number: +8613412345678; mandatory in create-user requests
  string phone = 3;
  // email: <EMAIL>
  string email = 4;
  string avatar = 5;

  // user's system role: member/admin/root
  string role = 6;

  // user's gender
  User.Gender.Enum gender = 7 [(validate.rules).enum = {defined_only: true}];
  // user's birthday in RFC339 format: 2010-06-07T00:00:00Z
  // use a string type to allow an empty birthday
  string birthday = 8;
  string province = 9;
  string city = 10;
  // organization uid
  // string org_uid = 11;
  // string work_team_uid = 12;
}
//message CreateUserReply {}

message BatchCreateUsersRequest {
  option (openapi.v3.schema) = {
    required: ["users"]
  };

  repeated CreateUserRequest users = 1;
}

message UpdateUserRequest {
  option (openapi.v3.schema) = {
    required: ["user"]
  };

  // string uid = 1;
  // // see CreateUserRequest for valid field names
  // map<string, string> fields = 2;

  CreateUserRequest user = 1;
  repeated string fields = 2;
}
//message UpdateUserReply {}

message DeleteUserRequest {
  string uid = 1;
}
//message DeleteUserReply {}

message GetUserRequest {
  string uid = 1;
}
//message GetUserReply {}

// message GetUserHierarchyRequest {
//   string uid = 1;
//   // how many upper-level teams to return; 0 to return all
//   int32 levels_up = 2;
// }

// message GetUserHierarchyReply {
//   option (openapi.v3.schema) = {
//     required: ["teams"]
//   };

//   repeated Team teams = 1;
// }

message ListUserRequest {
  int32 page = 1;
  int32 pagesz = 2 [(validate.rules).int32 = {gte:0, lte: 500}];

  // find by name pattern
  string name_pattern = 3;
  // find by uid
  repeated string uids = 4 [(validate.rules).repeated.items.string = {pattern: "^[a-z0-9]{11}$"}];
  // find by phone number
  repeated string phones = 5 [(validate.rules).repeated.items.string = {pattern: "^\\+\\d+$"}];
  // find by emails
  repeated string emails = 6 [(validate.rules).repeated.items.string = {pattern: "^[\\w.-]+@[\\w.-]+\\.[\\w.]+$"}];
  // include user's organization in the reply
  bool with_org = 7;
  // find by attached tags
  repeated string tags = 8;
  // find by org_uid
  string org_uid = 9;
  // find by roles
  repeated string roles = 10;
}

message ListUserReply {
  option (openapi.v3.schema) = {
    required: ["users"]
  };

  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated User users = 2;
  // the organizations that the user, at the corresponding position, belongs to.
  repeated BaseUser orgs = 3;
}

message LoginRequest {
  option (openapi.v3.schema) = {
    required: ["id_type", "identity", "auth_type", "credential"]
  };

  message AuthType {
    enum Enum {
      unspecified = 0;
      authcode = 1;
      password = 2;
      otp = 3;
    }
  }

  // type of user's identity
  IDType.Enum id_type = 1 [(validate.rules).enum = {defined_only: true, not_in: [0]}];
  // user's phone number-number/email/...
  string identity = 2 ;
  // type of the authentication
  AuthType.Enum auth_type = 3 [(validate.rules).enum = {defined_only: true, not_in: [0]}];
  // authentication credential according to auth_type
  string credential = 4;
  // signed version of user agreement; 0 means no update
  int32 agreement = 5;
}

message LoginReply {
  option (openapi.v3.schema) = {
    required: ["user", "token", "expire_time", "feperm"]
  };

  // user info; it may be an assumed user in an assume request
  User user = 1;

  // JWT token
  string token = 2;
  // token expire time in RFC3339 format: 2016-01-01T00:00:00+08:00
  google.protobuf.Timestamp expire_time = 3;
  // front-end permissions
  Feperm feperm = 4;
  // the real user in an assume request
  User assume_by = 5;
}

message SendAuthCodeRequest {
  option (openapi.v3.schema) = {
    required: ["purpose", "channel", "receiver", "locale"]
  };

  message Purpose {
    enum Enum {
      unspecified = 0;
      login = 1;
    }
  }
  message Channel {
    enum Enum {
      unspecified = 0;
      sms = 1;
      email = 2;
    }
  }

  // usage of the auth code
  Purpose.Enum purpose = 1 [(validate.rules).enum = {defined_only: true, not_in: [0]}];
  // authentication code dispatch channel
  Channel.Enum channel = 2 [(validate.rules).enum = {defined_only: true, not_in: [0]}];
  // a phone number or email address according to the channel
  string receiver = 3;
  // language code: zh-Hans, en-US
  string locale = 4;
}

message SendAuthCodeReply {
  option (openapi.v3.schema) = {
    required: ["sequence"]
  };

  // sequence number of the auth code
  int32 sequence = 1;
  // signed version of user agreement; 0 means not signed
  int32 agreement = 2;
}

message AssumeUserRequest {
  option (openapi.v3.schema) = {
    required: ["uid"]
  };

  // uid of the user to assume; use "me" to unassume;
  // use "identity" to specify the user by his/her phone or email
  string uid = 1;
  // when uid is "identity", this holds the phone or email of the user to assume
  string identity = 2;
}

message IsAllowedRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "actions"]
  };

  message Action {
    option (openapi.v3.schema) = {
      required: ["resource", "perm"]
    };

    // resource name. format: type:(uid|IamGroup:team-uid)
    // pattern: "^\\w+:([\\w:.-]+)*$"
    string resource = 1 [(validate.rules).string = {pattern: "^\\w+:([\\w:.-]+)*$"}];
    string perm = 2 [(validate.rules).string = {pattern: "^\\w+\\.\\w+$"}];
  }

  string uid = 1;
  // go-kratos does not support objects list in URL query like?:
  //   /users/xxx/is-allowed?actions[0].resource=xxx&actions[0].perm=xxx
  // repeated Action actions = 2;

  Action action = 2;
}
message IsAllowedReply {
  option (openapi.v3.schema) = {
    required: ["allowed"]
  };

  // true if all of the actions are allowed; false otherwise
  bool allowed = 1;
}

message GetPermsRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "resource"]
  };

  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // resource name in format: type:uid; when scope is specified, uid may be omitted
  // pattern: "^\\w+:([\\w.-]+)?$"
  string resource = 2 [(validate.rules).string = {pattern: "^\\w+:([\\w.-]+)?$"}];
  // check if the user can create/list resources within an organization/team
  // format: IamGroup:uid, pattern: "^(IamGroup:[\\w-]+)?$"
  string scope = 3 [(validate.rules).string = {pattern: "^(IamGroup:[\\w.-]+)?$"}];
  // if perms is not empty, only check for the permissions in the list
  // if it is empty, check all permissions valid to the resource type.
  // pattern: "^\\w+\\.\\w+$"
  repeated string perms = 4 [(validate.rules).repeated.items.string = {pattern: "^\\w+\\.\\w+$"}];
}
message GetPermsReply {
  option (openapi.v3.schema) = {
    required: ["perms"]
  };

  repeated string perms = 1;
}
