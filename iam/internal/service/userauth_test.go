package service

import (
	"testing"

	"iam/internal/biz"

	"github.com/stretchr/testify/assert"
)

func TestParseUserIdentity(t *testing.T) {
	cases := []struct {
		in  string
		typ string
		id  string
		err bool
	}{
		{"", "", "", true},
		{"abc", "", "", true},
		{"a@b", "", "", true},
		{"<EMAIL>", biz.IDTypeEmail, "<EMAIL>", false},
		{"+0110", biz.IDTypePhone, "+0110", false},
		{"+011ab", biz.IDTypePhone, "+011ab", false},
		{"+11234", biz.IDTypePhone, "+11234", false},
		{"+8613456789012", biz.IDTypePhone, "+8613456789012", false},
		{"+8623456789012", "", "", true},
		{"+861234", "", "", true},
		{"+1234ab", "", "", true},
		{"13456789012", biz.IDTypePhone, "+8613456789012", false},
		{"134567891202", "", "", true},
		{"8613456789012", biz.IDTypePhone, "+8613456789012", false},
		{"86123456789ab", "", "", true},
		{"86123456789012", "", "", true},
		{"ab12345678912", "", "", true},
		{"23456789012", "", "", true},
		{"012", "", "", true},
		{"01234", biz.IDTypePhone, "+01234", false},
		{"012ab", biz.IDTypePhone, "+012ab", false},
		{"01234567890123456", "", "", true},
		{"34567834567", biz.IDTypeUid, "34567834567", false},
		{"byzjevlpi8w", biz.IDTypeUid, "byzjevlpi8w", false},
	}
	for _, c := range cases {
		msg := "input: " + c.in
		typ, id, err := parseUserIdentity(c.in)
		assert.Equal(t, c.typ, typ, msg)
		assert.Equal(t, c.id, id, msg)
		assert.Equal(t, c.err, err != nil, msg)
	}
}
