syntax = "proto3";

package anno.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/api/field_behavior.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";
import "iam/v1/type.proto";
import "anno/v1/elemanno.proto";
import "anno/v1/type_lotconfig.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anno/v1;anno";
option java_multiple_files = true;
option java_package = "anno.v1";

service Jobs {
  // not intended for external use
  rpc CreateJob (CreateJobRequest) returns (Job) {
    // option (google.api.http) = {
    //   post: "/v1/jobs"
    //   body: "*"
    // };
  }

  // not intended for external use
  rpc UpdateJob (UpdateJobRequest) returns (Job) {
    //    option (google.api.http) = {
    //      patch: "/v1/jobs/{job.uid}"
    //      body: "job"
    //    };
  }

  // not intended for external use
  rpc DeleteJob (DeleteJobRequest) returns (google.protobuf.Empty) {
    //    option (google.api.http) = {
    //      delete: "/v1/jobs/{uid}"
    //    };
  }

  rpc GetJob (GetJobRequest) returns (Job) {
    option (google.api.http) = {
      get: "/v1/jobs/{uid}"
    };
  }

  rpc ListJob (ListJobRequest) returns (ListJobReply) {
    option (google.api.http) = {
      get: "/v1/jobs"
    };
  }

  rpc GetJoblog (GetJoblogRequest) returns (GetJoblogReply) {
    option (google.api.http) = {
      get: "/v1/jobs/{uid}/log"
    };
  }

  rpc GetRawJoblog (GetJoblogRequest) returns (GetRawJoblogReply) {
    option (google.api.http) = {
      get: "/v1/jobs/{uid}/rawlog"
    };
  }

  // Claim a job
  rpc ClaimJob (ClaimJobRequest) returns (ClaimJobResponse) {
    option (google.api.http) = {
      post: "/v1/jobs/claim"
      body: "*"
    };
  }

  // Assign a job
  rpc AssignJob (AssignJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/jobs/{uid}/assign"
      body: "*"
    };
  }

  // giveup a job
  rpc GiveupJob (GiveupJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/jobs/{uid}/giveup"
      body: "*"
    };
  }

  // Submit job annotations; used in the 1st phase only.
  rpc SubmitJob (SubmitJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/jobs/{uid}/submit"
      body: "*"
    };
  }

  // Review job annotations; used in 2nd phase onwards.
  rpc ReviewJob (ReviewJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/jobs/{uid}/review"
      body: "*"
    };
  }

  // Revert multiple jobs to a previous phase.
  rpc BatchRevertJob (BatchRevertJobRequest) returns (BatchRevertJobReply) {
    option (google.api.http) = {
      post: "/v1/jobs/revert"
      body: "*"
    };
  }

  // // Get jobs annotation result
  // rpc GetAnnos (GetJobAnnosRequest) returns (GetJobAnnosReply) {
  //   option (google.api.http) = {
  //     get: "/v1/jobs/annos"
  //   };
  // }

  // Set rawdata embedding.
  rpc SetRawdataEmbedding (SetRawdataEmbeddingRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/jobs/{uid}/elems/{elem_idx}/rawdatas/{rawdata_idx}/embedding"
      body: "*"
    };
  }

  // Save draft annos/comments in the server.
  rpc SaveJobDraft (SaveJobDraftRequest) returns (SaveJobDraftReply) {
    option (google.api.http) = {
      put: "/v1/jobs/{uid}/draft"
      body: "*"
    };
  }
  // Get draft annos/comments saved in the server.
  rpc GetJobDraft (GetJobDraftRequest) returns (GetJobDraftReply) {
    option (google.api.http) = {
      get: "/v1/jobs/{uid}/draft"
    };
  }

  // get the last commit joblog of a phase
  rpc GetJobLastCommitLog (GetJobLastCommitLogRequest) returns (GetJobLastCommitLogReply) {
    option (google.api.http) = {
      get: "/v1/jobs/{uid}/phases/{phase}/last-commit-log"
    };
  }

  // check if there is any holding jobs for users/orgs
  rpc HasHoldingJobs (HasHoldingJobsRequest) returns (HasHoldingJobsReply) {
    option (google.api.http) = {
      get: "/v1/jobs/-/holding"
    };
  }
  rpc SkipAnnotation (SkipAnnotationRequest) returns (SkipAnnotationReply) {
    option (google.api.http) = {
      post: "/v1/jobs/{uid}/skip-annotation"
      body: "*"
    };
  }
  rpc GetSkipAnnotation (GetSkipAnnotationRequest) returns (GetSkipAnnotationReply) {
    option (google.api.http) = {
      get: "/v1/jobs/{uid}/get-skip-annotation"
    };
  }

}

message CreateJobRequest {
  string lot_uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // job index in the lot
  int32 idx_in_lot = 2;
  // identify a subjob in a splitted lot.
  string subtype = 4;
  // 待标注的内容
  repeated Element elements = 5;
  // 标注结果
  JobAnno annotations = 6;
  repeated AnnoComment comments = 7;
  string state = 8;
  // phase number, starts from 1
  int32 phase = 9;
}
//message CreateJobReply {}

message UpdateJobRequest {
  message Updates {
    string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
    // string state = 2;
  }
  Updates job = 1;

  // name of fields to be updated
  repeated string fields = 2;
}
//message UpdateJobReply {}

message DeleteJobRequest {
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
}
//message DeleteJobReply {}

message GetJobRequest {
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // [PRIVILEGED] if true, the job data (e.g. elements, annotations) will be expanded
  bool expand = 2;
}
//message GetJobReply {}

message ListJobFilter {
  option (openapi.v3.schema) = {
    required: ["lot_uid"]
  };

  // filter by job uids; max length is 100
  repeated string uids = 1 [(validate.rules).repeated.items.string = {pattern: "^[a-z0-9]{11}$"}];
  // filter by belonging lot
  string lot_uid = 2 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|^$"}];
  // filter by jobs current phase; starts from 1
  int32 phase = 3 [(validate.rules).int32.gte = 0];
  // filter by jobs current state
  Job.State.Enum state = 4;
  // query jobs by their last executors; max length is 100
  repeated string last_executors = 5 [(validate.rules).repeated.items.string = {pattern: "^[a-z0-9]{11}$"}];
  // query jobs by the last executor's belonging team
  string last_execteam = 6 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|^$"}];

  repeated int32 phases = 7;
  string jobclip = 8;
}

message ListJobRequest {
  option (openapi.v3.schema) = {
    required: ["filter"]
  };

  int32 page = 1;
  int32 pagesz = 2 [(validate.rules).int32 = {gte:0, lte: 100}];

  ListJobFilter filter = 3;
  // if to return jobs' last_executor and last_execteam in the response
  bool show_last_executor = 4;
  // [PRIVILEGED] show full job info, including annotations, comments, etc.
  bool full_job = 5;
  // whether to return jobs' executor in each phase
  bool show_executors = 6;
  // query jobs by their containing elements' name pattern
  string elem_name_pattern = 7;
}

message ListJobReply {
  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated Job jobs = 2;
  // jobs' executors in each phase. the key is job uid.
  map<string, ElementAnno.Metadata> executors = 6;
}

message GetJoblogRequest {
  int32 page = 1;
  int32 pagesz = 2 [(validate.rules).int32 = {gte:0, lte: 100}];

  string uid = 3 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
}

message GetJoblogReply {
  message Log {
    option (openapi.v3.schema) = {
      required: ["operator", "action", "time"]
    };

    message Action {
      enum Enum {
        unspecified = 0;
        accept = 1;
        reject = 2;
        recycle = 3;
        submit = 4;
        claim = 5;
        giveup = 6;
        assign = 7;
        timeout = 8;
        end = 9; // lot completes or is cancelled
      }
    }

    message GiveupReason {
      option (openapi.v3.schema) = {
        required: ["reason", "details"]
      };

      string reason = 1;
      string details = 2;
    }

    message Details {
      option (openapi.v3.schema) = {
        required: ["add_comments", "resolves"]
      };

      // added comments in this log
      repeated AnnoComment add_comments = 1;
      // resolved comments in this log
      repeated ResolveAnnoComment resolves = 2;
      // job giveup reason
      GiveupReason giveup_reason = 3;
    }

    iam.v1.BaseUser operator = 1;
    Action.Enum action = 2;
    // detailed information if any
    Details details = 3;
    // phase number at this event
    int32 from_phase = 4;
    // phase number after this event
    int32 to_phase = 5;
    // created_at timestamp
    google.protobuf.Timestamp created_at = 15;
  }

  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated Log logs = 2;
}

message GetRawJoblogReply {
  // json encoded raw logs
  string logs = 1;
}

message AssignJobRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "executor_uid"]
  };

  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  string executor_uid = 2 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|^$"}];
}

message ClaimJobRequest {
  option (openapi.v3.schema) = {
    required: ["lot_uid"]
  };

  // which lot the job is claimed from; if not specified, the job is claimed from any assigned lot
  string lot_uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // for a splitted lot, this is to specify the sub-lot.
  string subtype = 2;
  // when lot_uid/subtype is specified and no available job can be claimed, this controls if it
  // should retry to claim in disregard of lot_uid/subtype
  bool fallback = 3;

  message Prefer {
    enum Enum {
      unspecified = 0;
      rejected_first = 1;
    }
  }
  // claim preference
  Prefer.Enum prefer = 4;
  reserved 5;
  // true to reset the claim countdown timer; the response will not contain the job info.
  bool renew = 6;
  // required if renew is true
  string job_uid = 7 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|^$"}];
}

message ClaimJobResponse {
  Job job = 1;
}

message GiveupJobRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "reason"]
  };

  // enum Reason {
  //   unspecified_reason = 0;
  //   too_hard = 1;
  //   other = 2;
  // }

  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  string reason = 2;
  string details = 3;
}

message SubmitJobRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "annotations"]
  };

  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  JobAnno annotations = 2;
  // comments to be resolved
  repeated ResolveAnnoComment resolves = 3;
  // // save result, but do not change job state
  // bool stash = 4;
}

message ReviewJobRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "decision"]
  };

  message Decision {
    enum Enum {
      unspecified = 0;
      accept = 1;
      reject = 2;
      recycle = 3;
    }
  }

  // job uid
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // if it is null, this review makes no change to previous job annotations;
  // otherwise, it is a complete result to replace previous job annotations.
  JobAnno annotations = 2;
  // "accept" will put the job to the next phase; annotations and resolve_comments can be set.
  // "reject" will put the job to the previous phase, and assign it to the previous executor at that phase;
  // annotations, comments and resolve_comments can be set.
  // "recycle" will put the job to its initial phase; previous annotations and comments will be cleared;
  // other fields will be ignored.
  Decision.Enum decision = 3 [(validate.rules).enum = {defined_only: true, not_in: [0]}];
  // new comments to the annotations when decision is "reject"
  repeated AnnoComment comments = 4;
  // comments to resolve
  repeated ResolveAnnoComment resolves = 5;
  // comments to update
  repeated AnnoComment updated_comments = 6;
  // comments to delete
  repeated ResolveAnnoComment deleted_comments = 7;
}

message ResolveAnnoComment {
  reserved 1, 2;
  // comment uuid
  string uuid = 3;
  // UUID of the associated objects if the comment is missed(漏标)
  repeated string obj_uuids = 4;
}

// comment to an annotation
message AnnoComment {
  option (openapi.v3.schema) = {
    required: ["uuid", "elem_idx", "obj_uuids", "reasons", "commenter", "add_phase", "resolve_phase", "created_at"]
  };

  // element index within the job
  int32 elem_idx = 1;
  reserved 2, 3;
  // details or additional information
  string content = 4;
  // the user who added the comment;
  // the field is ignored in review requests
  iam.v1.BaseUser commenter = 5;
  // number of the phase in which the comment is added;
  // the field is ignored in review requests
  int32 add_phase = 6;
  // number of the highest phase in which the comment is marked as resolved
  // in frontend, only executors in phases within (resolve_phase, add_phase] can see the comment and react to it;
  // the field is ignored in review requests
  int32 resolve_phase = 7;
  // rawdata index within the element; should not be omitted if the comment is missed（漏标）
  int32 rd_idx = 8;

  message ExtraInfo {
    reserved 1;
    // location of the missing object: (x,y,z)
    repeated double position = 2;
    // label of the missed object
    repeated string labels = 3;
  }
  // extra info associated with the comment
  ExtraInfo extra_info = 9;
  // UUID of the comment; it is obj_uuid when omitted. Must not be empty if reason is missed（漏标）.
  string uuid = 10;
  // UUID of the associated objects; it may be empty if the comment is missed(漏标), or assicated with the element or job
  repeated string obj_uuids = 11;
  // comment reasons
  AnnoCommentReason.Reasons reasons = 12;

  message Scope {
    enum Enum {
      unspecified = 0;
      object = 1;
      element = 2;
      job = 3;
    }
  }
  // scope of the comment
  Scope.Enum scope = 13;

  // the field is ignored in review requests
  google.protobuf.Timestamp created_at = 15;
}

message Job {
  option (openapi.v3.schema) = {
    required: ["uid", "idx_in_lot", "subtype", "elements", "annotations", "comments",
      "state", "cause", "phase", "ins_cnt", "lot_uid", "created_at"],
  };

  message State {
    enum Enum {
      unspecified = 0;
      unstart = 1;
      doing = 2;
      finished = 3;
    }
  }

  // enum StateCause {
  //   unspecified = 0;
  //   reject = 1;
  // }

  // job UID
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // lot UID
  string lot_uid = 2 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // job index in the lot
  int32 idx_in_lot = 3;
  // identify a subjob in a splitted lot.
  string subtype = 4;
  // 待标注的内容
  repeated Element elements = 5;
  // 标注结果
  repeated ElementAnno annotations = 6;
  // unresolved comments (a comment can only be resolved at the same phase when it is added)
  repeated AnnoComment comments = 7;
  State.Enum state = 8;
  // why job is in the state
  string cause = 9;
  // phase number, starts from 1
  int32 phase = 10;
  // manually annotated objects count in the job
  int32 ins_cnt = 11;
  // annotated objects count in the job (including the interpolated ones)
  int32 ins_total = 12;
  // current executor; valid for privileged requestors
  string executor_uid = 13;
  // if it is true, the annotations contain only the key element results,
  // and interpolation is needed to get the final result
  bool need_interpolation = 16;
  // current executor or last submitter; valid for privileged requestors
  iam.v1.BaseUser last_executor = 17;
  // team of last_executor; valid for privileged requestors
  iam.v1.BaseUser last_execteam = 18;
  // number of elements in this job
  int32 elems_cnt = 19;
  // job attributes
  repeated AttrAndValues job_attrs = 20;

  message CamParam {
    // extrinsic params: x,y,z,qx,qy,qz,qw
    repeated double extrinsic = 1;
    // intrinsic params: fx,fy,cx,cy
    repeated double intrinsic = 2;
    // distortion params: distortion_type,k1,k2,...
    repeated double distortion = 3;
  }

  // 相机映射参数，其中 key is rawdata.meta.image_meta.camera
  map<string, CamParam> cam_params = 21;

  message ElementData {
    // 所有帧
    repeated Element elements = 1;
    // 相机映射参数，其中 key is rawdata.meta.image_meta.camera
    map<string, CamParam> cam_params = 2;
  }

  message AnnotationData {
    // 所有标注结果
    repeated ElementAnno element_annos = 1;
    // 包属性
    repeated AttrAndValues job_attrs = 2;
  }

  message CommentData {
    // 所有批注
    repeated AnnoComment comments = 1;
  }

  // 保存了 elements 等数据, 其结构参考 Job_ElementData;
  // 如果有多个 url，完整的内容将是这些 url 所指文件的合集，cam_params 只会放在第一个文件里
  repeated string elements_urls = 22;
  // 保存了 annotations 等数据, 其结构参考 Job_AnnotationData
  string annotations_url = 23;
  // 保存了 comments 等数据，其结构参考 Job_CommentData
  string comments_url = 24;

  string job_elem_clip = 25;

  google.protobuf.Timestamp updated_at = 14 [(google.api.field_behavior) = OUTPUT_ONLY];
  google.protobuf.Timestamp created_at = 15 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// annotations of a job
message JobAnno {
  option (openapi.v3.schema) = {
    required: ["element_annos", "need_interpolation", "ins_cnt"]
  };

  // message Interpolation {
  //   option (openapi.v3.schema) = {
  //     required: ["track_id", "rawdata_idx", "key_elem_idx"]
  //   };

  //   // id to track the same object in different elements
  //   string track_id = 1;
  //   // index of the rawdata in which the object is observed
  //   int32 rawdata_idx = 2;
  //   // key element index in ascending order;
  //   // the element from which and onwards the object is disappeared is also a key element,
  //   // such an element can be identified by who is a key element while the object is not seen
  //   // in its annotations.
  //   repeated int32 key_elem_idx = 3;
  // }

  // 相应位置的元素对应 Job 的 Elements 相应位置的结果
  repeated ElementAnno element_annos = 1;
  // if it is true, the annotations contain only the key element results,
  // and interpolation is needed to get the final result
  bool need_interpolation = 2;
  // number of objects annotated in the job (including the interpolated objects)
  int32 ins_cnt = 3;
  // // interpolation info in a job (frame series)
  // repeated Interpolation interpolations = 3;

  // job index in the lot
  int32 job_index = 4;
  // job attributes
  repeated AttrAndValues attrs = 5;
}

message BatchRevertJobRequest {
  option (openapi.v3.schema) = {
    required: ["action", "options", "filter"]
  };

  message Action {
    option (openapi.v3.schema) = {
      required: ["to_phase"]
    };

    // set jobs to this phase number; starts from 1
    int32 to_phase = 1 [(validate.rules).int32.gte = 1];
  }

  message Options {
    option (openapi.v3.schema) = {
      required: ["keep_annos", "keep_comments", "to_previous_executor"]
    };

    // if to keep annotations
    bool keep_annos = 1;
    // if to keep comments
    bool keep_comments = 2;
    // if to reassign to previous executor
    bool to_previous_executor = 3;
  }

  // changes to be made to the jobs
  Action action = 1;
  // options to the action
  Options options = 2;
  // job filter
  ListJobFilter filter = 3;
}

message BatchRevertJobReply {
  option (openapi.v3.schema) = {
    required: ["ok_job_uids", "fail_job_uids"]
  };

  // jobs successfully updated
  repeated string ok_job_uids = 1;
  // jobs failed to update
  repeated string fail_job_uids = 2;
}

message GetJobAnnosRequest {
  option (openapi.v3.schema) = {
    required: ["filter"]
  };

  int32 pagesz = 1 [(validate.rules).int32 = {gte:0, lte: 100}];
  // An opaque pagination token returned from a previous call.
  // An empty token denotes the first page.
  string page_token = 2;

  // job filter
  ListJobFilter filter = 3;
}

message GetJobAnnosReply {
  option (openapi.v3.schema) = {
    required: ["annos", "next_page_token"]
  };

  // annotations
  repeated JobAnno annos = 1;
  string next_page_token = 2;
}

message SetRawdataEmbeddingRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "elem_idx", "rawdata_idx", "embedding_uri"]
  };

  // job UID
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // element index within job
  int32 elem_idx = 2;
  // rawdata index within element
  int32 rawdata_idx = 3;
  // embedding file URI
  string embedding_uri = 4 [(validate.rules).string.uri_ref = true];
}

message SaveJobDraftRequest {
  // job uid
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // draft version
  string version = 2;
  // job draft might contains annos, comments and etc
  string draft = 3;
}

message SaveJobDraftReply {
  // time in RFC3339 format: 2016-01-01T00:00:00+08:00
  string version = 1;
}

message GetJobDraftRequest {
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
}
message GetJobDraftReply {
  option (openapi.v3.schema) = {
    required: ["version"]
  };

  // time in RFC3339 format: 2016-01-01T00:00:00+08:00
  string version = 1;
  // draft annos/comments url, refer to SaveJobDraftRequest
  string draft_url = 2;
}

message GetJobLastCommitLogRequest {
  message Direction {
    enum Enum {
      unspecified = 0;
      // submit/accept
      up = 1;
      // reject/recycle
      down = 2;
    }
  }

  // job uid
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // phase number: 0 means all the phases
  int32 phase = 2 [(validate.rules).int32.gte = 0];
  // commit direction
  Direction.Enum direction = 3;
}

message GetJobLastCommitLogReply {
  // joblog without the details field
  GetJoblogReply.Log log = 1;
}

message HasHoldingJobsRequest {
  // if available, only query by org_uid; otherwise, query by user_uids
  string org_uid = 1;
  repeated string user_uids = 2;
}

message HasHoldingJobsReply {
  option (openapi.v3.schema) = {
    required: ["holding"]
  };

  map<string, bool> holding = 1;
}

message SkipAnnotationRequest {
  // if available, only query by org_uid; otherwise, query by user_uids
  string uid = 1;
  string lot_id = 2;
  bool   skip_annotation =3;
}

message SkipAnnotationReply {
  // if available, only query by org_uid; otherwise, query by user_uids
  bool skip_annotation =1;
}

message GetSkipAnnotationRequest {
  // if available, only query by org_uid; otherwise, query by user_uids
  string uid = 1;
}

message GetSkipAnnotationReply {
  // if available, only query by org_uid; otherwise, query by user_uids
  bool skip_annotation =1;
}


