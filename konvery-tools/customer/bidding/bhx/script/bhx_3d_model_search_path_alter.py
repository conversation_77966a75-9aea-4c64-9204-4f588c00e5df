import os
import glob
import tarfile
import shutil
import sys

sys.path.append('.')
from customer.common import tools


def run(input_dir):
    file_counter = 0

    # unzip file
    unzip_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_unzip')
    if os.path.exists(unzip_dir):
        shutil.rmtree(unzip_dir)
    os.mkdir(unzip_dir)

    tar = tarfile.open(input_dir)
    tar.extractall(path=unzip_dir)

    # create result_folder
    result_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_result')
    try:
        os.mkdir(result_dir)
    except FileExistsError:
        shutil.rmtree(result_dir)
        os.mkdir(result_dir)

    for file_dir in glob.glob(os.path.join(unzip_dir, '**', '**.png'), recursive=True):
        parent_dir = os.path.dirname(file_dir)
        parent_folder = os.path.basename(parent_dir)
        txt_file_dir = os.path.join(parent_dir, '00000_' + parent_folder + '.txt')
        temp_result_dir = result_dir

        # get folder name and remove txt file
        with open(txt_file_dir, 'r') as f:
            file_content = [line.strip('\n') for line in f]

        temp_result_dir = os.path.join(temp_result_dir, parent_folder + file_content[1], 'img')

        # move png files
        if os.path.basename(os.path.dirname(temp_result_dir)) not in os.listdir(result_dir):
            os.makedirs(temp_result_dir, exist_ok=True)
        shutil.copy2(file_dir,
                     os.path.join(temp_result_dir, os.path.basename(file_dir)))
        file_counter += 1

    trans_result = {
        "code": 0,
        "output": result_dir,
        "err_msg": '成功',
        "moved_files": file_counter
    }

    print(trans_result)
    return trans_result


if __name__ == "__main__":
    args = tools.get_args()
    run(args.input)

    # args_input = ''
    # run(args_input)
