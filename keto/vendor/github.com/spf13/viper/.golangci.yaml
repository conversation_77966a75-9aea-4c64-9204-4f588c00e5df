run:
    timeout: 5m

linters-settings:
    gci:
        sections:
            - standard
            - default
            - prefix(github.com/spf13/viper)
    golint:
        min-confidence: 0
    goimports:
        local-prefixes: github.com/spf13/viper

linters:
    disable-all: true
    enable:
        - bodyclose
        - deadcode
        - dogsled
        - dupl
        - durationcheck
        - exhaustive
        - exportloopref
        - gci
        - gofmt
        - gofumpt
        - goimports
        - gomoddirectives
        - goprintffuncname
        - govet
        - importas
        - ineffassign
        - makezero
        - misspell
        - nakedret
        - nilerr
        - noctx
        - nolintlint
        - prealloc
        - predeclared
        - revive
        - rowserrcheck
        - sqlclosecheck
        - staticcheck
        - structcheck
        - stylecheck
        - tparallel
        - typecheck
        - unconvert
        - unparam
        - unused
        - varcheck
        - wastedassign
        - whitespace

        # fixme
        # - cyclop
        # - errcheck
        # - errorlint
        # - exhaustivestruct
        # - forbidigo
        # - forcetypeassert
        # - gochecknoglobals
        # - gochecknoinits
        # - gocognit
        # - goconst
        # - gocritic
        # - gocyclo
        # - godot
        # - gosec
        # - gosimple
        # - ifshort
        # - lll
        # - nlreturn
        # - paralleltest
        # - scopelint
        # - thelper
        # - wrapcheck

        # unused
        # - depguard
        # - goheader
        # - gomodguard

        # don't enable:
        # - asciicheck
        # - funlen
        # - godox
        # - goerr113
        # - gomnd
        # - interfacer
        # - maligned
        # - nestif
        # - testpackage
        # - wsl
