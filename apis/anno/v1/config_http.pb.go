// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: anno/v1/config.proto

package anno

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationConfigsGetVersion = "/anno.v1.Configs/GetVersion"
const OperationConfigsListCommentReasons = "/anno.v1.Configs/ListCommentReasons"
const OperationConfigsListErrors = "/anno.v1.Configs/ListErrors"
const OperationConfigsPutCommentReasons = "/anno.v1.Configs/PutCommentReasons"

type ConfigsHTTPServer interface {
	GetVersion(context.Context, *emptypb.Empty) (*GetVersionReply, error)
	// ListCommentReasons List comment reasons
	ListCommentReasons(context.Context, *emptypb.Empty) (*ListCommentReasonsReply, error)
	// ListErrors List Errors info
	ListErrors(context.Context, *emptypb.Empty) (*Errors, error)
	// PutCommentReasons Modify comment reasons
	PutCommentReasons(context.Context, *PutCommentReasonsRequest) (*emptypb.Empty, error)
}

func RegisterConfigsHTTPServer(s *http.Server, srv ConfigsHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/errors", _Configs_ListErrors0_HTTP_Handler(srv))
	r.GET("/v1/version", _Configs_GetVersion0_HTTP_Handler(srv))
	r.GET("/v1/commentreasons", _Configs_ListCommentReasons0_HTTP_Handler(srv))
	r.PUT("/v1/commentreasons", _Configs_PutCommentReasons0_HTTP_Handler(srv))
}

func _Configs_ListErrors0_HTTP_Handler(srv ConfigsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationConfigsListErrors)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListErrors(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Errors)
		return ctx.Result(200, reply)
	}
}

func _Configs_GetVersion0_HTTP_Handler(srv ConfigsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationConfigsGetVersion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetVersion(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetVersionReply)
		return ctx.Result(200, reply)
	}
}

func _Configs_ListCommentReasons0_HTTP_Handler(srv ConfigsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationConfigsListCommentReasons)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListCommentReasons(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListCommentReasonsReply)
		return ctx.Result(200, reply)
	}
}

func _Configs_PutCommentReasons0_HTTP_Handler(srv ConfigsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PutCommentReasonsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationConfigsPutCommentReasons)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PutCommentReasons(ctx, req.(*PutCommentReasonsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type ConfigsHTTPClient interface {
	GetVersion(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *GetVersionReply, err error)
	ListCommentReasons(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *ListCommentReasonsReply, err error)
	ListErrors(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *Errors, err error)
	PutCommentReasons(ctx context.Context, req *PutCommentReasonsRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type ConfigsHTTPClientImpl struct {
	cc *http.Client
}

func NewConfigsHTTPClient(client *http.Client) ConfigsHTTPClient {
	return &ConfigsHTTPClientImpl{client}
}

func (c *ConfigsHTTPClientImpl) GetVersion(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*GetVersionReply, error) {
	var out GetVersionReply
	pattern := "/v1/version"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationConfigsGetVersion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ConfigsHTTPClientImpl) ListCommentReasons(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*ListCommentReasonsReply, error) {
	var out ListCommentReasonsReply
	pattern := "/v1/commentreasons"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationConfigsListCommentReasons))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ConfigsHTTPClientImpl) ListErrors(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*Errors, error) {
	var out Errors
	pattern := "/v1/errors"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationConfigsListErrors))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ConfigsHTTPClientImpl) PutCommentReasons(ctx context.Context, in *PutCommentReasonsRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/commentreasons"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationConfigsPutCommentReasons))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
