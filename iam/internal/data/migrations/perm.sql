-- mig:up 2022070101 initial

-- front-end permissions
CREATE TABLE feperms
(
    id           BIGSERIAL    NOT NULL PRIMARY KEY,
    role         VARCHAR(64)  NOT NULL UNIQUE,
    perms        JSONB,
    created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);

CREATE TABLE perms
(
    id           BIGSERIAL    NOT NULL PRIMARY KEY,
    name         VARCHAR(64)  NOT NULL UNIQUE, -- Iam<PERSON><PERSON>.create
    class        VARCHAR(64)  NOT NULL,        -- I<PERSON><PERSON><PERSON>
    created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_perms_class ON perms (class) INCLUDE (name);

-- mig:down 2022070101 initial
DROP TABLE feperms;
DROP TABLE perms;

