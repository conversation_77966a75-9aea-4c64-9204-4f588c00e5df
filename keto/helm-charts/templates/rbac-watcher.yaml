{{- if .Values.watcher.enabled }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "keto.serviceAccountName" . }}-watcher
  labels:
    app.kubernetes.io/name: {{ include "keto.name" . }}-watcher
    app.kubernetes.io/instance: {{ .Release.Name }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "keto.fullname" . }}-watcher
  namespace: {{ .Release.Namespace }}
rules:
  - apiGroups: ["apps"]
    resources: ["deployments"]
    verbs: 
      - list
      - watch
      - get
  - apiGroups: ["apps"]
    resources: ["deployments"]
    verbs:
      - get
      - list
      - patch
      - update
      - watch
    resourceNames:
      - {{ include "keto.fullname" . }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "keto.fullname" . }}-watcher
  namespace: {{ .Release.Namespace }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "keto.fullname" . }}-watcher
subjects:
  - kind: ServiceAccount
    name: {{ include "keto.fullname" . }}-watcher
    namespace: {{ .Release.Namespace }}
{{- end }}
