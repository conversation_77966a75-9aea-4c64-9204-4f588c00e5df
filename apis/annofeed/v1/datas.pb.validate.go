// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: annofeed/v1/datas.proto

package annofeed

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	anno "gitlab.rp.konvery.work/platform/apis/anno/v1"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = anno.Element_Type_Enum(0)
)

// Validate checks the field values on CreateDataRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateDataRequestMultiError, or nil if none found.
func (m *CreateDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for Desc

	if _, ok := anno.Element_Type_Enum_name[int32(m.GetType())]; !ok {
		err := CreateDataRequestValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for BaseOnUid

	if all {
		switch v := interface{}(m.GetSource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateDataRequestValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateDataRequestValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateDataRequestValidationError{
				field:  "Source",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if !_CreateDataRequest_OrgUid_Pattern.MatchString(m.GetOrgUid()) {
		err := CreateDataRequestValidationError{
			field:  "OrgUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for OrderUid

	if len(errors) > 0 {
		return CreateDataRequestMultiError(errors)
	}

	return nil
}

// CreateDataRequestMultiError is an error wrapping multiple validation errors
// returned by CreateDataRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateDataRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateDataRequestMultiError) AllErrors() []error { return m }

// CreateDataRequestValidationError is the validation error returned by
// CreateDataRequest.Validate if the designated constraints aren't met.
type CreateDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateDataRequestValidationError) ErrorName() string {
	return "CreateDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateDataRequestValidationError{}

var _CreateDataRequest_OrgUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on UpdateDataRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateDataRequestMultiError, or nil if none found.
func (m *UpdateDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateDataRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateDataRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateDataRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateDataRequestMultiError(errors)
	}

	return nil
}

// UpdateDataRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateDataRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateDataRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateDataRequestMultiError) AllErrors() []error { return m }

// UpdateDataRequestValidationError is the validation error returned by
// UpdateDataRequest.Validate if the designated constraints aren't met.
type UpdateDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateDataRequestValidationError) ErrorName() string {
	return "UpdateDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateDataRequestValidationError{}

// Validate checks the field values on DeleteDataRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDataRequestMultiError, or nil if none found.
func (m *DeleteDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_DeleteDataRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := DeleteDataRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteDataRequestMultiError(errors)
	}

	return nil
}

// DeleteDataRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteDataRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDataRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDataRequestMultiError) AllErrors() []error { return m }

// DeleteDataRequestValidationError is the validation error returned by
// DeleteDataRequest.Validate if the designated constraints aren't met.
type DeleteDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDataRequestValidationError) ErrorName() string {
	return "DeleteDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDataRequestValidationError{}

var _DeleteDataRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on GetDataRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDataRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetDataRequestMultiError,
// or nil if none found.
func (m *GetDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetDataRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := GetDataRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Simple

	if len(errors) > 0 {
		return GetDataRequestMultiError(errors)
	}

	return nil
}

// GetDataRequestMultiError is an error wrapping multiple validation errors
// returned by GetDataRequest.ValidateAll() if the designated constraints
// aren't met.
type GetDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDataRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDataRequestMultiError) AllErrors() []error { return m }

// GetDataRequestValidationError is the validation error returned by
// GetDataRequest.Validate if the designated constraints aren't met.
type GetDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDataRequestValidationError) ErrorName() string { return "GetDataRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDataRequestValidationError{}

var _GetDataRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on ListDataRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListDataRequestMultiError, or nil if none found.
func (m *ListDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListDataRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for OrgUid

	// no validation rules for CreatorUid

	// no validation rules for NamePattern

	// no validation rules for OrderUid

	if len(errors) > 0 {
		return ListDataRequestMultiError(errors)
	}

	return nil
}

// ListDataRequestMultiError is an error wrapping multiple validation errors
// returned by ListDataRequest.ValidateAll() if the designated constraints
// aren't met.
type ListDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListDataRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListDataRequestMultiError) AllErrors() []error { return m }

// ListDataRequestValidationError is the validation error returned by
// ListDataRequest.Validate if the designated constraints aren't met.
type ListDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListDataRequestValidationError) ErrorName() string { return "ListDataRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListDataRequestValidationError{}

// Validate checks the field values on ListDataReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListDataReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListDataReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListDataReplyMultiError, or
// nil if none found.
func (m *ListDataReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListDataReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetDatas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListDataReplyValidationError{
						field:  fmt.Sprintf("Datas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListDataReplyValidationError{
						field:  fmt.Sprintf("Datas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListDataReplyValidationError{
					field:  fmt.Sprintf("Datas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListDataReplyMultiError(errors)
	}

	return nil
}

// ListDataReplyMultiError is an error wrapping multiple validation errors
// returned by ListDataReply.ValidateAll() if the designated constraints
// aren't met.
type ListDataReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListDataReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListDataReplyMultiError) AllErrors() []error { return m }

// ListDataReplyValidationError is the validation error returned by
// ListDataReply.Validate if the designated constraints aren't met.
type ListDataReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListDataReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListDataReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListDataReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListDataReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListDataReplyValidationError) ErrorName() string { return "ListDataReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListDataReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListDataReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListDataReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListDataReplyValidationError{}

// Validate checks the field values on Data with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Data with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in DataMultiError, or nil if none found.
func (m *Data) ValidateAll() error {
	return m.validate(true)
}

func (m *Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for Desc

	if _, ok := anno.Element_Type_Enum_name[int32(m.GetType())]; !ok {
		err := DataValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for BaseOnUid

	if all {
		switch v := interface{}(m.GetSource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataValidationError{
				field:  "Source",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if !_Data_OrgUid_Pattern.MatchString(m.GetOrgUid()) {
		err := DataValidationError{
			field:  "OrgUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for OrderUid

	// no validation rules for Size

	// no validation rules for State

	// no validation rules for Error

	// no validation rules for CreatorUid

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataValidationError{
				field:  "Summary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DataMultiError(errors)
	}

	return nil
}

// DataMultiError is an error wrapping multiple validation errors returned by
// Data.ValidateAll() if the designated constraints aren't met.
type DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataMultiError) AllErrors() []error { return m }

// DataValidationError is the validation error returned by Data.Validate if the
// designated constraints aren't met.
type DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataValidationError) ErrorName() string { return "DataValidationError" }

// Error satisfies the builtin error interface
func (e DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataValidationError{}

var _Data_OrgUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on GetDataElementsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDataElementsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDataElementsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDataElementsRequestMultiError, or nil if none found.
func (m *GetDataElementsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDataElementsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetDataElementsRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := GetDataElementsRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetStartIdx() < 0 {
		err := GetDataElementsRequestValidationError{
			field:  "StartIdx",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Count

	if len(errors) > 0 {
		return GetDataElementsRequestMultiError(errors)
	}

	return nil
}

// GetDataElementsRequestMultiError is an error wrapping multiple validation
// errors returned by GetDataElementsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetDataElementsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDataElementsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDataElementsRequestMultiError) AllErrors() []error { return m }

// GetDataElementsRequestValidationError is the validation error returned by
// GetDataElementsRequest.Validate if the designated constraints aren't met.
type GetDataElementsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDataElementsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDataElementsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDataElementsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDataElementsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDataElementsRequestValidationError) ErrorName() string {
	return "GetDataElementsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDataElementsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDataElementsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDataElementsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDataElementsRequestValidationError{}

var _GetDataElementsRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on GetDataElementsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDataElementsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDataElementsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDataElementsReplyMultiError, or nil if none found.
func (m *GetDataElementsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDataElementsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetElements() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetDataElementsReplyValidationError{
						field:  fmt.Sprintf("Elements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetDataElementsReplyValidationError{
						field:  fmt.Sprintf("Elements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetDataElementsReplyValidationError{
					field:  fmt.Sprintf("Elements[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetDataElementsReplyMultiError(errors)
	}

	return nil
}

// GetDataElementsReplyMultiError is an error wrapping multiple validation
// errors returned by GetDataElementsReply.ValidateAll() if the designated
// constraints aren't met.
type GetDataElementsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDataElementsReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDataElementsReplyMultiError) AllErrors() []error { return m }

// GetDataElementsReplyValidationError is the validation error returned by
// GetDataElementsReply.Validate if the designated constraints aren't met.
type GetDataElementsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDataElementsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDataElementsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDataElementsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDataElementsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDataElementsReplyValidationError) ErrorName() string {
	return "GetDataElementsReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetDataElementsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDataElementsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDataElementsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDataElementsReplyValidationError{}

// Validate checks the field values on FindDataElementsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FindDataElementsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FindDataElementsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FindDataElementsRequestMultiError, or nil if none found.
func (m *FindDataElementsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FindDataElementsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_FindDataElementsRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := FindDataElementsRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetStartIdx() < 0 {
		err := FindDataElementsRequestValidationError{
			field:  "StartIdx",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Count

	// no validation rules for NamePattern

	if len(errors) > 0 {
		return FindDataElementsRequestMultiError(errors)
	}

	return nil
}

// FindDataElementsRequestMultiError is an error wrapping multiple validation
// errors returned by FindDataElementsRequest.ValidateAll() if the designated
// constraints aren't met.
type FindDataElementsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FindDataElementsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FindDataElementsRequestMultiError) AllErrors() []error { return m }

// FindDataElementsRequestValidationError is the validation error returned by
// FindDataElementsRequest.Validate if the designated constraints aren't met.
type FindDataElementsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FindDataElementsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FindDataElementsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FindDataElementsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FindDataElementsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FindDataElementsRequestValidationError) ErrorName() string {
	return "FindDataElementsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FindDataElementsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFindDataElementsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FindDataElementsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FindDataElementsRequestValidationError{}

var _FindDataElementsRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on GetDataMetaRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDataMetaRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDataMetaRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDataMetaRequestMultiError, or nil if none found.
func (m *GetDataMetaRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDataMetaRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetDataMetaRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := GetDataMetaRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for WithMetafiles

	if len(errors) > 0 {
		return GetDataMetaRequestMultiError(errors)
	}

	return nil
}

// GetDataMetaRequestMultiError is an error wrapping multiple validation errors
// returned by GetDataMetaRequest.ValidateAll() if the designated constraints
// aren't met.
type GetDataMetaRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDataMetaRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDataMetaRequestMultiError) AllErrors() []error { return m }

// GetDataMetaRequestValidationError is the validation error returned by
// GetDataMetaRequest.Validate if the designated constraints aren't met.
type GetDataMetaRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDataMetaRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDataMetaRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDataMetaRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDataMetaRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDataMetaRequestValidationError) ErrorName() string {
	return "GetDataMetaRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDataMetaRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDataMetaRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDataMetaRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDataMetaRequestValidationError{}

var _GetDataMetaRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on GetDataMetaReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetDataMetaReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDataMetaReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDataMetaReplyMultiError, or nil if none found.
func (m *GetDataMetaReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDataMetaReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Metadata

	if all {
		switch v := interface{}(m.GetMetafiles()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDataMetaReplyValidationError{
					field:  "Metafiles",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDataMetaReplyValidationError{
					field:  "Metafiles",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetafiles()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDataMetaReplyValidationError{
				field:  "Metafiles",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetDataMetaReplyMultiError(errors)
	}

	return nil
}

// GetDataMetaReplyMultiError is an error wrapping multiple validation errors
// returned by GetDataMetaReply.ValidateAll() if the designated constraints
// aren't met.
type GetDataMetaReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDataMetaReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDataMetaReplyMultiError) AllErrors() []error { return m }

// GetDataMetaReplyValidationError is the validation error returned by
// GetDataMetaReply.Validate if the designated constraints aren't met.
type GetDataMetaReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDataMetaReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDataMetaReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDataMetaReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDataMetaReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDataMetaReplyValidationError) ErrorName() string { return "GetDataMetaReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetDataMetaReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDataMetaReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDataMetaReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDataMetaReplyValidationError{}

// Validate checks the field values on SetRawdataEmbeddingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetRawdataEmbeddingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetRawdataEmbeddingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetRawdataEmbeddingRequestMultiError, or nil if none found.
func (m *SetRawdataEmbeddingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetRawdataEmbeddingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_SetRawdataEmbeddingRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := SetRawdataEmbeddingRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetRawdataName()) < 1 {
		err := SetRawdataEmbeddingRequestValidationError{
			field:  "RawdataName",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, err := url.Parse(m.GetEmbeddingUri()); err != nil {
		err = SetRawdataEmbeddingRequestValidationError{
			field:  "EmbeddingUri",
			reason: "value must be a valid URI",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SetRawdataEmbeddingRequestMultiError(errors)
	}

	return nil
}

// SetRawdataEmbeddingRequestMultiError is an error wrapping multiple
// validation errors returned by SetRawdataEmbeddingRequest.ValidateAll() if
// the designated constraints aren't met.
type SetRawdataEmbeddingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetRawdataEmbeddingRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetRawdataEmbeddingRequestMultiError) AllErrors() []error { return m }

// SetRawdataEmbeddingRequestValidationError is the validation error returned
// by SetRawdataEmbeddingRequest.Validate if the designated constraints aren't met.
type SetRawdataEmbeddingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetRawdataEmbeddingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetRawdataEmbeddingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetRawdataEmbeddingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetRawdataEmbeddingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetRawdataEmbeddingRequestValidationError) ErrorName() string {
	return "SetRawdataEmbeddingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SetRawdataEmbeddingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetRawdataEmbeddingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetRawdataEmbeddingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetRawdataEmbeddingRequestValidationError{}

var _SetRawdataEmbeddingRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on SetRawdataEmbeddingReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetRawdataEmbeddingReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetRawdataEmbeddingReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetRawdataEmbeddingReplyMultiError, or nil if none found.
func (m *SetRawdataEmbeddingReply) ValidateAll() error {
	return m.validate(true)
}

func (m *SetRawdataEmbeddingReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EmbeddingUrl

	if len(errors) > 0 {
		return SetRawdataEmbeddingReplyMultiError(errors)
	}

	return nil
}

// SetRawdataEmbeddingReplyMultiError is an error wrapping multiple validation
// errors returned by SetRawdataEmbeddingReply.ValidateAll() if the designated
// constraints aren't met.
type SetRawdataEmbeddingReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetRawdataEmbeddingReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetRawdataEmbeddingReplyMultiError) AllErrors() []error { return m }

// SetRawdataEmbeddingReplyValidationError is the validation error returned by
// SetRawdataEmbeddingReply.Validate if the designated constraints aren't met.
type SetRawdataEmbeddingReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetRawdataEmbeddingReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetRawdataEmbeddingReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetRawdataEmbeddingReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetRawdataEmbeddingReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetRawdataEmbeddingReplyValidationError) ErrorName() string {
	return "SetRawdataEmbeddingReplyValidationError"
}

// Error satisfies the builtin error interface
func (e SetRawdataEmbeddingReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetRawdataEmbeddingReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetRawdataEmbeddingReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetRawdataEmbeddingReplyValidationError{}

// Validate checks the field values on ParseDataRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ParseDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ParseDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ParseDataRequestMultiError, or nil if none found.
func (m *ParseDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ParseDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ParseDataRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := ParseDataRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := ParseDataRequest_Option_name[int32(m.GetOption())]; !ok {
		err := ParseDataRequestValidationError{
			field:  "Option",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ParseDataRequestMultiError(errors)
	}

	return nil
}

// ParseDataRequestMultiError is an error wrapping multiple validation errors
// returned by ParseDataRequest.ValidateAll() if the designated constraints
// aren't met.
type ParseDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ParseDataRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ParseDataRequestMultiError) AllErrors() []error { return m }

// ParseDataRequestValidationError is the validation error returned by
// ParseDataRequest.Validate if the designated constraints aren't met.
type ParseDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ParseDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ParseDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ParseDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ParseDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ParseDataRequestValidationError) ErrorName() string { return "ParseDataRequestValidationError" }

// Error satisfies the builtin error interface
func (e ParseDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sParseDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ParseDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ParseDataRequestValidationError{}

var _ParseDataRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on Data_State with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Data_State) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Data_State with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Data_StateMultiError, or
// nil if none found.
func (m *Data_State) ValidateAll() error {
	return m.validate(true)
}

func (m *Data_State) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Data_StateMultiError(errors)
	}

	return nil
}

// Data_StateMultiError is an error wrapping multiple validation errors
// returned by Data_State.ValidateAll() if the designated constraints aren't met.
type Data_StateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Data_StateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Data_StateMultiError) AllErrors() []error { return m }

// Data_StateValidationError is the validation error returned by
// Data_State.Validate if the designated constraints aren't met.
type Data_StateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Data_StateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Data_StateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Data_StateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Data_StateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Data_StateValidationError) ErrorName() string { return "Data_StateValidationError" }

// Error satisfies the builtin error interface
func (e Data_StateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sData_State.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Data_StateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Data_StateValidationError{}
