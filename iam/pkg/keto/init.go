package keto

var (
	defcli *Keto
	defam  *AccessMgr
)

func Init(cfg *Config) {
	var err error
	defcli, err = NewKeto(cfg)
	if err != nil {
		panic(err)
	}
	defam = NewAccessMgr(defcli)
}

func DefaultKeto() *Keto           { return defcli }
func DefaultAccessMgr() *AccessMgr { return defam }

type AccessMgr struct {
	kt *Keto
}

func NewAccessMgr(kt *Keto) *AccessMgr {
	if kt == nil {
		kt = defcli
	}
	return &AccessMgr{kt: kt}
}

func (o *AccessMgr) Keto() *Keto { return o.kt }
