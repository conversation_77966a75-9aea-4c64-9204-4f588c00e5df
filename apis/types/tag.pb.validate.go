// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: types/tag.proto

package types

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TagRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TagRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TagRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TagRequestMultiError, or
// nil if none found.
func (m *TagRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TagRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return TagRequestMultiError(errors)
	}

	return nil
}

// TagRequestMultiError is an error wrapping multiple validation errors
// returned by TagRequest.ValidateAll() if the designated constraints aren't met.
type TagRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TagRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TagRequestMultiError) AllErrors() []error { return m }

// TagRequestValidationError is the validation error returned by
// TagRequest.Validate if the designated constraints aren't met.
type TagRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TagRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TagRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TagRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TagRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TagRequestValidationError) ErrorName() string { return "TagRequestValidationError" }

// Error satisfies the builtin error interface
func (e TagRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTagRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TagRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TagRequestValidationError{}

// Validate checks the field values on TagList with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TagList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TagList with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TagListMultiError, or nil if none found.
func (m *TagList) ValidateAll() error {
	return m.validate(true)
}

func (m *TagList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TagListMultiError(errors)
	}

	return nil
}

// TagListMultiError is an error wrapping multiple validation errors returned
// by TagList.ValidateAll() if the designated constraints aren't met.
type TagListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TagListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TagListMultiError) AllErrors() []error { return m }

// TagListValidationError is the validation error returned by TagList.Validate
// if the designated constraints aren't met.
type TagListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TagListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TagListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TagListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TagListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TagListValidationError) ErrorName() string { return "TagListValidationError" }

// Error satisfies the builtin error interface
func (e TagListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTagList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TagListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TagListValidationError{}
