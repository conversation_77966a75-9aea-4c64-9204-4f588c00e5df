//以下为一帧数据的标注结果文件，标注结果数量与帧数一一对应
//更新时间：2023年4月20日
{
  "fileUrl": "",
  //文件地址，为相对路径，如：data/001.png
  "subNote": [
    "str",
    "str"
  ],
  //文件注释，说明是纯相机数据/纯点云数据(为空值)，或者是融合数据(如"subNote":["CameraMMT","LidarMMT"])
  //所有目标的标注信息：
  "annotations": [
    //2D框的标注信息格式
    {
      "markType": "2Dbox",
      //标注类型：2D框
      "label": "str",
      //该目标的注释标签。下同
      "region": [
        "x",
        "y",
        "w",
        "h"
      ],
      //x为2D框中心点的x轴，y为2D框中心点的y轴，w为2D框的宽，h为2D框的高。数据类型为float
      "attributes": [
        [
          "str",
          "str"
        ],
        //第一类属性：类别，名称，如天气--晴天属性：["weather","sunny"]。下同
        [
          "str",
          "str"
        ]
        //第二类属性：类别，名称，如颜色--红色属性：["color","red"]。无属性则为"attributes":[]。下同
      ],
      "subNote": [],
      //该帧数据的注释，纯图像或点云为空值，融合数据与第3行的信息保持一致，将标注结果与文件关联起来。下同
      "groupId": null//所属组的ID
      ，
      一般为int
      ，
      通过组ID可将同一个目标的不同标注类型关联起来
      ，
      若无分组信息则赋空值
      "groupId": null
      。
      下同
    },



    //2Dkeypoints/2Dpolyline/2Dpolygon的标注信息格式
    {
      "markType": "2Dkeypoints/2Dpolyline/2Dpolygon",
      //标注类型为关键点/折线/多边形
      "label": "str",
      //参考2D框
      "region": [
        [
          "x1",
          "y1"
        ],
        //2D关键点2Dkeypoints/2D折线2Dpolyline/2D多边形2Dpolygon第1个点的xy轴坐标。数据类型为float
        [
          "x2",
          "y2"
        ],
        //2D关键点2Dkeypoints/2D折线2Dpolyline/2D多边形2Dpolygon第2个点的xy轴坐标。数据类型为float
        [
          "xn",
          "yn"
        ]
        //2D关键点2Dkeypoints/2D折线2Dpolyline/2D多边形2Dpolygon第n个点的xy轴坐标。数据类型为float
      ],
      "attributes": [],
      //参考2D框
      "subNote": [],
      //参考2D框
      "groupId": null//参考2D框
    },


    //2D旋转框的标注信息格式
    {
      "markType": "str",
      //标注类型：2Drobox
      "label": "str",
      //参考2D框
      "region": [
        "x",
        "y",
        "w",
        "h",
        "a"
      ],
      //x为2D旋转框中心点的x轴，y为2D旋转框中心点的y轴，w为2D旋转框的宽，h为2D旋转框的高，a为2D旋转框的旋转角度，水平方向angle=0，顺时针方向旋转，得到的角度值是正值，旋转一周为pi，没有负值。数据类型为float
      "attributes": [],
      //参考2D框
      "subNote": [],
      //参考2D框
      "groupId": null
      //参考2D框
    },

    //点云3D框的标注信息格式
    {
      "markType": "3Dbox",
      //标注类似：3D框
      "label": "str",
      //参考2D框
      "region": [
        "x",
        "y",
        "z",
        "l",
        "w",
        "h",
        "r"
      ],
      //x为3D框中心点的x轴，y为3D框中心点的y轴，z为3D框中心点的z轴，l为3D框的长，w为3D框的宽，h为3D框的高，r为3D框的旋转角度(X轴右侧为90度，逆时针旋转90度到Y轴为0度；左侧为-90度，逆时针旋转90度到Y轴为正负180度)。数据类型为float
      "attributes": [],
      //参考2D框
      "subNote": [],
      //参考2D框
      "groupId": null
      //参考2D框
    }
  ]
}


