package service

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"

	"anno/api/client"
	"anno/internal/biz"

	"github.com/google/wire"
	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/pkg/data"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/errors"
)

// ProviderSet is service providers.
var ProviderSet = wire.NewSet(NewProjectsService, NewLabelwidgetsService, NewJobsService, NewLotsService,
	NewSkillsService, NewRolesService, NewLottplsService, NewLabelclzService, NewConfigsService, NewOrdersService,
	NewBizgrantsService, NewSpecgrantsService)

func CheckUpdateFlds(model any, flds []string, allowedSnakecase ...string) ([]string, error) {
	modelName := data.TypeName(model)
	flds = field.Snakecase(flds...)
	for _, fld := range flds {
		if !lo.Contains(allowedSnakecase, fld) {
			return nil, errors.NewErrUnsupportedField(errors.WithModel(modelName), errors.WithFields(fld))
		}
	}
	return flds, nil
}

func getListScope(ctx context.Context, orgUid, creatorUid string) (creator, scope string) {
	fmt.Printf("---> creatorUid: %s, orgUid: %s\n", creatorUid, orgUid)
	if creatorUid != "" {
		return creatorUid, client.UserScope(creatorUid)
	}
	if orgUid != "" {
		return creatorUid, client.GroupScope(orgUid)
	}

	op := biz.UserFromCtx(ctx)
	spew.Dump("---> user from ctx: - getListScope: ", op.GetRole())
	if client.IsPrivileged(op.GetRole()) {
		return
	}
	return op.GetUid(), client.UserScope(op.GetUid())
}
