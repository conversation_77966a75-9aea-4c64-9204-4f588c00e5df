// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: annostat/v1/stats.proto

package annostat

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Stats_GetOrderCount_FullMethodName        = "/annostat.v1.Stats/GetOrderCount"
	Stats_GetOrderConversion_FullMethodName   = "/annostat.v1.Stats/GetOrderConversion"
	Stats_GetLotCount_FullMethodName          = "/annostat.v1.Stats/GetLotCount"
	Stats_GetOngoingLots_FullMethodName       = "/annostat.v1.Stats/GetOngoingLots"
	Stats_GetLotStatus_FullMethodName         = "/annostat.v1.Stats/GetLotStatus"
	Stats_GetLotLabelStat_FullMethodName      = "/annostat.v1.Stats/GetLotLabelStat"
	Stats_GetLotStatByExecutor_FullMethodName = "/annostat.v1.Stats/GetLotStatByExecutor"
	Stats_ProductionByTime_FullMethodName     = "/annostat.v1.Stats/ProductionByTime"
)

// StatsClient is the client API for Stats service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StatsClient interface {
	// count orders
	GetOrderCount(ctx context.Context, in *GetOrderCountRequest, opts ...grpc.CallOption) (*GetOrderCountReply, error)
	// order-to-lot conversion statistics
	GetOrderConversion(ctx context.Context, in *GetOrderCountRequest, opts ...grpc.CallOption) (*GetOrderConversionReply, error)
	// count lots
	GetLotCount(ctx context.Context, in *GetLotCountRequest, opts ...grpc.CallOption) (*GetLotCountReply, error)
	// get ongoing lots
	GetOngoingLots(ctx context.Context, in *GetOngoingLotsRequest, opts ...grpc.CallOption) (*GetOngoingLotsReply, error)
	// lot statistics by phase
	GetLotStatus(ctx context.Context, in *GetLotStatusRequest, opts ...grpc.CallOption) (*GetLotStatusReply, error)
	// lot statistics by label
	GetLotLabelStat(ctx context.Context, in *GetLotStatusRequest, opts ...grpc.CallOption) (*GetLotLabelStatReply, error)
	// lot statistics by executor
	GetLotStatByExecutor(ctx context.Context, in *GetLotStatByExecutorRequest, opts ...grpc.CallOption) (*GetLotStatByExecutorReply, error)
	// get production by time
	ProductionByTime(ctx context.Context, in *ProductionByTimeRequest, opts ...grpc.CallOption) (*ProductionByTimeReply, error)
}

type statsClient struct {
	cc grpc.ClientConnInterface
}

func NewStatsClient(cc grpc.ClientConnInterface) StatsClient {
	return &statsClient{cc}
}

func (c *statsClient) GetOrderCount(ctx context.Context, in *GetOrderCountRequest, opts ...grpc.CallOption) (*GetOrderCountReply, error) {
	out := new(GetOrderCountReply)
	err := c.cc.Invoke(ctx, Stats_GetOrderCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statsClient) GetOrderConversion(ctx context.Context, in *GetOrderCountRequest, opts ...grpc.CallOption) (*GetOrderConversionReply, error) {
	out := new(GetOrderConversionReply)
	err := c.cc.Invoke(ctx, Stats_GetOrderConversion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statsClient) GetLotCount(ctx context.Context, in *GetLotCountRequest, opts ...grpc.CallOption) (*GetLotCountReply, error) {
	out := new(GetLotCountReply)
	err := c.cc.Invoke(ctx, Stats_GetLotCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statsClient) GetOngoingLots(ctx context.Context, in *GetOngoingLotsRequest, opts ...grpc.CallOption) (*GetOngoingLotsReply, error) {
	out := new(GetOngoingLotsReply)
	err := c.cc.Invoke(ctx, Stats_GetOngoingLots_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statsClient) GetLotStatus(ctx context.Context, in *GetLotStatusRequest, opts ...grpc.CallOption) (*GetLotStatusReply, error) {
	out := new(GetLotStatusReply)
	err := c.cc.Invoke(ctx, Stats_GetLotStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statsClient) GetLotLabelStat(ctx context.Context, in *GetLotStatusRequest, opts ...grpc.CallOption) (*GetLotLabelStatReply, error) {
	out := new(GetLotLabelStatReply)
	err := c.cc.Invoke(ctx, Stats_GetLotLabelStat_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statsClient) GetLotStatByExecutor(ctx context.Context, in *GetLotStatByExecutorRequest, opts ...grpc.CallOption) (*GetLotStatByExecutorReply, error) {
	out := new(GetLotStatByExecutorReply)
	err := c.cc.Invoke(ctx, Stats_GetLotStatByExecutor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statsClient) ProductionByTime(ctx context.Context, in *ProductionByTimeRequest, opts ...grpc.CallOption) (*ProductionByTimeReply, error) {
	out := new(ProductionByTimeReply)
	err := c.cc.Invoke(ctx, Stats_ProductionByTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StatsServer is the server API for Stats service.
// All implementations must embed UnimplementedStatsServer
// for forward compatibility
type StatsServer interface {
	// count orders
	GetOrderCount(context.Context, *GetOrderCountRequest) (*GetOrderCountReply, error)
	// order-to-lot conversion statistics
	GetOrderConversion(context.Context, *GetOrderCountRequest) (*GetOrderConversionReply, error)
	// count lots
	GetLotCount(context.Context, *GetLotCountRequest) (*GetLotCountReply, error)
	// get ongoing lots
	GetOngoingLots(context.Context, *GetOngoingLotsRequest) (*GetOngoingLotsReply, error)
	// lot statistics by phase
	GetLotStatus(context.Context, *GetLotStatusRequest) (*GetLotStatusReply, error)
	// lot statistics by label
	GetLotLabelStat(context.Context, *GetLotStatusRequest) (*GetLotLabelStatReply, error)
	// lot statistics by executor
	GetLotStatByExecutor(context.Context, *GetLotStatByExecutorRequest) (*GetLotStatByExecutorReply, error)
	// get production by time
	ProductionByTime(context.Context, *ProductionByTimeRequest) (*ProductionByTimeReply, error)
	mustEmbedUnimplementedStatsServer()
}

// UnimplementedStatsServer must be embedded to have forward compatible implementations.
type UnimplementedStatsServer struct {
}

func (UnimplementedStatsServer) GetOrderCount(context.Context, *GetOrderCountRequest) (*GetOrderCountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderCount not implemented")
}
func (UnimplementedStatsServer) GetOrderConversion(context.Context, *GetOrderCountRequest) (*GetOrderConversionReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderConversion not implemented")
}
func (UnimplementedStatsServer) GetLotCount(context.Context, *GetLotCountRequest) (*GetLotCountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLotCount not implemented")
}
func (UnimplementedStatsServer) GetOngoingLots(context.Context, *GetOngoingLotsRequest) (*GetOngoingLotsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOngoingLots not implemented")
}
func (UnimplementedStatsServer) GetLotStatus(context.Context, *GetLotStatusRequest) (*GetLotStatusReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLotStatus not implemented")
}
func (UnimplementedStatsServer) GetLotLabelStat(context.Context, *GetLotStatusRequest) (*GetLotLabelStatReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLotLabelStat not implemented")
}
func (UnimplementedStatsServer) GetLotStatByExecutor(context.Context, *GetLotStatByExecutorRequest) (*GetLotStatByExecutorReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLotStatByExecutor not implemented")
}
func (UnimplementedStatsServer) ProductionByTime(context.Context, *ProductionByTimeRequest) (*ProductionByTimeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProductionByTime not implemented")
}
func (UnimplementedStatsServer) mustEmbedUnimplementedStatsServer() {}

// UnsafeStatsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StatsServer will
// result in compilation errors.
type UnsafeStatsServer interface {
	mustEmbedUnimplementedStatsServer()
}

func RegisterStatsServer(s grpc.ServiceRegistrar, srv StatsServer) {
	s.RegisterService(&Stats_ServiceDesc, srv)
}

func _Stats_GetOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatsServer).GetOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Stats_GetOrderCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatsServer).GetOrderCount(ctx, req.(*GetOrderCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Stats_GetOrderConversion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatsServer).GetOrderConversion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Stats_GetOrderConversion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatsServer).GetOrderConversion(ctx, req.(*GetOrderCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Stats_GetLotCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLotCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatsServer).GetLotCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Stats_GetLotCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatsServer).GetLotCount(ctx, req.(*GetLotCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Stats_GetOngoingLots_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOngoingLotsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatsServer).GetOngoingLots(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Stats_GetOngoingLots_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatsServer).GetOngoingLots(ctx, req.(*GetOngoingLotsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Stats_GetLotStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLotStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatsServer).GetLotStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Stats_GetLotStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatsServer).GetLotStatus(ctx, req.(*GetLotStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Stats_GetLotLabelStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLotStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatsServer).GetLotLabelStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Stats_GetLotLabelStat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatsServer).GetLotLabelStat(ctx, req.(*GetLotStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Stats_GetLotStatByExecutor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLotStatByExecutorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatsServer).GetLotStatByExecutor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Stats_GetLotStatByExecutor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatsServer).GetLotStatByExecutor(ctx, req.(*GetLotStatByExecutorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Stats_ProductionByTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProductionByTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatsServer).ProductionByTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Stats_ProductionByTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatsServer).ProductionByTime(ctx, req.(*ProductionByTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Stats_ServiceDesc is the grpc.ServiceDesc for Stats service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Stats_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "annostat.v1.Stats",
	HandlerType: (*StatsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetOrderCount",
			Handler:    _Stats_GetOrderCount_Handler,
		},
		{
			MethodName: "GetOrderConversion",
			Handler:    _Stats_GetOrderConversion_Handler,
		},
		{
			MethodName: "GetLotCount",
			Handler:    _Stats_GetLotCount_Handler,
		},
		{
			MethodName: "GetOngoingLots",
			Handler:    _Stats_GetOngoingLots_Handler,
		},
		{
			MethodName: "GetLotStatus",
			Handler:    _Stats_GetLotStatus_Handler,
		},
		{
			MethodName: "GetLotLabelStat",
			Handler:    _Stats_GetLotLabelStat_Handler,
		},
		{
			MethodName: "GetLotStatByExecutor",
			Handler:    _Stats_GetLotStatByExecutor_Handler,
		},
		{
			MethodName: "ProductionByTime",
			Handler:    _Stats_ProductionByTime_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "annostat/v1/stats.proto",
}
