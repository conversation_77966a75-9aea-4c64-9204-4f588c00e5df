// source: anno/v1/lottpl.proto
package biz

import (
	"context"
	"time"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type Lottpl struct {
	ID int64 `json:"id" gorm:"default:null"`
	// Uid           string         `json:"uid" gorm:"default:null"`
	Name        string     `json:"name" gorm:"default:null"`
	Desc        string     `json:"desc" gorm:"default:null"`
	Type        string     `json:"type" gorm:"default:null"`
	JobSize     int32      `json:"job_size" gorm:"default:null"`
	Ontologies  Ontologies `json:"ontologies" gorm:"default:null"`
	Phases      Phases     `json:"phases" gorm:"default:null"`
	Out         OutConfig  `json:"out" gorm:"default:null"`
	Instruction string     `json:"instruction" gorm:"default:null"`
	CreatorUid  string     `json:"creator_uid" gorm:"default:null"`
	OrgUid      string     `json:"org_uid" gorm:"default:null"`
	UpdatedAt   time.Time  `json:"updated_at" gorm:"default:null"`
	CreatedAt   time.Time  `json:"created_at" gorm:"default:null"`
	DeletedAt   DeletedAt  `json:"deleted_at" gorm:"default:null"`
	// RejectReasons RejectReasons  `json:"reject_reasons" gorm:"default:null"`
}

func (o *Lottpl) GetID() int64 { return o.ID }
func (o *Lottpl) EnsureID() {
	if o.ID == 0 {
		o.ID = kid.NewID()
	}
}
func (o *Lottpl) GetUid() string { return kid.StringID(o.ID) }
func (o *Lottpl) UidFld() string { panic("code error") }

const LottplTableName = "lottpls"

var (
	Lottpl_             = field.RegObject(&Lottpl{})
	LottplUpdatableFlds = field.NewModel(LottplTableName, Lottpl_)

	LottplSfldName        = field.Sname(&Lottpl_.Name)
	LottplSfldDesc        = field.Sname(&Lottpl_.Desc)
	LottplSfldType        = field.Sname(&Lottpl_.Type)
	LottplSfldJobSize     = field.Sname(&Lottpl_.JobSize)
	LottplSfldOntologies  = field.Sname(&Lottpl_.Ontologies)
	LottplSfldPhases      = field.Sname(&Lottpl_.Phases)
	LottplSfldOut         = field.Sname(&Lottpl_.Out)
	LottplSfldInstruction = field.Sname(&Lottpl_.Instruction)
	LottplSfldCreatorUid  = field.Sname(&Lottpl_.CreatorUid)
	LottplSfldOrgUid      = field.Sname(&Lottpl_.OrgUid)
	// LottplSfldRejectReasons = field.Sname(&Lottpl_.RejectReasons)
)

type LottplListFilter struct {
	IDs         []int64
	OrgUid      string
	CreatorUid  string
	NamePattern string
	Type        string
}

type LottplsRepo interface {
	Create(context.Context, *Lottpl) (*Lottpl, error)
	Update(context.Context, *Lottpl, *FieldMask) (*Lottpl, error)
	GetByID(context.Context, int64) (*Lottpl, error)
	DeleteByID(context.Context, int64) error
	List(context.Context, *LottplListFilter, Pager) ([]*Lottpl, error)
	Count(context.Context, *LottplListFilter) (int, error)
}

type LottplsBiz struct {
	repo LottplsRepo
	log  *log.Helper
}

func NewLottplsBiz(repo LottplsRepo, logger log.Logger) *LottplsBiz {
	return &LottplsBiz{repo: repo, log: log.NewHelper(logger)}
}

func (o *LottplsBiz) Create(ctx context.Context, p *Lottpl) (*Lottpl, error) {
	o.log.Info(ctx, "CreateLottpl", "param", p)
	return o.repo.Create(ctx, p)
}

func (o *LottplsBiz) Update(ctx context.Context, p *Lottpl, fldMask *FieldMask) (*Lottpl, error) {
	o.log.Info(ctx, "UpdateLottpl", "param", p)
	return o.repo.Update(ctx, p, fldMask)
}

func (o *LottplsBiz) GetByID(ctx context.Context, id int64) (*Lottpl, error) {
	return o.repo.GetByID(ctx, id)
}

func (o *LottplsBiz) GetByUid(ctx context.Context, uid string) (*Lottpl, error) {
	return o.GetByID(ctx, kid.ParseID(uid))
}

func (o *LottplsBiz) DeleteByID(ctx context.Context, id int64) error {
	o.log.Info(ctx, "DeleteByIDLottpl", "id", id)
	return o.repo.DeleteByID(ctx, id)
}

func (o *LottplsBiz) DeleteByUid(ctx context.Context, uid string) error {
	o.log.Info(ctx, "DeleteByUidLottpl", "uid", uid)
	return o.DeleteByID(ctx, kid.ParseID(uid))
}

func (o *LottplsBiz) List(ctx context.Context, filter *LottplListFilter, pager Pager) ([]*Lottpl, error) {
	o.log.Info(ctx, "ListLottpl", "param", filter)
	return o.repo.List(ctx, filter, pager)
}

func (o *LottplsBiz) Count(ctx context.Context, filter *LottplListFilter) (int, error) {
	return o.repo.Count(ctx, filter)
}
