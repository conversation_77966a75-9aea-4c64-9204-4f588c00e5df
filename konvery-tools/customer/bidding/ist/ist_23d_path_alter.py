import json
import os
import shutil
import sys
import pandas as pd

sys.path.append('.')
from customer.common import tools


def read_csv(csv_file):
    with open(csv_file, 'rb') as f:
        _ = f.readline()
        df = pd.read_csv(f, header=None)
    df = df[df.columns[1:]]
    return df


def construct_config(output_seg_dir, param_dir):
    config = {
        "camera": {},
        "data_type": "fusion_pointcloud",
        "sensor_params": {}
    }
    file = tools.get_file_by_extension(param_dir, '.cfg')[0]
    with open(file) as f:
        params = json.load(f)['cameras']
    for param in params:
        camera_name = param['cameraId']
        config['camera'][camera_name] = param['cameraName']
        distortion = param['distortion'][0]
        extrinsic = param['extrinsics']
        intrinsic = param['intrinsics']
        camera_json = {
            "camera_model": "pinhole",
            "extrinsic": extrinsic,
            "fx": intrinsic[0][0],
            "fy": intrinsic[1][1],
            "cx": intrinsic[0][2],
            "cy": intrinsic[1][2],
            "k1": distortion['K1'],
            "k2": distortion['K2'],
            "p1": distortion['P1'],
            "p2": distortion['P2'],
            "k3": distortion['K3'],
            "k4": distortion['K4'],
            "k5": distortion['K5'],
            "k6": distortion['K6']
        }
        config["sensor_params"][camera_name] = camera_json
    with open(os.path.join(output_seg_dir, 'config.json'), 'w') as f:
        json.dump(config, f, indent=4)


def run(input_dir, outptu_dir):
    tools.check_dir(outptu_dir)
    for seg in tools.listdir(input_dir):
        lidar_out = os.path.join(outptu_dir, seg, 'lidar')
        tools.check_dir(lidar_out)
        for i in range(1, 7):
            camera_out = os.path.join(outptu_dir, seg, 'camera', f'camera{i}')
            tools.check_dir(camera_out)
        params_df = read_csv(os.path.join(input_dir, seg, '参数', '数据对应关系.csv'))
        construct_config(os.path.join(outptu_dir, seg), os.path.join(input_dir, seg, '参数'))
        for row in params_df.iterrows():
            lidar, cfg, *cameras = row[1]
            shutil.copy(os.path.join(input_dir, seg, '原始数据', lidar.replace('\\', os.sep)), lidar_out)
            for idx, camera in enumerate(cameras, start=1):
                camera_out = os.path.join(outptu_dir, seg, 'camera', f'camera{idx}', lidar.split('\\')[-1].replace('.pcd', '.jpg'))
                shutil.copyfile(os.path.join(input_dir, seg, '原始数据', camera.replace('\\', os.sep)), camera_out)


if __name__ == '__main__':
    args = tools.get_args()
    run(args.input, args.out)
