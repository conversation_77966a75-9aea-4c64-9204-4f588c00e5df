// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: annofeed/v1/param.proto

package annofeed

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CoordSys with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CoordSys) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CoordSys with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CoordSysMultiError, or nil
// if none found.
func (m *CoordSys) ValidateAll() error {
	return m.validate(true)
}

func (m *CoordSys) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if _, ok := CoordSys_Type_Enum_name[int32(m.GetType())]; !ok {
		err := CoordSysValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Parent

	if len(errors) > 0 {
		return CoordSysMultiError(errors)
	}

	return nil
}

// CoordSysMultiError is an error wrapping multiple validation errors returned
// by CoordSys.ValidateAll() if the designated constraints aren't met.
type CoordSysMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CoordSysMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CoordSysMultiError) AllErrors() []error { return m }

// CoordSysValidationError is the validation error returned by
// CoordSys.Validate if the designated constraints aren't met.
type CoordSysValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CoordSysValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CoordSysValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CoordSysValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CoordSysValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CoordSysValidationError) ErrorName() string { return "CoordSysValidationError" }

// Error satisfies the builtin error interface
func (e CoordSysValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCoordSys.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CoordSysValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CoordSysValidationError{}

// Validate checks the field values on Pose with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Pose) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Pose with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PoseMultiError, or nil if none found.
func (m *Pose) ValidateAll() error {
	return m.validate(true)
}

func (m *Pose) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CoordSys

	if len(errors) > 0 {
		return PoseMultiError(errors)
	}

	return nil
}

// PoseMultiError is an error wrapping multiple validation errors returned by
// Pose.ValidateAll() if the designated constraints aren't met.
type PoseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PoseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PoseMultiError) AllErrors() []error { return m }

// PoseValidationError is the validation error returned by Pose.Validate if the
// designated constraints aren't met.
type PoseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PoseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PoseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PoseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PoseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PoseValidationError) ErrorName() string { return "PoseValidationError" }

// Error satisfies the builtin error interface
func (e PoseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPose.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PoseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PoseValidationError{}

// Validate checks the field values on VehicleParam with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VehicleParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VehicleParam with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VehicleParamMultiError, or
// nil if none found.
func (m *VehicleParam) ValidateAll() error {
	return m.validate(true)
}

func (m *VehicleParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPose()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VehicleParamValidationError{
					field:  "Pose",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VehicleParamValidationError{
					field:  "Pose",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPose()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VehicleParamValidationError{
				field:  "Pose",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VehicleParamMultiError(errors)
	}

	return nil
}

// VehicleParamMultiError is an error wrapping multiple validation errors
// returned by VehicleParam.ValidateAll() if the designated constraints aren't met.
type VehicleParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VehicleParamMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VehicleParamMultiError) AllErrors() []error { return m }

// VehicleParamValidationError is the validation error returned by
// VehicleParam.Validate if the designated constraints aren't met.
type VehicleParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VehicleParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VehicleParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VehicleParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VehicleParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VehicleParamValidationError) ErrorName() string { return "VehicleParamValidationError" }

// Error satisfies the builtin error interface
func (e VehicleParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVehicleParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VehicleParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VehicleParamValidationError{}

// Validate checks the field values on LidarParam with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LidarParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LidarParam with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LidarParamMultiError, or
// nil if none found.
func (m *LidarParam) ValidateAll() error {
	return m.validate(true)
}

func (m *LidarParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPose()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LidarParamValidationError{
					field:  "Pose",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LidarParamValidationError{
					field:  "Pose",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPose()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LidarParamValidationError{
				field:  "Pose",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTransforms() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LidarParamValidationError{
						field:  fmt.Sprintf("Transforms[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LidarParamValidationError{
						field:  fmt.Sprintf("Transforms[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LidarParamValidationError{
					field:  fmt.Sprintf("Transforms[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Name

	if len(errors) > 0 {
		return LidarParamMultiError(errors)
	}

	return nil
}

// LidarParamMultiError is an error wrapping multiple validation errors
// returned by LidarParam.ValidateAll() if the designated constraints aren't met.
type LidarParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LidarParamMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LidarParamMultiError) AllErrors() []error { return m }

// LidarParamValidationError is the validation error returned by
// LidarParam.Validate if the designated constraints aren't met.
type LidarParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LidarParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LidarParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LidarParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LidarParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LidarParamValidationError) ErrorName() string { return "LidarParamValidationError" }

// Error satisfies the builtin error interface
func (e LidarParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLidarParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LidarParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LidarParamValidationError{}

// Validate checks the field values on CameraParam with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CameraParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CameraParam with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CameraParamMultiError, or
// nil if none found.
func (m *CameraParam) ValidateAll() error {
	return m.validate(true)
}

func (m *CameraParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPose()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CameraParamValidationError{
					field:  "Pose",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CameraParamValidationError{
					field:  "Pose",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPose()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CameraParamValidationError{
				field:  "Pose",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTransforms() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CameraParamValidationError{
						field:  fmt.Sprintf("Transforms[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CameraParamValidationError{
						field:  fmt.Sprintf("Transforms[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CameraParamValidationError{
					field:  fmt.Sprintf("Transforms[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Name

	if all {
		switch v := interface{}(m.GetDimensions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CameraParamValidationError{
					field:  "Dimensions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CameraParamValidationError{
					field:  "Dimensions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDimensions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CameraParamValidationError{
				field:  "Dimensions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Title

	if len(errors) > 0 {
		return CameraParamMultiError(errors)
	}

	return nil
}

// CameraParamMultiError is an error wrapping multiple validation errors
// returned by CameraParam.ValidateAll() if the designated constraints aren't met.
type CameraParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CameraParamMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CameraParamMultiError) AllErrors() []error { return m }

// CameraParamValidationError is the validation error returned by
// CameraParam.Validate if the designated constraints aren't met.
type CameraParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CameraParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CameraParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CameraParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CameraParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CameraParamValidationError) ErrorName() string { return "CameraParamValidationError" }

// Error satisfies the builtin error interface
func (e CameraParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCameraParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CameraParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CameraParamValidationError{}

// Validate checks the field values on ParamsFile with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ParamsFile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ParamsFile with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ParamsFileMultiError, or
// nil if none found.
func (m *ParamsFile) ValidateAll() error {
	return m.validate(true)
}

func (m *ParamsFile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetVehicle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ParamsFileValidationError{
					field:  "Vehicle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ParamsFileValidationError{
					field:  "Vehicle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVehicle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ParamsFileValidationError{
				field:  "Vehicle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetLidars()))
		i := 0
		for key := range m.GetLidars() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetLidars()[key]
			_ = val

			// no validation rules for Lidars[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ParamsFileValidationError{
							field:  fmt.Sprintf("Lidars[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ParamsFileValidationError{
							field:  fmt.Sprintf("Lidars[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ParamsFileValidationError{
						field:  fmt.Sprintf("Lidars[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	{
		sorted_keys := make([]string, len(m.GetCameras()))
		i := 0
		for key := range m.GetCameras() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetCameras()[key]
			_ = val

			// no validation rules for Cameras[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ParamsFileValidationError{
							field:  fmt.Sprintf("Cameras[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ParamsFileValidationError{
							field:  fmt.Sprintf("Cameras[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ParamsFileValidationError{
						field:  fmt.Sprintf("Cameras[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	{
		sorted_keys := make([]string, len(m.GetCoordSystems()))
		i := 0
		for key := range m.GetCoordSystems() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetCoordSystems()[key]
			_ = val

			// no validation rules for CoordSystems[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ParamsFileValidationError{
							field:  fmt.Sprintf("CoordSystems[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ParamsFileValidationError{
							field:  fmt.Sprintf("CoordSystems[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ParamsFileValidationError{
						field:  fmt.Sprintf("CoordSystems[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if all {
		switch v := interface{}(m.GetMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ParamsFileValidationError{
					field:  "Meta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ParamsFileValidationError{
					field:  "Meta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ParamsFileValidationError{
				field:  "Meta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ParamsFileMultiError(errors)
	}

	return nil
}

// ParamsFileMultiError is an error wrapping multiple validation errors
// returned by ParamsFile.ValidateAll() if the designated constraints aren't met.
type ParamsFileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ParamsFileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ParamsFileMultiError) AllErrors() []error { return m }

// ParamsFileValidationError is the validation error returned by
// ParamsFile.Validate if the designated constraints aren't met.
type ParamsFileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ParamsFileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ParamsFileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ParamsFileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ParamsFileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ParamsFileValidationError) ErrorName() string { return "ParamsFileValidationError" }

// Error satisfies the builtin error interface
func (e ParamsFileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sParamsFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ParamsFileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ParamsFileValidationError{}

// Validate checks the field values on LidarParamV2 with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LidarParamV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LidarParamV2 with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LidarParamV2MultiError, or
// nil if none found.
func (m *LidarParamV2) ValidateAll() error {
	return m.validate(true)
}

func (m *LidarParamV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Timestamp

	if len(errors) > 0 {
		return LidarParamV2MultiError(errors)
	}

	return nil
}

// LidarParamV2MultiError is an error wrapping multiple validation errors
// returned by LidarParamV2.ValidateAll() if the designated constraints aren't met.
type LidarParamV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LidarParamV2MultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LidarParamV2MultiError) AllErrors() []error { return m }

// LidarParamV2ValidationError is the validation error returned by
// LidarParamV2.Validate if the designated constraints aren't met.
type LidarParamV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LidarParamV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LidarParamV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LidarParamV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LidarParamV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LidarParamV2ValidationError) ErrorName() string { return "LidarParamV2ValidationError" }

// Error satisfies the builtin error interface
func (e LidarParamV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLidarParamV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LidarParamV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LidarParamV2ValidationError{}

// Validate checks the field values on CameraParamV2 with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CameraParamV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CameraParamV2 with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CameraParamV2MultiError, or
// nil if none found.
func (m *CameraParamV2) ValidateAll() error {
	return m.validate(true)
}

func (m *CameraParamV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Title

	if len(errors) > 0 {
		return CameraParamV2MultiError(errors)
	}

	return nil
}

// CameraParamV2MultiError is an error wrapping multiple validation errors
// returned by CameraParamV2.ValidateAll() if the designated constraints
// aren't met.
type CameraParamV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CameraParamV2MultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CameraParamV2MultiError) AllErrors() []error { return m }

// CameraParamV2ValidationError is the validation error returned by
// CameraParamV2.Validate if the designated constraints aren't met.
type CameraParamV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CameraParamV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CameraParamV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CameraParamV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CameraParamV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CameraParamV2ValidationError) ErrorName() string { return "CameraParamV2ValidationError" }

// Error satisfies the builtin error interface
func (e CameraParamV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCameraParamV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CameraParamV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CameraParamV2ValidationError{}

// Validate checks the field values on ParamFileMeta with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ParamFileMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ParamFileMeta with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ParamFileMetaMultiError, or
// nil if none found.
func (m *ParamFileMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *ParamFileMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	if len(errors) > 0 {
		return ParamFileMetaMultiError(errors)
	}

	return nil
}

// ParamFileMetaMultiError is an error wrapping multiple validation errors
// returned by ParamFileMeta.ValidateAll() if the designated constraints
// aren't met.
type ParamFileMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ParamFileMetaMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ParamFileMetaMultiError) AllErrors() []error { return m }

// ParamFileMetaValidationError is the validation error returned by
// ParamFileMeta.Validate if the designated constraints aren't met.
type ParamFileMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ParamFileMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ParamFileMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ParamFileMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ParamFileMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ParamFileMetaValidationError) ErrorName() string { return "ParamFileMetaValidationError" }

// Error satisfies the builtin error interface
func (e ParamFileMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sParamFileMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ParamFileMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ParamFileMetaValidationError{}

// Validate checks the field values on ParamFileV2 with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ParamFileV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ParamFileV2 with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ParamFileV2MultiError, or
// nil if none found.
func (m *ParamFileV2) ValidateAll() error {
	return m.validate(true)
}

func (m *ParamFileV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ParamFileV2ValidationError{
					field:  "Meta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ParamFileV2ValidationError{
					field:  "Meta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ParamFileV2ValidationError{
				field:  "Meta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLidar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ParamFileV2ValidationError{
					field:  "Lidar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ParamFileV2ValidationError{
					field:  "Lidar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLidar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ParamFileV2ValidationError{
				field:  "Lidar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLidars() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ParamFileV2ValidationError{
						field:  fmt.Sprintf("Lidars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ParamFileV2ValidationError{
						field:  fmt.Sprintf("Lidars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ParamFileV2ValidationError{
					field:  fmt.Sprintf("Lidars[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	{
		sorted_keys := make([]string, len(m.GetCameras()))
		i := 0
		for key := range m.GetCameras() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetCameras()[key]
			_ = val

			// no validation rules for Cameras[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ParamFileV2ValidationError{
							field:  fmt.Sprintf("Cameras[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ParamFileV2ValidationError{
							field:  fmt.Sprintf("Cameras[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ParamFileV2ValidationError{
						field:  fmt.Sprintf("Cameras[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return ParamFileV2MultiError(errors)
	}

	return nil
}

// ParamFileV2MultiError is an error wrapping multiple validation errors
// returned by ParamFileV2.ValidateAll() if the designated constraints aren't met.
type ParamFileV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ParamFileV2MultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ParamFileV2MultiError) AllErrors() []error { return m }

// ParamFileV2ValidationError is the validation error returned by
// ParamFileV2.Validate if the designated constraints aren't met.
type ParamFileV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ParamFileV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ParamFileV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ParamFileV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ParamFileV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ParamFileV2ValidationError) ErrorName() string { return "ParamFileV2ValidationError" }

// Error satisfies the builtin error interface
func (e ParamFileV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sParamFileV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ParamFileV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ParamFileV2ValidationError{}

// Validate checks the field values on CoordSys_Type with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CoordSys_Type) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CoordSys_Type with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CoordSys_TypeMultiError, or
// nil if none found.
func (m *CoordSys_Type) ValidateAll() error {
	return m.validate(true)
}

func (m *CoordSys_Type) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CoordSys_TypeMultiError(errors)
	}

	return nil
}

// CoordSys_TypeMultiError is an error wrapping multiple validation errors
// returned by CoordSys_Type.ValidateAll() if the designated constraints
// aren't met.
type CoordSys_TypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CoordSys_TypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CoordSys_TypeMultiError) AllErrors() []error { return m }

// CoordSys_TypeValidationError is the validation error returned by
// CoordSys_Type.Validate if the designated constraints aren't met.
type CoordSys_TypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CoordSys_TypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CoordSys_TypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CoordSys_TypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CoordSys_TypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CoordSys_TypeValidationError) ErrorName() string { return "CoordSys_TypeValidationError" }

// Error satisfies the builtin error interface
func (e CoordSys_TypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCoordSys_Type.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CoordSys_TypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CoordSys_TypeValidationError{}

// Validate checks the field values on CameraParam_Dimensions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CameraParam_Dimensions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CameraParam_Dimensions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CameraParam_DimensionsMultiError, or nil if none found.
func (m *CameraParam_Dimensions) ValidateAll() error {
	return m.validate(true)
}

func (m *CameraParam_Dimensions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Width

	// no validation rules for Height

	if len(errors) > 0 {
		return CameraParam_DimensionsMultiError(errors)
	}

	return nil
}

// CameraParam_DimensionsMultiError is an error wrapping multiple validation
// errors returned by CameraParam_Dimensions.ValidateAll() if the designated
// constraints aren't met.
type CameraParam_DimensionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CameraParam_DimensionsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CameraParam_DimensionsMultiError) AllErrors() []error { return m }

// CameraParam_DimensionsValidationError is the validation error returned by
// CameraParam_Dimensions.Validate if the designated constraints aren't met.
type CameraParam_DimensionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CameraParam_DimensionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CameraParam_DimensionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CameraParam_DimensionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CameraParam_DimensionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CameraParam_DimensionsValidationError) ErrorName() string {
	return "CameraParam_DimensionsValidationError"
}

// Error satisfies the builtin error interface
func (e CameraParam_DimensionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCameraParam_Dimensions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CameraParam_DimensionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CameraParam_DimensionsValidationError{}
