{{- $podAnnotations := ternary .Values.deployment.podAnnotations .Values.podAnnotations (not (empty .Values.deployment.podAnnotations )) -}}
{{- $podSecurityContext := ternary .Values.deployment.podSecurityContext .Values.podSecurityContext (not (empty .Values.deployment.podSecurityContext )) -}}
{{- $automountServiceAccountToken := ternary .Values.deployment.automountServiceAccountToken .Values.automountServiceAccountToken (not (empty .Values.deployment.automountServiceAccountToken )) -}}
{{- $livenessProbe := ternary .Values.deployment.livenessProbe .Values.livenessProbe (not (empty .Values.deployment.livenessProbe )) -}}
{{- $readinessProbe := ternary .Values.deployment.readinessProbe .Values.readinessProbe (not (empty .Values.deployment.readinessProbe )) -}}
{{- $autoscaling := ternary .Values.deployment.autoscaling .Values.autoscaling (not (empty .Values.deployment.autoscaling )) -}}
{{- $resources := ternary .Values.deployment.resources .Values.resources (not (empty .Values.deployment.resources )) -}}
{{- $extraInitContainers := ternary .Values.deployment.extraInitContainers .Values.extraInitContainers (not (empty .Values.deployment.extraInitContainers )) -}}
{{- $extraContainers := ternary .Values.deployment.extraContainers .Values.extraContainers (not (empty .Values.deployment.extraContainers )) -}}
{{- $extraLabels := ternary .Values.deployment.extraLabels .Values.extraLabels (not (empty .Values.deployment.extraLabels )) -}}
{{- $extraVolumeMounts := ternary .Values.deployment.extraVolumeMounts .Values.extraVolumeMounts (not (empty .Values.deployment.extraVolumeMounts )) -}}
{{- $extraVolumes := ternary .Values.deployment.extraVolumes .Values.extraVolumes (not (empty .Values.deployment.extraVolumes )) -}}
{{- $nodeSelector := ternary .Values.deployment.nodeSelector .Values.nodeSelector (not (empty .Values.deployment.nodeSelector )) -}}
{{- $affinity := ternary .Values.deployment.affinity .Values.affinity (not (empty .Values.deployment.affinity )) -}}
{{- $tolerations := ternary .Values.deployment.tolerations .Values.tolerations (not (empty .Values.deployment.tolerations )) -}}
{{- $topologySpreadConstraints := ternary .Values.deployment.topologySpreadConstraints .Values.topologySpreadConstraints (not (empty .Values.deployment.topologySpreadConstraints )) -}}
{{- include "keto.automigration.typeVerification" . -}}
{{- $migrationExtraEnv := ternary .Values.deployment.automigration.extraEnv .Values.deployment.extraEnv (not (empty .Values.deployment.automigration.extraEnv )) -}}

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "keto.fullname" . }}
  labels:
    {{- include "keto.labels" . | nindent 4 }}
    {{- with $extraLabels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  annotations:
    {{- with .Values.deployment.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
{{- if not $autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
{{- end }}
  selector:
    matchLabels:
      {{- include "keto.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        {{- include "keto.annotations.checksum" . | indent 8 -}}
        {{- with $podAnnotations }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with $.Values.deployment.podMetadata.annotations }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "keto.selectorLabels" . | nindent 8 }}
        {{- with $extraLabels }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with $.Values.deployment.podMetadata.labels }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      initContainers:
      {{- if and ( .Values.keto.automigration.enabled ) ( eq .Values.keto.automigration.type "initContainer" ) }}
        - name: {{ .Chart.Name }}-automigrate
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- if .Values.keto.automigration.customCommand }}
          command: {{- toYaml .Values.keto.automigration.customCommand | nindent 12 }}
          {{- else }}
          command: ["keto"]
          {{- end }}
          {{- if .Values.keto.automigration.customArgs }}
          args: {{- toYaml .Values.keto.automigration.customArgs | nindent 12 }}
          {{- else }}
          args: [ "migrate", "up", "-y" ]
          #args: [ "migrate", "up", "-y", "--config", "/etc/config/keto.yaml" ]
          {{- end }}
          volumeMounts:
            - name: {{ include "keto.name" . }}-config-volume
              mountPath: /etc/config
              readOnly: true
          {{- with $extraVolumeMounts }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          env:
            - name: DSN
              valueFrom:
                secretKeyRef:
                  name: {{ include "keto.secretname" . }}
                  key: dsn
            {{- with $migrationExtraEnv }}
              {{- toYaml . | nindent 12 }}
            {{- end }}
      {{- end }}
      {{- if $extraInitContainers}}
        {{- tpl $extraInitContainers . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "keto.serviceAccountName" . }}
      automountServiceAccountToken: {{ $automountServiceAccountToken }}
      securityContext:
        {{- toYaml $podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          {{- if .Values.securityContext }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          {{- end }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: [ "keto" ]
          args:
            - serve
            #- --config
            #- /etc/config/keto.yaml
          ports:
            - name: {{ .Values.service.read.name }}
              containerPort: {{ .Values.keto.config.serve.read.port }}
              protocol: TCP
            - name: {{ .Values.service.write.name }}
              containerPort: {{ .Values.keto.config.serve.write.port }}
              protocol: TCP
            - name: {{ .Values.service.metrics.name }}
              containerPort: {{ .Values.keto.config.serve.metrics.port }}
              protocol: TCP
            {{- with .Values.deployment.extraPorts }}
              {{- toYaml . | nindent 12 }}
            {{- end }}
          livenessProbe:
            httpGet:
              path: /health/alive
              port: {{ .Values.keto.config.serve.write.port }}
            {{- toYaml $livenessProbe | nindent 12 }}
          readinessProbe:
            httpGet:
              path: /health/ready
              port: {{ .Values.keto.config.serve.write.port }}
          {{- toYaml $readinessProbe | nindent 12 }}
          resources:
            {{- toYaml $resources | nindent 12 }}
          env:
            - name: DSN
              valueFrom:
                secretKeyRef:
                  name: {{ include "keto.secretname" . }}
                  key: dsn
            {{- with .Values.deployment.extraEnv }}
              {{- toYaml . | nindent 12 }}
            {{- end }}
          volumeMounts:
            - name: {{ include "keto.name" . }}-config-volume
              mountPath: /etc/config
              readOnly: true
          {{- with $extraVolumeMounts }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
        {{- if $extraContainers }}
          {{- tpl $extraContainers . | nindent 8 }}
        {{- end }}
      volumes:
        - name: {{ include "keto.name" . }}-config-volume
          configMap:
            name: {{ include "keto.fullname" . }}-config
      {{- with $extraVolumes }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- toYaml . | nindent 8 }}
      {{- end }}
