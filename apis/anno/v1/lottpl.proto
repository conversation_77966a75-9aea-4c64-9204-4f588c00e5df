syntax = "proto3";

package anno.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
// import "google/protobuf/field_mask.proto";
import "google/api/field_behavior.proto";
import "openapi/v3/annotations.proto";
import "anno/v1/lot.proto";
import "anno/v1/type_lotconfig.proto";
import "validate/validate.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anno/v1;anno";
option java_multiple_files = true;
option java_package = "anno.v1";

// lot template service
service Lottpls {
  rpc CreateLottpl (CreateLottplRequest) returns (Lottpl) {
    option (google.api.http) = {
      post: "/v1/lottpls"
      body: "*"
    };
  }

  rpc UpdateLottpl (UpdateLottplRequest) returns (Lottpl) {
    option (google.api.http) = {
      patch: "/v1/lottpls/{lottpl.uid}"
      body: "lottpl"
    };
  }

  rpc DeleteLottpl (DeleteLottplRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/lottpls/{uid}"
    };
  }

  rpc GetLottpl (GetLottplRequest) returns (Lottpl) {
    option (google.api.http) = {
      get: "/v1/lottpls/{uid}"
    };
  }

  rpc ListLottpl (ListLottplRequest) returns (ListLottplReply) {
    option (google.api.http) = {
      get: "/v1/lottpls"
    };
  }

  // rpc ListLottype (ListLottypeRequest) returns (ListLottypeReply) {
  //   option (google.api.http) = {
  //     get: "/v1/lottpls/types"
  //   };
  // }
}

message CreateLottplRequest {
  // mandatory in update-requests
  string uid = 1;
  // mandatory in create-requests
  string name = 2;
  string desc = 3;

  // mandatory in create-requests;
  // lot type
  Lot.Type.Enum type = 4 [(validate.rules).enum = {defined_only: true, not_in: [0]}];
  // lot ontologies
  Lotontologies ontologies = 5;
  // execution phases; phase number starts from 1
  repeated Lotphase phases = 6;
  // execution instructions in format of HTML or Markdown
  string instruction = 7;
  // annotation result output config
  OutConfig out = 8;
  // number of elements in a job
  int32 job_size = 9;
  // make annotations within the radius, unit is meter
  float work_range = 10;
  // repeated string reject_reasons = 10;
}
//message CreateLottplReply {}

message UpdateLottplRequest {
  option (openapi.v3.schema) = {
    required: ["lottpl", "fields"]
  };

  // update contents
  CreateLottplRequest lottpl = 1;
  // name of fields to be updated
  repeated string fields = 2;
}
//message UpdateLottplReply {}

message DeleteLottplRequest {
  string uid = 1;
}
//message DeleteLottplReply {}

message GetLottplRequest {
  string uid = 1;
}
//message GetLottplReply {}

message ListLottplRequest {
  int32 page = 1;
  int32 pagesz = 2  [(validate.rules).int32 = {gte:0, lte: 100}];

  // filter by organization
  string org_uid = 3;
  // filter by creator
  string creator_uid = 4;
  // filter by name pattern
  string name_pattern = 5;
  // filter by lot type
  Lot.Type.Enum type = 6;
}

message ListLottplReply {
  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated Lottpl lottpls = 2;
}

// message ListLottypeRequest {
// }

// message ListLottypeReply {
//   message Lottype {
//     string name = 1;
//     // // language => name
//     // map<string, string> display_names = 2;
//   }

//   repeated Lottype types = 1;
// }

message Lottpl {
  option (openapi.v3.schema) = {
    required: ["uid", "name", "type", "job_size", "ontologies", "phases", "created_at"]
  };

  // lot template UID
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // lot template name
  string name = 2;
  // lot template description
  string desc = 3;
  // task type
  Lot.Type.Enum type = 4 [(validate.rules).enum = {defined_only: true, not_in: [0]}];
  // lot ontologies
  Lotontologies ontologies = 5;
  // execution phases; phase number starts from 1
  repeated Lotphase phases = 6;
  // execution instructions in format of HTML or Markdown
  string instruction = 7;
  // annotation result output config
  OutConfig out = 8;
  // number of elements in a job
  int32 job_size = 9;
  // make annotations within the radius, unit is meter
  float work_range = 10;

  google.protobuf.Timestamp updated_at = 14 [(google.api.field_behavior) = OUTPUT_ONLY];
  google.protobuf.Timestamp created_at = 15 [(google.api.field_behavior) = OUTPUT_ONLY];
}
