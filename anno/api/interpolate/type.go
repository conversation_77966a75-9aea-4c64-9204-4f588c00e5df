package interpolate

import (
	"gitlab.rp.konvery.work/platform/apis/anno/v1"

	"github.com/google/uuid"
)

// Interpolator generates a new object using linear interpolation.
// span indicates the distance between o1 and o2.
// distance indicates the distance from o1 to the new object.
// if o2 is nil, a copy of o1 is returned.
type Interpolator func(o1, o2 *anno.Object, span, distance float64) *anno.Object

var interpolators = map[string]Interpolator{}

func RegisterInterpolator(name string, fn Interpolator) { interpolators[name] = fn }

func GetInterpolator(name string) Interpolator { return interpolators[name] }

func cloneObject(o *anno.Object) *anno.Object {
	w := o.Label.Widget
	return &anno.Object{
		Uuid:        uuid.NewString(), // assign a new ID
		TrackId:     o.TrackId,
		VanishLater: o.VanishLater,
		Label: &anno.Object_Label{
			Name:  o.Label.Name,
			Attrs: o.Label.Attrs, // attrs is shared
			Widget: &anno.Object_Widget{
				Name: w.Name,
				Data: append([]float64{}, w.Data...),
			},
		},
	}
}
