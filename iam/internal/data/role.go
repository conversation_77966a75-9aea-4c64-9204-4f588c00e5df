// source: gitlab.rp.konvery.work/platform/apis/iam/v1/role.proto
package data

import (
	"context"

	"iam/internal/biz"

	"gitlab.rp.konvery.work/platform/pkg/data"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"gorm.io/gorm/clause"
)

type rolesRepo struct {
	repo.GenericRepo[biz.Role]
	data *Data
}

func NewRolesRepo(d *Data, logger log.Logger) biz.RolesRepo {
	return &rolesRepo{GenericRepo: data.NewGenericRepo[biz.Role](d, logger, biz.RoleTableName), data: d}
}

func (o *rolesRepo) GetFeperm(ctx context.Context, role string) (biz.Feperms, error) {
	perm, err := data.GetByUid[biz.Feperm](ctx, o.data, role)
	if err != nil {
		return nil, err
	}
	return perm.Perms, nil
}

func (o *rolesRepo) SetFeperm(ctx context.Context, role string, perms biz.Feperms) error {
	p := &biz.Feperm{
		Role:  role,
		Perms: perms,
	}
	return o.data.WithCtx(ctx).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: biz.Feperm_Role}},
		DoUpdates: clause.AssignmentColumns([]string{biz.Feperm_Perms}),
	}).Create(p).Error
}
