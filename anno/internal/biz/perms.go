package biz

const (
	// common permissions
	PermCreate    = "create"
	PermUpdate    = "update"
	PermDelete    = "delete"
	PermGet       = "get"
	PermList      = "list"
	PermStat      = "stat"
	PermGetPolicy = "getPolicy"
	PermSetPolicy = "setPolicy"

	// PermShare      = "share"
	// PermGetPrivacy = "getPrivacy"

	// lot permissions
	PermListJob        = "listJob"
	PermExportAnno     = "exportAnno"
	PermAssignExecteam = "assignExecteam"

	// Job permissions
	// PermClaim  = "claim"
	// PermSubmit = "submit"
	// PermAssign = "assign"
	// PermReject = "reject" // force reject/recycle
	PermLog = "log" // check log
)

const (
	RoleAnnoKAM = "anno.kam"
	RoleAnnoPM  = "anno.pm"
)

const (
	PermClsUser    = "IamUser"
	PermClsGroup   = "IamGroup"
	PermClsLot     = "AnnoLot"
	PermClsJob     = "AnnoJob"
	PermClsLottpl  = "AnnoLottpl"
	PermClsOrder   = "AnnoOrder"
	PermClsProject = "AnnoProject"
)

const (
	RoleJobViewer = "AnnoJob.viewer"
)

func LotScope(uid string) string               { return ResourceScope(PermClsLot, uid) }
func JobScope(uid string) string               { return ResourceScope(PermClsJob, uid) }
func OrderScope(uid string) string             { return ResourceScope(PermClsOrder, uid) }
func ResourceScope(permCls, uid string) string { return permCls + ":" + uid }
