// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: anno/v1/project.proto

package anno

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationProjectsCreateProject = "/anno.v1.Projects/CreateProject"
const OperationProjectsDeleteProject = "/anno.v1.Projects/DeleteProject"
const OperationProjectsGetProject = "/anno.v1.Projects/GetProject"
const OperationProjectsListProject = "/anno.v1.Projects/ListProject"
const OperationProjectsUpdateProject = "/anno.v1.Projects/UpdateProject"

type ProjectsHTTPServer interface {
	CreateProject(context.Context, *CreateProjectRequest) (*Project, error)
	DeleteProject(context.Context, *DeleteProjectRequest) (*emptypb.Empty, error)
	GetProject(context.Context, *GetProjectRequest) (*Project, error)
	ListProject(context.Context, *ListProjectRequest) (*ListProjectReply, error)
	UpdateProject(context.Context, *UpdateProjectRequest) (*Project, error)
}

func RegisterProjectsHTTPServer(s *http.Server, srv ProjectsHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/projects", _Projects_CreateProject0_HTTP_Handler(srv))
	r.PATCH("/v1/projects/{project.uid}", _Projects_UpdateProject0_HTTP_Handler(srv))
	r.DELETE("/v1/projects/{uid}", _Projects_DeleteProject0_HTTP_Handler(srv))
	r.GET("/v1/projects/{uid}", _Projects_GetProject0_HTTP_Handler(srv))
	r.GET("/v1/projects", _Projects_ListProject0_HTTP_Handler(srv))
}

func _Projects_CreateProject0_HTTP_Handler(srv ProjectsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateProjectRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationProjectsCreateProject)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateProject(ctx, req.(*CreateProjectRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Project)
		return ctx.Result(200, reply)
	}
}

func _Projects_UpdateProject0_HTTP_Handler(srv ProjectsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateProjectRequest
		if err := ctx.Bind(&in.Project); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationProjectsUpdateProject)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateProject(ctx, req.(*UpdateProjectRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Project)
		return ctx.Result(200, reply)
	}
}

func _Projects_DeleteProject0_HTTP_Handler(srv ProjectsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteProjectRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationProjectsDeleteProject)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteProject(ctx, req.(*DeleteProjectRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Projects_GetProject0_HTTP_Handler(srv ProjectsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetProjectRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationProjectsGetProject)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetProject(ctx, req.(*GetProjectRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Project)
		return ctx.Result(200, reply)
	}
}

func _Projects_ListProject0_HTTP_Handler(srv ProjectsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListProjectRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationProjectsListProject)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListProject(ctx, req.(*ListProjectRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListProjectReply)
		return ctx.Result(200, reply)
	}
}

type ProjectsHTTPClient interface {
	CreateProject(ctx context.Context, req *CreateProjectRequest, opts ...http.CallOption) (rsp *Project, err error)
	DeleteProject(ctx context.Context, req *DeleteProjectRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetProject(ctx context.Context, req *GetProjectRequest, opts ...http.CallOption) (rsp *Project, err error)
	ListProject(ctx context.Context, req *ListProjectRequest, opts ...http.CallOption) (rsp *ListProjectReply, err error)
	UpdateProject(ctx context.Context, req *UpdateProjectRequest, opts ...http.CallOption) (rsp *Project, err error)
}

type ProjectsHTTPClientImpl struct {
	cc *http.Client
}

func NewProjectsHTTPClient(client *http.Client) ProjectsHTTPClient {
	return &ProjectsHTTPClientImpl{client}
}

func (c *ProjectsHTTPClientImpl) CreateProject(ctx context.Context, in *CreateProjectRequest, opts ...http.CallOption) (*Project, error) {
	var out Project
	pattern := "/v1/projects"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationProjectsCreateProject))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ProjectsHTTPClientImpl) DeleteProject(ctx context.Context, in *DeleteProjectRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/projects/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationProjectsDeleteProject))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ProjectsHTTPClientImpl) GetProject(ctx context.Context, in *GetProjectRequest, opts ...http.CallOption) (*Project, error) {
	var out Project
	pattern := "/v1/projects/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationProjectsGetProject))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ProjectsHTTPClientImpl) ListProject(ctx context.Context, in *ListProjectRequest, opts ...http.CallOption) (*ListProjectReply, error) {
	var out ListProjectReply
	pattern := "/v1/projects"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationProjectsListProject))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ProjectsHTTPClientImpl) UpdateProject(ctx context.Context, in *UpdateProjectRequest, opts ...http.CallOption) (*Project, error) {
	var out Project
	pattern := "/v1/projects/{project.uid}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationProjectsUpdateProject))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in.Project, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
