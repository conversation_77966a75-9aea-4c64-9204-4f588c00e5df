package middleware

import (
	"context"
	stdhttp "net/http"
	"strings"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/http"
	"gitlab.rp.konvery.work/platform/apis/client"
	"gitlab.rp.konvery.work/platform/pkg/kparser"
)

const bearerPrefix = "Bearer "

// Auth is a middleware to parse authorization.
func Auth() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			if tr, ok := transport.FromServerContext(ctx); ok {
				md := tr.RequestHeader()
				v := md.Get(client.HeaderAuthorization)
				if v == "" {
					v = md.Get(client.HeaderXmdAuthorization)
				}
				if v != "" && !strings.HasPrefix(v, bearerPrefix) {
					v = ""
				}
				if v == "" {
					v = tokenFromCookie(md)
				}
				if v == "" {
					return handler(ctx, req)
				}

				ctx = client.NewCtxWithRPCIdentity(ctx, &client.RPCIdentity{AuthToken: v})
				uc, err := client.GetMe2(ctx)
				if err != nil {
					return nil, err
				}
				ctx, _ = setCtxUser(ctx, uc.User, uc.AssumeBy)
			}
			return handler(ctx, req)
		}
	}
}

func tokenFromCookie(header interface{ Values(key string) []string }) string {
	for _, s := range header.Values(client.HeaderCookie) {
		kvs := kparser.ParseLineKVs(s, "; ", "=")
		if v := kvs["JWT"]; v != "" {
			return bearerPrefix + v
		}
	}
	return ""
}

// HTTPAuthFilter is a HTTP middleware to parse authorization.
func HTTPAuthFilter(logger log.Logger) http.FilterFunc {
	return func(h stdhttp.Handler) stdhttp.Handler {
		return Handler(func(w stdhttp.ResponseWriter, r *stdhttp.Request) {
			v := r.Header.Get(client.HeaderAuthorization)
			if v == "" {
				v = r.Header.Get(client.HeaderXmdAuthorization)
			}
			if v != "" && !strings.HasPrefix(v, bearerPrefix) {
				v = ""
			}
			if v == "" {
				v = tokenFromCookie(r.Header)
			}
			if v == "" {
				h.ServeHTTP(w, r)
				return
			}

			ctx := r.Context()
			ctx = client.NewCtxWithRPCIdentity(ctx, &client.RPCIdentity{AuthToken: v})
			uc, err := client.GetMe2(ctx)
			if err != nil {
				writeErr(w, errors.FromError(err))
				return
			}

			ctx, newctx := setCtxUser(ctx, uc.User, uc.AssumeBy)
			if newctx {
				r = r.WithContext(ctx)
			}
			h.ServeHTTP(w, r)
		})
	}
}
