version: "3.2"
services:
  keto-migrate:
    image: oryd/keto:v0.10.0-alpha.0
    links:
      - postgresd:postgresd
    volumes:
      - type: bind
        source: ./config
        target: /home/<USER>
    environment:
      - LOG_LEVEL=debug
      - DSN=***************************************/accesscontroldb?sslmode=disable
    command: ["migrate", "up", "-y"]
    restart: on-failure
  keto:
    links:
      - postgresd:postgresd
    volumes:
      - type: bind
        source: ./config
        target: /home/<USER>
    ports:
      - "4466:4466"
      - "4467:4467"
    depends_on:
      - keto-migrate
    environment:
      - DSN=***************************************/accesscontroldb?sslmode=disable
    restart: on-failure
  postgresd:
    image: postgres:9.6
    environment:
      - POSTGRES_USER=dbuser
      - POSTGRES_PASSWORD=secret
      - POSTGRES_DB=accesscontroldb
