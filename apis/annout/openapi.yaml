# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: Configs API
    version: 0.0.1
paths:
    /annout/v1/version:
        get:
            tags:
                - Configs
            operationId: Configs_GetVersion
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annout.v1.GetVersionReply'
components:
    schemas:
        annout.v1.GetVersionReply:
            type: object
            properties:
                version:
                    type: string
tags:
    - name: Configs
