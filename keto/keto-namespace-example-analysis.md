# Keto权限系统完整示例分析

本文将通过一个完整的示例，详细分析Keto权限系统从namespace定义到权限校验的整个流程。

## 1. 示例场景定义

假设我们有一个文档管理系统，需要实现以下权限模型：

- 用户（User）可以查看、编辑文档（Document）
- 用户可以属于组（Group）
- 组可以拥有对文档的权限
- 文档可以有所有者（Owner）

## 2. Namespace定义

首先，我们需要在`namespaces.keto.ts`文件中定义我们的权限模型：

```typescript
import { Namespace, SubjectSet, Context } from '@ory/keto-namespace-types';

// 用户命名空间
class User implements Namespace {
  related: {
    groups: Group[]  // 用户所属的组
  }

  permits = {
    // 基础权限检查函数
    check: (ctx: Context, perm: string): boolean =>
      User:"*".permits.check(ctx, perm) ||  // 全局权限检查
      this.related.groups.traverse((g) => g.permits.check(ctx, perm)),  // 组权限检查
  }
}

// 组命名空间
class Group implements Namespace {
  related: {
    members: (User | SubjectSet<Group, "members">)[]  // 组成员（用户或子组）
    permissions: Permission[]  // 组拥有的权限
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      Group:"*".permits.check(ctx, perm) ||  // 全局权限检查
      this.related.permissions.includes(perm),  // 直接权限检查
  }
}

// 文档命名空间
class Document implements Namespace {
  related: {
    owners: User[]  // 文档所有者
    viewers: (User | SubjectSet<Group, "members">)[]  // 可查看者
    editors: (User | SubjectSet<Group, "members">)[]  // 可编辑者
  }

  permits = {
    // 基础权限检查函数
    check: (ctx: Context, perm: string): boolean =>
      Document:"*".permits.check(ctx, perm) ||  // 全局权限检查
      this.related.owners.includes(ctx.subject),  // 所有者检查

    // 特定操作权限
    view: (ctx: Context): boolean =>
      this.permits.check(ctx, "view") ||
      this.related.viewers.includes(ctx.subject) ||
      this.related.editors.includes(ctx.subject) ||
      this.related.owners.includes(ctx.subject),

    edit: (ctx: Context): boolean =>
      this.permits.check(ctx, "edit") ||
      this.related.editors.includes(ctx.subject) ||
      this.related.owners.includes(ctx.subject),
  }
}

// 权限命名空间（简单定义，不包含关系）
class Permission implements Namespace {}
```

## 3. 解析过程与AST生成

当Keto服务启动时，会加载并解析上述namespace定义。以下是解析过程：

### 3.1 词法分析

首先，词法分析器（Lexer）会将输入文本转换为token流：

```
TOKEN: import { ... } from '@ory/keto-namespace-types'
TOKEN: class
TOKEN: User
TOKEN: implements
TOKEN: Namespace
TOKEN: {
TOKEN: related
TOKEN: :
TOKEN: {
TOKEN: groups
TOKEN: :
TOKEN: Group
TOKEN: [
TOKEN: ]
TOKEN: }
...
```

### 3.2 语法分析

然后，语法分析器（Parser）会将token流转换为AST：

```go
// 解析类定义
func (p *parser) parseClass() {
    var name string
    p.match(&name, "implements", "Namespace", "{")
    p.namespace = namespace{Name: name}
    
    // 解析类内容
    for !p.fatal {
        switch item := p.next(); {
        case item.Typ == itemBraceRight:
            p.namespaces = append(p.namespaces, p.namespace)
            return
        case item.Val == "related":
            p.parseRelated()
        case item.Val == "permits":
            p.parsePermits()
        }
    }
}
```

### 3.3 生成的AST结构

最终，解析器会生成以下AST结构：

```go
// User命名空间
Namespace{
    Name: "User",
    Relations: []ast.Relation{
        {
            Name: "groups",
            Types: []ast.RelationType{
                {Namespace: "Group"},
            },
            Params: []string{"subject"},
        },
        {
            Name: "check",
            SubjectSetRewrite: &ast.SubjectSetRewrite{
                Operation: ast.OperatorOr,
                Children: []ast.Child{
                    &ast.ComputedSubjectSet{
                        Namespace: "User",
                        Object: "*",
                        Relation: "check",
                        Args: []ast.Arg{ast.ContextArg, ast.NamedArg("perm")},
                    },
                    &ast.TupleToSubjectSet{
                        Relation: "groups",
                        ComputedSubjectSetRelation: "check",
                        Args: []ast.Arg{ast.ContextArg, ast.NamedArg("perm")},
                    },
                },
            },
            Params: []string{"ctx", "perm"},
        },
    },
}

// Document命名空间
Namespace{
    Name: "Document",
    Relations: []ast.Relation{
        {
            Name: "owners",
            Types: []ast.RelationType{
                {Namespace: "User"},
            },
            Params: []string{"subject"},
        },
        {
            Name: "viewers",
            Types: []ast.RelationType{
                {Namespace: "User"},
                {
                    Namespace: "Group",
                    Relation: "members",
                },
            },
            Params: []string{"subject"},
        },
        {
            Name: "editors",
            Types: []ast.RelationType{
                {Namespace: "User"},
                {
                    Namespace: "Group",
                    Relation: "members",
                },
            },
            Params: []string{"subject"},
        },
        {
            Name: "check",
            SubjectSetRewrite: &ast.SubjectSetRewrite{
                Operation: ast.OperatorOr,
                Children: []ast.Child{
                    &ast.ComputedSubjectSet{
                        Namespace: "Document",
                        Object: "*",
                        Relation: "check",
                        Args: []ast.Arg{ast.ContextArg, ast.NamedArg("perm")},
                    },
                    &ast.SubjectEqualsObject{},
                },
            },
            Params: []string{"ctx", "perm"},
        },
        {
            Name: "view",
            SubjectSetRewrite: &ast.SubjectSetRewrite{
                Operation: ast.OperatorOr,
                Children: []ast.Child{
                    &ast.ComputedSubjectSet{
                        Relation: "check",
                        Args: []ast.Arg{ast.ContextArg, ast.StringLiteralArg("view")},
                    },
                    &ast.TupleToSubjectSet{
                        Relation: "viewers",
                    },
                    &ast.TupleToSubjectSet{
                        Relation: "editors",
                    },
                    &ast.TupleToSubjectSet{
                        Relation: "owners",
                    },
                },
            },
            Params: []string{"ctx"},
        },
        {
            Name: "edit",
            SubjectSetRewrite: &ast.SubjectSetRewrite{
                Operation: ast.OperatorOr,
                Children: []ast.Child{
                    &ast.ComputedSubjectSet{
                        Relation: "check",
                        Args: []ast.Arg{ast.ContextArg, ast.StringLiteralArg("edit")},
                    },
                    &ast.TupleToSubjectSet{
                        Relation: "editors",
                    },
                    &ast.TupleToSubjectSet{
                        Relation: "owners",
                    },
                },
            },
            Params: []string{"ctx"},
        },
    },
}
```

## 4. AST存储

解析后的AST存储在内存中的命名空间管理器中：

```go
// 内存命名空间管理器
type memoryNamespaceManager struct {
    sync.RWMutex
    byName map[string]*namespace.Namespace
}

// 存储过程
func (s *memoryNamespaceManager) set(nn []*namespace.Namespace) {
    s.Lock()
    defer s.Unlock()
    
    s.byName = make(map[string]*namespace.Namespace, len(nn))
    for _, n := range nn {
        s.byName[n.Name] = n
    }
}
```

## 5. 关系元组定义与存储

接下来，我们需要定义具体的权限规则，这些规则以关系元组的形式存储在数据库中：

```
// 用户关系
User:alice#groups@Group:admins
User:bob#groups@Group:editors
User:charlie#groups@Group:viewers

// 组权限关系
Group:admins#permissions@Permission:admin
Group:editors#permissions@Permission:edit
Group:viewers#permissions@Permission:view

// 组成员关系
Group:editors#members@Group:viewers

// 文档权限关系
Document:doc1#owners@User:alice
Document:doc2#editors@User:bob
Document:doc3#viewers@User:charlie
Document:doc4#viewers@Group:viewers
```

这些关系元组在数据库中的存储结构如下：

```sql
CREATE TABLE relation_tuples (
    namespace VARCHAR(64) NOT NULL,
    object UUID NOT NULL,
    relation VARCHAR(64) NOT NULL,
    subject_id UUID,
    subject_set_namespace VARCHAR(64),
    subject_set_object UUID,
    subject_set_relation VARCHAR(64),
    PRIMARY KEY (namespace, object, relation, subject_id, subject_set_namespace, subject_set_object, subject_set_relation)
);
```

例如，`Document:doc1#owners@User:alice` 在数据库中的表示为：

```
namespace: "Document"
object: "doc1" (UUID)
relation: "owners"
subject_id: NULL
subject_set_namespace: "User"
subject_set_object: "alice" (UUID)
subject_set_relation: ""
```

## 6. 权限校验流程

现在，让我们模拟一个API权限校验过程，检查用户Bob是否有权限查看文档doc4：

### 6.1 API请求

```http
GET /relation-tuples/check?namespace=Document&object=doc4&relation=view&subject=User:bob
```

### 6.2 请求处理

```go
func (h *Handler) getCheck(ctx context.Context, q url.Values) (bool, error) {
    // 1. 解析最大深度
    maxDepth, err := x.GetMaxDepthFromQuery(q)
    if err != nil {
        return false, err
    }
    
    // 2. 从URL查询参数构建关系元组
    tuple, err := (&ketoapi.RelationTuple{}).FromURLQuery(q)
    if err != nil {
        return false, err
    }
    
    // 输出：
    // tuple = {
    //     Namespace: "Document",
    //     Object: "doc4",
    //     Relation: "view",
    //     Subject: {
    //         Namespace: "User",
    //         Object: "bob",
    //         Relation: "",
    //     }
    // }
    
    // 3. 转换为内部元组
    it, err := h.d.ReadOnlyMapper().FromTuple(ctx, tuple)
    if errors.Is(err, herodot.ErrNotFound) {
        return false, nil
    } else if err != nil {
        return false, err
    }
    
    // 输出：
    // it[0] = &relationtuple.RelationTuple{
    //     Namespace: "Document",
    //     Object: UUID("doc4"),
    //     Relation: "view",
    //     Subject: &SubjectID{
    //         ID: UUID("bob"),
    //     },
    // }
    
    // 4. 执行权限检查
    return h.d.PermissionEngine().CheckIsMember(ctx, it[0], maxDepth)
}
```

### 6.3 权限检查执行

```go
func (e *Engine) CheckIsMember(ctx context.Context, r *relationTuple, restDepth int) (bool, error) {
    // 1. 调用CheckRelationTuple进行检查
    result := e.CheckRelationTuple(ctx, r, restDepth)
    if result.Err != nil {
        return false, result.Err
    }
    
    // 2. 返回结果
    return result.Membership == checkgroup.IsMember, nil
}

func (e *Engine) CheckRelationTuple(ctx context.Context, r *relationTuple, restDepth int) (res checkgroup.Result) {
    // 1. 设置最大深度
    if globalMaxDepth := e.d.Config(ctx).MaxReadDepth(); restDepth <= 0 || globalMaxDepth < restDepth {
        restDepth = globalMaxDepth
    }
    
    // 2. 检查缓存
    if e.useCache {
        if membership := e.getBriefCache(ctx, r); membership != checkgroup.Unknown {
            return checkgroup.Result{Membership: membership}
        }
    }
    
    // 3. 执行权限检查
    resultCh := make(chan checkgroup.Result)
    go e.checkIsAllowed(ctx, r, restDepth, false)(ctx, resultCh)
    
    // 4. 获取结果
    select {
    case result := <-resultCh:
        e.addBriefCache(ctx, r, result.Membership)
        return result
    case <-ctx.Done():
        return checkgroup.Result{Err: errors.WithStack(ctx.Err())}
    }
}
```

### 6.4 权限检查详细过程

```go
func (e *Engine) checkIsAllowed(ctx context.Context, r *relationTuple, restDepth int, skipDirect bool) checkgroup.CheckFunc {
    // 1. 检查深度限制
    if restDepth <= 0 {
        return checkgroup.UnknownMemberFunc
    }
    
    // 2. 获取关系定义
    relation, err := e.astRelationFor(ctx, r)
    // 输出：
    // relation = &ast.Relation{
    //     Name: "view",
    //     SubjectSetRewrite: &ast.SubjectSetRewrite{
    //         Operation: ast.OperatorOr,
    //         Children: []ast.Child{...},
    //     },
    //     Params: []string{"ctx"},
    // }
    
    // 3. 处理参数
    ctx, err = e.processArgs(ctx, r, relation)
    
    // 4. 创建检查组
    g := checkgroup.New(ctx)
    
    // 5. 尝试系统缓存
    if e.trySysCache(ctx, g, r, restDepth) {
        return g.CheckFunc()
    }
    
    // 6. 检查是否有主体集重写
    hasRewrite := relation != nil && relation.SubjectSetRewrite != nil
    
    // 7. 执行权限检查
    if hasRewrite {
        // 7.1 检查主体集重写
        g.Add(e.checkSubjectSetRewrite(ctx, r, relation.SubjectSetRewrite, restDepth))
    }
    
    if (!strictMode || !hasRewrite) && !skipDirect && !fakeRelation {
        // 7.2 直接检查
        g.Add(e.checkDirect(r, restDepth-1))
    }
    
    if canHaveSubjectSets && !fakeRelation {
        // 7.3 展开主体集
        g.Add(e.checkExpandSubject(r, restDepth-1))
    }
    
    return g.CheckFunc()
}
```

### 6.5 主体集重写检查

```go
func (e *Engine) checkSubjectSetRewrite(ctx context.Context, r *relationTuple, rewrite *ast.SubjectSetRewrite, restDepth int) checkgroup.CheckFunc {
    // 1. 创建检查组
    g := checkgroup.New(ctx)
    
    // 2. 根据操作类型执行检查
    switch rewrite.Operation {
    case ast.OperatorOr:
        // 对于OR操作，任一子检查通过即可
        for _, child := range rewrite.Children {
            g.Add(e.checkChild(ctx, r, child, restDepth-1))
        }
    case ast.OperatorAnd:
        // 对于AND操作，所有子检查都必须通过
        for _, child := range rewrite.Children {
            g.Add(e.checkChild(ctx, r, child, restDepth-1))
        }
    }
    
    return g.CheckFunc()
}
```

### 6.6 子检查执行

```go
func (e *Engine) checkChild(ctx context.Context, r *relationTuple, child ast.Child, restDepth int) checkgroup.CheckFunc {
    switch c := child.(type) {
    case *ast.ComputedSubjectSet:
        // 处理计算主体集
        return e.checkComputedSubjectSet(ctx, r, c, restDepth)
    case *ast.TupleToSubjectSet:
        // 处理元组到主体集的转换
        return e.checkTupleToSubjectSet(ctx, r, c, restDepth)
    case *ast.SubjectEqualsObject:
        // 处理主体等于对象的情况
        return e.checkSubjectEqualsObject(ctx, r)
    case *ast.InvertResult:
        // 处理结果取反的情况
        return e.checkInvertResult(ctx, r, c, restDepth)
    }
    return checkgroup.NotMemberFunc
}
```

### 6.7 具体检查示例

以检查用户Bob是否有权限查看文档doc4为例，系统会执行以下步骤：

1. 首先检查Document:doc4#view@User:bob是否直接存在
   - 数据库查询：不存在

2. 检查Document:doc4#viewers@User:bob是否存在
   - 数据库查询：不存在

3. 检查Document:doc4#editors@User:bob是否存在
   - 数据库查询：不存在

4. 检查Document:doc4#owners@User:bob是否存在
   - 数据库查询：不存在

5. 检查Document:doc4#viewers@Group:*是否存在
   - 数据库查询：存在Document:doc4#viewers@Group:viewers

6. 检查Group:viewers#members@User:bob是否存在
   - 数据库查询：不存在直接关系

7. 检查Group:editors#members@User:bob是否存在
   - 数据库查询：存在Group:editors#members@User:bob

8. 检查Group:editors#members@Group:viewers是否存在
   - 数据库查询：存在Group:editors#members@Group:viewers

9. 由于Group:viewers是Document:doc4的viewers，且Bob是Group:editors的成员，而Group:editors包含Group:viewers，所以Bob有权限查看文档doc4

### 6.8 返回结果

```go
// 最终结果
allowed = true

// API响应
{
    "allowed": true
}
```

## 7. 完整流程总结

1. **Namespace定义**：在TypeScript文件中定义权限模型
2. **AST生成**：解析namespace定义，生成AST
3. **AST存储**：将AST存储在内存中的命名空间管理器中
4. **关系元组定义**：定义具体的权限规则
5. **关系元组存储**：将关系元组存储在数据库中
6. **权限校验**：
   - 接收API请求
   - 构建关系元组
   - 执行权限检查
   - 返回结果

这个完整的示例展示了Keto权限系统从namespace定义到权限校验的整个流程，包括每一步的数据结构和处理逻辑。通过这种方式，Keto实现了灵活且高效的权限管理。
