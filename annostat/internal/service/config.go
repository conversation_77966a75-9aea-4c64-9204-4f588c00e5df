package service

import (
	"context"

	"annostat/internal/conf"
	"gitlab.rp.konvery.work/platform/apis/annostat/v1"

	"google.golang.org/protobuf/types/known/emptypb"
)

type ConfigsService struct {
	annostat.UnimplementedConfigsServer
}

func NewConfigsService() *ConfigsService {
	return &ConfigsService{}
}

func (o *ConfigsService) GetVersion(ctx context.Context, req *emptypb.Empty) (*annostat.GetVersionReply, error) {
	return &annostat.GetVersionReply{Version: conf.Version}, nil
}
