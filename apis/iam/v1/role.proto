syntax = "proto3";

package iam.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";
import "iam/v1/type.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/iam/v1;iam";
option java_multiple_files = true;
option java_package = "api.iam.v1";

service Roles {
  rpc CreateRole (Role) returns (Role) {
    option (google.api.http) = {
      post: "/v1/roles"
      body: "*"
    };
  }

  rpc UpdateRole (UpdateRoleRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      patch: "/v1/roles/{name}"
      body: "*"
    };
  }

  rpc DeleteRole (DeleteRoleRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/roles/{name}"
    };
  }

  rpc GetRole (GetRoleRequest) returns (GetRoleReply) {
    option (google.api.http) = {
      get: "/v1/roles/{name}"
    };
  }

  rpc ListRole (ListRoleRequest) returns (ListRoleReply) {
    option (google.api.http) = {
      get: "/v1/roles"
    };
  }

  rpc GetRoleFeperm (GetRoleRequest) returns (GetRoleFepermReply) {
    option (google.api.http) = {
      get: "/v1/roles/{name}/feperm"
    };
  }

  rpc SetRoleFeperm (SetRoleFepermRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/roles/{name}/feperm"
      body: "*"
    };
  }
}

message Role {
  option (openapi.v3.schema) = {
    required: ["name", "perms"]
  };

  // name of the role
  // admins can create global roles; others can only create org-scope roles
  // role names should be unique within their scopes.
  // name of org-scope roles are in the format: org-uid.role-name
  // pattern: "^[\\w-]+(\\.[\\w-]+)?$"; length-in-bytes: [3, 30]
  string name = 1 [(validate.rules).string = {pattern: "^[\\w-]+(\\.[\\w-]+)?$", min_bytes: 3, max_bytes: 30}];
  // name displayed in UI; length-in-bytes: [3, 30]
  string display_name = 2 [(validate.rules).string = {min_bytes: 3, max_bytes: 30}];
  // list of roles (IamRole:xxx) and permissions
  repeated string perms = 3 [(validate.rules).repeated.items.string =
                              {pattern: "^(IamRole:)?\\w+(\\.\\w+)*$", min_bytes: 3, max_bytes: 60}];
}

message UpdateRoleRequest {
  option (openapi.v3.schema) = {
    required: ["name", "perms", "action"]
  };

  EditAction.Enum action = 1 [(validate.rules).enum = {defined_only: true, not_in: [0]}];
  string name = 2;
  // list of roles (IamRole:xxx) and permissions
  repeated string perms = 3 [(validate.rules).repeated.items.string =
                              {pattern: "^(IamRole:)?\\w+(\\.\\w+)*$", min_bytes: 3, max_bytes: 60}];
}

message DeleteRoleRequest {
  string name = 1;
}

message GetRoleRequest {
  string name = 1;
}
message GetRoleReply {
  option (openapi.v3.schema) = {
    required: ["name", "display_name", "perms"]
  };

  string name = 1;
  string display_name = 2;
  // list of roles (IamRole:xxx) and permissions
  repeated string perms = 3;
}

message ListRoleRequest {
  int32 pagesz = 1 [(validate.rules).int32 = {gte:0, lte: 100}];
  // An opaque pagination token returned from a previous call.
  // An empty token denotes the first page.
  string page_token = 2;
  // if not empty, only list roles created by this org
  string org_uid = 3;
  string name_pattern = 4;
}

message ListRoleReply {
  option (openapi.v3.schema) = {
    required: ["roles", "next_page_token"]
  };

  // message Role {
  //   option (openapi.v3.schema) = {
  //     required: ["name", "display_name"]
  //   };

  //   string name = 1;
  //   string display_name = 2;
  // }

  // roles info without perms
  repeated Role roles = 1;
  string next_page_token = 2;
}

message FepermItem {
  Team.Type.Enum team_type = 1;
  Feperm perm = 2;
}

message GetRoleFepermReply {
  repeated FepermItem perms = 1;
}

message SetRoleFepermRequest {
  // role name
  string name = 1;
  repeated FepermItem perms = 2;
}
