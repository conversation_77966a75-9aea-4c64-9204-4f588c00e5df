// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: anno/v1/specgrant.proto

package anno

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Specgrants_CreateSpecgrant_FullMethodName = "/anno.v1.Specgrants/CreateSpecgrant"
	Specgrants_DeleteSpecgrant_FullMethodName = "/anno.v1.Specgrants/DeleteSpecgrant"
	Specgrants_ListSpecgrant_FullMethodName   = "/anno.v1.Specgrants/ListSpecgrant"
)

// SpecgrantsClient is the client API for Specgrants service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SpecgrantsClient interface {
	CreateSpecgrant(ctx context.Context, in *CreateSpecgrantRequest, opts ...grpc.CallOption) (*Specgrant, error)
	DeleteSpecgrant(ctx context.Context, in *DeleteSpecgrantRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListSpecgrant(ctx context.Context, in *ListSpecgrantRequest, opts ...grpc.CallOption) (*ListSpecgrantReply, error)
}

type specgrantsClient struct {
	cc grpc.ClientConnInterface
}

func NewSpecgrantsClient(cc grpc.ClientConnInterface) SpecgrantsClient {
	return &specgrantsClient{cc}
}

func (c *specgrantsClient) CreateSpecgrant(ctx context.Context, in *CreateSpecgrantRequest, opts ...grpc.CallOption) (*Specgrant, error) {
	out := new(Specgrant)
	err := c.cc.Invoke(ctx, Specgrants_CreateSpecgrant_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *specgrantsClient) DeleteSpecgrant(ctx context.Context, in *DeleteSpecgrantRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Specgrants_DeleteSpecgrant_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *specgrantsClient) ListSpecgrant(ctx context.Context, in *ListSpecgrantRequest, opts ...grpc.CallOption) (*ListSpecgrantReply, error) {
	out := new(ListSpecgrantReply)
	err := c.cc.Invoke(ctx, Specgrants_ListSpecgrant_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SpecgrantsServer is the server API for Specgrants service.
// All implementations must embed UnimplementedSpecgrantsServer
// for forward compatibility
type SpecgrantsServer interface {
	CreateSpecgrant(context.Context, *CreateSpecgrantRequest) (*Specgrant, error)
	DeleteSpecgrant(context.Context, *DeleteSpecgrantRequest) (*emptypb.Empty, error)
	ListSpecgrant(context.Context, *ListSpecgrantRequest) (*ListSpecgrantReply, error)
	mustEmbedUnimplementedSpecgrantsServer()
}

// UnimplementedSpecgrantsServer must be embedded to have forward compatible implementations.
type UnimplementedSpecgrantsServer struct {
}

func (UnimplementedSpecgrantsServer) CreateSpecgrant(context.Context, *CreateSpecgrantRequest) (*Specgrant, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSpecgrant not implemented")
}
func (UnimplementedSpecgrantsServer) DeleteSpecgrant(context.Context, *DeleteSpecgrantRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSpecgrant not implemented")
}
func (UnimplementedSpecgrantsServer) ListSpecgrant(context.Context, *ListSpecgrantRequest) (*ListSpecgrantReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSpecgrant not implemented")
}
func (UnimplementedSpecgrantsServer) mustEmbedUnimplementedSpecgrantsServer() {}

// UnsafeSpecgrantsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SpecgrantsServer will
// result in compilation errors.
type UnsafeSpecgrantsServer interface {
	mustEmbedUnimplementedSpecgrantsServer()
}

func RegisterSpecgrantsServer(s grpc.ServiceRegistrar, srv SpecgrantsServer) {
	s.RegisterService(&Specgrants_ServiceDesc, srv)
}

func _Specgrants_CreateSpecgrant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSpecgrantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpecgrantsServer).CreateSpecgrant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Specgrants_CreateSpecgrant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpecgrantsServer).CreateSpecgrant(ctx, req.(*CreateSpecgrantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Specgrants_DeleteSpecgrant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSpecgrantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpecgrantsServer).DeleteSpecgrant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Specgrants_DeleteSpecgrant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpecgrantsServer).DeleteSpecgrant(ctx, req.(*DeleteSpecgrantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Specgrants_ListSpecgrant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSpecgrantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpecgrantsServer).ListSpecgrant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Specgrants_ListSpecgrant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpecgrantsServer).ListSpecgrant(ctx, req.(*ListSpecgrantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Specgrants_ServiceDesc is the grpc.ServiceDesc for Specgrants service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Specgrants_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "anno.v1.Specgrants",
	HandlerType: (*SpecgrantsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSpecgrant",
			Handler:    _Specgrants_CreateSpecgrant_Handler,
		},
		{
			MethodName: "DeleteSpecgrant",
			Handler:    _Specgrants_DeleteSpecgrant_Handler,
		},
		{
			MethodName: "ListSpecgrant",
			Handler:    _Specgrants_ListSpecgrant_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "anno/v1/specgrant.proto",
}
