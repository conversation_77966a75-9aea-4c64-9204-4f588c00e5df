// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: model/v1/run.proto

package model

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	types "gitlab.rp.konvery.work/platform/apis/types"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Run_State_Enum int32

const (
	Run_State_unspecified Run_State_Enum = 0
	Run_State_unstart     Run_State_Enum = 1
	Run_State_running     Run_State_Enum = 2
	Run_State_finished    Run_State_Enum = 3
	Run_State_failed      Run_State_Enum = 4
	Run_State_canceled    Run_State_Enum = 5
)

// Enum value maps for Run_State_Enum.
var (
	Run_State_Enum_name = map[int32]string{
		0: "unspecified",
		1: "unstart",
		2: "running",
		3: "finished",
		4: "failed",
		5: "canceled",
	}
	Run_State_Enum_value = map[string]int32{
		"unspecified": 0,
		"unstart":     1,
		"running":     2,
		"finished":    3,
		"failed":      4,
		"canceled":    5,
	}
)

func (x Run_State_Enum) Enum() *Run_State_Enum {
	p := new(Run_State_Enum)
	*p = x
	return p
}

func (x Run_State_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Run_State_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_model_v1_run_proto_enumTypes[0].Descriptor()
}

func (Run_State_Enum) Type() protoreflect.EnumType {
	return &file_model_v1_run_proto_enumTypes[0]
}

func (x Run_State_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Run_State_Enum.Descriptor instead.
func (Run_State_Enum) EnumDescriptor() ([]byte, []int) {
	return file_model_v1_run_proto_rawDescGZIP(), []int{2, 2, 0}
}

type StartRunRequest_Option_Enum int32

const (
	// only start unstart ones
	StartRunRequest_Option_unspecified StartRunRequest_Option_Enum = 0
	// start unstart/failed/canceled ones
	StartRunRequest_Option_retry_nok StartRunRequest_Option_Enum = 1
)

// Enum value maps for StartRunRequest_Option_Enum.
var (
	StartRunRequest_Option_Enum_name = map[int32]string{
		0: "unspecified",
		1: "retry_nok",
	}
	StartRunRequest_Option_Enum_value = map[string]int32{
		"unspecified": 0,
		"retry_nok":   1,
	}
)

func (x StartRunRequest_Option_Enum) Enum() *StartRunRequest_Option_Enum {
	p := new(StartRunRequest_Option_Enum)
	*p = x
	return p
}

func (x StartRunRequest_Option_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StartRunRequest_Option_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_model_v1_run_proto_enumTypes[1].Descriptor()
}

func (StartRunRequest_Option_Enum) Type() protoreflect.EnumType {
	return &file_model_v1_run_proto_enumTypes[1]
}

func (x StartRunRequest_Option_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StartRunRequest_Option_Enum.Descriptor instead.
func (StartRunRequest_Option_Enum) EnumDescriptor() ([]byte, []int) {
	return file_model_v1_run_proto_rawDescGZIP(), []int{8, 0, 0}
}

type CreateRunRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [mandatory for create] run name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// [mandatory for create] model type: od/segmentation
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	// dataset used for this run
	Dataset *Run_Dataset `protobuf:"bytes,3,opt,name=dataset,proto3" json:"dataset,omitempty"`
	// define the conditions to stop training
	Target string `protobuf:"bytes,4,opt,name=target,proto3" json:"target,omitempty"`
	// maximum credits the run can consume
	MaxCredits int32 `protobuf:"varint,5,opt,name=max_credits,json=maxCredits,proto3" json:"max_credits,omitempty"`
	// base model name and version
	BaseModel string `protobuf:"bytes,6,opt,name=base_model,json=baseModel,proto3" json:"base_model,omitempty"`
	// tags attached to the run
	Tags []string `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`
	// training pipeline image
	Pipeline *Run_Pipeline `protobuf:"bytes,8,opt,name=pipeline,proto3" json:"pipeline,omitempty"`
	// uid of the model uploaded by this run
	ModelUid string `protobuf:"bytes,9,opt,name=model_uid,json=modelUid,proto3" json:"model_uid,omitempty"`
	// artifacts uploaded by this run
	Artifacts string `protobuf:"bytes,10,opt,name=artifacts,proto3" json:"artifacts,omitempty"`
}

func (x *CreateRunRequest) Reset() {
	*x = CreateRunRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_run_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRunRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRunRequest) ProtoMessage() {}

func (x *CreateRunRequest) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_run_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRunRequest.ProtoReflect.Descriptor instead.
func (*CreateRunRequest) Descriptor() ([]byte, []int) {
	return file_model_v1_run_proto_rawDescGZIP(), []int{0}
}

func (x *CreateRunRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateRunRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CreateRunRequest) GetDataset() *Run_Dataset {
	if x != nil {
		return x.Dataset
	}
	return nil
}

func (x *CreateRunRequest) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *CreateRunRequest) GetMaxCredits() int32 {
	if x != nil {
		return x.MaxCredits
	}
	return 0
}

func (x *CreateRunRequest) GetBaseModel() string {
	if x != nil {
		return x.BaseModel
	}
	return ""
}

func (x *CreateRunRequest) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *CreateRunRequest) GetPipeline() *Run_Pipeline {
	if x != nil {
		return x.Pipeline
	}
	return nil
}

func (x *CreateRunRequest) GetModelUid() string {
	if x != nil {
		return x.ModelUid
	}
	return ""
}

func (x *CreateRunRequest) GetArtifacts() string {
	if x != nil {
		return x.Artifacts
	}
	return ""
}

type UpdateRunRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid    string            `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Data   *CreateRunRequest `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Fields []string          `protobuf:"bytes,3,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *UpdateRunRequest) Reset() {
	*x = UpdateRunRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_run_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRunRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRunRequest) ProtoMessage() {}

func (x *UpdateRunRequest) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_run_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRunRequest.ProtoReflect.Descriptor instead.
func (*UpdateRunRequest) Descriptor() ([]byte, []int) {
	return file_model_v1_run_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateRunRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *UpdateRunRequest) GetData() *CreateRunRequest {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateRunRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type Run struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// run uid
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// run name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// model type: od/segmentation
	Type string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	// operator uid
	OperatorUid string `protobuf:"bytes,4,opt,name=operator_uid,json=operatorUid,proto3" json:"operator_uid,omitempty"`
	// dataset used for this run
	Dataset *Run_Dataset `protobuf:"bytes,5,opt,name=dataset,proto3" json:"dataset,omitempty"`
	// define the conditions to stop training
	Target string `protobuf:"bytes,6,opt,name=target,proto3" json:"target,omitempty"`
	// maximum credits the run can consume
	MaxCredits int32 `protobuf:"varint,7,opt,name=max_credits,json=maxCredits,proto3" json:"max_credits,omitempty"`
	// base model name and version
	BaseModel string `protobuf:"bytes,8,opt,name=base_model,json=baseModel,proto3" json:"base_model,omitempty"`
	// tags attached to the run
	Tags []string `protobuf:"bytes,9,rep,name=tags,proto3" json:"tags,omitempty"`
	// training pipeline
	Pipeline *Run_Pipeline `protobuf:"bytes,10,opt,name=pipeline,proto3" json:"pipeline,omitempty"`
	// state of the run
	State Run_State_Enum `protobuf:"varint,11,opt,name=state,proto3,enum=model.v1.Run_State_Enum" json:"state,omitempty"`
	// fail reason
	FailReason string `protobuf:"bytes,12,opt,name=fail_reason,json=failReason,proto3" json:"fail_reason,omitempty"`
	// uid of the model uploaded by this run
	ModelUid  string                 `protobuf:"bytes,13,opt,name=model_uid,json=modelUid,proto3" json:"model_uid,omitempty"`
	StartedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	EndedAt   *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// artifacts uploaded by this run
	Artifacts string `protobuf:"bytes,18,opt,name=artifacts,proto3" json:"artifacts,omitempty"`
}

func (x *Run) Reset() {
	*x = Run{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_run_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Run) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Run) ProtoMessage() {}

func (x *Run) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_run_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Run.ProtoReflect.Descriptor instead.
func (*Run) Descriptor() ([]byte, []int) {
	return file_model_v1_run_proto_rawDescGZIP(), []int{2}
}

func (x *Run) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Run) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Run) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Run) GetOperatorUid() string {
	if x != nil {
		return x.OperatorUid
	}
	return ""
}

func (x *Run) GetDataset() *Run_Dataset {
	if x != nil {
		return x.Dataset
	}
	return nil
}

func (x *Run) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *Run) GetMaxCredits() int32 {
	if x != nil {
		return x.MaxCredits
	}
	return 0
}

func (x *Run) GetBaseModel() string {
	if x != nil {
		return x.BaseModel
	}
	return ""
}

func (x *Run) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Run) GetPipeline() *Run_Pipeline {
	if x != nil {
		return x.Pipeline
	}
	return nil
}

func (x *Run) GetState() Run_State_Enum {
	if x != nil {
		return x.State
	}
	return Run_State_unspecified
}

func (x *Run) GetFailReason() string {
	if x != nil {
		return x.FailReason
	}
	return ""
}

func (x *Run) GetModelUid() string {
	if x != nil {
		return x.ModelUid
	}
	return ""
}

func (x *Run) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *Run) GetEndedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.EndedAt
	}
	return nil
}

func (x *Run) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Run) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Run) GetArtifacts() string {
	if x != nil {
		return x.Artifacts
	}
	return ""
}

type RunFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// operator uid
	OperatorUid string `protobuf:"bytes,1,opt,name=operator_uid,json=operatorUid,proto3" json:"operator_uid,omitempty"`
	// type
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	// states
	States []Run_State_Enum `protobuf:"varint,3,rep,packed,name=states,proto3,enum=model.v1.Run_State_Enum" json:"states,omitempty"`
	// name pattern
	NamePattern string `protobuf:"bytes,4,opt,name=name_pattern,json=namePattern,proto3" json:"name_pattern,omitempty"`
	// base model pattern
	BaseModelPattern string `protobuf:"bytes,5,opt,name=base_model_pattern,json=baseModelPattern,proto3" json:"base_model_pattern,omitempty"`
	// attached tags
	Tags []string `protobuf:"bytes,6,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *RunFilter) Reset() {
	*x = RunFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_run_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunFilter) ProtoMessage() {}

func (x *RunFilter) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_run_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunFilter.ProtoReflect.Descriptor instead.
func (*RunFilter) Descriptor() ([]byte, []int) {
	return file_model_v1_run_proto_rawDescGZIP(), []int{3}
}

func (x *RunFilter) GetOperatorUid() string {
	if x != nil {
		return x.OperatorUid
	}
	return ""
}

func (x *RunFilter) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RunFilter) GetStates() []Run_State_Enum {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *RunFilter) GetNamePattern() string {
	if x != nil {
		return x.NamePattern
	}
	return ""
}

func (x *RunFilter) GetBaseModelPattern() string {
	if x != nil {
		return x.BaseModelPattern
	}
	return ""
}

func (x *RunFilter) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

type GetRunRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *GetRunRequest) Reset() {
	*x = GetRunRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_run_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRunRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRunRequest) ProtoMessage() {}

func (x *GetRunRequest) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_run_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRunRequest.ProtoReflect.Descriptor instead.
func (*GetRunRequest) Descriptor() ([]byte, []int) {
	return file_model_v1_run_proto_rawDescGZIP(), []int{4}
}

func (x *GetRunRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type DeleteRunRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid    string     `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Filter *RunFilter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *DeleteRunRequest) Reset() {
	*x = DeleteRunRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_run_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRunRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRunRequest) ProtoMessage() {}

func (x *DeleteRunRequest) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_run_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRunRequest.ProtoReflect.Descriptor instead.
func (*DeleteRunRequest) Descriptor() ([]byte, []int) {
	return file_model_v1_run_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteRunRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *DeleteRunRequest) GetFilter() *RunFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type ListRunRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagesz int32 `protobuf:"varint,1,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// An opaque pagination token returned from a previous call.
	// An empty token denotes the first page.
	PageToken string     `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	Filter    *RunFilter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListRunRequest) Reset() {
	*x = ListRunRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_run_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRunRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRunRequest) ProtoMessage() {}

func (x *ListRunRequest) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_run_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRunRequest.ProtoReflect.Descriptor instead.
func (*ListRunRequest) Descriptor() ([]byte, []int) {
	return file_model_v1_run_proto_rawDescGZIP(), []int{6}
}

func (x *ListRunRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListRunRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListRunRequest) GetFilter() *RunFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type ListRunReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Runs []*Run `protobuf:"bytes,1,rep,name=runs,proto3" json:"runs,omitempty"`
	// An opaque pagination token, if not empty, to be used to fetch the next page of results
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// total number of items found; valid only in the first page reply.
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListRunReply) Reset() {
	*x = ListRunReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_run_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRunReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRunReply) ProtoMessage() {}

func (x *ListRunReply) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_run_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRunReply.ProtoReflect.Descriptor instead.
func (*ListRunReply) Descriptor() ([]byte, []int) {
	return file_model_v1_run_proto_rawDescGZIP(), []int{7}
}

func (x *ListRunReply) GetRuns() []*Run {
	if x != nil {
		return x.Runs
	}
	return nil
}

func (x *ListRunReply) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListRunReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type StartRunRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// start option
	Option StartRunRequest_Option_Enum `protobuf:"varint,2,opt,name=option,proto3,enum=model.v1.StartRunRequest_Option_Enum" json:"option,omitempty"`
}

func (x *StartRunRequest) Reset() {
	*x = StartRunRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_run_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartRunRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartRunRequest) ProtoMessage() {}

func (x *StartRunRequest) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_run_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartRunRequest.ProtoReflect.Descriptor instead.
func (*StartRunRequest) Descriptor() ([]byte, []int) {
	return file_model_v1_run_proto_rawDescGZIP(), []int{8}
}

func (x *StartRunRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *StartRunRequest) GetOption() StartRunRequest_Option_Enum {
	if x != nil {
		return x.Option
	}
	return StartRunRequest_Option_unspecified
}

type FinishRunRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// fail reason when the run should be failed; empty means the run should be finished successfully
	FailReason string `protobuf:"bytes,2,opt,name=fail_reason,json=failReason,proto3" json:"fail_reason,omitempty"`
}

func (x *FinishRunRequest) Reset() {
	*x = FinishRunRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_run_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinishRunRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinishRunRequest) ProtoMessage() {}

func (x *FinishRunRequest) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_run_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinishRunRequest.ProtoReflect.Descriptor instead.
func (*FinishRunRequest) Descriptor() ([]byte, []int) {
	return file_model_v1_run_proto_rawDescGZIP(), []int{9}
}

func (x *FinishRunRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *FinishRunRequest) GetFailReason() string {
	if x != nil {
		return x.FailReason
	}
	return ""
}

type Run_Dataset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// uris of the dataset
	Uris []string `protobuf:"bytes,1,rep,name=uris,proto3" json:"uris,omitempty"`
	// dataset size in giga bytes
	SizeGb int32 `protobuf:"varint,2,opt,name=size_gb,json=sizeGb,proto3" json:"size_gb,omitempty"`
}

func (x *Run_Dataset) Reset() {
	*x = Run_Dataset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_run_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Run_Dataset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Run_Dataset) ProtoMessage() {}

func (x *Run_Dataset) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_run_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Run_Dataset.ProtoReflect.Descriptor instead.
func (*Run_Dataset) Descriptor() ([]byte, []int) {
	return file_model_v1_run_proto_rawDescGZIP(), []int{2, 0}
}

func (x *Run_Dataset) GetUris() []string {
	if x != nil {
		return x.Uris
	}
	return nil
}

func (x *Run_Dataset) GetSizeGb() int32 {
	if x != nil {
		return x.SizeGb
	}
	return 0
}

type Run_Pipeline struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image   string   `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	Command []string `protobuf:"bytes,2,rep,name=command,proto3" json:"command,omitempty"` // repeated string args = 3;
}

func (x *Run_Pipeline) Reset() {
	*x = Run_Pipeline{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_run_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Run_Pipeline) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Run_Pipeline) ProtoMessage() {}

func (x *Run_Pipeline) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_run_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Run_Pipeline.ProtoReflect.Descriptor instead.
func (*Run_Pipeline) Descriptor() ([]byte, []int) {
	return file_model_v1_run_proto_rawDescGZIP(), []int{2, 1}
}

func (x *Run_Pipeline) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *Run_Pipeline) GetCommand() []string {
	if x != nil {
		return x.Command
	}
	return nil
}

type Run_State struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Run_State) Reset() {
	*x = Run_State{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_run_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Run_State) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Run_State) ProtoMessage() {}

func (x *Run_State) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_run_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Run_State.ProtoReflect.Descriptor instead.
func (*Run_State) Descriptor() ([]byte, []int) {
	return file_model_v1_run_proto_rawDescGZIP(), []int{2, 2}
}

type StartRunRequest_Option struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StartRunRequest_Option) Reset() {
	*x = StartRunRequest_Option{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_run_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartRunRequest_Option) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartRunRequest_Option) ProtoMessage() {}

func (x *StartRunRequest_Option) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_run_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartRunRequest_Option.ProtoReflect.Descriptor instead.
func (*StartRunRequest_Option) Descriptor() ([]byte, []int) {
	return file_model_v1_run_proto_rawDescGZIP(), []int{8, 0}
}

var File_model_v1_run_proto protoreflect.FileDescriptor

var file_model_v1_run_proto_rawDesc = []byte{
	0x0a, 0x12, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x75, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x0f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x74, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xcb, 0x02, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x2f, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6e, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d,
	0x61, 0x78, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x61, 0x73,
	0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62,
	0x61, 0x73, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x32, 0x0a, 0x08,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6e, 0x2e, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x08, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x69, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x73, 0x3a, 0x03, 0xba, 0x47, 0x00,
	0x22, 0x87, 0x01, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x3a,
	0x19, 0xba, 0x47, 0x16, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x04, 0x64, 0x61, 0x74,
	0x61, 0xba, 0x01, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22, 0xb6, 0x08, 0x0a, 0x03, 0x52,
	0x75, 0x6e, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x72, 0x13,
	0x32, 0x11, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24,
	0x7c, 0x5e, 0x24, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64,
	0x12, 0x2f, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6e,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x78,
	0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x6d, 0x61, 0x78, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x62, 0x61, 0x73, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67,
	0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x32, 0x0a,
	0x08, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6e, 0x2e, 0x50,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x08, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x12, 0x2e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6e, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x75, 0x69, 0x64, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x72, 0x13, 0x32, 0x11, 0x5e, 0x5b,
	0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x7c, 0x5e, 0x24, 0x52,
	0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x69, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x73, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x73, 0x1a,
	0x5b, 0x0a, 0x07, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x04, 0x75, 0x72,
	0x69, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0d, 0xfa, 0x42, 0x0a, 0x92, 0x01, 0x07,
	0x22, 0x05, 0x72, 0x03, 0x90, 0x01, 0x01, 0x52, 0x04, 0x75, 0x72, 0x69, 0x73, 0x12, 0x17, 0x0a,
	0x07, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x67, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x73, 0x69, 0x7a, 0x65, 0x47, 0x62, 0x3a, 0x14, 0xba, 0x47, 0x11, 0xba, 0x01, 0x04, 0x75, 0x72,
	0x69, 0x73, 0xba, 0x01, 0x07, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x67, 0x62, 0x1a, 0x3a, 0x0a, 0x08,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x1a, 0x62, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x22, 0x59, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x75, 0x6e,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x72, 0x75, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64,
	0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x04, 0x12, 0x0c,
	0x0a, 0x08, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64, 0x10, 0x05, 0x3a, 0x45, 0xba, 0x47,
	0x42, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01,
	0x04, 0x74, 0x79, 0x70, 0x65, 0xba, 0x01, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x75, 0x69, 0x64, 0xba, 0x01, 0x07, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0xba, 0x01,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0xba, 0x01, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x22, 0xf3, 0x01, 0x0a, 0x09, 0x52, 0x75, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x3b, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x72, 0x13, 0x32, 0x11,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x7c, 0x5e,
	0x24, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x30, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x18, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75,
	0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x74,
	0x74, 0x65, 0x72, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x61, 0x6d, 0x65,
	0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x62, 0x61, 0x73, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x61,
	0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x22, 0x38, 0x0a, 0x0d, 0x47, 0x65, 0x74,
	0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x22, 0x71, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x75, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72, 0x19, 0x32, 0x17, 0x5e, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x7c, 0x62, 0x79, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x2b, 0x0a, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x7f, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x75,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65,
	0x73, 0x7a, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18,
	0x64, 0x28, 0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2b, 0x0a, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x8d, 0x01, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x75, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x21, 0x0a, 0x04, 0x72, 0x75, 0x6e, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x75, 0x6e, 0x52, 0x04, 0x72, 0x75, 0x6e, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e,
	0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x3a, 0x1c, 0xba, 0x47, 0x19, 0xba, 0x01,
	0x04, 0x72, 0x75, 0x6e, 0x73, 0xba, 0x01, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xb5, 0x01, 0x0a, 0x0f, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32,
	0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52,
	0x03, 0x75, 0x69, 0x64, 0x12, 0x47, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x30, 0x0a,
	0x06, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x26, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12,
	0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00,
	0x12, 0x0d, 0x0a, 0x09, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x6b, 0x10, 0x01, 0x22,
	0x5c, 0x0a, 0x10, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x66, 0x61, 0x69, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x66, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x32, 0xc1, 0x06,
	0x0a, 0x04, 0x52, 0x75, 0x6e, 0x73, 0x12, 0x48, 0x0a, 0x06, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x12, 0x1a, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0d, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6e, 0x22, 0x13, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x0d, 0x3a, 0x01, 0x2a, 0x22, 0x08, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x75, 0x6e, 0x73,
	0x12, 0x51, 0x0a, 0x06, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0d, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x75, 0x6e, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x3a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x32, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x75, 0x6e, 0x73, 0x2f, 0x7b, 0x75,
	0x69, 0x64, 0x7d, 0x12, 0x5b, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x19, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x75, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a, 0x1a, 0x14, 0x2f, 0x76, 0x31, 0x2f,
	0x72, 0x75, 0x6e, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x12, 0x5e, 0x0a, 0x06, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x12, 0x1a, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x52, 0x75, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x20,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x1a, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x72,
	0x75, 0x6e, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x12, 0x5b, 0x0a, 0x06, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x12, 0x17, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x1a, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x75, 0x6e, 0x73,
	0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x12, 0x54, 0x0a,
	0x06, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x1a, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x16, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x10, 0x2a, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x75, 0x6e, 0x73, 0x2f, 0x7b, 0x75,
	0x69, 0x64, 0x7d, 0x12, 0x45, 0x0a, 0x03, 0x47, 0x65, 0x74, 0x12, 0x17, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x0d, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x75, 0x6e, 0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x12, 0x0e, 0x2f, 0x76, 0x31, 0x2f,
	0x72, 0x75, 0x6e, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x4a, 0x0a, 0x04, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x18, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x75, 0x6e, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x10, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0a, 0x12, 0x08, 0x2f, 0x76,
	0x31, 0x2f, 0x72, 0x75, 0x6e, 0x73, 0x12, 0x4d, 0x0a, 0x06, 0x41, 0x64, 0x64, 0x54, 0x61, 0x67,
	0x12, 0x11, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x61, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x0e, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x61, 0x67, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x04, 0x74, 0x61, 0x67,
	0x73, 0x1a, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x75, 0x6e, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64,
	0x7d, 0x2f, 0x74, 0x61, 0x67, 0x12, 0x4a, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54,
	0x61, 0x67, 0x12, 0x11, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x61, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0e, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x61,
	0x67, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x2a, 0x12, 0x2f,
	0x76, 0x31, 0x2f, 0x72, 0x75, 0x6e, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x74, 0x61,
	0x67, 0x42, 0x35, 0x5a, 0x33, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f,
	0x76, 0x31, 0x3b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_model_v1_run_proto_rawDescOnce sync.Once
	file_model_v1_run_proto_rawDescData = file_model_v1_run_proto_rawDesc
)

func file_model_v1_run_proto_rawDescGZIP() []byte {
	file_model_v1_run_proto_rawDescOnce.Do(func() {
		file_model_v1_run_proto_rawDescData = protoimpl.X.CompressGZIP(file_model_v1_run_proto_rawDescData)
	})
	return file_model_v1_run_proto_rawDescData
}

var file_model_v1_run_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_model_v1_run_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_model_v1_run_proto_goTypes = []interface{}{
	(Run_State_Enum)(0),              // 0: model.v1.Run.State.Enum
	(StartRunRequest_Option_Enum)(0), // 1: model.v1.StartRunRequest.Option.Enum
	(*CreateRunRequest)(nil),         // 2: model.v1.CreateRunRequest
	(*UpdateRunRequest)(nil),         // 3: model.v1.UpdateRunRequest
	(*Run)(nil),                      // 4: model.v1.Run
	(*RunFilter)(nil),                // 5: model.v1.RunFilter
	(*GetRunRequest)(nil),            // 6: model.v1.GetRunRequest
	(*DeleteRunRequest)(nil),         // 7: model.v1.DeleteRunRequest
	(*ListRunRequest)(nil),           // 8: model.v1.ListRunRequest
	(*ListRunReply)(nil),             // 9: model.v1.ListRunReply
	(*StartRunRequest)(nil),          // 10: model.v1.StartRunRequest
	(*FinishRunRequest)(nil),         // 11: model.v1.FinishRunRequest
	(*Run_Dataset)(nil),              // 12: model.v1.Run.Dataset
	(*Run_Pipeline)(nil),             // 13: model.v1.Run.Pipeline
	(*Run_State)(nil),                // 14: model.v1.Run.State
	(*StartRunRequest_Option)(nil),   // 15: model.v1.StartRunRequest.Option
	(*timestamppb.Timestamp)(nil),    // 16: google.protobuf.Timestamp
	(*types.TagRequest)(nil),         // 17: types.TagRequest
	(*emptypb.Empty)(nil),            // 18: google.protobuf.Empty
	(*types.TagList)(nil),            // 19: types.TagList
}
var file_model_v1_run_proto_depIdxs = []int32{
	12, // 0: model.v1.CreateRunRequest.dataset:type_name -> model.v1.Run.Dataset
	13, // 1: model.v1.CreateRunRequest.pipeline:type_name -> model.v1.Run.Pipeline
	2,  // 2: model.v1.UpdateRunRequest.data:type_name -> model.v1.CreateRunRequest
	12, // 3: model.v1.Run.dataset:type_name -> model.v1.Run.Dataset
	13, // 4: model.v1.Run.pipeline:type_name -> model.v1.Run.Pipeline
	0,  // 5: model.v1.Run.state:type_name -> model.v1.Run.State.Enum
	16, // 6: model.v1.Run.started_at:type_name -> google.protobuf.Timestamp
	16, // 7: model.v1.Run.ended_at:type_name -> google.protobuf.Timestamp
	16, // 8: model.v1.Run.updated_at:type_name -> google.protobuf.Timestamp
	16, // 9: model.v1.Run.created_at:type_name -> google.protobuf.Timestamp
	0,  // 10: model.v1.RunFilter.states:type_name -> model.v1.Run.State.Enum
	5,  // 11: model.v1.DeleteRunRequest.filter:type_name -> model.v1.RunFilter
	5,  // 12: model.v1.ListRunRequest.filter:type_name -> model.v1.RunFilter
	4,  // 13: model.v1.ListRunReply.runs:type_name -> model.v1.Run
	1,  // 14: model.v1.StartRunRequest.option:type_name -> model.v1.StartRunRequest.Option.Enum
	2,  // 15: model.v1.Runs.Create:input_type -> model.v1.CreateRunRequest
	3,  // 16: model.v1.Runs.Update:input_type -> model.v1.UpdateRunRequest
	10, // 17: model.v1.Runs.Start:input_type -> model.v1.StartRunRequest
	11, // 18: model.v1.Runs.Finish:input_type -> model.v1.FinishRunRequest
	6,  // 19: model.v1.Runs.Cancel:input_type -> model.v1.GetRunRequest
	7,  // 20: model.v1.Runs.Delete:input_type -> model.v1.DeleteRunRequest
	6,  // 21: model.v1.Runs.Get:input_type -> model.v1.GetRunRequest
	8,  // 22: model.v1.Runs.List:input_type -> model.v1.ListRunRequest
	17, // 23: model.v1.Runs.AddTag:input_type -> types.TagRequest
	17, // 24: model.v1.Runs.DeleteTag:input_type -> types.TagRequest
	4,  // 25: model.v1.Runs.Create:output_type -> model.v1.Run
	4,  // 26: model.v1.Runs.Update:output_type -> model.v1.Run
	18, // 27: model.v1.Runs.Start:output_type -> google.protobuf.Empty
	18, // 28: model.v1.Runs.Finish:output_type -> google.protobuf.Empty
	18, // 29: model.v1.Runs.Cancel:output_type -> google.protobuf.Empty
	18, // 30: model.v1.Runs.Delete:output_type -> google.protobuf.Empty
	4,  // 31: model.v1.Runs.Get:output_type -> model.v1.Run
	9,  // 32: model.v1.Runs.List:output_type -> model.v1.ListRunReply
	19, // 33: model.v1.Runs.AddTag:output_type -> types.TagList
	19, // 34: model.v1.Runs.DeleteTag:output_type -> types.TagList
	25, // [25:35] is the sub-list for method output_type
	15, // [15:25] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_model_v1_run_proto_init() }
func file_model_v1_run_proto_init() {
	if File_model_v1_run_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_model_v1_run_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRunRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_run_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRunRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_run_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Run); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_run_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_run_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRunRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_run_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRunRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_run_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRunRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_run_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRunReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_run_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartRunRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_run_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinishRunRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_run_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Run_Dataset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_run_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Run_Pipeline); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_run_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Run_State); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_run_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartRunRequest_Option); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_model_v1_run_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_model_v1_run_proto_goTypes,
		DependencyIndexes: file_model_v1_run_proto_depIdxs,
		EnumInfos:         file_model_v1_run_proto_enumTypes,
		MessageInfos:      file_model_v1_run_proto_msgTypes,
	}.Build()
	File_model_v1_run_proto = out.File
	file_model_v1_run_proto_rawDesc = nil
	file_model_v1_run_proto_goTypes = nil
	file_model_v1_run_proto_depIdxs = nil
}
