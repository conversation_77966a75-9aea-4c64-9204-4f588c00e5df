import os
import glob
import shutil
import zipfile
import sys

sys.path.append('.')
from customer.common import tools


def run(input_dir, input_param):
    file_counter = 0
    unzip_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_unzip')
    if os.path.exists(unzip_dir):
        shutil.rmtree(unzip_dir)
    with zipfile.ZipFile(input_dir, 'r') as zip_ref:
        zip_ref.extractall(unzip_dir)

    counter = 0
    pkg_counter = 0
    segment_dir = ''
    for json_dir in glob.glob(os.path.join(unzip_dir, '**', '*.json'), recursive=True):
        file_name = os.path.basename(json_dir)
        root_dir = os.path.dirname(json_dir)

        # create dir for each 5000 files
        if input_param != -1:
            if counter % input_param == 0:
                segment_dir = os.path.join(root_dir, 'segment' + str(round(counter / input_param) + 1))
                os.mkdir(segment_dir)
                os.mkdir(os.path.join(segment_dir, 'img'))
                os.mkdir(os.path.join(segment_dir, 'pre_label'))
                pkg_counter += 1
        else:
            segment_dir = root_dir
            os.mkdir(segment_dir)
            os.mkdir(os.path.join(segment_dir, 'img'))
            os.mkdir(os.path.join(segment_dir, 'pre_label'))
            pkg_counter += 1

        # move files to corresponding img and prelabel folders
        shutil.move(json_dir,
                    os.path.join(segment_dir, 'pre_label', file_name))
        shutil.move(os.path.join(root_dir, file_name.split('.json')[0] + '.png'),
                    os.path.join(segment_dir, 'img', file_name.split('.json')[0] + '.png'))

        counter += 1

    trans_result = {
        "code": 0,
        "output": unzip_dir,
        "err_msg": '成功',
        "summary": {
            '生成文件数': file_counter,
            '分包数': pkg_counter
        }
    }

    return trans_result


if __name__ == "__main__":
    args = tools.get_args()
    run(args.input, int(args.txt))

    # args_input = '/Users/<USER>/Downloads/pose_top1.zip'
    # run(args_input)