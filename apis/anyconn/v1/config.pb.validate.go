// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anyconn/v1/config.proto

package anyconn

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetVersionReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetVersionReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVersionReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetVersionReplyMultiError, or nil if none found.
func (m *GetVersionReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVersionReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	if len(errors) > 0 {
		return GetVersionReplyMultiError(errors)
	}

	return nil
}

// GetVersionReplyMultiError is an error wrapping multiple validation errors
// returned by GetVersionReply.ValidateAll() if the designated constraints
// aren't met.
type GetVersionReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVersionReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVersionReplyMultiError) AllErrors() []error { return m }

// GetVersionReplyValidationError is the validation error returned by
// GetVersionReply.Validate if the designated constraints aren't met.
type GetVersionReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVersionReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVersionReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVersionReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVersionReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVersionReplyValidationError) ErrorName() string { return "GetVersionReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetVersionReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVersionReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVersionReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVersionReplyValidationError{}
