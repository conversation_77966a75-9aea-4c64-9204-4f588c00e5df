package biz

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/apis/annofeed/v1"
	"gitlab.rp.konvery.work/platform/pkg/kfs"
	"gitlab.rp.konvery.work/platform/pkg/kmath"
)

// old format
// type ElemParamV1 struct {
// 	Pose    *anno.RawdataParam              // vehicle pose
// 	Lidar   *anno.RawdataParam              // lidar pose
// 	Cameras map[string][]*anno.RawdataParam // cameras parameters
// }

// func (o *ElemParamV1) GetImageParams(camName string) []*anno.RawdataParam {
// 	return o.Cameras[camName]
// }

type ElemParamV1 = annofeed.ParamsFile
type ElemParamV2 = annofeed.ParamFileV2
type ParamMeta struct{ Meta *annofeed.ParamFileMeta }

func (o *ParamMeta) IsV2() bool { return o != nil && o.Meta != nil && o.Meta.Version == "v2" }

type ElemParamGroup struct {
	ElemParamV1 *ElemParamV1
	ElemParamV2 *ElemParamV2
}

type ElemParamData struct {
	RawData   []byte
	ElemParam *ElemParamGroup
}

func LoadElemParamData(basedir, subdir, name string) (*ElemParamData, error) {
	fpath := filepath.Join(subdir, name)
	data, err := os.ReadFile(filepath.Join(basedir, fpath))
	if err != nil {
		return nil, fmt.Errorf("failed to read file %v: %w", fpath, err)
	}

	elemParamGroup, err := ParseElemParam(data)
	fmt.Println("---> elemParamGroup: ", elemParamGroup)
	if err != nil {
		return nil, fmt.Errorf("failed to parse elem param")
	}

	return &ElemParamData{RawData: data, ElemParam: elemParamGroup}, nil
}

func ParseParamMeta(data []byte) (*ParamMeta, error) {
	meta := &ParamMeta{}
	if err := JSONCodec().Unmarshal(data, meta); err != nil {
		return nil, err
	}
	return meta, nil
}

func ParseElemParam(data []byte) (ep *ElemParamGroup, err error) {
	meta, err := ParseParamMeta(data)
	if err != nil {
		return nil, err
	}
	if meta.IsV2() {
		paramV2 := &ElemParamV2{}
		if err := JSONCodec().Unmarshal(data, paramV2); err != nil {
			return nil, fmt.Errorf("failed to parse paramV2 file: %w", err)
		}
		return &ElemParamGroup{ElemParamV2: paramV2}, nil
	}

	p := &ElemParamV1{}
	if err = JSONCodec().Unmarshal(data, p); err == nil {
		return &ElemParamGroup{ElemParamV1: p}, nil
	}

	// work with old format
	of := &struct {
		Cameras map[string][]*anno.RawdataParam
	}{}
	if err = JSONCodec().Unmarshal(data, of); err != nil {
		return nil, fmt.Errorf("failed to unmarshal using old ElemParamV1 format: %w", err)
	}
	p.Cameras = map[string]*annofeed.CameraParam{}
	for k, v := range of.Cameras {
		p.Cameras[k] = &annofeed.CameraParam{
			Name:       k,
			Pose:       &annofeed.Pose{Pose: v[0].Data},
			Transforms: v[1:],
		}
	}
	return &ElemParamGroup{ElemParamV1: p}, nil
}

func (ep *ElemParamGroup) GetLidarTransforms(name string) ([]*anno.RawdataParam, error) {
	if ep == nil {
		return nil, nil
	}

	if p := ep.ElemParamV2; p != nil {
		if tr := p.GetLidar().GetTransform(); len(tr) > 0 {
			data, err := ExtrinsicToRawdataParam(tr)
			if err != nil {
				return nil, err
			}
			return []*anno.RawdataParam{data}, nil
		}
		return nil, nil
	}

	l := ep.ElemParamV1.Lidars[name]
	return l.GetTransforms(), nil
}

func (ep *ElemParamGroup) GetLidarViewpoint(name string) (*anno.RawdataParam, error) {
	if ep == nil {
		return nil, nil
	}

	if ep.ElemParamV2 != nil {
		return ExtrinsicToRawdataParam(ep.ElemParamV2.GetLidar().GetViewpoint())
	}

	l := ep.ElemParamV1.GetLidars()[kfs.FileBareName(name)]
	// FIXME: should make coord-sys conversion if l.Pose.CoordSys != ""
	viewpoint, err := ExtrinsicToRawdataParam(l.GetPose().GetPose())
	return viewpoint, err
}

func (ep *ElemParamGroup) GetLidarPose() []float64 {
	if ep == nil {
		return nil
	}

	var pose []float64
	if ep.ElemParamV2 != nil {
		pose = ep.ElemParamV2.GetLidar().GetPose()
	} else {
		pose = ep.ElemParamV1.GetVehicle().GetPose().GetPose()
	}

	switch len(pose) {
	case 7:
		return pose
	case 16:
		p := kmath.MatrixToPose(pose, false)
		return p[:]
	}
	return nil
}

func (ep *ElemParamGroup) SetLidarPose(data []float64) {
	if ep == nil {
		return
	}

	if len(data) == 16 {
		p := kmath.PoseToMatrix(data, true)
		data = p[:]
	}

	if ep.ElemParamV2 != nil {
		if ep.ElemParamV2.Lidar == nil {
			ep.ElemParamV2.Lidar = &annofeed.LidarParamV2{}
		}
		ep.ElemParamV2.Lidar.Pose = data
		return
	}

	vehicle := ep.ElemParamV1.GetVehicle()
	if vehicle == nil {
		ep.ElemParamV1.Vehicle = &annofeed.VehicleParam{}
	}
	if vehicle.Pose == nil {
		vehicle.Pose = &annofeed.Pose{}
	}
	vehicle.Pose.Pose = data
	return
}

// GetImageTransforms returns the transform steps to map LiDAR points to pixels.
func (ep *ElemParamGroup) GetImageTransforms(camName string) ([]*anno.RawdataParam, error) {
	if ep == nil {
		return nil, nil
	}

	if ep.ElemParamV2 != nil {
		return ep.getImageTransformsV2(camName)
	}

	cam := ep.ElemParamV1.Cameras[camName]
	if cam == nil {
		return nil, nil
	}
	trans, err := GetCoordTransforms(ep.ElemParamV1.CoordSystems, cam.Pose)
	if err != nil {
		return nil, err
	}
	return append(trans, cam.Transforms...), nil
}

func (ep *ElemParamGroup) getImageTransformsV2(camName string) ([]*anno.RawdataParam, error) {
	if ep == nil {
		return nil, nil
	}

	cameras := ep.ElemParamV2.Cameras
	if cameras[camName] == nil {
		return nil, nil
	}

	var imageParams []*anno.RawdataParam
	extrinsicParam, err := ExtrinsicToRawdataParam(cameras[camName].GetExtrinsic())
	if err != nil {
		return nil, err
	}
	if extrinsicParam != nil {
		imageParams = append(imageParams, extrinsicParam)
	}

	intrinsicParam, err := IntrinsicToRawdataParam(cameras[camName].GetIntrinsic())
	if err != nil {
		return nil, err
	}
	if intrinsicParam != nil {
		imageParams = append(imageParams, intrinsicParam)
	}

	distortionParam, err := DistortionToRawdataParam(cameras[camName].GetDistortion())
	if err != nil {
		return nil, err
	}
	if intrinsicParam != nil {
		imageParams = append(imageParams, distortionParam)
	}

	return imageParams, nil
}

func (ep *ElemParamGroup) GetImageTitle(camName string) string {
	if ep == nil {
		return ""
	}

	if ep.ElemParamV2 != nil {
		return ep.ElemParamV2.Cameras[camName].GetTitle()
	}
	return ep.ElemParamV1.Cameras[camName].GetTitle()
}

func (ep *ElemParamGroup) WriteFile(fpath string) error {
	if ep == nil {
		return nil
	}

	var data []byte
	var err error
	if ep.ElemParamV2 != nil {
		data, err = json.Marshal(ep.ElemParamV2)
	} else {
		data, err = json.Marshal(ep.ElemParamV1)
	}
	if err != nil {
		return err
	}

	return os.WriteFile(fpath, data, 0644)
}

func ValidateRawdataParam(param *anno.RawdataParam) error {
	if param == nil {
		return nil
	}
	switch param.Type {
	case anno.RawdataParam_Type_matrix:
		if param.ColumnCnt == 0 {
			return fmt.Errorf("column count of matrix params must be positive")
		}
		if len(param.Data) == 0 {
			return fmt.Errorf("data of matrix params cannot be empty")
		}
		if len(param.Data)%int(param.ColumnCnt) != 0 {
			return fmt.Errorf("data length=%d of matrix params cannot be divided by column count=%d", len(param.Data), param.ColumnCnt)
		}
	case anno.RawdataParam_Type_extrinsic:
		if param.ColumnCnt != 1 {
			return fmt.Errorf("column count of extrinsic params must be 1")
		}
		if len(param.Data) != 7 { // x,y,z,qx,qy,qz,qw
			return fmt.Errorf("data length of extrinsic params must be 7 (x,y,z,qx,qy,qz,qw)")
		}
	case anno.RawdataParam_Type_intrinsic:
		if param.ColumnCnt != 1 {
			return fmt.Errorf("column count of intrinsic params must be 1")
		}
		if len(param.Data) != 4 { // fx,fy,cx,cy
			return fmt.Errorf("data length of intrinsic params must be 4 (fx,fy,cx,cy)")
		}
	case anno.RawdataParam_Type_distortion:
		if param.ColumnCnt != 1 {
			return fmt.Errorf("column count of distortion params must be 1")
		}
		if len(param.Data) == 0 {
			return fmt.Errorf("data of distortion params cannot be empty")
		}
		if _, ok := anno.RawdataParam_DistortionType_Enum_name[int32(param.Data[0])]; !ok {
			return fmt.Errorf("invalid distortion type: %f", param.Data[0])
		}
	}
	return nil
}

// GetCoordTransforms returns a transform list according to the coordinate system hierarchy.
func GetCoordTransforms(coords map[string]*annofeed.CoordSys, pose *annofeed.Pose) ([]*anno.RawdataParam, error) {
	if pose == nil {
		return nil, nil
	}

	cs := make([]*annofeed.CoordSys, 0, len(coords))
	name := pose.CoordSys
	for name != "" {
		if c := coords[name]; c != nil {
			cs = append(cs, c)
			name = c.Parent
		} else {
			return nil, fmt.Errorf("parent coords cannot be empty")
		}
	}

	var err error
	r := make([]*anno.RawdataParam, len(cs)+1)
	if len(pose.Pose) > 0 {
		// TODO: do coordinate system conversion according to coordinate system types
		r[len(r)-1], err = ExtrinsicToRawdataParam(pose.Pose)
		if err != nil {
			return nil, err
		}
	} else {
		r = r[:len(r)-1]
	}
	for i := len(cs) - 1; i >= 0; i-- {
		// TODO: do coordinate system conversion according to coordinate system types
		r[len(cs)-1-i], err = ExtrinsicToRawdataParam(cs[i].Origin)
		if err != nil {
			return nil, err
		}
	}
	return r, nil
}

func ExtrinsicToRawdataParam(extrinsic []float64) (*anno.RawdataParam, error) {
	if len(extrinsic) == 0 {
		return nil, nil
	}

	switch len(extrinsic) {
	case 16:
		return &anno.RawdataParam{
			Type:      anno.RawdataParam_Type_matrix,
			ColumnCnt: 4,
			Data:      extrinsic,
		}, nil
	case 7: // x,y,z,qx,qy,qz,qw
		return &anno.RawdataParam{
			Type:      anno.RawdataParam_Type_extrinsic,
			ColumnCnt: 1,
			Data:      extrinsic,
		}, nil
	default:
		return nil, fmt.Errorf("unsupported extrinsic data length: %d", len(extrinsic))
	}
}

func IntrinsicToRawdataParam(intrinsic []float64) (*anno.RawdataParam, error) {
	if len(intrinsic) == 0 {
		return nil, nil
	}

	var data []float64
	switch len(intrinsic) {
	case 4:
		data = intrinsic
	case 9:
		data = []float64{intrinsic[0], intrinsic[4], intrinsic[2], intrinsic[5]} // fx fy cx cy
	default:
		return nil, fmt.Errorf("unsupported intrinsic data length: %d", len(intrinsic))
	}

	return &anno.RawdataParam{
		Type:      anno.RawdataParam_Type_intrinsic,
		ColumnCnt: 1,
		Data:      data,
	}, nil
}

func DistortionToRawdataParam(distortion []float64) (*anno.RawdataParam, error) {
	if len(distortion) == 0 {
		return nil, nil
	}

	distType := anno.RawdataParam_DistortionType_Enum(distortion[0])
	distTypeName := distType.String()
	dataCnt := len(distortion) - 1
	errFormat := "unsupported distortion data length=%d for distortion type=%s"
	switch distType {
	case anno.RawdataParam_DistortionType_pinhole:
		if dataCnt != 4 && dataCnt != 5 && dataCnt != 8 {
			return nil, fmt.Errorf(errFormat, dataCnt, distTypeName)
		}
	case anno.RawdataParam_DistortionType_mei:
	case anno.RawdataParam_DistortionType_kb:
		if dataCnt != 4 {
			return nil, fmt.Errorf(errFormat, dataCnt, distTypeName)
		}
	case anno.RawdataParam_DistortionType_ocam:
		realDataCnt := int(distortion[len(distortion)-1])
		if dataCnt != realDataCnt+6 {
			return nil, fmt.Errorf(errFormat, dataCnt, distTypeName)
		}
	default:
		return nil, fmt.Errorf("unsupported distortion type=%d", distType)
	}

	return &anno.RawdataParam{
		Type:      anno.RawdataParam_Type_distortion,
		ColumnCnt: 1,
		Data:      distortion,
	}, nil
}
