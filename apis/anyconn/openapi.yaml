# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
paths:
    /anyconn/v1/batches:
        get:
            tags:
                - Batches
            operationId: Batches_ListBatch
            parameters:
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: org_uid
                  in: query
                  description: filter by orgnization
                  schema:
                    type: string
                - name: creator_uid
                  in: query
                  description: filter by creator
                  schema:
                    type: string
                - name: name_pattern
                  in: query
                  description: filter by name pattern
                  schema:
                    type: string
                - name: states
                  in: query
                  description: filter by batch state
                  schema:
                    type: array
                    items:
                        enum:
                            - unspecified
                            - initializing
                            - waiting
                            - ongoing
                            - finished
                            - canceled
                            - failed
                        type: string
                        format: enum
                - name: with_org
                  in: query
                  description: include batch's orgnization in the reply
                  schema:
                    type: boolean
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anyconn.v1.ListBatchReply'
        post:
            tags:
                - Batches
            operationId: Batches_CreateBatch
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anyconn.v1.CreateBatchRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anyconn.v1.Batch'
    /anyconn/v1/batches/{batch.uid}:
        patch:
            tags:
                - Batches
            operationId: Batches_UpdateBatch
            parameters:
                - name: batch.uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: fields
                  in: query
                  description: name of fileds to be updated
                  schema:
                    type: array
                    items:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anyconn.v1.Batch'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anyconn.v1.Batch'
    /anyconn/v1/batches/{uid}:
        get:
            tags:
                - Batches
            operationId: Batches_GetBatch
            parameters:
                - name: uid
                  in: path
                  description: batch UID
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anyconn.v1.Batch'
        delete:
            tags:
                - Batches
            operationId: Batches_DeleteBatch
            parameters:
                - name: uid
                  in: path
                  description: batch UID
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /anyconn/v1/batches/{uid}/anyconn-result:
        get:
            tags:
                - Batches
            operationId: Batches_GetAnnoResult
            parameters:
                - name: uid
                  in: path
                  description: batch UID
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anyconn.v1.GetBatchAnnoResultReply'
        put:
            tags:
                - Batches
            operationId: Batches_SetAnnoResult
            parameters:
                - name: uid
                  in: path
                  description: batch UID
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anyconn.v1.SetBatchAnnoResultRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anyconn/v1/batches/{uid}/cancel:
        put:
            tags:
                - Batches
            description: only platform admin can cancel batches
            operationId: Batches_CancelBatch
            parameters:
                - name: uid
                  in: path
                  description: batch UID
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anyconn.v1.GetBatchRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anyconn/v1/secrets:
        get:
            tags:
                - Secrets
            operationId: Secrets_ListSecret
            parameters:
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: org_uid
                  in: query
                  description: filter by orgnization
                  schema:
                    type: string
                - name: creator_uid
                  in: query
                  description: filter by creator
                  schema:
                    type: string
                - name: name_pattern
                  in: query
                  description: filter by name pattern
                  schema:
                    type: string
                - name: states
                  in: query
                  description: filter by secret state
                  schema:
                    type: array
                    items:
                        enum:
                            - unspecified
                            - initializing
                            - waiting
                            - ongoing
                            - finished
                            - canceled
                            - failed
                        type: string
                        format: enum
                - name: with_org
                  in: query
                  description: include secret's orgnization in the reply
                  schema:
                    type: boolean
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anyconn.v1.ListSecretReply'
        post:
            tags:
                - Secrets
            operationId: Secrets_CreateSecret
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anyconn.v1.CreateSecretRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anyconn.v1.Secret'
    /anyconn/v1/secrets/{secret.uid}:
        patch:
            tags:
                - Secrets
            operationId: Secrets_UpdateSecret
            parameters:
                - name: secret.uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: fields
                  in: query
                  description: name of fileds to be updated
                  schema:
                    type: array
                    items:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anyconn.v1.Secret'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anyconn.v1.Secret'
    /anyconn/v1/secrets/{uid}:
        get:
            tags:
                - Secrets
            operationId: Secrets_GetSecret
            parameters:
                - name: uid
                  in: path
                  description: secret UID
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anyconn.v1.Secret'
        delete:
            tags:
                - Secrets
            operationId: Secrets_DeleteSecret
            parameters:
                - name: uid
                  in: path
                  description: secret UID
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /anyconn/v1/version:
        get:
            tags:
                - Configs
            operationId: Configs_GetVersion
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anyconn.v1.GetVersionReply'
    /anyconn/v1/webhookevts:
        get:
            tags:
                - WebhookEvts
            operationId: WebhookEvts_ListWebhookEvt
            parameters:
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: |-
                    An opaque pagination token returned from a previous call.
                     An empty token denotes the first page.
                  schema:
                    type: string
                - name: filter.task_uid
                  in: query
                  description: filter by task
                  schema:
                    type: string
                - name: filter.job_uid
                  in: query
                  description: filter by job
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anyconn.v1.ListWebhookEvtReply'
        post:
            tags:
                - WebhookEvts
            operationId: WebhookEvts_CreateWebhookEvt
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anyconn.v1.CreateWebhookEvtRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anyconn.v1.WebhookEvt'
    /anyconn/v1/webhookevts/{uid}:
        get:
            tags:
                - WebhookEvts
            operationId: WebhookEvts_GetWebhookEvt
            parameters:
                - name: uid
                  in: path
                  description: webhookevt UID
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anyconn.v1.WebhookEvt'
        delete:
            tags:
                - WebhookEvts
            operationId: WebhookEvts_DeleteWebhookEvt
            parameters:
                - name: uid
                  in: path
                  description: webhookevt UID
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
        patch:
            tags:
                - WebhookEvts
            operationId: WebhookEvts_UpdateWebhookEvt
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: fields
                  in: query
                  description: name of fileds to be updated
                  schema:
                    type: array
                    items:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anyconn.v1.CreateWebhookEvtRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anyconn.v1.WebhookEvt'
components:
    schemas:
        anyconn.v1.Batch:
            required:
                - uid
                - name
                - org_uid
                - state
                - created_at
            type: object
            properties:
                uid:
                    type: string
                    description: batch UID
                name:
                    type: string
                    description: batch name
                org_uid:
                    type: string
                    description: UID of the organization which the batch belongs to
                source:
                    allOf:
                        - $ref: '#/components/schemas/anyconn.v1.Source'
                    description: files attached to the batch
                data_uid:
                    type: string
                    description: UID of the data associated with the batch
                creator_uid:
                    type: string
                    description: creator UID
                size:
                    type: integer
                    description: number of elements in the batch
                    format: int32
                state:
                    enum:
                        - unspecified
                        - initializing
                        - waiting
                        - ongoing
                        - finished
                        - canceled
                        - failed
                    type: string
                    description: batch state
                    format: enum
                ins_total:
                    type: integer
                    description: annotated object count (include interpolated ones); only available after lot is finished
                    format: int32
                anno_result_url:
                    type: string
                    description: annotation result file URL
                error:
                    type: string
                    description: when state is failed, this field will contain detailed error message
                created_at:
                    type: string
                    description: batch creation time
                    format: date-time
                data_summary:
                    readOnly: true
                    allOf:
                        - $ref: '#/components/schemas/anyconn.v1.DataValidationSummary'
                    description: data validation summary
        anyconn.v1.CreateBatchRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: mandatory in update-requests
                name:
                    type: string
                    description: |-
                        mandatory in create-requests;
                         pattern: ^[\\p{Han}\\w\\d-]{0,20}$
                org_uid:
                    type: string
                    description: batch's organization; if empty, this is the requestor's organization
                source:
                    allOf:
                        - $ref: '#/components/schemas/anyconn.v1.Source'
                    description: files attached to the batch
        anyconn.v1.CreateSecretRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: mandatory in update-requests
                name:
                    type: string
                    description: |-
                        mandatory in create-requests;
                         pattern: ^[\\p{Han}\\w\\d-]{0,20}$
                org_uid:
                    type: string
                    description: secret's organization; if empty, this is the requestor's organization
        anyconn.v1.CreateWebhookEvtRequest:
            type: object
            properties:
                platform:
                    type: string
                    description: platform if it is from a third-party platform
                evt_id:
                    type: string
                    description: unique event id within the platform
                project_uid:
                    type: string
                    description: UID of the project which the webhookevt belongs to
                task_uid:
                    type: string
                    description: UID of the task which the webhookevt belongs to
                job_uid:
                    type: string
                    description: UID of the job which the webhookevt belongs to
                body:
                    type: string
                    description: event body
        anyconn.v1.DataValidationSummary:
            type: object
            properties:
                total_errors:
                    type: integer
                    description: total number of validation errors found, includes those unsaved errors
                    format: int32
                errors:
                    type: array
                    items:
                        $ref: '#/components/schemas/anyconn.v1.DataValidationSummary_Error'
                    description: validation errors; only the first 10 errors are saved
        anyconn.v1.DataValidationSummary_Error:
            type: object
            properties:
                error:
                    enum:
                        - unspecified
                        - file_unknown
                        - file_missing
                    type: string
                    format: enum
                elem_index:
                    type: integer
                    format: int32
                rawdata_name:
                    type: string
        anyconn.v1.GetBatchAnnoResultReply:
            type: object
            properties:
                will_ready_at:
                    type: string
                    description: |-
                        the estimated time when the result is ready;
                         valid only when the batch is in the finished state
                    format: date-time
                url:
                    type: string
                    description: URL to the result if available
        anyconn.v1.GetBatchRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: batch UID
        anyconn.v1.GetVersionReply:
            type: object
            properties:
                version:
                    type: string
        anyconn.v1.ListBatchReply:
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                batches:
                    type: array
                    items:
                        $ref: '#/components/schemas/anyconn.v1.Batch'
        anyconn.v1.ListSecretReply:
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                secrets:
                    type: array
                    items:
                        $ref: '#/components/schemas/anyconn.v1.Secret'
        anyconn.v1.ListWebhookEvtReply:
            required:
                - webhookevts
                - next_page_token
            type: object
            properties:
                webhookevts:
                    type: array
                    items:
                        $ref: '#/components/schemas/anyconn.v1.WebhookEvt'
                next_page_token:
                    type: string
                    description: An opaque pagination token, if not empty, to be used to fetch the next page of results
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
        anyconn.v1.Secret:
            required:
                - uid
                - name
                - org_uid
                - state
                - created_at
            type: object
            properties:
                uid:
                    type: string
                    description: secret UID
                name:
                    type: string
                    description: secret name
                org_uid:
                    type: string
                    description: UID of the organization which the secret belongs to
                data_uid:
                    type: string
                    description: UID of the data associated with the secret
                creator_uid:
                    type: string
                    description: creator UID
                size:
                    type: integer
                    description: number of elements in the secret
                    format: int32
                state:
                    enum:
                        - unspecified
                        - initializing
                        - waiting
                        - ongoing
                        - finished
                        - canceled
                        - failed
                    type: string
                    description: secret state
                    format: enum
                ins_total:
                    type: integer
                    description: annotated object count (include interpolated ones); only available after lot is finished
                    format: int32
                anno_result_url:
                    type: string
                    description: annotation result file URL
                error:
                    type: string
                    description: when state is failed, this field will contain detailed error message
                created_at:
                    type: string
                    description: secret creation time
                    format: date-time
        anyconn.v1.SetBatchAnnoResultRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: batch UID
                url:
                    type: string
                    description: URL to the result
        anyconn.v1.Source:
            required:
                - uris
            type: object
            properties:
                uris:
                    type: array
                    items:
                        type: string
                    description: package file (.zip) URIs
                proprietary:
                    allOf:
                        - $ref: '#/components/schemas/anyconn.v1.Source_Proprietary'
                    description: access config when the files are hosted in a third-party platform
                style:
                    type: string
                    description: folder layout style within package files if not conform to Konvery standard
                is_frame_series:
                    type: boolean
                    description: |-
                        // element type
                         Element.Type.Enum elem_type = 4;
                         if source contains consecutive frames
                plain_size_gb:
                    type: integer
                    description: size of the unpacked data in GB
                    format: int32
                error_handlers:
                    type: array
                    items:
                        $ref: '#/components/schemas/anyconn.v1.Source_ParseErrorHandler'
                    description: |-
                        define parser error handlers; it will fail the parser if no handler is specified.
                         max size is count(RawdataErrorAction.Error) x count(Rawdata.Type).
        anyconn.v1.Source_ParseErrorHandler:
            type: object
            properties:
                error:
                    enum:
                        - unspecified
                        - file_unknown
                        - file_missing
                    type: string
                    description: anyconn.v1.Rawdata.Type.Enum rawdata_type = 1; // image or pcd
                    format: enum
                handler:
                    enum:
                        - unspecified
                        - fail
                        - ignore
                    type: string
                    format: enum
        anyconn.v1.Source_Proprietary:
            required:
                - type
                - config
            type: object
            properties:
                type:
                    type: string
                    description: 3rd-party file host service type
                config:
                    type: string
                    description: 3rd-party file host service access config
        anyconn.v1.WebhookEvt:
            required:
                - uid
                - webhookevted
                - created_at
            type: object
            properties:
                uid:
                    type: string
                    description: webhookevt UID
                platform:
                    type: string
                    description: platform if it is from a third-party platform
                evt_id:
                    type: string
                    description: unique event id within the platform
                project_uid:
                    type: string
                    description: UID of the project which the webhookevt belongs to
                task_uid:
                    type: string
                    description: UID of the task which the webhookevt belongs to
                job_uid:
                    type: string
                    description: UID of the job which the webhookevt belongs to
                done:
                    type: boolean
                    description: if the webhookevt is done
                extra:
                    type: string
                    description: extra info about the webhookevt
                body:
                    type: string
                    description: event body
                converted_annos:
                    type: string
                    description: converted annotations
                errors:
                    type: string
                    description: errors if any
                created_at:
                    type: string
                    description: webhookevt creation time
                    format: date-time
tags:
    - name: Batches
    - name: Configs
    - name: Secrets
    - name: WebhookEvts
