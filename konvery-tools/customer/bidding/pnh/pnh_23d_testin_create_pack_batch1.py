import os
import sys
import shutil
import numpy as np
import math
sys.path.append('.')
from customer.common import tools
from pyproj import Transformer



def get_nearest_frame(src, dsts):
    distance = math.inf
    target = None
    for dst in dsts:
        if abs(dst-src) < distance:
            distance = abs(dst-src)
            target = dst
    return target


def wgs84_to_epsg(lon, lat):
    """
    待转换的经纬度点lon, lat
    epsg编码对照：https://www.cnblogs.com/tangjielin/p/16561258.html
    """
    transformer = Transformer.from_crs("EPSG:4326", "EPSG:4528")
    x, y = transformer.transform(lat, lon)
    return x, y


def parse_params(params_file, camera_names):
    cameras = dict()
    params_data = tools.get_json_data(params_file)
    lidar2body = np.array(params_data['m1Lidar']['lidar_to_body_paradata']['data']).reshape(4, 4)
    for camera_name in camera_names:
        sensor_params = params_data[camera_name]
        camera2body = np.linalg.inv(np.array(sensor_params['camera_to_body_paradata']['data']).reshape(4, 4))
        camera_model_param = sensor_params['camera_model_param']
        if sensor_params['model_type'] == 'equidistant':
            camera_model = 'fisheye'
        elif sensor_params['model_type'] == 'radtan':
            camera_model = 'pinhole'
        else:
            raise ValueError(f'Unknown model_type: {sensor_params["model_type"]}')
        cameras[camera_name] = {
            'camera_model': camera_model,
            'fx': camera_model_param['fx'],
            'fy': camera_model_param['fy'],
            'cx': camera_model_param['cx'],
            'cy': camera_model_param['cy'],
            'k1': camera_model_param.get('k1', 0),
            'k2': camera_model_param.get('k2', 0),
            'p1': camera_model_param.get('p1', 0),
            'p2': camera_model_param.get('p2', 0),
            'k3': camera_model_param.get('k3', 0),
            'k4': camera_model_param.get('k4', 0),
            'k5': camera_model_param.get('k5', 0),
            'k6': camera_model_param.get('k6', 0),
            # 'extrinsic': (np.linalg.inv(camera2body) @ lidar2body).tolist(),
            'extrinsic': camera2body.tolist(),
        }
    return cameras


def run(input_dir, output_dir):
    out_clip_dir = os.path.join(output_dir, os.path.basename(input_dir))
    os.mkdir(out_clip_dir)
    out_camera_dir = os.path.join(out_clip_dir, 'camera')
    os.mkdir(out_camera_dir)
    out_lidar_dir = os.path.join(out_clip_dir, 'lidar')
    os.mkdir(out_lidar_dir)
    data_dir = os.path.join(input_dir, f'{os.path.basename(input_dir)}_1Hz')
    camera_dir = os.path.join(data_dir, 'camera')
    lidar_dir = os.path.join(data_dir, 'lidar')
    pose_dir = os.path.join(data_dir, 'Ins')
    camera_names = tools.listdir(camera_dir)
    pcds = tools.listdir(lidar_dir, full_path=True, sort=True)
    pose_files = tools.listdir(pose_dir, full_path=True, sort=True)
    assert len(pcds) == len(pose_files), f'pcd and pose_file mismatch, {len(pcds)} != {len(pose_files)}'
    image_mapping = dict()
    for camera_name in camera_names:
        image_mapping[camera_name] = tools.listdir(os.path.join(camera_dir, camera_name), full_path=True, sort=True)
        os.mkdir(os.path.join(out_camera_dir, camera_name))
        assert len(pcds) == len(image_mapping[camera_name]), f'pcd and image_file mismatch, {len(pcds)}!= {len(image_mapping[camera_name])}'
    poses = dict()
    for idx in range(len(pcds)):
        lidar_file = pcds[idx]
        frame_name = os.path.basename(lidar_file).rsplit('.', 1)[0]
        pose_data = tools.get_json_data(pose_files[idx])
        x, y = wgs84_to_epsg(float(pose_data['ins_longitude']), float(pose_data['ins_latitude']))
        pose_mat = tools.get_extrinsic_by_txyz_rxyz_opencv(
            np.array([x, y, float(pose_data['ins_locat_height'])]),
            np.array([pose_data['ins_roll_angle'], pose_data['ins_pitch_angle'], pose_data['ins_heading_angle']], dtype=np.float32),
        )
        poses[frame_name] = pose_mat.T.flatten().tolist()
        for camera_name in camera_names:
            img_file = image_mapping[camera_name][idx]
            shutil.copyfile(img_file, os.path.join(out_camera_dir, camera_name, f'{frame_name}.jpg'))
        shutil.copyfile(lidar_file, os.path.join(out_lidar_dir, os.path.basename(lidar_file)))

    config = {
        "data_type": "fusion_pointcloud",
        "sensor_params": parse_params(os.path.join(input_dir, 'params.txt'), camera_names),
        "poses": poses,
        "camera": {cam: cam for cam in camera_names}
    }
    tools.write_json_file(config, os.path.join(out_clip_dir, 'config.json'), indent=4)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
