package common

import (
	"encoding/json"
	"reflect"
)

type (
	internalTypeName  string
	internalByteValue json.RawMessage
)

type Details struct {
	Type      internalTypeName
	ByteValue internalByteValue
	value     any
}

func NewDetails(v any) *Details {
	name := RegTypeOf(v)
	return &Details{
		Type:  internalTypeName(name),
		value: v,
	}
}

func (o *Details) Set(v any) {
	name := RegTypeOf(v)
	o.value = v
	o.ByteValue = nil
	o.Type = internalTypeName(name)
}

// Value returns the wrapped value.
// If unmarshal is needed and the Type is not registered, hint's type is used instead.
func (o *Details) Value(hint any) any {
	if o.value != nil {
		return o.value
	}
	if len(o.ByteValue) == 0 || (o.Type == "" && hint == nil) {
		return nil
	}

	t := GetType(string(o.Type))
	if t == nil {
		if hint == nil {
			return nil
		}
		t = reflect.TypeOf(hint)
		RegType(NameOf(t), t)
	}
	v := NewValuePointerOf(t)
	pv := v.Interface()
	if err := json.Unmarshal(o.ByteValue, pv); err != nil {
		panic("failed to unmarshal details: " + err.Error())
	}
	if t.Kind() == reflect.Pointer {
		o.value = pv
	} else {
		o.value = v.Elem().Interface()
	}
	return o.value
}

func (o *Details) MarshalJSON() (data []byte, err error) {
	if o.ByteValue == nil && o.value == nil && o.Type == "" {
		return nil, nil
	}

	if o.ByteValue == nil && o.value != nil {
		data, err = json.Marshal(o.value)
		if err != nil {
			return nil, err
		}
		o.ByteValue = data
	}
	return json.Marshal(map[string]any{
		detailsFldType.Name:      o.Type,
		detailsFldByteValue.Name: o.ByteValue,
	})
}

func (o Details) getFld(name string) *reflect.StructField {
	fld, ok := reflect.TypeOf(o).FieldByName(name)
	if !ok {
		panic("field not found")
	}
	return &fld
}

func GetDetailsValue[T any](d *Details) *T {
	if d == nil {
		return nil
	}
	return d.Value((*T)(nil)).(*T)
}

var (
	detailsFldType      = Details{}.getFld("Type")
	detailsFldByteValue = Details{}.getFld("ByteValue")
)
