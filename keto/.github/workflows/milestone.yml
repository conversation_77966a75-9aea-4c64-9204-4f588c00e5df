# AUTO-GENERATED, DO NOT EDIT!
# Please edit the original at https://github.com/ory/meta/blob/master/templates/repository/server/.github/workflows/milestone.yml

name: Generate and Publish Milestone Document

on:
  workflow_dispatch:
  schedule:
    - cron: "0 0 * * *"

jobs:
  milestone:
    if: github.repository_owner == 'ory'
    name: Generate and Publish Milestone Document
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
        with:
          token: ${{ secrets.TOKEN_PRIVILEGED }}
      - name: Milestone Documentation Generator
        uses: ory/milestone-action@v0
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          outputFile: docs/docs/milestones.md
      - name: Commit Milestone Documentation
        uses: EndBug/add-and-commit@v4.4.0
        with:
          message: "autogen(docs): update milestone document"
          author_name: aeneasr
          author_email: "<EMAIL>"
        env:
          GITHUB_TOKEN: ${{ secrets.TOKEN_PRIVILEGED }}
