import os
import sys
import shutil
import numpy as np
sys.path.append('.')
from customer.common import tools
from customer.common.merge_pcd import merge_pcds_to_one


camera_names = ['back', 'front_narrow', 'front_wide', 'left_back', 'left_front', 'right_back', 'right_front']


def construct_config(calib_dir, out_file):
    sensor_params = dict()
    for camera_name in camera_names:
        params_file = os.path.join(calib_dir, f'calib_camera_{camera_name}_to_car.json')
        intrinsic_data = tools.get_json_data(params_file)
        intrinsic = intrinsic_data[f'camera-{camera_name}'.replace('_', '-')]['param']['cam_matrix']['data']
        distortion = intrinsic_data[f'camera-{camera_name}'.replace('_', '-')]['param']['cam_dist']['data']
        extrinsic = np.linalg.inv(intrinsic_data[f'camera-{camera_name}-to-car'.replace('_', '-')]['param']['sensor_calib']['data']).tolist()
        sensor_params[camera_name] = {
            "camera_model": 'pinhole',
            "extrinsic": extrinsic,
            "fx": intrinsic[0][0],
            "fy": intrinsic[1][1],
            "cx": intrinsic[0][2],
            "cy": intrinsic[1][2],
            "k1": 0,
            "k2": 0,
            "k3": 0,
            "p1": 0,
            "p2": 0,
            # "k1": distortion[0],
            # "k2": distortion[1],
            # "p1": distortion[2],
            # "p2": distortion[3],
            # "k3": distortion[4],
            # "k4": distortion[5],
            # "k5": distortion[6],
            # "k6": distortion[7],
        }
    config = {
        "data_type": "fusion_pointcloud",
        "sensor_params": sensor_params,
    }
    tools.write_json_file(config, out_file)


def run(input_dir, output_dir):
    out_clip_dir = os.path.join(output_dir, 'clip')
    os.mkdir(out_clip_dir)
    out_lidar_dir = os.path.join(out_clip_dir, 'lidar')
    os.mkdir(out_lidar_dir)
    out_cameras_dir = os.path.join(out_clip_dir, 'camera')
    os.mkdir(out_cameras_dir)
    camera_imgs = dict()
    for camera_name in camera_names:
        camera_imgs[camera_name] = tools.listdir(os.path.join(input_dir, 'rawdata', 'image_undistortion', camera_name), full_path=True, sort=True)
        os.mkdir(os.path.join(out_cameras_dir, camera_name))
    lidar_dir = os.path.join(input_dir, 'rawdata', 'lidar_raw')
    left_lidars = sorted(tools.get_file_by_extension(os.path.join(lidar_dir, 'left'), '.pcd'),
                         key=lambda x: x.name)
    right_lidars = sorted(tools.get_file_by_extension(os.path.join(lidar_dir, 'right'), '.pcd'),
                          key=lambda x: x.name)
    top_lidars = sorted(tools.get_file_by_extension(os.path.join(lidar_dir, 'top'), '.pcd'),
                        key=lambda x: x.name)
    assert len(left_lidars) == len(right_lidars) == len(top_lidars), 'lidar numbers do not match'
    calib_extract_dir = os.path.join(input_dir, 'rawdata', 'calib_extract')
    lidar_left_file = os.path.join(calib_extract_dir, 'calib_lidar_left_to_car.json')
    lidar_right_file = os.path.join(calib_extract_dir, 'calib_lidar_right_to_car.json')
    lidar_top_file = os.path.join(calib_extract_dir, 'calib_lidar_top_to_car.json')
    lidar_left_data = tools.get_json_data(lidar_left_file)
    lidar_left_extrinsic = lidar_left_data['lidar-left-to-car']['param']['sensor_calib']['data']
    lidar_right_data = tools.get_json_data(lidar_right_file)
    lidar_right_extrinsic = lidar_right_data['lidar-right-to-car']['param']['sensor_calib']['data']
    lidar_top_data = tools.get_json_data(lidar_top_file)
    lidar_top_extrinsic = lidar_top_data['lidar-top-to-car']['param']['sensor_calib']['data']
    lidar2worlds = [lidar_top_extrinsic, lidar_left_extrinsic, lidar_right_extrinsic]

    construct_config(calib_extract_dir, os.path.join(out_clip_dir, 'config.json'))

    for idx in range(10):
        output_pcd = os.path.join(out_lidar_dir, top_lidars[idx].name.split('.')[0] + '.pcd')
        input_pcds = [top_lidars[idx], left_lidars[idx], right_lidars[idx]]
        merge_pcds_to_one(input_pcds, lidar2worlds, output_pcd, has_intensity=True)
        for camera_name in camera_names:
            shutil.copyfile(camera_imgs[camera_name][idx], os.path.join(out_cameras_dir, camera_name, top_lidars[idx].name.split('.')[0] + '.jpg'))


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
