syntax = "proto3";

package model.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";
import "types/tag.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/model/v1;model";

service Models {
  rpc Create (CreateModelRequest) returns (Model) {
    option (google.api.http) = {
      post: "/v1/models"
      body: "*"
    };
  }

  rpc Update (UpdateModelRequest) returns (Model) {
    option (google.api.http) = {
      patch: "/v1/models/{uid}"
      body: "data"
    };
  }

  rpc Delete (DeleteModelRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/models/{uid}"
    };
  }

  rpc Get (GetModelRequest) returns (Model) {
    option (google.api.http) = {
      get: "/v1/models/{uid}"
    };
  }

  rpc List (ListModelRequest) returns (ListModelReply) {
    option (google.api.http) = {
      get: "/v1/models"
    };
  }

  rpc AddTag (types.TagRequest) returns (types.TagList) {
    option (google.api.http) = {
      put: "/v1/models/{uid}/tag"
      body: "tags"
    };
  }

  rpc DeleteTag (types.TagRequest) returns (types.TagList) {
    option (google.api.http) = {
      delete: "/v1/models/{uid}/tag"
    };
  }
}

message CreateModelRequest {
  option (openapi.v3.schema) = {
    // required: ["name", "type"]
  };

  // [mandatory for create] model name
  string name = 1;
  // [mandatory for create] model type: od/segmentation
  string type = 2;
  // base model name
  string base = 3;

  // run uid
  string run_uid = 4 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|^$"}];
  // uri
  string uri = 5 [(validate.rules).string = {uri_ref: true}];
  // [mandatory for create] version
  string version = 6 [(validate.rules).string = {pattern: "^[\\w.-]{3,32}$|^$"}];
  // the size of the model in bytes
  double bytes = 7;
  // metric of the model: precision/recall/accuracy/f1/mae/rmse/...
  map<string, double> metric = 8;
  // tags attached to the model
  repeated string tags = 9;
}

message UpdateModelRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "data", "fields"]
  };

  string uid = 1;
  CreateModelRequest data = 2;
  repeated string fields = 3;
}

message Model {
  option (openapi.v3.schema) = {
    required: ["uid", "name", "type", "uri", "version", "created_at"]
  };

  // model uid
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // model name
  string name = 2;
  // model type: od/segmentation
  string type = 3;
  // base model name
  string base = 4;
  // run uid
  string run_uid = 5 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|^$"}];
  // uri
  string uri = 6 [(validate.rules).string.uri_ref = true];
  // version
  string version = 7 [(validate.rules).string = {pattern: "^[\\w.-]{3-32}|^$"}];
  // the size of the model in bytes
  double bytes = 8;

  // metric of the model: precision/recall/accuracy/f1/mae/rmse/...
  map<string, double> metric = 9;
  // tags attached to the model
  repeated string tags = 10;

  google.protobuf.Timestamp updated_at = 14;
  google.protobuf.Timestamp created_at = 15;
}

message ModelFilter {
  // run uid
  string run_uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|^$"}];
  // type
  string type = 2;
  // name pattern
  string name_pattern = 4;
  // model version
  string version = 5;
  // base model pattern
  string base_pattern = 6;
  // attached tags
  repeated string tags = 7;
}

message GetModelRequest {
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
}

message DeleteModelRequest {
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|byfilter"}];
  ModelFilter filter = 2;
}

message ListModelRequest {
  int32 pagesz = 1 [(validate.rules).int32 = {gte:0, lte: 100}];
  // An opaque pagination token returned from a previous call.
  // An empty token denotes the first page.
  string page_token = 2;

  ModelFilter filter = 3;
}

message ListModelReply {
  option (openapi.v3.schema) = {
    required: ["models", "next_page_token"]
  };

  repeated Model models = 1;
  // An opaque pagination token, if not empty, to be used to fetch the next page of results
  string next_page_token = 2;
  // total number of items found; valid only in the first page reply.
  int32 total = 3;
}
