syntax = "proto3";
package kratos.api;

option go_package = "anno/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
  RPC rpc = 3;
  Temporal temporal = 4;
  Otel otel = 5;
  MQ mq = 6;
  ObjectStorage object_storage = 7;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
  // non-empty comma-separated list of origins will enable CORS
  string cors_origins = 3;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
    string endpoint = 3;
    int32 port = 4;
    string database = 5;
    string username = 6;
    string password = 7;
    string options = 8;
  }
  message Redis {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration read_timeout = 3;
    google.protobuf.Duration write_timeout = 4;
  }
  Database database = 1;
  Redis redis = 2;
}

message Service {
  string addr = 1;
  google.protobuf.Duration timeout = 2;
}

message RPC {
  string svc_account = 1;
  Service iam = 2;
  Service annofeed = 3;
  Service annout = 4;
}

message Temporal {
  bool disable_worker = 1;
  string addr = 2;
  string namespace = 3;
  string task_queue = 4;
}

message Otel {
  Tracing tracing = 1;
  Metrics metrics = 2;
  Log log = 3;
}

message Tracing {
  // OTLP GRPC endpoint of the tracing service
  string endpoint = 1;
}

message Metrics {
  // prometheus HTTP exporter listening address
  string serve_addr = 1;
}

message Log {
  // log level: debug, info, warn, error, fatal
  string level = 1;
  // log format: default, json
  string format = 2;
}

message MQ {
  enum Provider {
    unspecified = 0;
    redis_pubsub = 1;
    // redis_stream = 2;

    // producer only
    aws_sns = 3;
    aws_sqs = 4;
  }
  message Producer {
    Provider provider = 1;
    // default topic
    string topic = 2;
  }
  message Consumer {
    Provider provider = 1;
    // comma seperated topics list (not for AWS SQS consumers)
    string topics = 2;
    // number of handler threads
    int32 handler_threads = 3;
  }
  message SQS {
    // queue name or URL
    string name = 1;
  }

  Producer producer = 1;
  Consumer consumer = 2;
  Data.Redis redis = 3;
  SQS sqs = 4;
}

message ObjectStorage {
  message LocalFS {
    string base_dir = 1; // directory in local filesystem to put the uploaded files
  }

  // storage type:
  // "s3": store the uploaded files into S3;
  // "localfs": use local filesystem; this is the default;
  string type = 1;
  // directory in local filesystem to put temporary files: e.g. downloaded files
  string workdir = 2;
  LocalFS localfs = 3;
  S3Config s3 = 4;
  CloudFront cloudfront = 5; // CDN config
}

message S3Config {
  string access_key = 1;
  string secret_key = 2;

  // bucket for normal data
  string bucket = 3;
  // bucket for public resources: user/project/label avatars
  string public_bucket = 4;

  // default expires duration for signed URLs
  google.protobuf.Duration expires = 5;
}

message CloudFront {
  message Distribution {
    // matching origin, including the s3 scheme, bucket name and the path if any: s3://bucket/path
    string origin = 1;
    // access URL prefix, including scheme and domain name: https://example.com
    string url_prefix = 2;
    // if it is a public distribution
    bool public = 3;
  }

  bool enabled = 1;
  map<string, Distribution> distributions = 2;
  // signer key ID
  string sign_key_id = 3;
  // signer private key (RSA) in PEM format
  string sign_key = 4;
  // default expires duration for signed URLs
  google.protobuf.Duration expires = 5;
}
