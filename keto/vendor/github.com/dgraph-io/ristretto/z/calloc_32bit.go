// Copyright 2020 The LevelDB-Go and Pebble Authors. All rights reserved. Use
// of this source code is governed by a BSD-style license that can be found in
// the LICENSE file.

// +build 386 amd64p32 arm armbe  mips mipsle mips64p32 mips64p32le ppc sparc

package z

const (
	// MaxArrayLen is a safe maximum length for slices on this architecture.
	MaxArrayLen = 1<<31 - 1
	// MaxBufferSize is the size of virtually unlimited buffer on this architecture.
	MaxBufferSize = 1 << 30
)
