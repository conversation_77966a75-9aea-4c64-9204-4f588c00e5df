// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: annostat/v1/stats.proto

package annostat

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	v1 "gitlab.rp.konvery.work/platform/apis/iam/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DataType_Enum int32

const (
	DataType_unspecified DataType_Enum = 0
	// one image in an element
	DataType_image DataType_Enum = 1
	// one point-cloud file in an element
	DataType_pointcloud DataType_Enum = 2
	// multiple image files in an element
	DataType_fusion2d DataType_Enum = 3
	// multiple image files and a point-cloud file in an element
	DataType_fusion3d DataType_Enum = 4
)

// Enum value maps for DataType_Enum.
var (
	DataType_Enum_name = map[int32]string{
		0: "unspecified",
		1: "image",
		2: "pointcloud",
		3: "fusion2d",
		4: "fusion3d",
	}
	DataType_Enum_value = map[string]int32{
		"unspecified": 0,
		"image":       1,
		"pointcloud":  2,
		"fusion2d":    3,
		"fusion3d":    4,
	}
)

func (x DataType_Enum) Enum() *DataType_Enum {
	p := new(DataType_Enum)
	*p = x
	return p
}

func (x DataType_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataType_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_annostat_v1_stats_proto_enumTypes[0].Descriptor()
}

func (DataType_Enum) Type() protoreflect.EnumType {
	return &file_annostat_v1_stats_proto_enumTypes[0]
}

func (x DataType_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataType_Enum.Descriptor instead.
func (DataType_Enum) EnumDescriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{9, 0}
}

type LotType_Enum int32

const (
	LotType_unspecified LotType_Enum = 0
	LotType_annotate    LotType_Enum = 1
	LotType_segment     LotType_Enum = 2 // classify = 3;
)

// Enum value maps for LotType_Enum.
var (
	LotType_Enum_name = map[int32]string{
		0: "unspecified",
		1: "annotate",
		2: "segment",
	}
	LotType_Enum_value = map[string]int32{
		"unspecified": 0,
		"annotate":    1,
		"segment":     2,
	}
)

func (x LotType_Enum) Enum() *LotType_Enum {
	p := new(LotType_Enum)
	*p = x
	return p
}

func (x LotType_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LotType_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_annostat_v1_stats_proto_enumTypes[1].Descriptor()
}

func (LotType_Enum) Type() protoreflect.EnumType {
	return &file_annostat_v1_stats_proto_enumTypes[1]
}

func (x LotType_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LotType_Enum.Descriptor instead.
func (LotType_Enum) EnumDescriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{10, 0}
}

type GetOngoingLotsReply_Phase_Type_Enum int32

const (
	GetOngoingLotsReply_Phase_Type_unspecified GetOngoingLotsReply_Phase_Type_Enum = 0
	GetOngoingLotsReply_Phase_Type_label       GetOngoingLotsReply_Phase_Type_Enum = 1
	GetOngoingLotsReply_Phase_Type_review      GetOngoingLotsReply_Phase_Type_Enum = 2
)

// Enum value maps for GetOngoingLotsReply_Phase_Type_Enum.
var (
	GetOngoingLotsReply_Phase_Type_Enum_name = map[int32]string{
		0: "unspecified",
		1: "label",
		2: "review",
	}
	GetOngoingLotsReply_Phase_Type_Enum_value = map[string]int32{
		"unspecified": 0,
		"label":       1,
		"review":      2,
	}
)

func (x GetOngoingLotsReply_Phase_Type_Enum) Enum() *GetOngoingLotsReply_Phase_Type_Enum {
	p := new(GetOngoingLotsReply_Phase_Type_Enum)
	*p = x
	return p
}

func (x GetOngoingLotsReply_Phase_Type_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetOngoingLotsReply_Phase_Type_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_annostat_v1_stats_proto_enumTypes[2].Descriptor()
}

func (GetOngoingLotsReply_Phase_Type_Enum) Type() protoreflect.EnumType {
	return &file_annostat_v1_stats_proto_enumTypes[2]
}

func (x GetOngoingLotsReply_Phase_Type_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetOngoingLotsReply_Phase_Type_Enum.Descriptor instead.
func (GetOngoingLotsReply_Phase_Type_Enum) EnumDescriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{18, 0, 0, 0}
}

type GetOrderCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *GetOrderCountRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *GetOrderCountRequest) Reset() {
	*x = GetOrderCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderCountRequest) ProtoMessage() {}

func (x *GetOrderCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderCountRequest.ProtoReflect.Descriptor instead.
func (*GetOrderCountRequest) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{0}
}

func (x *GetOrderCountRequest) GetFilter() *GetOrderCountRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type GetOrderCountReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total    int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Ongoing  int32 `protobuf:"varint,2,opt,name=ongoing,proto3" json:"ongoing,omitempty"`
	Paused   int32 `protobuf:"varint,3,opt,name=paused,proto3" json:"paused,omitempty"`
	Finished int32 `protobuf:"varint,4,opt,name=finished,proto3" json:"finished,omitempty"`
}

func (x *GetOrderCountReply) Reset() {
	*x = GetOrderCountReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderCountReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderCountReply) ProtoMessage() {}

func (x *GetOrderCountReply) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderCountReply.ProtoReflect.Descriptor instead.
func (*GetOrderCountReply) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{1}
}

func (x *GetOrderCountReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetOrderCountReply) GetOngoing() int32 {
	if x != nil {
		return x.Ongoing
	}
	return 0
}

func (x *GetOrderCountReply) GetPaused() int32 {
	if x != nil {
		return x.Paused
	}
	return 0
}

func (x *GetOrderCountReply) GetFinished() int32 {
	if x != nil {
		return x.Finished
	}
	return 0
}

type GetOrderConversionReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Orders int32 `protobuf:"varint,1,opt,name=orders,proto3" json:"orders,omitempty"`
	Lots   int32 `protobuf:"varint,2,opt,name=lots,proto3" json:"lots,omitempty"`
}

func (x *GetOrderConversionReply) Reset() {
	*x = GetOrderConversionReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderConversionReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderConversionReply) ProtoMessage() {}

func (x *GetOrderConversionReply) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderConversionReply.ProtoReflect.Descriptor instead.
func (*GetOrderConversionReply) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{2}
}

func (x *GetOrderConversionReply) GetOrders() int32 {
	if x != nil {
		return x.Orders
	}
	return 0
}

func (x *GetOrderConversionReply) GetLots() int32 {
	if x != nil {
		return x.Lots
	}
	return 0
}

type GetLotCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *LotFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *GetLotCountRequest) Reset() {
	*x = GetLotCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotCountRequest) ProtoMessage() {}

func (x *GetLotCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotCountRequest.ProtoReflect.Descriptor instead.
func (*GetLotCountRequest) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{3}
}

func (x *GetLotCountRequest) GetFilter() *LotFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type GetLotCountReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total    int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Ongoing  int32 `protobuf:"varint,2,opt,name=ongoing,proto3" json:"ongoing,omitempty"`
	Paused   int32 `protobuf:"varint,3,opt,name=paused,proto3" json:"paused,omitempty"`
	Finished int32 `protobuf:"varint,4,opt,name=finished,proto3" json:"finished,omitempty"`
}

func (x *GetLotCountReply) Reset() {
	*x = GetLotCountReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotCountReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotCountReply) ProtoMessage() {}

func (x *GetLotCountReply) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotCountReply.ProtoReflect.Descriptor instead.
func (*GetLotCountReply) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{4}
}

func (x *GetLotCountReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetLotCountReply) GetOngoing() int32 {
	if x != nil {
		return x.Ongoing
	}
	return 0
}

func (x *GetLotCountReply) GetPaused() int32 {
	if x != nil {
		return x.Paused
	}
	return 0
}

func (x *GetLotCountReply) GetFinished() int32 {
	if x != nil {
		return x.Finished
	}
	return 0
}

type GetLotStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *GetLotStatusRequest) Reset() {
	*x = GetLotStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotStatusRequest) ProtoMessage() {}

func (x *GetLotStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotStatusRequest.ProtoReflect.Descriptor instead.
func (*GetLotStatusRequest) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{5}
}

func (x *GetLotStatusRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetLotStatusReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalElems int32 `protobuf:"varint,1,opt,name=total_elems,json=totalElems,proto3" json:"total_elems,omitempty"`
	// status of each phase
	Phases []*GetLotStatusReply_Phase `protobuf:"bytes,2,rep,name=phases,proto3" json:"phases,omitempty"`
}

func (x *GetLotStatusReply) Reset() {
	*x = GetLotStatusReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotStatusReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotStatusReply) ProtoMessage() {}

func (x *GetLotStatusReply) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotStatusReply.ProtoReflect.Descriptor instead.
func (*GetLotStatusReply) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{6}
}

func (x *GetLotStatusReply) GetTotalElems() int32 {
	if x != nil {
		return x.TotalElems
	}
	return 0
}

func (x *GetLotStatusReply) GetPhases() []*GetLotStatusReply_Phase {
	if x != nil {
		return x.Phases
	}
	return nil
}

type GetLotLabelStatReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// cuboid statistics, indexed by cuboid name
	Cuboids map[string]*GetLotLabelStatReply_Cuboid `protobuf:"bytes,1,rep,name=cuboids,proto3" json:"cuboids,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetLotLabelStatReply) Reset() {
	*x = GetLotLabelStatReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotLabelStatReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotLabelStatReply) ProtoMessage() {}

func (x *GetLotLabelStatReply) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotLabelStatReply.ProtoReflect.Descriptor instead.
func (*GetLotLabelStatReply) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{7}
}

func (x *GetLotLabelStatReply) GetCuboids() map[string]*GetLotLabelStatReply_Cuboid {
	if x != nil {
		return x.Cuboids
	}
	return nil
}

type TimeRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// time is within [from, to)
	From *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	To   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
}

func (x *TimeRange) Reset() {
	*x = TimeRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeRange) ProtoMessage() {}

func (x *TimeRange) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeRange.ProtoReflect.Descriptor instead.
func (*TimeRange) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{8}
}

func (x *TimeRange) GetFrom() *timestamppb.Timestamp {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *TimeRange) GetTo() *timestamppb.Timestamp {
	if x != nil {
		return x.To
	}
	return nil
}

type DataType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DataType) Reset() {
	*x = DataType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataType) ProtoMessage() {}

func (x *DataType) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataType.ProtoReflect.Descriptor instead.
func (*DataType) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{9}
}

type LotType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LotType) Reset() {
	*x = LotType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LotType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LotType) ProtoMessage() {}

func (x *LotType) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LotType.ProtoReflect.Descriptor instead.
func (*LotType) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{10}
}

type LotFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter lot by creation time range
	TimeRange *TimeRange `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
	// filter lot by type
	LotTypes []LotType_Enum `protobuf:"varint,2,rep,packed,name=lot_types,json=lotTypes,proto3,enum=annostat.v1.LotType_Enum" json:"lot_types,omitempty"`
	// filter lot by data type
	DataTypes []DataType_Enum `protobuf:"varint,3,rep,packed,name=data_types,json=dataTypes,proto3,enum=annostat.v1.DataType_Enum" json:"data_types,omitempty"`
	// filter lot by uid
	LotUids []string `protobuf:"bytes,4,rep,name=lot_uids,json=lotUids,proto3" json:"lot_uids,omitempty"`
	// filter lot by owner organization
	OwnerOrgs []string `protobuf:"bytes,5,rep,name=owner_orgs,json=ownerOrgs,proto3" json:"owner_orgs,omitempty"`
	// filter lot by executor organization
	ExecutorOrgs []string `protobuf:"bytes,6,rep,name=executor_orgs,json=executorOrgs,proto3" json:"executor_orgs,omitempty"`
	// filter lot by executor uid
	ExecutorUids []string `protobuf:"bytes,7,rep,name=executor_uids,json=executorUids,proto3" json:"executor_uids,omitempty"`
}

func (x *LotFilter) Reset() {
	*x = LotFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LotFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LotFilter) ProtoMessage() {}

func (x *LotFilter) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LotFilter.ProtoReflect.Descriptor instead.
func (*LotFilter) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{11}
}

func (x *LotFilter) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

func (x *LotFilter) GetLotTypes() []LotType_Enum {
	if x != nil {
		return x.LotTypes
	}
	return nil
}

func (x *LotFilter) GetDataTypes() []DataType_Enum {
	if x != nil {
		return x.DataTypes
	}
	return nil
}

func (x *LotFilter) GetLotUids() []string {
	if x != nil {
		return x.LotUids
	}
	return nil
}

func (x *LotFilter) GetOwnerOrgs() []string {
	if x != nil {
		return x.OwnerOrgs
	}
	return nil
}

func (x *LotFilter) GetExecutorOrgs() []string {
	if x != nil {
		return x.ExecutorOrgs
	}
	return nil
}

func (x *LotFilter) GetExecutorUids() []string {
	if x != nil {
		return x.ExecutorUids
	}
	return nil
}

type ProductionByTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// query scope
	Filter *LotFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// group results by time_unit (in seconds)
	TimeUnit int32 `protobuf:"varint,2,opt,name=time_unit,json=timeUnit,proto3" json:"time_unit,omitempty"`
	// items to count: anno2d, anno3d, elem, job
	Items []string `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *ProductionByTimeRequest) Reset() {
	*x = ProductionByTimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductionByTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductionByTimeRequest) ProtoMessage() {}

func (x *ProductionByTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductionByTimeRequest.ProtoReflect.Descriptor instead.
func (*ProductionByTimeRequest) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{12}
}

func (x *ProductionByTimeRequest) GetFilter() *LotFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ProductionByTimeRequest) GetTimeUnit() int32 {
	if x != nil {
		return x.TimeUnit
	}
	return 0
}

func (x *ProductionByTimeRequest) GetItems() []string {
	if x != nil {
		return x.Items
	}
	return nil
}

type ProductionByTimeReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// items and count in each time unit
	Units []*ProductionByTimeReply_Unit `protobuf:"bytes,1,rep,name=units,proto3" json:"units,omitempty"`
}

func (x *ProductionByTimeReply) Reset() {
	*x = ProductionByTimeReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductionByTimeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductionByTimeReply) ProtoMessage() {}

func (x *ProductionByTimeReply) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductionByTimeReply.ProtoReflect.Descriptor instead.
func (*ProductionByTimeReply) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{13}
}

func (x *ProductionByTimeReply) GetUnits() []*ProductionByTimeReply_Unit {
	if x != nil {
		return x.Units
	}
	return nil
}

type GetLotStatByExecutorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lot uid
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// phase number, start from 1
	Phase int32 `protobuf:"varint,2,opt,name=phase,proto3" json:"phase,omitempty"`
	// true to stats by execution teams, false to stats by individuals
	ByExecteam bool `protobuf:"varint,3,opt,name=by_execteam,json=byExecteam,proto3" json:"by_execteam,omitempty"`
	// stat time range; if not specified, default to last working day
	TimeRange *TimeRange `protobuf:"bytes,4,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
	Page      int32      `protobuf:"varint,14,opt,name=page,proto3" json:"page,omitempty"`
	Pagesz    int32      `protobuf:"varint,15,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
}

func (x *GetLotStatByExecutorRequest) Reset() {
	*x = GetLotStatByExecutorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotStatByExecutorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotStatByExecutorRequest) ProtoMessage() {}

func (x *GetLotStatByExecutorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotStatByExecutorRequest.ProtoReflect.Descriptor instead.
func (*GetLotStatByExecutorRequest) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{14}
}

func (x *GetLotStatByExecutorRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *GetLotStatByExecutorRequest) GetPhase() int32 {
	if x != nil {
		return x.Phase
	}
	return 0
}

func (x *GetLotStatByExecutorRequest) GetByExecteam() bool {
	if x != nil {
		return x.ByExecteam
	}
	return false
}

func (x *GetLotStatByExecutorRequest) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

func (x *GetLotStatByExecutorRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetLotStatByExecutorRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

type ExecutorCounter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// executor can be a user or a team
	Executor *v1.BaseUser `protobuf:"bytes,1,opt,name=executor,proto3" json:"executor,omitempty"`
	// number of jobs submitted
	Jobs int32 `protobuf:"varint,2,opt,name=jobs,proto3" json:"jobs,omitempty"`
	// number of elements submitted
	Elems int32 `protobuf:"varint,3,opt,name=elems,proto3" json:"elems,omitempty"`
	// number of annotations submitted
	Ins int32 `protobuf:"varint,4,opt,name=ins,proto3" json:"ins,omitempty"`
	// ins by widget: anno2d/anno3d/...
	InsByWidget map[string]int32 `protobuf:"bytes,5,rep,name=ins_by_widget,json=insByWidget,proto3" json:"ins_by_widget,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// number of jobs submitted per hour
	JobsPerHour float32 `protobuf:"fixed32,6,opt,name=jobs_per_hour,json=jobsPerHour,proto3" json:"jobs_per_hour,omitempty"`
	// number of elements submitted per hour
	ElemsPerHour float32 `protobuf:"fixed32,7,opt,name=elems_per_hour,json=elemsPerHour,proto3" json:"elems_per_hour,omitempty"`
	// element accuracy: [0, 1]
	ElemAccuracy float32 `protobuf:"fixed32,8,opt,name=elem_accuracy,json=elemAccuracy,proto3" json:"elem_accuracy,omitempty"`
	// ins accuracy: [0, 1]
	InsAccuracy float32 `protobuf:"fixed32,9,opt,name=ins_accuracy,json=insAccuracy,proto3" json:"ins_accuracy,omitempty"`
}

func (x *ExecutorCounter) Reset() {
	*x = ExecutorCounter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecutorCounter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutorCounter) ProtoMessage() {}

func (x *ExecutorCounter) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutorCounter.ProtoReflect.Descriptor instead.
func (*ExecutorCounter) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{15}
}

func (x *ExecutorCounter) GetExecutor() *v1.BaseUser {
	if x != nil {
		return x.Executor
	}
	return nil
}

func (x *ExecutorCounter) GetJobs() int32 {
	if x != nil {
		return x.Jobs
	}
	return 0
}

func (x *ExecutorCounter) GetElems() int32 {
	if x != nil {
		return x.Elems
	}
	return 0
}

func (x *ExecutorCounter) GetIns() int32 {
	if x != nil {
		return x.Ins
	}
	return 0
}

func (x *ExecutorCounter) GetInsByWidget() map[string]int32 {
	if x != nil {
		return x.InsByWidget
	}
	return nil
}

func (x *ExecutorCounter) GetJobsPerHour() float32 {
	if x != nil {
		return x.JobsPerHour
	}
	return 0
}

func (x *ExecutorCounter) GetElemsPerHour() float32 {
	if x != nil {
		return x.ElemsPerHour
	}
	return 0
}

func (x *ExecutorCounter) GetElemAccuracy() float32 {
	if x != nil {
		return x.ElemAccuracy
	}
	return 0
}

func (x *ExecutorCounter) GetInsAccuracy() float32 {
	if x != nil {
		return x.InsAccuracy
	}
	return 0
}

type GetLotStatByExecutorReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// executor statistics
	Counters []*ExecutorCounter `protobuf:"bytes,2,rep,name=counters,proto3" json:"counters,omitempty"`
}

func (x *GetLotStatByExecutorReply) Reset() {
	*x = GetLotStatByExecutorReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotStatByExecutorReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotStatByExecutorReply) ProtoMessage() {}

func (x *GetLotStatByExecutorReply) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotStatByExecutorReply.ProtoReflect.Descriptor instead.
func (*GetLotStatByExecutorReply) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{16}
}

func (x *GetLotStatByExecutorReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetLotStatByExecutorReply) GetCounters() []*ExecutorCounter {
	if x != nil {
		return x.Counters
	}
	return nil
}

type GetOngoingLotsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page   int32 `protobuf:"varint,14,opt,name=page,proto3" json:"page,omitempty"`
	Pagesz int32 `protobuf:"varint,15,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
}

func (x *GetOngoingLotsRequest) Reset() {
	*x = GetOngoingLotsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOngoingLotsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOngoingLotsRequest) ProtoMessage() {}

func (x *GetOngoingLotsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOngoingLotsRequest.ProtoReflect.Descriptor instead.
func (*GetOngoingLotsRequest) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{17}
}

func (x *GetOngoingLotsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetOngoingLotsRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

type GetOngoingLotsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total int32                      `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Lots  []*GetOngoingLotsReply_Lot `protobuf:"bytes,2,rep,name=lots,proto3" json:"lots,omitempty"`
}

func (x *GetOngoingLotsReply) Reset() {
	*x = GetOngoingLotsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOngoingLotsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOngoingLotsReply) ProtoMessage() {}

func (x *GetOngoingLotsReply) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOngoingLotsReply.ProtoReflect.Descriptor instead.
func (*GetOngoingLotsReply) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{18}
}

func (x *GetOngoingLotsReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetOngoingLotsReply) GetLots() []*GetOngoingLotsReply_Lot {
	if x != nil {
		return x.Lots
	}
	return nil
}

type GetOrderCountRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter by creation time range
	TimeRange *TimeRange `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
	// filter by owner organization
	OwnerOrgs []string `protobuf:"bytes,2,rep,name=owner_orgs,json=ownerOrgs,proto3" json:"owner_orgs,omitempty"`
}

func (x *GetOrderCountRequest_Filter) Reset() {
	*x = GetOrderCountRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderCountRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderCountRequest_Filter) ProtoMessage() {}

func (x *GetOrderCountRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderCountRequest_Filter.ProtoReflect.Descriptor instead.
func (*GetOrderCountRequest_Filter) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{0, 0}
}

func (x *GetOrderCountRequest_Filter) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

func (x *GetOrderCountRequest_Filter) GetOwnerOrgs() []string {
	if x != nil {
		return x.OwnerOrgs
	}
	return nil
}

type GetLotStatusReply_Phase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// number of elements in and before this phase
	ElemsToWork int32 `protobuf:"varint,1,opt,name=elems_to_work,json=elemsToWork,proto3" json:"elems_to_work,omitempty"`
	// estimated number of days needed to close this phase (days needed to close prior phases are not included);
	// -1 means estimation is unavailable
	EstimatedDaysLeft float32 `protobuf:"fixed32,2,opt,name=estimated_days_left,json=estimatedDaysLeft,proto3" json:"estimated_days_left,omitempty"`
}

func (x *GetLotStatusReply_Phase) Reset() {
	*x = GetLotStatusReply_Phase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotStatusReply_Phase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotStatusReply_Phase) ProtoMessage() {}

func (x *GetLotStatusReply_Phase) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotStatusReply_Phase.ProtoReflect.Descriptor instead.
func (*GetLotStatusReply_Phase) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{6, 0}
}

func (x *GetLotStatusReply_Phase) GetElemsToWork() int32 {
	if x != nil {
		return x.ElemsToWork
	}
	return 0
}

func (x *GetLotStatusReply_Phase) GetEstimatedDaysLeft() float32 {
	if x != nil {
		return x.EstimatedDaysLeft
	}
	return 0
}

type GetLotLabelStatReply_Cuboid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// number of cuboid objects with this label
	InsCnt int32 `protobuf:"varint,2,opt,name=ins_cnt,json=insCnt,proto3" json:"ins_cnt,omitempty"`
	// statistical cuboid scale on each axis: x, y, z
	Scales []float64 `protobuf:"fixed64,3,rep,packed,name=scales,proto3" json:"scales,omitempty"`
}

func (x *GetLotLabelStatReply_Cuboid) Reset() {
	*x = GetLotLabelStatReply_Cuboid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotLabelStatReply_Cuboid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotLabelStatReply_Cuboid) ProtoMessage() {}

func (x *GetLotLabelStatReply_Cuboid) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotLabelStatReply_Cuboid.ProtoReflect.Descriptor instead.
func (*GetLotLabelStatReply_Cuboid) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{7, 0}
}

func (x *GetLotLabelStatReply_Cuboid) GetInsCnt() int32 {
	if x != nil {
		return x.InsCnt
	}
	return 0
}

func (x *GetLotLabelStatReply_Cuboid) GetScales() []float64 {
	if x != nil {
		return x.Scales
	}
	return nil
}

type ProductionByTimeReply_Unit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// time range of this unit
	TimeRange *TimeRange `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
	// item name and count
	Items map[string]float32 `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
}

func (x *ProductionByTimeReply_Unit) Reset() {
	*x = ProductionByTimeReply_Unit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductionByTimeReply_Unit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductionByTimeReply_Unit) ProtoMessage() {}

func (x *ProductionByTimeReply_Unit) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductionByTimeReply_Unit.ProtoReflect.Descriptor instead.
func (*ProductionByTimeReply_Unit) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{13, 0}
}

func (x *ProductionByTimeReply_Unit) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

func (x *ProductionByTimeReply_Unit) GetItems() map[string]float32 {
	if x != nil {
		return x.Items
	}
	return nil
}

type GetOngoingLotsReply_Phase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// phase number, starts from 1
	Number int32                               `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty"`
	Name   string                              `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type   GetOngoingLotsReply_Phase_Type_Enum `protobuf:"varint,3,opt,name=type,proto3,enum=annostat.v1.GetOngoingLotsReply_Phase_Type_Enum" json:"type,omitempty"`
	// estimated number of days needed to close this phase (days needed to close prior phases are not included);
	// -1 means estimation is unavailable
	EstimatedDaysLeft float32 `protobuf:"fixed32,10,opt,name=estimated_days_left,json=estimatedDaysLeft,proto3" json:"estimated_days_left,omitempty"`
}

func (x *GetOngoingLotsReply_Phase) Reset() {
	*x = GetOngoingLotsReply_Phase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOngoingLotsReply_Phase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOngoingLotsReply_Phase) ProtoMessage() {}

func (x *GetOngoingLotsReply_Phase) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOngoingLotsReply_Phase.ProtoReflect.Descriptor instead.
func (*GetOngoingLotsReply_Phase) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{18, 0}
}

func (x *GetOngoingLotsReply_Phase) GetNumber() int32 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *GetOngoingLotsReply_Phase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetOngoingLotsReply_Phase) GetType() GetOngoingLotsReply_Phase_Type_Enum {
	if x != nil {
		return x.Type
	}
	return GetOngoingLotsReply_Phase_Type_unspecified
}

func (x *GetOngoingLotsReply_Phase) GetEstimatedDaysLeft() float32 {
	if x != nil {
		return x.EstimatedDaysLeft
	}
	return 0
}

type GetOngoingLotsReply_Lot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid      string        `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name     string        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	DataType DataType_Enum `protobuf:"varint,3,opt,name=data_type,json=dataType,proto3,enum=annostat.v1.DataType_Enum" json:"data_type,omitempty"`
	// number of elements in the lot
	DataSize int32                        `protobuf:"varint,4,opt,name=data_size,json=dataSize,proto3" json:"data_size,omitempty"`
	Phases   []*GetOngoingLotsReply_Phase `protobuf:"bytes,10,rep,name=phases,proto3" json:"phases,omitempty"`
	// expected end time
	ExpEndTime *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=exp_end_time,json=expEndTime,proto3" json:"exp_end_time,omitempty"`
	CreatedAt  *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *GetOngoingLotsReply_Lot) Reset() {
	*x = GetOngoingLotsReply_Lot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOngoingLotsReply_Lot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOngoingLotsReply_Lot) ProtoMessage() {}

func (x *GetOngoingLotsReply_Lot) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOngoingLotsReply_Lot.ProtoReflect.Descriptor instead.
func (*GetOngoingLotsReply_Lot) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{18, 1}
}

func (x *GetOngoingLotsReply_Lot) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *GetOngoingLotsReply_Lot) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetOngoingLotsReply_Lot) GetDataType() DataType_Enum {
	if x != nil {
		return x.DataType
	}
	return DataType_unspecified
}

func (x *GetOngoingLotsReply_Lot) GetDataSize() int32 {
	if x != nil {
		return x.DataSize
	}
	return 0
}

func (x *GetOngoingLotsReply_Lot) GetPhases() []*GetOngoingLotsReply_Phase {
	if x != nil {
		return x.Phases
	}
	return nil
}

func (x *GetOngoingLotsReply_Lot) GetExpEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpEndTime
	}
	return nil
}

func (x *GetOngoingLotsReply_Lot) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type GetOngoingLotsReply_Phase_Type struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetOngoingLotsReply_Phase_Type) Reset() {
	*x = GetOngoingLotsReply_Phase_Type{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annostat_v1_stats_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOngoingLotsReply_Phase_Type) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOngoingLotsReply_Phase_Type) ProtoMessage() {}

func (x *GetOngoingLotsReply_Phase_Type) ProtoReflect() protoreflect.Message {
	mi := &file_annostat_v1_stats_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOngoingLotsReply_Phase_Type.ProtoReflect.Descriptor instead.
func (*GetOngoingLotsReply_Phase_Type) Descriptor() ([]byte, []int) {
	return file_annostat_v1_stats_proto_rawDescGZIP(), []int{18, 0, 0}
}

var File_annostat_v1_stats_proto protoreflect.FileDescriptor

var file_annostat_v1_stats_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74,
	0x61, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x73,
	0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x69, 0x61,
	0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xb8, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73,
	0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x5e, 0x0a, 0x06, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73,
	0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x5f, 0x6f, 0x72, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4f, 0x72, 0x67, 0x73, 0x22, 0xa3, 0x01, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x6e, 0x67, 0x6f, 0x69,
	0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6f, 0x6e, 0x67, 0x6f, 0x69, 0x6e,
	0x67, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x75, 0x73, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x70, 0x61, 0x75, 0x73, 0x65, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6e,
	0x69, 0x73, 0x68, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x66, 0x69, 0x6e,
	0x69, 0x73, 0x68, 0x65, 0x64, 0x3a, 0x29, 0xba, 0x47, 0x26, 0xba, 0x01, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0xba, 0x01, 0x07, 0x6f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0xba, 0x01, 0x06, 0x70,
	0x61, 0x75, 0x73, 0x65, 0x64, 0xba, 0x01, 0x08, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64,
	0x22, 0x5a, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x6c, 0x6f, 0x74, 0x73, 0x3a, 0x13, 0xba, 0x47, 0x10, 0xba, 0x01, 0x06, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0xba, 0x01, 0x04, 0x6c, 0x6f, 0x74, 0x73, 0x22, 0x44, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x6f, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x22, 0xa1, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x18, 0x0a,
	0x07, 0x6f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x6f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x75, 0x73, 0x65,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x70, 0x61, 0x75, 0x73, 0x65, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x3a, 0x29, 0xba, 0x47, 0x26,
	0xba, 0x01, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0xba, 0x01, 0x07, 0x6f, 0x6e, 0x67, 0x6f, 0x69,
	0x6e, 0x67, 0xba, 0x01, 0x06, 0x70, 0x61, 0x75, 0x73, 0x65, 0x64, 0xba, 0x01, 0x08, 0x66, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x22, 0x3e, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72,
	0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d,
	0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x97, 0x02, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4c, 0x6f,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x73, 0x12, 0x3c, 0x0a,
	0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x50, 0x68,
	0x61, 0x73, 0x65, 0x52, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x1a, 0x86, 0x01, 0x0a, 0x05,
	0x50, 0x68, 0x61, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x65, 0x6c, 0x65, 0x6d, 0x73, 0x5f, 0x74,
	0x6f, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x65, 0x6c,
	0x65, 0x6d, 0x73, 0x54, 0x6f, 0x57, 0x6f, 0x72, 0x6b, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x73, 0x74,
	0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x5f, 0x6c, 0x65, 0x66, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65,
	0x64, 0x44, 0x61, 0x79, 0x73, 0x4c, 0x65, 0x66, 0x74, 0x3a, 0x29, 0xba, 0x47, 0x26, 0xba, 0x01,
	0x0d, 0x65, 0x6c, 0x65, 0x6d, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0xba, 0x01,
	0x13, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x5f,
	0x6c, 0x65, 0x66, 0x74, 0x3a, 0x1a, 0xba, 0x47, 0x17, 0xba, 0x01, 0x0b, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x73, 0xba, 0x01, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73,
	0x22, 0x99, 0x02, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x48, 0x0a, 0x07, 0x63, 0x75, 0x62,
	0x6f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x43, 0x75,
	0x62, 0x6f, 0x69, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x63, 0x75, 0x62, 0x6f,
	0x69, 0x64, 0x73, 0x1a, 0x51, 0x0a, 0x06, 0x43, 0x75, 0x62, 0x6f, 0x69, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x69, 0x6e, 0x73, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x69, 0x6e, 0x73, 0x43, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x01, 0x52, 0x06, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x73, 0x3a, 0x16,
	0xba, 0x47, 0x13, 0xba, 0x01, 0x07, 0x69, 0x6e, 0x73, 0x5f, 0x63, 0x6e, 0x74, 0xba, 0x01, 0x06,
	0x73, 0x63, 0x61, 0x6c, 0x65, 0x73, 0x1a, 0x64, 0x0a, 0x0c, 0x43, 0x75, 0x62, 0x6f, 0x69, 0x64,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74,
	0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x43, 0x75, 0x62, 0x6f, 0x69,
	0x64, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x78, 0x0a, 0x09,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x66, 0x72, 0x6f,
	0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x2a, 0x0a, 0x02, 0x74, 0x6f, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x02, 0x74, 0x6f, 0x3a, 0x0f, 0xba, 0x47, 0x0c, 0xba, 0x01, 0x04, 0x66, 0x72, 0x6f,
	0x6d, 0xba, 0x01, 0x02, 0x74, 0x6f, 0x22, 0x5a, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x4e, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e,
	0x32, 0x64, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x33, 0x64,
	0x10, 0x04, 0x22, 0x3d, 0x0a, 0x07, 0x4c, 0x6f, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x32, 0x0a,
	0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x65, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x10,
	0x02, 0x22, 0xcc, 0x03, 0x0a, 0x09, 0x4c, 0x6f, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x35, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x45, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x54, 0x79, 0x70, 0x65, 0x2e,
	0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0d, 0xfa, 0x42, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x82, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x08, 0x6c, 0x6f, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x48, 0x0a,
	0x0a, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0d, 0xfa,
	0x42, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x09, 0x64, 0x61,
	0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x35, 0x0a, 0x08, 0x6c, 0x6f, 0x74, 0x5f, 0x75,
	0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x92, 0x01,
	0x14, 0x22, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d,
	0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x07, 0x6c, 0x6f, 0x74, 0x55, 0x69, 0x64, 0x73, 0x12, 0x39,
	0x0a, 0x0a, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x6f, 0x72, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x92, 0x01, 0x14, 0x22, 0x12, 0x72, 0x10, 0x32, 0x0e,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x09,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4f, 0x72, 0x67, 0x73, 0x12, 0x3f, 0x0a, 0x0d, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x6f, 0x72, 0x5f, 0x6f, 0x72, 0x67, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09,
	0x42, 0x1a, 0xfa, 0x42, 0x17, 0x92, 0x01, 0x14, 0x22, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b,
	0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x0c, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x4f, 0x72, 0x67, 0x73, 0x12, 0x3f, 0x0a, 0x0d, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x92, 0x01, 0x14, 0x22, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e,
	0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x0c, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x73, 0x3a, 0x03, 0xba, 0x47, 0x00,
	0x22, 0xb3, 0x01, 0x0a, 0x17, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x0e, 0xfa, 0x42, 0x0b, 0x1a, 0x09, 0x30, 0x00, 0x30, 0x90, 0x1c, 0x30, 0x80, 0xa3, 0x05, 0x52,
	0x08, 0x74, 0x69, 0x6d, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x3b, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x25, 0xfa, 0x42, 0x22, 0x92, 0x01, 0x1f,
	0x22, 0x1d, 0x72, 0x1b, 0x52, 0x06, 0x61, 0x6e, 0x6e, 0x6f, 0x32, 0x64, 0x52, 0x06, 0x61, 0x6e,
	0x6e, 0x6f, 0x33, 0x64, 0x52, 0x04, 0x65, 0x6c, 0x65, 0x6d, 0x52, 0x03, 0x6a, 0x6f, 0x62, 0x52,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xc1, 0x02, 0x0a, 0x15, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x3d, 0x0a, 0x05, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x2e, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x05, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x1a,
	0xdb, 0x01, 0x0a, 0x04, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x35, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12,
	0x48, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x2e, 0x55, 0x6e, 0x69, 0x74, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x1a, 0x38, 0x0a, 0x0a, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x3a, 0x18, 0xba, 0x47, 0x15, 0xba, 0x01, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x72, 0x61, 0x6e, 0x67, 0x65, 0xba, 0x01, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x3a, 0x0b, 0xba,
	0x47, 0x08, 0xba, 0x01, 0x05, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x22, 0x87, 0x02, 0x0a, 0x1b, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x42, 0x79, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x01, 0x52, 0x05, 0x70, 0x68, 0x61,
	0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x79, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61,
	0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x62, 0x79, 0x45, 0x78, 0x65, 0x63, 0x74,
	0x65, 0x61, 0x6d, 0x12, 0x35, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74,
	0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x21,
	0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73,
	0x7a, 0x3a, 0x11, 0xba, 0x47, 0x0e, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x05, 0x70,
	0x68, 0x61, 0x73, 0x65, 0x22, 0x95, 0x04, 0x0a, 0x0f, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f,
	0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x08, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x08, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6c,
	0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65, 0x6c, 0x65, 0x6d, 0x73,
	0x12, 0x10, 0x0a, 0x03, 0x69, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x69,
	0x6e, 0x73, 0x12, 0x51, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x5f, 0x62, 0x79, 0x5f, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x73, 0x42, 0x79, 0x57, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x42, 0x79, 0x57,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x6a, 0x6f, 0x62, 0x73, 0x5f, 0x70, 0x65,
	0x72, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x6a, 0x6f,
	0x62, 0x73, 0x50, 0x65, 0x72, 0x48, 0x6f, 0x75, 0x72, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x6c, 0x65,
	0x6d, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0c, 0x65, 0x6c, 0x65, 0x6d, 0x73, 0x50, 0x65, 0x72, 0x48, 0x6f, 0x75, 0x72, 0x12,
	0x23, 0x0a, 0x0d, 0x65, 0x6c, 0x65, 0x6d, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x65, 0x6c, 0x65, 0x6d, 0x41, 0x63, 0x63, 0x75,
	0x72, 0x61, 0x63, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x75,
	0x72, 0x61, 0x63, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x41,
	0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x1a, 0x3e, 0x0a, 0x10, 0x49, 0x6e, 0x73, 0x42, 0x79,
	0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x3a, 0x73, 0xba, 0x47, 0x70, 0xba, 0x01, 0x08, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0xba, 0x01, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0xba, 0x01,
	0x05, 0x65, 0x6c, 0x65, 0x6d, 0x73, 0xba, 0x01, 0x03, 0x69, 0x6e, 0x73, 0xba, 0x01, 0x0d, 0x69,
	0x6e, 0x73, 0x5f, 0x62, 0x79, 0x5f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0xba, 0x01, 0x0d, 0x6a,
	0x6f, 0x62, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0xba, 0x01, 0x0e, 0x65,
	0x6c, 0x65, 0x6d, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0xba, 0x01, 0x0d,
	0x65, 0x6c, 0x65, 0x6d, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0xba, 0x01, 0x0c,
	0x69, 0x6e, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x22, 0x83, 0x01, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x42, 0x79, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x12, 0x38, 0x0a, 0x08, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72,
	0x52, 0x08, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x3a, 0x16, 0xba, 0x47, 0x13, 0xba,
	0x01, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0xba, 0x01, 0x08, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65,
	0x72, 0x73, 0x22, 0x4e, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67,
	0x4c, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x21, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65,
	0x73, 0x7a, 0x22, 0xc8, 0x06, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x67, 0x6f, 0x69, 0x6e,
	0x67, 0x4c, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x12, 0x38, 0x0a, 0x04, 0x6c, 0x6f, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x2e, 0x4c, 0x6f, 0x74, 0x52, 0x04, 0x6c, 0x6f, 0x74, 0x73, 0x1a, 0x9f, 0x02, 0x0a, 0x05, 0x50,
	0x68, 0x61, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x50, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x2e, 0x50, 0x68, 0x61, 0x73, 0x65, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x64, 0x61, 0x79, 0x73, 0x5f, 0x6c, 0x65, 0x66, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x11, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x79, 0x73, 0x4c, 0x65,
	0x66, 0x74, 0x1a, 0x36, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x22, 0x2e, 0x0a, 0x04, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x10, 0x01, 0x12, 0x0a,
	0x0a, 0x06, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x10, 0x02, 0x3a, 0x30, 0xba, 0x47, 0x2d, 0xba,
	0x01, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba,
	0x01, 0x04, 0x74, 0x79, 0x70, 0x65, 0xba, 0x01, 0x13, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x5f, 0x6c, 0x65, 0x66, 0x74, 0x1a, 0xaa, 0x03, 0x0a,
	0x03, 0x4c, 0x6f, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x41, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x3e, 0x0a, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x74, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x2e, 0x50, 0x68, 0x61, 0x73, 0x65, 0x52, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65,
	0x73, 0x12, 0x3c, 0x0a, 0x0c, 0x65, 0x78, 0x70, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x3a, 0x4d, 0xba, 0x47, 0x4a, 0xba,
	0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x09, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0xba, 0x01, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0xba, 0x01, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0xba, 0x01, 0x0c,
	0x65, 0x78, 0x70, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0xba, 0x01, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x3a, 0x12, 0xba, 0x47, 0x0f, 0xba, 0x01,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0xba, 0x01, 0x04, 0x6c, 0x6f, 0x74, 0x73, 0x32, 0xcc, 0x07,
	0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x6d, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73,
	0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x18, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x2f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x7c, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x24, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x12, 0x15, 0x2f,
	0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x65, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1f, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x12, 0x0e, 0x2f, 0x76, 0x31,
	0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x70, 0x0a, 0x0e, 0x47,
	0x65, 0x74, 0x4f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x74, 0x73, 0x12, 0x22, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f,
	0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x20, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x74, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f, 0x76, 0x31,
	0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f, 0x6f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x12, 0x6f, 0x0a,
	0x0c, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x4c, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x12, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74,
	0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x75,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x74, 0x61,
	0x74, 0x12, 0x20, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x74, 0x61,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x12, 0x15,
	0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x9c, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x42, 0x79, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x12, 0x28,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x42, 0x79, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73,
	0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x42, 0x79, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x12, 0x2a, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f,
	0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x2f,
	0x7b, 0x70, 0x68, 0x61, 0x73, 0x65, 0x7d, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x2d, 0x62, 0x79, 0x2d,
	0x65, 0x78, 0x65, 0x63, 0x12, 0x76, 0x0a, 0x10, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73,
	0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f, 0x76, 0x31, 0x2f,
	0x70, 0x72, 0x6f, 0x64, 0x2d, 0x62, 0x79, 0x2d, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x4a, 0x0a, 0x0b,
	0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x39, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x2f, 0x76, 0x31, 0x3b,
	0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_annostat_v1_stats_proto_rawDescOnce sync.Once
	file_annostat_v1_stats_proto_rawDescData = file_annostat_v1_stats_proto_rawDesc
)

func file_annostat_v1_stats_proto_rawDescGZIP() []byte {
	file_annostat_v1_stats_proto_rawDescOnce.Do(func() {
		file_annostat_v1_stats_proto_rawDescData = protoimpl.X.CompressGZIP(file_annostat_v1_stats_proto_rawDescData)
	})
	return file_annostat_v1_stats_proto_rawDescData
}

var file_annostat_v1_stats_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_annostat_v1_stats_proto_msgTypes = make([]protoimpl.MessageInfo, 29)
var file_annostat_v1_stats_proto_goTypes = []interface{}{
	(DataType_Enum)(0),                       // 0: annostat.v1.DataType.Enum
	(LotType_Enum)(0),                        // 1: annostat.v1.LotType.Enum
	(GetOngoingLotsReply_Phase_Type_Enum)(0), // 2: annostat.v1.GetOngoingLotsReply.Phase.Type.Enum
	(*GetOrderCountRequest)(nil),             // 3: annostat.v1.GetOrderCountRequest
	(*GetOrderCountReply)(nil),               // 4: annostat.v1.GetOrderCountReply
	(*GetOrderConversionReply)(nil),          // 5: annostat.v1.GetOrderConversionReply
	(*GetLotCountRequest)(nil),               // 6: annostat.v1.GetLotCountRequest
	(*GetLotCountReply)(nil),                 // 7: annostat.v1.GetLotCountReply
	(*GetLotStatusRequest)(nil),              // 8: annostat.v1.GetLotStatusRequest
	(*GetLotStatusReply)(nil),                // 9: annostat.v1.GetLotStatusReply
	(*GetLotLabelStatReply)(nil),             // 10: annostat.v1.GetLotLabelStatReply
	(*TimeRange)(nil),                        // 11: annostat.v1.TimeRange
	(*DataType)(nil),                         // 12: annostat.v1.DataType
	(*LotType)(nil),                          // 13: annostat.v1.LotType
	(*LotFilter)(nil),                        // 14: annostat.v1.LotFilter
	(*ProductionByTimeRequest)(nil),          // 15: annostat.v1.ProductionByTimeRequest
	(*ProductionByTimeReply)(nil),            // 16: annostat.v1.ProductionByTimeReply
	(*GetLotStatByExecutorRequest)(nil),      // 17: annostat.v1.GetLotStatByExecutorRequest
	(*ExecutorCounter)(nil),                  // 18: annostat.v1.ExecutorCounter
	(*GetLotStatByExecutorReply)(nil),        // 19: annostat.v1.GetLotStatByExecutorReply
	(*GetOngoingLotsRequest)(nil),            // 20: annostat.v1.GetOngoingLotsRequest
	(*GetOngoingLotsReply)(nil),              // 21: annostat.v1.GetOngoingLotsReply
	(*GetOrderCountRequest_Filter)(nil),      // 22: annostat.v1.GetOrderCountRequest.Filter
	(*GetLotStatusReply_Phase)(nil),          // 23: annostat.v1.GetLotStatusReply.Phase
	(*GetLotLabelStatReply_Cuboid)(nil),      // 24: annostat.v1.GetLotLabelStatReply.Cuboid
	nil,                                      // 25: annostat.v1.GetLotLabelStatReply.CuboidsEntry
	(*ProductionByTimeReply_Unit)(nil),       // 26: annostat.v1.ProductionByTimeReply.Unit
	nil,                                      // 27: annostat.v1.ProductionByTimeReply.Unit.ItemsEntry
	nil,                                      // 28: annostat.v1.ExecutorCounter.InsByWidgetEntry
	(*GetOngoingLotsReply_Phase)(nil),        // 29: annostat.v1.GetOngoingLotsReply.Phase
	(*GetOngoingLotsReply_Lot)(nil),          // 30: annostat.v1.GetOngoingLotsReply.Lot
	(*GetOngoingLotsReply_Phase_Type)(nil),   // 31: annostat.v1.GetOngoingLotsReply.Phase.Type
	(*timestamppb.Timestamp)(nil),            // 32: google.protobuf.Timestamp
	(*v1.BaseUser)(nil),                      // 33: iam.v1.BaseUser
}
var file_annostat_v1_stats_proto_depIdxs = []int32{
	22, // 0: annostat.v1.GetOrderCountRequest.filter:type_name -> annostat.v1.GetOrderCountRequest.Filter
	14, // 1: annostat.v1.GetLotCountRequest.filter:type_name -> annostat.v1.LotFilter
	23, // 2: annostat.v1.GetLotStatusReply.phases:type_name -> annostat.v1.GetLotStatusReply.Phase
	25, // 3: annostat.v1.GetLotLabelStatReply.cuboids:type_name -> annostat.v1.GetLotLabelStatReply.CuboidsEntry
	32, // 4: annostat.v1.TimeRange.from:type_name -> google.protobuf.Timestamp
	32, // 5: annostat.v1.TimeRange.to:type_name -> google.protobuf.Timestamp
	11, // 6: annostat.v1.LotFilter.time_range:type_name -> annostat.v1.TimeRange
	1,  // 7: annostat.v1.LotFilter.lot_types:type_name -> annostat.v1.LotType.Enum
	0,  // 8: annostat.v1.LotFilter.data_types:type_name -> annostat.v1.DataType.Enum
	14, // 9: annostat.v1.ProductionByTimeRequest.filter:type_name -> annostat.v1.LotFilter
	26, // 10: annostat.v1.ProductionByTimeReply.units:type_name -> annostat.v1.ProductionByTimeReply.Unit
	11, // 11: annostat.v1.GetLotStatByExecutorRequest.time_range:type_name -> annostat.v1.TimeRange
	33, // 12: annostat.v1.ExecutorCounter.executor:type_name -> iam.v1.BaseUser
	28, // 13: annostat.v1.ExecutorCounter.ins_by_widget:type_name -> annostat.v1.ExecutorCounter.InsByWidgetEntry
	18, // 14: annostat.v1.GetLotStatByExecutorReply.counters:type_name -> annostat.v1.ExecutorCounter
	30, // 15: annostat.v1.GetOngoingLotsReply.lots:type_name -> annostat.v1.GetOngoingLotsReply.Lot
	11, // 16: annostat.v1.GetOrderCountRequest.Filter.time_range:type_name -> annostat.v1.TimeRange
	24, // 17: annostat.v1.GetLotLabelStatReply.CuboidsEntry.value:type_name -> annostat.v1.GetLotLabelStatReply.Cuboid
	11, // 18: annostat.v1.ProductionByTimeReply.Unit.time_range:type_name -> annostat.v1.TimeRange
	27, // 19: annostat.v1.ProductionByTimeReply.Unit.items:type_name -> annostat.v1.ProductionByTimeReply.Unit.ItemsEntry
	2,  // 20: annostat.v1.GetOngoingLotsReply.Phase.type:type_name -> annostat.v1.GetOngoingLotsReply.Phase.Type.Enum
	0,  // 21: annostat.v1.GetOngoingLotsReply.Lot.data_type:type_name -> annostat.v1.DataType.Enum
	29, // 22: annostat.v1.GetOngoingLotsReply.Lot.phases:type_name -> annostat.v1.GetOngoingLotsReply.Phase
	32, // 23: annostat.v1.GetOngoingLotsReply.Lot.exp_end_time:type_name -> google.protobuf.Timestamp
	32, // 24: annostat.v1.GetOngoingLotsReply.Lot.created_at:type_name -> google.protobuf.Timestamp
	3,  // 25: annostat.v1.Stats.GetOrderCount:input_type -> annostat.v1.GetOrderCountRequest
	3,  // 26: annostat.v1.Stats.GetOrderConversion:input_type -> annostat.v1.GetOrderCountRequest
	6,  // 27: annostat.v1.Stats.GetLotCount:input_type -> annostat.v1.GetLotCountRequest
	20, // 28: annostat.v1.Stats.GetOngoingLots:input_type -> annostat.v1.GetOngoingLotsRequest
	8,  // 29: annostat.v1.Stats.GetLotStatus:input_type -> annostat.v1.GetLotStatusRequest
	8,  // 30: annostat.v1.Stats.GetLotLabelStat:input_type -> annostat.v1.GetLotStatusRequest
	17, // 31: annostat.v1.Stats.GetLotStatByExecutor:input_type -> annostat.v1.GetLotStatByExecutorRequest
	15, // 32: annostat.v1.Stats.ProductionByTime:input_type -> annostat.v1.ProductionByTimeRequest
	4,  // 33: annostat.v1.Stats.GetOrderCount:output_type -> annostat.v1.GetOrderCountReply
	5,  // 34: annostat.v1.Stats.GetOrderConversion:output_type -> annostat.v1.GetOrderConversionReply
	7,  // 35: annostat.v1.Stats.GetLotCount:output_type -> annostat.v1.GetLotCountReply
	21, // 36: annostat.v1.Stats.GetOngoingLots:output_type -> annostat.v1.GetOngoingLotsReply
	9,  // 37: annostat.v1.Stats.GetLotStatus:output_type -> annostat.v1.GetLotStatusReply
	10, // 38: annostat.v1.Stats.GetLotLabelStat:output_type -> annostat.v1.GetLotLabelStatReply
	19, // 39: annostat.v1.Stats.GetLotStatByExecutor:output_type -> annostat.v1.GetLotStatByExecutorReply
	16, // 40: annostat.v1.Stats.ProductionByTime:output_type -> annostat.v1.ProductionByTimeReply
	33, // [33:41] is the sub-list for method output_type
	25, // [25:33] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_annostat_v1_stats_proto_init() }
func file_annostat_v1_stats_proto_init() {
	if File_annostat_v1_stats_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_annostat_v1_stats_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderCountReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderConversionReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotCountReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotStatusReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotLabelStatReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LotType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LotFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductionByTimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductionByTimeReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotStatByExecutorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecutorCounter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotStatByExecutorReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOngoingLotsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOngoingLotsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderCountRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotStatusReply_Phase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotLabelStatReply_Cuboid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductionByTimeReply_Unit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOngoingLotsReply_Phase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOngoingLotsReply_Lot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annostat_v1_stats_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOngoingLotsReply_Phase_Type); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_annostat_v1_stats_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   29,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_annostat_v1_stats_proto_goTypes,
		DependencyIndexes: file_annostat_v1_stats_proto_depIdxs,
		EnumInfos:         file_annostat_v1_stats_proto_enumTypes,
		MessageInfos:      file_annostat_v1_stats_proto_msgTypes,
	}.Build()
	File_annostat_v1_stats_proto = out.File
	file_annostat_v1_stats_proto_rawDesc = nil
	file_annostat_v1_stats_proto_goTypes = nil
	file_annostat_v1_stats_proto_depIdxs = nil
}
