// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: anno/v1/job.proto

package anno

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Jobs_CreateJob_FullMethodName           = "/anno.v1.Jobs/CreateJob"
	Jobs_UpdateJob_FullMethodName           = "/anno.v1.Jobs/UpdateJob"
	Jobs_DeleteJob_FullMethodName           = "/anno.v1.Jobs/DeleteJob"
	Jobs_GetJob_FullMethodName              = "/anno.v1.Jobs/GetJob"
	Jobs_ListJob_FullMethodName             = "/anno.v1.Jobs/ListJob"
	Jobs_GetJoblog_FullMethodName           = "/anno.v1.Jobs/GetJoblog"
	Jobs_GetRawJoblog_FullMethodName        = "/anno.v1.Jobs/GetRawJoblog"
	Jobs_ClaimJob_FullMethodName            = "/anno.v1.Jobs/ClaimJob"
	Jobs_AssignJob_FullMethodName           = "/anno.v1.Jobs/AssignJob"
	Jobs_GiveupJob_FullMethodName           = "/anno.v1.Jobs/GiveupJob"
	Jobs_SubmitJob_FullMethodName           = "/anno.v1.Jobs/SubmitJob"
	Jobs_ReviewJob_FullMethodName           = "/anno.v1.Jobs/ReviewJob"
	Jobs_BatchRevertJob_FullMethodName      = "/anno.v1.Jobs/BatchRevertJob"
	Jobs_SetRawdataEmbedding_FullMethodName = "/anno.v1.Jobs/SetRawdataEmbedding"
	Jobs_SaveJobDraft_FullMethodName        = "/anno.v1.Jobs/SaveJobDraft"
	Jobs_GetJobDraft_FullMethodName         = "/anno.v1.Jobs/GetJobDraft"
	Jobs_GetJobLastCommitLog_FullMethodName = "/anno.v1.Jobs/GetJobLastCommitLog"
	Jobs_HasHoldingJobs_FullMethodName      = "/anno.v1.Jobs/HasHoldingJobs"
	Jobs_SkipAnnotation_FullMethodName      = "/anno.v1.Jobs/SkipAnnotation"
	Jobs_GetSkipAnnotation_FullMethodName   = "/anno.v1.Jobs/GetSkipAnnotation"
)

// JobsClient is the client API for Jobs service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type JobsClient interface {
	// not intended for external use
	CreateJob(ctx context.Context, in *CreateJobRequest, opts ...grpc.CallOption) (*Job, error)
	// not intended for external use
	UpdateJob(ctx context.Context, in *UpdateJobRequest, opts ...grpc.CallOption) (*Job, error)
	// not intended for external use
	DeleteJob(ctx context.Context, in *DeleteJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetJob(ctx context.Context, in *GetJobRequest, opts ...grpc.CallOption) (*Job, error)
	ListJob(ctx context.Context, in *ListJobRequest, opts ...grpc.CallOption) (*ListJobReply, error)
	GetJoblog(ctx context.Context, in *GetJoblogRequest, opts ...grpc.CallOption) (*GetJoblogReply, error)
	GetRawJoblog(ctx context.Context, in *GetJoblogRequest, opts ...grpc.CallOption) (*GetRawJoblogReply, error)
	// Claim a job
	ClaimJob(ctx context.Context, in *ClaimJobRequest, opts ...grpc.CallOption) (*ClaimJobResponse, error)
	// Assign a job
	AssignJob(ctx context.Context, in *AssignJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// giveup a job
	GiveupJob(ctx context.Context, in *GiveupJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Submit job annotations; used in the 1st phase only.
	SubmitJob(ctx context.Context, in *SubmitJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Review job annotations; used in 2nd phase onwards.
	ReviewJob(ctx context.Context, in *ReviewJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Revert multiple jobs to a previous phase.
	BatchRevertJob(ctx context.Context, in *BatchRevertJobRequest, opts ...grpc.CallOption) (*BatchRevertJobReply, error)
	// Set rawdata embedding.
	SetRawdataEmbedding(ctx context.Context, in *SetRawdataEmbeddingRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Save draft annos/comments in the server.
	SaveJobDraft(ctx context.Context, in *SaveJobDraftRequest, opts ...grpc.CallOption) (*SaveJobDraftReply, error)
	// Get draft annos/comments saved in the server.
	GetJobDraft(ctx context.Context, in *GetJobDraftRequest, opts ...grpc.CallOption) (*GetJobDraftReply, error)
	// get the last commit joblog of a phase
	GetJobLastCommitLog(ctx context.Context, in *GetJobLastCommitLogRequest, opts ...grpc.CallOption) (*GetJobLastCommitLogReply, error)
	// check if there is any holding jobs for users/orgs
	HasHoldingJobs(ctx context.Context, in *HasHoldingJobsRequest, opts ...grpc.CallOption) (*HasHoldingJobsReply, error)
	SkipAnnotation(ctx context.Context, in *SkipAnnotationRequest, opts ...grpc.CallOption) (*SkipAnnotationReply, error)
	GetSkipAnnotation(ctx context.Context, in *GetSkipAnnotationRequest, opts ...grpc.CallOption) (*GetSkipAnnotationReply, error)
}

type jobsClient struct {
	cc grpc.ClientConnInterface
}

func NewJobsClient(cc grpc.ClientConnInterface) JobsClient {
	return &jobsClient{cc}
}

func (c *jobsClient) CreateJob(ctx context.Context, in *CreateJobRequest, opts ...grpc.CallOption) (*Job, error) {
	out := new(Job)
	err := c.cc.Invoke(ctx, Jobs_CreateJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) UpdateJob(ctx context.Context, in *UpdateJobRequest, opts ...grpc.CallOption) (*Job, error) {
	out := new(Job)
	err := c.cc.Invoke(ctx, Jobs_UpdateJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) DeleteJob(ctx context.Context, in *DeleteJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Jobs_DeleteJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) GetJob(ctx context.Context, in *GetJobRequest, opts ...grpc.CallOption) (*Job, error) {
	out := new(Job)
	err := c.cc.Invoke(ctx, Jobs_GetJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) ListJob(ctx context.Context, in *ListJobRequest, opts ...grpc.CallOption) (*ListJobReply, error) {
	out := new(ListJobReply)
	err := c.cc.Invoke(ctx, Jobs_ListJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) GetJoblog(ctx context.Context, in *GetJoblogRequest, opts ...grpc.CallOption) (*GetJoblogReply, error) {
	out := new(GetJoblogReply)
	err := c.cc.Invoke(ctx, Jobs_GetJoblog_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) GetRawJoblog(ctx context.Context, in *GetJoblogRequest, opts ...grpc.CallOption) (*GetRawJoblogReply, error) {
	out := new(GetRawJoblogReply)
	err := c.cc.Invoke(ctx, Jobs_GetRawJoblog_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) ClaimJob(ctx context.Context, in *ClaimJobRequest, opts ...grpc.CallOption) (*ClaimJobResponse, error) {
	out := new(ClaimJobResponse)
	err := c.cc.Invoke(ctx, Jobs_ClaimJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) AssignJob(ctx context.Context, in *AssignJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Jobs_AssignJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) GiveupJob(ctx context.Context, in *GiveupJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Jobs_GiveupJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) SubmitJob(ctx context.Context, in *SubmitJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Jobs_SubmitJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) ReviewJob(ctx context.Context, in *ReviewJobRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Jobs_ReviewJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) BatchRevertJob(ctx context.Context, in *BatchRevertJobRequest, opts ...grpc.CallOption) (*BatchRevertJobReply, error) {
	out := new(BatchRevertJobReply)
	err := c.cc.Invoke(ctx, Jobs_BatchRevertJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) SetRawdataEmbedding(ctx context.Context, in *SetRawdataEmbeddingRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Jobs_SetRawdataEmbedding_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) SaveJobDraft(ctx context.Context, in *SaveJobDraftRequest, opts ...grpc.CallOption) (*SaveJobDraftReply, error) {
	out := new(SaveJobDraftReply)
	err := c.cc.Invoke(ctx, Jobs_SaveJobDraft_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) GetJobDraft(ctx context.Context, in *GetJobDraftRequest, opts ...grpc.CallOption) (*GetJobDraftReply, error) {
	out := new(GetJobDraftReply)
	err := c.cc.Invoke(ctx, Jobs_GetJobDraft_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) GetJobLastCommitLog(ctx context.Context, in *GetJobLastCommitLogRequest, opts ...grpc.CallOption) (*GetJobLastCommitLogReply, error) {
	out := new(GetJobLastCommitLogReply)
	err := c.cc.Invoke(ctx, Jobs_GetJobLastCommitLog_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) HasHoldingJobs(ctx context.Context, in *HasHoldingJobsRequest, opts ...grpc.CallOption) (*HasHoldingJobsReply, error) {
	out := new(HasHoldingJobsReply)
	err := c.cc.Invoke(ctx, Jobs_HasHoldingJobs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) SkipAnnotation(ctx context.Context, in *SkipAnnotationRequest, opts ...grpc.CallOption) (*SkipAnnotationReply, error) {
	out := new(SkipAnnotationReply)
	err := c.cc.Invoke(ctx, Jobs_SkipAnnotation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsClient) GetSkipAnnotation(ctx context.Context, in *GetSkipAnnotationRequest, opts ...grpc.CallOption) (*GetSkipAnnotationReply, error) {
	out := new(GetSkipAnnotationReply)
	err := c.cc.Invoke(ctx, Jobs_GetSkipAnnotation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JobsServer is the server API for Jobs service.
// All implementations must embed UnimplementedJobsServer
// for forward compatibility
type JobsServer interface {
	// not intended for external use
	CreateJob(context.Context, *CreateJobRequest) (*Job, error)
	// not intended for external use
	UpdateJob(context.Context, *UpdateJobRequest) (*Job, error)
	// not intended for external use
	DeleteJob(context.Context, *DeleteJobRequest) (*emptypb.Empty, error)
	GetJob(context.Context, *GetJobRequest) (*Job, error)
	ListJob(context.Context, *ListJobRequest) (*ListJobReply, error)
	GetJoblog(context.Context, *GetJoblogRequest) (*GetJoblogReply, error)
	GetRawJoblog(context.Context, *GetJoblogRequest) (*GetRawJoblogReply, error)
	// Claim a job
	ClaimJob(context.Context, *ClaimJobRequest) (*ClaimJobResponse, error)
	// Assign a job
	AssignJob(context.Context, *AssignJobRequest) (*emptypb.Empty, error)
	// giveup a job
	GiveupJob(context.Context, *GiveupJobRequest) (*emptypb.Empty, error)
	// Submit job annotations; used in the 1st phase only.
	SubmitJob(context.Context, *SubmitJobRequest) (*emptypb.Empty, error)
	// Review job annotations; used in 2nd phase onwards.
	ReviewJob(context.Context, *ReviewJobRequest) (*emptypb.Empty, error)
	// Revert multiple jobs to a previous phase.
	BatchRevertJob(context.Context, *BatchRevertJobRequest) (*BatchRevertJobReply, error)
	// Set rawdata embedding.
	SetRawdataEmbedding(context.Context, *SetRawdataEmbeddingRequest) (*emptypb.Empty, error)
	// Save draft annos/comments in the server.
	SaveJobDraft(context.Context, *SaveJobDraftRequest) (*SaveJobDraftReply, error)
	// Get draft annos/comments saved in the server.
	GetJobDraft(context.Context, *GetJobDraftRequest) (*GetJobDraftReply, error)
	// get the last commit joblog of a phase
	GetJobLastCommitLog(context.Context, *GetJobLastCommitLogRequest) (*GetJobLastCommitLogReply, error)
	// check if there is any holding jobs for users/orgs
	HasHoldingJobs(context.Context, *HasHoldingJobsRequest) (*HasHoldingJobsReply, error)
	SkipAnnotation(context.Context, *SkipAnnotationRequest) (*SkipAnnotationReply, error)
	GetSkipAnnotation(context.Context, *GetSkipAnnotationRequest) (*GetSkipAnnotationReply, error)
	mustEmbedUnimplementedJobsServer()
}

// UnimplementedJobsServer must be embedded to have forward compatible implementations.
type UnimplementedJobsServer struct {
}

func (UnimplementedJobsServer) CreateJob(context.Context, *CreateJobRequest) (*Job, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateJob not implemented")
}
func (UnimplementedJobsServer) UpdateJob(context.Context, *UpdateJobRequest) (*Job, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateJob not implemented")
}
func (UnimplementedJobsServer) DeleteJob(context.Context, *DeleteJobRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteJob not implemented")
}
func (UnimplementedJobsServer) GetJob(context.Context, *GetJobRequest) (*Job, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJob not implemented")
}
func (UnimplementedJobsServer) ListJob(context.Context, *ListJobRequest) (*ListJobReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListJob not implemented")
}
func (UnimplementedJobsServer) GetJoblog(context.Context, *GetJoblogRequest) (*GetJoblogReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJoblog not implemented")
}
func (UnimplementedJobsServer) GetRawJoblog(context.Context, *GetJoblogRequest) (*GetRawJoblogReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRawJoblog not implemented")
}
func (UnimplementedJobsServer) ClaimJob(context.Context, *ClaimJobRequest) (*ClaimJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClaimJob not implemented")
}
func (UnimplementedJobsServer) AssignJob(context.Context, *AssignJobRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AssignJob not implemented")
}
func (UnimplementedJobsServer) GiveupJob(context.Context, *GiveupJobRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GiveupJob not implemented")
}
func (UnimplementedJobsServer) SubmitJob(context.Context, *SubmitJobRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitJob not implemented")
}
func (UnimplementedJobsServer) ReviewJob(context.Context, *ReviewJobRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReviewJob not implemented")
}
func (UnimplementedJobsServer) BatchRevertJob(context.Context, *BatchRevertJobRequest) (*BatchRevertJobReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchRevertJob not implemented")
}
func (UnimplementedJobsServer) SetRawdataEmbedding(context.Context, *SetRawdataEmbeddingRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetRawdataEmbedding not implemented")
}
func (UnimplementedJobsServer) SaveJobDraft(context.Context, *SaveJobDraftRequest) (*SaveJobDraftReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveJobDraft not implemented")
}
func (UnimplementedJobsServer) GetJobDraft(context.Context, *GetJobDraftRequest) (*GetJobDraftReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobDraft not implemented")
}
func (UnimplementedJobsServer) GetJobLastCommitLog(context.Context, *GetJobLastCommitLogRequest) (*GetJobLastCommitLogReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobLastCommitLog not implemented")
}
func (UnimplementedJobsServer) HasHoldingJobs(context.Context, *HasHoldingJobsRequest) (*HasHoldingJobsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HasHoldingJobs not implemented")
}
func (UnimplementedJobsServer) SkipAnnotation(context.Context, *SkipAnnotationRequest) (*SkipAnnotationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SkipAnnotation not implemented")
}
func (UnimplementedJobsServer) GetSkipAnnotation(context.Context, *GetSkipAnnotationRequest) (*GetSkipAnnotationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSkipAnnotation not implemented")
}
func (UnimplementedJobsServer) mustEmbedUnimplementedJobsServer() {}

// UnsafeJobsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to JobsServer will
// result in compilation errors.
type UnsafeJobsServer interface {
	mustEmbedUnimplementedJobsServer()
}

func RegisterJobsServer(s grpc.ServiceRegistrar, srv JobsServer) {
	s.RegisterService(&Jobs_ServiceDesc, srv)
}

func _Jobs_CreateJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).CreateJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_CreateJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).CreateJob(ctx, req.(*CreateJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_UpdateJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).UpdateJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_UpdateJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).UpdateJob(ctx, req.(*UpdateJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_DeleteJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).DeleteJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_DeleteJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).DeleteJob(ctx, req.(*DeleteJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_GetJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).GetJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_GetJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).GetJob(ctx, req.(*GetJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_ListJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).ListJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_ListJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).ListJob(ctx, req.(*ListJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_GetJoblog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJoblogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).GetJoblog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_GetJoblog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).GetJoblog(ctx, req.(*GetJoblogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_GetRawJoblog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJoblogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).GetRawJoblog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_GetRawJoblog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).GetRawJoblog(ctx, req.(*GetJoblogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_ClaimJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClaimJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).ClaimJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_ClaimJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).ClaimJob(ctx, req.(*ClaimJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_AssignJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).AssignJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_AssignJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).AssignJob(ctx, req.(*AssignJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_GiveupJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiveupJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).GiveupJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_GiveupJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).GiveupJob(ctx, req.(*GiveupJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_SubmitJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).SubmitJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_SubmitJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).SubmitJob(ctx, req.(*SubmitJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_ReviewJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReviewJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).ReviewJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_ReviewJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).ReviewJob(ctx, req.(*ReviewJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_BatchRevertJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchRevertJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).BatchRevertJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_BatchRevertJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).BatchRevertJob(ctx, req.(*BatchRevertJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_SetRawdataEmbedding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetRawdataEmbeddingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).SetRawdataEmbedding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_SetRawdataEmbedding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).SetRawdataEmbedding(ctx, req.(*SetRawdataEmbeddingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_SaveJobDraft_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveJobDraftRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).SaveJobDraft(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_SaveJobDraft_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).SaveJobDraft(ctx, req.(*SaveJobDraftRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_GetJobDraft_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobDraftRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).GetJobDraft(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_GetJobDraft_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).GetJobDraft(ctx, req.(*GetJobDraftRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_GetJobLastCommitLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobLastCommitLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).GetJobLastCommitLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_GetJobLastCommitLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).GetJobLastCommitLog(ctx, req.(*GetJobLastCommitLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_HasHoldingJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HasHoldingJobsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).HasHoldingJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_HasHoldingJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).HasHoldingJobs(ctx, req.(*HasHoldingJobsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_SkipAnnotation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SkipAnnotationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).SkipAnnotation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_SkipAnnotation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).SkipAnnotation(ctx, req.(*SkipAnnotationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobs_GetSkipAnnotation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSkipAnnotationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServer).GetSkipAnnotation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jobs_GetSkipAnnotation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServer).GetSkipAnnotation(ctx, req.(*GetSkipAnnotationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Jobs_ServiceDesc is the grpc.ServiceDesc for Jobs service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Jobs_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "anno.v1.Jobs",
	HandlerType: (*JobsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateJob",
			Handler:    _Jobs_CreateJob_Handler,
		},
		{
			MethodName: "UpdateJob",
			Handler:    _Jobs_UpdateJob_Handler,
		},
		{
			MethodName: "DeleteJob",
			Handler:    _Jobs_DeleteJob_Handler,
		},
		{
			MethodName: "GetJob",
			Handler:    _Jobs_GetJob_Handler,
		},
		{
			MethodName: "ListJob",
			Handler:    _Jobs_ListJob_Handler,
		},
		{
			MethodName: "GetJoblog",
			Handler:    _Jobs_GetJoblog_Handler,
		},
		{
			MethodName: "GetRawJoblog",
			Handler:    _Jobs_GetRawJoblog_Handler,
		},
		{
			MethodName: "ClaimJob",
			Handler:    _Jobs_ClaimJob_Handler,
		},
		{
			MethodName: "AssignJob",
			Handler:    _Jobs_AssignJob_Handler,
		},
		{
			MethodName: "GiveupJob",
			Handler:    _Jobs_GiveupJob_Handler,
		},
		{
			MethodName: "SubmitJob",
			Handler:    _Jobs_SubmitJob_Handler,
		},
		{
			MethodName: "ReviewJob",
			Handler:    _Jobs_ReviewJob_Handler,
		},
		{
			MethodName: "BatchRevertJob",
			Handler:    _Jobs_BatchRevertJob_Handler,
		},
		{
			MethodName: "SetRawdataEmbedding",
			Handler:    _Jobs_SetRawdataEmbedding_Handler,
		},
		{
			MethodName: "SaveJobDraft",
			Handler:    _Jobs_SaveJobDraft_Handler,
		},
		{
			MethodName: "GetJobDraft",
			Handler:    _Jobs_GetJobDraft_Handler,
		},
		{
			MethodName: "GetJobLastCommitLog",
			Handler:    _Jobs_GetJobLastCommitLog_Handler,
		},
		{
			MethodName: "HasHoldingJobs",
			Handler:    _Jobs_HasHoldingJobs_Handler,
		},
		{
			MethodName: "SkipAnnotation",
			Handler:    _Jobs_SkipAnnotation_Handler,
		},
		{
			MethodName: "GetSkipAnnotation",
			Handler:    _Jobs_GetSkipAnnotation_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "anno/v1/job.proto",
}
