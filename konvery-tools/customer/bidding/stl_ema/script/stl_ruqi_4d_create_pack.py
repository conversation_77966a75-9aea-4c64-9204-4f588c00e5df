import json
import os
import shutil
import sys
import numpy as np
from pathlib import Path

sys.path.append('.')
from customer.common import tools


camera_names = [
    'camera-71',
    'camera-73',
    'camera-75',
    'camera-76',
    'camera-77',
    'camera-78',
    'camera-83'
]

mapping_id_EP40 = {
    'camera-71': 'front_narrow',
    'camera-73': 'front_wide',
    'camera-75': 'back_left',
    'camera-76': 'front_left',
    'camera-77': 'back_right',
    'camera-78': 'front_right',
    'camera-83': 'back',
}

mapping_id_EP40_dir = {
    'camera-71': 'camera_image_0',
    'camera-73': 'camera_image_1',
    'camera-75': 'camera_image_2',
    'camera-76': 'camera_image_3',
    'camera-77': 'camera_image_4',
    'camera-78': 'camera_image_5',
    'camera-83': 'camera_image_6',
}

camera_sizes = {
    'camera-71': {"width": 3840, "height": 2160},
    'camera-73': {"width": 3840, "height": 2160},
    'camera-75': {"width": 1920, "height": 1080},
    'camera-76': {"width": 1920, "height": 1080},
    'camera-77': {"width": 1920, "height": 1080},
    'camera-78': {"width": 1920, "height": 1080},
    'camera-83': {"width": 1920, "height": 1080},
}

file_mapping = {}


def construct_params_from_dir(params_root_path, out_params_file):
    camera_params = []
    for camera_name in camera_names:
        camera_name_extrinsic = 'top_center_lidar-to-' + mapping_id_EP40[camera_name] + '-extrinsic'
        camera_name_intrinsic = mapping_id_EP40[camera_name] + '-intrinsic'
        with open(os.path.join(params_root_path, camera_name_extrinsic+'.json')) as f:
            json_dict_extrinsic = json.load(f)
        with open(os.path.join(params_root_path, camera_name_intrinsic+'.json')) as f:
            json_dict_intrinsic = json.load(f)
        extrinsic_mat = np.linalg.inv(np.array(json_dict_extrinsic[camera_name_extrinsic]['param']['sensor_calib']['data']))
        intrinsic_mat = np.array(json_dict_intrinsic[camera_name_intrinsic]['param']['cam_K']['data'])
        dist_mat = json_dict_intrinsic[camera_name_intrinsic]['param']['cam_dist']['data']
        camera_param = {
            "camera_internal": {
                "fx": intrinsic_mat[0][0],
                "fy": intrinsic_mat[1][1],
                "cx": intrinsic_mat[0][2],
                "cy": intrinsic_mat[1][2]
            },
            "camera_external": extrinsic_mat.flatten().tolist(),
            "rowMajor": True,
            "distortionK": dist_mat[0][:2],
            "distortionP": dist_mat[0][2:4],
            "width": camera_sizes[camera_name]['width'],
            "height": camera_sizes[camera_name]['height']
        }
        camera_params.append(camera_param)
    tools.write_json_file(camera_params, out_params_file, indent=4)


def copy_file(src, dst, orig_prefix):
    global file_mapping
    shutil.copyfile(src, dst)
    file_mapping[os.sep.join(Path(dst).parts[-3:])] = src.replace(orig_prefix, '')


def run(input_dir, output_dir):
    prefix = os.path.dirname(input_dir)

    mcap = input_dir
    collection_out_dir = os.path.join(output_dir, 'data1')
    os.makedirs(collection_out_dir)

    lidar_file = os.path.join(mcap, 'downsampled_map.pcd')
    global_imu_pose_file = os.path.join(mcap, 'global_imu_pose.txt')
    param_dir = os.path.join(mcap, 'param')
    label_file = os.path.join(mcap, 'label_file')

    construct_params_from_dir(param_dir, os.path.join(collection_out_dir, 'camera_config', 'data1.json'))

    with open(global_imu_pose_file) as f:
        poses = f.readlines()
    global_imu_pose = {}
    for pose in poses:
        pose_split = pose.split()
        global_imu_pose[pose_split[0]] = [float(i) for i in pose_split[1:]]

    for out_cam_dir in mapping_id_EP40_dir.values():
        os.mkdir(os.path.join(collection_out_dir, out_cam_dir))
    frame_dirs = tools.get_sub_dir(label_file, name_only=False, sort=True)
    for idx, frame_dir in enumerate(frame_dirs, start=1):
        imgs = tools.get_file_by_extension(frame_dir, '.jpg')
        for img in imgs:
            img_type = img.name.rsplit('-', 1)[0]
            if img_type not in camera_names:
                continue
            copy_file(img.path, os.path.join(collection_out_dir, mapping_id_EP40_dir[img_type], f'data_{idx:02d}.jpg'), prefix)
    lidar_point_cloud_dir = os.path.join(collection_out_dir, 'lidar_point_cloud')
    os.mkdir(lidar_point_cloud_dir)
    copy_file(lidar_file, os.path.join(lidar_point_cloud_dir, 'data1.pcd'), prefix)
    tools.write_json_file(file_mapping,  os.path.join(output_dir, 'file_mapping.json'), ensure_ascii=False, indent=4)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
