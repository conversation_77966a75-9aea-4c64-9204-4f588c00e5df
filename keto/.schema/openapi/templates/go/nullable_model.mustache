type Nullable{{{classname}}} struct {
	value {{^isArray}}{{^isFreeFormObject}}*{{/isFreeFormObject}}{{/isArray}}{{{classname}}}
	isSet bool
}

func (v Nullable{{classname}}) Get() {{^isArray}}{{^isFreeFormObject}}*{{/isFreeFormObject}}{{/isArray}}{{classname}} {
	return v.value
}

func (v *Nullable{{classname}}) Set(val {{^isArray}}{{^isFreeFormObject}}*{{/isFreeFormObject}}{{/isArray}}{{classname}}) {
	v.value = val
	v.isSet = true
}

func (v Nullable{{classname}}) IsSet() bool {
	return v.isSet
}

func (v *Nullable{{classname}}) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullable{{classname}}(val {{^isArray}}{{^isFreeFormObject}}*{{/isFreeFormObject}}{{/isArray}}{{classname}}) *Nullable{{classname}} {
	return &Nullable{{classname}}{value: val, isSet: true}
}

func (v Nullable{{{classname}}}) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *Nullable{{{classname}}}) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
