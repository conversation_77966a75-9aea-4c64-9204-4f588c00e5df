syntax = "proto3";

package anno.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anno/v1;anno";
option java_multiple_files = true;
option java_package = "anno.v1";

service Specgrants {
  rpc CreateSpecgrant (CreateSpecgrantRequest) returns (Specgrant) {
    option (google.api.http) = {
      post: "/v1/specgrants"
      body: "*"
    };
  }

  rpc DeleteSpecgrant (DeleteSpecgrantRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/specgrants"
    };
  }

  // rpc GetSpecgrant (GetSpecgrantRequest) returns (GetSpecgrantReply) {
  //   option (google.api.http) = {
  //     get: "/v1/specgrants/{uid}"
  //   };
  // }

  rpc ListSpecgrant (ListSpecgrantRequest) returns (ListSpecgrantReply) {
    option (google.api.http) = {
      get: "/v1/specgrants"
    };
  }
}

message CreateSpecgrantRequest {
  option (openapi.v3.schema) = {
    required: ["grantee_uid", "item_type", "item_uid"]
  };

  // grantee uid
  string grantee_uid = 1;
  // type of the item
  Specgrant.ItemType.Enum item_type = 2 [(validate.rules).enum = {defined_only: true, not_in: [0]}];
  // item uid
  string item_uid = 3;
}

message Specgrant {
  option (openapi.v3.schema) = {
    required: ["grantor_uid", "grantee_uid", "org_uid", "item_type", "item_uid", "created_at"]
  };

  message ItemType {
    enum Enum {
      unspecified = 0;
      AnnoLot = 1;
    }
  }

  // grantor uid
  string grantor_uid = 1;
  // grantee uid
  string grantee_uid = 2;
  // item type
  ItemType.Enum item_type = 3 [(validate.rules).enum = { defined_only: true, not_in: [0]}];
  // item uid
  string item_uid = 4;
  // item owner organization uid
  string org_uid = 5;

  google.protobuf.Timestamp created_at = 15;
}

message SpecgrantFilter {
  // grantor uid
  string grantor_uid = 1;
  // grantee uid
  string grantee_uid = 2;
  // item owner orgnization uid
  string org_uid = 3;
  // item type
  Specgrant.ItemType.Enum item_type = 4;
  // item uid list
  repeated string item_uids = 5 [(validate.rules).repeated.items.string = {pattern: "^[a-z0-9]{11}$"}];
}

message DeleteSpecgrantRequest {
  SpecgrantFilter filter = 1 [(validate.rules).message.required = true];
}

message ListSpecgrantRequest {
  int32 pagesz = 1 [(validate.rules).int32 = {gte:0, lte: 100}];
  // An opaque pagination token returned from a previous call.
  // An empty token denotes the first page.
  string page_token = 2;

  SpecgrantFilter filter = 3;
}

message ListSpecgrantReply {
  option (openapi.v3.schema) = {
    required: ["grants", "next_page_token"]
  };

  repeated Specgrant grants = 1;
  // An opaque pagination token, if not empty, to be used to fetch the next page of results
  string next_page_token = 2;
}
