GOPATH:=$(shell go env GOPATH)
VERSION:=$(shell git describe --tags --always)
LDFLAGS:="" # -s
API_PATH_PREFIX:=/apidocs

.PHONY: build
# build
build:
	mkdir -p bin/ && go build -ldflags "-X main.version=$(VERSION) $(LDFLAGS)" -o ./bin/ ./...

# show help
help:
	@echo ''
	@echo 'Usage:'
	@echo ' make [target]'
	@echo ''
	@echo 'Targets:'
	@awk '/^[a-zA-Z\-_0-9]+:/ { \
	helpMessage = match(lastLine, /^# (.*)/); \
		if (helpMessage) { \
			helpCommand = substr($$1, 0, index($$1, ":")-1); \
			helpMessage = substr(lastLine, RSTART + 2, RLENGTH); \
			printf "\033[36m%-22s\033[0m %s\n", helpCommand,helpMessage; \
		} \
	} \
	{ lastLine = $$0 }' $(MAKEFILE_LIST)

.DEFAULT_GOAL := help
