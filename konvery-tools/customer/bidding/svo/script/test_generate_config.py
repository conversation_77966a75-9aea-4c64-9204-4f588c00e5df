import numpy as np
from scipy.spatial.transform import Rotation
import json


# def quaternion_to_matrix(quaternion):
#     x, y, z, w = quaternion
#     # 构建单位四元数矩阵
#     # rotation_matrix = np.array([
#     #     [1 - 2*(y**2 + z**2), 2*(x*y - z*w), 2*(x*z + y*w), 0],
#     #     [2*(x*y + z*w), 1 - 2*(x**2 + z**2), 2*(y*z - x*w), 0],
#     #     [2*(x*z - y*w), 2*(y*z + x*w), 1 - 2*(x**2 + y**2), 0],
#     #     [0, 0, 0, 1]
#     # ])
#     # print(rotation_matrix)
#     rotation = Rotation.from_quat([x, y, z, w])
#     rotation_vector = rotation.as_rotvec()
#     print(x, y, z, w)
#     print(rotation_vector)
#     return rotation_vector


def transformation_matrix(rotation, translation):
    # 创建旋转对象
    r = Rotation.from_quat(rotation)

    # 获取旋转矩阵
    rotation_matrix = r.as_matrix()

    # 构建4x4单位矩阵
    transformation_matrix = np.eye(4)

    # 添加旋转部分到变换矩阵的左上3x3子矩阵
    transformation_matrix[:3, :3] = rotation_matrix

    # 添加平移部分到变换矩阵的右侧
    transformation_matrix[:3, 3] = translation

    result_matrix = transformation_matrix.tolist()

    result_matrix = [
        [result_matrix[0][0], result_matrix[1][0], result_matrix[2][0], result_matrix[0][3]],
        [result_matrix[0][1], result_matrix[1][1], result_matrix[2][1], result_matrix[1][3]],
        [result_matrix[0][2], result_matrix[1][2], result_matrix[2][2], result_matrix[2][3]],
        [result_matrix[3][0], result_matrix[3][1], result_matrix[3][2], result_matrix[3][3]],
    ]

    return result_matrix  # 转换为列表形式


video0 = [0.400986, 0.165281, -0.198189, 0.518673, -0.504153, 0.483975, -0.492520]
video1 = [0.345215, 0.019256, -0.176292, -0.502368, 0.492845, -0.487605, 0.516692]
video6 = [-1.898077, 0.027305, -0.334986, -0.488298, -0.496504, 0.519686, 0.494950]
video8 = [0.352558, 0.478698, -0.299558, -0.659700, 0.197784, -0.195762, 0.698107]
video9 = [0.291963, -0.463984, -0.282881, 0.214169, -0.658895, 0.681338, -0.236151]
video10 = [0.991937, 0.932034, -0.992624, -0.583380, -0.375860, 0.382830, 0.609785]
video11 = [1.072448, -0.931587, -1.090752, -0.359195, -0.590443, 0.616943, 0.376481]

video0_matrix = transformation_matrix(video0[3:], video0[:3])
video1_matrix = transformation_matrix(video1[3:], video1[:3])
video6_matrix = transformation_matrix(video6[3:], video6[:3])
video8_matrix = transformation_matrix(video8[3:], video8[:3])
video9_matrix = transformation_matrix(video9[3:], video9[:3])
video10_matrix = transformation_matrix(video10[3:], video10[:3])
video11_matrix = transformation_matrix(video11[3:], video11[:3])

config_json = {
    'camera': {
        'video0': '车顶前向30度',
        'video1': '车顶前向120度',
        'video6': '后视',
        'video8': '左前视',
        'video9': '右前视',
        'video10': '左后视',
        'video11': '右后视',
    },
    'data_type': 'fusion_pointcloud',
    'sensor_params': {
        'video0': {
            'camera_model': 'pinhole',
            'extrinsic': video0_matrix,
            'fx': 7300.331395238032,
            'fy': 7300.331395238032,
            'cx': 1860.230282141251,
            'cy': 1024.229261907278,
            'k1': -0.06444095353246256,
            'k2': -3.948561054819121,
            'k3': 40.32173757539983,
            'p1': 0,
            'p2': 0,
        },
        'video1': {
            'camera_model': 'pinhole',
            'extrinsic': video1_matrix,
            'fx': 1942.352876286083,
            'fy': 1912.352876286083,
            'cx': 1911.909728366711,
            'cy': 1058.622409355667,
            'k1': -0.3726122490595825,
            'k2': 0.1831556314822537,
            'k3': -0.05043430626068737,
            'p1': 0,
            'p2': 0,
        },
        'video6': {
            'camera_model': 'pinhole',
            'extrinsic': video6_matrix,
            'fx': 1994.340079946031,
            'fy': 1934.340079946031,
            'cx': 966.8827052209002,
            'cy': 464.1850332637497,
            'k1': -0.5347149386636725,
            'k2': 0.2729171533520863,
            'k3': -0.01160629927663299,
            'p1': 0,
            'p2': 0,
        },
        'video8': {
            'camera_model': 'pinhole',
            'extrinsic': video8_matrix,
            'fx': 1954.656710320746,
            'fy': 1954.656710320746,
            'cx': 1889.332680783847,
            'cy': 1128.698077297232,
            'k1': -0.3662564072644919,
            'k2': 0.1704851578911585,
            'k3': -0.04284477647576217,
            'p1': 0,
            'p2': 0,
        },
        'video9': {
            'camera_model': 'pinhole',
            'extrinsic': video9_matrix,
            'fx': 1958.154578361326,
            'fy': 1958.154578361326,
            'cx': 1903.371267122821,
            'cy': 1062.071803048066,
            'k1': -0.3719597485891765,
            'k2': 0.1841993483580042,
            'k3': -0.05182117934150812,
            'p1': 0,
            'p2': 0,
        },
        'video10': {
            'camera_model': 'pinhole',
            'extrinsic': video10_matrix,
            'fx': 1900.090471153472,
            'fy': 1920.090471153472,
            'cx': 965.0938210619132,
            'cy': 538.5446105507351,
            'k1': -0.5345086450891524,
            'k2': 0.314456467342671,
            'k3': -0.1783791447419572,
            'p1': 0,
            'p2': 0,
        },
        'video11': {
            'camera_model': 'pinhole',
            'extrinsic': video11_matrix,
            'fx': 1969.329509489703,
            'fy': 1969.329509489703,
            'cx': 974.3465128041447,
            'cy': 528.0049010996765,
            'k1': -0.5396268198606394,
            'k2': 0.2664242040484838,
            'k3': -0.00841205834518587,
            'p1': 0,
            'p2': 0,
        },
    }
}

with open('/Users/<USER>/Downloads/svo/config.json', 'w') as f:
    json.dump(config_json, f, indent=4)
