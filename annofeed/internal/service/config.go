package service

import (
	"context"

	"annofeed/internal/conf"
	"gitlab.rp.konvery.work/platform/apis/annofeed/v1"

	"google.golang.org/protobuf/types/known/emptypb"
)

type ConfigsService struct {
	annofeed.UnimplementedConfigsServer
}

func NewConfigsService() *ConfigsService {
	return &ConfigsService{}
}

func (o *ConfigsService) GetVersion(ctx context.Context, req *emptypb.Empty) (*annofeed.GetVersionReply, error) {
	return &annofeed.GetVersionReply{Version: conf.Version}, nil
}
