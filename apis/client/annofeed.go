package client

import (
	"context"

	annov1 "gitlab.rp.konvery.work/platform/apis/anno/v1"
	annofeedv1 "gitlab.rp.konvery.work/platform/apis/annofeed/v1"

	"github.com/go-kratos/kratos/v2/errors"
	"google.golang.org/grpc"
)

type DataState string

const (
	DataStateRaw        DataState = "raw"
	DataStateFetching   DataState = "fetching"
	DataStateProcessing DataState = "processing"
	DataStateReady      DataState = "ready"
	DataStateAbandoned  DataState = "abandoned"
	DataStateDisabled   DataState = "disabled"
	DataStateFailed     DataState = "failed"
)

func (o DataState) String() string {
	return string(o)
}

func (o DataState) ToPb() annofeedv1.Data_State_Enum {
	v := annofeedv1.Data_State_Enum_value[o.String()]
	return annofeedv1.Data_State_Enum(v)
}

type (
	File    = annofeedv1.File
	Data    = annofeedv1.Data
	Element = annov1.Element

	Annofeed interface {
		annofeedv1.DatasClient
		annofeedv1.FilesClient
	}
)

func NewAnnofeed(conn *grpc.ClientConn) Annofeed {
	return struct {
		annofeedv1.DatasClient
		annofeedv1.FilesClient
	}{
		DatasClient: annofeedv1.NewDatasClient(conn),
		FilesClient: annofeedv1.NewFilesClient(conn),
	}
}

var feedsvc Annofeed

func InitAnnofeed(service *Service) {
	feedsvc = NewAnnofeed(newGrpcConn(service))
}

func SetAnnofeedSvc(annofeed Annofeed) {
	feedsvc = annofeed
}

func CreateFile(ctx context.Context, file *annofeedv1.CreateFileRequest) (*annofeedv1.CreateFileReply, error) {
	f, err := feedsvc.CreateFile(ctx, file)
	if err != nil {
		return nil, errors.FromError(err)
	}
	return f, nil
}

func FinishFileUpload(ctx context.Context, uid string, etags ...string) error {
	_, err := feedsvc.FinishFileUpload(ctx, &annofeedv1.FinishFileUploadRequest{Uid: uid, Etags: etags})
	if err != nil {
		return errors.FromError(err)
	}
	return nil
}

func ShareFile(ctx context.Context, uid string) (string, error) {
	rsp, err := feedsvc.ShareFile(ctx, &annofeedv1.ShareFileRequest{Uid: uid, Timeout: -1})
	if err != nil {
		return "", errors.FromError(err)
	}
	return rsp.Url, nil
}

func CreateData(ctx context.Context, data *annofeedv1.CreateDataRequest) (*Data, error) {
	d, err := feedsvc.CreateData(ctx, data)
	if err != nil {
		return nil, errors.FromError(err)
	}
	return d, nil
}

func GetData(ctx context.Context, uid string) (*Data, error) {
	d, err := feedsvc.GetData(ctx, &annofeedv1.GetDataRequest{Uid: uid})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return d, nil
}

func GetDataElements(ctx context.Context, uid string, startIdx, count int) ([]*Element, error) {
	resp, err := feedsvc.GetDataElements(ctx, &annofeedv1.GetDataElementsRequest{
		Uid: uid, StartIdx: int32(startIdx), Count: int32(count)})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return resp.Elements, nil
}

func FindDataElements(ctx context.Context, uid string, startIdx, count int, namePattern string) ([]*Element, error) {
	resp, err := feedsvc.FindDataElements(ctx, &annofeedv1.FindDataElementsRequest{
		Uid: uid, StartIdx: int32(startIdx), Count: int32(count), NamePattern: namePattern})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return resp.Elements, nil
}

func SetRawdataEmbedding(ctx context.Context, dataUid, rawdataName, embeddingURI string) (string, error) {
	rsp, err := feedsvc.SetRawdataEmbedding(ctx, &annofeedv1.SetRawdataEmbeddingRequest{
		Uid:          dataUid,
		RawdataName:  rawdataName,
		EmbeddingUri: embeddingURI})
	if err != nil {
		return "", errors.FromError(err)
	}
	return rsp.EmbeddingUrl, nil
}

func GetDataValidationSummary(ctx context.Context, dataUid string) (*annov1.DataValidationSummary, error) {
	summary, err := feedsvc.GetDataValidationSummary(ctx, &annofeedv1.GetDataRequest{Uid: dataUid})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return summary, nil
}

func GetDataMeta(ctx context.Context, req *annofeedv1.GetDataMetaRequest) (*annofeedv1.GetDataMetaReply, error) {
	metafiles, err := feedsvc.GetDataMeta(ctx, req)
	if err != nil {
		return nil, errors.FromError(err)
	}
	return metafiles, nil
}
