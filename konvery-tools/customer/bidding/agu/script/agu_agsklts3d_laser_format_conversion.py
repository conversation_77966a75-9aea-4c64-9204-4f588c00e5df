import os
import shutil
import zipfile
import json
import sys

sys.path.append('.')
from customer.common import tools

mac_file = '.DS_Store'


def run(input_dir):
    try:
        file_counter = 0
        # unzip file
        root_dir = os.path.join(os.path.dirname(input_dir), 'unzip')
        if os.path.exists(root_dir):
            shutil.rmtree(root_dir)
        with zipfile.ZipFile(input_dir, 'r') as zip_ref:
            zip_ref.extractall(root_dir)

        # create result_folder
        result_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_result')
        try:
            os.mkdir(result_dir)
        except FileExistsError:
            shutil.rmtree(result_dir)
            os.mkdir(result_dir)

        for ds in os.listdir(root_dir):
            if ds != mac_file:
                # ds_waxy3fpnuai8dd3dkxdy
                ds_dir = os.path.join(root_dir, ds)
                for num_folder in os.listdir(ds_dir):
                    if num_folder != mac_file:
                        # 2309306512
                        num_folder_dir = os.path.join(ds_dir, num_folder)
                        for result_folder in os.listdir(num_folder_dir):
                            if result_folder != mac_file:
                                # cloud_result
                                result_folder_dir = os.path.join(num_folder_dir, result_folder)
                                for segment in os.listdir(result_folder_dir):
                                    if segment != mac_file:
                                        # segment36
                                        segment_dir = os.path.join(result_folder_dir, segment)
                                        for label in os.listdir(segment_dir):
                                            if label != mac_file:
                                                # label
                                                label_dir = os.path.join(segment_dir, label)
                                                for json_file in os.listdir(label_dir):
                                                    if json_file != mac_file:
                                                        # 6382011.345276.json
                                                        json_dir = os.path.join(label_dir, json_file)
                                                        json_data = json.load(open(json_dir))

                                                        if 'lidar' in json_data:
                                                            # build result list
                                                            result_list = []
                                                            for lidar in json_data['lidar']:
                                                                # build result string
                                                                result_string = (str(lidar['annotation']['data']['position']['x'])
                                                                                 + ' ' + str(lidar['annotation']['data']['position']['y'])
                                                                                 + ' ' + str(lidar['annotation']['data']['position']['z'])
                                                                                 + ' ' + "{:.2f}".format(lidar['annotation']['data']['dimension']['l'])
                                                                                 + ' ' + "{:.2f}".format(lidar['annotation']['data']['dimension']['w'])
                                                                                 + ' ' + "{:.2f}".format(lidar['annotation']['data']['dimension']['h'])
                                                                                 + ' ' + str(lidar['annotation']['data']['rotation']['z'])
                                                                                 + ' ' + lidar['class'])

                                                                result_list.append(result_string)

                                                                with open(result_dir + '/' + json_file.split('.json')[0] + '.txt', 'w') as f:
                                                                    for result in result_list:
                                                                        f.write(f"{result}\n")
                                                            file_counter += 1
                                                        else:
                                                            print(json_dir)

        # compress the result folder
        shutil.make_archive(result_dir,
                            format='zip',
                            root_dir=os.path.dirname(result_dir),
                            base_dir=input_dir.split('/')[-1].split('.')[0] + '_result')

        trans_result = {
            "code": 0,
            "output": result_dir,
            "err_msg": '成功',
            "summary": {
                '转换文件数': file_counter,
            }
        }

    except Exception as e:
        trans_result = {
            "code": 1,
            "err_msg": e,
        }

    for key in trans_result:
        print(key + ': ' + str(trans_result[key]))

    print(trans_result)
    return trans_result


if __name__ == "__main__":
    args = tools.get_args()
    # args.input = '/Users/<USER>/Downloads/AGU-中秋国庆大礼包_230930a6bf0_验收_已通过_JSON标注结果文件_20231001_2746.zip'
    run(args.input)
