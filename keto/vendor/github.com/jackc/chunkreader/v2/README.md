[![](https://godoc.org/github.com/jackc/chunkreader?status.svg)](https://godoc.org/github.com/jackc/chunkreader)
[![Build Status](https://travis-ci.org/jackc/chunkreader.svg)](https://travis-ci.org/jackc/chunkreader)

# chunkreader

Package chunkreader provides an io.Reader wrapper that minimizes IO reads and memory allocations.

Extracted from original implementation in https://github.com/jackc/pgx.
