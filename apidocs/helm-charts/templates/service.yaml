apiVersion: v1
kind: Service
metadata:
  name: {{ include "appx.fullname" . }}
  labels:
    {{- include "appx.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
    - port: {{ .Values.service.grpcPort }}
      targetPort: grpc
      protocol: TCP
      name: grpc
  selector:
    {{- include "appx.selectorLabels" . | nindent 4 }}
