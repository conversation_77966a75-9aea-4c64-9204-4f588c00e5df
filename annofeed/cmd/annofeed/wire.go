//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"annofeed/internal/biz"
	"annofeed/internal/conf"
	"annofeed/internal/data"
	"annofeed/internal/server"
	"annofeed/internal/service"
	"annofeed/workflow"

	// "github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init kratos application.
func wireApp(string, *conf.Server, *conf.Data, *conf.FileServer, log.Logger) (*App, func(), error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, workflow.ProviderSet, newApp))
}
