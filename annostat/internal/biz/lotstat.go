package biz

import (
	"time"

	"annostat/internal/data/serial"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
)

type JobstatCuboid struct {
	InsCnt int        `json:"ins_cnt"`
	Scales [3]float64 `json:"scales"`
}

func (o JobstatCuboid) Add(c JobstatCuboid) JobstatCuboid {
	o.InsCnt += c.InsCnt
	for i, v := range c.Scales {
		o.Scales[i] += v
	}
	return o
}

func (o JobstatCuboid) Minus(c JobstatCuboid) JobstatCuboid {
	o.InsCnt -= c.InsCnt
	for i, v := range c.Scales {
		o.Scales[i] -= v
	}
	return o
}

type JobstatCuboidsW = serial.Map[string, JobstatCuboid]

type Jobstat struct {
	ID        int64           `json:"id" gorm:"default:null"`
	LotID     int64           `json:"lot_id" gorm:"default:null"`
	JobID     int64           `json:"job_id" gorm:"default:null"`
	Cuboids   JobstatCuboidsW `json:"cuboids" gorm:"default:null"`
	UpdatedAt time.Time       `json:"updated_at" gorm:"default:null"`
	CreatedAt time.Time       `json:"created_at" gorm:"default:null"`
}

func (o *Jobstat) GetID() int64 { return o.ID }

const JobstatTableName = "jobstats"

var (
	jobstat_ = field.RegObject(&Jobstat{})

	JobstatSfldLotID   = field.Sname(&jobstat_.LotID)
	JobstatSfldJobID   = field.Sname(&jobstat_.JobID)
	JobstatSfldCuboids = field.Sname(&jobstat_.Cuboids)
)

type JobstatsRepo repo.GenericRepo[Jobstat]

type JobstatListFilter struct {
	LotID int64
	JobID int64
}

func (o *JobstatListFilter) Apply(tx Tx) Tx {
	if o == nil {
		return tx
	}
	tbl := "jobstats."
	tx = ApplyFieldFilter(tx, tbl+JobstatSfldLotID, o.LotID)
	tx = ApplyFieldFilter(tx, tbl+JobstatSfldJobID, o.JobID)
	return tx
}
