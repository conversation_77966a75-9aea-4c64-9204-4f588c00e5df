package bosch

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/kfs"
	"gonum.org/v1/gonum/mat"
	"gopkg.in/yaml.v3"
)

type CamParam struct {
	// // lidar to vechicle: 4x4
	// T_v_l []float64

	// vehicle to camera: 4x4
	T_v2c []float64
	// fx,fy,cx,cy
	// Intrinsics []float64 `json:"intrinics"`
	// // 4
	// Distortion []float64 `json:"distortion"`

	DistortionType    anno.RawdataParam_DistortionType_Enum
	AffineParameters  []float64
	InvPolyParameters []float64
	ProjectParameters []float64
}

type Params struct {
	Cameras map[string]*CamParam
	Lidars  map[string][]float64 // lidar to vechicle: 4x4
}

func (o *Params) HasCamera(cam string) bool {
	return o.Cameras[cam] != nil
}

func (o *Params) GetImageTransforms(cam, lidar string) []*anno.RawdataParam {
	r := make([]*anno.RawdataParam, 0, 3)
	r = append(r, &anno.RawdataParam{
		Type:      anno.RawdataParam_Type_matrix,
		ColumnCnt: 4,
		Data:      o.Lidars[lidar],
	})
	pcam := o.Cameras[cam]
	r = append(r, &anno.RawdataParam{
		Type:      anno.RawdataParam_Type_matrix,
		ColumnCnt: 4,
		Data:      pcam.T_v2c,
	})
	r = append(r, &anno.RawdataParam{
		Type:      anno.RawdataParam_Type_distortion,
		ColumnCnt: 1,
		Data: []float64{
			float64(pcam.DistortionType),
		},
	})
	switch pcam.DistortionType {
	case anno.RawdataParam_DistortionType_ocam:
		r[len(r)-1].Data = append(r[len(r)-1].Data, pcam.AffineParameters...)
		r[len(r)-1].Data = append(r[len(r)-1].Data, float64(len(pcam.InvPolyParameters)))
		r[len(r)-1].Data = append(r[len(r)-1].Data, pcam.InvPolyParameters...)
	case anno.RawdataParam_DistortionType_kb:
		r[len(r)-1].Data = append(r[len(r)-1].Data, pcam.ProjectParameters[:4]...)
		r = append(r, &anno.RawdataParam{
			Type:      anno.RawdataParam_Type_intrinsic,
			ColumnCnt: 1,
			Data:      pcam.ProjectParameters[4:],
		})
	}
	return r
}

type OrigCam struct {
	ModelType        string `yaml:"model_type"`
	ProjectionParams struct {
		K2 float64 `yaml:"k2"`
		K3 float64 `yaml:"k3"`
		K4 float64 `yaml:"k4"`
		K5 float64 `yaml:"k5"`

		// mu(fx)，mv(fy),u0(cx),v0(cy)
		Mu float64 `yaml:"mu"`
		Mv float64 `yaml:"mv"`
		U0 float64 `yaml:"u0"`
		V0 float64 `yaml:"v0"`
	} `yaml:"projection_parameters"`

	InvPolyParameters struct {
		P0  float64 `yaml:"p0"`
		P1  float64 `yaml:"p1"`
		P2  float64 `yaml:"p2"`
		P3  float64 `yaml:"p3"`
		P4  float64 `yaml:"p4"`
		P5  float64 `yaml:"p5"`
		P6  float64 `yaml:"p6"`
		P7  float64 `yaml:"p7"`
		P8  float64 `yaml:"p8"`
		P9  float64 `yaml:"p9"`
		P10 float64 `yaml:"p10"`
		P11 float64 `yaml:"p11"`
		P12 float64 `yaml:"p12"`
		P13 float64 `yaml:"p13"`
		P14 float64 `yaml:"p14"`
		P15 float64 `yaml:"p15"`
		P16 float64 `yaml:"p16"`
		P17 float64 `yaml:"p17"`
		P18 float64 `yaml:"p18"`
		P19 float64 `yaml:"p19"`
	} `yaml:"inv_poly_parameters"`

	AffineParameters struct {
		Ac float64 `yaml:"ac"`
		Ad float64 `yaml:"ad"`
		Ae float64 `yaml:"ae"`
		Cx float64 `yaml:"cx"`
		Cy float64 `yaml:"cy"`
	} `yaml:"affine_parameters"`

	// Camera to vehicle: 4x4
	TVC Matrix `yaml:"T_v_c"`
}

func (p *OrigCam) getDistortionType() anno.RawdataParam_DistortionType_Enum {
	switch p.ModelType {
	case "SCARAMUZZA":
		return anno.RawdataParam_DistortionType_ocam
	case "KANNALA_BRANDT":
		return anno.RawdataParam_DistortionType_kb
	}
	return anno.RawdataParam_DistortionType_pinhole
}

func (p *OrigCam) getAffineParameters() []float64 {
	return []float64{
		p.AffineParameters.Ac,
		p.AffineParameters.Ad,
		p.AffineParameters.Ae,
		p.AffineParameters.Cx,
		p.AffineParameters.Cy,
	}
}

func (p *OrigCam) getInvPolyParameters() []float64 {
	data := []float64{
		p.InvPolyParameters.P0,
		p.InvPolyParameters.P1,
		p.InvPolyParameters.P2,
		p.InvPolyParameters.P3,
		p.InvPolyParameters.P4,
		p.InvPolyParameters.P5,
		p.InvPolyParameters.P6,
		p.InvPolyParameters.P7,
		p.InvPolyParameters.P8,
		p.InvPolyParameters.P9,
		p.InvPolyParameters.P10,
		p.InvPolyParameters.P11,
		p.InvPolyParameters.P12,
		p.InvPolyParameters.P13,
		p.InvPolyParameters.P14,
		p.InvPolyParameters.P15,
		p.InvPolyParameters.P16,
		p.InvPolyParameters.P17,
		p.InvPolyParameters.P18,
		p.InvPolyParameters.P19,
	}

	i := len(data) - 1
	for ; i >= 0; i-- {
		if data[i] != 0 {
			break
		}
	}

	return data[:i+1]
}

func (p *OrigCam) getProjectParameters() []float64 {
	data := []float64{
		p.ProjectionParams.K2,
		p.ProjectionParams.K3,
		p.ProjectionParams.K4,
		p.ProjectionParams.K5,
		p.ProjectionParams.Mu,
		p.ProjectionParams.Mv,
		p.ProjectionParams.U0,
		p.ProjectionParams.V0,
	}

	return data
}

type Matrix struct {
	Rows int       `yaml:"rows"`
	Cols int       `yaml:"cols"`
	Dt   string    `yaml:"dt"`
	Data []float64 `yaml:"data"`
}

func (o *Matrix) IsEmpty() bool {
	return o.Rows == 0 && o.Cols == 0
}

type OrigLidar struct {
	// Lidar to vehicle: 4x4
	TVL   Matrix
	TVL0  Matrix `yaml:"T_v_l0"`
	TVL1  Matrix `yaml:"T_v_l1"`
	TVL11 Matrix `yaml:"T_v_l11"`
	TVL12 Matrix `yaml:"T_v_l12"`
	TVL13 Matrix `yaml:"T_v_l13"`
	TVL14 Matrix `yaml:"T_v_l14"`
}

func (o *OrigLidar) Transform() {
	if !o.TVL0.IsEmpty() {
		o.TVL = o.TVL0
		o.TVL0 = Matrix{}
		return
	}
	if !o.TVL1.IsEmpty() {
		o.TVL = o.TVL1
		o.TVL1 = Matrix{}
		return
	}
	if !o.TVL11.IsEmpty() {
		o.TVL = o.TVL11
		o.TVL11 = Matrix{}
		return
	}
	if !o.TVL12.IsEmpty() {
		o.TVL = o.TVL12
		o.TVL12 = Matrix{}
		return
	}
	if !o.TVL13.IsEmpty() {
		o.TVL = o.TVL13
		o.TVL13 = Matrix{}
		return
	}
	if !o.TVL14.IsEmpty() {
		o.TVL = o.TVL14
		o.TVL14 = Matrix{}
		return
	}
}

func ParseYaml(fpath string, out any) error {
	data, err := os.ReadFile(fpath)
	if err != nil {
		return fmt.Errorf("failed to read file %v: %w", fpath, err)
	}
	if len(data) > 0 && data[0] == '%' {
		data = append([]byte{'#'}, data...)
	}
	err = yaml.Unmarshal(data, out)
	if err != nil {
		return fmt.Errorf("failed to parse file %v: %w", fpath, err)
	}
	return nil
}

func ParseParams(dir string) (*Params, error) {
	entries, err := os.ReadDir(dir)
	if err != nil {
		return nil, fmt.Errorf("failed to read dir %v: %w", dir, err)
	}
	params := &Params{
		Cameras: make(map[string]*CamParam),
		Lidars:  make(map[string][]float64),
	}
	for _, e := range entries {
		switch {
		case strings.Contains(strings.ToLower(e.Name()), "lidar"):
			p := &OrigLidar{}
			err = ParseYaml(filepath.Join(dir, e.Name()), p)
			if err != nil {
				return nil, err
			}
			p.Transform()
			params.Lidars[kfs.FileBareName(e.Name())] = p.TVL.Data
		case strings.Contains(strings.ToLower(e.Name()), "cam"):
			p := &OrigCam{}
			err = ParseYaml(filepath.Join(dir, e.Name()), p)
			if err != nil {
				return nil, err
			}
			v2c, err := InverseMatrix4x4(p.TVC.Data)
			if err != nil {
				return nil, err
			}
			params.Cameras[kfs.FileBareName(e.Name())] = &CamParam{
				T_v2c:             v2c,
				DistortionType:    p.getDistortionType(),
				AffineParameters:  p.getAffineParameters(),
				InvPolyParameters: p.getInvPolyParameters(),
				ProjectParameters: p.getProjectParameters(),
			}
		}
	}
	return params, nil
}

// InverseMatrix4x4 calculates the inverse of a 4x4 matrix.
func InverseMatrix4x4(m4x4 []float64) ([]float64, error) {
	src := mat.NewDense(4, 4, m4x4)
	var dst = &mat.Dense{}
	if err := dst.Inverse(src); err != nil {
		return nil, fmt.Errorf("failed to inverse matrix: %w", err)
	}
	return dst.RawMatrix().Data, nil
}
