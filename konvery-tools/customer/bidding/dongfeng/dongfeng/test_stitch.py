import os
import numpy as np
import open3d as o3d
from glob import glob
from generate_pos_mat import read_best_position, get_transformation_matrix

root = "/Users/<USER>/work/data/Zhonghaiting/raw/中海庭4D试标数据/4d_test/yunce_lh_1_20231121_chunsun_part1/"


if __name__ == '__main__':

    pcd_dir = root + '2045_bus_16_NULL_NULL_NULL_2023-11-21-14-49-55_23/pcd'

    bestposition_dir = root + '2045_bus_16_NULL_NULL_NULL_2023-11-21-14-49-55_23/bestposition'

    origin_translation = np.array([3370356.95, 8308.92, 28.3660283023])

    pcd_files = glob(os.path.join(pcd_dir, '*.pcd'))
    bestposition_files = glob(os.path.join(bestposition_dir, '*.yaml'))

    N = 10

    merged_cloud = np.empty((0, 3))
    for pcd, bestposition in zip(pcd_files[:N], bestposition_files[:N]):
        # 读取pcd文件中的点云
        pcd_data = o3d.io.read_point_cloud(pcd)

        guassian_info = read_best_position(bestposition)

        # 获取旋转矩阵和平移向量
        transform_matrix = get_transformation_matrix(guassian_info, origin_translation)
        rotation = transform_matrix[:3, :3]
        translation = transform_matrix[:3, 3]

        # 将点云进行旋转和平移变换
        rotated_cloud = np.matmul(pcd_data.points, rotation.T) + translation

        # 合并点云
        merged_cloud = np.vstack((merged_cloud, rotated_cloud))

    # 保存pcd
    merged_pcd = o3d.geometry.PointCloud()
    merged_pcd.points = o3d.utility.Vector3dVector(merged_cloud)
    o3d.io.write_point_cloud('merged.pcd', merged_pcd)

    # 可视化
    o3d.visualization.draw_geometries([merged_pcd])
