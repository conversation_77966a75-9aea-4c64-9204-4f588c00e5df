// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anyconn/v1/secret.proto

package anyconn

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateSecretRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateSecretRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSecretRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSecretRequestMultiError, or nil if none found.
func (m *CreateSecretRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSecretRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if !_CreateSecretRequest_Name_Pattern.MatchString(m.GetName()) {
		err := CreateSecretRequestValidationError{
			field:  "Name",
			reason: "value does not match regex pattern \"^[\\\\p{Han}\\\\w\\\\d-]{0,20}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for OrgUid

	if len(errors) > 0 {
		return CreateSecretRequestMultiError(errors)
	}

	return nil
}

// CreateSecretRequestMultiError is an error wrapping multiple validation
// errors returned by CreateSecretRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateSecretRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSecretRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSecretRequestMultiError) AllErrors() []error { return m }

// CreateSecretRequestValidationError is the validation error returned by
// CreateSecretRequest.Validate if the designated constraints aren't met.
type CreateSecretRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSecretRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSecretRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSecretRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSecretRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSecretRequestValidationError) ErrorName() string {
	return "CreateSecretRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateSecretRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSecretRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSecretRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSecretRequestValidationError{}

var _CreateSecretRequest_Name_Pattern = regexp.MustCompile("^[\\p{Han}\\w\\d-]{0,20}$")

// Validate checks the field values on UpdateSecretRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateSecretRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSecretRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateSecretRequestMultiError, or nil if none found.
func (m *UpdateSecretRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSecretRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSecret()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSecretRequestValidationError{
					field:  "Secret",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSecretRequestValidationError{
					field:  "Secret",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSecret()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSecretRequestValidationError{
				field:  "Secret",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateSecretRequestMultiError(errors)
	}

	return nil
}

// UpdateSecretRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateSecretRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateSecretRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSecretRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSecretRequestMultiError) AllErrors() []error { return m }

// UpdateSecretRequestValidationError is the validation error returned by
// UpdateSecretRequest.Validate if the designated constraints aren't met.
type UpdateSecretRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSecretRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSecretRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSecretRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSecretRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSecretRequestValidationError) ErrorName() string {
	return "UpdateSecretRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateSecretRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSecretRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSecretRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSecretRequestValidationError{}

// Validate checks the field values on DeleteSecretRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteSecretRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteSecretRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteSecretRequestMultiError, or nil if none found.
func (m *DeleteSecretRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteSecretRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return DeleteSecretRequestMultiError(errors)
	}

	return nil
}

// DeleteSecretRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteSecretRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteSecretRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteSecretRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteSecretRequestMultiError) AllErrors() []error { return m }

// DeleteSecretRequestValidationError is the validation error returned by
// DeleteSecretRequest.Validate if the designated constraints aren't met.
type DeleteSecretRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteSecretRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteSecretRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteSecretRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteSecretRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteSecretRequestValidationError) ErrorName() string {
	return "DeleteSecretRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteSecretRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteSecretRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteSecretRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteSecretRequestValidationError{}

// Validate checks the field values on GetSecretRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetSecretRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecretRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSecretRequestMultiError, or nil if none found.
func (m *GetSecretRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecretRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return GetSecretRequestMultiError(errors)
	}

	return nil
}

// GetSecretRequestMultiError is an error wrapping multiple validation errors
// returned by GetSecretRequest.ValidateAll() if the designated constraints
// aren't met.
type GetSecretRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecretRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecretRequestMultiError) AllErrors() []error { return m }

// GetSecretRequestValidationError is the validation error returned by
// GetSecretRequest.Validate if the designated constraints aren't met.
type GetSecretRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecretRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecretRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecretRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecretRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecretRequestValidationError) ErrorName() string { return "GetSecretRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetSecretRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecretRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecretRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecretRequestValidationError{}

// Validate checks the field values on ListSecretRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListSecretRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSecretRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSecretRequestMultiError, or nil if none found.
func (m *ListSecretRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSecretRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for Pagesz

	// no validation rules for OrgUid

	// no validation rules for CreatorUid

	// no validation rules for NamePattern

	// no validation rules for WithOrg

	if len(errors) > 0 {
		return ListSecretRequestMultiError(errors)
	}

	return nil
}

// ListSecretRequestMultiError is an error wrapping multiple validation errors
// returned by ListSecretRequest.ValidateAll() if the designated constraints
// aren't met.
type ListSecretRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSecretRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSecretRequestMultiError) AllErrors() []error { return m }

// ListSecretRequestValidationError is the validation error returned by
// ListSecretRequest.Validate if the designated constraints aren't met.
type ListSecretRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSecretRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSecretRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSecretRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSecretRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSecretRequestValidationError) ErrorName() string {
	return "ListSecretRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListSecretRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSecretRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSecretRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSecretRequestValidationError{}

// Validate checks the field values on ListSecretReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListSecretReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSecretReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSecretReplyMultiError, or nil if none found.
func (m *ListSecretReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSecretReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetSecrets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListSecretReplyValidationError{
						field:  fmt.Sprintf("Secrets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListSecretReplyValidationError{
						field:  fmt.Sprintf("Secrets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListSecretReplyValidationError{
					field:  fmt.Sprintf("Secrets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListSecretReplyMultiError(errors)
	}

	return nil
}

// ListSecretReplyMultiError is an error wrapping multiple validation errors
// returned by ListSecretReply.ValidateAll() if the designated constraints
// aren't met.
type ListSecretReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSecretReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSecretReplyMultiError) AllErrors() []error { return m }

// ListSecretReplyValidationError is the validation error returned by
// ListSecretReply.Validate if the designated constraints aren't met.
type ListSecretReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSecretReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSecretReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSecretReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSecretReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSecretReplyValidationError) ErrorName() string { return "ListSecretReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListSecretReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSecretReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSecretReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSecretReplyValidationError{}

// Validate checks the field values on Secret with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Secret) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Secret with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SecretMultiError, or nil if none found.
func (m *Secret) ValidateAll() error {
	return m.validate(true)
}

func (m *Secret) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for OrgUid

	// no validation rules for DataUid

	// no validation rules for CreatorUid

	// no validation rules for Size

	// no validation rules for State

	// no validation rules for InsTotal

	// no validation rules for AnnoResultUrl

	// no validation rules for Error

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SecretMultiError(errors)
	}

	return nil
}

// SecretMultiError is an error wrapping multiple validation errors returned by
// Secret.ValidateAll() if the designated constraints aren't met.
type SecretMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecretMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecretMultiError) AllErrors() []error { return m }

// SecretValidationError is the validation error returned by Secret.Validate if
// the designated constraints aren't met.
type SecretValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecretValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecretValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecretValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecretValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecretValidationError) ErrorName() string { return "SecretValidationError" }

// Error satisfies the builtin error interface
func (e SecretValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecret.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecretValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecretValidationError{}

// Validate checks the field values on Secret_State with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Secret_State) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Secret_State with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Secret_StateMultiError, or
// nil if none found.
func (m *Secret_State) ValidateAll() error {
	return m.validate(true)
}

func (m *Secret_State) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Secret_StateMultiError(errors)
	}

	return nil
}

// Secret_StateMultiError is an error wrapping multiple validation errors
// returned by Secret_State.ValidateAll() if the designated constraints aren't met.
type Secret_StateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Secret_StateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Secret_StateMultiError) AllErrors() []error { return m }

// Secret_StateValidationError is the validation error returned by
// Secret_State.Validate if the designated constraints aren't met.
type Secret_StateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Secret_StateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Secret_StateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Secret_StateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Secret_StateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Secret_StateValidationError) ErrorName() string { return "Secret_StateValidationError" }

// Error satisfies the builtin error interface
func (e Secret_StateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecret_State.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Secret_StateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Secret_StateValidationError{}
