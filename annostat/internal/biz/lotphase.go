package biz

import (
	"time"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
)

const (
	PhaseTypeLabel  = "label"
	PhaseTypeReview = "review"
)

type Lotphase struct {
	ID            int64     `json:"id" gorm:"default:null"`
	LotID         int64     `json:"lot_id" gorm:"default:null"`
	Number        int32     `json:"number" gorm:"default:null"` // starts from 1
	Name          string    `json:"name" gorm:"default:null"`
	Type          string    `json:"type" gorm:"default:null"` // label/review/...
	Editable      bool      `json:"editable" gorm:"default:null"`
	SamplePercent float32   `json:"sample_percent" gorm:"default:null"`
	MinSkillLevel int32     `json:"min_skill_level" gorm:"default:null"`
	Timeout       int32     `json:"timeout" gorm:"default:null"`
	Execteam      string    `json:"execteam" gorm:"default:null"` // the team assigned to execute the phase
	UpdatedAt     time.Time `json:"updated_at" gorm:"default:null"`
	CreatedAt     time.Time `json:"created_at" gorm:"default:null"`
}

func (o *Lotphase) GetID() int64 { return o.ID }

const LotphaseTableName = "lotphases"

var (
	lotphase_ = field.RegObject(&Lotphase{})
	// LotphaseUpdatableFlds = field.NewModel(lotphase_, "Name", "Type", "Editable", "SamplePercent",
	// 	"MinSkillLevel", "Timeout", "Merge", "Execteam")

	LotphaseSfldLotID         = field.Sname(&lotphase_.LotID)
	LotphaseSfldNumber        = field.Sname(&lotphase_.Number)
	LotphaseSfldName          = field.Sname(&lotphase_.Name)
	LotphaseSfldType          = field.Sname(&lotphase_.Type)
	LotphaseSfldEditable      = field.Sname(&lotphase_.Editable)
	LotphaseSfldSamplePercent = field.Sname(&lotphase_.SamplePercent)
	LotphaseSfldMinSkillLevel = field.Sname(&lotphase_.MinSkillLevel)
	LotphaseSfldTimeout       = field.Sname(&lotphase_.Timeout)
	LotphaseSfldExecteam      = field.Sname(&lotphase_.Execteam)
)

type LotphasesRepo repo.GenericRepo[Lotphase]

type LotphaseListFilter struct {
	LotIDs    []int64
	Execteams []string
}

func (o *LotphaseListFilter) Apply(tx Tx) Tx {
	if o == nil {
		return tx
	}
	tbl := "lotphases."
	tx = ApplyFieldFilter(tx, tbl+LotphaseSfldLotID, o.LotIDs)
	tx = ApplyFieldFilter(tx, tbl+LotphaseSfldExecteam, o.Execteams)
	return tx
}
