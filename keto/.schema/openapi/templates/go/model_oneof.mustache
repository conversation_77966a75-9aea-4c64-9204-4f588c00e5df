// {{classname}} - {{#description}}{{{description}}}{{/description}}{{^description}}struct for {{{classname}}}{{/description}}
type {{classname}} struct {
	{{#oneOf}}
	{{#lambda.titlecase}}{{{.}}}{{/lambda.titlecase}} *{{{.}}}
	{{/oneOf}}
}

{{#oneOf}}
// {{{.}}}As{{classname}} is a convenience function that returns {{{.}}} wrapped in {{classname}}
func {{#lambda.titlecase}}{{{.}}}{{/lambda.titlecase}}As{{classname}}(v *{{{.}}}) {{classname}} {
	return {{classname}}{
		{{#lambda.titlecase}}{{{.}}}{{/lambda.titlecase}}: v,
	}
}

{{/oneOf}}

// Unmarshal JSON data into one of the pointers in the struct
func (dst *{{classname}}) UnmarshalJSON(data []byte) error {
	var err error
	{{#isNullable}}
	// this object is nullable so check if the payload is null or empty string
	if string(data) == "" || string(data) == "{}" {
		return nil
	}

	{{/isNullable}}
	{{#useOneOfDiscriminatorLookup}}
	{{#discriminator}}
	{{#mappedModels}}
	{{#-first}}
	// use discriminator value to speed up the lookup
	var jsonDict map[string]interface{}
	err = newStrictDecoder(data).Decode(&jsonDict)
	if err != nil {
		return fmt.Errorf("Failed to unmarshal JSON into map for the discrimintor lookup.")
	}

	{{/-first}}
	// check if the discriminator value is '{{{mappingName}}}'
	if jsonDict["{{{propertyBaseName}}}"] == "{{{mappingName}}}" {
		// try to unmarshal JSON data into {{{modelName}}}
		err = json.Unmarshal(data, &dst.{{{modelName}}})
		if err == nil {
			return nil // data stored in dst.{{{modelName}}}, return on the first match
		} else {
			dst.{{{modelName}}} = nil
			return fmt.Errorf("Failed to unmarshal {{classname}} as {{{modelName}}}: %s", err.Error())
		}
	}

	{{/mappedModels}}
	{{/discriminator}}
	return nil
	{{/useOneOfDiscriminatorLookup}}
	{{^useOneOfDiscriminatorLookup}}
	match := 0
	{{#oneOf}}
	// try to unmarshal data into {{#lambda.titlecase}}{{{.}}}{{/lambda.titlecase}}
	err = newStrictDecoder(data).Decode(&dst.{{#lambda.titlecase}}{{{.}}}{{/lambda.titlecase}})
	if err == nil {
		json{{#lambda.titlecase}}{{{.}}}{{/lambda.titlecase}}, _ := json.Marshal(dst.{{#lambda.titlecase}}{{{.}}}{{/lambda.titlecase}})
		if string(json{{#lambda.titlecase}}{{{.}}}{{/lambda.titlecase}}) == "{}" { // empty struct
			dst.{{#lambda.titlecase}}{{{.}}}{{/lambda.titlecase}} = nil
		} else {
			match++
		}
	} else {
		dst.{{#lambda.titlecase}}{{{.}}}{{/lambda.titlecase}} = nil
	}

	{{/oneOf}}
	if match > 1 { // more than 1 match
		// reset to nil
		{{#oneOf}}
		dst.{{#lambda.titlecase}}{{{.}}}{{/lambda.titlecase}} = nil
		{{/oneOf}}

		return fmt.Errorf("Data matches more than one schema in oneOf({{classname}})")
	} else if match == 1 {
		return nil // exactly one match
	} else { // no match
		return fmt.Errorf("Data failed to match schemas in oneOf({{classname}})")
	}
	{{/useOneOfDiscriminatorLookup}}
}

// Marshal data from the first non-nil pointers in the struct to JSON
func (src {{classname}}) MarshalJSON() ([]byte, error) {
{{#oneOf}}
	if src.{{#lambda.titlecase}}{{{.}}}{{/lambda.titlecase}} != nil {
		return json.Marshal(&src.{{#lambda.titlecase}}{{{.}}}{{/lambda.titlecase}})
	}

{{/oneOf}}
	return nil, nil // no data in oneOf schemas
}

// Get the actual instance
func (obj *{{classname}}) GetActualInstance() (interface{}) {
	if obj == nil {
		return nil
	}
{{#oneOf}}
	if obj.{{#lambda.titlecase}}{{{.}}}{{/lambda.titlecase}} != nil {
		return obj.{{#lambda.titlecase}}{{{.}}}{{/lambda.titlecase}}
	}

{{/oneOf}}
	// all schemas are nil
	return nil
}

{{>nullable_model}}
