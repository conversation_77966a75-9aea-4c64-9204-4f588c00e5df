# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
security:
    - jwt: []
paths:
    /annofeed/v1/datas:
        get:
            tags:
                - Datas
            operationId: Datas_ListData
            parameters:
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: org_uid
                  in: query
                  description: filter by organization
                  schema:
                    type: string
                - name: creator_uid
                  in: query
                  description: filter by creator
                  schema:
                    type: string
                - name: name_pattern
                  in: query
                  description: filter by name pattern
                  schema:
                    type: string
                - name: order_uid
                  in: query
                  description: filter by order
                  schema:
                    type: string
                - name: states
                  in: query
                  description: filter by data state
                  schema:
                    type: array
                    items:
                        enum:
                            - unspecified
                            - raw
                            - fetching
                            - processing
                            - ready
                            - abandoned
                            - disabled
                            - failed
                        type: string
                        format: enum
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annofeed.v1.ListDataReply'
        post:
            tags:
                - Datas
            operationId: Datas_CreateData
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/annofeed.v1.CreateDataRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annofeed.v1.Data'
    /annofeed/v1/datas/{data.uid}:
        patch:
            tags:
                - Datas
            operationId: Datas_UpdateData
            parameters:
                - name: data.uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: fields
                  in: query
                  description: name of fields to be updated
                  schema:
                    type: array
                    items:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/annofeed.v1.CreateDataRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annofeed.v1.Data'
    /annofeed/v1/datas/{uid}:
        get:
            tags:
                - Datas
            operationId: Datas_GetData
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: simple
                  in: query
                  description: 'if true, only return the simple info: name, desc, state, etc.'
                  schema:
                    type: boolean
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annofeed.v1.Data'
        delete:
            tags:
                - Datas
            operationId: Datas_DeleteData
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /annofeed/v1/datas/{uid}/elements:
        get:
            tags:
                - Datas
            description: Fetch a list of elements
            operationId: Datas_GetDataElements
            parameters:
                - name: uid
                  in: path
                  description: data UID
                  required: true
                  schema:
                    type: string
                - name: start_idx
                  in: query
                  description: element start index
                  schema:
                    type: integer
                    format: int32
                - name: count
                  in: query
                  description: element count
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annofeed.v1.GetDataElementsReply'
    /annofeed/v1/datas/{uid}/find-elements:
        get:
            tags:
                - Datas
            description: Find a list of elements (only names and indexes are returned)
            operationId: Datas_FindDataElements
            parameters:
                - name: uid
                  in: path
                  description: data UID
                  required: true
                  schema:
                    type: string
                - name: start_idx
                  in: query
                  description: element start index
                  schema:
                    type: integer
                    format: int32
                - name: count
                  in: query
                  description: element count
                  schema:
                    type: integer
                    format: int32
                - name: name_pattern
                  in: query
                  description: search elements by the pattern of their names
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annofeed.v1.GetDataElementsReply'
    /annofeed/v1/datas/{uid}/meta:
        get:
            tags:
                - Datas
            description: Get data meta
            operationId: Datas_GetDataMeta
            parameters:
                - name: uid
                  in: path
                  description: data UID
                  required: true
                  schema:
                    type: string
                - name: with_metafiles
                  in: query
                  description: whether to return metafiles in response
                  schema:
                    type: boolean
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annofeed.v1.GetDataMetaReply'
    /annofeed/v1/datas/{uid}/parse:
        put:
            tags:
                - Datas
            description: Parse data
            operationId: Datas_ParseData
            parameters:
                - name: uid
                  in: path
                  description: data UID
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/annofeed.v1.ParseDataRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /annofeed/v1/datas/{uid}/rawdata-embedding:
        put:
            tags:
                - Datas
            description: Set rawdata embedding.
            operationId: Datas_SetRawdataEmbedding
            parameters:
                - name: uid
                  in: path
                  description: data UID
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/annofeed.v1.SetRawdataEmbeddingRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annofeed.v1.SetRawdataEmbeddingReply'
    /annofeed/v1/dummy:
        get:
            tags:
                - Dummy
            operationId: Dummy_Dummy
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annofeed.v1.DummyReply'
    /annofeed/v1/files:
        get:
            tags:
                - Files
            operationId: Files_ListFile
            parameters:
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  schema:
                    type: string
                - name: org_uid
                  in: query
                  description: filter by organization
                  schema:
                    type: string
                - name: creator_uid
                  in: query
                  description: filter by creator
                  schema:
                    type: string
                - name: name_pattern
                  in: query
                  description: filter by name pattern
                  schema:
                    type: string
                - name: uris
                  in: query
                  description: filter by URI
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annofeed.v1.ListFileReply'
        post:
            tags:
                - Files
            description: create a file record and presigned URLs for upload
            operationId: Files_CreateFile
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/annofeed.v1.CreateFileRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annofeed.v1.CreateFileReply'
    /annofeed/v1/files/{file.uid}:
        patch:
            tags:
                - Files
            operationId: Files_UpdateFile
            parameters:
                - name: file.uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: fields
                  in: query
                  description: name of fields to update
                  schema:
                    type: array
                    items:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/annofeed.v1.File'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annofeed.v1.File'
    /annofeed/v1/files/{uid}:
        get:
            tags:
                - Files
            operationId: Files_GetFile
            parameters:
                - name: uid
                  in: path
                  description: file UID
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annofeed.v1.File'
        delete:
            tags:
                - Files
            description: delete a file record. Ongoing multipart upload will be aborted.
            operationId: Files_DeleteFile
            parameters:
                - name: uid
                  in: path
                  description: file UID
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /annofeed/v1/files/{uid}/finish-upload:
        put:
            tags:
                - Files
            description: mark the file upload as completed
            operationId: Files_FinishFileUpload
            parameters:
                - name: uid
                  in: path
                  description: file uid
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/annofeed.v1.FinishFileUploadRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /annofeed/v1/files/{uid}/share:
        put:
            tags:
                - Files
            description: get a presigned URL to download the file
            operationId: Files_ShareFile
            parameters:
                - name: uid
                  in: path
                  description: file uid
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/annofeed.v1.ShareFileRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annofeed.v1.ShareFileReply'
    /annofeed/v1/files/{uid}/upload-urls:
        get:
            tags:
                - Files
            operationId: Files_GetFileUploadURLs
            parameters:
                - name: uid
                  in: path
                  description: file uid
                  required: true
                  schema:
                    type: string
                - name: parts
                  in: query
                  description: set number of parts in a multi-part upload
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annofeed.v1.GetFileUploadURLsReply'
    /annofeed/v1/version:
        get:
            tags:
                - Configs
            operationId: Configs_GetVersion
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/annofeed.v1.GetVersionReply'
    /annofeed/v1/upload:
        post:
            tags:
                - Files
            operationId: Files_Upload
            parameters:
                - name: name
                  in: query
                  schema:
                    type: string
            requestBody:
                content:
                    application/octet-stream:
                        # any media type is accepted, functionally equivalent to '*/*'
                        schema:
                            # a binary file of any type
                            type: string
                            format: binary
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UploadFileReply'
    /annofeed/v1/avatar:
        post:
            tags:
                - Files
            operationId: Files_UploadAvatar
            parameters:
                - name: name
                  in: query
                  schema:
                    type: string
                - name: type
                  in: query
                  schema:
                    enum:
                        - user
                        - prj
                        - res
                    type: string
                    format: enum
                - name: class
                  in: query
                  schema:
                    type: string
            requestBody:
                content:
                    application/octet-stream:
                        schema:
                            type: string
                            format: binary
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UploadFileReply'
    /annofeed/v1/rawdata/{uid}:
        get:
            tags:
                - Files
            operationId: Files_GetRawdata
            security: [{}, {"jwt": []}]
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "302":
                    description: Redirect to the actual URL.
    /annofeed/v1/pfile/{share_id}:
        get:
            tags:
                - Files
            operationId: Files_GetPfile
            description: get access URL to a shared file; authorization is required
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "302":
                    description: Redirect to the actual URL.
components:
    securitySchemes:
        jwt:
            type: http
            scheme: bearer
            bearerFormat: JWT
    schemas:
        anno.v1.AttrAndValues:
            required:
                - name
                - values
            type: object
            properties:
                name:
                    type: string
                    description: name of the attribute
                values:
                    type: array
                    items:
                        type: string
                    description: values of the attribute
        anno.v1.DataConverter:
            required:
                - runtime
                - uri
            type: object
            properties:
                runtime:
                    type: string
                    description: conversion script runtime; default is python3.10
                uri:
                    type: string
                    description: conversion script URI
        anno.v1.DataValidationSummary:
            type: object
            properties:
                total_errors:
                    type: integer
                    description: total number of validation errors found, includes those unsaved errors
                    format: int32
                errors:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.DataValidationSummary_Error'
                    description: validation errors; only the first 10 errors are saved
        anno.v1.DataValidationSummary_Error:
            type: object
            properties:
                error:
                    enum:
                        - unspecified
                        - file_unknown
                        - file_missing
                    type: string
                    format: enum
                elem_index:
                    type: integer
                    format: int32
                rawdata_name:
                    type: string
        anno.v1.Direction:
            required:
                - toward
            type: object
            properties:
                origin:
                    type: array
                    items:
                        type: number
                        format: double
                    description: |-
                        origin point; if empty, it is the widget center point;
                         x, y if 2D; x, y, z if 3D
                toward:
                    type: array
                    items:
                        type: number
                        format: double
                    description: |-
                        toward point;
                         x, y if 2D; x, y, z if 3D
        anno.v1.Element:
            required:
                - index
                - name
                - type
                - datas
            type: object
            properties:
                index:
                    type: integer
                    description: element index in the lot dataset
                    format: int32
                name:
                    type: string
                    description: 'name of the element: innermost-folder/frame-index'
                type:
                    enum:
                        - unspecified
                        - image
                        - pointcloud
                        - fusion2d
                        - fusion3d
                        - fusion4d
                    type: string
                    format: enum
                datas:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Rawdata'
                anno:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.ElementAnno'
                    description: |-
                        标注结果；
                         仅用于结果导出，或在导入时用于传输已经有标注信息的数据；
                         标注过程中，请使用 Job 里的相关字段
            description: 一帧数据，包含一个或多个待标注文件
        anno.v1.ElementAnno:
            required:
                - index
                - name
                - ins_cnt
                - attrs
                - rawdata_annos
            type: object
            properties:
                index:
                    type: integer
                    description: element index in the lot dataset
                    format: int32
                name:
                    type: string
                    description: 'name of the element: innermost-folder/frame-index'
                rawdata_annos:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.RawdataAnno'
                    description: 相应位置的元素对应 Element 的 datas 相应位置的结果
                attrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AttrAndValues'
                ins_cnt:
                    type: integer
                    description: number of objects annotated in the element
                    format: int32
                metadata:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.ElementAnno_Metadata'
                    description: metadata of the element annos
                segmentation3d:
                    $ref: '#/components/schemas/anno.v1.Segmentation3d'
            description: annotations of an element (frame)
        anno.v1.ElementAnno_Metadata:
            type: object
            properties:
                executors:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Metadata_Executor'
                    description: executor of each phase
        anno.v1.Metadata_Executor:
            required:
                - user
                - submit_at
            type: object
            properties:
                user:
                    $ref: '#/components/schemas/iam.v1.BaseUser'
                submit_at:
                    type: string
                    format: date-time
                phase:
                    type: integer
                    format: int32
        anno.v1.Object:
            required:
                - uuid
                - label
            type: object
            properties:
                uuid:
                    type: string
                    description: UUID of the object
                track_id:
                    type: string
                    description: |-
                        一个任务包中，相同的 track_id 表示同一个实体对象。
                         命名可以采用 Job.Uid + Label.name + index 的模式，例如：“xxx-car-1”
                label:
                    $ref: '#/components/schemas/anno.v1.Object_Label'
                vanish_later:
                    type: boolean
                    description: 在连续帧标注中，表示该物体从下一帧开始将不复存在
                compound:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Object_Compound'
                    description: it is a compounded object if not null
                source:
                    enum:
                        - unspecified
                        - manual
                        - prelabel
                        - interpolation
                        - projection
                    type: string
                    description: how the object is created
                    format: enum
        anno.v1.Object_Compound:
            required:
                - parts
            type: object
            properties:
                parts:
                    type: array
                    items:
                        type: string
                    description: referenced component object UUIDs
        anno.v1.Object_Label:
            required:
                - name
            type: object
            properties:
                name:
                    type: string
                    description: label name
                widget:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Object_Widget'
                    description: widget will be null if compound is not null
                attrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AttrAndValues'
                    description: attributes associated with the object
        anno.v1.Object_Widget:
            required:
                - name
                - data
            type: object
            properties:
                name:
                    enum:
                        - unspecified
                        - box2d
                        - pscuboid
                        - cuboid
                        - poly2d
                        - poly3d
                        - line2d
                        - line3d
                        - point2d
                        - point3d
                        - bitmap
                    type: string
                    description: widget name
                    format: enum
                data:
                    type: array
                    items:
                        type: number
                        format: double
                    description: characteristic values of geometric shapes, or bitmap origin (left, top)
                gaps:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Object_Widget'
                    description: gaps within the widget if any
                uri:
                    type: string
                    description: |-
                        bitmap file URL or data URI, e.g. data:image/png;base64,<base64-encoded-file-content>
                         pointcloud file URL or data URI, e.g. data:application/pcd;base64,<base64-encoded-file-content>
                forward:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Direction'
                    description: forward direction. if origin is null, then the center point of the widget is implied.
                point_cnt:
                    type: integer
                    description: number of points within the object (in 3D segmentation tasks)
                    format: int32
                seg_class_id:
                    type: integer
                    description: class ID in RawdataAnno.Segmentation; zero if the task is not a segmentation task
                    format: int32
                line_type:
                    enum:
                        - line
                        - crspline
                        - centerline
                    type: string
                    description: line type used to draw the widget
                    format: enum
                parent_lines:
                    type: array
                    items:
                        type: string
                    description: related parent lines
        anno.v1.Rawdata:
            required:
                - name
                - title
                - type
                - format
                - url
                - size
                - sha256
                - meta
                - ins
                - transform
            type: object
            properties:
                name:
                    type: string
                    description: file pathname
                type:
                    enum:
                        - unspecified
                        - image
                        - pointcloud
                    type: string
                    description: data type
                    format: enum
                format:
                    enum:
                        - unspecified
                        - json
                        - png
                        - jpg
                        - pcd
                        - filelist
                        - webp
                    type: string
                    description: data format
                    format: enum
                url:
                    type: string
                size:
                    type: number
                    description: number of bytes in the file
                    format: double
                sha256:
                    type: string
                    description: SHA-256 of the file
                title:
                    type: string
                    description: display name (sensor name)
                transform:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.RawdataParam'
                    description: 变换参数，可以将世界坐标系下的点映射到图片上
                meta:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Rawdata_Meta'
                    description: metadata of rawdata
                ins:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Object'
                    description: |-
                        识别出的物体列表；
                         仅用于结果导出，或在导入时用于传输已经有标注信息的数据；
                         标注过程中，请使用 Job 里的相关字段
                orig_name:
                    type: string
                    description: original pathname of the rawdata
                embedding:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Rawdata_Embedding'
                    description: file embedding
            description: 待标注文件，比如一张图片、点云的一帧
        anno.v1.RawdataAnno:
            required:
                - name
                - objects
                - attrs
            type: object
            properties:
                name:
                    type: string
                    description: name of the rawdata
                objects:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Object'
                attrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AttrAndValues'
                segmentation:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Segmentation'
                    description: segmentation result of the rawdata; null if the task is not a segmentation task
                orig_name:
                    type: string
                    description: original pathname of the rawdata
                url:
                    type: string
                    description: url of the rawdata, only available during exporting anno results
                metadata:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.RawdataAnno_Metadata'
                    description: metadata
            description: annotations of a rawdata
        anno.v1.RawdataAnno_Metadata:
            type: object
            properties:
                type:
                    enum:
                        - unspecified
                        - image
                        - pointcloud
                    type: string
                    description: rawdata type
                    format: enum
                camera:
                    type: string
                    description: camera name if the rawdata is an image (ImageMeta.camera)
        anno.v1.RawdataParam:
            required:
                - type
                - column_cnt
                - data
            type: object
            properties:
                type:
                    enum:
                        - matrix
                        - extrinsic
                        - intrinsic
                        - distortion
                    type: string
                    format: enum
                column_cnt:
                    type: integer
                    description: number of columns in the data
                    format: int32
                data:
                    type: array
                    items:
                        type: number
                        format: double
                    description: if type is distortion, the 1st number is the distortion model type (DistortionType)
            description: 与待标注文件相关的参数或变换矩阵
        anno.v1.Rawdata_Embedding:
            type: object
            properties:
                url:
                    type: string
                    description: embedding file URL
        anno.v1.Rawdata_ImageMeta:
            required:
                - width
                - height
            type: object
            properties:
                width:
                    type: integer
                    format: int32
                height:
                    type: integer
                    format: int32
                camera:
                    type: string
                    description: camera name
        anno.v1.Rawdata_Meta:
            type: object
            properties:
                image:
                    $ref: '#/components/schemas/anno.v1.Rawdata_ImageMeta'
                pcd:
                    $ref: '#/components/schemas/anno.v1.Rawdata_PCDMeta'
        anno.v1.Rawdata_PCDMeta:
            required:
                - points
            type: object
            properties:
                points:
                    type: integer
                    description: number of points in the pcd file
                    format: int32
                viewpoint:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.RawdataParam'
                    description: viewpoint of the point cloud in the point cloud's coordinate system
                pose:
                    type: array
                    items:
                        type: number
                        format: double
                    description: |-
                        origin point of the point cloud in world coordinate system when point cloud is not in world coordinate system,
                         in the form of [x,y,z,qx,qy,qz,qw]
        anno.v1.Segmentation:
            type: object
            properties:
                classes:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Segmentation_Class'
                    description: classes definition; if empty, class name is RawdataAnno.Object.uuid with the matching seg_class_id
                rle:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Segmentation_RLE'
                    description: unpacked RLE; either rle or rle_pack is set, not both
                rle_pack:
                    type: string
                    description: |-
                        packed RLE; serialize RLE using JSON, then compress using gzip, then encode in base64;
                         format: data:application/json;gzip;base64,[base64-encoded-content]
                              or http://packed-RLE-file-url (file format: RLE;json;gzip\ngzip-encoded-content)
        anno.v1.Segmentation3d:
            type: object
            properties:
                result:
                    $ref: '#/components/schemas/anno.v1.Segmentation3dResult'
                statistic:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Segmentation3dStatistic'
        anno.v1.Segmentation3dInstance:
            required:
                - instance_id
                - num
            type: object
            properties:
                instance_id:
                    type: integer
                    format: int32
                num:
                    type: integer
                    format: int32
        anno.v1.Segmentation3dResult:
            type: object
            properties:
                category:
                    type: array
                    items:
                        type: string
                instance_id:
                    type: array
                    items:
                        type: integer
                        format: int32
        anno.v1.Segmentation3dStatistic:
            required:
                - category_name
                - num
            type: object
            properties:
                category_name:
                    type: string
                num:
                    type: integer
                    format: int32
                instances:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Segmentation3dInstance'
        anno.v1.Segmentation_Class:
            required:
                - id
                - name
            type: object
            properties:
                id:
                    type: integer
                    description: serial number, start from 1
                    format: int32
                name:
                    type: string
                    description: it is Object.uuid for instance segmentation; it is label name for semantic/panoptic segmentation
        anno.v1.Segmentation_RLE:
            required:
                - runs
            type: object
            properties:
                runs:
                    type: array
                    items:
                        type: integer
                        format: int32
                    description: number of consecutive points having the same class ID
                vals:
                    type: array
                    items:
                        type: integer
                        format: int32
                    description: |-
                        Class.id of the points at the corresponding index in runs;
                         if empty, it implies a sequence of 0 and 1, starting with 0: [0, 1, 0, 1, ...]
        anno.v1.Source:
            required:
                - uris
            type: object
            properties:
                uris:
                    type: array
                    items:
                        type: string
                    description: package file (.zip) URIs
                proprietary:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Source_Proprietary'
                    description: access config when the files are hosted in a third-party platform
                style:
                    type: string
                    description: folder layout style within package files if not conform to Konvery standard
                elem_type:
                    enum:
                        - unspecified
                        - image
                        - pointcloud
                        - fusion2d
                        - fusion3d
                        - fusion4d
                    type: string
                    description: element type
                    format: enum
                is_frame_series:
                    type: boolean
                    description: if source contains consecutive frames
                plain_size_gb:
                    type: integer
                    description: size of the unpacked data in GB
                    format: int32
                error_handlers:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Source_ParseErrorHandler'
                    description: |-
                        define parser error handlers; it will fail the parser if no handler is specified.
                         max size is count(RawdataErrorAction.Error) x count(Rawdata.Type).
                auto_parse:
                    type: boolean
                    description: whether to automatically parse data in annofeed service
                named_uris:
                    type: object
                    additionalProperties:
                        type: string
                    description: define single file names and their corresponding expected names;
                converter:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.DataConverter'
                    description: converter is a piece of script to convert the source data to the platform accepted format
                metadata:
                    type: object
                    additionalProperties:
                        type: string
                    description: metadata about original data, which might be used in parsing data and exporting annos
        anno.v1.Source_ParseErrorHandler:
            type: object
            properties:
                rawdata_type:
                    enum:
                        - unspecified
                        - image
                        - pointcloud
                    type: string
                    format: enum
                error:
                    enum:
                        - unspecified
                        - file_unknown
                        - file_missing
                    type: string
                    format: enum
                handler:
                    enum:
                        - unspecified
                        - fail
                        - ignore
                    type: string
                    format: enum
        anno.v1.Source_Proprietary:
            required:
                - type
                - config
            type: object
            properties:
                type:
                    type: string
                    description: 3rd-party file host service type
                config:
                    type: string
                    description: 3rd-party file host service access config
        annofeed.v1.CameraParamV2:
            type: object
            properties:
                name:
                    type: string
                    description: name of the camera
                extrinsic:
                    type: array
                    items:
                        type: number
                        format: double
                    description: |-
                        extrinsic, either in the form of position and quaternion ([x,y,z,qx,qy,qz,qw]), or
                         in the form of 4x4 row-major matrix ([r00,r01,r02,r03,...,r30,r31,r32,r33]).
                         In the 1st format, it is the pose of the camera in the point cloud's coordinate system;
                         in the 2nd format, it is a matrix to convert point cloud into camera's coordinate system.
                         <2nd format> = inverse(to_matrix(<1st format>))
                intrinsic:
                    type: array
                    items:
                        type: number
                        format: double
                    description: |-
                        intrinsic, either in the form of [fx,fy,cx,cy], or
                         in the form of 3x3 row-major matrix ([fx,0,cx,0,fy,cy,0,0,1])
                distortion:
                    type: array
                    items:
                        type: number
                        format: double
                    description: 'distortion params: distortion_type,k1,k2,...'
                title:
                    type: string
                    description: used to display in anno platform if not empty, preferably in local language
        annofeed.v1.CreateDataRequest:
            required:
                - type
                - source
                - org_uid
            type: object
            properties:
                uid:
                    type: string
                    description: data UID (only needed in update-requests)
                name:
                    type: string
                    description: data name (mandatory in create-requests)
                desc:
                    type: string
                    description: data description
                type:
                    enum:
                        - unspecified
                        - image
                        - pointcloud
                        - fusion2d
                        - fusion3d
                        - fusion4d
                    type: string
                    description: data type
                    format: enum
                base_on_uid:
                    type: string
                    description: based on data UID
                source:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Source'
                    description: source files which the data originates from
                org_uid:
                    type: string
                    description: UID of the organization which the data belongs to
                order_uid:
                    type: string
                    description: UID of the order which caused the data
        annofeed.v1.CreateFileReply:
            required:
                - file
                - upload_urls
                - url_expires_at
            type: object
            properties:
                file:
                    $ref: '#/components/schemas/annofeed.v1.File'
                upload_urls:
                    type: array
                    items:
                        type: string
                    description: if len(upload_urls) > 1, file should be uploaded as multi-parts
                url_expires_at:
                    type: string
                    description: presigned URL expire time. upload should be done before that
                    format: date-time
                pre_existing:
                    type: boolean
                    description: if true, a previously created file is returned due to they have the same hash and size
        annofeed.v1.CreateFileRequest:
            required:
                - name
                - size
                - parts
                - mime
            type: object
            properties:
                name:
                    type: string
                    description: name the file to be created
                size:
                    type: number
                    description: number of bytes of the file
                    format: double
                parts:
                    type: integer
                    description: |-
                        set number of parts in a multi-part upload;
                         if unspecified, which is 0, server will choose a proper number based on file size.
                         Part size should be between 5 MiB to 5 GiB. There is no minimum size limit on the
                         last part of your multipart upload.
                    format: int32
                mime:
                    type: string
                    description: |-
                        MIME type of the file: e.g. "application/zip",
                         or file name extention: e.g. ".zip"
                sha256:
                    type: string
                    description: 'SHA256 digest of the file; it also accepts MD5 digest in the format: md5:xxx'
                org_uid:
                    type: string
                    description: organization that the file belongs to; if omitted, it is the user's organization
        annofeed.v1.Data:
            required:
                - uid
                - name
                - type
                - source
                - org_uid
                - state
                - created_at
            type: object
            properties:
                uid:
                    readOnly: true
                    type: string
                    description: data UID
                name:
                    type: string
                    description: data name
                desc:
                    type: string
                    description: data description
                type:
                    enum:
                        - unspecified
                        - image
                        - pointcloud
                        - fusion2d
                        - fusion3d
                        - fusion4d
                    type: string
                    description: data type
                    format: enum
                base_on_uid:
                    type: string
                    description: based on data UID
                source:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Source'
                    description: source files which the data originates from
                org_uid:
                    type: string
                    description: UID of the organization which the data belongs to
                order_uid:
                    type: string
                    description: UID of the order which caused the data
                size:
                    readOnly: true
                    type: integer
                    description: number of elements in the data
                    format: int32
                state:
                    readOnly: true
                    enum:
                        - unspecified
                        - raw
                        - fetching
                        - processing
                        - ready
                        - abandoned
                        - disabled
                        - failed
                    type: string
                    description: 'data state: fetching, processing, ready, failed'
                    format: enum
                error:
                    type: string
                    description: when state is failed, this field will contain the error message
                creator_uid:
                    type: string
                    description: UID of the creator
                created_at:
                    readOnly: true
                    type: string
                    description: data creation time
                    format: date-time
                summary:
                    readOnly: true
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.DataValidationSummary'
                    description: data validation summary
        annofeed.v1.DummyReply:
            type: object
            properties:
                ParamFileV2Ref:
                    $ref: '#/components/schemas/annofeed.v1.ParamFileV2'
        annofeed.v1.File:
            required:
                - uid
                - uri
                - name
                - size
                - sha256
                - state
                - org_uid
                - created_at
            type: object
            properties:
                uid:
                    type: string
                    description: file UID
                uri:
                    type: string
                    description: file URI
                name:
                    type: string
                    description: file name
                size:
                    type: number
                    description: file size in number of bytes
                    format: double
                mime:
                    type: string
                    description: file MIME type
                sha256:
                    type: string
                    description: SHA256/MD5 digest of the file
                org_uid:
                    type: string
                    description: UID of the organization which the file belongs to
                state:
                    enum:
                        - unspecified
                        - uploading
                        - uploaded
                    type: string
                    description: file state
                    format: enum
                creator_uid:
                    type: string
                    description: UID of the creator
                created_at:
                    type: string
                    description: file creation time
                    format: date-time
        annofeed.v1.FinishFileUploadRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: file uid
                etags:
                    type: array
                    items:
                        type: string
                    description: |-
                        ETAG returned from each part upload, in the part number order;
                         required in a multipart upload
        annofeed.v1.GetDataElementsReply:
            type: object
            properties:
                elements:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Element'
        annofeed.v1.GetDataMetaReply:
            type: object
            properties:
                metadata:
                    type: object
                    additionalProperties:
                        type: string
                metafiles:
                    $ref: '#/components/schemas/types.Filelist'
        annofeed.v1.GetFileUploadURLsReply:
            required:
                - upload_urls
                - url_expires_at
            type: object
            properties:
                upload_urls:
                    type: array
                    items:
                        type: string
                    description: if len(upload_urls) > 1, file should be uploaded as multi-parts
                url_expires_at:
                    type: string
                    description: presigned URL expire time. upload should be done before that
                    format: date-time
        annofeed.v1.GetVersionReply:
            type: object
            properties:
                version:
                    type: string
        annofeed.v1.LidarParamV2:
            type: object
            properties:
                viewpoint:
                    type: array
                    items:
                        type: number
                        format: double
                    description: point cloud viewpoint (in the pointcloud's coordinate system), in the form of position and quaternion ([x,y,z,qx,qy,qz,qw])
                pose:
                    type: array
                    items:
                        type: number
                        format: double
                    description: |-
                        origin point of the point cloud in world coordinate system when point cloud is not in world coordinate system,
                         either in the form of position and quaternion ([x,y,z,qx,qy,qz,qw]), or
                         in the form of 4x4 row-major matrix ([r00,r01,r02,r03,...,r30,r31,r32,r33]).
                         In the 1st format, it is the pose of the point cloud's origin point in world coordinate system;
                         in the 2nd format, it is a matrix to convert point cloud into world coordinate system.
                         <2nd format> = to_matrix(<1st format>)
                timestamp:
                    type: number
                    description: UNIX timestamp in seconds
                    format: double
                transform:
                    type: array
                    items:
                        type: number
                        format: double
                    description: |-
                        transforms the point cloud into lidar coordinate system when the point cloud is not in lidar coordinate system,
                         either in the form of position and quaternion ([x,y,z,qx,qy,qz,qw]), or
                         in the form of 4x4 row-major matrix ([r00,r01,r02,r03,...,r30,r31,r32,r33]).
                         In the 1st format, it is the position of the lidar in the point cloud's coordinate system;
                         in the 2nd format, it is a matrix to convert point cloud into lidar coordinate system.
                         <2nd format> = inverse(to_matrix(<1st format>))
        annofeed.v1.ListDataReply:
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                datas:
                    type: array
                    items:
                        $ref: '#/components/schemas/annofeed.v1.Data'
        annofeed.v1.ListFileReply:
            required:
                - files
                - next_page_token
            type: object
            properties:
                files:
                    type: array
                    items:
                        $ref: '#/components/schemas/annofeed.v1.File'
                next_page_token:
                    type: string
        annofeed.v1.ParamFileMeta:
            type: object
            properties:
                version:
                    type: string
                    description: version of the param file schema
        annofeed.v1.ParamFileV2:
            type: object
            properties:
                meta:
                    allOf:
                        - $ref: '#/components/schemas/annofeed.v1.ParamFileMeta'
                    description: version = "v2"
                lidar:
                    allOf:
                        - $ref: '#/components/schemas/annofeed.v1.LidarParamV2'
                    description: define elem param
                lidars:
                    type: array
                    items:
                        $ref: '#/components/schemas/annofeed.v1.LidarParamV2'
                    description: define clip param
                cameras:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/annofeed.v1.CameraParamV2'
        annofeed.v1.ParseDataRequest:
            required:
                - uid
            type: object
            properties:
                uid:
                    type: string
                    description: data UID
                option:
                    enum:
                        - unspecified
                        - force
                        - reparse_failed
                    type: string
                    description: parse option will not break current data parsing workflow if any
                    format: enum
        annofeed.v1.SetRawdataEmbeddingReply:
            type: object
            properties:
                embedding_url:
                    type: string
                    description: embedding file access URL
        annofeed.v1.SetRawdataEmbeddingRequest:
            required:
                - uid
                - rawdata_name
                - embedding_uri
            type: object
            properties:
                uid:
                    type: string
                    description: data UID
                rawdata_name:
                    type: string
                    description: rawdata name
                embedding_uri:
                    type: string
                    description: embedding file URI
        annofeed.v1.ShareFileReply:
            required:
                - url
                - expires_at
            type: object
            properties:
                url:
                    type: string
                    description: URL to download the file
                expires_at:
                    type: string
                    description: presigned URL expires time.
                    format: date-time
        annofeed.v1.ShareFileRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: file uid
                timeout:
                    type: integer
                    description: set URL's valid duration in seconds; if it is -1, it will return a permanent URL
                    format: int32
        iam.v1.BaseUser:
            required:
                - uid
                - name
                - avatar
            type: object
            properties:
                uid:
                    type: string
                    description: user/team uid
                name:
                    type: string
                    description: user/team name
                avatar:
                    type: string
                    description: user/team avatar url
        types.Filelist:
            required:
                - files
            type: object
            properties:
                files:
                    type: array
                    items:
                        $ref: '#/components/schemas/types.Filelist_File'
        types.Filelist_File:
            required:
                - url
                - size
            type: object
            properties:
                url:
                    type: string
                size:
                    type: number
                    description: file size in bytes
                    format: double
                name:
                    type: string
                    description: 'file name in path format without `data` prefix: e.g. frame1/meta/config.json'
        UploadFileReply:
            type: object
            properties:
                uri:
                    type: string
                url:
                    type: string
                    description: only available when uploading avatars
tags:
    - name: Configs
    - name: Datas
    - name: Dummy
      description: |-
        set reference to inexplicitly referenced data structures, so that
         they can be included in the openapi documentation.
    - name: Files
