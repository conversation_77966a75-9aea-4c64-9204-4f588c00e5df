// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: annofeed/v1/param.proto

package annofeed

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	v1 "gitlab.rp.konvery.work/platform/apis/anno/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CoordSys_Type_Enum int32

const (
	CoordSys_Type_unspecified CoordSys_Type_Enum = 0
	// Universal Transverse Mercator
	CoordSys_Type_utm CoordSys_Type_Enum = 1
	// World Geodetic System (Latitude, Longitude, Altitude)
	CoordSys_Type_wgs84 CoordSys_Type_Enum = 2
	// vehicle coordinate system, direction of XYZ: FLU(Forwards, Leftwards, Upwards)
	CoordSys_Type_iso8855 CoordSys_Type_Enum = 10
	// vehicle coordinate system, direction of XYZ: RFU(Rightwards, Forwards, Upwards)
	CoordSys_Type_imu CoordSys_Type_Enum = 11
	// vehicle coordinate system, direction of XYZ: FRD(Forwards, Rightwards, Downwards)
	CoordSys_Type_sae CoordSys_Type_Enum = 12
)

// Enum value maps for CoordSys_Type_Enum.
var (
	CoordSys_Type_Enum_name = map[int32]string{
		0:  "unspecified",
		1:  "utm",
		2:  "wgs84",
		10: "iso8855",
		11: "imu",
		12: "sae",
	}
	CoordSys_Type_Enum_value = map[string]int32{
		"unspecified": 0,
		"utm":         1,
		"wgs84":       2,
		"iso8855":     10,
		"imu":         11,
		"sae":         12,
	}
)

func (x CoordSys_Type_Enum) Enum() *CoordSys_Type_Enum {
	p := new(CoordSys_Type_Enum)
	*p = x
	return p
}

func (x CoordSys_Type_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CoordSys_Type_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_annofeed_v1_param_proto_enumTypes[0].Descriptor()
}

func (CoordSys_Type_Enum) Type() protoreflect.EnumType {
	return &file_annofeed_v1_param_proto_enumTypes[0]
}

func (x CoordSys_Type_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CoordSys_Type_Enum.Descriptor instead.
func (CoordSys_Type_Enum) EnumDescriptor() ([]byte, []int) {
	return file_annofeed_v1_param_proto_rawDescGZIP(), []int{0, 0, 0}
}

type CoordSys struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string             `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type CoordSys_Type_Enum `protobuf:"varint,2,opt,name=type,proto3,enum=annofeed.v1.CoordSys_Type_Enum" json:"type,omitempty"`
	// name of parent coordinate system
	Parent string `protobuf:"bytes,3,opt,name=parent,proto3" json:"parent,omitempty"`
	// [x,y,z,rx,ry,rz,rw] in parent coordinate system; if it is empty, it is [0,0,0,0,0,0,1]
	Origin []float64 `protobuf:"fixed64,4,rep,packed,name=origin,proto3" json:"origin,omitempty"`
}

func (x *CoordSys) Reset() {
	*x = CoordSys{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_param_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoordSys) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoordSys) ProtoMessage() {}

func (x *CoordSys) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_param_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoordSys.ProtoReflect.Descriptor instead.
func (*CoordSys) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_param_proto_rawDescGZIP(), []int{0}
}

func (x *CoordSys) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CoordSys) GetType() CoordSys_Type_Enum {
	if x != nil {
		return x.Type
	}
	return CoordSys_Type_unspecified
}

func (x *CoordSys) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *CoordSys) GetOrigin() []float64 {
	if x != nil {
		return x.Origin
	}
	return nil
}

type Pose struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// coordinate system
	CoordSys string `protobuf:"bytes,1,opt,name=coord_sys,json=coordSys,proto3" json:"coord_sys,omitempty"`
	// location and rotation in the format [x,y,z,rx,ry,rz,rw]
	Pose []float64 `protobuf:"fixed64,2,rep,packed,name=pose,proto3" json:"pose,omitempty"`
}

func (x *Pose) Reset() {
	*x = Pose{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_param_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pose) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pose) ProtoMessage() {}

func (x *Pose) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_param_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pose.ProtoReflect.Descriptor instead.
func (*Pose) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_param_proto_rawDescGZIP(), []int{1}
}

func (x *Pose) GetCoordSys() string {
	if x != nil {
		return x.CoordSys
	}
	return ""
}

func (x *Pose) GetPose() []float64 {
	if x != nil {
		return x.Pose
	}
	return nil
}

type VehicleParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pose *Pose `protobuf:"bytes,1,opt,name=pose,proto3" json:"pose,omitempty"`
	// location and rotation varies according to frame index: [x1,y1,z1,rx1,ry1,rz1,rw1, x2,y2,z2,rx2,ry2,rz2,rw2, ...]
	VaryPose []float64 `protobuf:"fixed64,3,rep,packed,name=vary_pose,json=varyPose,proto3" json:"vary_pose,omitempty"`
}

func (x *VehicleParam) Reset() {
	*x = VehicleParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_param_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VehicleParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VehicleParam) ProtoMessage() {}

func (x *VehicleParam) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_param_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VehicleParam.ProtoReflect.Descriptor instead.
func (*VehicleParam) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_param_proto_rawDescGZIP(), []int{2}
}

func (x *VehicleParam) GetPose() *Pose {
	if x != nil {
		return x.Pose
	}
	return nil
}

func (x *VehicleParam) GetVaryPose() []float64 {
	if x != nil {
		return x.VaryPose
	}
	return nil
}

type LidarParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pose       *Pose              `protobuf:"bytes,1,opt,name=pose,proto3" json:"pose,omitempty"`
	Transforms []*v1.RawdataParam `protobuf:"bytes,2,rep,name=transforms,proto3" json:"transforms,omitempty"`
	Name       string             `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *LidarParam) Reset() {
	*x = LidarParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_param_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LidarParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LidarParam) ProtoMessage() {}

func (x *LidarParam) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_param_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LidarParam.ProtoReflect.Descriptor instead.
func (*LidarParam) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_param_proto_rawDescGZIP(), []int{3}
}

func (x *LidarParam) GetPose() *Pose {
	if x != nil {
		return x.Pose
	}
	return nil
}

func (x *LidarParam) GetTransforms() []*v1.RawdataParam {
	if x != nil {
		return x.Transforms
	}
	return nil
}

func (x *LidarParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CameraParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pose *Pose `protobuf:"bytes,1,opt,name=pose,proto3" json:"pose,omitempty"`
	// intrinsic params, distortion params and other transformations
	Transforms []*v1.RawdataParam      `protobuf:"bytes,2,rep,name=transforms,proto3" json:"transforms,omitempty"`
	Name       string                  `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Dimensions *CameraParam_Dimensions `protobuf:"bytes,4,opt,name=dimensions,proto3" json:"dimensions,omitempty"`
	// used to display in anno platform if not empty, preferably in local language
	Title string `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
}

func (x *CameraParam) Reset() {
	*x = CameraParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_param_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraParam) ProtoMessage() {}

func (x *CameraParam) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_param_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraParam.ProtoReflect.Descriptor instead.
func (*CameraParam) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_param_proto_rawDescGZIP(), []int{4}
}

func (x *CameraParam) GetPose() *Pose {
	if x != nil {
		return x.Pose
	}
	return nil
}

func (x *CameraParam) GetTransforms() []*v1.RawdataParam {
	if x != nil {
		return x.Transforms
	}
	return nil
}

func (x *CameraParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CameraParam) GetDimensions() *CameraParam_Dimensions {
	if x != nil {
		return x.Dimensions
	}
	return nil
}

func (x *CameraParam) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type ParamsFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vehicle      *VehicleParam           `protobuf:"bytes,1,opt,name=vehicle,proto3" json:"vehicle,omitempty"`
	Lidars       map[string]*LidarParam  `protobuf:"bytes,2,rep,name=lidars,proto3" json:"lidars,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Cameras      map[string]*CameraParam `protobuf:"bytes,3,rep,name=cameras,proto3" json:"cameras,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	CoordSystems map[string]*CoordSys    `protobuf:"bytes,4,rep,name=coord_systems,json=coordSystems,proto3" json:"coord_systems,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// null, or version = "v1"
	Meta *ParamFileMeta `protobuf:"bytes,5,opt,name=meta,proto3" json:"meta,omitempty"`
}

func (x *ParamsFile) Reset() {
	*x = ParamsFile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_param_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParamsFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParamsFile) ProtoMessage() {}

func (x *ParamsFile) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_param_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParamsFile.ProtoReflect.Descriptor instead.
func (*ParamsFile) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_param_proto_rawDescGZIP(), []int{5}
}

func (x *ParamsFile) GetVehicle() *VehicleParam {
	if x != nil {
		return x.Vehicle
	}
	return nil
}

func (x *ParamsFile) GetLidars() map[string]*LidarParam {
	if x != nil {
		return x.Lidars
	}
	return nil
}

func (x *ParamsFile) GetCameras() map[string]*CameraParam {
	if x != nil {
		return x.Cameras
	}
	return nil
}

func (x *ParamsFile) GetCoordSystems() map[string]*CoordSys {
	if x != nil {
		return x.CoordSystems
	}
	return nil
}

func (x *ParamsFile) GetMeta() *ParamFileMeta {
	if x != nil {
		return x.Meta
	}
	return nil
}

type LidarParamV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// point cloud viewpoint (in the pointcloud's coordinate system), in the form of position and quaternion ([x,y,z,qx,qy,qz,qw])
	Viewpoint []float64 `protobuf:"fixed64,1,rep,packed,name=viewpoint,proto3" json:"viewpoint,omitempty"`
	// origin point of the point cloud in world coordinate system when point cloud is not in world coordinate system,
	// either in the form of position and quaternion ([x,y,z,qx,qy,qz,qw]), or
	// in the form of 4x4 row-major matrix ([r00,r01,r02,r03,...,r30,r31,r32,r33]).
	// In the 1st format, it is the pose of the point cloud's origin point in world coordinate system;
	// in the 2nd format, it is a matrix to convert point cloud into world coordinate system.
	// <2nd format> = to_matrix(<1st format>)
	Pose []float64 `protobuf:"fixed64,2,rep,packed,name=pose,proto3" json:"pose,omitempty"`
	// UNIX timestamp in seconds
	Timestamp float64 `protobuf:"fixed64,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// transforms the point cloud into lidar coordinate system when the point cloud is not in lidar coordinate system,
	// either in the form of position and quaternion ([x,y,z,qx,qy,qz,qw]), or
	// in the form of 4x4 row-major matrix ([r00,r01,r02,r03,...,r30,r31,r32,r33]).
	// In the 1st format, it is the position of the lidar in the point cloud's coordinate system;
	// in the 2nd format, it is a matrix to convert point cloud into lidar coordinate system.
	// <2nd format> = inverse(to_matrix(<1st format>))
	Transform []float64 `protobuf:"fixed64,4,rep,packed,name=transform,proto3" json:"transform,omitempty"`
}

func (x *LidarParamV2) Reset() {
	*x = LidarParamV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_param_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LidarParamV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LidarParamV2) ProtoMessage() {}

func (x *LidarParamV2) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_param_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LidarParamV2.ProtoReflect.Descriptor instead.
func (*LidarParamV2) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_param_proto_rawDescGZIP(), []int{6}
}

func (x *LidarParamV2) GetViewpoint() []float64 {
	if x != nil {
		return x.Viewpoint
	}
	return nil
}

func (x *LidarParamV2) GetPose() []float64 {
	if x != nil {
		return x.Pose
	}
	return nil
}

func (x *LidarParamV2) GetTimestamp() float64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *LidarParamV2) GetTransform() []float64 {
	if x != nil {
		return x.Transform
	}
	return nil
}

type CameraParamV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the camera
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// extrinsic, either in the form of position and quaternion ([x,y,z,qx,qy,qz,qw]), or
	// in the form of 4x4 row-major matrix ([r00,r01,r02,r03,...,r30,r31,r32,r33]).
	// In the 1st format, it is the pose of the camera in the point cloud's coordinate system;
	// in the 2nd format, it is a matrix to convert point cloud into camera's coordinate system.
	// <2nd format> = inverse(to_matrix(<1st format>))
	Extrinsic []float64 `protobuf:"fixed64,2,rep,packed,name=extrinsic,proto3" json:"extrinsic,omitempty"`
	// intrinsic, either in the form of [fx,fy,cx,cy], or
	// in the form of 3x3 row-major matrix ([fx,0,cx,0,fy,cy,0,0,1])
	Intrinsic []float64 `protobuf:"fixed64,3,rep,packed,name=intrinsic,proto3" json:"intrinsic,omitempty"`
	// distortion params: distortion_type,k1,k2,...
	Distortion []float64 `protobuf:"fixed64,4,rep,packed,name=distortion,proto3" json:"distortion,omitempty"`
	// used to display in anno platform if not empty, preferably in local language
	Title string `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
}

func (x *CameraParamV2) Reset() {
	*x = CameraParamV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_param_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraParamV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraParamV2) ProtoMessage() {}

func (x *CameraParamV2) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_param_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraParamV2.ProtoReflect.Descriptor instead.
func (*CameraParamV2) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_param_proto_rawDescGZIP(), []int{7}
}

func (x *CameraParamV2) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CameraParamV2) GetExtrinsic() []float64 {
	if x != nil {
		return x.Extrinsic
	}
	return nil
}

func (x *CameraParamV2) GetIntrinsic() []float64 {
	if x != nil {
		return x.Intrinsic
	}
	return nil
}

func (x *CameraParamV2) GetDistortion() []float64 {
	if x != nil {
		return x.Distortion
	}
	return nil
}

func (x *CameraParamV2) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type ParamFileMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// version of the param file schema
	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *ParamFileMeta) Reset() {
	*x = ParamFileMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_param_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParamFileMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParamFileMeta) ProtoMessage() {}

func (x *ParamFileMeta) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_param_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParamFileMeta.ProtoReflect.Descriptor instead.
func (*ParamFileMeta) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_param_proto_rawDescGZIP(), []int{8}
}

func (x *ParamFileMeta) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type ParamFileV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// version = "v2"
	Meta *ParamFileMeta `protobuf:"bytes,1,opt,name=meta,proto3" json:"meta,omitempty"`
	// define elem param
	Lidar *LidarParamV2 `protobuf:"bytes,2,opt,name=lidar,proto3" json:"lidar,omitempty"`
	// define clip param
	Lidars  []*LidarParamV2           `protobuf:"bytes,3,rep,name=lidars,proto3" json:"lidars,omitempty"`
	Cameras map[string]*CameraParamV2 `protobuf:"bytes,4,rep,name=cameras,proto3" json:"cameras,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ParamFileV2) Reset() {
	*x = ParamFileV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_param_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParamFileV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParamFileV2) ProtoMessage() {}

func (x *ParamFileV2) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_param_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParamFileV2.ProtoReflect.Descriptor instead.
func (*ParamFileV2) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_param_proto_rawDescGZIP(), []int{9}
}

func (x *ParamFileV2) GetMeta() *ParamFileMeta {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *ParamFileV2) GetLidar() *LidarParamV2 {
	if x != nil {
		return x.Lidar
	}
	return nil
}

func (x *ParamFileV2) GetLidars() []*LidarParamV2 {
	if x != nil {
		return x.Lidars
	}
	return nil
}

func (x *ParamFileV2) GetCameras() map[string]*CameraParamV2 {
	if x != nil {
		return x.Cameras
	}
	return nil
}

type CoordSys_Type struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CoordSys_Type) Reset() {
	*x = CoordSys_Type{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_param_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoordSys_Type) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoordSys_Type) ProtoMessage() {}

func (x *CoordSys_Type) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_param_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoordSys_Type.ProtoReflect.Descriptor instead.
func (*CoordSys_Type) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_param_proto_rawDescGZIP(), []int{0, 0}
}

type CameraParam_Dimensions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width  int32 `protobuf:"varint,1,opt,name=width,proto3" json:"width,omitempty"`
	Height int32 `protobuf:"varint,2,opt,name=height,proto3" json:"height,omitempty"`
}

func (x *CameraParam_Dimensions) Reset() {
	*x = CameraParam_Dimensions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_param_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraParam_Dimensions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraParam_Dimensions) ProtoMessage() {}

func (x *CameraParam_Dimensions) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_param_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraParam_Dimensions.ProtoReflect.Descriptor instead.
func (*CameraParam_Dimensions) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_param_proto_rawDescGZIP(), []int{4, 0}
}

func (x *CameraParam_Dimensions) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *CameraParam_Dimensions) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

var File_annofeed_v1_param_proto protoreflect.FileDescriptor

var file_annofeed_v1_param_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61,
	0x72, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x66,
	0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe1, 0x01, 0x0a, 0x08, 0x43, 0x6f, 0x6f, 0x72,
	0x64, 0x53, 0x79, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65,
	0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x53, 0x79, 0x73, 0x2e, 0x54, 0x79,
	0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x03, 0x28, 0x01, 0x52,
	0x06, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x1a, 0x52, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x4a, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x75, 0x74, 0x6d, 0x10,
	0x01, 0x12, 0x09, 0x0a, 0x05, 0x77, 0x67, 0x73, 0x38, 0x34, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07,
	0x69, 0x73, 0x6f, 0x38, 0x38, 0x35, 0x35, 0x10, 0x0a, 0x12, 0x07, 0x0a, 0x03, 0x69, 0x6d, 0x75,
	0x10, 0x0b, 0x12, 0x07, 0x0a, 0x03, 0x73, 0x61, 0x65, 0x10, 0x0c, 0x22, 0x37, 0x0a, 0x04, 0x50,
	0x6f, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x5f, 0x73, 0x79, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x53, 0x79, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x01, 0x52, 0x04,
	0x70, 0x6f, 0x73, 0x65, 0x22, 0x52, 0x0a, 0x0c, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x6f, 0x73, 0x65, 0x52, 0x04, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x76,
	0x61, 0x72, 0x79, 0x5f, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x01, 0x52, 0x08,
	0x76, 0x61, 0x72, 0x79, 0x50, 0x6f, 0x73, 0x65, 0x22, 0x7e, 0x0a, 0x0a, 0x4c, 0x69, 0x64, 0x61,
	0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x6f, 0x73, 0x65, 0x52, 0x04, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x35, 0x0a,
	0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x77, 0x64,
	0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x6f, 0x72, 0x6d, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x96, 0x02, 0x0a, 0x0b, 0x43, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x6f, 0x73, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65,
	0x64, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x73, 0x65, 0x52, 0x04, 0x70, 0x6f, 0x73, 0x65, 0x12,
	0x35, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61,
	0x77, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x6f, 0x72, 0x6d, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0a, 0x64, 0x69,
	0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x2e, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x0a, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x1a, 0x3a, 0x0a, 0x0a, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x22, 0xc0, 0x04, 0x0a, 0x0a, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x46, 0x69, 0x6c, 0x65,
	0x12, 0x33, 0x0a, 0x07, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x07, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x3b, 0x0a, 0x06, 0x6c, 0x69, 0x64, 0x61, 0x72, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x2e, 0x4c,
	0x69, 0x64, 0x61, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x69, 0x64, 0x61,
	0x72, 0x73, 0x12, 0x3e, 0x0a, 0x07, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x2e, 0x43, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x63, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x73, 0x12, 0x4e, 0x0a, 0x0d, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x5f, 0x73, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x46, 0x69,
	0x6c, 0x65, 0x2e, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x04, 0x6d, 0x65,
	0x74, 0x61, 0x1a, 0x52, 0x0a, 0x0b, 0x4c, 0x69, 0x64, 0x61, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x64, 0x61, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x54, 0x0a, 0x0c, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65,
	0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x56, 0x0a, 0x11,
	0x43, 0x6f, 0x6f, 0x72, 0x64, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x2b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x53, 0x79, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x7c, 0x0a, 0x0c, 0x4c, 0x69, 0x64, 0x61, 0x72, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x56, 0x32, 0x12, 0x1c, 0x0a, 0x09, 0x76, 0x69, 0x65, 0x77, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x01, 0x52, 0x09, 0x76, 0x69, 0x65, 0x77, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x01,
	0x52, 0x04, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72,
	0x6d, 0x18, 0x04, 0x20, 0x03, 0x28, 0x01, 0x52, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f,
	0x72, 0x6d, 0x22, 0x95, 0x01, 0x0a, 0x0d, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x56, 0x32, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x78, 0x74, 0x72,
	0x69, 0x6e, 0x73, 0x69, 0x63, 0x18, 0x02, 0x20, 0x03, 0x28, 0x01, 0x52, 0x09, 0x65, 0x78, 0x74,
	0x72, 0x69, 0x6e, 0x73, 0x69, 0x63, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x72, 0x69, 0x6e,
	0x73, 0x69, 0x63, 0x18, 0x03, 0x20, 0x03, 0x28, 0x01, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x72, 0x69,
	0x6e, 0x73, 0x69, 0x63, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x03, 0x28, 0x01, 0x52, 0x0a, 0x64, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x22, 0x29, 0x0a, 0x0d, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xba, 0x02, 0x0a, 0x0b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x46,
	0x69, 0x6c, 0x65, 0x56, 0x32, 0x12, 0x2e, 0x0a, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52,
	0x04, 0x6d, 0x65, 0x74, 0x61, 0x12, 0x2f, 0x0a, 0x05, 0x6c, 0x69, 0x64, 0x61, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x64, 0x61, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x32, 0x52,
	0x05, 0x6c, 0x69, 0x64, 0x61, 0x72, 0x12, 0x31, 0x0a, 0x06, 0x6c, 0x69, 0x64, 0x61, 0x72, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65,
	0x64, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x64, 0x61, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56,
	0x32, 0x52, 0x06, 0x6c, 0x69, 0x64, 0x61, 0x72, 0x73, 0x12, 0x3f, 0x0a, 0x07, 0x63, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x46, 0x69,
	0x6c, 0x65, 0x56, 0x32, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x07, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x1a, 0x56, 0x0a, 0x0c, 0x43, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x32, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x42, 0x4a, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x39, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65,
	0x65, 0x64, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_annofeed_v1_param_proto_rawDescOnce sync.Once
	file_annofeed_v1_param_proto_rawDescData = file_annofeed_v1_param_proto_rawDesc
)

func file_annofeed_v1_param_proto_rawDescGZIP() []byte {
	file_annofeed_v1_param_proto_rawDescOnce.Do(func() {
		file_annofeed_v1_param_proto_rawDescData = protoimpl.X.CompressGZIP(file_annofeed_v1_param_proto_rawDescData)
	})
	return file_annofeed_v1_param_proto_rawDescData
}

var file_annofeed_v1_param_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_annofeed_v1_param_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_annofeed_v1_param_proto_goTypes = []interface{}{
	(CoordSys_Type_Enum)(0),        // 0: annofeed.v1.CoordSys.Type.Enum
	(*CoordSys)(nil),               // 1: annofeed.v1.CoordSys
	(*Pose)(nil),                   // 2: annofeed.v1.Pose
	(*VehicleParam)(nil),           // 3: annofeed.v1.VehicleParam
	(*LidarParam)(nil),             // 4: annofeed.v1.LidarParam
	(*CameraParam)(nil),            // 5: annofeed.v1.CameraParam
	(*ParamsFile)(nil),             // 6: annofeed.v1.ParamsFile
	(*LidarParamV2)(nil),           // 7: annofeed.v1.LidarParamV2
	(*CameraParamV2)(nil),          // 8: annofeed.v1.CameraParamV2
	(*ParamFileMeta)(nil),          // 9: annofeed.v1.ParamFileMeta
	(*ParamFileV2)(nil),            // 10: annofeed.v1.ParamFileV2
	(*CoordSys_Type)(nil),          // 11: annofeed.v1.CoordSys.Type
	(*CameraParam_Dimensions)(nil), // 12: annofeed.v1.CameraParam.Dimensions
	nil,                            // 13: annofeed.v1.ParamsFile.LidarsEntry
	nil,                            // 14: annofeed.v1.ParamsFile.CamerasEntry
	nil,                            // 15: annofeed.v1.ParamsFile.CoordSystemsEntry
	nil,                            // 16: annofeed.v1.ParamFileV2.CamerasEntry
	(*v1.RawdataParam)(nil),        // 17: anno.v1.RawdataParam
}
var file_annofeed_v1_param_proto_depIdxs = []int32{
	0,  // 0: annofeed.v1.CoordSys.type:type_name -> annofeed.v1.CoordSys.Type.Enum
	2,  // 1: annofeed.v1.VehicleParam.pose:type_name -> annofeed.v1.Pose
	2,  // 2: annofeed.v1.LidarParam.pose:type_name -> annofeed.v1.Pose
	17, // 3: annofeed.v1.LidarParam.transforms:type_name -> anno.v1.RawdataParam
	2,  // 4: annofeed.v1.CameraParam.pose:type_name -> annofeed.v1.Pose
	17, // 5: annofeed.v1.CameraParam.transforms:type_name -> anno.v1.RawdataParam
	12, // 6: annofeed.v1.CameraParam.dimensions:type_name -> annofeed.v1.CameraParam.Dimensions
	3,  // 7: annofeed.v1.ParamsFile.vehicle:type_name -> annofeed.v1.VehicleParam
	13, // 8: annofeed.v1.ParamsFile.lidars:type_name -> annofeed.v1.ParamsFile.LidarsEntry
	14, // 9: annofeed.v1.ParamsFile.cameras:type_name -> annofeed.v1.ParamsFile.CamerasEntry
	15, // 10: annofeed.v1.ParamsFile.coord_systems:type_name -> annofeed.v1.ParamsFile.CoordSystemsEntry
	9,  // 11: annofeed.v1.ParamsFile.meta:type_name -> annofeed.v1.ParamFileMeta
	9,  // 12: annofeed.v1.ParamFileV2.meta:type_name -> annofeed.v1.ParamFileMeta
	7,  // 13: annofeed.v1.ParamFileV2.lidar:type_name -> annofeed.v1.LidarParamV2
	7,  // 14: annofeed.v1.ParamFileV2.lidars:type_name -> annofeed.v1.LidarParamV2
	16, // 15: annofeed.v1.ParamFileV2.cameras:type_name -> annofeed.v1.ParamFileV2.CamerasEntry
	4,  // 16: annofeed.v1.ParamsFile.LidarsEntry.value:type_name -> annofeed.v1.LidarParam
	5,  // 17: annofeed.v1.ParamsFile.CamerasEntry.value:type_name -> annofeed.v1.CameraParam
	1,  // 18: annofeed.v1.ParamsFile.CoordSystemsEntry.value:type_name -> annofeed.v1.CoordSys
	8,  // 19: annofeed.v1.ParamFileV2.CamerasEntry.value:type_name -> annofeed.v1.CameraParamV2
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_annofeed_v1_param_proto_init() }
func file_annofeed_v1_param_proto_init() {
	if File_annofeed_v1_param_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_annofeed_v1_param_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoordSys); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_param_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pose); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_param_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VehicleParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_param_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LidarParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_param_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CameraParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_param_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParamsFile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_param_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LidarParamV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_param_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CameraParamV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_param_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParamFileMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_param_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParamFileV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_param_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoordSys_Type); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_param_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CameraParam_Dimensions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_annofeed_v1_param_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_annofeed_v1_param_proto_goTypes,
		DependencyIndexes: file_annofeed_v1_param_proto_depIdxs,
		EnumInfos:         file_annofeed_v1_param_proto_enumTypes,
		MessageInfos:      file_annofeed_v1_param_proto_msgTypes,
	}.Build()
	File_annofeed_v1_param_proto = out.File
	file_annofeed_v1_param_proto_rawDesc = nil
	file_annofeed_v1_param_proto_goTypes = nil
	file_annofeed_v1_param_proto_depIdxs = nil
}
