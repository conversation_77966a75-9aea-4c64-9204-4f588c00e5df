package common

import (
	"context"

	"annout/internal/biz"
)

type Exporter interface {
	Export(ctx context.Context, dir string, reaper *biz.Reaper) error
}

type GenExporter func(cfg string) (Exporter, error)

var exporters = map[string]GenExporter{}

func RegisterGenExporter(name string, exporter GenExporter) {
	exporters[name] = exporter
}

func GetGenExporter(name string) GenExporter {
	return exporters[name]
}
