package pointcloud

import (
	"bufio"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_ParsePLYHeader_ToPCDHeader(t *testing.T) {
	testCases := []struct {
		name              string
		data              string
		expectError       bool
		expectedHeader    *PLYHeader
		expectedPCDHeader string
	}{
		{
			name: "PLY_header_is_valid",
			data: `ply
format ascii 1.0
comment Created in Blender version 4.0.2
element vertex 14
property float x
property float y
property float z
property float nx
property float ny
property float nz
property float s
property float t
element face 6
property list uchar uint vertex_indices
end_header
`,
			expectError: false,
			expectedHeader: &PLYHeader{
				Format:       PLYFormatAscii,
				Version:      1.0,
				Comments:     []string{"Created in Blender version 4.0.2"},
				VertexCount:  14,
				VertexTypes:  []string{"float32", "float32", "float32", "float32", "float32", "float32", "float32", "float32"},
				VertexFields: []string{"x", "y", "z", "nx", "ny", "nz", "s", "t"},
				RGBFlags:     []bool{false, false, false, false, false, false, false, false},
				Bytes:        285,
			},
			expectedPCDHeader: `VERSION 0.7
FIELDS x y z nx ny nz s t
SIZE 4 4 4 4 4 4 4 4
TYPE F F F F F F F F
COUNT 1 1 1 1 1 1 1 1
WIDTH 14
HEIGHT 1
VIEWPOINT 0.0000 0.0000 0.0000 1.0000 0.0000 0.0000 0.0000
POINTS 14
DATA binary
`,
		},
		{
			name: "PLY_header_has_rgb",
			data: `ply
format ascii 1.0
comment Created in Blender version 4.0.2
element vertex 14
property float x
property float y
property float z
property uchar r
property uchar g
property uchar b
property float s
property float t
element face 6
property list uchar uint vertex_indices
end_header
`,
			expectError: false,
			expectedHeader: &PLYHeader{
				Format:       PLYFormatAscii,
				Version:      1.0,
				Comments:     []string{"Created in Blender version 4.0.2"},
				VertexCount:  14,
				VertexTypes:  []string{"float32", "float32", "float32", "uint8", "uint8", "uint8", "float32", "float32"},
				VertexFields: []string{"x", "y", "z", "r", "g", "b", "s", "t"},
				RGBFlags:     []bool{false, false, false, true, true, true, false, false},
				Bytes:        282,
			},
			expectedPCDHeader: `VERSION 0.7
FIELDS x y z s t rgb
SIZE 4 4 4 4 4 4
TYPE F F F F F U
COUNT 1 1 1 1 1 1
WIDTH 14
HEIGHT 1
VIEWPOINT 0.0000 0.0000 0.0000 1.0000 0.0000 0.0000 0.0000
POINTS 14
DATA binary
`,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reader := strings.NewReader(tc.data)
			header, err := ParsePLYHeader(bufio.NewReader(reader))
			if (err != nil) != tc.expectError {
				t.Fatalf("unexpected error: %v", err)
			}
			assert.Equal(t, tc.expectedHeader, header)

			pcdHeader := header.ToPCDHeader()
			assert.Equal(t, tc.expectedPCDHeader, pcdHeader)
		})
	}
}

func Test_filterRGBFields(t *testing.T) {
	fields := []int{1, 2, 3, 4}
	rgbFlags := []bool{true, false, false, true}
	newFields := filterOutRGBFields(fields, rgbFlags)
	assert.Equal(t, []int{2, 3}, newFields)
}

func Test_parseFieldsAndRGB(t *testing.T) {
	fields := []string{"1", "2", "3", "28", "28", "26"}
	fieldNames := []string{"x", "y", "z", "r", "g", "b"}
	rgbFlags := []bool{false, false, false, true, true, true}
	newFields := parseFieldsAndRGB(fields, fieldNames, rgbFlags)
	assert.Equal(t, []string{"1", "2", "3", "4280032282"}, newFields)
}

func Test_parsePointAndRGB(t *testing.T) {
	pointBytes := []byte{1, 2, 3, 26, 26, 28}
	fieldNames := []string{"x", "y", "z", "r", "g", "b"}
	sizes := []int{1, 1, 1, 1, 1, 1}
	rgbFlags := []bool{false, false, false, true, true, true}
	newBytes := parsePointAndRGB(pointBytes, fieldNames, sizes, rgbFlags)
	assert.Equal(t, []byte{1, 2, 3, 28, 26, 26, 0xFF}, newBytes)
}

func Test_TransformPLYToPCD(t *testing.T) {
	testCases := []struct {
		name        string
		fpath       string
		expectError bool
	}{
		{
			name:        "ascii.ply",
			fpath:       "",
			expectError: false,
		},
		{
			name:        "binary.ply",
			fpath:       "",
			expectError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if tc.fpath == "" {
				return
			}

			// 1. transform
			dstPcd := tc.fpath + ".pcd"
			err := TransformPLYToPCD(tc.fpath, dstPcd)
			assert.NoError(t, err)
			// 2. open the generated pcd in CloudCompare
		})
	}
}
