package ecarx

import (
	"strings"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
)

type Params struct {
	// (x,y,z,rx,ry,rz,rw)
	LidarExtrinsic []float64
	// Camera name => (x,y,z,rx,ry,rz,rw)
	CameraExtrinsic map[string][]float64
	// Camera name => (fx,fy,cx,cy)
	CameraIntrinsic map[string][]float64
	// Camera name => (distortion_type, k1,k2,...)
	CameraDistortion map[string][]float64
}

func (o *Params) GetImageTransforms(camName string) []*anno.RawdataParam {
	r := make([]*anno.RawdataParam, 0, 2)
	p := o.CameraIntrinsic[camName]
	r = append(r, &anno.RawdataParam{
		Type:      anno.RawdataParam_Type_intrinsic,
		ColumnCnt: 1,
		Data:      p,
	})
	p = o.CameraDistortion[camName]
	r = append(r, &anno.RawdataParam{
		Type:      anno.RawdataParam_Type_distortion,
		ColumnCnt: 1,
		Data:      p,
	})
	return r
}

func fixCamName(name string) string {
	return "cam_" + strings.ReplaceAll(name, "-", "_")
}
