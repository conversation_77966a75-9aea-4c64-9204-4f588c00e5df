package biz

import (
	"context"
	"time"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"gorm.io/gorm"
)

type Bizgrant struct {
	ID        int64     `json:"id" gorm:"default:null"`
	GrantorID int64     `json:"grantor_id" gorm:"default:null"`
	GranteeID int64     `json:"grantee_id" gorm:"default:null"`
	OrgID     int64     `json:"org_id" gorm:"default:null"`
	Biz       string    `json:"biz" gorm:"default:null"`
	CreatedAt time.Time `json:"created_at" gorm:"default:null"`
}

const BizgrantTableName = "bizgrants"

var (
	bizgrant_             = field.RegObject(&Bizgrant{})
	BizgrantUpdatableFlds = field.NewModel(BizgrantTableName, bizgrant_)

	Bizgrant_GrantorID = field.Sname(&bizgrant_.GrantorID)
	Bizgrant_GranteeID = field.Sname(&bizgrant_.GranteeID)
	Bizgrant_OrgID     = field.Sname(&bizgrant_.OrgID)
	Bizgrant_Biz       = field.Sname(&bizgrant_.Biz)
)

type BizgrantFilter struct {
	GrantorID int64
	GranteeID int64
	OrgID     int64
	Biz       string
}

func (o *BizgrantFilter) Apply(tx Tx) Tx {
	if tx == nil {
		return nil
	}

	tbl := BizgrantTableName + "."
	tx = repo.ApplyFieldFilter(tx, tbl+Bizgrant_GrantorID, o.GrantorID)
	tx = repo.ApplyFieldFilter(tx, tbl+Bizgrant_GranteeID, o.GranteeID)
	tx = repo.ApplyFieldFilter(tx, tbl+Bizgrant_OrgID, o.OrgID)
	tx = repo.ApplyFieldFilter(tx, tbl+Bizgrant_Biz, o.Biz)
	return tx
}

type BizgrantsRepo interface {
	repo.GenericRepo[Bizgrant]

	DeleteByFilter(context.Context, *BizgrantFilter) error
}

type BizgrantsBiz struct {
	repo BizgrantsRepo
	log  *log.Helper
}

func NewBizgrantsBiz(repo BizgrantsRepo, logger log.Logger) *BizgrantsBiz {
	return &BizgrantsBiz{repo: repo, log: log.NewHelper(logger)}
}

func (o *BizgrantsBiz) BatchCreate(ctx context.Context, p []*Bizgrant) (gs []*Bizgrant, err error) {
	err = o.repo.DoTx(ctx, func(ctx context.Context, tx *gorm.DB) error {
		gs, err = o.repo.BatchCreate(ctx, p)
		// TODO: publish event
		// business related permissions are managed in business service
		return err
	})
	return
}

func (o *BizgrantsBiz) DeleteByFilter(ctx context.Context, f *BizgrantFilter) error {
	return o.repo.DeleteByFilter(ctx, f)
}

func (o *BizgrantsBiz) List(ctx context.Context, f *BizgrantFilter, p Pager) (
	grants []*Bizgrant, nextPageToken PageToken, err error) {
	return o.repo.List(ctx, f, p)
}
