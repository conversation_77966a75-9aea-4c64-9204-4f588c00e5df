"import { Namespace, SubjectSet, Context } from '@ory/keto-namespace-types'\n\n// Declare new namespaces as classes that implement `Namespace`\nclass one implements Namespace {\n  related: {\n    // Define relations to other objects here.\n    // Examples:\n    //\n    // parents: (File | Folder)[]\n    // viewers: SubjectSet<Group, \"members\">[]\n  }\n\n  permits = {\n    // Define permissions here. These can be derived from the relations above.\n    // Examples:\n    //\n    // view: (ctx: Context): boolean =>\n    //  this.related.viewers.includes(ctx.subject) ||\n    //  this.related.parents.traverse((p) => p.permits.view(ctx)),\n  }\n}\n"