package main

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"sync"
)

const hashFilename = ".hashes.json"

// DocsHash manages the sha256 hash of each API doc in each app.
type DocsHash struct {
	docsRoot string
	apps     map[string]map[string]string // app => sha256 => rev
	verExp   *regexp.Regexp
	lock     sync.RWMutex
}

func NewDocsHash(docsRoot string) *DocsHash {
	return &DocsHash{
		docsRoot: docsRoot,
		verExp:   regexp.MustCompile(`(\n\s+version\s*:\s*)[^\r\n]+`),
		apps:     make(map[string]map[string]string),
	}
}

func (o *DocsHash) getHashes(app string) (map[string]string, error) {
	o.lock.RLock()
	hashes := o.apps[app]
	o.lock.RUnlock()
	if hashes != nil {
		return hashes, nil
	}

	o.lock.Lock()
	defer o.lock.Unlock()
	hashes = o.apps[app]
	if hashes != nil {
		return hashes, nil
	}

	// load doc hashes of the app from file
	bs, err := os.ReadFile(filepath.Join(o.docsRoot, app, hashFilename))
	if err != nil {
		if errors.Is(err, os.ErrNotExist) {
			hashes = make(map[string]string, 10)
			o.apps[app] = hashes
			return hashes, nil
		}
		return nil, fmt.Errorf("failed to read hash file: %w", err)
	}
	if err = json.Unmarshal(bs, &hashes); err != nil {
		return nil, fmt.Errorf("failed to parse hash file: %w", err)
	}
	o.apps[app] = hashes
	return hashes, nil
}

func (o *DocsHash) FindDoc(app, sha256 string) (string, error) {
	hashes, err := o.getHashes(app)
	if err != nil {
		return "", err
	}
	return hashes[sha256], nil
}

// AddOrMatchDocHash tries to insert a new doc into the hash table.
// sha256 of the doc is caculated by the caller.
// If the hash is already in the table, the corresponding doc is returned immediately.
func (o *DocsHash) AddOrMatchDocHash(app, rev, sha256 string) (matchingRev string, err error) {
	hashes, err := o.getHashes(app)
	if err != nil {
		return
	}
	o.lock.Lock()
	defer o.lock.Unlock()
	if matchingRev = hashes[sha256]; matchingRev != "" {
		return
	}

	hashes[sha256] = rev
	defer func() {
		if err != nil {
			delete(hashes, sha256)
		}
	}()

	bs, err := json.Marshal(hashes)
	if err != nil {
		return "", fmt.Errorf("failed to marshal doc hashes: %w", err)
	}
	return "", os.WriteFile(filepath.Join(o.docsRoot, app, hashFilename), bs, 0644)
}

// AddOrMatchDoc tries to insert a new doc into the hash table.
// If the hash is already in the table, the corresponding doc is returned immediately.
func (o *DocsHash) AddOrMatchDoc(app, rev string) (matchingRev string, err error) {
	// caculate doc sha256 hash
	hash, err := o.CalDocHash(app, rev)
	if err != nil {
		return "", fmt.Errorf("failed to cal doc hash: %w", err)
	}
	return o.AddOrMatchDocHash(app, rev, hash)
}

func (o *DocsHash) CalDocHash(app, rev string) (string, error) {
	// caculate doc sha256 hash
	bs, err := os.ReadFile(filepath.Join(o.docsRoot, app, rev))
	if err != nil {
		return "", fmt.Errorf("failed to read doc: %w", err)
	}

	// ignore version
	bs = o.verExp.ReplaceAll(bs, []byte("$10.0.1"))

	h := sha256.New()
	_, err = h.Write(bs)
	if err != nil {
		return "", fmt.Errorf("failed to calculate doc hash: %w", err)
	}

	return hex.EncodeToString(h.Sum(nil)), nil
}
