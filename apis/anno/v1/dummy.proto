syntax = "proto3";
package anno.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "anno/v1/job.proto";
import "types/filelist.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anno/v1;anno";
option java_multiple_files = true;
option java_package = "anno.v1";

// set reference to inexplicitly referenced data structures, so that
// they can be included in the openapi documentation.
service Dummy {
  rpc Dummy (google.protobuf.Empty) returns (DummyReply) {
    option (google.api.http) = {
      get: "/v1/dummy"
    };
  }
}

message DummyReply {
  Job.ElementData ElementDataRef = 1;
  Job.AnnotationData AnnotationDataRef = 2;
  Job.CommentData CommentDataRef = 3;
  types.Filelist FilelistRef = 4;
}
