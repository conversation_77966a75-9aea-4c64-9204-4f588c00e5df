import os
import shutil
import sys
import numpy as np

sys.path.append(".")
from customer.common import tools


def construct_config(record_dir, output_file):
    config = {
        "data_type": "pointcloud",
        "sensor_params": {},
        "poses": {}
    }
    pose_dir = os.path.join(os.path.dirname(record_dir), 'pose')
    if os.path.exists(os.path.join(pose_dir, 'fast_lio_loop.txt')):
        pose_file = os.path.join(pose_dir, 'fast_lio_loop.txt')
    else:
        pose_file = os.path.join(pose_dir, 'fast_lio.txt')
    lidar_dir = os.path.join(record_dir, 'top_lidar')
    lidars = tools.listdir(lidar_dir)
    lidars = [lidar.rsplit('.', 1)[0] for lidar in lidars]
    with open(pose_file) as f:
        poses = f.readlines()
    poses = [pose for pose in poses if pose.split()[0].startswith(os.path.basename(record_dir))]
    poses = [pose for pose in poses if pose.split()[1] in lidars]
    for pose in sorted(poses):
        pose_arr = np.array(pose.split()[2:], dtype=np.float32)
        config['poses'][pose.split()[1]] = tools.pose_to_mat(pose_arr).T.flatten().tolist()
    tools.write_json_file(config, output_file)


def run(input_dir, output_dir):
    tools.check_dir(output_dir)
    for sub_dir in tools.listdir(input_dir, full_path=True):
        for record_dir in tools.listdir(sub_dir, full_path=True):
            record_name = os.path.basename(record_dir)
            if record_name == 'pose':
                continue

            output_record_dir = os.path.join(output_dir, record_name)
            os.mkdir(output_record_dir)

            output_file = os.path.join(output_dir, record_name, 'config.json')
            construct_config(record_dir, output_file)

            os.mkdir(os.path.join(output_record_dir, 'lidar'))
            cameras = [camera_dir for camera_dir in tools.listdir(record_dir) if camera_dir != 'top_lidar']
            new_cameras = [f"{int(camera.rsplit('_', 1)[-1]):02d}_{camera.rsplit('_', 1)[0]}" for camera in cameras]
            camera_files = dict()
            for idx, camera in enumerate(cameras):
                os.makedirs(os.path.join(output_record_dir, 'camera', new_cameras[idx]))
                camera_files[camera] = tools.listdir(os.path.join(record_dir, camera))
            for pcd_file in tools.listdir(os.path.join(record_dir, 'top_lidar'), full_path=True):
                pcd_file_name = os.path.basename(pcd_file)
                shutil.copyfile(pcd_file, os.path.join(output_record_dir, 'lidar', pcd_file_name))
                for idx, camera in enumerate(cameras):
                    filtered = filter(lambda x: x.startswith(pcd_file_name.rsplit('.', 1)[0]), camera_files[camera])
                    ori_img = os.path.join(record_dir, camera, list(filtered)[0])
                    target_img = os.path.join(output_record_dir, 'camera', new_cameras[idx], pcd_file_name.replace('.pcd', '.webp'))
                    shutil.copyfile(ori_img, target_img)


if __name__ == '__main__':
    args = tools.get_args()
    run(args.input, args.out)
