package main

import (
	"context"
	"fmt"
	"time"

	"annofeed/api/client"
	"annofeed/internal/biz"
	"annofeed/internal/conf"
	"annofeed/internal/data"

	"github.com/go-kratos/kratos/v2/log"
)

type accessMgr struct {
	fileRepo biz.FilesRepo
	dataRepo biz.DatasRepo
	cleanup  func()
	log      *log.Helper
}

func newAccessMgr(bc *conf.Bootstrap, logger log.Logger) *accessMgr {
	dataData, cleanup, err := data.NewData(bc.Data, logger)
	if err != nil {
		panic(fmt.Errorf("failed to create database client: %w", err))
	}

	client.Init(bc)
	return &accessMgr{
		fileRepo: data.NewFilesRepo(dataData, logger),
		dataRepo: data.NewDatasRepo(dataData, logger),
		cleanup:  cleanup,
		log:      log.NewHelper(logger),
	}
}

func (o *accessMgr) Close() {
	o.cleanup()
}

// CreateAllPolicies recreates keto tuples for all objects.
func (o *accessMgr) CreateAllPolicies() error {
	err := o.CreateTuplesForDatas()
	if err != nil {
		return err
	}
	return o.CreateTuplesForFiles()
}

func (o *accessMgr) CreateTuplesForDatas() error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	count, err := o.dataRepo.Count(ctx, &biz.DataListFilter{})
	cancel()
	if err != nil {
		return fmt.Errorf("failed to query data count: %w", err)
	}
	o.log.Infof("CreateTuplesForDatas: find %v datas", count)

	for page, pagesz := 0, 1000; ; page++ {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		datas, err := o.dataRepo.ListByFilter(ctx, &biz.DataListFilter{}, biz.Pager{Pagesz: pagesz, Page: page})
		cancel()
		if err != nil {
			return fmt.Errorf("failed to query data list: %w", err)
		}
		if len(datas) == 0 {
			break
		}

		for _, t := range datas {
			ctx, cancel = context.WithTimeout(context.Background(), 10*time.Second)
			err = o.CreateTuplesForData(ctx, t)
			cancel()
			if err != nil {
				return fmt.Errorf("failed to add data tuples: %w", err)
			}
		}
		o.log.Infof("CreateTuplesForDatas: finished %v datas", page*pagesz+len(datas))
	}
	o.log.Infof("CreateTuplesForDatas: finished")
	return nil
}

func (o *accessMgr) CreateTuplesForData(ctx context.Context, p *biz.Data) error {
	return client.CreateAccessPolicies(ctx, biz.PermClsData, p.GetUid(),
		[]string{client.UserScope(p.CreatorUid)}, []string{client.GroupScope(p.OrgUid)})
}

func (o *accessMgr) CreateTuplesForFiles() error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	count, err := o.fileRepo.Count(ctx, &biz.FileListFilter{})
	cancel()
	if err != nil {
		return fmt.Errorf("failed to query file count: %w", err)
	}
	o.log.Infof("CreateTupleForFiles: find %v files", count)

	var (
		files  []*biz.File
		token  biz.PageToken
		pagesz = 1000
		cnt    = 0
	)
	for {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		files, token, err = o.fileRepo.List(ctx, &biz.FileListFilter{}, biz.Pager{Pagesz: pagesz, PageToken: token})
		cancel()
		if err != nil {
			return fmt.Errorf("failed to query file list: %w", err)
		}
		if len(files) == 0 {
			break
		}

		for _, u := range files {
			ctx, cancel = context.WithTimeout(context.Background(), 10*time.Second)
			err = o.CreateTuplesForFile(ctx, u)
			cancel()
			if err != nil {
				return fmt.Errorf("failed to add file tuples: %w", err)
			}
		}
		cnt += len(files)
		o.log.Infof("CreateTupleForFiles: finished %v files", cnt)
		if token == "" {
			break
		}
	}
	o.log.Infof("CreateTupleForFiles: finished")
	return nil
}

func (o *accessMgr) CreateTuplesForFile(ctx context.Context, p *biz.File) error {
	return client.CreateAccessPolicies(ctx, biz.PermClsFile, p.GetUid(),
		[]string{client.UserScope(p.CreatorUid)}, []string{client.GroupScope(p.OrgUid)})
}
