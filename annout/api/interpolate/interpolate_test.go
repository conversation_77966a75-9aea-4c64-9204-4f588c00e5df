package interpolate

import (
	"testing"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"

	"github.com/stretchr/testify/assert"
)

func TestCloneObject(t *testing.T) {
	o1 := &anno.Object{
		Uuid:    "1111",
		TrackId: "car-1",
		Label: &anno.Object_Label{
			Name:  "car",
			Attrs: []*anno.AttrAndValues{},
			Widget: &anno.Object_Widget{
				Name: anno.WidgetName_box2d,
				Data: []float64{200, 200, 100, 100, 100},
			},
		},
	}
	o2 := cloneObject(o1)
	// t.Logf("o2: %+v", o2)
	assert.Equal(t, 36, len(o2.Uuid))
	o2.Label.Widget.Data[0] = 0
	assert.NotEqualValues(t, o1, o2)
}

func TestInterpolate(t *testing.T) {
	o1 := &anno.Object{
		Uuid:    "1111",
		TrackId: "car-1",
		Label: &anno.Object_Label{
			Name:  "car",
			Attrs: []*anno.AttrAndValues{},
			Widget: &anno.Object_Widget{
				Name: anno.WidgetName_box2d,
				Data: []float64{200, 200, 100, 100, 100},
			},
		},
	}
	o2 := testObjFrom(o1, false, []float64{400, 400, 200, 200, 100})

	cases := []struct {
		name     string
		job      *anno.JobAnno
		expCnt   int
		expTotal int
		expJob   *anno.JobAnno
	}{
		{name: "no objects",
			job: &anno.JobAnno{
				NeedInterpolation: true,
				ElementAnnos: []*anno.ElementAnno{
					{RawdataAnnos: []*anno.RawdataAnno{{}}},
				},
			},
			expCnt:   0,
			expTotal: 0,
			expJob: &anno.JobAnno{
				ElementAnnos: []*anno.ElementAnno{
					{RawdataAnnos: []*anno.RawdataAnno{{}}},
				},
			},
		},

		{name: "NO interpolation",
			job: &anno.JobAnno{
				NeedInterpolation: false,
				InsCnt:            2,
				ElementAnnos: []*anno.ElementAnno{
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{cloneObject(o1)}}},
					},
					{RawdataAnnos: []*anno.RawdataAnno{{}}},
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{cloneObject(o2)}}},
					},
				},
			},
			expCnt:   2,
			expTotal: 2,
			expJob: &anno.JobAnno{
				InsCnt: 2,
				ElementAnnos: []*anno.ElementAnno{
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{cloneObject(o1)}}},
					},
					{RawdataAnnos: []*anno.RawdataAnno{{}}},
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{cloneObject(o2)}}},
					},
				},
			},
		},

		{name: "interpolate 1 object between 2 elements",
			job: &anno.JobAnno{
				NeedInterpolation: true,
				InsCnt:            2,
				ElementAnnos: []*anno.ElementAnno{
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{cloneObject(o1)}}},
					},
					{RawdataAnnos: []*anno.RawdataAnno{{}}},
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{cloneObject(o2)}}},
					},
				},
			},
			expCnt:   2,
			expTotal: 3,
			expJob: &anno.JobAnno{
				InsCnt: 3,
				ElementAnnos: []*anno.ElementAnno{
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{cloneObject(o1)}}},
					},
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{testObjFrom(o1, false, []float64{300, 300, 150, 150, 100})}}}},
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{cloneObject(o2)}}},
					},
				},
			},
		},

		{name: "interpolate 1 object between 2 elements, with the object appears since the 2nd elment",
			job: &anno.JobAnno{
				NeedInterpolation: true,
				InsCnt:            2,
				ElementAnnos: []*anno.ElementAnno{
					{RawdataAnnos: []*anno.RawdataAnno{{}}},
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{cloneObject(o1)}}},
					},
					{RawdataAnnos: []*anno.RawdataAnno{{}}},
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{cloneObject(o2)}}},
					},
				},
			},
			expCnt:   2,
			expTotal: 3,
			expJob: &anno.JobAnno{
				InsCnt: 3,
				ElementAnnos: []*anno.ElementAnno{
					{RawdataAnnos: []*anno.RawdataAnno{{}}},
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{cloneObject(o1)}}},
					},
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{testObjFrom(o1, false, []float64{300, 300, 150, 150, 100})}}}},
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{cloneObject(o2)}}},
					},
				},
			},
		},

		{name: "interpolate 2 objects to the end",
			job: &anno.JobAnno{
				NeedInterpolation: true,
				InsCnt:            1,
				ElementAnnos: []*anno.ElementAnno{
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{cloneObject(o1)}}},
					},
					{RawdataAnnos: []*anno.RawdataAnno{{}}},
					{RawdataAnnos: []*anno.RawdataAnno{{}}},
				},
			},
			expCnt:   1,
			expTotal: 3,
			expJob: &anno.JobAnno{
				InsCnt: 3,
				ElementAnnos: []*anno.ElementAnno{
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{cloneObject(o1)}}},
					},
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{cloneObject(o1)}}},
					},
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{cloneObject(o1)}}},
					},
				},
			},
		},

		{name: "NO interpolation due to object disappears",
			job: &anno.JobAnno{
				NeedInterpolation: true,
				InsCnt:            1,
				ElementAnnos: []*anno.ElementAnno{
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{testObjFrom(o1, true, o1.Label.Widget.Data)}}},
					},
					{RawdataAnnos: []*anno.RawdataAnno{{}}},
					{RawdataAnnos: []*anno.RawdataAnno{{}}},
				},
			},
			expCnt:   1,
			expTotal: 1,
			expJob: &anno.JobAnno{
				InsCnt: 1,
				ElementAnnos: []*anno.ElementAnno{
					{
						InsCnt:       1,
						RawdataAnnos: []*anno.RawdataAnno{{Objects: []*anno.Object{testObjFrom(o1, true, o1.Label.Widget.Data)}}},
					},
					{RawdataAnnos: []*anno.RawdataAnno{{}}},
					{RawdataAnnos: []*anno.RawdataAnno{{}}},
				},
			},
		}}

	for _, c := range cases {
		cnt, total := CountIns(c.job)
		assert.Equal(t, c.expCnt, cnt)
		assert.Equal(t, c.expTotal, total)

		Interpolate(c.job)
		testEqualJobs(t, c.name, c.expJob, c.job)

		cnt, total = CountIns(c.job)
		assert.Equal(t, c.expTotal, cnt)
		assert.Equal(t, c.expTotal, total)
	}
}

func testObjFrom(o *anno.Object, vanish bool, data []float64) *anno.Object {
	oo := cloneObject(o)
	oo.VanishLater = vanish
	oo.Label.Widget.Data = data
	return oo
}

func testEqualJobs(t *testing.T, cname string, exp, job *anno.JobAnno) {
	assert.False(t, exp.NeedInterpolation, job.NeedInterpolation, cname)
	assert.Equal(t, exp.InsCnt, job.InsCnt, cname)
	for i, e := range exp.ElementAnnos {
		testEqualElems(t, cname, e, job.ElementAnnos[i])
	}
}

func testEqualElems(t *testing.T, cname string, exp, el *anno.ElementAnno) {
	assert.EqualValues(t, exp.InsCnt, el.InsCnt, cname)
	for k, rd := range exp.RawdataAnnos {
		testEqualRawdatas(t, cname, rd, el.RawdataAnnos[k])
	}
}

func testEqualRawdatas(t *testing.T, cname string, exp, rd *anno.RawdataAnno) {
	for k, o := range exp.Objects {
		assert.EqualValues(t, o.Label.Widget.Data, rd.Objects[k].Label.Widget.Data, cname)
	}
}
