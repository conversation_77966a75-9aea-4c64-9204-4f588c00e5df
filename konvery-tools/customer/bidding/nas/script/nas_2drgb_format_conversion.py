import os
import json
import shutil
import zipfile
import sys

sys.path.append('.')
from customer.common import tools

mac_file = ['.DS_Store', '__MACOSX']


def build_dish_map(config_dir):
    config_data = json.load(open(os.path.join(config_dir, 'config.json')))
    dish_map = {}

    for item in config_data['properties'][0]['class']:
        dish_map[item['pname']] = item['ptitle']

    return dish_map


def run(input_dir, dish_map):
    try:
        file_counter = 0

        # unzip file
        unzip_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_unzip')
        result_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_result')
        if os.path.exists(unzip_dir):
            shutil.rmtree(unzip_dir)
        with zipfile.ZipFile(input_dir, 'r') as zip_ref:
            zip_ref.extractall(unzip_dir)

        try:
            os.mkdir(result_dir)
        except FileExistsError:
            shutil.rmtree(result_dir)
            os.mkdir(result_dir)

        for ds in os.listdir(unzip_dir):
            if ds not in mac_file:
                # ds_bx3w75bczgvwtxfuytzb
                ds_dir = os.path.join(unzip_dir, ds)
                for num_folder in os.listdir(ds_dir):
                    # 2310116624
                    if num_folder not in mac_file:
                        num_folder_dir = os.path.join(ds_dir, num_folder)
                        for chn_folder in os.listdir(num_folder_dir):
                            if chn_folder not in mac_file:
                                # 送标10.10
                                chn_folder_dir = os.path.join(num_folder_dir, chn_folder)
                                for segment in os.listdir(chn_folder_dir):
                                    if segment not in mac_file:
                                        # segmengt1
                                        segment_dir = os.path.join(chn_folder_dir, segment)
                                        result_segment_dir = os.path.join(result_dir, segment)
                                        os.mkdir(result_segment_dir)
                                        for label in os.listdir(segment_dir):
                                            if label not in mac_file:
                                                # label
                                                label_dir = os.path.join(segment_dir, label)
                                                for json_file in os.listdir(label_dir):
                                                    if json_file not in mac_file:
                                                        # load json file
                                                        json_dir = os.path.join(label_dir, json_file)
                                                        json_data = json.load(open(json_dir))

                                                        # build result json
                                                        result_dict = {
                                                            'version': '0.3.3',
                                                            'flags': {},
                                                        }
                                                        shapes_list = []

                                                        for json_label in json_data['labels']:
                                                            shape_content = {
                                                                'label': dish_map[json_label['class']],
                                                                'text': ''
                                                            }
                                                            points_list = []

                                                            for data in json_label['annotation']['data']:
                                                                points_content = [
                                                                    data['x'],
                                                                    data['y']
                                                                ]
                                                                points_list.append(points_content)

                                                            shape_content['points'] = points_list
                                                            shape_content['group_id'] = None
                                                            shape_content['shape_type'] = json_label['annotation']['type']
                                                            shape_content['flags'] = {}
                                                            shapes_list.append(shape_content)

                                                        result_dict['shapes'] = shapes_list
                                                        result_dict['imagePath'] = segment.split('-')[-1] + '\\' + os.path.splitext(json_file)[0] + '.jpg'
                                                        result_dict['imageData'] = None
                                                        result_dict['imageHeight'] = json_data['label_meta']['source_info']['height']
                                                        result_dict['imageWidth'] = json_data['label_meta']['source_info']['width']

                                                        # write as json file
                                                        with open(os.path.join(result_segment_dir, json_file), 'w') as f:
                                                            json.dump(result_dict, f,
                                                                      indent=4,
                                                                      ensure_ascii=False)

                                                        file_counter += 1

        trans_result = {
            "code": 0,
            "output": result_dir,
            "err_msg": '成功',
            "summary": {
                '生成文件数': file_counter,
            }
        }

    except Exception as e:
        trans_result = {
            "code": 1,
            "err_msg": e,
        }

    for key in trans_result:
        print(key + ': ' + str(trans_result[key]))

    print(trans_result)
    return trans_result


if __name__ == "__main__":
    args = tools.get_args()
    dish_map = build_dish_map(args.pcd)
    run(args.input, dish_map)

    # args_input = '/Users/<USER>/Downloads/NAS-拿骚2DRGB图像分割-小菜1_231011a5cfd_验收_已通过_JSON标注结果文件_20231013_3078.zip'
    # run(args_input)

