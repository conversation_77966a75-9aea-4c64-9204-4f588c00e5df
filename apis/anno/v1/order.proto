syntax = "proto3";

package anno.v1;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";
import "iam/v1/type.proto";
import "anno/v1/elemanno.proto";
import "anno/v1/type_lotconfig.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anno/v1;anno";
option java_multiple_files = true;
option java_package = "anno.v1";

service Orders {
  rpc CreateOrder (CreateOrderRequest) returns (Order) {
    option (google.api.http) = {
      post: "/v1/orders"
      body: "*"
    };
  }

  rpc UpdateOrder (UpdateOrderRequest) returns (Order) {
    option (google.api.http) = {
      patch: "/v1/orders/{order.uid}"
      body: "order"
    };
  }

  // only platform admin can cancel orders
  rpc CancelOrder (GetOrderRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/orders/{uid}/cancel"
      body: "*"
    };
  }

  rpc DeleteOrder (DeleteOrderRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/orders/{uid}"
    };
  }

  rpc GetOrder (GetOrderRequest) returns (Order) {
    option (google.api.http) = {
      get: "/v1/orders/{uid}"
    };
  }

  rpc ListOrder (ListOrderRequest) returns (ListOrderReply) {
    option (google.api.http) = {
      get: "/v1/orders"
    };
  }

  rpc SetAnnoResult (SetOrderAnnoResultRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/orders/{uid}/anno-result"
      body: "*"
    };
  }

  rpc GetAnnoResult (GetOrderRequest) returns (GetOrderAnnoResultReply) {
    option (google.api.http) = {
      get: "/v1/orders/{uid}/anno-result"
    };
  }

  rpc ExportOrderAnnos(ExportOrderAnnosRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/orders/{uid}/export-annos"
      body: "*"
    };
  }
}

message Source {
  option (openapi.v3.schema) = {
    required: ["uris"]
  };

  // message File {
  //   string uri = 1;
  // }
  // message OSS {
  //   string provider = 1;
  //   string access_key = 2;
  //   string secret_key = 3;
  //   string bucket = 4;
  //   repeated string dirs = 5;
  // }
  // OSS bucket = 2;

  message Proprietary {
    option (openapi.v3.schema) = {
      required: ["type", "config"]
    };

    // 3rd-party file host service type
    string type = 1;
    // 3rd-party file host service access config
    string config = 2;
  }

  message ParseErrorHandler {
    message Error {
      enum Enum {
        unspecified = 0;
        file_unknown = 1;
        file_missing = 2;
      }
    }

    message Handler {
      enum Enum {
        unspecified = 0;
        fail = 1; // if a rawdata like image has error, fail the total data workflow
        ignore = 2; // if a rawdata like image has error, just ignore this error
      }
    }

    anno.v1.Rawdata.Type.Enum rawdata_type = 1; // image or pcd
    Error.Enum error = 2;
    Handler.Enum handler = 3;
  }

  // package file (.zip) URIs
  repeated string uris = 1;
  // access config when the files are hosted in a third-party platform
  Proprietary proprietary = 2;
  // folder layout style within package files if not conform to Konvery standard
  string style = 3;
  // element type
  Element.Type.Enum elem_type = 4;
  // if source contains consecutive frames
  bool is_frame_series = 5;
  // size of the unpacked data in GB
  int32 plain_size_gb = 6;
  // define parser error handlers; it will fail the parser if no handler is specified.
  // max size is count(RawdataErrorAction.Error) x count(Rawdata.Type).
  repeated ParseErrorHandler error_handlers = 7;
  // whether to automatically parse data in annofeed service
  bool auto_parse = 8;
  // define single file names and their corresponding expected names;
  map<string, string> named_uris = 9;
  // converter is a piece of script to convert the source data to the platform accepted format
  DataConverter converter = 10;
  // metadata about original data, which might be used in parsing data and exporting annos
  map<string, string> metadata = 11;
}

message CreateOrderRequest {
  option (openapi.v3.schema) = {
    required: []
  };

  // mandatory in update-requests
  string uid = 1;
  // mandatory in create-requests;
  // pattern: ^[\\p{Han}\\w\\d_-]{0,256}$
  string name = 2 [(validate.rules).string = {pattern: "^[\\p{Han}\\w\\d_-]{0,256}$"}];
  // order's organization; if empty, this is the requestor's organization
  string org_uid = 3;
  // files attached to the order
  Source source = 4;
}

message UpdateOrderRequest {
  option (openapi.v3.schema) = {
    required: ["order", "fields"]
  };

  // update contents
  Order order = 1;
  // name of fileds to be updated
  repeated string fields = 2;
}

message DeleteOrderRequest {
  // order UID
  string uid = 1;
}

message GetOrderRequest {
  // order UID
  string uid = 1;
}

message ListOrderRequest {
  int32 page = 1;
  int32 pagesz = 2 [(validate.rules).int32 = {gte:0, lte: 100}];

  // filter by orgnization
  string org_uid = 3;
  // filter by creator
  string creator_uid = 4;
  // filter by name pattern
  string name_pattern = 5;

  // filter by order state
  repeated Order.State.Enum states = 6 [(validate.rules).repeated.items.enum = {defined_only: true, not_in: [0]}];
  // include order's organization in the reply
  bool with_org = 7;
}

message ListOrderReply {
  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated Order orders = 2;
  // the organization that the order, at the corresponding position, belongs to.
  repeated iam.v1.BaseUser orgs = 3;
}

message Order {
  option (openapi.v3.schema) = {
    required: ["uid", "name", "org_uid", "state", "created_at"]
  };

  message State {
    enum Enum {
      unspecified = 0;
      // parsing the data specified in the source
      initializing = 1;
      // waiting for the order related lot to be started
      waiting = 2;
      ongoing = 3;
      finished = 4;
      canceled = 5;
      // error occurred when parsing data specified by the source
      failed = 6;
    }
  }

  // order UID
  string uid = 1;
  // order name
  string name = 2;
  // UID of the organization which the order belongs to
  string org_uid = 3;
  // files attached to the order
  Source source = 4;

  // UID of the data associated with the order
  string data_uid = 5;
  // creator UID
  string creator_uid = 6;

  // number of elements in the order
  int32 size = 10;
  // order state
  State.Enum state = 11 [(validate.rules).enum = {defined_only: true}];
  // annotated object count (include interpolated ones); only available after lot is finished
  int32 ins_total = 12;
  // annotation result file URL
  string anno_result_url = 13;
  // when state is failed, this field will contain detailed error message
  string error = 14;

  // order creation time
  google.protobuf.Timestamp created_at = 15;
  // data validation summary
  DataValidationSummary data_summary = 16 [(google.api.field_behavior) = OUTPUT_ONLY];
  // whether the demander can export annos:
  // 1) if false, anno_result_url will be always empty and the demander cannot export annos;
  // 2) if true,
  // 2.1) if anno_result_url is not empty, the demander can use it to download anno result;
  // 2.2) otherwise, the demander can export annos;
  bool can_export_annos = 17 [(google.api.field_behavior) = OUTPUT_ONLY];
}

message SetOrderAnnoResultRequest {
  // order UID
  string uid = 1;
  // URL to the result
  string url = 2 [(validate.rules).string.uri_ref = true];
}

message GetOrderAnnoResultReply {
  // the estimated time when the result is ready;
  // valid only when the order is in the finished state
  google.protobuf.Timestamp will_ready_at = 1;
  // URL to the result if available
  string url = 2;
}

// Note: the messages below should be defined in annofeed and imported in anno.
// To avoid recursively import, we define them in anno and import them in annofeed.

message DataValidationSummary {
  message Error {
    Source.ParseErrorHandler.Error.Enum error = 1;
    int32 elem_index = 2;
    string rawdata_name = 3;
  }

  // total number of validation errors found, includes those unsaved errors
  int32 total_errors = 1;
  // validation errors; only the first 10 errors are saved
  repeated Error errors = 2;
}

message ExportOrderAnnosRequest {
  option (openapi.v3.schema) = {
    required: ["uid"]
  };

  message Option {
    enum Enum {
      unspecified = 0; // all anno results
      finished = 1; // anno result of jobs that have been accepted by customer
    }
  }

  // order UID
  string uid = 1;
  // option
  Option.Enum option = 2;
}
