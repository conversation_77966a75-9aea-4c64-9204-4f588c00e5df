// Copyright 2022 The OpenZipkin Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package model

// Kind clarifies context of timestamp, duration and remoteEndpoint in a span.
type Kind string

// Available Kind values
const (
	Undetermined Kind = ""
	Client       Kind = "CLIENT"
	Server       Kind = "SERVER"
	Producer     Kind = "PRODUCER"
	Consumer     Kind = "CONSUMER"
)
