// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/role.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UpdateRoleRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateRoleRequestMultiError, or nil if none found.
func (m *UpdateRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRole()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRoleRequestValidationError{
					field:  "Role",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRoleRequestValidationError{
					field:  "Role",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRole()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRoleRequestValidationError{
				field:  "Role",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateRoleRequestMultiError(errors)
	}

	return nil
}

// UpdateRoleRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateRoleRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateRoleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateRoleRequestMultiError) AllErrors() []error { return m }

// UpdateRoleRequestValidationError is the validation error returned by
// UpdateRoleRequest.Validate if the designated constraints aren't met.
type UpdateRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateRoleRequestValidationError) ErrorName() string {
	return "UpdateRoleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateRoleRequestValidationError{}

// Validate checks the field values on DeleteRoleRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteRoleRequestMultiError, or nil if none found.
func (m *DeleteRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return DeleteRoleRequestMultiError(errors)
	}

	return nil
}

// DeleteRoleRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteRoleRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteRoleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteRoleRequestMultiError) AllErrors() []error { return m }

// DeleteRoleRequestValidationError is the validation error returned by
// DeleteRoleRequest.Validate if the designated constraints aren't met.
type DeleteRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteRoleRequestValidationError) ErrorName() string {
	return "DeleteRoleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteRoleRequestValidationError{}

// Validate checks the field values on GetRoleRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRoleRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetRoleRequestMultiError,
// or nil if none found.
func (m *GetRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return GetRoleRequestMultiError(errors)
	}

	return nil
}

// GetRoleRequestMultiError is an error wrapping multiple validation errors
// returned by GetRoleRequest.ValidateAll() if the designated constraints
// aren't met.
type GetRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRoleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRoleRequestMultiError) AllErrors() []error { return m }

// GetRoleRequestValidationError is the validation error returned by
// GetRoleRequest.Validate if the designated constraints aren't met.
type GetRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRoleRequestValidationError) ErrorName() string { return "GetRoleRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRoleRequestValidationError{}

// Validate checks the field values on ListRoleRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListRoleRequestMultiError, or nil if none found.
func (m *ListRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListRoleRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for NamePattern

	if len(errors) > 0 {
		return ListRoleRequestMultiError(errors)
	}

	return nil
}

// ListRoleRequestMultiError is an error wrapping multiple validation errors
// returned by ListRoleRequest.ValidateAll() if the designated constraints
// aren't met.
type ListRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListRoleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListRoleRequestMultiError) AllErrors() []error { return m }

// ListRoleRequestValidationError is the validation error returned by
// ListRoleRequest.Validate if the designated constraints aren't met.
type ListRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListRoleRequestValidationError) ErrorName() string { return "ListRoleRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListRoleRequestValidationError{}

// Validate checks the field values on ListRoleReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListRoleReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListRoleReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListRoleReplyMultiError, or
// nil if none found.
func (m *ListRoleReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListRoleReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetRoles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListRoleReplyValidationError{
						field:  fmt.Sprintf("Roles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListRoleReplyValidationError{
						field:  fmt.Sprintf("Roles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListRoleReplyValidationError{
					field:  fmt.Sprintf("Roles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListRoleReplyMultiError(errors)
	}

	return nil
}

// ListRoleReplyMultiError is an error wrapping multiple validation errors
// returned by ListRoleReply.ValidateAll() if the designated constraints
// aren't met.
type ListRoleReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListRoleReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListRoleReplyMultiError) AllErrors() []error { return m }

// ListRoleReplyValidationError is the validation error returned by
// ListRoleReply.Validate if the designated constraints aren't met.
type ListRoleReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListRoleReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListRoleReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListRoleReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListRoleReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListRoleReplyValidationError) ErrorName() string { return "ListRoleReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListRoleReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListRoleReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListRoleReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListRoleReplyValidationError{}

// Validate checks the field values on Role with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Role) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Role with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in RoleMultiError, or nil if none found.
func (m *Role) ValidateAll() error {
	return m.validate(true)
}

func (m *Role) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Langs

	if len(errors) > 0 {
		return RoleMultiError(errors)
	}

	return nil
}

// RoleMultiError is an error wrapping multiple validation errors returned by
// Role.ValidateAll() if the designated constraints aren't met.
type RoleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RoleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RoleMultiError) AllErrors() []error { return m }

// RoleValidationError is the validation error returned by Role.Validate if the
// designated constraints aren't met.
type RoleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RoleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RoleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RoleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RoleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RoleValidationError) ErrorName() string { return "RoleValidationError" }

// Error satisfies the builtin error interface
func (e RoleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRole.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RoleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RoleValidationError{}

// Validate checks the field values on ListUsersRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListUsersRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUsersRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListUsersRoleRequestMultiError, or nil if none found.
func (m *ListUsersRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUsersRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListUsersRoleRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for TeamUid

	if len(errors) > 0 {
		return ListUsersRoleRequestMultiError(errors)
	}

	return nil
}

// ListUsersRoleRequestMultiError is an error wrapping multiple validation
// errors returned by ListUsersRoleRequest.ValidateAll() if the designated
// constraints aren't met.
type ListUsersRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUsersRoleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUsersRoleRequestMultiError) AllErrors() []error { return m }

// ListUsersRoleRequestValidationError is the validation error returned by
// ListUsersRoleRequest.Validate if the designated constraints aren't met.
type ListUsersRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUsersRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUsersRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUsersRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUsersRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUsersRoleRequestValidationError) ErrorName() string {
	return "ListUsersRoleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListUsersRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUsersRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUsersRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUsersRoleRequestValidationError{}

// Validate checks the field values on ListUsersRoleReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListUsersRoleReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUsersRoleReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListUsersRoleReplyMultiError, or nil if none found.
func (m *ListUsersRoleReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUsersRoleReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUsersRoleReplyValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUsersRoleReplyValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUsersRoleReplyValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListUsersRoleReplyMultiError(errors)
	}

	return nil
}

// ListUsersRoleReplyMultiError is an error wrapping multiple validation errors
// returned by ListUsersRoleReply.ValidateAll() if the designated constraints
// aren't met.
type ListUsersRoleReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUsersRoleReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUsersRoleReplyMultiError) AllErrors() []error { return m }

// ListUsersRoleReplyValidationError is the validation error returned by
// ListUsersRoleReply.Validate if the designated constraints aren't met.
type ListUsersRoleReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUsersRoleReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUsersRoleReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUsersRoleReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUsersRoleReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUsersRoleReplyValidationError) ErrorName() string {
	return "ListUsersRoleReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListUsersRoleReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUsersRoleReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUsersRoleReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUsersRoleReplyValidationError{}

// Validate checks the field values on UserRole with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserRole) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserRole with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserRoleMultiError, or nil
// if none found.
func (m *UserRole) ValidateAll() error {
	return m.validate(true)
}

func (m *UserRole) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Role

	if len(errors) > 0 {
		return UserRoleMultiError(errors)
	}

	return nil
}

// UserRoleMultiError is an error wrapping multiple validation errors returned
// by UserRole.ValidateAll() if the designated constraints aren't met.
type UserRoleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserRoleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserRoleMultiError) AllErrors() []error { return m }

// UserRoleValidationError is the validation error returned by
// UserRole.Validate if the designated constraints aren't met.
type UserRoleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserRoleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserRoleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserRoleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserRoleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserRoleValidationError) ErrorName() string { return "UserRoleValidationError" }

// Error satisfies the builtin error interface
func (e UserRoleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserRole.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserRoleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserRoleValidationError{}

// Validate checks the field values on SetUsersRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetUsersRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetUsersRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetUsersRoleRequestMultiError, or nil if none found.
func (m *SetUsersRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetUsersRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetScope()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetUsersRoleRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetUsersRoleRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScope()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetUsersRoleRequestValidationError{
				field:  "Scope",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRoles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SetUsersRoleRequestValidationError{
						field:  fmt.Sprintf("Roles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SetUsersRoleRequestValidationError{
						field:  fmt.Sprintf("Roles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SetUsersRoleRequestValidationError{
					field:  fmt.Sprintf("Roles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SetUsersRoleRequestMultiError(errors)
	}

	return nil
}

// SetUsersRoleRequestMultiError is an error wrapping multiple validation
// errors returned by SetUsersRoleRequest.ValidateAll() if the designated
// constraints aren't met.
type SetUsersRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetUsersRoleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetUsersRoleRequestMultiError) AllErrors() []error { return m }

// SetUsersRoleRequestValidationError is the validation error returned by
// SetUsersRoleRequest.Validate if the designated constraints aren't met.
type SetUsersRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetUsersRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetUsersRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetUsersRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetUsersRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetUsersRoleRequestValidationError) ErrorName() string {
	return "SetUsersRoleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SetUsersRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetUsersRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetUsersRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetUsersRoleRequestValidationError{}

// Validate checks the field values on DeleteUsersRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteUsersRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteUsersRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteUsersRoleRequestMultiError, or nil if none found.
func (m *DeleteUsersRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteUsersRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetScope()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteUsersRoleRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteUsersRoleRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScope()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteUsersRoleRequestValidationError{
				field:  "Scope",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteUsersRoleRequestMultiError(errors)
	}

	return nil
}

// DeleteUsersRoleRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteUsersRoleRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteUsersRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteUsersRoleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteUsersRoleRequestMultiError) AllErrors() []error { return m }

// DeleteUsersRoleRequestValidationError is the validation error returned by
// DeleteUsersRoleRequest.Validate if the designated constraints aren't met.
type DeleteUsersRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteUsersRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteUsersRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteUsersRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteUsersRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteUsersRoleRequestValidationError) ErrorName() string {
	return "DeleteUsersRoleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteUsersRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteUsersRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteUsersRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteUsersRoleRequestValidationError{}

// Validate checks the field values on GetUserRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserRoleRequestMultiError, or nil if none found.
func (m *GetUserRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if all {
		switch v := interface{}(m.GetScope()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserRoleRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserRoleRequestValidationError{
					field:  "Scope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScope()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserRoleRequestValidationError{
				field:  "Scope",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserRoleRequestMultiError(errors)
	}

	return nil
}

// GetUserRoleRequestMultiError is an error wrapping multiple validation errors
// returned by GetUserRoleRequest.ValidateAll() if the designated constraints
// aren't met.
type GetUserRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserRoleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserRoleRequestMultiError) AllErrors() []error { return m }

// GetUserRoleRequestValidationError is the validation error returned by
// GetUserRoleRequest.Validate if the designated constraints aren't met.
type GetUserRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserRoleRequestValidationError) ErrorName() string {
	return "GetUserRoleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserRoleRequestValidationError{}

// Validate checks the field values on GetUserRoleReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetUserRoleReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserRoleReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserRoleReplyMultiError, or nil if none found.
func (m *GetUserRoleReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserRoleReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Role

	if len(errors) > 0 {
		return GetUserRoleReplyMultiError(errors)
	}

	return nil
}

// GetUserRoleReplyMultiError is an error wrapping multiple validation errors
// returned by GetUserRoleReply.ValidateAll() if the designated constraints
// aren't met.
type GetUserRoleReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserRoleReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserRoleReplyMultiError) AllErrors() []error { return m }

// GetUserRoleReplyValidationError is the validation error returned by
// GetUserRoleReply.Validate if the designated constraints aren't met.
type GetUserRoleReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserRoleReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserRoleReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserRoleReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserRoleReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserRoleReplyValidationError) ErrorName() string { return "GetUserRoleReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetUserRoleReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserRoleReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserRoleReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserRoleReplyValidationError{}
