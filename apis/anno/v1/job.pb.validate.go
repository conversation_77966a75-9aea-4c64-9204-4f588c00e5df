// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/job.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateJobRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateJobRequestMultiError, or nil if none found.
func (m *CreateJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_CreateJobRequest_LotUid_Pattern.MatchString(m.GetLotUid()) {
		err := CreateJobRequestValidationError{
			field:  "LotUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for IdxInLot

	// no validation rules for Subtype

	for idx, item := range m.GetElements() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateJobRequestValidationError{
						field:  fmt.Sprintf("Elements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateJobRequestValidationError{
						field:  fmt.Sprintf("Elements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateJobRequestValidationError{
					field:  fmt.Sprintf("Elements[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetAnnotations()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateJobRequestValidationError{
					field:  "Annotations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateJobRequestValidationError{
					field:  "Annotations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnotations()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateJobRequestValidationError{
				field:  "Annotations",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetComments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateJobRequestValidationError{
						field:  fmt.Sprintf("Comments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateJobRequestValidationError{
						field:  fmt.Sprintf("Comments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateJobRequestValidationError{
					field:  fmt.Sprintf("Comments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for State

	// no validation rules for Phase

	if len(errors) > 0 {
		return CreateJobRequestMultiError(errors)
	}

	return nil
}

// CreateJobRequestMultiError is an error wrapping multiple validation errors
// returned by CreateJobRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateJobRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateJobRequestMultiError) AllErrors() []error { return m }

// CreateJobRequestValidationError is the validation error returned by
// CreateJobRequest.Validate if the designated constraints aren't met.
type CreateJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateJobRequestValidationError) ErrorName() string { return "CreateJobRequestValidationError" }

// Error satisfies the builtin error interface
func (e CreateJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateJobRequestValidationError{}

var _CreateJobRequest_LotUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on UpdateJobRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateJobRequestMultiError, or nil if none found.
func (m *UpdateJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetJob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateJobRequestValidationError{
					field:  "Job",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateJobRequestValidationError{
					field:  "Job",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateJobRequestValidationError{
				field:  "Job",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateJobRequestMultiError(errors)
	}

	return nil
}

// UpdateJobRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateJobRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateJobRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateJobRequestMultiError) AllErrors() []error { return m }

// UpdateJobRequestValidationError is the validation error returned by
// UpdateJobRequest.Validate if the designated constraints aren't met.
type UpdateJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateJobRequestValidationError) ErrorName() string { return "UpdateJobRequestValidationError" }

// Error satisfies the builtin error interface
func (e UpdateJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateJobRequestValidationError{}

// Validate checks the field values on DeleteJobRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteJobRequestMultiError, or nil if none found.
func (m *DeleteJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_DeleteJobRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := DeleteJobRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteJobRequestMultiError(errors)
	}

	return nil
}

// DeleteJobRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteJobRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteJobRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteJobRequestMultiError) AllErrors() []error { return m }

// DeleteJobRequestValidationError is the validation error returned by
// DeleteJobRequest.Validate if the designated constraints aren't met.
type DeleteJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteJobRequestValidationError) ErrorName() string { return "DeleteJobRequestValidationError" }

// Error satisfies the builtin error interface
func (e DeleteJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteJobRequestValidationError{}

var _DeleteJobRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on GetJobRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetJobRequestMultiError, or
// nil if none found.
func (m *GetJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetJobRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := GetJobRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Expand

	if len(errors) > 0 {
		return GetJobRequestMultiError(errors)
	}

	return nil
}

// GetJobRequestMultiError is an error wrapping multiple validation errors
// returned by GetJobRequest.ValidateAll() if the designated constraints
// aren't met.
type GetJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobRequestMultiError) AllErrors() []error { return m }

// GetJobRequestValidationError is the validation error returned by
// GetJobRequest.Validate if the designated constraints aren't met.
type GetJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobRequestValidationError) ErrorName() string { return "GetJobRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobRequestValidationError{}

var _GetJobRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on ListJobFilter with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListJobFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListJobFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListJobFilterMultiError, or
// nil if none found.
func (m *ListJobFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *ListJobFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUids() {
		_, _ = idx, item

		if !_ListJobFilter_Uids_Pattern.MatchString(item) {
			err := ListJobFilterValidationError{
				field:  fmt.Sprintf("Uids[%v]", idx),
				reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if !_ListJobFilter_LotUid_Pattern.MatchString(m.GetLotUid()) {
		err := ListJobFilterValidationError{
			field:  "LotUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|^$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPhase() < 0 {
		err := ListJobFilterValidationError{
			field:  "Phase",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for State

	for idx, item := range m.GetLastExecutors() {
		_, _ = idx, item

		if !_ListJobFilter_LastExecutors_Pattern.MatchString(item) {
			err := ListJobFilterValidationError{
				field:  fmt.Sprintf("LastExecutors[%v]", idx),
				reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if !_ListJobFilter_LastExecteam_Pattern.MatchString(m.GetLastExecteam()) {
		err := ListJobFilterValidationError{
			field:  "LastExecteam",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|^$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Jobclip

	if len(errors) > 0 {
		return ListJobFilterMultiError(errors)
	}

	return nil
}

// ListJobFilterMultiError is an error wrapping multiple validation errors
// returned by ListJobFilter.ValidateAll() if the designated constraints
// aren't met.
type ListJobFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListJobFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListJobFilterMultiError) AllErrors() []error { return m }

// ListJobFilterValidationError is the validation error returned by
// ListJobFilter.Validate if the designated constraints aren't met.
type ListJobFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListJobFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListJobFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListJobFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListJobFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListJobFilterValidationError) ErrorName() string { return "ListJobFilterValidationError" }

// Error satisfies the builtin error interface
func (e ListJobFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListJobFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListJobFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListJobFilterValidationError{}

var _ListJobFilter_Uids_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

var _ListJobFilter_LotUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|^$")

var _ListJobFilter_LastExecutors_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

var _ListJobFilter_LastExecteam_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|^$")

// Validate checks the field values on ListJobRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListJobRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListJobRequestMultiError,
// or nil if none found.
func (m *ListJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListJobRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListJobRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListJobRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListJobRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShowLastExecutor

	// no validation rules for FullJob

	// no validation rules for ShowExecutors

	// no validation rules for ElemNamePattern

	if len(errors) > 0 {
		return ListJobRequestMultiError(errors)
	}

	return nil
}

// ListJobRequestMultiError is an error wrapping multiple validation errors
// returned by ListJobRequest.ValidateAll() if the designated constraints
// aren't met.
type ListJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListJobRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListJobRequestMultiError) AllErrors() []error { return m }

// ListJobRequestValidationError is the validation error returned by
// ListJobRequest.Validate if the designated constraints aren't met.
type ListJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListJobRequestValidationError) ErrorName() string { return "ListJobRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListJobRequestValidationError{}

// Validate checks the field values on ListJobReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListJobReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListJobReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListJobReplyMultiError, or
// nil if none found.
func (m *ListJobReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListJobReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetJobs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListJobReplyValidationError{
						field:  fmt.Sprintf("Jobs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListJobReplyValidationError{
						field:  fmt.Sprintf("Jobs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListJobReplyValidationError{
					field:  fmt.Sprintf("Jobs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	{
		sorted_keys := make([]string, len(m.GetExecutors()))
		i := 0
		for key := range m.GetExecutors() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetExecutors()[key]
			_ = val

			// no validation rules for Executors[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ListJobReplyValidationError{
							field:  fmt.Sprintf("Executors[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ListJobReplyValidationError{
							field:  fmt.Sprintf("Executors[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ListJobReplyValidationError{
						field:  fmt.Sprintf("Executors[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return ListJobReplyMultiError(errors)
	}

	return nil
}

// ListJobReplyMultiError is an error wrapping multiple validation errors
// returned by ListJobReply.ValidateAll() if the designated constraints aren't met.
type ListJobReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListJobReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListJobReplyMultiError) AllErrors() []error { return m }

// ListJobReplyValidationError is the validation error returned by
// ListJobReply.Validate if the designated constraints aren't met.
type ListJobReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListJobReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListJobReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListJobReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListJobReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListJobReplyValidationError) ErrorName() string { return "ListJobReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListJobReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListJobReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListJobReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListJobReplyValidationError{}

// Validate checks the field values on GetJoblogRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetJoblogRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJoblogRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJoblogRequestMultiError, or nil if none found.
func (m *GetJoblogRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJoblogRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := GetJoblogRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetJoblogRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := GetJoblogRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetJoblogRequestMultiError(errors)
	}

	return nil
}

// GetJoblogRequestMultiError is an error wrapping multiple validation errors
// returned by GetJoblogRequest.ValidateAll() if the designated constraints
// aren't met.
type GetJoblogRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJoblogRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJoblogRequestMultiError) AllErrors() []error { return m }

// GetJoblogRequestValidationError is the validation error returned by
// GetJoblogRequest.Validate if the designated constraints aren't met.
type GetJoblogRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJoblogRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJoblogRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJoblogRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJoblogRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJoblogRequestValidationError) ErrorName() string { return "GetJoblogRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetJoblogRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJoblogRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJoblogRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJoblogRequestValidationError{}

var _GetJoblogRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on GetJoblogReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetJoblogReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJoblogReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetJoblogReplyMultiError,
// or nil if none found.
func (m *GetJoblogReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJoblogReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetLogs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetJoblogReplyValidationError{
						field:  fmt.Sprintf("Logs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetJoblogReplyValidationError{
						field:  fmt.Sprintf("Logs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetJoblogReplyValidationError{
					field:  fmt.Sprintf("Logs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetJoblogReplyMultiError(errors)
	}

	return nil
}

// GetJoblogReplyMultiError is an error wrapping multiple validation errors
// returned by GetJoblogReply.ValidateAll() if the designated constraints
// aren't met.
type GetJoblogReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJoblogReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJoblogReplyMultiError) AllErrors() []error { return m }

// GetJoblogReplyValidationError is the validation error returned by
// GetJoblogReply.Validate if the designated constraints aren't met.
type GetJoblogReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJoblogReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJoblogReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJoblogReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJoblogReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJoblogReplyValidationError) ErrorName() string { return "GetJoblogReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetJoblogReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJoblogReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJoblogReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJoblogReplyValidationError{}

// Validate checks the field values on GetRawJoblogReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetRawJoblogReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRawJoblogReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRawJoblogReplyMultiError, or nil if none found.
func (m *GetRawJoblogReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRawJoblogReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Logs

	if len(errors) > 0 {
		return GetRawJoblogReplyMultiError(errors)
	}

	return nil
}

// GetRawJoblogReplyMultiError is an error wrapping multiple validation errors
// returned by GetRawJoblogReply.ValidateAll() if the designated constraints
// aren't met.
type GetRawJoblogReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRawJoblogReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRawJoblogReplyMultiError) AllErrors() []error { return m }

// GetRawJoblogReplyValidationError is the validation error returned by
// GetRawJoblogReply.Validate if the designated constraints aren't met.
type GetRawJoblogReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRawJoblogReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRawJoblogReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRawJoblogReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRawJoblogReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRawJoblogReplyValidationError) ErrorName() string {
	return "GetRawJoblogReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetRawJoblogReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRawJoblogReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRawJoblogReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRawJoblogReplyValidationError{}

// Validate checks the field values on AssignJobRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AssignJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssignJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AssignJobRequestMultiError, or nil if none found.
func (m *AssignJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AssignJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_AssignJobRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := AssignJobRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_AssignJobRequest_ExecutorUid_Pattern.MatchString(m.GetExecutorUid()) {
		err := AssignJobRequestValidationError{
			field:  "ExecutorUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|^$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AssignJobRequestMultiError(errors)
	}

	return nil
}

// AssignJobRequestMultiError is an error wrapping multiple validation errors
// returned by AssignJobRequest.ValidateAll() if the designated constraints
// aren't met.
type AssignJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssignJobRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssignJobRequestMultiError) AllErrors() []error { return m }

// AssignJobRequestValidationError is the validation error returned by
// AssignJobRequest.Validate if the designated constraints aren't met.
type AssignJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssignJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssignJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssignJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssignJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssignJobRequestValidationError) ErrorName() string { return "AssignJobRequestValidationError" }

// Error satisfies the builtin error interface
func (e AssignJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssignJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssignJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssignJobRequestValidationError{}

var _AssignJobRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

var _AssignJobRequest_ExecutorUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|^$")

// Validate checks the field values on ClaimJobRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ClaimJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClaimJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClaimJobRequestMultiError, or nil if none found.
func (m *ClaimJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ClaimJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ClaimJobRequest_LotUid_Pattern.MatchString(m.GetLotUid()) {
		err := ClaimJobRequestValidationError{
			field:  "LotUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Subtype

	// no validation rules for Fallback

	// no validation rules for Prefer

	// no validation rules for Renew

	if !_ClaimJobRequest_JobUid_Pattern.MatchString(m.GetJobUid()) {
		err := ClaimJobRequestValidationError{
			field:  "JobUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|^$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ClaimJobRequestMultiError(errors)
	}

	return nil
}

// ClaimJobRequestMultiError is an error wrapping multiple validation errors
// returned by ClaimJobRequest.ValidateAll() if the designated constraints
// aren't met.
type ClaimJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClaimJobRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClaimJobRequestMultiError) AllErrors() []error { return m }

// ClaimJobRequestValidationError is the validation error returned by
// ClaimJobRequest.Validate if the designated constraints aren't met.
type ClaimJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClaimJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClaimJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClaimJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClaimJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClaimJobRequestValidationError) ErrorName() string { return "ClaimJobRequestValidationError" }

// Error satisfies the builtin error interface
func (e ClaimJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClaimJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClaimJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClaimJobRequestValidationError{}

var _ClaimJobRequest_LotUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

var _ClaimJobRequest_JobUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|^$")

// Validate checks the field values on ClaimJobResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ClaimJobResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClaimJobResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClaimJobResponseMultiError, or nil if none found.
func (m *ClaimJobResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ClaimJobResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetJob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClaimJobResponseValidationError{
					field:  "Job",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClaimJobResponseValidationError{
					field:  "Job",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClaimJobResponseValidationError{
				field:  "Job",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ClaimJobResponseMultiError(errors)
	}

	return nil
}

// ClaimJobResponseMultiError is an error wrapping multiple validation errors
// returned by ClaimJobResponse.ValidateAll() if the designated constraints
// aren't met.
type ClaimJobResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClaimJobResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClaimJobResponseMultiError) AllErrors() []error { return m }

// ClaimJobResponseValidationError is the validation error returned by
// ClaimJobResponse.Validate if the designated constraints aren't met.
type ClaimJobResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClaimJobResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClaimJobResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClaimJobResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClaimJobResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClaimJobResponseValidationError) ErrorName() string { return "ClaimJobResponseValidationError" }

// Error satisfies the builtin error interface
func (e ClaimJobResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClaimJobResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClaimJobResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClaimJobResponseValidationError{}

// Validate checks the field values on GiveupJobRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GiveupJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GiveupJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GiveupJobRequestMultiError, or nil if none found.
func (m *GiveupJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GiveupJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GiveupJobRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := GiveupJobRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Reason

	// no validation rules for Details

	if len(errors) > 0 {
		return GiveupJobRequestMultiError(errors)
	}

	return nil
}

// GiveupJobRequestMultiError is an error wrapping multiple validation errors
// returned by GiveupJobRequest.ValidateAll() if the designated constraints
// aren't met.
type GiveupJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GiveupJobRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GiveupJobRequestMultiError) AllErrors() []error { return m }

// GiveupJobRequestValidationError is the validation error returned by
// GiveupJobRequest.Validate if the designated constraints aren't met.
type GiveupJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GiveupJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GiveupJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GiveupJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GiveupJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GiveupJobRequestValidationError) ErrorName() string { return "GiveupJobRequestValidationError" }

// Error satisfies the builtin error interface
func (e GiveupJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGiveupJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GiveupJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GiveupJobRequestValidationError{}

var _GiveupJobRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on SubmitJobRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SubmitJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubmitJobRequestMultiError, or nil if none found.
func (m *SubmitJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_SubmitJobRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := SubmitJobRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAnnotations()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitJobRequestValidationError{
					field:  "Annotations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitJobRequestValidationError{
					field:  "Annotations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnotations()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitJobRequestValidationError{
				field:  "Annotations",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetResolves() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SubmitJobRequestValidationError{
						field:  fmt.Sprintf("Resolves[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SubmitJobRequestValidationError{
						field:  fmt.Sprintf("Resolves[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SubmitJobRequestValidationError{
					field:  fmt.Sprintf("Resolves[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SubmitJobRequestMultiError(errors)
	}

	return nil
}

// SubmitJobRequestMultiError is an error wrapping multiple validation errors
// returned by SubmitJobRequest.ValidateAll() if the designated constraints
// aren't met.
type SubmitJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitJobRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitJobRequestMultiError) AllErrors() []error { return m }

// SubmitJobRequestValidationError is the validation error returned by
// SubmitJobRequest.Validate if the designated constraints aren't met.
type SubmitJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitJobRequestValidationError) ErrorName() string { return "SubmitJobRequestValidationError" }

// Error satisfies the builtin error interface
func (e SubmitJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitJobRequestValidationError{}

var _SubmitJobRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on ReviewJobRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReviewJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewJobRequestMultiError, or nil if none found.
func (m *ReviewJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ReviewJobRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := ReviewJobRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAnnotations()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewJobRequestValidationError{
					field:  "Annotations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewJobRequestValidationError{
					field:  "Annotations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnotations()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewJobRequestValidationError{
				field:  "Annotations",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _ReviewJobRequest_Decision_NotInLookup[m.GetDecision()]; ok {
		err := ReviewJobRequestValidationError{
			field:  "Decision",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := ReviewJobRequest_Decision_Enum_name[int32(m.GetDecision())]; !ok {
		err := ReviewJobRequestValidationError{
			field:  "Decision",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetComments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReviewJobRequestValidationError{
						field:  fmt.Sprintf("Comments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReviewJobRequestValidationError{
						field:  fmt.Sprintf("Comments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReviewJobRequestValidationError{
					field:  fmt.Sprintf("Comments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetResolves() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReviewJobRequestValidationError{
						field:  fmt.Sprintf("Resolves[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReviewJobRequestValidationError{
						field:  fmt.Sprintf("Resolves[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReviewJobRequestValidationError{
					field:  fmt.Sprintf("Resolves[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetUpdatedComments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReviewJobRequestValidationError{
						field:  fmt.Sprintf("UpdatedComments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReviewJobRequestValidationError{
						field:  fmt.Sprintf("UpdatedComments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReviewJobRequestValidationError{
					field:  fmt.Sprintf("UpdatedComments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetDeletedComments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReviewJobRequestValidationError{
						field:  fmt.Sprintf("DeletedComments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReviewJobRequestValidationError{
						field:  fmt.Sprintf("DeletedComments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReviewJobRequestValidationError{
					field:  fmt.Sprintf("DeletedComments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ReviewJobRequestMultiError(errors)
	}

	return nil
}

// ReviewJobRequestMultiError is an error wrapping multiple validation errors
// returned by ReviewJobRequest.ValidateAll() if the designated constraints
// aren't met.
type ReviewJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewJobRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewJobRequestMultiError) AllErrors() []error { return m }

// ReviewJobRequestValidationError is the validation error returned by
// ReviewJobRequest.Validate if the designated constraints aren't met.
type ReviewJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewJobRequestValidationError) ErrorName() string { return "ReviewJobRequestValidationError" }

// Error satisfies the builtin error interface
func (e ReviewJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewJobRequestValidationError{}

var _ReviewJobRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

var _ReviewJobRequest_Decision_NotInLookup = map[ReviewJobRequest_Decision_Enum]struct{}{
	0: {},
}

// Validate checks the field values on ResolveAnnoComment with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResolveAnnoComment) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResolveAnnoComment with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResolveAnnoCommentMultiError, or nil if none found.
func (m *ResolveAnnoComment) ValidateAll() error {
	return m.validate(true)
}

func (m *ResolveAnnoComment) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uuid

	if len(errors) > 0 {
		return ResolveAnnoCommentMultiError(errors)
	}

	return nil
}

// ResolveAnnoCommentMultiError is an error wrapping multiple validation errors
// returned by ResolveAnnoComment.ValidateAll() if the designated constraints
// aren't met.
type ResolveAnnoCommentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResolveAnnoCommentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResolveAnnoCommentMultiError) AllErrors() []error { return m }

// ResolveAnnoCommentValidationError is the validation error returned by
// ResolveAnnoComment.Validate if the designated constraints aren't met.
type ResolveAnnoCommentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResolveAnnoCommentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResolveAnnoCommentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResolveAnnoCommentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResolveAnnoCommentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResolveAnnoCommentValidationError) ErrorName() string {
	return "ResolveAnnoCommentValidationError"
}

// Error satisfies the builtin error interface
func (e ResolveAnnoCommentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResolveAnnoComment.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResolveAnnoCommentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResolveAnnoCommentValidationError{}

// Validate checks the field values on AnnoComment with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AnnoComment) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnnoComment with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AnnoCommentMultiError, or
// nil if none found.
func (m *AnnoComment) ValidateAll() error {
	return m.validate(true)
}

func (m *AnnoComment) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ElemIdx

	// no validation rules for Content

	if all {
		switch v := interface{}(m.GetCommenter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnnoCommentValidationError{
					field:  "Commenter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnnoCommentValidationError{
					field:  "Commenter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommenter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnnoCommentValidationError{
				field:  "Commenter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AddPhase

	// no validation rules for ResolvePhase

	// no validation rules for RdIdx

	if all {
		switch v := interface{}(m.GetExtraInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnnoCommentValidationError{
					field:  "ExtraInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnnoCommentValidationError{
					field:  "ExtraInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExtraInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnnoCommentValidationError{
				field:  "ExtraInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Uuid

	if all {
		switch v := interface{}(m.GetReasons()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnnoCommentValidationError{
					field:  "Reasons",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnnoCommentValidationError{
					field:  "Reasons",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReasons()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnnoCommentValidationError{
				field:  "Reasons",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Scope

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnnoCommentValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnnoCommentValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnnoCommentValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AnnoCommentMultiError(errors)
	}

	return nil
}

// AnnoCommentMultiError is an error wrapping multiple validation errors
// returned by AnnoComment.ValidateAll() if the designated constraints aren't met.
type AnnoCommentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnoCommentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnoCommentMultiError) AllErrors() []error { return m }

// AnnoCommentValidationError is the validation error returned by
// AnnoComment.Validate if the designated constraints aren't met.
type AnnoCommentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnoCommentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnoCommentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnoCommentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnoCommentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnoCommentValidationError) ErrorName() string { return "AnnoCommentValidationError" }

// Error satisfies the builtin error interface
func (e AnnoCommentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnoComment.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnoCommentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnoCommentValidationError{}

// Validate checks the field values on Job with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Job) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Job with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in JobMultiError, or nil if none found.
func (m *Job) ValidateAll() error {
	return m.validate(true)
}

func (m *Job) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_Job_Uid_Pattern.MatchString(m.GetUid()) {
		err := JobValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_Job_LotUid_Pattern.MatchString(m.GetLotUid()) {
		err := JobValidationError{
			field:  "LotUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for IdxInLot

	// no validation rules for Subtype

	for idx, item := range m.GetElements() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, JobValidationError{
						field:  fmt.Sprintf("Elements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, JobValidationError{
						field:  fmt.Sprintf("Elements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return JobValidationError{
					field:  fmt.Sprintf("Elements[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetAnnotations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, JobValidationError{
						field:  fmt.Sprintf("Annotations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, JobValidationError{
						field:  fmt.Sprintf("Annotations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return JobValidationError{
					field:  fmt.Sprintf("Annotations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetComments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, JobValidationError{
						field:  fmt.Sprintf("Comments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, JobValidationError{
						field:  fmt.Sprintf("Comments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return JobValidationError{
					field:  fmt.Sprintf("Comments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for State

	// no validation rules for Cause

	// no validation rules for Phase

	// no validation rules for InsCnt

	// no validation rules for InsTotal

	// no validation rules for ExecutorUid

	// no validation rules for NeedInterpolation

	if all {
		switch v := interface{}(m.GetLastExecutor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "LastExecutor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "LastExecutor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastExecutor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return JobValidationError{
				field:  "LastExecutor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLastExecteam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "LastExecteam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "LastExecteam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastExecteam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return JobValidationError{
				field:  "LastExecteam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ElemsCnt

	for idx, item := range m.GetJobAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, JobValidationError{
						field:  fmt.Sprintf("JobAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, JobValidationError{
						field:  fmt.Sprintf("JobAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return JobValidationError{
					field:  fmt.Sprintf("JobAttrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	{
		sorted_keys := make([]string, len(m.GetCamParams()))
		i := 0
		for key := range m.GetCamParams() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetCamParams()[key]
			_ = val

			// no validation rules for CamParams[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, JobValidationError{
							field:  fmt.Sprintf("CamParams[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, JobValidationError{
							field:  fmt.Sprintf("CamParams[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return JobValidationError{
						field:  fmt.Sprintf("CamParams[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for AnnotationsUrl

	// no validation rules for CommentsUrl

	// no validation rules for JobElemClip

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return JobValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, JobValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return JobValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return JobMultiError(errors)
	}

	return nil
}

// JobMultiError is an error wrapping multiple validation errors returned by
// Job.ValidateAll() if the designated constraints aren't met.
type JobMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JobMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JobMultiError) AllErrors() []error { return m }

// JobValidationError is the validation error returned by Job.Validate if the
// designated constraints aren't met.
type JobValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JobValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JobValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JobValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JobValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JobValidationError) ErrorName() string { return "JobValidationError" }

// Error satisfies the builtin error interface
func (e JobValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJob.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JobValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JobValidationError{}

var _Job_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

var _Job_LotUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on JobAnno with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *JobAnno) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JobAnno with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in JobAnnoMultiError, or nil if none found.
func (m *JobAnno) ValidateAll() error {
	return m.validate(true)
}

func (m *JobAnno) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetElementAnnos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, JobAnnoValidationError{
						field:  fmt.Sprintf("ElementAnnos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, JobAnnoValidationError{
						field:  fmt.Sprintf("ElementAnnos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return JobAnnoValidationError{
					field:  fmt.Sprintf("ElementAnnos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NeedInterpolation

	// no validation rules for InsCnt

	// no validation rules for JobIndex

	for idx, item := range m.GetAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, JobAnnoValidationError{
						field:  fmt.Sprintf("Attrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, JobAnnoValidationError{
						field:  fmt.Sprintf("Attrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return JobAnnoValidationError{
					field:  fmt.Sprintf("Attrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return JobAnnoMultiError(errors)
	}

	return nil
}

// JobAnnoMultiError is an error wrapping multiple validation errors returned
// by JobAnno.ValidateAll() if the designated constraints aren't met.
type JobAnnoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JobAnnoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JobAnnoMultiError) AllErrors() []error { return m }

// JobAnnoValidationError is the validation error returned by JobAnno.Validate
// if the designated constraints aren't met.
type JobAnnoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JobAnnoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JobAnnoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JobAnnoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JobAnnoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JobAnnoValidationError) ErrorName() string { return "JobAnnoValidationError" }

// Error satisfies the builtin error interface
func (e JobAnnoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJobAnno.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JobAnnoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JobAnnoValidationError{}

// Validate checks the field values on BatchRevertJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchRevertJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchRevertJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchRevertJobRequestMultiError, or nil if none found.
func (m *BatchRevertJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchRevertJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchRevertJobRequestValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchRevertJobRequestValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchRevertJobRequestValidationError{
				field:  "Action",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchRevertJobRequestValidationError{
					field:  "Options",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchRevertJobRequestValidationError{
					field:  "Options",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchRevertJobRequestValidationError{
				field:  "Options",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchRevertJobRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchRevertJobRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchRevertJobRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BatchRevertJobRequestMultiError(errors)
	}

	return nil
}

// BatchRevertJobRequestMultiError is an error wrapping multiple validation
// errors returned by BatchRevertJobRequest.ValidateAll() if the designated
// constraints aren't met.
type BatchRevertJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchRevertJobRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchRevertJobRequestMultiError) AllErrors() []error { return m }

// BatchRevertJobRequestValidationError is the validation error returned by
// BatchRevertJobRequest.Validate if the designated constraints aren't met.
type BatchRevertJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchRevertJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchRevertJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchRevertJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchRevertJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchRevertJobRequestValidationError) ErrorName() string {
	return "BatchRevertJobRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchRevertJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchRevertJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchRevertJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchRevertJobRequestValidationError{}

// Validate checks the field values on BatchRevertJobReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchRevertJobReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchRevertJobReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchRevertJobReplyMultiError, or nil if none found.
func (m *BatchRevertJobReply) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchRevertJobReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BatchRevertJobReplyMultiError(errors)
	}

	return nil
}

// BatchRevertJobReplyMultiError is an error wrapping multiple validation
// errors returned by BatchRevertJobReply.ValidateAll() if the designated
// constraints aren't met.
type BatchRevertJobReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchRevertJobReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchRevertJobReplyMultiError) AllErrors() []error { return m }

// BatchRevertJobReplyValidationError is the validation error returned by
// BatchRevertJobReply.Validate if the designated constraints aren't met.
type BatchRevertJobReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchRevertJobReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchRevertJobReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchRevertJobReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchRevertJobReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchRevertJobReplyValidationError) ErrorName() string {
	return "BatchRevertJobReplyValidationError"
}

// Error satisfies the builtin error interface
func (e BatchRevertJobReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchRevertJobReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchRevertJobReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchRevertJobReplyValidationError{}

// Validate checks the field values on GetJobAnnosRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetJobAnnosRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobAnnosRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJobAnnosRequestMultiError, or nil if none found.
func (m *GetJobAnnosRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobAnnosRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := GetJobAnnosRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetJobAnnosRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetJobAnnosRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetJobAnnosRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetJobAnnosRequestMultiError(errors)
	}

	return nil
}

// GetJobAnnosRequestMultiError is an error wrapping multiple validation errors
// returned by GetJobAnnosRequest.ValidateAll() if the designated constraints
// aren't met.
type GetJobAnnosRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobAnnosRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobAnnosRequestMultiError) AllErrors() []error { return m }

// GetJobAnnosRequestValidationError is the validation error returned by
// GetJobAnnosRequest.Validate if the designated constraints aren't met.
type GetJobAnnosRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobAnnosRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobAnnosRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobAnnosRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobAnnosRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobAnnosRequestValidationError) ErrorName() string {
	return "GetJobAnnosRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetJobAnnosRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobAnnosRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobAnnosRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobAnnosRequestValidationError{}

// Validate checks the field values on GetJobAnnosReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetJobAnnosReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobAnnosReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJobAnnosReplyMultiError, or nil if none found.
func (m *GetJobAnnosReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobAnnosReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAnnos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetJobAnnosReplyValidationError{
						field:  fmt.Sprintf("Annos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetJobAnnosReplyValidationError{
						field:  fmt.Sprintf("Annos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetJobAnnosReplyValidationError{
					field:  fmt.Sprintf("Annos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if len(errors) > 0 {
		return GetJobAnnosReplyMultiError(errors)
	}

	return nil
}

// GetJobAnnosReplyMultiError is an error wrapping multiple validation errors
// returned by GetJobAnnosReply.ValidateAll() if the designated constraints
// aren't met.
type GetJobAnnosReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobAnnosReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobAnnosReplyMultiError) AllErrors() []error { return m }

// GetJobAnnosReplyValidationError is the validation error returned by
// GetJobAnnosReply.Validate if the designated constraints aren't met.
type GetJobAnnosReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobAnnosReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobAnnosReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobAnnosReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobAnnosReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobAnnosReplyValidationError) ErrorName() string { return "GetJobAnnosReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetJobAnnosReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobAnnosReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobAnnosReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobAnnosReplyValidationError{}

// Validate checks the field values on SetRawdataEmbeddingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetRawdataEmbeddingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetRawdataEmbeddingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetRawdataEmbeddingRequestMultiError, or nil if none found.
func (m *SetRawdataEmbeddingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetRawdataEmbeddingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_SetRawdataEmbeddingRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := SetRawdataEmbeddingRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ElemIdx

	// no validation rules for RawdataIdx

	if _, err := url.Parse(m.GetEmbeddingUri()); err != nil {
		err = SetRawdataEmbeddingRequestValidationError{
			field:  "EmbeddingUri",
			reason: "value must be a valid URI",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SetRawdataEmbeddingRequestMultiError(errors)
	}

	return nil
}

// SetRawdataEmbeddingRequestMultiError is an error wrapping multiple
// validation errors returned by SetRawdataEmbeddingRequest.ValidateAll() if
// the designated constraints aren't met.
type SetRawdataEmbeddingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetRawdataEmbeddingRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetRawdataEmbeddingRequestMultiError) AllErrors() []error { return m }

// SetRawdataEmbeddingRequestValidationError is the validation error returned
// by SetRawdataEmbeddingRequest.Validate if the designated constraints aren't met.
type SetRawdataEmbeddingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetRawdataEmbeddingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetRawdataEmbeddingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetRawdataEmbeddingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetRawdataEmbeddingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetRawdataEmbeddingRequestValidationError) ErrorName() string {
	return "SetRawdataEmbeddingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SetRawdataEmbeddingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetRawdataEmbeddingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetRawdataEmbeddingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetRawdataEmbeddingRequestValidationError{}

var _SetRawdataEmbeddingRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on SaveJobDraftRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SaveJobDraftRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveJobDraftRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SaveJobDraftRequestMultiError, or nil if none found.
func (m *SaveJobDraftRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveJobDraftRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_SaveJobDraftRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := SaveJobDraftRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Version

	// no validation rules for Draft

	if len(errors) > 0 {
		return SaveJobDraftRequestMultiError(errors)
	}

	return nil
}

// SaveJobDraftRequestMultiError is an error wrapping multiple validation
// errors returned by SaveJobDraftRequest.ValidateAll() if the designated
// constraints aren't met.
type SaveJobDraftRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveJobDraftRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveJobDraftRequestMultiError) AllErrors() []error { return m }

// SaveJobDraftRequestValidationError is the validation error returned by
// SaveJobDraftRequest.Validate if the designated constraints aren't met.
type SaveJobDraftRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveJobDraftRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveJobDraftRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveJobDraftRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveJobDraftRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveJobDraftRequestValidationError) ErrorName() string {
	return "SaveJobDraftRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SaveJobDraftRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveJobDraftRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveJobDraftRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveJobDraftRequestValidationError{}

var _SaveJobDraftRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on SaveJobDraftReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SaveJobDraftReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveJobDraftReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SaveJobDraftReplyMultiError, or nil if none found.
func (m *SaveJobDraftReply) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveJobDraftReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	if len(errors) > 0 {
		return SaveJobDraftReplyMultiError(errors)
	}

	return nil
}

// SaveJobDraftReplyMultiError is an error wrapping multiple validation errors
// returned by SaveJobDraftReply.ValidateAll() if the designated constraints
// aren't met.
type SaveJobDraftReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveJobDraftReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveJobDraftReplyMultiError) AllErrors() []error { return m }

// SaveJobDraftReplyValidationError is the validation error returned by
// SaveJobDraftReply.Validate if the designated constraints aren't met.
type SaveJobDraftReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveJobDraftReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveJobDraftReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveJobDraftReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveJobDraftReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveJobDraftReplyValidationError) ErrorName() string {
	return "SaveJobDraftReplyValidationError"
}

// Error satisfies the builtin error interface
func (e SaveJobDraftReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveJobDraftReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveJobDraftReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveJobDraftReplyValidationError{}

// Validate checks the field values on GetJobDraftRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetJobDraftRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobDraftRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJobDraftRequestMultiError, or nil if none found.
func (m *GetJobDraftRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobDraftRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetJobDraftRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := GetJobDraftRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetJobDraftRequestMultiError(errors)
	}

	return nil
}

// GetJobDraftRequestMultiError is an error wrapping multiple validation errors
// returned by GetJobDraftRequest.ValidateAll() if the designated constraints
// aren't met.
type GetJobDraftRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobDraftRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobDraftRequestMultiError) AllErrors() []error { return m }

// GetJobDraftRequestValidationError is the validation error returned by
// GetJobDraftRequest.Validate if the designated constraints aren't met.
type GetJobDraftRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobDraftRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobDraftRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobDraftRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobDraftRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobDraftRequestValidationError) ErrorName() string {
	return "GetJobDraftRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetJobDraftRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobDraftRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobDraftRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobDraftRequestValidationError{}

var _GetJobDraftRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on GetJobDraftReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetJobDraftReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobDraftReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJobDraftReplyMultiError, or nil if none found.
func (m *GetJobDraftReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobDraftReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	// no validation rules for DraftUrl

	if len(errors) > 0 {
		return GetJobDraftReplyMultiError(errors)
	}

	return nil
}

// GetJobDraftReplyMultiError is an error wrapping multiple validation errors
// returned by GetJobDraftReply.ValidateAll() if the designated constraints
// aren't met.
type GetJobDraftReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobDraftReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobDraftReplyMultiError) AllErrors() []error { return m }

// GetJobDraftReplyValidationError is the validation error returned by
// GetJobDraftReply.Validate if the designated constraints aren't met.
type GetJobDraftReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobDraftReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobDraftReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobDraftReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobDraftReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobDraftReplyValidationError) ErrorName() string { return "GetJobDraftReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetJobDraftReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobDraftReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobDraftReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobDraftReplyValidationError{}

// Validate checks the field values on GetJobLastCommitLogRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetJobLastCommitLogRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobLastCommitLogRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJobLastCommitLogRequestMultiError, or nil if none found.
func (m *GetJobLastCommitLogRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobLastCommitLogRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetJobLastCommitLogRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := GetJobLastCommitLogRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPhase() < 0 {
		err := GetJobLastCommitLogRequestValidationError{
			field:  "Phase",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Direction

	if len(errors) > 0 {
		return GetJobLastCommitLogRequestMultiError(errors)
	}

	return nil
}

// GetJobLastCommitLogRequestMultiError is an error wrapping multiple
// validation errors returned by GetJobLastCommitLogRequest.ValidateAll() if
// the designated constraints aren't met.
type GetJobLastCommitLogRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobLastCommitLogRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobLastCommitLogRequestMultiError) AllErrors() []error { return m }

// GetJobLastCommitLogRequestValidationError is the validation error returned
// by GetJobLastCommitLogRequest.Validate if the designated constraints aren't met.
type GetJobLastCommitLogRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobLastCommitLogRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobLastCommitLogRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobLastCommitLogRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobLastCommitLogRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobLastCommitLogRequestValidationError) ErrorName() string {
	return "GetJobLastCommitLogRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetJobLastCommitLogRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobLastCommitLogRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobLastCommitLogRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobLastCommitLogRequestValidationError{}

var _GetJobLastCommitLogRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on GetJobLastCommitLogReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetJobLastCommitLogReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobLastCommitLogReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJobLastCommitLogReplyMultiError, or nil if none found.
func (m *GetJobLastCommitLogReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobLastCommitLogReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLog()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetJobLastCommitLogReplyValidationError{
					field:  "Log",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetJobLastCommitLogReplyValidationError{
					field:  "Log",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLog()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetJobLastCommitLogReplyValidationError{
				field:  "Log",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetJobLastCommitLogReplyMultiError(errors)
	}

	return nil
}

// GetJobLastCommitLogReplyMultiError is an error wrapping multiple validation
// errors returned by GetJobLastCommitLogReply.ValidateAll() if the designated
// constraints aren't met.
type GetJobLastCommitLogReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobLastCommitLogReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobLastCommitLogReplyMultiError) AllErrors() []error { return m }

// GetJobLastCommitLogReplyValidationError is the validation error returned by
// GetJobLastCommitLogReply.Validate if the designated constraints aren't met.
type GetJobLastCommitLogReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobLastCommitLogReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobLastCommitLogReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobLastCommitLogReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobLastCommitLogReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobLastCommitLogReplyValidationError) ErrorName() string {
	return "GetJobLastCommitLogReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetJobLastCommitLogReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobLastCommitLogReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobLastCommitLogReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobLastCommitLogReplyValidationError{}

// Validate checks the field values on HasHoldingJobsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HasHoldingJobsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HasHoldingJobsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HasHoldingJobsRequestMultiError, or nil if none found.
func (m *HasHoldingJobsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *HasHoldingJobsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrgUid

	if len(errors) > 0 {
		return HasHoldingJobsRequestMultiError(errors)
	}

	return nil
}

// HasHoldingJobsRequestMultiError is an error wrapping multiple validation
// errors returned by HasHoldingJobsRequest.ValidateAll() if the designated
// constraints aren't met.
type HasHoldingJobsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HasHoldingJobsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HasHoldingJobsRequestMultiError) AllErrors() []error { return m }

// HasHoldingJobsRequestValidationError is the validation error returned by
// HasHoldingJobsRequest.Validate if the designated constraints aren't met.
type HasHoldingJobsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HasHoldingJobsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HasHoldingJobsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HasHoldingJobsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HasHoldingJobsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HasHoldingJobsRequestValidationError) ErrorName() string {
	return "HasHoldingJobsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e HasHoldingJobsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHasHoldingJobsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HasHoldingJobsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HasHoldingJobsRequestValidationError{}

// Validate checks the field values on HasHoldingJobsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HasHoldingJobsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HasHoldingJobsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HasHoldingJobsReplyMultiError, or nil if none found.
func (m *HasHoldingJobsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *HasHoldingJobsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Holding

	if len(errors) > 0 {
		return HasHoldingJobsReplyMultiError(errors)
	}

	return nil
}

// HasHoldingJobsReplyMultiError is an error wrapping multiple validation
// errors returned by HasHoldingJobsReply.ValidateAll() if the designated
// constraints aren't met.
type HasHoldingJobsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HasHoldingJobsReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HasHoldingJobsReplyMultiError) AllErrors() []error { return m }

// HasHoldingJobsReplyValidationError is the validation error returned by
// HasHoldingJobsReply.Validate if the designated constraints aren't met.
type HasHoldingJobsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HasHoldingJobsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HasHoldingJobsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HasHoldingJobsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HasHoldingJobsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HasHoldingJobsReplyValidationError) ErrorName() string {
	return "HasHoldingJobsReplyValidationError"
}

// Error satisfies the builtin error interface
func (e HasHoldingJobsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHasHoldingJobsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HasHoldingJobsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HasHoldingJobsReplyValidationError{}

// Validate checks the field values on SkipAnnotationRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SkipAnnotationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SkipAnnotationRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SkipAnnotationRequestMultiError, or nil if none found.
func (m *SkipAnnotationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SkipAnnotationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for LotId

	// no validation rules for SkipAnnotation

	if len(errors) > 0 {
		return SkipAnnotationRequestMultiError(errors)
	}

	return nil
}

// SkipAnnotationRequestMultiError is an error wrapping multiple validation
// errors returned by SkipAnnotationRequest.ValidateAll() if the designated
// constraints aren't met.
type SkipAnnotationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SkipAnnotationRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SkipAnnotationRequestMultiError) AllErrors() []error { return m }

// SkipAnnotationRequestValidationError is the validation error returned by
// SkipAnnotationRequest.Validate if the designated constraints aren't met.
type SkipAnnotationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SkipAnnotationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SkipAnnotationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SkipAnnotationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SkipAnnotationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SkipAnnotationRequestValidationError) ErrorName() string {
	return "SkipAnnotationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SkipAnnotationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSkipAnnotationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SkipAnnotationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SkipAnnotationRequestValidationError{}

// Validate checks the field values on SkipAnnotationReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SkipAnnotationReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SkipAnnotationReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SkipAnnotationReplyMultiError, or nil if none found.
func (m *SkipAnnotationReply) ValidateAll() error {
	return m.validate(true)
}

func (m *SkipAnnotationReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SkipAnnotation

	if len(errors) > 0 {
		return SkipAnnotationReplyMultiError(errors)
	}

	return nil
}

// SkipAnnotationReplyMultiError is an error wrapping multiple validation
// errors returned by SkipAnnotationReply.ValidateAll() if the designated
// constraints aren't met.
type SkipAnnotationReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SkipAnnotationReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SkipAnnotationReplyMultiError) AllErrors() []error { return m }

// SkipAnnotationReplyValidationError is the validation error returned by
// SkipAnnotationReply.Validate if the designated constraints aren't met.
type SkipAnnotationReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SkipAnnotationReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SkipAnnotationReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SkipAnnotationReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SkipAnnotationReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SkipAnnotationReplyValidationError) ErrorName() string {
	return "SkipAnnotationReplyValidationError"
}

// Error satisfies the builtin error interface
func (e SkipAnnotationReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSkipAnnotationReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SkipAnnotationReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SkipAnnotationReplyValidationError{}

// Validate checks the field values on GetSkipAnnotationRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSkipAnnotationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSkipAnnotationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSkipAnnotationRequestMultiError, or nil if none found.
func (m *GetSkipAnnotationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSkipAnnotationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return GetSkipAnnotationRequestMultiError(errors)
	}

	return nil
}

// GetSkipAnnotationRequestMultiError is an error wrapping multiple validation
// errors returned by GetSkipAnnotationRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSkipAnnotationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSkipAnnotationRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSkipAnnotationRequestMultiError) AllErrors() []error { return m }

// GetSkipAnnotationRequestValidationError is the validation error returned by
// GetSkipAnnotationRequest.Validate if the designated constraints aren't met.
type GetSkipAnnotationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSkipAnnotationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSkipAnnotationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSkipAnnotationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSkipAnnotationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSkipAnnotationRequestValidationError) ErrorName() string {
	return "GetSkipAnnotationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSkipAnnotationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSkipAnnotationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSkipAnnotationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSkipAnnotationRequestValidationError{}

// Validate checks the field values on GetSkipAnnotationReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSkipAnnotationReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSkipAnnotationReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSkipAnnotationReplyMultiError, or nil if none found.
func (m *GetSkipAnnotationReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSkipAnnotationReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SkipAnnotation

	if len(errors) > 0 {
		return GetSkipAnnotationReplyMultiError(errors)
	}

	return nil
}

// GetSkipAnnotationReplyMultiError is an error wrapping multiple validation
// errors returned by GetSkipAnnotationReply.ValidateAll() if the designated
// constraints aren't met.
type GetSkipAnnotationReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSkipAnnotationReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSkipAnnotationReplyMultiError) AllErrors() []error { return m }

// GetSkipAnnotationReplyValidationError is the validation error returned by
// GetSkipAnnotationReply.Validate if the designated constraints aren't met.
type GetSkipAnnotationReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSkipAnnotationReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSkipAnnotationReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSkipAnnotationReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSkipAnnotationReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSkipAnnotationReplyValidationError) ErrorName() string {
	return "GetSkipAnnotationReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetSkipAnnotationReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSkipAnnotationReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSkipAnnotationReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSkipAnnotationReplyValidationError{}

// Validate checks the field values on UpdateJobRequest_Updates with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateJobRequest_Updates) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateJobRequest_Updates with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateJobRequest_UpdatesMultiError, or nil if none found.
func (m *UpdateJobRequest_Updates) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateJobRequest_Updates) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_UpdateJobRequest_Updates_Uid_Pattern.MatchString(m.GetUid()) {
		err := UpdateJobRequest_UpdatesValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UpdateJobRequest_UpdatesMultiError(errors)
	}

	return nil
}

// UpdateJobRequest_UpdatesMultiError is an error wrapping multiple validation
// errors returned by UpdateJobRequest_Updates.ValidateAll() if the designated
// constraints aren't met.
type UpdateJobRequest_UpdatesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateJobRequest_UpdatesMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateJobRequest_UpdatesMultiError) AllErrors() []error { return m }

// UpdateJobRequest_UpdatesValidationError is the validation error returned by
// UpdateJobRequest_Updates.Validate if the designated constraints aren't met.
type UpdateJobRequest_UpdatesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateJobRequest_UpdatesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateJobRequest_UpdatesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateJobRequest_UpdatesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateJobRequest_UpdatesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateJobRequest_UpdatesValidationError) ErrorName() string {
	return "UpdateJobRequest_UpdatesValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateJobRequest_UpdatesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateJobRequest_Updates.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateJobRequest_UpdatesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateJobRequest_UpdatesValidationError{}

var _UpdateJobRequest_Updates_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on GetJoblogReply_Log with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetJoblogReply_Log) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJoblogReply_Log with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJoblogReply_LogMultiError, or nil if none found.
func (m *GetJoblogReply_Log) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJoblogReply_Log) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetOperator()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetJoblogReply_LogValidationError{
					field:  "Operator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetJoblogReply_LogValidationError{
					field:  "Operator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOperator()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetJoblogReply_LogValidationError{
				field:  "Operator",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Action

	if all {
		switch v := interface{}(m.GetDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetJoblogReply_LogValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetJoblogReply_LogValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetJoblogReply_LogValidationError{
				field:  "Details",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FromPhase

	// no validation rules for ToPhase

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetJoblogReply_LogValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetJoblogReply_LogValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetJoblogReply_LogValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetJoblogReply_LogMultiError(errors)
	}

	return nil
}

// GetJoblogReply_LogMultiError is an error wrapping multiple validation errors
// returned by GetJoblogReply_Log.ValidateAll() if the designated constraints
// aren't met.
type GetJoblogReply_LogMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJoblogReply_LogMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJoblogReply_LogMultiError) AllErrors() []error { return m }

// GetJoblogReply_LogValidationError is the validation error returned by
// GetJoblogReply_Log.Validate if the designated constraints aren't met.
type GetJoblogReply_LogValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJoblogReply_LogValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJoblogReply_LogValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJoblogReply_LogValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJoblogReply_LogValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJoblogReply_LogValidationError) ErrorName() string {
	return "GetJoblogReply_LogValidationError"
}

// Error satisfies the builtin error interface
func (e GetJoblogReply_LogValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJoblogReply_Log.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJoblogReply_LogValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJoblogReply_LogValidationError{}

// Validate checks the field values on GetJoblogReply_Log_Action with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetJoblogReply_Log_Action) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJoblogReply_Log_Action with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJoblogReply_Log_ActionMultiError, or nil if none found.
func (m *GetJoblogReply_Log_Action) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJoblogReply_Log_Action) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetJoblogReply_Log_ActionMultiError(errors)
	}

	return nil
}

// GetJoblogReply_Log_ActionMultiError is an error wrapping multiple validation
// errors returned by GetJoblogReply_Log_Action.ValidateAll() if the
// designated constraints aren't met.
type GetJoblogReply_Log_ActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJoblogReply_Log_ActionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJoblogReply_Log_ActionMultiError) AllErrors() []error { return m }

// GetJoblogReply_Log_ActionValidationError is the validation error returned by
// GetJoblogReply_Log_Action.Validate if the designated constraints aren't met.
type GetJoblogReply_Log_ActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJoblogReply_Log_ActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJoblogReply_Log_ActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJoblogReply_Log_ActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJoblogReply_Log_ActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJoblogReply_Log_ActionValidationError) ErrorName() string {
	return "GetJoblogReply_Log_ActionValidationError"
}

// Error satisfies the builtin error interface
func (e GetJoblogReply_Log_ActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJoblogReply_Log_Action.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJoblogReply_Log_ActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJoblogReply_Log_ActionValidationError{}

// Validate checks the field values on GetJoblogReply_Log_GiveupReason with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetJoblogReply_Log_GiveupReason) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJoblogReply_Log_GiveupReason with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetJoblogReply_Log_GiveupReasonMultiError, or nil if none found.
func (m *GetJoblogReply_Log_GiveupReason) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJoblogReply_Log_GiveupReason) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Reason

	// no validation rules for Details

	if len(errors) > 0 {
		return GetJoblogReply_Log_GiveupReasonMultiError(errors)
	}

	return nil
}

// GetJoblogReply_Log_GiveupReasonMultiError is an error wrapping multiple
// validation errors returned by GetJoblogReply_Log_GiveupReason.ValidateAll()
// if the designated constraints aren't met.
type GetJoblogReply_Log_GiveupReasonMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJoblogReply_Log_GiveupReasonMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJoblogReply_Log_GiveupReasonMultiError) AllErrors() []error { return m }

// GetJoblogReply_Log_GiveupReasonValidationError is the validation error
// returned by GetJoblogReply_Log_GiveupReason.Validate if the designated
// constraints aren't met.
type GetJoblogReply_Log_GiveupReasonValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJoblogReply_Log_GiveupReasonValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJoblogReply_Log_GiveupReasonValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJoblogReply_Log_GiveupReasonValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJoblogReply_Log_GiveupReasonValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJoblogReply_Log_GiveupReasonValidationError) ErrorName() string {
	return "GetJoblogReply_Log_GiveupReasonValidationError"
}

// Error satisfies the builtin error interface
func (e GetJoblogReply_Log_GiveupReasonValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJoblogReply_Log_GiveupReason.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJoblogReply_Log_GiveupReasonValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJoblogReply_Log_GiveupReasonValidationError{}

// Validate checks the field values on GetJoblogReply_Log_Details with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetJoblogReply_Log_Details) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJoblogReply_Log_Details with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJoblogReply_Log_DetailsMultiError, or nil if none found.
func (m *GetJoblogReply_Log_Details) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJoblogReply_Log_Details) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAddComments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetJoblogReply_Log_DetailsValidationError{
						field:  fmt.Sprintf("AddComments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetJoblogReply_Log_DetailsValidationError{
						field:  fmt.Sprintf("AddComments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetJoblogReply_Log_DetailsValidationError{
					field:  fmt.Sprintf("AddComments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetResolves() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetJoblogReply_Log_DetailsValidationError{
						field:  fmt.Sprintf("Resolves[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetJoblogReply_Log_DetailsValidationError{
						field:  fmt.Sprintf("Resolves[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetJoblogReply_Log_DetailsValidationError{
					field:  fmt.Sprintf("Resolves[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetGiveupReason()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetJoblogReply_Log_DetailsValidationError{
					field:  "GiveupReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetJoblogReply_Log_DetailsValidationError{
					field:  "GiveupReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGiveupReason()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetJoblogReply_Log_DetailsValidationError{
				field:  "GiveupReason",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetJoblogReply_Log_DetailsMultiError(errors)
	}

	return nil
}

// GetJoblogReply_Log_DetailsMultiError is an error wrapping multiple
// validation errors returned by GetJoblogReply_Log_Details.ValidateAll() if
// the designated constraints aren't met.
type GetJoblogReply_Log_DetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJoblogReply_Log_DetailsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJoblogReply_Log_DetailsMultiError) AllErrors() []error { return m }

// GetJoblogReply_Log_DetailsValidationError is the validation error returned
// by GetJoblogReply_Log_Details.Validate if the designated constraints aren't met.
type GetJoblogReply_Log_DetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJoblogReply_Log_DetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJoblogReply_Log_DetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJoblogReply_Log_DetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJoblogReply_Log_DetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJoblogReply_Log_DetailsValidationError) ErrorName() string {
	return "GetJoblogReply_Log_DetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetJoblogReply_Log_DetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJoblogReply_Log_Details.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJoblogReply_Log_DetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJoblogReply_Log_DetailsValidationError{}

// Validate checks the field values on ClaimJobRequest_Prefer with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ClaimJobRequest_Prefer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClaimJobRequest_Prefer with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClaimJobRequest_PreferMultiError, or nil if none found.
func (m *ClaimJobRequest_Prefer) ValidateAll() error {
	return m.validate(true)
}

func (m *ClaimJobRequest_Prefer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ClaimJobRequest_PreferMultiError(errors)
	}

	return nil
}

// ClaimJobRequest_PreferMultiError is an error wrapping multiple validation
// errors returned by ClaimJobRequest_Prefer.ValidateAll() if the designated
// constraints aren't met.
type ClaimJobRequest_PreferMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClaimJobRequest_PreferMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClaimJobRequest_PreferMultiError) AllErrors() []error { return m }

// ClaimJobRequest_PreferValidationError is the validation error returned by
// ClaimJobRequest_Prefer.Validate if the designated constraints aren't met.
type ClaimJobRequest_PreferValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClaimJobRequest_PreferValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClaimJobRequest_PreferValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClaimJobRequest_PreferValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClaimJobRequest_PreferValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClaimJobRequest_PreferValidationError) ErrorName() string {
	return "ClaimJobRequest_PreferValidationError"
}

// Error satisfies the builtin error interface
func (e ClaimJobRequest_PreferValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClaimJobRequest_Prefer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClaimJobRequest_PreferValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClaimJobRequest_PreferValidationError{}

// Validate checks the field values on ReviewJobRequest_Decision with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReviewJobRequest_Decision) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewJobRequest_Decision with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewJobRequest_DecisionMultiError, or nil if none found.
func (m *ReviewJobRequest_Decision) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewJobRequest_Decision) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ReviewJobRequest_DecisionMultiError(errors)
	}

	return nil
}

// ReviewJobRequest_DecisionMultiError is an error wrapping multiple validation
// errors returned by ReviewJobRequest_Decision.ValidateAll() if the
// designated constraints aren't met.
type ReviewJobRequest_DecisionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewJobRequest_DecisionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewJobRequest_DecisionMultiError) AllErrors() []error { return m }

// ReviewJobRequest_DecisionValidationError is the validation error returned by
// ReviewJobRequest_Decision.Validate if the designated constraints aren't met.
type ReviewJobRequest_DecisionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewJobRequest_DecisionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewJobRequest_DecisionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewJobRequest_DecisionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewJobRequest_DecisionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewJobRequest_DecisionValidationError) ErrorName() string {
	return "ReviewJobRequest_DecisionValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewJobRequest_DecisionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewJobRequest_Decision.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewJobRequest_DecisionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewJobRequest_DecisionValidationError{}

// Validate checks the field values on AnnoComment_ExtraInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AnnoComment_ExtraInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnnoComment_ExtraInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AnnoComment_ExtraInfoMultiError, or nil if none found.
func (m *AnnoComment_ExtraInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AnnoComment_ExtraInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AnnoComment_ExtraInfoMultiError(errors)
	}

	return nil
}

// AnnoComment_ExtraInfoMultiError is an error wrapping multiple validation
// errors returned by AnnoComment_ExtraInfo.ValidateAll() if the designated
// constraints aren't met.
type AnnoComment_ExtraInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnoComment_ExtraInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnoComment_ExtraInfoMultiError) AllErrors() []error { return m }

// AnnoComment_ExtraInfoValidationError is the validation error returned by
// AnnoComment_ExtraInfo.Validate if the designated constraints aren't met.
type AnnoComment_ExtraInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnoComment_ExtraInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnoComment_ExtraInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnoComment_ExtraInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnoComment_ExtraInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnoComment_ExtraInfoValidationError) ErrorName() string {
	return "AnnoComment_ExtraInfoValidationError"
}

// Error satisfies the builtin error interface
func (e AnnoComment_ExtraInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnoComment_ExtraInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnoComment_ExtraInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnoComment_ExtraInfoValidationError{}

// Validate checks the field values on AnnoComment_Scope with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AnnoComment_Scope) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnnoComment_Scope with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AnnoComment_ScopeMultiError, or nil if none found.
func (m *AnnoComment_Scope) ValidateAll() error {
	return m.validate(true)
}

func (m *AnnoComment_Scope) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AnnoComment_ScopeMultiError(errors)
	}

	return nil
}

// AnnoComment_ScopeMultiError is an error wrapping multiple validation errors
// returned by AnnoComment_Scope.ValidateAll() if the designated constraints
// aren't met.
type AnnoComment_ScopeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnoComment_ScopeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnoComment_ScopeMultiError) AllErrors() []error { return m }

// AnnoComment_ScopeValidationError is the validation error returned by
// AnnoComment_Scope.Validate if the designated constraints aren't met.
type AnnoComment_ScopeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnoComment_ScopeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnoComment_ScopeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnoComment_ScopeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnoComment_ScopeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnoComment_ScopeValidationError) ErrorName() string {
	return "AnnoComment_ScopeValidationError"
}

// Error satisfies the builtin error interface
func (e AnnoComment_ScopeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnoComment_Scope.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnoComment_ScopeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnoComment_ScopeValidationError{}

// Validate checks the field values on Job_State with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Job_State) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Job_State with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Job_StateMultiError, or nil
// if none found.
func (m *Job_State) ValidateAll() error {
	return m.validate(true)
}

func (m *Job_State) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Job_StateMultiError(errors)
	}

	return nil
}

// Job_StateMultiError is an error wrapping multiple validation errors returned
// by Job_State.ValidateAll() if the designated constraints aren't met.
type Job_StateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Job_StateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Job_StateMultiError) AllErrors() []error { return m }

// Job_StateValidationError is the validation error returned by
// Job_State.Validate if the designated constraints aren't met.
type Job_StateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Job_StateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Job_StateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Job_StateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Job_StateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Job_StateValidationError) ErrorName() string { return "Job_StateValidationError" }

// Error satisfies the builtin error interface
func (e Job_StateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJob_State.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Job_StateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Job_StateValidationError{}

// Validate checks the field values on Job_CamParam with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Job_CamParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Job_CamParam with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Job_CamParamMultiError, or
// nil if none found.
func (m *Job_CamParam) ValidateAll() error {
	return m.validate(true)
}

func (m *Job_CamParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Job_CamParamMultiError(errors)
	}

	return nil
}

// Job_CamParamMultiError is an error wrapping multiple validation errors
// returned by Job_CamParam.ValidateAll() if the designated constraints aren't met.
type Job_CamParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Job_CamParamMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Job_CamParamMultiError) AllErrors() []error { return m }

// Job_CamParamValidationError is the validation error returned by
// Job_CamParam.Validate if the designated constraints aren't met.
type Job_CamParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Job_CamParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Job_CamParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Job_CamParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Job_CamParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Job_CamParamValidationError) ErrorName() string { return "Job_CamParamValidationError" }

// Error satisfies the builtin error interface
func (e Job_CamParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJob_CamParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Job_CamParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Job_CamParamValidationError{}

// Validate checks the field values on Job_ElementData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *Job_ElementData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Job_ElementData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Job_ElementDataMultiError, or nil if none found.
func (m *Job_ElementData) ValidateAll() error {
	return m.validate(true)
}

func (m *Job_ElementData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetElements() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, Job_ElementDataValidationError{
						field:  fmt.Sprintf("Elements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, Job_ElementDataValidationError{
						field:  fmt.Sprintf("Elements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return Job_ElementDataValidationError{
					field:  fmt.Sprintf("Elements[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	{
		sorted_keys := make([]string, len(m.GetCamParams()))
		i := 0
		for key := range m.GetCamParams() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetCamParams()[key]
			_ = val

			// no validation rules for CamParams[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, Job_ElementDataValidationError{
							field:  fmt.Sprintf("CamParams[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, Job_ElementDataValidationError{
							field:  fmt.Sprintf("CamParams[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return Job_ElementDataValidationError{
						field:  fmt.Sprintf("CamParams[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return Job_ElementDataMultiError(errors)
	}

	return nil
}

// Job_ElementDataMultiError is an error wrapping multiple validation errors
// returned by Job_ElementData.ValidateAll() if the designated constraints
// aren't met.
type Job_ElementDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Job_ElementDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Job_ElementDataMultiError) AllErrors() []error { return m }

// Job_ElementDataValidationError is the validation error returned by
// Job_ElementData.Validate if the designated constraints aren't met.
type Job_ElementDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Job_ElementDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Job_ElementDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Job_ElementDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Job_ElementDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Job_ElementDataValidationError) ErrorName() string { return "Job_ElementDataValidationError" }

// Error satisfies the builtin error interface
func (e Job_ElementDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJob_ElementData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Job_ElementDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Job_ElementDataValidationError{}

// Validate checks the field values on Job_AnnotationData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Job_AnnotationData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Job_AnnotationData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Job_AnnotationDataMultiError, or nil if none found.
func (m *Job_AnnotationData) ValidateAll() error {
	return m.validate(true)
}

func (m *Job_AnnotationData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetElementAnnos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, Job_AnnotationDataValidationError{
						field:  fmt.Sprintf("ElementAnnos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, Job_AnnotationDataValidationError{
						field:  fmt.Sprintf("ElementAnnos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return Job_AnnotationDataValidationError{
					field:  fmt.Sprintf("ElementAnnos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetJobAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, Job_AnnotationDataValidationError{
						field:  fmt.Sprintf("JobAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, Job_AnnotationDataValidationError{
						field:  fmt.Sprintf("JobAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return Job_AnnotationDataValidationError{
					field:  fmt.Sprintf("JobAttrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return Job_AnnotationDataMultiError(errors)
	}

	return nil
}

// Job_AnnotationDataMultiError is an error wrapping multiple validation errors
// returned by Job_AnnotationData.ValidateAll() if the designated constraints
// aren't met.
type Job_AnnotationDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Job_AnnotationDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Job_AnnotationDataMultiError) AllErrors() []error { return m }

// Job_AnnotationDataValidationError is the validation error returned by
// Job_AnnotationData.Validate if the designated constraints aren't met.
type Job_AnnotationDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Job_AnnotationDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Job_AnnotationDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Job_AnnotationDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Job_AnnotationDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Job_AnnotationDataValidationError) ErrorName() string {
	return "Job_AnnotationDataValidationError"
}

// Error satisfies the builtin error interface
func (e Job_AnnotationDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJob_AnnotationData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Job_AnnotationDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Job_AnnotationDataValidationError{}

// Validate checks the field values on Job_CommentData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *Job_CommentData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Job_CommentData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Job_CommentDataMultiError, or nil if none found.
func (m *Job_CommentData) ValidateAll() error {
	return m.validate(true)
}

func (m *Job_CommentData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetComments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, Job_CommentDataValidationError{
						field:  fmt.Sprintf("Comments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, Job_CommentDataValidationError{
						field:  fmt.Sprintf("Comments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return Job_CommentDataValidationError{
					field:  fmt.Sprintf("Comments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return Job_CommentDataMultiError(errors)
	}

	return nil
}

// Job_CommentDataMultiError is an error wrapping multiple validation errors
// returned by Job_CommentData.ValidateAll() if the designated constraints
// aren't met.
type Job_CommentDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Job_CommentDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Job_CommentDataMultiError) AllErrors() []error { return m }

// Job_CommentDataValidationError is the validation error returned by
// Job_CommentData.Validate if the designated constraints aren't met.
type Job_CommentDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Job_CommentDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Job_CommentDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Job_CommentDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Job_CommentDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Job_CommentDataValidationError) ErrorName() string { return "Job_CommentDataValidationError" }

// Error satisfies the builtin error interface
func (e Job_CommentDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJob_CommentData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Job_CommentDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Job_CommentDataValidationError{}

// Validate checks the field values on BatchRevertJobRequest_Action with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchRevertJobRequest_Action) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchRevertJobRequest_Action with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchRevertJobRequest_ActionMultiError, or nil if none found.
func (m *BatchRevertJobRequest_Action) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchRevertJobRequest_Action) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetToPhase() < 1 {
		err := BatchRevertJobRequest_ActionValidationError{
			field:  "ToPhase",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return BatchRevertJobRequest_ActionMultiError(errors)
	}

	return nil
}

// BatchRevertJobRequest_ActionMultiError is an error wrapping multiple
// validation errors returned by BatchRevertJobRequest_Action.ValidateAll() if
// the designated constraints aren't met.
type BatchRevertJobRequest_ActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchRevertJobRequest_ActionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchRevertJobRequest_ActionMultiError) AllErrors() []error { return m }

// BatchRevertJobRequest_ActionValidationError is the validation error returned
// by BatchRevertJobRequest_Action.Validate if the designated constraints
// aren't met.
type BatchRevertJobRequest_ActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchRevertJobRequest_ActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchRevertJobRequest_ActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchRevertJobRequest_ActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchRevertJobRequest_ActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchRevertJobRequest_ActionValidationError) ErrorName() string {
	return "BatchRevertJobRequest_ActionValidationError"
}

// Error satisfies the builtin error interface
func (e BatchRevertJobRequest_ActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchRevertJobRequest_Action.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchRevertJobRequest_ActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchRevertJobRequest_ActionValidationError{}

// Validate checks the field values on BatchRevertJobRequest_Options with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchRevertJobRequest_Options) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchRevertJobRequest_Options with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BatchRevertJobRequest_OptionsMultiError, or nil if none found.
func (m *BatchRevertJobRequest_Options) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchRevertJobRequest_Options) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KeepAnnos

	// no validation rules for KeepComments

	// no validation rules for ToPreviousExecutor

	if len(errors) > 0 {
		return BatchRevertJobRequest_OptionsMultiError(errors)
	}

	return nil
}

// BatchRevertJobRequest_OptionsMultiError is an error wrapping multiple
// validation errors returned by BatchRevertJobRequest_Options.ValidateAll()
// if the designated constraints aren't met.
type BatchRevertJobRequest_OptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchRevertJobRequest_OptionsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchRevertJobRequest_OptionsMultiError) AllErrors() []error { return m }

// BatchRevertJobRequest_OptionsValidationError is the validation error
// returned by BatchRevertJobRequest_Options.Validate if the designated
// constraints aren't met.
type BatchRevertJobRequest_OptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchRevertJobRequest_OptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchRevertJobRequest_OptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchRevertJobRequest_OptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchRevertJobRequest_OptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchRevertJobRequest_OptionsValidationError) ErrorName() string {
	return "BatchRevertJobRequest_OptionsValidationError"
}

// Error satisfies the builtin error interface
func (e BatchRevertJobRequest_OptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchRevertJobRequest_Options.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchRevertJobRequest_OptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchRevertJobRequest_OptionsValidationError{}

// Validate checks the field values on GetJobLastCommitLogRequest_Direction
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetJobLastCommitLogRequest_Direction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobLastCommitLogRequest_Direction
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetJobLastCommitLogRequest_DirectionMultiError, or nil if none found.
func (m *GetJobLastCommitLogRequest_Direction) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobLastCommitLogRequest_Direction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetJobLastCommitLogRequest_DirectionMultiError(errors)
	}

	return nil
}

// GetJobLastCommitLogRequest_DirectionMultiError is an error wrapping multiple
// validation errors returned by
// GetJobLastCommitLogRequest_Direction.ValidateAll() if the designated
// constraints aren't met.
type GetJobLastCommitLogRequest_DirectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobLastCommitLogRequest_DirectionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobLastCommitLogRequest_DirectionMultiError) AllErrors() []error { return m }

// GetJobLastCommitLogRequest_DirectionValidationError is the validation error
// returned by GetJobLastCommitLogRequest_Direction.Validate if the designated
// constraints aren't met.
type GetJobLastCommitLogRequest_DirectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobLastCommitLogRequest_DirectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobLastCommitLogRequest_DirectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobLastCommitLogRequest_DirectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobLastCommitLogRequest_DirectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobLastCommitLogRequest_DirectionValidationError) ErrorName() string {
	return "GetJobLastCommitLogRequest_DirectionValidationError"
}

// Error satisfies the builtin error interface
func (e GetJobLastCommitLogRequest_DirectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobLastCommitLogRequest_Direction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobLastCommitLogRequest_DirectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobLastCommitLogRequest_DirectionValidationError{}
