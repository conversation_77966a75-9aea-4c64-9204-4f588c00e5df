import os
import shutil
import sys

sys.path.append('.')
from customer.common import tools

code = 0
processed_file_count = 0
unmarked_file_count = 0


def get_code_msg(msg_code):
    msg = "失败"
    if 0 == msg_code:
        msg = "成功"
    return msg


def check_mark_status(data):
    markable = False
    global unmarked_file_count

    if "label_meta" in data.keys():
        if data["label_meta"]["mark_status"] == 0:
            markable = True
    else:
        markable = True

    if not markable:
        unmarked_file_count += 1

    return markable


def get_point_top_bottom(data):
    # point_list = []
    x = data["x"]
    y = data["y"]
    width = data["width"]
    height = data["height"]
    rotate = data["rotate"]

    if rotate != 0:
        print(f"rotate is {rotate} ")

    left_top_x = x - width / 2
    left_top_y = y - height / 2
    right_bottom_x = x + width / 2
    right_bottom_y = y + height / 2

    point_list = [[left_top_x, left_top_y], [right_bottom_x, right_bottom_y]]

    return point_list


def get_image_timestamp(image_name):
    # rear_image1691478235317596284.json
    image_name = image_name.replace(".jpg", "")
    image_name = image_name.split("image")[-1]
    return image_name


def convert(json_file):
    json_data = tools.get_json_data(json_file)
    mark_status = check_mark_status(json_data)

    if not mark_status:
        print(f"{json_file} is not markable!!!")

    source_info = json_data["label_meta"]["source_info"]
    file_name = source_info["file_name"]
    width = source_info["width"]
    height = source_info["height"]

    mark_data = json_data["labels"]
    res_list = []
    res_data = {
        "imagePath": file_name,
        "timestamp": get_image_timestamp(file_name),
        "imageHeight": height,
        "imageWidth": width,
        "flags": {},
        "shapes": res_list
    }
    for elem in mark_data:
        class_name = elem["class"]
        attrs = elem["attrs"]
        object_id = elem["object_id"]
        data = elem["annotation"]["data"]

        points = get_point_top_bottom(data)
        shape = {
            "id": object_id,
            "points": points,
            "shape_type": "rectangle",
            "attributes": {
                "type": class_name
            },
            "tags": {
                "occluded": int(attrs["occluded"][0]),
                "truncated": int(attrs["truncated"][0]),
                "ignore": 0
            }
        }
        res_list.append(shape)

    return res_data


def run(input_dir, output_dir):
    """
       主函数
    """
    global processed_file_count
    global structure_data
    input_dir = tools.unzip(input_dir)
    tools.check_dir(output_dir)

    for label_dir in tools.find_dir_by_pre_name(input_dir, "label"):
        for json_file in tools.get_json_files(label_dir):
            output_file = output_dir + os.sep + json_file.name
            # print(json_file.path)
            data = convert(json_file.path)
            processed_file_count += 1
            tools.write_json_file(data=data, file=output_file, indent=4)

    trans_result = {"code": code, "err_msg": get_code_msg(code), "output": output_dir,
                    "converted_file_count": processed_file_count, "unmarked_file_count": unmarked_file_count}

    print(trans_result)
    return trans_result


"""
    项目： CWB 2D 项目
    对接平台：云测新平台 
    功能： 将云测导出的标注结果，转换成客户要求的格式
    输入： 云测导出的结果文件
    输出： 分包后的数据
    开发： Justin
    https://qcjmmm41aj.feishu.cn/sheets/C1tfspMz1h6OMZtg5gLcN8S8nmf?from=from_copylink
"""
if __name__ == "__main__":

    args = tools.get_args()

    if args.input == "../../input/test":
        args.input = "/Users/<USER>/Downloads/Convert/CWB/2d-test-in"
        # args.pcd = "/Users/<USER>/Downloads/Convert/CWB/保留这个文件-导出时会用到.json"
        args.out = "/Users/<USER>/Downloads/cwb-2d-output"

    run(input_dir=args.input, output_dir=args.out)
