// source: anno/v1/project.proto
package data

import (
	biz "anno/internal/biz"
	context "context"

	log "github.com/go-kratos/kratos/v2/log"
	gorm "gorm.io/gorm"
)

type projectsRepo struct {
	data *Data
	log  *log.Helper
}

func NewProjectsRepo(data *Data, logger log.Logger) biz.ProjectsRepo {
	return &projectsRepo{data: data, log: log.NewHelper(logger)}
}

func (o *projectsRepo) Create(ctx context.Context, p *biz.Project) (*biz.Project, error) {
	return Create(ctx, o.data, p)
}

func (o *projectsRepo) Update(ctx context.Context, p *biz.Project, fldMask *biz.FieldMask) (*biz.Project, error) {
	return Update(ctx, o.data, p, biz.ProjectUpdatableFlds, fldMask)
}

func (o *projectsRepo) loadAssociations(ctx context.Context, data *Data, mod *biz.Project) error {
	return nil
}

func (o *projectsRepo) GetByID(ctx context.Context, id int64) (*biz.Project, error) {
	return GetByID[biz.Project](ctx, o.data, id, o.loadAssociations)
}

func (o *projectsRepo) DeleteByID(ctx context.Context, id int64) error {
	return Delete(ctx, o.data, &biz.Project{ID: id})
}

func (o *projectsRepo) buildListQuery(ctx context.Context, p *biz.ProjectListFilter) *gorm.DB {
	q := o.data.WithCtx(ctx)
	if len(p.IDs) > 0 {
		q = q.Where("id IN ?", p.IDs)
	}
	if p.OrgUid != "" {
		q = q.Where(biz.ProjectSfldOrgUid+" = ?", p.OrgUid)
	}
	if p.CreatorUid != "" {
		q = q.Where(biz.ProjectSfldCreatorUid+" = ?", p.CreatorUid)
	}
	if p.NamePattern != "" {
		q = q.Where(biz.ProjectSfldName+" LIKE ?", "%"+p.NamePattern+"%")
	}
	return q
}

func (o *projectsRepo) List(ctx context.Context, p *biz.ProjectListFilter, pager biz.Pager) ([]*biz.Project, error) {
	datas := []*biz.Project{}
	q := o.buildListQuery(ctx, p).Order("id DESC")
	if len(p.IDs) == 0 {
		q = q.Offset(pager.Offset()).Limit(pager.Pagesz)
	}
	err := q.Find(&datas).Error
	if err != nil {
		return nil, err
	}
	return datas, nil
}

func (o *projectsRepo) Count(ctx context.Context, p *biz.ProjectListFilter) (int, error) {
	mod := &biz.Project{}
	var cnt int64
	q := o.buildListQuery(ctx, p)
	err := q.Model(mod).Count(&cnt).Error
	if err != nil {
		return 0, err
	}
	return int(cnt), nil
}
