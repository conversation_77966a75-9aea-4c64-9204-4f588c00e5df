# Keto权限系统中namespaces的使用与实现分析

## 1. Namespaces的定义与结构

在Keto权限系统中，namespaces是权限模型的核心概念，它通过TypeScript文件（如`namespaces.keto.ts`）定义。每个namespace代表一种资源类型或权限域，例如：

```typescript
class IamRole implements Namespace {
  related: {
    parents: IamGroup[]
    policies: IamPolicy[]
    perms: (IamPerm | SubjectSet<IamRole, "perms">)[]
  }

  permits = {
    has_perm: (ctx: Context, perm: string): boolean => this.related.perms.includes(perm),

    check: (ctx: Context, perm: string): boolean =>
      IamRole:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),
```

每个namespace包含两个主要部分：
1. **关系定义（related）**：定义与其他namespace的关系
2. **权限检查函数（permits）**：定义权限检查的逻辑

## 2. Namespaces到AST的转换过程

当系统启动时，会执行以下步骤将namespaces转换为AST：

1. **读取配置文件**：从配置文件（如`config/keto.yml`）中读取namespace文件位置
2. **解析namespace文件**：
   - 系统调用`readNamespaceFile`函数读取文件内容
   - 使用TypeScript解析器（通过`schema/parser.go`）解析文件内容
   - 将解析结果转换为内部namespace对象和AST

```go
func Parse(input string) ([]namespace, []*ParseError) {
	p := &parser{
		lexer:  Lex("input", input),
		params: make(map[string][]string),
	}
	return p.parse()
}

func (p *parser) parse() ([]namespace, []*ParseError) {
loop:
	for !p.fatal {
		switch item := p.next(); item.Typ {
		case itemEOF:
			break loop
		case itemError:
			p.addFatal(item, "fatal: %s", item.Val)
		case itemKeywordClass:
			p.parseClass()
		}
	}

	if len(p.errors) == 0 {
		p.typeCheck()
	}

	return p.namespaces, p.errors
}
```

解析过程包括：
- 词法分析（Lexer）：将输入文本转换为token流
- 语法分析（Parser）：将token流转换为AST
- 类型检查：验证namespace定义的有效性

## 3. AST的存储方式

解析后的namespace AST主要存储在内存中，而不是数据库中。这是通过以下组件实现的：

1. **命名空间管理器（NamespaceManager）**：
   - 内存中的命名空间管理器（`memoryNamespaceManager`）
   - 文件监视器命名空间管理器（`NamespaceWatcher`）

```go
func (s *memoryNamespaceManager) GetNamespaceByName(_ context.Context, name string) (*namespace.Namespace, error) {
	s.RLock()
	defer s.RUnlock()

	if n, ok := s.byName[name]; ok {
		return n, nil
	}

	return nil, errors.WithStack(herodot.ErrNotFound.WithReasonf("Unknown namespace with name %q.", name))
}
```

命名空间管理器提供以下功能：
- 根据名称获取命名空间
- 获取所有命名空间
- 监控命名空间文件变化并重新加载

## 4. 关系元组的存储

与namespace AST不同，关系元组（relation tuples）是存储在数据库中的。关系元组定义了具体的权限规则，例如：

```
IamRole:admin#perms@IamPerm:iam.get
IamRole:admin#policies@IamPolicy:sys/admin
IamPolicy:sys/admin#users@IamUser:user1
```

关系元组的结构定义在`internal/relationtuple/definitions.go`中：

```go
type RelationTuple struct {
	Namespace string    `json:"namespace"`
	Object    uuid.UUID `json:"object"`
	Relation  string    `json:"relation"`
	Subject   Subject   `json:"subject"`

	Formula *ast.Relation `json:"-"`
}
```

## 5. 权限校验的实现原理

权限校验的核心是`Engine.CheckIsMember`和`Engine.CheckRelationTuple`方法，实现在`internal/check/engine.go`中：

```go
func (e *Engine) CheckIsMember(ctx context.Context, r *relationTuple, restDepth int) (bool, error) {
	result := e.CheckRelationTuple(ctx, r, restDepth)
	if result.Err != nil {
		return false, result.Err
	}
	return result.Membership == checkgroup.IsMember, nil
}
```

权限校验的流程如下：

1. **接收权限检查请求**：
   - 通过API接收权限检查请求
   - 解析请求参数（命名空间、对象、关系、主体）

2. **构建关系元组**：
   ```go
   tuple := &relationtuple.RelationTuple{
       Namespace: "IamRole",
       Object:    objectID,
       Relation:  "get",
       Subject:   &SubjectID{ID: userID},
   }
   ```

3. **执行权限检查**：
   - 首先尝试使用缓存（`trySysCache`）
   - 如果缓存未命中，则执行以下检查：
     - 直接检查（`checkDirect`）：检查数据库中是否存在直接的关系元组
     - 主体集展开（`checkExpandSubject`）：检查通过主体集扩展的权限
     - 主体集重写（`checkSubjectSetRewrite`）：检查通过AST定义的重写规则

4. **递归检查**：
   - 权限检查是递归进行的，通过`restDepth`参数控制递归深度
   - 系统会沿着关系图遍历，直到找到匹配的权限或达到最大深度

## 6. 缓存机制

Keto实现了两种缓存：
1. **系统缓存（SysCache）**：缓存常用的系统级权限检查结果
2. **简要缓存（BriefCache）**：缓存最近的权限检查结果

缓存机制可以显著提高权限检查的性能，特别是对于频繁检查的权限。

## 7. 权限检查的实际执行

当需要检查用户是否有某个权限时，系统会执行以下步骤：

1. **构建检查请求**：
   ```go
   tuple, err := (&ketoapi.RelationTuple{}).FromURLQuery(q)
   ```

2. **转换为内部元组**：
   ```go
   it, err := h.d.ReadOnlyMapper().FromTuple(ctx, tuple)
   ```

3. **执行权限检查**：
   ```go
   allowed, err := h.d.PermissionEngine().CheckIsMember(ctx, it[0], maxDepth)
   ```

4. **返回结果**：
   ```go
   h.d.Writer().Write(w, r, &CheckPermissionResult{Allowed: allowed})
   ```

## 总结

Keto权限系统的工作原理可以概括为：

1. **命名空间定义**：通过TypeScript文件定义权限模型和关系
2. **AST转换**：将命名空间定义解析为AST，存储在内存中
3. **关系元组**：具体的权限规则存储在数据库中
4. **权限校验**：
   - 首先尝试使用缓存
   - 直接检查数据库中的关系元组
   - 通过主体集展开和重写规则递归检查
   - 返回最终的权限检查结果

这种设计将权限模型（命名空间）和具体权限规则（关系元组）分离，提供了灵活且高效的权限管理机制。命名空间定义了权限的"结构"，而关系元组定义了具体的"数据"，两者结合实现了复杂的权限控制。
