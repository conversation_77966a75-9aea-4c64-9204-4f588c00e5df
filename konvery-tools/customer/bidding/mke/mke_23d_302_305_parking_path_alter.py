import os
import sys
import shutil
import json
import numpy as np

sys.path.append(".")
from customer.common import tools


def _listdir(input_dir):
    """wraps for os.listdir but dose not contains mac os system file"""
    mac_file = ['.DS_Store', '__MACOSX']
    return [os.path.join(input_dir, f) for f in os.listdir(input_dir) if f not in mac_file]


def get_params(seg):
    params_file = tools.get_file_by_extension(seg, extension='txt')[0]
    with open(params_file.path) as f:
        params = json.load(f)
    return params


def construct_config(seg_dir, output_seg_dir):
    config = {
        "data_type": "fusion_pointcloud",
        "sensor_params": {}
    }
    params = get_params(seg_dir)
    for camera_name, param in params.items():
        if camera_name == 'lidar':
            continue

        distortion = np.array(param['distortion_params'], dtype=np.float64).tolist()
        intrinsic = np.array(param['intrinsic_matrix'], dtype=np.float64).tolist()
        rotation_matrix = np.array(param['extrinsic_rotation_matrix'], dtype=np.float64)
        translation_vector = np.array(param['extrinsic_translation_matrix'], dtype=np.float64)
        # 创建一个 4x4 的单位矩阵
        extrinsic_matrix = np.eye(4)
        # 将旋转矩阵和平移向量嵌入到外参矩阵中
        extrinsic_matrix[:3, :3] = rotation_matrix
        extrinsic_matrix[:3, 3] = translation_vector
        camera_json = {
            "camera_model": 'fisheye',
            "fov": 50,
            "extrinsic": np.linalg.inv(extrinsic_matrix).tolist(),
            "fx": intrinsic[0][0],
            "fy": intrinsic[1][1],
            "cx": intrinsic[0][2],
            "cy": intrinsic[1][2],
            "k1": distortion[0],
            "k2": distortion[1],
            "p1": distortion[2],
            "p2": distortion[3],
            "k3": distortion[4],
            "k4": distortion[5],
            "k5": distortion[6],
            "k6": distortion[7]
        }
        config["sensor_params"][camera_name] = camera_json
    with open(os.path.join(output_seg_dir, 'config.json'), 'w') as f:
        json.dump(config, f, indent=4)


def run(input_dir, output_dir):
    tools.check_dir(output_dir)
    for seg in _listdir(input_dir):
        output_seg_dir = os.path.join(output_dir, os.path.basename(seg))
        tools.check_dir(output_seg_dir)

        construct_config(seg, output_seg_dir)

        output_lidar_dir = os.path.join(output_seg_dir, "lidar")
        tools.check_dir(output_lidar_dir)
        output_camera_dir = os.path.join(output_seg_dir, "camera")
        tools.check_dir(output_camera_dir)
        cameras = ['fisheye_front', 'fisheye_left', 'fisheye_rear', 'fisheye_right', 'front']
        for camera in cameras:
            tools.check_dir(os.path.join(output_camera_dir, camera))
        for frame in _listdir(seg):
            shutil.copyfile(os.path.join(frame, 'velodyne_points', 'velodyne64.pcd'), os.path.join(output_lidar_dir, f'{os.path.basename(frame)}.pcd'))
            for camera in cameras:
                for img in _listdir(os.path.join(frame, 'images', camera)):
                    shutil.copyfile(img, os.path.join(output_camera_dir, camera, f'{os.path.basename(frame)}.jpg'))


"""
    项目： 
        MKE-密尔沃基 23D融合305泊车检测连续帧
        MKE-密尔沃基 23D融合302泊车车道线
    对接平台：云测平台
    功能： 分包
"""
if __name__ == '__main__':
    args = tools.get_args()
    input_dir = args.intput
    output_dir = args.out
    run(input_dir, output_dir)
