// source: anno/v1/config.proto
package service

import (
	"context"

	"anno/api/client"
	"anno/internal/biz"
	"anno/internal/conf"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/apis/types"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"google.golang.org/protobuf/types/known/emptypb"
)

type ConfigsService struct {
	anno.UnimplementedConfigsServer
	log *log.Helper
	bz  *biz.ConfigBiz
}

func NewConfigsService(logger log.Logger, bz *biz.ConfigBiz) *ConfigsService {
	return &ConfigsService{log: log.NewHelper(logger), bz: bz}
}

func (o *ConfigsService) ListErrors(ctx context.Context, req *emptypb.Empty) (*anno.Errors, error) {
	locales := map[string]*types.Multilingual{}
	for k, v := range errors.LocalizedErrors {
		locales[k] = &types.Multilingual{Langs: v}
	}
	return &anno.Errors{Errors: locales}, nil
}

func (o *ConfigsService) GetVersion(ctx context.Context, req *emptypb.Empty) (*anno.GetVersionReply, error) {
	return &anno.GetVersionReply{Version: conf.Version}, nil
}

func (o *ConfigsService) ListCommentReasons(ctx context.Context, req *emptypb.Empty) (*anno.ListCommentReasonsReply, error) {
	reasons, err := o.bz.GetCommentReasons(ctx)
	if err != nil {
		return nil, err
	}
	return &anno.ListCommentReasonsReply{Classes: reasons}, nil
}

func (o *ConfigsService) PutCommentReasons(ctx context.Context, req *anno.PutCommentReasonsRequest) (*emptypb.Empty, error) {
	if len(req.Classes) == 0 {
		return &emptypb.Empty{}, nil
	}
	for _, c := range req.Classes {
		if c.Class == nil || c.Class.Name == "" {
			return nil, errors.NewErrEmptyField(errors.WithFields("class"))
		}
	}

	op := biz.UserFromCtx(ctx).User
	if !client.IsPrivilegedUser(op) {
		return nil, errors.NewErrForbidden()
	}
	return &emptypb.Empty{}, o.bz.PutCommentReasons(ctx, req.Classes)
}
