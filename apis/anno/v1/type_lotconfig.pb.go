// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anno/v1/type_lotconfig.proto

package anno

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// [DEPRECATED]
type AnnoCommentReason_Enum int32

const (
	AnnoCommentReason_unspecified           AnnoCommentReason_Enum = 0
	AnnoCommentReason_undesired_label       AnnoCommentReason_Enum = 1   // 多标
	AnnoCommentReason_missing_label         AnnoCommentReason_Enum = 2   // 漏标
	AnnoCommentReason_wrong_label           AnnoCommentReason_Enum = 3   // 错标（标签错误）
	AnnoCommentReason_wrong_attr            AnnoCommentReason_Enum = 4   // 错标（属性错误）
	AnnoCommentReason_other                 AnnoCommentReason_Enum = 100 // 其他问题
	AnnoCommentReason_uncentered            AnnoCommentReason_Enum = 101 // 未居中
	AnnoCommentReason_poor_fit              AnnoCommentReason_Enum = 102 // 未贴合
	AnnoCommentReason_illogical_imagination AnnoCommentReason_Enum = 103 // 脑补不合理
)

// Enum value maps for AnnoCommentReason_Enum.
var (
	AnnoCommentReason_Enum_name = map[int32]string{
		0:   "unspecified",
		1:   "undesired_label",
		2:   "missing_label",
		3:   "wrong_label",
		4:   "wrong_attr",
		100: "other",
		101: "uncentered",
		102: "poor_fit",
		103: "illogical_imagination",
	}
	AnnoCommentReason_Enum_value = map[string]int32{
		"unspecified":           0,
		"undesired_label":       1,
		"missing_label":         2,
		"wrong_label":           3,
		"wrong_attr":            4,
		"other":                 100,
		"uncentered":            101,
		"poor_fit":              102,
		"illogical_imagination": 103,
	}
)

func (x AnnoCommentReason_Enum) Enum() *AnnoCommentReason_Enum {
	p := new(AnnoCommentReason_Enum)
	*p = x
	return p
}

func (x AnnoCommentReason_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AnnoCommentReason_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_type_lotconfig_proto_enumTypes[0].Descriptor()
}

func (AnnoCommentReason_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_type_lotconfig_proto_enumTypes[0]
}

func (x AnnoCommentReason_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AnnoCommentReason_Enum.Descriptor instead.
func (AnnoCommentReason_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_type_lotconfig_proto_rawDescGZIP(), []int{0, 0}
}

type Lotphase_Type_Enum int32

const (
	Lotphase_Type_unspecified Lotphase_Type_Enum = 0
	Lotphase_Type_label       Lotphase_Type_Enum = 1
	Lotphase_Type_review      Lotphase_Type_Enum = 2
)

// Enum value maps for Lotphase_Type_Enum.
var (
	Lotphase_Type_Enum_name = map[int32]string{
		0: "unspecified",
		1: "label",
		2: "review",
	}
	Lotphase_Type_Enum_value = map[string]int32{
		"unspecified": 0,
		"label":       1,
		"review":      2,
	}
)

func (x Lotphase_Type_Enum) Enum() *Lotphase_Type_Enum {
	p := new(Lotphase_Type_Enum)
	*p = x
	return p
}

func (x Lotphase_Type_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Lotphase_Type_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_type_lotconfig_proto_enumTypes[1].Descriptor()
}

func (Lotphase_Type_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_type_lotconfig_proto_enumTypes[1]
}

func (x Lotphase_Type_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Lotphase_Type_Enum.Descriptor instead.
func (Lotphase_Type_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_type_lotconfig_proto_rawDescGZIP(), []int{2, 0, 0}
}

type Lotphase_ClaimPolicy_Enum int32

const (
	Lotphase_ClaimPolicy_unspecified      Lotphase_ClaimPolicy_Enum = 0
	Lotphase_ClaimPolicy_only_same_team   Lotphase_ClaimPolicy_Enum = 1 // executors only claim the jobs done by the same team
	Lotphase_ClaimPolicy_only_other_teams Lotphase_ClaimPolicy_Enum = 2 // executors only claim the jobs done by other teams
)

// Enum value maps for Lotphase_ClaimPolicy_Enum.
var (
	Lotphase_ClaimPolicy_Enum_name = map[int32]string{
		0: "unspecified",
		1: "only_same_team",
		2: "only_other_teams",
	}
	Lotphase_ClaimPolicy_Enum_value = map[string]int32{
		"unspecified":      0,
		"only_same_team":   1,
		"only_other_teams": 2,
	}
)

func (x Lotphase_ClaimPolicy_Enum) Enum() *Lotphase_ClaimPolicy_Enum {
	p := new(Lotphase_ClaimPolicy_Enum)
	*p = x
	return p
}

func (x Lotphase_ClaimPolicy_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Lotphase_ClaimPolicy_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_type_lotconfig_proto_enumTypes[2].Descriptor()
}

func (Lotphase_ClaimPolicy_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_type_lotconfig_proto_enumTypes[2]
}

func (x Lotphase_ClaimPolicy_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Lotphase_ClaimPolicy_Enum.Descriptor instead.
func (Lotphase_ClaimPolicy_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_type_lotconfig_proto_rawDescGZIP(), []int{2, 3, 0}
}

type OutConfig_Encoder_Enum int32

const (
	OutConfig_Encoder_json OutConfig_Encoder_Enum = 0 // pbtext = 2;
)

// Enum value maps for OutConfig_Encoder_Enum.
var (
	OutConfig_Encoder_Enum_name = map[int32]string{
		0: "json",
	}
	OutConfig_Encoder_Enum_value = map[string]int32{
		"json": 0,
	}
)

func (x OutConfig_Encoder_Enum) Enum() *OutConfig_Encoder_Enum {
	p := new(OutConfig_Encoder_Enum)
	*p = x
	return p
}

func (x OutConfig_Encoder_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OutConfig_Encoder_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_type_lotconfig_proto_enumTypes[3].Descriptor()
}

func (OutConfig_Encoder_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_type_lotconfig_proto_enumTypes[3]
}

func (x OutConfig_Encoder_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OutConfig_Encoder_Enum.Descriptor instead.
func (OutConfig_Encoder_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_type_lotconfig_proto_rawDescGZIP(), []int{4, 0, 0}
}

type AnnoCommentReason struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AnnoCommentReason) Reset() {
	*x = AnnoCommentReason{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_lotconfig_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnoCommentReason) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnoCommentReason) ProtoMessage() {}

func (x *AnnoCommentReason) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_lotconfig_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnoCommentReason.ProtoReflect.Descriptor instead.
func (*AnnoCommentReason) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_lotconfig_proto_rawDescGZIP(), []int{0}
}

type Lotontologies struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// definitions for all attributes
	Attrs map[string]*Attr `protobuf:"bytes,1,rep,name=attrs,proto3" json:"attrs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// global attribute names for an element(frame)
	ElemAttrs *AttrRefList `protobuf:"bytes,2,opt,name=elem_attrs,json=elemAttrs,proto3" json:"elem_attrs,omitempty"`
	// global attribute names for a rawdata(image/file)
	RawdataAttrs *AttrRefList `protobuf:"bytes,3,opt,name=rawdata_attrs,json=rawdataAttrs,proto3" json:"rawdata_attrs,omitempty"`
	// when there are mutiple groups, the lot will be splitted according to groups
	Groups []*Lotontologies_Group `protobuf:"bytes,4,rep,name=groups,proto3" json:"groups,omitempty"`
	// global attribute names for a job
	JobAttrs *AttrRefList `protobuf:"bytes,5,opt,name=job_attrs,json=jobAttrs,proto3" json:"job_attrs,omitempty"`
}

func (x *Lotontologies) Reset() {
	*x = Lotontologies{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_lotconfig_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lotontologies) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lotontologies) ProtoMessage() {}

func (x *Lotontologies) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_lotconfig_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lotontologies.ProtoReflect.Descriptor instead.
func (*Lotontologies) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_lotconfig_proto_rawDescGZIP(), []int{1}
}

func (x *Lotontologies) GetAttrs() map[string]*Attr {
	if x != nil {
		return x.Attrs
	}
	return nil
}

func (x *Lotontologies) GetElemAttrs() *AttrRefList {
	if x != nil {
		return x.ElemAttrs
	}
	return nil
}

func (x *Lotontologies) GetRawdataAttrs() *AttrRefList {
	if x != nil {
		return x.RawdataAttrs
	}
	return nil
}

func (x *Lotontologies) GetGroups() []*Lotontologies_Group {
	if x != nil {
		return x.Groups
	}
	return nil
}

func (x *Lotontologies) GetJobAttrs() *AttrRefList {
	if x != nil {
		return x.JobAttrs
	}
	return nil
}

// Lotphase describes an exeuction phase in a lot
type Lotphase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// phase number, start from 1
	Number int32 `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty"`
	// name of this phase, e.g. label/review-1/review-2/acceptance/..., 标注/审核1/审核2/验收/...
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// type of this phase
	Type Lotphase_Type_Enum `protobuf:"varint,3,opt,name=type,proto3,enum=anno.v1.Lotphase_Type_Enum" json:"type,omitempty"`
	// can reviewer edit the annotations
	Editable bool `protobuf:"varint,4,opt,name=editable,proto3" json:"editable,omitempty"`
	// percent of the annotations to be reviewed
	SamplePercent float32 `protobuf:"fixed32,5,opt,name=sample_percent,json=samplePercent,proto3" json:"sample_percent,omitempty"`
	// minimum skill level required for executor eligible for this lot phase
	MinSkillLevel int32 `protobuf:"varint,7,opt,name=min_skill_level,json=minSkillLevel,proto3" json:"min_skill_level,omitempty"`
	// assignees should finish their assignments within this number of seconds
	Timeout int32 `protobuf:"varint,8,opt,name=timeout,proto3" json:"timeout,omitempty"`
	// whether to merge the annotation results of splitted jobs after this phase
	Merge bool `protobuf:"varint,9,opt,name=merge,proto3" json:"merge,omitempty"`
	// support multiple execution team-uids in this phase;
	Execteams   []*Lotphase_Execteam      `protobuf:"bytes,10,rep,name=execteams,proto3" json:"execteams,omitempty"`
	ClaimPolicy Lotphase_ClaimPolicy_Enum `protobuf:"varint,11,opt,name=claim_policy,json=claimPolicy,proto3,enum=anno.v1.Lotphase_ClaimPolicy_Enum" json:"claim_policy,omitempty"`
}

func (x *Lotphase) Reset() {
	*x = Lotphase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_lotconfig_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lotphase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lotphase) ProtoMessage() {}

func (x *Lotphase) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_lotconfig_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lotphase.ProtoReflect.Descriptor instead.
func (*Lotphase) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_lotconfig_proto_rawDescGZIP(), []int{2}
}

func (x *Lotphase) GetNumber() int32 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *Lotphase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Lotphase) GetType() Lotphase_Type_Enum {
	if x != nil {
		return x.Type
	}
	return Lotphase_Type_unspecified
}

func (x *Lotphase) GetEditable() bool {
	if x != nil {
		return x.Editable
	}
	return false
}

func (x *Lotphase) GetSamplePercent() float32 {
	if x != nil {
		return x.SamplePercent
	}
	return 0
}

func (x *Lotphase) GetMinSkillLevel() int32 {
	if x != nil {
		return x.MinSkillLevel
	}
	return 0
}

func (x *Lotphase) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *Lotphase) GetMerge() bool {
	if x != nil {
		return x.Merge
	}
	return false
}

func (x *Lotphase) GetExecteams() []*Lotphase_Execteam {
	if x != nil {
		return x.Execteams
	}
	return nil
}

func (x *Lotphase) GetClaimPolicy() Lotphase_ClaimPolicy_Enum {
	if x != nil {
		return x.ClaimPolicy
	}
	return Lotphase_ClaimPolicy_unspecified
}

type DataConverter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversion script runtime; default is python3.10
	Runtime string `protobuf:"bytes,1,opt,name=runtime,proto3" json:"runtime,omitempty"`
	// conversion script URI
	Uri string `protobuf:"bytes,2,opt,name=uri,proto3" json:"uri,omitempty"`
}

func (x *DataConverter) Reset() {
	*x = DataConverter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_lotconfig_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataConverter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataConverter) ProtoMessage() {}

func (x *DataConverter) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_lotconfig_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataConverter.ProtoReflect.Descriptor instead.
func (*DataConverter) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_lotconfig_proto_rawDescGZIP(), []int{3}
}

func (x *DataConverter) GetRuntime() string {
	if x != nil {
		return x.Runtime
	}
	return ""
}

func (x *DataConverter) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

type OutConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// exporter configuration
	Exporter *OutConfig_Exporter `protobuf:"bytes,1,opt,name=exporter,proto3" json:"exporter,omitempty"`
	// encoding type
	Encoder OutConfig_Encoder_Enum `protobuf:"varint,2,opt,name=encoder,proto3,enum=anno.v1.OutConfig_Encoder_Enum" json:"encoder,omitempty"`
	// files layout
	Layout string `protobuf:"bytes,3,opt,name=layout,proto3" json:"layout,omitempty"`
	// file style
	Style string `protobuf:"bytes,4,opt,name=style,proto3" json:"style,omitempty"`
	// converter is a piece of script to convert the annos from platform format to customer format
	Converter *DataConverter `protobuf:"bytes,5,opt,name=converter,proto3" json:"converter,omitempty"`
}

func (x *OutConfig) Reset() {
	*x = OutConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_lotconfig_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OutConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutConfig) ProtoMessage() {}

func (x *OutConfig) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_lotconfig_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutConfig.ProtoReflect.Descriptor instead.
func (*OutConfig) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_lotconfig_proto_rawDescGZIP(), []int{4}
}

func (x *OutConfig) GetExporter() *OutConfig_Exporter {
	if x != nil {
		return x.Exporter
	}
	return nil
}

func (x *OutConfig) GetEncoder() OutConfig_Encoder_Enum {
	if x != nil {
		return x.Encoder
	}
	return OutConfig_Encoder_json
}

func (x *OutConfig) GetLayout() string {
	if x != nil {
		return x.Layout
	}
	return ""
}

func (x *OutConfig) GetStyle() string {
	if x != nil {
		return x.Style
	}
	return ""
}

func (x *OutConfig) GetConverter() *DataConverter {
	if x != nil {
		return x.Converter
	}
	return nil
}

type AnnoCommentReason_Reasons struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// one of unwanted（多标）, missed（漏标）, flawed（错标）, other（其他）
	Class string `protobuf:"bytes,1,opt,name=class,proto3" json:"class,omitempty"`
	// reason list in the class
	Reasons []string `protobuf:"bytes,2,rep,name=reasons,proto3" json:"reasons,omitempty"`
}

func (x *AnnoCommentReason_Reasons) Reset() {
	*x = AnnoCommentReason_Reasons{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_lotconfig_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnoCommentReason_Reasons) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnoCommentReason_Reasons) ProtoMessage() {}

func (x *AnnoCommentReason_Reasons) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_lotconfig_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnoCommentReason_Reasons.ProtoReflect.Descriptor instead.
func (*AnnoCommentReason_Reasons) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_lotconfig_proto_rawDescGZIP(), []int{0, 0}
}

func (x *AnnoCommentReason_Reasons) GetClass() string {
	if x != nil {
		return x.Class
	}
	return ""
}

func (x *AnnoCommentReason_Reasons) GetReasons() []string {
	if x != nil {
		return x.Reasons
	}
	return nil
}

type Lotontologies_Group struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// annotation scope
	Labels []*Label `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty"`
}

func (x *Lotontologies_Group) Reset() {
	*x = Lotontologies_Group{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_lotconfig_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lotontologies_Group) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lotontologies_Group) ProtoMessage() {}

func (x *Lotontologies_Group) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_lotconfig_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lotontologies_Group.ProtoReflect.Descriptor instead.
func (*Lotontologies_Group) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_lotconfig_proto_rawDescGZIP(), []int{1, 0}
}

func (x *Lotontologies_Group) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Lotontologies_Group) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

type Lotphase_Type struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Lotphase_Type) Reset() {
	*x = Lotphase_Type{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_lotconfig_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lotphase_Type) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lotphase_Type) ProtoMessage() {}

func (x *Lotphase_Type) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_lotconfig_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lotphase_Type.ProtoReflect.Descriptor instead.
func (*Lotphase_Type) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_lotconfig_proto_rawDescGZIP(), []int{2, 0}
}

type Lotphase_Quota struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// minimum job quota in percentage, [0, 100];
	// if min is not 0 and min is equal to max, means this team needs to finish min% of total jobs;
	Min int32 `protobuf:"varint,1,opt,name=min,proto3" json:"min,omitempty"`
	// maximum job quota in percentage, [0, 100];
	// max must be explicitly set and we do not treat 0 specially.
	Max int32 `protobuf:"varint,2,opt,name=max,proto3" json:"max,omitempty"`
}

func (x *Lotphase_Quota) Reset() {
	*x = Lotphase_Quota{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_lotconfig_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lotphase_Quota) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lotphase_Quota) ProtoMessage() {}

func (x *Lotphase_Quota) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_lotconfig_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lotphase_Quota.ProtoReflect.Descriptor instead.
func (*Lotphase_Quota) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_lotconfig_proto_rawDescGZIP(), []int{2, 1}
}

func (x *Lotphase_Quota) GetMin() int32 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *Lotphase_Quota) GetMax() int32 {
	if x != nil {
		return x.Max
	}
	return 0
}

type Lotphase_Execteam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// execution team-uid
	Execteam string          `protobuf:"bytes,1,opt,name=execteam,proto3" json:"execteam,omitempty"`
	Quota    *Lotphase_Quota `protobuf:"bytes,2,opt,name=quota,proto3" json:"quota,omitempty"`
	// [OUTPUT] the number of jobs that this team has claimed (not percentage)
	ClaimedJobs int32 `protobuf:"varint,3,opt,name=claimed_jobs,json=claimedJobs,proto3" json:"claimed_jobs,omitempty"`
}

func (x *Lotphase_Execteam) Reset() {
	*x = Lotphase_Execteam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_lotconfig_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lotphase_Execteam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lotphase_Execteam) ProtoMessage() {}

func (x *Lotphase_Execteam) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_lotconfig_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lotphase_Execteam.ProtoReflect.Descriptor instead.
func (*Lotphase_Execteam) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_lotconfig_proto_rawDescGZIP(), []int{2, 2}
}

func (x *Lotphase_Execteam) GetExecteam() string {
	if x != nil {
		return x.Execteam
	}
	return ""
}

func (x *Lotphase_Execteam) GetQuota() *Lotphase_Quota {
	if x != nil {
		return x.Quota
	}
	return nil
}

func (x *Lotphase_Execteam) GetClaimedJobs() int32 {
	if x != nil {
		return x.ClaimedJobs
	}
	return 0
}

type Lotphase_ClaimPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Lotphase_ClaimPolicy) Reset() {
	*x = Lotphase_ClaimPolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_lotconfig_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lotphase_ClaimPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lotphase_ClaimPolicy) ProtoMessage() {}

func (x *Lotphase_ClaimPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_lotconfig_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lotphase_ClaimPolicy.ProtoReflect.Descriptor instead.
func (*Lotphase_ClaimPolicy) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_lotconfig_proto_rawDescGZIP(), []int{2, 3}
}

type OutConfig_Encoder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *OutConfig_Encoder) Reset() {
	*x = OutConfig_Encoder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_lotconfig_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OutConfig_Encoder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutConfig_Encoder) ProtoMessage() {}

func (x *OutConfig_Encoder) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_lotconfig_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutConfig_Encoder.ProtoReflect.Descriptor instead.
func (*OutConfig_Encoder) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_lotconfig_proto_rawDescGZIP(), []int{4, 0}
}

type OutConfig_Exporter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// exporter name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// exporter specific configuration
	Config string `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
	// if to data uid
	IncludeDataUid bool `protobuf:"varint,3,opt,name=include_data_uid,json=includeDataUid,proto3" json:"include_data_uid,omitempty"`
}

func (x *OutConfig_Exporter) Reset() {
	*x = OutConfig_Exporter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_lotconfig_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OutConfig_Exporter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutConfig_Exporter) ProtoMessage() {}

func (x *OutConfig_Exporter) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_lotconfig_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutConfig_Exporter.ProtoReflect.Descriptor instead.
func (*OutConfig_Exporter) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_lotconfig_proto_rawDescGZIP(), []int{4, 1}
}

func (x *OutConfig_Exporter) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *OutConfig_Exporter) GetConfig() string {
	if x != nil {
		return x.Config
	}
	return ""
}

func (x *OutConfig_Exporter) GetIncludeDataUid() bool {
	if x != nil {
		return x.IncludeDataUid
	}
	return false
}

var File_anno_v1_type_lotconfig_proto protoreflect.FileDescriptor

var file_anno_v1_type_lotconfig_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c,
	0x6f, 0x74, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x1a, 0x18, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8c, 0x02, 0x0a, 0x11, 0x41, 0x6e, 0x6e,
	0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x1a, 0x50,
	0x0a, 0x07, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x07, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x3a, 0x15, 0xba, 0x47, 0x12, 0xba, 0x01,
	0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0xba, 0x01, 0x07, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73,
	0x22, 0xa4, 0x01, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x75, 0x6e,
	0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x10, 0x01, 0x12,
	0x11, 0x0a, 0x0d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x77, 0x72, 0x6f, 0x6e, 0x67, 0x5f, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x77, 0x72, 0x6f, 0x6e, 0x67, 0x5f, 0x61, 0x74, 0x74,
	0x72, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x10, 0x64, 0x12, 0x0e,
	0x0a, 0x0a, 0x75, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x64, 0x10, 0x65, 0x12, 0x0c,
	0x0a, 0x08, 0x70, 0x6f, 0x6f, 0x72, 0x5f, 0x66, 0x69, 0x74, 0x10, 0x66, 0x12, 0x19, 0x0a, 0x15,
	0x69, 0x6c, 0x6c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x67, 0x22, 0xda, 0x03, 0x0a, 0x0d, 0x4c, 0x6f, 0x74, 0x6f,
	0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x12, 0x37, 0x0a, 0x05, 0x61, 0x74, 0x74,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73,
	0x2e, 0x41, 0x74, 0x74, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x61, 0x74, 0x74,
	0x72, 0x73, 0x12, 0x33, 0x0a, 0x0a, 0x65, 0x6c, 0x65, 0x6d, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x74, 0x74, 0x72, 0x52, 0x65, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x09, 0x65, 0x6c,
	0x65, 0x6d, 0x41, 0x74, 0x74, 0x72, 0x73, 0x12, 0x39, 0x0a, 0x0d, 0x72, 0x61, 0x77, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x52, 0x65, 0x66,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x0c, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x41, 0x74, 0x74,
	0x72, 0x73, 0x12, 0x34, 0x0a, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74,
	0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x31, 0x0a, 0x09, 0x6a, 0x6f, 0x62, 0x5f,
	0x61, 0x74, 0x74, 0x72, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x52, 0x65, 0x66, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x08, 0x6a, 0x6f, 0x62, 0x41, 0x74, 0x74, 0x72, 0x73, 0x1a, 0x58, 0x0a, 0x05, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x3a, 0x13, 0xba, 0x47, 0x10, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x06, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x47, 0x0a, 0x0a, 0x41, 0x74, 0x74, 0x72, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x23, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x74, 0x74, 0x72, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x3a, 0x14,
	0xba, 0x47, 0x11, 0xba, 0x01, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0xba, 0x01, 0x05, 0x61,
	0x74, 0x74, 0x72, 0x73, 0x22, 0xbb, 0x06, 0x0a, 0x08, 0x4c, 0x6f, 0x74, 0x70, 0x68, 0x61, 0x73,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x70, 0x68, 0x61, 0x73, 0x65, 0x2e, 0x54,
	0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x64,
	0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x65, 0x64,
	0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65,
	0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d,
	0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x0a,
	0x0f, 0x6d, 0x69, 0x6e, 0x5f, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6d, 0x69, 0x6e, 0x53, 0x6b, 0x69, 0x6c, 0x6c,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05,
	0x6d, 0x65, 0x72, 0x67, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x65, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61,
	0x6d, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x70, 0x68, 0x61, 0x73, 0x65, 0x2e, 0x45, 0x78, 0x65, 0x63,
	0x74, 0x65, 0x61, 0x6d, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x12,
	0x45, 0x0a, 0x0c, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x6f, 0x74, 0x70, 0x68, 0x61, 0x73, 0x65, 0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0b, 0x63, 0x6c, 0x61, 0x69, 0x6d,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x1a, 0x36, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x22, 0x2e,
	0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x10, 0x02, 0x1a, 0x41,
	0x0a, 0x05, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x03, 0x6d, 0x69, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52,
	0x03, 0x6d, 0x69, 0x6e, 0x12, 0x1b, 0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52, 0x03, 0x6d, 0x61,
	0x78, 0x1a, 0x88, 0x01, 0x0a, 0x08, 0x45, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x12, 0x1a,
	0x0a, 0x08, 0x65, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x65, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x12, 0x2d, 0x0a, 0x05, 0x71, 0x75,
	0x6f, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x70, 0x68, 0x61, 0x73, 0x65, 0x2e, 0x51, 0x75, 0x6f,
	0x74, 0x61, 0x52, 0x05, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x61,
	0x69, 0x6d, 0x65, 0x64, 0x5f, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x73, 0x3a, 0x0e, 0xba, 0x47,
	0x0b, 0xba, 0x01, 0x08, 0x65, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x1a, 0x50, 0x0a, 0x0b,
	0x43, 0x6c, 0x61, 0x69, 0x6d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x22, 0x41, 0x0a, 0x04, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x73, 0x61, 0x6d,
	0x65, 0x5f, 0x74, 0x65, 0x61, 0x6d, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x6f, 0x6e, 0x6c, 0x79,
	0x5f, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x10, 0x02, 0x3a, 0x4c,
	0xba, 0x47, 0x49, 0xba, 0x01, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0xba, 0x01, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0xba, 0x01, 0x04, 0x74, 0x79, 0x70, 0x65, 0xba, 0x01, 0x08, 0x65, 0x64, 0x69,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0xba, 0x01, 0x0e, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x70,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0xba, 0x01, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0xba, 0x01, 0x09, 0x65, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x4a, 0x04, 0x08, 0x06,
	0x10, 0x07, 0x22, 0x50, 0x0a, 0x0d, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x74, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x69, 0x3a,
	0x13, 0xba, 0x47, 0x10, 0xba, 0x01, 0x07, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0xba, 0x01,
	0x03, 0x75, 0x72, 0x69, 0x22, 0xac, 0x03, 0x0a, 0x09, 0x4f, 0x75, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x37, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x75, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x72, 0x52, 0x08, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x07, 0x65,
	0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72,
	0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x34,
	0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x65, 0x72, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x74, 0x65, 0x72, 0x1a, 0x1b, 0x0a, 0x07, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x22,
	0x10, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x08, 0x0a, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0x10,
	0x00, 0x1a, 0x75, 0x0a, 0x08, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x6e, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x55, 0x69, 0x64, 0x3a, 0x13, 0xba, 0x47, 0x10, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba,
	0x01, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x3a, 0x29, 0xba, 0x47, 0x26, 0xba, 0x01, 0x08,
	0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0xba, 0x01, 0x07, 0x65, 0x6e, 0x63, 0x6f, 0x64,
	0x65, 0x72, 0xba, 0x01, 0x06, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0xba, 0x01, 0x05, 0x73, 0x74,
	0x79, 0x6c, 0x65, 0x42, 0x3e, 0x0a, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x31, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x3b, 0x61,
	0x6e, 0x6e, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_anno_v1_type_lotconfig_proto_rawDescOnce sync.Once
	file_anno_v1_type_lotconfig_proto_rawDescData = file_anno_v1_type_lotconfig_proto_rawDesc
)

func file_anno_v1_type_lotconfig_proto_rawDescGZIP() []byte {
	file_anno_v1_type_lotconfig_proto_rawDescOnce.Do(func() {
		file_anno_v1_type_lotconfig_proto_rawDescData = protoimpl.X.CompressGZIP(file_anno_v1_type_lotconfig_proto_rawDescData)
	})
	return file_anno_v1_type_lotconfig_proto_rawDescData
}

var file_anno_v1_type_lotconfig_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_anno_v1_type_lotconfig_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_anno_v1_type_lotconfig_proto_goTypes = []interface{}{
	(AnnoCommentReason_Enum)(0),       // 0: anno.v1.AnnoCommentReason.Enum
	(Lotphase_Type_Enum)(0),           // 1: anno.v1.Lotphase.Type.Enum
	(Lotphase_ClaimPolicy_Enum)(0),    // 2: anno.v1.Lotphase.ClaimPolicy.Enum
	(OutConfig_Encoder_Enum)(0),       // 3: anno.v1.OutConfig.Encoder.Enum
	(*AnnoCommentReason)(nil),         // 4: anno.v1.AnnoCommentReason
	(*Lotontologies)(nil),             // 5: anno.v1.Lotontologies
	(*Lotphase)(nil),                  // 6: anno.v1.Lotphase
	(*DataConverter)(nil),             // 7: anno.v1.DataConverter
	(*OutConfig)(nil),                 // 8: anno.v1.OutConfig
	(*AnnoCommentReason_Reasons)(nil), // 9: anno.v1.AnnoCommentReason.Reasons
	(*Lotontologies_Group)(nil),       // 10: anno.v1.Lotontologies.Group
	nil,                               // 11: anno.v1.Lotontologies.AttrsEntry
	(*Lotphase_Type)(nil),             // 12: anno.v1.Lotphase.Type
	(*Lotphase_Quota)(nil),            // 13: anno.v1.Lotphase.Quota
	(*Lotphase_Execteam)(nil),         // 14: anno.v1.Lotphase.Execteam
	(*Lotphase_ClaimPolicy)(nil),      // 15: anno.v1.Lotphase.ClaimPolicy
	(*OutConfig_Encoder)(nil),         // 16: anno.v1.OutConfig.Encoder
	(*OutConfig_Exporter)(nil),        // 17: anno.v1.OutConfig.Exporter
	(*AttrRefList)(nil),               // 18: anno.v1.AttrRefList
	(*Label)(nil),                     // 19: anno.v1.Label
	(*Attr)(nil),                      // 20: anno.v1.Attr
}
var file_anno_v1_type_lotconfig_proto_depIdxs = []int32{
	11, // 0: anno.v1.Lotontologies.attrs:type_name -> anno.v1.Lotontologies.AttrsEntry
	18, // 1: anno.v1.Lotontologies.elem_attrs:type_name -> anno.v1.AttrRefList
	18, // 2: anno.v1.Lotontologies.rawdata_attrs:type_name -> anno.v1.AttrRefList
	10, // 3: anno.v1.Lotontologies.groups:type_name -> anno.v1.Lotontologies.Group
	18, // 4: anno.v1.Lotontologies.job_attrs:type_name -> anno.v1.AttrRefList
	1,  // 5: anno.v1.Lotphase.type:type_name -> anno.v1.Lotphase.Type.Enum
	14, // 6: anno.v1.Lotphase.execteams:type_name -> anno.v1.Lotphase.Execteam
	2,  // 7: anno.v1.Lotphase.claim_policy:type_name -> anno.v1.Lotphase.ClaimPolicy.Enum
	17, // 8: anno.v1.OutConfig.exporter:type_name -> anno.v1.OutConfig.Exporter
	3,  // 9: anno.v1.OutConfig.encoder:type_name -> anno.v1.OutConfig.Encoder.Enum
	7,  // 10: anno.v1.OutConfig.converter:type_name -> anno.v1.DataConverter
	19, // 11: anno.v1.Lotontologies.Group.labels:type_name -> anno.v1.Label
	20, // 12: anno.v1.Lotontologies.AttrsEntry.value:type_name -> anno.v1.Attr
	13, // 13: anno.v1.Lotphase.Execteam.quota:type_name -> anno.v1.Lotphase.Quota
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_anno_v1_type_lotconfig_proto_init() }
func file_anno_v1_type_lotconfig_proto_init() {
	if File_anno_v1_type_lotconfig_proto != nil {
		return
	}
	file_anno_v1_type_label_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_anno_v1_type_lotconfig_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnoCommentReason); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_lotconfig_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lotontologies); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_lotconfig_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lotphase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_lotconfig_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataConverter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_lotconfig_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OutConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_lotconfig_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnoCommentReason_Reasons); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_lotconfig_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lotontologies_Group); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_lotconfig_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lotphase_Type); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_lotconfig_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lotphase_Quota); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_lotconfig_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lotphase_Execteam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_lotconfig_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lotphase_ClaimPolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_lotconfig_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OutConfig_Encoder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_lotconfig_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OutConfig_Exporter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anno_v1_type_lotconfig_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_anno_v1_type_lotconfig_proto_goTypes,
		DependencyIndexes: file_anno_v1_type_lotconfig_proto_depIdxs,
		EnumInfos:         file_anno_v1_type_lotconfig_proto_enumTypes,
		MessageInfos:      file_anno_v1_type_lotconfig_proto_msgTypes,
	}.Build()
	File_anno_v1_type_lotconfig_proto = out.File
	file_anno_v1_type_lotconfig_proto_rawDesc = nil
	file_anno_v1_type_lotconfig_proto_goTypes = nil
	file_anno_v1_type_lotconfig_proto_depIdxs = nil
}
