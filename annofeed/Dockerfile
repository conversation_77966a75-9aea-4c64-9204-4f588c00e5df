# build program
ARG IMAGE_PREFIX
FROM ${IMAGE_PREFIX}golang:1.22-alpine AS builder

ARG VERSION
ARG GOPROXY
ARG GOPRIVATE
ENV LDFLAGS="-s"

COPY . /src
WORKDIR /src

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk add make git
RUN make build

# create image
FROM ${IMAGE_PREFIX}alpine:3.18

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk update && apk add --no-cache ca-certificates tzdata mailcap && update-ca-certificates

COPY --from=builder /src/bin /app

RUN mkdir -p /data/conf
COPY --from=builder /src/configs/config.yaml /data/conf

WORKDIR /app

EXPOSE 6060
EXPOSE 8020
EXPOSE 8021
VOLUME /data/conf
VOLUME /work

CMD ["/app/annofeed", "-conf", "/data/conf"]
