syntax = "proto3";

package iam.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/iam/v1;iam";
option java_multiple_files = true;
option java_package = "iam.v1";

service Policies {
  // Create and attach a policy to a resource
  rpc CreatePolicy (CreatePolicyRequest) returns (Policy) {
    option (google.api.http) = {
      post: "/v1/policies"
      body: "*"
    };
  }

  rpc UpdatePolicy (UpdatePolicyRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      patch: "/v1/policies/{name}"
      body: "*"
    };
  }

  // detach and delete the policy
  rpc DeletePolicy (DeletePolicyRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/policies/{name}"
    };
  }

  rpc GetPolicy (GetPolicyRequest) returns (Policy) {
    option (google.api.http) = {
      get: "/v1/policies/{name}"
    };
  }

  // rpc ListPolicy (ListPolicyRequest) returns (ListPolicyReply) {
  //   option (google.api.http) = {
  //     get: "/v1/policies"
  //   };
  // }

  // // attach the policy to resources
  // rpc AttachPolicyTo (AttachPolicyRequest) returns (google.protobuf.Empty) {
  //   option (google.api.http) = {
  //     put: "/v1/policies/{name}/attach"
  //     body: "*"
  //   };
  // }

  // // detach the policy from resources
  // rpc DetachPolicyFrom (AttachPolicyRequest) returns (google.protobuf.Empty) {
  //   option (google.api.http) = {
  //     put: "/v1/policies/{name}/dettach"
  //     body: "*"
  //   };
  // }

  // // get resources attached to the policy
  // rpc GetAttachedResources (GetPolicyRequest) returns (GetAttachedResourcesReply) {
  //   option (google.api.http) = {
  //     get: "/v1/policies/{name}/resources"
  //   };
  // }

  // // attach/detach policies to/from a resource
  // rpc ManageResourcePolicies (ManageResourcePoliciesRequest) returns (google.protobuf.Empty) {
  //   option (google.api.http) = {
  //     patch: "/v1/resources/{name}/policies"
  //     body: "*"
  //   };
  // }

  // get policies attached to a resource
  rpc GetAttachedPolicies (GetAttachedPoliciesRequest) returns (GetAttachedPoliciesReply) {
    option (google.api.http) = {
      get: "/v1/resources/{name}/policies"
    };
  }

  // [rpc only] intialize access policies for a resource
  rpc CreateResource (CreateResourceRequest) returns (google.protobuf.Empty);
  //   option (google.api.http) = {
  //     post: "/v1/resources/{name}/policies"
  //     body: "*"
  //   };
  // }

  // [rpc only] clear access policies for a resource
  rpc DeleteResource (DeleteResourceRequest) returns (google.protobuf.Empty);
  //   option (google.api.http) = {
  //     delete: "/v1/resources/{name}/policies"
  //   };
  // }

  // [rpc only] revoke users' role on a resource
  rpc RevokeUsersRole (RevokeUsersRoleRequest) returns (google.protobuf.Empty);

  // rpc GetResourceTree (GetResourceTreeRequest) returns (GetResourceTreeReply) {
  //   option (google.api.http) = {
  //     get: "/v1/resources/{name}/tree"
  //   };
  // }
}

message CreatePolicyRequest {
  option (openapi.v3.schema) = {
    required: ["role", "users", "resource"]
  };

  string role = 1;
  // uid of users (IamUser:xxx) or groups(IamGroup:xxx)
  // pattern: "^(IamUser|IamGroup):[\\w-]+(:[\\w-]+)?$"
  repeated string users = 2 [(validate.rules).repeated.items.string =
                              {pattern: "^(IamUser|IamGroup):[\\w-]+$"}];
  // the name of the resource to be attached to; pattern: "^([\\w-]+:){1,2}([\\w-]+(.|/))+$"
  string resource = 3 [(validate.rules).string = {pattern: "^([\\w-]+:){1,2}([\\w-]+(.|/))+$"}];
}

message Policy {
  option (openapi.v3.schema) = {
    required: ["name", "role", "users"]
  };

  string name = 1;
  string role = 2;
  // uid of users (IamUser:xxx) or groups(IamGroup:xxx)
  repeated string users = 3;
}

message UpdatePolicyRequest {
  option (openapi.v3.schema) = {
    required: ["name", "users"]
  };

  string name = 1;
  // uid of users (IamUser:xxx) or groups(IamGroup:xxx)
  repeated string users = 2 [(validate.rules).repeated.items.string =
                              {pattern: "^(IamUser|IamGroup):[\\w-]+$"}];
}

message DeletePolicyRequest {
  string name = 1;
}

message GetPolicyRequest {
  string name = 1;
}

// message ListPolicyRequest {
//   int32 pagesz = 1 [(validate.rules).int32 = {gte:0, lte: 100}];
//   // An opaque pagination token returned from a previous call.
//   // An empty token denotes the first page.
//   string page_token = 2;
//   // if not empty, only list policies created by this organization
//   string org_uid = 3;
// }

// message ListPolicyReply {
//   option (openapi.v3.schema) = {
//     required: ["policies", "next_page_token"]
//   };

//   repeated Policy policies = 1;
//   string next_page_token = 2;
// }

// message AttachPolicyRequest {
//   option (openapi.v3.schema) = {
//     required: ["name", "resources"]
//   };

//   string name = 1;
//   // resource is in the format: type:(uid|name)
//   // type: iam.group/iam.user/iam.role/anno.lot/anno.job/...
//   repeated string resources = 2 [(validate.rules).repeated.items.string = {pattern: "^\\w+(:[\\w-]+)*$"}];
// }

// message ManageResourcePoliciesRequest {
//   option (openapi.v3.schema) = {
//     required: ["name", "action", "policies"]
//   };

//   enum Action {
//     attach = 0;
//     detach = 1;
//   }
//   Action action = 1;
//   // resource name in the format: type:(uid|name)
//   // type: iam.group/iam.user/iam.role/anno.lot/anno.job/...
//   string name = 2;
//   repeated string policies = 3;
// }

message GetAttachedPoliciesRequest {
  option (openapi.v3.schema) = {
    required: ["name"]
  };

  // resource name in the format: type:(uid|name)
  // type: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
  string name = 1 [(validate.rules).string = {pattern: "^([\\w-]+:){1,2}([\\w-]+(.|/))+$"}];
  // get attached policy binding the role
  string role = 2;
}

message GetAttachedPoliciesReply {
  option (openapi.v3.schema) = {
    required: ["policy_names"]
  };

  repeated string policy_names = 1;
}

message CreateResourceRequest {
  option (openapi.v3.schema) = {
    required: ["name", "owners"]
  };

  // resource name in the format: type:(uid|name)
  // type: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
  string name = 1;
  // uid of users (IamUser:xxx) or groups(IamGroup:xxx) to be granted the owner role
  // pattern: "^(IamUser|IamGroup):[\\w-]+(:[\\w-]+)?$"
  repeated string owners = 2;
  // subjects, in the format: type:(uid|name), of the resource's parents relation
  repeated string parents = 3;
}

// message GetAttachedResourcesReply {
//   option (openapi.v3.schema) = {
//     required: ["resources"]
//   };

//   repeated string resources = 1;
// }

// message GetResourceTreeRequest {
//   // resource name in the format: type:(uid|name)
//   string name = 1;
//   // common relations include: owners, parents, children, members
//   string relation = 2;
//   int32 max_depth = 3;
// }

// message GetResourceTreeReply {
//   option (openapi.v3.schema) = {
//     required: []
//   };

//   message Node {
//     option (openapi.v3.schema) = {
//       required: ["name", "items", "inherits"]
//     };

//     string name = 1;
//     repeated Node inherits = 2;
//     repeated string items = 3;
//   }

//   Node tree = 1;
// }

message DeleteResourceRequest {
  option (openapi.v3.schema) = {
    required: ["name"]
  };

  // resource name in the format: type:(uid|name)
  // type: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
  string name = 1 [(validate.rules).string = {pattern: "^([\\w-]+:){1,2}([\\w-]+(.|/))+$"}];
}

message RevokeUsersRoleRequest {
  option (openapi.v3.schema) = {
    required: ["resource", "role", "users"]
  };

  // resource name in the format: type:(uid|name)
  // type: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
  string resource = 1 [(validate.rules).string = {pattern: "^([\\w-]+:){1,2}([\\w-]+(.|/))+$"}];
  // role
  string role = 2;
  // user list
  repeated string users = 3;
}
