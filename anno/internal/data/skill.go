// source: anno/v1/skill.proto
package data

import (
	biz "anno/internal/biz"
	context "context"

	log "github.com/go-kratos/kratos/v2/log"
	gorm "gorm.io/gorm"
)

type skillsRepo struct {
	data *Data
	log  *log.Helper
}

func NewSkillsRepo(data *Data, logger log.Logger) biz.SkillsRepo {
	return &skillsRepo{data: data, log: log.<PERSON>elper(logger)}
}

func (o *skillsRepo) Create(ctx context.Context, p *biz.Skill) (*biz.Skill, error) {
	return Create(ctx, o.data, p)
}

func (o *skillsRepo) Update(ctx context.Context, p *biz.Skill, fldMask *biz.FieldMask) (*biz.Skill, error) {
	return Update(ctx, o.data, p, biz.SkillUpdatableFlds, fldMask)
}

func (o *skillsRepo) loadAssociations(ctx context.Context, data *Data, mod *biz.Skill) error {
	return nil
}

func (o *skillsRepo) GetByID(ctx context.Context, id int64) (*biz.Skill, error) {
	return GetByID[biz.Skill](ctx, o.data, id, o.loadAssociations)
}

func (o *skillsRepo) GetByUid(ctx context.Context, uid string) (*biz.Skill, error) {
	return GetByUid[biz.Skill](ctx, o.data, uid, o.loadAssociations)
}

func (o *skillsRepo) DeleteByID(ctx context.Context, id int64) error {
	return Delete(ctx, o.data, &biz.Skill{ID: id})
}

func (o *skillsRepo) DeleteByUid(ctx context.Context, uid string) error {
	return Delete(ctx, o.data, &biz.Skill{Name: uid})
}

func (o *skillsRepo) buildListQuery(ctx context.Context, p *biz.SkillListFilter) *gorm.DB {
	q := o.data.WithCtx(ctx)
	if len(p.Uids) > 0 {
		q = q.Where(biz.SkillSfldName+" IN ?", p.Uids)
	}
	if p.NamePattern != "" {
		q = q.Where(biz.SkillSfldName+" LIKE ?", "%"+p.NamePattern+"%")
	}
	return q
}

func (o *skillsRepo) List(ctx context.Context, p *biz.SkillListFilter, pager biz.Pager) ([]*biz.Skill, error) {
	datas := []*biz.Skill{}
	q := o.buildListQuery(ctx, p).Order("id")
	if len(p.Uids) == 0 {
		q = q.Offset(pager.Offset()).Limit(pager.Pagesz)
	}
	err := q.Find(&datas).Error
	if err != nil {
		return nil, err
	}
	return datas, nil
}

func (o *skillsRepo) Count(ctx context.Context, p *biz.SkillListFilter) (int, error) {
	mod := &biz.Skill{}
	var cnt int64
	q := o.buildListQuery(ctx, p)
	err := q.Model(mod).Count(&cnt).Error
	if err != nil {
		return 0, err
	}
	return int(cnt), nil
}
