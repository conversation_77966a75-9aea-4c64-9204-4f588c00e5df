package client

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/apis/iam/v1"
	iamv1 "gitlab.rp.konvery.work/platform/apis/iam/v1"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/emptypb"
)

type (
	User           = iamv1.User
	BaseUser       = iamv1.BaseUser
	UserContext    = iam.UserContext
	Team           = iamv1.Team
	UsersClient    = iamv1.UsersClient
	TeamsClient    = iamv1.TeamsClient
	PoliciesClient = iamv1.PoliciesClient
)

type IAM interface {
	UsersClient
	TeamsClient
	PoliciesClient
}

func NewIAM(conn *grpc.ClientConn) IAM {
	uc := iamv1.NewUsersClient(conn)
	tc := iamv1.NewTeamsClient(conn)
	pc := iamv1.NewPoliciesClient(conn)
	return struct {
		UsersClient
		TeamsClient
		PoliciesClient
	}{uc, tc, pc}
}

var iamsvc IAM

func InitIAM(service *Service) {
	iamsvc = NewIAM(newGrpcConn(service))
}

func SetIAMSvc(iam IAM) {
	iamsvc = iam
}

func ListUsersInMap(ctx context.Context, uids []string) (map[string]*BaseUser, error) {
	uuids := lo.Uniq(uids)
	if len(uuids) == 0 {
		return make(map[string]*BaseUser), nil
	}
	resp, err := iamsvc.ListUser(ctx, &iamv1.ListUserRequest{Uids: uuids, Page: 0, Pagesz: 500})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return lo.SliceToMap(resp.Users, func(u *User) (string, *BaseUser) { return u.Uid, ToBaseUser(u) }), nil
}

func GetUser(ctx context.Context, uid string) (*User, error) {
	u, err := iamsvc.GetUser(ctx, &iamv1.GetUserRequest{Uid: uid})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return u, nil
}

func GetMe(ctx context.Context) (*User, error) {
	u, err := iamsvc.GetMe(ctx, &emptypb.Empty{})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return u, nil
}

func GetMe2(ctx context.Context) (*UserContext, error) {
	uc, err := iamsvc.GetMe2(ctx, &emptypb.Empty{})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return uc, nil
}

func GetTeam(ctx context.Context, uid string) (*Team, error) {
	t, err := iamsvc.GetTeam(ctx, &iamv1.GetTeamRequest{Uid: uid})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return t, nil
}

func ListTeamsInMap(ctx context.Context, uids []string) (map[string]*BaseUser, error) {
	ids := lo.Map(uids, func(v string, _ int) int64 { return kid.ParseID(v) })
	ids = lo.Uniq(ids)
	if len(ids) == 0 {
		return make(map[string]*BaseUser), nil
	}
	// this API will by-pass permission check
	resp, err := iamsvc.ListTeamByIDs(ctx, &iamv1.ListTeamByIDsRequest{Ids: ids})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return lo.SliceToMap(resp.Teams, func(t *Team) (string, *BaseUser) { return t.Uid, ToBaseUser(t) }), nil
}

func ListOrgTeams(ctx context.Context, orgUid string) ([]*Team, error) {
	// TODO: bypass team count
	rsp, err := iamsvc.ListTeam(ctx, &iamv1.ListTeamRequest{ParentUid: orgUid, Pagesz: 100})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return rsp.Teams, nil
}

func GetTeamMembers(ctx context.Context, uid string) ([]*User, error) {
	rep, err := iamsvc.ListMembers(ctx, &iamv1.ListMembersRequest{Uid: uid, Pagesz: 100})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return rep.Members, nil
}

func IsAllowed(ctx context.Context, uid, perm, objCls, object string) bool {
	if uid == "" {
		uid = UserFromCtx(ctx).GetUser().GetUid()
	}
	if objCls != "" {
		object = objCls + ":" + object
	}
	rp, err := iamsvc.IsAllowed(ctx, &iamv1.IsAllowedRequest{
		Uid: uid,
		Action: &iamv1.IsAllowedRequest_Action{
			Resource: object,
			Perm:     objCls + "." + perm,
		}})
	fmt.Printf("---> iam client - check - uid: %s,  perm: %s, objCls: %s, object: %s\n", uid, perm, objCls, object)
	fmt.Println("---> iam client - check is allowed: ", rp, err)
	//return true
	return err == nil && rp.Allowed
}

func ToBaseUser(v any) *BaseUser {
	if v == nil {
		return nil
	}
	switch u := v.(type) {
	case *User:
		return &BaseUser{
			Uid:    u.Uid,
			Name:   u.Name,
			Avatar: u.Avatar,
		}
	case *Team:
		return &BaseUser{
			Uid:    u.Uid,
			Name:   u.Name,
			Avatar: u.Avatar,
		}
	}
	panic("unsupported type")
}

// CreateAccessPolicies attaches access policies to the resource.
func CreateAccessPolicies(ctx context.Context, cls, name string, owners, parents []string) error {
	_, err := iamsvc.CreateResource(ctx, &iamv1.CreateResourceRequest{
		Name:    cls + ":" + name,
		Owners:  owners,
		Parents: parents,
	})
	if err != nil {
		return errors.FromError(err)
	}
	return nil
}

// DeleteAccessPolicies deletes resource's access policies.
func DeleteAccessPolicies(ctx context.Context, cls, name string) error {
	_, err := iamsvc.DeleteResource(ctx, &iamv1.DeleteResourceRequest{Name: cls + ":" + name})
	if err != nil {
		return errors.FromError(err)
	}
	return nil
}

// RevokeUsersRole deletes users' role from a resource's access policies.
func RevokeUsersRole(ctx context.Context, cls, name, role string, users ...string) error {
	_, err := iamsvc.RevokeUsersRole(ctx, &iamv1.RevokeUsersRoleRequest{
		Resource: cls + ":" + name,
		Role:     role,
		Users:    users,
	})
	if err != nil {
		return errors.FromError(err)
	}
	return nil
}

func GetAttachedPolicies(ctx context.Context, resource, role string) (policies []string, err error) {
	rsp, err := iamsvc.GetAttachedPolicies(ctx, &iamv1.GetAttachedPoliciesRequest{
		Name: resource, Role: role})
	if err != nil {
		return nil, errors.FromError(err)
	}
	return rsp.PolicyNames, nil
}

func CreatePolicy(ctx context.Context, resource, role string, users []string) (name string, err error) {
	p, err := iamsvc.CreatePolicy(ctx, &iamv1.CreatePolicyRequest{
		Resource: resource, Role: role, Users: users})
	if err != nil {
		return "", errors.FromError(err)
	}
	return p.Name, nil
}

func UpdatePolicy(ctx context.Context, name string, users []string) (err error) {
	_, err = iamsvc.UpdatePolicy(ctx, &iamv1.UpdatePolicyRequest{
		Name: name, Users: users})
	if err != nil {
		return errors.FromError(err)
	}
	return nil
}
