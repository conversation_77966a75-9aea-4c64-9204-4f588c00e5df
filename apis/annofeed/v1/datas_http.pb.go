// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: annofeed/v1/datas.proto

package annofeed

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationDatasCreateData = "/annofeed.v1.Datas/CreateData"
const OperationDatasDeleteData = "/annofeed.v1.Datas/DeleteData"
const OperationDatasFindDataElements = "/annofeed.v1.Datas/FindDataElements"
const OperationDatasGetData = "/annofeed.v1.Datas/GetData"
const OperationDatasGetDataElements = "/annofeed.v1.Datas/GetDataElements"
const OperationDatasGetDataMeta = "/annofeed.v1.Datas/GetDataMeta"
const OperationDatasListData = "/annofeed.v1.Datas/ListData"
const OperationDatasParseData = "/annofeed.v1.Datas/ParseData"
const OperationDatasSetRawdataEmbedding = "/annofeed.v1.Datas/SetRawdataEmbedding"
const OperationDatasUpdateData = "/annofeed.v1.Datas/UpdateData"

type DatasHTTPServer interface {
	CreateData(context.Context, *CreateDataRequest) (*Data, error)
	DeleteData(context.Context, *DeleteDataRequest) (*emptypb.Empty, error)
	// FindDataElements Find a list of elements (only names and indexes are returned)
	FindDataElements(context.Context, *FindDataElementsRequest) (*GetDataElementsReply, error)
	GetData(context.Context, *GetDataRequest) (*Data, error)
	// GetDataElements Fetch a list of elements
	GetDataElements(context.Context, *GetDataElementsRequest) (*GetDataElementsReply, error)
	// GetDataMeta Get data meta
	GetDataMeta(context.Context, *GetDataMetaRequest) (*GetDataMetaReply, error)
	ListData(context.Context, *ListDataRequest) (*ListDataReply, error)
	// ParseData Parse data
	ParseData(context.Context, *ParseDataRequest) (*emptypb.Empty, error)
	// SetRawdataEmbedding Set rawdata embedding.
	SetRawdataEmbedding(context.Context, *SetRawdataEmbeddingRequest) (*SetRawdataEmbeddingReply, error)
	UpdateData(context.Context, *UpdateDataRequest) (*Data, error)
}

func RegisterDatasHTTPServer(s *http.Server, srv DatasHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/datas", _Datas_CreateData0_HTTP_Handler(srv))
	r.PATCH("/v1/datas/{data.uid}", _Datas_UpdateData0_HTTP_Handler(srv))
	r.DELETE("/v1/datas/{uid}", _Datas_DeleteData0_HTTP_Handler(srv))
	r.GET("/v1/datas/{uid}", _Datas_GetData0_HTTP_Handler(srv))
	r.GET("/v1/datas", _Datas_ListData0_HTTP_Handler(srv))
	r.GET("/v1/datas/{uid}/elements", _Datas_GetDataElements0_HTTP_Handler(srv))
	r.GET("/v1/datas/{uid}/find-elements", _Datas_FindDataElements0_HTTP_Handler(srv))
	r.GET("/v1/datas/{uid}/meta", _Datas_GetDataMeta0_HTTP_Handler(srv))
	r.PUT("/v1/datas/{uid}/rawdata-embedding", _Datas_SetRawdataEmbedding0_HTTP_Handler(srv))
	r.PUT("/v1/datas/{uid}/parse", _Datas_ParseData0_HTTP_Handler(srv))
}

func _Datas_CreateData0_HTTP_Handler(srv DatasHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateDataRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDatasCreateData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateData(ctx, req.(*CreateDataRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Data)
		return ctx.Result(200, reply)
	}
}

func _Datas_UpdateData0_HTTP_Handler(srv DatasHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateDataRequest
		if err := ctx.Bind(&in.Data); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDatasUpdateData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateData(ctx, req.(*UpdateDataRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Data)
		return ctx.Result(200, reply)
	}
}

func _Datas_DeleteData0_HTTP_Handler(srv DatasHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteDataRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDatasDeleteData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteData(ctx, req.(*DeleteDataRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Datas_GetData0_HTTP_Handler(srv DatasHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDataRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDatasGetData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetData(ctx, req.(*GetDataRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Data)
		return ctx.Result(200, reply)
	}
}

func _Datas_ListData0_HTTP_Handler(srv DatasHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListDataRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDatasListData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListData(ctx, req.(*ListDataRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListDataReply)
		return ctx.Result(200, reply)
	}
}

func _Datas_GetDataElements0_HTTP_Handler(srv DatasHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDataElementsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDatasGetDataElements)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDataElements(ctx, req.(*GetDataElementsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetDataElementsReply)
		return ctx.Result(200, reply)
	}
}

func _Datas_FindDataElements0_HTTP_Handler(srv DatasHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FindDataElementsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDatasFindDataElements)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FindDataElements(ctx, req.(*FindDataElementsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetDataElementsReply)
		return ctx.Result(200, reply)
	}
}

func _Datas_GetDataMeta0_HTTP_Handler(srv DatasHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDataMetaRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDatasGetDataMeta)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDataMeta(ctx, req.(*GetDataMetaRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetDataMetaReply)
		return ctx.Result(200, reply)
	}
}

func _Datas_SetRawdataEmbedding0_HTTP_Handler(srv DatasHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetRawdataEmbeddingRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDatasSetRawdataEmbedding)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetRawdataEmbedding(ctx, req.(*SetRawdataEmbeddingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SetRawdataEmbeddingReply)
		return ctx.Result(200, reply)
	}
}

func _Datas_ParseData0_HTTP_Handler(srv DatasHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ParseDataRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDatasParseData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ParseData(ctx, req.(*ParseDataRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type DatasHTTPClient interface {
	CreateData(ctx context.Context, req *CreateDataRequest, opts ...http.CallOption) (rsp *Data, err error)
	DeleteData(ctx context.Context, req *DeleteDataRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	FindDataElements(ctx context.Context, req *FindDataElementsRequest, opts ...http.CallOption) (rsp *GetDataElementsReply, err error)
	GetData(ctx context.Context, req *GetDataRequest, opts ...http.CallOption) (rsp *Data, err error)
	GetDataElements(ctx context.Context, req *GetDataElementsRequest, opts ...http.CallOption) (rsp *GetDataElementsReply, err error)
	GetDataMeta(ctx context.Context, req *GetDataMetaRequest, opts ...http.CallOption) (rsp *GetDataMetaReply, err error)
	ListData(ctx context.Context, req *ListDataRequest, opts ...http.CallOption) (rsp *ListDataReply, err error)
	ParseData(ctx context.Context, req *ParseDataRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	SetRawdataEmbedding(ctx context.Context, req *SetRawdataEmbeddingRequest, opts ...http.CallOption) (rsp *SetRawdataEmbeddingReply, err error)
	UpdateData(ctx context.Context, req *UpdateDataRequest, opts ...http.CallOption) (rsp *Data, err error)
}

type DatasHTTPClientImpl struct {
	cc *http.Client
}

func NewDatasHTTPClient(client *http.Client) DatasHTTPClient {
	return &DatasHTTPClientImpl{client}
}

func (c *DatasHTTPClientImpl) CreateData(ctx context.Context, in *CreateDataRequest, opts ...http.CallOption) (*Data, error) {
	var out Data
	pattern := "/v1/datas"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDatasCreateData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DatasHTTPClientImpl) DeleteData(ctx context.Context, in *DeleteDataRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/datas/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDatasDeleteData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DatasHTTPClientImpl) FindDataElements(ctx context.Context, in *FindDataElementsRequest, opts ...http.CallOption) (*GetDataElementsReply, error) {
	var out GetDataElementsReply
	pattern := "/v1/datas/{uid}/find-elements"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDatasFindDataElements))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DatasHTTPClientImpl) GetData(ctx context.Context, in *GetDataRequest, opts ...http.CallOption) (*Data, error) {
	var out Data
	pattern := "/v1/datas/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDatasGetData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DatasHTTPClientImpl) GetDataElements(ctx context.Context, in *GetDataElementsRequest, opts ...http.CallOption) (*GetDataElementsReply, error) {
	var out GetDataElementsReply
	pattern := "/v1/datas/{uid}/elements"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDatasGetDataElements))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DatasHTTPClientImpl) GetDataMeta(ctx context.Context, in *GetDataMetaRequest, opts ...http.CallOption) (*GetDataMetaReply, error) {
	var out GetDataMetaReply
	pattern := "/v1/datas/{uid}/meta"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDatasGetDataMeta))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DatasHTTPClientImpl) ListData(ctx context.Context, in *ListDataRequest, opts ...http.CallOption) (*ListDataReply, error) {
	var out ListDataReply
	pattern := "/v1/datas"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDatasListData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DatasHTTPClientImpl) ParseData(ctx context.Context, in *ParseDataRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/datas/{uid}/parse"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDatasParseData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DatasHTTPClientImpl) SetRawdataEmbedding(ctx context.Context, in *SetRawdataEmbeddingRequest, opts ...http.CallOption) (*SetRawdataEmbeddingReply, error) {
	var out SetRawdataEmbeddingReply
	pattern := "/v1/datas/{uid}/rawdata-embedding"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDatasSetRawdataEmbedding))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *DatasHTTPClientImpl) UpdateData(ctx context.Context, in *UpdateDataRequest, opts ...http.CallOption) (*Data, error) {
	var out Data
	pattern := "/v1/datas/{data.uid}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDatasUpdateData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in.Data, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
