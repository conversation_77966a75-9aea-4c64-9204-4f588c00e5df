package fetcher

import (
	"archive/zip"
	"context"
	"fmt"
	"io"
	"io/fs"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"strings"

	"annofeed/internal/biz"
	"annofeed/workflow/common"

	"gitlab.rp.konvery.work/platform/pkg/download"
	"gitlab.rp.konvery.work/platform/pkg/kfs"
	"gitlab.rp.konvery.work/platform/pkg/progress"
	"gitlab.rp.konvery.work/platform/pkg/wf/wfutil"

	"github.com/samber/lo"
)

type Heartbeat = common.Heartbeat

func Fetch(ctx context.Context, data *biz.Data, dir string, startIdx int, hb Heartbeat) (err error) {
	fetcher := ""
	if data.Source.E.Proprietary != nil {
		fetcher = data.Source.E.Proprietary.Type
	}
	fmt.Println("---> fetcher: ", fetcher)
	switch fetcher {
	case "", "default":
		err = defaultFetch(ctx, data, dir, startIdx, hb)
	default:
		err = wfutil.NewNonRetryableError("unsupported fetcher "+fetcher, nil)
	}
	return
}

func defaultFetch(ctx context.Context, data *biz.Data, dir string, startIdx int, hb Heartbeat) (err error) {
	if err := kfs.MkdirAll(dir, 0777); err != nil {
		return err
	}
	tmpdir, err := os.MkdirTemp(dir, ".tmp")
	fmt.Println("---> fetch tmp dir: ", tmpdir)
	if err != nil {
		return fmt.Errorf("failed to create temporary directory: %w", err)
	}
	defer os.RemoveAll(tmpdir)
	dload := download.New(&download.Config{WorkDir: tmpdir})

	uris := data.Source.E.Uris
	fmt.Println("---> fetch uris: ", uris)
	for i := startIdx; i < len(uris); i++ {
		uri := uris[i]
		file, err := dload.Download(ctx, &download.File{
			URI:    uri,
			Report: func(p *progress.Progress) { hb(ctx) },
		})
		if err != nil {
			return fmt.Errorf("failed to download file %v: %w", uri, err)
		}

		zipFile, err := checkZipFile(file)
		if err != nil {
			return fmt.Errorf("failed to check zip file %v: %w", uri, err)
		}
		if zipFile {
			fmt.Println("---> zip file: ", file)
			err = extractZipFile(ctx, file, dir, hb)
			if err != nil {
				return wfutil.NewNonRetryableError("failed to extract file "+uri, err)
			}

			if err = os.Remove(file); err != nil {
				return fmt.Errorf("failed to remove file %v: %w", file, err)
			}
		} else {
			dstFilePath := ""
			fileName := getFileName(uri, file) // keep original name if possible
			if strings.ToLower(filepath.Ext(fileName)) == ".json" {
				dstFilePath = filepath.Join(dir, fileName)
			} else {
				dstFilePath = filepath.Join(dir, common.DataPrefix, fileName)
			}
			if err = moveFileBySrcFilePath(file, dstFilePath); err != nil {
				return fmt.Errorf("failed to move file %s to %s: %w", file, dstFilePath, err)
			}
		}

		hb(ctx, i)
	}

	for expectedName, uri := range data.Source.E.GetNamedUris() {
		file, err := dload.Download(ctx, &download.File{
			URI:    uri,
			Report: func(p *progress.Progress) { hb(ctx) },
		})
		if err != nil {
			return fmt.Errorf("failed to download file %v: %w", uri, err)
		}

		dstFile := filepath.Join(dir, expectedName)
		if err := moveFileBySrcFilePath(file, dstFile); err != nil {
			return fmt.Errorf("failed to rename file %s to %s: %w", file, dstFile, err)
		}

		hb(ctx, expectedName)
	}

	return
}

// getFileName returns the last part of uri path if it is a valid name, otherwise,
// it returns filepath.Base(fpath).
func getFileName(uri, fpath string) string {
	u, _ := url.Parse(uri)
	if u != nil {
		name := path.Base(u.Path)
		if strings.TrimLeft(name, "./") != "" {
			return name
		}
	}
	return filepath.Base(fpath)
}

// checkZipFile checks if a file is a zip file.
func checkZipFile(filePath string) (bool, error) {
	fr, err := os.Open(filePath)
	if err != nil {
		return false, fmt.Errorf("failed to open file %v: %w", filePath, err)
	}
	defer fr.Close()

	// Read the first 4 bytes of the file to check if it matches the ZIP file signature
	buffer := make([]byte, 4)
	_, err = fr.Read(buffer)
	if err != nil {
		return false, err
	}

	zipSignature := []byte{0x50, 0x4b, 0x03, 0x04}
	return string(buffer) == string(zipSignature), nil
}

// moveFileBySrcFilePath moves a file to a new path.
func moveFileBySrcFilePath(srcFilePath, dstFilePath string) error {
	// make sure that the dir of dstFilePath exists
	dir := filepath.Dir(dstFilePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory %v: %w", dir, err)
	}

	if err := os.Rename(srcFilePath, dstFilePath); err != nil {
		return fmt.Errorf("failed to link file %s to %s: %w", srcFilePath, dstFilePath, err)
	}
	return nil
}

func extractZipFile(ctx context.Context, zipfile string, dstdir string, hb Heartbeat) error {
	fmt.Println("---> extract zip file: ", zipfile, dstdir)
	_ = PrintTree(dstdir)
	// Open a zip archive for reading.
	r, err := zip.OpenReader(zipfile)
	if err != nil {
		return err
	}
	defer r.Close()

	// Iterate through the files in the archive
	fmt.Println("---> zipFiles: ", zipfile, len(r.File))
	fmt.Println("+++++++++++++++++++++++++++")
	// o.log.WithContext(ctx).Infof("extracting zip file %v: found %v items", zipfile, len(r.File))
	for _, f := range r.File {
		fi := f.FileInfo()
		fmt.Println("---> fileInfo: ", fi.Name(), fi.IsDir(), fi.Size())
		// o.log.WithContext(ctx).Debugf("save item #%v: path=%v, isdir=%v, size=%v", i+1, f.Name, fi.IsDir(), fi.Size())

		// skip directory entries and system files
		if fi.IsDir() || isSystemFile(f.Name) {
			fmt.Println("---> skipping: ", f.Name, fi.IsDir())
			// 	dir := filepath.Join(dstdir, path.Clean(f.Name))
			// 	err := os.MkdirAll(dir, 0755)
			// 	if err != nil {
			// 		return fmt.Errorf("failed to create directory %v: %w", dir, err)
			// 	}
			continue
		}
		if strings.HasSuffix(f.Name, ".DS_Store") {
			fmt.Println("---> skipping ds_store: ", f.Name)
			fmt.Println()
			continue
		}

		rc, err := f.Open()
		if err != nil {
			return fmt.Errorf("failed to open file %v: %w", f.Name, err)
		}
		name := filepath.Join(dstdir, f.Name)
		dir := filepath.Dir(name)
		fmt.Println("---> mk file dir: ", dir, f.Name)
		fmt.Println()
		err = os.MkdirAll(dir, 0755)
		if err != nil {
			return fmt.Errorf("failed to create directory %v: %w", dir, err)
		}
		file, err := os.Create(name)
		if err != nil {
			rc.Close()
			return fmt.Errorf("failed to create file %v: %w", name, err)
		}
		_, err = io.Copy(file, rc)
		rc.Close()
		file.Close()
		if err != nil {
			return fmt.Errorf("failed to save file %v: %w", name, err)
		}

		hb(ctx)
	}
	fmt.Printf("解压完毕，列出目录 '%s' 下的所有文件...\n", dstdir)
	_ = PrintTree(dstdir)
	fmt.Println("+++++++++++++++++++++++++++")
	return nil
}

func isSystemFile(path string) bool {
	var sysFilePathPrefix = []string{"__MACOSX/"}
	var sysFolder = []string{"/__MACOSX/"}
	return lo.SomeBy(sysFilePathPrefix, func(v string) bool { return strings.HasPrefix(path, v) }) ||
		lo.SomeBy(sysFolder, func(v string) bool { return strings.Contains(path, v) })
}

// PrintTree 函数以树状结构带缩进的方式打印给定路径的目录结构。
func PrintTree(dirPath string) error {
	fmt.Printf("正在生成目录 '%s' 的树状结构...\n", dirPath)

	// 获取 dirPath 的绝对路径以确保正确的相对深度计算
	absDirPath, err := filepath.Abs(dirPath)
	if err != nil {
		return fmt.Errorf("获取目录 '%s' 的绝对路径失败: %w", dirPath, err)
	}

	err = filepath.Walk(absDirPath, func(path string, info fs.FileInfo, err error) error {
		if err != nil {
			// 如果是权限错误，可能需要特殊处理，比如打印警告但继续
			if os.IsPermission(err) {
				fmt.Printf("无法访问 '%s' (权限不足): %v\n", path, err)
				return nil // 返回 nil 允许 Walk 继续遍历其他路径
			}
			return fmt.Errorf("遍历目录 '%s' 时出错: %w", path, err)
		}

		// 计算当前路径相对于起始目录的深度
		// 使用 filepath.Rel 获取相对路径，更容易计算深度
		relPath, err := filepath.Rel(absDirPath, path)
		if err != nil {
			return fmt.Errorf("计算相对路径失败: %w", err)
		}

		// 根据相对路径的层级计算缩进深度
		depth := 0
		if relPath != "." { // 如果不是根目录本身
			// 每一层级对应一个分隔符，所以需要加上1（表示第一层子目录）
			depth = strings.Count(relPath, string(filepath.Separator)) + 1
		}

		// 如果当前路径是起始目录本身，特殊处理深度为 0
		if path == absDirPath {
			depth = 0
		}

		indent := strings.Repeat("    ", depth) // 每个层级使用4个空格缩进

		// 打印带有缩进的文件或目录名称
		if path == absDirPath { // 根目录特殊处理，不缩进，显示其名称
			if info.IsDir() {
				fmt.Println(info.Name() + "/")
			} else {
				// 理论上，传入的 dirPath 应该是一个目录，但以防万一
				fmt.Println(info.Name())
			}
		} else if info.IsDir() {
			fmt.Printf("%s📁 %s/\n", indent, info.Name())
		} else {
			fmt.Printf("%s📄 %s\n", indent, info.Name())
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("生成目录树失败: %w", err)
	}

	fmt.Println("目录树已生成完毕。")
	return nil
}
