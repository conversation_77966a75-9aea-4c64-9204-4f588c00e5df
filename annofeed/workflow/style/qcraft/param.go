package qcraft

import (
	"encoding/json"
	"fmt"
	"strings"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/kmath"
)

type Params struct {
	// frame index => (x,y,z,rx,ry,rz,rw)
	Poses [][]float64

	// LiDAR name => (x,y,z,rx,ry,rz,rw)
	LidarExtrinsic map[string][]float64
	// Camera name => (x,y,z,rx,ry,rz,rw)
	CameraExtrinsic map[string][]float64
	// Camera name => (fx,fy,cx,cy)
	CameraIntrinsic map[string][]float64
	// Camera name => (distortion_type, k1,k2,...)
	CameraDistortion      map[string][]float64
	CameraNameToLidarName map[string]string
	FrameSize             int // number of rawdatas in a frame

	// transform matrixes
	Transformers map[string]*Matrix `json:",omitempty"`
}

type Matrix struct {
	Rows   int
	Values []float64
}

// image matrix to be applied after all extrinsic params but before all intrinsic params
const transformerImgInter = "img-inter"

func (o *Params) GetImageTransforms(camName string) []*anno.RawdataParam {
	r := make([]*anno.RawdataParam, 0, 3)
	if m := o.Transformers[transformerImgInter]; m != nil {
		r = append(r, &anno.RawdataParam{
			Type:      anno.RawdataParam_Type_matrix,
			ColumnCnt: int32(m.Rows),
			Data:      m.Values,
		})
	}
	p := o.CameraIntrinsic[camName]
	r = append(r, &anno.RawdataParam{
		Type:      anno.RawdataParam_Type_intrinsic,
		ColumnCnt: 1,
		Data:      p,
	})
	p = o.CameraDistortion[camName]
	r = append(r, &anno.RawdataParam{
		Type:      anno.RawdataParam_Type_distortion,
		ColumnCnt: 1,
		Data:      p,
	})
	return r
}

func convertEulerAngles(vf []float64) ([]float64, error) {
	// x,y,z,yaw,pitch,roll
	if len(vf) != 6 {
		return nil, fmt.Errorf("invalid euler angles: %v", vf)
	}
	x := kmath.FromEulerAngles(kmath.RotationOrderXYZ, vf[5], vf[4], vf[3])
	q := make([]float64, 0, 7)
	q = append(q, vf[:3]...)
	q = append(q, x[:]...)
	return q, nil
}

func ParsePoses(o *Params, data []byte) error {
	err := json.Unmarshal(data, &o.Poses)
	if err != nil {
		return err
	}
	// convert Euler angles (yaw,pitch,roll) to Quaternion
	for i, v := range o.Poses {
		q, err := convertEulerAngles(v)
		if err != nil {
			return fmt.Errorf("invalid pose at #%v: %w", i+1, err)
		}
		o.Poses[i] = q
	}
	return nil
}

// CommonNameParams helps to parse param files with the format:
// [[["name1", ...],[v1, v2, ...], [va, vb, ...], ...], ...]
type CommonNameParams struct {
	Names  []string
	Params [][]float64
}

func NewCommonNameParams(data []byte) ([]CommonNameParams, error) {
	vvv := [][][]any{}
	if err := json.Unmarshal(data, &vvv); err != nil {
		return nil, err
	}

	nps := make([]CommonNameParams, len(vvv))
	for i, vv := range vvv {
		np := &nps[i]
		if len(vv) < 2 {
			return nil, fmt.Errorf("expect name slice followed by param slices")
		}
		// parse name slice
		np.Names = make([]string, len(vv[0]))
		for j, v := range vv[0] {
			name, ok := v.(string)
			if !ok {
				return nil, fmt.Errorf("expect string names")
			}
			np.Names[j] = name
		}

		// parse param slices
		np.Params = make([][]float64, len(vv)-1)
		for j := 1; j < len(vv); j++ {
			np.Params[j-1] = make([]float64, len(vv[j]))
			for k, v := range vv[j] {
				f, ok := v.(float64)
				if !ok {
					return nil, fmt.Errorf("expect numbers")
				}
				np.Params[j-1][k] = f
			}
		}
	}
	return nps, nil
}

func ParseLidarExtrinsic(o *Params, data []byte) (err error) {
	nps, err := NewCommonNameParams(data)
	if err != nil {
		return
	}
	l := make(map[string][]float64, len(nps))
	defer func() {
		if err == nil {
			o.LidarExtrinsic = l
		}
	}()
	for _, np := range nps {
		if len(np.Names) != 1 {
			return fmt.Errorf("expect exactly one LiDAR name")
		}
		name := strings.ToLower(np.Names[0])

		if len(np.Params) != 1 {
			return fmt.Errorf("expect exactly one LiDAR extrinsic param slice")
		}
		q, err := convertEulerAngles(np.Params[0])
		if err != nil {
			return fmt.Errorf("LiDAR extrinsic param length is not 6")
		}
		l[name] = q
	}
	return
}

func ParseCameraParams(o *Params, data []byte) (err error) {
	nps, err := NewCommonNameParams(data)
	if err != nil {
		return
	}
	ex := make(map[string][]float64, len(nps))
	in := make(map[string][]float64, len(nps))
	distortion := make(map[string][]float64, len(nps))
	cam2Lidar := make(map[string]string, len(nps))
	defer func() {
		if err == nil {
			o.CameraExtrinsic = ex
			o.CameraIntrinsic = in
			o.CameraNameToLidarName = cam2Lidar
			o.CameraDistortion = distortion
		}
	}()
	for _, np := range nps {
		if len(np.Names) != 2 {
			return fmt.Errorf("expect exactly a Camera and LiDAR name pair")
		}
		camName := np.Names[0][len("CAM_PBQ_"):]
		camName = "cam_" + strings.ToLower(camName)
		cam2Lidar[camName] = strings.ToLower(np.Names[1])

		if len(np.Params) != 2 {
			return fmt.Errorf("expect exactly an intrinsic and an extrinsic param slices")
		}
		vf := np.Params[0]
		switch len(vf) {
		case 9:
			distortion[camName] = append([]float64{float64(anno.RawdataParam_DistortionType_pinhole)}, vf[:5]...)
		case 12:
			distortion[camName] = append([]float64{float64(anno.RawdataParam_DistortionType_pinhole)}, vf[:8]...)
		default:
			return fmt.Errorf("invalid camera distortion param length")
		}
		in[camName] = vf[len(vf)-4:]
		q, err := convertEulerAngles(np.Params[1])
		if err != nil {
			return fmt.Errorf("Camera extrinsic param length is not 6")
		}
		ex[camName] = q
	}
	return
}
