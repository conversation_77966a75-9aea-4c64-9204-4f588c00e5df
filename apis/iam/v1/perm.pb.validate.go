// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: iam/v1/perm.proto

package iam

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on EditPermsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EditPermsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EditPermsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EditPermsRequestMultiError, or nil if none found.
func (m *EditPermsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EditPermsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _EditPermsRequest_Action_NotInLookup[m.GetAction()]; ok {
		err := EditPermsRequestValidationError{
			field:  "Action",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := EditAction_Enum_name[int32(m.GetAction())]; !ok {
		err := EditPermsRequestValidationError{
			field:  "Action",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetPerms() {
		_, _ = idx, item

		if len(item) > 64 {
			err := EditPermsRequestValidationError{
				field:  fmt.Sprintf("Perms[%v]", idx),
				reason: "value length must be at most 64 bytes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_EditPermsRequest_Perms_Pattern.MatchString(item) {
			err := EditPermsRequestValidationError{
				field:  fmt.Sprintf("Perms[%v]", idx),
				reason: "value does not match regex pattern \"^\\\\w+\\\\.\\\\w+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return EditPermsRequestMultiError(errors)
	}

	return nil
}

// EditPermsRequestMultiError is an error wrapping multiple validation errors
// returned by EditPermsRequest.ValidateAll() if the designated constraints
// aren't met.
type EditPermsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EditPermsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EditPermsRequestMultiError) AllErrors() []error { return m }

// EditPermsRequestValidationError is the validation error returned by
// EditPermsRequest.Validate if the designated constraints aren't met.
type EditPermsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EditPermsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EditPermsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EditPermsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EditPermsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EditPermsRequestValidationError) ErrorName() string { return "EditPermsRequestValidationError" }

// Error satisfies the builtin error interface
func (e EditPermsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEditPermsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EditPermsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EditPermsRequestValidationError{}

var _EditPermsRequest_Action_NotInLookup = map[EditAction_Enum]struct{}{
	0: {},
}

var _EditPermsRequest_Perms_Pattern = regexp.MustCompile("^\\w+\\.\\w+$")

// Validate checks the field values on ListPermClassReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPermClassReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPermClassReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPermClassReplyMultiError, or nil if none found.
func (m *ListPermClassReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPermClassReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListPermClassReplyMultiError(errors)
	}

	return nil
}

// ListPermClassReplyMultiError is an error wrapping multiple validation errors
// returned by ListPermClassReply.ValidateAll() if the designated constraints
// aren't met.
type ListPermClassReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPermClassReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPermClassReplyMultiError) AllErrors() []error { return m }

// ListPermClassReplyValidationError is the validation error returned by
// ListPermClassReply.Validate if the designated constraints aren't met.
type ListPermClassReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPermClassReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPermClassReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPermClassReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPermClassReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPermClassReplyValidationError) ErrorName() string {
	return "ListPermClassReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListPermClassReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPermClassReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPermClassReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPermClassReplyValidationError{}

// Validate checks the field values on ListPermRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListPermRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPermRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPermRequestMultiError, or nil if none found.
func (m *ListPermRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPermRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListPermRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	// no validation rules for Class

	// no validation rules for NamePattern

	if len(errors) > 0 {
		return ListPermRequestMultiError(errors)
	}

	return nil
}

// ListPermRequestMultiError is an error wrapping multiple validation errors
// returned by ListPermRequest.ValidateAll() if the designated constraints
// aren't met.
type ListPermRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPermRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPermRequestMultiError) AllErrors() []error { return m }

// ListPermRequestValidationError is the validation error returned by
// ListPermRequest.Validate if the designated constraints aren't met.
type ListPermRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPermRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPermRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPermRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPermRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPermRequestValidationError) ErrorName() string { return "ListPermRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListPermRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPermRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPermRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPermRequestValidationError{}

// Validate checks the field values on ListPermReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListPermReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPermReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListPermReplyMultiError, or
// nil if none found.
func (m *ListPermReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPermReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NextPageToken

	if len(errors) > 0 {
		return ListPermReplyMultiError(errors)
	}

	return nil
}

// ListPermReplyMultiError is an error wrapping multiple validation errors
// returned by ListPermReply.ValidateAll() if the designated constraints
// aren't met.
type ListPermReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPermReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPermReplyMultiError) AllErrors() []error { return m }

// ListPermReplyValidationError is the validation error returned by
// ListPermReply.Validate if the designated constraints aren't met.
type ListPermReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPermReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPermReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPermReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPermReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPermReplyValidationError) ErrorName() string { return "ListPermReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListPermReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPermReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPermReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPermReplyValidationError{}
