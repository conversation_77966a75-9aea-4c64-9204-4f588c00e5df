// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: annout/v1/reapers.proto

package annout

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	v1 "gitlab.rp.konvery.work/platform/apis/anno/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateReaperRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lot UID
	LotUid string `protobuf:"bytes,1,opt,name=lot_uid,json=lotUid,proto3" json:"lot_uid,omitempty"`
	// order UID
	OrderUid string `protobuf:"bytes,2,opt,name=order_uid,json=orderUid,proto3" json:"order_uid,omitempty"`
	// reaper config
	Config *v1.OutConfig `protobuf:"bytes,3,opt,name=config,proto3" json:"config,omitempty"`
	// lot ontologies
	Ontologies *v1.Lotontologies `protobuf:"bytes,4,opt,name=ontologies,proto3" json:"ontologies,omitempty"`
	// data UID
	DataUid string `protobuf:"bytes,5,opt,name=data_uid,json=dataUid,proto3" json:"data_uid,omitempty"`
}

func (x *CreateReaperRequest) Reset() {
	*x = CreateReaperRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annout_v1_reapers_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateReaperRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateReaperRequest) ProtoMessage() {}

func (x *CreateReaperRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annout_v1_reapers_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateReaperRequest.ProtoReflect.Descriptor instead.
func (*CreateReaperRequest) Descriptor() ([]byte, []int) {
	return file_annout_v1_reapers_proto_rawDescGZIP(), []int{0}
}

func (x *CreateReaperRequest) GetLotUid() string {
	if x != nil {
		return x.LotUid
	}
	return ""
}

func (x *CreateReaperRequest) GetOrderUid() string {
	if x != nil {
		return x.OrderUid
	}
	return ""
}

func (x *CreateReaperRequest) GetConfig() *v1.OutConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *CreateReaperRequest) GetOntologies() *v1.Lotontologies {
	if x != nil {
		return x.Ontologies
	}
	return nil
}

func (x *CreateReaperRequest) GetDataUid() string {
	if x != nil {
		return x.DataUid
	}
	return ""
}

type GetReaperRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotUid string `protobuf:"bytes,1,opt,name=lot_uid,json=lotUid,proto3" json:"lot_uid,omitempty"`
}

func (x *GetReaperRequest) Reset() {
	*x = GetReaperRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annout_v1_reapers_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReaperRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReaperRequest) ProtoMessage() {}

func (x *GetReaperRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annout_v1_reapers_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReaperRequest.ProtoReflect.Descriptor instead.
func (*GetReaperRequest) Descriptor() ([]byte, []int) {
	return file_annout_v1_reapers_proto_rawDescGZIP(), []int{1}
}

func (x *GetReaperRequest) GetLotUid() string {
	if x != nil {
		return x.LotUid
	}
	return ""
}

type Reaper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State string `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *Reaper) Reset() {
	*x = Reaper{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annout_v1_reapers_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reaper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reaper) ProtoMessage() {}

func (x *Reaper) ProtoReflect() protoreflect.Message {
	mi := &file_annout_v1_reapers_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reaper.ProtoReflect.Descriptor instead.
func (*Reaper) Descriptor() ([]byte, []int) {
	return file_annout_v1_reapers_proto_rawDescGZIP(), []int{2}
}

func (x *Reaper) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

type ExportLotAnnosRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lot UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// order UID
	OrderUid string `protobuf:"bytes,2,opt,name=order_uid,json=orderUid,proto3" json:"order_uid,omitempty"`
	// option
	Option v1.ExportOrderAnnosRequest_Option_Enum `protobuf:"varint,3,opt,name=option,proto3,enum=anno.v1.ExportOrderAnnosRequest_Option_Enum" json:"option,omitempty"`
	Phases []int32                                `protobuf:"varint,4,rep,packed,name=phases,proto3" json:"phases,omitempty"`
}

func (x *ExportLotAnnosRequest) Reset() {
	*x = ExportLotAnnosRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annout_v1_reapers_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportLotAnnosRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportLotAnnosRequest) ProtoMessage() {}

func (x *ExportLotAnnosRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annout_v1_reapers_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportLotAnnosRequest.ProtoReflect.Descriptor instead.
func (*ExportLotAnnosRequest) Descriptor() ([]byte, []int) {
	return file_annout_v1_reapers_proto_rawDescGZIP(), []int{3}
}

func (x *ExportLotAnnosRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ExportLotAnnosRequest) GetOrderUid() string {
	if x != nil {
		return x.OrderUid
	}
	return ""
}

func (x *ExportLotAnnosRequest) GetOption() v1.ExportOrderAnnosRequest_Option_Enum {
	if x != nil {
		return x.Option
	}
	return v1.ExportOrderAnnosRequest_Option_Enum(0)
}

func (x *ExportLotAnnosRequest) GetPhases() []int32 {
	if x != nil {
		return x.Phases
	}
	return nil
}

var File_annout_v1_reapers_proto protoreflect.FileDescriptor

var file_annout_v1_reapers_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x61, 0x70,
	0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x61, 0x6e, 0x6e, 0x6f, 0x75,
	0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76,
	0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x6f, 0x74, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf0, 0x01, 0x0a, 0x13,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x07, 0x6c, 0x6f, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x06, 0x6c, 0x6f, 0x74,
	0x55, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x75, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x69, 0x64,
	0x12, 0x2a, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x36, 0x0a, 0x0a,
	0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x6f, 0x6e,
	0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f,
	0x67, 0x69, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x55, 0x69, 0x64, 0x3a,
	0x0d, 0xba, 0x47, 0x0a, 0xba, 0x01, 0x07, 0x6c, 0x6f, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x22, 0x51,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2e, 0x0a, 0x07, 0x6c, 0x6f, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x06, 0x6c, 0x6f, 0x74, 0x55,
	0x69, 0x64, 0x3a, 0x0d, 0xba, 0x47, 0x0a, 0xba, 0x01, 0x07, 0x6c, 0x6f, 0x74, 0x5f, 0x75, 0x69,
	0x64, 0x22, 0x1e, 0x0a, 0x06, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x22, 0xd0, 0x01, 0x0a, 0x15, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x74, 0x41,
	0x6e, 0x6e, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32,
	0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52,
	0x03, 0x75, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x75, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x69,
	0x64, 0x12, 0x4e, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x3a, 0x09, 0xba, 0x47, 0x06, 0xba, 0x01,
	0x03, 0x75, 0x69, 0x64, 0x32, 0xd5, 0x01, 0x0a, 0x07, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x73,
	0x12, 0x41, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72,
	0x12, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61,
	0x70, 0x65, 0x72, 0x12, 0x3b, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72,
	0x12, 0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x11, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72,
	0x12, 0x4a, 0x0a, 0x0e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x74, 0x41, 0x6e, 0x6e,
	0x6f, 0x73, 0x12, 0x20, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x44, 0x0a, 0x09,
	0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x35, 0x67, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77,
	0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e, 0x6e, 0x6f,
	0x75, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_annout_v1_reapers_proto_rawDescOnce sync.Once
	file_annout_v1_reapers_proto_rawDescData = file_annout_v1_reapers_proto_rawDesc
)

func file_annout_v1_reapers_proto_rawDescGZIP() []byte {
	file_annout_v1_reapers_proto_rawDescOnce.Do(func() {
		file_annout_v1_reapers_proto_rawDescData = protoimpl.X.CompressGZIP(file_annout_v1_reapers_proto_rawDescData)
	})
	return file_annout_v1_reapers_proto_rawDescData
}

var file_annout_v1_reapers_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_annout_v1_reapers_proto_goTypes = []interface{}{
	(*CreateReaperRequest)(nil),                 // 0: annout.v1.CreateReaperRequest
	(*GetReaperRequest)(nil),                    // 1: annout.v1.GetReaperRequest
	(*Reaper)(nil),                              // 2: annout.v1.Reaper
	(*ExportLotAnnosRequest)(nil),               // 3: annout.v1.ExportLotAnnosRequest
	(*v1.OutConfig)(nil),                        // 4: anno.v1.OutConfig
	(*v1.Lotontologies)(nil),                    // 5: anno.v1.Lotontologies
	(v1.ExportOrderAnnosRequest_Option_Enum)(0), // 6: anno.v1.ExportOrderAnnosRequest.Option.Enum
	(*emptypb.Empty)(nil),                       // 7: google.protobuf.Empty
}
var file_annout_v1_reapers_proto_depIdxs = []int32{
	4, // 0: annout.v1.CreateReaperRequest.config:type_name -> anno.v1.OutConfig
	5, // 1: annout.v1.CreateReaperRequest.ontologies:type_name -> anno.v1.Lotontologies
	6, // 2: annout.v1.ExportLotAnnosRequest.option:type_name -> anno.v1.ExportOrderAnnosRequest.Option.Enum
	0, // 3: annout.v1.Reapers.CreateReaper:input_type -> annout.v1.CreateReaperRequest
	1, // 4: annout.v1.Reapers.GetReaper:input_type -> annout.v1.GetReaperRequest
	3, // 5: annout.v1.Reapers.ExportLotAnnos:input_type -> annout.v1.ExportLotAnnosRequest
	2, // 6: annout.v1.Reapers.CreateReaper:output_type -> annout.v1.Reaper
	2, // 7: annout.v1.Reapers.GetReaper:output_type -> annout.v1.Reaper
	7, // 8: annout.v1.Reapers.ExportLotAnnos:output_type -> google.protobuf.Empty
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_annout_v1_reapers_proto_init() }
func file_annout_v1_reapers_proto_init() {
	if File_annout_v1_reapers_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_annout_v1_reapers_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateReaperRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annout_v1_reapers_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReaperRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annout_v1_reapers_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reaper); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annout_v1_reapers_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportLotAnnosRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_annout_v1_reapers_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_annout_v1_reapers_proto_goTypes,
		DependencyIndexes: file_annout_v1_reapers_proto_depIdxs,
		MessageInfos:      file_annout_v1_reapers_proto_msgTypes,
	}.Build()
	File_annout_v1_reapers_proto = out.File
	file_annout_v1_reapers_proto_rawDesc = nil
	file_annout_v1_reapers_proto_goTypes = nil
	file_annout_v1_reapers_proto_depIdxs = nil
}
