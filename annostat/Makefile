REPO:=annostat
GOPATH:=$(shell go env GOPATH)
VERSION:=$(shell git describe --tags --always)
LDFLAGS:="-X $(REPO)/internal/conf.Version=$(VERSION)"
INTERNAL_PROTO_FILES=$(shell find internal -name *.proto)

.PHONY: init
# init env
init:
	go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
	go install github.com/go-kratos/kratos/cmd/kratos/v2@latest
	go install github.com/go-kratos/kratos/cmd/protoc-gen-go-http/v2@latest
	go install github.com/google/gnostic/cmd/protoc-gen-openapi@latest
	go install github.com/envoyproxy/protoc-gen-validate@latest

.PHONY: config
# generate internal proto
config:
	protoc --proto_path=./internal \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./internal \
	       $(INTERNAL_PROTO_FILES)

.PHONY: build
# build
build:
	mkdir -p bin/ && go build -ldflags $(LDFLAGS) -o ./bin/ ./...

.PHONY: generate
# generate
generate:
	go mod tidy
	# go get github.com/google/wire/cmd/wire@latest
	go generate ./...

.PHONY: migrations
# migrations
migrations:
	go run gitlab.rp.konvery.work/platform/pkg/data/migrations@main \
		--outdir internal/data/migrations/autogen internal/data/migrations
	@git add -N internal/data/migrations/autogen
	@git diff --exit-code || (echo commit the changes before proceeding && false)

.PHONY: all
# generate all
all:
	make api;
	make config;
	make generate;

# show help
help:
	@echo ''
	@echo 'Usage:'
	@echo ' make [target]'
	@echo ''
	@echo 'Targets:'
	@awk '/^[a-zA-Z\-\_0-9]+:/ { \
	helpMessage = match(lastLine, /^# (.*)/); \
		if (helpMessage) { \
			helpCommand = substr($$1, 0, index($$1, ":")-1); \
			helpMessage = substr(lastLine, RSTART + 2, RLENGTH); \
			printf "\033[36m%-22s\033[0m %s\n", helpCommand,helpMessage; \
		} \
	} \
	{ lastLine = $$0 }' $(MAKEFILE_LIST)

.DEFAULT_GOAL := help
