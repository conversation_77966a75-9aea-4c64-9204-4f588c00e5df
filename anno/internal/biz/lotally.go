// source: anno/v1/lot.proto
package biz

import (
	"fmt"
	"time"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
	"gitlab.rp.konvery.work/platform/pkg/sampler"
)

type (
	Ontologies    = serial.Type[*anno.Lotontologies]
	OutConfig     = serial.Type[*anno.OutConfig]
	LotError      = serial.Type[*anno.Error]
	Phases        = serial.Slice[*anno.Lotphase]
	RejectReasons = serial.Slice[string] //[*v1.RejectReason]
	SampleBits    = serial.Slice[sampler.SampleBits]
	ToolConfig    = serial.Type[*anno.Lot_ToolConfig]
)

// Lotally contains large fields of lots
type Lotally struct {
	ID          int64      `json:"id" gorm:"default:null"`
	Ontologies  Ontologies `json:"ontologies" gorm:"default:null"`
	Out         OutConfig  `json:"out" gorm:"default:null"`
	Instruction string     `json:"instruction" gorm:"default:null"`
	SampleBits  SampleBits `json:"sample_bits" gorm:"default:null"`
	ToolConfig  ToolConfig `json:"tool_config" gorm:"default:null"`
	UpdatedAt   time.Time  `json:"updated_at" gorm:"default:null"`
	CreatedAt   time.Time  `json:"created_at" gorm:"default:null"`
	// RejectReasons RejectReasons  `json:"reject_reasons" gorm:"default:null"`
}

func (o *Lotally) GetID() int64       { return o.ID }
func (o *Lotally) IsComplexLot() bool { return o.Ontologies.E != nil && len(o.Ontologies.E.Groups) > 1 }
func (o *Lotally) IsJobSampled(phase, jobIdx int) bool {
	return IsJobSampled(o.SampleBits, phase, jobIdx)
}

const LotallyTableName = "lotallies"

var (
	Lotally_             = field.RegObject(&Lotally{})
	LotallyUpdatableFlds = field.NewModel(LotallyTableName, Lotally_, "Ontologies", "Out", "Instruction", "SampleBits")
	// , "RejectReasons"

	LotallySfldOntologies  = field.Sname(&Lotally_.Ontologies)
	LotallySfldOut         = field.Sname(&Lotally_.Out)
	LotallySfldInstruction = field.Sname(&Lotally_.Instruction)
	LotallySfldSampleBits  = field.Sname(&Lotally_.SampleBits)
	// LotallySfldRejectReasons = field.Sname(&Lotally_.RejectReasons)
)

func IsJobSampled(bits SampleBits, phase, jobIdx int) bool {
	if phase-1 < len(bits) {
		fmt.Println("bits: ", bits[phase-1])
		return bits[phase-1].Get(jobIdx)
	}
	return false
}
