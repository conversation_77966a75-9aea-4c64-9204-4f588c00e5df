package authfilter

import (
	"regexp"
	"strings"

	"gitlab.rp.konvery.work/platform/pkg/container/kset"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
)

// NoauthFilter returns true to skip the auth enforcement.
// endpoint may be the full method selector in a GRPC context, or the URL path in a HTTP context.
type NoauthFilter func(endpoint string) bool

// WhitelistNoauthFilter generates a NoauthFilter based on a list of whitelisted endpoints.
func WhitelistNoauthFilter(endpoints ...string) NoauthFilter {
	set := kset.NewSetFrom(endpoints...)
	return func(endpoint string) bool {
		return set.Has(endpoint)
	}
}

// ListNoauthFilter generates a NoauthFilter to test the endpoint towards each element in the list.
// If either of the test returns true, it returns true.
func ListNoauthFilter(test func(endpoint, elem string) bool, list ...string) NoauthFilter {
	return func(endpoint string) bool {
		for _, elem := range list {
			if test(endpoint, elem) {
				return true
			}
		}
		return false
	}
}

// PrefixNoauthFilter generates a NoauthFilter based on a list of whitelisted prefixes.
func PrefixNoauthFilter(prefixes ...string) NoauthFilter {
	return ListNoauthFilter(strings.HasPrefix, prefixes...)
}

// SuffixNoauthFilter generates a NoauthFilter based on a list of whitelisted suffixes.
func SuffixNoauthFilter(suffixes ...string) NoauthFilter {
	return ListNoauthFilter(strings.HasSuffix, suffixes...)
}

// RegexpNoauthFilter generates a NoauthFilter based on a list of regular expressions.
// If the endpoint matches any of the regular expressions, it returns true.
func RegexpNoauthFilter(exps ...string) NoauthFilter {
	x := kslice.Map(func(s string) *regexp.Regexp { return regexp.MustCompile(s) }, exps)
	return func(endpoint string) bool {
		for _, exp := range x {
			if exp.MatchString(endpoint) {
				return true
			}
		}
		return false
	}
}

// SkipAuth tests endpoint towards a list of filters.
// It returns true if either of the filters returns true.
func SkipAuth(filters []NoauthFilter, endpoint string) bool {
	for _, f := range filters {
		if f(endpoint) {
			return true
		}
	}
	return false
}
