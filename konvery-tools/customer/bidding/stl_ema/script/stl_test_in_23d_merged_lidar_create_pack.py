import json
import os
import shutil
import sys
import glob

import numpy as np

from customer.bidding.stl_ema.script.transform import Transform

sys.path.append('.')
from customer.common import tools
from customer.common import pypcd


code = 0
package_index = 0

pose_data = {}
test_in_camera_params = {
}

camera_model = {
    'front_narrow': 'pinhole',
    'front_wide': 'fisheye',
    'front_left': 'pinhole',
    'back_left': 'pinhole',
    'front_right': 'pinhole',
    'back_right': 'pinhole',
    'back': 'pinhole',
    'spherical-left': 'fisheye',
    'obstacle': 'fisheye',
    'spherical-right': 'fisheye',
    'spherical-backward': 'fisheye'
}

# cameras to be copied to package directory
camera_name_map = {
    "camera-71": "前视窄",
    "camera-73": "前视宽",
    "camera-75": "左后",
    "camera-76": "左前",
    "camera-77": "后右",
    "camera-78": "前右",
    "camera-83": "后",
}

camera_mapping_id = {
    'camera-71': 'front_narrow',
    'camera-73': 'front_wide',
    'camera-75': 'back_left',
    'camera-76': 'front_left',
    'camera-77': 'back_right',
    'camera-78': 'front_right',
    'camera-83': 'back',
}

camera_inv_mapping_id = {
    'front_narrow': 'camera-71',
    'front_wide': 'camera-73',
    'back_left': 'camera-75',
    'front_left': 'camera-76',
    'back_right': 'camera-77',
    'front_right': 'camera-78',
    'back': 'camera-83'
}


def get_code_msg(msg_code):
    msg = "失败"
    if 0 == msg_code:
        msg = "成功"
    return msg


def get_merged_pcd():
    return ""


def get_pose_data(pose_file):
    global pose_data

    data = tools.read_file(pose_file)
    for f in data:
        sep_list = f.split(" ")
        pcd = sep_list[0]
        pose = sep_list[1:]
        pose_data[pcd] = tools.pose_to_mat(pose).flatten().tolist()


def get_params(params_root_path):
    global test_in_camera_params

    camera_ids = ["front_narrow", "front_wide", "front_left", "back_left", "front_right", "back_right", "back"]
    calibration_type = 'SensorCalibration'
    camera_params = {}

    if calibration_type == 'SensorCalibration':
        camera_ids_ = camera_ids
    for camera_name in camera_ids_:
        if calibration_type == 'SensorCalibration':
            camera_name_extrinsic = 'top_center_lidar-to-' + camera_name + '-extrinsic'
            camera_name_intrinsic = camera_name + '-intrinsic'
            with open(os.path.join(params_root_path, camera_name_extrinsic + '.json')) as f:
                json_dict_extrinsic = json.load(f)
            with open(os.path.join(params_root_path, camera_name_intrinsic + '.json')) as f:
                json_dict_intrinsic = json.load(f)
            camera_params[camera_name] = Transform()
            extrinsic_mat = np.array(json_dict_extrinsic[camera_name_extrinsic]['param']['sensor_calib']['data'])
            camera_params[camera_name].SetExtrinsic2(extrinsic_mat[:3, 3], extrinsic_mat[:3, :3])
            intrinsic_mat = np.array(json_dict_intrinsic[camera_name_intrinsic]['param']['cam_K']['data'])
            camera_params[camera_name].SetIntrinsicMat(intrinsic_mat)
            dist_mat = json_dict_intrinsic[camera_name_intrinsic]['param']['cam_dist']['data']
            camera_params[camera_name].SetDistortion(dist_mat[0])
            camera_params[camera_name].SetImageHeight(json_dict_intrinsic[camera_name_intrinsic]['param']['img_dist_h'])
            camera_params[camera_name].SetImageWidth(json_dict_intrinsic[camera_name_intrinsic]['param']['img_dist_w'])

    global test_in_camera_params
    test_in_camera_params = {}

    for cam in camera_params:
        cam_data = camera_params[cam]
        extrinsic = cam_data.GetExtrinsicMat().tolist()
        intrinsic = cam_data.GetIntrinsicMat().flatten().tolist()
        distortion = cam_data.GetDistortion()
        cam_model = "pinhole" if not cam_data.is_fisheye else "fisheye"
        sensor = {}

        if cam_model == "pinhole":
            sensor = {
                "camera_model": "pinhole",
                "extrinsic": extrinsic,
                "fx": intrinsic[0],
                "fy": intrinsic[4],
                "cx": intrinsic[2],
                "cy": intrinsic[5],
                "k1": distortion[0],
                "k2": distortion[1],
                "p1": distortion[2],
                "p2": distortion[3],
                "k3": distortion[4],
                "k4": distortion[5],
                "k5": distortion[6],
                "k6": distortion[7]
            }
        else:
            sensor = {
                "camera_model": "fisheye",
                "extrinsic": extrinsic,
                "fx": intrinsic[0],
                "fy": intrinsic[4],
                "cx": intrinsic[2],
                "cy": intrinsic[5],
                "k1": distortion[0],
                "k2": distortion[1],
                "k3": distortion[4],
                "k4": distortion[5]
            }

        test_in_camera_params[camera_inv_mapping_id[cam]] = sensor

    return test_in_camera_params


def create_test_in_config(output_dir):
    global test_in_camera_params

    test_in_config_filename = output_dir + os.sep + "config.json"
    cameras = {}
    config = {
        "camera": cameras,
        "data_type": "fusion_pointcloud",
        "sensor_params": test_in_camera_params,
        # "poses": {}
    }

    for cam in camera_name_map.keys():
        cam = cam.lower()
        cameras[cam] = camera_name_map[cam]

    tools.write_json_file(file=test_in_config_filename, data=config, ensure_ascii=False, indent=4)


def add_pre_label(input_file, output_file):
    res = {
        "label_meta": {"mark_status": 0, "global": {}},
        "lidar": []
    }
    with open(input_file, encoding='utf-8') as f:
        lines = f.readlines()
    for idx, line in enumerate(lines, start=1):
        line = line.split()
        mark_data = {
            "class": line[0],
            "attrs": {},
            "track_id": idx,
            "group_id": 0,
            "annotation": {
                "type": "volume",
                "data": {
                    "position": {
                        "x": float(line[11]),
                        "y": float(line[12]),
                        "z": float(line[13]) + float(line[10]) / 2
                    },
                    "dimension": {
                        "l": float(line[8]),
                        "w": float(line[9]),
                        "h": float(line[10])
                    },
                    "rotation": {
                        "x": 0,
                        "y": 0,
                        "z": float(line[14])
                    }
                }
            }
        }
        res['lidar'].append(mark_data)
    tools.write_json_file(res, output_file)


def create_pack_2frame(input_dir, output_dir):
    """
        对原始文件进行分包
    """

    global package_index

    package_index += 1
    pack_root_dir = os.path.dirname(input_dir)
    pack_name = os.path.basename(pack_root_dir)
    param_dir = pack_root_dir + os.sep + "param"
    pose_file = pack_root_dir + os.sep + "global_imu_pose.txt"
    get_params(param_dir)
    # get_pose_data(pose_file)

    out_collection_dir = output_dir + os.sep + str(package_index) + "_" + pack_name
    tools.check_dir(out_collection_dir)

    out_cam_dir = os.path.join(out_collection_dir, 'camera')
    os.mkdir(out_cam_dir)
    out_lidar_dir = os.path.join(out_collection_dir, 'lidar')
    os.mkdir(out_lidar_dir)
    for camera_name in camera_name_map.keys():
        os.mkdir(os.path.join(out_cam_dir, camera_name))
    for seg_dir in tools.listdir(input_dir):
        for camera_name in camera_name_map.keys():
            cam_imgs = glob.glob(os.path.join(input_dir, seg_dir, f'{camera_name}-*.jpg'))
            if len(cam_imgs) > 1:
                cam_imgs = [img for img in cam_imgs if 'resize' not in img]
            cam_img = cam_imgs[0]
            shutil.copyfile(cam_img, os.path.join(out_cam_dir, camera_name, f'{seg_dir}.jpg'))
        hesai_pcd = glob.glob(os.path.join(input_dir, seg_dir, 'hesai-*.pcd'))[0]
        st_pcd = glob.glob(os.path.join(input_dir, seg_dir, 'st-*.pcd'))[0]
        merge_two_pcd(hesai_pcd, st_pcd, os.path.join(out_lidar_dir, f'{seg_dir}.pcd'))
        # add_pre_label(os.path.join(os.path.dirname(input_dir), 'result', 'final_result', seg_dir + '.txt'), os.path.join(out_seg_dir, 'pre_label', seg_dir + '.json'))
        create_test_in_config(out_collection_dir)


def merge_two_pcd(hs_file, st_file, target_file):
    rgb1 = 8.315529295e-39
    rgb2 = 8.285065e-39
    hs_pc = pypcd.point_cloud_from_path(hs_file)
    hs_pc = pypcd.delete_fields(hs_pc, ['time', 'distance', 'pitch', 'yaw', 'ring', 'block', 'label'])
    st_pc = pypcd.point_cloud_from_path(st_file)

    label_mark_data = {
        'fields': ['rgb'],
        'count': [1],
        'size': [4],
        'type': ['F'],
    }
    hs_pts_rgb = np.array([rgb1] * hs_pc.points, dtype=np.dtype([('rgb', np.float32), ]))
    st_pts_rgb = np.array([rgb2] * st_pc.points, dtype=np.dtype([('rgb', np.float32), ]))
    hs_pc = pypcd.add_fields(hs_pc, label_mark_data, hs_pts_rgb)
    st_pc = pypcd.add_fields(st_pc, label_mark_data, st_pts_rgb)

    merged_pc = pypcd.cat_point_clouds(hs_pc, st_pc)
    merged_pc.save_pcd(target_file)


def run(input_dir, output_dir):
    """
       主函数
    """
    input_dir = tools.unzip(input_dir)
    tools.check_dir(output_dir)

    # 一个label_file是一个分包
    label_file_list = tools.find_dir_by_pre_name(input_dir, pre_name="label_file")
    for label_file in label_file_list:
        create_pack_2frame(input_dir=label_file, output_dir=output_dir)
    trans_result = {"code": code, "output": output_dir, "err_msg": get_code_msg(code)}

    print(trans_result)
    return trans_result


"""
    项目： STL项目 23D 拉框、分割
    对接平台：云测新平台 
    功能： 将客户的文件, 标注结果转换成云测的格式， 并按数量分包
    输入： 原始数据
    输出： 分包后的数据
    开发： Justin
"""
if __name__ == "__main__":
    # 90, 140, 80 > 5934160(0rgb) 1351375360(bgr0) 1519144960(rgb0) -> 8.315529295e-39
    # 90, 55, 100 > 5912420(0rgb)  1681349120(bgr0) 1513579520(rgb0) -> 8.285065e-39
    args = tools.get_args()

#    if args.input == "../../input/test":
    args.input = "/Users/<USER>/Downloads/华为/3D障碍物/EP41-ORIN-0S13-00G_EP41_ORIN_02.10.16_1225_20231101-021208_0.mcap"
    args.out = "/Users/<USER>/Downloads/华为/3D障碍物/EP41-ORIN-0S13-00G_EP41_ORIN_02.10.16_1225_20231101-021208_0_merge_out"

    run(input_dir=args.input, output_dir=args.out)