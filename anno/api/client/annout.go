package client

import (
	"anno/internal/conf"
	"gitlab.rp.konvery.work/platform/apis/annout/v1"
	"gitlab.rp.konvery.work/platform/apis/client"
)

type (
	CreateReaperRequest   = annout.CreateReaperRequest
	ExportLotAnnosRequest = annout.ExportLotAnnosRequest
)

func initAnnout(c *conf.Bootstrap) {
	client.InitAnnout(&client.Service{Addr: c.Rpc.Annout.Addr})
}

var (
	CreateReaper   = client.CreateReaper
	ExportLotAnnos = client.ExportLotAnnos
)
