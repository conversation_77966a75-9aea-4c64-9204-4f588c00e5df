import json
import os
import shutil

import sys

sys.path.append(".")
from customer.common import tools

line_count = 0
file_count = 0
err_files = {}
file_box_dict = {}
current_file = ""
object_count = 0
PI = 3.141592653589793


def convert(data, name=None):
    global current_file
    global err_files
    annotations = []
    for label in data["lidar"]:
        class_val = label["class"],
        label = label["annotation"]["data"]

        r = label["rotation"]["z"]

        # if -PI <= r <= PI / 2:
        #     r = -r - PI / 2
        # else:
        #     r = -r + PI / 2

        if -PI <= r <= -PI / 2:
            r = -r - (PI / 2) * 3
        else:
            r = -r + PI / 2

        annotations.append({
            "markType": "3Dbox",
            "label": class_val[0].replace("car", "Car"),
            # ["x", "y", "z", "l", "w", "h", "r"],
            "region": [
                label["position"]["x"],
                label["position"]["y"],
                label["position"]["z"],
                label["dimension"]["l"],
                label["dimension"]["w"],
                label["dimension"]["h"],
                r,
            ],
            "attributes": [],
            "subNote": [],
            "groupId": None
        })

    new_data = {
        "fileUrl": "data/" + name,
        "subNote": [],
        "annotations": annotations
    }

    return new_data


def run(input_dir, output_dir):
    global file_count
    global line_count
    global current_file
    global file_box_dict

    file_count = 0
    invalid_file_count = 0
    invalid_file_list = []

    tools.check_dir(output_dir)
    input_dir = tools.unzip(input_dir)

    for f in tools.get_json_files(input_dir):

        if f.name.endswith('.json') and not f.name.startswith("."):

            convert_data = {}
            output_file_name = ""

            with open(f.path) as json_file:
                data = json.load(json_file)
                output_file_name = f.path.replace(input_dir, "").replace("label", "")
                current_file = f.name
                file_box_dict[current_file] = 0
                if data["label_meta"]["mark_status"] == 0:
                    file_count += 1
                    convert_data = convert(data, f.name.replace(".json", ".pcd"))
                else:
                    invalid_file_count += 1
                    invalid_file_list.append(f.path)
                    continue

            output_file_name = output_dir + output_file_name
            # os.makedirs(os.path.dirname(output_file_name), exist_ok=True)

            with open(output_file_name, "w") as outfile:
                json.dump(convert_data, outfile, indent=4)

    err_title = "错误信息"
    err_type_list = ["无效值"]
    return tools.check_error(output_dir, err_files, err_title, file_count, object_count, err_type_list, is_zip=False)


"""
    项目： LED 3D项目
    对接平台：云测新平台 
    功能： 云测导出的数据转换成客户格式
    输入： json
    输出： json
    云测3D输出格式： http://label-docs.testin.cn/dataset/pointcloud/
"""
if __name__ == "__main__":
    args = tools.get_args()
    args.input = "../../../input/test/3d"
    run(args.input, args.out)
