package protoconv

import (
	"fmt"

	"github.com/samber/lo"
)

// ProtoEnum represents a proto enum type.
type ProtoEnum interface {
	String() string
	~int32
}

// protoEnumStringConverter makes conversion between proto enum values and biz models.
// Conversions are made via their string representation with consideration of the prefix added in proto definitions.
type protoEnumStringConverter[T ProtoEnum] protoEnumConverter[T, stringWrap]

// NewProtoEnumStringConverter creates a converter.
func NewProtoEnumStringConverter[T ProtoEnum](protoEnumValues map[string]int32, protoAlignSample T, bizAlignSample string,
) *protoEnumStringConverter[T] {
	return (*protoEnumStringConverter[T])(NewProtoEnumConverter(
		protoEnumValues,
		func(v string) stringWrap { return stringWrap(v) },
		protoAlignSample, stringWrap(bizAlignSample),
	))
}

func (o *protoEnumStringConverter[T]) MustConv(full bool, vals ...string) {
	c := (*protoEnumConverter[T, stringWrap])(o)
	c.MustConv(full, lo.Map(vals, func(v string, _ int) stringWrap { return stringWrap(v) })...)
}

func (o *protoEnumStringConverter[T]) FromProto(v T) string {
	c := (*protoEnumConverter[T, stringWrap])(o)
	return string(c.FromProto(v))
}

func (o *protoEnumStringConverter[T]) ToProto(v string) T {
	c := (*protoEnumConverter[T, stringWrap])(o)
	return c.ToProto(stringWrap(v))
}

type stringWrap string

func (o stringWrap) String() string { return string(o) }

// BizEnum represents a biz enum type.
type BizEnum interface {
	String() string
}
type NewBizEnum[T BizEnum] func(string) T

// protoEnumConverter converts proto enum values from/to biz models.
type protoEnumConverter[T1 ProtoEnum, T2 BizEnum] struct {
	prefix      string
	newBizEnum  NewBizEnum[T2]
	protoValues map[string]int32
}

// NewProtoEnumConverter creates a converter.
func NewProtoEnumConverter[T1 ProtoEnum, T2 BizEnum](protoEnumValues map[string]int32, newBizEnum NewBizEnum[T2],
	protoAlignSample T1, bizAlignSample T2) *protoEnumConverter[T1, T2] {
	return &protoEnumConverter[T1, T2]{
		protoValues: protoEnumValues,
		newBizEnum:  newBizEnum,
		prefix:      protoAlignSample.String()[:len(protoAlignSample.String())-len(bizAlignSample.String())],
	}
}

// MustConv tests conversions of the provided values.
// It will panic if some of the conversions fails.
// If full is true, number of possible values of the proto enum type must equal to len(vals).
func (o *protoEnumConverter[T1, T2]) MustConv(full bool, vals ...T2) {
	if full && len(o.protoValues) != len(vals) {
		panic(fmt.Sprintf("protoEnumConverter[%#v, %#v] values count mismatch", T1(0), vals[0]))
	}
	for _, v := range vals {
		v1 := o.ToProto(v)
		v2 := o.FromProto(v1)
		if v.String() != v2.String() {
			panic(fmt.Sprintf("protoEnumConverter[%#v, %#v] fails on %v", v1, v2, v))
		}
	}
}

func (o *protoEnumConverter[T1, T2]) FromProto(v T1) T2 {
	return o.newBizEnum(v.String()[len(o.prefix):])
}

func (o *protoEnumConverter[T1, T2]) ToProto(v T2) T1 {
	return T1(o.protoValues[o.prefix+v.String()])
}
