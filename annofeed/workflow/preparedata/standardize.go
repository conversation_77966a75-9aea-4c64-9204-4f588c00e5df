package preparedata

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"annofeed/internal/biz"
	"annofeed/workflow/common"
	commonpcd "annofeed/workflow/common/pointcloud"

	"gitlab.rp.konvery.work/platform/pkg/kfs"
	"gitlab.rp.konvery.work/platform/pkg/wf/wfutil"
	"go.temporal.io/sdk/activity"
)

func (o *Activities) PrepareDataStandardize(ctx context.Context, dir string, data *biz.Data) (err error) {
	fmt.Println("---> prepare data standardize: ", dir)
	fmt.Println("---> 标准化之前结构：")
	_ = PrintTree(dir)
	if err := o.standardizeDataFormat(ctx, dir); err != nil {
		return wfutil.NewNonRetryableError("standardize data format", err)
	}
	fmt.Println("---> 标准化之后结构：")
	_ = PrintTree(dir)
	if err := o.deleteUnwatedFiles(ctx, dir); err != nil {
		return wfutil.NewNonRetryableError("delete unwanted files", err)
	}
	if err := o.standardizePCD(ctx, dir); err != nil {
		return wfutil.NewNonRetryableError("standardize pcd", err)
	}

	return nil
}

// standardizeDataFormat standardizes data format: create a `data` dir if it does not exist and move all the data files to `data` dir.
func (o *Activities) standardizeDataFormat(ctx context.Context, basedir string) (err error) {
	dataDir := filepath.Join(basedir, common.DataDir)
	fmt.Printf("---> standardizeDataFormat: dataDir: %s, baseDir: %s", dataDir, basedir)
	dataDirExists := kfs.PathExist(dataDir)
	fmt.Printf("---> dataDir %s, Exists: %v", dataDir, dataDirExists)
	if dataDirExists { // move the files under `data` dir to `data/data` dir
		entries, err := os.ReadDir(dataDir)
		if err != nil {
			return err
		}
		for _, entry := range entries {
			fmt.Printf("=== entry-1: entry name: %s, isDir: %v, path: %s", entry.Name(), entry.IsDir(), filepath.Join(dataDir, entry.Name()))
			if entry.IsDir() {
				fmt.Printf("entry-1: dir skip")
				continue
			}
			name := entry.Name()
			dstPath := filepath.Join(dataDir, common.DataDir, name)
			fmt.Printf("---> move file from: %s -> %s: ", filepath.Join(dataDir, name), dstPath)
			if err := kfs.MoveFile(filepath.Join(dataDir, name), dstPath); err != nil {
				return err
			}
		}
	}

	entries, err := os.ReadDir(basedir)
	if err != nil {
		return err
	}
	for _, entry := range entries {
		// move the files and dirs under basedir to `data/data` and `data` dir respectively
		name := entry.Name()
		fmt.Printf("=== entry-2: entry name: %s, isDir: %v, path: %s", entry.Name(), entry.IsDir(), filepath.Join(dataDir, entry.Name()))
		if entry.IsDir() && common.StandardTopDirs[name] || common.StandardTopFiles[name] {
			fmt.Println("entry-2: standard dir or file skip.")
			continue
		}

		dstPath := filepath.Join(dataDir, common.DataDir, name)
		if entry.IsDir() {
			dstPath = filepath.Join(dataDir, name)
		}
		fmt.Printf("---> move file from: %s -> %s: ", filepath.Join(dataDir, name), dstPath)
		if err := kfs.MoveFile(filepath.Join(basedir, name), dstPath); err != nil {
			return err
		}
	}

	//traverseAndCleanDir(basedir, basedir)

	return nil
}

// 遍历目录并处理每个文件或目录，去除多余的路径层级
func traverseAndCleanDir(basedir string, dataDir string) error {
	// 读取目录中的所有条目
	entries, err := os.ReadDir(basedir)
	if err != nil {
		return fmt.Errorf("failed to read directory: %v", err)
	}

	for _, entry := range entries {
		// 获取文件或目录的完整路径
		name := entry.Name()
		srcPath := filepath.Join(basedir, name)
		fmt.Println("---> srcPath: ", srcPath)

		// 如果是目录，则递归调用该方法
		if entry.IsDir() {
			// 清理多余的路径层级
			cleanedPath := cleanNestedPath(srcPath)
			fmt.Println("---> dstPath: ", cleanedPath)

			// 确保目标目录存在
			if err := os.MkdirAll(cleanedPath, os.ModePerm); err != nil {
				return fmt.Errorf("failed to create directory %s: %v", cleanedPath, err)
			}

			// 递归调用处理子目录
			if err := traverseAndCleanDir(srcPath, cleanedPath); err != nil {
				return err
			}
		} else {
			// 如果是文件，清理路径并移动
			dstPath := cleanNestedPath(srcPath)
			fmt.Println("---> dstPath: ", dstPath)

			// 确保目标路径的目录存在
			dstDir := filepath.Dir(dstPath)
			if err := os.MkdirAll(dstDir, os.ModePerm); err != nil {
				return fmt.Errorf("failed to create directory for file %s: %v", dstDir, err)
			}

			// 移动文件
			if err := os.Rename(srcPath, dstPath); err != nil {
				return fmt.Errorf("failed to move file from %s to %s: %v", srcPath, dstPath, err)
			}
		}
	}

	// 处理完目录后，尝试删除空目录
	if err := removeEmptyDirs(basedir); err != nil {
		return err
	}

	return nil
}

// 删除空目录的函数
func removeEmptyDirs(path string) error {
	// 判断目录是否为空
	entries, err := os.ReadDir(path)
	if err != nil {
		return fmt.Errorf("failed to read directory %s: %v", path, err)
	}

	// 如果目录为空，删除它
	if len(entries) == 0 {
		if err := os.Remove(path); err != nil {
			return fmt.Errorf("failed to remove empty directory %s: %v", path, err)
		}
		fmt.Printf("Removed empty directory: %s\n", path)
	}
	return nil
}

// 清理路径的函数：去除多余的目录层级
func cleanNestedPath(path string) string {
	// 正则表达式去掉类似 `data/xxx/data` 或 `data/xxx/annos` 的路径结构
	// 匹配 `data/任意目录/data` 或 `data/任意目录/annos`
	re := regexp.MustCompile(`/data/[^/]+/(data|annos)`)
	return re.ReplaceAllString(path, "/data/$1")
}

func (o *Activities) deleteUnwatedFiles(_ context.Context, basedir string) (err error) {
	return kfs.IterateDir(basedir, func(entry os.DirEntry, subdir string, depth int) error {
		name := entry.Name()
		if entry.IsDir() || !strings.HasPrefix(name, ".") {
			return nil
		}
		fpath := filepath.Join(basedir, subdir, name)
		return os.RemoveAll(fpath)
	})
}

// standardizePCD standardizes the pcd format to `binary`.
func (o *Activities) standardizePCD(ctx context.Context, basedir string) (err error) {
	return kfs.IterateDir(basedir, func(entry os.DirEntry, subdir string, depth int) error {
		name := entry.Name()
		if entry.IsDir() {
			return nil
		}

		fileExt := common.FileExt(name)
		fpath := filepath.Join(basedir, subdir, name)
		pcdPath := ""
		switch fileExt {
		case common.RawdataFormatPCD:
			pcdPath = fpath
			if err := commonpcd.TransformPCDToBinary(fpath); err != nil {
				return err
			}
		case common.RawdataFormatPLY:
			defer os.Remove(fpath)
			pcdPath = fpath + ".pcd"
			if err = commonpcd.TransformPLYToPCD(fpath, pcdPath); err != nil {
				return fmt.Errorf("failed to transform ply to pcd")
			}
		default:
			return nil
		}

		if name != common.LidarPCD {
			lidar := filepath.Join(basedir, subdir, common.LidarPCD)
			if err := os.Rename(pcdPath, lidar); err != nil {
				return err
			}
			ofile := filepath.Join(subdir, name)
			if err := os.WriteFile(lidar+common.OrigExt, []byte(ofile), 0644); err != nil {
				return err
			}
			activity.RecordHeartbeat(ctx, ofile)
		}
		return nil
	})
}
