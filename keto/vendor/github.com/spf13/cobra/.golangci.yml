# Copyright 2013-2022 The Cobra Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

run:
  deadline: 5m

linters:
  disable-all: true
  enable:
    #- bodyclose
    - deadcode
    #- depguard
    #- dogsled
    #- dupl
    - errcheck
    #- exhaustive
    #- funlen
    - gas
    #- gochecknoinits
    - goconst
    #- gocritic
    #- gocyclo
    #- gofmt
    - goimports
    - golint
    #- gomnd
    #- goprintffuncname
    #- gosec
    #- gosimple
    - govet
    - ineffassign
    - interfacer
    #- lll
    - maligned
    - megacheck
    #- misspell
    #- nakedret
    #- noctx
    #- nolintlint
    #- rowserrcheck
    #- scopelint
    #- staticcheck
    - structcheck
    #- stylecheck
    #- typecheck
    - unconvert
    #- unparam
    #- unused
    - varcheck
    #- whitespace
  fast: false
