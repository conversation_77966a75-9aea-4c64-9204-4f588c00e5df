package biz

import (
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"time"

	"anno/api/interpolate"

	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kset"
	"gitlab.rp.konvery.work/platform/pkg/kmath"
)

type InsByClass map[string]int
type EvtJobsubmit struct {
	ID           int64      `json:"id,omitempty"`
	JobID        int64      `json:"job_id,omitempty"`
	LotID        int64      `json:"lot_id,omitempty"`
	ExecutorUid  string     `json:"executor_uid,omitempty"` // executor uid
	ExecteamUid  string     `json:"execteam_uid,omitempty"` // executor org uid
	Action       string     `json:"action,omitempty"`
	Elems        int32      `json:"elems,omitempty"`
	Phase        int32      `json:"phase,omitempty"` // starts from 1
	InsCnt       int32      `json:"ins_cnt,omitempty"`
	InsTotal     int32      `json:"ins_total,omitempty"` // include interpolated objects
	Ins2d        int32      `json:"ins_2d,omitempty"`
	Ins3d        int32      `json:"ins_3d,omitempty"`
	InsByClass   InsByClass `json:"ins_by_class,omitempty"`
	RrElems      int32      `json:"rr_elems,omitempty"`
	RrIns        int32      `json:"rr_ins,omitempty"`
	RrIns2d      int32      `json:"rr_ins_2d,omitempty"`
	RrIns3d      int32      `json:"rr_ins_3d,omitempty"`
	RrInsByClass InsByClass `json:"rr_ins_by_class,omitempty"`
	Duration     int32      `json:"duration,omitempty"`
	CreatedAt    time.Time  `json:"created_at,omitempty"`
	// Redo            int32      `json:"redo,omitempty"`
	ClaimedAt time.Time `json:"claimed_at,omitempty"` // ClaimedAt = CreatedAt - Duration
	// LastExecutorUid string     `json:"last_executor_uid,omitempty"` // last phase executor uid
	// LastExecteamUid string     `json:"last_execteam_uid,omitempty"` // last phase executor org uid
}

// CountAnnos counts labeled, rejected, resolved(fixed) objects/elements in a job.
func CountAnnos(job *Job, req *anno.ReviewJobRequest) *EvtJobsubmit {
	js := &EvtJobsubmit{}
	if req == nil {
		req = &anno.ReviewJobRequest{}
	}

	comments := make([]*AnnoComment, 0, len(req.Comments)+len(req.Resolves))
	// pick new review comments in case this is a reject request
	comments = append(comments, req.Comments...)
	// pick resolved comments in case this is a submit/accept request
	for _, r := range req.Resolves {
		c, ok := lo.Find(job.Ally.Comments, func(v *anno.AnnoComment) bool { return r.Uuid == v.Uuid })
		if ok {
			comments = append(comments, c)
		}
	}

	// count rejected/resolved objects
	checkObj := func(elemIdx, rdIdx int, obj *anno.Object) bool {
		// mark all objects as rejected/resolved if decision is recycle
		rr := req.Decision == anno.ReviewJobRequest_Decision_recycle ||
			// mark the object as rejected/resolved if they are in the new comments or resolves list,
			// or if its elemIdx match the element comment's elemIdx and the reason class is flawed.
			// do not count missed comments here.
			lo.FindOrElse(comments, nil, func(v *AnnoComment) bool {
				return v.Reasons.Class != commentClassMissed &&
					((elemIdx == int(v.ElemIdx) && v.Scope == anno.AnnoComment_Scope_element) ||
						lo.Contains(v.ObjUuids, obj.Uuid))
			}) != nil
		fmt.Println("elemIdx", elemIdx, "rdIdx", rdIdx, "obj", obj)
		fmt.Println("rr", rr)
		spew.Dump("---------")
		if rr {
			rd := job.Ally.Elements[elemIdx].Datas[rdIdx]
			if rd.Type == anno.Rawdata_Type_image {
				js.RrIns2d++
			} else {
				js.RrIns3d++
			}
			js.RrIns++
		}
		return true
	}
	if req.Annotations != nil {
		fmt.Println("---> req.Annotations")
		walkAnnoObjs(req.Annotations, checkObj)
		spew.Dump(js)
	}
	if job.Ally.Annotations.E != nil {
		fmt.Println("---> job.Ally.Annotations.E")
		walkAnnoObjs(job.Ally.Annotations.E, checkObj)
		spew.Dump(js)
	}

	// count missed annotations
	for _, c := range comments {
		if c.Reasons.Class == commentClassMissed {
			rd := job.Ally.Elements[c.ElemIdx].Datas[c.RdIdx]
			cnt := int32(kmath.Max(1, len(c.ObjUuids)))
			if rd.Type == anno.Rawdata_Type_image {
				js.RrIns2d += cnt
			} else {
				js.RrIns3d += cnt
			}
			js.RrIns += cnt
		}
	}
	// count rejected/resolved elements
	rrElems := kset.NewSet[int]()
	lo.ForEach(comments, func(v *anno.AnnoComment, _ int) { rrElems.Add(int(v.ElemIdx)) })
	spew.Dump("rrElems: ", rrElems)
	js.RrElems = int32(rrElems.Size())

	// count objects
	annos := job.Ally.Annotations.E
	if req.Annotations != nil {
		annos = req.Annotations
	}
	cnt, total, byRd := interpolate.CountIns(annos)
	js.InsCnt = int32(cnt)
	js.InsTotal = int32(total)
	for k, v := range byRd {
		rd := job.Ally.Elements[k[0]].Datas[k[1]]
		if rd.Type == anno.Rawdata_Type_image {
			js.Ins2d += int32(v)
		} else {
			js.Ins3d += int32(v)
		}
	}

	return js
}

// walkAnnoObjs iterates over each anno object, and stops when fn returns false.
func walkAnnoObjs(annos *anno.JobAnno, fn func(elemIdx, rdIdx int, obj *anno.Object) bool) {
	for i, e := range annos.ElementAnnos {
		for j, rd := range e.RawdataAnnos {
			for _, obj := range rd.Objects {
				if !fn(i, j, obj) {
					return
				}
			}
		}
	}
}
