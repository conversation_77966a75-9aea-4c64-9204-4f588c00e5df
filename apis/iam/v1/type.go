package iam

import "github.com/samber/lo"

func (o *UserContext) IsEmpty() bool { return o.GetUser().GetUid() == "" }
func (o *UserContext) RealUser() *User {
	return lo.Ternary(o.GetAssumeBy() != nil, o.GetAssumeBy(), o.GetUser())
}

func (o *UserContext) GetUid() string             { return o.GetUser().GetUid() }
func (o *UserContext) GetName() string            { return o.GetUser().GetName() }
func (o *UserContext) GetRole() string            { return o.GetUser().GetRole() }
func (o *UserContext) GetOrgUid() string          { return o.GetUser().GetOrgUid() }
func (o *UserContext) GetOrgType() Team_Type_Enum { return o.GetUser().GetOrgType() }
func (o *UserContext) GetPhone() string           { return o.GetUser().GetPhone() }
func (o *UserContext) GetEmail() string           { return o.GetUser().GetEmail() }
func (o *UserContext) GetImperfect() bool         { return o.GetUser().GetImperfect() }
