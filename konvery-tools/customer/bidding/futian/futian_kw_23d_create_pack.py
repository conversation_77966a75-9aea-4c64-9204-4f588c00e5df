import os
import shutil
import sys
import numpy as np

sys.path.append('.')
from customer.common import tools


def run(input_dir, output_dir, pose_file):
    col_name = os.path.basename(input_dir)
    out_col_dir = os.path.join(output_dir, 'data', col_name)
    os.makedirs(out_col_dir)
    params = tools.get_json_data('./params.json')
    pose_lines = tools.read_file(pose_file)
    pose = {}
    for pose_line in pose_lines[1:]:
        pose_split = pose_line.split()
        pose[pose_split[0]] = np.array(pose_split[8:11] + pose_split[-3:] + [pose_split[11]], dtype=np.float32).tolist()
    for ele_dir in tools.listdir(input_dir, full_path=True):
        params['lidar']['pose'] = pose[os.path.basename(ele_dir)]
        shutil.copytree(ele_dir, os.path.join(out_col_dir, os.path.basename(ele_dir)))
        target_params_file = os.path.join(os.path.join(out_col_dir, os.path.basename(ele_dir), 'params.json'))
        tools.write_json_file(params, target_params_file)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out, args.txt)
