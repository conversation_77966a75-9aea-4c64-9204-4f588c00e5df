version: "3"

services:
  keto:
    build:
      context: "."
      dockerfile: ".docker/Dockerfile-build"
    ports:
      - "4466:4466"
      - "4467:4467"
      - "4468:4468"
    command: serve -c /home/<USER>/keto.yml
    restart: on-failure
    volumes:
      - type: bind
        source: "./config"
        target: "/home/<USER>"
    environment:
      - LOG_LEVEL=debug
      - PORT=4466
      - DSN=memory
