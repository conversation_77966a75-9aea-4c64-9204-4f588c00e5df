import os
import glob
import json
import shutil
import zipfile
import sys

sys.path.append('.')
from customer.common import tools

mac_file = ['.DS_Store', '__MACOSX']


def run(input_dir, source_file_dir):
    # try:
    line_counter = 0

    # unzip file
    unzip_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_unzip')
    result_dir = ''
    if os.path.exists(unzip_dir):
        shutil.rmtree(unzip_dir)
    with zipfile.ZipFile(input_dir, 'r') as zip_ref:
        zip_ref.extractall(unzip_dir)

    for area in os.listdir(source_file_dir):
        if area not in mac_file:
            area_dir = os.path.join(source_file_dir, area)
            for room in os.listdir(area_dir):
                if room not in mac_file:
                    room_dir = os.path.join(area_dir, room)
                    result_dir = os.path.join(room_dir, 'Annotions')

                    for text_file_dir in glob.glob(os.path.join(room_dir, '*.txt')):

                        with open(text_file_dir, 'r') as f:
                            source_file = [line.rstrip() for line in f]

                        for json_dir in glob.glob(os.path.join(unzip_dir, '**', area, room, 'label', '*.json'), recursive=True):
                            # json_dir = os.path.join(target_folder, 'label', room + '.json')
                            json_data = json.load(open(json_dir))

                            # get file name and number from json data
                            result_key = json_data['segmentation']['result'][0]
                            result_value = json_data['segmentation']['result'][1]

                            # raise exception if the annotation data and source data doesn't match
                            if len(result_key) != len(source_file):
                                raise ValueError('标注数据与源数据行数不一致！')

                            # iterate through file name, match the file number to create file, match source_file to get the content
                            for index, value in enumerate(result_key):
                                # skip empty annotation
                                if value == '':
                                    continue

                                result_file = open(os.path.join(result_dir, value + '_' + str(result_value[index]) + '.txt'), 'a')
                                result_file.write(source_file[index] + '\n')
                                result_file.close()
                                line_counter += 1

                        # rename files
                        name_counter = {}
                        for item in os.listdir(result_dir):
                            prefix = item.split('_')[0]
                            if prefix in name_counter:
                                name_counter[prefix] += 1
                            else:
                                name_counter[prefix] = 1
                            os.rename(os.path.join(result_dir, item),
                                      os.path.join(result_dir, prefix + '-' + str(name_counter[prefix]) + '.txt'))

    trans_result = {
        "code": 0,
        "output": result_dir,
        "err_msg": '成功',
        "summary": {
            '写入行数': line_counter
        }
    }
    #
    # except Exception as e:
    #     trans_result = {
    #         "code": 1,
    #         "err_msg": e,
    #     }

    for key in trans_result:
        print(key + ': ' + str(trans_result[key]))

    print(trans_result)
    return trans_result


if __name__ == "__main__":
    args = tools.get_args()
    run(args.input, args.pcd)

    # args_input = '/Users/<USER>/Downloads/AUH-室内分割结果导出/AUH-室内分割_231012a42e1_验收_已通过_JSON标注结果文件_20231120_4478.zip'
    # args_pcd = '/Users/<USER>/Downloads/D7N2EKCX4Sj-待上传'
    # run(args_input, args_pcd)
