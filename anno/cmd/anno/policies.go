package main

import (
	"context"
	"fmt"
	"time"

	"anno/api/client"
	"anno/internal/biz"
	"anno/internal/conf"
	"anno/internal/data"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/pkg/kid"
)

type accessMgr struct {
	jobRepo   biz.JobsRepo
	lotRepo   biz.LotsRepo
	orderRepo biz.OrdersRepo
	cleanup   func()
	log       *log.Helper
}

func newAccessMgr(bc *conf.Bootstrap, logger log.Logger) *accessMgr {
	dataData, cleanup, err := data.NewData(bc.Data, logger)
	if err != nil {
		panic(fmt.Errorf("failed to create database client: %w", err))
	}

	client.Init(bc)
	return &accessMgr{
		jobRepo:   data.NewJobsRepo(dataData, logger),
		lotRepo:   data.NewLotsRepo(dataData, logger),
		orderRepo: data.NewOrdersRepo(dataData, logger),
		cleanup:   cleanup,
		log:       log.<PERSON>elper(logger),
	}
}

func (o *accessMgr) Close() {
	o.cleanup()
}

// CreateAllPolicies recreates keto tuples for all jobs/lots/orders.
func (o *accessMgr) CreateAllPolicies() error {
	err := o.CreateTuplesForLots()
	if err != nil {
		return err
	}
	err = o.CreateTuplesForOrders()
	if err != nil {
		return err
	}
	return o.CreateTuplesForJobs()
}

func (o *accessMgr) CreateTuplesForLots() error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	count, err := o.lotRepo.Count(ctx, &biz.LotListFilter{})
	cancel()
	if err != nil {
		return fmt.Errorf("failed to query lot count: %w", err)
	}
	o.log.Infof("CreateTuplesForLots: find %v lots", count)

	for page, pagesz := 0, 1000; ; page++ {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		lots, err := o.lotRepo.List(ctx, &biz.LotListFilter{}, biz.Pager{Pagesz: pagesz, Page: page})
		cancel()
		if err != nil {
			return fmt.Errorf("failed to query lot list: %w", err)
		}
		if len(lots) == 0 {
			break
		}

		for _, t := range lots {
			ctx, cancel = context.WithTimeout(context.Background(), 10*time.Second)
			err = o.CreateTuplesForLot(ctx, t)
			cancel()
			if err != nil {
				return fmt.Errorf("failed to add lot tuples: %w", err)
			}
		}
		o.log.Infof("CreateTuplesForLots: finished %v lots", page*pagesz+len(lots))
	}
	o.log.Infof("CreateTuplesForLots: finished")
	return nil
}

func (o *accessMgr) CreateTuplesForLot(ctx context.Context, lot *biz.Lot) error {
	err := client.CreateAccessPolicies(ctx, biz.PermClsLot, lot.GetUid(),
		[]string{client.UserScope(lot.CreatorUid)}, []string{client.GroupScope(lot.OrgUid)})
	if err != nil {
		return err
	}
	return o.grantLotExecteamAccess(ctx, lot)
}

func (o *accessMgr) grantLotExecteamAccess(ctx context.Context, lot *biz.Lot) error {
	phases, err := o.lotRepo.LoadPhases(ctx, lot.ID, 0)
	if err != nil {
		return err
	}
	teams := lo.Map(phases, func(v *biz.Lotphase, _ int) string { return v.Execteam })
	teams = lo.WithoutEmpty(teams)
	if len(teams) > 0 {
		teams = lo.Uniq(teams)
		err := o.GrantExecteamAccess(ctx, lot.ID, teams...)
		if err != nil {
			return err
		}
	}
	return nil
}

func (o *accessMgr) GrantExecteamAccess(ctx context.Context, lotID int64, teamUids ...string) (err error) {
	// revoke access policy from old team and add access policy for the new team
	resource := biz.LotScope(kid.StringID(lotID))
	jobs := lo.Map(teamUids, func(v string, _ int) string { return client.GroupScope(v) })
	// o.log.Infow("msg", "grant access to execteam", "lot", resource, "jobs", jobs)
	_, err = client.CreatePolicy(ctx, resource, biz.RoleJobViewer, jobs)
	return err
}

func (o *accessMgr) CreateTuplesForJobs() error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	count, err := o.jobRepo.Count(ctx, &biz.JobListFilter{})
	cancel()
	if err != nil {
		return fmt.Errorf("failed to query job count: %w", err)
	}
	o.log.Infof("CreateTupleForJobs: find %v jobs", count)

	for page, pagesz := 0, 1000; ; page++ {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		jobs, err := o.jobRepo.List(ctx, &biz.JobListFilter{}, biz.Pager{Pagesz: pagesz, Page: page})
		cancel()
		if err != nil {
			return fmt.Errorf("failed to query job list: %w", err)
		}
		if len(jobs) == 0 {
			break
		}

		for _, u := range jobs {
			ctx, cancel = context.WithTimeout(context.Background(), 10*time.Second)
			err = o.CreateTuplesForJob(ctx, u)
			cancel()
			if err != nil {
				return fmt.Errorf("failed to add job tuples: %w", err)
			}
		}
		o.log.Infof("CreateTupleForJobs: finished %v jobs", page*pagesz+len(jobs))
	}
	o.log.Infof("CreateTupleForJobs: finished")
	return nil
}

func (o *accessMgr) CreateTuplesForJob(ctx context.Context, job *biz.Job) error {
	return client.CreateAccessPolicies(ctx, biz.PermClsJob, job.GetUid(), nil,
		[]string{biz.LotScope(kid.StringID(job.LotID))})
}

func (o *accessMgr) CreateTuplesForOrders() error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	count, err := o.orderRepo.Count(ctx, &biz.OrderListFilter{})
	cancel()
	if err != nil {
		return fmt.Errorf("failed to query order count: %w", err)
	}
	o.log.Infof("CreateTupleForOrders: find %v orders", count)

	for page, pagesz := 0, 1000; ; page++ {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		orders, err := o.orderRepo.List(ctx, &biz.OrderListFilter{}, biz.Pager{Pagesz: pagesz, Page: page})
		cancel()
		if err != nil {
			return fmt.Errorf("failed to query order list: %w", err)
		}
		if len(orders) == 0 {
			break
		}

		for _, u := range orders {
			ctx, cancel = context.WithTimeout(context.Background(), 10*time.Second)
			err = o.CreateTuplesForOrder(ctx, u)
			cancel()
			if err != nil {
				return fmt.Errorf("failed to add order tuples: %w", err)
			}
		}
		o.log.Infof("CreateTupleForOrders: finished %v orders", page*pagesz+len(orders))
	}
	o.log.Infof("CreateTupleForOrders: finished")
	return nil
}

func (o *accessMgr) CreateTuplesForOrder(ctx context.Context, p *biz.Order) error {
	return client.CreateAccessPolicies(ctx, biz.PermClsOrder, p.GetUid(),
		[]string{client.UserScope(p.CreatorUid)}, []string{client.GroupScope(p.OrgUid)})
}
