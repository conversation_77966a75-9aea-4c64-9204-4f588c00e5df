// Code generated by "stringer -type=Location location.go"; DO NOT EDIT.

package reflectwalk

import "fmt"

const _Location_name = "NoneMapMapKeyMapValueSliceSliceElemArrayArrayElemStructStructFieldWalkLoc"

var _Location_index = [...]uint8{0, 4, 7, 13, 21, 26, 35, 40, 49, 55, 66, 73}

func (i Location) String() string {
	if i >= Location(len(_Location_index)-1) {
		return fmt.Sprintf("Location(%d)", i)
	}
	return _Location_name[_Location_index[i]:_Location_index[i+1]]
}
