{{- if and (.Capabilities.APIVersions.Has "monitoring.coreos.com/v1") (.Values.service.metrics.enabled) }}
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "appx.fullname" . }}-metrics
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/component: metrics
{{ include "appx.labels" . | indent 4 }}
  {{- with .Values.serviceMonitor.labels }}
      {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.service.metrics.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  endpoints:
  - path: /metrics
    port: {{ .Values.service.metrics.name }}
    scheme: {{ .Values.serviceMonitor.scheme }}
    interval: {{ .Values.serviceMonitor.scrapeInterval }}
    scrapeTimeout: {{ .Values.serviceMonitor.scrapeTimeout }}
    {{- with .Values.serviceMonitor.tlsConfig }}
    tlsConfig:
      {{- toYaml . | nindent 6 }}
    {{- end }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "appx.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
      app.kubernetes.io/component: metrics
{{- end -}}
