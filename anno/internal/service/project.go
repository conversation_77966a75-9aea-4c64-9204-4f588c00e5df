// source: anno/v1/project.proto
package service

import (
	"context"

	"anno/api/client"
	"anno/internal/biz"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"

	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func FromBizProject(d *biz.Project) *anno.Project {
	if d == nil {
		return nil
	}
	o := &anno.Project{
		Uid:       d.GetUid(),
		Name:      d.Name,
		Desc:      d.Desc,
		Avatar:    d.Avatar,
		UpdatedAt: timestamppb.New(d.UpdatedAt),
		CreatedAt: timestamppb.New(d.CreatedAt),
	}
	return o
}

func ToBizProject(d *anno.CreateProjectRequest) *biz.Project {
	if d == nil {
		return nil
	}
	o := &biz.Project{
		ID:     kid.ParseID(d.Uid),
		Name:   d.Name,
		Desc:   d.Desc,
		Avatar: d.Avatar,
	}
	return o
}

type ProjectsService struct {
	anno.UnimplementedProjectsServer
	bz *biz.ProjectsBiz
}

func NewProjectsService(bz *biz.ProjectsBiz) *ProjectsService {
	// TODO: implement access control
	bz = nil // fail all calls
	return &ProjectsService{bz: bz}
}

func (o *ProjectsService) CreateProject(ctx context.Context, req *anno.CreateProjectRequest) (*anno.Project, error) {
	data, err := o.bz.Create(ctx, ToBizProject(req))
	return FromBizProject(data), err
}

func (o *ProjectsService) UpdateProject(ctx context.Context, req *anno.UpdateProjectRequest) (*anno.Project, error) {
	data, err := o.bz.Update(ctx, ToBizProject(req.Project), field.NewMask(req.Fields...))
	return FromBizProject(data), err
}

func (o *ProjectsService) DeleteProject(ctx context.Context, req *anno.DeleteProjectRequest) (*emptypb.Empty, error) {
	return &emptypb.Empty{}, o.bz.DeleteByUid(ctx, req.Uid)
}

func (o *ProjectsService) GetProject(ctx context.Context, req *anno.GetProjectRequest) (*anno.Project, error) {
	data, err := o.bz.GetByUid(ctx, req.Uid)
	return FromBizProject(data), err
}

func (o *ProjectsService) ListProject(ctx context.Context, req *anno.ListProjectRequest) (*anno.ListProjectReply, error) {
	creator, scope := getListScope(ctx, req.OrgUid, req.CreatorUid)
	if !client.IsAllowed(ctx, "", biz.PermList, biz.PermClsProject, scope) {
		return nil, errors.NewErrForbidden()
	}

	pager := biz.Pager{
		Pagesz: int(req.Pagesz),
		Page:   int(req.Page),
	}
	filter := &biz.ProjectListFilter{
		OrgUid:      req.OrgUid,
		CreatorUid:  creator,
		NamePattern: req.NamePattern,
	}

	var cnt int
	if pager.Page == 0 {
		var err error
		cnt, err = o.bz.Count(ctx, filter)
		if err != nil {
			return nil, err
		}
	}
	datas, err := o.bz.List(ctx, filter, pager)
	if err != nil {
		return nil, err
	}
	return &anno.ListProjectReply{Total: int32(cnt), Projects: kslice.Map(FromBizProject, datas)}, err
}
