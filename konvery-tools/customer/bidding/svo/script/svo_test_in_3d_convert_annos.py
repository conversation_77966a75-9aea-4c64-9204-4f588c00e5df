import datetime
import sys
import os
from glob import glob

import jsonschema
import numpy as np
from scipy.spatial.transform import Rotation as R

sys.path.append(".")
from customer.common import tools


anno_types = [
    "truck",
    "car",
    "duty vehicle",
    "ambulance",
    "fire truck",
    "school bus",
    "police car",
    "cyclist",
    "pedestrian",
    "animal"
]

camera_id_mapping = {
    "front_camera": 0,
    "left_camera": 1,
    "back_camera": 2,
    "right_camera": 3
}

# 采样间隔时间 (5Hz, 即每帧间隔 0.2s)
DELTA_T = 0.2


def listdir(input_dir):
    """wraps for os.listdir but dose not contains mac os system file"""
    mac_file = ['.DS_Store', '__MACOSX']
    return sorted([os.path.join(input_dir, f) for f in os.listdir(input_dir) if f not in mac_file])


def find_folders(root_dir, target_folder):
    found_folders = []

    for dirpath, dirnames, filenames in os.walk(root_dir):
        if target_folder in dirnames:
            found_folders.append(os.path.join(dirpath, target_folder))
    return found_folders


def quaternion_to_euler(qx, qy, qz, qw):
    """
    Convert quaternion (qx, qy, qz, qw) to Euler angles (roll, pitch, yaw)
    """
    # 计算 roll (x)
    sinr_cosp = 2 * (qw * qx + qy * qz)
    cosr_cosp = 1 - 2 * (qx**2 + qy**2)
    roll = np.arctan2(sinr_cosp, cosr_cosp)

    # 计算 pitch (y)
    sinp = 2 * (qw * qy - qz * qx)
    pitch = np.arcsin(sinp) if abs(sinp) <= 1 else np.sign(sinp) * (np.pi / 2)

    # 计算 yaw (z)
    siny_cosp = 2 * (qw * qz + qx * qy)
    cosy_cosp = 1 - 2 * (qy**2 + qz**2)
    yaw = np.arctan2(siny_cosp, cosy_cosp)

    # 转换为角度
    return np.degrees(roll), np.degrees(pitch), np.degrees(yaw)


def compute_box_corners(data):
    x, y, z = data[0:3]  # 中心点坐标
    sx, sy, sz = data[3:6]  # 轴长
    qx, qy, qz, qw = data[6:10]  # 旋转四元数

    # 局部坐标系下的 8 个角点
    local_corners = np.array([
        [-sx / 2, -sy / 2, -sz / 2],  # 角点 1
        [-sx / 2, sy / 2, -sz / 2],  # 角点 2
        [sx / 2, -sy / 2, -sz / 2],  # 角点 3
        [sx / 2, sy / 2, -sz / 2],  # 角点 4
        [-sx / 2, -sy / 2, sz / 2],  # 角点 5 (上层)
        [-sx / 2, sy / 2, sz / 2],  # 角点 6
        [sx / 2, -sy / 2, sz / 2],  # 角点 7
        [sx / 2, sy / 2, sz / 2],  # 角点 8
    ])

    # 旋转局部坐标到全局坐标
    rotation = R.from_quat([qx, qy, qz, qw])
    global_corners = rotation.apply(local_corners)

    # 平移到中心点
    global_corners += np.array([x, y, z])

    return global_corners


def run(input_dir, output_dir, src_dir):
    annos_dirs = find_folders(input_dir, 'annos')
    if len(annos_dirs) == 0:
        print("no annos found in dir: ", input_dir)
        return
    for annos_path in annos_dirs:
        seg_dirs = listdir(annos_path)
        for seg_dir in seg_dirs:
            src_seg_dir = os.path.join(src_dir, os.path.basename(seg_dir))
            json_files = sorted(glob(os.path.join(seg_dir, "*", "annos.json")),
                                key=lambda x: float(os.path.basename(os.path.dirname(x))))
            # {track_id: [frame1, frame2, ...]}
            track_data = {}
            for json_file in json_files:
                # print(json_file)
                clip_name = os.path.basename(os.path.dirname(json_file))
                timestamp = float(clip_name)
                camera_params_path = os.path.join(os.path.join(src_seg_dir, clip_name), 'params.json')
                camera_data = tools.get_json_data(camera_params_path)
                annos_data = tools.get_json_data(json_file)

                # track_data = convert(annos_data, camera_data, timestamp, track_data)
                # print(clip_name, seg_dir)
                outout_file = os.path.join(os.path.basename(seg_dir), clip_name+"_annos.json")
                output_file_path = os.path.join(output_dir, outout_file)
                # print(outout_file, output_file_path)
                # return
                if annos_data is None or annos_data["element_annos"] is None:
                    return

                # 当前帧的物体数据
                frame_objects = []
                for frame in annos_data["element_annos"]:
                    for item in frame["rawdata_annos"]:
                        if "objects" not in item:
                            continue
                        if len(item["objects"]) == 0:
                            continue

                        for obj in item["objects"]:
                            track_id = obj['track_id']
                            obj_type = obj["label"]["name"].replace("_", " ")
                            if obj_type not in anno_types:
                                continue
                            # print(obj_type)
                            data = obj["label"]["widget"]["data"]

                            # 解析 3D 立方体坐标
                            x, y, z = data[0:3]
                            length, width, height = data[3:6]
                            qx, qy, qz, qw = data[6:10]
                            # 计算欧拉角
                            angle_x, angle_y, angle_z = quaternion_to_euler(qx, qy, qz, qw)

                            # 相机数据
                            coordinates = []
                            if obj["label"]["widget"] is not None:
                                coordinates = obj["label"]["widget"]['data']
                            cam_2d_position = []
                            box_pts = compute_box_corners(coordinates)
                            points = [[x, y, z] for x, y, z in box_pts]
                            for camera_name in camera_id_mapping.keys():
                                camera_id = camera_id_mapping[camera_name]
                                camera_name_params = camera_data['cameras'][camera_name]
                                camera_extrinsic = camera_name_params['extrinsic']
                                if len(camera_extrinsic) == 16:
                                    camera_extrinsic = np.array(camera_extrinsic).reshape((4, 4))
                                camera_intrinsic = camera_name_params['intrinsic']
                                if len(camera_intrinsic) == 9:
                                    camera_intrinsic = np.array(camera_intrinsic).reshape((3, 3))
                                x_vals = []
                                y_vals = []
                                for pw in points:
                                    if isinstance(pw, (list, tuple)):
                                        pw = np.array(pw)
                                    if pw.shape == (3,):
                                        pw = np.append(pw, values=1)
                                    pc = camera_extrinsic @ pw
                                    pi = camera_intrinsic @ pc[:-1]
                                    if pi[2] > 0:
                                        pi = pi / pi[2]
                                        x_vals.append(pi[0])
                                        y_vals.append(pi[1])

                                # print(x_vals, y_vals)
                                if len(x_vals) == 0 or len(y_vals) == 0:
                                    continue
                                cam_2d_position.append({
                                    "camera_id": camera_id,
                                    "timestamp": float(timestamp),
                                    "max_x": max(x_vals),
                                    "max_y": max(y_vals),
                                    "min_x": min(x_vals),
                                    "min_y": min(y_vals),
                                })

                            # 计算速度和加速度
                            if track_id in track_data:
                                # 获取上一个有数据的帧
                                prev = track_data[track_id]
                                # 动态计算时间间隔 保留 1 位小数，减少浮点误差
                                # dt = round(timestamp - prev["timestamp"], 1)
                                dt = timestamp - prev["timestamp"]
                                # 计算 dt，但四舍五入到 0.2 的倍数
                                dt = round(dt / 0.2) * 0.2
                                print('---', track_id, timestamp, prev["timestamp"], dt)
                                # 只在时间前进时计算
                                if dt > 0:
                                    vx = (x - prev["imu.x"]) / dt
                                    vy = (y - prev["imu.y"]) / dt
                                    vz = (z - prev["imu.z"]) / dt
                                    ax = (vx - prev["imu.vx"]) / dt
                                    ay = (vy - prev["imu.vy"]) / dt
                                    az = (vz - prev["imu.vz"]) / dt
                                elif dt == 0:
                                    # 时间未前进，保持上一帧速度，但加速度设为 0
                                    vx, vy, vz = prev["imu.x"], prev["imu.y"], prev["imu.z"]
                                    ax = ay = az = 0
                                else:
                                    # 时间回退（异常情况），设为 0
                                    vx = vy = vz = ax = ay = az = 0
                            else:
                                # 该 track_id 首次出现，速度/加速度设为 0
                                vx = vy = vz = ax = ay = az = 0

                            item = {
                                "timestamp": timestamp,
                                "id": track_id,
                                "type": obj_type,
                                "length": length,
                                "width": width,
                                "height": height,
                                "imu.x": x,
                                "imu.y": y,
                                "imu.z": z,
                                "imu.vx": vx,
                                "imu.vy": vy,
                                "imu.vz": vz,
                                "imu.ax": ax,
                                "imu.ay": ay,
                                "imu.az": az,
                                "angle.x": angle_x,
                                "angle.y": angle_y,
                                "angle.z": angle_z,
                            }
                            if len(cam_2d_position) > 0:
                                item['cam_2d_position'] = cam_2d_position

                            # 记录 `track_id` 的最新帧数据
                            track_data[track_id] = item
                            frame_objects.append(item)

                if not frame_objects:
                    # print(json_file)
                    frame_objects = []
                tools.write_json_file(frame_objects, output_file_path, indent=4, ensure_ascii=False)
                # print(f"Generated: {output_file_path}")


if __name__ == "__main__":
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out, args.txt)
