package mq

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"strings"

	"anno/internal/conf"
	"gitlab.rp.konvery.work/platform/pkg/log"

	"gitlab.rp.konvery.work/pkg/mq"
	"gitlab.rp.konvery.work/pkg/mq/redis"
	"gitlab.rp.konvery.work/pkg/mq/sns"
	"gitlab.rp.konvery.work/pkg/mq/sqs"
)

type MQ interface {
	mq.Producer
	mq.Consumer
}

type (
	Message            = mq.Message
	BaseMessage        = mq.BaseMessage
	BasePublishOptions = mq.BasePublishOptions
	BaseConsumeOptions = mq.BaseConsumeOptions
	PublishOptions     = mq.PublishOptions
	ConsumeOptions     = mq.ConsumeOptions
	ConsumerHandler    = mq.ConsumerHandler
)

var (
	producer    mq.Producer
	consumer    mq.Consumer
	consumerMws []Middleware
	evtHandlers = map[string]map[string]EventHandler{}
	lg          *log.Helper
)

func Init(c *conf.MQ, logger log.Logger) {
	var err error
	lg = log.NewHelper(logger)
	if c.Producer != nil && c.Producer.Provider != conf.MQ_unspecified {
		spew.Dump(c.Producer)
		producer, err = newMQ(c.Producer.Provider, c.Producer.Topic, c, logger)
		if err != nil {
			panic(fmt.Errorf("failed to init producer: %w", err))
		}
	}
	spew.Dump(c.Consumer.Provider)
	if c.Consumer != nil && c.Consumer.Provider != conf.MQ_unspecified {
		spew.Dump(c.Consumer)
		consumer, err = newMQ(c.Consumer.Provider, "", c, logger)
		if err != nil {
			panic(fmt.Errorf("failed to init consumer: %w", err))
		}
	}
}

func newMQ(provider conf.MQ_Provider, defaultTopic string, mqc *conf.MQ, logger log.Logger) (MQ, error) {
	switch provider {
	case conf.MQ_redis_pubsub:
		cfg := &redis.Config{
			Addrs: []string{mqc.Redis.Addr},
			Type:  redis.Type_Pubsub,

			DefaultTopic: defaultTopic,
			Logger:       log.NewMqLogger(logger),
		}
		//spew.Dump(cfg)
		return redis.New(cfg)
	case conf.MQ_aws_sns:
		fmt.Println("sns")
		return sns.New(&sns.Config{DefaultTopicARN: defaultTopic})
	case conf.MQ_aws_sqs:
		return sqs.New(&sqs.Config{NameOrUrl: mqc.Sqs.Name, Logger: log.NewMqLogger(logger)})
	default:
		return nil, fmt.Errorf("unsupported mq type: %s", provider)
	}
}

func PublishEvt(ctx context.Context, typ, subtype string, body any) (err error) {
	spew.Dump("---> publish evt: ", typ, subtype, body)
	_, err = Publish(ctx, &Event{Type: typ, Subtype: subtype, Body: body}, nil)
	args := []any{"type", typ, "subtype", subtype, "body", body}
	if err == nil {
		lg.Info(ctx, "successfully emit an event", args...)
	} else {
		lg.Error(ctx, "failed to emit an event", err, args...)
	}
	return
}

func Publish(ctx context.Context, ev *Event, opts mq.PublishOptions) (msgID string, err error) {
	ev.TraceID = log.GetTraceIDOrCustomTraceID(ctx)
	msg, err := ev.Message()
	if err != nil {
		return
	}
	fmt.Println("---> event publish: ", ev.Type, ev.Subtype)
	return producer.Publish(ctx, msg, opts)
}

// func Consume(ctx context.Context, handler mq.ConsumerHandler, opts mq.ConsumeOptions) error {
// 	return consumer.Consume(ctx, handler, opts)
// }

// SetConsumerMws sets the middlewares for the consumer.
func SetConsumerMws(mws ...Middleware) { consumerMws = mws }

// StartConsumer starts to consume messages with the registered event handlers.
func StartConsumer(ctx context.Context, c *conf.MQ_Consumer) error {
	fmt.Println("---> start consumer...")
	if consumer == nil {
		fmt.Println("---> consumer not initialized.")
		return nil
	}

	handler := func(ctx context.Context, msg mq.Message) error {
		ev := NewEventFromMessage(msg)
		h := MatchEventHandler(ev)
		ctx = context.WithValue(ctx, log.CtxKeyCustomTraceID{}, ev.TraceID)
		return Chain(consumerMws...)(h)(ctx, ev)
	}
	topics := strings.Split(c.Topics, ",")
	if len(topics) == 0 {
		// SQS doesn't require a topic.
		topics = []string{""}
	}
	for _, topic := range topics {
		err := consumer.Consume(ctx, handler, &mq.BaseConsumeOptions{
			Topic:          topic,
			HandlerThreads: int(c.HandlerThreads),
		})
		if err != nil {
			return err
		}
	}
	return nil
}

// RegEventHandler registers an event handler for a given event type and subtype.
// If subtype is "", it registers a default handler for the event type.
// If both typ and subtype are "", it registers a global default handler.
func RegEventHandler(typ, subtype string, handler EventHandler) {
	handlers := evtHandlers[typ]
	if handlers == nil {
		handlers = map[string]EventHandler{}
		evtHandlers[typ] = handlers
	}
	handlers[subtype] = handler
}

func defaultEventHandler(ctx context.Context, ev *Event) error {
	lg.Info(ctx, "unhandled event", "type", ev.Type, "subtype", ev.Subtype)
	return nil
}

// MatchEventHandler returns the registered handler for the event.
func MatchEventHandler(ev *Event) EventHandler {
	hs := evtHandlers[ev.Type]
	if hs == nil {
		if h := evtHandlers[""][""]; h != nil {
			return h
		}
		return defaultEventHandler
	}

	if h := hs[ev.Subtype]; h != nil {
		return h
	}
	if h := hs[""]; h != nil {
		return h
	}
	return defaultEventHandler
}
