package bosch

import (
	"bytes"
	"context"
	"encoding/json"
	"os"
	"testing"

	"annofeed/internal/biz"

	"github.com/stretchr/testify/require"
)

// cp -r /Users/<USER>/prj/datalab/data/dazhuo/orig/3dpc workflow/style/dazhuo/3dpc.o
// cp -r /Users/<USER>/prj/datalab/data/dazhuo/orig/clp_1666344349200.json workflow/style/dazhuo/3dpc.o/params.json

func TestParseParams(t *testing.T) {
	file := "" // params file
	if file == "" {
		return
	}

	data, err := os.ReadFile(file)
	require.NoError(t, err)
	data = bytes.ReplaceAll(data, []byte("NaN"), []byte("null"))
	var p any
	err = json.Unmarshal(data, &p)
	require.NoError(t, err)
	t.Logf("%+v", p)
}

func TestTransform(t *testing.T) {
	odir := "" // source data dir
	if odir == "" {
		return
	}

	// os.MkdirAll(ndir, 0766)
	dataType, err := Transform(context.Background(), nil, odir, 0, func(context.Context, ...interface{}) {})
	require.NoError(t, err)
	require.Equal(t, biz.DataTypeFusion3D, dataType)
}

func TestYaml(t *testing.T) {
	yamlPath := ""
	if yamlPath == "" {
		return
	}

	data, err := os.ReadFile(yamlPath)
	require.NoError(t, err)
	if len(data) > 0 && data[0] == '%' {
		data = append([]byte{'#'}, data...)
	}
	var out OrigLidar
	// var out OrigCam
	// err = yaml.Unmarshal(data, &out)
	err = ParseYaml(yamlPath, &out)
	out.Transform()
	require.NoError(t, err)
	t.Logf("%+v", out)
}

func TestMatrixInverse(t *testing.T) {
	m4x4 := []float64{
		-0.02281, 0.00141, 0.99974, 1.93074,
		-0.99972, -0.00682, -0.02280, -0.03499,
		0.00679, -0.99998, 0.00156, 1.42999,
		0.00000, 0.00000, 0.00000, 1.00000}
	exp := []float64{-2.2809986e-02, -9.9971354e-01, 6.7860200e-03, -6.4376736e-04,
		1.4047391e-03, -6.8237595e-03, -9.9997151e-01, 1.4269984e+00,
		9.9973768e-01, -2.2799773e-02, 1.5651559e-03, -1.9332694e+00,
		0.0000000e+00, 0.0000000e+00, 0.0000000e+00, 1.0000000e+00}
	inv, err := InverseMatrix4x4(m4x4)
	require.NoError(t, err)
	// t.Log(inv)
	for i, v := range inv {
		require.InDelta(t, exp[i], v, 1e-6)
	}
}
