syntax = "proto3";

package anno.v1;

import "openapi/v3/annotations.proto";
import "validate/validate.proto";
import "types/range.proto";
import "anno/v1/elemanno.proto";
import "anno/v1/widget.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anno/v1;anno";
option java_multiple_files = true;
option java_package = "anno.v1";

// Label used in a task config
message Label {
  option (openapi.v3.schema) = {
    required: ["name", "display_name", "color"]
  };

  // key of the label
  string name = 1;
  string display_name = 2;
  string desc = 3;
  string avatar = 4;
  // RGB value: #RRGGBBAA
  string color = 5;

  // // [DEPRECATED] situation and corresponding widget used; e.g. image -> box, pointcloud -> cuboid
  // // key is of DataType
  // map<string, string> widgets = 6;
  reserved 6;

  // attributes to be attached
  AttrRefList attrs = 7;
  // muti-level classes: human -> adult, car -> mpv, ...
  repeated string class = 8;

  message Widget {
    option (openapi.v3.schema) = {
      required: ["name", "rawdata_type"]
    };

    // widget name
    WidgetName.Enum name = 1 [(validate.rules).enum = {defined_only: true}];
    // rawdata type the widget is applicable to
    Rawdata.Type.Enum rawdata_type = 2 [(validate.rules).enum = {defined_only: true}];
    // preset scale: it is
    // [width, height] for box2d in pixel;
    // [width, height, depth (distance between two center-points)] for pscuboid in pixel;
    // [sx, sy, sz] for cuboid in meter;
    repeated float preset = 3;
    // define the minimum and maximum scales the widget must conform to; see comments on the preset field for its format
    types.Range scale = 4;

    // line type used to draw the widget
    WidgetLineType.Enum line_type = 5 [(validate.rules).enum = {defined_only: true}];
  }
  repeated Widget widgets_v2 = 9;

  message Compound {
    option (openapi.v3.schema) = {
      required: ["parts"]
    };

    message Part {
      option (openapi.v3.schema) = {
        required: ["label"]
      };

      // label name
      string label = 1;
      // define minimum and maximum occurences of the part within a compound
      types.RangeInt32 occurs = 2;
    }

    // components
    repeated Part parts = 1;
  }
  // a compounded object
  Compound compound = 10;

  bool is_3d_segmentation = 11;
  bool has_instance = 12;
}

message AttrValue {
  option (openapi.v3.schema) = {
    required: ["name", "display_name", "value"]
  };

  // key of the AttrValue
  string name = 1;
  // display name
  string display_name = 2;
  // description
  string desc = 3;
  // avatar of the value
  string avatar = 4;
  // value, e.g. #FF00FF for RGB color
  string value = 5;
}

message Attr {
  option (openapi.v3.schema) = {
    required: ["name", "display_name", "type", "value_type", "choices"]
  };

  message Type {
    enum Enum {
      input = 0;
      bool = 1;
      radiobox = 2;
      checkbox = 3;
    }
  }
  message ValueType {
    enum Enum {
      int = 0;
      float = 1;
      text = 2;
      color = 5;
      date = 6;
      time = 7;
      datetime = 8;
      bool = 9;
    }
  }
  // enum ConstraintName {
  //   // for number values
  //   min = 0;
  //   max = 1;
  //   step = 2;
  // }
  // enum ConstraintValueType {
  //   bool = 0;
  //   float = 1;
  //   int = 2;
  //   expression = 3;
  // }
  // message ConstraintValue {
  //   option (openapi.v3.schema) = {
  //     required: ["type", "value"]
  //   };
  
  //   ConstraintValueType type = 1;
  //   string value = 2;
  // }

  // key of the attribute
  string name = 1;
  string display_name = 2;
  string desc = 3;
  string avatar = 4;

  Type.Enum type = 5 [(validate.rules).enum = {defined_only: true}];
  // accepted value type if type is input
  ValueType.Enum value_type = 6 [(validate.rules).enum = {defined_only: true}];
  // if not empty, value should be chosen from the list
  repeated AttrValue choices = 7;

  // // constraints associated with the attr. key is of type ConstraintName
  // // there're two types of constraints:
  // //   1-limitations applicable to the value itself. for example, for number values,
  // //     there may be min/max/step to define its value range and intervals for valid values.
  // //   2-dependencies to/mutually exclusiveness with other attributes
  // map<string, ConstraintValue> constraints = 8;
  // // if set, this attribute must present in the annotation result
  // bool required = 9;

  // // applicale situations
  // // the relationship of data_types and attrs is AND
  // message Condition {
  //   // the relationship of multiple data_types is OR
  //   repeated DataType data_types = 1;

  //   message AttrCondition {
  //     string name = 1;
  //     // the relationship of multiple values is OR
  //     repeated string values = 2;
  //   }
  //   // the relationship of multiple attrs is AND
  //   repeated AttrCondition attrs = 2;
  // }
  // Condition condition = 10;
}

message AttrRefList {
  option (openapi.v3.schema) = {
    required: ["optional_v2", "required_v2"]
  };
  message Attr {
    option (openapi.v3.schema) = {
      required: ["name"]
    };

    // attribute name
    string name = 1;
    // default value of this attribute
    string default = 2;
    // which rawdata types the attribute is applicable to
    repeated Rawdata.Type.Enum rawdata_types = 3;
  }

  // // [DEPRECATED] names of optional attributes
  // repeated string optional = 1;
  // // [DEPRECATED] names of required attributes
  // repeated string required = 2;
  reserved 1, 2;

  // optional attributes
  repeated Attr optional_v2 = 3;
  // required attributes
  repeated Attr required_v2 = 4;
}
