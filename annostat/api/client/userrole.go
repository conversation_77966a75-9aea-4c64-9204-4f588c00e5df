package client

import (
	"gitlab.rp.konvery.work/platform/apis/client"
)

const (
	SysRoleInspector = client.SysRoleInspector
	SysRoleKAM       = client.SysRoleKAM
	SysRolePM        = client.SysRolePM

	TeamRoleMember = client.TeamRoleMember
)

var (
	IsSysRole        = client.IsSysRole
	IsPrivileged     = client.IsPrivileged
	IsPrivilegedUser = client.IsPrivilegedUser
	GroupScope       = client.GroupScope
	UserScope        = client.UserScope
)
