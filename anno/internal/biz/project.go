// source: anno/v1/project.proto
package biz

import (
	"context"
	"time"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type Project struct {
	ID int64 `json:"id" gorm:"default:null"`
	// Uid    string `json:"uid" gorm:"default:null"`
	Name   string `json:"name" gorm:"default:null"`
	Desc   string `json:"desc" gorm:"default:null"`
	Avatar string `json:"avatar" gorm:"default:null"`
	Status string `json:"status" gorm:"default:null"`

	CreatorUid string    `json:"creator_uid" gorm:"default:null"`
	OrgUid     string    `json:"org_uid" gorm:"default:null"`
	UpdatedAt  time.Time `json:"updated_at" gorm:"default:null"`
	CreatedAt  time.Time `json:"created_at" gorm:"default:null"`
	DeletedAt  DeletedAt `json:"deleted_at" gorm:"default:null"`
}

func (o *Project) GetID() int64 { return o.ID }
func (o *Project) EnsureID() {
	if o.ID == 0 {
		o.ID = kid.NewID()
	}
}
func (o *Project) GetUid() string { return kid.StringID(o.ID) }
func (o *Project) UidFld() string { panic("code error") }

const ProjectTableName = "projects"

var (
	Project_             = field.RegObject(&Project{})
	ProjectUpdatableFlds = field.NewModel(ProjectTableName, Project_)

	ProjectSfldName       = field.Sname(&Project_.Name)
	ProjectSfldDesc       = field.Sname(&Project_.Desc)
	ProjectSfldAvatar     = field.Sname(&Project_.Avatar)
	ProjectSfldStatus     = field.Sname(&Project_.Status)
	ProjectSfldCreatorUid = field.Sname(&Project_.CreatorUid)
	ProjectSfldOrgUid     = field.Sname(&Project_.OrgUid)
)

type ProjectListFilter struct {
	IDs         []int64
	OrgUid      string
	CreatorUid  string
	NamePattern string
}

type ProjectsRepo interface {
	Create(context.Context, *Project) (*Project, error)
	Update(context.Context, *Project, *FieldMask) (*Project, error)
	GetByID(context.Context, int64) (*Project, error)
	DeleteByID(context.Context, int64) error
	List(context.Context, *ProjectListFilter, Pager) ([]*Project, error)
	Count(context.Context, *ProjectListFilter) (int, error)
}

type ProjectsBiz struct {
	repo ProjectsRepo
	log  *log.Helper
}

func NewProjectsBiz(repo ProjectsRepo, logger log.Logger) *ProjectsBiz {
	return &ProjectsBiz{repo: repo, log: log.NewHelper(logger)}
}

func (o *ProjectsBiz) Create(ctx context.Context, p *Project) (*Project, error) {
	o.log.Info(ctx, "CreateProject", "param", p)
	return o.repo.Create(ctx, p)
}

func (o *ProjectsBiz) Update(ctx context.Context, p *Project, fldMask *FieldMask) (*Project, error) {
	o.log.Info(ctx, "UpdateProject", "param", p)
	return o.repo.Update(ctx, p, fldMask)
}

func (o *ProjectsBiz) GetByID(ctx context.Context, id int64) (*Project, error) {
	return o.repo.GetByID(ctx, id)
}

func (o *ProjectsBiz) GetByUid(ctx context.Context, uid string) (*Project, error) {
	return o.GetByID(ctx, kid.ParseID(uid))
}

func (o *ProjectsBiz) DeleteByID(ctx context.Context, id int64) error {
	o.log.Info(ctx, "DeleteByIDProject", "id", id)
	return o.repo.DeleteByID(ctx, id)
}

func (o *ProjectsBiz) DeleteByUid(ctx context.Context, uid string) error {
	o.log.Info(ctx, "DeleteByUidProject", "uid", uid)
	return o.DeleteByID(ctx, kid.ParseID(uid))
}

func (o *ProjectsBiz) List(ctx context.Context, filter *ProjectListFilter, pager Pager) ([]*Project, error) {
	o.log.Info(ctx, "ListProject", "param", filter)
	return o.repo.List(ctx, filter, pager)
}

func (o *ProjectsBiz) Count(ctx context.Context, filter *ProjectListFilter) (int, error) {
	return o.repo.Count(ctx, filter)
}
