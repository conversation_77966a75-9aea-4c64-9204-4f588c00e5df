// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: annostat/v1/stats.proto

package annostat

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetOrderCountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrderCountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderCountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrderCountRequestMultiError, or nil if none found.
func (m *GetOrderCountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderCountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderCountRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderCountRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderCountRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOrderCountRequestMultiError(errors)
	}

	return nil
}

// GetOrderCountRequestMultiError is an error wrapping multiple validation
// errors returned by GetOrderCountRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOrderCountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderCountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderCountRequestMultiError) AllErrors() []error { return m }

// GetOrderCountRequestValidationError is the validation error returned by
// GetOrderCountRequest.Validate if the designated constraints aren't met.
type GetOrderCountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderCountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderCountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderCountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderCountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderCountRequestValidationError) ErrorName() string {
	return "GetOrderCountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderCountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderCountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderCountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderCountRequestValidationError{}

// Validate checks the field values on GetOrderCountReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrderCountReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderCountReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrderCountReplyMultiError, or nil if none found.
func (m *GetOrderCountReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderCountReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	// no validation rules for Ongoing

	// no validation rules for Paused

	// no validation rules for Finished

	if len(errors) > 0 {
		return GetOrderCountReplyMultiError(errors)
	}

	return nil
}

// GetOrderCountReplyMultiError is an error wrapping multiple validation errors
// returned by GetOrderCountReply.ValidateAll() if the designated constraints
// aren't met.
type GetOrderCountReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderCountReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderCountReplyMultiError) AllErrors() []error { return m }

// GetOrderCountReplyValidationError is the validation error returned by
// GetOrderCountReply.Validate if the designated constraints aren't met.
type GetOrderCountReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderCountReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderCountReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderCountReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderCountReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderCountReplyValidationError) ErrorName() string {
	return "GetOrderCountReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderCountReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderCountReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderCountReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderCountReplyValidationError{}

// Validate checks the field values on GetOrderConversionReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrderConversionReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderConversionReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrderConversionReplyMultiError, or nil if none found.
func (m *GetOrderConversionReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderConversionReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Orders

	// no validation rules for Lots

	if len(errors) > 0 {
		return GetOrderConversionReplyMultiError(errors)
	}

	return nil
}

// GetOrderConversionReplyMultiError is an error wrapping multiple validation
// errors returned by GetOrderConversionReply.ValidateAll() if the designated
// constraints aren't met.
type GetOrderConversionReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderConversionReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderConversionReplyMultiError) AllErrors() []error { return m }

// GetOrderConversionReplyValidationError is the validation error returned by
// GetOrderConversionReply.Validate if the designated constraints aren't met.
type GetOrderConversionReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderConversionReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderConversionReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderConversionReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderConversionReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderConversionReplyValidationError) ErrorName() string {
	return "GetOrderConversionReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderConversionReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderConversionReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderConversionReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderConversionReplyValidationError{}

// Validate checks the field values on GetLotCountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLotCountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLotCountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLotCountRequestMultiError, or nil if none found.
func (m *GetLotCountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLotCountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLotCountRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLotCountRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLotCountRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLotCountRequestMultiError(errors)
	}

	return nil
}

// GetLotCountRequestMultiError is an error wrapping multiple validation errors
// returned by GetLotCountRequest.ValidateAll() if the designated constraints
// aren't met.
type GetLotCountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLotCountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLotCountRequestMultiError) AllErrors() []error { return m }

// GetLotCountRequestValidationError is the validation error returned by
// GetLotCountRequest.Validate if the designated constraints aren't met.
type GetLotCountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLotCountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLotCountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLotCountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLotCountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLotCountRequestValidationError) ErrorName() string {
	return "GetLotCountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLotCountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLotCountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLotCountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLotCountRequestValidationError{}

// Validate checks the field values on GetLotCountReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetLotCountReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLotCountReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLotCountReplyMultiError, or nil if none found.
func (m *GetLotCountReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLotCountReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	// no validation rules for Ongoing

	// no validation rules for Paused

	// no validation rules for Finished

	if len(errors) > 0 {
		return GetLotCountReplyMultiError(errors)
	}

	return nil
}

// GetLotCountReplyMultiError is an error wrapping multiple validation errors
// returned by GetLotCountReply.ValidateAll() if the designated constraints
// aren't met.
type GetLotCountReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLotCountReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLotCountReplyMultiError) AllErrors() []error { return m }

// GetLotCountReplyValidationError is the validation error returned by
// GetLotCountReply.Validate if the designated constraints aren't met.
type GetLotCountReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLotCountReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLotCountReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLotCountReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLotCountReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLotCountReplyValidationError) ErrorName() string { return "GetLotCountReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetLotCountReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLotCountReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLotCountReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLotCountReplyValidationError{}

// Validate checks the field values on GetLotStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLotStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLotStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLotStatusRequestMultiError, or nil if none found.
func (m *GetLotStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLotStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetLotStatusRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := GetLotStatusRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetLotStatusRequestMultiError(errors)
	}

	return nil
}

// GetLotStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetLotStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLotStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLotStatusRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLotStatusRequestMultiError) AllErrors() []error { return m }

// GetLotStatusRequestValidationError is the validation error returned by
// GetLotStatusRequest.Validate if the designated constraints aren't met.
type GetLotStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLotStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLotStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLotStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLotStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLotStatusRequestValidationError) ErrorName() string {
	return "GetLotStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLotStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLotStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLotStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLotStatusRequestValidationError{}

var _GetLotStatusRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on GetLotStatusReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetLotStatusReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLotStatusReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLotStatusReplyMultiError, or nil if none found.
func (m *GetLotStatusReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLotStatusReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalElems

	for idx, item := range m.GetPhases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLotStatusReplyValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLotStatusReplyValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLotStatusReplyValidationError{
					field:  fmt.Sprintf("Phases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetLotStatusReplyMultiError(errors)
	}

	return nil
}

// GetLotStatusReplyMultiError is an error wrapping multiple validation errors
// returned by GetLotStatusReply.ValidateAll() if the designated constraints
// aren't met.
type GetLotStatusReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLotStatusReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLotStatusReplyMultiError) AllErrors() []error { return m }

// GetLotStatusReplyValidationError is the validation error returned by
// GetLotStatusReply.Validate if the designated constraints aren't met.
type GetLotStatusReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLotStatusReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLotStatusReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLotStatusReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLotStatusReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLotStatusReplyValidationError) ErrorName() string {
	return "GetLotStatusReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetLotStatusReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLotStatusReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLotStatusReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLotStatusReplyValidationError{}

// Validate checks the field values on GetLotLabelStatReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLotLabelStatReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLotLabelStatReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLotLabelStatReplyMultiError, or nil if none found.
func (m *GetLotLabelStatReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLotLabelStatReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetCuboids()))
		i := 0
		for key := range m.GetCuboids() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetCuboids()[key]
			_ = val

			// no validation rules for Cuboids[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetLotLabelStatReplyValidationError{
							field:  fmt.Sprintf("Cuboids[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetLotLabelStatReplyValidationError{
							field:  fmt.Sprintf("Cuboids[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetLotLabelStatReplyValidationError{
						field:  fmt.Sprintf("Cuboids[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetLotLabelStatReplyMultiError(errors)
	}

	return nil
}

// GetLotLabelStatReplyMultiError is an error wrapping multiple validation
// errors returned by GetLotLabelStatReply.ValidateAll() if the designated
// constraints aren't met.
type GetLotLabelStatReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLotLabelStatReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLotLabelStatReplyMultiError) AllErrors() []error { return m }

// GetLotLabelStatReplyValidationError is the validation error returned by
// GetLotLabelStatReply.Validate if the designated constraints aren't met.
type GetLotLabelStatReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLotLabelStatReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLotLabelStatReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLotLabelStatReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLotLabelStatReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLotLabelStatReplyValidationError) ErrorName() string {
	return "GetLotLabelStatReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetLotLabelStatReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLotLabelStatReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLotLabelStatReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLotLabelStatReplyValidationError{}

// Validate checks the field values on TimeRange with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TimeRange) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TimeRange with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TimeRangeMultiError, or nil
// if none found.
func (m *TimeRange) ValidateAll() error {
	return m.validate(true)
}

func (m *TimeRange) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFrom()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "From",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "From",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFrom()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TimeRangeValidationError{
				field:  "From",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "To",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "To",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TimeRangeValidationError{
				field:  "To",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TimeRangeMultiError(errors)
	}

	return nil
}

// TimeRangeMultiError is an error wrapping multiple validation errors returned
// by TimeRange.ValidateAll() if the designated constraints aren't met.
type TimeRangeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TimeRangeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TimeRangeMultiError) AllErrors() []error { return m }

// TimeRangeValidationError is the validation error returned by
// TimeRange.Validate if the designated constraints aren't met.
type TimeRangeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TimeRangeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TimeRangeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TimeRangeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TimeRangeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TimeRangeValidationError) ErrorName() string { return "TimeRangeValidationError" }

// Error satisfies the builtin error interface
func (e TimeRangeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTimeRange.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TimeRangeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TimeRangeValidationError{}

// Validate checks the field values on DataType with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DataType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataType with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DataTypeMultiError, or nil
// if none found.
func (m *DataType) ValidateAll() error {
	return m.validate(true)
}

func (m *DataType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DataTypeMultiError(errors)
	}

	return nil
}

// DataTypeMultiError is an error wrapping multiple validation errors returned
// by DataType.ValidateAll() if the designated constraints aren't met.
type DataTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataTypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataTypeMultiError) AllErrors() []error { return m }

// DataTypeValidationError is the validation error returned by
// DataType.Validate if the designated constraints aren't met.
type DataTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataTypeValidationError) ErrorName() string { return "DataTypeValidationError" }

// Error satisfies the builtin error interface
func (e DataTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataTypeValidationError{}

// Validate checks the field values on LotType with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LotType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LotType with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in LotTypeMultiError, or nil if none found.
func (m *LotType) ValidateAll() error {
	return m.validate(true)
}

func (m *LotType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return LotTypeMultiError(errors)
	}

	return nil
}

// LotTypeMultiError is an error wrapping multiple validation errors returned
// by LotType.ValidateAll() if the designated constraints aren't met.
type LotTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LotTypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LotTypeMultiError) AllErrors() []error { return m }

// LotTypeValidationError is the validation error returned by LotType.Validate
// if the designated constraints aren't met.
type LotTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LotTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LotTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LotTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LotTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LotTypeValidationError) ErrorName() string { return "LotTypeValidationError" }

// Error satisfies the builtin error interface
func (e LotTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLotType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LotTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LotTypeValidationError{}

// Validate checks the field values on LotFilter with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LotFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LotFilter with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LotFilterMultiError, or nil
// if none found.
func (m *LotFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *LotFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LotFilterValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LotFilterValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LotFilterValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLotTypes() {
		_, _ = idx, item

		if _, ok := LotType_Enum_name[int32(item)]; !ok {
			err := LotFilterValidationError{
				field:  fmt.Sprintf("LotTypes[%v]", idx),
				reason: "value must be one of the defined enum values",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	for idx, item := range m.GetDataTypes() {
		_, _ = idx, item

		if _, ok := DataType_Enum_name[int32(item)]; !ok {
			err := LotFilterValidationError{
				field:  fmt.Sprintf("DataTypes[%v]", idx),
				reason: "value must be one of the defined enum values",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	for idx, item := range m.GetLotUids() {
		_, _ = idx, item

		if !_LotFilter_LotUids_Pattern.MatchString(item) {
			err := LotFilterValidationError{
				field:  fmt.Sprintf("LotUids[%v]", idx),
				reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	for idx, item := range m.GetOwnerOrgs() {
		_, _ = idx, item

		if !_LotFilter_OwnerOrgs_Pattern.MatchString(item) {
			err := LotFilterValidationError{
				field:  fmt.Sprintf("OwnerOrgs[%v]", idx),
				reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	for idx, item := range m.GetExecutorOrgs() {
		_, _ = idx, item

		if !_LotFilter_ExecutorOrgs_Pattern.MatchString(item) {
			err := LotFilterValidationError{
				field:  fmt.Sprintf("ExecutorOrgs[%v]", idx),
				reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	for idx, item := range m.GetExecutorUids() {
		_, _ = idx, item

		if !_LotFilter_ExecutorUids_Pattern.MatchString(item) {
			err := LotFilterValidationError{
				field:  fmt.Sprintf("ExecutorUids[%v]", idx),
				reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return LotFilterMultiError(errors)
	}

	return nil
}

// LotFilterMultiError is an error wrapping multiple validation errors returned
// by LotFilter.ValidateAll() if the designated constraints aren't met.
type LotFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LotFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LotFilterMultiError) AllErrors() []error { return m }

// LotFilterValidationError is the validation error returned by
// LotFilter.Validate if the designated constraints aren't met.
type LotFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LotFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LotFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LotFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LotFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LotFilterValidationError) ErrorName() string { return "LotFilterValidationError" }

// Error satisfies the builtin error interface
func (e LotFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLotFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LotFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LotFilterValidationError{}

var _LotFilter_LotUids_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

var _LotFilter_OwnerOrgs_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

var _LotFilter_ExecutorOrgs_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

var _LotFilter_ExecutorUids_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on ProductionByTimeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProductionByTimeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProductionByTimeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProductionByTimeRequestMultiError, or nil if none found.
func (m *ProductionByTimeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProductionByTimeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProductionByTimeRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProductionByTimeRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProductionByTimeRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _ProductionByTimeRequest_TimeUnit_InLookup[m.GetTimeUnit()]; !ok {
		err := ProductionByTimeRequestValidationError{
			field:  "TimeUnit",
			reason: "value must be in list [0 3600 86400]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if _, ok := _ProductionByTimeRequest_Items_InLookup[item]; !ok {
			err := ProductionByTimeRequestValidationError{
				field:  fmt.Sprintf("Items[%v]", idx),
				reason: "value must be in list [anno2d anno3d elem job]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return ProductionByTimeRequestMultiError(errors)
	}

	return nil
}

// ProductionByTimeRequestMultiError is an error wrapping multiple validation
// errors returned by ProductionByTimeRequest.ValidateAll() if the designated
// constraints aren't met.
type ProductionByTimeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProductionByTimeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProductionByTimeRequestMultiError) AllErrors() []error { return m }

// ProductionByTimeRequestValidationError is the validation error returned by
// ProductionByTimeRequest.Validate if the designated constraints aren't met.
type ProductionByTimeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProductionByTimeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProductionByTimeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProductionByTimeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProductionByTimeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProductionByTimeRequestValidationError) ErrorName() string {
	return "ProductionByTimeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProductionByTimeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProductionByTimeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProductionByTimeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProductionByTimeRequestValidationError{}

var _ProductionByTimeRequest_TimeUnit_InLookup = map[int32]struct{}{
	0:     {},
	3600:  {},
	86400: {},
}

var _ProductionByTimeRequest_Items_InLookup = map[string]struct{}{
	"anno2d": {},
	"anno3d": {},
	"elem":   {},
	"job":    {},
}

// Validate checks the field values on ProductionByTimeReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProductionByTimeReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProductionByTimeReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProductionByTimeReplyMultiError, or nil if none found.
func (m *ProductionByTimeReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ProductionByTimeReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUnits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProductionByTimeReplyValidationError{
						field:  fmt.Sprintf("Units[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProductionByTimeReplyValidationError{
						field:  fmt.Sprintf("Units[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProductionByTimeReplyValidationError{
					field:  fmt.Sprintf("Units[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProductionByTimeReplyMultiError(errors)
	}

	return nil
}

// ProductionByTimeReplyMultiError is an error wrapping multiple validation
// errors returned by ProductionByTimeReply.ValidateAll() if the designated
// constraints aren't met.
type ProductionByTimeReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProductionByTimeReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProductionByTimeReplyMultiError) AllErrors() []error { return m }

// ProductionByTimeReplyValidationError is the validation error returned by
// ProductionByTimeReply.Validate if the designated constraints aren't met.
type ProductionByTimeReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProductionByTimeReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProductionByTimeReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProductionByTimeReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProductionByTimeReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProductionByTimeReplyValidationError) ErrorName() string {
	return "ProductionByTimeReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ProductionByTimeReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProductionByTimeReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProductionByTimeReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProductionByTimeReplyValidationError{}

// Validate checks the field values on GetLotStatByExecutorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLotStatByExecutorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLotStatByExecutorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLotStatByExecutorRequestMultiError, or nil if none found.
func (m *GetLotStatByExecutorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLotStatByExecutorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetLotStatByExecutorRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := GetLotStatByExecutorRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPhase() < 1 {
		err := GetLotStatByExecutorRequestValidationError{
			field:  "Phase",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ByExecteam

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLotStatByExecutorRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLotStatByExecutorRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLotStatByExecutorRequestValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Page

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := GetLotStatByExecutorRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetLotStatByExecutorRequestMultiError(errors)
	}

	return nil
}

// GetLotStatByExecutorRequestMultiError is an error wrapping multiple
// validation errors returned by GetLotStatByExecutorRequest.ValidateAll() if
// the designated constraints aren't met.
type GetLotStatByExecutorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLotStatByExecutorRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLotStatByExecutorRequestMultiError) AllErrors() []error { return m }

// GetLotStatByExecutorRequestValidationError is the validation error returned
// by GetLotStatByExecutorRequest.Validate if the designated constraints
// aren't met.
type GetLotStatByExecutorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLotStatByExecutorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLotStatByExecutorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLotStatByExecutorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLotStatByExecutorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLotStatByExecutorRequestValidationError) ErrorName() string {
	return "GetLotStatByExecutorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLotStatByExecutorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLotStatByExecutorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLotStatByExecutorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLotStatByExecutorRequestValidationError{}

var _GetLotStatByExecutorRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on ExecutorCounter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ExecutorCounter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecutorCounter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExecutorCounterMultiError, or nil if none found.
func (m *ExecutorCounter) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecutorCounter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetExecutor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecutorCounterValidationError{
					field:  "Executor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecutorCounterValidationError{
					field:  "Executor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExecutor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecutorCounterValidationError{
				field:  "Executor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Jobs

	// no validation rules for Elems

	// no validation rules for Ins

	// no validation rules for InsByWidget

	// no validation rules for JobsPerHour

	// no validation rules for ElemsPerHour

	// no validation rules for ElemAccuracy

	// no validation rules for InsAccuracy

	if len(errors) > 0 {
		return ExecutorCounterMultiError(errors)
	}

	return nil
}

// ExecutorCounterMultiError is an error wrapping multiple validation errors
// returned by ExecutorCounter.ValidateAll() if the designated constraints
// aren't met.
type ExecutorCounterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecutorCounterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecutorCounterMultiError) AllErrors() []error { return m }

// ExecutorCounterValidationError is the validation error returned by
// ExecutorCounter.Validate if the designated constraints aren't met.
type ExecutorCounterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecutorCounterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecutorCounterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecutorCounterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecutorCounterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecutorCounterValidationError) ErrorName() string { return "ExecutorCounterValidationError" }

// Error satisfies the builtin error interface
func (e ExecutorCounterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecutorCounter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecutorCounterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecutorCounterValidationError{}

// Validate checks the field values on GetLotStatByExecutorReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLotStatByExecutorReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLotStatByExecutorReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLotStatByExecutorReplyMultiError, or nil if none found.
func (m *GetLotStatByExecutorReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLotStatByExecutorReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetCounters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLotStatByExecutorReplyValidationError{
						field:  fmt.Sprintf("Counters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLotStatByExecutorReplyValidationError{
						field:  fmt.Sprintf("Counters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLotStatByExecutorReplyValidationError{
					field:  fmt.Sprintf("Counters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetLotStatByExecutorReplyMultiError(errors)
	}

	return nil
}

// GetLotStatByExecutorReplyMultiError is an error wrapping multiple validation
// errors returned by GetLotStatByExecutorReply.ValidateAll() if the
// designated constraints aren't met.
type GetLotStatByExecutorReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLotStatByExecutorReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLotStatByExecutorReplyMultiError) AllErrors() []error { return m }

// GetLotStatByExecutorReplyValidationError is the validation error returned by
// GetLotStatByExecutorReply.Validate if the designated constraints aren't met.
type GetLotStatByExecutorReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLotStatByExecutorReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLotStatByExecutorReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLotStatByExecutorReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLotStatByExecutorReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLotStatByExecutorReplyValidationError) ErrorName() string {
	return "GetLotStatByExecutorReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetLotStatByExecutorReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLotStatByExecutorReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLotStatByExecutorReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLotStatByExecutorReplyValidationError{}

// Validate checks the field values on GetOngoingLotsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOngoingLotsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOngoingLotsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOngoingLotsRequestMultiError, or nil if none found.
func (m *GetOngoingLotsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOngoingLotsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := GetOngoingLotsRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetOngoingLotsRequestMultiError(errors)
	}

	return nil
}

// GetOngoingLotsRequestMultiError is an error wrapping multiple validation
// errors returned by GetOngoingLotsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOngoingLotsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOngoingLotsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOngoingLotsRequestMultiError) AllErrors() []error { return m }

// GetOngoingLotsRequestValidationError is the validation error returned by
// GetOngoingLotsRequest.Validate if the designated constraints aren't met.
type GetOngoingLotsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOngoingLotsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOngoingLotsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOngoingLotsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOngoingLotsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOngoingLotsRequestValidationError) ErrorName() string {
	return "GetOngoingLotsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOngoingLotsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOngoingLotsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOngoingLotsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOngoingLotsRequestValidationError{}

// Validate checks the field values on GetOngoingLotsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOngoingLotsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOngoingLotsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOngoingLotsReplyMultiError, or nil if none found.
func (m *GetOngoingLotsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOngoingLotsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetLots() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOngoingLotsReplyValidationError{
						field:  fmt.Sprintf("Lots[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOngoingLotsReplyValidationError{
						field:  fmt.Sprintf("Lots[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOngoingLotsReplyValidationError{
					field:  fmt.Sprintf("Lots[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetOngoingLotsReplyMultiError(errors)
	}

	return nil
}

// GetOngoingLotsReplyMultiError is an error wrapping multiple validation
// errors returned by GetOngoingLotsReply.ValidateAll() if the designated
// constraints aren't met.
type GetOngoingLotsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOngoingLotsReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOngoingLotsReplyMultiError) AllErrors() []error { return m }

// GetOngoingLotsReplyValidationError is the validation error returned by
// GetOngoingLotsReply.Validate if the designated constraints aren't met.
type GetOngoingLotsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOngoingLotsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOngoingLotsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOngoingLotsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOngoingLotsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOngoingLotsReplyValidationError) ErrorName() string {
	return "GetOngoingLotsReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetOngoingLotsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOngoingLotsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOngoingLotsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOngoingLotsReplyValidationError{}

// Validate checks the field values on GetOrderCountRequest_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrderCountRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderCountRequest_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrderCountRequest_FilterMultiError, or nil if none found.
func (m *GetOrderCountRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderCountRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderCountRequest_FilterValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderCountRequest_FilterValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderCountRequest_FilterValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOrderCountRequest_FilterMultiError(errors)
	}

	return nil
}

// GetOrderCountRequest_FilterMultiError is an error wrapping multiple
// validation errors returned by GetOrderCountRequest_Filter.ValidateAll() if
// the designated constraints aren't met.
type GetOrderCountRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderCountRequest_FilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderCountRequest_FilterMultiError) AllErrors() []error { return m }

// GetOrderCountRequest_FilterValidationError is the validation error returned
// by GetOrderCountRequest_Filter.Validate if the designated constraints
// aren't met.
type GetOrderCountRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderCountRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderCountRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderCountRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderCountRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderCountRequest_FilterValidationError) ErrorName() string {
	return "GetOrderCountRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderCountRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderCountRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderCountRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderCountRequest_FilterValidationError{}

// Validate checks the field values on GetLotStatusReply_Phase with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLotStatusReply_Phase) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLotStatusReply_Phase with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLotStatusReply_PhaseMultiError, or nil if none found.
func (m *GetLotStatusReply_Phase) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLotStatusReply_Phase) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ElemsToWork

	// no validation rules for EstimatedDaysLeft

	if len(errors) > 0 {
		return GetLotStatusReply_PhaseMultiError(errors)
	}

	return nil
}

// GetLotStatusReply_PhaseMultiError is an error wrapping multiple validation
// errors returned by GetLotStatusReply_Phase.ValidateAll() if the designated
// constraints aren't met.
type GetLotStatusReply_PhaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLotStatusReply_PhaseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLotStatusReply_PhaseMultiError) AllErrors() []error { return m }

// GetLotStatusReply_PhaseValidationError is the validation error returned by
// GetLotStatusReply_Phase.Validate if the designated constraints aren't met.
type GetLotStatusReply_PhaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLotStatusReply_PhaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLotStatusReply_PhaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLotStatusReply_PhaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLotStatusReply_PhaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLotStatusReply_PhaseValidationError) ErrorName() string {
	return "GetLotStatusReply_PhaseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLotStatusReply_PhaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLotStatusReply_Phase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLotStatusReply_PhaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLotStatusReply_PhaseValidationError{}

// Validate checks the field values on GetLotLabelStatReply_Cuboid with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLotLabelStatReply_Cuboid) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLotLabelStatReply_Cuboid with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLotLabelStatReply_CuboidMultiError, or nil if none found.
func (m *GetLotLabelStatReply_Cuboid) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLotLabelStatReply_Cuboid) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InsCnt

	if len(errors) > 0 {
		return GetLotLabelStatReply_CuboidMultiError(errors)
	}

	return nil
}

// GetLotLabelStatReply_CuboidMultiError is an error wrapping multiple
// validation errors returned by GetLotLabelStatReply_Cuboid.ValidateAll() if
// the designated constraints aren't met.
type GetLotLabelStatReply_CuboidMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLotLabelStatReply_CuboidMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLotLabelStatReply_CuboidMultiError) AllErrors() []error { return m }

// GetLotLabelStatReply_CuboidValidationError is the validation error returned
// by GetLotLabelStatReply_Cuboid.Validate if the designated constraints
// aren't met.
type GetLotLabelStatReply_CuboidValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLotLabelStatReply_CuboidValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLotLabelStatReply_CuboidValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLotLabelStatReply_CuboidValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLotLabelStatReply_CuboidValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLotLabelStatReply_CuboidValidationError) ErrorName() string {
	return "GetLotLabelStatReply_CuboidValidationError"
}

// Error satisfies the builtin error interface
func (e GetLotLabelStatReply_CuboidValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLotLabelStatReply_Cuboid.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLotLabelStatReply_CuboidValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLotLabelStatReply_CuboidValidationError{}

// Validate checks the field values on ProductionByTimeReply_Unit with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProductionByTimeReply_Unit) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProductionByTimeReply_Unit with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProductionByTimeReply_UnitMultiError, or nil if none found.
func (m *ProductionByTimeReply_Unit) ValidateAll() error {
	return m.validate(true)
}

func (m *ProductionByTimeReply_Unit) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProductionByTimeReply_UnitValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProductionByTimeReply_UnitValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProductionByTimeReply_UnitValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Items

	if len(errors) > 0 {
		return ProductionByTimeReply_UnitMultiError(errors)
	}

	return nil
}

// ProductionByTimeReply_UnitMultiError is an error wrapping multiple
// validation errors returned by ProductionByTimeReply_Unit.ValidateAll() if
// the designated constraints aren't met.
type ProductionByTimeReply_UnitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProductionByTimeReply_UnitMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProductionByTimeReply_UnitMultiError) AllErrors() []error { return m }

// ProductionByTimeReply_UnitValidationError is the validation error returned
// by ProductionByTimeReply_Unit.Validate if the designated constraints aren't met.
type ProductionByTimeReply_UnitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProductionByTimeReply_UnitValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProductionByTimeReply_UnitValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProductionByTimeReply_UnitValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProductionByTimeReply_UnitValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProductionByTimeReply_UnitValidationError) ErrorName() string {
	return "ProductionByTimeReply_UnitValidationError"
}

// Error satisfies the builtin error interface
func (e ProductionByTimeReply_UnitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProductionByTimeReply_Unit.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProductionByTimeReply_UnitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProductionByTimeReply_UnitValidationError{}

// Validate checks the field values on GetOngoingLotsReply_Phase with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOngoingLotsReply_Phase) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOngoingLotsReply_Phase with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOngoingLotsReply_PhaseMultiError, or nil if none found.
func (m *GetOngoingLotsReply_Phase) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOngoingLotsReply_Phase) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Number

	// no validation rules for Name

	if _, ok := _GetOngoingLotsReply_Phase_Type_NotInLookup[m.GetType()]; ok {
		err := GetOngoingLotsReply_PhaseValidationError{
			field:  "Type",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := GetOngoingLotsReply_Phase_Type_Enum_name[int32(m.GetType())]; !ok {
		err := GetOngoingLotsReply_PhaseValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EstimatedDaysLeft

	if len(errors) > 0 {
		return GetOngoingLotsReply_PhaseMultiError(errors)
	}

	return nil
}

// GetOngoingLotsReply_PhaseMultiError is an error wrapping multiple validation
// errors returned by GetOngoingLotsReply_Phase.ValidateAll() if the
// designated constraints aren't met.
type GetOngoingLotsReply_PhaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOngoingLotsReply_PhaseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOngoingLotsReply_PhaseMultiError) AllErrors() []error { return m }

// GetOngoingLotsReply_PhaseValidationError is the validation error returned by
// GetOngoingLotsReply_Phase.Validate if the designated constraints aren't met.
type GetOngoingLotsReply_PhaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOngoingLotsReply_PhaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOngoingLotsReply_PhaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOngoingLotsReply_PhaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOngoingLotsReply_PhaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOngoingLotsReply_PhaseValidationError) ErrorName() string {
	return "GetOngoingLotsReply_PhaseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOngoingLotsReply_PhaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOngoingLotsReply_Phase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOngoingLotsReply_PhaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOngoingLotsReply_PhaseValidationError{}

var _GetOngoingLotsReply_Phase_Type_NotInLookup = map[GetOngoingLotsReply_Phase_Type_Enum]struct{}{
	0: {},
}

// Validate checks the field values on GetOngoingLotsReply_Lot with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOngoingLotsReply_Lot) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOngoingLotsReply_Lot with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOngoingLotsReply_LotMultiError, or nil if none found.
func (m *GetOngoingLotsReply_Lot) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOngoingLotsReply_Lot) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetOngoingLotsReply_Lot_Uid_Pattern.MatchString(m.GetUid()) {
		err := GetOngoingLotsReply_LotValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Name

	if _, ok := DataType_Enum_name[int32(m.GetDataType())]; !ok {
		err := GetOngoingLotsReply_LotValidationError{
			field:  "DataType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for DataSize

	for idx, item := range m.GetPhases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOngoingLotsReply_LotValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOngoingLotsReply_LotValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOngoingLotsReply_LotValidationError{
					field:  fmt.Sprintf("Phases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetExpEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOngoingLotsReply_LotValidationError{
					field:  "ExpEndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOngoingLotsReply_LotValidationError{
					field:  "ExpEndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOngoingLotsReply_LotValidationError{
				field:  "ExpEndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOngoingLotsReply_LotValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOngoingLotsReply_LotValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOngoingLotsReply_LotValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOngoingLotsReply_LotMultiError(errors)
	}

	return nil
}

// GetOngoingLotsReply_LotMultiError is an error wrapping multiple validation
// errors returned by GetOngoingLotsReply_Lot.ValidateAll() if the designated
// constraints aren't met.
type GetOngoingLotsReply_LotMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOngoingLotsReply_LotMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOngoingLotsReply_LotMultiError) AllErrors() []error { return m }

// GetOngoingLotsReply_LotValidationError is the validation error returned by
// GetOngoingLotsReply_Lot.Validate if the designated constraints aren't met.
type GetOngoingLotsReply_LotValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOngoingLotsReply_LotValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOngoingLotsReply_LotValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOngoingLotsReply_LotValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOngoingLotsReply_LotValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOngoingLotsReply_LotValidationError) ErrorName() string {
	return "GetOngoingLotsReply_LotValidationError"
}

// Error satisfies the builtin error interface
func (e GetOngoingLotsReply_LotValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOngoingLotsReply_Lot.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOngoingLotsReply_LotValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOngoingLotsReply_LotValidationError{}

var _GetOngoingLotsReply_Lot_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on GetOngoingLotsReply_Phase_Type with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOngoingLotsReply_Phase_Type) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOngoingLotsReply_Phase_Type with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetOngoingLotsReply_Phase_TypeMultiError, or nil if none found.
func (m *GetOngoingLotsReply_Phase_Type) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOngoingLotsReply_Phase_Type) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetOngoingLotsReply_Phase_TypeMultiError(errors)
	}

	return nil
}

// GetOngoingLotsReply_Phase_TypeMultiError is an error wrapping multiple
// validation errors returned by GetOngoingLotsReply_Phase_Type.ValidateAll()
// if the designated constraints aren't met.
type GetOngoingLotsReply_Phase_TypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOngoingLotsReply_Phase_TypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOngoingLotsReply_Phase_TypeMultiError) AllErrors() []error { return m }

// GetOngoingLotsReply_Phase_TypeValidationError is the validation error
// returned by GetOngoingLotsReply_Phase_Type.Validate if the designated
// constraints aren't met.
type GetOngoingLotsReply_Phase_TypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOngoingLotsReply_Phase_TypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOngoingLotsReply_Phase_TypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOngoingLotsReply_Phase_TypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOngoingLotsReply_Phase_TypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOngoingLotsReply_Phase_TypeValidationError) ErrorName() string {
	return "GetOngoingLotsReply_Phase_TypeValidationError"
}

// Error satisfies the builtin error interface
func (e GetOngoingLotsReply_Phase_TypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOngoingLotsReply_Phase_Type.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOngoingLotsReply_Phase_TypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOngoingLotsReply_Phase_TypeValidationError{}
