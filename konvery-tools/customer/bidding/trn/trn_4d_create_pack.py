import os
import shutil
import sys
import numpy as np
sys.path.append('.')
from customer.common import tools
from pathlib import Path


input_dir = '/Users/<USER>/Downloads/trn/TRN项目数据'
output_dir = '/Users/<USER>/Downloads/trn/TRN项目数据_out'
params_file = os.path.join(input_dir, 'res_train.json')
data = tools.get_json_data(params_file)
ext_params_file = os.path.join(input_dir, '3dannotation_data', 'submaplist.json')
ext_data = tools.get_json_data(ext_params_file)

id2viewpoint = {str(ext_submap['id']): [ext_submap["point_X"], ext_submap["point_y"], ext_submap["point_z"]]
                for ext_submap in ext_data['submaplist']}

tools.check_dir(output_dir)
frames = []

for submap in data['submaplist']:
    cameras = {}
    lidar_parts = Path(submap['lidar_path']).parts
    if not os.path.exists(os.path.join(input_dir, *lidar_parts[:3])):
        continue
    ele_out_dir = os.path.join(output_dir, *lidar_parts[:3])
    frames.append(ele_out_dir)
    os.makedirs(ele_out_dir)
    lidar2ego_pose = submap['lidar2ego_translation'] + submap['lidar2ego_rotation']
    lidar2ego_mat = tools.pose_to_mat(lidar2ego_pose)
    for cam_name, cam_params in submap['cams'].items():
        cur2center_mat = np.array(cam_params['curbase2centerbase_pose']).reshape(4, -1)
        cam_extrinsic = np.array(cam_params['cam_extrinsic'] + [[0, 0, 0, 1]])
        temp_mat = np.eye(4)
        temp_mat[1, 1] = -1
        temp_mat[2, 2] = -1
        extrinsic = cam_extrinsic @ temp_mat @ cur2center_mat @ np.linalg.inv(lidar2ego_mat)
        if cam_params['cam_distortion_type'] == 'fisheye':
            distortion = [2] + cam_params['cam_distortion'][:2] + cam_params['cam_distortion'][4:6]
        else:
            distortion = [0] + cam_params['cam_distortion'] + [0, 0]
        cameras[cam_name] = {
            "extrinsic": extrinsic.flatten().tolist(),
            "intrinsic": np.array(cam_params['cam_intrinsic']).flatten().tolist(),
            "distortion": distortion
        }
        src_img_path = os.path.join(input_dir, cam_params['img_path'])
        img_parts = Path(cam_params['img_path']).parts
        dst_img_path = os.path.join(ele_out_dir, f'{img_parts[3]}.jpg')
        tools.copy_file(src_img_path, dst_img_path)
    lidar = {
        "viewpoint": lidar2ego_pose[:3] + [0, 0, 0, 1],
        "pose": lidar2ego_pose
    }
    config = {
        "meta": {
            "version": "v2"
        },
        "lidar": lidar,
        "cameras": cameras
    }
    tools.write_json_file(config, os.path.join(ele_out_dir, 'params.json'))

first_frame = sorted(frames)[0]
pcd_file = tools.get_file_by_extension(os.path.join(input_dir, '3dannotation_data'), '.pcd')[0]
shutil.copyfile(pcd_file, os.path.join(first_frame, 'lidar.pcd'))
