title = "TOML Marshal Testing"

[basic_lists]
  floats = [12.3,45.6,78.9]
  bools = [true,false,true]
  dates = [1979-05-27T07:32:00Z,1980-05-27T07:32:00Z]
  ints = [8001,8001,8002]
  uints = [5002,5003]
  strings = ["One","Two","Three"]

[[subdocptrs]]
  name = "Second"

[basic_map]
  one = "one"
  two = "two"

[subdoc]

  [subdoc.second]
    name = "Second"

  [subdoc.first]
    name = "First"

[basic]
  uint = 5001
  bool = true
  float = 123.4
  float64 = 123.456782132399
  int = 5000
  string = "Bite me"
  date = 1979-05-27T07:32:00Z

[[subdoclist]]
  name = "List.First"

[[subdoclist]]
  name = "List.Second"
