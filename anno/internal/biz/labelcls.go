// source: anno/v1/labelcls.proto
package biz

import (
	"context"
	"time"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type Labelcls struct {
	ID        int64     `json:"id" gorm:"default:null"`
	Name      string    `json:"name" gorm:"default:null"`
	Langs     StrMap    `json:"langs" gorm:"default:null"`
	CreatedAt time.Time `json:"created_at" gorm:"default:null"`
}

func (Labelcls) TableName() string { return "labelcls" }
func (o *Labelcls) GetID() int64   { return o.ID }
func (o *Labelcls) EnsureID() {
	if o.ID == 0 {
		o.ID = kid.NewID()
	}
}
func (o *Labelcls) GetUid() string { return kid.StringID(o.ID) }
func (o *Labelcls) UidFld() string { return LabelclsSfldName }

const LabelclsTableName = "labelcls"

var (
	Labelcls_             = field.RegObject(&Labelcls{})
	LabelclsUpdatableFlds = field.NewModel(LabelclsTableName, Labelcls_, "langs")

	LabelclsSfldName  = field.Sname(&Labelcls_.Name)
	LabelclsSfldLangs = field.Sname(&Labelcls_.Langs)
)

type LabelclsListFilter struct {
	Uids        []string
	NamePattern string
}

type LabelclzRepo interface {
	Create(context.Context, *Labelcls) (*Labelcls, error)
	Update(context.Context, *Labelcls, *FieldMask) (*Labelcls, error)
	GetByID(context.Context, int64) (*Labelcls, error)
	GetByUid(context.Context, string) (*Labelcls, error)
	DeleteByID(context.Context, int64) error
	DeleteByUid(context.Context, string) error
	List(context.Context, *LabelclsListFilter, Pager) ([]*Labelcls, error)
	Count(context.Context, *LabelclsListFilter) (int, error)
}

type LabelclzBiz struct {
	repo LabelclzRepo
	log  *log.Helper
}

func NewLabelclzBiz(repo LabelclzRepo, logger log.Logger) *LabelclzBiz {
	return &LabelclzBiz{repo: repo, log: log.NewHelper(logger)}
}

func (o *LabelclzBiz) Create(ctx context.Context, p *Labelcls) (*Labelcls, error) {
	o.log.Info(ctx, "CreateLabelcls", "param", p)
	return o.repo.Create(ctx, p)
}

func (o *LabelclzBiz) Update(ctx context.Context, p *Labelcls, fldMask *FieldMask) (*Labelcls, error) {
	o.log.Info(ctx, "UpdateLabelcls", "param", p)
	return o.repo.Update(ctx, p, fldMask)
}

func (o *LabelclzBiz) GetByID(ctx context.Context, id int64) (*Labelcls, error) {
	return o.repo.GetByID(ctx, id)
}

func (o *LabelclzBiz) GetByUid(ctx context.Context, uid string) (*Labelcls, error) {
	return o.repo.GetByUid(ctx, uid)
}

func (o *LabelclzBiz) DeleteByID(ctx context.Context, id int64) error {
	o.log.Info(ctx, "DeleteByIDLabelcls", "id", id)
	return o.repo.DeleteByID(ctx, id)
}

func (o *LabelclzBiz) DeleteByUid(ctx context.Context, uid string) error {
	o.log.Info(ctx, "DeleteByUidLabelcls", "uid", uid)
	return o.repo.DeleteByUid(ctx, uid)
}

func (o *LabelclzBiz) List(ctx context.Context, filter *LabelclsListFilter, pager Pager) ([]*Labelcls, error) {
	o.log.Info(ctx, "ListLabelcls", "param", filter)
	return o.repo.List(ctx, filter, pager)
}

func (o *LabelclzBiz) Count(ctx context.Context, filter *LabelclsListFilter) (int, error) {
	return o.repo.Count(ctx, filter)
}
