package iam

import (
	"testing"

	"gitlab.rp.konvery.work/platform/apis/iam/v1"
)

// ------ Bizgrant Test ------

func Test_CreateBizgrantRequest(t *testing.T) {
	req := iam.CreateBizgrantRequest{
		GranteeUid: "grantee1234",
		OrgUid:     "org12345678",
		Bizz:       []iam.BizType_Enum{iam.BizType_anno},
	}

	cases := []struct {
		name       string
		newReqFunc func(iam.CreateBizgrantRequest) iam.CreateBizgrantRequest
		expectErr  bool
	}{
		{
			name:       "happy_flow",
			newReqFunc: nil,
			expectErr:  false,
		},
		{
			name: "GranteeUid_is_missing",
			newReqFunc: func(req iam.CreateBizgrantRequest) iam.CreateBizgrantRequest {
				req.GranteeUid = ""
				return req
			},
			expectErr: true,
		},
		{
			name: "OrgUid_is_missing",
			newReqFunc: func(req iam.CreateBizgrantRequest) iam.CreateBizgrantRequest {
				req.OrgUid = ""
				return req
			},
			expectErr: true,
		},
		{
			name: "Bizz_has_invalid_value",
			newReqFunc: func(req iam.CreateBizgrantRequest) iam.CreateBizgrantRequest {
				req.Bizz = []iam.BizType_Enum{iam.BizType_unspecified}
				return req
			},
			expectErr: true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			newReq := req
			if c.newReqFunc != nil {
				newReq = c.newReqFunc(req)
			}
			if err := newReq.Validate(); (err != nil) != c.expectErr {
				t.Errorf("Validate() expect error: %v, got: %v", c.expectErr, err)
			}
			if err := newReq.ValidateAll(); (err != nil) != c.expectErr {
				t.Errorf("ValidateAll() expect error: %v, got: %v", c.expectErr, err)
			}
		})
	}
}

// ------ Perms Test ------

func Test_EditPermsRequest(t *testing.T) {
	req := iam.EditPermsRequest{
		Action: iam.EditAction_add,
		Perms:  []string{"add.policy"},
	}

	cases := []struct {
		name       string
		newReqFunc func(iam.EditPermsRequest) iam.EditPermsRequest
		expectErr  bool
	}{
		{
			"happy_flow",
			nil,
			false,
		},
		{
			"Action_is_unspecified",
			func(req iam.EditPermsRequest) iam.EditPermsRequest {
				req.Action = iam.EditAction_unspecified
				return req
			},
			true,
		},
		{
			"Perms_has_invalid_value",
			func(req iam.EditPermsRequest) iam.EditPermsRequest {
				req.Perms = []string{"invalid", "invalid2"}
				return req
			},
			true,
		},
		{
			"Perms_has_very_big_value",
			func(req iam.EditPermsRequest) iam.EditPermsRequest {
				req.Perms = []string{"add.abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyz1234567890"}
				return req
			},
			true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			newReq := req
			if c.newReqFunc != nil {
				newReq = c.newReqFunc(req)
			}
			if err := newReq.Validate(); (err != nil) != c.expectErr {
				t.Errorf("Validate() expect error: %v, got: %v", c.expectErr, err)
			}
			if err := newReq.ValidateAll(); (err != nil) != c.expectErr {
				t.Errorf("ValidateAll() expect error: %v, got: %v", c.expectErr, err)
			}
		})
	}
}

// ------ Policy Test ------

func Test_CreatePolicyRequest(t *testing.T) {
	req := iam.CreatePolicyRequest{
		Role:     "role1234",
		Users:    []string{"IamUser:viewer", "IamGroup:sys"},
		Resource: "AnnoLot:get",
	}

	cases := []struct {
		name       string
		newReqFunc func(iam.CreatePolicyRequest) iam.CreatePolicyRequest
		expectErr  bool
	}{
		{
			"happy_flow",
			nil,
			false,
		},
		{
			"Users_has_invalid_values",
			func(req iam.CreatePolicyRequest) iam.CreatePolicyRequest {
				req.Users = []string{"IamUser2:viewer", "IamGroup2:sys"}
				return req
			},
			true,
		},
		{
			"Resources_has_invalid_values",
			func(req iam.CreatePolicyRequest) iam.CreatePolicyRequest {
				req.Resource = "AnnoLot"
				return req
			},
			true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			newReq := req
			if c.newReqFunc != nil {
				newReq = c.newReqFunc(req)
			}
			if err := newReq.Validate(); (err != nil) != c.expectErr {
				t.Errorf("Validate() expect error: %v, got: %v", c.expectErr, err)
			}
			if err := newReq.ValidateAll(); (err != nil) != c.expectErr {
				t.Errorf("ValidateAll() expect error: %v, got: %v", c.expectErr, err)
			}
		})
	}
}

// ------ Role Test ------

func Test_CreateRole(t *testing.T) {
	req := iam.Role{
		Name:        "name1234",
		DisplayName: "displayName1234",
		Perms:       []string{"IamRole.viewer"},
	}

	cases := []struct {
		name       string
		newReqFunc func(iam.Role) iam.Role
		expectErr  bool
	}{
		{
			"happy_flow",
			nil,
			false,
		},
		{
			"Name_violates_regex",
			func(req iam.Role) iam.Role {
				req.Name = "name."
				return req
			},
			true,
		},
		{
			"DisplayName_is_too_short",
			func(req iam.Role) iam.Role {
				req.DisplayName = "ab"
				return req
			},
			true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			newReq := req
			if c.newReqFunc != nil {
				newReq = c.newReqFunc(req)
			}
			if err := newReq.Validate(); (err != nil) != c.expectErr {
				t.Errorf("Validate() expect error: %v, got: %v", c.expectErr, err)
			}
			if err := newReq.ValidateAll(); (err != nil) != c.expectErr {
				t.Errorf("ValidateAll() expect error: %v, got: %v", c.expectErr, err)
			}
		})
	}
}

// ------ Team Test ------

func Test_CreateTeamRequest(t *testing.T) {
	req := iam.CreateTeamRequest{
		Name:      "name1234",
		Desc:      "desc1234",
		Avatar:    "",
		Province:  "Beijing",
		City:      "Beijing",
		ParentUid: "",
		Type:      iam.Team_Type_demander,
		Owner:     "uid:abcdef",
	}

	cases := []struct {
		name       string
		newReqFunc func(iam.CreateTeamRequest) iam.CreateTeamRequest
		expectErr  bool
	}{
		{
			"happy_flow",
			nil,
			false,
		},
		{
			"Owner_is_phone_number",
			func(req iam.CreateTeamRequest) iam.CreateTeamRequest {
				req.Owner = "phone:+8618801020304"
				return req
			},
			false,
		},
		{
			"Owner_is_wrong_phone_number",
			func(req iam.CreateTeamRequest) iam.CreateTeamRequest {
				req.Owner = "phone:+"
				return req
			},
			true,
		},
		{
			"Owner_is_email",
			func(req iam.CreateTeamRequest) iam.CreateTeamRequest {
				req.Owner = "email:<EMAIL>"
				return req
			},
			false,
		},
		{
			"Owner_is_wrong_email",
			func(req iam.CreateTeamRequest) iam.CreateTeamRequest {
				req.Owner = "email:abc@163"
				return req
			},
			true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			newReq := req
			if c.newReqFunc != nil {
				newReq = c.newReqFunc(req)
			}
			if err := newReq.Validate(); (err != nil) != c.expectErr {
				t.Errorf("Validate() expect error: %v, got: %v", c.expectErr, err)
			}
			if err := newReq.ValidateAll(); (err != nil) != c.expectErr {
				t.Errorf("ValidateAll() expect error: %v, got: %v", c.expectErr, err)
			}
		})
	}
}

// ------ User Test ------

func Test_ListUserRequest(t *testing.T) {
	req := iam.ListUserRequest{
		Page:        10,
		Pagesz:      0,
		NamePattern: "bd*",
		Uids:        []string{"uid12345678", "uid23456789"},
		Phones:      []string{"+8618801020304", "+8618801020305"},
		Emails:      []string{"<EMAIL>", "<EMAIL>"},
	}

	cases := []struct {
		name       string
		newReqFunc func(iam.ListUserRequest) iam.ListUserRequest
		expectErr  bool
	}{
		{
			"happy_flow",
			nil,
			false,
		},
		{
			"Phones_has_wrong_values",
			func(req iam.ListUserRequest) iam.ListUserRequest {
				req.Phones = []string{"+188wrong"}
				return req
			},
			true,
		},
		{
			"Emails_has_wrong_values",
			func(req iam.ListUserRequest) iam.ListUserRequest {
				req.Emails = []string{"abc@163"}
				return req
			},
			true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			newReq := req
			if c.newReqFunc != nil {
				newReq = c.newReqFunc(req)
			}
			if err := newReq.Validate(); (err != nil) != c.expectErr {
				t.Errorf("Validate() expect error: %v, got: %v", c.expectErr, err)
			}
			if err := newReq.ValidateAll(); (err != nil) != c.expectErr {
				t.Errorf("ValidateAll() expect error: %v, got: %v", c.expectErr, err)
			}
		})
	}
}
