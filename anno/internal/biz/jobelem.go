package biz

import (
	"time"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
)

type Jobelem struct {
	ID        int64     `json:"id"`
	JobID     int64     `json:"job_id" gorm:"default:null"`
	LotID     int64     `json:"lot_id" gorm:"default:null"`
	DataUid   string    `json:"data_uid" gorm:"default:null"`
	ElemIdx   int32     `json:"elem_idx" gorm:"default:null"`
	ElemName  string    `json:"elem_name" gorm:"default:null"`
	CreatedAt time.Time `json:"created_at" gorm:"default:null"`
}

func (Jobelem) TableName() string { return JobelemTableName }
func (o *Jobelem) GetID() int64 {
	return o.ID
}

const JobelemTableName = "jobelems"

var (
	_jobelem = field.RegObject(&Jobelem{})

	Jobelem_JobID    = repo.NewTableFieldT[Jobelem](&_jobelem.JobID)
	Jobelem_LotID    = repo.NewTableFieldT[Jobelem](&_jobelem.LotID)
	Jobelem_DataUid  = repo.NewTableFieldT[Jobelem](&_jobelem.DataUid)
	Jobelem_ElemIdx  = repo.NewTableFieldT[Jobelem](&_jobelem.ElemIdx)
	Jobelem_ElemName = repo.NewTableFieldT[Jobelem](&_jobelem.ElemName)
)

type JobelemFilter struct {
	LotID           int64
	JobID           int64
	JobIds          []int64
	ElemIdx         int32
	DataUid         string
	ElemNamePattern string
}

func (o *JobelemFilter) Apply(tx Tx) Tx {
	tx = repo.ApplyFieldFilter(tx, Jobelem_JobID.Full(), o.JobID)
	tx = repo.ApplyFieldFilter(tx, Jobelem_JobID.Full(), o.JobIds)
	tx = repo.ApplyFieldFilter(tx, Jobelem_LotID.Full(), o.LotID)
	tx = repo.ApplyFieldFilter(tx, Jobelem_DataUid.Full(), o.DataUid)
	tx = repo.ApplyFieldFilter(tx, Jobelem_ElemIdx.Full(), o.ElemIdx)
	tx = repo.ApplyPatternFilter(tx, Jobelem_ElemName.Full(), o.ElemNamePattern)
	return tx
}

type JobelemRepo interface {
	repo.GenericRepo[Jobelem]
}
