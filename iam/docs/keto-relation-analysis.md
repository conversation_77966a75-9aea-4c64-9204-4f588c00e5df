# Keto 权限关系设计解析

本文档详细解析了项目中基于 Keto 实现的权限关系设计。

## 一、基础语法回顾

Keto 关系元组的基本格式为：
```
Namespace:Object#Relation@Subject
```

其中：
- `Namespace`: 命名空间，表示对象类型
- `Object`: 具体对象的标识符
- `Relation`: 关系类型
- `Subject`: 主体（可以是具体ID或另一个关系集合）

## 二、核心关系模板解析

### 1. 用户关系模板 (userTpl)

```
# user owners policy
IamPolicy:IamUser.<user-name>.owner#roles@IamRole:owner
IamPolicy:IamUser.<user-name>.owner#users@IamUser:<user-name>
IamUser:<user-name>#owners@IamUser:<user-name>
IamUser:<user-name>#policies@IamPolicy:sys/admin
IamGroup:sys/allUsers#members@IamUser:<user-name>
```

解读：
- 用户基础权限设置：
  1. `IamPolicy:IamUser.<user-name>.owner#roles@IamRole:owner`
     - 含义：用户的owner策略包含owner角色
  2. `IamPolicy:IamUser.<user-name>.owner#users@IamUser:<user-name>`
     - 含义：用户自己可以访问自己的owner策略
  3. `IamUser:<user-name>#owners@IamUser:<user-name>`
     - 含义：用户是自己资源的所有者（快速访问路径）
  4. `IamUser:<user-name>#policies@IamPolicy:sys/admin`
     - 含义：用户可以访问系统管理策略
  5. `IamGroup:sys/allUsers#members@IamUser:<user-name>`
     - 含义：用户自动成为系统全局用户组的成员

### 2. 团队关系模板 (groupTpl)

```
# group owners policy
IamPolicy:IamGroup.<group-name>.owner#roles@IamRole:owner
IamPolicy:IamGroup.<group-name>.owner#users@IamGroup:<group-name>.owner#members
IamGroup:<group-name>#policies@IamPolicy:IamGroup.<group-name>.owner
IamGroup:<group-name>#policies@IamPolicy:sys/admin
```

解读：
- 团队权限结构：
  1. `IamPolicy:IamGroup.<group-name>.owner#roles@IamRole:owner`
     - 含义：团队的owner策略包含owner角色
  2. `IamPolicy:IamGroup.<group-name>.owner#users@IamGroup:<group-name>.owner#members`
     - 含义：团队owner成员列表中的用户可以访问团队的owner策略
  3. `IamGroup:<group-name>#policies@IamPolicy:IamGroup.<group-name>.owner`
     - 含义：团队受其owner策略管控
  4. `IamGroup:<group-name>#policies@IamPolicy:sys/admin`
     - 含义：团队可以访问系统管理策略

### 3. 资源关系模板 (resourceTpl)

```
# resource owners policy
IamPolicy:<namespace>.<resource-name>.owner#roles@IamRole:<namespace>.owner
<namespace>:<resource-name>#policies@IamPolicy:<namespace>.<resource-name>.owner
<namespace>:<resource-name>#policies@IamPolicy:sys/admin
```

解读：
- 资源权限管理：
  1. `IamPolicy:<namespace>.<resource-name>.owner#roles@IamRole:<namespace>.owner`
     - 含义：资源的owner策略包含该命名空间的owner角色
  2. `<namespace>:<resource-name>#policies@IamPolicy:<namespace>.<resource-name>.owner`
     - 含义：资源受其owner策略管控
  3. `<namespace>:<resource-name>#policies@IamPolicy:sys/admin`
     - 含义：资源可以被系统管理策略访问

### 4. 组织顶级关系模板 (orgTpl)

```
# make top-team managers can create new things
IamRole:IamGroup.<group-name>#parents@IamGroup:<group-name>
IamUser:IamGroup.<group-name>#parents@IamGroup:<group-name>
IamGroup:IamGroup.<group-name>#parents@IamGroup:<group-name>
```

解读：
- 顶级团队特权：
  1. `IamRole:IamGroup.<group-name>#parents@IamGroup:<group-name>`
     - 含义：顶级团队可以创建和管理角色
  2. `IamUser:IamGroup.<group-name>#parents@IamGroup:<group-name>`
     - 含义：顶级团队可以创建和管理用户
  3. `IamGroup:IamGroup.<group-name>#parents@IamGroup:<group-name>`
     - 含义：顶级团队可以创建和管理子团队

## 三、整体权限架构

### 1. 基础层
- 所有用户自动加入系统全局用户组
- 用户对自己的资源有完全控制权

### 2. 团队层
- 团队拥有自己的owner策略和成员管理
- 团队可以访问系统管理策略
- 顶级团队具有创建角色、用户和子团队的特权

### 3. 资源层
- 资源通过owner策略和系统策略进行管控
- 支持资源所有权的灵活分配

### 4. 策略层
- 使用IamPolicy命名空间统一管理各类权限策略
- 通过角色(IamRole)实现权限的分组和复用

## 四、设计特点

1. 分层管理
   - 清晰的层级结构
   - 权限继承和传递机制
   - 灵活的资源访问控制

2. 安全性
   - 细粒度的权限控制
   - 基于角色的访问控制
   - 系统级别的权限保护

3. 可扩展性
   - 支持自定义命名空间
   - 灵活的策略组合
   - 可扩展的关系定义

4. 实用性
   - 直观的权限管理
   - 高效的权限检查
   - 统一的权限模型