version: "3.9"

services:
  foo:

    build:
      context: ./dir
      dockerfile: Dockerfile
      args:
        foo: bar
      target: foo
      network: foo
      cache_from:
        - foo
        - bar
      extra_hosts:
        - "ipv4.example.com:127.0.0.1"
        - "ipv6.example.com:::1"
      labels: [FOO=BAR]


    cap_add:
      - ALL

    cap_drop:
      - NET_ADMIN
      - SYS_ADMIN

    cgroup_parent: m-executor-abcd

    # String or list
    command: bundle exec thin -p 3000
    # command: ["bundle", "exec", "thin", "-p", "3000"]

    configs:
      - config1
      - source: config2
        target: /my_config
        uid: '103'
        gid: '103'
        mode: 0440

    container_name: my-web-container

    depends_on:
      - db
      - redis

    deploy:
      mode: replicated
      replicas: 6
      labels: [FOO=BAR]
      rollback_config:
        parallelism: 3
        delay: 10s
        failure_action: continue
        monitor: 60s
        max_failure_ratio: 0.3
        order: start-first
      update_config:
        parallelism: 3
        delay: 10s
        failure_action: continue
        monitor: 60s
        max_failure_ratio: 0.3
        order: start-first
      resources:
        limits:
          cpus: '0.001'
          memory: 50M
          pids: 100
        reservations:
          cpus: '0.0001'
          memory: 20M
          generic_resources:
            - discrete_resource_spec:
                kind: 'gpu'
                value: 2
            - discrete_resource_spec:
                kind: 'ssd'
                value: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      placement:
        constraints: [node=foo]
        max_replicas_per_node: 5
        preferences:
          - spread: node.labels.az
      endpoint_mode: dnsrr

    devices:
      - "/dev/ttyUSB0:/dev/ttyUSB0"

    # String or list
    # dns: 8.8.8.8
    dns:
      - 8.8.8.8
      - 9.9.9.9

    # String or list
    # dns_search: example.com
    dns_search:
      - dc1.example.com
      - dc2.example.com

    domainname: foo.com

    # String or list
    # entrypoint: /code/entrypoint.sh -p 3000
    entrypoint: ["/code/entrypoint.sh", "-p", "3000"]

    # String or list
    # env_file: .env
    env_file:
      - ./example1.env
      - ./example2.env

    # Mapping or list
    # Mapping values can be strings, numbers or null
    # Booleans are not allowed - must be quoted
    environment:
      BAZ: baz_from_service_def
      QUX:
    # environment:
    #   - RACK_ENV=development
    #   - SHOW=true
    #   - SESSION_SECRET

    # Items can be strings or numbers
    expose:
     - "3000"
     - 8000

    external_links:
      - redis_1
      - project_db_1:mysql
      - project_db_1:postgresql

    # Mapping or list
    # Mapping values must be strings
    # extra_hosts:
    #   somehost: "**************"
    #   otherhost: "*************"
    #   host.docker.internal: "host-gateway"
    extra_hosts:
      - "somehost:**************"
      - "otherhost:*************"
      - "host.docker.internal:host-gateway"

    hostname: foo

    healthcheck:
      test: echo "hello world"
      interval: 10s
      timeout: 1s
      retries: 5
      start_period: 15s

    # Any valid image reference - repo, tag, id, sha
    image: redis
    # image: ubuntu:14.04
    # image: tutum/influxdb
    # image: example-registry.com:4000/postgresql
    # image: a4bc65fd
    # image: busybox@sha256:38a203e1986cf79639cfb9b2e1d6e773de84002feea2d4eb006b52004ee8502d

    ipc: host

    # Mapping or list
    # Mapping values can be strings, numbers or null
    labels:
      com.example.description: "Accounting webapp"
      com.example.number: 42
      com.example.empty-label:
    # labels:
    #   - "com.example.description=Accounting webapp"
    #   - "com.example.number=42"
    #   - "com.example.empty-label"

    links:
     - db
     - db:database
     - redis

    logging:
      driver: syslog
      options:
        syslog-address: "tcp://************:123"

    mac_address: 02:42:ac:11:65:43

    # network_mode: "bridge"
    # network_mode: "host"
    # network_mode: "none"
    # Use the network mode of an arbitrary container from another service
    # network_mode: "service:db"
    # Use the network mode of another container, specified by name or id
    # network_mode: "container:some-container"
    network_mode: "container:0cfeab0f748b9a743dc3da582046357c6ef497631c1a016d28d2bf9b4f899f7b"

    networks:
      some-network:
        aliases:
         - alias1
         - alias3
      other-network:
        ipv4_address: ************0
        ipv6_address: 2001:3984:3989::10
      other-other-network:

    pid: "host"

    ports:
      - 3000
      - "3001-3005"
      - "8000:8000"
      - "9090-9091:8080-8081"
      - "49100:22"
      - "127.0.0.1:8001:8001"
      - "127.0.0.1:5000-5010:5000-5010"

    privileged: true

    read_only: true

    restart: always

    secrets:
      - secret1
      - source: secret2
        target: my_secret
        uid: '103'
        gid: '103'
        mode: 0440

    security_opt:
      - label=level:s0:c100,c200
      - label=type:svirt_apache_t

    stdin_open: true

    stop_grace_period: 20s

    stop_signal: SIGUSR1

    sysctls:
      net.core.somaxconn: 1024
      net.ipv4.tcp_syncookies: 0

    # String or list
    # tmpfs: /run
    tmpfs:
      - /run
      - /tmp

    tty: true

    ulimits:
      # Single number or mapping with soft + hard limits
      nproc: 65535
      nofile:
        soft: 20000
        hard: 40000

    user: someone

    volumes:
      # Just specify a path and let the Engine create a volume
      - /var/lib/mysql
      # Specify an absolute path mapping
      - /opt/data:/var/lib/mysql
      # Path on the host, relative to the Compose file
      - .:/code
      - ./static:/var/www/html
      # User-relative path
      - ~/configs:/etc/configs/:ro
      # Named volume
      - datavolume:/var/lib/mysql
      - type: bind
        source: ./opt
        target: /opt
        consistency: cached
      - type: tmpfs
        target: /opt
        tmpfs:
          size: 10000

    working_dir: /code
    x-bar: baz
    x-foo: bar

networks:
  # Entries can be null, which specifies simply that a network
  # called "{project name}_some-network" should be created and
  # use the default driver
  some-network:

  other-network:
    driver: overlay

    driver_opts:
      # Values can be strings or numbers
      foo: "bar"
      baz: 1

    ipam:
      driver: overlay
      # driver_opts:
      #   # Values can be strings or numbers
      #   com.docker.network.enable_ipv6: "true"
      #   com.docker.network.numeric_value: 1
      config:
      - subnet: ************/24
        # gateway: ************
      - subnet: 2001:3984:3989::/64
        # gateway: 2001:3984:3989::1

    labels:
      foo: bar

  external-network:
    # Specifies that a pre-existing network called "external-network"
    # can be referred to within this file as "external-network"
    external: true

  other-external-network:
    # Specifies that a pre-existing network called "my-cool-network"
    # can be referred to within this file as "other-external-network"
    external:
      name: my-cool-network
    x-bar: baz
    x-foo: bar

volumes:
  # Entries can be null, which specifies simply that a volume
  # called "{project name}_some-volume" should be created and
  # use the default driver
  some-volume:

  other-volume:
    driver: flocker

    driver_opts:
      # Values can be strings or numbers
      foo: "bar"
      baz: 1
    labels:
      foo: bar

  another-volume:
    name: "user_specified_name"
    driver: vsphere

    driver_opts:
      # Values can be strings or numbers
      foo: "bar"
      baz: 1

  external-volume:
    # Specifies that a pre-existing volume called "external-volume"
    # can be referred to within this file as "external-volume"
    external: true

  other-external-volume:
    # Specifies that a pre-existing volume called "my-cool-volume"
    # can be referred to within this file as "other-external-volume"
    # This example uses the deprecated "volume.external.name" (replaced by "volume.name")
    external:
      name: my-cool-volume

  external-volume3:
    # Specifies that a pre-existing volume called "this-is-volume3"
    # can be referred to within this file as "external-volume3"
    name: this-is-volume3
    external: true
    x-bar: baz
    x-foo: bar

configs:
  config1:
    file: ./config_data
    labels:
      foo: bar
  config2:
    external:
      name: my_config
  config3:
    external: true
  config4:
    name: foo
    x-bar: baz
    x-foo: bar

secrets:
  secret1:
    file: ./secret_data
    labels:
      foo: bar
  secret2:
    external:
      name: my_secret
  secret3:
    external: true
  secret4:
    name: bar
    x-bar: baz
    x-foo: bar
x-bar: baz
x-foo: bar
x-nested:
  bar: baz
  foo: bar
