// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: model/v1/run.proto

package model

import (
	context "context"
	types "gitlab.rp.konvery.work/platform/apis/types"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Runs_Create_FullMethodName    = "/model.v1.Runs/Create"
	Runs_Update_FullMethodName    = "/model.v1.Runs/Update"
	Runs_Start_FullMethodName     = "/model.v1.Runs/Start"
	Runs_Finish_FullMethodName    = "/model.v1.Runs/Finish"
	Runs_Cancel_FullMethodName    = "/model.v1.Runs/Cancel"
	Runs_Delete_FullMethodName    = "/model.v1.Runs/Delete"
	Runs_Get_FullMethodName       = "/model.v1.Runs/Get"
	Runs_List_FullMethodName      = "/model.v1.Runs/List"
	Runs_AddTag_FullMethodName    = "/model.v1.Runs/AddTag"
	Runs_DeleteTag_FullMethodName = "/model.v1.Runs/DeleteTag"
)

// RunsClient is the client API for Runs service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RunsClient interface {
	Create(ctx context.Context, in *CreateRunRequest, opts ...grpc.CallOption) (*Run, error)
	Update(ctx context.Context, in *UpdateRunRequest, opts ...grpc.CallOption) (*Run, error)
	Start(ctx context.Context, in *StartRunRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	Finish(ctx context.Context, in *FinishRunRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	Cancel(ctx context.Context, in *GetRunRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	Delete(ctx context.Context, in *DeleteRunRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	Get(ctx context.Context, in *GetRunRequest, opts ...grpc.CallOption) (*Run, error)
	List(ctx context.Context, in *ListRunRequest, opts ...grpc.CallOption) (*ListRunReply, error)
	AddTag(ctx context.Context, in *types.TagRequest, opts ...grpc.CallOption) (*types.TagList, error)
	DeleteTag(ctx context.Context, in *types.TagRequest, opts ...grpc.CallOption) (*types.TagList, error)
}

type runsClient struct {
	cc grpc.ClientConnInterface
}

func NewRunsClient(cc grpc.ClientConnInterface) RunsClient {
	return &runsClient{cc}
}

func (c *runsClient) Create(ctx context.Context, in *CreateRunRequest, opts ...grpc.CallOption) (*Run, error) {
	out := new(Run)
	err := c.cc.Invoke(ctx, Runs_Create_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runsClient) Update(ctx context.Context, in *UpdateRunRequest, opts ...grpc.CallOption) (*Run, error) {
	out := new(Run)
	err := c.cc.Invoke(ctx, Runs_Update_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runsClient) Start(ctx context.Context, in *StartRunRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Runs_Start_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runsClient) Finish(ctx context.Context, in *FinishRunRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Runs_Finish_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runsClient) Cancel(ctx context.Context, in *GetRunRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Runs_Cancel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runsClient) Delete(ctx context.Context, in *DeleteRunRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Runs_Delete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runsClient) Get(ctx context.Context, in *GetRunRequest, opts ...grpc.CallOption) (*Run, error) {
	out := new(Run)
	err := c.cc.Invoke(ctx, Runs_Get_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runsClient) List(ctx context.Context, in *ListRunRequest, opts ...grpc.CallOption) (*ListRunReply, error) {
	out := new(ListRunReply)
	err := c.cc.Invoke(ctx, Runs_List_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runsClient) AddTag(ctx context.Context, in *types.TagRequest, opts ...grpc.CallOption) (*types.TagList, error) {
	out := new(types.TagList)
	err := c.cc.Invoke(ctx, Runs_AddTag_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *runsClient) DeleteTag(ctx context.Context, in *types.TagRequest, opts ...grpc.CallOption) (*types.TagList, error) {
	out := new(types.TagList)
	err := c.cc.Invoke(ctx, Runs_DeleteTag_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RunsServer is the server API for Runs service.
// All implementations must embed UnimplementedRunsServer
// for forward compatibility
type RunsServer interface {
	Create(context.Context, *CreateRunRequest) (*Run, error)
	Update(context.Context, *UpdateRunRequest) (*Run, error)
	Start(context.Context, *StartRunRequest) (*emptypb.Empty, error)
	Finish(context.Context, *FinishRunRequest) (*emptypb.Empty, error)
	Cancel(context.Context, *GetRunRequest) (*emptypb.Empty, error)
	Delete(context.Context, *DeleteRunRequest) (*emptypb.Empty, error)
	Get(context.Context, *GetRunRequest) (*Run, error)
	List(context.Context, *ListRunRequest) (*ListRunReply, error)
	AddTag(context.Context, *types.TagRequest) (*types.TagList, error)
	DeleteTag(context.Context, *types.TagRequest) (*types.TagList, error)
	mustEmbedUnimplementedRunsServer()
}

// UnimplementedRunsServer must be embedded to have forward compatible implementations.
type UnimplementedRunsServer struct {
}

func (UnimplementedRunsServer) Create(context.Context, *CreateRunRequest) (*Run, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedRunsServer) Update(context.Context, *UpdateRunRequest) (*Run, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedRunsServer) Start(context.Context, *StartRunRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Start not implemented")
}
func (UnimplementedRunsServer) Finish(context.Context, *FinishRunRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Finish not implemented")
}
func (UnimplementedRunsServer) Cancel(context.Context, *GetRunRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Cancel not implemented")
}
func (UnimplementedRunsServer) Delete(context.Context, *DeleteRunRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (UnimplementedRunsServer) Get(context.Context, *GetRunRequest) (*Run, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (UnimplementedRunsServer) List(context.Context, *ListRunRequest) (*ListRunReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedRunsServer) AddTag(context.Context, *types.TagRequest) (*types.TagList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTag not implemented")
}
func (UnimplementedRunsServer) DeleteTag(context.Context, *types.TagRequest) (*types.TagList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTag not implemented")
}
func (UnimplementedRunsServer) mustEmbedUnimplementedRunsServer() {}

// UnsafeRunsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RunsServer will
// result in compilation errors.
type UnsafeRunsServer interface {
	mustEmbedUnimplementedRunsServer()
}

func RegisterRunsServer(s grpc.ServiceRegistrar, srv RunsServer) {
	s.RegisterService(&Runs_ServiceDesc, srv)
}

func _Runs_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRunRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunsServer).Create(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Runs_Create_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunsServer).Create(ctx, req.(*CreateRunRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Runs_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRunRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunsServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Runs_Update_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunsServer).Update(ctx, req.(*UpdateRunRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Runs_Start_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartRunRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunsServer).Start(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Runs_Start_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunsServer).Start(ctx, req.(*StartRunRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Runs_Finish_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FinishRunRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunsServer).Finish(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Runs_Finish_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunsServer).Finish(ctx, req.(*FinishRunRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Runs_Cancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRunRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunsServer).Cancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Runs_Cancel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunsServer).Cancel(ctx, req.(*GetRunRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Runs_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRunRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunsServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Runs_Delete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunsServer).Delete(ctx, req.(*DeleteRunRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Runs_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRunRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunsServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Runs_Get_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunsServer).Get(ctx, req.(*GetRunRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Runs_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRunRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunsServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Runs_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunsServer).List(ctx, req.(*ListRunRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Runs_AddTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(types.TagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunsServer).AddTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Runs_AddTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunsServer).AddTag(ctx, req.(*types.TagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Runs_DeleteTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(types.TagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RunsServer).DeleteTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Runs_DeleteTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RunsServer).DeleteTag(ctx, req.(*types.TagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Runs_ServiceDesc is the grpc.ServiceDesc for Runs service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Runs_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "model.v1.Runs",
	HandlerType: (*RunsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Create",
			Handler:    _Runs_Create_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _Runs_Update_Handler,
		},
		{
			MethodName: "Start",
			Handler:    _Runs_Start_Handler,
		},
		{
			MethodName: "Finish",
			Handler:    _Runs_Finish_Handler,
		},
		{
			MethodName: "Cancel",
			Handler:    _Runs_Cancel_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _Runs_Delete_Handler,
		},
		{
			MethodName: "Get",
			Handler:    _Runs_Get_Handler,
		},
		{
			MethodName: "List",
			Handler:    _Runs_List_Handler,
		},
		{
			MethodName: "AddTag",
			Handler:    _Runs_AddTag_Handler,
		},
		{
			MethodName: "DeleteTag",
			Handler:    _Runs_DeleteTag_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "model/v1/run.proto",
}
