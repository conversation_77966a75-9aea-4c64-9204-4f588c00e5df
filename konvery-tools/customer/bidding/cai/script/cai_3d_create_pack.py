import os
import sys
import shutil
import numpy as np
sys.path.append('.')
from PIL import Image
from customer.common import tools

cameras = ['FishEye_Back', 'FishEye_Left', 'FishEye_Front', 'FishEye_Right']


def construct_config():
    config = {
        "camera": {
            "FishEye_Back": "FishEye_Back",
            "FishEye_Front": "FishEye_Front",
            "FishEye_Left": "FishEye_Left",
            "FishEye_Right": "FishEye_Right"
        },
        "data_type": "fusion_pointcloud",
        "sensor_params": {
            "FishEye_Back": {
                "camera_model": "fisheye",
                "extrinsic": np.linalg.inv(np.array([
                    [-0.01009022, 0.25672687, -0.96643132, -0.36806621],
                    [0.99958165, 0.02878784, -0.00278901, 0.0254554],
                    [0.02710546, -0.96605516, -0.25690995, 1.15085914],
                    [0., 0., 0., 1.]
                ])).tolist(),
                "fx": 417.734009,
                "fy": 417.917999,
                "cx": 958.721985,
                "cy": 637.757996,
                "k1": 0.176023,
                "k2": -0.015370,
                "k3": -0.015150,
                "k4": 0.004100,
            },
            "FishEye_Front": {
                "camera_model": "fisheye",
                "extrinsic": np.linalg.inv(np.array([
                    [0.04692615, -0.29457222, 0.95447637, 1.47116978],
                    [-0.99881677, -0.02604978, 0.04106658, 0.0345184],
                    [0.01276682, -0.95527411, - 0.29544609, 1.19473947],
                    [0., 0., 0., 1.]
                ])).tolist(),
                "fx": 417.756012,
                "fy": 418.014008,
                "cx": 957.984985,
                "cy": 642.856018,
                "k1": 0.175831,
                "k2": -0.013463,
                "k3": -0.016808,
                "k4": 0.004500,
            },
            "FishEye_Left": {
                "camera_model": "fisheye",
                "extrinsic": np.linalg.inv(np.array([
                    [0.99863923, -0.0495209, 0.01635141, 0.80967278],
                    [-0.02913574, -0.26974486, 0.96249094, 0.491684],
                    [-0.04325271, -0.96165763, -0.27082063, 1.22583935],
                    [0., 0., 0., 1.]
                ])).tolist(),
                "fx": 417.527008,
                "fy": 417.800995,
                "cx": 960.307007,
                "cy": 636.640991,
                'k1': 0.175364,
                'k2': -0.014604,
                'k3': -0.015375,
                'k4': 0.004100
            },
            "FishEye_Right": {
                "camera_model": "fisheye",
                "extrinsic": np.linalg.inv(np.array([
                    [-0.99864069, -0.04329949, 0.02901585, 0.79433078],
                    [-0.04039948, 0.29126248, -0.95578975, -0.438751],
                    [0.03293398, -0.95566277, -0.29261584, 1.20898935],
                    [0., 0., 0., 1.]
                ])).tolist(),
                "fx": 417.203003,
                "fy": 417.270996,
                "cx": 961.008972,
                "cy": 643.791016,
                'k1': 0.172344,
                'k2': -0.009587,
                'k3': -0.018930,
                'k4': 0.004927
            },
        }
    }
    return config


def run(input_dir, output_dir):
    clip_name = os.path.basename(input_dir)
    out_clip_dir = os.path.join(output_dir, clip_name)
    os.mkdir(out_clip_dir)
    out_lidar_dir = os.path.join(out_clip_dir, 'lidar')
    os.mkdir(out_lidar_dir)
    out_cameras_dir = os.path.join(out_clip_dir, 'camera')
    os.mkdir(out_cameras_dir)
    for camera in cameras:
        os.mkdir(os.path.join(out_cameras_dir, camera))
    for pcd in sorted(tools.get_file_by_extension(os.path.join(input_dir, 'pcd'), '.pcd'), key=lambda x: x.name):
        shutil.copyfile(pcd, os.path.join(out_lidar_dir, pcd.name))
        for camera in cameras:
            src_img = os.path.join(input_dir, camera, pcd.name.replace('.pcd', '.jpg'))
            if not os.path.exists(src_img):
                black_img = Image.new('RGB', (1920, 1280), (0, 0, 0))
                black_img.save(os.path.join(out_cameras_dir, camera, pcd.name.replace('.pcd', '.jpg')))
            else:
                shutil.copyfile(src_img, os.path.join(out_cameras_dir, camera, pcd.name.replace('.pcd', '.jpg')))
    config = construct_config()
    tools.write_json_file(config, os.path.join(out_clip_dir, 'config.json'), indent=4)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
