package middleware

import (
	"context"

	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/iancoleman/strcase"
	"gitlab.rp.konvery.work/platform/pkg/errors"
)

// validator is copied from https://github.com/go-kratos/kratos/blob/main/middleware/validate/validate.go
type validator interface {
	Validate() error
}

// validatorError is an subset of GRPC validation error interface.
type validatorError interface {
	Field() string
	Reason() string
}

// Validator is base on https://github.com/go-kratos/kratos/blob/main/middleware/validate/validate.go
func Validator() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			if v, ok := req.(validator); ok {
				if err := v.Validate(); err != nil {
					if ve, ok := err.(validatorError); ok {
						var options []errors.Option

						reason := ve.Reason()
						if reason != "" {
							options = append(options, errors.WithMessage(reason))
						}
						field := ve.Field()
						if field != "" {
							options = append(options, errors.WithFields(strcase.ToSnake(field)))
						}

						err = errors.NewErrInvalidField(options...).WithCause(err)
					}
					return nil, err
				}
			}
			return handler(ctx, req)
		}
	}
}
