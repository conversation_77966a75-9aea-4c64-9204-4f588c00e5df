import os
import shutil
import sys
import numpy as np
import glob
import pandas as pd
import tempfile

sys.path.append('.')
from customer.common import tools


camera_names = ['CamFisheyeLeft', 'CamFrontNarrowLeft', 'CamRearRight', 'CamFisheyeRight', 'CamRearLeft']
camera_verbose_name_mapping = {
    'CamFisheyeLeft': '左环视',
    'CamFrontNarrowLeft': '前视中距离',
    'CamRearRight': '右后',
    'CamFisheyeRight': '右环视',
    'CamRearLeft': '左后'
}


def construct_config(config_file, out_config_file):
    config_json = tools.get_json_data(config_file)
    camera_configs = config_json['camera']
    sensor_params = {}
    for camera_config in camera_configs:
        camera_name = camera_config['name']
        if camera_name in camera_names:
            distortion = camera_config["intrinsic_param"]["distortion_coeffcients"]
            intrinsic = camera_config["intrinsic_param"]["camera_matrix"]
            extrinsic = np.linalg.inv(tools.get_extrinsic_by_txyz_rxyz_opencv(np.array(camera_config['extrinsic_param']['translation']), np.array(camera_config['extrinsic_param']['rotation'])))
            if camera_config["intrinsic_param"]['camera_model'] == 'opencv_fisheye':
                sensor_params[camera_name] = {
                    "camera_model": 'fisheye',
                    "extrinsic": extrinsic.tolist(),
                    "fx": intrinsic[0][0],
                    "fy": intrinsic[1][1],
                    "cx": intrinsic[0][2],
                    "cy": intrinsic[1][2],
                    "k1": distortion[0],
                    "k2": distortion[1],
                    "k3": 0,
                    "k4": 0
                }
            elif camera_config["intrinsic_param"]['camera_model'] == 'opencv_omni':
                sensor_params[camera_name] = {
                    "camera_model": 'omnidirectional',
                    "extrinsic": extrinsic.tolist(),
                    "fx": intrinsic[0][0],
                    "fy": intrinsic[1][1],
                    "cx": intrinsic[0][2],
                    "cy": intrinsic[1][2],
                    "k1": distortion[0],
                    "k2": distortion[1],
                    "k3": 0,
                    "p1": distortion[2],
                    "p2": distortion[3],
                    "xi": camera_config["intrinsic_param"]['xi']
                }
            elif camera_config["intrinsic_param"]['camera_model'] == 'opencv_pinhole':
                sensor_params[camera_name] = {
                    "camera_model": 'pinhole',
                    "extrinsic": extrinsic.tolist(),
                    "fx": intrinsic[0][0],
                    "fy": intrinsic[1][1],
                    "cx": intrinsic[0][2],
                    "cy": intrinsic[1][2],
                    "k1": distortion[0],
                    "k2": distortion[1],
                    "k3": 0,
                    "p1": distortion[2],
                    "p2": distortion[3]
                }
            else:
                raise Exception('不支持的相机类型' + camera_config['camera_model'])
    configs = {
        "camera": camera_verbose_name_mapping,
        "data_type": "fusion_pointcloud",
        "sensor_params": sensor_params
    }
    tools.write_json_file(configs, out_config_file, ensure_ascii=False, indent=4)


def construct_data(seg_dir, out_seg_dir, mapping_csv):
    with tempfile.TemporaryDirectory() as fix_dir:
        mapping_csv_data = tools.read_file(mapping_csv)
        fixed_mapping_csv_data = mapping_csv_data[2:3] + mapping_csv_data[4:]
        fixed_mapping_csv = os.path.join(fix_dir, 'fixed_mapping.csv')
        tools.write_file(fixed_mapping_csv, fixed_mapping_csv_data)
        df = pd.read_csv(fixed_mapping_csv)
    out_lidar_dir = os.path.join(out_seg_dir, 'lidar')
    os.mkdir(out_lidar_dir)
    out_camera_dir = os.path.join(out_seg_dir, 'camera')
    os.mkdir(out_camera_dir)
    for camera_name in camera_names:
        os.mkdir(os.path.join(out_camera_dir, camera_name))
    for pcd in tools.get_file_by_extension(os.path.join(seg_dir, 'PCD'), '.pcd'):
        frame_info = df[df['LidarHelios'] == pcd.name.replace('_Fusion.pcd', '_LidarHelios.pcd')].iloc[0]
        for camera_name in camera_names:
            img = eval(f'frame_info.{camera_name}')
            src = os.path.join(seg_dir, camera_name, img)
            dst = os.path.join(out_camera_dir, camera_name, pcd.name.replace('.pcd', '.jpeg'))
            shutil.copyfile(src, dst)
        shutil.copyfile(pcd.path, os.path.join(out_lidar_dir, pcd.name))


def run(input_dir, output_dir):
    out_seg_dir = os.path.join(output_dir, os.path.basename(input_dir))
    config_file = os.path.join(input_dir, 'config', 'calibration.json')
    out_config_file = os.path.join(out_seg_dir, 'config.json')
    construct_config(config_file, out_config_file)

    data_dir = os.path.join(input_dir, 'data')
    mapping_csv = glob.glob(os.path.join(input_dir, '**', '*processed_collection.csv'), recursive=True)[0]
    construct_data(data_dir, out_seg_dir, mapping_csv)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
