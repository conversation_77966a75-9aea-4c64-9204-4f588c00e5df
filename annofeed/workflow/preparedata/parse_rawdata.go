package preparedata

import (
	"annofeed/internal/biz"
	"annofeed/internal/data/serial"
	"annofeed/workflow/common"
	"context"
	"fmt"
	"os"
	"path"
	"path/filepath"
	"strings"

	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kfs"
	"gitlab.rp.konvery.work/platform/pkg/wf/wfutil"
	"go.temporal.io/sdk/activity"
)

// with heartbeat
func (o *Activities) PrepareDataParseRawdatas(ctx context.Context, basedir string, data *biz.Data) (err error) {
	fmt.Println()
	fmt.Println("---> PrepareDataParseRawdatas: ", basedir)
	data, err = o.databiz.GetByID(ctx, data.ID)
	if err != nil {
		return fixError(err, "")
	}
	if data.BaseOn > 0 {
		return nil
	}

	defer func() { err = wfutil.RecreateNonRetryableError(err) }()
	elem := &struct {
		Name      string
		Index     int32
		IdxInClip int
		ClipName  string

		// non-heartbeat fields
		path       string
		fileIdx    int
		elemParams *biz.ElemParamData
	}{Index: -1, IdxInClip: -1}
	startIdx := wfutil.GetActivtityStartIndex(ctx, elem)
	i := -1

	changeElem := func(dir string) {
		fmt.Println("------> change dir: ", dir)
		elem.path = dir
		elem.Name = strings.TrimPrefix(dir, common.DataPrefix) // elem.Name should never be empty
		elem.Index++
		elem.fileIdx = 10 // < 10 is reserved for PCD, annotations, params, etc.
		elem.IdxInClip++
		if clip := filepath.Dir(dir); clip != elem.ClipName {
			elem.ClipName = clip
			elem.IdxInClip = 0
		}
	}

	if startIdx == 0 {
		_, err := o.databiz.SetState(ctx, data, biz.DataStateProcessing)
		if errors.IsNotFound(err) {
			return wfutil.NewNonRetryableError("data not found", err)
		}
	}
	startIdx = lo.Ternary(startIdx == 0, 0, startIdx-1)
	fmt.Println("---> startIdx: ", startIdx)

	var metafiles []*biz.Metafile
	err = kfs.IterateDir(basedir, func(entry os.DirEntry, subdir string, depth int) error {
		// skip directories and processed files
		fmt.Printf("------> entry subdir: %s, entry name: %s, depth: %d.\n", subdir, entry.Name(), depth)
		name := entry.Name()
		i++
		if entry.IsDir() {
			fmt.Println("------> entry is dir - skip: ", name)
			fmt.Println()
			return nil
		}
		if path.Base(subdir) == common.MetaDir { // collect metafiles
			elemName := strings.TrimPrefix(subdir, common.DataPrefix)
			metafiles = append(metafiles, &biz.Metafile{Name: path.Join(elemName, name)})
			fmt.Println("------> meta file - skip: ", subdir, metafiles)
			fmt.Println()
			return nil
		}
		fmt.Println("------> skip cond:", i, startIdx, subdir, name)
		if i < startIdx || subdir == "" ||
			strings.HasPrefix(subdir, common.AnnosPrefix) ||
			strings.HasSuffix(name, common.OrigExt) {
			fmt.Println("------> suffix skip:", i < startIdx, subdir == "", subdir, name)
			fmt.Println()
			return nil
		}

		var rd *biz.Rawdata
		switch name {
		case common.ParamsFile:
			fmt.Println("---> hit params.json.", basedir, subdir, name)
			rd, err = o.rawdataParser.parseElemParams(basedir, subdir, name)
		case common.AnnosFile:
			fmt.Println("---> hit annos.json.", basedir, subdir, name)
			rd, err = o.rawdataParser.parseElemAnnos(basedir, subdir, name)
		case common.LidarPCD:
			fmt.Println("---> hit pcd file.", basedir, subdir, name)
			rd, err = o.rawdataParser.parsePCD(basedir, subdir, name)
		default:
			if common.IsSupportedImageFormat(name) {
				rd, err = o.rawdataParser.parseImage(basedir, subdir, name)
			} else {
				err = fmt.Errorf("unknown file %v", filepath.Join(subdir, name))
			}
		}
		if err != nil {
			return wfutil.NewNonRetryableError("failed to parse rawdatas", err)
		}
		fmt.Println("------> rawdatas: ", rd)
		fmt.Println("------> elem path: ", elem.path, "subdir: ", subdir, "i:", i)
		if elem.path != subdir {
			if i > startIdx {
				activity.RecordHeartbeat(ctx, i, elem) // Report progress.
			}
			changeElem(subdir)
			paramData, err := biz.LoadElemParamData(basedir, subdir, common.ParamsFile)
			if err != nil && !kfs.IsNotExist(err) {
				return err
			}
			elem.elemParams = paramData

			// fake a lidar.pcd to carry lidar pose for non-first element in clips in fusion4d datas
			if elem.IdxInClip > 0 && data.Type == biz.DataTypeFusion4D {
				lidar, err := o.rawdataParser.fakePCD(basedir, subdir, common.LidarPCD)
				if err != nil {
					return wfutil.NewNonRetryableError("failed to parse rawdatas", err)
				}
				lidar.Name = path.Join(elem.Name, common.LidarPCD)
				lidar.DataID = data.ID
				lidar.Folder = elem.Name
				lidar.ElemIdxInData = elem.Index
				if _, err = o.rawdatabiz.Create(ctx, lidar); err != nil && !errors.IsConflict(err) {
					return fmt.Errorf("failed to save rawdata %v: %w", lidar.Name, err)
				}
				if err := o.validateElemParams(elem.elemParams, biz.RawdataTypePointcloud, common.KeyLidar); err != nil {
					return wfutil.NewNonRetryableError("failed to validate elem params", err)
				}
			}
		}
		if err := o.validateElemParams(elem.elemParams, rd.Type, name); err != nil {
			return wfutil.NewNonRetryableError("failed to validate elem params", err)
		}

		fpath := filepath.Join(basedir, subdir, name)
		if bs, err := os.ReadFile(fpath + common.OrigExt); err == nil {
			rd.OrigName = strings.TrimSpace(string(bs))
		}
		rd.Size, rd.Sha256, err = o.rawdataParser.StatFile(fpath)
		if err != nil {
			return fmt.Errorf("failed to stat file %v: %w", fpath, err)
		}
		elem.fileIdx++
		if !lo.Contains([]string{common.ParamsFile, common.AnnosFile, common.LidarPCD}, name) {
			rd.Idx = elem.fileIdx
		}
		rd.Name = path.Join(elem.Name, name)
		rd.DataID = data.ID
		rd.Folder = elem.Name
		rd.ElemIdxInData = elem.Index
		//fmt.Println(rd)
		//return nil
		fmt.Printf("------> rd.Name: %s, rd.Folder: %s, rd.ElemIdxInData: %d", rd.Name, rd.Folder, rd.ElemIdxInData)
		fmt.Println()
		if _, err = o.rawdatabiz.Create(ctx, rd); err != nil && !errors.IsConflict(err) {
			return fmt.Errorf("failed to save rawdata %v: %w", fpath, err)
		}

		return nil
	})
	//return errors.NewErrBadRequest()
	if err != nil {
		return fixError(err, "")
	}
	if len(metafiles) > 0 { // create meta rawdata
		if _, err := o.rawdatabiz.Create(ctx, &biz.Rawdata{
			DataID:    data.ID,
			Name:      biz.RawdataTypeMeta, // just make name not empty
			Type:      biz.RawdataTypeMeta,
			Format:    common.RawdataFormatFilelist,
			ExtraData: *serial.New(&biz.ExtraData{Metafiles: metafiles}),
		}); err != nil {
			return fixError(err, "failed to create meta rawdata")
		}
	}

	data.Size = elem.Index + 1
	_, err = o.databiz.Update(ctx, data, field.NewMask(biz.DataSfldSize.String()))
	return fixError(err, "failed to save data size")
}

func (o *Activities) validateElemParams(elemParams *biz.ElemParamData, rdType string, rdName string) error {
	if elemParams == nil {
		return nil
	}

	var transforms []*anno.RawdataParam
	var err error
	switch rdType {
	case biz.RawdataTypePointcloud:
		transforms, err = elemParams.ElemParam.GetLidarTransforms(common.KeyLidar)
	case biz.RawdataTypeImage:
		camName := kfs.FileBareName(rdName)
		transforms, err = elemParams.ElemParam.GetImageTransforms(camName)
	}
	if err != nil {
		return err
	}

	for _, t := range transforms {
		if err := biz.ValidateRawdataParam(t); err != nil {
			return fmt.Errorf("failed to validate transform: %w", err)
		}
	}
	return nil
}
