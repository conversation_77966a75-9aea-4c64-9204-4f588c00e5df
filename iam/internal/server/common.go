package server

import (
	"context"
	"fmt"
	mw "iam/internal/server/middleware"

	prom "github.com/go-kratos/kratos/contrib/metrics/prometheus/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/middleware/validate"
	"github.com/prometheus/client_golang/prometheus"
	amw "gitlab.rp.konvery.work/platform/apis/middleware"
	"gitlab.rp.konvery.work/platform/pkg/errors"
)

func getMiddlewares(logger log.Logger, extra ...middleware.Middleware) []middleware.Middleware {
	common := []middleware.Middleware{
		metadata.Server(),
		recovery.Recovery(recovery.WithHandler(recoveryHandler)),
		metrics.Server(
			metrics.WithSeconds(prom.NewHistogram(metricSeconds)),
			metrics.WithRequests(prom.NewCounter(metricRequests)),
		),
		tracing.Server(),
		mw.Log(logger),
		validate.Validator(),
		amw.HandleError(),
		amw.DebugEnabler(),
		amw.CheckPager(),
	}
	return append(common, extra...)
}

func recoveryHandler(ctx context.Context, req, err interface{}) error {
	e, ok := err.(error)
	if !ok {
		e = fmt.Errorf("%+v", err)
	}
	return errors.NewErrServerError().WithCause(e)
}

var (
	metricSeconds = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Namespace: "server",
		Subsystem: "requests",
		Name:      "duration_sec",
		Help:      "server requests duratio(sec).",
		Buckets:   []float64{0.005, 0.01, 0.025, 0.05, 0.1, 0.250, 0.5, 1},
	}, []string{"kind", "operation"})

	metricRequests = prometheus.NewCounterVec(prometheus.CounterOpts{
		Namespace: "client",
		Subsystem: "requests",
		Name:      "code_total",
		Help:      "The total number of processed requests",
	}, []string{"kind", "operation", "code", "reason"})
)

func init() {
	prometheus.MustRegister(metricSeconds, metricRequests)
}
