// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: model/v1/model.proto

package model

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateModelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateModelRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateModelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateModelRequestMultiError, or nil if none found.
func (m *CreateModelRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateModelRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for Base

	if !_CreateModelRequest_RunUid_Pattern.MatchString(m.GetRunUid()) {
		err := CreateModelRequestValidationError{
			field:  "RunUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|^$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, err := url.Parse(m.GetUri()); err != nil {
		err = CreateModelRequestValidationError{
			field:  "Uri",
			reason: "value must be a valid URI",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateModelRequest_Version_Pattern.MatchString(m.GetVersion()) {
		err := CreateModelRequestValidationError{
			field:  "Version",
			reason: "value does not match regex pattern \"^[\\\\w.-]{3,32}$|^$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Bytes

	// no validation rules for Metric

	if len(errors) > 0 {
		return CreateModelRequestMultiError(errors)
	}

	return nil
}

// CreateModelRequestMultiError is an error wrapping multiple validation errors
// returned by CreateModelRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateModelRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateModelRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateModelRequestMultiError) AllErrors() []error { return m }

// CreateModelRequestValidationError is the validation error returned by
// CreateModelRequest.Validate if the designated constraints aren't met.
type CreateModelRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateModelRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateModelRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateModelRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateModelRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateModelRequestValidationError) ErrorName() string {
	return "CreateModelRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateModelRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateModelRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateModelRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateModelRequestValidationError{}

var _CreateModelRequest_RunUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|^$")

var _CreateModelRequest_Version_Pattern = regexp.MustCompile("^[\\w.-]{3,32}$|^$")

// Validate checks the field values on UpdateModelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateModelRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateModelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateModelRequestMultiError, or nil if none found.
func (m *UpdateModelRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateModelRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateModelRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateModelRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateModelRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateModelRequestMultiError(errors)
	}

	return nil
}

// UpdateModelRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateModelRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateModelRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateModelRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateModelRequestMultiError) AllErrors() []error { return m }

// UpdateModelRequestValidationError is the validation error returned by
// UpdateModelRequest.Validate if the designated constraints aren't met.
type UpdateModelRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateModelRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateModelRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateModelRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateModelRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateModelRequestValidationError) ErrorName() string {
	return "UpdateModelRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateModelRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateModelRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateModelRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateModelRequestValidationError{}

// Validate checks the field values on Model with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Model) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Model with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ModelMultiError, or nil if none found.
func (m *Model) ValidateAll() error {
	return m.validate(true)
}

func (m *Model) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_Model_Uid_Pattern.MatchString(m.GetUid()) {
		err := ModelValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for Base

	if !_Model_RunUid_Pattern.MatchString(m.GetRunUid()) {
		err := ModelValidationError{
			field:  "RunUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|^$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, err := url.Parse(m.GetUri()); err != nil {
		err = ModelValidationError{
			field:  "Uri",
			reason: "value must be a valid URI",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_Model_Version_Pattern.MatchString(m.GetVersion()) {
		err := ModelValidationError{
			field:  "Version",
			reason: "value does not match regex pattern \"^[\\\\w.-]{3-32}|^$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Bytes

	// no validation rules for Metric

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModelValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModelValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModelValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModelValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModelValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModelValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ModelMultiError(errors)
	}

	return nil
}

// ModelMultiError is an error wrapping multiple validation errors returned by
// Model.ValidateAll() if the designated constraints aren't met.
type ModelMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModelMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModelMultiError) AllErrors() []error { return m }

// ModelValidationError is the validation error returned by Model.Validate if
// the designated constraints aren't met.
type ModelValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModelValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModelValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModelValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModelValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModelValidationError) ErrorName() string { return "ModelValidationError" }

// Error satisfies the builtin error interface
func (e ModelValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModel.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModelValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModelValidationError{}

var _Model_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

var _Model_RunUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|^$")

var _Model_Version_Pattern = regexp.MustCompile("^[\\w.-]{3-32}|^$")

// Validate checks the field values on ModelFilter with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ModelFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModelFilter with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ModelFilterMultiError, or
// nil if none found.
func (m *ModelFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *ModelFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ModelFilter_RunUid_Pattern.MatchString(m.GetRunUid()) {
		err := ModelFilterValidationError{
			field:  "RunUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|^$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Type

	// no validation rules for NamePattern

	// no validation rules for Version

	// no validation rules for BasePattern

	if len(errors) > 0 {
		return ModelFilterMultiError(errors)
	}

	return nil
}

// ModelFilterMultiError is an error wrapping multiple validation errors
// returned by ModelFilter.ValidateAll() if the designated constraints aren't met.
type ModelFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModelFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModelFilterMultiError) AllErrors() []error { return m }

// ModelFilterValidationError is the validation error returned by
// ModelFilter.Validate if the designated constraints aren't met.
type ModelFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModelFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModelFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModelFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModelFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModelFilterValidationError) ErrorName() string { return "ModelFilterValidationError" }

// Error satisfies the builtin error interface
func (e ModelFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModelFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModelFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModelFilterValidationError{}

var _ModelFilter_RunUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|^$")

// Validate checks the field values on GetModelRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetModelRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetModelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetModelRequestMultiError, or nil if none found.
func (m *GetModelRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetModelRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetModelRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := GetModelRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetModelRequestMultiError(errors)
	}

	return nil
}

// GetModelRequestMultiError is an error wrapping multiple validation errors
// returned by GetModelRequest.ValidateAll() if the designated constraints
// aren't met.
type GetModelRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetModelRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetModelRequestMultiError) AllErrors() []error { return m }

// GetModelRequestValidationError is the validation error returned by
// GetModelRequest.Validate if the designated constraints aren't met.
type GetModelRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetModelRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetModelRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetModelRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetModelRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetModelRequestValidationError) ErrorName() string { return "GetModelRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetModelRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetModelRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetModelRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetModelRequestValidationError{}

var _GetModelRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on DeleteModelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteModelRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteModelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteModelRequestMultiError, or nil if none found.
func (m *DeleteModelRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteModelRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_DeleteModelRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := DeleteModelRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|byfilter\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteModelRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteModelRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteModelRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteModelRequestMultiError(errors)
	}

	return nil
}

// DeleteModelRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteModelRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteModelRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteModelRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteModelRequestMultiError) AllErrors() []error { return m }

// DeleteModelRequestValidationError is the validation error returned by
// DeleteModelRequest.Validate if the designated constraints aren't met.
type DeleteModelRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteModelRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteModelRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteModelRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteModelRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteModelRequestValidationError) ErrorName() string {
	return "DeleteModelRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteModelRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteModelRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteModelRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteModelRequestValidationError{}

var _DeleteModelRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|byfilter")

// Validate checks the field values on ListModelRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListModelRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListModelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListModelRequestMultiError, or nil if none found.
func (m *ListModelRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListModelRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListModelRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListModelRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListModelRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListModelRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListModelRequestMultiError(errors)
	}

	return nil
}

// ListModelRequestMultiError is an error wrapping multiple validation errors
// returned by ListModelRequest.ValidateAll() if the designated constraints
// aren't met.
type ListModelRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListModelRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListModelRequestMultiError) AllErrors() []error { return m }

// ListModelRequestValidationError is the validation error returned by
// ListModelRequest.Validate if the designated constraints aren't met.
type ListModelRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListModelRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListModelRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListModelRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListModelRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListModelRequestValidationError) ErrorName() string { return "ListModelRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListModelRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListModelRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListModelRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListModelRequestValidationError{}

// Validate checks the field values on ListModelReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListModelReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListModelReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListModelReplyMultiError,
// or nil if none found.
func (m *ListModelReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListModelReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetModels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListModelReplyValidationError{
						field:  fmt.Sprintf("Models[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListModelReplyValidationError{
						field:  fmt.Sprintf("Models[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListModelReplyValidationError{
					field:  fmt.Sprintf("Models[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for Total

	if len(errors) > 0 {
		return ListModelReplyMultiError(errors)
	}

	return nil
}

// ListModelReplyMultiError is an error wrapping multiple validation errors
// returned by ListModelReply.ValidateAll() if the designated constraints
// aren't met.
type ListModelReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListModelReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListModelReplyMultiError) AllErrors() []error { return m }

// ListModelReplyValidationError is the validation error returned by
// ListModelReply.Validate if the designated constraints aren't met.
type ListModelReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListModelReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListModelReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListModelReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListModelReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListModelReplyValidationError) ErrorName() string { return "ListModelReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListModelReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListModelReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListModelReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListModelReplyValidationError{}
