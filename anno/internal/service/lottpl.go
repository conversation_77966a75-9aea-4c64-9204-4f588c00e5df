// source: anno/v1/lottpl.proto
package service

import (
	"context"

	"anno/api/client"
	"anno/internal/biz"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func FromBizLottpl(d *biz.Lottpl) *anno.Lottpl {
	if d == nil {
		return nil
	}
	o := &anno.Lottpl{
		Uid:         d.GetUid(),
		Name:        d.Name,
		Desc:        d.<PERSON>,
		JobSize:     d.JobSize,
		Type:        anno.Lot_Type_Enum(anno.Lotphase_Type_Enum_value[d.Type]),
		Ontologies:  d.Ontologies.E,
		Phases:      d.Phases,
		Out:         d.Out.E,
		Instruction: d.Instruction,
		UpdatedAt:   timestamppb.New(d.UpdatedAt),
		CreatedAt:   timestamppb.New(d.CreatedAt),
		// RejectReasons: d.RejectReasons,
	}
	return o
}

func ToBizLottpl(d *anno.CreateLottplRequest) *biz.Lottpl {
	if d == nil {
		return nil
	}
	o := &biz.Lottpl{
		ID:          kid.ParseID(d.Uid),
		Name:        d.Name,
		Desc:        d.Desc,
		JobSize:     d.JobSize,
		Type:        d.Type.String(),
		Out:         *serial.New(d.Out),
		Ontologies:  *serial.New(d.Ontologies),
		Phases:      d.Phases,
		Instruction: d.Instruction,
		// RejectReasons: d.RejectReasons,
	}
	return o
}

type LottplsService struct {
	anno.UnimplementedLottplsServer
	bz *biz.LottplsBiz
}

func NewLottplsService(bz *biz.LottplsBiz) *LottplsService {
	// TODO: implement access control
	bz = nil // fail all calls
	return &LottplsService{bz: bz}
}

func (o *LottplsService) CreateLottpl(ctx context.Context, req *anno.CreateLottplRequest) (*anno.Lottpl, error) {
	data, err := o.bz.Create(ctx, ToBizLottpl(req))
	return FromBizLottpl(data), err
}

func (o *LottplsService) UpdateLottpl(ctx context.Context, req *anno.UpdateLottplRequest) (*anno.Lottpl, error) {
	data, err := o.bz.Update(ctx, ToBizLottpl(req.Lottpl), field.NewMask(req.Fields...))
	return FromBizLottpl(data), err
}

func (o *LottplsService) DeleteLottpl(ctx context.Context, req *anno.DeleteLottplRequest) (*emptypb.Empty, error) {
	return &emptypb.Empty{}, o.bz.DeleteByUid(ctx, req.Uid)
}

func (o *LottplsService) GetLottpl(ctx context.Context, req *anno.GetLottplRequest) (*anno.Lottpl, error) {
	data, err := o.bz.GetByUid(ctx, req.Uid)
	return FromBizLottpl(data), err
}

func (o *LottplsService) ListLottpl(ctx context.Context, req *anno.ListLottplRequest) (*anno.ListLottplReply, error) {
	creator, scope := getListScope(ctx, req.OrgUid, req.CreatorUid)
	if !client.IsAllowed(ctx, "", biz.PermList, biz.PermClsLottpl, scope) {
		return nil, errors.NewErrForbidden()
	}

	typ := ""
	if req.Type != anno.Lot_Type_unspecified {
		typ = req.Type.String()
	}
	pager := biz.Pager{
		Pagesz: int(req.Pagesz),
		Page:   int(req.Page),
	}
	filter := &biz.LottplListFilter{
		OrgUid:      req.OrgUid,
		CreatorUid:  creator,
		NamePattern: req.NamePattern,
		Type:        typ,
	}

	var cnt int
	if pager.Page == 0 {
		var err error
		cnt, err = o.bz.Count(ctx, filter)
		if err != nil {
			return nil, err
		}
	}
	datas, err := o.bz.List(ctx, filter, pager)
	if err != nil {
		return nil, err
	}
	return &anno.ListLottplReply{Total: int32(cnt), Lottpls: kslice.Map(FromBizLottpl, datas)}, err
}
