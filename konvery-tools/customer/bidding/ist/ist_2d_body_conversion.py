import json
import os
import sys

sys.path.append('.')
from customer.common import tools

body_sequence = ['nose', 'left_eye', 'right_eye', 'left_ear', 'right_ear', 'left_shoulder', 'right_shoulder',
                 'left_elbow', 'right_elbow', 'left_wrist', 'right_wrist', 'left_hip', 'right_hip', 'left_knee',
                 'right_knee', 'left_ankle', 'right_ankle']


def run(input_dir, output_dir):
    input_dir_len = len(os.path.dirname(input_dir)) + 1
    label_files = tools.get_json_files(input_dir)
    label_files.sort(key=lambda x: x.name)
    result = {
        "images": [],
        "annotations": []
    }
    image_id = 0
    person_id = 0
    error_list = []
    for idx, label_file in enumerate(label_files, start=1):
        data = tools.get_json_data(label_file.path)
        time_stamp = label_file.name.split('__')[-1].split('.')[0]
        image = {
            "height": data["label_meta"]["source_info"]["height"],
            "width": data["label_meta"]["source_info"]["width"],
            "id": image_id,
            "file_name": time_stamp + ".jpg",
            "data_captured": time_stamp
        }
        group_id2labels = dict()
        wrong_labels = []
        for label in data["labels"]:
            group_id = label.get('group_id', -1)
            if group_id not in group_id2labels:
                group_id2labels[group_id] = dict()
            if label["class"] not in group_id2labels[group_id]:
                group_id2labels[group_id][label["class"]] = label
            else:
                wrong_labels.append(label["class"])
        if wrong_labels:
            error_list.append((idx, label_file.path[input_dir_len:], wrong_labels))

        group_ids = sorted(list(group_id2labels.keys()))
        for group_id in group_ids:
            keypoints = []
            num_keypoints = 0
            labels = group_id2labels[group_id]
            for body in body_sequence:
                if body in labels:
                    num_keypoints += 1
                    keypoints.append(float(labels[body]["annotation"]["data"]["x"]))
                    keypoints.append(float(labels[body]["annotation"]["data"]["y"]))
                    keypoints.append(int(labels[body]["attrs"]["visibility"][0]))
                else:
                    keypoints.append(0)
                    keypoints.append(0)
                    keypoints.append(0)
            try:
                box_data = labels["pedestrian"]["annotation"]["data"]
            except Exception as a:
                if labels == {}:
                    continue
                else:
                    error_list.append((idx, label_file.path[input_dir_len:]))
            annotation = {
                "keypoints": keypoints,
                "image_id": image_id,
                "category_id": 1,
                "id": person_id,
                "bbox": [box_data["x"], box_data["y"], box_data["width"], box_data["height"]],
                "area": box_data["width"] * box_data["height"],
                "num_keypoints": num_keypoints
            }
            if len(keypoints) != 51:
                error_list.append((idx, label_file.path[input_dir_len:], keypoints))
            result["annotations"].append(annotation)
            person_id += 1
        result["images"].append(image)
        image_id += 1
    with open(os.path.join(output_dir, os.path.basename(input_dir.replace('.zip', '')) + '.json'), 'w') as file:
        json.dump(result, file, indent=4)
    if error_list:
        print("error_list:", error_list)


def merge_result(input_dir, output_dir):
    # TODO bugfix
    json_files = tools.get_json_files(input_dir)
    json_files.sort(key=lambda x: x.name)
    img_id_diff = 0
    person_id_diff = 0
    merge_img = []
    merge_person = []
    for json_file in json_files:
        json_data = tools.get_json_data(json_file.path)
        for img in json_data["images"]:
            img["id"] += img_id_diff
            merge_img.append(img)

            if img["id"] in check_ids:
                print("img:", json_file.name, img)

        for person in json_data["annotations"]:
            person["id"] += person_id_diff
            person["image_id"] += img_id_diff
            if "category_id" not in person:
                person["category_id"] = 1
            merge_person.append(person)

            if person["image_id"] in check_ids:
                print("anno:", json_file.name, person)
            if len(person['keypoints']) != 51:
                print("keypoints:", json_file.name, len(person['keypoints']))

        img_id_diff += json_data["images"][-1]["id"] + 1
        person_id_diff += json_data["annotations"][-1]["id"] + 1

    merge_result = {
        "images": merge_img,
        "annotations": merge_person
    }
    tools.write_json_file(merge_result, os.path.join(output_dir, os.path.basename(input_dir.replace('.zip', '')) + '.json'))


if __name__ == "__main__":
    check_ids = []
    args = tools.get_args()
    tools.check_dir(args.out)
    # if isinstance(args.txt, str) and args.txt.strip() == 'merge':
    #     merge_result(args.input, args.out)
    # else:
    if args.input.endswith("zip"):
        input_dir = tools.unzip(args.input, extract_dir=os.path.dirname(args.input))
        input_dir = tools.get_sub_dir(input_dir, name_only=False)[0]
        os.makedirs(args.out, exist_ok=True)
        run(input_dir, args.out)
    else:
        run(args.input, args.out)
