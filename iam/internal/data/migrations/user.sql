-- mig:up ********** initial
CREATE TABLE meta
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    name        VARCHAR(64)  NOT NULL UNIQUE,
    value       VARCHAR(512) NOT NULL,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);

-- make first manually created user the super user
INSERT INTO meta (name, value) VALUES ('autoCreateRoot', 'true');

CREATE TABLE users
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    -- uid         VARCHAR(32)  NOT NULL UNIQUE,
    -- account     VARCHAR(64)  NOT NULL UNIQUE,
    name        VARCHAR(64)  NOT NULL DEFAULT '',
    phone       VARCHAR(64)  <PERSON>IQUE,
    email       VARCHAR(128) UNIQUE,
    password    VARCHAR(256) NOT NULL DEFAULT '',
    avatar      VARCHAR(512) NOT NULL DEFAULT '',
    -- hierarchy   BIGINT[]     NOT NULL DEFAULT '{}', -- team hierarchy: [team-level-1, team-level-2]
    org_id      BIGINT       NOT NULL DEFAULT 0,
    -- work_team_id BIGINT      NOT NULL DEFAULT 0,
    role        VARCHAR(32)  NOT NULL, -- global role or orgnization role
    status      SMALLINT     NOT NULL DEFAULT 0, -- 0-valid, 1-
    province    VARCHAR(256) NOT NULL DEFAULT '',
    city        VARCHAR(256) NOT NULL DEFAULT '',
    gender      SMALLINT     NOT NULL DEFAULT 0, -- 0-unkown, 1-male, 2-female, 3-X
    birthday    TIMESTAMPTZ,
    agreement   INT          NOT NULL DEFAULT 0, -- version number of signed user agreement
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    deleted_at  TIMESTAMPTZ
);
CREATE INDEX idx_users_name ON users ((lower(name))) INCLUDE (status, deleted_at);
-- CREATE INDEX idx_users_hierarchy ON users USING GIN (hierarchy); --INCLUDE (status, deleted_at);
-- CREATE INDEX idx_users_hierarchy1 ON users ((hierarchy[1])) INCLUDE (status, deleted_at);
-- CREATE INDEX idx_users_hierarchy_last1 ON users ((hierarchy[array_upper(hierarchy, 1)])) INCLUDE (status, deleted_at);
CREATE INDEX idx_users_org_id ON users (org_id) INCLUDE (status, deleted_at);
-- CREATE INDEX idx_users_role ON users (role) INCLUDE (status, deleted_at);
CREATE INDEX idx_users_created_at ON users (created_at) INCLUDE (status, deleted_at);

-- service accounts for RPC usage
INSERT INTO users (id, name, role, gender, birthday, province, city, avatar)
  VALUES (13742, 'Anno', 'service', 0, '2022-02-24', '北京市', '北京市', '');

-- mig:up ********** add column tags to users
ALTER TABLE users ADD COLUMN tags VARCHAR(64)[];
CREATE INDEX idx_users_tags ON users USING GIN (tags);

-- mig:down ********** add column tags to users
DROP INDEX idx_users_tags;
ALTER TABLE users DROP COLUMN tags;

-- mig:down ********** initial
DROP TABLE meta;
DROP TABLE users;

-- mig:up ********** add_last_login_time
ALTER TABLE users ADD COLUMN last_login_at TIMESTAMPTZ;
CREATE INDEX idx_users_last_login_at ON users (last_login_at);

-- mig:down ********** add_last_login_time
DROP INDEX idx_users_last_login_at;
ALTER TABLE users DROP COLUMN last_login_at;