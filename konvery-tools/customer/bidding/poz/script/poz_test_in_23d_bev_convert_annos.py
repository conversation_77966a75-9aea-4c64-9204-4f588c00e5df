import sys
import os
from collections import defaultdict

sys.path.append(".")
from customer.common import tools


SPEC_ATTR_KEYS = [
    "urgency_status",
    "rearview_status",
    "rider_status",
    "child_status",
    "fake_man",
    "gate_status"
]

# 车灯属性对应的键（顺序：左转向灯、右转向灯、刹车灯、高位刹车灯），缺失则默认 "0"
LIGHT_KEYS = ["left_turn_signal", "right_turn_signal", "brake_light", "high_brake_light"]
# 车门属性对应的键（顺序：左前车门、左后车门、右前车门、右后车门、后备箱），缺失则默认 "0"
DOOR_KEYS = ["left_front_door", "left_rear_door", "right_front_door", "right_rear_door", "trunk"]


def extract_first_value(attrs, key, default="0"):
    """ 从 attrs 获取 key 的第一个值，如果不存在则返回默认值 """
    value = attrs.get(key, [default])
    return value[0] if isinstance(value, list) else value


def process_json_file(input_path, output_path, clip_name):
    data = tools.get_json_data(input_path)
    # print(data)
    timestamp = os.path.splitext(os.path.basename(input_path))[0]
    transformed = {
        "version": "1.0.1",
        "project_name": "23D融合BEV障碍物检测",
        "rule_version": "1.0.0",
        "producer_id": "",
        "timestamp": timestamp,
        "car_id": "",
        "clip_id": clip_name,
        "calib_path": "",
        "generate_mode": 0,
        "frame_ignore": "false",
        "camera": [],
        "lidar": [],
        "ego_pose": {
          "rotation": {
              "w": 0.7151251383112748,
              "x": -0.0006668864343345617,
              "y": 0.0009489323301695645,
              "z": 0.6989954873568187
          },
          "translation": {
              "x": 0.028941783130549535,
              "y": 0.4242166445070774,
              "z": -0.2879552052263564
          }
        },
        "coordinate_system": "ego",
        "annotations": []
    }

    # 关联 track_id 数据：每个 track_id 对应一个annotation（可能包含多个3D/2D对象）
    track_dict = defaultdict(lambda: {"category": "", "attributes": {}, "objects": []})

    # 处理 LiDAR 数据（3D框）
    for obj in data.get("lidar", []):
        track_id = obj.get("track_id", 0)
        # category取自lidar的class
        if not track_dict[track_id]["category"]:
            track_dict[track_id]["category"] = obj.get("class", "")
        # 提取属性，仅从lidar的attrs中获取
        lidar_attrs = obj.get("attrs", {})
        for key in SPEC_ATTR_KEYS:
            attr_val = extract_first_value(lidar_attrs, key, default="0")
            if attr_val == 'True':
                attr_val = '1'
            if attr_val == 'False':
                attr_val = '0'
            track_dict[track_id]["attributes"][key] = attr_val
        track_dict[track_id]["attributes"]["light_status"] = "".join([extract_first_value(lidar_attrs, key, "0") for key in LIGHT_KEYS])
        track_dict[track_id]["attributes"]["door_status"] = "".join([extract_first_value(lidar_attrs, key, "0") for key in DOOR_KEYS])

        # 取 is_crowd 值来自于 annotation 中的 "Crowd" 字段（如果没有则默认 "false"）
        is_crowd_value = lidar_attrs.get("Crowd", "false")
        # 添加 3D 对象信息
        anno_3d_obj = obj.get("annotation", {}).get("data", {})
        track_dict[track_id]["objects"].append({
            "group_index": obj.get("group_id", 0),
            "sub_category": obj.get("class", "unknown"),
            "track_id": track_id,
            "points_num": obj.get("point_num", 0),
            "is_crowd": is_crowd_value,
            "visible_in_lidar": "true",
            "visible_in_2d": "false",
            "anno_3d": {
                "position": {
                    "length": anno_3d_obj.get("position", {}).get("x", 0),
                    "width": anno_3d_obj.get("position", {}).get("y", 0),
                    "height": anno_3d_obj.get("position", {}).get("z", 0)
                },
                "motion_position": {"x": 0, "y": 0, "z": 0},
                "dimension": {
                    "length": anno_3d_obj.get("dimension", {}).get("l", 0),
                    "width": anno_3d_obj.get("dimension", {}).get("w", 0),
                    "height": anno_3d_obj.get("dimension", {}).get("h", 0)
                },
                "rotation": {
                    "roll": anno_3d_obj.get("rotation", {}).get("x", 0),
                    "pitch": anno_3d_obj.get("rotation", {}).get("y", 0),
                    "yaw": anno_3d_obj.get("rotation", {}).get("z", 0)
                },
                "velocity": {"x": 0, "y": 0, "z": 0}
            }
            # 后续将添加anno_2d数据
        })
    # print(track_dict)
    # return
    # 处理 Camera 数据（2D框），camera字段为字典类型，key为摄像头名称
    for cam_name, objs in data.get("camera", {}).items():
        transformed["camera"].append({
            "name": cam_name,
            "timestamp": timestamp,  # 可根据实际数据调整摄像头曝光时间
            "path": ""
        })
        for obj in objs:
            track_id = obj.get("track_id", 0)
            # 对应的2D信息：构造 2D框信息，利用annotation数据中的 x, y, width, height
            cam_ann = obj.get("annotation", {}).get("data", {})
            if all(k in cam_ann for k in ("x", "y", "width", "height")):
                points = [
                    (cam_ann["x"], cam_ann["y"]),
                    (cam_ann["x"] + cam_ann["width"], cam_ann["y"]),
                    (cam_ann["x"] + cam_ann["width"], cam_ann["y"] + cam_ann["height"]),
                    (cam_ann["x"], cam_ann["y"] + cam_ann["height"])
                ]
            else:
                points = []
            # occlusion 和 truncated 取自 camera对象的attrs
            cam_attrs = obj.get("attrs", {})
            occlusion = extract_first_value(cam_attrs, "occluded", default="0")
            truncated = extract_first_value(cam_attrs, "truncated", default="0")

            # 将2D框添加到对应 track_id 的所有3D对象中
            for item in track_dict[track_id]["objects"]:
                if item["track_id"] == track_id:
                    item.setdefault("anno_2d", []).append({
                        "camera_name": cam_name,
                        "occlusion": int(occlusion),
                        "truncated": int(truncated),
                        "points": points
                    })

    # 输出 lidar 传感器信息
    lidar_sensors = set()
    for obj in data.get("lidar", []):
        pass
        # sensor = obj.get("sensor_name", "lidar")
        # lidar_sensors.add(sensor)
    for sensor in lidar_sensors:
        transformed["lidar"].append({
            "name": sensor,
            "timestamp": timestamp,
            "path": ""
        })

    # 整理 annotations 数据：按 track_id 聚合
    for track_id, track_data in track_dict.items():
        # 更新每个对象的 visible_in_2d 属性
        has_2d = any(obj.get('anno_2d') for obj in track_data['objects'])
        for obj in track_data['objects']:
            obj['visible_in_2d'] = "true" if has_2d else "false"
        transformed["annotations"].append({
            "group_id": str(track_id),
            "category": track_data["category"],
            "attributes": track_data["attributes"],
            "objects": track_data["objects"]
        })
    #
    tools.write_json_file(transformed, output_path, indent=4, ensure_ascii=False)


def run(input_dir, output_dir, config_path):
    last_path_level = os.path.basename(os.path.normpath(input_dir))
    output_dir = os.path.join(output_dir, last_path_level)
    for dirpath, dirnames, filenames in os.walk(input_dir):
        if 'label' in dirnames:
            clip_name = os.path.basename(dirpath)
            label_dir = os.path.join(dirpath, 'label')
            relative_path = os.path.relpath(label_dir, input_dir)
            output_label_dir = os.path.join(str(output_dir), relative_path)
            json_files = sorted([f for f in os.listdir(label_dir) if f.endswith('.json')])
            for filename in json_files:
                # print('---', filename)
                input_file = os.path.join(label_dir, filename)
                output_file = os.path.join(output_label_dir, filename)
                process_json_file(input_file, output_file, clip_name)


if __name__ == "__main__":
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out, args.txt)
