syntax = "proto3";

package anno.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
// import "google/protobuf/timestamp.proto";
// import "google/protobuf/field_mask.proto";
// import "google/api/field_behavior.proto";
// import "openapi/v3/annotations.proto";
import "anno/v1/type.proto";
import "validate/validate.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anno/v1;anno";
option java_multiple_files = true;
option java_package = "anno.v1";

service Roles {
  rpc CreateRole (Role) returns (Role) {
    option (google.api.http) = {
      post: "/v1/roles"
      body: "*"
    };
  }

  rpc UpdateRole (UpdateRoleRequest) returns (Role) {
    option (google.api.http) = {
      patch: "/v1/roles/{role.name}"
      body: "role"
    };
  }

  rpc DeleteRole (DeleteRoleRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/roles/{name}"
    };
  }

  rpc GetRole (GetRoleRequest) returns (Role) {
    option (google.api.http) = {
      get: "/v1/roles/{name}"
    };
  }

  rpc ListRole (ListRoleRequest) returns (ListRoleReply) {
    option (google.api.http) = {
      get: "/v1/roles"
    };
  }

  //
  // user roles
  //

  rpc ListUsersRole (ListUsersRoleRequest) returns (ListUsersRoleReply) {
    option (google.api.http) = {
      get: "/v1/roles/users"
    };
  }

  rpc GetUserRole (GetUserRoleRequest) returns (GetUserRoleReply) {
    option (google.api.http) = {
      get: "/v1/roles/users/{uid}"
    };
  }

  // rpc GetUsersRole (GetUsersRoleRequest) returns (GetUsersRoleReply) {
  //   option (google.api.http) = {
  //     get: "/v1/roles/users"
  //   };
  // }

  rpc SetUsersRole (SetUsersRoleRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/roles/users"
      body: "*"
    };
  }

  rpc DeleteUsersRole (DeleteUsersRoleRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/roles/users"
    };
  }
}

//message CreateRoleRequest {}
//message CreateRoleReply {}

message UpdateRoleRequest {
  Role role = 1;

  repeated string fields = 2;
}
//message UpdateRoleReply {}

message DeleteRoleRequest {
  string name = 1;
}
//message DeleteRoleReply {}

message GetRoleRequest {
  string name = 1;
}
//message GetRoleReply {}

message ListRoleRequest {
  int32 page = 1;
  int32 pagesz = 2 [(validate.rules).int32 = {gte:0, lte: 100}];

  string name_pattern = 3;
}

message ListRoleReply {
  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated Role roles = 2;
}

message Role {
  string name = 1;
  // language => name
  map<string, string> langs = 2;
}

//
// user roles
//

message ListUsersRoleRequest {
  int32 page = 1;
  int32 pagesz = 2 [(validate.rules).int32 = {gte:0, lte: 100}];

  // Scope scope = 3;
  string team_uid = 4;
  repeated string user_uids = 5;
  repeated string role = 6;
}

message ListUsersRoleReply {
  // message User {
  //   string uid = 1;
  //   string name = 2;
  //   string role = 3;
  //   int32 role_level = 4;
  // }

  // repeated User users = 1;
  repeated UserRole users = 1;
}

message UserRole {
  string uid = 1;
  string role = 2;
  // int32 role_level = 3;
}

message SetUsersRoleRequest {
  Scope scope = 1;
  repeated UserRole roles = 2;
}

message DeleteUsersRoleRequest {
  Scope scope = 1;
  repeated string user_ids = 2;
}

message GetUserRoleRequest {
  // user uid
  string uid = 1;
  Scope scope = 2;
}

message GetUserRoleReply {
  string role = 2;
  // int32 role_level = 3;
}

// message GetUsersRoleRequest {
//   Scope scope = 1;
//   string team_uid = 2;
//   repeated string user_uids = 3;
// }

// message GetUsersRoleReply {
//   repeated UserRole users = 2;
// }
