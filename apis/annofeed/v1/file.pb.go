// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: annofeed/v1/file.proto

package annofeed

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type File_State_Enum int32

const (
	File_State_unspecified File_State_Enum = 0
	// file uploading is not finished yet
	File_State_uploading File_State_Enum = 1
	// file has been successfully uploaded
	File_State_uploaded File_State_Enum = 2
)

// Enum value maps for File_State_Enum.
var (
	File_State_Enum_name = map[int32]string{
		0: "unspecified",
		1: "uploading",
		2: "uploaded",
	}
	File_State_Enum_value = map[string]int32{
		"unspecified": 0,
		"uploading":   1,
		"uploaded":    2,
	}
)

func (x File_State_Enum) Enum() *File_State_Enum {
	p := new(File_State_Enum)
	*p = x
	return p
}

func (x File_State_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (File_State_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_annofeed_v1_file_proto_enumTypes[0].Descriptor()
}

func (File_State_Enum) Type() protoreflect.EnumType {
	return &file_annofeed_v1_file_proto_enumTypes[0]
}

func (x File_State_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use File_State_Enum.Descriptor instead.
func (File_State_Enum) EnumDescriptor() ([]byte, []int) {
	return file_annofeed_v1_file_proto_rawDescGZIP(), []int{12, 0, 0}
}

type CreateFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name the file to be created
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// number of bytes of the file
	Size float64 `protobuf:"fixed64,2,opt,name=size,proto3" json:"size,omitempty"`
	// set number of parts in a multi-part upload;
	// if unspecified, which is 0, server will choose a proper number based on file size.
	// Part size should be between 5 MiB to 5 GiB. There is no minimum size limit on the
	// last part of your multipart upload.
	Parts int32 `protobuf:"varint,3,opt,name=parts,proto3" json:"parts,omitempty"`
	// MIME type of the file: e.g. "application/zip",
	// or file name extention: e.g. ".zip"
	Mime string `protobuf:"bytes,4,opt,name=mime,proto3" json:"mime,omitempty"`
	// SHA256 digest of the file; it also accepts MD5 digest in the format: md5:xxx
	Sha256 string `protobuf:"bytes,5,opt,name=sha256,proto3" json:"sha256,omitempty"`
	// organization that the file belongs to; if omitted, it is the user's organization
	OrgUid string `protobuf:"bytes,6,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
}

func (x *CreateFileRequest) Reset() {
	*x = CreateFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_file_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateFileRequest) ProtoMessage() {}

func (x *CreateFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_file_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateFileRequest.ProtoReflect.Descriptor instead.
func (*CreateFileRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_file_proto_rawDescGZIP(), []int{0}
}

func (x *CreateFileRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateFileRequest) GetSize() float64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *CreateFileRequest) GetParts() int32 {
	if x != nil {
		return x.Parts
	}
	return 0
}

func (x *CreateFileRequest) GetMime() string {
	if x != nil {
		return x.Mime
	}
	return ""
}

func (x *CreateFileRequest) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *CreateFileRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

type CreateFileReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	File *File `protobuf:"bytes,1,opt,name=file,proto3" json:"file,omitempty"`
	// if len(upload_urls) > 1, file should be uploaded as multi-parts
	UploadUrls []string `protobuf:"bytes,2,rep,name=upload_urls,json=uploadUrls,proto3" json:"upload_urls,omitempty"`
	// presigned URL expire time. upload should be done before that
	UrlExpiresAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=url_expires_at,json=urlExpiresAt,proto3" json:"url_expires_at,omitempty"`
	// if true, a previously created file is returned due to they have the same hash and size
	PreExisting bool `protobuf:"varint,4,opt,name=pre_existing,json=preExisting,proto3" json:"pre_existing,omitempty"`
}

func (x *CreateFileReply) Reset() {
	*x = CreateFileReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_file_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateFileReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateFileReply) ProtoMessage() {}

func (x *CreateFileReply) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_file_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateFileReply.ProtoReflect.Descriptor instead.
func (*CreateFileReply) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_file_proto_rawDescGZIP(), []int{1}
}

func (x *CreateFileReply) GetFile() *File {
	if x != nil {
		return x.File
	}
	return nil
}

func (x *CreateFileReply) GetUploadUrls() []string {
	if x != nil {
		return x.UploadUrls
	}
	return nil
}

func (x *CreateFileReply) GetUrlExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UrlExpiresAt
	}
	return nil
}

func (x *CreateFileReply) GetPreExisting() bool {
	if x != nil {
		return x.PreExisting
	}
	return false
}

type UpdateFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// only allow to change file name
	File *File `protobuf:"bytes,1,opt,name=file,proto3" json:"file,omitempty"`
	// name of fields to update
	Fields []string `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *UpdateFileRequest) Reset() {
	*x = UpdateFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_file_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFileRequest) ProtoMessage() {}

func (x *UpdateFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_file_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFileRequest.ProtoReflect.Descriptor instead.
func (*UpdateFileRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_file_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateFileRequest) GetFile() *File {
	if x != nil {
		return x.File
	}
	return nil
}

func (x *UpdateFileRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type GetFileUploadURLsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file uid
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// set number of parts in a multi-part upload
	Parts int32 `protobuf:"varint,2,opt,name=parts,proto3" json:"parts,omitempty"`
}

func (x *GetFileUploadURLsRequest) Reset() {
	*x = GetFileUploadURLsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_file_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFileUploadURLsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileUploadURLsRequest) ProtoMessage() {}

func (x *GetFileUploadURLsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_file_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileUploadURLsRequest.ProtoReflect.Descriptor instead.
func (*GetFileUploadURLsRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_file_proto_rawDescGZIP(), []int{3}
}

func (x *GetFileUploadURLsRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *GetFileUploadURLsRequest) GetParts() int32 {
	if x != nil {
		return x.Parts
	}
	return 0
}

type GetFileUploadURLsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// if len(upload_urls) > 1, file should be uploaded as multi-parts
	UploadUrls []string `protobuf:"bytes,1,rep,name=upload_urls,json=uploadUrls,proto3" json:"upload_urls,omitempty"`
	// presigned URL expire time. upload should be done before that
	UrlExpiresAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=url_expires_at,json=urlExpiresAt,proto3" json:"url_expires_at,omitempty"`
}

func (x *GetFileUploadURLsReply) Reset() {
	*x = GetFileUploadURLsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_file_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFileUploadURLsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileUploadURLsReply) ProtoMessage() {}

func (x *GetFileUploadURLsReply) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_file_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileUploadURLsReply.ProtoReflect.Descriptor instead.
func (*GetFileUploadURLsReply) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_file_proto_rawDescGZIP(), []int{4}
}

func (x *GetFileUploadURLsReply) GetUploadUrls() []string {
	if x != nil {
		return x.UploadUrls
	}
	return nil
}

func (x *GetFileUploadURLsReply) GetUrlExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UrlExpiresAt
	}
	return nil
}

type FinishFileUploadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file uid
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// ETAG returned from each part upload, in the part number order;
	// required in a multipart upload
	Etags []string `protobuf:"bytes,2,rep,name=etags,proto3" json:"etags,omitempty"`
}

func (x *FinishFileUploadRequest) Reset() {
	*x = FinishFileUploadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_file_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinishFileUploadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinishFileUploadRequest) ProtoMessage() {}

func (x *FinishFileUploadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_file_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinishFileUploadRequest.ProtoReflect.Descriptor instead.
func (*FinishFileUploadRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_file_proto_rawDescGZIP(), []int{5}
}

func (x *FinishFileUploadRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *FinishFileUploadRequest) GetEtags() []string {
	if x != nil {
		return x.Etags
	}
	return nil
}

type DeleteFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *DeleteFileRequest) Reset() {
	*x = DeleteFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_file_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFileRequest) ProtoMessage() {}

func (x *DeleteFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_file_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFileRequest.ProtoReflect.Descriptor instead.
func (*DeleteFileRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_file_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteFileRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *GetFileRequest) Reset() {
	*x = GetFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_file_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileRequest) ProtoMessage() {}

func (x *GetFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_file_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileRequest.ProtoReflect.Descriptor instead.
func (*GetFileRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_file_proto_rawDescGZIP(), []int{7}
}

func (x *GetFileRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type ListFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagesz    int32  `protobuf:"varint,1,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// filter by organization
	OrgUid string `protobuf:"bytes,3,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// filter by creator
	CreatorUid string `protobuf:"bytes,4,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	// filter by name pattern
	NamePattern string `protobuf:"bytes,5,opt,name=name_pattern,json=namePattern,proto3" json:"name_pattern,omitempty"`
	// filter by URI
	Uris []string `protobuf:"bytes,6,rep,name=uris,proto3" json:"uris,omitempty"`
}

func (x *ListFileRequest) Reset() {
	*x = ListFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_file_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFileRequest) ProtoMessage() {}

func (x *ListFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_file_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFileRequest.ProtoReflect.Descriptor instead.
func (*ListFileRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_file_proto_rawDescGZIP(), []int{8}
}

func (x *ListFileRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListFileRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListFileRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *ListFileRequest) GetCreatorUid() string {
	if x != nil {
		return x.CreatorUid
	}
	return ""
}

func (x *ListFileRequest) GetNamePattern() string {
	if x != nil {
		return x.NamePattern
	}
	return ""
}

func (x *ListFileRequest) GetUris() []string {
	if x != nil {
		return x.Uris
	}
	return nil
}

type ListFileReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Files         []*File `protobuf:"bytes,1,rep,name=files,proto3" json:"files,omitempty"`
	NextPageToken string  `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListFileReply) Reset() {
	*x = ListFileReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_file_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListFileReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFileReply) ProtoMessage() {}

func (x *ListFileReply) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_file_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFileReply.ProtoReflect.Descriptor instead.
func (*ListFileReply) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_file_proto_rawDescGZIP(), []int{9}
}

func (x *ListFileReply) GetFiles() []*File {
	if x != nil {
		return x.Files
	}
	return nil
}

func (x *ListFileReply) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

type ShareFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file uid
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// set URL's valid duration in seconds; if it is -1, it will return a permanent URL
	Timeout int32 `protobuf:"varint,7,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *ShareFileRequest) Reset() {
	*x = ShareFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_file_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShareFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShareFileRequest) ProtoMessage() {}

func (x *ShareFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_file_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShareFileRequest.ProtoReflect.Descriptor instead.
func (*ShareFileRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_file_proto_rawDescGZIP(), []int{10}
}

func (x *ShareFileRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ShareFileRequest) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

type ShareFileReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// URL to download the file
	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	// presigned URL expires time.
	ExpiresAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
}

func (x *ShareFileReply) Reset() {
	*x = ShareFileReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_file_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShareFileReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShareFileReply) ProtoMessage() {}

func (x *ShareFileReply) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_file_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShareFileReply.ProtoReflect.Descriptor instead.
func (*ShareFileReply) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_file_proto_rawDescGZIP(), []int{11}
}

func (x *ShareFileReply) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ShareFileReply) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

type File struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// file URI
	Uri string `protobuf:"bytes,2,opt,name=uri,proto3" json:"uri,omitempty"`
	// file name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// file size in number of bytes
	Size float64 `protobuf:"fixed64,4,opt,name=size,proto3" json:"size,omitempty"`
	// file MIME type
	Mime string `protobuf:"bytes,5,opt,name=mime,proto3" json:"mime,omitempty"`
	// SHA256/MD5 digest of the file
	Sha256 string `protobuf:"bytes,6,opt,name=sha256,proto3" json:"sha256,omitempty"`
	// UID of the organization which the file belongs to
	OrgUid string `protobuf:"bytes,7,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// file state
	State File_State_Enum `protobuf:"varint,8,opt,name=state,proto3,enum=annofeed.v1.File_State_Enum" json:"state,omitempty"`
	// UID of the creator
	CreatorUid string `protobuf:"bytes,9,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	// file creation time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *File) Reset() {
	*x = File{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_file_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *File) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*File) ProtoMessage() {}

func (x *File) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_file_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use File.ProtoReflect.Descriptor instead.
func (*File) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_file_proto_rawDescGZIP(), []int{12}
}

func (x *File) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *File) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *File) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *File) GetSize() float64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *File) GetMime() string {
	if x != nil {
		return x.Mime
	}
	return ""
}

func (x *File) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *File) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *File) GetState() File_State_Enum {
	if x != nil {
		return x.State
	}
	return File_State_unspecified
}

func (x *File) GetCreatorUid() string {
	if x != nil {
		return x.CreatorUid
	}
	return ""
}

func (x *File) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type File_State struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *File_State) Reset() {
	*x = File_State{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_file_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *File_State) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*File_State) ProtoMessage() {}

func (x *File_State) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_file_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use File_State.ProtoReflect.Descriptor instead.
func (*File_State) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_file_proto_rawDescGZIP(), []int{12, 0}
}

var File_annofeed_v1_file_proto protoreflect.FileDescriptor

var file_annofeed_v1_file_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65,
	0x65, 0x64, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb8, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x61, 0x72, 0x74, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x61, 0x72, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x6d, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x69, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f,
	0x75, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69,
	0x64, 0x3a, 0x20, 0xba, 0x47, 0x1d, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0xba, 0x01, 0x05, 0x70, 0x61, 0x72, 0x74, 0x73, 0xba, 0x01, 0x04, 0x6d,
	0x69, 0x6d, 0x65, 0x22, 0xe9, 0x01, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x69,
	0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x25, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x73, 0x12,
	0x40, 0x0a, 0x0e, 0x75, 0x72, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0c, 0x75, 0x72, 0x6c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x41,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e,
	0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x45, 0x78, 0x69, 0x73,
	0x74, 0x69, 0x6e, 0x67, 0x3a, 0x29, 0xba, 0x47, 0x26, 0xba, 0x01, 0x04, 0x66, 0x69, 0x6c, 0x65,
	0xba, 0x01, 0x0b, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0xba, 0x01,
	0x0e, 0x75, 0x72, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x22,
	0x67, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x73, 0x3a, 0x13, 0xba, 0x47, 0x10, 0xba, 0x01, 0x04, 0x66, 0x69, 0x6c, 0x65, 0xba,
	0x01, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22, 0x59, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x46,
	0x69, 0x6c, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x52, 0x4c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x61, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x61,
	0x72, 0x74, 0x73, 0x22, 0x9f, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x52, 0x4c, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1f,
	0x0a, 0x0b, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x73, 0x12,
	0x40, 0x0a, 0x0e, 0x75, 0x72, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0c, 0x75, 0x72, 0x6c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x41,
	0x74, 0x3a, 0x22, 0xba, 0x47, 0x1f, 0xba, 0x01, 0x0b, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f,
	0x75, 0x72, 0x6c, 0x73, 0xba, 0x01, 0x0e, 0x75, 0x72, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x73, 0x5f, 0x61, 0x74, 0x22, 0x58, 0x0a, 0x17, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x46,
	0x69, 0x6c, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa,
	0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b,
	0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x74, 0x61,
	0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x65, 0x74, 0x61, 0x67, 0x73, 0x22,
	0x3c, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x39, 0x0a,
	0x0e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42,
	0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31,
	0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0xc4, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73,
	0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x06,
	0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x17,
	0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65,
	0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x6e, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x72, 0x69, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x75, 0x72, 0x69, 0x73, 0x22,
	0x7f, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x27, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69,
	0x6c, 0x65, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x3a, 0x1d, 0xba, 0x47, 0x1a, 0xba, 0x01, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0xba, 0x01,
	0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x22, 0x55, 0x0a, 0x10, 0x53, 0x68, 0x61, 0x72, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x22, 0x75, 0x0a, 0x0e, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x39, 0x0a, 0x0a, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x73, 0x41, 0x74, 0x3a, 0x16, 0xba, 0x47, 0x13, 0xba, 0x01, 0x03, 0x75, 0x72,
	0x6c, 0xba, 0x01, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x22, 0xce,
	0x03, 0x0a, 0x04, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x72, 0x69, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x69,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x69, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64, 0x12,
	0x3c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c,
	0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0x3d, 0x0a, 0x05, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x22, 0x34, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x75,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x75, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x10, 0x02, 0x3a, 0x45, 0xba, 0x47, 0x42, 0xba, 0x01, 0x03,
	0x75, 0x69, 0x64, 0xba, 0x01, 0x03, 0x75, 0x72, 0x69, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0xba, 0x01, 0x04, 0x73, 0x69, 0x7a, 0x65, 0xba, 0x01, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36,
	0xba, 0x01, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0xba, 0x01, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75,
	0x69, 0x64, 0xba, 0x01, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x32,
	0xc8, 0x06, 0x0a, 0x05, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x60, 0x0a, 0x0a, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65,
	0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65,
	0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x14, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0e, 0x3a, 0x01, 0x2a,
	0x22, 0x09, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x63, 0x0a, 0x0a, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x22, 0x22, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x32, 0x14, 0x2f, 0x76, 0x31, 0x2f,
	0x66, 0x69, 0x6c, 0x65, 0x73, 0x2f, 0x7b, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x75, 0x69, 0x64, 0x7d,
	0x12, 0x7a, 0x0a, 0x10, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x12, 0x24, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x1a, 0x1d, 0x2f,
	0x76, 0x31, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x66,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x2d, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x84, 0x01, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x52,
	0x4c, 0x73, 0x12, 0x25, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x52,
	0x4c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x52, 0x4c, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x23,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x69, 0x6c, 0x65,
	0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x2d, 0x75,
	0x72, 0x6c, 0x73, 0x12, 0x5d, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c,
	0x65, 0x12, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x11, 0x2a, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x2f, 0x7b, 0x75, 0x69,
	0x64, 0x7d, 0x12, 0x52, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x1b, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x11, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x22, 0x17, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x11, 0x12, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x73,
	0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x57, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69,
	0x6c, 0x65, 0x12, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x11, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x0b, 0x12, 0x09, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x12,
	0x69, 0x0a, 0x09, 0x53, 0x68, 0x61, 0x72, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x1d, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x68, 0x61, 0x72, 0x65, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a,
	0x3a, 0x01, 0x2a, 0x1a, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x2f, 0x7b,
	0x75, 0x69, 0x64, 0x7d, 0x2f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x42, 0x4a, 0x0a, 0x0b, 0x61, 0x6e,
	0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x39, 0x67, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77,
	0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e,
	0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_annofeed_v1_file_proto_rawDescOnce sync.Once
	file_annofeed_v1_file_proto_rawDescData = file_annofeed_v1_file_proto_rawDesc
)

func file_annofeed_v1_file_proto_rawDescGZIP() []byte {
	file_annofeed_v1_file_proto_rawDescOnce.Do(func() {
		file_annofeed_v1_file_proto_rawDescData = protoimpl.X.CompressGZIP(file_annofeed_v1_file_proto_rawDescData)
	})
	return file_annofeed_v1_file_proto_rawDescData
}

var file_annofeed_v1_file_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_annofeed_v1_file_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_annofeed_v1_file_proto_goTypes = []interface{}{
	(File_State_Enum)(0),             // 0: annofeed.v1.File.State.Enum
	(*CreateFileRequest)(nil),        // 1: annofeed.v1.CreateFileRequest
	(*CreateFileReply)(nil),          // 2: annofeed.v1.CreateFileReply
	(*UpdateFileRequest)(nil),        // 3: annofeed.v1.UpdateFileRequest
	(*GetFileUploadURLsRequest)(nil), // 4: annofeed.v1.GetFileUploadURLsRequest
	(*GetFileUploadURLsReply)(nil),   // 5: annofeed.v1.GetFileUploadURLsReply
	(*FinishFileUploadRequest)(nil),  // 6: annofeed.v1.FinishFileUploadRequest
	(*DeleteFileRequest)(nil),        // 7: annofeed.v1.DeleteFileRequest
	(*GetFileRequest)(nil),           // 8: annofeed.v1.GetFileRequest
	(*ListFileRequest)(nil),          // 9: annofeed.v1.ListFileRequest
	(*ListFileReply)(nil),            // 10: annofeed.v1.ListFileReply
	(*ShareFileRequest)(nil),         // 11: annofeed.v1.ShareFileRequest
	(*ShareFileReply)(nil),           // 12: annofeed.v1.ShareFileReply
	(*File)(nil),                     // 13: annofeed.v1.File
	(*File_State)(nil),               // 14: annofeed.v1.File.State
	(*timestamppb.Timestamp)(nil),    // 15: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),            // 16: google.protobuf.Empty
}
var file_annofeed_v1_file_proto_depIdxs = []int32{
	13, // 0: annofeed.v1.CreateFileReply.file:type_name -> annofeed.v1.File
	15, // 1: annofeed.v1.CreateFileReply.url_expires_at:type_name -> google.protobuf.Timestamp
	13, // 2: annofeed.v1.UpdateFileRequest.file:type_name -> annofeed.v1.File
	15, // 3: annofeed.v1.GetFileUploadURLsReply.url_expires_at:type_name -> google.protobuf.Timestamp
	13, // 4: annofeed.v1.ListFileReply.files:type_name -> annofeed.v1.File
	15, // 5: annofeed.v1.ShareFileReply.expires_at:type_name -> google.protobuf.Timestamp
	0,  // 6: annofeed.v1.File.state:type_name -> annofeed.v1.File.State.Enum
	15, // 7: annofeed.v1.File.created_at:type_name -> google.protobuf.Timestamp
	1,  // 8: annofeed.v1.Files.CreateFile:input_type -> annofeed.v1.CreateFileRequest
	3,  // 9: annofeed.v1.Files.UpdateFile:input_type -> annofeed.v1.UpdateFileRequest
	6,  // 10: annofeed.v1.Files.FinishFileUpload:input_type -> annofeed.v1.FinishFileUploadRequest
	4,  // 11: annofeed.v1.Files.GetFileUploadURLs:input_type -> annofeed.v1.GetFileUploadURLsRequest
	7,  // 12: annofeed.v1.Files.DeleteFile:input_type -> annofeed.v1.DeleteFileRequest
	8,  // 13: annofeed.v1.Files.GetFile:input_type -> annofeed.v1.GetFileRequest
	9,  // 14: annofeed.v1.Files.ListFile:input_type -> annofeed.v1.ListFileRequest
	11, // 15: annofeed.v1.Files.ShareFile:input_type -> annofeed.v1.ShareFileRequest
	2,  // 16: annofeed.v1.Files.CreateFile:output_type -> annofeed.v1.CreateFileReply
	13, // 17: annofeed.v1.Files.UpdateFile:output_type -> annofeed.v1.File
	16, // 18: annofeed.v1.Files.FinishFileUpload:output_type -> google.protobuf.Empty
	5,  // 19: annofeed.v1.Files.GetFileUploadURLs:output_type -> annofeed.v1.GetFileUploadURLsReply
	16, // 20: annofeed.v1.Files.DeleteFile:output_type -> google.protobuf.Empty
	13, // 21: annofeed.v1.Files.GetFile:output_type -> annofeed.v1.File
	10, // 22: annofeed.v1.Files.ListFile:output_type -> annofeed.v1.ListFileReply
	12, // 23: annofeed.v1.Files.ShareFile:output_type -> annofeed.v1.ShareFileReply
	16, // [16:24] is the sub-list for method output_type
	8,  // [8:16] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_annofeed_v1_file_proto_init() }
func file_annofeed_v1_file_proto_init() {
	if File_annofeed_v1_file_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_annofeed_v1_file_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_file_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateFileReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_file_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_file_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFileUploadURLsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_file_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFileUploadURLsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_file_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinishFileUploadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_file_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_file_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_file_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_file_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListFileReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_file_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShareFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_file_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShareFileReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_file_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*File); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_file_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*File_State); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_annofeed_v1_file_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_annofeed_v1_file_proto_goTypes,
		DependencyIndexes: file_annofeed_v1_file_proto_depIdxs,
		EnumInfos:         file_annofeed_v1_file_proto_enumTypes,
		MessageInfos:      file_annofeed_v1_file_proto_msgTypes,
	}.Build()
	File_annofeed_v1_file_proto = out.File
	file_annofeed_v1_file_proto_rawDesc = nil
	file_annofeed_v1_file_proto_goTypes = nil
	file_annofeed_v1_file_proto_depIdxs = nil
}
