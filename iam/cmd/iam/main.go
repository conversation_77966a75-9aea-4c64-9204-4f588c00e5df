package main

import (
	"flag"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"github.com/go-kratos/kratos/v2/config/env"
	"os"

	"iam/internal/conf"
	"iam/internal/data"
	"iam/pkg/keto"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	ilog "gitlab.rp.konvery.work/platform/pkg/log"
	"gitlab.rp.konvery.work/platform/pkg/otel"
)

var (
	// Name is the name of the compiled software.
	Name string = "iam"
	// flagconf is the config flag.
	flagconf string

	id, _ = os.Hostname()
)

func init() {
	flag.StringVar(&flagconf, "conf", "../../configs", "config path, eg: -conf config.yaml")
}

func newApp(logger log.Logger, hs *http.Server, gs *grpc.Server) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(conf.Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(
			hs,
			gs,
		),
	)
}

func main() {
	flag.Parse()
	c := config.New(
		config.WithSource(
			file.NewSource(flagconf),
			env.NewSource(),
		),
		// config.WithResolver(CustomResolver),
	)
	defer c.Close()

	if err := c.Load(); err != nil {
		panic(err)
	}
	// 打印整个配置树
	var merged map[string]interface{}
	if err := c.Scan(&merged); err != nil {
		panic(err)
	}
	spew.Dump("[DEBUG] All config after merge: ", merged)

	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}
	logger := ilog.GetLogger(&ilog.Config{
		Level:  bc.Otel.Log.Level,
		Format: bc.Otel.Log.Format,
	})
	log.SetLogger(logger)

	switch flag.Arg(0) {
	case "migrate":
		data.Migrate(bc.Data.Database, flag.Arg(1))
		return
	case "keto-add-ns": // add a namespace to keto
		ns := flag.Arg(1)
		if ns == "" {
			fmt.Println("lack of keto resource namespace, e.g. AnnoOrder")
			os.Exit(1)
		}
		km := newKetoMgr(&bc, logger)
		if err := km.InitNamespace(ns); err != nil {
			fmt.Println("failed to create namespace:", err)
			os.Exit(1)
		}
		km.Close()
		return
	case "create-all-policies": // recreate access policies for all users and orgnizations
		km := newKetoMgr(&bc, logger)
		if err := km.CreateAllPolicies(); err != nil {
			fmt.Println("failed to recreate policies:", err)
			os.Exit(1)
		}
		km.Close()
		return
	}

	initJWT(bc.Jwt)
	initAlisms(bc.Alisms)

	app, cleanup, err := wireApp(bc.Server, bc.Data, bc.DangerTest, logger)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	keto.Init(&keto.Config{
		ReadAddr:  bc.Keto.ReadAddr,
		WriteAddr: bc.Keto.WriteAddr,
		Logger:    logger,
	})

	otelcfg := otel.NewOtelCfg(bc.Otel.GetLog(), bc.Otel.GetMetrics(), bc.Otel.GetTracing())
	shutdown, err := otel.InitOtel(Name, conf.Version, otelcfg, logger)
	if err != nil {
		panic(err)
	}
	defer shutdown()

	// workflow.Init(bc.Temporal)
	// if !bc.Temporal.DisableWorker {
	// 	go workflow.StartWorker(app.activities)
	// }

	// start and wait for stop signal
	log.Infof("[main] current version: %s", conf.Version)
	if err := app.Run(); err != nil {
		panic(err)
	}
}
