// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: anno/v1/labelwidget.proto

package anno

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationLabelwidgetsListLabelwidget = "/anno.v1.Labelwidgets/ListLabelwidget"

type LabelwidgetsHTTPServer interface {
	ListLabelwidget(context.Context, *ListLabelwidgetRequest) (*ListLabelwidgetReply, error)
}

func RegisterLabelwidgetsHTTPServer(s *http.Server, srv LabelwidgetsHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/labelwidgets", _Labelwidgets_ListLabelwidget0_HTTP_Handler(srv))
}

func _Labelwidgets_ListLabelwidget0_HTTP_Handler(srv LabelwidgetsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListLabelwidgetRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLabelwidgetsListLabelwidget)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListLabelwidget(ctx, req.(*ListLabelwidgetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListLabelwidgetReply)
		return ctx.Result(200, reply)
	}
}

type LabelwidgetsHTTPClient interface {
	ListLabelwidget(ctx context.Context, req *ListLabelwidgetRequest, opts ...http.CallOption) (rsp *ListLabelwidgetReply, err error)
}

type LabelwidgetsHTTPClientImpl struct {
	cc *http.Client
}

func NewLabelwidgetsHTTPClient(client *http.Client) LabelwidgetsHTTPClient {
	return &LabelwidgetsHTTPClientImpl{client}
}

func (c *LabelwidgetsHTTPClientImpl) ListLabelwidget(ctx context.Context, in *ListLabelwidgetRequest, opts ...http.CallOption) (*ListLabelwidgetReply, error) {
	var out ListLabelwidgetReply
	pattern := "/v1/labelwidgets"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLabelwidgetsListLabelwidget))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
