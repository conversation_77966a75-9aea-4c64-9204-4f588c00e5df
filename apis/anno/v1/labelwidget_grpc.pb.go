// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: anno/v1/labelwidget.proto

package anno

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Labelwidgets_ListLabelwidget_FullMethodName = "/anno.v1.Labelwidgets/ListLabelwidget"
)

// LabelwidgetsClient is the client API for Labelwidgets service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LabelwidgetsClient interface {
	ListLabelwidget(ctx context.Context, in *ListLabelwidgetRequest, opts ...grpc.CallOption) (*ListLabelwidgetReply, error)
}

type labelwidgetsClient struct {
	cc grpc.ClientConnInterface
}

func NewLabelwidgetsClient(cc grpc.ClientConnInterface) LabelwidgetsClient {
	return &labelwidgetsClient{cc}
}

func (c *labelwidgetsClient) ListLabelwidget(ctx context.Context, in *ListLabelwidgetRequest, opts ...grpc.CallOption) (*ListLabelwidgetReply, error) {
	out := new(ListLabelwidgetReply)
	err := c.cc.Invoke(ctx, Labelwidgets_ListLabelwidget_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LabelwidgetsServer is the server API for Labelwidgets service.
// All implementations must embed UnimplementedLabelwidgetsServer
// for forward compatibility
type LabelwidgetsServer interface {
	ListLabelwidget(context.Context, *ListLabelwidgetRequest) (*ListLabelwidgetReply, error)
	mustEmbedUnimplementedLabelwidgetsServer()
}

// UnimplementedLabelwidgetsServer must be embedded to have forward compatible implementations.
type UnimplementedLabelwidgetsServer struct {
}

func (UnimplementedLabelwidgetsServer) ListLabelwidget(context.Context, *ListLabelwidgetRequest) (*ListLabelwidgetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLabelwidget not implemented")
}
func (UnimplementedLabelwidgetsServer) mustEmbedUnimplementedLabelwidgetsServer() {}

// UnsafeLabelwidgetsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LabelwidgetsServer will
// result in compilation errors.
type UnsafeLabelwidgetsServer interface {
	mustEmbedUnimplementedLabelwidgetsServer()
}

func RegisterLabelwidgetsServer(s grpc.ServiceRegistrar, srv LabelwidgetsServer) {
	s.RegisterService(&Labelwidgets_ServiceDesc, srv)
}

func _Labelwidgets_ListLabelwidget_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLabelwidgetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LabelwidgetsServer).ListLabelwidget(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Labelwidgets_ListLabelwidget_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LabelwidgetsServer).ListLabelwidget(ctx, req.(*ListLabelwidgetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Labelwidgets_ServiceDesc is the grpc.ServiceDesc for Labelwidgets service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Labelwidgets_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "anno.v1.Labelwidgets",
	HandlerType: (*LabelwidgetsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListLabelwidget",
			Handler:    _Labelwidgets_ListLabelwidget_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "anno/v1/labelwidget.proto",
}
