from PIL import Image
import json
import os
import shutil
import sys
from pathlib import Path

import numpy as np
import pandas as pd
import yaml

sys.path.append('.')

from customer.common import tools
from scipy.spatial.transform import Rotation as R

camera_name_mapping = [
    "front_camera",
    "left_camera",
    "back_camera",
    "right_camera"
]


def load_camera_config(config_path):
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)


def load_ego_state(csv_path):
    return pd.read_csv(csv_path)


def create_target_structure(base_dir, collection_name, element_name):
    data_dir = Path(base_dir) / f"data/{collection_name}/{element_name}"
    data_dir.mkdir(parents=True, exist_ok=True)
    return data_dir


def convert_camera_files(source_dir, target_dir, frame_name, camera_config):
    camera_mapping = {
        "Ego_001_FrontCamera": "front_camera",
        "Ego_001_LeftCamera": "left_camera",
        "Ego_001_RightCamera": "right_camera",
        "Ego_001_BackCamera": "back_camera",
        "Ego_001_FrontLidar": "lidar"
    }

    for src_subdir, camera_name in camera_mapping.items():
        src_path = Path(source_dir) / src_subdir
        if not src_path.exists():
            continue

        matched_files = [file for file in src_path.iterdir() if file.stem == frame_name]
        if matched_files:
            file_extension = matched_files[0].suffix  # 保持原始后缀
            dst_file = target_dir / f"{camera_name}{file_extension}"
            shutil.copy(matched_files[0], dst_file)
        else:
            # print(f"No file found for frame '{frame_name}' in directory '{src_path}'. Creating placeholder image.")
            try:
                width, height = get_camera_image_size(camera_config, camera_name)
                placeholder_path = target_dir / f"{camera_name}.jpg"
                create_placeholder_image(placeholder_path, width, height)
            except Exception as e:
                print(f"Error creating placeholder image for {src_subdir}: {e}")


def get_camera_image_size(camera_config, ego_camera_name):
    for camera_name, camera_info in camera_config.items():
        # Map internal camera name to external camera name
        if camera_name == ego_camera_name:
            intrinsic_params = camera_info['camera_intrinsics']
            if not intrinsic_params:
                raise ValueError(f"Camera '{camera_name}' does not have 'camera_intrinsics' key.")

            image_size_x = intrinsic_params['image_size_x']
            image_size_y = intrinsic_params['image_size_y']
            if not image_size_x or not image_size_y:
                raise ValueError(f"Camera '{camera_name}' does not have valid 'image_size_x' or 'image_size_y' "
                                 f"parameters.")
            return image_size_x, image_size_y


def create_placeholder_image(target_path, width, height, color=(128, 128, 128)):
    """生成一个占位图片"""
    placeholder_image = Image.new('RGB', (width, height), color=color)
    placeholder_image.save(target_path)
    # print(f"Placeholder image created at: {target_path}")


def euler_to_quaternion(roll, pitch, yaw):
    """Convert Euler angles to quaternion (qx, qy, qz, qw)."""
    quaternion = R.from_euler('xyz', [roll, pitch, yaw], degrees=False).as_quat()
    return quaternion[0], quaternion[1], quaternion[2], quaternion[3]


def convert_pose_to_matrix(x, y, z, qx, qy, qz, qw):
    """ Converts pose (x, y, z, qx, qy, qz, qw) to a 4x4 transformation matrix """
    R_mat = R.from_quat([qw, qx, qy, qz]).as_matrix()  # 修正四元数顺序
    T = np.eye(4)
    T[:3, :3] = R_mat
    T[:3, 3] = [x, y, z]
    return T



def inverse_transform_matrix(T):
    """Compute the inverse of a 4x4 transformation matrix."""
    R = T[:3, :3]
    t = T[:3, 3]
    R_inv = R.T
    t_inv = -R_inv @ t
    T_inv = np.eye(4)
    T_inv[:3, :3] = R_inv
    T_inv[:3, 3] = t_inv
    return T_inv


def interpolate_ego_state(ego_state, timestamp):
    """ 根据 timestamp 线性插值 ego_state """
    timestamps = ego_state["Timestamp"].values
    if timestamp in timestamps:
        return ego_state[ego_state["Timestamp"] == timestamp].iloc[0]

    # 选取最近的两个时间点进行插值
    idx = np.searchsorted(timestamps, timestamp)
    if idx == 0 or idx >= len(timestamps):
        return ego_state.iloc[0 if idx == 0 else -1]  # 取最接近的值

    t1, t2 = timestamps[idx - 1], timestamps[idx]
    w = (timestamp - t1) / (t2 - t1)  # 线性插值权重
    row1, row2 = ego_state.iloc[idx - 1], ego_state.iloc[idx]

    interpolated = row1 + w * (row2 - row1)  # 插值计算
    return interpolated


def create_params_json(target_dir, camera_config, ego_state, frame_name):
    if ego_state is None:
        return
    # formatted_timestamps = ego_state['Timestamp'].apply(lambda x: f"{x:.2f}")
    # pose_row = ego_state[formatted_timestamps == frame_name]
    pose_row = interpolate_ego_state(ego_state, float(frame_name))
    # print(pose_row)
    if pose_row is None or pose_row.empty:
        print(f"No matching pose data found for frame: {frame_name}")
        return
    row = pose_row.tolist()
    x, y, z = row[1], row[2], row[3]
    roll, pitch, yaw = np.radians(row[4]), np.radians(row[5]), np.radians(row[6])
    qx, qy, qz, qw = euler_to_quaternion(roll, pitch, yaw)

    pose_matrix = convert_pose_to_matrix(x, y, z, qx, qy, qz, qw)
    params = {
        "meta": {"version": "v2"},
        "lidar": {
            "viewpoint": [0, 0, 0, 0, 0, 0, 1],
            "pose": pose_matrix.flatten().tolist(),
            "transform": [],
            "timestamp": float(pose_row["Timestamp"])
        },
        "cameras": {}
    }

    # Add camera parameters from YAML
    for camera_name, camera_info in camera_config.items():
        # Map internal camera name to external camera name
        if camera_name not in camera_name_mapping:
            continue

        intrinsic_params = camera_info['camera_intrinsics']
        extrinsic_params = camera_info['camera_to_imu_extrinsics']

        # Extract intrinsic parameters
        intrinsic_matrix = [
            intrinsic_params['focal_length_x'], 0, intrinsic_params['principal_point_x'],
            0, intrinsic_params['focal_length_y'], intrinsic_params['principal_point_y'],
            0, 0, 1
        ]

        # Extract distortion coefficients
        distortion_coeffs = [
            0,  # Camera model (0: pinhole)
            intrinsic_params['distortion_coefficients']['k1'],
            intrinsic_params['distortion_coefficients']['k2'],
            intrinsic_params['distortion_coefficients']['p1'],
            intrinsic_params['distortion_coefficients']['p2'],
            intrinsic_params['distortion_coefficients']['k3']
        ]

        # Extract extrinsic parameters (rotation matrix and translation vector)
        rotation_matrix = np.array([
            extrinsic_params['rotation_matrix']['r11'], extrinsic_params['rotation_matrix']['r12'],
            extrinsic_params['rotation_matrix']['r13'],
            extrinsic_params['rotation_matrix']['r21'], extrinsic_params['rotation_matrix']['r22'],
            extrinsic_params['rotation_matrix']['r23'],
            extrinsic_params['rotation_matrix']['r31'], extrinsic_params['rotation_matrix']['r32'],
            extrinsic_params['rotation_matrix']['r33']
        ]).reshape(3, 3)
        translation_vector = np.array([
            extrinsic_params['translation_vector']['tx'],
            extrinsic_params['translation_vector']['ty'],
            extrinsic_params['translation_vector']['tz']
        ])

        # Combine rotation matrix and translation vector into a 4x4 matrix
        extrinsic_matrix = np.eye(4)
        extrinsic_matrix[:3, :3] = rotation_matrix
        extrinsic_matrix[:3, 3] = translation_vector

        # Add camera parameters to the params dictionary
        params['cameras'][camera_name] = {
            "extrinsic": extrinsic_matrix.flatten().tolist(),
            "intrinsic": intrinsic_matrix,
            "distortion": distortion_coeffs
        }
    with open(target_dir / "params.json", 'w') as f:
        json.dump(params, f, indent=4)


def run(input_dir, output_dir):
    input_dir = os.path.join(input_dir, 'trial_annotation_data')
    if not os.path.exists(input_dir):
        print('Input dir does not exist:', input_dir)
        return
    camera_config_path = os.path.join(input_dir, 'multi_camera_config.yaml')
    if not os.path.exists(camera_config_path):
        print('camera_config_path does not exist: ', camera_config_path)
        return
    camera_config = load_camera_config(camera_config_path)

    for collection in os.listdir(input_dir):
        if collection == ".DS_Store" or collection == "multi_camera_config.yaml":
            continue
        collection_path = os.path.join(input_dir, collection)
        if not os.path.isdir(collection_path):
            # print('collection path does not exist: ', collection_path)
            return
        ego_state_path = os.path.join(collection_path, "ego_state.csv")
        if not os.path.exists(ego_state_path):
            # print('ego_state path does not exist: ', ego_state_path)
            return
        ego_state = load_ego_state(ego_state_path)

        for elem_dir in os.listdir(collection_path):
            if elem_dir == ".DS_Store" or elem_dir == "ego_state.csv":
                continue
            elem_dir_path = os.path.join(collection_path, elem_dir)
            if not os.path.exists(elem_dir_path):
                print('elem_dir_path does not exist: ', elem_dir_path)
                return
            for elem_file in os.listdir(elem_dir_path):
                if elem_file == ".DS_Store":
                    continue
                frame_name = Path(elem_file).stem
                lidar_path = 'Ego_001_FrontLidar'
                pcd_file = Path(collection_path) / f"{lidar_path}" / f"{frame_name}.pcd"
                if not os.path.isfile(pcd_file):
                    # print('pcd_path does not exist: ', pcd_file)
                    continue
                data_dir = create_target_structure(output_dir, collection, frame_name)
                convert_camera_files(collection_path, data_dir, frame_name, camera_config)
                create_params_json(data_dir, camera_config, ego_state, frame_name)


'''
源数据格式如下
├── README.md
└── trial_annotation_data
    ├── ego_accept_merge
    │ ├── Ego_001_BackCamera
    │ │ └── 1249.65.jpg
    │ ├── Ego_001_FrontCamera
    │ │ └── 1249.65.jpg
    │ ├── Ego_001_FrontLidar
    │ │ └── 1249.65.pcd
    │ ├── Ego_001_LeftCamera
    │ │ └── 1249.65.jpg
    │ ├── Ego_001_RightCamera
    │ │ └── 1249.65.jpg
    │ └── ego_state.csv
    └── multi_camera_config.yaml
'''
if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
