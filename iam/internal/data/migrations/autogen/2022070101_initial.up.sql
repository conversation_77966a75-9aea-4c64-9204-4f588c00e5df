BEGIN;

CREATE TABLE authcodes
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    user_id     BIGINT       NOT NULL UNIQUE,
    sequence    INTEGER      NOT NULL DEFAULT 1,
    channel     VARCHAR(64)  NOT NULL, -- sms/email/...
    code        VARCHAR(64)  NOT NULL,
    status      SMALLINT     NOT NULL DEFAULT 0, -- 0-valid, 1-used, 2-expired
    expired_at  TIMESTAMPTZ  NOT NULL,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_authcodes_channel ON authcodes (channel) INCLUDE (status, expired_at);
CREATE INDEX idx_authcodes_created_at ON authcodes (created_at) INCLUDE (status, expired_at);
-- CREATE INDEX idx_authcodes_user_id_code ON authcodes (user_id, code) INCLUDE (sequence, status, expired_at, created_at);


-- front-end permissions
CREATE TABLE feperms
(
    id           BIGSERIAL    NOT NULL PRIMARY KEY,
    role         VARCHAR(64)  NOT NULL UNIQUE,
    perms        JSONB,
    created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);

CREATE TABLE perms
(
    id           BIGSERIAL    NOT NULL PRIMARY KEY,
    name         VARCHAR(64)  NOT NULL UNIQUE, -- IamUser.create
    class        VARCHAR(64)  NOT NULL,        -- IamUser
    created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_perms_class ON perms (class) INCLUDE (name);


CREATE TABLE roles
(
    id           BIGSERIAL    NOT NULL PRIMARY KEY,
    name         VARCHAR(64)  NOT NULL UNIQUE,
    display_name VARCHAR(64)  NOT NULL DEFAULT '',
    -- avatar       VARCHAR(4096) NOT NULL DEFAULT '',
    -- titles       JSONB        NOT NULL DEFAULT '{}'::JSONB, -- display name
    org_id       BIGINT,
    created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_roles_org_id ON roles (org_id);
CREATE INDEX idx_roles_display_name ON roles ((lower(display_name)));

-- predefined roles
INSERT INTO roles (name, display_name) VALUES ('root', 'root');
INSERT INTO roles (name, display_name) VALUES ('admin', 'admin');
INSERT INTO roles (name, display_name) VALUES ('service', 'service');
INSERT INTO roles (name, display_name) VALUES ('subAdmin', 'sub admin');
INSERT INTO roles (name, display_name) VALUES ('pm', 'Project Manager');
INSERT INTO roles (name, display_name) VALUES ('kam', 'Key Account Manager');
INSERT INTO roles (name, display_name) VALUES ('inspector', 'inspector');
INSERT INTO roles (name, display_name) VALUES ('owner', 'owner');
INSERT INTO roles (name, display_name) VALUES ('manager', 'manager');
INSERT INTO roles (name, display_name) VALUES ('member', 'member');

CREATE TABLE teams
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    -- uid         VARCHAR(32)  NOT NULL UNIQUE,
    name        VARCHAR(64)  NOT NULL,
    "desc"      VARCHAR(512) NOT NULL DEFAULT '',
    type        SMALLINT     NOT NULL DEFAULT 0, -- 0-unknown, 1-demander, 2-operator, 3-supplier
    province    VARCHAR(256) NOT NULL DEFAULT '',
    city        VARCHAR(256) NOT NULL DEFAULT '',
    avatar      VARCHAR(512) NOT NULL DEFAULT '',
    -- org_id      BIGINT       NOT NULL DEFAULT 0, -- ID of top-level team
    parent_id   BIGINT       NOT NULL DEFAULT 0, -- ID of upper-level team
    hierarchy   JSONB, -- team hierarchy: [team-level-1, team-level-2]
    status      SMALLINT     NOT NULL DEFAULT 0, -- 0-valid, 1-
    creator_id  BIGINT       NOT NULL DEFAULT 0,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    deleted_at  TIMESTAMPTZ
);
-- CREATE INDEX idx_teams_creator_id ON teams (creator_id) INCLUDE (status, deleted_at);
CREATE INDEX idx_teams_created_at ON teams (created_at) INCLUDE (status, deleted_at);
CREATE INDEX idx_teams_parent_id ON teams (parent_id) INCLUDE (status, deleted_at);
-- CREATE INDEX idx_teams_org_id ON teams (org_id) INCLUDE (status, deleted_at);
-- CREATE INDEX idx_teams_hierarchy ON users USING GIN (hierarchy); --INCLUDE (status, deleted_at);
CREATE INDEX idx_teams_name ON teams ((lower(name))) INCLUDE (status, deleted_at);

CREATE TABLE team_members
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    team_id     BIGINT       NOT NULL DEFAULT 0, -- ID of team
    user_id     BIGINT       NOT NULL DEFAULT 0, -- ID of user
    role        VARCHAR(32)  NOT NULL DEFAULT 'member', -- owner/manager/member
    status      SMALLINT     NOT NULL DEFAULT 0, -- 0-valid, 1-
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_team_members_team_id_user_id ON team_members (team_id, user_id) INCLUDE (role, status);
CREATE INDEX idx_team_members_user_id ON team_members (user_id) INCLUDE (team_id, role, status);

CREATE TABLE meta
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    name        VARCHAR(64)  NOT NULL UNIQUE,
    value       VARCHAR(512) NOT NULL,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);

-- make first manually created user the super user
INSERT INTO meta (name, value) VALUES ('autoCreateRoot', 'true');

CREATE TABLE users
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    -- uid         VARCHAR(32)  NOT NULL UNIQUE,
    -- account     VARCHAR(64)  NOT NULL UNIQUE,
    name        VARCHAR(64)  NOT NULL DEFAULT '',
    phone       VARCHAR(64)  UNIQUE,
    email       VARCHAR(128) UNIQUE,
    password    VARCHAR(256) NOT NULL DEFAULT '',
    avatar      VARCHAR(512) NOT NULL DEFAULT '',
    -- hierarchy   BIGINT[]     NOT NULL DEFAULT '{}', -- team hierarchy: [team-level-1, team-level-2]
    org_id      BIGINT       NOT NULL DEFAULT 0,
    -- work_team_id BIGINT      NOT NULL DEFAULT 0,
    role        VARCHAR(32)  NOT NULL, -- global role or orgnization role
    status      SMALLINT     NOT NULL DEFAULT 0, -- 0-valid, 1-
    province    VARCHAR(256) NOT NULL DEFAULT '',
    city        VARCHAR(256) NOT NULL DEFAULT '',
    gender      SMALLINT     NOT NULL DEFAULT 0, -- 0-unkown, 1-male, 2-female, 3-X
    birthday    TIMESTAMPTZ,
    agreement   INT          NOT NULL DEFAULT 0, -- version number of signed user agreement
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    deleted_at  TIMESTAMPTZ
);
CREATE INDEX idx_users_name ON users ((lower(name))) INCLUDE (status, deleted_at);
-- CREATE INDEX idx_users_hierarchy ON users USING GIN (hierarchy); --INCLUDE (status, deleted_at);
-- CREATE INDEX idx_users_hierarchy1 ON users ((hierarchy[1])) INCLUDE (status, deleted_at);
-- CREATE INDEX idx_users_hierarchy_last1 ON users ((hierarchy[array_upper(hierarchy, 1)])) INCLUDE (status, deleted_at);
CREATE INDEX idx_users_org_id ON users (org_id) INCLUDE (status, deleted_at);
-- CREATE INDEX idx_users_role ON users (role) INCLUDE (status, deleted_at);
CREATE INDEX idx_users_created_at ON users (created_at) INCLUDE (status, deleted_at);

-- service accounts for RPC usage
INSERT INTO users (id, name, role, gender, birthday, province, city, avatar)
  VALUES (13742, 'Anno', 'service', 0, '2022-02-24', '北京市', '北京市', '');

COMMIT;
