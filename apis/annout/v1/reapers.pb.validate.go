// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: annout/v1/reapers.proto

package annout

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	anno "gitlab.rp.konvery.work/platform/apis/anno/v1"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = anno.ExportOrderAnnosRequest_Option_Enum(0)
)

// Validate checks the field values on CreateReaperRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateReaperRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateReaperRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateReaperRequestMultiError, or nil if none found.
func (m *CreateReaperRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateReaperRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_CreateReaperRequest_LotUid_Pattern.MatchString(m.GetLotUid()) {
		err := CreateReaperRequestValidationError{
			field:  "LotUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for OrderUid

	if all {
		switch v := interface{}(m.GetConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateReaperRequestValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateReaperRequestValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateReaperRequestValidationError{
				field:  "Config",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOntologies()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateReaperRequestValidationError{
					field:  "Ontologies",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateReaperRequestValidationError{
					field:  "Ontologies",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOntologies()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateReaperRequestValidationError{
				field:  "Ontologies",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DataUid

	if len(errors) > 0 {
		return CreateReaperRequestMultiError(errors)
	}

	return nil
}

// CreateReaperRequestMultiError is an error wrapping multiple validation
// errors returned by CreateReaperRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateReaperRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateReaperRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateReaperRequestMultiError) AllErrors() []error { return m }

// CreateReaperRequestValidationError is the validation error returned by
// CreateReaperRequest.Validate if the designated constraints aren't met.
type CreateReaperRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateReaperRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateReaperRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateReaperRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateReaperRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateReaperRequestValidationError) ErrorName() string {
	return "CreateReaperRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateReaperRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateReaperRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateReaperRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateReaperRequestValidationError{}

var _CreateReaperRequest_LotUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on GetReaperRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetReaperRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReaperRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetReaperRequestMultiError, or nil if none found.
func (m *GetReaperRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReaperRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetReaperRequest_LotUid_Pattern.MatchString(m.GetLotUid()) {
		err := GetReaperRequestValidationError{
			field:  "LotUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetReaperRequestMultiError(errors)
	}

	return nil
}

// GetReaperRequestMultiError is an error wrapping multiple validation errors
// returned by GetReaperRequest.ValidateAll() if the designated constraints
// aren't met.
type GetReaperRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReaperRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReaperRequestMultiError) AllErrors() []error { return m }

// GetReaperRequestValidationError is the validation error returned by
// GetReaperRequest.Validate if the designated constraints aren't met.
type GetReaperRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReaperRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReaperRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReaperRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReaperRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReaperRequestValidationError) ErrorName() string { return "GetReaperRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetReaperRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReaperRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReaperRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReaperRequestValidationError{}

var _GetReaperRequest_LotUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on Reaper with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Reaper) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Reaper with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ReaperMultiError, or nil if none found.
func (m *Reaper) ValidateAll() error {
	return m.validate(true)
}

func (m *Reaper) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for State

	if len(errors) > 0 {
		return ReaperMultiError(errors)
	}

	return nil
}

// ReaperMultiError is an error wrapping multiple validation errors returned by
// Reaper.ValidateAll() if the designated constraints aren't met.
type ReaperMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReaperMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReaperMultiError) AllErrors() []error { return m }

// ReaperValidationError is the validation error returned by Reaper.Validate if
// the designated constraints aren't met.
type ReaperValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReaperValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReaperValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReaperValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReaperValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReaperValidationError) ErrorName() string { return "ReaperValidationError" }

// Error satisfies the builtin error interface
func (e ReaperValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReaper.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReaperValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReaperValidationError{}

// Validate checks the field values on ExportLotAnnosRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExportLotAnnosRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExportLotAnnosRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExportLotAnnosRequestMultiError, or nil if none found.
func (m *ExportLotAnnosRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ExportLotAnnosRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ExportLotAnnosRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := ExportLotAnnosRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for OrderUid

	if _, ok := anno.ExportOrderAnnosRequest_Option_Enum_name[int32(m.GetOption())]; !ok {
		err := ExportLotAnnosRequestValidationError{
			field:  "Option",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ExportLotAnnosRequestMultiError(errors)
	}

	return nil
}

// ExportLotAnnosRequestMultiError is an error wrapping multiple validation
// errors returned by ExportLotAnnosRequest.ValidateAll() if the designated
// constraints aren't met.
type ExportLotAnnosRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExportLotAnnosRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExportLotAnnosRequestMultiError) AllErrors() []error { return m }

// ExportLotAnnosRequestValidationError is the validation error returned by
// ExportLotAnnosRequest.Validate if the designated constraints aren't met.
type ExportLotAnnosRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExportLotAnnosRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExportLotAnnosRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExportLotAnnosRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExportLotAnnosRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExportLotAnnosRequestValidationError) ErrorName() string {
	return "ExportLotAnnosRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ExportLotAnnosRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExportLotAnnosRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExportLotAnnosRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExportLotAnnosRequestValidationError{}

var _ExportLotAnnosRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")
