// source: annostat/v1/lot.proto
package biz

import (
	"time"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/kid"
)

type LotState string

func (o LotState) IsFinal() bool  { return o == LotStateFinished || o == LotStateCanceled }
func (o LotState) String() string { return string(o) }

const (
	LotStateUnspecified  LotState = "unspecified"
	LotStateUnstart      LotState = "unstart" // wait for manual start
	LotStateInitializing LotState = "initializing"
	LotStateOngoing      LotState = "ongoing"
	LotStateFinished     LotState = "finished"
	LotStatePaused       LotState = "paused"
	LotStateCanceled     LotState = "canceled"
)

type Lot struct {
	ID            int64    `json:"id" gorm:"default:null"`
	Name          string   `json:"name" gorm:"default:null"`
	Type          string   `json:"type" gorm:"default:null"`
	OrderID       int64    `json:"order_id" gorm:"default:null"`
	DataUid       string   `json:"data_uid" gorm:"default:null"`
	DataType      string   `json:"data_type" gorm:"default:null"`
	DataSize      int64    `json:"data_size" gorm:"default:null"`
	Priority      int32    `json:"priority" gorm:"default:null"`
	PhaseCount    int      `json:"phase_count" gorm:"default:null"`
	IsFrameSeries bool     `json:"is_frame_series" gorm:"default:null"`
	State         LotState `json:"state" gorm:"default:null"`
	InsCnt        int64    `json:"ins_cnt" gorm:"default:null"`
	InsTotal      int64    `json:"ins_total" gorm:"default:null"` // include interpolated objects
	JobReady      bool     `json:"job_ready" gorm:"default:null"`
	JobCount      int      `json:"job_count" gorm:"default:null"` // major job count
	JobSize       int32    `json:"job_size" gorm:"default:null"`

	CreatorUid string    `json:"creator_uid" gorm:"default:null"`
	OrgUid     string    `json:"org_uid" gorm:"default:null"`
	ExpEndTime time.Time `json:"exp_end_time" gorm:"default:null"`
	FinishedAt time.Time `json:"finished_at" gorm:"default:null"`
	CreatedAt  time.Time `json:"created_at" gorm:"default:null"`
}

func (o *Lot) GetID() int64   { return o.ID }
func (o *Lot) GetUid() string { return kid.StringID(o.ID) }
func (o *Lot) UidFld() string { panic("code error") }

const LotTableName = "lots"

var (
	lot_ = field.RegObject(&Lot{})
	// LotUpdatableFlds = field.NewModel(lot_, "Name", "Desc", "Priority", "DataSize", "DataType", "OrderID",
	// 	"PhaseCount", "State", "InsCnt", "InsTotal", "Error", "HasError", "JobReady", "JobCount", "IsFrameSeries")

	// field name in snake case
	LotSfldID            = field.Sname(&lot_.ID)
	LotSfldName          = field.Sname(&lot_.Name)
	LotSfldType          = field.Sname(&lot_.Type)
	LotSfldOrderID       = field.Sname(&lot_.OrderID)
	LotSfldDataUid       = field.Sname(&lot_.DataUid)
	LotSfldPriority      = field.Sname(&lot_.Priority)
	LotSfldJobSize       = field.Sname(&lot_.JobSize)
	LotSfldPhaseCount    = field.Sname(&lot_.PhaseCount)
	LotSfldState         = field.Sname(&lot_.State)
	LotSfldDataSize      = field.Sname(&lot_.DataSize)
	LotSfldDataType      = field.Sname(&lot_.DataType)
	LotSfldInsCnt        = field.Sname(&lot_.InsCnt)
	LotSfldInsTotal      = field.Sname(&lot_.InsTotal)
	LotSfldJobReady      = field.Sname(&lot_.JobReady)
	LotSfldJobCount      = field.Sname(&lot_.JobCount)
	LotSfldCreatorUid    = field.Sname(&lot_.CreatorUid)
	LotSfldOrgUid        = field.Sname(&lot_.OrgUid)
	LotSfldExpEndTime    = field.Sname(&lot_.ExpEndTime)
	LotSfldIsFrameSeries = field.Sname(&lot_.IsFrameSeries)
	LotSfldCreatedAt     = field.Sname(&lot_.CreatedAt)
	LotSfldFinishedAt    = field.Sname(&lot_.FinishedAt)
)

type LotListFilter struct {
	TimeRange
	IDs         []int64
	OrgUids     []string
	CreatorUid  string
	NamePattern string
	Types       []string
	DataTypes   []string
	States      []LotState
	OrderID     int64

	BizgranteeUid  string // to check kam's grants
	SpecgranteeUid string // to check pm's grants

	ExecutorOrgs []string
	// ExecutorUids []string

	FilterByOrder ListFilter
}

func (o *LotListFilter) Apply(tx Tx) Tx {
	if o == nil {
		return tx
	}
	tbl := "lots."
	tx = ApplyFieldFilter(tx, tbl+"id", o.IDs)
	tx = ApplyFieldFilter(tx, tbl+LotSfldOrgUid, o.OrgUids)
	tx = ApplyFieldFilter(tx, tbl+LotSfldCreatorUid, o.CreatorUid)
	tx = ApplyFieldFilter(tx, tbl+LotSfldState, o.States)
	tx = ApplyFieldFilter(tx, tbl+LotSfldType, o.Types)
	tx = ApplyFieldFilter(tx, tbl+LotSfldDataType, o.DataTypes)
	tx = ApplyFieldFilter(tx, tbl+LotSfldOrderID, o.OrderID)
	tx = ApplyPatternFilter(tx, tbl+LotSfldName, o.NamePattern)
	tx = ApplyTimeRangeFilter(tx, tbl+LotSfldCreatedAt, &o.TimeRange)

	if len(o.ExecutorOrgs) > 0 {
		f := &JoinFilter{
			LeftModel:    &Lot{},
			LeftJoinFld:  "id",
			RightModel:   &Lotphase{},
			RightJoinFld: LotphaseSfldLotID,
			Filter:       &LotphaseListFilter{Execteams: o.ExecutorOrgs},
		}
		tx = f.Apply(tx)
	}
	// if len(o.ExecutorUids) > 0 {
	// 	f := &JoinFilter{
	// 		LeftModel:    &Lot{},
	// 		LeftJoinFld:  "id",
	// 		RightModel:   &Joblog{},
	// 		RightJoinFld: JoblogSfldLotID,
	// 		Filter:       &JoblogListFilter{OperatorUids: o.ExecutorUids},
	// 	}
	// 	tx = f.Apply(tx)
	// }

	if o.BizgranteeUid != "" { // check kam's grants: join on bizgrants table
		f := &JoinFilter{
			LeftModel:    &Lot{},
			LeftJoinFld:  LotSfldOrgUid,
			RightModel:   &Bizgrant{},
			RightJoinFld: BizgrantSfldOrgUid.String(),
			Filter:       &BizgrantFilter{GranteeUid: o.BizgranteeUid},
		}
		tx = f.Apply(tx)
	}
	if o.SpecgranteeUid != "" { // check pm's grants: join on specgrants table
		f := &JoinFilter{
			LeftModel:    &Lot{},
			LeftJoinFld:  LotSfldID,
			RightModel:   &Specgrant{},
			RightJoinFld: SpecgrantSfldItemID.String(),
			Filter: &SpecgrantFilter{
				GranteeUid: o.SpecgranteeUid,
				ItemType:   anno.Specgrant_ItemType_AnnoLot.String(), // AnnoLot
			},
		}
		tx = f.Apply(tx)
	}

	if o.FilterByOrder != nil {
		tx = tx.Joins("JOIN orders ON orders.id = lots." + LotSfldOrderID)
		tx = o.FilterByOrder.Apply(tx)
	}
	return tx
}

type LotsRepo repo.GenericRepo[Lot]

// type LotsBiz struct {
// 	repo   LotsRepo
// 	bgtask BackgroundTask
// 	log    *log.Helper
// }

// func NewLotsBiz(repo LotsRepo, bgtask BackgroundTask, logger log.Logger) *LotsBiz {
// 	return &LotsBiz{repo: repo, bgtask: bgtask, log: log.NewHelper(logger)}
// }
