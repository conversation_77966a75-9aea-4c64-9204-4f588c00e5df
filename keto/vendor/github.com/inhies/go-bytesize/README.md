ByteSize
========

Bytesize is a package for working with measurements of bytes. Using this package
allows you to easily add 100KB to 4928MB and get a nicely formatted string
representation of the result.

[![Go Reference](https://pkg.go.dev/badge/github.com/inhies/go-bytesize.svg)](https://pkg.go.dev/github.com/inhies/go-bytesize)
[![Build Status](https://travis-ci.org/inhies/go-bytesize.png)](https://travis-ci.org/inhies/go-bytesize)
[![Coverage Status](https://coveralls.io/repos/inhies/go-bytesize/badge.svg?branch=master&service=github)](https://coveralls.io/github/inhies/go-bytesize?branch=master)

Usage
-----

Check the built in documentation for examples using the godoc link above or by
running godoc locally. 
