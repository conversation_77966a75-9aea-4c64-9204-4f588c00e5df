import os
import shutil
import numpy as np
import sys
import copy


sys.path.append('.')
from customer.common import tools


camera_names = ['camera_frontmain', 'camera_frontwide', 'camera_leftfront', 'camera_leftrear', 'camera_rearmain',
                'camera_rightfront', 'camera_rightrear']

def construct_poses(label_file_dir):
    poses = {}
    for frame_dir in tools.listdir(label_file_dir, full_path=True, sort=True):
        pose_file = os.path.join(frame_dir, 'location.json')
        pose_data = tools.get_json_data(pose_file)
        pose = [pose_data['position']['x'], pose_data['position']['y'], pose_data['position']['z'],
                pose_data['quaternion']['x'], pose_data['quaternion']['y'], pose_data['quaternion']['z'], pose_data['quaternion']['w']]
        pose = tools.pose_to_mat(pose)
        poses[os.path.basename(frame_dir)] = pose.T.flatten().tolist()
    return poses


def construct_config(calibration_dir, out_config_file, label_file_dir):
    sensor_params = {}
    for camera_name in camera_names:
        intrinsic_data = tools.get_json_data(os.path.join(calibration_dir, f'{camera_name}-intrinsic.json'))
        intrinsic_params = intrinsic_data[f'{camera_name}-intrinsic']
        extrinsic_data = tools.get_json_data(os.path.join(calibration_dir, f'lidar_1-to-{camera_name}-extrinsic.json'))
        sensor_params[camera_name] = {
            "camera_model": "pinhole",
            "extrinsic": extrinsic_data[f'lidar_1-to-{camera_name}-extrinsic'],
            "fx": intrinsic_params['cam_K'][0][0],
            "fy": intrinsic_params['cam_K'][1][0],
            "cx": intrinsic_params['cam_K'][0][2],
            "cy": intrinsic_params['cam_K'][1][2],
            "k1": intrinsic_params['cam_dist'][0],
            "k2": intrinsic_params['cam_dist'][1],
            "k3": intrinsic_params['cam_dist'][4],
            "p1": intrinsic_params['cam_dist'][2],
            "p2": intrinsic_params['cam_dist'][3],
        }
    poses = construct_poses(label_file_dir)
    config = {
        "camera": {
            "camera_frontmain": "camera_frontmain",
            "camera_frontwide": "camera_frontwide",
            "camera_leftfront": "camera_leftfront",
            "camera_leftrear": "camera_leftrear",
            "camera_rearmain": "camera_rearmain",
            "camera_rightfront": "camera_rightfront",
            "camera_rightrear": "camera_rightrear"
        },
        "data_type": "fusion_pointcloud",
        "sensor_params": sensor_params,
        "poses": poses
    }
    tools.write_json_file(config, out_config_file, indent=4)


def run(input_dir, output_dir):
    calibration_dir = os.path.join(input_dir, 'calibration')
    label_file_dir = os.path.join(input_dir, 'label_file')
    out_lidar_dir = os.path.join(output_dir, 'lidar')
    out_camera_dir = os.path.join(output_dir, 'camera')
    os.mkdir(out_lidar_dir)
    os.mkdir(out_camera_dir)
    for camera_name in camera_names:
        os.mkdir(os.path.join(out_camera_dir, camera_name))
    for frame_dir in tools.listdir(label_file_dir, full_path=True):
        frame_name = os.path.basename(frame_dir)
        for camera_name in camera_names:
            img_src = os.path.join(frame_dir, f'{camera_name}-{frame_name}.jpg')
            img_dst = os.path.join(out_camera_dir, camera_name, f'{frame_name}.jpg')
            shutil.copyfile(img_src, img_dst)
        lidar_src = os.path.join(frame_dir, f'lidar_1-{frame_name}.pcd')
        lidar_dst = os.path.join(out_lidar_dir, f'{frame_name}.pcd')
        shutil.copyfile(lidar_src, lidar_dst)
    out_config_file = os.path.join(output_dir, 'config.json')
    construct_config(calibration_dir, out_config_file, label_file_dir)


if __name__ == "__main__":
    args = tools.get_args()
    if args.input == "../../input/test":
        args.input = "/Users/<USER>/Downloads/华为/试标数据/高速"
        args.out = "/Users/<USER>/Downloads/华为/试标数据/高速_out"
    tools.check_dir(args.out)
    run(input_dir=args.input, output_dir=args.out)
