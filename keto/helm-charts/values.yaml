# Default values for keto.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
# -- Number of replicas in deployment
replicaCount: 1

image:
  # -- <PERSON>y <PERSON> image
  repository: artifactory.rp.konvery.work/docker/keto
  # -- Default image pull policy
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  # -- Ory <PERSON> version
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # -- Specifies whether a service account should be created
  create: true
  # -- Annotations to add to the service account
  annotations: {}
  # -- The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

# -- Default security context configuration
securityContext:
  capabilities:
    drop:
      - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 100
  allowPrivilegeEscalation: false
  privileged: false

# -- Values for initialization job
job:
  # -- If you do want to specify annotations, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'annotations:'.
  annotations:
    helm.sh/hook-weight: "1"
    helm.sh/hook: "pre-install, pre-upgrade"
    helm.sh/hook-delete-policy: "before-hook-creation,hook-succeeded"
  # kubernetes.io/ingress.class: nginx
  # kubernetes.io/tls-acme: "true"

  # -- If you want to add extra sidecar containers.
  extraContainers: ""
  # extraContainers: |
  #  - name: ...
  #    image: ...

  # -- If you want to add extra init containers.
  extraInitContainers: ""
  # extraInitContainers: |
  #  - name: ...
  #    image: ...

  # -- Array of extra envs to be passed to the job. This takes precedence over deployment variables. Kubernetes format is expected
  # - name: FOO
  #   value: BAR
  extraEnv: []

  # -- Node labels for pod assignment.
  nodeSelector: {}
  # If you do want to specify node labels, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'nodeSelector:'.
  #   foo: bar

  # -- If you want to add lifecycle hooks.
  lifecycle: ""
  # lifecycle: |
  #   preStop:
  #     exec:
  #       command: [...]

  # -- Set automounting of the SA token
  automountServiceAccountToken: true

  # -- Set sharing process namespace
  shareProcessNamespace: false

  # -- Specify the serviceAccountName value.
  # In some situations it is needed to provides specific permissions to Hydra deployments
  # Like for example installing Hydra on a cluster with a PosSecurityPolicy and Istio.
  # Uncoment if it is needed to provide a ServiceAccount for the Hydra deployment.
  serviceAccount:
    # -- Specifies whether a service account should be created
    create: true
    # -- Annotations to add to the service account
    annotations:
      helm.sh/hook-weight: "0"
      helm.sh/hook: "pre-install, pre-upgrade"
      helm.sh/hook-delete-policy: "before-hook-creation"
    # -- The name of the service account to use. If not set and create is true, a name is generated using the fullname template
    name: ""

  # -- Specify pod metadata, this metadata is added directly to the pod, and not higher objects
  podMetadata:
    # -- Extra pod level labels
    labels: {}
    # -- Extra pod level annotations
    annotations: {}

  spec:
    # -- Set job back off limit
    backoffLimit: 10

# -- Ingress definitions
ingress:
  read:
    enabled: false
    className: ""
    annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
    hosts:
      - host: chart-example.local
        paths:
          - path: /read
            pathType: Prefix
    tls: []
    #  - secretName: chart-example-tls
    #    hosts:
    #      - chart-example.local
  write:
    enabled: false
    className: ""
    annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
    hosts:
      - host: chart-example.local
        paths:
          - path: /write
            pathType: Prefix
    tls: []
    #  - secretName: chart-example-tls
    #    hosts:
    #      - chart-example.local

# -- Service configurations
service:
  # -- Read service
  read:
    enabled: true
    type: ClusterIP
    name: grpc-read
    port: 80
  # -- Write service
  write:
    enabled: true
    type: ClusterIP
    name: grpc-write
    port: 80
  # -- Metrics service
  metrics:
    enabled: false
    type: ClusterIP
    name: http-metrics
    port: 80
    annotations: {}

# -- Extra services to be deployed
extraServices: {}

# -- Secret management
secret:
  # -- Switch to false to prevent creating the secret
  enabled: true
  # -- Provide custom name of existing secret, or custom name of secret to be created
  nameOverride: ""
  # nameOverride: "myCustomSecret"
  # -- Annotations to be added to secret. Annotations are added only when secret is being created. Existing secret will not be modified.
  secretAnnotations:
    # Create the secret before installation, and only then. This saves the secret from regenerating during an upgrade
    # pre-upgrade is needed to upgrade from 0.7.0 to newer. Can be deleted afterwards.
    helm.sh/hook-weight: "0"
    helm.sh/hook: "pre-install, pre-upgrade"
    helm.sh/hook-delete-policy: "before-hook-creation"
    helm.sh/resource-policy: "keep"
  # -- switch to false to prevent checksum annotations being maintained and propogated to the pods
  hashSumEnabled: true

# -- Main application config.
keto:
  # -- Enables database migration
  automigration:
    enabled: true
    # -- Configure the way to execute database migration. Possible values: job, initContainer
    # When set to job, the migration will be executed as a job on release or upgrade.
    # When set to initContainer, the migration will be executed when kratos pod is created
    # Defaults to job  
    type: job
    # -- Ability to override the entrypoint of the automigration container
    # (e.g. to source dynamic secrets or export environment dynamic variables)
    customCommand: []
    # -- Ability to override arguments of the entrypoint. Can be used in-depended of customCommand 
    # eg:
    # - sleep 5;
    #   - keto
    customArgs: []
  # -- Direct keto config. Full documentation can be found in https://www.ory.sh/keto/docs/reference/configuration
  config:
    serve:
      read:
        port: 4466
      write:
        port: 4467
      metrics:
        port: 4468
    namespaces:
      - id: 0
        name: sample
    dsn: memory

# -- Configure the probes for when the deployment is considered ready and ongoing health check
deployment:
  # -- DEPRECATED Set custom pod annotations
  podAnnotations: {}

  # -- Specify pod metadata, this metadata is added directly to the pod, and not higher objects
  podMetadata:
    # -- Extra pod level labels
    labels: {}
    # -- Extra pod level annotations
    annotations: {}

  # -- Set custom security context for pods
  podSecurityContext: {}
  # fsGroup: 2000

  # https://github.com/kubernetes/kubernetes/issues/57601
  automountServiceAccountToken: true

  livenessProbe:
    initialDelaySeconds: 30
    periodSeconds: 10
    failureThreshold: 5
  readinessProbe:
    initialDelaySeconds: 30
    periodSeconds: 10
    failureThreshold: 5

  # -- Add custom annotations to the deployment
  annotations: {}

  resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
    limits:
      cpu: 500m
  #   memory: 128Mi
    requests:
      cpu: 100m
  #   memory: 128Mi

  # -- Autoscaling for keto deployment
  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 100
    targetCPUUtilizationPercentage: 100
    # targetMemoryUtilizationPercentage: 80

  nodeSelector: {}

  # -- If you want to add extra sidecar containers.
  extraContainers: ""
  # extraContainers: |
  #  - name: ...
  #    image: ...

  # -- Array of extra Envs to be added to the deployment. K8s format expected
  # - name: FOO
  #   value: BAR
  extraEnv: []

  # -- Array of extra Volumes to be added to the deployment. K8s format expected
  # - name: my-volume
  #   secret:
  #     secretName: my-secret
  extraVolumes: []

  # -- Array of extra VolumeMounts to be added to the deployment. K8s format expected
  # - name: my-volume
  #   mountPath: /etc/secrets/my-secret
  #   readOnly: true
  extraVolumeMounts: []

  # -- If you want to add extra init containers.
  extraInitContainers: {}
  # extraInitContainers: |
  #  - name: ...
  #    image: ...

  # -- Extra labels to be added to the deployment, and pods. K8s object format expected
  # foo: bar
  # my.special.label/type: value
  extraLabels: {}

  # -- Extra ports to be exposed by the main deployment
  extraPorts: []

  tolerations: []

  affinity: {}

  # -- Configure pod topologySpreadConstraints.
  topologySpreadConstraints: []
  # - maxSkew: 1
  #   topologyKey: topology.kubernetes.io/zone
  #   whenUnsatisfiable: DoNotSchedule
  #   labelSelector:
  #     matchLabels:
  #       app.kubernetes.io/name: keto
  #       app.kubernetes.io/instance: keto

  # -- Parameters for the automigration initContainer
  automigration:
    # -- Array of extra envs to be passed to the initContainer. Kubernetes format is expected
    # - name: FOO
    #   value: BAR
    extraEnv: []    

# -- Watcher sidecar configuration
watcher:
  enabled: false
  image: oryd/k8s-toolbox:0.0.4
  # -- Path to mounted file, which wil be monitored for changes. eg: /etc/secrets/my-secret/foo
  mountFile: ""
  # -- Specify pod metadata, this metadata is added directly to the pod, and not higher objects
  podMetadata:
    # -- Extra pod level labels
    labels: {}
    # -- Extra pod level annotations
    annotations: {}
  # -- Label key used for managing applications
  watchLabelKey: 'ory.sh/watcher'

# -- PodDistributionBudget configuration
pdb:
  enabled: false
  spec:
    minAvailable: 1

# -- Parameters for the Prometheus ServiceMonitor objects.
# Reference: https://docs.openshift.com/container-platform/4.6/rest_api/monitoring_apis/servicemonitor-monitoring-coreos-com-v1.html
serviceMonitor:
  # -- HTTP scheme to use for scraping.
  scheme: http
  # -- Interval at which metrics should be scraped
  scrapeInterval: 60s
  # -- Timeout after which the scrape is ended
  scrapeTimeout: 30s
  # -- Provide additionnal labels to the ServiceMonitor ressource metadata
  labels: {}
  # -- TLS configuration to use when scraping the endpoint
  tlsConfig: {}

configmap:
  # -- switch to false to prevent checksum annotations being maintained and propogated to the pods
  hashSumEnabled: true
