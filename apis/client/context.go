package client

import (
	"context"
)

const (
	HeaderUser             = "x-md-User"
	HeaderUserUid          = "x-md-User-Uid"
	HeaderCookie           = "Cookie"
	HeaderAuthorization    = "Authorization"
	HeaderXmdAuthorization = "x-md-Authorization"
)

type (
	userContextKey         struct{}
	userIdentityContextKey struct{}

	// RPCIdentity holds either a user UID or a user's access token.
	// It is used to authenticate the user in RPC calls.
	RPCIdentity struct {
		Uid       string
		AuthToken string
	}
)

func (o *RPCIdentity) IsEmpty() bool {
	return o == nil || (o.Uid == "" && o.AuthToken == "")
}

var svcAccount *User

func InitSvcAccount(account string) {
	svcAccount = &User{
		Uid:  account,
		Name: account,
		Role: SysRoleService,
	}
}

// NewCtxWithUser wraps a user, and an optionally assume-by user in the context.
func NewCtxWithUser(ctx context.Context, user *User, assumeBy ...*User) context.Context {
	var by *User
	if len(assumeBy) > 0 {
		by = assumeBy[0]
	}
	return context.WithValue(ctx, userContextKey{}, &UserContext{User: user, AssumeBy: by})
}

// NewCtxUseSvcAccount wraps the configured service account in the context.
func NewCtxUseSvcAccount(ctx context.Context) context.Context {
	return NewCtxWithUser(ctx, svcAccount)
}

// UserFromCtx retrieves the user from the context.
func UserFromCtx(ctx context.Context) *UserContext {
	user, _ := ctx.Value(userContextKey{}).(*UserContext)
	return user
}

// NewCtxWithRPCIdentity returns a new context with the given id.
// The new context is suitale to use in a RPC request.
func NewCtxWithRPCIdentity(ctx context.Context, id *RPCIdentity) context.Context {
	return context.WithValue(ctx, userIdentityContextKey{}, id)
}

// RPCIdentityFromCtx retrieves the RPCIdentity from the context.
func RPCIdentityFromCtx(ctx context.Context) *RPCIdentity {
	id, _ := ctx.Value(userIdentityContextKey{}).(*RPCIdentity)
	return id
}
