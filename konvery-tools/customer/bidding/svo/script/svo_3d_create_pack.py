import os
import numpy as np
import sys
import shutil
import yaml
sys.path.append('.')
from customer.common import tools


camera_mapping = {
    'camera_01': '前视(左)',
    'camera_02': '前视(右)',
    'camera_03': '前左视',
    'camera_04': '前右视',
    'camera_05': '左侧后视',
    'camera_06': '右侧后视',
    'camera_07': '(后面)左侧前视',
    'camera_08': '(后面)右侧前视',
    'camera_09': '后视',
}


def construct_config(params_dir):
    sensor_params = dict()
    for camera_name in camera_mapping.keys():
        extrinsic_file = os.path.join(params_dir, f'{camera_name}_extrinsics.yaml')
        intrinsic_file = os.path.join(params_dir, f'{camera_name}_intrinsics.yaml')
        if os.path.exists(extrinsic_file):
            with open(extrinsic_file, 'r') as f:
                extrinsic_cfg = yaml.safe_load(f)
            with open(intrinsic_file, 'r') as f:
                intrinsic_cfg = yaml.safe_load(f)
            extrinsic = [
                extrinsic_cfg['transform']['translation']['x'],
                extrinsic_cfg['transform']['translation']['y'],
                extrinsic_cfg['transform']['translation']['z'],
                extrinsic_cfg['transform']['rotation']['x'],
                extrinsic_cfg['transform']['rotation']['y'],
                extrinsic_cfg['transform']['rotation']['z'],
                extrinsic_cfg['transform']['rotation']['w']
            ]
            extrinsic = tools.pose_to_mat(extrinsic)
            intrinsic = intrinsic_cfg['K']
            distortion = intrinsic_cfg['D']
            assert intrinsic_cfg['type'] == 'pinhole', 'only support pinhole model'
        else:
            extrinsic = np.eye(4)
            intrinsic = [0] * 16
            distortion = [0] * 8

        sensor_params[camera_name] = {
            'camera_model': 'pinhole',
            'extrinsic': np.linalg.inv(extrinsic).tolist(),
            "fx": intrinsic[0],
            "fy": intrinsic[4],
            "cx": intrinsic[2],
            "cy": intrinsic[5],
            "k1": distortion[0],
            "k2": distortion[1],
            "p1": distortion[2],
            "p2": distortion[3],
            "k3": distortion[4],
        }
    config = {
        "camera": camera_mapping,
        "data_type": "fusion_pointcloud",
        "sensor_params": sensor_params
    }
    return config


def run(input_dir, output_dir):
    out_clip_dir = os.path.join(output_dir, os.path.basename(input_dir))
    os.mkdir(out_clip_dir)
    tools.check_dir(out_clip_dir)
    out_lidar_dir = os.path.join(out_clip_dir, 'lidar')
    os.mkdir(out_lidar_dir)
    out_camera_dir = os.path.join(out_clip_dir, 'camera')
    os.mkdir(out_camera_dir)
    pcds = sorted(tools.get_file_by_extension(os.path.join(input_dir, '雷达'), '.pcd'), key=lambda x: x.name)
    cameras_imgs = dict()
    for camera_name, camera_dir_name in camera_mapping.items():
        cam_imgs = tools.get_file_by_extension(os.path.join(input_dir, camera_dir_name), '.png')
        cam_imgs = sorted(cam_imgs, key=lambda x: x.name)
        assert len(cam_imgs) == len(pcds), 'pcd and image number not match'
        cameras_imgs[camera_name] = cam_imgs
        os.mkdir(os.path.join(out_camera_dir, camera_name))
    for idx, pcd in enumerate(pcds):
        shutil.copyfile(pcd, os.path.join(out_lidar_dir, pcd.name))
        for camera_name in cameras_imgs:
            shutil.copyfile(cameras_imgs[camera_name][idx], os.path.join(out_camera_dir, camera_name, pcd.name.replace('.pcd', '.png')))
    config = construct_config(os.path.join(input_dir, 'params'))
    tools.write_json_file(config, os.path.join(out_clip_dir, 'config.json'), indent=4, ensure_ascii=False)


"""智控&恺望数据试标群"""
if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
