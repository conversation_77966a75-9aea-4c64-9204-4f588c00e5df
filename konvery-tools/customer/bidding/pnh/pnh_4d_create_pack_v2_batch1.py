import os
import sys
import shutil
import numpy as np
import copy
import math
from scipy.spatial.transform import Rotation as R
from datetime import datetime
sys.path.append('.')
from customer.common import tools
from pyproj import Transformer
from customer.common import pypcd
from customer.common.bin2pcd.pcdtool.all import write_pcd


date_format = '%Y%m%d%H%M%S%f'


def merge_pcds_to_one(input_pcds, lidar2worlds, output_pcd, has_intensity=False):
    new_pc_data = []
    for input_pcd, lidar2world in zip(input_pcds, lidar2worlds):
        pcd = pypcd.point_cloud_from_path(input_pcd)
        for row in pcd.pc_data:
            if row[4] != 0:
                continue
            if np.isnan(row[0]):
                continue
            point = np.array([row[0], row[1], row[2], 1])
            point = lidar2world @ point
            if has_intensity:
                point[3] = row[3]
            else:
                point = point[:3]
            new_pc_data.append(point)
    write_pcd(new_pc_data, output_pcd, has_intensity)


def get_nearest_frame(src, dsts):
    distance = math.inf
    target = None
    for dst in dsts:
        if abs(dst-src) < distance:
            distance = abs(dst-src)
            target = dst
    return target


def parse_params(params_file, camera_names):
    cameras = dict()
    params_data = tools.get_json_data(params_file)
    lidar2body = np.array(params_data['m1Lidar']['lidar_to_body_paradata']['data']).reshape(4, 4)
    for camera_name in camera_names:
        sensor_params = params_data[camera_name]
        camera2body = np.linalg.inv(np.array(sensor_params['camera_to_body_paradata']['data']).reshape(4, 4))
        camera_model_param = sensor_params['camera_model_param']
        if sensor_params['model_type'] == 'equidistant':
            distortion = [2, camera_model_param['k1'], camera_model_param['k2'], camera_model_param['k3'],
                          camera_model_param['k4']]
        elif sensor_params['model_type'] == 'radtan':
            distortion = [0, camera_model_param['k1'], camera_model_param['k2'], camera_model_param['p1'],
                          camera_model_param['p2'], camera_model_param['k3'], camera_model_param['k4'], 0, 0]
        else:
            raise ValueError(f'Unknown model_type: {sensor_params["model_type"]}')
        cameras[camera_name] = {
            'intrinsic': [
                camera_model_param['fx'], 0, camera_model_param['cx'],
                0, camera_model_param['fy'], camera_model_param['cy'],
                0, 0, 1
            ],
            'extrinsic': camera2body.flatten().tolist(),
            'distortion': distortion,
        }
    return cameras


def get_gps_data(gps_path):
     # 读取GPS文件
     gps_data = []
     with open(gps_path, 'r') as f:
         header = f.readline()
         for line in f:
             values = [float(x) for x in line.strip().rstrip(';').split(',')]  # 使用逗号分隔符
             gps_data.append(values)
     gps_data = np.array(gps_data)
     gps_timestamps = gps_data[:, 0] / 1e9  # 将GPS时间戳从纳秒转为秒
     return gps_data, gps_timestamps


def run(input_dir, output_dir):
    out_clip_dir = os.path.join(output_dir, os.path.basename(input_dir))
    os.mkdir(out_clip_dir)
    data_dir = os.path.join(input_dir, f'{os.path.basename(input_dir)}_1Hz')
    camera_dir = os.path.join(data_dir, 'camera')
    params_file = os.path.join(input_dir, 'params.txt')
    lidar_dir = os.path.join(data_dir, 'lidar')
    gps_file = '/Users/<USER>/Downloads/PNH环宇/2042_0816_07/2024_08_16_07/GPS/20240816172656_new.txt'
    gps_data, gps_timestamps = get_gps_data(gps_file)
    pose_file = '/Users/<USER>/Downloads/PNH环宇/2042_0816_07/pose_1214_600.txt'
    pcd_timestamp2pose_mat = dict()
    with open(pose_file, 'r') as f:
        header = f.readline()
        lines = f.readlines()
        for line in lines:
            line_parts = line.strip().replace(',', '').split(' ')
            closest_gps_idx = np.argmin(np.abs(gps_timestamps - float(line_parts[0])))
            pcd_timestamp2pose_mat[float(line_parts[0])] = tools.get_extrinsic_by_txyz_rxyz_opencv(
                np.array(line_parts[1:3] + [float(line_parts[3]) + 3.53900], dtype=np.float32),
                np.array(line_parts[4:7], dtype=np.float32)
            )
    camera_names = tools.listdir(camera_dir)
    cameras_params = parse_params(params_file, camera_names)
    pcds = tools.listdir(lidar_dir, full_path=True, sort=True)
    image_mapping = dict()
    for camera_name in camera_names:
        image_mapping[camera_name] = tools.listdir(os.path.join(camera_dir, camera_name), full_path=True, sort=True)
        assert len(pcds) == len(image_mapping[camera_name]), f'pcd and image_file mismatch, {len(pcds)}!= {len(image_mapping[camera_name])}'
    first_frame = None
    pose_mats = []
    for idx in range(0, len(pcds)):
        lidar_file = pcds[idx]
        pcd_datetime = os.path.basename(lidar_file).split('_')[0]
        pcd_timestamp = datetime.strptime(pcd_datetime, date_format).timestamp()
        nearest_pcd_timestamp = get_nearest_frame(pcd_timestamp, pcd_timestamp2pose_mat.keys())
        pose_mat = pcd_timestamp2pose_mat[nearest_pcd_timestamp]
        pose = tools.mat_to_pose(pose_mat)
        pose_mat_inv = np.linalg.inv(pose_mat)
        pose_mats.append(pose_mat)
        out_frame_dir = os.path.join(out_clip_dir, os.path.basename(lidar_file).split('.')[0])
        os.mkdir(out_frame_dir)
        if first_frame is None:
            first_frame = out_frame_dir
        for camera_name in camera_names:
            img_file = image_mapping[camera_name][idx]
            shutil.copyfile(img_file, os.path.join(out_frame_dir, f'{camera_name}.jpg'))
        new_cameras_params = copy.deepcopy(cameras_params)
        for camera_name in camera_names:
            sensor_params = np.array(new_cameras_params[camera_name]['extrinsic']).reshape(4, 4)
            new_cameras_params[camera_name]['extrinsic'] = (sensor_params @ pose_mat_inv).flatten().tolist()
        params = {
            "meta": {
                "version": "v2"
            },
            "lidar": {
                "viewpoint": pose,
            },
            "cameras": new_cameras_params,
        }
        tools.write_json_file(params, os.path.join(out_frame_dir, 'params.json'))
    # merge_pcds_to_one(pcds, pose_mats, os.path.join(first_frame, 'lidar.pcd'), has_intensity=True)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
