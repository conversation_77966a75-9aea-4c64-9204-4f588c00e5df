package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"runtime"

	"gitlab.rp.konvery.work/platform/pkg/download"
	"gitlab.rp.konvery.work/platform/pkg/k8s"
	"gitlab.rp.konvery.work/platform/pkg/progress"

	"k8s.io/apimachinery/pkg/runtime/schema"
)

func DebugSubcmd() {
	exit := true
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("panic:", err)
			buf := make([]byte, 1024)
			n := runtime.Stack(buf, false)
			buf = buf[:n]
			fmt.Println(string(buf))
		}
		if exit {
			os.Exit(0)
		}
	}()

	switch flag.Arg(0) {
	case "download":
		uri := flag.Arg(1)
		fmt.Println("downloading", uri)
		file, err := download.Download(context.Background(), &download.File{
			URI:    uri,
			Report: func(p *progress.Progress) { fmt.Print(".") },
		})
		if err != nil {
			fmt.Printf("failed to download file %v: %v\n", uri, err)
		} else {
			fmt.Println("file is saved to", file)
		}
	case "createjob":
		CreateK8sJob(flag.Arg(1))
	case "getjob":
		// TODO: make it work
		GetK8sJob(flag.Arg(1))
	case "sa":
		saName := flag.Arg(1)
		fmt.Println("get sa", saName)
		sa, err := k8s.GetServiceAccount(context.Background(), "", saName)
		if err != nil {
			fmt.Println(err)
			return
		}
		fmt.Println("sa GroupVersionKind:", sa.GroupVersionKind())
		sa.SetGroupVersionKind(schema.GroupVersionKind{Group: "core", Version: "v1", Kind: "ServiceAccount"})
		data, err := k8s.ToYAML(sa)
		if err != nil {
			fmt.Println("failed to marshal sa", err)
		} else {
			fmt.Printf("sa spec:\n%s\n", data)
		}
	case "sattl":
		saName := flag.Arg(1)
		sa, err := k8s.GetServiceAccount(context.Background(), "", saName)
		if err != nil {
			fmt.Println(err)
			return
		}

		ttl, err := k8s.GetServiceAccountTTL(context.Background(), sa)
		if err != nil {
			fmt.Println(err)
			return
		}
		fmt.Println("ttl:", ttl)
	case "refresh-sa":
		saName := flag.Arg(1)
		sa, err := k8s.GetServiceAccount(context.Background(), "", saName)
		if err != nil {
			fmt.Println(err)
			return
		}
		err = k8s.RefreshServiceAccountTTL(context.Background(), sa)
		if err != nil {
			fmt.Println(err)
			return
		}
		ttl, err := k8s.GetServiceAccountTTL(context.Background(), sa)
		if err != nil {
			fmt.Println(err)
			return
		}
		fmt.Println("new ttl:", ttl)
	default:
		exit = false
	}
}

func CreateK8sJob(fpath string) error {
	bs, err := os.ReadFile(fpath)
	if err != nil {
		fmt.Printf("failed to read file: %v: %v\n", fpath, err)
		return err
	}
	_, err = k8s.CreateJobFromYAML(context.Background(), "", bs)
	if err != nil {
		fmt.Printf("failed to create job: %v\n", err)
		return err
	}
	fmt.Println("created a k8s job")
	return nil
}

func GetK8sJob(name string) error {
	job, err := k8s.GetJob(context.Background(), "", name)
	if err != nil {
		fmt.Printf("failed to get job %v: %v\n", name, err)
		return err
	}

	status := "running"
	if k8s.IsJobComplete(job) {
		status = "complete"
	} else if k8s.IsJobFailed(job) {
		status = "failed"
	}
	fmt.Println("Job status:", status)

	job.SetGroupVersionKind(schema.GroupVersionKind{Group: "batch", Version: "v1", Kind: "Job"})
	data, err := k8s.ToYAML(job)
	if err != nil {
		fmt.Println("failed to marshal job", err)
	} else {
		fmt.Printf("job spec:\n%s\n", data)
	}
	return err
}
