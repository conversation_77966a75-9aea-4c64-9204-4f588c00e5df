// source: gitlab.rp.konvery.work/platform/apis/iam/v1/user.proto
package data

import (
	"context"
	"time"

	"iam/internal/biz"

	"gitlab.rp.konvery.work/platform/pkg/data"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type usersRepo struct {
	repo.GenericRepo[biz.User]
	data *Data
}

func NewUsersRepo(d *Data, logger log.Logger) biz.UsersRepo {
	return &usersRepo{GenericRepo: data.NewGenericRepo[biz.User](d, logger, biz.UserTableName), data: d}
}

func userOrgLoader(ctx context.Context, d *Data, user *biz.User) error {
	if user.OrgID == 0 {
		return nil
	}
	team, err := data.GetByID[biz.Team](ctx, d, user.OrgID)
	if err == nil {
		user.Org = team
	}
	return err
}

func (o *usersRepo) GetByID(ctx context.Context, id int64) (*biz.User, error) {
	return data.GetByID[biz.User](ctx, o.data, id, userOrgLoader)
}

// func (o *usersRepo) Get(ctx context.Context, uid string) (*biz.User, error) {
// 	return GetByUid(ctx, o.data, uid, &biz.User{}, userOrgLoader)
// }

func (o *usersRepo) DeleteByID(ctx context.Context, id int64) error {
	return data.Delete(ctx, o.data, &biz.User{ID: id})
}

// func (o *usersRepo) Delete(ctx context.Context, uid string) error {
// 	return Delete(ctx, o.data, &biz.User{Uid: uid})
// }

func (o *usersRepo) JoinOrg(ctx context.Context, orgID int64, role string, users ...int64) error {
	// do not overwrite roles of users who have system roles
	res := o.data.WithCtx(ctx).Model(&biz.User{}).Where("id IN ?", users).
		Where(biz.User_Role+" IN ?", biz.SysRoles()).
		Updates(map[string]any{
			biz.User_OrgID: orgID,
		})
	if res.Error != nil || int(res.RowsAffected) == len(users) {
		return Convert(&biz.User{}, res.Error)
	}

	// set roles of users who have no system roles
	err := o.data.WithCtx(ctx).Model(&biz.User{}).Where("id IN ?", users).
		Where(biz.User_Role+" NOT IN ?", biz.SysRoles()).
		Updates(map[string]any{
			biz.User_OrgID: orgID,
			biz.User_Role:  role,
		}).Error
	return Convert(&biz.User{}, err)
}

func (o *usersRepo) SetRole(ctx context.Context, role string, users ...int64) error {
	err := o.data.WithCtx(ctx).Model(&biz.User{}).
		Where("id IN ?", users).
		Updates(map[string]any{
			biz.User_Role: role,
		}).Error
	return Convert(&biz.User{}, err)
}

func (o *usersRepo) LeaveOrg(ctx context.Context, users ...int64) error {
	return o.JoinOrg(ctx, 0, biz.TeamRoleMember, users...)
}

func (o *usersRepo) DisbandOrg(ctx context.Context, orgID int64) error {
	// do not overwrite roles of users who have system roles
	err := o.data.WithCtx(ctx).Model(&biz.User{}).
		Where(biz.User_OrgID+" = ?", orgID).
		Where(biz.User_Role+" IN ?", biz.SysRoles()).
		Updates(map[string]any{
			biz.User_OrgID: 0,
		}).Error
	if err != nil {
		return Convert(&biz.User{}, err)
	}

	// set roles of users who have no system roles
	err = o.data.WithCtx(ctx).Model(&biz.User{}).
		Where(biz.User_OrgID+" = ?", orgID).
		Where(biz.User_Role+" NOT IN ?", biz.SysRoles()).
		Updates(map[string]any{
			biz.User_OrgID: 0,
			biz.User_Role:  biz.TeamRoleMember,
		}).Error
	return Convert(&biz.User{}, err)
}

func (o *usersRepo) GetBy(ctx context.Context, id_type, identity string) (*biz.User, error) {
	q := o.data.WithCtx(ctx)
	switch id_type {
	case biz.IDTypeUid:
		q = q.Where("id = ?", kid.ParseID(identity))
	case biz.IDTypePhone:
		q = q.Where(biz.User_Phone+" = ?", identity)
	case biz.IDTypeEmail:
		q = q.Where(biz.User_Email+" = ?", identity)
	default:
		return nil, errors.NewErrInvalidField(errors.WithFields("id_type"))
	}
	p := &biz.User{}
	err := q.First(p).Error
	if err != nil {
		return nil, Convert(p, err)
	}
	return p, nil
}

func (o *usersRepo) SaveAuthcode(ctx context.Context, p *biz.Authcode, minInterval time.Duration) (*biz.Authcode, error) {
	code, err := o.GetAuthcode(ctx, p.UserID, p.Receiver)
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, err
		}
		if p.Code == "" {
			p.Code = kid.NewAuthcode()
		}
		code, err = data.Create(ctx, o.data, p)
	} else {
		if time.Since(code.UpdatedAt) < minInterval {
			return nil, errors.NewErrResourceExhausted(errors.WithMessage("please retry later"))
		}
		p.ID = code.ID
		p.Sequence = code.Sequence + 1
		if p.Code == "" {
			p.Code = code.Code
		}
		code, err = data.Update(ctx, o.data, p, biz.AuthcodeUpdatableFlds, biz.AuthcodeUpdatableFlds.FieldMask())
	}
	if err != nil {
		return nil, err
	}
	return code, nil
}

func (o *usersRepo) GetAuthcode(ctx context.Context, userID int64, identity string) (*biz.Authcode, error) {
	code := &biz.Authcode{}
	q := o.data.WithCtx(ctx)
	if userID > 0 {
		q = q.Where(biz.Authcode_UserID+" = ?", userID)
	}
	if identity != "" {
		q = q.Or(biz.Authcode_Receiver+" = ?", identity)
	}
	if err := q.First(code).Error; err != nil {
		return nil, Convert(code, err)
	}
	return code, nil
}

func (o *usersRepo) UpdateAuthcode(ctx context.Context, p *biz.Authcode, fldMask *biz.FieldMask) (*biz.Authcode, error) {
	return data.Update(ctx, o.data, p, biz.AuthcodeUpdatableFlds, fldMask)
}

func (o *usersRepo) UpdateLastLoginTime(ctx context.Context, userID int64) error {
	err := o.data.WithCtx(ctx).Model(&biz.User{}).
		Where("id = ?", userID).
		Update("last_login_at", time.Now()).Error
	return Convert(&biz.User{}, err)
}
