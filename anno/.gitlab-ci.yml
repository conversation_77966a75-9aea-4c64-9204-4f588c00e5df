variables:
  REPO_NAME: sansheng-anno
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_VERIFY: 1
  DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  CI_ARTIFACTORY_REGISTRY: $CI_ARTIFACTORY_REGISTRY
  CI_ARTIFACTORY_DOCKER_REPO: $CI_ARTIFACTORY_DOCKER_REPO
  CI_ARTIFACTORY_GO_REPO: $CI_ARTIFACTORY_GO_REPO
  CI_ARTIFACTORY_HELM_REPO: $CI_ARTIFACTORY_HELM_REPO
  CI_ARTIFACTORY_USER: $CI_ARTIFACTORY_USER
  CI_ARTIFACTORY_TOKEN: $CI_ARTIFACTORY_TOKEN
  IMAGE_PREFIX: $CI_ARTIFACTORY_DOCKER_REPO/
  COMMIT_NAME: "${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}"
  DOCKER_TAG_LOGIC: '(if [ "$${CI_COMMIT_TAG}" == "" ]; then echo "$$COMMIT_NAME"; else echo "$${CI_COMMIT_TAG}"; fi);'
  GOPRIVATE: $CI_GOPRIVATE
  GOPROXY: $CI_GOPROXY

stages:
  - lint
  - test
  - build
  - deploy

.lint:
  stage: lint
  tags: ["k8s"]
  image: ${IMAGE_PREFIX}golangci/golangci-lint:latest
  script:
    - golangci-lint run -v

test:
  stage: test
  tags: ["k8s"]
  image: ${IMAGE_PREFIX}golang:1.22-alpine
  script:
    - sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
    - apk update && apk add --no-cache make git build-base
    - if [ -z $CI_COMMIT_TAG ]
    - then
    -   make build
    - else
    -   echo "current tag:$CI_COMMIT_TAG"
    - fi
    - go vet ./...
    - go test  -v -cover ./...
    - make migrations

build-docker:
  stage: build
  tags: ["k8s"]
  only: ["tags"]
  image: ${IMAGE_PREFIX}docker:latest
  services:
    - name: ${IMAGE_PREFIX}docker:dind
      alias: docker
  before_script:
    - until docker info; do sleep 1; done #https://gitlab.com/gitlab-org/gitlab-runner/-/issues/27384
    - docker login -u $CI_ARTIFACTORY_USER -p $CI_ARTIFACTORY_TOKEN $CI_ARTIFACTORY_REGISTRY
  script:
    - TAG=$(eval $DOCKER_TAG_LOGIC)
    - BUILD_ARGS="--build-arg GIT_TAG=$TAG --build-arg GOPROXY --build-arg GOPRIVATE --build-arg IMAGE_PREFIX"
    - IMAGE_TAG=$IMAGE_PREFIX$REPO_NAME:$TAG
    - docker build $BUILD_ARGS -t $IMAGE_TAG .
    - docker push $IMAGE_TAG

helm-charts:
  stage: deploy
  tags: ["k8s"]
  only: ["tags"]
  image:
    name: ${IMAGE_PREFIX}alpine/k8s:1.25.3
    entrypoint: [""]
  script:
    - cd helm-charts
    - echo "build helm charts Triggered by a tag $CI_COMMIT_TAG"
    - SEMANTIC_VERSION=$(echo $CI_COMMIT_TAG | sed 's/^v//')
    - sed -i "s/0.1.0/$SEMANTIC_VERSION/" Chart.yaml
    - helm package .
    - CHART_TAG=$REPO_NAME-$SEMANTIC_VERSION
    - curl --fail-with-body -u "$CI_ARTIFACTORY_USER:$CI_ARTIFACTORY_TOKEN" -T $CHART_TAG.tgz "$CI_ARTIFACTORY_HELM_REPO/$CHART_TAG.tgz"

deploy:
  stage: deploy
  tags: ["k8s"]
  when: manual
  script:
    - export
    - echo "deployed to development"
  environment:
    name: development
    url: https://anno.np.konvery.work
    kubernetes:
      namespace: development
