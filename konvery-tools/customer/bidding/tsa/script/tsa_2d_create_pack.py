import os
import shutil
import sys
sys.path.append('.')
from customer.common import tools


category_mapping = {
    "road_edge": "road_edge",

    # task_arrow_polygon
    "c_right": "c_right_arrow",
    "crosswalk": "crosswalk",
    "diamond_marking": "diamond_marking",
    "inverted_triangle_marking": "inverted_triangle_marking",
    "left": "left_arrow",
    "right": "right_arrow",
    "speed_bump": "speed_bump",
    'straight': "straight_arrow",
    "straight_left": "straight_left_arrow",
    "straight_right": "straight_right_arrow",
    # stop_line为单独类别
    'stopline': 'stop_line',

    # task_lane_divider
    "dash_lane": "dash_lane",
    "pending_transfer_area_dash": "pending_transfer_area_dash_lane",
    "solid_lane": "solid_lane",
    "wide_dash_lane": "wide_dash_lane",
    "wide_solid_lane": "wide_solid_lane"
}


def construct_pre_label(pre_label_file, out_pre_label_dir):
    res = {
        "label_meta": {"mark_status": 0, "global": {}},
        "labels": []
    }
    tools.write_json_file(res, os.path.join(out_pre_label_dir, 'rgb.json'))

    labels = []
    pre_label_data = tools.get_json_data(pre_label_file)
    for obj in pre_label_data['task_lane_divider']:
        label = {
            "class": 'class',
            "attrs": {
                "category": [category_mapping[obj['class']]],
                "color": [obj['color']]
            },
            "annotation": {
                "type": "line",
                "data": [{'x': pt[0], 'y': pt[1]} for pt in obj['point_uv']]
            }
        }
        labels.append(label)
    for obj in pre_label_data['task_road_edge']:
        label = {
            "class": 'road',
            "attrs": {
                "classification": [category_mapping[obj['class']]],
            },
            "annotation": {
                "type": "line",
                "data": [{'x': pt[0], 'y': pt[1]} for pt in obj['point_uv']]
            }
        }
        labels.append(label)
    for obj in pre_label_data['task_arrow_polygon']:
        if obj['class'] == 'stopline':
            label = {
                "class": 'stop_line',
                "attrs": {
                    "polyline": [category_mapping[obj['class']]]
                },
                "annotation": {
                    "type": "line",
                    "data": [{'x': pt[0], 'y': pt[1]} for pt in obj['point_uv']]
                }
            }
        else:
            label = {
                "class": 'Groundmarking',
                "attrs": {
                    "category": [category_mapping[obj['class']]]
                },
                "annotation": {
                    "type": "polygon",
                    "data": [{'x': pt[0], 'y': pt[1]} for pt in obj['point_uv']]
                }
            }
        labels.append(label)
    res['labels'] = labels
    tools.write_json_file(res, os.path.join(out_pre_label_dir, 'intensity.json'))


def run(input_dir, output_dir, pre_label_file):
    imgs = tools.get_file_by_extension(input_dir, '.jpg')
    imgs.sort(key=lambda x: x.name)
    intensity_imgs = [img for img in imgs if img.name == 'intensity.jpg']
    assert len(intensity_imgs) == 1, f'not find intensity img: {intensity_imgs}'
    for intensity_img in intensity_imgs:
        rgb_img = intensity_img.path.replace('intensity', 'rgb')
        if not os.path.exists(rgb_img):
            print(f'not find rgb img: {rgb_img}')
            continue
        out_clip_dir = os.path.join(output_dir, intensity_img.name.split('.')[0])
        os.mkdir(out_clip_dir)
        out_img_dir = os.path.join(out_clip_dir, 'img')
        os.mkdir(out_img_dir)
        out_pre_label_dir = os.path.join(out_clip_dir, 'pre_label')
        os.mkdir(out_pre_label_dir)
        shutil.copyfile(intensity_img, os.path.join(out_img_dir, 'intensity.jpg'))
        shutil.copyfile(rgb_img, os.path.join(out_img_dir, 'rgb.jpg'))
        construct_pre_label(pre_label_file, out_pre_label_dir)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    pre_label_file = '/Users/<USER>/Downloads/TSA/pre_label.json'
    run(args.input, args.out, pre_label_file)
