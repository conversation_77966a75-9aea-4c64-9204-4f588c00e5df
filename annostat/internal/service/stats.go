// source: gitlab.rp.konvery.work/platform/apis/annostat
package service

import (
	"context"
	"fmt"
	"time"

	"annostat/api/client"
	"annostat/internal/biz"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/apis/annostat/v1"
	"gitlab.rp.konvery.work/platform/apis/iam/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kmap"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/ktime"
	"gitlab.rp.konvery.work/platform/pkg/log"

	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func ToBizOrderFilter(d *annostat.GetOrderCountRequest_Filter) (f *biz.OrderListFilter) {
	// defer func() { f.Enforce() }()
	if d == nil {
		return &biz.OrderListFilter{}
	}

	f = &biz.OrderListFilter{
		OrgUids:   d.OwnerOrgs,
		TimeRange: ToBizTimeRange(d.TimeRange),
	}
	return
}

func ToBizLotFilter(d *annostat.LotFilter) (f *biz.LotListFilter) {
	// defer func() { f.Enforce() }()
	if d == nil {
		return &biz.LotListFilter{}
	}

	f = &biz.LotListFilter{
		IDs:          biz.GetIDs(d.LotUids),
		OrgUids:      d.OwnerOrgs,
		ExecutorOrgs: d.ExecutorOrgs,
		TimeRange:    ToBizTimeRange(d.TimeRange),
		// ExecutorUids: d.ExecutorUids,
	}
	for _, v := range d.LotTypes {
		if v != annostat.LotType_unspecified {
			f.Types = append(f.Types, v.String())
		}
	}
	for _, v := range d.DataTypes {
		if v != annostat.DataType_unspecified {
			f.DataTypes = append(f.DataTypes, v.String())
		}
	}
	return
}

func ToBizTimeRange(d *annostat.TimeRange) (t biz.TimeRange) {
	if d == nil {
		return
	}

	if d.From != nil {
		t.From = d.From.AsTime()
	}
	if d.To != nil {
		t.To = d.To.AsTime()
	}
	return
}

type StatsService struct {
	annostat.UnimplementedStatsServer

	jobRepo      biz.JobsRepo
	jobstatRepo  biz.JobstatsRepo
	lotRepo      biz.LotsRepo
	lotphaseRepo biz.LotphasesRepo
	orderRepo    biz.OrdersRepo
	prodRepo     biz.ProductionsRepo
	accuracyRepo biz.AccuraciesRepo
	log          *log.Helper
}

func NewStatsService(
	lotRepo biz.LotsRepo, lotphaseRepo biz.LotphasesRepo,
	jobRepo biz.JobsRepo, jobstatRepo biz.JobstatsRepo,
	orderRepo biz.OrdersRepo,
	prodRepo biz.ProductionsRepo, accuracyRepo biz.AccuraciesRepo,
	logger log.Logger,
) *StatsService {
	return &StatsService{
		lotRepo:      lotRepo,
		lotphaseRepo: lotphaseRepo,
		jobRepo:      jobRepo,
		jobstatRepo:  jobstatRepo,
		orderRepo:    orderRepo,
		prodRepo:     prodRepo,
		accuracyRepo: accuracyRepo,
		log:          log.NewHelper(logger),
	}
}

func (o *StatsService) GetOrderCount(ctx context.Context, req *annostat.GetOrderCountRequest) (*annostat.GetOrderCountReply, error) {
	op := client.UserFromCtx(ctx)
	filter := ToBizOrderFilter(req.Filter)
	if len(filter.OrgUids) == 0 && !client.IsSysRole(op.GetRole()) {
		filter.OrgUids = []string{op.GetOrgUid()}
	}
	if !client.IsPrivilegedUser(op.User) {
		switch op.GetRole() {
		case client.SysRoleKAM:
			filter.BizgranteeUid = op.GetUid()
		}
		if len(filter.OrgUids) == 0 {
			return nil, errors.NewErrForbidden()
		}
		for _, org := range filter.OrgUids {
			if org != op.GetOrgUid() && !client.IsAllowed(ctx, "", biz.PermList, biz.PermClsOrder, client.GroupScope(org)) {
				return nil, errors.NewErrForbidden()
			}
		}
	}
	cnts, _, err := o.orderRepo.GroupCountStrFld(ctx, biz.OrderSfldState, filter, biz.Pager{OrderBy: biz.OrderSfldState, Pagesz: 10})
	if err != nil {
		return nil, err
	}

	return &annostat.GetOrderCountReply{
		Total:    int32(kslice.Sum(lo.Values(cnts))),
		Ongoing:  int32(cnts[string(biz.OrderStateOngoing)]),
		Paused:   int32(cnts[string(biz.OrderStatePaused)]),
		Finished: int32(cnts[string(biz.OrderStateFinished)]),
	}, nil
}

func (o *StatsService) GetOrderConversion(ctx context.Context, req *annostat.GetOrderCountRequest) (*annostat.GetOrderConversionReply, error) {
	op := client.UserFromCtx(ctx)
	filter := ToBizOrderFilter(req.Filter)
	if len(filter.OrgUids) == 0 && !client.IsSysRole(op.GetRole()) {
		filter.OrgUids = []string{op.GetOrgUid()}
	}
	if !client.IsPrivilegedUser(op.User) {
		switch op.GetRole() {
		case client.SysRoleKAM:
			filter.BizgranteeUid = op.GetUid()
		}
		if len(filter.OrgUids) == 0 {
			return nil, errors.NewErrForbidden()
		}
		for _, org := range filter.OrgUids {
			if !client.IsAllowed(ctx, "", biz.PermStat, biz.PermClsLot, client.GroupScope(org)) {
				return nil, errors.NewErrForbidden()
			}
		}
	}

	cnt1, err := o.orderRepo.Count(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to count order: %w", err)
	}
	cnt2, err := o.lotRepo.CountUniqueValue(ctx, &biz.LotListFilter{FilterByOrder: filter}, biz.LotSfldOrderID)
	if err != nil {
		return nil, fmt.Errorf("failed to count lot: %w", err)
	}

	return &annostat.GetOrderConversionReply{Orders: int32(cnt1), Lots: int32(cnt2)}, nil
}

func getVisibleLotIDs(ctx context.Context, opUid string, lotStates ...anno.Lot_State_Enum) ([]int64, error) {
	reply, err := client.GetVisibleLots(ctx, opUid, lotStates, false, true)
	if err != nil {
		return nil, fmt.Errorf("failed to get visible lot ids: %w", err)
	}
	return lo.Map(reply.LotUids, func(item string, _ int) int64 { return kid.ParseID(item) }), nil
}

func getVisibleLotCount(ctx context.Context, opUid string, lotStates ...anno.Lot_State_Enum) (map[string]int32, error) {
	reply, err := client.GetVisibleLots(ctx, opUid, lotStates, true, false)
	if err != nil {
		return nil, fmt.Errorf("failed to get visible lot count: %w", err)
	}

	return reply.GetLotCount(), nil
}

func (o *StatsService) GetLotCount(ctx context.Context, req *annostat.GetLotCountRequest) (*annostat.GetLotCountReply, error) {
	op := client.UserFromCtx(ctx)
	filter := ToBizLotFilter(req.Filter)
	var lotCount map[string]int32
	var err error
	switch op.GetOrgType() {
	case iam.Team_Type_demander:
		filter.OrgUids = []string{op.GetOrgUid()}
	case iam.Team_Type_supplier:
		filter.ExecutorOrgs = []string{op.GetOrgUid()}
		if op.GetRole() == client.TeamRoleMember {
			lotCount, err = getVisibleLotCount(ctx, op.GetUid(), anno.Lot_State_ongoing, anno.Lot_State_paused, anno.Lot_State_finished)
			if err != nil {
				return nil, err
			}

			return &annostat.GetLotCountReply{
				Total:    kmap.SumMapValues(lotCount),
				Ongoing:  lotCount[biz.LotStateOngoing.String()],
				Paused:   lotCount[biz.LotStatePaused.String()],
				Finished: lotCount[biz.LotStateFinished.String()],
			}, nil
		}
	default:
		switch op.GetRole() {
		case client.SysRoleKAM:
			filter.BizgranteeUid = op.GetUid()
		case client.SysRolePM:
			filter.SpecgranteeUid = op.GetUid()
		default:
			if !client.IsPrivilegedUser(op.User) && op.GetRole() != client.SysRoleInspector {
				return nil, errors.NewErrForbidden()
			}
		}
	}
	cnts, _, err := o.lotRepo.GroupCountStrFld(ctx, biz.LotSfldState, filter, biz.Pager{OrderBy: biz.LotSfldState, Pagesz: 10})
	if err != nil {
		return nil, err
	}

	return &annostat.GetLotCountReply{
		Total:    int32(kslice.Sum(lo.Values(cnts))),
		Ongoing:  int32(cnts[string(biz.LotStateOngoing)]),
		Paused:   int32(cnts[string(biz.LotStatePaused)]),
		Finished: int32(cnts[string(biz.LotStateFinished)]),
	}, nil
}

type Counter struct {
	ElemsAtPhase     int
	AccumulatedElems int
	EstimatedDays    float32
}

type LotEstimation struct {
	Lot         *biz.Lot
	Phases      []*biz.Lotphase
	Productions []*biz.Production
	Counters    []Counter
}

func (o *StatsService) doLotEstimation(ctx context.Context, filter *biz.LotListFilter, pager biz.Pager) (
	res []LotEstimation, err error) {

	// query lots
	lots, _, err := o.lotRepo.List(ctx, filter, pager)
	if err != nil {
		return nil, fmt.Errorf("failed to list lot: %w", err)
	}
	if len(lots) == 0 {
		return nil, nil
	}

	// loading phase info
	lotIDs := lo.Map(lots, func(v *biz.Lot, _ int) int64 { return v.ID })
	phases, _, err := o.lotphaseRepo.List(ctx, &biz.LotphaseListFilter{LotIDs: lotIDs}, biz.Pager{Pagesz: 1000})
	phaseMap := make(map[int64][]*biz.Lotphase, len(lots))
	for _, l := range lots {
		phaseMap[l.ID] = make([]*biz.Lotphase, l.PhaseCount)
	}
	for _, phase := range phases {
		if phaseMap[phase.LotID][phase.Number-1] == nil {
			phase.Execteam = ""
			phaseMap[phase.LotID][phase.Number-1] = phase
		}
	}

	// loading previous working day production info
	groupLastFilter := &biz.GroupLastFilter{
		Model:     &biz.Production{},
		GroupFlds: []string{biz.ProductionSfldLotID, biz.ProductionSfldPhase},
		Filter: biz.NewAndFilters(
			&biz.ProductionListFilter{
				LotIDs:   lotIDs,
				Duration: int32(24 * time.Hour / time.Second),
			},
			&biz.EqualFilter{Field: biz.ProductionSfldExecteamUid},
			&biz.EqualFilter{Field: biz.ProductionSfldExecutorUid},
		),
	}
	prods, _, err := o.prodRepo.List(ctx, groupLastFilter, biz.Pager{Pagesz: 1000})
	if err != nil {
		return nil, fmt.Errorf("failed to list production: %w", err)
	}
	prodMap := make(map[int64][]*biz.Production, len(lots))
	for _, p := range prods {
		m := prodMap[p.LotID]
		if m == nil {
			m = make([]*biz.Production, len(phaseMap[p.LotID]))
			prodMap[p.LotID] = m
		}
		m[p.Phase-1] = p
	}

	// loading element count by phase
	elems := []struct {
		LotID int64
		Phase int
		Elems int
	}{}
	flds := []string{biz.JobSfldLotID, biz.JobSfldPhase}
	_, err = o.jobRepo.GroupBy(ctx, flds, []string{"SUM(" + biz.JobSfldElems + ") AS elems"},
		&biz.JobListFilter{LotIDs: lotIDs}, biz.Pager{Pagesz: 1000, OrderBy: field.Join(flds...)}, &elems)
	if err != nil {
		return nil, fmt.Errorf("failed to list job: %w", err)
	}
	elemMap := make(map[int64][]Counter, len(lots))
	for _, l := range lots {
		acc := 0
		m := elemMap[l.ID]
		if m == nil {
			// finished jobs' phase is lastPhase+1
			m = make([]Counter, l.PhaseCount+1)
			elemMap[l.ID] = m
		}
		for _, e := range elems {
			if e.LotID != l.ID {
				continue
			}
			acc += e.Elems
			m[e.Phase-1] = Counter{ElemsAtPhase: e.Elems, AccumulatedElems: acc}
		}
		// fill the gaps
		for i := 1; i < len(m); i++ {
			if m[i].AccumulatedElems == 0 {
				m[i] = m[i-1]
			}
		}
	}

	// caculate estimated days left for each phase in each lot
	res = make([]LotEstimation, len(lots))
	for i, l := range lots {
		counters := make([]Counter, l.PhaseCount)
		for j := 0; j < l.PhaseCount; j++ {
			days := float32(-1)
			phaseElems := elemMap[l.ID][j]
			if pp := prodMap[l.ID]; pp != nil && pp[j] != nil && pp[j].Elems > 0 {
				days = float32(phaseElems.AccumulatedElems) / float32(pp[j].Elems)
			}
			counters[j] = Counter{
				ElemsAtPhase:     phaseElems.ElemsAtPhase,
				AccumulatedElems: phaseElems.AccumulatedElems,
				EstimatedDays:    days,
			}
		}
		res[i] = LotEstimation{
			Lot:         l,
			Phases:      phaseMap[l.ID],
			Productions: prodMap[l.ID],
			Counters:    counters,
		}
	}

	return res, err
}

func (o *StatsService) GetOngoingLots(ctx context.Context, req *annostat.GetOngoingLotsRequest) (*annostat.GetOngoingLotsReply, error) {
	op := client.UserFromCtx(ctx)

	filter := &biz.LotListFilter{States: []biz.LotState{biz.LotStateOngoing}}
	switch op.GetOrgType() {
	case iam.Team_Type_demander:
		filter.OrgUids = []string{op.GetOrgUid()}
	case iam.Team_Type_supplier:
		filter.ExecutorOrgs = []string{op.GetOrgUid()}
		if op.GetRole() == client.TeamRoleMember {
			lotIDs, err := getVisibleLotIDs(ctx, op.GetUid(), anno.Lot_State_ongoing)
			if err != nil {
				return nil, err
			}
			if len(lotIDs) == 0 {
				return nil, nil
			}
			filter.IDs = lotIDs
		}
	default:
		switch op.GetRole() {
		case client.SysRoleKAM:
			filter.BizgranteeUid = op.GetUid()
		case client.SysRolePM:
			filter.SpecgranteeUid = op.GetUid()
		default:
			if !client.IsPrivilegedUser(op.User) && op.GetRole() != client.SysRoleInspector {
				return nil, errors.NewErrForbidden()
			}
		}
	}

	var total int64
	if req.Page == 0 {
		cnt, err := o.lotRepo.Count(ctx, filter)
		if err != nil {
			return nil, fmt.Errorf("failed to count lot: %w", err)
		}
		total = cnt
	}

	pager := biz.Pager{Page: int(req.Page), Pagesz: int(req.Pagesz), OrderBy: biz.LotSfldExpEndTime}
	if pager.Pagesz > 10 {
		pager.Pagesz = 10
	}
	lots, err := o.doLotEstimation(ctx, filter, pager)
	if err != nil {
		return nil, err
	}

	// caculate estimated days left for each phase in each lot
	r := make([]*annostat.GetOngoingLotsReply_Lot, len(lots))
	for i, e := range lots {
		l := e.Lot
		ps := make([]*annostat.GetOngoingLotsReply_Phase, l.PhaseCount)
		for j, p := range e.Phases {
			ps[j] = &annostat.GetOngoingLotsReply_Phase{
				Number:            p.Number,
				Name:              p.Name,
				Type:              annostat.GetOngoingLotsReply_Phase_Type_Enum(annostat.GetOngoingLotsReply_Phase_Type_Enum_value[p.Type]),
				EstimatedDaysLeft: e.Counters[j].EstimatedDays,
			}
		}
		r[i] = &annostat.GetOngoingLotsReply_Lot{
			Uid:        l.GetUid(),
			Name:       l.Name,
			DataType:   annostat.DataType_Enum(annostat.DataType_Enum_value[l.DataType]),
			DataSize:   int32(l.DataSize),
			Phases:     ps,
			ExpEndTime: timestamppb.New(l.ExpEndTime),
			CreatedAt:  timestamppb.New(l.CreatedAt),
		}
	}
	return &annostat.GetOngoingLotsReply{Total: int32(total), Lots: r}, nil
}

func (o *StatsService) GetLotStatus(ctx context.Context, req *annostat.GetLotStatusRequest) (*annostat.GetLotStatusReply, error) {
	if !client.IsPrivilegedUser(client.UserFromCtx(ctx).User) && !client.IsAllowed(ctx, "", biz.PermGet, biz.PermClsLot, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	filter := &biz.LotListFilter{IDs: []int64{kid.ParseID(req.Uid)}}
	lots, err := o.doLotEstimation(ctx, filter, biz.Pager{Pagesz: 1})
	if err != nil {
		return nil, err
	}
	if len(lots) == 0 {
		return nil, errors.NewErrNotFound(errors.WithModel("Lot"))
	}

	e := lots[0]
	ps := make([]*annostat.GetLotStatusReply_Phase, e.Lot.PhaseCount)
	for j := 0; j < e.Lot.PhaseCount; j++ {
		elemsToWork := int32(e.Counters[j].AccumulatedElems)
		if !e.Lot.JobReady {
			elemsToWork = int32(e.Lot.DataSize)
		}
		ps[j] = &annostat.GetLotStatusReply_Phase{
			ElemsToWork:       elemsToWork,
			EstimatedDaysLeft: e.Counters[j].EstimatedDays,
		}
	}
	return &annostat.GetLotStatusReply{TotalElems: int32(e.Lot.DataSize), Phases: ps}, nil
}

func (o *StatsService) GetLotStatByExecutor(ctx context.Context, req *annostat.GetLotStatByExecutorRequest) (*annostat.GetLotStatByExecutorReply, error) {
	if !client.IsPrivilegedUser(client.UserFromCtx(ctx).User) && !client.IsAllowed(ctx, "", biz.PermGet, biz.PermClsLot, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	period := ToBizTimeRange(req.TimeRange)
	prodFilter := &biz.ProductionListFilter{
		TimeRange: period,
		LotIDs:    []int64{kid.ParseID(req.Uid)},
		Phases:    []int32{req.Phase},
		Duration:  int32(24 * time.Hour / time.Second),
	}
	if period.IsEmpty() {
		// find last period
		prodGroupLastFilter := &biz.GroupLastFilter{
			Model:  &biz.Production{},
			Filter: prodFilter,
		}
		prods, _, err := o.prodRepo.List(ctx, prodGroupLastFilter, biz.Pager{Pagesz: int(req.Pagesz)})
		if err != nil {
			return nil, fmt.Errorf("failed to list production: %w", err)
		}
		if len(prods) == 0 {
			return &annostat.GetLotStatByExecutorReply{}, nil
		}
		period.From = prods[0].PeriodStart
		prodFilter.TimeRange = period
	}

	// build list filters
	pager := biz.Pager{Page: int(req.Page), Pagesz: int(req.Pagesz)}
	prodFilters := biz.NewAndFilters(prodFilter)
	accFilters := biz.NewAndFilters(&biz.AccuracyListFilter{
		TimeRange: period,
		LotIDs:    []int64{kid.ParseID(req.Uid)},
		Phases:    []int32{req.Phase},
		Duration:  int32(24 * time.Hour / time.Second),
	})
	// groupFlds := []string{}
	if req.ByExecteam {
		pager.OrderBy = biz.ProductionSfldExecteamUid
		// groupFlds = []string{biz.ProductionSfldExecteamUid}
		prodFilters.Append(&biz.UnequalFilter{Field: biz.ProductionSfldExecteamUid, Value: nil})
		prodFilters.Append(&biz.EqualFilter{Field: biz.ProductionSfldExecutorUid, Value: nil})
		accFilters.Append(&biz.UnequalFilter{Field: biz.ProductionSfldExecteamUid, Value: nil})
		accFilters.Append(&biz.EqualFilter{Field: biz.ProductionSfldExecutorUid, Value: nil})
	} else {
		pager.OrderBy = biz.ProductionSfldExecutorUid
		// groupFlds = []string{biz.ProductionSfldExecutorUid}
		prodFilters.Append(&biz.UnequalFilter{Field: biz.ProductionSfldExecutorUid, Value: nil})
		// prodFilters.Append(&biz.EqualFilter{Field: biz.ProductionSfldExecteamUid, Value: nil})
		accFilters.Append(&biz.UnequalFilter{Field: biz.ProductionSfldExecutorUid, Value: nil})
		// accFilters.Append(&biz.EqualFilter{Field: biz.ProductionSfldExecteamUid, Value: nil})
	}

	// count total number of executors
	var total int64
	if req.Page == 0 {
		cnt, err := o.prodRepo.Count(ctx, prodFilters)
		if err != nil {
			return nil, fmt.Errorf("failed to count production: %w", err)
		}
		total = cnt
	}

	// loading previous working day production info
	// prodGroupLastFilter := &biz.GroupLastFilter{
	// 	Model:     &biz.Production{},
	// 	Filter:    prodFilters,
	// 	GroupFlds: groupFlds,
	// }
	// prods, err := o.prodRepo.List(ctx, prodGroupLastFilter, pager)
	prods, _, err := o.prodRepo.List(ctx, prodFilters, pager)
	if err != nil {
		return nil, fmt.Errorf("failed to list production: %w", err)
	}

	// loading previous working day accuracy info
	// accGroupLastFilter := &biz.GroupLastFilter{
	// 	Model:     &biz.Accuracy{},
	// 	Filter:    accFilters,
	// 	GroupFlds: groupFlds,
	// }
	// accuracies, err := o.accuracyRepo.List(ctx, accGroupLastFilter, pager)
	accuracies, _, err := o.accuracyRepo.List(ctx, accFilters, pager)
	if err != nil {
		return nil, fmt.Errorf("failed to list accuracy: %w", err)
	}
	accMap := lo.SliceToMap(accuracies, func(v *biz.Accuracy) (string, *biz.Accuracy) {
		if req.ByExecteam {
			return v.ExecteamUid, v
		}
		return v.ExecutorUid, v
	})

	// get exeuctor info
	execUids := lo.Map(prods, func(v *biz.Production, _ int) string {
		if req.ByExecteam {
			return v.ExecteamUid
		}
		return v.ExecutorUid
	})
	var execMap map[string]*client.BaseUser
	if req.ByExecteam {
		execMap, err = client.ListTeamsInMap(client.NewCtxUseSvcAccount(ctx), execUids)
		if err != nil {
			return nil, fmt.Errorf("failed to list teams: %w", err)
		}
	} else {
		execMap, err = client.ListUsersInMap(client.NewCtxUseSvcAccount(ctx), execUids)
		if err != nil {
			return nil, fmt.Errorf("failed to list users: %w", err)
		}
	}

	// assemble result
	counters := make([]*annostat.ExecutorCounter, len(prods))
	for i, p := range prods {
		uid := execUids[i]
		hours := float32(p.WorkDuration) / 3600
		if hours < 1 {
			hours = 1 // at least 1 hour
		}
		acc := struct {
			RateElems float32
			RateIns   float32
		}{-1, -1}
		if a := accMap[uid]; a != nil {
			acc.RateElems = a.RateElems
			acc.RateIns = a.RateIns
		}
		counters[i] = &annostat.ExecutorCounter{
			Executor: execMap[uid],
			Jobs:     p.Jobs,
			Elems:    p.Elems,
			Ins:      p.Ins,
			InsByWidget: map[string]int32{
				"anno2d": p.Ins2d,
				"anno3d": p.Ins3d,
			},
			JobsPerHour:  float32(p.Jobs) / hours,
			ElemsPerHour: float32(p.Elems) / hours,
			ElemAccuracy: acc.RateElems,
			InsAccuracy:  acc.RateIns,
		}
	}

	return &annostat.GetLotStatByExecutorReply{Total: int32(total), Counters: counters}, nil
}

func (o *StatsService) ProductionByTime(ctx context.Context, req *annostat.ProductionByTimeRequest) (*annostat.ProductionByTimeReply, error) {
	if req.TimeUnit == 0 {
		req.TimeUnit = 3600
	}
	if len(req.Items) == 0 {
		req.Items = []string{"anno2d", "anno3d"}
	}

	prodFilter := &biz.ProductionListFilter{
		Duration: req.TimeUnit,
	}
	op := client.UserFromCtx(ctx)
	// filter := ToBizLotFilter(req.Filter)
	switch {
	case client.IsPrivilegedUser(op.User) || client.SysRoleInspector == op.GetRole():
	case op.GetRole() == client.SysRoleKAM:
		prodFilter.BizgranteeUid = op.GetUid()
	case op.GetRole() == client.SysRolePM:
		prodFilter.SpecgranteeUid = op.GetUid()
	case !client.IsSysRole(op.GetRole()):
		// filter.ExecutorOrgs = []string{op.GetOrgUid()}
		prodFilter.ExecteamUids = []string{op.GetOrgUid()}
		if op.GetRole() == client.TeamRoleMember {
			prodFilter.ExecutorUids = []string{op.GetUid()}
		}
	default:
		return nil, errors.NewErrForbidden(errors.WithMessage(fmt.Sprintf("role %s is not supported yet", op.GetRole())))
	}

	// fill time range and calculate time point
	reqTimeRange := req.GetFilter().GetTimeRange()
	prodFilter.TimeRange = ToBizTimeRange(reqTimeRange)
	timeUnit := time.Duration(req.TimeUnit) * time.Second
	timePoints, err := fixTimeRange(prodFilter, timeUnit)
	if err != nil {
		return nil, err
	}

	// query productions
	filter := biz.NewAndFilters(prodFilter)
	if op.GetRole() != client.TeamRoleMember {
		filter.Append(
			&biz.EqualFilter{Field: biz.ProductionSfldExecutorUid},
			&biz.EqualFilter{Field: biz.ProductionSfldLotID},
		)
	}
	switch {
	case client.IsPrivilegedUser(op.User) || client.SysRoleInspector == op.GetRole():
		filter.Append(&biz.EqualFilter{Field: biz.ProductionSfldExecteamUid})
	}
	prods, _, err := o.prodRepo.List(ctx, filter, biz.Pager{Pagesz: 10})
	if err != nil {
		return nil, fmt.Errorf("failed to list production: %w", err)
	}

	// assemble result
	units := make([]*annostat.ProductionByTimeReply_Unit, len(timePoints))
	cnt := 0
	for i := range timePoints {
		timePoint := timePoints[i]
		timeRange := &annostat.TimeRange{
			From: timestamppb.New(timePoint),
			To:   timestamppb.New(timePoint.Add(timeUnit)),
		}
		units[i] = &annostat.ProductionByTimeReply_Unit{TimeRange: timeRange,
			Items: map[string]float32{
				"anno2d": 0.0,
				"anno3d": 0.0,
			},
		}
		if cnt >= len(prods) {
			continue
		}
		psStart := getTimeStart(prods[cnt].PeriodStart, timeUnit)
		if !timePoint.Equal(psStart) {
			continue
		}

		p := prods[cnt]
		cnt++
		items := map[string]float32{
			"anno2d": float32(p.Ins2d),
			"anno3d": float32(p.Ins3d),
		}
		units[i].Items = items
	}

	return &annostat.ProductionByTimeReply{Units: units}, nil
}

func fixTimeRange(prodFilter *biz.ProductionListFilter, timeUnit time.Duration) (timePoints []time.Time, err error) {
	var periodCnt int
	switch timeUnit {
	case time.Hour:
		periodCnt = 24
	case time.Hour * 24: // TODO might need to consider time zone and do more testing
		periodCnt = 30
		return nil, errors.NewErrInvalidField(errors.WithMessage("day is not supported yet"),
			errors.WithFields("time_unit"))
	default:
		return nil, errors.NewErrInvalidField(errors.WithFields("time_unit"))
	}

	if prodFilter.TimeRange.IsEmpty() || prodFilter.TimeRange.From.IsZero() { // TimeRange is empty or TimeRange.From is zero
		end := getTimeStart(time.Now().UTC(), timeUnit)
		prodFilter.TimeRange = biz.TimeRange{From: end.Add(-timeUnit * time.Duration(periodCnt))}
	} else { // TimeRange.From is valid, TimeRange.To is optional
		from := getTimeStart(prodFilter.TimeRange.From, timeUnit)
		prodFilter.TimeRange.From = from
		to := prodFilter.TimeRange.To
		end := time.Now().UTC()
		if !to.IsZero() && to.Before(end) {
			end = prodFilter.TimeRange.To
		}
		end = getTimeStart(end, timeUnit)
		periodCnt = int(end.Sub(from) / timeUnit)
	}

	timePoints = make([]time.Time, periodCnt)
	for i := 0; i < periodCnt; i++ {
		timePoints[i] = prodFilter.TimeRange.From.Add(timeUnit * time.Duration(i))
	}
	return
}

func getTimeStart(t time.Time, timeUnit time.Duration) time.Time {
	if timeUnit == time.Hour {
		return ktime.GetStartOfHour(t)
	}
	return ktime.GetStartOfDay(t)
}
