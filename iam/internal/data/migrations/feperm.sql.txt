-- team_type: 0-unknown, 1-demander, 2-operator, 3-supplier

-- global (operator) roles
INSERT INTO "feperms" ("role","perms") VALUES ('root','[{"perm":{"visible_pages":["/admin/home","/admin/teams/manage","/admin/users/manage","/admin/tasks/manage"]}}]') ON CONFLICT ("role") DO UPDATE SET "perms"="excluded"."perms";
INSERT INTO "feperms" ("role","perms") VALUES ('admin','[{"perm":{"visible_pages":["/admin/home","/admin/teams/manage","/admin/users/manage","/admin/tasks/manage"]}}]') ON CONFLICT ("role") DO UPDATE SET "perms"="excluded"."perms";
INSERT INTO "feperms" ("role","perms") VALUES ('inspector','[{"perm":{"visible_pages":["/admin/home","/admin/teams/manage","/admin/users/manage","/admin/tasks/manage"]}}]') ON CONFLICT ("role") DO UPDATE SET "perms"="excluded"."perms";
INSERT INTO "feperms" ("role","perms") VALUES ('subAdmin','[{"perm":{"visible_pages":["/admin/home","/admin/teams/manage","/admin/users/manage","/admin/tasks/manage"]}}]') ON CONFLICT ("role") DO UPDATE SET "perms"="excluded"."perms";
INSERT INTO "feperms" ("role","perms") VALUES ('kam','[{"perm":{"visible_pages":["/admin/home","/admin/tasks/manage"]}}]') ON CONFLICT ("role") DO UPDATE SET "perms"="excluded"."perms";
INSERT INTO "feperms" ("role","perms") VALUES ('pm','[{"perm":{"visible_pages":["/admin/home","/admin/tasks/manage"]}}]') ON CONFLICT ("role") DO UPDATE SET "perms"="excluded"."perms";

-- custormer roles
INSERT INTO "feperms" ("role","perms") VALUES ('owner','[{"team_type":1,"perm":{"visible_pages":["/admin/home","/admin/teams/members","/admin/orders/manage"]}}, {"team_type":3,"perm":{"visible_pages":["/admin/home","/admin/teams/members","/admin/tasks/produce"]}}]') ON CONFLICT ("role") DO UPDATE SET "perms"="excluded"."perms";
INSERT INTO "feperms" ("role","perms") VALUES ('manager','[{"team_type":1,"perm":{"visible_pages":["/admin/home","/admin/teams/members","/admin/orders/manage"]}}, {"team_type":3,"perm":{"visible_pages":["/admin/home","/admin/teams/members","/admin/tasks/produce"]}}]') ON CONFLICT ("role") DO UPDATE SET "perms"="excluded"."perms";
INSERT INTO "feperms" ("role","perms") VALUES ('member','[{"team_type":1,"perm":{"visible_pages":["/admin/home","/admin/tasks/hall"]}}, {"team_type":3,"perm":{"visible_pages":["/admin/home","/admin/tasks/hall"]}}]') ON CONFLICT ("role") DO UPDATE SET "perms"="excluded"."perms";
