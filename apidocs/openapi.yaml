openapi: 3.0.3
info:
    title: "API Documentation Service"
    version: 0.0.1
security:
  - {}
  - jwt: []
paths:
    /apidocs/healthz:
        get:
            tags:
                - Misc
            description: detect service health status
            operationId: GetHealthz
            security: []
            responses:
                "200":
                    description: OK
    /apidocs/assets/{path}:
        get:
            tags:
                - Assets
            description: get an asset
            operationId: GetAsset
            parameters:
                - name: path
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: content of the asset
                    content:
                      text/plain:
                        schema:
                          type: string
    /apidocs/list/{path}:
        get:
            tags:
                - Assets
            description: get content of a dir or an asset
            operationId: GetList
            parameters:
                - name: path
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: content of a dir or an asset
                    content:
                      text/plain:
                        schema:
                          type: string
    /apidocs/docs/{app}/{rev}:
        get:
            tags:
                - Docs
            description: get content of a doc
            operationId: GetDoc
            parameters:
                - name: app
                  in: path
                  required: true
                  schema:
                    type: string
                - name: rev
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: content of the doc
                    content:
                      text/plain:
                        schema:
                          type: string
        put:
            tags:
                - Docs
            description: upload a doc
            operationId: UploadDoc
            parameters:
                - name: app
                  in: path
                  required: true
                  schema:
                    type: string
                - name: rev
                  in: path
                  required: true
                  schema:
                    type: string
                - name: alias
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
        # delete:
        #     tags:
        #         - Docs
        #     description: delete a doc
        #     operationId: DeleteDoc
        #     parameters:
        #         - name: app
        #           in: path
        #           required: true
        #           schema:
        #             type: string
        #         - name: rev
        #           in: path
        #           required: true
        #           schema:
        #             type: string
        #     responses:
        #         "204":
        #             description: OK
    /apidocs/docs/{app}/{rev}/alias:
        put:
            tags:
                - Docs
            description: create aliases for a doc
            operationId: CreateDocAlias
            parameters:
                - name: app
                  in: path
                  required: true
                  schema:
                    type: string
                - name: rev
                  in: path
                  required: true
                  schema:
                    type: string
                - name: alias
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "204":
                    description: OK
    /apidocs/view/{app}/{rev}:
        get:
            tags:
                - Docs
            description: show the doc in a API doc viewer
            operationId: ViewDoc
            parameters:
                - name: app
                  in: path
                  required: true
                  schema:
                    type: string
                - name: rev
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: content of the doc
                    content:
                      text/html:
                        schema:
                          type: string
components:
  securitySchemes:
    jwt:
      type: http
      scheme: bearer
      bearerFormat: JWT
tags:
    - name: Misc
    - name: Docs
    - name: Assets
