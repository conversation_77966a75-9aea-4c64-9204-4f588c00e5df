// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: model/v1/run.proto

package model

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	types "gitlab.rp.konvery.work/platform/apis/types"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationRunsAddTag = "/model.v1.Runs/AddTag"
const OperationRunsCancel = "/model.v1.Runs/Cancel"
const OperationRunsCreate = "/model.v1.Runs/Create"
const OperationRunsDelete = "/model.v1.Runs/Delete"
const OperationRunsDeleteTag = "/model.v1.Runs/DeleteTag"
const OperationRunsFinish = "/model.v1.Runs/Finish"
const OperationRunsGet = "/model.v1.Runs/Get"
const OperationRunsList = "/model.v1.Runs/List"
const OperationRunsStart = "/model.v1.Runs/Start"
const OperationRunsUpdate = "/model.v1.Runs/Update"

type RunsHTTPServer interface {
	AddTag(context.Context, *types.TagRequest) (*types.TagList, error)
	Cancel(context.Context, *GetRunRequest) (*emptypb.Empty, error)
	Create(context.Context, *CreateRunRequest) (*Run, error)
	Delete(context.Context, *DeleteRunRequest) (*emptypb.Empty, error)
	DeleteTag(context.Context, *types.TagRequest) (*types.TagList, error)
	Finish(context.Context, *FinishRunRequest) (*emptypb.Empty, error)
	Get(context.Context, *GetRunRequest) (*Run, error)
	List(context.Context, *ListRunRequest) (*ListRunReply, error)
	Start(context.Context, *StartRunRequest) (*emptypb.Empty, error)
	Update(context.Context, *UpdateRunRequest) (*Run, error)
}

func RegisterRunsHTTPServer(s *http.Server, srv RunsHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/runs", _Runs_Create0_HTTP_Handler(srv))
	r.PATCH("/v1/runs/{uid}", _Runs_Update0_HTTP_Handler(srv))
	r.PUT("/v1/runs/{uid}/start", _Runs_Start0_HTTP_Handler(srv))
	r.PUT("/v1/runs/{uid}/finish", _Runs_Finish0_HTTP_Handler(srv))
	r.PUT("/v1/runs/{uid}/cancel", _Runs_Cancel0_HTTP_Handler(srv))
	r.DELETE("/v1/runs/{uid}", _Runs_Delete0_HTTP_Handler(srv))
	r.GET("/v1/runs/{uid}", _Runs_Get0_HTTP_Handler(srv))
	r.GET("/v1/runs", _Runs_List0_HTTP_Handler(srv))
	r.PUT("/v1/runs/{uid}/tag", _Runs_AddTag0_HTTP_Handler(srv))
	r.DELETE("/v1/runs/{uid}/tag", _Runs_DeleteTag0_HTTP_Handler(srv))
}

func _Runs_Create0_HTTP_Handler(srv RunsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateRunRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRunsCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Create(ctx, req.(*CreateRunRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Run)
		return ctx.Result(200, reply)
	}
}

func _Runs_Update0_HTTP_Handler(srv RunsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateRunRequest
		if err := ctx.Bind(&in.Data); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRunsUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Update(ctx, req.(*UpdateRunRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Run)
		return ctx.Result(200, reply)
	}
}

func _Runs_Start0_HTTP_Handler(srv RunsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StartRunRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRunsStart)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Start(ctx, req.(*StartRunRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Runs_Finish0_HTTP_Handler(srv RunsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FinishRunRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRunsFinish)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Finish(ctx, req.(*FinishRunRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Runs_Cancel0_HTTP_Handler(srv RunsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRunRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRunsCancel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Cancel(ctx, req.(*GetRunRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Runs_Delete0_HTTP_Handler(srv RunsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteRunRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRunsDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Delete(ctx, req.(*DeleteRunRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Runs_Get0_HTTP_Handler(srv RunsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRunRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRunsGet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Get(ctx, req.(*GetRunRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Run)
		return ctx.Result(200, reply)
	}
}

func _Runs_List0_HTTP_Handler(srv RunsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListRunRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRunsList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.List(ctx, req.(*ListRunRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListRunReply)
		return ctx.Result(200, reply)
	}
}

func _Runs_AddTag0_HTTP_Handler(srv RunsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in types.TagRequest
		if err := ctx.Bind(&in.Tags); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRunsAddTag)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddTag(ctx, req.(*types.TagRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*types.TagList)
		return ctx.Result(200, reply)
	}
}

func _Runs_DeleteTag0_HTTP_Handler(srv RunsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in types.TagRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRunsDeleteTag)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteTag(ctx, req.(*types.TagRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*types.TagList)
		return ctx.Result(200, reply)
	}
}

type RunsHTTPClient interface {
	AddTag(ctx context.Context, req *types.TagRequest, opts ...http.CallOption) (rsp *types.TagList, err error)
	Cancel(ctx context.Context, req *GetRunRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Create(ctx context.Context, req *CreateRunRequest, opts ...http.CallOption) (rsp *Run, err error)
	Delete(ctx context.Context, req *DeleteRunRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteTag(ctx context.Context, req *types.TagRequest, opts ...http.CallOption) (rsp *types.TagList, err error)
	Finish(ctx context.Context, req *FinishRunRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Get(ctx context.Context, req *GetRunRequest, opts ...http.CallOption) (rsp *Run, err error)
	List(ctx context.Context, req *ListRunRequest, opts ...http.CallOption) (rsp *ListRunReply, err error)
	Start(ctx context.Context, req *StartRunRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	Update(ctx context.Context, req *UpdateRunRequest, opts ...http.CallOption) (rsp *Run, err error)
}

type RunsHTTPClientImpl struct {
	cc *http.Client
}

func NewRunsHTTPClient(client *http.Client) RunsHTTPClient {
	return &RunsHTTPClientImpl{client}
}

func (c *RunsHTTPClientImpl) AddTag(ctx context.Context, in *types.TagRequest, opts ...http.CallOption) (*types.TagList, error) {
	var out types.TagList
	pattern := "/v1/runs/{uid}/tag"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRunsAddTag))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in.Tags, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RunsHTTPClientImpl) Cancel(ctx context.Context, in *GetRunRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/runs/{uid}/cancel"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRunsCancel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RunsHTTPClientImpl) Create(ctx context.Context, in *CreateRunRequest, opts ...http.CallOption) (*Run, error) {
	var out Run
	pattern := "/v1/runs"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRunsCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RunsHTTPClientImpl) Delete(ctx context.Context, in *DeleteRunRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/runs/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRunsDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RunsHTTPClientImpl) DeleteTag(ctx context.Context, in *types.TagRequest, opts ...http.CallOption) (*types.TagList, error) {
	var out types.TagList
	pattern := "/v1/runs/{uid}/tag"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRunsDeleteTag))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RunsHTTPClientImpl) Finish(ctx context.Context, in *FinishRunRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/runs/{uid}/finish"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRunsFinish))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RunsHTTPClientImpl) Get(ctx context.Context, in *GetRunRequest, opts ...http.CallOption) (*Run, error) {
	var out Run
	pattern := "/v1/runs/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRunsGet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RunsHTTPClientImpl) List(ctx context.Context, in *ListRunRequest, opts ...http.CallOption) (*ListRunReply, error) {
	var out ListRunReply
	pattern := "/v1/runs"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRunsList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RunsHTTPClientImpl) Start(ctx context.Context, in *StartRunRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/runs/{uid}/start"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRunsStart))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RunsHTTPClientImpl) Update(ctx context.Context, in *UpdateRunRequest, opts ...http.CallOption) (*Run, error) {
	var out Run
	pattern := "/v1/runs/{uid}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRunsUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in.Data, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
