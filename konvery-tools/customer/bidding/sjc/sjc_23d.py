import os
import sys
import json
import shutil
import numpy as np

sys.path.append("..")
from customer.common import tools


def run(input_dir):
    camera_map_json_files = tools.get_file_by_extension(input_dir, extension="camera2map_calib.json")
    output_dir = os.path.join(os.path.dirname(input_dir), "testin_output")
    tools.check_dir(output_dir)
    for camera_map in camera_map_json_files:
        data = tools.get_json_data(camera_map.path)
        batch_dir = os.path.dirname(camera_map.path)
        data_dir = tools.get_sub_dir(tools.get_sub_dir(batch_dir, name_only=False)[0], name_only=False)
        sensor_params = {}
        group_map_dir = os.path.join(os.path.dirname(camera_map.path), "group2map_calib.json")
        group_map_data = tools.get_json_data(group_map_dir)
        lidar2world = tools.pose_to_mat(group_map_data[tools.get_sub_dir(batch_dir)[0].split("_")[0]]["translation"]
                                        + group_map_data[tools.get_sub_dir(batch_dir)[0].split("_")[0]]["rotation"])
        for camera_name, params in data.items():
            sensor_params[camera_name] = {}
            extrinsic = (np.linalg.inv(tools.pose_to_mat(
                params["translation"] + params["rotation"])) @ lidar2world).tolist()
            sensor_params[camera_name] = {
                "camera_model": "pinhole",
                "extrinsic": extrinsic,
                "fx": params["fx"],
                "fy": params["fy"],
                "cx": params["cx"],
                "cy": params["cy"],
                "k1": params["radial_distortion"][0],
                "k2": params["radial_distortion"][1],
                "k3": 0,
                "p1": params["radial_distortion"][2],
                "p2": params["radial_distortion"][3]
            }
        testin_json = {
            "data_type": "fusion_pointcloud",
            "sensor_params": sensor_params
        }
        config_dir = os.path.join(output_dir, os.path.basename(batch_dir), "config.json")
        tools.check_dir(os.path.dirname(config_dir))
        with open(config_dir, "w") as f:
            json.dump(testin_json, f, indent=4)
        for folder in data_dir:
            if os.path.basename(folder) == "lidar":
                shutil.copytree(folder, os.path.join(os.path.dirname(config_dir), os.path.basename(folder)))
            else:
                shutil.copytree(folder, os.path.join(os.path.dirname(config_dir), "camera", os.path.basename(folder)))


if __name__ == "__main__":
    args = tools.get_args()
    run(args.input)
