package service

import (
	"context"

	"annostat/api/client"
	"annostat/internal/biz"
	"annostat/internal/data"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/errors"

	"github.com/google/wire"
	"github.com/samber/lo"
)

// ProviderSet is service providers.
var ProviderSet = wire.NewSet(NewStatsService, NewConfigsService)

func CheckUpdateFlds(model any, flds []string, allowedSnakecase ...string) ([]string, error) {
	modelName := data.TypeName(model)
	flds = field.Snakecase(flds...)
	for _, fld := range flds {
		if !lo.Contains(allowedSnakecase, fld) {
			return nil, errors.NewErrUnsupportedField(errors.WithModel(modelName), errors.WithFields(fld))
		}
	}
	return flds, nil
}

func getListScope(ctx context.Context, orgUid, creatorUid string) (creator, scope string) {
	if creatorUid != "" {
		return creatorUid, client.UserScope(creatorUid)
	}
	if orgUid != "" {
		return creatorUid, client.GroupScope(orgUid)
	}

	op := biz.UserFromCtx(ctx)
	if client.IsPrivileged(op.GetRole()) {
		return
	}
	return op.GetUid(), client.UserScope(op.GetUid())
}
