package common

import (
	"context"
	"fmt"

	prom "github.com/go-kratos/kratos/contrib/metrics/prometheus/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/prometheus/client_golang/prometheus"
	mw "gitlab.rp.konvery.work/platform/apis/middleware"
	"gitlab.rp.konvery.work/platform/pkg/errors"
)

func Middlewares(logger log.Logger, extra ...middleware.Middleware) []middleware.Middleware {
	common := []middleware.Middleware{
		metadata.Server(),
		recovery.Recovery(recovery.WithHandler(recoveryHandler)),
		metrics.Server(
			metrics.WithSeconds(prom.NewHistogram(metricSeconds)),
			metrics.WithRequests(prom.NewCounter(metricRequests)),
		),
		tracing.Server(),
		mw.SvrLog(logger),
		mw.Validator(),
		mw.HandleError(),
		mw.DebugEnabler(),
		mw.CheckPager(),
	}
	return append(common, extra...)
}

func recoveryHandler(ctx context.Context, req, err interface{}) error {
	e, ok := err.(error)
	if !ok {
		e = fmt.Errorf("%+v", err)
	}
	return errors.NewErrServerError().WithCause(e)
}

var (
	metricSeconds = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Namespace: "server",
		Subsystem: "request",
		Name:      "duration_seconds",
		Help:      "requests duration in seconds by API",
		Buckets:   []float64{0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1, 2.5, 5, 10, 30, 60},
	}, []string{"kind", "operation"})

	metricRequests = prometheus.NewCounterVec(prometheus.CounterOpts{
		Namespace: "server",
		Subsystem: "request",
		Name:      "total",
		Help:      "total number of processed requests by code and reason",
	}, []string{"kind", "operation", "code", "reason"})
)

func init() {
	prometheus.MustRegister(metricSeconds, metricRequests)
}
