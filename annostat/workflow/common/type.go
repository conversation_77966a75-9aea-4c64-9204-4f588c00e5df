package common

import (
	"context"

	"gitlab.rp.konvery.work/platform/pkg/wf"
)

type Heartbeat func(ctx context.Context, details ...interface{})

const (
	EvtConfigChannged = "ConfigChanged"
	EvtLotStarted     = "LotStarted"
	EvtLotPaused      = "LotPaused"
	EvtLotCompleted   = "LotCompleted"
	EvtLotCanceled    = "LotCanceled"
	EvtLotRevertJobs  = "LotRevertJobs"

	EvtJobCompleted    = "JobCompleted"
	EvtJobChanged      = "JobChanged"
	EvtJobDoing        = "JobDoing"
	EvtJobChecking     = "JobChecking"
	EvtJobPendingMerge = "JobPendingMerge"

	EvtJobsubmit = "Jobsubmit"

	DefaultSignalName = wf.DefaultSignalName
)

type Event struct {
	Event   string   `json:"event,omitempty"`
	LotID   int64    `json:"lot_id,omitempty"`
	JobID   int64    `json:"job_id,omitempty"`
	TraceID string   `json:"trace_id,omitempty"`
	Details *Details `json:"details,omitempty"`
	// Extra   map[string]any `json:"extra,omitempty"`
	// JobAction string         `json:"job_action,omitempty"`
}

func (o *Event) SetDetails(v any) {
	o.Details = NewDetails(v)
}

func GetDetails[T any](e *Event) *T {
	return GetDetailsValue[T](e.Details)
}
