// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: annofeed/v1/datas.proto

package annofeed

import (
	context "context"
	v1 "gitlab.rp.konvery.work/platform/apis/anno/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Datas_CreateData_FullMethodName               = "/annofeed.v1.Datas/CreateData"
	Datas_UpdateData_FullMethodName               = "/annofeed.v1.Datas/UpdateData"
	Datas_DeleteData_FullMethodName               = "/annofeed.v1.Datas/DeleteData"
	Datas_GetData_FullMethodName                  = "/annofeed.v1.Datas/GetData"
	Datas_ListData_FullMethodName                 = "/annofeed.v1.Datas/ListData"
	Datas_GetDataElements_FullMethodName          = "/annofeed.v1.Datas/GetDataElements"
	Datas_FindDataElements_FullMethodName         = "/annofeed.v1.Datas/FindDataElements"
	Datas_GetDataMeta_FullMethodName              = "/annofeed.v1.Datas/GetDataMeta"
	Datas_SetRawdataEmbedding_FullMethodName      = "/annofeed.v1.Datas/SetRawdataEmbedding"
	Datas_GetDataValidationSummary_FullMethodName = "/annofeed.v1.Datas/GetDataValidationSummary"
	Datas_ParseData_FullMethodName                = "/annofeed.v1.Datas/ParseData"
)

// DatasClient is the client API for Datas service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DatasClient interface {
	CreateData(ctx context.Context, in *CreateDataRequest, opts ...grpc.CallOption) (*Data, error)
	UpdateData(ctx context.Context, in *UpdateDataRequest, opts ...grpc.CallOption) (*Data, error)
	DeleteData(ctx context.Context, in *DeleteDataRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetData(ctx context.Context, in *GetDataRequest, opts ...grpc.CallOption) (*Data, error)
	ListData(ctx context.Context, in *ListDataRequest, opts ...grpc.CallOption) (*ListDataReply, error)
	// Fetch a list of elements
	GetDataElements(ctx context.Context, in *GetDataElementsRequest, opts ...grpc.CallOption) (*GetDataElementsReply, error)
	// Find a list of elements (only names and indexes are returned)
	FindDataElements(ctx context.Context, in *FindDataElementsRequest, opts ...grpc.CallOption) (*GetDataElementsReply, error)
	// Get data meta
	GetDataMeta(ctx context.Context, in *GetDataMetaRequest, opts ...grpc.CallOption) (*GetDataMetaReply, error)
	// Set rawdata embedding.
	SetRawdataEmbedding(ctx context.Context, in *SetRawdataEmbeddingRequest, opts ...grpc.CallOption) (*SetRawdataEmbeddingReply, error)
	// Get data validation summary
	GetDataValidationSummary(ctx context.Context, in *GetDataRequest, opts ...grpc.CallOption) (*v1.DataValidationSummary, error)
	// Parse data
	ParseData(ctx context.Context, in *ParseDataRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type datasClient struct {
	cc grpc.ClientConnInterface
}

func NewDatasClient(cc grpc.ClientConnInterface) DatasClient {
	return &datasClient{cc}
}

func (c *datasClient) CreateData(ctx context.Context, in *CreateDataRequest, opts ...grpc.CallOption) (*Data, error) {
	out := new(Data)
	err := c.cc.Invoke(ctx, Datas_CreateData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datasClient) UpdateData(ctx context.Context, in *UpdateDataRequest, opts ...grpc.CallOption) (*Data, error) {
	out := new(Data)
	err := c.cc.Invoke(ctx, Datas_UpdateData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datasClient) DeleteData(ctx context.Context, in *DeleteDataRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Datas_DeleteData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datasClient) GetData(ctx context.Context, in *GetDataRequest, opts ...grpc.CallOption) (*Data, error) {
	out := new(Data)
	err := c.cc.Invoke(ctx, Datas_GetData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datasClient) ListData(ctx context.Context, in *ListDataRequest, opts ...grpc.CallOption) (*ListDataReply, error) {
	out := new(ListDataReply)
	err := c.cc.Invoke(ctx, Datas_ListData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datasClient) GetDataElements(ctx context.Context, in *GetDataElementsRequest, opts ...grpc.CallOption) (*GetDataElementsReply, error) {
	out := new(GetDataElementsReply)
	err := c.cc.Invoke(ctx, Datas_GetDataElements_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datasClient) FindDataElements(ctx context.Context, in *FindDataElementsRequest, opts ...grpc.CallOption) (*GetDataElementsReply, error) {
	out := new(GetDataElementsReply)
	err := c.cc.Invoke(ctx, Datas_FindDataElements_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datasClient) GetDataMeta(ctx context.Context, in *GetDataMetaRequest, opts ...grpc.CallOption) (*GetDataMetaReply, error) {
	out := new(GetDataMetaReply)
	err := c.cc.Invoke(ctx, Datas_GetDataMeta_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datasClient) SetRawdataEmbedding(ctx context.Context, in *SetRawdataEmbeddingRequest, opts ...grpc.CallOption) (*SetRawdataEmbeddingReply, error) {
	out := new(SetRawdataEmbeddingReply)
	err := c.cc.Invoke(ctx, Datas_SetRawdataEmbedding_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datasClient) GetDataValidationSummary(ctx context.Context, in *GetDataRequest, opts ...grpc.CallOption) (*v1.DataValidationSummary, error) {
	out := new(v1.DataValidationSummary)
	err := c.cc.Invoke(ctx, Datas_GetDataValidationSummary_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datasClient) ParseData(ctx context.Context, in *ParseDataRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Datas_ParseData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DatasServer is the server API for Datas service.
// All implementations must embed UnimplementedDatasServer
// for forward compatibility
type DatasServer interface {
	CreateData(context.Context, *CreateDataRequest) (*Data, error)
	UpdateData(context.Context, *UpdateDataRequest) (*Data, error)
	DeleteData(context.Context, *DeleteDataRequest) (*emptypb.Empty, error)
	GetData(context.Context, *GetDataRequest) (*Data, error)
	ListData(context.Context, *ListDataRequest) (*ListDataReply, error)
	// Fetch a list of elements
	GetDataElements(context.Context, *GetDataElementsRequest) (*GetDataElementsReply, error)
	// Find a list of elements (only names and indexes are returned)
	FindDataElements(context.Context, *FindDataElementsRequest) (*GetDataElementsReply, error)
	// Get data meta
	GetDataMeta(context.Context, *GetDataMetaRequest) (*GetDataMetaReply, error)
	// Set rawdata embedding.
	SetRawdataEmbedding(context.Context, *SetRawdataEmbeddingRequest) (*SetRawdataEmbeddingReply, error)
	// Get data validation summary
	GetDataValidationSummary(context.Context, *GetDataRequest) (*v1.DataValidationSummary, error)
	// Parse data
	ParseData(context.Context, *ParseDataRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedDatasServer()
}

// UnimplementedDatasServer must be embedded to have forward compatible implementations.
type UnimplementedDatasServer struct {
}

func (UnimplementedDatasServer) CreateData(context.Context, *CreateDataRequest) (*Data, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateData not implemented")
}
func (UnimplementedDatasServer) UpdateData(context.Context, *UpdateDataRequest) (*Data, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateData not implemented")
}
func (UnimplementedDatasServer) DeleteData(context.Context, *DeleteDataRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteData not implemented")
}
func (UnimplementedDatasServer) GetData(context.Context, *GetDataRequest) (*Data, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetData not implemented")
}
func (UnimplementedDatasServer) ListData(context.Context, *ListDataRequest) (*ListDataReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListData not implemented")
}
func (UnimplementedDatasServer) GetDataElements(context.Context, *GetDataElementsRequest) (*GetDataElementsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDataElements not implemented")
}
func (UnimplementedDatasServer) FindDataElements(context.Context, *FindDataElementsRequest) (*GetDataElementsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindDataElements not implemented")
}
func (UnimplementedDatasServer) GetDataMeta(context.Context, *GetDataMetaRequest) (*GetDataMetaReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDataMeta not implemented")
}
func (UnimplementedDatasServer) SetRawdataEmbedding(context.Context, *SetRawdataEmbeddingRequest) (*SetRawdataEmbeddingReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetRawdataEmbedding not implemented")
}
func (UnimplementedDatasServer) GetDataValidationSummary(context.Context, *GetDataRequest) (*v1.DataValidationSummary, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDataValidationSummary not implemented")
}
func (UnimplementedDatasServer) ParseData(context.Context, *ParseDataRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ParseData not implemented")
}
func (UnimplementedDatasServer) mustEmbedUnimplementedDatasServer() {}

// UnsafeDatasServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DatasServer will
// result in compilation errors.
type UnsafeDatasServer interface {
	mustEmbedUnimplementedDatasServer()
}

func RegisterDatasServer(s grpc.ServiceRegistrar, srv DatasServer) {
	s.RegisterService(&Datas_ServiceDesc, srv)
}

func _Datas_CreateData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatasServer).CreateData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Datas_CreateData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatasServer).CreateData(ctx, req.(*CreateDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Datas_UpdateData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatasServer).UpdateData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Datas_UpdateData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatasServer).UpdateData(ctx, req.(*UpdateDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Datas_DeleteData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatasServer).DeleteData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Datas_DeleteData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatasServer).DeleteData(ctx, req.(*DeleteDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Datas_GetData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatasServer).GetData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Datas_GetData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatasServer).GetData(ctx, req.(*GetDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Datas_ListData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatasServer).ListData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Datas_ListData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatasServer).ListData(ctx, req.(*ListDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Datas_GetDataElements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDataElementsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatasServer).GetDataElements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Datas_GetDataElements_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatasServer).GetDataElements(ctx, req.(*GetDataElementsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Datas_FindDataElements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindDataElementsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatasServer).FindDataElements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Datas_FindDataElements_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatasServer).FindDataElements(ctx, req.(*FindDataElementsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Datas_GetDataMeta_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDataMetaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatasServer).GetDataMeta(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Datas_GetDataMeta_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatasServer).GetDataMeta(ctx, req.(*GetDataMetaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Datas_SetRawdataEmbedding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetRawdataEmbeddingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatasServer).SetRawdataEmbedding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Datas_SetRawdataEmbedding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatasServer).SetRawdataEmbedding(ctx, req.(*SetRawdataEmbeddingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Datas_GetDataValidationSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatasServer).GetDataValidationSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Datas_GetDataValidationSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatasServer).GetDataValidationSummary(ctx, req.(*GetDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Datas_ParseData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ParseDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatasServer).ParseData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Datas_ParseData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatasServer).ParseData(ctx, req.(*ParseDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Datas_ServiceDesc is the grpc.ServiceDesc for Datas service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Datas_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "annofeed.v1.Datas",
	HandlerType: (*DatasServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateData",
			Handler:    _Datas_CreateData_Handler,
		},
		{
			MethodName: "UpdateData",
			Handler:    _Datas_UpdateData_Handler,
		},
		{
			MethodName: "DeleteData",
			Handler:    _Datas_DeleteData_Handler,
		},
		{
			MethodName: "GetData",
			Handler:    _Datas_GetData_Handler,
		},
		{
			MethodName: "ListData",
			Handler:    _Datas_ListData_Handler,
		},
		{
			MethodName: "GetDataElements",
			Handler:    _Datas_GetDataElements_Handler,
		},
		{
			MethodName: "FindDataElements",
			Handler:    _Datas_FindDataElements_Handler,
		},
		{
			MethodName: "GetDataMeta",
			Handler:    _Datas_GetDataMeta_Handler,
		},
		{
			MethodName: "SetRawdataEmbedding",
			Handler:    _Datas_SetRawdataEmbedding_Handler,
		},
		{
			MethodName: "GetDataValidationSummary",
			Handler:    _Datas_GetDataValidationSummary_Handler,
		},
		{
			MethodName: "ParseData",
			Handler:    _Datas_ParseData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "annofeed/v1/datas.proto",
}
