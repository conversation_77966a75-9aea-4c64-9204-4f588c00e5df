package biz

import (
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/kid"
)

type IDGetter interface {
	GetID() int64
}

type UidGetter interface {
	UidField() string
	GetUid() string
}

func GetModelsID[T IDGetter](mods ...[]T) []int64 {
	return kslice.Map(func(m T) int64 {
		return m.GetID()
	}, mods...)
}

func GetModelsUid[T UidGetter](mods ...[]T) []string {
	return kslice.Map(func(m T) string {
		return m.GetUid()
	}, mods...)
}

func MakeDictWithID[T IDGetter](mods ...[]T) map[int64]T {
	return kslice.MakeMap(func(m T) (int64, T) {
		return m.GetID(), m
	}, mods...)
}

func MakeDictWithUid[T UidGetter](mods ...[]T) map[string]T {
	return kslice.MakeMap(func(m T) (string, T) {
		return m.GetUid(), m
	}, mods...)
}

func GetUids(ids []int64) []string {
	return kslice.Map(kid.StringID, ids)
}

func GetIDs(uids []string) []int64 {
	return kslice.Map(kid.ParseID, uids)
}
