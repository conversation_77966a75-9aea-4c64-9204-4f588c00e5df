// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: annofeed/v1/meta.proto

package annofeed

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	anno "gitlab.rp.konvery.work/platform/apis/anno/v1"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = anno.Element_Type_Enum(0)
)

// Validate checks the field values on Meta with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Meta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Meta with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in MetaMultiError, or nil if none found.
func (m *Meta) ValidateAll() error {
	return m.validate(true)
}

func (m *Meta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Style

	for idx, item := range m.GetElems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MetaValidationError{
						field:  fmt.Sprintf("Elems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MetaValidationError{
						field:  fmt.Sprintf("Elems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MetaValidationError{
					field:  fmt.Sprintf("Elems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BaseOnUid

	// no validation rules for Metadata

	if all {
		switch v := interface{}(m.GetMetafiles()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MetaValidationError{
					field:  "Metafiles",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MetaValidationError{
					field:  "Metafiles",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetafiles()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MetaValidationError{
				field:  "Metafiles",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRelatedLot()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MetaValidationError{
					field:  "RelatedLot",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MetaValidationError{
					field:  "RelatedLot",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRelatedLot()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MetaValidationError{
				field:  "RelatedLot",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MetaMultiError(errors)
	}

	return nil
}

// MetaMultiError is an error wrapping multiple validation errors returned by
// Meta.ValidateAll() if the designated constraints aren't met.
type MetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MetaMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MetaMultiError) AllErrors() []error { return m }

// MetaValidationError is the validation error returned by Meta.Validate if the
// designated constraints aren't met.
type MetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MetaValidationError) ErrorName() string { return "MetaValidationError" }

// Error satisfies the builtin error interface
func (e MetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MetaValidationError{}

// Validate checks the field values on Meta_Lot with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Meta_Lot) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Meta_Lot with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Meta_LotMultiError, or nil
// if none found.
func (m *Meta_Lot) ValidateAll() error {
	return m.validate(true)
}

func (m *Meta_Lot) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Name

	if len(errors) > 0 {
		return Meta_LotMultiError(errors)
	}

	return nil
}

// Meta_LotMultiError is an error wrapping multiple validation errors returned
// by Meta_Lot.ValidateAll() if the designated constraints aren't met.
type Meta_LotMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Meta_LotMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Meta_LotMultiError) AllErrors() []error { return m }

// Meta_LotValidationError is the validation error returned by
// Meta_Lot.Validate if the designated constraints aren't met.
type Meta_LotValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Meta_LotValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Meta_LotValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Meta_LotValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Meta_LotValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Meta_LotValidationError) ErrorName() string { return "Meta_LotValidationError" }

// Error satisfies the builtin error interface
func (e Meta_LotValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMeta_Lot.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Meta_LotValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Meta_LotValidationError{}
