# Plush Stands on the Shoulders of Giants

Plush does not try to reinvent the wheel! Instead, it uses the already great wheels developed by the Go community and puts them all together in the best way possible. Without these giants, this project would not be possible. Please make sure to check them out and thank them for all of their hard work.

Thank you to the following **GIANTS**:

* [github.com/aymerick/douceur](https://godoc.org/github.com/aymerick/douceur)
* [github.com/davecgh/go-spew](https://godoc.org/github.com/davecgh/go-spew)
* [github.com/fatih/structs](https://godoc.org/github.com/fatih/structs)
* [github.com/gobuffalo/flect](https://godoc.org/github.com/gobuffalo/flect)
* [github.com/gobuffalo/github_flavored_markdown](https://godoc.org/github.com/gobuffalo/github_flavored_markdown)
* [github.com/gobuffalo/helpers](https://godoc.org/github.com/gobuffalo/helpers)
* [github.com/gobuffalo/tags/v3](https://godoc.org/github.com/gobuffalo/tags/v3)
* [github.com/gobuffalo/validate/v3](https://godoc.org/github.com/gobuffalo/validate/v3)
* [github.com/gofrs/uuid](https://godoc.org/github.com/gofrs/uuid)
* [github.com/gorilla/css](https://godoc.org/github.com/gorilla/css)
* [github.com/kr/pretty](https://godoc.org/github.com/kr/pretty)
* [github.com/kr/pty](https://godoc.org/github.com/kr/pty)
* [github.com/kr/text](https://godoc.org/github.com/kr/text)
* [github.com/microcosm-cc/bluemonday](https://godoc.org/github.com/microcosm-cc/bluemonday)
* [github.com/pmezard/go-difflib](https://godoc.org/github.com/pmezard/go-difflib)
* [github.com/sergi/go-diff](https://godoc.org/github.com/sergi/go-diff)
* [github.com/sourcegraph/annotate](https://godoc.org/github.com/sourcegraph/annotate)
* [github.com/sourcegraph/syntaxhighlight](https://godoc.org/github.com/sourcegraph/syntaxhighlight)
* [github.com/stretchr/objx](https://godoc.org/github.com/stretchr/objx)
* [github.com/stretchr/testify](https://godoc.org/github.com/stretchr/testify)
* [golang.org/x/net](https://godoc.org/golang.org/x/net)
* [golang.org/x/sync](https://godoc.org/golang.org/x/sync)
* [golang.org/x/sys](https://godoc.org/golang.org/x/sys)
* [golang.org/x/term](https://godoc.org/golang.org/x/term)
* [golang.org/x/text](https://godoc.org/golang.org/x/text)
* [golang.org/x/tools](https://godoc.org/golang.org/x/tools)
* [gopkg.in/check.v1](https://godoc.org/gopkg.in/check.v1)
* [gopkg.in/yaml.v2](https://godoc.org/gopkg.in/yaml.v2)
* [gopkg.in/yaml.v3](https://godoc.org/gopkg.in/yaml.v3)
