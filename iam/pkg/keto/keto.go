package keto

import (
	"context"
	"fmt"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"strings"

	"github.com/google/wire"
	keto "github.com/ory/keto/proto/ory/keto/relation_tuples/v1alpha2"
	"github.com/samber/lo"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"go.opentelemetry.io/otel/propagation"
	"google.golang.org/grpc"
)

var ProviderSet = wire.NewSet(NewKeto)

type (
	CheckRequest               = keto.CheckRequest
	RelationTuple              = keto.RelationTuple
	ExpandRequest              = keto.ExpandRequest
	ExpandResponse             = keto.ExpandResponse
	RelationTupleDelta         = keto.RelationTupleDelta
	Subject                    = keto.Subject
	ListRelationTuplesRequest  = keto.ListRelationTuplesRequest
	ListRelationTuplesResponse = keto.ListRelationTuplesResponse
	RelationQuery              = keto.RelationQuery
	SubjectTree                = keto.SubjectTree
)

type Config struct {
	ReadAddr  string
	WriteAddr string
	Logger    log.Logger
}

type Keto struct {
	checker  keto.CheckServiceClient
	expander keto.ExpandServiceClient
	writer   keto.WriteServiceClient
	reader   keto.ReadServiceClient
	log      *log.Helper
}

func NewKeto(cfg *Config) (*Keto, error) {
	propagator := propagation.NewCompositeTextMapPropagator(propagation.Baggage{}, propagation.TraceContext{})
	opts := []grpc.DialOption{
		grpc.WithInsecure(),
		grpc.WithUnaryInterceptor(otelgrpc.UnaryClientInterceptor(otelgrpc.WithPropagators(propagator))),
	}
	wconn, err := grpc.Dial(cfg.WriteAddr, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to keto writer: %w", err)
	}
	rconn, err := grpc.Dial(cfg.ReadAddr, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to keto reader: %w", err)
	}

	return &Keto{
		writer:   keto.NewWriteServiceClient(wconn),
		reader:   keto.NewReadServiceClient(rconn),
		checker:  keto.NewCheckServiceClient(rconn),
		expander: keto.NewExpandServiceClient(rconn),
		log:      log.NewHelper(cfg.Logger),
	}, nil
}

func (o *Keto) Check(ctx context.Context, req *keto.CheckRequest) (allowed bool, err error) {
	rsp, err := o.checker.Check(ctx, req)
	if err != nil {
		o.log.Error(ctx, "failed to check tuple", err, "tuple", FormatTuple(req.Tuple))
		return false, err
	}
	return rsp.Allowed, nil
}

func (o *Keto) transact(ctx context.Context, action keto.RelationTupleDelta_Action, tuples []*keto.RelationTuple) (err error) {
	deltas := make([]*RelationTupleDelta, len(tuples))
	for i, t := range tuples {
		deltas[i] = &RelationTupleDelta{
			Action:        action,
			RelationTuple: t,
		}
	}
	_, err = o.writer.TransactRelationTuples(ctx, &keto.TransactRelationTuplesRequest{
		RelationTupleDeltas: deltas,
	})
	if err == nil {
		tps := lo.Map(tuples, func(t *keto.RelationTuple, _ int) string { return FormatTuple(t) })
		o.log.Info(ctx, "keto_transact", "action", action, "count", len(tps), "tuples", strings.Join(tps, "; "))
	}
	return
}

func (o *Keto) Add(ctx context.Context, tuples ...*keto.RelationTuple) (err error) {
	tps := make([]*RelationTuple, 0, len(tuples))
	for _, t := range tuples {
		if ok, err := o.ExistTuple(ctx, t); err != nil {
			return err
		} else if !ok {
			tps = append(tps, t)
		}
	}
	if len(tps) == 0 {
		return nil
	}
	return o.transact(ctx, keto.RelationTupleDelta_ACTION_INSERT, tps)
}

func (o *Keto) Delete(ctx context.Context, tuples ...*keto.RelationTuple) (err error) {
	// for _, t := range tuples {
	// 	if IsSysObj(t.Object) && t.Subject == nil {
	// 		return ErrForbidden
	// 	}
	// }
	return o.transact(ctx, keto.RelationTupleDelta_ACTION_DELETE, tuples)
}

func (o *Keto) DeleteByQuery(ctx context.Context, query *keto.RelationQuery) (err error) {
	if query.Object != nil && IsSysObj(*query.Object) {
		return ErrForbidden
	}

	// try to do query and delete (all affected records will be logged)
	rsp, err := o.List(ctx, &keto.ListRelationTuplesRequest{RelationQuery: query})
	if err != nil {
		q := FormatTuple(QueryToTuple(query))
		return fmt.Errorf("failed to query relations %v for delete: %w", q, err)
	}
	if rsp.NextPageToken == "" {
		return o.Delete(ctx, rsp.RelationTuples...)
	}

	// too many tuples affected, fallback to delete-by-query
	q := FormatTuple(QueryToTuple(query))
	o.log.Warn(ctx, "too many tuples returned in query, fallback to delete-by-query", "query", q)
	tps := lo.Map(rsp.RelationTuples, func(t *keto.RelationTuple, _ int) string { return FormatTuple(t) })
	o.log.Info(ctx, "keto_delete_by_query", "tuples", strings.Join(tps, "; "))
	_, err = o.writer.DeleteRelationTuples(ctx, &keto.DeleteRelationTuplesRequest{RelationQuery: query})

	return
}

func (o *Keto) Expand(ctx context.Context, req *keto.ExpandRequest) (*keto.ExpandResponse, error) {
	return o.expander.Expand(ctx, req)
}

func (o *Keto) List(ctx context.Context, req *keto.ListRelationTuplesRequest) (*keto.ListRelationTuplesResponse, error) {
	return o.reader.ListRelationTuples(ctx, req)
}

func (o *Keto) DeleteObj(ctx context.Context, ns, obj string) (err error) {
	// try to do query and delete (all affected records will be logged and be deleted in a transaction)
	var tuples []*keto.RelationTuple
	q1 := NewQuery("", "", "", NewSubjectSet(ns, obj, ""))
	q2 := NewQuery(ns, obj, "", nil)
	rsp, err := o.List(ctx, &keto.ListRelationTuplesRequest{RelationQuery: q1})
	if err != nil {
		qs := FormatTuple(QueryToTuple(q1))
		return fmt.Errorf("failed to query relations %v for delete: %w", qs, err)
	}
	tuples = rsp.RelationTuples
	if rsp.NextPageToken == "" {
		rsp, err = o.List(ctx, &keto.ListRelationTuplesRequest{RelationQuery: q2})
		if err != nil {
			qs := FormatTuple(QueryToTuple(q2))
			return fmt.Errorf("failed to query relations %v for delete: %w", qs, err)
		}
		if rsp.NextPageToken == "" {
			tuples = append(tuples, rsp.RelationTuples...)
			return o.Delete(ctx, tuples...)
		}
	}
	tuples = append(tuples, rsp.RelationTuples...)

	// too many tuples affected, fallback to delete-by-query
	tps := lo.Map(tuples, func(t *keto.RelationTuple, _ int) string { return FormatTuple(t) })
	qs1 := FormatTuple(QueryToTuple(q1))
	qs2 := FormatTuple(QueryToTuple(q2))
	o.log.Warn(ctx, "too many tuples returned in queries, fallback to delete-by-query", "queries", []string{qs1, qs2})
	o.log.Info(ctx, "keto_delete_by_query", "tuples", strings.Join(tps, "; "))
	_, err = o.writer.DeleteRelationTuples(ctx, &keto.DeleteRelationTuplesRequest{RelationQuery: q1})
	if err != nil {
		return err
	}
	_, err = o.writer.DeleteRelationTuples(ctx, &keto.DeleteRelationTuplesRequest{RelationQuery: q2})
	return err
}

func (o *Keto) ExistTuple(ctx context.Context, tp *keto.RelationTuple) (bool, error) {
	req := &keto.ListRelationTuplesRequest{
		RelationQuery: TupleToQuery(tp),
	}
	rsp, err := o.reader.ListRelationTuples(ctx, req)
	if err != nil {
		return false, err
	}
	return len(rsp.RelationTuples) > 0, nil
}
