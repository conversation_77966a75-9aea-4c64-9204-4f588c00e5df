// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: types/nv.proto

package types

import (
	_ "github.com/google/gnostic/openapiv3"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Name struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *Name) Reset() {
	*x = Name{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_nv_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Name) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Name) ProtoMessage() {}

func (x *Name) ProtoReflect() protoreflect.Message {
	mi := &file_types_nv_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Name.ProtoReflect.Descriptor instead.
func (*Name) Descriptor() ([]byte, []int) {
	return file_types_nv_proto_rawDescGZIP(), []int{0}
}

func (x *Name) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type NameValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NameValue) Reset() {
	*x = NameValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_nv_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NameValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NameValue) ProtoMessage() {}

func (x *NameValue) ProtoReflect() protoreflect.Message {
	mi := &file_types_nv_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NameValue.ProtoReflect.Descriptor instead.
func (*NameValue) Descriptor() ([]byte, []int) {
	return file_types_nv_proto_rawDescGZIP(), []int{1}
}

func (x *NameValue) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NameValue) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

var File_types_nv_proto protoreflect.FileDescriptor

var file_types_nv_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x6e, 0x76, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x26, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x3a, 0x0a, 0xba, 0x47, 0x07, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x49, 0x0a,
	0x09, 0x4e, 0x61, 0x6d, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x12, 0xba, 0x47, 0x0f, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0xba, 0x01, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x3b, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x50, 0x01, 0x5a, 0x30, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x3b,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_types_nv_proto_rawDescOnce sync.Once
	file_types_nv_proto_rawDescData = file_types_nv_proto_rawDesc
)

func file_types_nv_proto_rawDescGZIP() []byte {
	file_types_nv_proto_rawDescOnce.Do(func() {
		file_types_nv_proto_rawDescData = protoimpl.X.CompressGZIP(file_types_nv_proto_rawDescData)
	})
	return file_types_nv_proto_rawDescData
}

var file_types_nv_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_types_nv_proto_goTypes = []interface{}{
	(*Name)(nil),      // 0: types.Name
	(*NameValue)(nil), // 1: types.NameValue
}
var file_types_nv_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_types_nv_proto_init() }
func file_types_nv_proto_init() {
	if File_types_nv_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_types_nv_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Name); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_nv_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NameValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_types_nv_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_types_nv_proto_goTypes,
		DependencyIndexes: file_types_nv_proto_depIdxs,
		MessageInfos:      file_types_nv_proto_msgTypes,
	}.Build()
	File_types_nv_proto = out.File
	file_types_nv_proto_rawDesc = nil
	file_types_nv_proto_goTypes = nil
	file_types_nv_proto_depIdxs = nil
}
