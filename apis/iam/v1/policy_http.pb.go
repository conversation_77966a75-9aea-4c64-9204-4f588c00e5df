// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: iam/v1/policy.proto

package iam

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationPoliciesCreatePolicy = "/iam.v1.Policies/CreatePolicy"
const OperationPoliciesDeletePolicy = "/iam.v1.Policies/DeletePolicy"
const OperationPoliciesGetAttachedPolicies = "/iam.v1.Policies/GetAttachedPolicies"
const OperationPoliciesGetPolicy = "/iam.v1.Policies/GetPolicy"
const OperationPoliciesUpdatePolicy = "/iam.v1.Policies/UpdatePolicy"

type PoliciesHTTPServer interface {
	// CreatePolicy Create and attach a policy to a resource
	CreatePolicy(context.Context, *CreatePolicyRequest) (*Policy, error)
	// DeletePolicy detach and delete the policy
	DeletePolicy(context.Context, *DeletePolicyRequest) (*emptypb.Empty, error)
	// GetAttachedPolicies get policies attached to a resource
	GetAttachedPolicies(context.Context, *GetAttachedPoliciesRequest) (*GetAttachedPoliciesReply, error)
	GetPolicy(context.Context, *GetPolicyRequest) (*Policy, error)
	UpdatePolicy(context.Context, *UpdatePolicyRequest) (*emptypb.Empty, error)
}

func RegisterPoliciesHTTPServer(s *http.Server, srv PoliciesHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/policies", _Policies_CreatePolicy0_HTTP_Handler(srv))
	r.PATCH("/v1/policies/{name}", _Policies_UpdatePolicy0_HTTP_Handler(srv))
	r.DELETE("/v1/policies/{name}", _Policies_DeletePolicy0_HTTP_Handler(srv))
	r.GET("/v1/policies/{name}", _Policies_GetPolicy0_HTTP_Handler(srv))
	r.GET("/v1/resources/{name}/policies", _Policies_GetAttachedPolicies0_HTTP_Handler(srv))
}

func _Policies_CreatePolicy0_HTTP_Handler(srv PoliciesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreatePolicyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPoliciesCreatePolicy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreatePolicy(ctx, req.(*CreatePolicyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Policy)
		return ctx.Result(200, reply)
	}
}

func _Policies_UpdatePolicy0_HTTP_Handler(srv PoliciesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdatePolicyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPoliciesUpdatePolicy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdatePolicy(ctx, req.(*UpdatePolicyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Policies_DeletePolicy0_HTTP_Handler(srv PoliciesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeletePolicyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPoliciesDeletePolicy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeletePolicy(ctx, req.(*DeletePolicyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Policies_GetPolicy0_HTTP_Handler(srv PoliciesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetPolicyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPoliciesGetPolicy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPolicy(ctx, req.(*GetPolicyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Policy)
		return ctx.Result(200, reply)
	}
}

func _Policies_GetAttachedPolicies0_HTTP_Handler(srv PoliciesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAttachedPoliciesRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPoliciesGetAttachedPolicies)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAttachedPolicies(ctx, req.(*GetAttachedPoliciesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAttachedPoliciesReply)
		return ctx.Result(200, reply)
	}
}

type PoliciesHTTPClient interface {
	CreatePolicy(ctx context.Context, req *CreatePolicyRequest, opts ...http.CallOption) (rsp *Policy, err error)
	DeletePolicy(ctx context.Context, req *DeletePolicyRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetAttachedPolicies(ctx context.Context, req *GetAttachedPoliciesRequest, opts ...http.CallOption) (rsp *GetAttachedPoliciesReply, err error)
	GetPolicy(ctx context.Context, req *GetPolicyRequest, opts ...http.CallOption) (rsp *Policy, err error)
	UpdatePolicy(ctx context.Context, req *UpdatePolicyRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type PoliciesHTTPClientImpl struct {
	cc *http.Client
}

func NewPoliciesHTTPClient(client *http.Client) PoliciesHTTPClient {
	return &PoliciesHTTPClientImpl{client}
}

func (c *PoliciesHTTPClientImpl) CreatePolicy(ctx context.Context, in *CreatePolicyRequest, opts ...http.CallOption) (*Policy, error) {
	var out Policy
	pattern := "/v1/policies"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPoliciesCreatePolicy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PoliciesHTTPClientImpl) DeletePolicy(ctx context.Context, in *DeletePolicyRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/policies/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPoliciesDeletePolicy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PoliciesHTTPClientImpl) GetAttachedPolicies(ctx context.Context, in *GetAttachedPoliciesRequest, opts ...http.CallOption) (*GetAttachedPoliciesReply, error) {
	var out GetAttachedPoliciesReply
	pattern := "/v1/resources/{name}/policies"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPoliciesGetAttachedPolicies))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PoliciesHTTPClientImpl) GetPolicy(ctx context.Context, in *GetPolicyRequest, opts ...http.CallOption) (*Policy, error) {
	var out Policy
	pattern := "/v1/policies/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPoliciesGetPolicy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PoliciesHTTPClientImpl) UpdatePolicy(ctx context.Context, in *UpdatePolicyRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/policies/{name}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPoliciesUpdatePolicy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
