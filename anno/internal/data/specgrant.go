// source: iam/v1/specgrant.proto
package data

import (
	"context"
	"strconv"

	"anno/internal/biz"

	"gitlab.rp.konvery.work/platform/pkg/errors"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type specgrantsRepo struct {
	data *Data
	log  *log.Helper
}

func NewSpecgrantsRepo(data *Data, logger log.Logger) biz.SpecgrantsRepo {
	return &specgrantsRepo{data: data, log: log.New<PERSON>elper(logger)}
}

func (o *specgrantsRepo) DoTx(ctx context.Context, fn func(ctx context.Context, tx biz.Tx) error) (err error) {
	return o.data.DoTx(ctx, fn)
}

func (o *specgrantsRepo) Create(ctx context.Context, p *biz.Specgrant) (*biz.Specgrant, error) {
	return Create(ctx, o.data, p)
}

func (o *specgrantsRepo) DeleteByID(ctx context.Context, id int64) error {
	return Delete(ctx, o.data, &biz.Specgrant{ID: id})
}

func (o *specgrantsRepo) DeleteByFilter(ctx context.Context, f *biz.SpecgrantFilter) (ids []int64, err error) {
	var mod *biz.Specgrant
	err = o.buildListQuery(ctx, f).Model(mod).
		Clauses(clause.Returning{Columns: []clause.Column{{Name: biz.SpecgrantSfldID.String()}}}).
		Delete(&ids).Error
	return ids, Convert(mod, err)

}

func (o *specgrantsRepo) buildListQuery(ctx context.Context, p *biz.SpecgrantFilter) *gorm.DB {
	q := o.data.WithCtx(ctx)
	if p.GrantorUid != "" {
		q = q.Where(biz.SpecgrantSfldGrantorUid.WithTable()+" = ?", p.GrantorUid)
	}
	if p.GranteeUid != "" {
		q = q.Where(biz.SpecgrantSfldGranteeUid.WithTable()+" = ?", p.GranteeUid)
	}
	if p.OrgUid != "" {
		q = q.Where(biz.SpecgrantSfldOrgUid.WithTable()+" = ?", p.OrgUid)
	}
	if p.ItemType != "" {
		q = q.Where(biz.SpecgrantSfldItemType.WithTable()+" = ?", p.ItemType)
	}
	if len(p.ItemIDs) > 0 {
		q = q.Where(biz.SpecgrantSfldItemID.WithTable()+" IN ?", p.ItemIDs)
	}
	return q
}

func (o *specgrantsRepo) List(ctx context.Context, p *biz.SpecgrantFilter, pager biz.Pager) (
	grants []*biz.Specgrant, nextPageToken string, err error) {
	q := o.buildListQuery(ctx, p).Order("id").Limit(pager.Pagesz)
	if pager.PageToken != "" {
		id, err := strconv.ParseInt(pager.PageToken.String(), 10, 64)
		if err != nil {
			return nil, "", errors.NewErrInvalidField(errors.WithFields("page_token"))
		}
		q = q.Where("id > ?", id)
	}
	err = q.Find(&grants).Error
	err = Convert(&biz.Specgrant{}, err)
	if err != nil {
		return
	}
	if len(grants) == pager.Pagesz && pager.Pagesz > 0 {
		nextPageToken = strconv.FormatInt(grants[len(grants)-1].ID, 10)
	}
	return
}

func (o *specgrantsRepo) Count(ctx context.Context, p *biz.SpecgrantFilter) (int, error) {
	mod := &biz.Specgrant{}
	var cnt int64
	q := o.buildListQuery(ctx, p)
	err := q.Model(mod).Count(&cnt).Error
	if err != nil {
		return 0, Convert(mod, err)
	}
	return int(cnt), nil
}
