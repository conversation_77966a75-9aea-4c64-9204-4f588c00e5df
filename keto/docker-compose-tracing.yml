###########################################################################
#######             FOR DEMONSTRATION PURPOSES ONLY                 #######
###########################################################################
#                                                                         #
# If you have not yet read the tutorial, do so now:                       #
#  https://www.ory.sh/docs/hydra/5min-tutorial                            #
#                                                                         #
# This set up is only for demonstration purposes. The login               #
# endpoint can only be used if you follow the steps in the tutorial.      #
#                                                                         #
###########################################################################
version: "3"
services:
  keto:
    depends_on:
      - jaeger
    environment:
      - TRACING_PROVIDER=jaeger
      - TRACING_PROVIDER_JAEGER_SAMPLING_SERVER_URL=http://jaeger:5778/sampling
      - TRACING_PROVIDER_JAEGER_LOCAL_AGENT_ADDRESS=jaeger:6831

  jaeger:
    image: jaegertracing/all-in-one:1.7.0
    ports:
      - "5775:5775/udp"
      - "6831:6831/udp"
      - "6832:6832/udp"
      - "5778:5778"
      - "16686:16686"
      - "14268:14268"
      - "9411:9411"
