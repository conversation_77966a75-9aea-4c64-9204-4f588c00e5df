// Copyright © 2023 Ory Corp
// SPDX-License-Identifier: Apache-2.0

// Package main ORY <PERSON>
//
// <PERSON><PERSON> is a cloud native access control server providing best-practice patterns (RBAC, ABAC, ACL, AWS IAM Policies, Kubernetes Roles, ...) via REST APIs.
//
//	Schemes: http, https
//	Host:
//	BasePath: /
//	Version: Latest
//	License: Apache 2.0 https://github.com/ory/keto/blob/master/LICENSE
//	Contact: ORY <<EMAIL>> https://www.ory.sh
//
//	Consumes:
//	- application/json
//
//	Produces:
//	- application/json
//
// swagger:meta
package main
