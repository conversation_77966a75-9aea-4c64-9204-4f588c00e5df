// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anyconn/v1/secret.proto

package anyconn

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Secret_State_Enum int32

const (
	Secret_State_unspecified Secret_State_Enum = 0
	// parsing the data specified in the source
	Secret_State_initializing Secret_State_Enum = 1
	// waiting for the secret related lot to be started
	Secret_State_waiting  Secret_State_Enum = 2
	Secret_State_ongoing  Secret_State_Enum = 3
	Secret_State_finished Secret_State_Enum = 4
	Secret_State_canceled Secret_State_Enum = 5
	// error occurred when parsing data specified by the source
	Secret_State_failed Secret_State_Enum = 6
)

// Enum value maps for Secret_State_Enum.
var (
	Secret_State_Enum_name = map[int32]string{
		0: "unspecified",
		1: "initializing",
		2: "waiting",
		3: "ongoing",
		4: "finished",
		5: "canceled",
		6: "failed",
	}
	Secret_State_Enum_value = map[string]int32{
		"unspecified":  0,
		"initializing": 1,
		"waiting":      2,
		"ongoing":      3,
		"finished":     4,
		"canceled":     5,
		"failed":       6,
	}
)

func (x Secret_State_Enum) Enum() *Secret_State_Enum {
	p := new(Secret_State_Enum)
	*p = x
	return p
}

func (x Secret_State_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Secret_State_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anyconn_v1_secret_proto_enumTypes[0].Descriptor()
}

func (Secret_State_Enum) Type() protoreflect.EnumType {
	return &file_anyconn_v1_secret_proto_enumTypes[0]
}

func (x Secret_State_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Secret_State_Enum.Descriptor instead.
func (Secret_State_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anyconn_v1_secret_proto_rawDescGZIP(), []int{6, 0, 0}
}

type CreateSecretRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory in update-requests
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// mandatory in create-requests;
	// pattern: ^[\\p{Han}\\w\\d-]{0,20}$
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// secret's organization; if empty, this is the requestor's organization
	OrgUid string `protobuf:"bytes,3,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
}

func (x *CreateSecretRequest) Reset() {
	*x = CreateSecretRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_secret_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSecretRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSecretRequest) ProtoMessage() {}

func (x *CreateSecretRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_secret_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSecretRequest.ProtoReflect.Descriptor instead.
func (*CreateSecretRequest) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_secret_proto_rawDescGZIP(), []int{0}
}

func (x *CreateSecretRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *CreateSecretRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateSecretRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

type UpdateSecretRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update contents
	Secret *Secret `protobuf:"bytes,1,opt,name=secret,proto3" json:"secret,omitempty"`
	// name of fileds to be updated
	Fields []string `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *UpdateSecretRequest) Reset() {
	*x = UpdateSecretRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_secret_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSecretRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSecretRequest) ProtoMessage() {}

func (x *UpdateSecretRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_secret_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSecretRequest.ProtoReflect.Descriptor instead.
func (*UpdateSecretRequest) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_secret_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateSecretRequest) GetSecret() *Secret {
	if x != nil {
		return x.Secret
	}
	return nil
}

func (x *UpdateSecretRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type DeleteSecretRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// secret UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *DeleteSecretRequest) Reset() {
	*x = DeleteSecretRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_secret_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSecretRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSecretRequest) ProtoMessage() {}

func (x *DeleteSecretRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_secret_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSecretRequest.ProtoReflect.Descriptor instead.
func (*DeleteSecretRequest) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_secret_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteSecretRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetSecretRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// secret UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *GetSecretRequest) Reset() {
	*x = GetSecretRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_secret_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecretRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecretRequest) ProtoMessage() {}

func (x *GetSecretRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_secret_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecretRequest.ProtoReflect.Descriptor instead.
func (*GetSecretRequest) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_secret_proto_rawDescGZIP(), []int{3}
}

func (x *GetSecretRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type ListSecretRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page   int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Pagesz int32 `protobuf:"varint,2,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// filter by orgnization
	OrgUid string `protobuf:"bytes,3,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// filter by creator
	CreatorUid string `protobuf:"bytes,4,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	// filter by name pattern
	NamePattern string `protobuf:"bytes,5,opt,name=name_pattern,json=namePattern,proto3" json:"name_pattern,omitempty"`
	// filter by secret state
	States []Secret_State_Enum `protobuf:"varint,6,rep,packed,name=states,proto3,enum=anyconn.v1.Secret_State_Enum" json:"states,omitempty"`
	// include secret's orgnization in the reply
	WithOrg bool `protobuf:"varint,7,opt,name=with_org,json=withOrg,proto3" json:"with_org,omitempty"`
}

func (x *ListSecretRequest) Reset() {
	*x = ListSecretRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_secret_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSecretRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSecretRequest) ProtoMessage() {}

func (x *ListSecretRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_secret_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSecretRequest.ProtoReflect.Descriptor instead.
func (*ListSecretRequest) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_secret_proto_rawDescGZIP(), []int{4}
}

func (x *ListSecretRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListSecretRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListSecretRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *ListSecretRequest) GetCreatorUid() string {
	if x != nil {
		return x.CreatorUid
	}
	return ""
}

func (x *ListSecretRequest) GetNamePattern() string {
	if x != nil {
		return x.NamePattern
	}
	return ""
}

func (x *ListSecretRequest) GetStates() []Secret_State_Enum {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *ListSecretRequest) GetWithOrg() bool {
	if x != nil {
		return x.WithOrg
	}
	return false
}

type ListSecretReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total   int32     `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Secrets []*Secret `protobuf:"bytes,2,rep,name=secrets,proto3" json:"secrets,omitempty"`
}

func (x *ListSecretReply) Reset() {
	*x = ListSecretReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_secret_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSecretReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSecretReply) ProtoMessage() {}

func (x *ListSecretReply) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_secret_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSecretReply.ProtoReflect.Descriptor instead.
func (*ListSecretReply) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_secret_proto_rawDescGZIP(), []int{5}
}

func (x *ListSecretReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListSecretReply) GetSecrets() []*Secret {
	if x != nil {
		return x.Secrets
	}
	return nil
}

type Secret struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// secret UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// secret name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// UID of the organization which the secret belongs to
	OrgUid string `protobuf:"bytes,3,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// UID of the data associated with the secret
	DataUid string `protobuf:"bytes,5,opt,name=data_uid,json=dataUid,proto3" json:"data_uid,omitempty"`
	// creator UID
	CreatorUid string `protobuf:"bytes,6,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	// number of elements in the secret
	Size int32 `protobuf:"varint,10,opt,name=size,proto3" json:"size,omitempty"`
	// secret state
	State Secret_State_Enum `protobuf:"varint,11,opt,name=state,proto3,enum=anyconn.v1.Secret_State_Enum" json:"state,omitempty"`
	// annotated object count (include interpolated ones); only available after lot is finished
	InsTotal int32 `protobuf:"varint,12,opt,name=ins_total,json=insTotal,proto3" json:"ins_total,omitempty"`
	// annotation result file URL
	AnnoResultUrl string `protobuf:"bytes,13,opt,name=anno_result_url,json=annoResultUrl,proto3" json:"anno_result_url,omitempty"`
	// when state is failed, this field will contain detailed error message
	Error string `protobuf:"bytes,14,opt,name=error,proto3" json:"error,omitempty"`
	// secret creation time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *Secret) Reset() {
	*x = Secret{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_secret_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Secret) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Secret) ProtoMessage() {}

func (x *Secret) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_secret_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Secret.ProtoReflect.Descriptor instead.
func (*Secret) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_secret_proto_rawDescGZIP(), []int{6}
}

func (x *Secret) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Secret) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Secret) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *Secret) GetDataUid() string {
	if x != nil {
		return x.DataUid
	}
	return ""
}

func (x *Secret) GetCreatorUid() string {
	if x != nil {
		return x.CreatorUid
	}
	return ""
}

func (x *Secret) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Secret) GetState() Secret_State_Enum {
	if x != nil {
		return x.State
	}
	return Secret_State_unspecified
}

func (x *Secret) GetInsTotal() int32 {
	if x != nil {
		return x.InsTotal
	}
	return 0
}

func (x *Secret) GetAnnoResultUrl() string {
	if x != nil {
		return x.AnnoResultUrl
	}
	return ""
}

func (x *Secret) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *Secret) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type Secret_State struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Secret_State) Reset() {
	*x = Secret_State{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_secret_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Secret_State) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Secret_State) ProtoMessage() {}

func (x *Secret_State) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_secret_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Secret_State.ProtoReflect.Descriptor instead.
func (*Secret_State) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_secret_proto_rawDescGZIP(), []int{6, 0}
}

var File_anyconn_v1_secret_proto protoreflect.FileDescriptor

var file_anyconn_v1_secret_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x61, 0x6e, 0x79, 0x63, 0x6f,
	0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x78, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69,
	0x64, 0x12, 0x31, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x1d, 0xfa, 0x42, 0x1a, 0x72, 0x18, 0x32, 0x16, 0x5e, 0x5b, 0x5c, 0x70, 0x7b, 0x48, 0x61, 0x6e,
	0x7d, 0x5c, 0x77, 0x5c, 0x64, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x32, 0x30, 0x7d, 0x24, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64, 0x3a, 0x03, 0xba,
	0x47, 0x00, 0x22, 0x70, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x6e, 0x79, 0x63,
	0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x06, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x3a, 0x15, 0xba,
	0x47, 0x12, 0xba, 0x01, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0xba, 0x01, 0x06, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x22, 0x27, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x24, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x22, 0xee, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x70,
	0x61, 0x67, 0x65, 0x73, 0x7a, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x74, 0x74, 0x65,
	0x72, 0x6e, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x45, 0x6e, 0x75,
	0x6d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x77, 0x69, 0x74,
	0x68, 0x5f, 0x6f, 0x72, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x77, 0x69, 0x74,
	0x68, 0x4f, 0x72, 0x67, 0x22, 0x55, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2c, 0x0a,
	0x07, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x52, 0x07, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x22, 0x89, 0x04, 0x0a, 0x06,
	0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x72, 0x67, 0x55, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x55, 0x69, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x33, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6e,
	0x73, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69,
	0x6e, 0x73, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x6e, 0x6e, 0x6f, 0x5f,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x61, 0x6e, 0x6e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x55, 0x72, 0x6c, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x1a, 0x74, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x6b, 0x0a, 0x04, 0x45, 0x6e, 0x75,
	0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x69,
	0x6e, 0x67, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x10,
	0x02, 0x12, 0x0b, 0x0a, 0x07, 0x6f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x10, 0x03, 0x12, 0x0c,
	0x0a, 0x08, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08,
	0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x66, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x10, 0x06, 0x3a, 0x2f, 0xba, 0x47, 0x2c, 0xba, 0x01, 0x03, 0x75, 0x69,
	0x64, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75,
	0x69, 0x64, 0xba, 0x01, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0xba, 0x01, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x32, 0xf2, 0x03, 0x0a, 0x07, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x12, 0x5b, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x12, 0x1f, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10,
	0x3a, 0x01, 0x2a, 0x22, 0x0b, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x12, 0x6d, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x12, 0x1f, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x12, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x06, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x32, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x73, 0x2f, 0x7b, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x2e, 0x75, 0x69, 0x64, 0x7d, 0x12,
	0x62, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12,
	0x1f, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13,
	0x2a, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2f, 0x7b, 0x75,
	0x69, 0x64, 0x7d, 0x12, 0x58, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x12, 0x1c, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12,
	0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x12, 0x11, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x5d, 0x0a,
	0x0a, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x1d, 0x2e, 0x61, 0x6e,
	0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x6e, 0x79,
	0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x13, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0d, 0x12,
	0x0b, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x42, 0x47, 0x0a, 0x0a,
	0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x37, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e,
	0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e,
	0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_anyconn_v1_secret_proto_rawDescOnce sync.Once
	file_anyconn_v1_secret_proto_rawDescData = file_anyconn_v1_secret_proto_rawDesc
)

func file_anyconn_v1_secret_proto_rawDescGZIP() []byte {
	file_anyconn_v1_secret_proto_rawDescOnce.Do(func() {
		file_anyconn_v1_secret_proto_rawDescData = protoimpl.X.CompressGZIP(file_anyconn_v1_secret_proto_rawDescData)
	})
	return file_anyconn_v1_secret_proto_rawDescData
}

var file_anyconn_v1_secret_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_anyconn_v1_secret_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_anyconn_v1_secret_proto_goTypes = []interface{}{
	(Secret_State_Enum)(0),        // 0: anyconn.v1.Secret.State.Enum
	(*CreateSecretRequest)(nil),   // 1: anyconn.v1.CreateSecretRequest
	(*UpdateSecretRequest)(nil),   // 2: anyconn.v1.UpdateSecretRequest
	(*DeleteSecretRequest)(nil),   // 3: anyconn.v1.DeleteSecretRequest
	(*GetSecretRequest)(nil),      // 4: anyconn.v1.GetSecretRequest
	(*ListSecretRequest)(nil),     // 5: anyconn.v1.ListSecretRequest
	(*ListSecretReply)(nil),       // 6: anyconn.v1.ListSecretReply
	(*Secret)(nil),                // 7: anyconn.v1.Secret
	(*Secret_State)(nil),          // 8: anyconn.v1.Secret.State
	(*timestamppb.Timestamp)(nil), // 9: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),         // 10: google.protobuf.Empty
}
var file_anyconn_v1_secret_proto_depIdxs = []int32{
	7,  // 0: anyconn.v1.UpdateSecretRequest.secret:type_name -> anyconn.v1.Secret
	0,  // 1: anyconn.v1.ListSecretRequest.states:type_name -> anyconn.v1.Secret.State.Enum
	7,  // 2: anyconn.v1.ListSecretReply.secrets:type_name -> anyconn.v1.Secret
	0,  // 3: anyconn.v1.Secret.state:type_name -> anyconn.v1.Secret.State.Enum
	9,  // 4: anyconn.v1.Secret.created_at:type_name -> google.protobuf.Timestamp
	1,  // 5: anyconn.v1.Secrets.CreateSecret:input_type -> anyconn.v1.CreateSecretRequest
	2,  // 6: anyconn.v1.Secrets.UpdateSecret:input_type -> anyconn.v1.UpdateSecretRequest
	3,  // 7: anyconn.v1.Secrets.DeleteSecret:input_type -> anyconn.v1.DeleteSecretRequest
	4,  // 8: anyconn.v1.Secrets.GetSecret:input_type -> anyconn.v1.GetSecretRequest
	5,  // 9: anyconn.v1.Secrets.ListSecret:input_type -> anyconn.v1.ListSecretRequest
	7,  // 10: anyconn.v1.Secrets.CreateSecret:output_type -> anyconn.v1.Secret
	7,  // 11: anyconn.v1.Secrets.UpdateSecret:output_type -> anyconn.v1.Secret
	10, // 12: anyconn.v1.Secrets.DeleteSecret:output_type -> google.protobuf.Empty
	7,  // 13: anyconn.v1.Secrets.GetSecret:output_type -> anyconn.v1.Secret
	6,  // 14: anyconn.v1.Secrets.ListSecret:output_type -> anyconn.v1.ListSecretReply
	10, // [10:15] is the sub-list for method output_type
	5,  // [5:10] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_anyconn_v1_secret_proto_init() }
func file_anyconn_v1_secret_proto_init() {
	if File_anyconn_v1_secret_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_anyconn_v1_secret_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSecretRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_secret_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSecretRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_secret_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSecretRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_secret_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecretRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_secret_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSecretRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_secret_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSecretReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_secret_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Secret); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_secret_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Secret_State); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anyconn_v1_secret_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_anyconn_v1_secret_proto_goTypes,
		DependencyIndexes: file_anyconn_v1_secret_proto_depIdxs,
		EnumInfos:         file_anyconn_v1_secret_proto_enumTypes,
		MessageInfos:      file_anyconn_v1_secret_proto_msgTypes,
	}.Build()
	File_anyconn_v1_secret_proto = out.File
	file_anyconn_v1_secret_proto_rawDesc = nil
	file_anyconn_v1_secret_proto_goTypes = nil
	file_anyconn_v1_secret_proto_depIdxs = nil
}
