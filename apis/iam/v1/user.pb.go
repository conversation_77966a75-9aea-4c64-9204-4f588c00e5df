// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: iam/v1/user.proto

package iam

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	types "gitlab.rp.konvery.work/platform/apis/types"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LoginRequest_AuthType_Enum int32

const (
	LoginRequest_AuthType_unspecified LoginRequest_AuthType_Enum = 0
	LoginRequest_AuthType_authcode    LoginRequest_AuthType_Enum = 1
	LoginRequest_AuthType_password    LoginRequest_AuthType_Enum = 2
	LoginRequest_AuthType_otp         LoginRequest_AuthType_Enum = 3
)

// Enum value maps for LoginRequest_AuthType_Enum.
var (
	LoginRequest_AuthType_Enum_name = map[int32]string{
		0: "unspecified",
		1: "authcode",
		2: "password",
		3: "otp",
	}
	LoginRequest_AuthType_Enum_value = map[string]int32{
		"unspecified": 0,
		"authcode":    1,
		"password":    2,
		"otp":         3,
	}
)

func (x LoginRequest_AuthType_Enum) Enum() *LoginRequest_AuthType_Enum {
	p := new(LoginRequest_AuthType_Enum)
	*p = x
	return p
}

func (x LoginRequest_AuthType_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoginRequest_AuthType_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_iam_v1_user_proto_enumTypes[0].Descriptor()
}

func (LoginRequest_AuthType_Enum) Type() protoreflect.EnumType {
	return &file_iam_v1_user_proto_enumTypes[0]
}

func (x LoginRequest_AuthType_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoginRequest_AuthType_Enum.Descriptor instead.
func (LoginRequest_AuthType_Enum) EnumDescriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{7, 0, 0}
}

type SendAuthCodeRequest_Purpose_Enum int32

const (
	SendAuthCodeRequest_Purpose_unspecified SendAuthCodeRequest_Purpose_Enum = 0
	SendAuthCodeRequest_Purpose_login       SendAuthCodeRequest_Purpose_Enum = 1
)

// Enum value maps for SendAuthCodeRequest_Purpose_Enum.
var (
	SendAuthCodeRequest_Purpose_Enum_name = map[int32]string{
		0: "unspecified",
		1: "login",
	}
	SendAuthCodeRequest_Purpose_Enum_value = map[string]int32{
		"unspecified": 0,
		"login":       1,
	}
)

func (x SendAuthCodeRequest_Purpose_Enum) Enum() *SendAuthCodeRequest_Purpose_Enum {
	p := new(SendAuthCodeRequest_Purpose_Enum)
	*p = x
	return p
}

func (x SendAuthCodeRequest_Purpose_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SendAuthCodeRequest_Purpose_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_iam_v1_user_proto_enumTypes[1].Descriptor()
}

func (SendAuthCodeRequest_Purpose_Enum) Type() protoreflect.EnumType {
	return &file_iam_v1_user_proto_enumTypes[1]
}

func (x SendAuthCodeRequest_Purpose_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SendAuthCodeRequest_Purpose_Enum.Descriptor instead.
func (SendAuthCodeRequest_Purpose_Enum) EnumDescriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{9, 0, 0}
}

type SendAuthCodeRequest_Channel_Enum int32

const (
	SendAuthCodeRequest_Channel_unspecified SendAuthCodeRequest_Channel_Enum = 0
	SendAuthCodeRequest_Channel_sms         SendAuthCodeRequest_Channel_Enum = 1
	SendAuthCodeRequest_Channel_email       SendAuthCodeRequest_Channel_Enum = 2
)

// Enum value maps for SendAuthCodeRequest_Channel_Enum.
var (
	SendAuthCodeRequest_Channel_Enum_name = map[int32]string{
		0: "unspecified",
		1: "sms",
		2: "email",
	}
	SendAuthCodeRequest_Channel_Enum_value = map[string]int32{
		"unspecified": 0,
		"sms":         1,
		"email":       2,
	}
)

func (x SendAuthCodeRequest_Channel_Enum) Enum() *SendAuthCodeRequest_Channel_Enum {
	p := new(SendAuthCodeRequest_Channel_Enum)
	*p = x
	return p
}

func (x SendAuthCodeRequest_Channel_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SendAuthCodeRequest_Channel_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_iam_v1_user_proto_enumTypes[2].Descriptor()
}

func (SendAuthCodeRequest_Channel_Enum) Type() protoreflect.EnumType {
	return &file_iam_v1_user_proto_enumTypes[2]
}

func (x SendAuthCodeRequest_Channel_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SendAuthCodeRequest_Channel_Enum.Descriptor instead.
func (SendAuthCodeRequest_Channel_Enum) EnumDescriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{9, 1, 0}
}

type CreateUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// mandatory in create-user requests
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// phone number: +8613412345678; mandatory in create-user requests
	Phone string `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone,omitempty"`
	// email: <EMAIL>
	Email  string `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	Avatar string `protobuf:"bytes,5,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// user's system role: member/admin/root
	Role string `protobuf:"bytes,6,opt,name=role,proto3" json:"role,omitempty"`
	// user's gender
	Gender User_Gender_Enum `protobuf:"varint,7,opt,name=gender,proto3,enum=iam.v1.User_Gender_Enum" json:"gender,omitempty"`
	// user's birthday in RFC339 format: 2010-06-07T00:00:00Z
	// use a string type to allow an empty birthday
	Birthday string `protobuf:"bytes,8,opt,name=birthday,proto3" json:"birthday,omitempty"`
	Province string `protobuf:"bytes,9,opt,name=province,proto3" json:"province,omitempty"`
	City     string `protobuf:"bytes,10,opt,name=city,proto3" json:"city,omitempty"`
}

func (x *CreateUserRequest) Reset() {
	*x = CreateUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserRequest) ProtoMessage() {}

func (x *CreateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserRequest.ProtoReflect.Descriptor instead.
func (*CreateUserRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{0}
}

func (x *CreateUserRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *CreateUserRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateUserRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *CreateUserRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateUserRequest) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *CreateUserRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *CreateUserRequest) GetGender() User_Gender_Enum {
	if x != nil {
		return x.Gender
	}
	return User_Gender_unspecified
}

func (x *CreateUserRequest) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *CreateUserRequest) GetProvince() string {
	if x != nil {
		return x.Province
	}
	return ""
}

func (x *CreateUserRequest) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

type BatchCreateUsersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users []*CreateUserRequest `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *BatchCreateUsersRequest) Reset() {
	*x = BatchCreateUsersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCreateUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateUsersRequest) ProtoMessage() {}

func (x *BatchCreateUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateUsersRequest.ProtoReflect.Descriptor instead.
func (*BatchCreateUsersRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{1}
}

func (x *BatchCreateUsersRequest) GetUsers() []*CreateUserRequest {
	if x != nil {
		return x.Users
	}
	return nil
}

type UpdateUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	User   *CreateUserRequest `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Fields []string           `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *UpdateUserRequest) Reset() {
	*x = UpdateUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserRequest) ProtoMessage() {}

func (x *UpdateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateUserRequest) GetUser() *CreateUserRequest {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UpdateUserRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type DeleteUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *DeleteUserRequest) Reset() {
	*x = DeleteUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserRequest) ProtoMessage() {}

func (x *DeleteUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteUserRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *GetUserRequest) Reset() {
	*x = GetUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRequest) ProtoMessage() {}

func (x *GetUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRequest.ProtoReflect.Descriptor instead.
func (*GetUserRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{4}
}

func (x *GetUserRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type ListUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page   int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Pagesz int32 `protobuf:"varint,2,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// find by name pattern
	NamePattern string `protobuf:"bytes,3,opt,name=name_pattern,json=namePattern,proto3" json:"name_pattern,omitempty"`
	// find by uid
	Uids []string `protobuf:"bytes,4,rep,name=uids,proto3" json:"uids,omitempty"`
	// find by phone number
	Phones []string `protobuf:"bytes,5,rep,name=phones,proto3" json:"phones,omitempty"`
	// find by emails
	Emails []string `protobuf:"bytes,6,rep,name=emails,proto3" json:"emails,omitempty"`
	// include user's organization in the reply
	WithOrg bool `protobuf:"varint,7,opt,name=with_org,json=withOrg,proto3" json:"with_org,omitempty"`
	// find by attached tags
	Tags []string `protobuf:"bytes,8,rep,name=tags,proto3" json:"tags,omitempty"`
	// find by org_uid
	OrgUid string `protobuf:"bytes,9,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// find by roles
	Roles []string `protobuf:"bytes,10,rep,name=roles,proto3" json:"roles,omitempty"`
}

func (x *ListUserRequest) Reset() {
	*x = ListUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserRequest) ProtoMessage() {}

func (x *ListUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserRequest.ProtoReflect.Descriptor instead.
func (*ListUserRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{5}
}

func (x *ListUserRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListUserRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListUserRequest) GetNamePattern() string {
	if x != nil {
		return x.NamePattern
	}
	return ""
}

func (x *ListUserRequest) GetUids() []string {
	if x != nil {
		return x.Uids
	}
	return nil
}

func (x *ListUserRequest) GetPhones() []string {
	if x != nil {
		return x.Phones
	}
	return nil
}

func (x *ListUserRequest) GetEmails() []string {
	if x != nil {
		return x.Emails
	}
	return nil
}

func (x *ListUserRequest) GetWithOrg() bool {
	if x != nil {
		return x.WithOrg
	}
	return false
}

func (x *ListUserRequest) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ListUserRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *ListUserRequest) GetRoles() []string {
	if x != nil {
		return x.Roles
	}
	return nil
}

type ListUserReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total int32   `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Users []*User `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
	// the organizations that the user, at the corresponding position, belongs to.
	Orgs []*BaseUser `protobuf:"bytes,3,rep,name=orgs,proto3" json:"orgs,omitempty"`
}

func (x *ListUserReply) Reset() {
	*x = ListUserReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUserReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserReply) ProtoMessage() {}

func (x *ListUserReply) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserReply.ProtoReflect.Descriptor instead.
func (*ListUserReply) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{6}
}

func (x *ListUserReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListUserReply) GetUsers() []*User {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *ListUserReply) GetOrgs() []*BaseUser {
	if x != nil {
		return x.Orgs
	}
	return nil
}

type LoginRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// type of user's identity
	IdType IDType_Enum `protobuf:"varint,1,opt,name=id_type,json=idType,proto3,enum=iam.v1.IDType_Enum" json:"id_type,omitempty"`
	// user's phone number-number/email/...
	Identity string `protobuf:"bytes,2,opt,name=identity,proto3" json:"identity,omitempty"`
	// type of the authentication
	AuthType LoginRequest_AuthType_Enum `protobuf:"varint,3,opt,name=auth_type,json=authType,proto3,enum=iam.v1.LoginRequest_AuthType_Enum" json:"auth_type,omitempty"`
	// authentication credential according to auth_type
	Credential string `protobuf:"bytes,4,opt,name=credential,proto3" json:"credential,omitempty"`
	// signed version of user agreement; 0 means no update
	Agreement int32 `protobuf:"varint,5,opt,name=agreement,proto3" json:"agreement,omitempty"`
}

func (x *LoginRequest) Reset() {
	*x = LoginRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginRequest) ProtoMessage() {}

func (x *LoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginRequest.ProtoReflect.Descriptor instead.
func (*LoginRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{7}
}

func (x *LoginRequest) GetIdType() IDType_Enum {
	if x != nil {
		return x.IdType
	}
	return IDType_unspecified
}

func (x *LoginRequest) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

func (x *LoginRequest) GetAuthType() LoginRequest_AuthType_Enum {
	if x != nil {
		return x.AuthType
	}
	return LoginRequest_AuthType_unspecified
}

func (x *LoginRequest) GetCredential() string {
	if x != nil {
		return x.Credential
	}
	return ""
}

func (x *LoginRequest) GetAgreement() int32 {
	if x != nil {
		return x.Agreement
	}
	return 0
}

type LoginReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user info; it may be an assumed user in an assume request
	User *User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	// JWT token
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	// token expire time in RFC3339 format: 2016-01-01T00:00:00+08:00
	ExpireTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	// front-end permissions
	Feperm *Feperm `protobuf:"bytes,4,opt,name=feperm,proto3" json:"feperm,omitempty"`
	// the real user in an assume request
	AssumeBy *User `protobuf:"bytes,5,opt,name=assume_by,json=assumeBy,proto3" json:"assume_by,omitempty"`
}

func (x *LoginReply) Reset() {
	*x = LoginReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginReply) ProtoMessage() {}

func (x *LoginReply) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginReply.ProtoReflect.Descriptor instead.
func (*LoginReply) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{8}
}

func (x *LoginReply) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *LoginReply) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *LoginReply) GetExpireTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpireTime
	}
	return nil
}

func (x *LoginReply) GetFeperm() *Feperm {
	if x != nil {
		return x.Feperm
	}
	return nil
}

func (x *LoginReply) GetAssumeBy() *User {
	if x != nil {
		return x.AssumeBy
	}
	return nil
}

type SendAuthCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// usage of the auth code
	Purpose SendAuthCodeRequest_Purpose_Enum `protobuf:"varint,1,opt,name=purpose,proto3,enum=iam.v1.SendAuthCodeRequest_Purpose_Enum" json:"purpose,omitempty"`
	// authentication code dispatch channel
	Channel SendAuthCodeRequest_Channel_Enum `protobuf:"varint,2,opt,name=channel,proto3,enum=iam.v1.SendAuthCodeRequest_Channel_Enum" json:"channel,omitempty"`
	// a phone number or email address according to the channel
	Receiver string `protobuf:"bytes,3,opt,name=receiver,proto3" json:"receiver,omitempty"`
	// language code: zh-Hans, en-US
	Locale string `protobuf:"bytes,4,opt,name=locale,proto3" json:"locale,omitempty"`
}

func (x *SendAuthCodeRequest) Reset() {
	*x = SendAuthCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendAuthCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendAuthCodeRequest) ProtoMessage() {}

func (x *SendAuthCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendAuthCodeRequest.ProtoReflect.Descriptor instead.
func (*SendAuthCodeRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{9}
}

func (x *SendAuthCodeRequest) GetPurpose() SendAuthCodeRequest_Purpose_Enum {
	if x != nil {
		return x.Purpose
	}
	return SendAuthCodeRequest_Purpose_unspecified
}

func (x *SendAuthCodeRequest) GetChannel() SendAuthCodeRequest_Channel_Enum {
	if x != nil {
		return x.Channel
	}
	return SendAuthCodeRequest_Channel_unspecified
}

func (x *SendAuthCodeRequest) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

func (x *SendAuthCodeRequest) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

type SendAuthCodeReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// sequence number of the auth code
	Sequence int32 `protobuf:"varint,1,opt,name=sequence,proto3" json:"sequence,omitempty"`
	// signed version of user agreement; 0 means not signed
	Agreement int32 `protobuf:"varint,2,opt,name=agreement,proto3" json:"agreement,omitempty"`
}

func (x *SendAuthCodeReply) Reset() {
	*x = SendAuthCodeReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendAuthCodeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendAuthCodeReply) ProtoMessage() {}

func (x *SendAuthCodeReply) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendAuthCodeReply.ProtoReflect.Descriptor instead.
func (*SendAuthCodeReply) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{10}
}

func (x *SendAuthCodeReply) GetSequence() int32 {
	if x != nil {
		return x.Sequence
	}
	return 0
}

func (x *SendAuthCodeReply) GetAgreement() int32 {
	if x != nil {
		return x.Agreement
	}
	return 0
}

type AssumeUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// uid of the user to assume; use "me" to unassume;
	// use "identity" to specify the user by his/her phone or email
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// when uid is "identity", this holds the phone or email of the user to assume
	Identity string `protobuf:"bytes,2,opt,name=identity,proto3" json:"identity,omitempty"`
}

func (x *AssumeUserRequest) Reset() {
	*x = AssumeUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssumeUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssumeUserRequest) ProtoMessage() {}

func (x *AssumeUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssumeUserRequest.ProtoReflect.Descriptor instead.
func (*AssumeUserRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{11}
}

func (x *AssumeUserRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *AssumeUserRequest) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

type IsAllowedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid    string                   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Action *IsAllowedRequest_Action `protobuf:"bytes,2,opt,name=action,proto3" json:"action,omitempty"`
}

func (x *IsAllowedRequest) Reset() {
	*x = IsAllowedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsAllowedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsAllowedRequest) ProtoMessage() {}

func (x *IsAllowedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsAllowedRequest.ProtoReflect.Descriptor instead.
func (*IsAllowedRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{12}
}

func (x *IsAllowedRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *IsAllowedRequest) GetAction() *IsAllowedRequest_Action {
	if x != nil {
		return x.Action
	}
	return nil
}

type IsAllowedReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// true if all of the actions are allowed; false otherwise
	Allowed bool `protobuf:"varint,1,opt,name=allowed,proto3" json:"allowed,omitempty"`
}

func (x *IsAllowedReply) Reset() {
	*x = IsAllowedReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsAllowedReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsAllowedReply) ProtoMessage() {}

func (x *IsAllowedReply) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsAllowedReply.ProtoReflect.Descriptor instead.
func (*IsAllowedReply) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{13}
}

func (x *IsAllowedReply) GetAllowed() bool {
	if x != nil {
		return x.Allowed
	}
	return false
}

type GetPermsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// resource name in format: type:uid; when scope is specified, uid may be omitted
	// pattern: "^\\w+:([\\w.-]+)?$"
	Resource string `protobuf:"bytes,2,opt,name=resource,proto3" json:"resource,omitempty"`
	// check if the user can create/list resources within an organization/team
	// format: IamGroup:uid, pattern: "^(IamGroup:[\\w-]+)?$"
	Scope string `protobuf:"bytes,3,opt,name=scope,proto3" json:"scope,omitempty"`
	// if perms is not empty, only check for the permissions in the list
	// if it is empty, check all permissions valid to the resource type.
	// pattern: "^\\w+\\.\\w+$"
	Perms []string `protobuf:"bytes,4,rep,name=perms,proto3" json:"perms,omitempty"`
}

func (x *GetPermsRequest) Reset() {
	*x = GetPermsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPermsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPermsRequest) ProtoMessage() {}

func (x *GetPermsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPermsRequest.ProtoReflect.Descriptor instead.
func (*GetPermsRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{14}
}

func (x *GetPermsRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *GetPermsRequest) GetResource() string {
	if x != nil {
		return x.Resource
	}
	return ""
}

func (x *GetPermsRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *GetPermsRequest) GetPerms() []string {
	if x != nil {
		return x.Perms
	}
	return nil
}

type GetPermsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Perms []string `protobuf:"bytes,1,rep,name=perms,proto3" json:"perms,omitempty"`
}

func (x *GetPermsReply) Reset() {
	*x = GetPermsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPermsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPermsReply) ProtoMessage() {}

func (x *GetPermsReply) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPermsReply.ProtoReflect.Descriptor instead.
func (*GetPermsReply) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{15}
}

func (x *GetPermsReply) GetPerms() []string {
	if x != nil {
		return x.Perms
	}
	return nil
}

type LoginRequest_AuthType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LoginRequest_AuthType) Reset() {
	*x = LoginRequest_AuthType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginRequest_AuthType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginRequest_AuthType) ProtoMessage() {}

func (x *LoginRequest_AuthType) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginRequest_AuthType.ProtoReflect.Descriptor instead.
func (*LoginRequest_AuthType) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{7, 0}
}

type SendAuthCodeRequest_Purpose struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendAuthCodeRequest_Purpose) Reset() {
	*x = SendAuthCodeRequest_Purpose{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendAuthCodeRequest_Purpose) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendAuthCodeRequest_Purpose) ProtoMessage() {}

func (x *SendAuthCodeRequest_Purpose) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendAuthCodeRequest_Purpose.ProtoReflect.Descriptor instead.
func (*SendAuthCodeRequest_Purpose) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{9, 0}
}

type SendAuthCodeRequest_Channel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendAuthCodeRequest_Channel) Reset() {
	*x = SendAuthCodeRequest_Channel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendAuthCodeRequest_Channel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendAuthCodeRequest_Channel) ProtoMessage() {}

func (x *SendAuthCodeRequest_Channel) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendAuthCodeRequest_Channel.ProtoReflect.Descriptor instead.
func (*SendAuthCodeRequest_Channel) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{9, 1}
}

type IsAllowedRequest_Action struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// resource name. format: type:(uid|IamGroup:team-uid)
	// pattern: "^\\w+:([\\w:.-]+)*$"
	Resource string `protobuf:"bytes,1,opt,name=resource,proto3" json:"resource,omitempty"`
	Perm     string `protobuf:"bytes,2,opt,name=perm,proto3" json:"perm,omitempty"`
}

func (x *IsAllowedRequest_Action) Reset() {
	*x = IsAllowedRequest_Action{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_user_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsAllowedRequest_Action) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsAllowedRequest_Action) ProtoMessage() {}

func (x *IsAllowedRequest_Action) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_user_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsAllowedRequest_Action.ProtoReflect.Descriptor instead.
func (*IsAllowedRequest_Action) Descriptor() ([]byte, []int) {
	return file_iam_v1_user_proto_rawDescGZIP(), []int{12, 0}
}

func (x *IsAllowedRequest_Action) GetResource() string {
	if x != nil {
		return x.Resource
	}
	return ""
}

func (x *IsAllowedRequest_Action) GetPerm() string {
	if x != nil {
		return x.Perm
	}
	return ""
}

var File_iam_v1_user_proto protoreflect.FileDescriptor

var file_iam_v1_user_proto_rawDesc = []byte{
	0x0a, 0x11, 0x69, 0x61, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x69, 0x61, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x0f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x74, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x99, 0x02, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x75, 0x6d,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69,
	0x74, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x22, 0x57,
	0x0a, 0x17, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x05, 0x75, 0x73, 0x65,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x3a, 0x0b, 0xba, 0x47, 0x08, 0xba,
	0x01, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22, 0x66, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x04,
	0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x73, 0x3a, 0x0a, 0xba, 0x47, 0x07, 0xba, 0x01, 0x04, 0x75, 0x73, 0x65, 0x72, 0x22,
	0x25, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x22, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0xe6, 0x02, 0x0a, 0x0f, 0x4c,
	0x69, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x22, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xf4, 0x03, 0x28, 0x00, 0x52, 0x06,
	0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x70,
	0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x61,
	0x6d, 0x65, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x2e, 0x0a, 0x04, 0x75, 0x69, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x92, 0x01, 0x14, 0x22,
	0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31,
	0x31, 0x7d, 0x24, 0x52, 0x04, 0x75, 0x69, 0x64, 0x73, 0x12, 0x2b, 0x0a, 0x06, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01,
	0x0d, 0x22, 0x0b, 0x72, 0x09, 0x32, 0x07, 0x5e, 0x5c, 0x2b, 0x5c, 0x64, 0x2b, 0x24, 0x52, 0x06,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x06, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x42, 0x25, 0xfa, 0x42, 0x22, 0x92, 0x01, 0x1f, 0x22, 0x1d,
	0x72, 0x1b, 0x32, 0x19, 0x5e, 0x5b, 0x5c, 0x77, 0x2e, 0x2d, 0x5d, 0x2b, 0x40, 0x5b, 0x5c, 0x77,
	0x2e, 0x2d, 0x5d, 0x2b, 0x5c, 0x2e, 0x5b, 0x5c, 0x77, 0x2e, 0x5d, 0x2b, 0x24, 0x52, 0x06, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x6f, 0x72,
	0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x77, 0x69, 0x74, 0x68, 0x4f, 0x72, 0x67,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x72, 0x6f,
	0x6c, 0x65, 0x73, 0x22, 0x7c, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x22, 0x0a, 0x05, 0x75, 0x73,
	0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12, 0x24,
	0x0a, 0x04, 0x6f, 0x72, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04,
	0x6f, 0x72, 0x67, 0x73, 0x3a, 0x0b, 0xba, 0x47, 0x08, 0xba, 0x01, 0x05, 0x75, 0x73, 0x65, 0x72,
	0x73, 0x22, 0xec, 0x02, 0x0a, 0x0c, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x38, 0x0a, 0x07, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x44, 0x54,
	0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x06, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x4b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x69, 0x61,
	0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x61, 0x75, 0x74,
	0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x1a, 0x48, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x3c, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x61, 0x75, 0x74, 0x68,
	0x63, 0x6f, 0x64, 0x65, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x10, 0x03, 0x3a, 0x31, 0xba,
	0x47, 0x2e, 0xba, 0x01, 0x07, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0xba, 0x01, 0x08, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0xba, 0x01, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0xba, 0x01, 0x0a, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c,
	0x22, 0xff, 0x01, 0x0a, 0x0a, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x20, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x3b, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x06, 0x66, 0x65, 0x70, 0x65, 0x72, 0x6d, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65,
	0x70, 0x65, 0x72, 0x6d, 0x52, 0x06, 0x66, 0x65, 0x70, 0x65, 0x72, 0x6d, 0x12, 0x29, 0x0a, 0x09,
	0x61, 0x73, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0c, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x08, 0x61,
	0x73, 0x73, 0x75, 0x6d, 0x65, 0x42, 0x79, 0x3a, 0x29, 0xba, 0x47, 0x26, 0xba, 0x01, 0x04, 0x75,
	0x73, 0x65, 0x72, 0xba, 0x01, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0xba, 0x01, 0x0b, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0xba, 0x01, 0x06, 0x66, 0x65, 0x70, 0x65,
	0x72, 0x6d, 0x22, 0xfd, 0x02, 0x0a, 0x13, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x75, 0x74, 0x68, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4e, 0x0a, 0x07, 0x70, 0x75,
	0x72, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x69, 0x61,
	0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20,
	0x00, 0x52, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x07, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x69, 0x61,
	0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20,
	0x00, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x65, 0x1a, 0x2d,
	0x0a, 0x07, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x22, 0x22, 0x0a, 0x04, 0x45, 0x6e, 0x75,
	0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x10, 0x01, 0x1a, 0x36, 0x0a,
	0x07, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0x2b, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d,
	0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10,
	0x00, 0x12, 0x07, 0x0a, 0x03, 0x73, 0x6d, 0x73, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x10, 0x02, 0x3a, 0x2b, 0xba, 0x47, 0x28, 0xba, 0x01, 0x07, 0x70, 0x75, 0x72,
	0x70, 0x6f, 0x73, 0x65, 0xba, 0x01, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0xba, 0x01,
	0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0xba, 0x01, 0x06, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x65, 0x22, 0x5d, 0x0a, 0x11, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x75, 0x74, 0x68, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x3a, 0x0e, 0xba, 0x47, 0x0b, 0xba, 0x01, 0x08, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x65, 0x22, 0x4c, 0x0a, 0x11, 0x41, 0x73, 0x73, 0x75, 0x6d, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x3a, 0x09, 0xba, 0x47, 0x06, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0x22,
	0xf0, 0x01, 0x0a, 0x10, 0x49, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x37, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a,
	0x7c, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x08, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xfa, 0x42, 0x15,
	0x72, 0x13, 0x32, 0x11, 0x5e, 0x5c, 0x77, 0x2b, 0x3a, 0x28, 0x5b, 0x5c, 0x77, 0x3a, 0x2e, 0x2d,
	0x5d, 0x2b, 0x29, 0x2a, 0x24, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x25, 0x0a, 0x04, 0x70, 0x65, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0xfa,
	0x42, 0x0e, 0x72, 0x0c, 0x32, 0x0a, 0x5e, 0x5c, 0x77, 0x2b, 0x5c, 0x2e, 0x5c, 0x77, 0x2b, 0x24,
	0x52, 0x04, 0x70, 0x65, 0x72, 0x6d, 0x3a, 0x15, 0xba, 0x47, 0x12, 0xba, 0x01, 0x08, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0xba, 0x01, 0x04, 0x70, 0x65, 0x72, 0x6d, 0x3a, 0x13, 0xba,
	0x47, 0x10, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x07, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x22, 0x39, 0x0a, 0x0e, 0x49, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x3a, 0x0d,
	0xba, 0x47, 0x0a, 0xba, 0x01, 0x07, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x22, 0xe7, 0x01,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15,
	0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d,
	0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x08, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0xfa, 0x42,
	0x14, 0x72, 0x12, 0x32, 0x10, 0x5e, 0x5c, 0x77, 0x2b, 0x3a, 0x28, 0x5b, 0x5c, 0x77, 0x2e, 0x2d,
	0x5d, 0x2b, 0x29, 0x3f, 0x24, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x32, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1c,
	0xfa, 0x42, 0x19, 0x72, 0x17, 0x32, 0x15, 0x5e, 0x28, 0x49, 0x61, 0x6d, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x3a, 0x5b, 0x5c, 0x77, 0x2e, 0x2d, 0x5d, 0x2b, 0x29, 0x3f, 0x24, 0x52, 0x05, 0x73, 0x63,
	0x6f, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x05, 0x70, 0x65, 0x72, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x16, 0xfa, 0x42, 0x13, 0x92, 0x01, 0x10, 0x22, 0x0e, 0x72, 0x0c, 0x32, 0x0a,
	0x5e, 0x5c, 0x77, 0x2b, 0x5c, 0x2e, 0x5c, 0x77, 0x2b, 0x24, 0x52, 0x05, 0x70, 0x65, 0x72, 0x6d,
	0x73, 0x3a, 0x14, 0xba, 0x47, 0x11, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x08, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x32, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x50, 0x65,
	0x72, 0x6d, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x65, 0x72, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x70, 0x65, 0x72, 0x6d, 0x73, 0x3a, 0x0b,
	0xba, 0x47, 0x08, 0xba, 0x01, 0x05, 0x70, 0x65, 0x72, 0x6d, 0x73, 0x32, 0x99, 0x0c, 0x0a, 0x05,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x4b, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x19, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0c,
	0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x22, 0x14, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x0e, 0x3a, 0x01, 0x2a, 0x22, 0x09, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65,
	0x72, 0x73, 0x12, 0x66, 0x0a, 0x10, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x1f, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1a,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x3a, 0x01, 0x2a, 0x22, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x73, 0x2f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x12, 0x59, 0x0a, 0x0a, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x19, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x0c, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x32,
	0x14, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x58, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x19, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x2a, 0x0f,
	0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12,
	0x43, 0x0a, 0x05, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x0c, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x22, 0x14,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0e, 0x12, 0x0c, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72,
	0x73, 0x2f, 0x6d, 0x65, 0x12, 0x4c, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x32, 0x12, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x13, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0x15, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x0f, 0x12, 0x0d, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2f, 0x6d,
	0x65, 0x32, 0x12, 0x52, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4d, 0x79, 0x46, 0x65, 0x70, 0x65, 0x72,
	0x6d, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x0e, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x46, 0x65, 0x70, 0x65, 0x72, 0x6d, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x15, 0x12, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2f, 0x6d, 0x65, 0x2f,
	0x66, 0x65, 0x70, 0x65, 0x72, 0x6d, 0x12, 0x48, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x16, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0c, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x12,
	0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d,
	0x12, 0x4d, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x12, 0x17, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x11, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x0b, 0x12, 0x09, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12,
	0x4e, 0x0a, 0x06, 0x41, 0x64, 0x64, 0x54, 0x61, 0x67, 0x12, 0x11, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x54, 0x61, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0e, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x61, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x21, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x1a, 0x13, 0x2f, 0x76, 0x31, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x74, 0x61, 0x67, 0x12,
	0x4b, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61, 0x67, 0x12, 0x11, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x61, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x0e, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x61, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x2a, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65,
	0x72, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x74, 0x61, 0x67, 0x12, 0x4d, 0x0a, 0x05,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x14, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x69, 0x61,
	0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x3a, 0x01, 0x2a, 0x22, 0x0f, 0x2f, 0x76, 0x31, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x73, 0x2f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x55, 0x0a, 0x06, 0x4c,
	0x6f, 0x67, 0x6f, 0x75, 0x74, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x3a, 0x01, 0x2a,
	0x22, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2f, 0x6c, 0x6f, 0x67, 0x6f,
	0x75, 0x74, 0x12, 0x6b, 0x0a, 0x0c, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x75, 0x74, 0x68, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x1b, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64,
	0x41, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x19, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x75, 0x74,
	0x68, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73,
	0x2f, 0x73, 0x65, 0x6e, 0x64, 0x2d, 0x61, 0x75, 0x74, 0x68, 0x2d, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x5b, 0x0a, 0x0a, 0x41, 0x73, 0x73, 0x75, 0x6d, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x19, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73, 0x73, 0x75, 0x6d, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1e, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x18, 0x12, 0x16, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2f,
	0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x61, 0x73, 0x73, 0x75, 0x6d, 0x65, 0x12, 0x5b, 0x0a, 0x0c,
	0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x12, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19,
	0x12, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x2f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x12, 0x61, 0x0a, 0x09, 0x49, 0x73, 0x41,
	0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12, 0x18, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x16, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x73, 0x41, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c,
	0x12, 0x1a, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64,
	0x7d, 0x2f, 0x69, 0x73, 0x2d, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12, 0x59, 0x0a, 0x08,
	0x47, 0x65, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x73, 0x12, 0x17, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x15, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65,
	0x72, 0x6d, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17,
	0x12, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64,
	0x7d, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x73, 0x42, 0x3b, 0x0a, 0x06, 0x69, 0x61, 0x6d, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x76, 0x31,
	0x3b, 0x69, 0x61, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_iam_v1_user_proto_rawDescOnce sync.Once
	file_iam_v1_user_proto_rawDescData = file_iam_v1_user_proto_rawDesc
)

func file_iam_v1_user_proto_rawDescGZIP() []byte {
	file_iam_v1_user_proto_rawDescOnce.Do(func() {
		file_iam_v1_user_proto_rawDescData = protoimpl.X.CompressGZIP(file_iam_v1_user_proto_rawDescData)
	})
	return file_iam_v1_user_proto_rawDescData
}

var file_iam_v1_user_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_iam_v1_user_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_iam_v1_user_proto_goTypes = []interface{}{
	(LoginRequest_AuthType_Enum)(0),       // 0: iam.v1.LoginRequest.AuthType.Enum
	(SendAuthCodeRequest_Purpose_Enum)(0), // 1: iam.v1.SendAuthCodeRequest.Purpose.Enum
	(SendAuthCodeRequest_Channel_Enum)(0), // 2: iam.v1.SendAuthCodeRequest.Channel.Enum
	(*CreateUserRequest)(nil),             // 3: iam.v1.CreateUserRequest
	(*BatchCreateUsersRequest)(nil),       // 4: iam.v1.BatchCreateUsersRequest
	(*UpdateUserRequest)(nil),             // 5: iam.v1.UpdateUserRequest
	(*DeleteUserRequest)(nil),             // 6: iam.v1.DeleteUserRequest
	(*GetUserRequest)(nil),                // 7: iam.v1.GetUserRequest
	(*ListUserRequest)(nil),               // 8: iam.v1.ListUserRequest
	(*ListUserReply)(nil),                 // 9: iam.v1.ListUserReply
	(*LoginRequest)(nil),                  // 10: iam.v1.LoginRequest
	(*LoginReply)(nil),                    // 11: iam.v1.LoginReply
	(*SendAuthCodeRequest)(nil),           // 12: iam.v1.SendAuthCodeRequest
	(*SendAuthCodeReply)(nil),             // 13: iam.v1.SendAuthCodeReply
	(*AssumeUserRequest)(nil),             // 14: iam.v1.AssumeUserRequest
	(*IsAllowedRequest)(nil),              // 15: iam.v1.IsAllowedRequest
	(*IsAllowedReply)(nil),                // 16: iam.v1.IsAllowedReply
	(*GetPermsRequest)(nil),               // 17: iam.v1.GetPermsRequest
	(*GetPermsReply)(nil),                 // 18: iam.v1.GetPermsReply
	(*LoginRequest_AuthType)(nil),         // 19: iam.v1.LoginRequest.AuthType
	(*SendAuthCodeRequest_Purpose)(nil),   // 20: iam.v1.SendAuthCodeRequest.Purpose
	(*SendAuthCodeRequest_Channel)(nil),   // 21: iam.v1.SendAuthCodeRequest.Channel
	(*IsAllowedRequest_Action)(nil),       // 22: iam.v1.IsAllowedRequest.Action
	(User_Gender_Enum)(0),                 // 23: iam.v1.User.Gender.Enum
	(*User)(nil),                          // 24: iam.v1.User
	(*BaseUser)(nil),                      // 25: iam.v1.BaseUser
	(IDType_Enum)(0),                      // 26: iam.v1.IDType.Enum
	(*timestamppb.Timestamp)(nil),         // 27: google.protobuf.Timestamp
	(*Feperm)(nil),                        // 28: iam.v1.Feperm
	(*emptypb.Empty)(nil),                 // 29: google.protobuf.Empty
	(*types.TagRequest)(nil),              // 30: types.TagRequest
	(*UserContext)(nil),                   // 31: iam.v1.UserContext
	(*types.TagList)(nil),                 // 32: types.TagList
}
var file_iam_v1_user_proto_depIdxs = []int32{
	23, // 0: iam.v1.CreateUserRequest.gender:type_name -> iam.v1.User.Gender.Enum
	3,  // 1: iam.v1.BatchCreateUsersRequest.users:type_name -> iam.v1.CreateUserRequest
	3,  // 2: iam.v1.UpdateUserRequest.user:type_name -> iam.v1.CreateUserRequest
	24, // 3: iam.v1.ListUserReply.users:type_name -> iam.v1.User
	25, // 4: iam.v1.ListUserReply.orgs:type_name -> iam.v1.BaseUser
	26, // 5: iam.v1.LoginRequest.id_type:type_name -> iam.v1.IDType.Enum
	0,  // 6: iam.v1.LoginRequest.auth_type:type_name -> iam.v1.LoginRequest.AuthType.Enum
	24, // 7: iam.v1.LoginReply.user:type_name -> iam.v1.User
	27, // 8: iam.v1.LoginReply.expire_time:type_name -> google.protobuf.Timestamp
	28, // 9: iam.v1.LoginReply.feperm:type_name -> iam.v1.Feperm
	24, // 10: iam.v1.LoginReply.assume_by:type_name -> iam.v1.User
	1,  // 11: iam.v1.SendAuthCodeRequest.purpose:type_name -> iam.v1.SendAuthCodeRequest.Purpose.Enum
	2,  // 12: iam.v1.SendAuthCodeRequest.channel:type_name -> iam.v1.SendAuthCodeRequest.Channel.Enum
	22, // 13: iam.v1.IsAllowedRequest.action:type_name -> iam.v1.IsAllowedRequest.Action
	3,  // 14: iam.v1.Users.CreateUser:input_type -> iam.v1.CreateUserRequest
	4,  // 15: iam.v1.Users.BatchCreateUsers:input_type -> iam.v1.BatchCreateUsersRequest
	5,  // 16: iam.v1.Users.UpdateUser:input_type -> iam.v1.UpdateUserRequest
	6,  // 17: iam.v1.Users.DeleteUser:input_type -> iam.v1.DeleteUserRequest
	29, // 18: iam.v1.Users.GetMe:input_type -> google.protobuf.Empty
	29, // 19: iam.v1.Users.GetMe2:input_type -> google.protobuf.Empty
	29, // 20: iam.v1.Users.GetMyFeperm:input_type -> google.protobuf.Empty
	7,  // 21: iam.v1.Users.GetUser:input_type -> iam.v1.GetUserRequest
	8,  // 22: iam.v1.Users.ListUser:input_type -> iam.v1.ListUserRequest
	30, // 23: iam.v1.Users.AddTag:input_type -> types.TagRequest
	30, // 24: iam.v1.Users.DeleteTag:input_type -> types.TagRequest
	10, // 25: iam.v1.Users.Login:input_type -> iam.v1.LoginRequest
	29, // 26: iam.v1.Users.Logout:input_type -> google.protobuf.Empty
	12, // 27: iam.v1.Users.SendAuthCode:input_type -> iam.v1.SendAuthCodeRequest
	14, // 28: iam.v1.Users.AssumeUser:input_type -> iam.v1.AssumeUserRequest
	29, // 29: iam.v1.Users.RefreshToken:input_type -> google.protobuf.Empty
	15, // 30: iam.v1.Users.IsAllowed:input_type -> iam.v1.IsAllowedRequest
	17, // 31: iam.v1.Users.GetPerms:input_type -> iam.v1.GetPermsRequest
	24, // 32: iam.v1.Users.CreateUser:output_type -> iam.v1.User
	9,  // 33: iam.v1.Users.BatchCreateUsers:output_type -> iam.v1.ListUserReply
	24, // 34: iam.v1.Users.UpdateUser:output_type -> iam.v1.User
	29, // 35: iam.v1.Users.DeleteUser:output_type -> google.protobuf.Empty
	24, // 36: iam.v1.Users.GetMe:output_type -> iam.v1.User
	31, // 37: iam.v1.Users.GetMe2:output_type -> iam.v1.UserContext
	28, // 38: iam.v1.Users.GetMyFeperm:output_type -> iam.v1.Feperm
	24, // 39: iam.v1.Users.GetUser:output_type -> iam.v1.User
	9,  // 40: iam.v1.Users.ListUser:output_type -> iam.v1.ListUserReply
	32, // 41: iam.v1.Users.AddTag:output_type -> types.TagList
	32, // 42: iam.v1.Users.DeleteTag:output_type -> types.TagList
	11, // 43: iam.v1.Users.Login:output_type -> iam.v1.LoginReply
	29, // 44: iam.v1.Users.Logout:output_type -> google.protobuf.Empty
	13, // 45: iam.v1.Users.SendAuthCode:output_type -> iam.v1.SendAuthCodeReply
	11, // 46: iam.v1.Users.AssumeUser:output_type -> iam.v1.LoginReply
	11, // 47: iam.v1.Users.RefreshToken:output_type -> iam.v1.LoginReply
	16, // 48: iam.v1.Users.IsAllowed:output_type -> iam.v1.IsAllowedReply
	18, // 49: iam.v1.Users.GetPerms:output_type -> iam.v1.GetPermsReply
	32, // [32:50] is the sub-list for method output_type
	14, // [14:32] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_iam_v1_user_proto_init() }
func file_iam_v1_user_proto_init() {
	if File_iam_v1_user_proto != nil {
		return
	}
	file_iam_v1_type_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_iam_v1_user_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCreateUsersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListUserReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendAuthCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendAuthCodeReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssumeUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsAllowedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsAllowedReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPermsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPermsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginRequest_AuthType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendAuthCodeRequest_Purpose); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendAuthCodeRequest_Channel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_user_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsAllowedRequest_Action); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_iam_v1_user_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_iam_v1_user_proto_goTypes,
		DependencyIndexes: file_iam_v1_user_proto_depIdxs,
		EnumInfos:         file_iam_v1_user_proto_enumTypes,
		MessageInfos:      file_iam_v1_user_proto_msgTypes,
	}.Build()
	File_iam_v1_user_proto = out.File
	file_iam_v1_user_proto_rawDesc = nil
	file_iam_v1_user_proto_goTypes = nil
	file_iam_v1_user_proto_depIdxs = nil
}
