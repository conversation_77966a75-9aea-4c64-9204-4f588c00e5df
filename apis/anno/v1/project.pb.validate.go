// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/project.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateProjectRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateProjectRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateProjectRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateProjectRequestMultiError, or nil if none found.
func (m *CreateProjectRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateProjectRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for Desc

	// no validation rules for Avatar

	if len(errors) > 0 {
		return CreateProjectRequestMultiError(errors)
	}

	return nil
}

// CreateProjectRequestMultiError is an error wrapping multiple validation
// errors returned by CreateProjectRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateProjectRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateProjectRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateProjectRequestMultiError) AllErrors() []error { return m }

// CreateProjectRequestValidationError is the validation error returned by
// CreateProjectRequest.Validate if the designated constraints aren't met.
type CreateProjectRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateProjectRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateProjectRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateProjectRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateProjectRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateProjectRequestValidationError) ErrorName() string {
	return "CreateProjectRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateProjectRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateProjectRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateProjectRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateProjectRequestValidationError{}

// Validate checks the field values on UpdateProjectRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateProjectRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateProjectRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateProjectRequestMultiError, or nil if none found.
func (m *UpdateProjectRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateProjectRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetProject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateProjectRequestValidationError{
					field:  "Project",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateProjectRequestValidationError{
					field:  "Project",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateProjectRequestValidationError{
				field:  "Project",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateProjectRequestMultiError(errors)
	}

	return nil
}

// UpdateProjectRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateProjectRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateProjectRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateProjectRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateProjectRequestMultiError) AllErrors() []error { return m }

// UpdateProjectRequestValidationError is the validation error returned by
// UpdateProjectRequest.Validate if the designated constraints aren't met.
type UpdateProjectRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateProjectRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateProjectRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateProjectRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateProjectRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateProjectRequestValidationError) ErrorName() string {
	return "UpdateProjectRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateProjectRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateProjectRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateProjectRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateProjectRequestValidationError{}

// Validate checks the field values on DeleteProjectRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteProjectRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteProjectRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteProjectRequestMultiError, or nil if none found.
func (m *DeleteProjectRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteProjectRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return DeleteProjectRequestMultiError(errors)
	}

	return nil
}

// DeleteProjectRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteProjectRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteProjectRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteProjectRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteProjectRequestMultiError) AllErrors() []error { return m }

// DeleteProjectRequestValidationError is the validation error returned by
// DeleteProjectRequest.Validate if the designated constraints aren't met.
type DeleteProjectRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteProjectRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteProjectRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteProjectRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteProjectRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteProjectRequestValidationError) ErrorName() string {
	return "DeleteProjectRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteProjectRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteProjectRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteProjectRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteProjectRequestValidationError{}

// Validate checks the field values on GetProjectRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetProjectRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetProjectRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetProjectRequestMultiError, or nil if none found.
func (m *GetProjectRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetProjectRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return GetProjectRequestMultiError(errors)
	}

	return nil
}

// GetProjectRequestMultiError is an error wrapping multiple validation errors
// returned by GetProjectRequest.ValidateAll() if the designated constraints
// aren't met.
type GetProjectRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetProjectRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetProjectRequestMultiError) AllErrors() []error { return m }

// GetProjectRequestValidationError is the validation error returned by
// GetProjectRequest.Validate if the designated constraints aren't met.
type GetProjectRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetProjectRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetProjectRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetProjectRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetProjectRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetProjectRequestValidationError) ErrorName() string {
	return "GetProjectRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetProjectRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetProjectRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetProjectRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetProjectRequestValidationError{}

// Validate checks the field values on ListProjectRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListProjectRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListProjectRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListProjectRequestMultiError, or nil if none found.
func (m *ListProjectRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListProjectRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListProjectRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for OrgUid

	// no validation rules for CreatorUid

	// no validation rules for NamePattern

	if len(errors) > 0 {
		return ListProjectRequestMultiError(errors)
	}

	return nil
}

// ListProjectRequestMultiError is an error wrapping multiple validation errors
// returned by ListProjectRequest.ValidateAll() if the designated constraints
// aren't met.
type ListProjectRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListProjectRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListProjectRequestMultiError) AllErrors() []error { return m }

// ListProjectRequestValidationError is the validation error returned by
// ListProjectRequest.Validate if the designated constraints aren't met.
type ListProjectRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListProjectRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListProjectRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListProjectRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListProjectRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListProjectRequestValidationError) ErrorName() string {
	return "ListProjectRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListProjectRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListProjectRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListProjectRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListProjectRequestValidationError{}

// Validate checks the field values on ListProjectReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListProjectReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListProjectReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListProjectReplyMultiError, or nil if none found.
func (m *ListProjectReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListProjectReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetProjects() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListProjectReplyValidationError{
						field:  fmt.Sprintf("Projects[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListProjectReplyValidationError{
						field:  fmt.Sprintf("Projects[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListProjectReplyValidationError{
					field:  fmt.Sprintf("Projects[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListProjectReplyMultiError(errors)
	}

	return nil
}

// ListProjectReplyMultiError is an error wrapping multiple validation errors
// returned by ListProjectReply.ValidateAll() if the designated constraints
// aren't met.
type ListProjectReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListProjectReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListProjectReplyMultiError) AllErrors() []error { return m }

// ListProjectReplyValidationError is the validation error returned by
// ListProjectReply.Validate if the designated constraints aren't met.
type ListProjectReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListProjectReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListProjectReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListProjectReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListProjectReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListProjectReplyValidationError) ErrorName() string { return "ListProjectReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListProjectReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListProjectReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListProjectReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListProjectReplyValidationError{}

// Validate checks the field values on Project with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Project) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Project with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ProjectMultiError, or nil if none found.
func (m *Project) ValidateAll() error {
	return m.validate(true)
}

func (m *Project) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for Desc

	// no validation rules for Avatar

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProjectValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProjectValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProjectValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProjectValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProjectValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProjectValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProjectMultiError(errors)
	}

	return nil
}

// ProjectMultiError is an error wrapping multiple validation errors returned
// by Project.ValidateAll() if the designated constraints aren't met.
type ProjectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProjectMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProjectMultiError) AllErrors() []error { return m }

// ProjectValidationError is the validation error returned by Project.Validate
// if the designated constraints aren't met.
type ProjectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProjectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProjectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProjectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProjectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProjectValidationError) ErrorName() string { return "ProjectValidationError" }

// Error satisfies the builtin error interface
func (e ProjectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProject.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProjectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProjectValidationError{}
