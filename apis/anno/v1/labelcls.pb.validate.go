// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/labelcls.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on DeleteLabelclsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteLabelclsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteLabelclsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteLabelclsRequestMultiError, or nil if none found.
func (m *DeleteLabelclsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteLabelclsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return DeleteLabelclsRequestMultiError(errors)
	}

	return nil
}

// DeleteLabelclsRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteLabelclsRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteLabelclsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteLabelclsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteLabelclsRequestMultiError) AllErrors() []error { return m }

// DeleteLabelclsRequestValidationError is the validation error returned by
// DeleteLabelclsRequest.Validate if the designated constraints aren't met.
type DeleteLabelclsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteLabelclsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteLabelclsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteLabelclsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteLabelclsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteLabelclsRequestValidationError) ErrorName() string {
	return "DeleteLabelclsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteLabelclsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteLabelclsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteLabelclsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteLabelclsRequestValidationError{}

// Validate checks the field values on GetLabelclsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLabelclsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLabelclsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLabelclsRequestMultiError, or nil if none found.
func (m *GetLabelclsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLabelclsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return GetLabelclsRequestMultiError(errors)
	}

	return nil
}

// GetLabelclsRequestMultiError is an error wrapping multiple validation errors
// returned by GetLabelclsRequest.ValidateAll() if the designated constraints
// aren't met.
type GetLabelclsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLabelclsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLabelclsRequestMultiError) AllErrors() []error { return m }

// GetLabelclsRequestValidationError is the validation error returned by
// GetLabelclsRequest.Validate if the designated constraints aren't met.
type GetLabelclsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLabelclsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLabelclsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLabelclsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLabelclsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLabelclsRequestValidationError) ErrorName() string {
	return "GetLabelclsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLabelclsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLabelclsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLabelclsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLabelclsRequestValidationError{}

// Validate checks the field values on ListLabelclsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListLabelclsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLabelclsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListLabelclsRequestMultiError, or nil if none found.
func (m *ListLabelclsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLabelclsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListLabelclsRequestMultiError(errors)
	}

	return nil
}

// ListLabelclsRequestMultiError is an error wrapping multiple validation
// errors returned by ListLabelclsRequest.ValidateAll() if the designated
// constraints aren't met.
type ListLabelclsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLabelclsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLabelclsRequestMultiError) AllErrors() []error { return m }

// ListLabelclsRequestValidationError is the validation error returned by
// ListLabelclsRequest.Validate if the designated constraints aren't met.
type ListLabelclsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLabelclsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLabelclsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLabelclsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLabelclsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLabelclsRequestValidationError) ErrorName() string {
	return "ListLabelclsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListLabelclsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLabelclsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLabelclsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLabelclsRequestValidationError{}

// Validate checks the field values on ListLabelclsReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListLabelclsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLabelclsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListLabelclsReplyMultiError, or nil if none found.
func (m *ListLabelclsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLabelclsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetLabelcls() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListLabelclsReplyValidationError{
						field:  fmt.Sprintf("Labelcls[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListLabelclsReplyValidationError{
						field:  fmt.Sprintf("Labelcls[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListLabelclsReplyValidationError{
					field:  fmt.Sprintf("Labelcls[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListLabelclsReplyMultiError(errors)
	}

	return nil
}

// ListLabelclsReplyMultiError is an error wrapping multiple validation errors
// returned by ListLabelclsReply.ValidateAll() if the designated constraints
// aren't met.
type ListLabelclsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLabelclsReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLabelclsReplyMultiError) AllErrors() []error { return m }

// ListLabelclsReplyValidationError is the validation error returned by
// ListLabelclsReply.Validate if the designated constraints aren't met.
type ListLabelclsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLabelclsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLabelclsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLabelclsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLabelclsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLabelclsReplyValidationError) ErrorName() string {
	return "ListLabelclsReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListLabelclsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLabelclsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLabelclsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLabelclsReplyValidationError{}

// Validate checks the field values on Labelcls with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Labelcls) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Labelcls with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LabelclsMultiError, or nil
// if none found.
func (m *Labelcls) ValidateAll() error {
	return m.validate(true)
}

func (m *Labelcls) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Langs

	if len(errors) > 0 {
		return LabelclsMultiError(errors)
	}

	return nil
}

// LabelclsMultiError is an error wrapping multiple validation errors returned
// by Labelcls.ValidateAll() if the designated constraints aren't met.
type LabelclsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LabelclsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LabelclsMultiError) AllErrors() []error { return m }

// LabelclsValidationError is the validation error returned by
// Labelcls.Validate if the designated constraints aren't met.
type LabelclsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LabelclsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LabelclsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LabelclsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LabelclsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LabelclsValidationError) ErrorName() string { return "LabelclsValidationError" }

// Error satisfies the builtin error interface
func (e LabelclsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLabelcls.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LabelclsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LabelclsValidationError{}
