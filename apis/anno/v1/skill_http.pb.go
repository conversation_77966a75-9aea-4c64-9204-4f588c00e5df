// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: anno/v1/skill.proto

package anno

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationSkillsAddTeamSkill = "/anno.v1.Skills/AddTeamSkill"
const OperationSkillsAddUsersSkill = "/anno.v1.Skills/AddUsersSkill"
const OperationSkillsCreateSkill = "/anno.v1.Skills/CreateSkill"
const OperationSkillsDeleteSkill = "/anno.v1.Skills/DeleteSkill"
const OperationSkillsDeleteTeamSkill = "/anno.v1.Skills/DeleteTeamSkill"
const OperationSkillsDeleteUsersSkill = "/anno.v1.Skills/DeleteUsersSkill"
const OperationSkillsGetSkill = "/anno.v1.Skills/GetSkill"
const OperationSkillsGetUserSkill = "/anno.v1.Skills/GetUserSkill"
const OperationSkillsListSkill = "/anno.v1.Skills/ListSkill"
const OperationSkillsListUsersSkill = "/anno.v1.Skills/ListUsersSkill"
const OperationSkillsUpdateSkill = "/anno.v1.Skills/UpdateSkill"

type SkillsHTTPServer interface {
	// AddTeamSkill add skills to users in a team
	AddTeamSkill(context.Context, *AddTeamSkillRequest) (*emptypb.Empty, error)
	AddUsersSkill(context.Context, *AddUsersSkillRequest) (*emptypb.Empty, error)
	CreateSkill(context.Context, *Skill) (*Skill, error)
	DeleteSkill(context.Context, *DeleteSkillRequest) (*emptypb.Empty, error)
	// DeleteTeamSkill delete skills from users in a team
	DeleteTeamSkill(context.Context, *DeleteTeamSkillRequest) (*emptypb.Empty, error)
	DeleteUsersSkill(context.Context, *DeleteUsersSkillRequest) (*emptypb.Empty, error)
	GetSkill(context.Context, *GetSkillRequest) (*Skill, error)
	// GetUserSkill
	// users skills
	//
	GetUserSkill(context.Context, *GetUserSkillRequest) (*GetUserSkillReply, error)
	ListSkill(context.Context, *ListSkillRequest) (*ListSkillReply, error)
	ListUsersSkill(context.Context, *ListUsersSkillRequest) (*ListUsersSkillReply, error)
	UpdateSkill(context.Context, *UpdateSkillRequest) (*Skill, error)
}

func RegisterSkillsHTTPServer(s *http.Server, srv SkillsHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/skills", _Skills_CreateSkill0_HTTP_Handler(srv))
	r.PATCH("/v1/skills/{skill.name}", _Skills_UpdateSkill0_HTTP_Handler(srv))
	r.DELETE("/v1/skills/{name}", _Skills_DeleteSkill0_HTTP_Handler(srv))
	r.GET("/v1/skills/{name}", _Skills_GetSkill0_HTTP_Handler(srv))
	r.GET("/v1/skills", _Skills_ListSkill0_HTTP_Handler(srv))
	r.GET("/v1/skills/users/{uid}", _Skills_GetUserSkill0_HTTP_Handler(srv))
	r.GET("/v1/skills/users", _Skills_ListUsersSkill0_HTTP_Handler(srv))
	r.POST("/v1/skills/users", _Skills_AddUsersSkill0_HTTP_Handler(srv))
	r.DELETE("/v1/skills/users", _Skills_DeleteUsersSkill0_HTTP_Handler(srv))
	r.POST("/v1/skills/teams/{uid}", _Skills_AddTeamSkill0_HTTP_Handler(srv))
	r.DELETE("/v1/skills/teams/{uid}", _Skills_DeleteTeamSkill0_HTTP_Handler(srv))
}

func _Skills_CreateSkill0_HTTP_Handler(srv SkillsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in Skill
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSkillsCreateSkill)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateSkill(ctx, req.(*Skill))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Skill)
		return ctx.Result(200, reply)
	}
}

func _Skills_UpdateSkill0_HTTP_Handler(srv SkillsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateSkillRequest
		if err := ctx.Bind(&in.Skill); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSkillsUpdateSkill)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateSkill(ctx, req.(*UpdateSkillRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Skill)
		return ctx.Result(200, reply)
	}
}

func _Skills_DeleteSkill0_HTTP_Handler(srv SkillsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteSkillRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSkillsDeleteSkill)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteSkill(ctx, req.(*DeleteSkillRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Skills_GetSkill0_HTTP_Handler(srv SkillsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSkillRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSkillsGetSkill)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSkill(ctx, req.(*GetSkillRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Skill)
		return ctx.Result(200, reply)
	}
}

func _Skills_ListSkill0_HTTP_Handler(srv SkillsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListSkillRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSkillsListSkill)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListSkill(ctx, req.(*ListSkillRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListSkillReply)
		return ctx.Result(200, reply)
	}
}

func _Skills_GetUserSkill0_HTTP_Handler(srv SkillsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserSkillRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSkillsGetUserSkill)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserSkill(ctx, req.(*GetUserSkillRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserSkillReply)
		return ctx.Result(200, reply)
	}
}

func _Skills_ListUsersSkill0_HTTP_Handler(srv SkillsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListUsersSkillRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSkillsListUsersSkill)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUsersSkill(ctx, req.(*ListUsersSkillRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListUsersSkillReply)
		return ctx.Result(200, reply)
	}
}

func _Skills_AddUsersSkill0_HTTP_Handler(srv SkillsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddUsersSkillRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSkillsAddUsersSkill)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddUsersSkill(ctx, req.(*AddUsersSkillRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Skills_DeleteUsersSkill0_HTTP_Handler(srv SkillsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteUsersSkillRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSkillsDeleteUsersSkill)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteUsersSkill(ctx, req.(*DeleteUsersSkillRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Skills_AddTeamSkill0_HTTP_Handler(srv SkillsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddTeamSkillRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSkillsAddTeamSkill)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddTeamSkill(ctx, req.(*AddTeamSkillRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Skills_DeleteTeamSkill0_HTTP_Handler(srv SkillsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteTeamSkillRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSkillsDeleteTeamSkill)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteTeamSkill(ctx, req.(*DeleteTeamSkillRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type SkillsHTTPClient interface {
	AddTeamSkill(ctx context.Context, req *AddTeamSkillRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	AddUsersSkill(ctx context.Context, req *AddUsersSkillRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CreateSkill(ctx context.Context, req *Skill, opts ...http.CallOption) (rsp *Skill, err error)
	DeleteSkill(ctx context.Context, req *DeleteSkillRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteTeamSkill(ctx context.Context, req *DeleteTeamSkillRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteUsersSkill(ctx context.Context, req *DeleteUsersSkillRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetSkill(ctx context.Context, req *GetSkillRequest, opts ...http.CallOption) (rsp *Skill, err error)
	GetUserSkill(ctx context.Context, req *GetUserSkillRequest, opts ...http.CallOption) (rsp *GetUserSkillReply, err error)
	ListSkill(ctx context.Context, req *ListSkillRequest, opts ...http.CallOption) (rsp *ListSkillReply, err error)
	ListUsersSkill(ctx context.Context, req *ListUsersSkillRequest, opts ...http.CallOption) (rsp *ListUsersSkillReply, err error)
	UpdateSkill(ctx context.Context, req *UpdateSkillRequest, opts ...http.CallOption) (rsp *Skill, err error)
}

type SkillsHTTPClientImpl struct {
	cc *http.Client
}

func NewSkillsHTTPClient(client *http.Client) SkillsHTTPClient {
	return &SkillsHTTPClientImpl{client}
}

func (c *SkillsHTTPClientImpl) AddTeamSkill(ctx context.Context, in *AddTeamSkillRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/skills/teams/{uid}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSkillsAddTeamSkill))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SkillsHTTPClientImpl) AddUsersSkill(ctx context.Context, in *AddUsersSkillRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/skills/users"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSkillsAddUsersSkill))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SkillsHTTPClientImpl) CreateSkill(ctx context.Context, in *Skill, opts ...http.CallOption) (*Skill, error) {
	var out Skill
	pattern := "/v1/skills"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSkillsCreateSkill))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SkillsHTTPClientImpl) DeleteSkill(ctx context.Context, in *DeleteSkillRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/skills/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSkillsDeleteSkill))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SkillsHTTPClientImpl) DeleteTeamSkill(ctx context.Context, in *DeleteTeamSkillRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/skills/teams/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSkillsDeleteTeamSkill))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SkillsHTTPClientImpl) DeleteUsersSkill(ctx context.Context, in *DeleteUsersSkillRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/skills/users"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSkillsDeleteUsersSkill))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SkillsHTTPClientImpl) GetSkill(ctx context.Context, in *GetSkillRequest, opts ...http.CallOption) (*Skill, error) {
	var out Skill
	pattern := "/v1/skills/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSkillsGetSkill))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SkillsHTTPClientImpl) GetUserSkill(ctx context.Context, in *GetUserSkillRequest, opts ...http.CallOption) (*GetUserSkillReply, error) {
	var out GetUserSkillReply
	pattern := "/v1/skills/users/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSkillsGetUserSkill))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SkillsHTTPClientImpl) ListSkill(ctx context.Context, in *ListSkillRequest, opts ...http.CallOption) (*ListSkillReply, error) {
	var out ListSkillReply
	pattern := "/v1/skills"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSkillsListSkill))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SkillsHTTPClientImpl) ListUsersSkill(ctx context.Context, in *ListUsersSkillRequest, opts ...http.CallOption) (*ListUsersSkillReply, error) {
	var out ListUsersSkillReply
	pattern := "/v1/skills/users"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSkillsListUsersSkill))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SkillsHTTPClientImpl) UpdateSkill(ctx context.Context, in *UpdateSkillRequest, opts ...http.CallOption) (*Skill, error) {
	var out Skill
	pattern := "/v1/skills/{skill.name}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSkillsUpdateSkill))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in.Skill, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
