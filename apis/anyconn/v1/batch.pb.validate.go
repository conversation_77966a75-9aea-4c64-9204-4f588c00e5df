// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anyconn/v1/batch.proto

package anyconn

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Source with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Source) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Source with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SourceMultiError, or nil if none found.
func (m *Source) ValidateAll() error {
	return m.validate(true)
}

func (m *Source) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetProprietary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SourceValidationError{
					field:  "Proprietary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SourceValidationError{
					field:  "Proprietary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProprietary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SourceValidationError{
				field:  "Proprietary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Style

	// no validation rules for IsFrameSeries

	// no validation rules for PlainSizeGb

	for idx, item := range m.GetErrorHandlers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SourceValidationError{
						field:  fmt.Sprintf("ErrorHandlers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SourceValidationError{
						field:  fmt.Sprintf("ErrorHandlers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SourceValidationError{
					field:  fmt.Sprintf("ErrorHandlers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SourceMultiError(errors)
	}

	return nil
}

// SourceMultiError is an error wrapping multiple validation errors returned by
// Source.ValidateAll() if the designated constraints aren't met.
type SourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SourceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SourceMultiError) AllErrors() []error { return m }

// SourceValidationError is the validation error returned by Source.Validate if
// the designated constraints aren't met.
type SourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SourceValidationError) ErrorName() string { return "SourceValidationError" }

// Error satisfies the builtin error interface
func (e SourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SourceValidationError{}

// Validate checks the field values on CreateBatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateBatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateBatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateBatchRequestMultiError, or nil if none found.
func (m *CreateBatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateBatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if !_CreateBatchRequest_Name_Pattern.MatchString(m.GetName()) {
		err := CreateBatchRequestValidationError{
			field:  "Name",
			reason: "value does not match regex pattern \"^[\\\\p{Han}\\\\w\\\\d-]{0,20}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for OrgUid

	if all {
		switch v := interface{}(m.GetSource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateBatchRequestValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateBatchRequestValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateBatchRequestValidationError{
				field:  "Source",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateBatchRequestMultiError(errors)
	}

	return nil
}

// CreateBatchRequestMultiError is an error wrapping multiple validation errors
// returned by CreateBatchRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateBatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateBatchRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateBatchRequestMultiError) AllErrors() []error { return m }

// CreateBatchRequestValidationError is the validation error returned by
// CreateBatchRequest.Validate if the designated constraints aren't met.
type CreateBatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateBatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateBatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateBatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateBatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateBatchRequestValidationError) ErrorName() string {
	return "CreateBatchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateBatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateBatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateBatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateBatchRequestValidationError{}

var _CreateBatchRequest_Name_Pattern = regexp.MustCompile("^[\\p{Han}\\w\\d-]{0,20}$")

// Validate checks the field values on UpdateBatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateBatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateBatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateBatchRequestMultiError, or nil if none found.
func (m *UpdateBatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateBatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBatch()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateBatchRequestValidationError{
					field:  "Batch",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateBatchRequestValidationError{
					field:  "Batch",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBatch()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateBatchRequestValidationError{
				field:  "Batch",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateBatchRequestMultiError(errors)
	}

	return nil
}

// UpdateBatchRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateBatchRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateBatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateBatchRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateBatchRequestMultiError) AllErrors() []error { return m }

// UpdateBatchRequestValidationError is the validation error returned by
// UpdateBatchRequest.Validate if the designated constraints aren't met.
type UpdateBatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateBatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateBatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateBatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateBatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateBatchRequestValidationError) ErrorName() string {
	return "UpdateBatchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateBatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateBatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateBatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateBatchRequestValidationError{}

// Validate checks the field values on DeleteBatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteBatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteBatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteBatchRequestMultiError, or nil if none found.
func (m *DeleteBatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteBatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return DeleteBatchRequestMultiError(errors)
	}

	return nil
}

// DeleteBatchRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteBatchRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteBatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteBatchRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteBatchRequestMultiError) AllErrors() []error { return m }

// DeleteBatchRequestValidationError is the validation error returned by
// DeleteBatchRequest.Validate if the designated constraints aren't met.
type DeleteBatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteBatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteBatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteBatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteBatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteBatchRequestValidationError) ErrorName() string {
	return "DeleteBatchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteBatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteBatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteBatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteBatchRequestValidationError{}

// Validate checks the field values on GetBatchRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetBatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBatchRequestMultiError, or nil if none found.
func (m *GetBatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return GetBatchRequestMultiError(errors)
	}

	return nil
}

// GetBatchRequestMultiError is an error wrapping multiple validation errors
// returned by GetBatchRequest.ValidateAll() if the designated constraints
// aren't met.
type GetBatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBatchRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBatchRequestMultiError) AllErrors() []error { return m }

// GetBatchRequestValidationError is the validation error returned by
// GetBatchRequest.Validate if the designated constraints aren't met.
type GetBatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBatchRequestValidationError) ErrorName() string { return "GetBatchRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetBatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBatchRequestValidationError{}

// Validate checks the field values on ListBatchRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListBatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListBatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListBatchRequestMultiError, or nil if none found.
func (m *ListBatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListBatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for Pagesz

	// no validation rules for OrgUid

	// no validation rules for CreatorUid

	// no validation rules for NamePattern

	// no validation rules for WithOrg

	if len(errors) > 0 {
		return ListBatchRequestMultiError(errors)
	}

	return nil
}

// ListBatchRequestMultiError is an error wrapping multiple validation errors
// returned by ListBatchRequest.ValidateAll() if the designated constraints
// aren't met.
type ListBatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListBatchRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListBatchRequestMultiError) AllErrors() []error { return m }

// ListBatchRequestValidationError is the validation error returned by
// ListBatchRequest.Validate if the designated constraints aren't met.
type ListBatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListBatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListBatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListBatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListBatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListBatchRequestValidationError) ErrorName() string { return "ListBatchRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListBatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListBatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListBatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListBatchRequestValidationError{}

// Validate checks the field values on ListBatchReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListBatchReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListBatchReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListBatchReplyMultiError,
// or nil if none found.
func (m *ListBatchReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListBatchReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetBatches() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListBatchReplyValidationError{
						field:  fmt.Sprintf("Batches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListBatchReplyValidationError{
						field:  fmt.Sprintf("Batches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListBatchReplyValidationError{
					field:  fmt.Sprintf("Batches[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListBatchReplyMultiError(errors)
	}

	return nil
}

// ListBatchReplyMultiError is an error wrapping multiple validation errors
// returned by ListBatchReply.ValidateAll() if the designated constraints
// aren't met.
type ListBatchReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListBatchReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListBatchReplyMultiError) AllErrors() []error { return m }

// ListBatchReplyValidationError is the validation error returned by
// ListBatchReply.Validate if the designated constraints aren't met.
type ListBatchReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListBatchReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListBatchReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListBatchReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListBatchReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListBatchReplyValidationError) ErrorName() string { return "ListBatchReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListBatchReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListBatchReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListBatchReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListBatchReplyValidationError{}

// Validate checks the field values on Batch with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Batch) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Batch with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in BatchMultiError, or nil if none found.
func (m *Batch) ValidateAll() error {
	return m.validate(true)
}

func (m *Batch) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for OrgUid

	if all {
		switch v := interface{}(m.GetSource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchValidationError{
				field:  "Source",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DataUid

	// no validation rules for CreatorUid

	// no validation rules for Size

	// no validation rules for State

	// no validation rules for InsTotal

	// no validation rules for AnnoResultUrl

	// no validation rules for Error

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDataSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchValidationError{
					field:  "DataSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchValidationError{
					field:  "DataSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDataSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchValidationError{
				field:  "DataSummary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BatchMultiError(errors)
	}

	return nil
}

// BatchMultiError is an error wrapping multiple validation errors returned by
// Batch.ValidateAll() if the designated constraints aren't met.
type BatchMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchMultiError) AllErrors() []error { return m }

// BatchValidationError is the validation error returned by Batch.Validate if
// the designated constraints aren't met.
type BatchValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchValidationError) ErrorName() string { return "BatchValidationError" }

// Error satisfies the builtin error interface
func (e BatchValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatch.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchValidationError{}

// Validate checks the field values on SetBatchAnnoResultRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetBatchAnnoResultRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetBatchAnnoResultRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetBatchAnnoResultRequestMultiError, or nil if none found.
func (m *SetBatchAnnoResultRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetBatchAnnoResultRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Url

	if len(errors) > 0 {
		return SetBatchAnnoResultRequestMultiError(errors)
	}

	return nil
}

// SetBatchAnnoResultRequestMultiError is an error wrapping multiple validation
// errors returned by SetBatchAnnoResultRequest.ValidateAll() if the
// designated constraints aren't met.
type SetBatchAnnoResultRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetBatchAnnoResultRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetBatchAnnoResultRequestMultiError) AllErrors() []error { return m }

// SetBatchAnnoResultRequestValidationError is the validation error returned by
// SetBatchAnnoResultRequest.Validate if the designated constraints aren't met.
type SetBatchAnnoResultRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetBatchAnnoResultRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetBatchAnnoResultRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetBatchAnnoResultRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetBatchAnnoResultRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetBatchAnnoResultRequestValidationError) ErrorName() string {
	return "SetBatchAnnoResultRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SetBatchAnnoResultRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetBatchAnnoResultRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetBatchAnnoResultRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetBatchAnnoResultRequestValidationError{}

// Validate checks the field values on GetBatchAnnoResultReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBatchAnnoResultReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBatchAnnoResultReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBatchAnnoResultReplyMultiError, or nil if none found.
func (m *GetBatchAnnoResultReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBatchAnnoResultReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetWillReadyAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBatchAnnoResultReplyValidationError{
					field:  "WillReadyAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBatchAnnoResultReplyValidationError{
					field:  "WillReadyAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWillReadyAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBatchAnnoResultReplyValidationError{
				field:  "WillReadyAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Url

	if len(errors) > 0 {
		return GetBatchAnnoResultReplyMultiError(errors)
	}

	return nil
}

// GetBatchAnnoResultReplyMultiError is an error wrapping multiple validation
// errors returned by GetBatchAnnoResultReply.ValidateAll() if the designated
// constraints aren't met.
type GetBatchAnnoResultReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBatchAnnoResultReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBatchAnnoResultReplyMultiError) AllErrors() []error { return m }

// GetBatchAnnoResultReplyValidationError is the validation error returned by
// GetBatchAnnoResultReply.Validate if the designated constraints aren't met.
type GetBatchAnnoResultReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBatchAnnoResultReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBatchAnnoResultReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBatchAnnoResultReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBatchAnnoResultReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBatchAnnoResultReplyValidationError) ErrorName() string {
	return "GetBatchAnnoResultReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetBatchAnnoResultReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBatchAnnoResultReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBatchAnnoResultReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBatchAnnoResultReplyValidationError{}

// Validate checks the field values on DataValidationSummary with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DataValidationSummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataValidationSummary with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DataValidationSummaryMultiError, or nil if none found.
func (m *DataValidationSummary) ValidateAll() error {
	return m.validate(true)
}

func (m *DataValidationSummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalErrors

	for idx, item := range m.GetErrors() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DataValidationSummaryValidationError{
						field:  fmt.Sprintf("Errors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DataValidationSummaryValidationError{
						field:  fmt.Sprintf("Errors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DataValidationSummaryValidationError{
					field:  fmt.Sprintf("Errors[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DataValidationSummaryMultiError(errors)
	}

	return nil
}

// DataValidationSummaryMultiError is an error wrapping multiple validation
// errors returned by DataValidationSummary.ValidateAll() if the designated
// constraints aren't met.
type DataValidationSummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataValidationSummaryMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataValidationSummaryMultiError) AllErrors() []error { return m }

// DataValidationSummaryValidationError is the validation error returned by
// DataValidationSummary.Validate if the designated constraints aren't met.
type DataValidationSummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataValidationSummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataValidationSummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataValidationSummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataValidationSummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataValidationSummaryValidationError) ErrorName() string {
	return "DataValidationSummaryValidationError"
}

// Error satisfies the builtin error interface
func (e DataValidationSummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataValidationSummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataValidationSummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataValidationSummaryValidationError{}

// Validate checks the field values on Source_Proprietary with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Source_Proprietary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Source_Proprietary with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Source_ProprietaryMultiError, or nil if none found.
func (m *Source_Proprietary) ValidateAll() error {
	return m.validate(true)
}

func (m *Source_Proprietary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Config

	if len(errors) > 0 {
		return Source_ProprietaryMultiError(errors)
	}

	return nil
}

// Source_ProprietaryMultiError is an error wrapping multiple validation errors
// returned by Source_Proprietary.ValidateAll() if the designated constraints
// aren't met.
type Source_ProprietaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Source_ProprietaryMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Source_ProprietaryMultiError) AllErrors() []error { return m }

// Source_ProprietaryValidationError is the validation error returned by
// Source_Proprietary.Validate if the designated constraints aren't met.
type Source_ProprietaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Source_ProprietaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Source_ProprietaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Source_ProprietaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Source_ProprietaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Source_ProprietaryValidationError) ErrorName() string {
	return "Source_ProprietaryValidationError"
}

// Error satisfies the builtin error interface
func (e Source_ProprietaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSource_Proprietary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Source_ProprietaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Source_ProprietaryValidationError{}

// Validate checks the field values on Source_ParseErrorHandler with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Source_ParseErrorHandler) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Source_ParseErrorHandler with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Source_ParseErrorHandlerMultiError, or nil if none found.
func (m *Source_ParseErrorHandler) ValidateAll() error {
	return m.validate(true)
}

func (m *Source_ParseErrorHandler) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Error

	// no validation rules for Handler

	if len(errors) > 0 {
		return Source_ParseErrorHandlerMultiError(errors)
	}

	return nil
}

// Source_ParseErrorHandlerMultiError is an error wrapping multiple validation
// errors returned by Source_ParseErrorHandler.ValidateAll() if the designated
// constraints aren't met.
type Source_ParseErrorHandlerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Source_ParseErrorHandlerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Source_ParseErrorHandlerMultiError) AllErrors() []error { return m }

// Source_ParseErrorHandlerValidationError is the validation error returned by
// Source_ParseErrorHandler.Validate if the designated constraints aren't met.
type Source_ParseErrorHandlerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Source_ParseErrorHandlerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Source_ParseErrorHandlerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Source_ParseErrorHandlerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Source_ParseErrorHandlerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Source_ParseErrorHandlerValidationError) ErrorName() string {
	return "Source_ParseErrorHandlerValidationError"
}

// Error satisfies the builtin error interface
func (e Source_ParseErrorHandlerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSource_ParseErrorHandler.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Source_ParseErrorHandlerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Source_ParseErrorHandlerValidationError{}

// Validate checks the field values on Source_ParseErrorHandler_Error with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Source_ParseErrorHandler_Error) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Source_ParseErrorHandler_Error with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// Source_ParseErrorHandler_ErrorMultiError, or nil if none found.
func (m *Source_ParseErrorHandler_Error) ValidateAll() error {
	return m.validate(true)
}

func (m *Source_ParseErrorHandler_Error) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Source_ParseErrorHandler_ErrorMultiError(errors)
	}

	return nil
}

// Source_ParseErrorHandler_ErrorMultiError is an error wrapping multiple
// validation errors returned by Source_ParseErrorHandler_Error.ValidateAll()
// if the designated constraints aren't met.
type Source_ParseErrorHandler_ErrorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Source_ParseErrorHandler_ErrorMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Source_ParseErrorHandler_ErrorMultiError) AllErrors() []error { return m }

// Source_ParseErrorHandler_ErrorValidationError is the validation error
// returned by Source_ParseErrorHandler_Error.Validate if the designated
// constraints aren't met.
type Source_ParseErrorHandler_ErrorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Source_ParseErrorHandler_ErrorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Source_ParseErrorHandler_ErrorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Source_ParseErrorHandler_ErrorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Source_ParseErrorHandler_ErrorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Source_ParseErrorHandler_ErrorValidationError) ErrorName() string {
	return "Source_ParseErrorHandler_ErrorValidationError"
}

// Error satisfies the builtin error interface
func (e Source_ParseErrorHandler_ErrorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSource_ParseErrorHandler_Error.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Source_ParseErrorHandler_ErrorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Source_ParseErrorHandler_ErrorValidationError{}

// Validate checks the field values on Source_ParseErrorHandler_Handler with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *Source_ParseErrorHandler_Handler) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Source_ParseErrorHandler_Handler with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// Source_ParseErrorHandler_HandlerMultiError, or nil if none found.
func (m *Source_ParseErrorHandler_Handler) ValidateAll() error {
	return m.validate(true)
}

func (m *Source_ParseErrorHandler_Handler) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Source_ParseErrorHandler_HandlerMultiError(errors)
	}

	return nil
}

// Source_ParseErrorHandler_HandlerMultiError is an error wrapping multiple
// validation errors returned by
// Source_ParseErrorHandler_Handler.ValidateAll() if the designated
// constraints aren't met.
type Source_ParseErrorHandler_HandlerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Source_ParseErrorHandler_HandlerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Source_ParseErrorHandler_HandlerMultiError) AllErrors() []error { return m }

// Source_ParseErrorHandler_HandlerValidationError is the validation error
// returned by Source_ParseErrorHandler_Handler.Validate if the designated
// constraints aren't met.
type Source_ParseErrorHandler_HandlerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Source_ParseErrorHandler_HandlerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Source_ParseErrorHandler_HandlerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Source_ParseErrorHandler_HandlerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Source_ParseErrorHandler_HandlerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Source_ParseErrorHandler_HandlerValidationError) ErrorName() string {
	return "Source_ParseErrorHandler_HandlerValidationError"
}

// Error satisfies the builtin error interface
func (e Source_ParseErrorHandler_HandlerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSource_ParseErrorHandler_Handler.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Source_ParseErrorHandler_HandlerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Source_ParseErrorHandler_HandlerValidationError{}

// Validate checks the field values on Batch_State with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Batch_State) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Batch_State with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Batch_StateMultiError, or
// nil if none found.
func (m *Batch_State) ValidateAll() error {
	return m.validate(true)
}

func (m *Batch_State) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Batch_StateMultiError(errors)
	}

	return nil
}

// Batch_StateMultiError is an error wrapping multiple validation errors
// returned by Batch_State.ValidateAll() if the designated constraints aren't met.
type Batch_StateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Batch_StateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Batch_StateMultiError) AllErrors() []error { return m }

// Batch_StateValidationError is the validation error returned by
// Batch_State.Validate if the designated constraints aren't met.
type Batch_StateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Batch_StateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Batch_StateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Batch_StateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Batch_StateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Batch_StateValidationError) ErrorName() string { return "Batch_StateValidationError" }

// Error satisfies the builtin error interface
func (e Batch_StateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatch_State.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Batch_StateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Batch_StateValidationError{}

// Validate checks the field values on DataValidationSummary_Error with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DataValidationSummary_Error) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataValidationSummary_Error with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DataValidationSummary_ErrorMultiError, or nil if none found.
func (m *DataValidationSummary_Error) ValidateAll() error {
	return m.validate(true)
}

func (m *DataValidationSummary_Error) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Error

	// no validation rules for ElemIndex

	// no validation rules for RawdataName

	if len(errors) > 0 {
		return DataValidationSummary_ErrorMultiError(errors)
	}

	return nil
}

// DataValidationSummary_ErrorMultiError is an error wrapping multiple
// validation errors returned by DataValidationSummary_Error.ValidateAll() if
// the designated constraints aren't met.
type DataValidationSummary_ErrorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataValidationSummary_ErrorMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataValidationSummary_ErrorMultiError) AllErrors() []error { return m }

// DataValidationSummary_ErrorValidationError is the validation error returned
// by DataValidationSummary_Error.Validate if the designated constraints
// aren't met.
type DataValidationSummary_ErrorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataValidationSummary_ErrorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataValidationSummary_ErrorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataValidationSummary_ErrorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataValidationSummary_ErrorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataValidationSummary_ErrorValidationError) ErrorName() string {
	return "DataValidationSummary_ErrorValidationError"
}

// Error satisfies the builtin error interface
func (e DataValidationSummary_ErrorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataValidationSummary_Error.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataValidationSummary_ErrorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataValidationSummary_ErrorValidationError{}
