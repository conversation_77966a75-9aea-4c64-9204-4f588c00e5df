import json
import sys
import os
import math
import numpy as np

sys.path.append(".")
from customer.common import tools

err_files = {}
current_file = ""
jpg_count = 0

FORWARD = "forward"
LEFT = "left"
RIGHT = "right"
UTURN = "uturn"
MERGE_LEFT = "merge_left"
MERGE_RIGHT = "merge_right"

type_dict = {
    "speed_bump": "speed_bump",

    "lane": "lane",
    "park_line": "lane",
    "conventional_lane": "lane",

    "gostraight": ["arrow", FORWARD],
    "Left": ["arrow", LEFT],
    "Turnright": ["arrow", RIGHT],
    "Turn": ["arrow", UTURN],
    "Turnleft_right": ["arrow", LEFT, RIGHT],
    "Gostraight_turnright": ["arrow", FORWARD, RIGHT],
    "Gostraight_MakeaU_turn": ["arrow", FORWARD, UTURN],
    "Gostraight_turnleft": ["arrow", FORWARD, LEFT],
    "Confluencetotheleft": ["arrow", MERGE_LEFT],
    "Confluencetotheright": ["arrow", MERGE_RIGHT],
    "Turnleft_MakeaU_turn": ["arrow", LEFT, UTURN],
    "Gostraight_turnleft_turnright": ["arrow", FORWARD, LEFT, RIGHT]
}


def convert(test_in_data, hm_data, out_file):
    if test_in_data["label_meta"]["mark_status"] != 0:
        file_name = out_file.split(os.sep)[-1]
        print(f"{file_name} is not markable")
        return

    label_data = test_in_data["labels"]
    group_data = get_group_data(label_data)

    out_data = {
        "schema_version": "2.0",
        "project": hm_data["project"],
        "label_project_id": "ap_freespace_4d",
        "label_rule_version": "1.0",
        "label_producer_id": "YC",
        "label_clip_id": hm_data["label_clip_id"],
        "label_clip_version": hm_data["label_clip_version"],
        "label_sensors": "multi_lidar_pc_elevation_bev",
        "carid": hm_data["carid"],
        "trigger_name": hm_data["trigger_name"],
        "trigger_time": hm_data["trigger_time"],
        "stamp": hm_data["stamp"],

        "hardware_config_path": hm_data["hardware_config_path"],
        "hardware_config_http_path": hm_data["hardware_config_http_path"],
        "can_oss_path": hm_data["can_oss_path"],
        "can_http_path": hm_data["can_http_path"],
        "sensor_oss_path": hm_data["sensor_oss_path"],
        "sensor_http_path": hm_data["sensor_http_path"],
        "bundle_oss_path": hm_data["bundle_oss_path"],

        "rtk": hm_data["rtk"],
        "camera": hm_data["camera"],
        "lidar": hm_data["lidar"],
        "lidar_merge": hm_data["lidar_merge"],
        "camera_synthetic_bev": hm_data["camera_synthetic_bev"],
        "labeled_data": {"attributes": {},
                         "groups": group_data},
    }

    with open(out_file, "w") as f:
        json.dump(out_data, f, indent=4)


def get_arrow_data(label_data, sub_type):
    """
    "annotation": {
        "type": "box", // 标注类型，矩形为box
        "data": {
          "x": 354, // 矩形框左上点x值
          "y": 459, // 矩形框左上点y值
          "width": 43, // 矩形框宽度
          "height": 22, // 矩形框高度
          "rotate": 0 // 矩形框绕中心点旋转的角度，单位为角度
        },
        "inside_points": [ // 矩形框内的点
          {
            "x": 370,
            "y": 462
          }
        ]
      }
    """
    direction = {
        "forward": 0,
        "left": 0,
        "right": 0,
        "uturn": 0,
        "merge_left": 0,
        "merge_right": 0
    }

    # 设置方向
    for prop in type_dict[sub_type]:
        if prop in direction.keys():
            direction[prop] = 1

    # 计算矩形四个点
    x = label_data["annotation"]["data"]["x"]
    y = label_data["annotation"]["data"]["y"]
    width = label_data["annotation"]["data"]["width"]
    height = label_data["annotation"]["data"]["height"]
    rotate = float(label_data["annotation"]["data"]["rotate"])
    bbox = get_rectangle_coordinates(x, y, width, height, rotate)

    # 获取方向边的两点
    inside_points = label_data["annotation"]["inside_points"][0]
    min_val = 1000
    idx = 0

    for index, _ in enumerate(bbox):

        point = [inside_points["x"], inside_points["y"]]
        point_a = [bbox[index]["x"], bbox[index]["y"]]
        point_b = [bbox[(index + 1) % 4]["x"], bbox[(index + 1) % 4]["y"]]
        dist = point_to_edge_distance(point, point_a, point_b)

        if min_val > dist:
            min_val = dist
            idx = index

    direction_points = [bbox[idx], bbox[(idx + 1) % 4]]
    print(bbox)
    print(f"--min: {round(min_val,1)},in: {inside_points}, direction:{direction_points},rotation:{rotate}")
    return {"direction": direction, "bbox": bbox, "direction_points": direction_points}


def rotate_point(x, y, angle):
    """将点(x,y)绕原点旋转角度angle（弧度制）"""
    sin_a = math.sin(angle)
    cos_a = math.cos(angle)
    x_new = x * cos_a - y * sin_a
    y_new = x * sin_a + y * cos_a
    return x_new, y_new


def get_rectangle_coordinates(x, y, width, height, rotate):
    """计算矩形的四个顶点坐标"""
    # 左上角坐标
    x1, y1 = x, y

    # 计算其他三个顶点坐标
    x2, y2 = x + width, y
    x3, y3 = x + width, y + height
    x4, y4 = x, y + height

    # 将四个顶点绕中心旋转
    center_x = x + width / 2
    center_y = y + height / 2
    x1, y1 = rotate_point(x1 - center_x, y1 - center_y, rotate)  # + (center_x, center_y)
    x1, y1 = x1 + center_x, y1 + center_y

    x2, y2 = rotate_point(x2 - center_x, y2 - center_y, rotate)  # + (center_x, center_y)
    x2, y2 = x2 + center_x, y2 + center_y

    x3, y3 = rotate_point(x3 - center_x, y3 - center_y, rotate)  # + (center_x, center_y)
    x3, y3 = x3 + center_x, y3 + center_y

    x4, y4 = rotate_point(x4 - center_x, y4 - center_y, rotate)  # + (center_x, center_y)
    x4, y4 = x4 + center_x, y4 + center_y

    return [{"x": x1, "y": y1}, {"x": x2, "y": y2}, {"x": x3, "y": y3}, {"x": x4, "y": y4}, ]


def point_to_edge_distance(point, point_a, point_b):
    # 计算点到线段的垂足C的坐标
    ac_vector = (point[0] - point_a[0], point[1] - point_a[1])
    ab_vector = (point_b[0] - point_a[0], point_b[1] - point_a[1])
    ab_length_square = ab_vector[0] ** 2 + ab_vector[1] ** 2
    ac_dot_ab = ac_vector[0] * ab_vector[0] + ac_vector[1] * ab_vector[1]
    t = ac_dot_ab / ab_length_square
    vertical_point_c = (point_a[0] + t * ab_vector[0], point_a[1] + t * ab_vector[1])

    # 计算点C到点(x,y)的距离
    dist = ((vertical_point_c[0] - point[0]) ** 2 + (vertical_point_c[1] - point[1]) ** 2) ** 0.5

    return dist


# def point_on_line_segment(p1, p2, p):
#     """
#     可以通过计算向量的叉积来判断一个点是否在某条线段上。如果点C在线段AB上，且向量AB和AC的叉积的大小为0，则点C在线段AB上。计算向量的叉积可以使用numpy库中的`cross`函数。
#
#     具体步骤如下：
#
#     1. 计算线段向量AB和AC的向量；
#     2. 使用numpy库中的`cross`函数计算向量AB和AC的叉积，得到一个标量结果；
#     3. 如果叉积的大小为0，则点C在线段AB上。
#
#     判断点p是否在线段p1p2上
#     参数:
#         p1: tuple, 第一个点坐标 (x1, y1)
#         p2: tuple, 第二个点坐标 (x2, y2)
#         p: tuple, 待判断点坐标 (x, y)
#     返回值:
#         True，如果点p在线段p1p2上，否则返回False
#     """
#     x1, y1 = p1
#     x2, y2 = p2
#     x, y = p
#
#     # 计算向量AB和AC的叉积
#     AB = np.array([x2 - x1, y2 - y1])
#     AC = np.array([x - x1, y - y1])
#     cross_product = np.cross(AB, AC)
#
#     # 判断点p是否在线段p1p2上
#     if abs(cross_product) < 1e-6:
#         # 如果叉积的大小为0，则点C在线段AB上
#         min_x, min_y = min(x1, x2), min(y1, y2)
#         max_x, max_y = max(x1, x2), max(y1, y2)
#         return min_x <= x <= max_x and min_y <= y <= max_y
#     else:
#         return False


def get_object_data(label_data, main_type, sub_type):
    object_data = {}

    if main_type == "lane" or main_type == "speed_bump":

        object_data = {
            "type": sub_type,
            "geometry": "polyline",
            "sensor": "fisheye_front_left_rear_right_bev",
            "attributes": {
                "points": label_data["annotation"]["data"]
            }}
    else:
        object_data = {
            "type": "arrow",
            "geometry": "bbox",
            "sensor": "fisheye_front_left_rear_right_bev",
            "attributes": get_arrow_data(label_data, sub_type)}
    return object_data


def get_main_type(sub_type):
    main_type = type_dict[sub_type]
    if isinstance(main_type, list):
        main_type = "arrow"
    return main_type


def get_group_data(label_data):
    group_data = {}

    for label in label_data:
        sub_type = label["class"]
        main_type = get_main_type(sub_type)

        obj_data = get_object_data(label, main_type, sub_type)

        if main_type in group_data.keys():
            group_data[main_type]["objects"].append(obj_data)
        else:
            group_data[main_type] = {"type": main_type,
                                     "attributes": {}, "objects": [obj_data]}

    return group_data


def run(test_in_input_dir, hm_input_dir, output_dir):
    global current_file

    file_count = 0

    tools.check_dir(output_dir)
    test_in_input_dir = tools.unzip(test_in_input_dir)

    test_in_hm_dict = {}

    # 云测 json和原始hm json的对应关系
    for f in tools.get_json_files(hm_input_dir):
        if f.name.endswith('.json') and not f.name.startswith("."):
            with open(f.path) as json_file:
                data = json.load(json_file)
                for item in data["camera_synthetic_bev"]:
                    test_in_name = item["http_path"].split("/")[-1].split("?")[0].replace(".jpg", ".json")
                    test_in_hm_dict[test_in_name] = f.path

    for f in tools.get_json_files(test_in_input_dir):
        if f.name.endswith('.json') and not f.name.startswith("."):
            with open(f.path) as json_file:
                test_in_data = json.load(json_file)

            with open(test_in_hm_dict[f.name]) as json_file:
                hm_data = json.load(json_file)

            out_file = test_in_hm_dict[f.name].split("/")[-1]
            convert(test_in_data, hm_data, output_dir + os.sep + out_file)

            file_count += 1

    err_title = ""
    err_type_list = []
    return tools.check_error(output_dir, err_files, err_title, file_count, 0, err_type_list, is_zip=False)


"""
    项目： 毫末
    对接平台：云测新平台 
    功能： 结果转换
    输入： 云测json和原始json
    输出： 毫末格式json    
"""
if __name__ == "__main__":
    args = tools.get_args()

    args.input = "../../../input/test/zdbc"
    args.pcd = "../../../input/test/hm"

    run(args.input, args.pcd, args.out)
