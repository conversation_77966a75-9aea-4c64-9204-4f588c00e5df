// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anno/v1/widget.proto

package anno

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WidgetName_Enum int32

const (
	WidgetName_unspecified WidgetName_Enum = 0
	// 2D矩形框
	WidgetName_box2d WidgetName_Enum = 1
	// 平面伪3D矩形框
	WidgetName_pscuboid WidgetName_Enum = 2
	// 3D矩形框
	WidgetName_cuboid WidgetName_Enum = 3
	// 2D多边形框
	WidgetName_poly2d WidgetName_Enum = 4
	// 3D多边形框
	WidgetName_poly3d WidgetName_Enum = 5
	// 2D 折线（含直线）
	WidgetName_line2d WidgetName_Enum = 6
	// 3D 折线（含直线）
	WidgetName_line3d WidgetName_Enum = 7
	// 2D 点
	WidgetName_point2d WidgetName_Enum = 8
	// 3D 点
	WidgetName_point3d WidgetName_Enum = 9
	// 位图
	WidgetName_bitmap WidgetName_Enum = 10
)

// Enum value maps for WidgetName_Enum.
var (
	WidgetName_Enum_name = map[int32]string{
		0:  "unspecified",
		1:  "box2d",
		2:  "pscuboid",
		3:  "cuboid",
		4:  "poly2d",
		5:  "poly3d",
		6:  "line2d",
		7:  "line3d",
		8:  "point2d",
		9:  "point3d",
		10: "bitmap",
	}
	WidgetName_Enum_value = map[string]int32{
		"unspecified": 0,
		"box2d":       1,
		"pscuboid":    2,
		"cuboid":      3,
		"poly2d":      4,
		"poly3d":      5,
		"line2d":      6,
		"line3d":      7,
		"point2d":     8,
		"point3d":     9,
		"bitmap":      10,
	}
)

func (x WidgetName_Enum) Enum() *WidgetName_Enum {
	p := new(WidgetName_Enum)
	*p = x
	return p
}

func (x WidgetName_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WidgetName_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_widget_proto_enumTypes[0].Descriptor()
}

func (WidgetName_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_widget_proto_enumTypes[0]
}

func (x WidgetName_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WidgetName_Enum.Descriptor instead.
func (WidgetName_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_widget_proto_rawDescGZIP(), []int{0, 0}
}

type WidgetLineType_Enum int32

const (
	WidgetLineType_line WidgetLineType_Enum = 0
	// Catmull–Rom spline
	WidgetLineType_crspline WidgetLineType_Enum = 1
	// center line
	WidgetLineType_centerline WidgetLineType_Enum = 2
)

// Enum value maps for WidgetLineType_Enum.
var (
	WidgetLineType_Enum_name = map[int32]string{
		0: "line",
		1: "crspline",
		2: "centerline",
	}
	WidgetLineType_Enum_value = map[string]int32{
		"line":       0,
		"crspline":   1,
		"centerline": 2,
	}
)

func (x WidgetLineType_Enum) Enum() *WidgetLineType_Enum {
	p := new(WidgetLineType_Enum)
	*p = x
	return p
}

func (x WidgetLineType_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WidgetLineType_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_widget_proto_enumTypes[1].Descriptor()
}

func (WidgetLineType_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_widget_proto_enumTypes[1]
}

func (x WidgetLineType_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WidgetLineType_Enum.Descriptor instead.
func (WidgetLineType_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_widget_proto_rawDescGZIP(), []int{1, 0}
}

type WidgetName struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WidgetName) Reset() {
	*x = WidgetName{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_widget_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WidgetName) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WidgetName) ProtoMessage() {}

func (x *WidgetName) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_widget_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WidgetName.ProtoReflect.Descriptor instead.
func (*WidgetName) Descriptor() ([]byte, []int) {
	return file_anno_v1_widget_proto_rawDescGZIP(), []int{0}
}

type WidgetLineType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WidgetLineType) Reset() {
	*x = WidgetLineType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_widget_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WidgetLineType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WidgetLineType) ProtoMessage() {}

func (x *WidgetLineType) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_widget_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WidgetLineType.ProtoReflect.Descriptor instead.
func (*WidgetLineType) Descriptor() ([]byte, []int) {
	return file_anno_v1_widget_proto_rawDescGZIP(), []int{1}
}

var File_anno_v1_widget_proto protoreflect.FileDescriptor

var file_anno_v1_widget_proto_rawDesc = []byte{
	0x0a, 0x14, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x22,
	0xa1, 0x01, 0x0a, 0x0a, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x92,
	0x01, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x62, 0x6f, 0x78, 0x32,
	0x64, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x70, 0x73, 0x63, 0x75, 0x62, 0x6f, 0x69, 0x64, 0x10,
	0x02, 0x12, 0x0a, 0x0a, 0x06, 0x63, 0x75, 0x62, 0x6f, 0x69, 0x64, 0x10, 0x03, 0x12, 0x0a, 0x0a,
	0x06, 0x70, 0x6f, 0x6c, 0x79, 0x32, 0x64, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x70, 0x6f, 0x6c,
	0x79, 0x33, 0x64, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x64, 0x10,
	0x06, 0x12, 0x0a, 0x0a, 0x06, 0x6c, 0x69, 0x6e, 0x65, 0x33, 0x64, 0x10, 0x07, 0x12, 0x0b, 0x0a,
	0x07, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x32, 0x64, 0x10, 0x08, 0x12, 0x0b, 0x0a, 0x07, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x33, 0x64, 0x10, 0x09, 0x12, 0x0a, 0x0a, 0x06, 0x62, 0x69, 0x74, 0x6d, 0x61,
	0x70, 0x10, 0x0a, 0x22, 0x40, 0x0a, 0x0e, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x4c, 0x69, 0x6e,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x2e, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x08, 0x0a,
	0x04, 0x6c, 0x69, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x63, 0x72, 0x73, 0x70, 0x6c,
	0x69, 0x6e, 0x65, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x6c,
	0x69, 0x6e, 0x65, 0x10, 0x02, 0x42, 0x3e, 0x0a, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x31, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31,
	0x3b, 0x61, 0x6e, 0x6e, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_anno_v1_widget_proto_rawDescOnce sync.Once
	file_anno_v1_widget_proto_rawDescData = file_anno_v1_widget_proto_rawDesc
)

func file_anno_v1_widget_proto_rawDescGZIP() []byte {
	file_anno_v1_widget_proto_rawDescOnce.Do(func() {
		file_anno_v1_widget_proto_rawDescData = protoimpl.X.CompressGZIP(file_anno_v1_widget_proto_rawDescData)
	})
	return file_anno_v1_widget_proto_rawDescData
}

var file_anno_v1_widget_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_anno_v1_widget_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_anno_v1_widget_proto_goTypes = []interface{}{
	(WidgetName_Enum)(0),     // 0: anno.v1.WidgetName.Enum
	(WidgetLineType_Enum)(0), // 1: anno.v1.WidgetLineType.Enum
	(*WidgetName)(nil),       // 2: anno.v1.WidgetName
	(*WidgetLineType)(nil),   // 3: anno.v1.WidgetLineType
}
var file_anno_v1_widget_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_anno_v1_widget_proto_init() }
func file_anno_v1_widget_proto_init() {
	if File_anno_v1_widget_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_anno_v1_widget_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WidgetName); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_widget_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WidgetLineType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anno_v1_widget_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_anno_v1_widget_proto_goTypes,
		DependencyIndexes: file_anno_v1_widget_proto_depIdxs,
		EnumInfos:         file_anno_v1_widget_proto_enumTypes,
		MessageInfos:      file_anno_v1_widget_proto_msgTypes,
	}.Build()
	File_anno_v1_widget_proto = out.File
	file_anno_v1_widget_proto_rawDesc = nil
	file_anno_v1_widget_proto_goTypes = nil
	file_anno_v1_widget_proto_depIdxs = nil
}
