BEGIN;

-- grant organization biz permissions to a kam
CREATE TABLE bizgrants
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    grantor_uid VARCHAR(32)  NOT NULL,
    grantee_uid VARCHAR(32)  NOT NULL,
    org_uid     VARCHAR(32)  NOT NULL,
    -- biz         VARCHAR(32)  NOT NULL, -- business type, e.g. anno
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_bizgrants_grantee_uid_org_uid ON bizgrants (grantee_uid, org_uid);
CREATE INDEX idx_bizgrants_org_uid ON bizgrants (org_uid) INCLUDE (grantee_uid);
CREATE INDEX idx_bizgrants_grantor_uid ON bizgrants (grantor_uid);

-- grant a specific resource access to a pm
CREATE TABLE specgrants
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    grantor_uid VARCHAR(32)  NOT NULL,
    grantee_uid VARCHAR(32)  NOT NULL,
    item_id     BIGINT       NOT NULL,
    item_type   VARCHAR(32)  NOT NULL, -- item type, e.g. AnnoLot/...
    org_uid     VARCHAR(32)  NOT NULL, -- item's owner organization
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_specgrants_item_type_item_id_grantee_uid ON specgrants (item_type, item_id, grantee_uid) INCLUDE (org_uid);
CREATE INDEX idx_specgrants_grantee_uid_item_type_org_uid ON specgrants (grantee_uid, item_type, org_uid) INCLUDE (item_id);
CREATE INDEX idx_specgrants_org_uid_item_type ON specgrants (org_uid, item_type) INCLUDE (grantee_uid, item_id);
CREATE INDEX idx_specgrants_grantor_uid ON specgrants (grantor_uid);

COMMIT;
