// Code generated by Thrift Compiler (0.14.1). DO NOT EDIT.

package zipkincore

import (
	"bytes"
	"context"
	"database/sql/driver"
	"errors"
	"fmt"
	"time"

	"go.opentelemetry.io/otel/exporters/jaeger/internal/third_party/thrift/lib/go/thrift"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

type AnnotationType int64

const (
	AnnotationType_BOOL   AnnotationType = 0
	AnnotationType_BYTES  AnnotationType = 1
	AnnotationType_I16    AnnotationType = 2
	AnnotationType_I32    AnnotationType = 3
	AnnotationType_I64    AnnotationType = 4
	AnnotationType_DOUBLE AnnotationType = 5
	AnnotationType_STRING AnnotationType = 6
)

func (p AnnotationType) String() string {
	switch p {
	case AnnotationType_BOOL:
		return "BOOL"
	case AnnotationType_BYTES:
		return "BYTES"
	case AnnotationType_I16:
		return "I16"
	case AnnotationType_I32:
		return "I32"
	case AnnotationType_I64:
		return "I64"
	case AnnotationType_DOUBLE:
		return "DOUBLE"
	case AnnotationType_STRING:
		return "STRING"
	}
	return "<UNSET>"
}

func AnnotationTypeFromString(s string) (AnnotationType, error) {
	switch s {
	case "BOOL":
		return AnnotationType_BOOL, nil
	case "BYTES":
		return AnnotationType_BYTES, nil
	case "I16":
		return AnnotationType_I16, nil
	case "I32":
		return AnnotationType_I32, nil
	case "I64":
		return AnnotationType_I64, nil
	case "DOUBLE":
		return AnnotationType_DOUBLE, nil
	case "STRING":
		return AnnotationType_STRING, nil
	}
	return AnnotationType(0), fmt.Errorf("not a valid AnnotationType string")
}

func AnnotationTypePtr(v AnnotationType) *AnnotationType { return &v }

func (p AnnotationType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *AnnotationType) UnmarshalText(text []byte) error {
	q, err := AnnotationTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

func (p *AnnotationType) Scan(value interface{}) error {
	v, ok := value.(int64)
	if !ok {
		return errors.New("Scan value is not int64")
	}
	*p = AnnotationType(v)
	return nil
}

func (p *AnnotationType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// Indicates the network context of a service recording an annotation with two
// exceptions.
//
// When a BinaryAnnotation, and key is CLIENT_ADDR or SERVER_ADDR,
// the endpoint indicates the source or destination of an RPC. This exception
// allows zipkin to display network context of uninstrumented services, or
// clients such as web browsers.
//
// Attributes:
//  - Ipv4: IPv4 host address packed into 4 bytes.
//
// Ex for the ip *******, it would be (1 << 24) | (2 << 16) | (3 << 8) | 4
//  - Port: IPv4 port
//
// Note: this is to be treated as an unsigned integer, so watch for negatives.
//
// Conventionally, when the port isn't known, port = 0.
//  - ServiceName: Service name in lowercase, such as "memcache" or "zipkin-web"
//
// Conventionally, when the service name isn't known, service_name = "unknown".
//  - Ipv6: IPv6 host address packed into 16 bytes. Ex Inet6Address.getBytes()
type Endpoint struct {
	Ipv4        int32  `thrift:"ipv4,1" db:"ipv4" json:"ipv4"`
	Port        int16  `thrift:"port,2" db:"port" json:"port"`
	ServiceName string `thrift:"service_name,3" db:"service_name" json:"service_name"`
	Ipv6        []byte `thrift:"ipv6,4" db:"ipv6" json:"ipv6,omitempty"`
}

func NewEndpoint() *Endpoint {
	return &Endpoint{}
}

func (p *Endpoint) GetIpv4() int32 {
	return p.Ipv4
}

func (p *Endpoint) GetPort() int16 {
	return p.Port
}

func (p *Endpoint) GetServiceName() string {
	return p.ServiceName
}

var Endpoint_Ipv6_DEFAULT []byte

func (p *Endpoint) GetIpv6() []byte {
	return p.Ipv6
}
func (p *Endpoint) IsSetIpv6() bool {
	return p.Ipv6 != nil
}

func (p *Endpoint) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I16 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *Endpoint) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Ipv4 = v
	}
	return nil
}

func (p *Endpoint) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Port = v
	}
	return nil
}

func (p *Endpoint) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.ServiceName = v
	}
	return nil
}

func (p *Endpoint) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.Ipv6 = v
	}
	return nil
}

func (p *Endpoint) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "Endpoint"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *Endpoint) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "ipv4", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ipv4: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Ipv4)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.ipv4 (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ipv4: ", p), err)
	}
	return err
}

func (p *Endpoint) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "port", thrift.I16, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:port: ", p), err)
	}
	if err := oprot.WriteI16(ctx, int16(p.Port)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.port (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:port: ", p), err)
	}
	return err
}

func (p *Endpoint) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "service_name", thrift.STRING, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:service_name: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.ServiceName)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.service_name (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:service_name: ", p), err)
	}
	return err
}

func (p *Endpoint) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetIpv6() {
		if err := oprot.WriteFieldBegin(ctx, "ipv6", thrift.STRING, 4); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:ipv6: ", p), err)
		}
		if err := oprot.WriteBinary(ctx, p.Ipv6); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.ipv6 (4) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 4:ipv6: ", p), err)
		}
	}
	return err
}

func (p *Endpoint) Equals(other *Endpoint) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Ipv4 != other.Ipv4 {
		return false
	}
	if p.Port != other.Port {
		return false
	}
	if p.ServiceName != other.ServiceName {
		return false
	}
	if bytes.Compare(p.Ipv6, other.Ipv6) != 0 {
		return false
	}
	return true
}

func (p *Endpoint) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Endpoint(%+v)", *p)
}

// An annotation is similar to a log statement. It includes a host field which
// allows these events to be attributed properly, and also aggregatable.
//
// Attributes:
//  - Timestamp: Microseconds from epoch.
//
// This value should use the most precise value possible. For example,
// gettimeofday or syncing nanoTime against a tick of currentTimeMillis.
//  - Value
//  - Host: Always the host that recorded the event. By specifying the host you allow
// rollup of all events (such as client requests to a service) by IP address.
type Annotation struct {
	Timestamp int64     `thrift:"timestamp,1" db:"timestamp" json:"timestamp"`
	Value     string    `thrift:"value,2" db:"value" json:"value"`
	Host      *Endpoint `thrift:"host,3" db:"host" json:"host,omitempty"`
}

func NewAnnotation() *Annotation {
	return &Annotation{}
}

func (p *Annotation) GetTimestamp() int64 {
	return p.Timestamp
}

func (p *Annotation) GetValue() string {
	return p.Value
}

var Annotation_Host_DEFAULT *Endpoint

func (p *Annotation) GetHost() *Endpoint {
	if !p.IsSetHost() {
		return Annotation_Host_DEFAULT
	}
	return p.Host
}
func (p *Annotation) IsSetHost() bool {
	return p.Host != nil
}

func (p *Annotation) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *Annotation) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Timestamp = v
	}
	return nil
}

func (p *Annotation) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Value = v
	}
	return nil
}

func (p *Annotation) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	p.Host = &Endpoint{}
	if err := p.Host.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Host), err)
	}
	return nil
}

func (p *Annotation) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "Annotation"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *Annotation) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "timestamp", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:timestamp: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.Timestamp)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.timestamp (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:timestamp: ", p), err)
	}
	return err
}

func (p *Annotation) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "value", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:value: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Value)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.value (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:value: ", p), err)
	}
	return err
}

func (p *Annotation) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetHost() {
		if err := oprot.WriteFieldBegin(ctx, "host", thrift.STRUCT, 3); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:host: ", p), err)
		}
		if err := p.Host.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Host), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 3:host: ", p), err)
		}
	}
	return err
}

func (p *Annotation) Equals(other *Annotation) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Timestamp != other.Timestamp {
		return false
	}
	if p.Value != other.Value {
		return false
	}
	if !p.Host.Equals(other.Host) {
		return false
	}
	return true
}

func (p *Annotation) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Annotation(%+v)", *p)
}

// Binary annotations are tags applied to a Span to give it context. For
// example, a binary annotation of "http.uri" could the path to a resource in a
// RPC call.
//
// Binary annotations of type STRING are always queryable, though more a
// historical implementation detail than a structural concern.
//
// Binary annotations can repeat, and vary on the host. Similar to Annotation,
// the host indicates who logged the event. This allows you to tell the
// difference between the client and server side of the same key. For example,
// the key "http.uri" might be different on the client and server side due to
// rewriting, like "/api/v1/myresource" vs "/myresource. Via the host field,
// you can see the different points of view, which often help in debugging.
//
// Attributes:
//  - Key
//  - Value
//  - AnnotationType
//  - Host: The host that recorded tag, which allows you to differentiate between
// multiple tags with the same key. There are two exceptions to this.
//
// When the key is CLIENT_ADDR or SERVER_ADDR, host indicates the source or
// destination of an RPC. This exception allows zipkin to display network
// context of uninstrumented services, or clients such as web browsers.
type BinaryAnnotation struct {
	Key            string         `thrift:"key,1" db:"key" json:"key"`
	Value          []byte         `thrift:"value,2" db:"value" json:"value"`
	AnnotationType AnnotationType `thrift:"annotation_type,3" db:"annotation_type" json:"annotation_type"`
	Host           *Endpoint      `thrift:"host,4" db:"host" json:"host,omitempty"`
}

func NewBinaryAnnotation() *BinaryAnnotation {
	return &BinaryAnnotation{}
}

func (p *BinaryAnnotation) GetKey() string {
	return p.Key
}

func (p *BinaryAnnotation) GetValue() []byte {
	return p.Value
}

func (p *BinaryAnnotation) GetAnnotationType() AnnotationType {
	return p.AnnotationType
}

var BinaryAnnotation_Host_DEFAULT *Endpoint

func (p *BinaryAnnotation) GetHost() *Endpoint {
	if !p.IsSetHost() {
		return BinaryAnnotation_Host_DEFAULT
	}
	return p.Host
}
func (p *BinaryAnnotation) IsSetHost() bool {
	return p.Host != nil
}

func (p *BinaryAnnotation) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *BinaryAnnotation) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Key = v
	}
	return nil
}

func (p *BinaryAnnotation) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Value = v
	}
	return nil
}

func (p *BinaryAnnotation) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		temp := AnnotationType(v)
		p.AnnotationType = temp
	}
	return nil
}

func (p *BinaryAnnotation) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	p.Host = &Endpoint{}
	if err := p.Host.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Host), err)
	}
	return nil
}

func (p *BinaryAnnotation) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "BinaryAnnotation"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *BinaryAnnotation) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "key", thrift.STRING, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:key: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Key)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.key (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:key: ", p), err)
	}
	return err
}

func (p *BinaryAnnotation) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "value", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:value: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Value); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.value (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:value: ", p), err)
	}
	return err
}

func (p *BinaryAnnotation) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "annotation_type", thrift.I32, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:annotation_type: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.AnnotationType)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.annotation_type (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:annotation_type: ", p), err)
	}
	return err
}

func (p *BinaryAnnotation) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetHost() {
		if err := oprot.WriteFieldBegin(ctx, "host", thrift.STRUCT, 4); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:host: ", p), err)
		}
		if err := p.Host.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Host), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 4:host: ", p), err)
		}
	}
	return err
}

func (p *BinaryAnnotation) Equals(other *BinaryAnnotation) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Key != other.Key {
		return false
	}
	if bytes.Compare(p.Value, other.Value) != 0 {
		return false
	}
	if p.AnnotationType != other.AnnotationType {
		return false
	}
	if !p.Host.Equals(other.Host) {
		return false
	}
	return true
}

func (p *BinaryAnnotation) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BinaryAnnotation(%+v)", *p)
}

// A trace is a series of spans (often RPC calls) which form a latency tree.
//
// The root span is where trace_id = id and parent_id = Nil. The root span is
// usually the longest interval in the trace, starting with a SERVER_RECV
// annotation and ending with a SERVER_SEND.
//
// Attributes:
//  - TraceID
//  - Name: Span name in lowercase, rpc method for example
//
// Conventionally, when the span name isn't known, name = "unknown".
//  - ID
//  - ParentID
//  - Annotations
//  - BinaryAnnotations
//  - Debug
//  - Timestamp: Microseconds from epoch of the creation of this span.
//
// This value should be set directly by instrumentation, using the most
// precise value possible. For example, gettimeofday or syncing nanoTime
// against a tick of currentTimeMillis.
//
// For compatibility with instrumentation that precede this field, collectors
// or span stores can derive this via Annotation.timestamp.
// For example, SERVER_RECV.timestamp or CLIENT_SEND.timestamp.
//
// This field is optional for compatibility with old data: first-party span
// stores are expected to support this at time of introduction.
//  - Duration: Measurement of duration in microseconds, used to support queries.
//
// This value should be set directly, where possible. Doing so encourages
// precise measurement decoupled from problems of clocks, such as skew or NTP
// updates causing time to move backwards.
//
// For compatibility with instrumentation that precede this field, collectors
// or span stores can derive this by subtracting Annotation.timestamp.
// For example, SERVER_SEND.timestamp - SERVER_RECV.timestamp.
//
// If this field is persisted as unset, zipkin will continue to work, except
// duration query support will be implementation-specific. Similarly, setting
// this field non-atomically is implementation-specific.
//
// This field is i64 vs i32 to support spans longer than 35 minutes.
//  - TraceIDHigh: Optional unique 8-byte additional identifier for a trace. If non zero, this
// means the trace uses 128 bit traceIds instead of 64 bit.
type Span struct {
	TraceID int64 `thrift:"trace_id,1" db:"trace_id" json:"trace_id"`
	// unused field # 2
	Name        string        `thrift:"name,3" db:"name" json:"name"`
	ID          int64         `thrift:"id,4" db:"id" json:"id"`
	ParentID    *int64        `thrift:"parent_id,5" db:"parent_id" json:"parent_id,omitempty"`
	Annotations []*Annotation `thrift:"annotations,6" db:"annotations" json:"annotations"`
	// unused field # 7
	BinaryAnnotations []*BinaryAnnotation `thrift:"binary_annotations,8" db:"binary_annotations" json:"binary_annotations"`
	Debug             bool                `thrift:"debug,9" db:"debug" json:"debug"`
	Timestamp         *int64              `thrift:"timestamp,10" db:"timestamp" json:"timestamp,omitempty"`
	Duration          *int64              `thrift:"duration,11" db:"duration" json:"duration,omitempty"`
	TraceIDHigh       *int64              `thrift:"trace_id_high,12" db:"trace_id_high" json:"trace_id_high,omitempty"`
}

func NewSpan() *Span {
	return &Span{}
}

func (p *Span) GetTraceID() int64 {
	return p.TraceID
}

func (p *Span) GetName() string {
	return p.Name
}

func (p *Span) GetID() int64 {
	return p.ID
}

var Span_ParentID_DEFAULT int64

func (p *Span) GetParentID() int64 {
	if !p.IsSetParentID() {
		return Span_ParentID_DEFAULT
	}
	return *p.ParentID
}

func (p *Span) GetAnnotations() []*Annotation {
	return p.Annotations
}

func (p *Span) GetBinaryAnnotations() []*BinaryAnnotation {
	return p.BinaryAnnotations
}

var Span_Debug_DEFAULT bool = false

func (p *Span) GetDebug() bool {
	return p.Debug
}

var Span_Timestamp_DEFAULT int64

func (p *Span) GetTimestamp() int64 {
	if !p.IsSetTimestamp() {
		return Span_Timestamp_DEFAULT
	}
	return *p.Timestamp
}

var Span_Duration_DEFAULT int64

func (p *Span) GetDuration() int64 {
	if !p.IsSetDuration() {
		return Span_Duration_DEFAULT
	}
	return *p.Duration
}

var Span_TraceIDHigh_DEFAULT int64

func (p *Span) GetTraceIDHigh() int64 {
	if !p.IsSetTraceIDHigh() {
		return Span_TraceIDHigh_DEFAULT
	}
	return *p.TraceIDHigh
}
func (p *Span) IsSetParentID() bool {
	return p.ParentID != nil
}

func (p *Span) IsSetDebug() bool {
	return p.Debug != Span_Debug_DEFAULT
}

func (p *Span) IsSetTimestamp() bool {
	return p.Timestamp != nil
}

func (p *Span) IsSetDuration() bool {
	return p.Duration != nil
}

func (p *Span) IsSetTraceIDHigh() bool {
	return p.TraceIDHigh != nil
}

func (p *Span) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField6(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField8(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.BOOL {
				if err := p.ReadField9(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField10(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField11(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField12(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *Span) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.TraceID = v
	}
	return nil
}

func (p *Span) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Span) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.ID = v
	}
	return nil
}

func (p *Span) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 5: ", err)
	} else {
		p.ParentID = &v
	}
	return nil
}

func (p *Span) ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]*Annotation, 0, size)
	p.Annotations = tSlice
	for i := 0; i < size; i++ {
		_elem0 := &Annotation{}
		if err := _elem0.Read(ctx, iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem0), err)
		}
		p.Annotations = append(p.Annotations, _elem0)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *Span) ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]*BinaryAnnotation, 0, size)
	p.BinaryAnnotations = tSlice
	for i := 0; i < size; i++ {
		_elem1 := &BinaryAnnotation{}
		if err := _elem1.Read(ctx, iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem1), err)
		}
		p.BinaryAnnotations = append(p.BinaryAnnotations, _elem1)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *Span) ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(ctx); err != nil {
		return thrift.PrependError("error reading field 9: ", err)
	} else {
		p.Debug = v
	}
	return nil
}

func (p *Span) ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 10: ", err)
	} else {
		p.Timestamp = &v
	}
	return nil
}

func (p *Span) ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 11: ", err)
	} else {
		p.Duration = &v
	}
	return nil
}

func (p *Span) ReadField12(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 12: ", err)
	} else {
		p.TraceIDHigh = &v
	}
	return nil
}

func (p *Span) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "Span"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField6(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField8(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField9(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField10(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField11(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField12(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *Span) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "trace_id", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:trace_id: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.TraceID)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.trace_id (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:trace_id: ", p), err)
	}
	return err
}

func (p *Span) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "name", thrift.STRING, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:name: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Name)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.name (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:name: ", p), err)
	}
	return err
}

func (p *Span) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "id", thrift.I64, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:id: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.ID)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.id (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:id: ", p), err)
	}
	return err
}

func (p *Span) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetParentID() {
		if err := oprot.WriteFieldBegin(ctx, "parent_id", thrift.I64, 5); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:parent_id: ", p), err)
		}
		if err := oprot.WriteI64(ctx, int64(*p.ParentID)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.parent_id (5) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 5:parent_id: ", p), err)
		}
	}
	return err
}

func (p *Span) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "annotations", thrift.LIST, 6); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:annotations: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Annotations)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.Annotations {
		if err := v.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 6:annotations: ", p), err)
	}
	return err
}

func (p *Span) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "binary_annotations", thrift.LIST, 8); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:binary_annotations: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.BinaryAnnotations)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.BinaryAnnotations {
		if err := v.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 8:binary_annotations: ", p), err)
	}
	return err
}

func (p *Span) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetDebug() {
		if err := oprot.WriteFieldBegin(ctx, "debug", thrift.BOOL, 9); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:debug: ", p), err)
		}
		if err := oprot.WriteBool(ctx, bool(p.Debug)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.debug (9) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 9:debug: ", p), err)
		}
	}
	return err
}

func (p *Span) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetTimestamp() {
		if err := oprot.WriteFieldBegin(ctx, "timestamp", thrift.I64, 10); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:timestamp: ", p), err)
		}
		if err := oprot.WriteI64(ctx, int64(*p.Timestamp)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.timestamp (10) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 10:timestamp: ", p), err)
		}
	}
	return err
}

func (p *Span) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetDuration() {
		if err := oprot.WriteFieldBegin(ctx, "duration", thrift.I64, 11); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:duration: ", p), err)
		}
		if err := oprot.WriteI64(ctx, int64(*p.Duration)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.duration (11) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 11:duration: ", p), err)
		}
	}
	return err
}

func (p *Span) writeField12(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetTraceIDHigh() {
		if err := oprot.WriteFieldBegin(ctx, "trace_id_high", thrift.I64, 12); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 12:trace_id_high: ", p), err)
		}
		if err := oprot.WriteI64(ctx, int64(*p.TraceIDHigh)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.trace_id_high (12) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 12:trace_id_high: ", p), err)
		}
	}
	return err
}

func (p *Span) Equals(other *Span) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.TraceID != other.TraceID {
		return false
	}
	if p.Name != other.Name {
		return false
	}
	if p.ID != other.ID {
		return false
	}
	if p.ParentID != other.ParentID {
		if p.ParentID == nil || other.ParentID == nil {
			return false
		}
		if (*p.ParentID) != (*other.ParentID) {
			return false
		}
	}
	if len(p.Annotations) != len(other.Annotations) {
		return false
	}
	for i, _tgt := range p.Annotations {
		_src2 := other.Annotations[i]
		if !_tgt.Equals(_src2) {
			return false
		}
	}
	if len(p.BinaryAnnotations) != len(other.BinaryAnnotations) {
		return false
	}
	for i, _tgt := range p.BinaryAnnotations {
		_src3 := other.BinaryAnnotations[i]
		if !_tgt.Equals(_src3) {
			return false
		}
	}
	if p.Debug != other.Debug {
		return false
	}
	if p.Timestamp != other.Timestamp {
		if p.Timestamp == nil || other.Timestamp == nil {
			return false
		}
		if (*p.Timestamp) != (*other.Timestamp) {
			return false
		}
	}
	if p.Duration != other.Duration {
		if p.Duration == nil || other.Duration == nil {
			return false
		}
		if (*p.Duration) != (*other.Duration) {
			return false
		}
	}
	if p.TraceIDHigh != other.TraceIDHigh {
		if p.TraceIDHigh == nil || other.TraceIDHigh == nil {
			return false
		}
		if (*p.TraceIDHigh) != (*other.TraceIDHigh) {
			return false
		}
	}
	return true
}

func (p *Span) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Span(%+v)", *p)
}

// Attributes:
//  - Ok
type Response struct {
	Ok bool `thrift:"ok,1,required" db:"ok" json:"ok"`
}

func NewResponse() *Response {
	return &Response{}
}

func (p *Response) GetOk() bool {
	return p.Ok
}
func (p *Response) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetOk bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetOk = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetOk {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ok is not set"))
	}
	return nil
}

func (p *Response) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Ok = v
	}
	return nil
}

func (p *Response) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "Response"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *Response) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "ok", thrift.BOOL, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ok: ", p), err)
	}
	if err := oprot.WriteBool(ctx, bool(p.Ok)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.ok (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ok: ", p), err)
	}
	return err
}

func (p *Response) Equals(other *Response) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Ok != other.Ok {
		return false
	}
	return true
}

func (p *Response) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Response(%+v)", *p)
}

type ZipkinCollector interface {
	// Parameters:
	//  - Spans
	SubmitZipkinBatch(ctx context.Context, spans []*Span) (_r []*Response, _err error)
}

type ZipkinCollectorClient struct {
	c    thrift.TClient
	meta thrift.ResponseMeta
}

func NewZipkinCollectorClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *ZipkinCollectorClient {
	return &ZipkinCollectorClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewZipkinCollectorClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *ZipkinCollectorClient {
	return &ZipkinCollectorClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewZipkinCollectorClient(c thrift.TClient) *ZipkinCollectorClient {
	return &ZipkinCollectorClient{
		c: c,
	}
}

func (p *ZipkinCollectorClient) Client_() thrift.TClient {
	return p.c
}

func (p *ZipkinCollectorClient) LastResponseMeta_() thrift.ResponseMeta {
	return p.meta
}

func (p *ZipkinCollectorClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
	p.meta = meta
}

// Parameters:
//  - Spans
func (p *ZipkinCollectorClient) SubmitZipkinBatch(ctx context.Context, spans []*Span) (_r []*Response, _err error) {
	var _args4 ZipkinCollectorSubmitZipkinBatchArgs
	_args4.Spans = spans
	var _result6 ZipkinCollectorSubmitZipkinBatchResult
	var _meta5 thrift.ResponseMeta
	_meta5, _err = p.Client_().Call(ctx, "submitZipkinBatch", &_args4, &_result6)
	p.SetLastResponseMeta_(_meta5)
	if _err != nil {
		return
	}
	return _result6.GetSuccess(), nil
}

type ZipkinCollectorProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      ZipkinCollector
}

func (p *ZipkinCollectorProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *ZipkinCollectorProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *ZipkinCollectorProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewZipkinCollectorProcessor(handler ZipkinCollector) *ZipkinCollectorProcessor {

	self7 := &ZipkinCollectorProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self7.processorMap["submitZipkinBatch"] = &zipkinCollectorProcessorSubmitZipkinBatch{handler: handler}
	return self7
}

func (p *ZipkinCollectorProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
	if err2 != nil {
		return false, thrift.WrapTException(err2)
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(ctx, thrift.STRUCT)
	iprot.ReadMessageEnd(ctx)
	x8 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
	x8.Write(ctx, oprot)
	oprot.WriteMessageEnd(ctx)
	oprot.Flush(ctx)
	return false, x8

}

type zipkinCollectorProcessorSubmitZipkinBatch struct {
	handler ZipkinCollector
}

func (p *zipkinCollectorProcessorSubmitZipkinBatch) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := ZipkinCollectorSubmitZipkinBatchArgs{}
	var err2 error
	if err2 = args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "submitZipkinBatch", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := ZipkinCollectorSubmitZipkinBatchResult{}
	var retval []*Response
	if retval, err2 = p.handler.SubmitZipkinBatch(ctx, args.Spans); err2 != nil {
		tickerCancel()
		if err2 == thrift.ErrAbandonRequest {
			return false, thrift.WrapTException(err2)
		}
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing submitZipkinBatch: "+err2.Error())
		oprot.WriteMessageBegin(ctx, "submitZipkinBatch", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return true, thrift.WrapTException(err2)
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 = oprot.WriteMessageBegin(ctx, "submitZipkinBatch", thrift.REPLY, seqId); err2 != nil {
		err = thrift.WrapTException(err2)
	}
	if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
		err = thrift.WrapTException(err2)
	}
	if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
		err = thrift.WrapTException(err2)
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = thrift.WrapTException(err2)
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//  - Spans
type ZipkinCollectorSubmitZipkinBatchArgs struct {
	Spans []*Span `thrift:"spans,1" db:"spans" json:"spans"`
}

func NewZipkinCollectorSubmitZipkinBatchArgs() *ZipkinCollectorSubmitZipkinBatchArgs {
	return &ZipkinCollectorSubmitZipkinBatchArgs{}
}

func (p *ZipkinCollectorSubmitZipkinBatchArgs) GetSpans() []*Span {
	return p.Spans
}
func (p *ZipkinCollectorSubmitZipkinBatchArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *ZipkinCollectorSubmitZipkinBatchArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]*Span, 0, size)
	p.Spans = tSlice
	for i := 0; i < size; i++ {
		_elem9 := &Span{}
		if err := _elem9.Read(ctx, iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem9), err)
		}
		p.Spans = append(p.Spans, _elem9)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *ZipkinCollectorSubmitZipkinBatchArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "submitZipkinBatch_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *ZipkinCollectorSubmitZipkinBatchArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "spans", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:spans: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Spans)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.Spans {
		if err := v.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:spans: ", p), err)
	}
	return err
}

func (p *ZipkinCollectorSubmitZipkinBatchArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ZipkinCollectorSubmitZipkinBatchArgs(%+v)", *p)
}

// Attributes:
//  - Success
type ZipkinCollectorSubmitZipkinBatchResult struct {
	Success []*Response `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewZipkinCollectorSubmitZipkinBatchResult() *ZipkinCollectorSubmitZipkinBatchResult {
	return &ZipkinCollectorSubmitZipkinBatchResult{}
}

var ZipkinCollectorSubmitZipkinBatchResult_Success_DEFAULT []*Response

func (p *ZipkinCollectorSubmitZipkinBatchResult) GetSuccess() []*Response {
	return p.Success
}
func (p *ZipkinCollectorSubmitZipkinBatchResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *ZipkinCollectorSubmitZipkinBatchResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *ZipkinCollectorSubmitZipkinBatchResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]*Response, 0, size)
	p.Success = tSlice
	for i := 0; i < size; i++ {
		_elem10 := &Response{}
		if err := _elem10.Read(ctx, iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem10), err)
		}
		p.Success = append(p.Success, _elem10)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *ZipkinCollectorSubmitZipkinBatchResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "submitZipkinBatch_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *ZipkinCollectorSubmitZipkinBatchResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.LIST, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Success)); err != nil {
			return thrift.PrependError("error writing list begin: ", err)
		}
		for _, v := range p.Success {
			if err := v.Write(ctx, oprot); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
			}
		}
		if err := oprot.WriteListEnd(ctx); err != nil {
			return thrift.PrependError("error writing list end: ", err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *ZipkinCollectorSubmitZipkinBatchResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ZipkinCollectorSubmitZipkinBatchResult(%+v)", *p)
}
