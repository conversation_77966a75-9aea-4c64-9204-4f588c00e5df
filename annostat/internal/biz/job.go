// source: annostat/v1/job.proto
package biz

import (
	"time"

	"annostat/internal/data/serial"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/kid"
)

// InsCnt -> InsManual
// InsTotal -> Ins

type InsByClass = serial.Map[string, int]

type Job struct {
	ID         int64     `json:"id" gorm:"default:null"`
	LotID      int64     `json:"lot_id" gorm:"default:null"`
	IdxInLot   int32     `json:"idx_in_lot" gorm:"default:null"`
	Elems      int32     `json:"elems" gorm:"default:null"`
	Phase      int32     `json:"phase" gorm:"default:null"` // starts from 1
	Finished   bool      `json:"finished" gorm:"default:null"`
	Submits    int32     `json:"submits" gorm:"default:null"`
	Duration   int32     `json:"duration" gorm:"default:null"`
	FirstClaim time.Time `json:"first_claim" gorm:"default:null"`
	LastSubmit time.Time `json:"last_submit" gorm:"default:null"`
	CreatedAt  time.Time `json:"created_at" gorm:"default:null"`

	// InsCnt     int32      `json:"ins_cnt" gorm:"default:null"`
	// InsTotal   int32      `json:"ins_total" gorm:"default:null"` // include interpolated objects
	// Ins2d      int32      `json:"ins_2d" gorm:"default:null;column:ins_2d"`
	// Ins3d      int32      `json:"ins_3d" gorm:"default:null;column:ins_3d"`
	// InsByClass InsByClass `json:"ins_by_class" gorm:"default:null"`

	// Lot *Lot `json:"-" gorm:"-"`
}

func (o *Job) GetID() int64   { return o.ID }
func (o *Job) GetUid() string { return kid.StringID(o.ID) }
func (o *Job) UidFld() string { panic("code error") }

const JobTableName = "jobs"

var (
	job_ = field.RegObject(&Job{})
	// JobUpdatableFlds = field.NewModel(job_)

	// field name in snake case
	JobSfldLotID    = field.Sname(&job_.LotID)
	JobSfldIdxInLot = field.Sname(&job_.IdxInLot)
	JobSfldPhase    = field.Sname(&job_.Phase)
	JobSfldFinished = field.Sname(&job_.Finished)
	JobSfldElems    = field.Sname(&job_.Elems)
	// JobSfldInsCnt     = field.Sname(&job_.InsCnt)
	// JobSfldInsTotal   = field.Sname(&job_.InsTotal)
	JobSfldSubmits    = field.Sname(&job_.Submits)
	JobSfldDuration   = field.Sname(&job_.Duration)
	JobSfldFirstClaim = field.Sname(&job_.FirstClaim)
	JobSfldLastSubmit = field.Sname(&job_.LastSubmit)
)

type JobListFilter struct {
	IDs    []int64
	LotIDs []int64
	Phases []int32
}

func (o *JobListFilter) Apply(tx Tx) Tx {
	if o == nil {
		return tx
	}
	tbl := "jobs."
	tx = ApplyFieldFilter(tx, tbl+"id", o.IDs)
	tx = ApplyFieldFilter(tx, tbl+JobSfldLotID, o.LotIDs)
	tx = ApplyFieldFilter(tx, tbl+JobSfldPhase, o.Phases)
	return tx
}

type JobsRepo repo.GenericRepo[Job]

// type JobsBiz struct {
// 	repo   JobsRepo
// 	bgtask BackgroundTask
// 	log    *log.Helper
// }

// func NewJobsBiz(repo JobsRepo, bgtask BackgroundTask, logger log.Logger) *JobsBiz {
// 	return &JobsBiz{repo: repo, bgtask: bgtask, log: log.NewHelper(logger)}
// }
