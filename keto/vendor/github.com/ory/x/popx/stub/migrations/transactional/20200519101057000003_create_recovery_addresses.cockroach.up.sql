CREATE TABLE "selfservice_recovery_requests" (
"id" UUID NOT NULL,
<PERSON><PERSON>AR<PERSON> KEY("id"),
"request_url" VARCHAR (2048) NOT NULL,
"issued_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
"expires_at" timestamp NOT NULL,
"messages" json,
"active_method" VARCHAR (32),
"csrf_token" VARCHAR (255) NOT NULL,
"state" VARCHAR (32) NOT NULL,
"recovered_identity_id" UUID,
"created_at" timestamp NOT NULL,
"updated_at" timestamp NOT NULL,
CONSTRAINT "selfservice_recovery_requests_identities_id_fk" FOREIGN KEY ("recovered_identity_id") REFERENCES "identities" ("id") ON DELETE cascade
)