// source: anno/v1/job.proto
package service

import (
	"context"
	"encoding/base64"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"path"
	"strings"
	"time"

	"anno/api/client"
	"anno/internal/biz"
	"anno/internal/ostore"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var fromBizJobStatesMapping = map[biz.JobState]anno.Job_State_Enum{
	biz.JobStateUnstart:  anno.Job_State_unstart,
	biz.JobStateDoing:    anno.Job_State_doing,
	biz.JobStateFinished: anno.Job_State_finished,
}
var toBizJobStateMapping = lo.Invert(fromBizJobStatesMapping)

func FromBizJob(d *biz.Job) *anno.Job {
	if d == nil {
		return nil
	}
	o := &anno.Job{
		Uid: d.GetUid(),
		// Lot:       FromBizLot(d.Lot),
		IdxInLot:  d.IdxInLot,
		Subtype:   d.Subtype,
		ElemsCnt:  d.ElemsCnt,
		State:     fromBizJobStatesMapping[d.State],
		Phase:     d.Phase,
		Cause:     d.Cause,
		InsCnt:    d.InsCnt,
		InsTotal:  d.InsTotal,
		LotUid:    kid.StringID(d.LotID),
		UpdatedAt: timestamppb.New(d.UpdatedAt),
		CreatedAt: timestamppb.New(d.CreatedAt),
	}
	if d.Ally != nil {
		o.Elements = d.Ally.Elements
		o.Comments = d.Ally.Comments
		o.ElementsUrls = lo.Map(d.Ally.ElementsSegments, func(v *biz.ElementsSegment, _ int) string { return v.URI })
		o.CommentsUrl = d.Ally.CommentsURI
		o.AnnotationsUrl = d.Ally.AnnotationsURI

		if e := d.Ally.Annotations.E; e != nil {
			o.Annotations = e.ElementAnnos
			o.JobAttrs = e.Attrs
			o.NeedInterpolation = e.NeedInterpolation
		}
		if e := d.Ally.RawdataParams.E; e != nil {
			o.CamParams = e.CamParams
		}

		// fill title
		for _, e := range o.Elements {
			for _, rd := range e.Datas {
				if rd.Title == "" {
					rd.Title = path.Base(rd.Name)
					rd.Title = rd.Title[:len(rd.Title)-len(path.Ext(rd.Title))]
				}
			}
		}
	}
	return o
}

func ToBizJob(d *anno.CreateJobRequest) *biz.Job {
	if d == nil {
		return nil
	}
	o := &biz.Job{
		LotID:    kid.ParseID(d.LotUid),
		IdxInLot: d.IdxInLot,
		Subtype:  d.Subtype,
		ElemsCnt: int32(len(d.Elements)),
		State:    biz.ToJobState(d.State),
		Phase:    d.Phase,
		Ally: &biz.Jobally{
			Elements: d.Elements,
			Comments: d.Comments,
		},
	}
	if d.Annotations != nil {
		o.InsCnt = d.Annotations.InsCnt
		// o.InsTotal = d.Annotations.InsTotal
		o.Ally.Annotations = *serial.New(d.Annotations)
	}
	return o
}

func ToJoblogAction(action string) anno.GetJoblogReply_Log_Action_Enum {
	if strings.HasPrefix(action, biz.ForceActionPrefix) {
		action = action[len(biz.ForceActionPrefix):]
	}
	return anno.GetJoblogReply_Log_Action_Enum(anno.GetJoblogReply_Log_Action_Enum_value[action])
}

func ToJoblogDetails(d *biz.JoblogDetails, commenter *client.BaseUser, commentPhase int32) *anno.GetJoblogReply_Log_Details {
	if d == nil {
		return nil
	}
	var giveup *anno.GetJoblogReply_Log_GiveupReason
	if d.GiveupReason != nil {
		giveup = &anno.GetJoblogReply_Log_GiveupReason{
			Reason:  d.GiveupReason.Reason,
			Details: d.GiveupReason.Details,
		}
	}
	lo.ForEach(d.AddComments, func(v *anno.AnnoComment, _ int) {
		v.Commenter = commenter
	})
	return &anno.GetJoblogReply_Log_Details{
		GiveupReason: giveup,
		Resolves:     d.Resolves,
		AddComments:  d.AddComments,
	}
}

type JobsService struct {
	anno.UnimplementedJobsServer
	bz    *biz.JobsBiz
	lotbz *biz.LotsBiz
	log   *log.Helper

	jobelemrepo           biz.JobelemRepo
	jobskipannotationrepo biz.JobSkipAnnotationRepo
}

func NewJobsService(bz *biz.JobsBiz, lotbz *biz.LotsBiz, jobelemrepo biz.JobelemRepo, jobskipannotation biz.JobSkipAnnotationRepo, logger log.Logger) *JobsService {
	return &JobsService{bz: bz, lotbz: lotbz, jobelemrepo: jobelemrepo, jobskipannotationrepo: jobskipannotation, log: log.NewHelper(logger)}
}

func (o *JobsService) CreateJob(ctx context.Context, req *anno.CreateJobRequest) (*anno.Job, error) {
	// check permissions
	user := biz.UserFromCtx(ctx)
	if client.IsPrivileged(user.GetRole()) {
		return nil, errors.NewErrForbidden()
	}

	lot, err := o.lotbz.GetByUid(ctx, req.LotUid)
	if err != nil {
		return nil, err
	}
	job := ToBizJob(req)
	job.LotID = lot.ID
	data, err := o.bz.Create(ctx, job)
	return FromBizJob(data), err
}

func (o *JobsService) UpdateJob(ctx context.Context, req *anno.UpdateJobRequest) (*anno.Job, error) {
	if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsJob, req.Job.Uid) {
		return nil, errors.NewErrForbidden()
	}

	job, err := o.bz.GetByUid(ctx, req.Job.Uid, false)
	if err != nil {
		return nil, err
	}
	data, err := o.bz.Update(ctx, job, field.NewMask(req.Fields...))
	return FromBizJob(data), err
}

func (o *JobsService) DeleteJob(ctx context.Context, req *anno.DeleteJobRequest) (*emptypb.Empty, error) {
	if !client.IsAllowed(ctx, "", biz.PermDelete, biz.PermClsJob, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	return &emptypb.Empty{}, o.bz.DeleteByUid(ctx, req.Uid)
}

func (o *JobsService) GetJob(ctx context.Context, req *anno.GetJobRequest) (*anno.Job, error) {
	if !client.IsAllowed(ctx, "", biz.PermGet, biz.PermClsJob, req.Uid) {
		return nil, errors.NewErrForbidden()
	}
	if req.Expand && !client.IsPrivilegedUser(biz.UserFromCtx(ctx).User) {
		return nil, errors.NewErrForbidden(errors.WithMessage("cannot expand job"))
	}

	data, err := o.bz.GetByUid(ctx, req.Uid, true)
	spew.Dump(data)
	if err != nil {
		return nil, err
	}

	r := FromBizJob(data)
	if req.Expand {
		if err := o.expandJob(ctx, data, r); err != nil {
			return nil, err
		}
	}
	if err := o.signJob(ctx, data, r); err != nil {
		return nil, err
	}
	// set executor uid for privileged requestors
	if op := biz.UserFromCtx(ctx); client.IsPrivileged(op.GetRole()) {
		r.ExecutorUid = data.ExecutorUid

		// fill LastExecutor
		lastExecutorUid := data.LastExecutor
		if lastExecutorUid == "" {
			return r, nil
		}

		user, err := client.GetUser(ctx, lastExecutorUid)
		if err != nil {
			return nil, err
		}
		r.LastExecutor = client.ToBaseUser(user)

		// fill LastExecteam
		team, err := client.GetTeam(ctx, data.LastExecteam)
		if err != nil {
			return nil, err
		}
		r.LastExecteam = client.ToBaseUser(team)
	}
	return r, err
}

func (o *JobsService) ListJob(ctx context.Context, req *anno.ListJobRequest) (*anno.ListJobReply, error) {
	if req.Filter == nil || (len(req.Filter.Uids) == 0 && req.Filter.LotUid == "" && req.Filter.Jobclip == "") {
		return nil, errors.NewErrEmptyField(errors.WithFields("uids", "lot_uid", "jobclip"))
	}
	isPrivileged := client.IsPrivilegedUser(biz.UserFromCtx(ctx).User)
	if !isPrivileged {
		if req.FullJob {
			return nil, errors.NewErrForbidden()
		}
		if !client.IsAllowed(ctx, "", biz.PermListJob, biz.PermClsLot, req.Filter.LotUid) {
			return nil, errors.NewErrForbidden()
		}
	}

	if req.ElemNamePattern != "" && req.Filter.LotUid != "" {
		filter := &biz.JobelemFilter{
			LotID:           kid.ParseID(req.Filter.LotUid),
			ElemNamePattern: req.ElemNamePattern,
		}
		type Jobelem struct {
			JobID int64
		}
		var elems []Jobelem
		_, err := o.jobelemrepo.GroupBy(ctx, []string{biz.Jobelem_JobID.Full()}, nil, filter, repo.Pager{}, &elems)
		if err != nil {
			return nil, err
		}
		if len(elems) == 0 {
			return &anno.ListJobReply{}, nil
		}
		req.Filter.Uids = lo.Map(elems, func(e Jobelem, _ int) string { return kid.StringID(e.JobID) })
	}
	if req.Filter.Jobclip != "" {
		jobelem, err := o.bz.Repo.GetJobByClip(ctx, req.Filter.Jobclip)
		if err != nil {
			return &anno.ListJobReply{}, nil
		}
		if jobelem.JobID == 0 {
			return &anno.ListJobReply{}, nil
		}
		jobidE := kid.StringID(jobelem.JobID)

		if len(req.Filter.Uids) != 0 {
			flagjobidE := false
			for _, uid := range req.Filter.Uids {
				if uid == jobidE {
					flagjobidE = true
					break
				}
			}
			if !flagjobidE {
				return &anno.ListJobReply{}, nil
			}

		}

		req.Filter.Uids = []string{jobidE}
	}

	pager := biz.Pager{
		Pagesz: int(req.Pagesz),
		Page:   int(req.Page),
	}
	filter := &biz.JobListFilter{
		IDs:           biz.GetIDs(req.Filter.Uids),
		LotID:         kid.ParseID(req.Filter.LotUid),
		LastExecteam:  req.Filter.LastExecteam,
		LastExecutors: req.Filter.LastExecutors,
		FullJob:       req.FullJob,
	}
	if phase := req.Filter.Phase; phase > 0 {
		filter.Phases = []int32{phase}
	}
	if phasess := req.Filter.Phases; len(phasess) > 0 {
		filter.Phases = phasess
	}
	if state := req.Filter.State; state != anno.Job_State_unspecified {
		filter.States = []biz.JobState{toBizJobStateMapping[state]}
	}

	var cnt int
	if pager.Page == 0 {
		var err error
		cnt, err = o.bz.Count(ctx, filter)
		if err != nil {
			return nil, err
		}
	}
	datas, err := o.bz.List(ctx, filter, pager)
	if err != nil {
		return nil, err
	}

	jobids := lo.Map(datas, func(v *biz.Job, _ int) int64 { return v.ID })

	jobelemsdata, err := o.bz.Repo.GetJobClips(ctx, jobids)
	jobidToEleName := lo.SliceToMap(jobelemsdata, func(item *biz.Jobelem) (int64, string) {
		return item.JobID, item.ElemName
	})
	// set executor uid for privileged requestors
	jobs := kslice.Map(FromBizJob, datas)
	if !isPrivileged || !req.FullJob {
		for _, job := range jobs {
			job.Elements = nil
			job.Annotations = nil
			job.Comments = nil
			job.CamParams = nil
		}
	}
	lo.ForEach(jobs, func(v *anno.Job, _ int) { v.JobElemClip = jobidToEleName[kid.ParseID(v.Uid)] })
	// if !isPrivileged {
	// 	return &anno.ListJobReply{Total: int32(cnt), Jobs: jobs}, nil
	// }

	jobIDs := make([]int64, 0, len(jobs))
	executors := make([]string, 0, len(jobs))
	teamUids := make([]string, 0, len(jobs))
	maxPhase := int32(0)
	for i, v := range jobs {
		job := datas[i]
		v.ExecutorUid = job.ExecutorUid
		if executor := job.LastExecutor; executor != "" && req.ShowLastExecutor {
			executors = append(executors, executor)
			teamUids = append(teamUids, job.LastExecteam)
		}
		if phase := job.Phase; phase > 1 && req.ShowExecutors {
			jobIDs = append(jobIDs, job.ID)
			if phase > maxPhase {
				maxPhase = phase
			}
		}
	}

	joblogs := make(map[int64][]*biz.Joblog, len(jobs))
	if len(jobIDs) > 0 {
		joblogs, err = o.bz.GetPhaseExecutors(ctx, jobIDs, maxPhase-1)
		if err != nil {
			return nil, err
		}
		for _, jlogs := range joblogs {
			executors = append(executors, lo.Map(jlogs, func(j *biz.Joblog, _ int) string { return j.ToExecutorUid })...)
			// teamUids = append(teamUids, lo.Map(jlogs, func(j *biz.Joblog, _ int) string { return j.ToExecteam })...)
		}
	}
	if len(executors) == 0 {
		return &anno.ListJobReply{Total: int32(cnt), Jobs: jobs}, nil
	}

	// fill LastExecutor and LastExecteam and Metadata.Executors
	users, err := client.ListUsersInMap(ctx, executors)
	if err != nil {
		return nil, err
	}
	teams, err := client.ListTeamsInMap(ctx, teamUids)
	if err != nil {
		return nil, err
	}
	phaseExecutors := make(map[string]*anno.ElementAnno_Metadata, len(jobs))
	for i, v := range jobs {
		if req.ShowLastExecutor {
			v.LastExecutor = users[datas[i].LastExecutor]
			v.LastExecteam = teams[datas[i].LastExecteam]
		}
		if req.ShowExecutors {
			phaseExecutors[v.Uid] = &anno.ElementAnno_Metadata{
				Executors: buildExecutors(joblogs[datas[i].ID], users),
			}
		}
	}
	return &anno.ListJobReply{Total: int32(cnt), Jobs: jobs, Executors: phaseExecutors}, nil
}

func buildExecutors(joblogs []*biz.Joblog, users map[string]*client.BaseUser) []*anno.ElementAnno_Metadata_Executor {
	var phaseExecutors []*anno.ElementAnno_Metadata_Executor
	for i := len(joblogs) - 1; i >= 0; i-- { // joblogs are already sorted by time
		joblog := joblogs[i]
		executor := &anno.ElementAnno_Metadata_Executor{
			User:     users[joblog.ToExecutorUid],
			SubmitAt: timestamppb.New(joblog.CreatedAt),
			Phase:    joblog.ToPhase,
		}

		if i == len(joblogs)-1 {
			phaseExecutors = make([]*anno.ElementAnno_Metadata_Executor, joblog.ToPhase)
			phaseExecutors[joblog.ToPhase-1] = executor
			continue
		}

		if int(joblog.ToPhase-1) < len(phaseExecutors) && phaseExecutors[joblog.ToPhase-1] == nil {
			phaseExecutors[joblog.ToPhase-1] = executor
			if joblog.ToPhase == 1 {
				break
			}
		}
	}
	return phaseExecutors
}

func (o *JobsService) GetJoblog(ctx context.Context, req *anno.GetJoblogRequest) (*anno.GetJoblogReply, error) {
	if !client.IsAllowed(ctx, "", biz.PermLog, biz.PermClsJob, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	pager := biz.Pager{
		Pagesz: int(req.Pagesz),
		Page:   int(req.Page),
	}
	job, err := o.bz.GetByUid(ctx, req.Uid, true)
	if err != nil {
		return nil, err
	}
	var cnt int
	if pager.Page == 0 {
		var err error
		cnt, err = o.bz.CountJoblog(ctx, job.ID)
		if err != nil {
			return nil, err
		}
	}
	logs, err := o.bz.ListJoblogByID(ctx, job.ID, pager)
	if err != nil {
		return nil, err
	}
	if len(logs) == 0 {
		return &anno.GetJoblogReply{}, nil
	}

	users, err := client.ListUsersInMap(client.NewCtxUseSvcAccount(ctx),
		lo.Map(logs, func(l *biz.Joblog, _ int) string { return l.OperatorUid }))
	if err != nil {
		return nil, err
	}
	return &anno.GetJoblogReply{
		Total: int32(cnt),
		Logs: lo.Map(logs, func(l *biz.Joblog, _ int) *anno.GetJoblogReply_Log {
			op := users[l.OperatorUid]
			return &anno.GetJoblogReply_Log{
				Operator:  op,
				Action:    ToJoblogAction(l.Action),
				FromPhase: l.FromPhase,
				ToPhase:   l.ToPhase,
				CreatedAt: timestamppb.New(l.CreatedAt),
				Details:   ToJoblogDetails(l.Details.E, op, l.FromPhase),
			}
		}),
	}, err
}

func (o *JobsService) GetRawJoblog(ctx context.Context, req *anno.GetJoblogRequest) (*anno.GetRawJoblogReply, error) {
	if !client.IsPrivilegedUser(biz.UserFromCtx(ctx).User) {
		return nil, errors.NewErrForbidden()
	}

	pager := biz.Pager{
		Pagesz: int(req.Pagesz),
		Page:   int(req.Page),
	}
	job, err := o.bz.GetByUid(ctx, req.Uid, true)
	if err != nil {
		return nil, err
	}
	logs, err := o.bz.ListJoblogByID(ctx, job.ID, pager)
	if err != nil {
		return nil, err
	}

	data, err := biz.JSONCodec().Marshal(logs)
	if err != nil {
		return nil, errors.NewErrServerError(errors.WithMessage("failed to marshal logs")).WithCause(err)
	}
	return &anno.GetRawJoblogReply{Logs: string(data)}, nil
}

func (o *JobsService) ClaimJob(ctx context.Context, req *anno.ClaimJobRequest) (*anno.ClaimJobResponse, error) {
	// no need to check permissions here
	// if !client.IsAllowed(ctx, "", biz.PermClaimJob, biz.PermClsLot, req.LotUid) {
	// 	return nil, errors.NewErrForbidden()
	// }
	spew.Dump("---> before claimJob：", req)
	if req.Renew {
		job, err := o.bz.GetByUid(ctx, req.JobUid, false)
		if err != nil {
			return nil, err
		}
		op := biz.UserFromCtx(ctx)
		if job.ExecutorUid != op.GetUid() || job.State != biz.JobStateDoing {
			return nil, errors.NewErrForbidden(errors.WithMessage("you have not claimed the job"))
		}

		job.UpdatedAt = time.Now()
		job, err = o.bz.Update(ctx, job, field.NewMask(biz.JobSfldUpdatedAt))
		if err != nil {
			return nil, err
		}
		return &anno.ClaimJobResponse{Job: &anno.Job{UpdatedAt: timestamppb.New(job.UpdatedAt)}}, nil
	}

	job, err := o.bz.ClaimJob(ctx, req)
	if job == nil {
		return nil, err
	}

	annoJob := FromBizJob(job)
	spew.Dump("---> before annoJob：", annoJob)
	if err := o.signJob(ctx, job, annoJob); err != nil {
		spew.Dump(err)
		return nil, err
	}
	spew.Dump("---> after annoJob：", annoJob)
	return &anno.ClaimJobResponse{Job: annoJob}, nil
}

func (o *JobsService) signJob(ctx context.Context, bizJob *biz.Job, annoJob *anno.Job) error {
	if bizJob == nil || bizJob.Ally == nil {
		return nil
	}

	annoJob.ElementsUrls = make([]string, len(bizJob.Ally.ElementsSegments))
	for i, seg := range bizJob.Ally.ElementsSegments {
		fmt.Println("---> sign elements: ", seg)
		result, err := ostore.SignGetURL(ctx, seg.URI, 0, 0)
		if err != nil {
			o.log.Errorw("msg", "failed to sign elements url", "error", err)
			return err
		}
		annoJob.ElementsUrls[i] = result.URL
	}

	result, err := ostore.SignGetURL(ctx, bizJob.Ally.AnnotationsURI, 0, 0)
	if err != nil {
		o.log.Errorw("msg", "failed to sign annotations url", "error", err)
		return err
	}
	annoJob.AnnotationsUrl = result.URL

	result, err = ostore.SignGetURL(ctx, bizJob.Ally.CommentsURI, 0, 0)
	if err != nil {
		o.log.Errorw("msg", "failed to sign comments url", "error", err)
		return err
	}
	annoJob.CommentsUrl = result.URL

	return nil
}

func (o *JobsService) expandJob(ctx context.Context, bizJob *biz.Job, annoJob *anno.Job) error {
	if bizJob == nil || bizJob.Ally == nil {
		return nil
	}

	annoJob.ElementsUrls = make([]string, len(bizJob.Ally.ElementsSegments))
	for _, seg := range bizJob.Ally.ElementsSegments {
		if seg.URI != "" {
			element, err := biz.DownloadData[biz.ElementData](ctx, seg.URI)
			if err != nil {
				return err
			}
			annoJob.Elements = append(annoJob.Elements, element.Elements...)
			if annoJob.CamParams == nil && element.CamParams != nil {
				annoJob.CamParams = element.CamParams
			}
		}
	}

	if bizJob.Ally.AnnotationsURI != "" {
		annotation, err := biz.DownloadData[biz.AnnotationData](ctx, bizJob.Ally.AnnotationsURI)
		if err != nil {
			return err
		}
		annoJob.Annotations = append(annoJob.Annotations, annotation.ElementAnnos...)
		annoJob.JobAttrs = annotation.JobAttrs
	}

	if bizJob.Ally.CommentsURI != "" {
		comment, err := biz.DownloadData[biz.CommentData](ctx, bizJob.Ally.CommentsURI)
		if err != nil {
			return err
		}
		annoJob.Comments = append(annoJob.Comments, comment.Comments...)
	}

	return nil
}

func (o *JobsService) AssignJob(ctx context.Context, req *anno.AssignJobRequest) (*emptypb.Empty, error) {
	job, err := o.bz.GetByUid(ctx, req.Uid, true)
	if err != nil {
		return nil, err
	}
	if job.Lot.State.IsFinal() {
		return nil, errors.NewErrFailedPrecondition(errors.WithMessage("lot is already in final state"))
	}
	// require execteam manager's permission
	if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsGroup, job.LotPhase.Execteam) {
		return nil, errors.NewErrForbidden()
	}

	return &emptypb.Empty{}, o.bz.AssignJob(ctx, job, req)
}

func (o *JobsService) GiveupJob(ctx context.Context, req *anno.GiveupJobRequest) (*emptypb.Empty, error) {
	// no need to check permissions here
	job, err := o.bz.GetByUid(ctx, req.Uid, true)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, o.bz.GiveupJob(ctx, job, req)
}

func (o *JobsService) SubmitJob(ctx context.Context, req *anno.SubmitJobRequest) (*emptypb.Empty, error) {
	t := time.Now()
	fmt.Println("---> enter submit job: ", t.String())
	fmt.Println("---> enter get job by uid: ", time.Now().String())
	// no need to check permissions here
	job, err := o.bz.GetByUid(ctx, req.Uid, true, biz.WithElements(), biz.WithAnnotations(), biz.WithComments())
	if err != nil {
		return nil, err
	}
	fmt.Println("---> end get job by uid: ", time.Now().Sub(t), time.Now().String())
	return &emptypb.Empty{}, o.bz.SubmitJob(ctx, job, req)
}

func (o *JobsService) ReviewJob(ctx context.Context, req *anno.ReviewJobRequest) (*emptypb.Empty, error) {
	o.log.Warn("----入口review", "time", time.Now())
	t := time.Now()
	fmt.Println("---> out enter review job: ", t.String())
	fmt.Println("---> enter get job by uid: ", time.Now().String())
	job, err := o.bz.GetByUid(ctx, req.Uid, true, biz.WithElements(), biz.WithAnnotations(), biz.WithComments())
	if err != nil {
		return nil, err
	}
	fmt.Println("---> end get job by uid: ", time.Now().Sub(t), time.Now().String())
	o.log.Warn("----入口review2", "time", time.Now())
	err = o.bz.ReviewJob(ctx, job, req)
	fmt.Println("---> out end review job: ", time.Now().Sub(t), time.Now().String())
	return &emptypb.Empty{}, err
}

func (o *JobsService) BatchRevertJob(ctx context.Context, req *anno.BatchRevertJobRequest) (*anno.BatchRevertJobReply, error) {
	if req.Filter == nil || (len(req.Filter.Uids) == 0 && (req.Filter.LotUid == "" || req.Filter.Phase == 0)) {
		return nil, errors.NewErrEmptyField(errors.WithFields("uids", "lot_uid", "phase"))
	}
	if req.Action == nil || req.Action.ToPhase <= 0 {
		return nil, errors.NewErrInvalidField(errors.WithFields("to_phase"))
	}
	if req.Options == nil {
		req.Options = &anno.BatchRevertJobRequest_Options{}
	}
	spew.Dump(req)
	// allow only platform operators
	op := biz.UserFromCtx(ctx)
	if !(client.IsPrivileged(op.GetRole()) || (client.IsSysRole(op.GetRole()) &&
		client.IsAllowed(ctx, "", biz.PermSetPolicy, biz.PermClsLot, req.Filter.LotUid))) {
		return nil, errors.NewErrForbidden()
	}

	pager := biz.Pager{Pagesz: 1000}
	filter := &biz.JobListFilter{
		IDs:           biz.GetIDs(req.Filter.Uids),
		LotID:         kid.ParseID(req.Filter.LotUid),
		LastExecteam:  req.Filter.LastExecteam,
		LastExecutors: req.Filter.LastExecutors,
	}
	if phase := req.Filter.Phase; phase > 0 {
		filter.Phases = []int32{phase}
	}
	if state := req.Filter.State; state != anno.Job_State_unspecified {
		filter.States = []biz.JobState{toBizJobStateMapping[state]}
	}
	spew.Dump(filter)
	//return nil, nil

	var err error
	ok, fail := []int64{}, []int64{}
outerLoop:
	for {
		var jobs []*biz.Job
		var failJobs []*biz.Job
		jobs, err = o.bz.List(ctx, filter, pager)
		if err != nil || len(jobs) == 0 {
			if err != nil {
				o.log.Errorw("msg", "failed to query jobs", "error", err)
			}
			break
		}
		pager.Page++
		ok = append(ok, lo.Map(jobs, func(v *biz.Job, _ int) int64 { return v.ID })...)
		for _, job := range jobs {
			if job.LotID != jobs[0].LotID {
				fail = append(fail, lo.Map(jobs, func(v *biz.Job, _ int) int64 { return v.ID })...)
				err = errors.NewErrBadRequest(errors.WithMessage("not all jobs belong to the same lot"))
				o.log.Errorw("msg", "failed to BatchRevertJob", "error", err)
				break outerLoop
			}
		}
		spew.Dump(jobs)

		failJobs, err = o.bz.BatchRevertJob(ctx, jobs, req.Action.ToPhase, req.Options)
		if err != nil {
			fail = append(fail, lo.Map(jobs, func(v *biz.Job, _ int) int64 { return v.ID })...)
			o.log.Errorw("msg", "failed to BatchRevertJob", "error", err)
			continue
		}
		fail = append(fail, lo.Map(failJobs, func(v *biz.Job, _ int) int64 { return v.ID })...)
	}

	ok, _ = lo.Difference(ok, fail)
	o.log.Infow("msg", "BatchRevertJob", "ok_jobs", len(ok), "fail_jobs", len(fail))
	if len(ok) == 0 && err != nil {
		return nil, err
	}
	return &anno.BatchRevertJobReply{OkJobUids: biz.GetUids(ok), FailJobUids: biz.GetUids(fail)}, nil
}

func (o *JobsService) GetAnnos(ctx context.Context, req *anno.GetJobAnnosRequest) (*anno.GetJobAnnosReply, error) {
	if req.Filter == nil || req.Filter.LotUid == "" {
		return nil, errors.NewErrEmptyField(errors.WithFields("lot_uid"))
	}
	if !client.IsAllowed(ctx, "", biz.PermExportAnno, biz.PermClsLot, req.Filter.LotUid) {
		return nil, errors.NewErrForbidden()
	}

	pager := biz.Pager{
		Pagesz:    int(req.Pagesz),
		PageToken: biz.PageToken(req.PageToken),
	}
	filter := &biz.JobListFilter{
		IDs:           biz.GetIDs(req.Filter.Uids),
		LotID:         kid.ParseID(req.Filter.LotUid),
		LastExecteam:  req.Filter.LastExecteam,
		LastExecutors: req.Filter.LastExecutors,
	}
	if phase := req.Filter.Phase; phase > 0 {
		filter.Phases = []int32{phase}
	}
	if state := req.Filter.State; state != anno.Job_State_unspecified {
		filter.States = []biz.JobState{toBizJobStateMapping[state]}
	}

	annos, nextPageToken, err := o.bz.GetAnnos(ctx, filter, pager)
	if err != nil {
		return nil, err
	}
	return &anno.GetJobAnnosReply{
		Annos:         lo.Map(annos, func(v *biz.Jobally, _ int) *anno.JobAnno { return v.Annotations.E }),
		NextPageToken: nextPageToken,
	}, nil
}

func (o *JobsService) SetRawdataEmbedding(ctx context.Context, req *anno.SetRawdataEmbeddingRequest) (
	*emptypb.Empty, error) {

	if !client.IsPrivilegedUser(biz.UserFromCtx(ctx).User) &&
		!client.IsAllowed(ctx, "", biz.PermGet, biz.PermClsJob, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	job, err := o.bz.GetByUid(ctx, req.Uid, false)
	if err != nil {
		return nil, err
	}
	jobally, err := o.bz.GetJoballyByID(ctx, job.ID)
	if err != nil {
		return nil, err
	}

	job.Ally = jobally
	if err := job.Ally.DownloadElements(ctx); err != nil {
		return nil, err
	}

	elems := job.Ally.Elements
	if len(elems) <= int(req.ElemIdx) || len(elems[req.ElemIdx].Datas) <= int(req.RawdataIdx) {
		return nil, errors.NewErrInvalidField(errors.WithMessage("element index or rawdata index"),
			errors.WithFields("elem_idx", "rawdata_idx"))
	}

	rd := elems[req.ElemIdx].Datas[req.RawdataIdx]
	url, err := client.SetRawdataEmbedding(client.NewCtxUseSvcAccount(ctx), job.Lot.DataUid, rd.Name, req.EmbeddingUri)
	if err != nil {
		return nil, err
	}
	rd.Embedding = &anno.Rawdata_Embedding{Url: url}

	lot, err := o.lotbz.GetByID(ctx, job.LotID, false)
	if err != nil {
		return nil, err
	}

	uploaded, err := job.UploadElements(ctx, &biz.ElementData{Elements: elems}, lot.OrgUid, lot.GetUid())
	if err != nil {
		return nil, err
	}

	if uploaded {
		_, err = o.bz.Update(ctx, job, field.NewMask(biz.JoballySfldElementsSegments.String()))
	} else {
		_, err = o.bz.Update(ctx, job, field.NewMask(biz.JoballySfldElements.String()))
	}
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, err
}

func (o *JobsService) SaveJobDraft(ctx context.Context, req *anno.SaveJobDraftRequest) (*anno.SaveJobDraftReply, error) {
	t1 := time.Now()
	t := time.Now()
	fmt.Println("---> enter job draft: ", t1.String())
	//if req.CompressedDraft != nil && len(req.CompressedDraft) > 0 {
	//	uncompressedData, err := decompressData(req.CompressedDraft)
	//	if err != nil {
	//		return nil, err
	//	}
	//	req.CompressedDraft = nil
	//	req.Draft = string(uncompressedData)
	//}
	jobID := kid.ParseID(req.Uid)
	if err := o.bz.CheckJobQualification(ctx, jobID); err != nil {
		return nil, err
	}
	fmt.Println("---> draft len: ", len(req.Draft))

	if req.Draft == "" { // clear job draft
		if err := o.bz.Repo.SaveJobDraft(ctx, jobID, nil); err != nil {
			return nil, err
		}
		return &anno.SaveJobDraftReply{}, nil
	}
	// 判断是否带有3d分割数据
	//var draftContent struct {
	//	Draft string `json:"draft"`
	//}
	//err = json.Unmarshal(draft.Content, &draftContent)
	//if err != nil {
	//	return err
	//}
	var draftSegInfo struct {
		Segmentation3d []*anno.Segmentation3D `json:"segmentation3d"`
	}
	err := biz.JSONCodec().Unmarshal([]byte(req.Draft), &draftSegInfo)
	if err != nil {
		return nil, err
	}
	if draftSegInfo.Segmentation3d != nil && len(draftSegInfo.Segmentation3d) > 0 {
		if lo.ContainsBy(draftSegInfo.Segmentation3d, func(item *anno.Segmentation3D) bool {
			return item != nil
		}) {
			fmt.Println("has segmentation3d !!!")
			ctx = biz.WithSegmentation3dFlag(ctx, true)
		}
	}
	fmt.Println("---> parse job draft req: ", time.Now().Sub(t))
	t = time.Now()
	version := time.Now().Format(time.RFC3339)
	draft := &biz.JobDraft{Version: version}
	content, err := biz.JSONCodec().Marshal(req)
	if err != nil {
		return nil, err
	}
	draft.Content = content
	fmt.Println("---> marshal job req: ", time.Now().Sub(t))
	if err := o.bz.Repo.SaveJobDraft(ctx, jobID, draft); err != nil {
		return nil, err
	}
	fmt.Println("---> end job draft: ", time.Now().Sub(t1), time.Now().String())
	return &anno.SaveJobDraftReply{Version: version}, nil
}

func (o *JobsService) GetJobDraft(ctx context.Context, req *anno.GetJobDraftRequest) (*anno.GetJobDraftReply, error) {
	if !client.IsAllowed(ctx, "", biz.PermGet, biz.PermClsJob, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	jobID := kid.ParseID(req.Uid)
	if err := o.bz.CheckJobQualification(ctx, jobID); err != nil {
		return nil, err
	}

	draft, err := o.bz.Repo.GetJobDraft(ctx, jobID)
	if err != nil {
		return nil, err
	}
	if draft == nil {
		return &anno.GetJobDraftReply{}, nil
	}

	result, err := ostore.SignGetURLWithS3(ctx, draft.URI, 0)
	if err != nil {
		return nil, err
	}

	draftURL := result.URL
	if draftURL == "" && len(draft.Content) > 0 {
		content := base64.URLEncoding.EncodeToString(draft.Content)
		draftURL = "data:plain/text," + content
	}

	return &anno.GetJobDraftReply{Version: draft.Version, DraftUrl: draftURL}, nil
}

func (o *JobsService) GetJobLastCommitLog(ctx context.Context, req *anno.GetJobLastCommitLogRequest) (
	*anno.GetJobLastCommitLogReply, error) {
	if !client.IsAllowed(ctx, "", biz.PermGet, biz.PermClsJob, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	jobID := kid.ParseID(req.Uid)
	phase := req.Phase
	var actions []string
	switch req.Direction {
	case anno.GetJobLastCommitLogRequest_Direction_up:
		actions = []string{biz.JobActionSubmit, biz.JobActionAccept}
	case anno.GetJobLastCommitLogRequest_Direction_down:
		actions = []string{biz.JobActionReject, biz.JobActionRecycle, biz.JobActionForceReject, biz.JobActionForceRecycle}
	default:
		actions = []string{biz.JobActionSubmit, biz.JobActionAccept, biz.JobActionReject, biz.JobActionRecycle,
			biz.JobActionForceReject, biz.JobActionForceRecycle}
	}

	joblogs, err := o.bz.ListJoblog(ctx,
		&biz.JoblogFilter{
			JobID:     jobID,
			FromPhase: phase,
			Actions:   actions,
		}, repo.Pager{Pagesz: 1})
	if err != nil {
		return nil, err
	}
	if len(joblogs) == 0 {
		return &anno.GetJobLastCommitLogReply{}, nil
	}

	joblog := joblogs[0]
	user, err := client.GetUser(client.NewCtxUseSvcAccount(ctx), joblog.OperatorUid)
	if err != nil {
		return nil, err
	}
	return &anno.GetJobLastCommitLogReply{
		Log: &anno.GetJoblogReply_Log{
			Operator:  client.ToBaseUser(user),
			Action:    ToJoblogAction(joblog.Action),
			FromPhase: joblog.FromPhase,
			ToPhase:   joblog.ToPhase,
			CreatedAt: timestamppb.New(joblog.CreatedAt),
		},
	}, nil
}

// HasHoldingJobs checks if there is any holding jobs for users/orgs.
// Background: Before deleting a user/org, if there is any holding jobs/lots, the delete operation should be rejected.
func (o *JobsService) HasHoldingJobs(ctx context.Context, req *anno.HasHoldingJobsRequest) (*anno.HasHoldingJobsReply, error) {
	holding := make(map[string]bool)

	if req.OrgUid != "" {
		has, err := o.lotbz.Repo().HasHoldingLots(ctx, req.OrgUid)
		if err != nil {
			return nil, err
		}
		holding[req.OrgUid] = has
	} else if len(req.UserUids) > 0 {
		userUids, err := o.bz.Repo.HasHoldingJobs(ctx, req.UserUids)
		if err != nil {
			return nil, err
		}
		for _, userUid := range userUids {
			holding[userUid] = true
		}
	}

	return &anno.HasHoldingJobsReply{Holding: holding}, nil
}

func (o *JobsService) SkipAnnotation(ctx context.Context, req *anno.SkipAnnotationRequest) (*anno.SkipAnnotationReply, error) {
	if req.Uid == "" || req.LotId == "" {
		return nil, errors.NewErrNotFound(errors.WithMessage("参数错误，uid和lotid为必传参数"))
	}
	jobdata, err := o.bz.GetByUid(ctx, req.Uid, false)
	fmt.Println(22)
	if err != nil {
		return nil, err
	}
	if jobdata.LotID != kid.ParseID(req.LotId) {
		return nil, errors.NewErrNotFound(errors.WithMessage("lot_id与job_id不匹配"))
	}

	resp := &anno.SkipAnnotationReply{SkipAnnotation: req.SkipAnnotation}
	err = o.jobskipannotationrepo.DeleteByID(ctx, kid.ParseID(req.Uid))

	data := biz.JobSkipAnnotation{
		ID:             kid.ParseID(req.Uid),
		LotID:          kid.ParseID(req.LotId),
		SkipAnnotation: req.SkipAnnotation,
	}
	_, err = o.jobskipannotationrepo.Create(ctx, &data)

	return resp, err
}

func (o *JobsService) GetSkipAnnotation(ctx context.Context, req *anno.GetSkipAnnotationRequest) (*anno.GetSkipAnnotationReply, error) {
	if req.Uid == "" {
		return nil, errors.NewErrNotFound(errors.WithMessage("参数错误，uid和lotid为必传参数"))
	}
	info, _ := o.jobskipannotationrepo.GetByID(ctx, kid.ParseID(req.Uid))

	SkipAnnotation := false
	if info != nil {
		SkipAnnotation = info.SkipAnnotation
	}

	resp := &anno.GetSkipAnnotationReply{SkipAnnotation: SkipAnnotation}
	return resp, nil
}
