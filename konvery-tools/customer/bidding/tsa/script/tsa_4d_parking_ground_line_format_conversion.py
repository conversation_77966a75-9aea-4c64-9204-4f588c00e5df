import os
import sys
import numpy as np
from scipy.spatial.transform import Rotation as R
sys.path.append('.')
from customer.common import tools


def process_groundline(label, groups_offset, offset_mapping, obj_id):
    category = tools.get_value_from_kw_attrs(label['label']['attrs'], 'category')
    if category == "wall_edge":
        if label['uuid'] in groups_offset:
            if groups_offset[label['uuid']] not in offset_mapping:
                offset_mapping[groups_offset[label['uuid']]] = obj_id
            obj_id = offset_mapping[groups_offset[label['uuid']]]
    if category == 'unknown':
        category = 'other'
    if label['label']['widget']['name'] == 'cuboid':
        point = {
            "x": label['label']['widget']['data'][0],
            "y": label['label']['widget']['data'][1],
            "z": label['label']['widget']['data'][2],
            "w": label['label']['widget']['data'][4],
            "h": label['label']['widget']['data'][5],
            "l": label['label']['widget']['data'][3],
            "yaw": R.from_quat(label['label']['widget']['data'][-4:]).as_euler('xyz')[2]
        }
        point_type = 'box'
    elif label['label']['widget']['name'] == 'poly3d':
        point = np.array(label['label']['widget']['data']).reshape(-1, 3).tolist()
        point_type = 'polygon'
    elif label['label']['widget']['name'] == 'line3d':
        point = np.array(label['label']['widget']['data']).reshape(-1, 3).tolist()
        point_type = 'polyline'
    else:
        raise ValueError(f'Unsupport widget: {label["label"]["widget"]["name"]}')
    obj = {
        "id": obj_id,
        "type": point_type,
        "point": point,
        "class": category
    }
    return obj


def process_groundmarking(label, groups_offset, offset_mapping, obj_id):
    category = tools.get_value_from_kw_attrs(label['label']['attrs'], 'category1')
    if label['label']['widget']['name'] == 'cuboid':
        point = {
            "x": label['label']['widget']['data'][0],
            "y": label['label']['widget']['data'][1],
            "z": label['label']['widget']['data'][2],
            "w": label['label']['widget']['data'][4],
            "h": label['label']['widget']['data'][5],
            "l": label['label']['widget']['data'][3],
            "yaw": R.from_quat(label['label']['widget']['data'][-4:]).as_euler('xyz')[2]
        }
        point_type = 'box'
    elif label['label']['widget']['name'] == 'poly3d':
        point = np.array(label['label']['widget']['data']).reshape(-1, 3).tolist()
        point_type = 'polygon'
    elif label['label']['widget']['name'] == 'line3d':
        point = np.array(label['label']['widget']['data']).reshape(-1, 3).tolist()
        point_type = 'polyline'
    else:
        raise ValueError(f'Unsupport widget: {label["label"]["widget"]["name"]}')
    obj = {
        "type": point_type,
        "point": point,
        "class": category
    }
    if category == "stop_block":
        if label['uuid'] in groups_offset:
            if groups_offset[label['uuid']] not in offset_mapping:
                offset_mapping[groups_offset[label['uuid']]] = obj_id
            obj_id = offset_mapping[groups_offset[label['uuid']]]
            obj["groupID"] = obj_id
        else:
            obj["id"] = obj_id
    return obj


def process_arrow_mark(label):
    category = tools.get_value_from_kw_attrs(label['label']['attrs'], 'category2')
    if label['label']['widget']['name'] == 'poly3d':
        point = np.array(label['label']['widget']['data']).reshape(-1, 3).tolist()
        point_type = 'polygon'
    else:
        raise ValueError(f'Unsupport widget: {label["label"]["widget"]["name"]}')
    obj = {
        "type": point_type,
        "point": point,
        "class": category
    }
    return obj


def process_land_mark(label):
    category = tools.get_value_from_kw_attrs(label['label']['attrs'], 'category3')
    if label['label']['widget']['name'] == 'poly3d':
        point = np.array(label['label']['widget']['data']).reshape(-1, 3).tolist()
        point_type = 'polygon'
    elif label['label']['widget']['name'] == 'line3d':
        point = np.array(label['label']['widget']['data']).reshape(-1, 3).tolist()
        point_type = 'polyline'
    else:
        raise ValueError(f'Unsupport widget: {label["label"]["widget"]["name"]}')
    obj = {
        "type": point_type,
        "point": point,
        "class": category
    }
    return obj


def process_lane(label):
    category = tools.get_value_from_kw_attrs(label['label']['attrs'], 'category4')
    if label['label']['widget']['name'] == 'line3d':
        point = np.array(label['label']['widget']['data']).reshape(-1, 3).tolist()
        point_type = 'polyline'
    else:
        raise ValueError(f'Unsupport widget: {label["label"]["widget"]["name"]}')
    obj = {
        "type": point_type,
        "point": point,
        "color": tools.get_value_from_kw_attrs(label['label']['attrs'], 'color'),
        "class": category
    }
    return obj


def process_parking_slot(label):
    parking_slot_type_mapping = {
        "perpendicular": 0,
        "parallel": 1,
        "incline": 2
    }
    occupy_mapping = {
        "无占用": 0,
        "车辆占用": 1,
        "其他占用": 2,
        "车辆压线": 3
    }
    attr_parking_slot_type = tools.get_value_from_kw_attrs(label['label']['attrs'], 'category5')
    attr_occupy = tools.get_value_from_kw_attrs(label['label']['attrs'], 'attribute_value')
    if label['label']['widget']['name'] == 'line3d':
        point = np.array(label['label']['widget']['data']).reshape(-1, 3).tolist()
    else:
        raise ValueError(f'Unsupport widget: {label["label"]["widget"]["name"]}')
    obj = {
        "parking_line": point,
        "attr_occupy": occupy_mapping[attr_occupy],
        "attr_parking_slot_type": parking_slot_type_mapping[attr_parking_slot_type]
    }
    return obj


def run(input_dir, output_dir):
    for clip_dir in tools.listdir(os.path.join(input_dir, 'annos'), full_path=True, sort=True):
        first_json_file = os.path.join(tools.listdir(clip_dir, full_path=True, sort=True)[0], 'annos.json')
        json_data = tools.get_json_data(first_json_file)
        groundline_res = []
        groundmarking_res = []
        arrow_mark_res = []
        land_mark_res = []
        lane_res = []
        parking_slot_res = []
        rawdata_annos = json_data['element_annos'][0]['rawdata_annos']
        for rawdata_anno in rawdata_annos:
            if not rawdata_anno['name'].endswith('lidar.pcd'):
                continue
            offset = 0
            groups_offset = dict()
            offset_mapping = dict()
            for label in rawdata_anno['objects']:
                if label['label']['name'] == 'group':
                    for part in label['compound']['parts']:
                        groups_offset[part] = offset
                    offset += 1
            idx = 0
            for label in rawdata_anno['objects']:
                idx += 1
                obj_id = str(idx)
                if label['label']['name'] == 'groundline':
                    obj = process_groundline(label, groups_offset, offset_mapping, obj_id)
                    groundline_res.append(obj)
                elif label['label']['name'] == 'groundmarking':
                    obj = process_groundmarking(label, groups_offset, offset_mapping, obj_id)
                    groundmarking_res.append(obj)
                elif label['label']['name'] == 'arrow_mark':
                    obj = process_arrow_mark(label)
                    arrow_mark_res.append(obj)
                elif label['label']['name'] == 'land_mark':
                    obj = process_land_mark(label)
                    land_mark_res.append(obj)
                elif label['label']['name'] == 'lane':
                    obj = process_lane(label)
                    lane_res.append(obj)
                elif label['label']['name'] == 'parking_slot':
                    obj = process_parking_slot(label)
                    parking_slot_res.append(obj)
        result = {
            "data_folder": f"{os.path.basename(clip_dir)}/{os.path.basename(os.path.dirname(first_json_file))}",
            "timestamp": os.path.basename(os.path.dirname(first_json_file)),
            "groundline": {
                "state": "exist" if len(groundline_res) > 0 else "non-exist",
                "anno": groundline_res
            },
            "groundmarking": {
                "state": "exist" if len(groundmarking_res) > 0 else "non-exist",
                "anno": groundmarking_res
            },
            "arrow_mark": {
                "state": "exist" if len(arrow_mark_res) > 0 else "non-exist",
                "anno": arrow_mark_res
            },
            "land_mark": {
                "state": "exist" if len(land_mark_res) > 0 else "non-exist",
                "anno": land_mark_res
            },
            "lane": {
                "state": "exist" if len(lane_res) > 0 else "non-exist",
                "anno": lane_res
            },
            "parking_slot": {
                "state": "exist" if len(parking_slot_res) > 0 else "non-exist",
                "anno": parking_slot_res
            }
        }
        tools.write_json_file(result, os.path.join(output_dir, f'{os.path.basename(os.path.dirname(first_json_file))}.json'), indent=4)


if __name__ == '__main__':
    args = tools.get_args()
    if args.input == "../../input/test":
        args.input = '/Users/<USER>/Downloads/TSA/tsa-4dbo-che-dian-yun-jie-di-xian-01_lot-chp6ykl8oez_order-chlgwddsqzb_ins-0_241114-1101'
        args.out = '/Users/<USER>/Downloads/TSA/tsa-4dbo-che-dian-yun-jie-di-xian-01_lot-chp6ykl8oez_order-chlgwddsqzb_ins-0_241114-1101_out'
    tools.check_dir(args.out)
    run(args.input, args.out)
