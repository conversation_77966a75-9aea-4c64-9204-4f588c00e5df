package interpolate

import (
	"gitlab.rp.konvery.work/platform/apis/anno/v1"

	go3dvec2 "github.com/ungerik/go3d/float64/vec2"
)

func init() {
	RegisterInterpolator(anno.WidgetName_box2d, commonInterpolate)
	RegisterInterpolator(anno.WidgetName_pscuboid, commonInterpolate)
	RegisterInterpolator(anno.WidgetName_point2d, commonInterpolate)
	RegisterInterpolator(anno.WidgetName_point3d, commonInterpolate)
	RegisterInterpolator(anno.WidgetName_line2d, commonInterpolate)
	RegisterInterpolator(anno.WidgetName_line3d, commonInterpolate)
	RegisterInterpolator(anno.WidgetName_poly2d, commonInterpolate)
	RegisterInterpolator(anno.WidgetName_poly3d, commonInterpolate)
}

// calLinearInterpolation calculates the linear interpolation between two slice.
func calLinearInterpolation(a, b []float64, percent float64) []float64 {
	res := make([]float64, len(a))
	for i := range a {
		d1 := a[i]
		d2 := b[i]
		res[i] = d1 + (d2-d1)*percent
	}
	return res
}

// commonInterpolate generates a new object using linear interpolation.
// span indicates the distance between o1 and o2.
// distance indicates the distance from o1 to the new object.
// if o2 is nil, a copy of o1 is returned.
func commonInterpolate(o1, o2 *anno.Object, span, distance float64) *anno.Object {
	o := cloneObject(o1)
	o.Source = anno.Object_Source_interpolation
	if o2 == nil {
		return o
	}

	o.Label.Widget.Data = calLinearInterpolation(o1.Label.Widget.Data, o2.Label.Widget.Data, distance/span)
	data := o.Label.Widget.Data

	// do interpolation for internal gaps
	switch o.Label.Widget.Name {
	case anno.WidgetName_box2d,
		anno.WidgetName_poly2d:
		for i, gap := range o.Label.Widget.Gaps {
			switch gap.Name {
			case anno.WidgetName_box2d,
				anno.WidgetName_poly2d:
				gap.Data = calLinearInterpolation(
					o1.Label.Widget.Gaps[i].Data,
					o2.Label.Widget.Gaps[i].Data,
					distance/span,
				)
			default:
				panic("unsupported gap widget: " + gap.Name.String())
			}
		}
	}

	// do interpolation for forward
	switch o.Label.Widget.Name {
	case anno.WidgetName_box2d:
		data1 := o1.Label.Widget.Data       // o1 data
		forward1 := o1.Label.Widget.Forward // o1 forward
		if len(forward1.Toward) == 0 {
			break
		}
		newDir := calDirection2D(
			forward1,
			Point(data1[:2]),
			[2]float64{data[0] - data1[0], data[1] - data1[1]},
			data[len(data)-1]-data1[len(data1)-1],
		)
		o.Label.Widget.Forward = newDir
	}

	return o
}

type Point = go3dvec2.T

// calDirection2D calculates a new direction for an interpolated 2D object.
func calDirection2D(dir *anno.Direction, center Point, offset [2]float64, rotation float64) *anno.Direction {
	origin := dir.Origin
	toward := dir.Toward
	rotateCenter := center
	var rotatedOrigin = Point{}
	if len(origin) != 0 {
		rotatedOrigin = rotateAroundCenter(Point(origin[:2]), rotateCenter, rotation)
	}
	rotatedToward := rotateAroundCenter(Point(toward[:2]), rotateCenter, rotation)

	newDir := &anno.Direction{}
	offsetP := Point(offset)
	if len(origin) != 0 {
		newDir.Origin = rotatedOrigin.Add(&offsetP)[:2]
	}
	newDir.Toward = rotatedToward.Add(&offsetP)[:2]

	return newDir
}

// rotateAroundCenter rotates a vector clockwise by radian.
func rotateAroundCenter(point, center Point, radian float64) Point {
	rotated := point.RotateAroundPoint(&center, -radian) // use negative radian
	return *rotated
}
