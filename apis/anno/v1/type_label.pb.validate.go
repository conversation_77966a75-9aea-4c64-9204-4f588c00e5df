// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/type_label.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Label with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Label) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Label with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in LabelMultiError, or nil if none found.
func (m *Label) ValidateAll() error {
	return m.validate(true)
}

func (m *Label) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for DisplayName

	// no validation rules for Desc

	// no validation rules for Avatar

	// no validation rules for Color

	if all {
		switch v := interface{}(m.GetAttrs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LabelValidationError{
					field:  "Attrs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LabelValidationError{
					field:  "Attrs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAttrs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LabelValidationError{
				field:  "Attrs",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetWidgetsV2() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LabelValidationError{
						field:  fmt.Sprintf("WidgetsV2[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LabelValidationError{
						field:  fmt.Sprintf("WidgetsV2[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LabelValidationError{
					field:  fmt.Sprintf("WidgetsV2[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCompound()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LabelValidationError{
					field:  "Compound",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LabelValidationError{
					field:  "Compound",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompound()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LabelValidationError{
				field:  "Compound",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Is_3DSegmentation

	// no validation rules for HasInstance

	if len(errors) > 0 {
		return LabelMultiError(errors)
	}

	return nil
}

// LabelMultiError is an error wrapping multiple validation errors returned by
// Label.ValidateAll() if the designated constraints aren't met.
type LabelMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LabelMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LabelMultiError) AllErrors() []error { return m }

// LabelValidationError is the validation error returned by Label.Validate if
// the designated constraints aren't met.
type LabelValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LabelValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LabelValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LabelValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LabelValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LabelValidationError) ErrorName() string { return "LabelValidationError" }

// Error satisfies the builtin error interface
func (e LabelValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLabel.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LabelValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LabelValidationError{}

// Validate checks the field values on AttrValue with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AttrValue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttrValue with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AttrValueMultiError, or nil
// if none found.
func (m *AttrValue) ValidateAll() error {
	return m.validate(true)
}

func (m *AttrValue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for DisplayName

	// no validation rules for Desc

	// no validation rules for Avatar

	// no validation rules for Value

	if len(errors) > 0 {
		return AttrValueMultiError(errors)
	}

	return nil
}

// AttrValueMultiError is an error wrapping multiple validation errors returned
// by AttrValue.ValidateAll() if the designated constraints aren't met.
type AttrValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttrValueMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttrValueMultiError) AllErrors() []error { return m }

// AttrValueValidationError is the validation error returned by
// AttrValue.Validate if the designated constraints aren't met.
type AttrValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttrValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttrValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttrValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttrValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttrValueValidationError) ErrorName() string { return "AttrValueValidationError" }

// Error satisfies the builtin error interface
func (e AttrValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttrValue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttrValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttrValueValidationError{}

// Validate checks the field values on Attr with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Attr) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Attr with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AttrMultiError, or nil if none found.
func (m *Attr) ValidateAll() error {
	return m.validate(true)
}

func (m *Attr) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for DisplayName

	// no validation rules for Desc

	// no validation rules for Avatar

	if _, ok := Attr_Type_Enum_name[int32(m.GetType())]; !ok {
		err := AttrValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := Attr_ValueType_Enum_name[int32(m.GetValueType())]; !ok {
		err := AttrValidationError{
			field:  "ValueType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetChoices() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AttrValidationError{
						field:  fmt.Sprintf("Choices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AttrValidationError{
						field:  fmt.Sprintf("Choices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AttrValidationError{
					field:  fmt.Sprintf("Choices[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AttrMultiError(errors)
	}

	return nil
}

// AttrMultiError is an error wrapping multiple validation errors returned by
// Attr.ValidateAll() if the designated constraints aren't met.
type AttrMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttrMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttrMultiError) AllErrors() []error { return m }

// AttrValidationError is the validation error returned by Attr.Validate if the
// designated constraints aren't met.
type AttrValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttrValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttrValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttrValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttrValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttrValidationError) ErrorName() string { return "AttrValidationError" }

// Error satisfies the builtin error interface
func (e AttrValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttr.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttrValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttrValidationError{}

// Validate checks the field values on AttrRefList with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AttrRefList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttrRefList with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AttrRefListMultiError, or
// nil if none found.
func (m *AttrRefList) ValidateAll() error {
	return m.validate(true)
}

func (m *AttrRefList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetOptionalV2() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AttrRefListValidationError{
						field:  fmt.Sprintf("OptionalV2[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AttrRefListValidationError{
						field:  fmt.Sprintf("OptionalV2[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AttrRefListValidationError{
					field:  fmt.Sprintf("OptionalV2[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetRequiredV2() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AttrRefListValidationError{
						field:  fmt.Sprintf("RequiredV2[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AttrRefListValidationError{
						field:  fmt.Sprintf("RequiredV2[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AttrRefListValidationError{
					field:  fmt.Sprintf("RequiredV2[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AttrRefListMultiError(errors)
	}

	return nil
}

// AttrRefListMultiError is an error wrapping multiple validation errors
// returned by AttrRefList.ValidateAll() if the designated constraints aren't met.
type AttrRefListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttrRefListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttrRefListMultiError) AllErrors() []error { return m }

// AttrRefListValidationError is the validation error returned by
// AttrRefList.Validate if the designated constraints aren't met.
type AttrRefListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttrRefListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttrRefListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttrRefListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttrRefListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttrRefListValidationError) ErrorName() string { return "AttrRefListValidationError" }

// Error satisfies the builtin error interface
func (e AttrRefListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttrRefList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttrRefListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttrRefListValidationError{}

// Validate checks the field values on Label_Widget with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Label_Widget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Label_Widget with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Label_WidgetMultiError, or
// nil if none found.
func (m *Label_Widget) ValidateAll() error {
	return m.validate(true)
}

func (m *Label_Widget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := WidgetName_Enum_name[int32(m.GetName())]; !ok {
		err := Label_WidgetValidationError{
			field:  "Name",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := Rawdata_Type_Enum_name[int32(m.GetRawdataType())]; !ok {
		err := Label_WidgetValidationError{
			field:  "RawdataType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetScale()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Label_WidgetValidationError{
					field:  "Scale",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Label_WidgetValidationError{
					field:  "Scale",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScale()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Label_WidgetValidationError{
				field:  "Scale",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := WidgetLineType_Enum_name[int32(m.GetLineType())]; !ok {
		err := Label_WidgetValidationError{
			field:  "LineType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return Label_WidgetMultiError(errors)
	}

	return nil
}

// Label_WidgetMultiError is an error wrapping multiple validation errors
// returned by Label_Widget.ValidateAll() if the designated constraints aren't met.
type Label_WidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Label_WidgetMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Label_WidgetMultiError) AllErrors() []error { return m }

// Label_WidgetValidationError is the validation error returned by
// Label_Widget.Validate if the designated constraints aren't met.
type Label_WidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Label_WidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Label_WidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Label_WidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Label_WidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Label_WidgetValidationError) ErrorName() string { return "Label_WidgetValidationError" }

// Error satisfies the builtin error interface
func (e Label_WidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLabel_Widget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Label_WidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Label_WidgetValidationError{}

// Validate checks the field values on Label_Compound with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Label_Compound) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Label_Compound with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Label_CompoundMultiError,
// or nil if none found.
func (m *Label_Compound) ValidateAll() error {
	return m.validate(true)
}

func (m *Label_Compound) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetParts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, Label_CompoundValidationError{
						field:  fmt.Sprintf("Parts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, Label_CompoundValidationError{
						field:  fmt.Sprintf("Parts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return Label_CompoundValidationError{
					field:  fmt.Sprintf("Parts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return Label_CompoundMultiError(errors)
	}

	return nil
}

// Label_CompoundMultiError is an error wrapping multiple validation errors
// returned by Label_Compound.ValidateAll() if the designated constraints
// aren't met.
type Label_CompoundMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Label_CompoundMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Label_CompoundMultiError) AllErrors() []error { return m }

// Label_CompoundValidationError is the validation error returned by
// Label_Compound.Validate if the designated constraints aren't met.
type Label_CompoundValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Label_CompoundValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Label_CompoundValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Label_CompoundValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Label_CompoundValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Label_CompoundValidationError) ErrorName() string { return "Label_CompoundValidationError" }

// Error satisfies the builtin error interface
func (e Label_CompoundValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLabel_Compound.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Label_CompoundValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Label_CompoundValidationError{}

// Validate checks the field values on Label_Compound_Part with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Label_Compound_Part) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Label_Compound_Part with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Label_Compound_PartMultiError, or nil if none found.
func (m *Label_Compound_Part) ValidateAll() error {
	return m.validate(true)
}

func (m *Label_Compound_Part) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Label

	if all {
		switch v := interface{}(m.GetOccurs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Label_Compound_PartValidationError{
					field:  "Occurs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Label_Compound_PartValidationError{
					field:  "Occurs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOccurs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Label_Compound_PartValidationError{
				field:  "Occurs",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return Label_Compound_PartMultiError(errors)
	}

	return nil
}

// Label_Compound_PartMultiError is an error wrapping multiple validation
// errors returned by Label_Compound_Part.ValidateAll() if the designated
// constraints aren't met.
type Label_Compound_PartMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Label_Compound_PartMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Label_Compound_PartMultiError) AllErrors() []error { return m }

// Label_Compound_PartValidationError is the validation error returned by
// Label_Compound_Part.Validate if the designated constraints aren't met.
type Label_Compound_PartValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Label_Compound_PartValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Label_Compound_PartValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Label_Compound_PartValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Label_Compound_PartValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Label_Compound_PartValidationError) ErrorName() string {
	return "Label_Compound_PartValidationError"
}

// Error satisfies the builtin error interface
func (e Label_Compound_PartValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLabel_Compound_Part.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Label_Compound_PartValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Label_Compound_PartValidationError{}

// Validate checks the field values on Attr_Type with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Attr_Type) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Attr_Type with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Attr_TypeMultiError, or nil
// if none found.
func (m *Attr_Type) ValidateAll() error {
	return m.validate(true)
}

func (m *Attr_Type) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Attr_TypeMultiError(errors)
	}

	return nil
}

// Attr_TypeMultiError is an error wrapping multiple validation errors returned
// by Attr_Type.ValidateAll() if the designated constraints aren't met.
type Attr_TypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Attr_TypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Attr_TypeMultiError) AllErrors() []error { return m }

// Attr_TypeValidationError is the validation error returned by
// Attr_Type.Validate if the designated constraints aren't met.
type Attr_TypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Attr_TypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Attr_TypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Attr_TypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Attr_TypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Attr_TypeValidationError) ErrorName() string { return "Attr_TypeValidationError" }

// Error satisfies the builtin error interface
func (e Attr_TypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttr_Type.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Attr_TypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Attr_TypeValidationError{}

// Validate checks the field values on Attr_ValueType with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Attr_ValueType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Attr_ValueType with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Attr_ValueTypeMultiError,
// or nil if none found.
func (m *Attr_ValueType) ValidateAll() error {
	return m.validate(true)
}

func (m *Attr_ValueType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Attr_ValueTypeMultiError(errors)
	}

	return nil
}

// Attr_ValueTypeMultiError is an error wrapping multiple validation errors
// returned by Attr_ValueType.ValidateAll() if the designated constraints
// aren't met.
type Attr_ValueTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Attr_ValueTypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Attr_ValueTypeMultiError) AllErrors() []error { return m }

// Attr_ValueTypeValidationError is the validation error returned by
// Attr_ValueType.Validate if the designated constraints aren't met.
type Attr_ValueTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Attr_ValueTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Attr_ValueTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Attr_ValueTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Attr_ValueTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Attr_ValueTypeValidationError) ErrorName() string { return "Attr_ValueTypeValidationError" }

// Error satisfies the builtin error interface
func (e Attr_ValueTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttr_ValueType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Attr_ValueTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Attr_ValueTypeValidationError{}

// Validate checks the field values on AttrRefList_Attr with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AttrRefList_Attr) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttrRefList_Attr with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AttrRefList_AttrMultiError, or nil if none found.
func (m *AttrRefList_Attr) ValidateAll() error {
	return m.validate(true)
}

func (m *AttrRefList_Attr) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Default

	if len(errors) > 0 {
		return AttrRefList_AttrMultiError(errors)
	}

	return nil
}

// AttrRefList_AttrMultiError is an error wrapping multiple validation errors
// returned by AttrRefList_Attr.ValidateAll() if the designated constraints
// aren't met.
type AttrRefList_AttrMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttrRefList_AttrMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttrRefList_AttrMultiError) AllErrors() []error { return m }

// AttrRefList_AttrValidationError is the validation error returned by
// AttrRefList_Attr.Validate if the designated constraints aren't met.
type AttrRefList_AttrValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttrRefList_AttrValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttrRefList_AttrValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttrRefList_AttrValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttrRefList_AttrValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttrRefList_AttrValidationError) ErrorName() string { return "AttrRefList_AttrValidationError" }

// Error satisfies the builtin error interface
func (e AttrRefList_AttrValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttrRefList_Attr.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttrRefList_AttrValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttrRefList_AttrValidationError{}
