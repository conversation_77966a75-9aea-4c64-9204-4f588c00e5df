// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: anyconn/v1/batch.proto

package anyconn

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Batches_CreateBatch_FullMethodName   = "/anyconn.v1.Batches/CreateBatch"
	Batches_UpdateBatch_FullMethodName   = "/anyconn.v1.Batches/UpdateBatch"
	Batches_CancelBatch_FullMethodName   = "/anyconn.v1.Batches/CancelBatch"
	Batches_DeleteBatch_FullMethodName   = "/anyconn.v1.Batches/DeleteBatch"
	Batches_GetBatch_FullMethodName      = "/anyconn.v1.Batches/GetBatch"
	Batches_ListBatch_FullMethodName     = "/anyconn.v1.Batches/ListBatch"
	Batches_SetAnnoResult_FullMethodName = "/anyconn.v1.Batches/SetAnnoResult"
	Batches_GetAnnoResult_FullMethodName = "/anyconn.v1.Batches/GetAnnoResult"
)

// BatchesClient is the client API for Batches service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BatchesClient interface {
	CreateBatch(ctx context.Context, in *CreateBatchRequest, opts ...grpc.CallOption) (*Batch, error)
	UpdateBatch(ctx context.Context, in *UpdateBatchRequest, opts ...grpc.CallOption) (*Batch, error)
	// only platform admin can cancel batches
	CancelBatch(ctx context.Context, in *GetBatchRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	DeleteBatch(ctx context.Context, in *DeleteBatchRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetBatch(ctx context.Context, in *GetBatchRequest, opts ...grpc.CallOption) (*Batch, error)
	ListBatch(ctx context.Context, in *ListBatchRequest, opts ...grpc.CallOption) (*ListBatchReply, error)
	SetAnnoResult(ctx context.Context, in *SetBatchAnnoResultRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetAnnoResult(ctx context.Context, in *GetBatchRequest, opts ...grpc.CallOption) (*GetBatchAnnoResultReply, error)
}

type batchesClient struct {
	cc grpc.ClientConnInterface
}

func NewBatchesClient(cc grpc.ClientConnInterface) BatchesClient {
	return &batchesClient{cc}
}

func (c *batchesClient) CreateBatch(ctx context.Context, in *CreateBatchRequest, opts ...grpc.CallOption) (*Batch, error) {
	out := new(Batch)
	err := c.cc.Invoke(ctx, Batches_CreateBatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *batchesClient) UpdateBatch(ctx context.Context, in *UpdateBatchRequest, opts ...grpc.CallOption) (*Batch, error) {
	out := new(Batch)
	err := c.cc.Invoke(ctx, Batches_UpdateBatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *batchesClient) CancelBatch(ctx context.Context, in *GetBatchRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Batches_CancelBatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *batchesClient) DeleteBatch(ctx context.Context, in *DeleteBatchRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Batches_DeleteBatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *batchesClient) GetBatch(ctx context.Context, in *GetBatchRequest, opts ...grpc.CallOption) (*Batch, error) {
	out := new(Batch)
	err := c.cc.Invoke(ctx, Batches_GetBatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *batchesClient) ListBatch(ctx context.Context, in *ListBatchRequest, opts ...grpc.CallOption) (*ListBatchReply, error) {
	out := new(ListBatchReply)
	err := c.cc.Invoke(ctx, Batches_ListBatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *batchesClient) SetAnnoResult(ctx context.Context, in *SetBatchAnnoResultRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Batches_SetAnnoResult_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *batchesClient) GetAnnoResult(ctx context.Context, in *GetBatchRequest, opts ...grpc.CallOption) (*GetBatchAnnoResultReply, error) {
	out := new(GetBatchAnnoResultReply)
	err := c.cc.Invoke(ctx, Batches_GetAnnoResult_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BatchesServer is the server API for Batches service.
// All implementations must embed UnimplementedBatchesServer
// for forward compatibility
type BatchesServer interface {
	CreateBatch(context.Context, *CreateBatchRequest) (*Batch, error)
	UpdateBatch(context.Context, *UpdateBatchRequest) (*Batch, error)
	// only platform admin can cancel batches
	CancelBatch(context.Context, *GetBatchRequest) (*emptypb.Empty, error)
	DeleteBatch(context.Context, *DeleteBatchRequest) (*emptypb.Empty, error)
	GetBatch(context.Context, *GetBatchRequest) (*Batch, error)
	ListBatch(context.Context, *ListBatchRequest) (*ListBatchReply, error)
	SetAnnoResult(context.Context, *SetBatchAnnoResultRequest) (*emptypb.Empty, error)
	GetAnnoResult(context.Context, *GetBatchRequest) (*GetBatchAnnoResultReply, error)
	mustEmbedUnimplementedBatchesServer()
}

// UnimplementedBatchesServer must be embedded to have forward compatible implementations.
type UnimplementedBatchesServer struct {
}

func (UnimplementedBatchesServer) CreateBatch(context.Context, *CreateBatchRequest) (*Batch, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBatch not implemented")
}
func (UnimplementedBatchesServer) UpdateBatch(context.Context, *UpdateBatchRequest) (*Batch, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBatch not implemented")
}
func (UnimplementedBatchesServer) CancelBatch(context.Context, *GetBatchRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelBatch not implemented")
}
func (UnimplementedBatchesServer) DeleteBatch(context.Context, *DeleteBatchRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBatch not implemented")
}
func (UnimplementedBatchesServer) GetBatch(context.Context, *GetBatchRequest) (*Batch, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBatch not implemented")
}
func (UnimplementedBatchesServer) ListBatch(context.Context, *ListBatchRequest) (*ListBatchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBatch not implemented")
}
func (UnimplementedBatchesServer) SetAnnoResult(context.Context, *SetBatchAnnoResultRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetAnnoResult not implemented")
}
func (UnimplementedBatchesServer) GetAnnoResult(context.Context, *GetBatchRequest) (*GetBatchAnnoResultReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAnnoResult not implemented")
}
func (UnimplementedBatchesServer) mustEmbedUnimplementedBatchesServer() {}

// UnsafeBatchesServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BatchesServer will
// result in compilation errors.
type UnsafeBatchesServer interface {
	mustEmbedUnimplementedBatchesServer()
}

func RegisterBatchesServer(s grpc.ServiceRegistrar, srv BatchesServer) {
	s.RegisterService(&Batches_ServiceDesc, srv)
}

func _Batches_CreateBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BatchesServer).CreateBatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Batches_CreateBatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BatchesServer).CreateBatch(ctx, req.(*CreateBatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Batches_UpdateBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BatchesServer).UpdateBatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Batches_UpdateBatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BatchesServer).UpdateBatch(ctx, req.(*UpdateBatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Batches_CancelBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BatchesServer).CancelBatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Batches_CancelBatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BatchesServer).CancelBatch(ctx, req.(*GetBatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Batches_DeleteBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BatchesServer).DeleteBatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Batches_DeleteBatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BatchesServer).DeleteBatch(ctx, req.(*DeleteBatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Batches_GetBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BatchesServer).GetBatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Batches_GetBatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BatchesServer).GetBatch(ctx, req.(*GetBatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Batches_ListBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BatchesServer).ListBatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Batches_ListBatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BatchesServer).ListBatch(ctx, req.(*ListBatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Batches_SetAnnoResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBatchAnnoResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BatchesServer).SetAnnoResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Batches_SetAnnoResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BatchesServer).SetAnnoResult(ctx, req.(*SetBatchAnnoResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Batches_GetAnnoResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BatchesServer).GetAnnoResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Batches_GetAnnoResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BatchesServer).GetAnnoResult(ctx, req.(*GetBatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Batches_ServiceDesc is the grpc.ServiceDesc for Batches service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Batches_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "anyconn.v1.Batches",
	HandlerType: (*BatchesServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateBatch",
			Handler:    _Batches_CreateBatch_Handler,
		},
		{
			MethodName: "UpdateBatch",
			Handler:    _Batches_UpdateBatch_Handler,
		},
		{
			MethodName: "CancelBatch",
			Handler:    _Batches_CancelBatch_Handler,
		},
		{
			MethodName: "DeleteBatch",
			Handler:    _Batches_DeleteBatch_Handler,
		},
		{
			MethodName: "GetBatch",
			Handler:    _Batches_GetBatch_Handler,
		},
		{
			MethodName: "ListBatch",
			Handler:    _Batches_ListBatch_Handler,
		},
		{
			MethodName: "SetAnnoResult",
			Handler:    _Batches_SetAnnoResult_Handler,
		},
		{
			MethodName: "GetAnnoResult",
			Handler:    _Batches_GetAnnoResult_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "anyconn/v1/batch.proto",
}
