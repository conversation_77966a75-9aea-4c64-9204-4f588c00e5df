# Default values for the service.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: artifactory.rp.konvery.work/docker/sansheng-annostat
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""
  svcCommand: ["./annostat", "-conf", "/data/conf"]
  migrateCommand: ["./annostat", "-conf", "/data/conf", "migrate", "autoup"]

imagePullSecrets: []
#  - name: "sansheng-artifactory-token"
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations:
    eks.amazonaws.com/role-arn: arn:aws-cn:iam::************:role/non-prod-workload-sansheng
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "sansheng-annostat"

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

secretDBName: "sansheng-role-secret"

service:
  type: ClusterIP
  httpPort: 8040
  grpcPort: 8041
  metrics:
    enabled: true
    port: 6060
    name: metrics
    annotations: {}

# -- Parameters for the Prometheus ServiceMonitor objects.
# Reference: https://docs.openshift.com/container-platform/4.6/rest_api/monitoring_apis/servicemonitor-monitoring-coreos-com-v1.html
serviceMonitor:
  # -- HTTP scheme to use for scraping.
  scheme: http
  # -- Interval at which metrics should be scraped
  scrapeInterval: 60s
  # -- Timeout after which the scrape is ended
  scrapeTimeout: 30s
  # -- Provide additionnal labels to the ServiceMonitor ressource metadata
  labels: {}
  # -- TLS configuration to use when scraping the endpoint
  tlsConfig: {}

hostName: annostat.np.konvery.work

# not needed any more because we are behind a API gateway
ingress:
  enabled: false
  className: "ingress-nginx-internal"
  annotations:
    # nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    external-dns-annotation: ingress-nginx-internal
    nginx.ingress.kubernetes.io/proxy-body-size: 1024m
    nginx.ingress.kubernetes.io/enable-cors: "true"
  hosts:
    # todo: should change to the actual host when make deployment
    - host: annostat.np.konvery.work
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls:
    - secretName: wildcard-np-konvery-work
      hosts:
        - annostat.np.konvery.work

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 500m
    memory: 1Gi
  requests:
    cpu: 100m
    memory: 256Mi

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 100
  targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

database:
  DATABASE: sansheng-annostat

envVars:
  AWS_DEFAULT_REGION: cn-northwest-1
  annofeed.addr: annofeed:8021
  usercenter.addr: usercenter:8001
  otel.tracing.endpoint: #jaeger:4317
  otel.metrics.serve_addr: :6060
  otel.log.format: json
