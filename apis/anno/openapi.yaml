# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
security:
    - jwt: []
paths:
    /anno/v1/bizgrants:
        get:
            tags:
                - Bizgrants
            operationId: Bizgrants_ListBizgrant
            parameters:
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: |-
                    An opaque pagination token returned from a previous call.
                     An empty token denotes the first page.
                  schema:
                    type: string
                - name: filter.grantor_uid
                  in: query
                  description: grantor uid
                  schema:
                    type: string
                - name: filter.grantee_uid
                  in: query
                  description: grantee uid
                  schema:
                    type: string
                - name: filter.org_uid
                  in: query
                  description: uid of the organization whose business permissions are granted
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ListBizgrantReply'
        post:
            tags:
                - Bizgrants
            operationId: Bizgrants_CreateBizgrant
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.CreateBizgrantRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Bizgrant'
        delete:
            tags:
                - Bizgrants
            operationId: Bizgrants_DeleteBizgrant
            parameters:
                - name: filter.grantor_uid
                  in: query
                  description: grantor uid
                  schema:
                    type: string
                - name: filter.grantee_uid
                  in: query
                  description: grantee uid
                  schema:
                    type: string
                - name: filter.org_uid
                  in: query
                  description: uid of the organization whose business permissions are granted
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/commentreasons:
        get:
            tags:
                - Configs
            description: List comment reasons
            operationId: Configs_ListCommentReasons
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ListCommentReasonsReply'
        put:
            tags:
                - Configs
            description: Modify comment reasons
            operationId: Configs_PutCommentReasons
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.PutCommentReasonsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/dummy:
        get:
            tags:
                - Dummy
            operationId: Dummy_Dummy
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.DummyReply'
    /anno/v1/errors:
        get:
            tags:
                - Configs
            description: List Errors info
            operationId: Configs_ListErrors
            security: []
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Errors'
    /anno/v1/job-count-by-lots:
        get:
            tags:
                - Lots
            operationId: Lots_JobCountByLots
            parameters:
                - name: ids
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.JobCountByLotidsReply'
    /anno/v1/jobs:
        get:
            tags:
                - Jobs
            operationId: Jobs_ListJob
            parameters:
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: filter.uids
                  in: query
                  description: filter by job uids; max length is 100
                  schema:
                    type: array
                    items:
                        type: string
                - name: filter.lot_uid
                  in: query
                  description: filter by belonging lot
                  schema:
                    type: string
                - name: filter.phase
                  in: query
                  description: filter by jobs current phase; starts from 1
                  schema:
                    type: integer
                    format: int32
                - name: filter.state
                  in: query
                  description: filter by jobs current state
                  schema:
                    enum:
                        - unspecified
                        - unstart
                        - doing
                        - finished
                    type: string
                    format: enum
                - name: filter.last_executors
                  in: query
                  description: query jobs by their last executors; max length is 100
                  schema:
                    type: array
                    items:
                        type: string
                - name: filter.last_execteam
                  in: query
                  description: query jobs by the last executor's belonging team
                  schema:
                    type: string
                - name: filter.phases
                  in: query
                  schema:
                    type: array
                    items:
                        type: integer
                        format: int32
                - name: filter.jobclip
                  in: query
                  schema:
                    type: string
                - name: show_last_executor
                  in: query
                  description: if to return jobs' last_executor and last_execteam in the response
                  schema:
                    type: boolean
                - name: full_job
                  in: query
                  description: '[PRIVILEGED] show full job info, including annotations, comments, etc.'
                  schema:
                    type: boolean
                - name: show_executors
                  in: query
                  description: whether to return jobs' executor in each phase
                  schema:
                    type: boolean
                - name: elem_name_pattern
                  in: query
                  description: query jobs by their containing elements' name pattern
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ListJobReply'
    /anno/v1/jobs/-/holding:
        get:
            tags:
                - Jobs
            description: check if there is any holding jobs for users/orgs
            operationId: Jobs_HasHoldingJobs
            parameters:
                - name: org_uid
                  in: query
                  description: if available, only query by org_uid; otherwise, query by user_uids
                  schema:
                    type: string
                - name: user_uids
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.HasHoldingJobsReply'
    /anno/v1/jobs/claim:
        post:
            tags:
                - Jobs
            description: Claim a job
            operationId: Jobs_ClaimJob
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.ClaimJobRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ClaimJobResponse'
    /anno/v1/jobs/revert:
        post:
            tags:
                - Jobs
            description: Revert multiple jobs to a previous phase.
            operationId: Jobs_BatchRevertJob
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.BatchRevertJobRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.BatchRevertJobReply'
    /anno/v1/jobs/{uid}:
        get:
            tags:
                - Jobs
            operationId: Jobs_GetJob
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: expand
                  in: query
                  description: '[PRIVILEGED] if true, the job data (e.g. elements, annotations) will be expanded'
                  schema:
                    type: boolean
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Job'
    /anno/v1/jobs/{uid}/assign:
        post:
            tags:
                - Jobs
            description: Assign a job
            operationId: Jobs_AssignJob
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.AssignJobRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/jobs/{uid}/draft:
        get:
            tags:
                - Jobs
            description: Get draft annos/comments saved in the server.
            operationId: Jobs_GetJobDraft
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.GetJobDraftReply'
        put:
            tags:
                - Jobs
            description: Save draft annos/comments in the server.
            operationId: Jobs_SaveJobDraft
            parameters:
                - name: uid
                  in: path
                  description: job uid
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.SaveJobDraftRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.SaveJobDraftReply'
    /anno/v1/jobs/{uid}/elems/{elem_idx}/rawdatas/{rawdata_idx}/embedding:
        put:
            tags:
                - Jobs
            description: Set rawdata embedding.
            operationId: Jobs_SetRawdataEmbedding
            parameters:
                - name: uid
                  in: path
                  description: job UID
                  required: true
                  schema:
                    type: string
                - name: elem_idx
                  in: path
                  description: element index within job
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: rawdata_idx
                  in: path
                  description: rawdata index within element
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.SetRawdataEmbeddingRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/jobs/{uid}/get-skip-annotation:
        get:
            tags:
                - Jobs
            operationId: Jobs_GetSkipAnnotation
            parameters:
                - name: uid
                  in: path
                  description: if available, only query by org_uid; otherwise, query by user_uids
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.GetSkipAnnotationReply'
    /anno/v1/jobs/{uid}/giveup:
        post:
            tags:
                - Jobs
            description: giveup a job
            operationId: Jobs_GiveupJob
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.GiveupJobRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/jobs/{uid}/log:
        get:
            tags:
                - Jobs
            operationId: Jobs_GetJoblog
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.GetJoblogReply'
    /anno/v1/jobs/{uid}/phases/{phase}/last-commit-log:
        get:
            tags:
                - Jobs
            description: get the last commit joblog of a phase
            operationId: Jobs_GetJobLastCommitLog
            parameters:
                - name: uid
                  in: path
                  description: job uid
                  required: true
                  schema:
                    type: string
                - name: phase
                  in: path
                  description: 'phase number: 0 means all the phases'
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: direction
                  in: query
                  description: commit direction
                  schema:
                    enum:
                        - unspecified
                        - up
                        - down
                    type: string
                    format: enum
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.GetJobLastCommitLogReply'
    /anno/v1/jobs/{uid}/rawlog:
        get:
            tags:
                - Jobs
            operationId: Jobs_GetRawJoblog
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.GetRawJoblogReply'
    /anno/v1/jobs/{uid}/review:
        post:
            tags:
                - Jobs
            description: Review job annotations; used in 2nd phase onwards.
            operationId: Jobs_ReviewJob
            parameters:
                - name: uid
                  in: path
                  description: job uid
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.ReviewJobRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/jobs/{uid}/skip-annotation:
        post:
            tags:
                - Jobs
            operationId: Jobs_SkipAnnotation
            parameters:
                - name: uid
                  in: path
                  description: if available, only query by org_uid; otherwise, query by user_uids
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.SkipAnnotationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.SkipAnnotationReply'
    /anno/v1/jobs/{uid}/submit:
        post:
            tags:
                - Jobs
            description: Submit job annotations; used in the 1st phase only.
            operationId: Jobs_SubmitJob
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.SubmitJobRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/labelcls:
        get:
            tags:
                - Labelclz
            operationId: Labelclz_ListLabelcls
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ListLabelclsReply'
        post:
            tags:
                - Labelclz
            operationId: Labelclz_CreateLabelcls
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.Labelcls'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Labelcls'
    /anno/v1/labelcls/{name}:
        get:
            tags:
                - Labelclz
            operationId: Labelclz_GetLabelcls
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Labelcls'
        put:
            tags:
                - Labelclz
            operationId: Labelclz_UpdateLabelcls
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.Labelcls'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Labelcls'
        delete:
            tags:
                - Labelclz
            operationId: Labelclz_DeleteLabelcls
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/labelwidgets:
        get:
            tags:
                - Labelwidgets
            operationId: Labelwidgets_ListLabelwidget
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ListLabelwidgetReply'
    /anno/v1/lots:
        get:
            tags:
                - Lots
            operationId: Lots_ListLot
            parameters:
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: org_uid
                  in: query
                  description: filter by orgnization
                  schema:
                    type: string
                - name: creator_uid
                  in: query
                  description: filter by creator
                  schema:
                    type: string
                - name: name_pattern
                  in: query
                  description: filter by name pattern
                  schema:
                    type: string
                - name: states
                  in: query
                  description: filter by lot state
                  schema:
                    type: array
                    items:
                        enum:
                            - unspecified
                            - initializing
                            - unstart
                            - ongoing
                            - finished
                            - paused
                            - canceled
                        type: string
                        format: enum
                - name: type
                  in: query
                  description: filter by lot type
                  schema:
                    enum:
                        - unspecified
                        - annotate
                        - segment_instance
                        - segment_semantic
                        - segment_panoptic
                    type: string
                    format: enum
                - name: order_uid
                  in: query
                  description: filter by order
                  schema:
                    type: string
                - name: tags
                  in: query
                  description: find by attached tags
                  schema:
                    type: array
                    items:
                        type: string
                - name: with_org
                  in: query
                  description: include lot's orgnization in the reply
                  schema:
                    type: boolean
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ListLotReply'
        post:
            tags:
                - Lots
            operationId: Lots_CreateLot
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.CreateLotRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Lot'
    /anno/v1/lots/by-executor:
        get:
            tags:
                - Lots
            description: |-
                get lots assigned to a user or a team.
                 if the phase's execteam is empty, the phase is not assigned to the user or team.
            operationId: Lots_ListLotsByExecutor
            parameters:
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: user_uid
                  in: query
                  description: |-
                    list lots assigned to the user;
                     requestor should have manager role in the user's organization, if it is not oneself.
                     if omitted, default to the requestor.
                  schema:
                    type: string
                - name: team_uid
                  in: query
                  description: |-
                    list lots assigned to the team;
                     requestor should have manager role in this team.
                  schema:
                    type: string
                - name: org_uid
                  in: query
                  description: |-
                    list lots assigned to teams within this organization;
                     requestor should have IamGroup.list permission in this organization, e.g. a manager,.
                  schema:
                    type: string
                - name: states
                  in: query
                  description: filter by lot state
                  schema:
                    type: array
                    items:
                        enum:
                            - unspecified
                            - initializing
                            - unstart
                            - ongoing
                            - finished
                            - paused
                            - canceled
                        type: string
                        format: enum
                - name: type
                  in: query
                  description: filter by lot type
                  schema:
                    enum:
                        - unspecified
                        - annotate
                        - segment_instance
                        - segment_semantic
                        - segment_panoptic
                    type: string
                    format: enum
                - name: name_pattern
                  in: query
                  description: filter by name pattern
                  schema:
                    type: string
                - name: claimable
                  in: query
                  description: if to skip lots that a claim request will return no job for an executor; available only to executor query.
                  schema:
                    type: boolean
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ListLotsByExecutorReply'
    /anno/v1/lots/{lot.uid}:
        patch:
            tags:
                - Lots
            description: update lot. only simple information like name or desc update are allowed.
            operationId: Lots_UpdateLot
            parameters:
                - name: lot.uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: fields
                  in: query
                  description: name of fields to be updated
                  schema:
                    type: array
                    items:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.CreateLotRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Lot'
    /anno/v1/lots/{uid}:
        get:
            tags:
                - Lots
            operationId: Lots_GetLot
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Lot'
        delete:
            tags:
                - Lots
            operationId: Lots_DeleteLot
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/lots/{uid}/allow-download-annos:
        put:
            tags:
                - Lots
            description: allows demander to download annos
            operationId: Lots_AllowDownloadAnnos
            parameters:
                - name: uid
                  in: path
                  description: lot UID
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.AllowDownloadAnnosRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/lots/{uid}/cancel:
        put:
            tags:
                - Lots
            operationId: Lots_CancelLot
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.GetLotRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/lots/{uid}/clone:
        post:
            tags:
                - Lots
            description: create a new lot by cloning an existing lot.
            operationId: Lots_CloneLot
            parameters:
                - name: uid
                  in: path
                  description: source lot uid
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.CloneLotRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Lot'
    /anno/v1/lots/{uid}/execteam:
        put:
            tags:
                - Lots
            description: assign execution team, update or delete an execution team
            operationId: Lots_AssignExecteam
            parameters:
                - name: uid
                  in: path
                  description: lot uid
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.AssignExecteamRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/lots/{uid}/execteams:
        get:
            tags:
                - Lots
            description: list assigned execution teams and executors
            operationId: Lots_ListExecteams
            parameters:
                - name: uid
                  in: path
                  description: lot uid
                  required: true
                  schema:
                    type: string
                - name: with_execteams
                  in: query
                  description: if to include team info in the reply
                  schema:
                    type: boolean
                - name: with_executors
                  in: query
                  description: if to include executors in the reply
                  schema:
                    type: boolean
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ListExecteamsReply'
    /anno/v1/lots/{uid}/executors:
        get:
            tags:
                - Lots
            description: list executors at a phase
            operationId: Lots_ListExecutors
            parameters:
                - name: uid
                  in: path
                  description: lot uid
                  required: true
                  schema:
                    type: string
                - name: phase
                  in: query
                  description: phase number, starts from 1
                  schema:
                    type: integer
                    format: int32
                - name: subtype
                  in: query
                  schema:
                    type: string
                - name: team_uid
                  in: query
                  description: mandatory in a multi-team configuration
                  schema:
                    type: string
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ListExecutorsReply'
        post:
            tags:
                - Lots
            description: add or remove executors
            operationId: Lots_ManageExecutors
            parameters:
                - name: uid
                  in: path
                  description: lot uid
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.ManageExecutorsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/lots/{uid}/export-annos:
        put:
            tags:
                - Lots
            description: ExportLotAnnos exports lot annotations
            operationId: Lots_ExportLotAnnos
            parameters:
                - name: uid
                  in: path
                  description: lot UID
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.ExportLotAnnosRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/lots/{uid}/pause:
        put:
            tags:
                - Lots
            operationId: Lots_PauseLot
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.GetLotRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/lots/{uid}/start:
        put:
            tags:
                - Lots
            operationId: Lots_StartLot
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.GetLotRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/lots/{uid}/summary:
        get:
            tags:
                - Lots
            description: Get summary of a lot
            operationId: Lots_GetSummary
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.GetLotSummaryReply'
    /anno/v1/lots/{uid}/tag:
        put:
            tags:
                - Lots
            operationId: Lots_AddTag
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            type: string
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/types.TagList'
        delete:
            tags:
                - Lots
            operationId: Lots_DeleteTag
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: tags
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/types.TagList'
    /anno/v1/lottpls:
        get:
            tags:
                - Lottpls
            operationId: Lottpls_ListLottpl
            parameters:
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: org_uid
                  in: query
                  description: filter by organization
                  schema:
                    type: string
                - name: creator_uid
                  in: query
                  description: filter by creator
                  schema:
                    type: string
                - name: name_pattern
                  in: query
                  description: filter by name pattern
                  schema:
                    type: string
                - name: type
                  in: query
                  description: filter by lot type
                  schema:
                    enum:
                        - unspecified
                        - annotate
                        - segment_instance
                        - segment_semantic
                        - segment_panoptic
                    type: string
                    format: enum
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ListLottplReply'
        post:
            tags:
                - Lottpls
            operationId: Lottpls_CreateLottpl
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.CreateLottplRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Lottpl'
    /anno/v1/lottpls/{lottpl.uid}:
        patch:
            tags:
                - Lottpls
            operationId: Lottpls_UpdateLottpl
            parameters:
                - name: lottpl.uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: fields
                  in: query
                  description: name of fields to be updated
                  schema:
                    type: array
                    items:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.CreateLottplRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Lottpl'
    /anno/v1/lottpls/{uid}:
        get:
            tags:
                - Lottpls
            operationId: Lottpls_GetLottpl
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Lottpl'
        delete:
            tags:
                - Lottpls
            operationId: Lottpls_DeleteLottpl
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/orders:
        get:
            tags:
                - Orders
            operationId: Orders_ListOrder
            parameters:
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: org_uid
                  in: query
                  description: filter by orgnization
                  schema:
                    type: string
                - name: creator_uid
                  in: query
                  description: filter by creator
                  schema:
                    type: string
                - name: name_pattern
                  in: query
                  description: filter by name pattern
                  schema:
                    type: string
                - name: states
                  in: query
                  description: filter by order state
                  schema:
                    type: array
                    items:
                        enum:
                            - unspecified
                            - initializing
                            - waiting
                            - ongoing
                            - finished
                            - canceled
                            - failed
                        type: string
                        format: enum
                - name: with_org
                  in: query
                  description: include order's organization in the reply
                  schema:
                    type: boolean
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ListOrderReply'
        post:
            tags:
                - Orders
            operationId: Orders_CreateOrder
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.CreateOrderRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Order'
    /anno/v1/orders/{order.uid}:
        patch:
            tags:
                - Orders
            operationId: Orders_UpdateOrder
            parameters:
                - name: order.uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: fields
                  in: query
                  description: name of fileds to be updated
                  schema:
                    type: array
                    items:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.Order'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Order'
    /anno/v1/orders/{uid}:
        get:
            tags:
                - Orders
            operationId: Orders_GetOrder
            parameters:
                - name: uid
                  in: path
                  description: order UID
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Order'
        delete:
            tags:
                - Orders
            operationId: Orders_DeleteOrder
            parameters:
                - name: uid
                  in: path
                  description: order UID
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/orders/{uid}/anno-result:
        get:
            tags:
                - Orders
            operationId: Orders_GetAnnoResult
            parameters:
                - name: uid
                  in: path
                  description: order UID
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.GetOrderAnnoResultReply'
        put:
            tags:
                - Orders
            operationId: Orders_SetAnnoResult
            parameters:
                - name: uid
                  in: path
                  description: order UID
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.SetOrderAnnoResultRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/orders/{uid}/cancel:
        put:
            tags:
                - Orders
            description: only platform admin can cancel orders
            operationId: Orders_CancelOrder
            parameters:
                - name: uid
                  in: path
                  description: order UID
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.GetOrderRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/orders/{uid}/export-annos:
        put:
            tags:
                - Orders
            operationId: Orders_ExportOrderAnnos
            parameters:
                - name: uid
                  in: path
                  description: order UID
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.ExportOrderAnnosRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/projects:
        get:
            tags:
                - Projects
            operationId: Projects_ListProject
            parameters:
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: org_uid
                  in: query
                  description: filter by orgnization
                  schema:
                    type: string
                - name: creator_uid
                  in: query
                  description: filter by creator
                  schema:
                    type: string
                - name: name_pattern
                  in: query
                  description: filter by name pattern
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ListProjectReply'
        post:
            tags:
                - Projects
            operationId: Projects_CreateProject
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.CreateProjectRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Project'
    /anno/v1/projects/{project.uid}:
        patch:
            tags:
                - Projects
            operationId: Projects_UpdateProject
            parameters:
                - name: project.uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: fields
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.CreateProjectRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Project'
    /anno/v1/projects/{uid}:
        get:
            tags:
                - Projects
            operationId: Projects_GetProject
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Project'
        delete:
            tags:
                - Projects
            operationId: Projects_DeleteProject
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/roles:
        get:
            tags:
                - Roles
            operationId: Roles_ListRole
            parameters:
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: name_pattern
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ListRoleReply'
        post:
            tags:
                - Roles
            operationId: Roles_CreateRole
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.Role'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Role'
    /anno/v1/roles/users:
        get:
            tags:
                - Roles
            operationId: Roles_ListUsersRole
            parameters:
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: team_uid
                  in: query
                  description: Scope scope = 3;
                  schema:
                    type: string
                - name: user_uids
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
                - name: role
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ListUsersRoleReply'
        put:
            tags:
                - Roles
            operationId: Roles_SetUsersRole
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.SetUsersRoleRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
        delete:
            tags:
                - Roles
            operationId: Roles_DeleteUsersRole
            parameters:
                - name: scope.type
                  in: query
                  description: global/project/lot
                  schema:
                    type: string
                - name: scope.uid
                  in: query
                  description: project-uid/lot-uid
                  schema:
                    type: string
                - name: user_ids
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/roles/users/{uid}:
        get:
            tags:
                - Roles
            operationId: Roles_GetUserRole
            parameters:
                - name: uid
                  in: path
                  description: user uid
                  required: true
                  schema:
                    type: string
                - name: scope.type
                  in: query
                  description: global/project/lot
                  schema:
                    type: string
                - name: scope.uid
                  in: query
                  description: project-uid/lot-uid
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.GetUserRoleReply'
    /anno/v1/roles/{name}:
        get:
            tags:
                - Roles
            operationId: Roles_GetRole
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Role'
        delete:
            tags:
                - Roles
            operationId: Roles_DeleteRole
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/roles/{role.name}:
        patch:
            tags:
                - Roles
            operationId: Roles_UpdateRole
            parameters:
                - name: role.name
                  in: path
                  required: true
                  schema:
                    type: string
                - name: fields
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.Role'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Role'
    /anno/v1/skills:
        get:
            tags:
                - Skills
            operationId: Skills_ListSkill
            parameters:
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: type
                  in: query
                  schema:
                    enum:
                        - unspecified
                        - task_type
                        - label_class
                        - widget
                    type: string
                    format: enum
                - name: name_pattern
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ListSkillReply'
        post:
            tags:
                - Skills
            operationId: Skills_CreateSkill
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.Skill'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Skill'
    /anno/v1/skills/teams/{uid}:
        post:
            tags:
                - Skills
            description: add skills to users in a team
            operationId: Skills_AddTeamSkill
            parameters:
                - name: uid
                  in: path
                  description: team uid
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.AddTeamSkillRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
        delete:
            tags:
                - Skills
            description: delete skills from users in a team
            operationId: Skills_DeleteTeamSkill
            parameters:
                - name: uid
                  in: path
                  description: team uid
                  required: true
                  schema:
                    type: string
                - name: scope.type
                  in: query
                  description: global/project/lot
                  schema:
                    type: string
                - name: scope.uid
                  in: query
                  description: project-uid/lot-uid
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/skills/users:
        get:
            tags:
                - Skills
            operationId: Skills_ListUsersSkill
            parameters:
                - name: scope.type
                  in: query
                  description: global/project/lot
                  schema:
                    type: string
                - name: scope.uid
                  in: query
                  description: project-uid/lot-uid
                  schema:
                    type: string
                - name: team_uid
                  in: query
                  schema:
                    type: string
                - name: user_uids
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ListUsersSkillReply'
        post:
            tags:
                - Skills
            operationId: Skills_AddUsersSkill
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.AddUsersSkillRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
        delete:
            tags:
                - Skills
            operationId: Skills_DeleteUsersSkill
            parameters:
                - name: scope.type
                  in: query
                  description: global/project/lot
                  schema:
                    type: string
                - name: scope.uid
                  in: query
                  description: project-uid/lot-uid
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/skills/users/{uid}:
        get:
            tags:
                - Skills
            description: users skills
            operationId: Skills_GetUserSkill
            parameters:
                - name: uid
                  in: path
                  description: user uid
                  required: true
                  schema:
                    type: string
                - name: scope.type
                  in: query
                  description: global/project/lot
                  schema:
                    type: string
                - name: scope.uid
                  in: query
                  description: project-uid/lot-uid
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.GetUserSkillReply'
    /anno/v1/skills/{name}:
        get:
            tags:
                - Skills
            operationId: Skills_GetSkill
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Skill'
        delete:
            tags:
                - Skills
            operationId: Skills_DeleteSkill
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/skills/{skill.name}:
        patch:
            tags:
                - Skills
            operationId: Skills_UpdateSkill
            parameters:
                - name: skill.name
                  in: path
                  required: true
                  schema:
                    type: string
                - name: fields
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.Skill'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Skill'
    /anno/v1/specgrants:
        get:
            tags:
                - Specgrants
            operationId: Specgrants_ListSpecgrant
            parameters:
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: |-
                    An opaque pagination token returned from a previous call.
                     An empty token denotes the first page.
                  schema:
                    type: string
                - name: filter.grantor_uid
                  in: query
                  description: grantor uid
                  schema:
                    type: string
                - name: filter.grantee_uid
                  in: query
                  description: grantee uid
                  schema:
                    type: string
                - name: filter.org_uid
                  in: query
                  description: item owner orgnization uid
                  schema:
                    type: string
                - name: filter.item_type
                  in: query
                  description: item type
                  schema:
                    enum:
                        - unspecified
                        - AnnoLot
                    type: string
                    format: enum
                - name: filter.item_uids
                  in: query
                  description: item uid list
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.ListSpecgrantReply'
        post:
            tags:
                - Specgrants
            operationId: Specgrants_CreateSpecgrant
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/anno.v1.CreateSpecgrantRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.Specgrant'
        delete:
            tags:
                - Specgrants
            operationId: Specgrants_DeleteSpecgrant
            parameters:
                - name: filter.grantor_uid
                  in: query
                  description: grantor uid
                  schema:
                    type: string
                - name: filter.grantee_uid
                  in: query
                  description: grantee uid
                  schema:
                    type: string
                - name: filter.org_uid
                  in: query
                  description: item owner orgnization uid
                  schema:
                    type: string
                - name: filter.item_type
                  in: query
                  description: item type
                  schema:
                    enum:
                        - unspecified
                        - AnnoLot
                    type: string
                    format: enum
                - name: filter.item_uids
                  in: query
                  description: item uid list
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /anno/v1/version:
        get:
            tags:
                - Configs
            operationId: Configs_GetVersion
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/anno.v1.GetVersionReply'
components:
    securitySchemes:
        jwt:
            type: http
            scheme: bearer
            bearerFormat: JWT
    schemas:
        anno.v1.AddTeamSkillRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: team uid
                scope:
                    $ref: '#/components/schemas/anno.v1.Scope'
                skills:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Skill'
        anno.v1.AddUsersSkillRequest:
            type: object
            properties:
                scope:
                    $ref: '#/components/schemas/anno.v1.Scope'
                skills:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.UserSkill'
        anno.v1.AllowDownloadAnnosRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: lot UID
                allow:
                    type: boolean
                    description: 'whether to allow demanders to download order annos: if false, means we have allowed it before but we want to disallow it now'
        anno.v1.AnnoComment:
            required:
                - uuid
                - elem_idx
                - obj_uuids
                - reasons
                - commenter
                - add_phase
                - resolve_phase
                - created_at
            type: object
            properties:
                elem_idx:
                    type: integer
                    description: element index within the job
                    format: int32
                content:
                    type: string
                    description: details or additional information
                commenter:
                    allOf:
                        - $ref: '#/components/schemas/iam.v1.BaseUser'
                    description: |-
                        the user who added the comment;
                         the field is ignored in review requests
                add_phase:
                    type: integer
                    description: |-
                        number of the phase in which the comment is added;
                         the field is ignored in review requests
                    format: int32
                resolve_phase:
                    type: integer
                    description: |-
                        number of the highest phase in which the comment is marked as resolved
                         in frontend, only executors in phases within (resolve_phase, add_phase] can see the comment and react to it;
                         the field is ignored in review requests
                    format: int32
                rd_idx:
                    type: integer
                    description: rawdata index within the element; should not be omitted if the comment is missed（漏标）
                    format: int32
                extra_info:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.AnnoComment_ExtraInfo'
                    description: extra info associated with the comment
                uuid:
                    type: string
                    description: UUID of the comment; it is obj_uuid when omitted. Must not be empty if reason is missed（漏标）.
                obj_uuids:
                    type: array
                    items:
                        type: string
                    description: UUID of the associated objects; it may be empty if the comment is missed(漏标), or assicated with the element or job
                reasons:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.AnnoCommentReason_Reasons'
                    description: comment reasons
                scope:
                    enum:
                        - unspecified
                        - object
                        - element
                        - job
                    type: string
                    description: scope of the comment
                    format: enum
                created_at:
                    type: string
                    description: the field is ignored in review requests
                    format: date-time
            description: comment to an annotation
        anno.v1.AnnoCommentReason_Reasons:
            required:
                - class
                - reasons
            type: object
            properties:
                class:
                    type: string
                    description: one of unwanted（多标）, missed（漏标）, flawed（错标）, other（其他）
                reasons:
                    type: array
                    items:
                        type: string
                    description: reason list in the class
        anno.v1.AnnoComment_ExtraInfo:
            type: object
            properties:
                position:
                    type: array
                    items:
                        type: number
                        format: double
                    description: 'location of the missing object: (x,y,z)'
                labels:
                    type: array
                    items:
                        type: string
                    description: label of the missed object
        anno.v1.AssignExecteamRequest:
            required:
                - uid
                - phases
            type: object
            properties:
                uid:
                    type: string
                    description: lot uid
                phases:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AssignExecteamRequest_Phase'
        anno.v1.AssignExecteamRequest_Phase:
            required:
                - phase
                - execteams
            type: object
            properties:
                phase:
                    type: integer
                    description: phase number, starts from 1
                    format: int32
                execteams:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Lotphase_Execteam'
                    description: support multiple teams in one phase
        anno.v1.AssignJobRequest:
            required:
                - uid
                - executor_uid
            type: object
            properties:
                uid:
                    type: string
                executor_uid:
                    type: string
        anno.v1.Attr:
            required:
                - name
                - display_name
                - type
                - value_type
                - choices
            type: object
            properties:
                name:
                    type: string
                    description: key of the attribute
                display_name:
                    type: string
                desc:
                    type: string
                avatar:
                    type: string
                type:
                    enum:
                        - input
                        - bool
                        - radiobox
                        - checkbox
                    type: string
                    format: enum
                value_type:
                    enum:
                        - int
                        - float
                        - text
                        - color
                        - date
                        - time
                        - datetime
                        - bool
                    type: string
                    description: accepted value type if type is input
                    format: enum
                choices:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AttrValue'
                    description: if not empty, value should be chosen from the list
        anno.v1.AttrAndValues:
            required:
                - name
                - values
            type: object
            properties:
                name:
                    type: string
                    description: name of the attribute
                values:
                    type: array
                    items:
                        type: string
                    description: values of the attribute
        anno.v1.AttrRefList:
            required:
                - optional_v2
                - required_v2
            type: object
            properties:
                optional_v2:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AttrRefList_Attr'
                    description: optional attributes
                required_v2:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AttrRefList_Attr'
                    description: required attributes
        anno.v1.AttrRefList_Attr:
            required:
                - name
            type: object
            properties:
                name:
                    type: string
                    description: attribute name
                default:
                    type: string
                    description: default value of this attribute
                rawdata_types:
                    type: array
                    items:
                        enum:
                            - unspecified
                            - image
                            - pointcloud
                        type: string
                        format: enum
                    description: which rawdata types the attribute is applicable to
        anno.v1.AttrValue:
            required:
                - name
                - display_name
                - value
            type: object
            properties:
                name:
                    type: string
                    description: key of the AttrValue
                display_name:
                    type: string
                    description: display name
                desc:
                    type: string
                    description: description
                avatar:
                    type: string
                    description: avatar of the value
                value:
                    type: string
                    description: 'value, e.g. #FF00FF for RGB color'
        anno.v1.BatchRevertJobReply:
            required:
                - ok_job_uids
                - fail_job_uids
            type: object
            properties:
                ok_job_uids:
                    type: array
                    items:
                        type: string
                    description: jobs successfully updated
                fail_job_uids:
                    type: array
                    items:
                        type: string
                    description: jobs failed to update
        anno.v1.BatchRevertJobRequest:
            required:
                - action
                - options
                - filter
            type: object
            properties:
                action:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.BatchRevertJobRequest_Action'
                    description: changes to be made to the jobs
                options:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.BatchRevertJobRequest_Options'
                    description: options to the action
                filter:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.ListJobFilter'
                    description: job filter
        anno.v1.BatchRevertJobRequest_Action:
            required:
                - to_phase
            type: object
            properties:
                to_phase:
                    type: integer
                    description: set jobs to this phase number; starts from 1
                    format: int32
        anno.v1.BatchRevertJobRequest_Options:
            required:
                - keep_annos
                - keep_comments
                - to_previous_executor
            type: object
            properties:
                keep_annos:
                    type: boolean
                    description: if to keep annotations
                keep_comments:
                    type: boolean
                    description: if to keep comments
                to_previous_executor:
                    type: boolean
                    description: if to reassign to previous executor
        anno.v1.Bizgrant:
            required:
                - grantor_uid
                - grantee_uid
                - org_uid
                - created_at
            type: object
            properties:
                grantor_uid:
                    type: string
                    description: grantor uid
                grantee_uid:
                    type: string
                    description: grantee uid
                org_uid:
                    type: string
                    description: uid of the organization whose business permissions are granted
                created_at:
                    type: string
                    format: date-time
        anno.v1.ClaimJobRequest:
            required:
                - lot_uid
            type: object
            properties:
                lot_uid:
                    type: string
                    description: which lot the job is claimed from; if not specified, the job is claimed from any assigned lot
                subtype:
                    type: string
                    description: for a splitted lot, this is to specify the sub-lot.
                fallback:
                    type: boolean
                    description: |-
                        when lot_uid/subtype is specified and no available job can be claimed, this controls if it
                         should retry to claim in disregard of lot_uid/subtype
                prefer:
                    enum:
                        - unspecified
                        - rejected_first
                    type: string
                    description: claim preference
                    format: enum
                renew:
                    type: boolean
                    description: true to reset the claim countdown timer; the response will not contain the job info.
                job_uid:
                    type: string
                    description: required if renew is true
        anno.v1.ClaimJobResponse:
            type: object
            properties:
                job:
                    $ref: '#/components/schemas/anno.v1.Job'
        anno.v1.CloneLotRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: source lot uid
                updates:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.UpdateLotRequest'
                    description: modified fields
                copy_executors:
                    type: boolean
                    description: if to copy executors
        anno.v1.CommentReasonClass:
            required:
                - class
                - reasons
            type: object
            properties:
                class:
                    allOf:
                        - $ref: '#/components/schemas/types.DisplayItem'
                    description: reason class
                reasons:
                    type: array
                    items:
                        $ref: '#/components/schemas/types.DisplayItem'
                    description: reasons in the class
        anno.v1.Compound_Part:
            required:
                - label
            type: object
            properties:
                label:
                    type: string
                    description: label name
                occurs:
                    allOf:
                        - $ref: '#/components/schemas/types.RangeInt32'
                    description: define minimum and maximum occurences of the part within a compound
        anno.v1.CreateBizgrantRequest:
            required:
                - grantee_uid
                - org_uid
            type: object
            properties:
                grantee_uid:
                    type: string
                    description: grantee uid
                org_uid:
                    type: string
                    description: uid of the organization whose business permissions are granted
        anno.v1.CreateLotRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: mandatory in update-requests
                name:
                    type: string
                    description: mandatory in create-requests
                desc:
                    type: string
                project_uid:
                    type: string
                    description: mandatory in create-requests
                type:
                    enum:
                        - unspecified
                        - annotate
                        - segment_instance
                        - segment_semantic
                        - segment_panoptic
                    type: string
                    description: task type; mandatory in create-requests
                    format: enum
                priority:
                    type: integer
                    description: larger number indicates higher priority
                    format: int32
                autostart:
                    type: boolean
                    description: whether to automatically start
                job_size:
                    type: integer
                    description: number of elements in a job
                    format: int32
                ontologies:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Lotontologies'
                    description: lot ontologies
                phases:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Lotphase'
                    description: execution phases; phase number starts from 1
                instruction:
                    type: string
                    description: execution instructions in format of HTML or Markdown
                org_uid:
                    type: string
                data_uid:
                    type: string
                    description: mandatory in create-requests
                out:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.OutConfig'
                    description: annotation result output config
                exp_end_time:
                    type: string
                    description: expected end time
                    format: date-time
                is_frame_series:
                    type: boolean
                    description: if to treat job as consecutive frames
                order_uid:
                    type: string
                    description: order UID
                tool_cfg:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Lot_ToolConfig'
                    description: annotation tool configuration
                comment_reasons:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.CommentReasonClass'
                    description: allowed comment reason list
        anno.v1.CreateLottplRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: mandatory in update-requests
                name:
                    type: string
                    description: mandatory in create-requests
                desc:
                    type: string
                type:
                    enum:
                        - unspecified
                        - annotate
                        - segment_instance
                        - segment_semantic
                        - segment_panoptic
                    type: string
                    description: |-
                        mandatory in create-requests;
                         lot type
                    format: enum
                ontologies:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Lotontologies'
                    description: lot ontologies
                phases:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Lotphase'
                    description: execution phases; phase number starts from 1
                instruction:
                    type: string
                    description: execution instructions in format of HTML or Markdown
                out:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.OutConfig'
                    description: annotation result output config
                job_size:
                    type: integer
                    description: number of elements in a job
                    format: int32
                work_range:
                    type: number
                    description: make annotations within the radius, unit is meter
                    format: float
        anno.v1.CreateOrderRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: mandatory in update-requests
                name:
                    type: string
                    description: |-
                        mandatory in create-requests;
                         pattern: ^[\\p{Han}\\w\\d_-]{0,256}$
                org_uid:
                    type: string
                    description: order's organization; if empty, this is the requestor's organization
                source:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Source'
                    description: files attached to the order
        anno.v1.CreateProjectRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: mandatory in update-requests
                name:
                    type: string
                    description: mandatory in create-requests
                desc:
                    type: string
                avatar:
                    type: string
        anno.v1.CreateSpecgrantRequest:
            required:
                - grantee_uid
                - item_type
                - item_uid
            type: object
            properties:
                grantee_uid:
                    type: string
                    description: grantee uid
                item_type:
                    enum:
                        - unspecified
                        - AnnoLot
                    type: string
                    description: type of the item
                    format: enum
                item_uid:
                    type: string
                    description: item uid
        anno.v1.DataConverter:
            required:
                - runtime
                - uri
            type: object
            properties:
                runtime:
                    type: string
                    description: conversion script runtime; default is python3.10
                uri:
                    type: string
                    description: conversion script URI
        anno.v1.DataValidationSummary:
            type: object
            properties:
                total_errors:
                    type: integer
                    description: total number of validation errors found, includes those unsaved errors
                    format: int32
                errors:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.DataValidationSummary_Error'
                    description: validation errors; only the first 10 errors are saved
        anno.v1.DataValidationSummary_Error:
            type: object
            properties:
                error:
                    enum:
                        - unspecified
                        - file_unknown
                        - file_missing
                    type: string
                    format: enum
                elem_index:
                    type: integer
                    format: int32
                rawdata_name:
                    type: string
        anno.v1.Direction:
            required:
                - toward
            type: object
            properties:
                origin:
                    type: array
                    items:
                        type: number
                        format: double
                    description: |-
                        origin point; if empty, it is the widget center point;
                         x, y if 2D; x, y, z if 3D
                toward:
                    type: array
                    items:
                        type: number
                        format: double
                    description: |-
                        toward point;
                         x, y if 2D; x, y, z if 3D
        anno.v1.DummyReply:
            type: object
            properties:
                ElementDataRef:
                    $ref: '#/components/schemas/anno.v1.Job_ElementData'
                AnnotationDataRef:
                    $ref: '#/components/schemas/anno.v1.Job_AnnotationData'
                CommentDataRef:
                    $ref: '#/components/schemas/anno.v1.Job_CommentData'
                FilelistRef:
                    $ref: '#/components/schemas/types.Filelist'
        anno.v1.Element:
            required:
                - index
                - name
                - type
                - datas
            type: object
            properties:
                index:
                    type: integer
                    description: element index in the lot dataset
                    format: int32
                name:
                    type: string
                    description: 'name of the element: innermost-folder/frame-index'
                type:
                    enum:
                        - unspecified
                        - image
                        - pointcloud
                        - fusion2d
                        - fusion3d
                        - fusion4d
                    type: string
                    format: enum
                datas:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Rawdata'
                anno:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.ElementAnno'
                    description: |-
                        标注结果；
                         仅用于结果导出，或在导入时用于传输已经有标注信息的数据；
                         标注过程中，请使用 Job 里的相关字段
            description: 一帧数据，包含一个或多个待标注文件
        anno.v1.ElementAnno:
            required:
                - index
                - name
                - ins_cnt
                - attrs
                - rawdata_annos
            type: object
            properties:
                index:
                    type: integer
                    description: element index in the lot dataset
                    format: int32
                name:
                    type: string
                    description: 'name of the element: innermost-folder/frame-index'
                rawdata_annos:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.RawdataAnno'
                    description: 相应位置的元素对应 Element 的 datas 相应位置的结果
                attrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AttrAndValues'
                ins_cnt:
                    type: integer
                    description: number of objects annotated in the element
                    format: int32
                metadata:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.ElementAnno_Metadata'
                    description: metadata of the element annos
                segmentation3d:
                    $ref: '#/components/schemas/anno.v1.Segmentation3d'
            description: annotations of an element (frame)
        anno.v1.ElementAnno_Metadata:
            type: object
            properties:
                executors:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Metadata_Executor'
                    description: executor of each phase
        anno.v1.Error:
            required:
                - reason
                - message
            type: object
            properties:
                reason:
                    type: string
                message:
                    type: string
        anno.v1.Errors:
            type: object
            properties:
                errors:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/types.Multilingual'
                    description: error-reason -> (language -> display-name)
        anno.v1.ExportLotAnnosRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: lot UID
                option:
                    enum:
                        - unspecified
                        - finished
                    type: string
                    description: option
                    format: enum
                phases:
                    type: array
                    items:
                        type: integer
                        format: int32
        anno.v1.ExportOrderAnnosRequest:
            required:
                - uid
            type: object
            properties:
                uid:
                    type: string
                    description: order UID
                option:
                    enum:
                        - unspecified
                        - finished
                    type: string
                    description: option
                    format: enum
        anno.v1.GetJobDraftReply:
            required:
                - version
            type: object
            properties:
                version:
                    type: string
                    description: 'time in RFC3339 format: 2016-01-01T00:00:00+08:00'
                draft_url:
                    type: string
                    description: draft annos/comments url, refer to SaveJobDraftRequest
        anno.v1.GetJobLastCommitLogReply:
            type: object
            properties:
                log:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.GetJoblogReply_Log'
                    description: joblog without the details field
        anno.v1.GetJoblogReply:
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                logs:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.GetJoblogReply_Log'
        anno.v1.GetJoblogReply_Log:
            required:
                - operator
                - action
                - time
            type: object
            properties:
                operator:
                    $ref: '#/components/schemas/iam.v1.BaseUser'
                action:
                    enum:
                        - unspecified
                        - accept
                        - reject
                        - recycle
                        - submit
                        - claim
                        - giveup
                        - assign
                        - timeout
                        - end
                    type: string
                    format: enum
                details:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Log_Details'
                    description: detailed information if any
                from_phase:
                    type: integer
                    description: phase number at this event
                    format: int32
                to_phase:
                    type: integer
                    description: phase number after this event
                    format: int32
                created_at:
                    type: string
                    description: created_at timestamp
                    format: date-time
        anno.v1.GetLotRequest:
            type: object
            properties:
                uid:
                    type: string
        anno.v1.GetLotSummaryReply:
            required:
                - total_jobs
                - jobs_at_phase
            type: object
            properties:
                total_jobs:
                    type: integer
                    description: total number of jobs
                    format: int32
                jobs_at_phase:
                    type: array
                    items:
                        type: integer
                        format: int32
                    description: |-
                        jobs at each phase 1~n, the last element means the finished jobs.
                         if the lot has n phases, the length of jobs_at_phase is n+1.
            description: contains number of jobs in various phases
        anno.v1.GetOrderAnnoResultReply:
            type: object
            properties:
                will_ready_at:
                    type: string
                    description: |-
                        the estimated time when the result is ready;
                         valid only when the order is in the finished state
                    format: date-time
                url:
                    type: string
                    description: URL to the result if available
        anno.v1.GetOrderRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: order UID
        anno.v1.GetRawJoblogReply:
            type: object
            properties:
                logs:
                    type: string
                    description: json encoded raw logs
        anno.v1.GetSkipAnnotationReply:
            type: object
            properties:
                skip_annotation:
                    type: boolean
                    description: if available, only query by org_uid; otherwise, query by user_uids
        anno.v1.GetUserRoleReply:
            type: object
            properties:
                role:
                    type: string
        anno.v1.GetUserSkillReply:
            type: object
            properties:
                skills:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Skill'
        anno.v1.GetVersionReply:
            type: object
            properties:
                version:
                    type: string
        anno.v1.GiveupJobRequest:
            required:
                - uid
                - reason
            type: object
            properties:
                uid:
                    type: string
                reason:
                    type: string
                details:
                    type: string
        anno.v1.HasHoldingJobsReply:
            required:
                - holding
            type: object
            properties:
                holding:
                    type: object
                    additionalProperties:
                        type: boolean
        anno.v1.Job:
            required:
                - uid
                - idx_in_lot
                - subtype
                - elements
                - annotations
                - comments
                - state
                - cause
                - phase
                - ins_cnt
                - lot_uid
                - created_at
            type: object
            properties:
                uid:
                    type: string
                    description: job UID
                lot_uid:
                    type: string
                    description: lot UID
                idx_in_lot:
                    type: integer
                    description: job index in the lot
                    format: int32
                subtype:
                    type: string
                    description: identify a subjob in a splitted lot.
                elements:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Element'
                    description: 待标注的内容
                annotations:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.ElementAnno'
                    description: 标注结果
                comments:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AnnoComment'
                    description: unresolved comments (a comment can only be resolved at the same phase when it is added)
                state:
                    enum:
                        - unspecified
                        - unstart
                        - doing
                        - finished
                    type: string
                    format: enum
                cause:
                    type: string
                    description: why job is in the state
                phase:
                    type: integer
                    description: phase number, starts from 1
                    format: int32
                ins_cnt:
                    type: integer
                    description: manually annotated objects count in the job
                    format: int32
                ins_total:
                    type: integer
                    description: annotated objects count in the job (including the interpolated ones)
                    format: int32
                executor_uid:
                    type: string
                    description: current executor; valid for privileged requestors
                need_interpolation:
                    type: boolean
                    description: |-
                        if it is true, the annotations contain only the key element results,
                         and interpolation is needed to get the final result
                last_executor:
                    allOf:
                        - $ref: '#/components/schemas/iam.v1.BaseUser'
                    description: current executor or last submitter; valid for privileged requestors
                last_execteam:
                    allOf:
                        - $ref: '#/components/schemas/iam.v1.BaseUser'
                    description: team of last_executor; valid for privileged requestors
                elems_cnt:
                    type: integer
                    description: number of elements in this job
                    format: int32
                job_attrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AttrAndValues'
                    description: job attributes
                cam_params:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/anno.v1.Job_CamParam'
                    description: 相机映射参数，其中 key is rawdata.meta.image_meta.camera
                elements_urls:
                    type: array
                    items:
                        type: string
                    description: |-
                        保存了 elements 等数据, 其结构参考 Job_ElementData;
                         如果有多个 url，完整的内容将是这些 url 所指文件的合集，cam_params 只会放在第一个文件里
                annotations_url:
                    type: string
                    description: 保存了 annotations 等数据, 其结构参考 Job_AnnotationData
                comments_url:
                    type: string
                    description: 保存了 comments 等数据，其结构参考 Job_CommentData
                job_elem_clip:
                    type: string
                updated_at:
                    readOnly: true
                    type: string
                    format: date-time
                created_at:
                    readOnly: true
                    type: string
                    format: date-time
        anno.v1.JobAnno:
            required:
                - element_annos
                - need_interpolation
                - ins_cnt
            type: object
            properties:
                element_annos:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.ElementAnno'
                    description: 相应位置的元素对应 Job 的 Elements 相应位置的结果
                need_interpolation:
                    type: boolean
                    description: |-
                        if it is true, the annotations contain only the key element results,
                         and interpolation is needed to get the final result
                ins_cnt:
                    type: integer
                    description: number of objects annotated in the job (including the interpolated objects)
                    format: int32
                job_index:
                    type: integer
                    description: job index in the lot
                    format: int32
                attrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AttrAndValues'
                    description: job attributes
            description: annotations of a job
        anno.v1.JobCountByLotidsReply:
            type: object
            properties:
                lots:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.JobCountByLotidsReply_LotInfo'
        anno.v1.JobCountByLotidsReply_LotInfo:
            type: object
            properties:
                lot_id:
                    type: string
                count:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.JobCountByLotidsReply_PhaseCount'
        anno.v1.JobCountByLotidsReply_PhaseCount:
            type: object
            properties:
                phase:
                    type: integer
                    format: int32
                count:
                    type: integer
                    format: int32
        anno.v1.Job_AnnotationData:
            type: object
            properties:
                element_annos:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.ElementAnno'
                    description: 所有标注结果
                job_attrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AttrAndValues'
                    description: 包属性
        anno.v1.Job_CamParam:
            type: object
            properties:
                extrinsic:
                    type: array
                    items:
                        type: number
                        format: double
                    description: 'extrinsic params: x,y,z,qx,qy,qz,qw'
                intrinsic:
                    type: array
                    items:
                        type: number
                        format: double
                    description: 'intrinsic params: fx,fy,cx,cy'
                distortion:
                    type: array
                    items:
                        type: number
                        format: double
                    description: 'distortion params: distortion_type,k1,k2,...'
        anno.v1.Job_CommentData:
            type: object
            properties:
                comments:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AnnoComment'
                    description: 所有批注
        anno.v1.Job_ElementData:
            type: object
            properties:
                elements:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Element'
                    description: 所有帧
                cam_params:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/anno.v1.Job_CamParam'
                    description: 相机映射参数，其中 key is rawdata.meta.image_meta.camera
        anno.v1.Label:
            required:
                - name
                - display_name
                - color
            type: object
            properties:
                name:
                    type: string
                    description: key of the label
                display_name:
                    type: string
                desc:
                    type: string
                avatar:
                    type: string
                color:
                    type: string
                    description: 'RGB value: #RRGGBBAA'
                attrs:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.AttrRefList'
                    description: attributes to be attached
                class:
                    type: array
                    items:
                        type: string
                    description: 'muti-level classes: human -> adult, car -> mpv, ...'
                widgets_v2:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Label_Widget'
                compound:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Label_Compound'
                    description: a compounded object
                is_3d_segmentation:
                    type: boolean
                has_instance:
                    type: boolean
            description: Label used in a task config
        anno.v1.Label_Compound:
            required:
                - parts
            type: object
            properties:
                parts:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Compound_Part'
                    description: components
        anno.v1.Label_Widget:
            required:
                - name
                - rawdata_type
            type: object
            properties:
                name:
                    enum:
                        - unspecified
                        - box2d
                        - pscuboid
                        - cuboid
                        - poly2d
                        - poly3d
                        - line2d
                        - line3d
                        - point2d
                        - point3d
                        - bitmap
                    type: string
                    description: widget name
                    format: enum
                rawdata_type:
                    enum:
                        - unspecified
                        - image
                        - pointcloud
                    type: string
                    description: rawdata type the widget is applicable to
                    format: enum
                preset:
                    type: array
                    items:
                        type: number
                        format: float
                    description: |-
                        preset scale: it is
                         [width, height] for box2d in pixel;
                         [width, height, depth (distance between two center-points)] for pscuboid in pixel;
                         [sx, sy, sz] for cuboid in meter;
                scale:
                    allOf:
                        - $ref: '#/components/schemas/types.Range'
                    description: define the minimum and maximum scales the widget must conform to; see comments on the preset field for its format
                line_type:
                    enum:
                        - line
                        - crspline
                        - centerline
                    type: string
                    description: line type used to draw the widget
                    format: enum
        anno.v1.Labelcls:
            type: object
            properties:
                name:
                    type: string
                langs:
                    type: object
                    additionalProperties:
                        type: string
                    description: language => name
        anno.v1.Labelwidget:
            type: object
            properties:
                name:
                    type: string
                langs:
                    type: object
                    additionalProperties:
                        type: string
                    description: language => name
        anno.v1.ListBizgrantReply:
            required:
                - grants
                - next_page_token
            type: object
            properties:
                grants:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Bizgrant'
                next_page_token:
                    type: string
                    description: An opaque pagination token, if not empty, to be used to fetch the next page of results
        anno.v1.ListCommentReasonsReply:
            type: object
            properties:
                classes:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.CommentReasonClass'
        anno.v1.ListExecteamsReply:
            required:
                - phases
            type: object
            properties:
                phases:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.ListExecteamsReply_Phase'
                    description: execution teams and executors info for each phase
        anno.v1.ListExecteamsReply_Execteam:
            required:
                - team
                - executors
            type: object
            properties:
                team:
                    allOf:
                        - $ref: '#/components/schemas/iam.v1.BaseUser'
                    description: execution team info
                executors:
                    type: array
                    items:
                        $ref: '#/components/schemas/iam.v1.BaseUser'
                    description: executors info
                quota:
                    $ref: '#/components/schemas/anno.v1.Lotphase_Quota'
        anno.v1.ListExecteamsReply_Phase:
            required:
                - teams
            type: object
            properties:
                teams:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.ListExecteamsReply_Execteam'
                    description: execution teams and executors info in this phase
        anno.v1.ListExecutorsReply:
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                users:
                    type: array
                    items:
                        $ref: '#/components/schemas/iam.v1.BaseUser'
                    description: repeated string user_uids = 2;
        anno.v1.ListJobFilter:
            required:
                - lot_uid
            type: object
            properties:
                uids:
                    type: array
                    items:
                        type: string
                    description: filter by job uids; max length is 100
                lot_uid:
                    type: string
                    description: filter by belonging lot
                phase:
                    type: integer
                    description: filter by jobs current phase; starts from 1
                    format: int32
                state:
                    enum:
                        - unspecified
                        - unstart
                        - doing
                        - finished
                    type: string
                    description: filter by jobs current state
                    format: enum
                last_executors:
                    type: array
                    items:
                        type: string
                    description: query jobs by their last executors; max length is 100
                last_execteam:
                    type: string
                    description: query jobs by the last executor's belonging team
                phases:
                    type: array
                    items:
                        type: integer
                        format: int32
                jobclip:
                    type: string
        anno.v1.ListJobReply:
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                jobs:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Job'
                executors:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/anno.v1.ElementAnno_Metadata'
                    description: jobs' executors in each phase. the key is job uid.
        anno.v1.ListLabelclsReply:
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                labelcls:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Labelcls'
        anno.v1.ListLabelwidgetReply:
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                widgets:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Labelwidget'
        anno.v1.ListLotReply:
            required:
                - lots
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                lots:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Lot'
                orgs:
                    type: array
                    items:
                        $ref: '#/components/schemas/iam.v1.BaseUser'
                    description: the organization that the lot, at the corresponding position, belongs to.
        anno.v1.ListLotsByExecutorReply:
            required:
                - lots
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                lots:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Lot'
                extras:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/anno.v1.ListLotsByExecutorReply_Extra'
                    description: the extra info that the lot has
        anno.v1.ListLotsByExecutorReply_Extra:
            type: object
            properties:
                has_rejected_jobs:
                    type: boolean
        anno.v1.ListLottplReply:
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                lottpls:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Lottpl'
        anno.v1.ListOrderReply:
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                orders:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Order'
                orgs:
                    type: array
                    items:
                        $ref: '#/components/schemas/iam.v1.BaseUser'
                    description: the organization that the order, at the corresponding position, belongs to.
        anno.v1.ListProjectReply:
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                projects:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Project'
        anno.v1.ListRoleReply:
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                roles:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Role'
        anno.v1.ListSkillReply:
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                skills:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Skill'
        anno.v1.ListSpecgrantReply:
            required:
                - grants
                - next_page_token
            type: object
            properties:
                grants:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Specgrant'
                next_page_token:
                    type: string
                    description: An opaque pagination token, if not empty, to be used to fetch the next page of results
        anno.v1.ListUsersRoleReply:
            type: object
            properties:
                users:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.UserRole'
                    description: repeated User users = 1;
        anno.v1.ListUsersSkillReply:
            type: object
            properties:
                users:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.UserSkill'
        anno.v1.Log_Details:
            required:
                - add_comments
                - resolves
            type: object
            properties:
                add_comments:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AnnoComment'
                    description: added comments in this log
                resolves:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.ResolveAnnoComment'
                    description: resolved comments in this log
                giveup_reason:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Log_GiveupReason'
                    description: job giveup reason
        anno.v1.Log_GiveupReason:
            required:
                - reason
                - details
            type: object
            properties:
                reason:
                    type: string
                details:
                    type: string
        anno.v1.Lot:
            required:
                - uid
                - name
                - state
                - type
                - priority
                - job_size
                - ontologies
                - phases
                - created_at
                - org_uid
                - data_uid
            type: object
            properties:
                uid:
                    type: string
                    description: lot UID
                name:
                    type: string
                    description: lot name
                desc:
                    type: string
                    description: lot description
                state:
                    enum:
                        - unspecified
                        - initializing
                        - unstart
                        - ongoing
                        - finished
                        - paused
                        - canceled
                    type: string
                    description: lot state
                    format: enum
                type:
                    enum:
                        - unspecified
                        - annotate
                        - segment_instance
                        - segment_semantic
                        - segment_panoptic
                    type: string
                    description: lot type
                    format: enum
                data_type:
                    enum:
                        - unspecified
                        - image
                        - pointcloud
                        - fusion2d
                        - fusion3d
                        - fusion4d
                    type: string
                    description: data type
                    format: enum
                is_frame_series:
                    type: boolean
                    description: if to treat job as consecutive frames
                priority:
                    type: integer
                    description: larger number indicates higher priority
                    format: int32
                autostart:
                    type: boolean
                    description: whether to automatically start
                job_size:
                    type: integer
                    description: |-
                        maximum number of elements in a job.
                         if it is 0, a job is created per subfolder
                    format: int32
                ontologies:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Lotontologies'
                    description: lot ontologies
                phases:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Lotphase'
                    description: execution phases; phase number starts from 1
                instruction:
                    type: string
                    description: execution instructions in format of Markdown?
                creator_uid:
                    type: string
                    description: creator UID
                data_size:
                    readOnly: true
                    type: integer
                    description: number of elements in this lot
                    format: int32
                exp_end_time:
                    type: string
                    description: expected end time
                    format: date-time
                updated_at:
                    readOnly: true
                    type: string
                    format: date-time
                created_at:
                    readOnly: true
                    type: string
                    format: date-time
                error:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Error'
                    description: error info if state is ls_error
                org_uid:
                    type: string
                    description: UID of the organization which the lot belongs to
                data_uid:
                    type: string
                    description: UID of the data associated with the lot
                out:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.OutConfig'
                    description: annotation result output config
                job_count:
                    type: integer
                    description: number of jobs in this lot
                    format: int32
                job_ready:
                    type: boolean
                    description: if jobs are created
                order_uid:
                    type: string
                    description: order UID
                ins_cnt:
                    type: integer
                    description: manually annotated objects count; only available after lot is finished
                    format: int32
                ins_total:
                    type: integer
                    description: annotated objects count (including interpolated ones); only available after lot is finished
                    format: int32
                tool_cfg:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Lot_ToolConfig'
                    description: annotation tool configuration
                anno_result_url:
                    type: string
                    description: anno result url
                can_export_annos:
                    type: boolean
                    description: indicate if the demander can export annos
                comment_reasons:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.CommentReasonClass'
                    description: allowed comment reason list
                tags:
                    type: array
                    items:
                        type: string
                    description: tags attached to the lot
        anno.v1.Lot_Range:
            required:
                - shape
                - data
            type: object
            properties:
                shape:
                    enum:
                        - unspecified
                        - circle
                        - rectangle
                    type: string
                    description: shape type
                    format: enum
                data:
                    type: array
                    items:
                        type: number
                        format: float
                    description: |-
                        the params of the shape; specific to shape type.
                         circle: [radius], the unit is meter (in pointclouds), or pixel (in images);
                         rectangle: [width, height], the unit is meter (in pointclouds), or pixel (in images);
                zrange:
                    allOf:
                        - $ref: '#/components/schemas/types.Range'
                    description: range on the z-axis, the unit is meter
                rawdata_types:
                    type: array
                    items:
                        enum:
                            - unspecified
                            - image
                            - pointcloud
                        type: string
                        format: enum
                    description: which rawdata types the range is applicable to
        anno.v1.Lot_ToolConfig:
            type: object
            properties:
                ranges:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Lot_Range'
                    description: work range indicators
                projected_editable:
                    type: boolean
                    description: if projected objects are editable in a 23D fusion task
                redo_projection:
                    type: boolean
                    description: if to regenerate 2D projected objects according to 3D objects
                rulers:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.ToolConfig_Ruler'
                    description: 定义各种类型和大小的量尺，方便比对标志物的尺寸是否合规
                save_projected:
                    type: boolean
                    description: whether to save 2D projected objects
                image_order:
                    type: array
                    items:
                        type: string
                    description: the order of images in a single frame, specified with images' rawdata.meta.image.camera
                segmentation_3d_enabled:
                    type: boolean
                pre_box:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.ToolConfig_PreBox'
        anno.v1.Lotontologies:
            required:
                - groups
                - attrs
            type: object
            properties:
                attrs:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/anno.v1.Attr'
                    description: definitions for all attributes
                elem_attrs:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.AttrRefList'
                    description: global attribute names for an element(frame)
                rawdata_attrs:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.AttrRefList'
                    description: global attribute names for a rawdata(image/file)
                groups:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Lotontologies_Group'
                    description: when there are mutiple groups, the lot will be splitted according to groups
                job_attrs:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.AttrRefList'
                    description: global attribute names for a job
        anno.v1.Lotontologies_Group:
            required:
                - name
                - labels
            type: object
            properties:
                name:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Label'
                    description: annotation scope
        anno.v1.Lotphase:
            required:
                - number
                - name
                - type
                - editable
                - sample_percent
                - timeout
                - execteams
            type: object
            properties:
                number:
                    type: integer
                    description: phase number, start from 1
                    format: int32
                name:
                    type: string
                    description: name of this phase, e.g. label/review-1/review-2/acceptance/..., 标注/审核1/审核2/验收/...
                type:
                    enum:
                        - unspecified
                        - label
                        - review
                    type: string
                    description: type of this phase
                    format: enum
                editable:
                    type: boolean
                    description: can reviewer edit the annotations
                sample_percent:
                    type: number
                    description: percent of the annotations to be reviewed
                    format: float
                min_skill_level:
                    type: integer
                    description: minimum skill level required for executor eligible for this lot phase
                    format: int32
                timeout:
                    type: integer
                    description: assignees should finish their assignments within this number of seconds
                    format: int32
                merge:
                    type: boolean
                    description: whether to merge the annotation results of splitted jobs after this phase
                execteams:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Lotphase_Execteam'
                    description: support multiple execution team-uids in this phase;
                claim_policy:
                    enum:
                        - unspecified
                        - only_same_team
                        - only_other_teams
                    type: string
                    format: enum
            description: Lotphase describes an exeuction phase in a lot
        anno.v1.Lotphase_Execteam:
            required:
                - execteam
            type: object
            properties:
                execteam:
                    type: string
                    description: execution team-uid
                quota:
                    $ref: '#/components/schemas/anno.v1.Lotphase_Quota'
                claimed_jobs:
                    type: integer
                    description: '[OUTPUT] the number of jobs that this team has claimed (not percentage)'
                    format: int32
        anno.v1.Lotphase_Quota:
            type: object
            properties:
                min:
                    type: integer
                    description: |-
                        minimum job quota in percentage, [0, 100];
                         if min is not 0 and min is equal to max, means this team needs to finish min% of total jobs;
                    format: int32
                max:
                    type: integer
                    description: |-
                        maximum job quota in percentage, [0, 100];
                         max must be explicitly set and we do not treat 0 specially.
                    format: int32
        anno.v1.Lottpl:
            required:
                - uid
                - name
                - type
                - job_size
                - ontologies
                - phases
                - created_at
            type: object
            properties:
                uid:
                    type: string
                    description: lot template UID
                name:
                    type: string
                    description: lot template name
                desc:
                    type: string
                    description: lot template description
                type:
                    enum:
                        - unspecified
                        - annotate
                        - segment_instance
                        - segment_semantic
                        - segment_panoptic
                    type: string
                    description: task type
                    format: enum
                ontologies:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Lotontologies'
                    description: lot ontologies
                phases:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Lotphase'
                    description: execution phases; phase number starts from 1
                instruction:
                    type: string
                    description: execution instructions in format of HTML or Markdown
                out:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.OutConfig'
                    description: annotation result output config
                job_size:
                    type: integer
                    description: number of elements in a job
                    format: int32
                work_range:
                    type: number
                    description: make annotations within the radius, unit is meter
                    format: float
                updated_at:
                    readOnly: true
                    type: string
                    format: date-time
                created_at:
                    readOnly: true
                    type: string
                    format: date-time
        anno.v1.ManageExecutorsRequest:
            required:
                - uid
                - phases
            type: object
            properties:
                uid:
                    type: string
                    description: lot uid
                phases:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.ManageExecutorsRequest_Phase'
        anno.v1.ManageExecutorsRequest_Phase:
            required:
                - phase
                - team_uid
            type: object
            properties:
                phase:
                    type: integer
                    description: phase number, starts from 1
                    format: int32
                team_uid:
                    type: string
                    description: the executors' team
                add:
                    type: array
                    items:
                        type: string
                    description: user uids to add
                delete:
                    type: array
                    items:
                        type: string
                    description: user uids to remove
        anno.v1.Metadata_Executor:
            required:
                - user
                - submit_at
            type: object
            properties:
                user:
                    $ref: '#/components/schemas/iam.v1.BaseUser'
                submit_at:
                    type: string
                    format: date-time
                phase:
                    type: integer
                    format: int32
        anno.v1.Object:
            required:
                - uuid
                - label
            type: object
            properties:
                uuid:
                    type: string
                    description: UUID of the object
                track_id:
                    type: string
                    description: |-
                        一个任务包中，相同的 track_id 表示同一个实体对象。
                         命名可以采用 Job.Uid + Label.name + index 的模式，例如：“xxx-car-1”
                label:
                    $ref: '#/components/schemas/anno.v1.Object_Label'
                vanish_later:
                    type: boolean
                    description: 在连续帧标注中，表示该物体从下一帧开始将不复存在
                compound:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Object_Compound'
                    description: it is a compounded object if not null
                source:
                    enum:
                        - unspecified
                        - manual
                        - prelabel
                        - interpolation
                        - projection
                    type: string
                    description: how the object is created
                    format: enum
        anno.v1.Object_Compound:
            required:
                - parts
            type: object
            properties:
                parts:
                    type: array
                    items:
                        type: string
                    description: referenced component object UUIDs
        anno.v1.Object_Label:
            required:
                - name
            type: object
            properties:
                name:
                    type: string
                    description: label name
                widget:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Object_Widget'
                    description: widget will be null if compound is not null
                attrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AttrAndValues'
                    description: attributes associated with the object
        anno.v1.Object_Widget:
            required:
                - name
                - data
            type: object
            properties:
                name:
                    enum:
                        - unspecified
                        - box2d
                        - pscuboid
                        - cuboid
                        - poly2d
                        - poly3d
                        - line2d
                        - line3d
                        - point2d
                        - point3d
                        - bitmap
                    type: string
                    description: widget name
                    format: enum
                data:
                    type: array
                    items:
                        type: number
                        format: double
                    description: characteristic values of geometric shapes, or bitmap origin (left, top)
                gaps:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Object_Widget'
                    description: gaps within the widget if any
                uri:
                    type: string
                    description: |-
                        bitmap file URL or data URI, e.g. data:image/png;base64,<base64-encoded-file-content>
                         pointcloud file URL or data URI, e.g. data:application/pcd;base64,<base64-encoded-file-content>
                forward:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Direction'
                    description: forward direction. if origin is null, then the center point of the widget is implied.
                point_cnt:
                    type: integer
                    description: number of points within the object (in 3D segmentation tasks)
                    format: int32
                seg_class_id:
                    type: integer
                    description: class ID in RawdataAnno.Segmentation; zero if the task is not a segmentation task
                    format: int32
                line_type:
                    enum:
                        - line
                        - crspline
                        - centerline
                    type: string
                    description: line type used to draw the widget
                    format: enum
                parent_lines:
                    type: array
                    items:
                        type: string
                    description: related parent lines
        anno.v1.Order:
            required:
                - uid
                - name
                - org_uid
                - state
                - created_at
            type: object
            properties:
                uid:
                    type: string
                    description: order UID
                name:
                    type: string
                    description: order name
                org_uid:
                    type: string
                    description: UID of the organization which the order belongs to
                source:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Source'
                    description: files attached to the order
                data_uid:
                    type: string
                    description: UID of the data associated with the order
                creator_uid:
                    type: string
                    description: creator UID
                size:
                    type: integer
                    description: number of elements in the order
                    format: int32
                state:
                    enum:
                        - unspecified
                        - initializing
                        - waiting
                        - ongoing
                        - finished
                        - canceled
                        - failed
                    type: string
                    description: order state
                    format: enum
                ins_total:
                    type: integer
                    description: annotated object count (include interpolated ones); only available after lot is finished
                    format: int32
                anno_result_url:
                    type: string
                    description: annotation result file URL
                error:
                    type: string
                    description: when state is failed, this field will contain detailed error message
                created_at:
                    type: string
                    description: order creation time
                    format: date-time
                data_summary:
                    readOnly: true
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.DataValidationSummary'
                    description: data validation summary
                can_export_annos:
                    readOnly: true
                    type: boolean
                    description: |-
                        whether the demander can export annos:
                         1) if false, anno_result_url will be always empty and the demander cannot export annos;
                         2) if true,
                         2.1) if anno_result_url is not empty, the demander can use it to download anno result;
                         2.2) otherwise, the demander can export annos;
        anno.v1.OutConfig:
            required:
                - exporter
                - encoder
                - layout
                - style
            type: object
            properties:
                exporter:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.OutConfig_Exporter'
                    description: exporter configuration
                encoder:
                    enum:
                        - json
                    type: string
                    description: encoding type
                    format: enum
                layout:
                    type: string
                    description: files layout
                style:
                    type: string
                    description: file style
                converter:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.DataConverter'
                    description: converter is a piece of script to convert the annos from platform format to customer format
        anno.v1.OutConfig_Exporter:
            required:
                - name
                - config
            type: object
            properties:
                name:
                    type: string
                    description: exporter name
                config:
                    type: string
                    description: exporter specific configuration
                include_data_uid:
                    type: boolean
                    description: if to data uid
        anno.v1.Project:
            required:
                - uid
                - name
                - created_at
            type: object
            properties:
                uid:
                    type: string
                name:
                    type: string
                desc:
                    type: string
                avatar:
                    type: string
                updated_at:
                    readOnly: true
                    type: string
                    format: date-time
                created_at:
                    readOnly: true
                    type: string
                    format: date-time
        anno.v1.PutCommentReasonsRequest:
            type: object
            properties:
                classes:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.CommentReasonClass'
                    description: corresponding classes will be replaced by this request
        anno.v1.Rawdata:
            required:
                - name
                - title
                - type
                - format
                - url
                - size
                - sha256
                - meta
                - ins
                - transform
            type: object
            properties:
                name:
                    type: string
                    description: file pathname
                type:
                    enum:
                        - unspecified
                        - image
                        - pointcloud
                    type: string
                    description: data type
                    format: enum
                format:
                    enum:
                        - unspecified
                        - json
                        - png
                        - jpg
                        - pcd
                        - filelist
                        - webp
                    type: string
                    description: data format
                    format: enum
                url:
                    type: string
                size:
                    type: number
                    description: number of bytes in the file
                    format: double
                sha256:
                    type: string
                    description: SHA-256 of the file
                title:
                    type: string
                    description: display name (sensor name)
                transform:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.RawdataParam'
                    description: 变换参数，可以将世界坐标系下的点映射到图片上
                meta:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Rawdata_Meta'
                    description: metadata of rawdata
                ins:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Object'
                    description: |-
                        识别出的物体列表；
                         仅用于结果导出，或在导入时用于传输已经有标注信息的数据；
                         标注过程中，请使用 Job 里的相关字段
                orig_name:
                    type: string
                    description: original pathname of the rawdata
                embedding:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Rawdata_Embedding'
                    description: file embedding
            description: 待标注文件，比如一张图片、点云的一帧
        anno.v1.RawdataAnno:
            required:
                - name
                - objects
                - attrs
            type: object
            properties:
                name:
                    type: string
                    description: name of the rawdata
                objects:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Object'
                attrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AttrAndValues'
                segmentation:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Segmentation'
                    description: segmentation result of the rawdata; null if the task is not a segmentation task
                orig_name:
                    type: string
                    description: original pathname of the rawdata
                url:
                    type: string
                    description: url of the rawdata, only available during exporting anno results
                metadata:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.RawdataAnno_Metadata'
                    description: metadata
            description: annotations of a rawdata
        anno.v1.RawdataAnno_Metadata:
            type: object
            properties:
                type:
                    enum:
                        - unspecified
                        - image
                        - pointcloud
                    type: string
                    description: rawdata type
                    format: enum
                camera:
                    type: string
                    description: camera name if the rawdata is an image (ImageMeta.camera)
        anno.v1.RawdataParam:
            required:
                - type
                - column_cnt
                - data
            type: object
            properties:
                type:
                    enum:
                        - matrix
                        - extrinsic
                        - intrinsic
                        - distortion
                    type: string
                    format: enum
                column_cnt:
                    type: integer
                    description: number of columns in the data
                    format: int32
                data:
                    type: array
                    items:
                        type: number
                        format: double
                    description: if type is distortion, the 1st number is the distortion model type (DistortionType)
            description: 与待标注文件相关的参数或变换矩阵
        anno.v1.Rawdata_Embedding:
            type: object
            properties:
                url:
                    type: string
                    description: embedding file URL
        anno.v1.Rawdata_ImageMeta:
            required:
                - width
                - height
            type: object
            properties:
                width:
                    type: integer
                    format: int32
                height:
                    type: integer
                    format: int32
                camera:
                    type: string
                    description: camera name
        anno.v1.Rawdata_Meta:
            type: object
            properties:
                image:
                    $ref: '#/components/schemas/anno.v1.Rawdata_ImageMeta'
                pcd:
                    $ref: '#/components/schemas/anno.v1.Rawdata_PCDMeta'
        anno.v1.Rawdata_PCDMeta:
            required:
                - points
            type: object
            properties:
                points:
                    type: integer
                    description: number of points in the pcd file
                    format: int32
                viewpoint:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.RawdataParam'
                    description: viewpoint of the point cloud in the point cloud's coordinate system
                pose:
                    type: array
                    items:
                        type: number
                        format: double
                    description: |-
                        origin point of the point cloud in world coordinate system when point cloud is not in world coordinate system,
                         in the form of [x,y,z,qx,qy,qz,qw]
        anno.v1.ResolveAnnoComment:
            type: object
            properties:
                uuid:
                    type: string
                    description: comment uuid
                obj_uuids:
                    type: array
                    items:
                        type: string
                    description: UUID of the associated objects if the comment is missed(漏标)
        anno.v1.ReviewJobRequest:
            required:
                - uid
                - decision
            type: object
            properties:
                uid:
                    type: string
                    description: job uid
                annotations:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.JobAnno'
                    description: |-
                        if it is null, this review makes no change to previous job annotations;
                         otherwise, it is a complete result to replace previous job annotations.
                decision:
                    enum:
                        - unspecified
                        - accept
                        - reject
                        - recycle
                    type: string
                    description: |-
                        "accept" will put the job to the next phase; annotations and resolve_comments can be set.
                         "reject" will put the job to the previous phase, and assign it to the previous executor at that phase;
                         annotations, comments and resolve_comments can be set.
                         "recycle" will put the job to its initial phase; previous annotations and comments will be cleared;
                         other fields will be ignored.
                    format: enum
                comments:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AnnoComment'
                    description: new comments to the annotations when decision is "reject"
                resolves:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.ResolveAnnoComment'
                    description: comments to resolve
                updated_comments:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.AnnoComment'
                    description: comments to update
                deleted_comments:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.ResolveAnnoComment'
                    description: comments to delete
        anno.v1.Role:
            type: object
            properties:
                name:
                    type: string
                langs:
                    type: object
                    additionalProperties:
                        type: string
                    description: language => name
        anno.v1.SaveJobDraftReply:
            type: object
            properties:
                version:
                    type: string
                    description: 'time in RFC3339 format: 2016-01-01T00:00:00+08:00'
        anno.v1.SaveJobDraftRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: job uid
                version:
                    type: string
                    description: draft version
                draft:
                    type: string
                    description: job draft might contains annos, comments and etc
        anno.v1.Scope:
            required:
                - uid
                - type
            type: object
            properties:
                type:
                    type: string
                    description: global/project/lot
                uid:
                    type: string
                    description: project-uid/lot-uid
        anno.v1.Segmentation:
            type: object
            properties:
                classes:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Segmentation_Class'
                    description: classes definition; if empty, class name is RawdataAnno.Object.uuid with the matching seg_class_id
                rle:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Segmentation_RLE'
                    description: unpacked RLE; either rle or rle_pack is set, not both
                rle_pack:
                    type: string
                    description: |-
                        packed RLE; serialize RLE using JSON, then compress using gzip, then encode in base64;
                         format: data:application/json;gzip;base64,[base64-encoded-content]
                              or http://packed-RLE-file-url (file format: RLE;json;gzip\ngzip-encoded-content)
        anno.v1.Segmentation3d:
            type: object
            properties:
                result:
                    $ref: '#/components/schemas/anno.v1.Segmentation3dResult'
                statistic:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Segmentation3dStatistic'
        anno.v1.Segmentation3dInstance:
            required:
                - instance_id
                - num
            type: object
            properties:
                instance_id:
                    type: integer
                    format: int32
                num:
                    type: integer
                    format: int32
        anno.v1.Segmentation3dResult:
            type: object
            properties:
                category:
                    type: array
                    items:
                        type: string
                instance_id:
                    type: array
                    items:
                        type: integer
                        format: int32
        anno.v1.Segmentation3dStatistic:
            required:
                - category_name
                - num
            type: object
            properties:
                category_name:
                    type: string
                num:
                    type: integer
                    format: int32
                instances:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Segmentation3dInstance'
        anno.v1.Segmentation_Class:
            required:
                - id
                - name
            type: object
            properties:
                id:
                    type: integer
                    description: serial number, start from 1
                    format: int32
                name:
                    type: string
                    description: it is Object.uuid for instance segmentation; it is label name for semantic/panoptic segmentation
        anno.v1.Segmentation_RLE:
            required:
                - runs
            type: object
            properties:
                runs:
                    type: array
                    items:
                        type: integer
                        format: int32
                    description: number of consecutive points having the same class ID
                vals:
                    type: array
                    items:
                        type: integer
                        format: int32
                    description: |-
                        Class.id of the points at the corresponding index in runs;
                         if empty, it implies a sequence of 0 and 1, starting with 0: [0, 1, 0, 1, ...]
        anno.v1.SetOrderAnnoResultRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: order UID
                url:
                    type: string
                    description: URL to the result
        anno.v1.SetRawdataEmbeddingRequest:
            required:
                - uid
                - elem_idx
                - rawdata_idx
                - embedding_uri
            type: object
            properties:
                uid:
                    type: string
                    description: job UID
                elem_idx:
                    type: integer
                    description: element index within job
                    format: int32
                rawdata_idx:
                    type: integer
                    description: rawdata index within element
                    format: int32
                embedding_uri:
                    type: string
                    description: embedding file URI
        anno.v1.SetUsersRoleRequest:
            type: object
            properties:
                scope:
                    $ref: '#/components/schemas/anno.v1.Scope'
                roles:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.UserRole'
        anno.v1.Skill:
            type: object
            properties:
                type:
                    enum:
                        - unspecified
                        - task_type
                        - label_class
                        - widget
                    type: string
                    format: enum
                name:
                    type: string
                max_level:
                    type: integer
                    format: int32
                langs:
                    type: object
                    additionalProperties:
                        type: string
                    description: language => name
        anno.v1.SkipAnnotationReply:
            type: object
            properties:
                skip_annotation:
                    type: boolean
                    description: if available, only query by org_uid; otherwise, query by user_uids
        anno.v1.SkipAnnotationRequest:
            type: object
            properties:
                uid:
                    type: string
                    description: if available, only query by org_uid; otherwise, query by user_uids
                lot_id:
                    type: string
                skip_annotation:
                    type: boolean
        anno.v1.Source:
            required:
                - uris
            type: object
            properties:
                uris:
                    type: array
                    items:
                        type: string
                    description: package file (.zip) URIs
                proprietary:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.Source_Proprietary'
                    description: access config when the files are hosted in a third-party platform
                style:
                    type: string
                    description: folder layout style within package files if not conform to Konvery standard
                elem_type:
                    enum:
                        - unspecified
                        - image
                        - pointcloud
                        - fusion2d
                        - fusion3d
                        - fusion4d
                    type: string
                    description: element type
                    format: enum
                is_frame_series:
                    type: boolean
                    description: if source contains consecutive frames
                plain_size_gb:
                    type: integer
                    description: size of the unpacked data in GB
                    format: int32
                error_handlers:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.Source_ParseErrorHandler'
                    description: |-
                        define parser error handlers; it will fail the parser if no handler is specified.
                         max size is count(RawdataErrorAction.Error) x count(Rawdata.Type).
                auto_parse:
                    type: boolean
                    description: whether to automatically parse data in annofeed service
                named_uris:
                    type: object
                    additionalProperties:
                        type: string
                    description: define single file names and their corresponding expected names;
                converter:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.DataConverter'
                    description: converter is a piece of script to convert the source data to the platform accepted format
                metadata:
                    type: object
                    additionalProperties:
                        type: string
                    description: metadata about original data, which might be used in parsing data and exporting annos
        anno.v1.Source_ParseErrorHandler:
            type: object
            properties:
                rawdata_type:
                    enum:
                        - unspecified
                        - image
                        - pointcloud
                    type: string
                    format: enum
                error:
                    enum:
                        - unspecified
                        - file_unknown
                        - file_missing
                    type: string
                    format: enum
                handler:
                    enum:
                        - unspecified
                        - fail
                        - ignore
                    type: string
                    format: enum
        anno.v1.Source_Proprietary:
            required:
                - type
                - config
            type: object
            properties:
                type:
                    type: string
                    description: 3rd-party file host service type
                config:
                    type: string
                    description: 3rd-party file host service access config
        anno.v1.Specgrant:
            required:
                - grantor_uid
                - grantee_uid
                - org_uid
                - item_type
                - item_uid
                - created_at
            type: object
            properties:
                grantor_uid:
                    type: string
                    description: grantor uid
                grantee_uid:
                    type: string
                    description: grantee uid
                item_type:
                    enum:
                        - unspecified
                        - AnnoLot
                    type: string
                    description: item type
                    format: enum
                item_uid:
                    type: string
                    description: item uid
                org_uid:
                    type: string
                    description: item owner organization uid
                created_at:
                    type: string
                    format: date-time
        anno.v1.SubmitJobRequest:
            required:
                - uid
                - annotations
            type: object
            properties:
                uid:
                    type: string
                annotations:
                    $ref: '#/components/schemas/anno.v1.JobAnno'
                resolves:
                    type: array
                    items:
                        $ref: '#/components/schemas/anno.v1.ResolveAnnoComment'
                    description: comments to be resolved
        anno.v1.ToolConfig_PreBox:
            required:
                - name
                - length
                - width
                - height
            type: object
            properties:
                name:
                    type: string
                length:
                    type: number
                    format: float
                width:
                    type: number
                    format: float
                height:
                    type: number
                    format: float
        anno.v1.ToolConfig_Ruler:
            required:
                - name
                - type
                - data
            type: object
            properties:
                name:
                    type: string
                    description: a friendly name for the ruler
                type:
                    enum:
                        - unspecified
                        - circle
                        - rectangle
                    type: string
                    description: shape of the ruler
                    format: enum
                data:
                    type: array
                    items:
                        type: number
                        format: double
                    description: |-
                        the params of the shape; specific to shape type.
                         rectangle: [width, height], the unit is pixel;
                         circle: [radius], the unit is pixel;
                rawdata_types:
                    type: array
                    items:
                        enum:
                            - unspecified
                            - image
                            - pointcloud
                        type: string
                        format: enum
                    description: which rawdata types the ruler is applicable to
        anno.v1.UpdateLotRequest:
            required:
                - lot
                - fields
            type: object
            properties:
                lot:
                    allOf:
                        - $ref: '#/components/schemas/anno.v1.CreateLotRequest'
                    description: update contents
                fields:
                    type: array
                    items:
                        type: string
                    description: name of fields to be updated
        anno.v1.UserRole:
            type: object
            properties:
                uid:
                    type: string
                role:
                    type: string
        anno.v1.UserSkill:
            type: object
            properties:
                uid:
                    type: string
                    description: user/team uid
                type:
                    enum:
                        - unspecified
                        - task_type
                        - label_class
                        - widget
                    type: string
                    format: enum
                name:
                    type: string
                level:
                    type: integer
                    format: int32
        iam.v1.BaseUser:
            required:
                - uid
                - name
                - avatar
            type: object
            properties:
                uid:
                    type: string
                    description: user/team uid
                name:
                    type: string
                    description: user/team name
                avatar:
                    type: string
                    description: user/team avatar url
        types.DisplayItem:
            required:
                - name
                - display_name
            type: object
            properties:
                name:
                    type: string
                    description: item name
                display_name:
                    type: string
                    description: item display name
        types.Filelist:
            required:
                - files
            type: object
            properties:
                files:
                    type: array
                    items:
                        $ref: '#/components/schemas/types.Filelist_File'
        types.Filelist_File:
            required:
                - url
                - size
            type: object
            properties:
                url:
                    type: string
                size:
                    type: number
                    description: file size in bytes
                    format: double
                name:
                    type: string
                    description: 'file name in path format without `data` prefix: e.g. frame1/meta/config.json'
        types.Multilingual:
            required:
                - langs
            type: object
            properties:
                langs:
                    type: object
                    additionalProperties:
                        type: string
                    description: language => text
        types.Range:
            required:
                - min
                - max
            type: object
            properties:
                min:
                    type: array
                    items:
                        type: number
                        format: float
                    description: min[0] indicates the minimum value; empty list means unspecified
                max:
                    type: array
                    items:
                        type: number
                        format: float
                    description: max[0] indicates the maximum value; empty list means unspecified
        types.RangeInt32:
            required:
                - min
                - max
            type: object
            properties:
                min:
                    type: array
                    items:
                        type: integer
                        format: int32
                    description: min[0] indicates the minimum value; empty list means unspecified
                max:
                    type: array
                    items:
                        type: integer
                        format: int32
                    description: max[0] indicates the maximum value; empty list means unspecified
        types.TagList:
            required:
                - tags
            type: object
            properties:
                tags:
                    type: array
                    items:
                        type: string
tags:
    - name: Bizgrants
    - name: Configs
    - name: Dummy
      description: |-
        set reference to inexplicitly referenced data structures, so that
         they can be included in the openapi documentation.
    - name: Jobs
    - name: Labelclz
    - name: Labelwidgets
    - name: Lots
    - name: Lottpls
      description: lot template service
    - name: Orders
    - name: Projects
    - name: Roles
    - name: Skills
    - name: Specgrants
