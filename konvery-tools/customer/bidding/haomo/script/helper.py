"""
Most of the code below is provided by <PERSON><PERSON><PERSON>.
"""

import math
import numpy as np
import pandas as pd
from scipy.spatial.transform import Rotation as R


def GPStoXY(lat, lon, ref_lat=23.019048, ref_lon=113.499673):
    """
    将经纬度坐标转变为 XY 坐标
    output XY in meters (m) X:正北 Y:正东（应该是是正西）
    广汽研究院定义了自己的 xyz 坐标系，其原点的经纬度为（23.019048,113.499673），x 轴为正北方向，y 轴为正西方向，z 轴朝上。
    """
    CONSTANTS_RADIUS_OF_EARTH = 6371000
    lat_rad = math.radians(lat)
    lon_rad = math.radians(lon)
    ref_lat_rad = math.radians(ref_lat)
    ref_lon_rad = math.radians(ref_lon)
    sin_lat = math.sin(lat_rad)  # 返回弧度的正弦值。
    cos_lat = math.cos(lat_rad)  # 返回弧度的余弦弦值。
    ref_sin_lat = math.sin(ref_lat_rad)
    ref_cos_lat = math.cos(ref_lat_rad)
    cos_d_lon = math.cos(lon_rad - ref_lon_rad)
    arg = np.clip(ref_sin_lat * sin_lat + ref_cos_lat * cos_lat * cos_d_lon, -1.0,
                  1.0)
    c = math.acos(arg)
    k = 1.0
    if abs(c) > 0:
        k = (c / math.sin(c))
    x = float(k * (ref_cos_lat * sin_lat - ref_sin_lat * cos_lat * cos_d_lon) *
              CONSTANTS_RADIUS_OF_EARTH)
    y = - float(k * cos_lat * math.sin(lon_rad - ref_lon_rad) *
                CONSTANTS_RADIUS_OF_EARTH)
    return x, y


def EularToQuotation(yaw, pitch, roll):
    ret = R.from_euler('ZYX', [yaw, pitch, roll], degrees=True)
    Q = ret.as_quat()
    quaternion = [list(Q)[3], list(Q)[0], list(Q)[1], list(Q)[2]]  # 得到四元数 (w,x,y,z)
    return quaternion


def test_xyz():
    filepath = "/Users/<USER>/work/data/Guangqiyan/raw/downloaded/20231012/LNABLAB39N5506120-10/drive-south_main_line-afternoon-cloudy-smooth-dry-expressway-asphalt-1230show1-same_time/20231012-15-15-12/10hz30s/samples/<EMAIL>"
    data = pd.read_csv(filepath)
    lat = data['Latitude[°]'].tolist()  # 纬度
    lon = data['Longitude[°]'].tolist()  # 经度
    for i in range(5):
        x, y = GPStoXY(lat[i], lon[i])
        print("xyz", x, y)


def test_rotation():
    filepath = "/Users/<USER>/work/data/Guangqiyan/raw/downloaded/20231012/LNABLAB39N5506120-10/drive-south_main_line-afternoon-cloudy-smooth-dry-expressway-asphalt-1230show1-same_time/20231012-15-15-12/10hz30s/samples/<EMAIL>"
    data = pd.read_csv(filepath)
    for i in range(5):
        yaw = data['Orientation[°]'].tolist()[i]
        pitch = - data['Pitch angle[°]'].tolist()[i]
        roll = data['Roll angle[°]'].tolist()[i]
        print("rotation", yaw, pitch, roll)
        quat = EularToQuotation(yaw, pitch, roll)
        print("quat", quat)


if __name__ == "__main__":
    test_xyz()
    test_rotation()
