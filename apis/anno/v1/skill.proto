syntax = "proto3";

package anno.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
// import "google/protobuf/timestamp.proto";
// import "google/protobuf/field_mask.proto";
// import "google/api/field_behavior.proto";
// import "openapi/v3/annotations.proto";
import "anno/v1/type.proto";
import "validate/validate.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anno/v1;anno";
option java_multiple_files = true;
option java_package = "anno.v1";

service Skills {
  rpc CreateSkill (Skill) returns (Skill) {
    option (google.api.http) = {
      post: "/v1/skills"
      body: "*"
    };
  }

  rpc UpdateSkill (UpdateSkillRequest) returns (Skill) {
    option (google.api.http) = {
      patch: "/v1/skills/{skill.name}"
      body: "skill"
    };
  }

  rpc DeleteSkill (DeleteSkillRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/skills/{name}"
    };
  }

  rpc GetSkill (GetSkillRequest) returns (Skill) {
    option (google.api.http) = {
      get: "/v1/skills/{name}"
    };
  }

  rpc ListSkill (ListSkillRequest) returns (ListSkillReply) {
    option (google.api.http) = {
      get: "/v1/skills"
    };
  }

  //
  // users skills
  //
  rpc GetUserSkill (GetUserSkillRequest) returns (GetUserSkillReply) {
    option (google.api.http) = {
      get: "/v1/skills/users/{uid}"
    };
  }

  rpc ListUsersSkill (ListUsersSkillRequest) returns (ListUsersSkillReply) {
    option (google.api.http) = {
      get: "/v1/skills/users"
    };
  }

  rpc AddUsersSkill (AddUsersSkillRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/skills/users"
      body: "*"
    };
  }

  rpc DeleteUsersSkill (DeleteUsersSkillRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/skills/users"
    };
  }

  // add skills to users in a team
  rpc AddTeamSkill (AddTeamSkillRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/skills/teams/{uid}"
      body: "*"
    };
  }

  // delete skills from users in a team
  rpc DeleteTeamSkill (DeleteTeamSkillRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/skills/teams/{uid}"
    };
  }
}

//message CreateSkillRequest {}
//message CreateSkillReply {}

message UpdateSkillRequest {
  Skill skill = 1;

  repeated string fields = 2;
}
//message UpdateSkillReply {}

message DeleteSkillRequest {
  string name = 1;
}
//message DeleteSkillReply {}

message GetSkillRequest {
  string name = 1;
}
//message GetSkillReply {}

message ListSkillRequest {
  int32 page = 1;
  int32 pagesz = 2 [(validate.rules).int32 = {gte:0, lte: 100}];

  Skill.Type.Enum type = 3 [(validate.rules).enum = {defined_only: true}];
  string name_pattern = 4;
}

message ListSkillReply {
  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated Skill skills = 2;
}

message Skill {
  message Type {
    enum Enum {
      unspecified = 0;
      task_type = 1;
      label_class = 2;
      widget = 3;
    }
  }

  Type.Enum type = 1 [(validate.rules).enum = {defined_only: true}];
  string name = 2;
  int32 max_level = 3;
  // language => name
  map<string, string> langs = 4;
}

//
// user skills (to be implemented)
//

message UserSkill {
  // user/team uid
  string uid = 1;
  Skill.Type.Enum type = 2 [(validate.rules).enum = {defined_only: true}];
  string name = 3;
  int32 level = 4;
}

message GetUserSkillRequest {
  // user uid
  string uid = 1;
  Scope scope = 2;
}

message GetUserSkillReply {
  repeated Skill skills = 1;
}

message ListUsersSkillRequest {
  Scope scope = 1;
  string team_uid = 2;
  repeated string user_uids = 3;
  repeated Skill skills = 4;
}

message ListUsersSkillReply {
  repeated UserSkill users = 2;
}

message AddUsersSkillRequest {
  Scope scope = 1;
  repeated UserSkill skills = 2;
}

message DeleteUsersSkillRequest {
  message UserSkill {
    string uid = 1;
    string type = 2;
    string name = 3;
  }
  Scope scope = 1;
  repeated UserSkill users = 2;
}

message AddTeamSkillRequest {
  // team uid
  string uid = 1;
  Scope scope = 2;
  repeated Skill skills = 3;
}

message DeleteTeamSkillRequest {
  // team uid
  string uid = 1;
  Scope scope = 2;
  repeated Skill skills = 3;
}
