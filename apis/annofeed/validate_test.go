package annofeed

import (
	"testing"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/apis/annofeed/v1"
)

func Test_CreateDataRequest(t *testing.T) {
	req := annofeed.CreateDataRequest{
		Uid:      "",
		Name:     "TestName",
		Type:     anno.Element_Type_image,
		Source:   &anno.Source{},
		OrgUid:   "org12345678",
		OrderUid: "order123456",
	}

	cases := []struct {
		name       string
		newReqFunc func(req annofeed.CreateDataRequest) annofeed.CreateDataRequest
		expectErr  bool
	}{
		{
			"happy_flow",
			nil,
			false,
		},
		{
			"Type_is_not_specified",
			func(req annofeed.CreateDataRequest) annofeed.CreateDataRequest {
				req.Type = 100
				return req
			},
			true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			newReq := req
			if c.newReqFunc != nil {
				newReq = c.newReqFunc(req)
			}
			if err := newReq.Validate(); (err != nil) != c.expectErr {
				t.Errorf("Validate() expect error: %v, got: %v", c.expectErr, err)
				return
			}
			if err := newReq.ValidateAll(); (err != nil) != c.expectErr {
				t.Errorf("ValidateAll() expect error: %v, got: %v", c.expectErr, err)
				return
			}
		})
	}
}

func Test_SetRawdataEmbeddingRequest(t *testing.T) {
	req := annofeed.SetRawdataEmbeddingRequest{
		Uid:          "uid12345678",
		RawdataName:  "name123456",
		EmbeddingUri: "s3://test.txt", // [scheme:][//[userinfo@]host][/]path[?query][#fragment]
	}

	cases := []struct {
		name       string
		newReqFunc func(req annofeed.SetRawdataEmbeddingRequest) annofeed.SetRawdataEmbeddingRequest
		expectErr  bool
	}{
		{
			"happy_flow",
			nil,
			false,
		},
		{
			"RawdataName_is_empty",
			func(req annofeed.SetRawdataEmbeddingRequest) annofeed.SetRawdataEmbeddingRequest {
				req.RawdataName = ""
				return req
			},
			true,
		},
		{
			"EmbeddingUri_is_wrong",
			func(req annofeed.SetRawdataEmbeddingRequest) annofeed.SetRawdataEmbeddingRequest {
				req.EmbeddingUri = "s3://@#$%"
				return req
			},
			true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			newReq := req
			if c.newReqFunc != nil {
				newReq = c.newReqFunc(req)
			}
			if err := newReq.Validate(); (err != nil) != c.expectErr {
				t.Errorf("Validate() expect error: %v, got: %v", c.expectErr, err)
				return
			}
			if err := newReq.ValidateAll(); (err != nil) != c.expectErr {
				t.Errorf("ValidateAll() expect error: %v, got: %v", c.expectErr, err)
				return
			}
		})
	}
}
