syntax = "proto3";

package anno.v1;

import "anno/v1/type_label.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anno/v1;anno";
option java_multiple_files = true;
option java_package = "anno.v1";

message AnnoCommentReason {
  // [DEPRECATED]
  enum Enum {
    unspecified = 0;
    undesired_label = 1; // 多标
    missing_label = 2;   // 漏标
    wrong_label = 3;     // 错标（标签错误）
    wrong_attr = 4;      // 错标（属性错误）
    // wrong_value = 5;     // 错标（值错误）

    // // the comment receiver disagrees with the commenter
    // disagree = 100;

    other = 100;         // 其他问题
    uncentered = 101;    // 未居中
    poor_fit = 102;      // 未贴合
    illogical_imagination = 103; // 脑补不合理
  }

  message Reasons {
    option (openapi.v3.schema) = {
      required: ["class", "reasons"]
    };

    // one of unwanted（多标）, missed（漏标）, flawed（错标）, other（其他）
    string class = 1;
    // reason list in the class
    repeated string reasons = 2;
  }
}

message Lotontologies {
  option (openapi.v3.schema) = {
    required: ["groups", "attrs"]
  };

  message Group {
    option (openapi.v3.schema) = {
      required: ["name", "labels"]
    };
  
    string name = 1;
    // annotation scope
    repeated Label labels = 2;
  }

  // definitions for all attributes
  map<string, Attr> attrs = 1;
  // global attribute names for an element(frame)
  AttrRefList elem_attrs = 2;
  // global attribute names for a rawdata(image/file)
  AttrRefList rawdata_attrs = 3;
  // when there are mutiple groups, the lot will be splitted according to groups
  repeated Group groups = 4;
  // global attribute names for a job
  AttrRefList job_attrs = 5;
}

// Lotphase describes an exeuction phase in a lot
message Lotphase {
  option (openapi.v3.schema) = {
    required: ["number", "name", "type", "editable", "sample_percent", "timeout", "execteams"]
  };

  message Type {
    enum Enum {
      unspecified = 0;
      label = 1;
      review = 2;
    }
  }

  message Quota {
    // minimum job quota in percentage, [0, 100];
    // if min is not 0 and min is equal to max, means this team needs to finish min% of total jobs;
    int32 min = 1 [(validate.rules).int32 = {gte: 0, lte: 100}];
    // maximum job quota in percentage, [0, 100];
    // max must be explicitly set and we do not treat 0 specially.
    int32 max = 2 [(validate.rules).int32 = {gte: 0, lte: 100}];
  }

  message Execteam {
    option (openapi.v3.schema) = {
      required: ["execteam"]
    };

    // execution team-uid
    string execteam = 1;
    Quota quota = 2;
    // [OUTPUT] the number of jobs that this team has claimed (not percentage)
    int32 claimed_jobs = 3;
  }

  // phase number, start from 1
  int32 number = 1;
  // name of this phase, e.g. label/review-1/review-2/acceptance/..., 标注/审核1/审核2/验收/...
  string name = 2;
  // type of this phase
  Type.Enum type = 3 [(validate.rules).enum = {defined_only: true, not_in: [0]}];
  // can reviewer edit the annotations
  bool editable = 4;
  // percent of the annotations to be reviewed
  float sample_percent = 5;

  // // [DEPRECATED] execution team-uid in this stage; if it is "ai", it will be executed by the AI bot.
  // string execteam = 6;
  reserved 6;

  // minimum skill level required for executor eligible for this lot phase
  int32 min_skill_level = 7;
  // assignees should finish their assignments within this number of seconds
  int32 timeout = 8;
  // whether to merge the annotation results of splitted jobs after this phase
  bool merge = 9;
  // support multiple execution team-uids in this phase;
  repeated Execteam execteams = 10;

  message ClaimPolicy {
    enum Enum {
      unspecified = 0;
      only_same_team = 1; // executors only claim the jobs done by the same team
      only_other_teams = 2; // executors only claim the jobs done by other teams
    }
  }

  ClaimPolicy.Enum claim_policy = 11;
}

//////////////////////////
// Output configurations
//////////////////////////

message DataConverter {
  option (openapi.v3.schema) = {
    required: ["runtime", "uri"]
  };

  // conversion script runtime; default is python3.10
  string runtime = 1;
  // conversion script URI
  string uri = 2;
}

message OutConfig {
  option (openapi.v3.schema) = {
    required: ["exporter", "encoder", "layout", "style"]
  };

  message Encoder {
    enum Enum {
      json = 0;
      // pbtext = 2;
    }
  }

  message Exporter {
    option (openapi.v3.schema) = {
      required: ["name", "config"]
    };

    // exporter name
    string name = 1;
    // exporter specific configuration
    string config = 2;
    // if to data uid
    bool include_data_uid = 3;
  }

  // exporter configuration
  Exporter exporter = 1;
  // encoding type
  Encoder.Enum encoder = 2 [(validate.rules).enum = {defined_only: true}];
  // files layout
  string layout = 3;
  // file style
  string style = 4;
  // converter is a piece of script to convert the annos from platform format to customer format
  DataConverter converter = 5;

  // // timely: 完成一帧传一帧 / entirety: 任务完成之后整体上传
  // string exporting_style = 2;
}
