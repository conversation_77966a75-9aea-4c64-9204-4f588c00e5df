// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: iam/v1/type.proto

package iam

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Team with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Team) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Team with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TeamMultiError, or nil if none found.
func (m *Team) ValidateAll() error {
	return m.validate(true)
}

func (m *Team) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for Desc

	// no validation rules for Avatar

	if _, ok := Team_Type_Enum_name[int32(m.GetType())]; !ok {
		err := TeamValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Province

	// no validation rules for City

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TeamValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TeamValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TeamValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TeamMultiError(errors)
	}

	return nil
}

// TeamMultiError is an error wrapping multiple validation errors returned by
// Team.ValidateAll() if the designated constraints aren't met.
type TeamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TeamMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TeamMultiError) AllErrors() []error { return m }

// TeamValidationError is the validation error returned by Team.Validate if the
// designated constraints aren't met.
type TeamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TeamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TeamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TeamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TeamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TeamValidationError) ErrorName() string { return "TeamValidationError" }

// Error satisfies the builtin error interface
func (e TeamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTeam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TeamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TeamValidationError{}

// Validate checks the field values on User with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *User) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on User with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in UserMultiError, or nil if none found.
func (m *User) ValidateAll() error {
	return m.validate(true)
}

func (m *User) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for Phone

	// no validation rules for Email

	// no validation rules for Avatar

	// no validation rules for Role

	if _, ok := User_Gender_Enum_name[int32(m.GetGender())]; !ok {
		err := UserValidationError{
			field:  "Gender",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Birthday

	// no validation rules for Province

	// no validation rules for City

	// no validation rules for OrgUid

	if _, ok := Team_Type_Enum_name[int32(m.GetOrgType())]; !ok {
		err := UserValidationError{
			field:  "OrgType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Imperfect

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UserMultiError(errors)
	}

	return nil
}

// UserMultiError is an error wrapping multiple validation errors returned by
// User.ValidateAll() if the designated constraints aren't met.
type UserMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserMultiError) AllErrors() []error { return m }

// UserValidationError is the validation error returned by User.Validate if the
// designated constraints aren't met.
type UserValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserValidationError) ErrorName() string { return "UserValidationError" }

// Error satisfies the builtin error interface
func (e UserValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUser.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserValidationError{}

// Validate checks the field values on UserContext with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserContext with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserContextMultiError, or
// nil if none found.
func (m *UserContext) ValidateAll() error {
	return m.validate(true)
}

func (m *UserContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserContextValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserContextValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserContextValidationError{
				field:  "User",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAssumeBy()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserContextValidationError{
					field:  "AssumeBy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserContextValidationError{
					field:  "AssumeBy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAssumeBy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserContextValidationError{
				field:  "AssumeBy",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UserContextMultiError(errors)
	}

	return nil
}

// UserContextMultiError is an error wrapping multiple validation errors
// returned by UserContext.ValidateAll() if the designated constraints aren't met.
type UserContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserContextMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserContextMultiError) AllErrors() []error { return m }

// UserContextValidationError is the validation error returned by
// UserContext.Validate if the designated constraints aren't met.
type UserContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserContextValidationError) ErrorName() string { return "UserContextValidationError" }

// Error satisfies the builtin error interface
func (e UserContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserContextValidationError{}

// Validate checks the field values on BaseUser with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BaseUser) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BaseUser with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BaseUserMultiError, or nil
// if none found.
func (m *BaseUser) ValidateAll() error {
	return m.validate(true)
}

func (m *BaseUser) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for Avatar

	if len(errors) > 0 {
		return BaseUserMultiError(errors)
	}

	return nil
}

// BaseUserMultiError is an error wrapping multiple validation errors returned
// by BaseUser.ValidateAll() if the designated constraints aren't met.
type BaseUserMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BaseUserMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BaseUserMultiError) AllErrors() []error { return m }

// BaseUserValidationError is the validation error returned by
// BaseUser.Validate if the designated constraints aren't met.
type BaseUserValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BaseUserValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BaseUserValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BaseUserValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BaseUserValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BaseUserValidationError) ErrorName() string { return "BaseUserValidationError" }

// Error satisfies the builtin error interface
func (e BaseUserValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBaseUser.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BaseUserValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BaseUserValidationError{}

// Validate checks the field values on IDType with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IDType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IDType with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in IDTypeMultiError, or nil if none found.
func (m *IDType) ValidateAll() error {
	return m.validate(true)
}

func (m *IDType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return IDTypeMultiError(errors)
	}

	return nil
}

// IDTypeMultiError is an error wrapping multiple validation errors returned by
// IDType.ValidateAll() if the designated constraints aren't met.
type IDTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IDTypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IDTypeMultiError) AllErrors() []error { return m }

// IDTypeValidationError is the validation error returned by IDType.Validate if
// the designated constraints aren't met.
type IDTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IDTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IDTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IDTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IDTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IDTypeValidationError) ErrorName() string { return "IDTypeValidationError" }

// Error satisfies the builtin error interface
func (e IDTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIDType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IDTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IDTypeValidationError{}

// Validate checks the field values on Feperm with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Feperm) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Feperm with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in FepermMultiError, or nil if none found.
func (m *Feperm) ValidateAll() error {
	return m.validate(true)
}

func (m *Feperm) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return FepermMultiError(errors)
	}

	return nil
}

// FepermMultiError is an error wrapping multiple validation errors returned by
// Feperm.ValidateAll() if the designated constraints aren't met.
type FepermMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FepermMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FepermMultiError) AllErrors() []error { return m }

// FepermValidationError is the validation error returned by Feperm.Validate if
// the designated constraints aren't met.
type FepermValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FepermValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FepermValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FepermValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FepermValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FepermValidationError) ErrorName() string { return "FepermValidationError" }

// Error satisfies the builtin error interface
func (e FepermValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeperm.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FepermValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FepermValidationError{}

// Validate checks the field values on EditAction with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EditAction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EditAction with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EditActionMultiError, or
// nil if none found.
func (m *EditAction) ValidateAll() error {
	return m.validate(true)
}

func (m *EditAction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return EditActionMultiError(errors)
	}

	return nil
}

// EditActionMultiError is an error wrapping multiple validation errors
// returned by EditAction.ValidateAll() if the designated constraints aren't met.
type EditActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EditActionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EditActionMultiError) AllErrors() []error { return m }

// EditActionValidationError is the validation error returned by
// EditAction.Validate if the designated constraints aren't met.
type EditActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EditActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EditActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EditActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EditActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EditActionValidationError) ErrorName() string { return "EditActionValidationError" }

// Error satisfies the builtin error interface
func (e EditActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEditAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EditActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EditActionValidationError{}

// Validate checks the field values on Team_Type with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Team_Type) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Team_Type with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Team_TypeMultiError, or nil
// if none found.
func (m *Team_Type) ValidateAll() error {
	return m.validate(true)
}

func (m *Team_Type) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Team_TypeMultiError(errors)
	}

	return nil
}

// Team_TypeMultiError is an error wrapping multiple validation errors returned
// by Team_Type.ValidateAll() if the designated constraints aren't met.
type Team_TypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Team_TypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Team_TypeMultiError) AllErrors() []error { return m }

// Team_TypeValidationError is the validation error returned by
// Team_Type.Validate if the designated constraints aren't met.
type Team_TypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Team_TypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Team_TypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Team_TypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Team_TypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Team_TypeValidationError) ErrorName() string { return "Team_TypeValidationError" }

// Error satisfies the builtin error interface
func (e Team_TypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTeam_Type.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Team_TypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Team_TypeValidationError{}

// Validate checks the field values on User_Gender with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *User_Gender) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on User_Gender with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in User_GenderMultiError, or
// nil if none found.
func (m *User_Gender) ValidateAll() error {
	return m.validate(true)
}

func (m *User_Gender) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return User_GenderMultiError(errors)
	}

	return nil
}

// User_GenderMultiError is an error wrapping multiple validation errors
// returned by User_Gender.ValidateAll() if the designated constraints aren't met.
type User_GenderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m User_GenderMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m User_GenderMultiError) AllErrors() []error { return m }

// User_GenderValidationError is the validation error returned by
// User_Gender.Validate if the designated constraints aren't met.
type User_GenderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e User_GenderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e User_GenderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e User_GenderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e User_GenderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e User_GenderValidationError) ErrorName() string { return "User_GenderValidationError" }

// Error satisfies the builtin error interface
func (e User_GenderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUser_Gender.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = User_GenderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = User_GenderValidationError{}
