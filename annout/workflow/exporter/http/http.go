package http

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	"annout/internal/biz"
	"annout/workflow/exporter/common"
	"annout/workflow/types"

	"github.com/go-resty/resty/v2"
)

// TODO: instead of creating a zipwriter and writing each piece directly to file,
//       saving each piece to file so that it can be resumed by workflow when errors.

const Name = "http"

type Config struct {
	UploadURL string
	Headers   map[string]string
	Method    string
}

func init() {
	common.RegisterGenExporter(Name, newHTTPExporter)
}

type httpExporter struct {
	cfg *Config
}

func newHTTPExporter(scfg string) (common.Exporter, error) {
	cfg := &Config{}
	if err := json.Unmarshal([]byte(scfg), cfg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	return &httpExporter{cfg: cfg}, nil
}

func (o *httpExporter) Export(ctx context.Context, dir string, reaper *biz.Reaper) error {
	file, err := os.Open(types.PackAnnosFile)
	if err != nil {
		return fmt.Errorf("failed to open file to export: %w", err)
	}
	defer os.Remove(types.PackAnnosFile)
	defer file.Close()

	// do upload
	rsp, err := resty.New().R().SetContext(ctx).
		SetContentLength(true).
		SetHeaders(o.cfg.Headers).
		SetBody(file).
		Put(o.cfg.UploadURL)
	if err == nil && rsp.IsError() {
		err = fmt.Errorf("HTTP server responds %v: %s", rsp.Status(), rsp.Body())
	}
	if err != nil {
		return fmt.Errorf("failed to upload file to %v: %w", o.cfg.UploadURL, err)
	}

	return nil
}
