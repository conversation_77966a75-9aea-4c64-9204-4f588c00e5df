package biz

import (
	"time"

	"gitlab.rp.konvery.work/platform/apis/iam/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
)

type Feperms = serial.Slice[*iam.FepermItem]

type Feperm struct {
	ID        int64     `json:"id" gorm:"default:null"`
	Role      string    `json:"role" gorm:"default:null"`
	Perms     Feperms   `json:"perms" gorm:"default:null"`
	CreatedAt time.Time `json:"created_at" gorm:"default:null"`
}

func (o *Feperm) GetID() int64     { return o.ID }
func (o *Feperm) GetUid() string   { return o.Role }
func (o *Feperm) UidField() string { return Feperm_Role }

const FepermTableName = "feperms"

var (
	feperm_             = field.RegObject(&Feperm{})
	FepermUpdatableFlds = field.NewModel(FepermTableName, feperm_, "Perms")

	Feperm_Role  = field.Sname(&feperm_.Role)
	Feperm_Perms = field.Sname(&feperm_.Perms)
)
