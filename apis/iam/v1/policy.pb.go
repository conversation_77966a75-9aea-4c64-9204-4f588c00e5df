// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: iam/v1/policy.proto

package iam

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreatePolicyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Role string `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	// uid of users (IamUser:xxx) or groups(IamGroup:xxx)
	// pattern: "^(IamUser|IamGroup):[\\w-]+(:[\\w-]+)?$"
	Users []string `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
	// the name of the resource to be attached to; pattern: "^([\\w-]+:){1,2}([\\w-]+(.|/))+$"
	Resource string `protobuf:"bytes,3,opt,name=resource,proto3" json:"resource,omitempty"`
}

func (x *CreatePolicyRequest) Reset() {
	*x = CreatePolicyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_policy_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePolicyRequest) ProtoMessage() {}

func (x *CreatePolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_policy_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePolicyRequest.ProtoReflect.Descriptor instead.
func (*CreatePolicyRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_policy_proto_rawDescGZIP(), []int{0}
}

func (x *CreatePolicyRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *CreatePolicyRequest) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *CreatePolicyRequest) GetResource() string {
	if x != nil {
		return x.Resource
	}
	return ""
}

type Policy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Role string `protobuf:"bytes,2,opt,name=role,proto3" json:"role,omitempty"`
	// uid of users (IamUser:xxx) or groups(IamGroup:xxx)
	Users []string `protobuf:"bytes,3,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *Policy) Reset() {
	*x = Policy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_policy_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Policy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Policy) ProtoMessage() {}

func (x *Policy) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_policy_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Policy.ProtoReflect.Descriptor instead.
func (*Policy) Descriptor() ([]byte, []int) {
	return file_iam_v1_policy_proto_rawDescGZIP(), []int{1}
}

func (x *Policy) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Policy) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *Policy) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

type UpdatePolicyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// uid of users (IamUser:xxx) or groups(IamGroup:xxx)
	Users []string `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *UpdatePolicyRequest) Reset() {
	*x = UpdatePolicyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_policy_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePolicyRequest) ProtoMessage() {}

func (x *UpdatePolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_policy_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePolicyRequest.ProtoReflect.Descriptor instead.
func (*UpdatePolicyRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_policy_proto_rawDescGZIP(), []int{2}
}

func (x *UpdatePolicyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdatePolicyRequest) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

type DeletePolicyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeletePolicyRequest) Reset() {
	*x = DeletePolicyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_policy_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePolicyRequest) ProtoMessage() {}

func (x *DeletePolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_policy_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePolicyRequest.ProtoReflect.Descriptor instead.
func (*DeletePolicyRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_policy_proto_rawDescGZIP(), []int{3}
}

func (x *DeletePolicyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetPolicyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetPolicyRequest) Reset() {
	*x = GetPolicyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_policy_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPolicyRequest) ProtoMessage() {}

func (x *GetPolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_policy_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPolicyRequest.ProtoReflect.Descriptor instead.
func (*GetPolicyRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_policy_proto_rawDescGZIP(), []int{4}
}

func (x *GetPolicyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetAttachedPoliciesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// resource name in the format: type:(uid|name)
	// type: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// get attached policy binding the role
	Role string `protobuf:"bytes,2,opt,name=role,proto3" json:"role,omitempty"`
}

func (x *GetAttachedPoliciesRequest) Reset() {
	*x = GetAttachedPoliciesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_policy_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAttachedPoliciesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAttachedPoliciesRequest) ProtoMessage() {}

func (x *GetAttachedPoliciesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_policy_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAttachedPoliciesRequest.ProtoReflect.Descriptor instead.
func (*GetAttachedPoliciesRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_policy_proto_rawDescGZIP(), []int{5}
}

func (x *GetAttachedPoliciesRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetAttachedPoliciesRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

type GetAttachedPoliciesReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PolicyNames []string `protobuf:"bytes,1,rep,name=policy_names,json=policyNames,proto3" json:"policy_names,omitempty"`
}

func (x *GetAttachedPoliciesReply) Reset() {
	*x = GetAttachedPoliciesReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_policy_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAttachedPoliciesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAttachedPoliciesReply) ProtoMessage() {}

func (x *GetAttachedPoliciesReply) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_policy_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAttachedPoliciesReply.ProtoReflect.Descriptor instead.
func (*GetAttachedPoliciesReply) Descriptor() ([]byte, []int) {
	return file_iam_v1_policy_proto_rawDescGZIP(), []int{6}
}

func (x *GetAttachedPoliciesReply) GetPolicyNames() []string {
	if x != nil {
		return x.PolicyNames
	}
	return nil
}

type CreateResourceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// resource name in the format: type:(uid|name)
	// type: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// uid of users (IamUser:xxx) or groups(IamGroup:xxx) to be granted the owner role
	// pattern: "^(IamUser|IamGroup):[\\w-]+(:[\\w-]+)?$"
	Owners []string `protobuf:"bytes,2,rep,name=owners,proto3" json:"owners,omitempty"`
	// subjects, in the format: type:(uid|name), of the resource's parents relation
	Parents []string `protobuf:"bytes,3,rep,name=parents,proto3" json:"parents,omitempty"`
}

func (x *CreateResourceRequest) Reset() {
	*x = CreateResourceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_policy_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateResourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateResourceRequest) ProtoMessage() {}

func (x *CreateResourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_policy_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateResourceRequest.ProtoReflect.Descriptor instead.
func (*CreateResourceRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_policy_proto_rawDescGZIP(), []int{7}
}

func (x *CreateResourceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateResourceRequest) GetOwners() []string {
	if x != nil {
		return x.Owners
	}
	return nil
}

func (x *CreateResourceRequest) GetParents() []string {
	if x != nil {
		return x.Parents
	}
	return nil
}

type DeleteResourceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// resource name in the format: type:(uid|name)
	// type: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteResourceRequest) Reset() {
	*x = DeleteResourceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_policy_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteResourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteResourceRequest) ProtoMessage() {}

func (x *DeleteResourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_policy_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteResourceRequest.ProtoReflect.Descriptor instead.
func (*DeleteResourceRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_policy_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteResourceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type RevokeUsersRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// resource name in the format: type:(uid|name)
	// type: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
	Resource string `protobuf:"bytes,1,opt,name=resource,proto3" json:"resource,omitempty"`
	// role
	Role string `protobuf:"bytes,2,opt,name=role,proto3" json:"role,omitempty"`
	// user list
	Users []string `protobuf:"bytes,3,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *RevokeUsersRoleRequest) Reset() {
	*x = RevokeUsersRoleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_policy_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RevokeUsersRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeUsersRoleRequest) ProtoMessage() {}

func (x *RevokeUsersRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_policy_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeUsersRoleRequest.ProtoReflect.Descriptor instead.
func (*RevokeUsersRoleRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_policy_proto_rawDescGZIP(), []int{9}
}

func (x *RevokeUsersRoleRequest) GetResource() string {
	if x != nil {
		return x.Resource
	}
	return ""
}

func (x *RevokeUsersRoleRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *RevokeUsersRoleRequest) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

var File_iam_v1_policy_proto protoreflect.FileDescriptor

var file_iam_v1_policy_proto_rawDesc = []byte{
	0x0a, 0x13, 0x69, 0x61, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xca, 0x01, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x3d, 0x0a, 0x05, 0x75,
	0x73, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x27, 0xfa, 0x42, 0x24, 0x92,
	0x01, 0x21, 0x22, 0x1f, 0x72, 0x1d, 0x32, 0x1b, 0x5e, 0x28, 0x49, 0x61, 0x6d, 0x55, 0x73, 0x65,
	0x72, 0x7c, 0x49, 0x61, 0x6d, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x29, 0x3a, 0x5b, 0x5c, 0x77, 0x2d,
	0x5d, 0x2b, 0x24, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12, 0x41, 0x0a, 0x08, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x25, 0xfa, 0x42,
	0x22, 0x72, 0x20, 0x32, 0x1e, 0x5e, 0x28, 0x5b, 0x5c, 0x77, 0x2d, 0x5d, 0x2b, 0x3a, 0x29, 0x7b,
	0x31, 0x2c, 0x32, 0x7d, 0x28, 0x5b, 0x5c, 0x77, 0x2d, 0x5d, 0x2b, 0x28, 0x2e, 0x7c, 0x2f, 0x29,
	0x29, 0x2b, 0x24, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x3a, 0x1d, 0xba,
	0x47, 0x1a, 0xba, 0x01, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0xba, 0x01, 0x05, 0x75, 0x73, 0x65, 0x72,
	0x73, 0xba, 0x01, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x61, 0x0a, 0x06,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x75,
	0x73, 0x65, 0x72, 0x73, 0x3a, 0x19, 0xba, 0x47, 0x16, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0xba, 0x01, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0xba, 0x01, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22,
	0x7c, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x05, 0x75, 0x73,
	0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x27, 0xfa, 0x42, 0x24, 0x92, 0x01,
	0x21, 0x22, 0x1f, 0x72, 0x1d, 0x32, 0x1b, 0x5e, 0x28, 0x49, 0x61, 0x6d, 0x55, 0x73, 0x65, 0x72,
	0x7c, 0x49, 0x61, 0x6d, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x29, 0x3a, 0x5b, 0x5c, 0x77, 0x2d, 0x5d,
	0x2b, 0x24, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x3a, 0x12, 0xba, 0x47, 0x0f, 0xba, 0x01,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22, 0x29, 0x0a,
	0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x26, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0x77, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x65, 0x64, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x39,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x25, 0xfa, 0x42,
	0x22, 0x72, 0x20, 0x32, 0x1e, 0x5e, 0x28, 0x5b, 0x5c, 0x77, 0x2d, 0x5d, 0x2b, 0x3a, 0x29, 0x7b,
	0x31, 0x2c, 0x32, 0x7d, 0x28, 0x5b, 0x5c, 0x77, 0x2d, 0x5d, 0x2b, 0x28, 0x2e, 0x7c, 0x2f, 0x29,
	0x29, 0x2b, 0x24, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x3a, 0x0a, 0xba,
	0x47, 0x07, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x51, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x65, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x3a, 0x12, 0xba, 0x47, 0x0f, 0xba, 0x01, 0x0c,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0x72, 0x0a, 0x15,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x73, 0x3a, 0x13, 0xba, 0x47, 0x10,
	0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73,
	0x22, 0x5e, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x25, 0xfa, 0x42, 0x22, 0x72, 0x20, 0x32, 0x1e,
	0x5e, 0x28, 0x5b, 0x5c, 0x77, 0x2d, 0x5d, 0x2b, 0x3a, 0x29, 0x7b, 0x31, 0x2c, 0x32, 0x7d, 0x28,
	0x5b, 0x5c, 0x77, 0x2d, 0x5d, 0x2b, 0x28, 0x2e, 0x7c, 0x2f, 0x29, 0x29, 0x2b, 0x24, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x3a, 0x0a, 0xba, 0x47, 0x07, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0xa4, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x41, 0x0a, 0x08, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x25, 0xfa,
	0x42, 0x22, 0x72, 0x20, 0x32, 0x1e, 0x5e, 0x28, 0x5b, 0x5c, 0x77, 0x2d, 0x5d, 0x2b, 0x3a, 0x29,
	0x7b, 0x31, 0x2c, 0x32, 0x7d, 0x28, 0x5b, 0x5c, 0x77, 0x2d, 0x5d, 0x2b, 0x28, 0x2e, 0x7c, 0x2f,
	0x29, 0x29, 0x2b, 0x24, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f,
	0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x3a, 0x1d, 0xba, 0x47, 0x1a, 0xba, 0x01, 0x08,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0xba, 0x01, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0xba,
	0x01, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x32, 0xdd, 0x05, 0x0a, 0x08, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x69, 0x65, 0x73, 0x12, 0x54, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x12, 0x1b, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x0e, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x3a, 0x01, 0x2a, 0x22, 0x0c, 0x2f, 0x76,
	0x31, 0x2f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x12, 0x63, 0x0a, 0x0c, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x1b, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x3a, 0x01, 0x2a, 0x32, 0x13, 0x2f, 0x76, 0x31, 0x2f,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12,
	0x60, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12,
	0x1b, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x2a, 0x13, 0x2f, 0x76,
	0x31, 0x2f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x12, 0x52, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x18,
	0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0e, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15,
	0x12, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x2f, 0x7b,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x82, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x65, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x12, 0x22, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x65, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x20, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x65, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x12, 0x1d, 0x2f, 0x76, 0x31,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x12, 0x47, 0x0a, 0x0e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1d, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x47, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1d, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x49, 0x0a, 0x0f,
	0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x6f, 0x6c, 0x65, 0x12,
	0x1e, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x3b, 0x0a, 0x06, 0x69, 0x61, 0x6d, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x76, 0x31,
	0x3b, 0x69, 0x61, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_iam_v1_policy_proto_rawDescOnce sync.Once
	file_iam_v1_policy_proto_rawDescData = file_iam_v1_policy_proto_rawDesc
)

func file_iam_v1_policy_proto_rawDescGZIP() []byte {
	file_iam_v1_policy_proto_rawDescOnce.Do(func() {
		file_iam_v1_policy_proto_rawDescData = protoimpl.X.CompressGZIP(file_iam_v1_policy_proto_rawDescData)
	})
	return file_iam_v1_policy_proto_rawDescData
}

var file_iam_v1_policy_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_iam_v1_policy_proto_goTypes = []interface{}{
	(*CreatePolicyRequest)(nil),        // 0: iam.v1.CreatePolicyRequest
	(*Policy)(nil),                     // 1: iam.v1.Policy
	(*UpdatePolicyRequest)(nil),        // 2: iam.v1.UpdatePolicyRequest
	(*DeletePolicyRequest)(nil),        // 3: iam.v1.DeletePolicyRequest
	(*GetPolicyRequest)(nil),           // 4: iam.v1.GetPolicyRequest
	(*GetAttachedPoliciesRequest)(nil), // 5: iam.v1.GetAttachedPoliciesRequest
	(*GetAttachedPoliciesReply)(nil),   // 6: iam.v1.GetAttachedPoliciesReply
	(*CreateResourceRequest)(nil),      // 7: iam.v1.CreateResourceRequest
	(*DeleteResourceRequest)(nil),      // 8: iam.v1.DeleteResourceRequest
	(*RevokeUsersRoleRequest)(nil),     // 9: iam.v1.RevokeUsersRoleRequest
	(*emptypb.Empty)(nil),              // 10: google.protobuf.Empty
}
var file_iam_v1_policy_proto_depIdxs = []int32{
	0,  // 0: iam.v1.Policies.CreatePolicy:input_type -> iam.v1.CreatePolicyRequest
	2,  // 1: iam.v1.Policies.UpdatePolicy:input_type -> iam.v1.UpdatePolicyRequest
	3,  // 2: iam.v1.Policies.DeletePolicy:input_type -> iam.v1.DeletePolicyRequest
	4,  // 3: iam.v1.Policies.GetPolicy:input_type -> iam.v1.GetPolicyRequest
	5,  // 4: iam.v1.Policies.GetAttachedPolicies:input_type -> iam.v1.GetAttachedPoliciesRequest
	7,  // 5: iam.v1.Policies.CreateResource:input_type -> iam.v1.CreateResourceRequest
	8,  // 6: iam.v1.Policies.DeleteResource:input_type -> iam.v1.DeleteResourceRequest
	9,  // 7: iam.v1.Policies.RevokeUsersRole:input_type -> iam.v1.RevokeUsersRoleRequest
	1,  // 8: iam.v1.Policies.CreatePolicy:output_type -> iam.v1.Policy
	10, // 9: iam.v1.Policies.UpdatePolicy:output_type -> google.protobuf.Empty
	10, // 10: iam.v1.Policies.DeletePolicy:output_type -> google.protobuf.Empty
	1,  // 11: iam.v1.Policies.GetPolicy:output_type -> iam.v1.Policy
	6,  // 12: iam.v1.Policies.GetAttachedPolicies:output_type -> iam.v1.GetAttachedPoliciesReply
	10, // 13: iam.v1.Policies.CreateResource:output_type -> google.protobuf.Empty
	10, // 14: iam.v1.Policies.DeleteResource:output_type -> google.protobuf.Empty
	10, // 15: iam.v1.Policies.RevokeUsersRole:output_type -> google.protobuf.Empty
	8,  // [8:16] is the sub-list for method output_type
	0,  // [0:8] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_iam_v1_policy_proto_init() }
func file_iam_v1_policy_proto_init() {
	if File_iam_v1_policy_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_iam_v1_policy_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePolicyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_policy_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Policy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_policy_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePolicyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_policy_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePolicyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_policy_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPolicyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_policy_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAttachedPoliciesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_policy_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAttachedPoliciesReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_policy_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateResourceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_policy_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteResourceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_policy_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RevokeUsersRoleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_iam_v1_policy_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_iam_v1_policy_proto_goTypes,
		DependencyIndexes: file_iam_v1_policy_proto_depIdxs,
		MessageInfos:      file_iam_v1_policy_proto_msgTypes,
	}.Build()
	File_iam_v1_policy_proto = out.File
	file_iam_v1_policy_proto_rawDesc = nil
	file_iam_v1_policy_proto_goTypes = nil
	file_iam_v1_policy_proto_depIdxs = nil
}
