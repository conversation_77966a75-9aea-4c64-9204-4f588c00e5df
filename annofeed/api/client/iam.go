package client

import (
	"annofeed/internal/conf"
	"gitlab.rp.konvery.work/platform/apis/client"
	"gitlab.rp.konvery.work/platform/apis/iam/v1"
)

type (
	User           = iam.User
	BaseUser       = iam.BaseUser
	Team           = iam.Team
	UsersClient    = iam.UsersClient
	TeamsClient    = iam.TeamsClient
	PoliciesClient = iam.PoliciesClient
)

func initIAM(c *conf.Bootstrap) {
	client.InitIAM(&client.Service{Addr: c.Rpc.Iam.Addr})
}

var (
	GetUser              = client.GetUser
	GetMe                = client.GetMe
	IsAllowed            = client.IsAllowed
	CreateAccessPolicies = client.CreateAccessPolicies
	DeleteAccessPolicies = client.DeleteAccessPolicies
)
