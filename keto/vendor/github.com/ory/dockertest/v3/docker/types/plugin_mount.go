package types

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

// PluginMount plugin mount
// swagger:model PluginMount
type PluginMount struct {

	// description
	// Required: true
	Description string `json:"Description"`

	// destination
	// Required: true
	Destination string `json:"Destination"`

	// name
	// Required: true
	Name string `json:"Name"`

	// options
	// Required: true
	Options []string `json:"Options"`

	// settable
	// Required: true
	Settable []string `json:"Settable"`

	// source
	// Required: true
	Source *string `json:"Source"`

	// type
	// Required: true
	Type string `json:"Type"`
}
