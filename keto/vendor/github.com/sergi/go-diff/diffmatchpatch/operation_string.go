// Code generated by "stringer -type=Operation -trimprefix=Diff"; DO NOT EDIT.

package diffmatchpatch

import "fmt"

const _Operation_name = "DeleteEqualInsert"

var _Operation_index = [...]uint8{0, 6, 11, 17}

func (i Operation) String() string {
	i -= -1
	if i < 0 || i >= Operation(len(_Operation_index)-1) {
		return fmt.Sprintf("Operation(%d)", i+-1)
	}
	return _Operation_name[_Operation_index[i]:_Operation_index[i+1]]
}
