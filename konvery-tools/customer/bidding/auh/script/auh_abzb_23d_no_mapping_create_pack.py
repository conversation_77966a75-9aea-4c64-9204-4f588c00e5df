import os
import sys
import shutil
sys.path.append('.')
from customer.common import tools


cams = ['front', 'leftfront', 'rightfront']


def run(input_dir, batch_size):
    if isinstance(batch_size, str):
        if batch_size.isdecimal():
            batch_size = int(batch_size)
        else:
            raise ValueError(f'Batch size must be an integer: {batch_size}')
    else:
        batch_size = 40
    for sub_dir in tools.listdir(input_dir, full_path=True, sort=True):
        for lidar_name in ['lidar', 'pcd']:
            lidar_dir = os.path.join(sub_dir, lidar_name)
            if os.path.isdir(lidar_dir):
                break
        else:
            raise ValueError(f'Lidar directory does not exist')
        lidar_files = tools.listdir(lidar_dir, full_path=True, sort=True)

        for i in range(0, len(lidar_files), batch_size):
            batch_lidar_files = lidar_files[i:i + batch_size]
            batch_dir = os.path.join(input_dir, f'{os.path.basename(sub_dir)}_batch_{i // batch_size:04d}')
            os.mkdir(batch_dir)
            batch_lidar_dir = os.path.join(batch_dir, 'lidar')
            os.mkdir(batch_lidar_dir)
            batch_camera_dir = os.path.join(batch_dir, 'camera')
            os.mkdir(batch_camera_dir)
            for cam in cams:
                os.mkdir(os.path.join(batch_camera_dir, cam))
            for lidar_file in batch_lidar_files:
                shutil.move(lidar_file, os.path.join(batch_lidar_dir, os.path.basename(lidar_file)))
                for cam in cams:
                    src = os.path.join(sub_dir, cam, os.path.basename(lidar_file).replace('.pcd', '.png'))
                    dst = os.path.join(batch_camera_dir, cam, os.path.basename(lidar_file).replace('.pcd', '.png'))
                    shutil.move(src, dst)
        shutil.rmtree(sub_dir)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.txt)
