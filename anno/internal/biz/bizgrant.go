package biz

import (
	"context"
	"fmt"
	"time"

	"anno/api/client"
	"anno/internal/mq"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/log"

	"gorm.io/gorm"
)

type Bizgrant struct {
	ID         int64     `json:"id" gorm:"default:null"`
	GrantorUid string    `json:"grantor_id" gorm:"default:null"`
	GranteeUid string    `json:"grantee_id" gorm:"default:null"`
	OrgUid     string    `json:"org_id" gorm:"default:null"`
	CreatedAt  time.Time `json:"created_at" gorm:"default:null"`

	Biz string `json:"biz,omitempty" gorm:"-"`
}

const BizgrantTableName = "bizgrants"

type BizgrantSlfd string

func (o BizgrantSlfd) String() string { return string(o) }

func (o BizgrantSlfd) WithTable() string { return BizgrantTableName + "." + string(o) }

var (
	bizgrant_             = field.RegObject(&Bizgrant{})
	BizgrantUpdatableFlds = field.NewModel(BizgrantTableName, bizgrant_)

	BizgrantSfldID         = BizgrantSlfd(field.Sname(&bizgrant_.ID))
	BizgrantSfldGrantorUid = BizgrantSlfd(field.Sname(&bizgrant_.GrantorUid))
	BizgrantSfldGranteeUid = BizgrantSlfd(field.Sname(&bizgrant_.GranteeUid))
	BizgrantSfldOrgUid     = BizgrantSlfd(field.Sname(&bizgrant_.OrgUid))
)

type BizgrantFilter struct {
	GrantorUid string
	GranteeUid string
	OrgUid     string
}

type BizgrantsRepo interface {
	DoTx(ctx context.Context, fn func(ctx context.Context, tx Tx) error) (err error)

	Create(context.Context, *Bizgrant) (*Bizgrant, error)
	DeleteByFilter(context.Context, *BizgrantFilter) ([]int64, error)
	List(context.Context, *BizgrantFilter, Pager) (grants []*Bizgrant, nextPageToken string, err error)
}

type BizgrantsBiz struct {
	repo BizgrantsRepo
	log  *log.Helper
}

func NewBizgrantsBiz(repo BizgrantsRepo, logger log.Logger) *BizgrantsBiz {
	return &BizgrantsBiz{repo: repo, log: log.NewHelper(logger)}
}

func (o *BizgrantsBiz) Create(ctx context.Context, p *Bizgrant) (g *Bizgrant, err error) {
	err = o.repo.DoTx(ctx, func(ctx context.Context, tx *gorm.DB) error {
		g, err = o.repo.Create(ctx, p)
		if err != nil {
			return fmt.Errorf("failed to create Bizgrant: %w", err)
		}

		// grant anno permissions to KAM
		resource := client.GroupScope(p.OrgUid)
		users := []string{client.UserScope(p.GranteeUid)}
		_, err = client.CreatePolicy(ctx, resource, RoleAnnoKAM, users)
		if err != nil {
			return fmt.Errorf("failed to create access policy: %w", err)
		}
		o.log.Info(ctx, "grant KAM access on org to user", "org", resource, "user", users[0])

		if err := mq.PublishEvt(ctx, EvtTypeAnnoBizgrant, EvtSubtypeCreate, g); err != nil {
			return fmt.Errorf("failed to publish bizgrant create event: %w", err)
		}

		return nil
	})
	return
}

func (o *BizgrantsBiz) DeleteByFilter(ctx context.Context, f *BizgrantFilter) error {
	err := client.RevokeUsersRole(ctx, client.ScopeClsGroup, f.OrgUid, RoleAnnoKAM, client.UserScope(f.GranteeUid))
	if err != nil {
		return fmt.Errorf("failed to revoke user role: %w", err)
	}

	return o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		bizgrantIDs, err := o.repo.DeleteByFilter(ctx, f)
		if err != nil {
			return fmt.Errorf("failed to delete Bizgrant: %w", err)
		}
		if len(bizgrantIDs) == 0 {
			return nil
		}

		if err := mq.PublishEvt(ctx, EvtTypeAnnoBizgrant, EvtSubtypeDelete, bizgrantIDs); err != nil {
			return fmt.Errorf("failed to publish bizgrant delete event: %w", err)
		}

		return nil
	})
}

func (o *BizgrantsBiz) List(ctx context.Context, f *BizgrantFilter, p Pager) (
	grants []*Bizgrant, nextPageToken string, err error) {
	return o.repo.List(ctx, f, p)
}
