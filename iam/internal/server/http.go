package server

import (
	"strings"

	"iam/internal/biz"
	"iam/internal/conf"
	"iam/internal/server/middleware"
	"iam/internal/service"

	"github.com/go-kratos/kratos/v2/encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/gorilla/handlers"
	"gitlab.rp.konvery.work/platform/apis/iam/v1"
)

// NewHTTPServer new a HTTP server.
func NewHTTPServer(c *conf.Server,
	cfgsvc *service.ConfigsService,
	perm *service.PermsService,
	policy *service.PoliciesService,
	role *service.RolesService,
	team *service.TeamsService,
	user *service.UsersService,
	userBiz *biz.UsersBiz,
	grant *service.BizgrantsService,
	logger log.Logger) *http.Server {
	mws := getMiddlewares(logger,
		middleware.Auth(c.<PERSON>, userBiz),
		middleware.LoadUser(userBiz, noauthFilters),
	)
	var opts = []http.ServerOption{
		http.Middleware(mws...),
	}
	if len(c.Cors<PERSON>) > 0 {
		opts = append(opts, http.Filter(handlers.CORS(
			handlers.AllowCredentials(),
			handlers.AllowedHeaders([]string{"X-Requested-With", "Content-Type", "Authorization"}),
			handlers.AllowedMethods([]string{"GET", "POST", "PUT", "HEAD", "OPTIONS", "DELETE", "PATCH"}),
			handlers.AllowedOrigins(strings.Split(c.CorsOrigins, ",")),
		)))
	}
	opts = append(opts, http.Filter(middleware.SetCookieFilter(logger, c.CookieDomain)))
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}
	json.MarshalOptions.EmitUnpopulated = true
	json.MarshalOptions.UseProtoNames = true
	srv := http.NewServer(opts...)

	handleHealth(srv)
	iam.RegisterConfigsHTTPServer(srv, cfgsvc)
	iam.RegisterUsersHTTPServer(srv, user)
	iam.RegisterTeamsHTTPServer(srv, team)
	iam.RegisterRolesHTTPServer(srv, role)
	iam.RegisterPermsHTTPServer(srv, perm)
	iam.RegisterPoliciesHTTPServer(srv, policy)
	iam.RegisterBizgrantsHTTPServer(srv, grant)
	return srv
}

func handleHealth(srv *http.Server) {
	route := srv.Route("/")
	route.GET("/healthz", func(ctx http.Context) error {
		ctx.String(200, "OK")
		return nil
	})
}
