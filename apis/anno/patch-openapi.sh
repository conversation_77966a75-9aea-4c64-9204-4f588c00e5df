#!/bin/sh

# add security description
whitelist='
Configs_ListErrors
'
for op in $whitelist; do
  sed -i "s/^\( \+\)\(operationId: $op\)$/\1\2\n\1security: []/" openapi.yaml
done
sed -i "s/^paths:$/security:\n    - jwt: []\npaths:/" openapi.yaml
cat << EOF | sed -i "/^components:$/r /dev/stdin" openapi.yaml
    securitySchemes:
        jwt:
            type: http
            scheme: bearer
            bearerFormat: JWT
EOF
