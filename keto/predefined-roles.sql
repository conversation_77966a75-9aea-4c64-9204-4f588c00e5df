-- grep '#perms@' tuples.txt | cut -d# -f1 | cut -d: -f2 | sort -u | grep '\w' | sed "s/\(.*\)/INSERT INTO roles (name, display_name) VALUES ('\1', '\1');/"
INSERT INTO roles (name, display_name) VALUES ('AnnoJob.editor', 'AnnoJob.editor');
INSERT INTO roles (name, display_name) VALUES ('AnnoJob.owner', 'AnnoJob.owner');
INSERT INTO roles (name, display_name) VALUES ('AnnoJob.regulator', 'AnnoJob.regulator');
INSERT INTO roles (name, display_name) VALUES ('AnnoJob.viewer', 'AnnoJob.viewer');
INSERT INTO roles (name, display_name) VALUES ('AnnoLot.editor', 'AnnoLot.editor');
INSERT INTO roles (name, display_name) VALUES ('AnnoLot.executor', 'AnnoLot.executor');
INSERT INTO roles (name, display_name) VALUES ('AnnoLot.owner', 'AnnoLot.owner');
INSERT INTO roles (name, display_name) VALUES ('AnnoLot.viewer', 'AnnoLot.viewer');
INSERT INTO roles (name, display_name) VALUES ('AnnoOrder.editor', 'AnnoOrder.editor');
INSERT INTO roles (name, display_name) VALUES ('AnnoOrder.owner', 'AnnoOrder.owner');
INSERT INTO roles (name, display_name) VALUES ('AnnoOrder.viewer', 'AnnoOrder.viewer');
INSERT INTO roles (name, display_name) VALUES ('AnnofeedData.editor', 'AnnofeedData.editor');
INSERT INTO roles (name, display_name) VALUES ('AnnofeedData.owner', 'AnnofeedData.owner');
INSERT INTO roles (name, display_name) VALUES ('AnnofeedData.viewer', 'AnnofeedData.viewer');
INSERT INTO roles (name, display_name) VALUES ('AnnofeedFile.editor', 'AnnofeedFile.editor');
INSERT INTO roles (name, display_name) VALUES ('AnnofeedFile.owner', 'AnnofeedFile.owner');
INSERT INTO roles (name, display_name) VALUES ('AnnofeedFile.viewer', 'AnnofeedFile.viewer');
INSERT INTO roles (name, display_name) VALUES ('FePage.editor', 'FePage.editor');
INSERT INTO roles (name, display_name) VALUES ('FePage.owner', 'FePage.owner');
INSERT INTO roles (name, display_name) VALUES ('FePage.viewer', 'FePage.viewer');
INSERT INTO roles (name, display_name) VALUES ('IamGroup.editor', 'IamGroup.editor');
INSERT INTO roles (name, display_name) VALUES ('IamGroup.owner', 'IamGroup.owner');
INSERT INTO roles (name, display_name) VALUES ('IamGroup.viewer', 'IamGroup.viewer');
INSERT INTO roles (name, display_name) VALUES ('IamRole.editor', 'IamRole.editor');
INSERT INTO roles (name, display_name) VALUES ('IamRole.owner', 'IamRole.owner');
INSERT INTO roles (name, display_name) VALUES ('IamRole.viewer', 'IamRole.viewer');
INSERT INTO roles (name, display_name) VALUES ('IamUser.editor', 'IamUser.editor');
INSERT INTO roles (name, display_name) VALUES ('IamUser.owner', 'IamUser.owner');
INSERT INTO roles (name, display_name) VALUES ('IamUser.viewer', 'IamUser.viewer');
INSERT INTO roles (name, display_name) VALUES ('admin', 'admin');
INSERT INTO roles (name, display_name) VALUES ('anno.editor', 'anno.editor');
INSERT INTO roles (name, display_name) VALUES ('anno.owner', 'anno.owner');
INSERT INTO roles (name, display_name) VALUES ('anno.regulator', 'anno.regulator');
INSERT INTO roles (name, display_name) VALUES ('anno.viewer', 'anno.viewer');
INSERT INTO roles (name, display_name) VALUES ('annofeed.editor', 'annofeed.editor');
INSERT INTO roles (name, display_name) VALUES ('annofeed.owner', 'annofeed.owner');
INSERT INTO roles (name, display_name) VALUES ('annofeed.viewer', 'annofeed.viewer');
INSERT INTO roles (name, display_name) VALUES ('fe.editor', 'fe.editor');
INSERT INTO roles (name, display_name) VALUES ('fe.owner', 'fe.owner');
INSERT INTO roles (name, display_name) VALUES ('fe.viewer', 'fe.viewer');
INSERT INTO roles (name, display_name) VALUES ('iam.editor', 'iam.editor');
INSERT INTO roles (name, display_name) VALUES ('iam.owner', 'iam.owner');
INSERT INTO roles (name, display_name) VALUES ('iam.viewer', 'iam.viewer');
INSERT INTO roles (name, display_name) VALUES ('inspector', 'inspector');
INSERT INTO roles (name, display_name) VALUES ('kam', 'kam');
INSERT INTO roles (name, display_name) VALUES ('manager', 'manager');
INSERT INTO roles (name, display_name) VALUES ('member', 'member');
INSERT INTO roles (name, display_name) VALUES ('owner', 'owner');
INSERT INTO roles (name, display_name) VALUES ('pm', 'pm');
INSERT INTO roles (name, display_name) VALUES ('root', 'root');
INSERT INTO roles (name, display_name) VALUES ('service', 'service');
INSERT INTO roles (name, display_name) VALUES ('subAdmin', 'subAdmin');
INSERT INTO roles (name, display_name) VALUES ('viewer', 'viewer');
