syntax = "proto3";
package types;

import "openapi/v3/annotations.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/types;types";
option java_multiple_files = true;
option java_package = "types";


message Filelist {
  option (openapi.v3.schema) = {
    required: ["files"]
  };

  message File {
    option (openapi.v3.schema) = {
      required: ["url", "size"]
    };

    string url = 1;
    // file size in bytes
    double size = 2;
    // file name in path format without `data` prefix: e.g. frame1/meta/config.json
    string name = 3;
  }

  repeated File files = 1;
}
