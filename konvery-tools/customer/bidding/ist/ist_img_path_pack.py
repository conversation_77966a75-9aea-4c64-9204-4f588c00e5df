import os
import shutil
import sys

sys.path.append('.')
from customer.common import tools


def run(input_dir, output_dir, depth=4):
    tools.check_dir(output_dir)
    for zip_file in tools.listdir(input_dir, full_path=True):
        if not zip_file.endswith('.zip'):
            continue
        unzip_dir = tools.unzip(zip_file, input_dir)
        for image in tools.get_file_by_extension(unzip_dir, '.jpg'):
            shutil.copyfile(image, os.path.join(output_dir, '__'.join(image.path.split(os.sep)[-depth:])))


if __name__ == '__main__':
    args = tools.get_args()
    if args.txt:
        depth = int(args.txt)
    else:
        depth = 4
    run(args.input, args.out, depth)
