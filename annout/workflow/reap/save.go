package reap

import (
	"archive/zip"
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"os"
	"path"
	"path/filepath"
	"time"

	"annout/api/client"
	"annout/internal/biz"
	"annout/workflow/codec"
	"annout/workflow/types"

	"github.com/go-resty/resty/v2"
	"github.com/gosimple/slug"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/apis/annofeed/v1"
	"gitlab.rp.konvery.work/platform/pkg/kfs"
	"gitlab.rp.konvery.work/platform/pkg/ktime"
)

// ReapSaveAnnos packs annos, upload it to s3, and save its URI into Lot and Order.
func (o *Activities) ReapSaveAnnos(ctx context.Context, dir string, reaper *biz.Reaper) (err error) {
	ctx = client.NewCtxUseSvcAccount(ctx)

	// get lot
	lot, err := client.GetLot(ctx, reaper.LotUid)
	if err != nil {
		return fmt.Errorf("failed to GetLot: %w", err)
	}

	fmt.Println("---> reap save annos")
	spew.Dump(dir)
	spew.Dump(reaper)
	spew.Dump(lot)
	// pack and upload the annos
	err = o.packAnnos(ctx, dir, reaper, lot, false)
	if err != nil {
		fmt.Println("111-", err)
		return err
	}
	cfg, err := o.prepareUpload(ctx, reaper, lot, false)
	if err != nil {
		fmt.Println("222-", err)
		return err
	}
	err = o.uploadAnnos(ctx, cfg.UploadURL)
	if err != nil {
		fmt.Println("333-", err)
		return err
	}
	return o.postUpload(ctx, reaper, cfg, false)
}

func (o *Activities) packAnnos(ctx context.Context, dir string, reaper *biz.Reaper, lot *anno.Lot, workonConverted bool) (err error) {
	file, err := os.Create(types.PackAnnosFile)
	if err != nil {
		return fmt.Errorf("failed to create zipfile: %w", err)
	}
	defer file.Close()
	zw := zip.NewWriter(file)
	defer zw.Close()

	if !workonConverted {
		fmt.Println(workonConverted)
		if err := o.createMeta(ctx, reaper, lot.Name, zw); err != nil {
			return err
		}
	}
	return kfs.IterateDir(dir, func(entry kfs.DirEntry, subdir string, depth int) error {
		name := entry.Name()
		if entry.IsDir() || name == types.PackAnnosFile {
			return nil
		}

		data, err := os.ReadFile(filepath.Join(dir, subdir, name))
		if err != nil {
			return fmt.Errorf("failed to read file %s: %w", name, err)
		}
		w, err := zw.Create(path.Join(subdir, name))
		if err != nil {
			return fmt.Errorf("failed to create file %s: %w", name, err)
		}
		_, err = w.Write(data)
		if err != nil {
			return fmt.Errorf("failed to write file %s: %w", name, err)
		}
		return nil
	})
}

func (o *Activities) createMeta(ctx context.Context, reaper *biz.Reaper, lotName string, zw *zip.Writer) error {
	// if !reaper.Config.Exporter.IncludeDataUid {
	// 	return nil
	// }

	fname := types.MetaFile
	meta := &annofeed.Meta{BaseOnUid: reaper.DataUid, RelatedLot: &annofeed.Meta_Lot{Uid: reaper.LotUid, Name: lotName}}
	dataMeta, err := client.GetDataMeta(ctx, &annofeed.GetDataMetaRequest{Uid: reaper.DataUid, WithMetafiles: true})
	if err != nil {
		return fmt.Errorf("failed to get data metafiles")
	}
	meta.Metadata = dataMeta.GetMetadata()
	meta.Metafiles = dataMeta.GetMetafiles()

	data, err := codec.GetCodec("json").Marshal(meta)
	if err != nil {
		return fmt.Errorf("failed to marshal %s: %w", fname, err)
	}
	w, err := zw.Create(fname)
	if err != nil {
		return fmt.Errorf("failed to create file %s: %w", fname, err)
	}
	_, err = w.Write(data)
	if err != nil {
		return fmt.Errorf("failed to write file %s: %w", fname, err)
	}
	return nil
}

func (o *Activities) uploadAnnos(ctx context.Context, url string) (err error) {
	file, err := os.Open(types.PackAnnosFile)
	if err != nil {
		return fmt.Errorf("failed to open zipfile: %w", err)
	}
	defer os.Remove(types.PackAnnosFile)
	defer file.Close()

	// do upload
	rsp, err := resty.New().R().SetContext(ctx).
		SetContentLength(true).SetHeader("Content-Type", "application/zip").
		SetBody(file).Put(url)
	if err == nil && rsp.IsError() {
		err = fmt.Errorf("HTTP server responds %v: %s", rsp.Status(), rsp.Body())
	}
	if err != nil {
		return fmt.Errorf("failed to upload file to %v: %w", url, err)
	}

	return nil
}

type uploadCfg struct {
	FileUid   string
	FileURI   string
	UploadURL string
}

func (o *Activities) prepareUpload(ctx context.Context, reaper *biz.Reaper, lot *anno.Lot, workonConverted bool) (cfg *uploadCfg, err error) {
	filename := fmt.Sprintf("%v_lot-%v_order-%v_ins-%v_%v.zip", slug.Make(lot.Name), lot.Uid, lot.OrderUid, lot.InsCnt,
		time.Now().In(ktime.TZCST).Format("060102-1504"))
	if workonConverted {
		filename = fmt.Sprintf("%v_lot-%v_order-%v_ins-%v_%v-convert.zip", slug.Make(lot.Name), lot.Uid, lot.OrderUid, lot.InsCnt,
			time.Now().In(ktime.TZCST).Format("060102-1504"))
	}
	rsp, err := client.CreateFile(ctx, &client.CreateFileRequest{
		Name:   filename,
		Parts:  1,
		Mime:   "application/zip",
		OrgUid: lot.OrgUid,
		Size:   0,
		Sha256: "",
	})
	if err != nil {
		return nil, fmt.Errorf("failed to CreateFile: %w", err)
	}

	return &uploadCfg{
		FileUid:   rsp.File.Uid,
		FileURI:   rsp.File.Uri,
		UploadURL: rsp.UploadUrls[0],
	}, nil
}

func (o *Activities) postUpload(ctx context.Context, reaper *biz.Reaper, cfg *uploadCfg, workonConverted bool) (err error) {
	err = client.FinishFileUpload(ctx, cfg.FileUid)
	if err != nil {
		return fmt.Errorf("failed to FinishFileUpload: %w", err)
	}

	url := cfg.FileURI // use original file URI
	if reaper.OrderUid != "" {
		err = client.SetOrderAnnoResult(ctx, reaper.OrderUid, url)
		if err != nil {
			return fmt.Errorf("failed to SetOrderAnnoResult: %w", err)
		}
	}
	if workonConverted {
		return
	}

	if err := client.SetLotAnnoResult(ctx, reaper.LotUid, url); err != nil {
		return fmt.Errorf("failed to SetLotAnnoResult: %w", err)
	}
	return nil
}
