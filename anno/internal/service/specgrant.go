// source: iam/v1/grant.proto
package service

import (
	"context"

	"anno/api/client"
	"anno/internal/biz"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"

	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func FromBizSpecgrant(d *biz.Specgrant) *anno.Specgrant {
	if d == nil {
		return nil
	}
	o := &anno.Specgrant{
		GrantorUid: d.<PERSON>id,
		GranteeUid: d.<PERSON>,
		ItemType:   anno.Specgrant_ItemType_Enum(anno.Specgrant_ItemType_Enum_value[d.ItemType]),
		ItemUid:    kid.StringID(d.Item<PERSON>),
		OrgUid:     d.<PERSON>g<PERSON>,
		CreatedAt:  timestamppb.New(d.CreatedAt),
	}
	return o
}

func ToBizSpecgrant(d *anno.CreateSpecgrantRequest) *biz.Specgrant {
	if d == nil {
		return nil
	}
	return &biz.Specgrant{
		GranteeUid: d.GranteeUid,
		ItemType:   d.ItemType.String(),
		ItemID:     kid.ParseID(d.ItemUid),
	}
}

func ToBizSpecgrantFilter(f *anno.SpecgrantFilter) *biz.SpecgrantFilter {
	if f == nil {
		return &biz.SpecgrantFilter{}
	}
	typ := ""
	if f.ItemType != anno.Specgrant_ItemType_unspecified {
		typ = f.ItemType.String()
	}

	return &biz.SpecgrantFilter{
		GrantorUid: f.GrantorUid,
		GranteeUid: f.GranteeUid,
		OrgUid:     f.OrgUid,
		ItemType:   typ,
		ItemIDs:    biz.GetIDs(f.ItemUids),
	}
}

type SpecgrantsService struct {
	anno.UnimplementedSpecgrantsServer
	bz      *biz.SpecgrantsBiz
	repo    biz.SpecgrantsRepo
	lotrepo biz.LotsRepo
}

func NewSpecgrantsService(bz *biz.SpecgrantsBiz, repo biz.SpecgrantsRepo, lotrepo biz.LotsRepo) *SpecgrantsService {
	return &SpecgrantsService{bz: bz, repo: repo, lotrepo: lotrepo}
}

func (o *SpecgrantsService) CreateSpecgrant(ctx context.Context, req *anno.CreateSpecgrantRequest) (
	*anno.Specgrant, error) {
	g := ToBizSpecgrant(req)
	if !client.IsAllowed(ctx, "", biz.PermSetPolicy, g.ItemType, req.ItemUid) {
		return nil, errors.NewErrForbidden()
	}
	switch g.ItemType {
	case biz.PermClsLot:
		lot, err := o.lotrepo.GetByID(ctx, g.ItemID, false)
		if err != nil {
			return nil, err
		}
		g.OrgUid = lot.OrgUid
	default:
		return nil, errors.NewErrUnsupportedField(errors.WithFields(g.ItemType))
	}

	op := biz.UserFromCtx(ctx)
	g.GrantorUid = op.GetUid()
	g, err := o.bz.Create(ctx, g)
	if err != nil {
		return nil, err
	}
	return FromBizSpecgrant(g), err
}

func (o *SpecgrantsService) DeleteSpecgrant(ctx context.Context, req *anno.DeleteSpecgrantRequest) (*emptypb.Empty, error) {
	f := req.Filter
	if f.GranteeUid == "" || f.ItemType == anno.Specgrant_ItemType_unspecified || len(f.ItemUids) == 0 {
		return nil, errors.NewErrEmptyField(errors.WithFields("grantee_uid", "item_type", "item_uids"))
	}
	filter := ToBizSpecgrantFilter(f)
	for _, uid := range f.ItemUids {
		if !client.IsAllowed(ctx, "", biz.PermSetPolicy, filter.ItemType, uid) {
			return nil, errors.NewErrForbidden()
		}
	}

	return &emptypb.Empty{}, o.bz.DeleteByFilter(ctx, filter)
}

func (o *SpecgrantsService) ListSpecgrant(ctx context.Context, req *anno.ListSpecgrantRequest) (*anno.ListSpecgrantReply, error) {
	f := req.Filter
	if f == nil || (f.GrantorUid == "" && f.GranteeUid == "" && f.OrgUid == "" && len(f.ItemUids) == 0) {
		return nil, errors.NewErrEmptyField(errors.WithFields("grantor_uid", "grantee_uid", "org_uid", "item_uids"))
	}
	filter := ToBizSpecgrantFilter(f)

	// TODO: check permissions
	// if !client.IsAllowed(ctx, "", biz.PermGetPolicy, filter.ItemType, xxx) {
	// 	return nil, errors.NewErrForbidden()
	// }

	pager := biz.Pager{
		Pagesz:    int(req.Pagesz),
		PageToken: biz.PageToken(req.PageToken),
	}
	datas, nextPageToken, err := o.bz.List(ctx, filter, pager)
	if err != nil {
		return nil, err
	}
	return &anno.ListSpecgrantReply{NextPageToken: nextPageToken, Grants: kslice.Map(FromBizSpecgrant, datas)}, nil
}
