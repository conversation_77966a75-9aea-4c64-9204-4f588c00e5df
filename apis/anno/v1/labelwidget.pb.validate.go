// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/labelwidget.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListLabelwidgetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListLabelwidgetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLabelwidgetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListLabelwidgetRequestMultiError, or nil if none found.
func (m *ListLabelwidgetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLabelwidgetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListLabelwidgetRequestMultiError(errors)
	}

	return nil
}

// ListLabelwidgetRequestMultiError is an error wrapping multiple validation
// errors returned by ListLabelwidgetRequest.ValidateAll() if the designated
// constraints aren't met.
type ListLabelwidgetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLabelwidgetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLabelwidgetRequestMultiError) AllErrors() []error { return m }

// ListLabelwidgetRequestValidationError is the validation error returned by
// ListLabelwidgetRequest.Validate if the designated constraints aren't met.
type ListLabelwidgetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLabelwidgetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLabelwidgetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLabelwidgetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLabelwidgetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLabelwidgetRequestValidationError) ErrorName() string {
	return "ListLabelwidgetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListLabelwidgetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLabelwidgetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLabelwidgetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLabelwidgetRequestValidationError{}

// Validate checks the field values on ListLabelwidgetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListLabelwidgetReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLabelwidgetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListLabelwidgetReplyMultiError, or nil if none found.
func (m *ListLabelwidgetReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLabelwidgetReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetWidgets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListLabelwidgetReplyValidationError{
						field:  fmt.Sprintf("Widgets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListLabelwidgetReplyValidationError{
						field:  fmt.Sprintf("Widgets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListLabelwidgetReplyValidationError{
					field:  fmt.Sprintf("Widgets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListLabelwidgetReplyMultiError(errors)
	}

	return nil
}

// ListLabelwidgetReplyMultiError is an error wrapping multiple validation
// errors returned by ListLabelwidgetReply.ValidateAll() if the designated
// constraints aren't met.
type ListLabelwidgetReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLabelwidgetReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLabelwidgetReplyMultiError) AllErrors() []error { return m }

// ListLabelwidgetReplyValidationError is the validation error returned by
// ListLabelwidgetReply.Validate if the designated constraints aren't met.
type ListLabelwidgetReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLabelwidgetReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLabelwidgetReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLabelwidgetReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLabelwidgetReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLabelwidgetReplyValidationError) ErrorName() string {
	return "ListLabelwidgetReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListLabelwidgetReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLabelwidgetReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLabelwidgetReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLabelwidgetReplyValidationError{}

// Validate checks the field values on Labelwidget with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Labelwidget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Labelwidget with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LabelwidgetMultiError, or
// nil if none found.
func (m *Labelwidget) ValidateAll() error {
	return m.validate(true)
}

func (m *Labelwidget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Langs

	if len(errors) > 0 {
		return LabelwidgetMultiError(errors)
	}

	return nil
}

// LabelwidgetMultiError is an error wrapping multiple validation errors
// returned by Labelwidget.ValidateAll() if the designated constraints aren't met.
type LabelwidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LabelwidgetMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LabelwidgetMultiError) AllErrors() []error { return m }

// LabelwidgetValidationError is the validation error returned by
// Labelwidget.Validate if the designated constraints aren't met.
type LabelwidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LabelwidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LabelwidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LabelwidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LabelwidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LabelwidgetValidationError) ErrorName() string { return "LabelwidgetValidationError" }

// Error satisfies the builtin error interface
func (e LabelwidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLabelwidget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LabelwidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LabelwidgetValidationError{}
