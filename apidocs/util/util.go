package util

import (
	"fmt"
	"os"
	"path/filepath"
)

func IsFileExist(path string) bool {
	fi, err := os.Stat(path)
	if err != nil {
		return false
	}
	return !fi.IsDir()
}

func IsDir(path string) bool {
	if fi, err := os.Stat(path); err == nil && fi.IsDir() {
		return true
	}
	return false
}

func ReadDir(path string) ([][]string, error) {
	entries, err := os.ReadDir(path)
	if err != nil {
		return nil, err
	}
	files := make([][]string, len(entries))
	for i, e := range entries {
		if e.Type()&os.ModeSymlink > 0 {
			lnk, err := os.Readlink(filepath.Join(path, e.Name()))
			if err != nil {
				return nil, fmt.Errorf("failed to read link file: %w", err)
			}
			files[i] = []string{e.Name(), lnk}
		} else {
			files[i] = []string{e.Name()}
		}
	}
	return files, nil
}
