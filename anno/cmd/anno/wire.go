//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"anno/internal/biz"
	"anno/internal/conf"
	"anno/internal/data"
	"anno/internal/server"
	"anno/internal/service"
	"anno/workflow"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init kratos application.
func wireApp(*conf.Server, *conf.Data, log.Logger) (*App, func(), error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, workflow.ProviderSet, newApp))
}
