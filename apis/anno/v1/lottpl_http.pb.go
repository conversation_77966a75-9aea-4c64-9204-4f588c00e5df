// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: anno/v1/lottpl.proto

package anno

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationLottplsCreateLottpl = "/anno.v1.Lottpls/CreateLottpl"
const OperationLottplsDeleteLottpl = "/anno.v1.Lottpls/DeleteLottpl"
const OperationLottplsGetLottpl = "/anno.v1.Lottpls/GetLottpl"
const OperationLottplsListLottpl = "/anno.v1.Lottpls/ListLottpl"
const OperationLottplsUpdateLottpl = "/anno.v1.Lottpls/UpdateLottpl"

type LottplsHTTPServer interface {
	CreateLottpl(context.Context, *CreateLottplRequest) (*Lottpl, error)
	DeleteLottpl(context.Context, *DeleteLottplRequest) (*emptypb.Empty, error)
	GetLottpl(context.Context, *GetLottplRequest) (*Lottpl, error)
	ListLottpl(context.Context, *ListLottplRequest) (*ListLottplReply, error)
	UpdateLottpl(context.Context, *UpdateLottplRequest) (*Lottpl, error)
}

func RegisterLottplsHTTPServer(s *http.Server, srv LottplsHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/lottpls", _Lottpls_CreateLottpl0_HTTP_Handler(srv))
	r.PATCH("/v1/lottpls/{lottpl.uid}", _Lottpls_UpdateLottpl0_HTTP_Handler(srv))
	r.DELETE("/v1/lottpls/{uid}", _Lottpls_DeleteLottpl0_HTTP_Handler(srv))
	r.GET("/v1/lottpls/{uid}", _Lottpls_GetLottpl0_HTTP_Handler(srv))
	r.GET("/v1/lottpls", _Lottpls_ListLottpl0_HTTP_Handler(srv))
}

func _Lottpls_CreateLottpl0_HTTP_Handler(srv LottplsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateLottplRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLottplsCreateLottpl)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateLottpl(ctx, req.(*CreateLottplRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Lottpl)
		return ctx.Result(200, reply)
	}
}

func _Lottpls_UpdateLottpl0_HTTP_Handler(srv LottplsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateLottplRequest
		if err := ctx.Bind(&in.Lottpl); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLottplsUpdateLottpl)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateLottpl(ctx, req.(*UpdateLottplRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Lottpl)
		return ctx.Result(200, reply)
	}
}

func _Lottpls_DeleteLottpl0_HTTP_Handler(srv LottplsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteLottplRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLottplsDeleteLottpl)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteLottpl(ctx, req.(*DeleteLottplRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Lottpls_GetLottpl0_HTTP_Handler(srv LottplsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLottplRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLottplsGetLottpl)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLottpl(ctx, req.(*GetLottplRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Lottpl)
		return ctx.Result(200, reply)
	}
}

func _Lottpls_ListLottpl0_HTTP_Handler(srv LottplsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListLottplRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLottplsListLottpl)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListLottpl(ctx, req.(*ListLottplRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListLottplReply)
		return ctx.Result(200, reply)
	}
}

type LottplsHTTPClient interface {
	CreateLottpl(ctx context.Context, req *CreateLottplRequest, opts ...http.CallOption) (rsp *Lottpl, err error)
	DeleteLottpl(ctx context.Context, req *DeleteLottplRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetLottpl(ctx context.Context, req *GetLottplRequest, opts ...http.CallOption) (rsp *Lottpl, err error)
	ListLottpl(ctx context.Context, req *ListLottplRequest, opts ...http.CallOption) (rsp *ListLottplReply, err error)
	UpdateLottpl(ctx context.Context, req *UpdateLottplRequest, opts ...http.CallOption) (rsp *Lottpl, err error)
}

type LottplsHTTPClientImpl struct {
	cc *http.Client
}

func NewLottplsHTTPClient(client *http.Client) LottplsHTTPClient {
	return &LottplsHTTPClientImpl{client}
}

func (c *LottplsHTTPClientImpl) CreateLottpl(ctx context.Context, in *CreateLottplRequest, opts ...http.CallOption) (*Lottpl, error) {
	var out Lottpl
	pattern := "/v1/lottpls"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLottplsCreateLottpl))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LottplsHTTPClientImpl) DeleteLottpl(ctx context.Context, in *DeleteLottplRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/lottpls/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLottplsDeleteLottpl))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LottplsHTTPClientImpl) GetLottpl(ctx context.Context, in *GetLottplRequest, opts ...http.CallOption) (*Lottpl, error) {
	var out Lottpl
	pattern := "/v1/lottpls/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLottplsGetLottpl))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LottplsHTTPClientImpl) ListLottpl(ctx context.Context, in *ListLottplRequest, opts ...http.CallOption) (*ListLottplReply, error) {
	var out ListLottplReply
	pattern := "/v1/lottpls"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLottplsListLottpl))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LottplsHTTPClientImpl) UpdateLottpl(ctx context.Context, in *UpdateLottplRequest, opts ...http.CallOption) (*Lottpl, error) {
	var out Lottpl
	pattern := "/v1/lottpls/{lottpl.uid}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLottplsUpdateLottpl))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in.Lottpl, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
