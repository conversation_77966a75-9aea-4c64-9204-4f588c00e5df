import sys
import os
import shutil
sys.path.append('.')
from customer.common import tools


def run(input_dir, output_dir):
    label_dirs = tools.find_dir_by_pre_name(input_dir, 'label')
    for label_dir in label_dirs:
        clip_name = os.path.basename(os.path.dirname(label_dir))
        out_clip_dir = os.path.join(output_dir, clip_name)
        os.mkdir(out_clip_dir)
        out_json_dir = os.path.join(out_clip_dir, 'json')
        os.mkdir(out_json_dir)
        json_files = tools.get_json_files(label_dir)
        for json_file in json_files:
            group_id2instance = dict()
            json_data = tools.get_json_data(json_file)
            for obj in json_data.get('labels', []):
                group_id = obj.get('group_id', 0)
                if group_id not in group_id2instance:
                    group_id2instance[group_id] = {
                        "regions": []
                    }
                regions = {
                    "contour": [
                        [[pt['x'], pt['y']] for pt in obj['annotation']['data']]
                    ],
                    "label": [obj['class']]
                }
                if 'gaps' in obj['annotation']:
                    for gap in obj['annotation']['gaps']:
                        regions['contour'].append([[pt['x'], pt['y']] for pt in gap])
                group_id2instance[group_id]['regions'].append(regions)
            json_result = {
                "name": os.path.basename(json_file).replace('.json', '.jpg'),
                "instance": list(group_id2instance.values())
            }
            tools.write_json_file(json_result, os.path.join(out_json_dir, os.path.basename(json_file)))


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
