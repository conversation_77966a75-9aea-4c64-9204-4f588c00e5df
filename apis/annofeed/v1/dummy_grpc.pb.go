// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: annofeed/v1/dummy.proto

package annofeed

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Dummy_Dummy_FullMethodName = "/annofeed.v1.Dummy/Dummy"
)

// DummyClient is the client API for Dummy service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DummyClient interface {
	Dummy(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*DummyReply, error)
}

type dummyClient struct {
	cc grpc.ClientConnInterface
}

func NewDummyClient(cc grpc.ClientConnInterface) DummyClient {
	return &dummyClient{cc}
}

func (c *dummyClient) Dummy(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*DummyReply, error) {
	out := new(DummyReply)
	err := c.cc.Invoke(ctx, Dummy_Dummy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DummyServer is the server API for Dummy service.
// All implementations must embed UnimplementedDummyServer
// for forward compatibility
type DummyServer interface {
	Dummy(context.Context, *emptypb.Empty) (*DummyReply, error)
	mustEmbedUnimplementedDummyServer()
}

// UnimplementedDummyServer must be embedded to have forward compatible implementations.
type UnimplementedDummyServer struct {
}

func (UnimplementedDummyServer) Dummy(context.Context, *emptypb.Empty) (*DummyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Dummy not implemented")
}
func (UnimplementedDummyServer) mustEmbedUnimplementedDummyServer() {}

// UnsafeDummyServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DummyServer will
// result in compilation errors.
type UnsafeDummyServer interface {
	mustEmbedUnimplementedDummyServer()
}

func RegisterDummyServer(s grpc.ServiceRegistrar, srv DummyServer) {
	s.RegisterService(&Dummy_ServiceDesc, srv)
}

func _Dummy_Dummy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DummyServer).Dummy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dummy_Dummy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DummyServer).Dummy(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// Dummy_ServiceDesc is the grpc.ServiceDesc for Dummy service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Dummy_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "annofeed.v1.Dummy",
	HandlerType: (*DummyServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Dummy",
			Handler:    _Dummy_Dummy_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "annofeed/v1/dummy.proto",
}
