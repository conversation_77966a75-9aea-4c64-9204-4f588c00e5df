package interpolate

import (
	"testing"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"

	"github.com/stretchr/testify/assert"
)

// Test_slerp tests the slerp function.
// The results can be calculated in http://www.euclideanspace.com/maths/algebra/realNormedAlgebra/quaternions/slerp/
func Test_slerp(t *testing.T) {
	cases := []struct {
		name     string
		q1       Quat
		q2       Quat
		t        float64
		expected Quat
	}{
		{
			"happy_flow_1",
			Quat{0, 0, 1, 0},
			Quat{0, 0, 0.70710678, 0.70710678},
			0.333,
			Quat{0, 0, 0.9659935519263585, 0.25856615717587234},
			// results:   0, 0, 0.9659935517096141, 0.25856615711785663
		},
		{
			"happy_flow_2",
			Quat{0, 0, 1, 0},
			Quat{0, 0, 0.7071067811865476, 0.7071067811865476},
			0.667,
			Quat{0, 0, 0.8658944744138117, 0.5002267077832097},
			// results:   0, 0, 0.8658944738539345, 0.500226707073567
		},
		{
			"both_quats_are_same",
			Quat{0, 0, 1, 0},
			Quat{0, 0, 1, 0},
			0.667,
			Quat{0, 0, 1, 0},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			got := slerp(c.q1, c.q2, c.t)
			assert.Equal(t, c.expected, got)
		})
	}
}

// The expected results can be calculated by Python scipy package.
/* For example:
>>> from scipy.spatial.transform import Rotation as R
>>> r = R.from_quat([0, 1, 0, 1])
>>> r.as_quat()
array([0.        , 0.70710678, 0.        , 0.70710678])
>>> r.apply([1, 1, 1])
array([ 1.,  1., -1.])
*/
func Test_rotateByQuaternion(t *testing.T) {
	cases := []struct {
		name     string
		vec      Point3D
		center   Point3D
		quat     Quat
		expected Point3D
	}{
		{
			"center_is_zero",
			Point3D{1, 1, 1},
			Point3D{0, 0, 0},
			Quat{0, 1, 0, 1},
			Point3D{0.9999999999999998, 0.9999999999999998, -0.9999999999999998}, // 1, 1, -1
		},
		{
			"center_x_is_not_zero",
			Point3D{2, 1, 1},
			Point3D{1, 0, 0},
			Quat{0., 1, 0., 1},
			Point3D{1.9999999999999998, 0.9999999999999998, -0.9999999999999998}, // 2, 1, -1
		},
		{
			"center_xyz_are_not_zero",
			Point3D{2, 3, 4},
			Point3D{1, 2, 3},
			Quat{0., 1, 0., 1},
			Point3D{1.9999999999999998, 3, 2}, // 2, 3, 2
		},
		{
			"quat_is_zero",
			Point3D{2, 3, 4},
			Point3D{0, 0, 0},
			Quat{0., 0, 0, 0},
			Point3D{2, 3, 4},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			got := rotateByQuaternion(c.vec, c.center, c.quat)
			assert.Equal(t, c.expected, got)
		})
	}
}

func Test_cuboid3dInterpolate(t *testing.T) {
	o1 := &anno.Object{
		Uuid:    "1111",
		TrackId: "car-1",
		Label: &anno.Object_Label{
			Name:  "car",
			Attrs: []*anno.AttrAndValues{},
			Widget: &anno.Object_Widget{
				Name: anno.WidgetName_box2d,
				Data: []float64{0, 0, 0, 10, 10, 10, 0, 0, 0, 1},
				Forward: &anno.Direction{
					Origin: []float64{0, 0, 0},
					Toward: []float64{10, 0, 0},
				},
			},
		},
	}
	o2 := cloneObject(o1)
	o2.Label.Widget.Data = []float64{30, 0, 0, 10, 10, 10, 0, 0, 0, 1}
	o2.Label.Widget.Forward = &anno.Direction{
		Origin: []float64{30, 0, 0},
		Toward: []float64{40, 0, 0},
	}

	cases := []struct {
		name       string
		o1         *anno.Object
		o2         *anno.Object
		span       float64
		distance   float64
		expData    []float64
		expForward *anno.Direction
	}{
		{
			"happy_flow",
			o1,
			o2,
			10,
			5,
			[]float64{15, 0, 0, 10, 10, 10, 0, 0, 0, 1},
			&anno.Direction{
				Origin: []float64{15, 0, 0},
				Toward: []float64{25, 0, 0},
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			o := cuboid3dInterpolate(c.o1, c.o2, c.span, c.distance)
			assert.EqualValues(t, c.expData, o.Label.Widget.Data)
			assert.EqualValues(t, c.expForward.Origin, o.Label.Widget.Forward.Origin)
			assert.EqualValues(t, c.expForward.Toward, o.Label.Widget.Forward.Toward)
		})
	}
}
