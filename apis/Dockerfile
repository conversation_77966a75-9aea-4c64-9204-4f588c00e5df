ARG IMAGE_PREFIX
FROM ${IMAGE_PREFIX}golang:1.22-alpine AS builder
ARG GOPROXY
ARG GOPRIVATE
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk update && apk add --no-cache make git protoc sed
ENV GOPROXY=${GOPROXY}
ENV GOPRIVATE=${GOPRIVATE}
COPY . /app
WORKDIR /app
CMD ["sh", "-c", "make init && make test"]


# docker build  --build-arg GOPRIVATE="gitlab.rp.konvery.work" --build-arg IMAGE_PREFIX="artifactory.rp.konvery.work/docker/" --build-arg GOPROXY="https://artifactory.rp.konvery.work/artifactory/api/go/go,https://goproxy.cn,direct" -t api:tv3 .