// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: anno/v1/lot.proto

package anno

import (
	context "context"
	types "gitlab.rp.konvery.work/platform/apis/types"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Lots_CreateLot_FullMethodName          = "/anno.v1.Lots/CreateLot"
	Lots_CloneLot_FullMethodName           = "/anno.v1.Lots/CloneLot"
	Lots_UpdateLot_FullMethodName          = "/anno.v1.Lots/UpdateLot"
	Lots_DeleteLot_FullMethodName          = "/anno.v1.Lots/DeleteLot"
	Lots_ListLotsByExecutor_FullMethodName = "/anno.v1.Lots/ListLotsByExecutor"
	Lots_GetLot_FullMethodName             = "/anno.v1.Lots/GetLot"
	Lots_ListLot_FullMethodName            = "/anno.v1.Lots/ListLot"
	Lots_StartLot_FullMethodName           = "/anno.v1.Lots/StartLot"
	Lots_PauseLot_FullMethodName           = "/anno.v1.Lots/PauseLot"
	Lots_CancelLot_FullMethodName          = "/anno.v1.Lots/CancelLot"
	Lots_ListExecteams_FullMethodName      = "/anno.v1.Lots/ListExecteams"
	Lots_AssignExecteam_FullMethodName     = "/anno.v1.Lots/AssignExecteam"
	Lots_ManageExecutors_FullMethodName    = "/anno.v1.Lots/ManageExecutors"
	Lots_ListExecutors_FullMethodName      = "/anno.v1.Lots/ListExecutors"
	Lots_GetSummary_FullMethodName         = "/anno.v1.Lots/GetSummary"
	Lots_GetVisibleLots_FullMethodName     = "/anno.v1.Lots/GetVisibleLots"
	Lots_ExportLotAnnos_FullMethodName     = "/anno.v1.Lots/ExportLotAnnos"
	Lots_SetLotAnnoResult_FullMethodName   = "/anno.v1.Lots/SetLotAnnoResult"
	Lots_AllowDownloadAnnos_FullMethodName = "/anno.v1.Lots/AllowDownloadAnnos"
	Lots_AddTag_FullMethodName             = "/anno.v1.Lots/AddTag"
	Lots_DeleteTag_FullMethodName          = "/anno.v1.Lots/DeleteTag"
	Lots_JobCountByLots_FullMethodName     = "/anno.v1.Lots/JobCountByLots"
)

// LotsClient is the client API for Lots service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LotsClient interface {
	CreateLot(ctx context.Context, in *CreateLotRequest, opts ...grpc.CallOption) (*Lot, error)
	// create a new lot by cloning an existing lot.
	CloneLot(ctx context.Context, in *CloneLotRequest, opts ...grpc.CallOption) (*Lot, error)
	// update lot. only simple information like name or desc update are allowed.
	UpdateLot(ctx context.Context, in *UpdateLotRequest, opts ...grpc.CallOption) (*Lot, error)
	DeleteLot(ctx context.Context, in *DeleteLotRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// get lots assigned to a user or a team.
	// if the phase's execteam is empty, the phase is not assigned to the user or team.
	ListLotsByExecutor(ctx context.Context, in *ListLotsByExecutorRequest, opts ...grpc.CallOption) (*ListLotsByExecutorReply, error)
	GetLot(ctx context.Context, in *GetLotRequest, opts ...grpc.CallOption) (*Lot, error)
	ListLot(ctx context.Context, in *ListLotRequest, opts ...grpc.CallOption) (*ListLotReply, error)
	StartLot(ctx context.Context, in *GetLotRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	PauseLot(ctx context.Context, in *GetLotRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	CancelLot(ctx context.Context, in *GetLotRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// list assigned execution teams and executors
	ListExecteams(ctx context.Context, in *ListExecteamsRequest, opts ...grpc.CallOption) (*ListExecteamsReply, error)
	// assign execution team, update or delete an execution team
	AssignExecteam(ctx context.Context, in *AssignExecteamRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// add or remove executors
	ManageExecutors(ctx context.Context, in *ManageExecutorsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// list executors at a phase
	ListExecutors(ctx context.Context, in *ListExecutorsRequest, opts ...grpc.CallOption) (*ListExecutorsReply, error)
	// Get summary of a lot
	GetSummary(ctx context.Context, in *GetLotRequest, opts ...grpc.CallOption) (*GetLotSummaryReply, error)
	// GetVisibleLots is used to query visible lots for a user (e.g. anno member)
	GetVisibleLots(ctx context.Context, in *GetVisibleLotsRequest, opts ...grpc.CallOption) (*GetVisibleLotsReply, error)
	// ExportLotAnnos exports lot annotations
	ExportLotAnnos(ctx context.Context, in *ExportLotAnnosRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// SetLotAnnoResult sets the annotation result in lot
	SetLotAnnoResult(ctx context.Context, in *SetLotAnnoResultRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// allows demander to download annos
	AllowDownloadAnnos(ctx context.Context, in *AllowDownloadAnnosRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	AddTag(ctx context.Context, in *types.TagRequest, opts ...grpc.CallOption) (*types.TagList, error)
	DeleteTag(ctx context.Context, in *types.TagRequest, opts ...grpc.CallOption) (*types.TagList, error)
	JobCountByLots(ctx context.Context, in *JobCountByLotidsRequest, opts ...grpc.CallOption) (*JobCountByLotidsReply, error)
}

type lotsClient struct {
	cc grpc.ClientConnInterface
}

func NewLotsClient(cc grpc.ClientConnInterface) LotsClient {
	return &lotsClient{cc}
}

func (c *lotsClient) CreateLot(ctx context.Context, in *CreateLotRequest, opts ...grpc.CallOption) (*Lot, error) {
	out := new(Lot)
	err := c.cc.Invoke(ctx, Lots_CreateLot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) CloneLot(ctx context.Context, in *CloneLotRequest, opts ...grpc.CallOption) (*Lot, error) {
	out := new(Lot)
	err := c.cc.Invoke(ctx, Lots_CloneLot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) UpdateLot(ctx context.Context, in *UpdateLotRequest, opts ...grpc.CallOption) (*Lot, error) {
	out := new(Lot)
	err := c.cc.Invoke(ctx, Lots_UpdateLot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) DeleteLot(ctx context.Context, in *DeleteLotRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Lots_DeleteLot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) ListLotsByExecutor(ctx context.Context, in *ListLotsByExecutorRequest, opts ...grpc.CallOption) (*ListLotsByExecutorReply, error) {
	out := new(ListLotsByExecutorReply)
	err := c.cc.Invoke(ctx, Lots_ListLotsByExecutor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) GetLot(ctx context.Context, in *GetLotRequest, opts ...grpc.CallOption) (*Lot, error) {
	out := new(Lot)
	err := c.cc.Invoke(ctx, Lots_GetLot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) ListLot(ctx context.Context, in *ListLotRequest, opts ...grpc.CallOption) (*ListLotReply, error) {
	out := new(ListLotReply)
	err := c.cc.Invoke(ctx, Lots_ListLot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) StartLot(ctx context.Context, in *GetLotRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Lots_StartLot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) PauseLot(ctx context.Context, in *GetLotRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Lots_PauseLot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) CancelLot(ctx context.Context, in *GetLotRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Lots_CancelLot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) ListExecteams(ctx context.Context, in *ListExecteamsRequest, opts ...grpc.CallOption) (*ListExecteamsReply, error) {
	out := new(ListExecteamsReply)
	err := c.cc.Invoke(ctx, Lots_ListExecteams_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) AssignExecteam(ctx context.Context, in *AssignExecteamRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Lots_AssignExecteam_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) ManageExecutors(ctx context.Context, in *ManageExecutorsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Lots_ManageExecutors_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) ListExecutors(ctx context.Context, in *ListExecutorsRequest, opts ...grpc.CallOption) (*ListExecutorsReply, error) {
	out := new(ListExecutorsReply)
	err := c.cc.Invoke(ctx, Lots_ListExecutors_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) GetSummary(ctx context.Context, in *GetLotRequest, opts ...grpc.CallOption) (*GetLotSummaryReply, error) {
	out := new(GetLotSummaryReply)
	err := c.cc.Invoke(ctx, Lots_GetSummary_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) GetVisibleLots(ctx context.Context, in *GetVisibleLotsRequest, opts ...grpc.CallOption) (*GetVisibleLotsReply, error) {
	out := new(GetVisibleLotsReply)
	err := c.cc.Invoke(ctx, Lots_GetVisibleLots_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) ExportLotAnnos(ctx context.Context, in *ExportLotAnnosRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Lots_ExportLotAnnos_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) SetLotAnnoResult(ctx context.Context, in *SetLotAnnoResultRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Lots_SetLotAnnoResult_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) AllowDownloadAnnos(ctx context.Context, in *AllowDownloadAnnosRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Lots_AllowDownloadAnnos_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) AddTag(ctx context.Context, in *types.TagRequest, opts ...grpc.CallOption) (*types.TagList, error) {
	out := new(types.TagList)
	err := c.cc.Invoke(ctx, Lots_AddTag_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) DeleteTag(ctx context.Context, in *types.TagRequest, opts ...grpc.CallOption) (*types.TagList, error) {
	out := new(types.TagList)
	err := c.cc.Invoke(ctx, Lots_DeleteTag_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotsClient) JobCountByLots(ctx context.Context, in *JobCountByLotidsRequest, opts ...grpc.CallOption) (*JobCountByLotidsReply, error) {
	out := new(JobCountByLotidsReply)
	err := c.cc.Invoke(ctx, Lots_JobCountByLots_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LotsServer is the server API for Lots service.
// All implementations must embed UnimplementedLotsServer
// for forward compatibility
type LotsServer interface {
	CreateLot(context.Context, *CreateLotRequest) (*Lot, error)
	// create a new lot by cloning an existing lot.
	CloneLot(context.Context, *CloneLotRequest) (*Lot, error)
	// update lot. only simple information like name or desc update are allowed.
	UpdateLot(context.Context, *UpdateLotRequest) (*Lot, error)
	DeleteLot(context.Context, *DeleteLotRequest) (*emptypb.Empty, error)
	// get lots assigned to a user or a team.
	// if the phase's execteam is empty, the phase is not assigned to the user or team.
	ListLotsByExecutor(context.Context, *ListLotsByExecutorRequest) (*ListLotsByExecutorReply, error)
	GetLot(context.Context, *GetLotRequest) (*Lot, error)
	ListLot(context.Context, *ListLotRequest) (*ListLotReply, error)
	StartLot(context.Context, *GetLotRequest) (*emptypb.Empty, error)
	PauseLot(context.Context, *GetLotRequest) (*emptypb.Empty, error)
	CancelLot(context.Context, *GetLotRequest) (*emptypb.Empty, error)
	// list assigned execution teams and executors
	ListExecteams(context.Context, *ListExecteamsRequest) (*ListExecteamsReply, error)
	// assign execution team, update or delete an execution team
	AssignExecteam(context.Context, *AssignExecteamRequest) (*emptypb.Empty, error)
	// add or remove executors
	ManageExecutors(context.Context, *ManageExecutorsRequest) (*emptypb.Empty, error)
	// list executors at a phase
	ListExecutors(context.Context, *ListExecutorsRequest) (*ListExecutorsReply, error)
	// Get summary of a lot
	GetSummary(context.Context, *GetLotRequest) (*GetLotSummaryReply, error)
	// GetVisibleLots is used to query visible lots for a user (e.g. anno member)
	GetVisibleLots(context.Context, *GetVisibleLotsRequest) (*GetVisibleLotsReply, error)
	// ExportLotAnnos exports lot annotations
	ExportLotAnnos(context.Context, *ExportLotAnnosRequest) (*emptypb.Empty, error)
	// SetLotAnnoResult sets the annotation result in lot
	SetLotAnnoResult(context.Context, *SetLotAnnoResultRequest) (*emptypb.Empty, error)
	// allows demander to download annos
	AllowDownloadAnnos(context.Context, *AllowDownloadAnnosRequest) (*emptypb.Empty, error)
	AddTag(context.Context, *types.TagRequest) (*types.TagList, error)
	DeleteTag(context.Context, *types.TagRequest) (*types.TagList, error)
	JobCountByLots(context.Context, *JobCountByLotidsRequest) (*JobCountByLotidsReply, error)
	mustEmbedUnimplementedLotsServer()
}

// UnimplementedLotsServer must be embedded to have forward compatible implementations.
type UnimplementedLotsServer struct {
}

func (UnimplementedLotsServer) CreateLot(context.Context, *CreateLotRequest) (*Lot, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLot not implemented")
}
func (UnimplementedLotsServer) CloneLot(context.Context, *CloneLotRequest) (*Lot, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloneLot not implemented")
}
func (UnimplementedLotsServer) UpdateLot(context.Context, *UpdateLotRequest) (*Lot, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLot not implemented")
}
func (UnimplementedLotsServer) DeleteLot(context.Context, *DeleteLotRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLot not implemented")
}
func (UnimplementedLotsServer) ListLotsByExecutor(context.Context, *ListLotsByExecutorRequest) (*ListLotsByExecutorReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLotsByExecutor not implemented")
}
func (UnimplementedLotsServer) GetLot(context.Context, *GetLotRequest) (*Lot, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLot not implemented")
}
func (UnimplementedLotsServer) ListLot(context.Context, *ListLotRequest) (*ListLotReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLot not implemented")
}
func (UnimplementedLotsServer) StartLot(context.Context, *GetLotRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartLot not implemented")
}
func (UnimplementedLotsServer) PauseLot(context.Context, *GetLotRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PauseLot not implemented")
}
func (UnimplementedLotsServer) CancelLot(context.Context, *GetLotRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelLot not implemented")
}
func (UnimplementedLotsServer) ListExecteams(context.Context, *ListExecteamsRequest) (*ListExecteamsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExecteams not implemented")
}
func (UnimplementedLotsServer) AssignExecteam(context.Context, *AssignExecteamRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AssignExecteam not implemented")
}
func (UnimplementedLotsServer) ManageExecutors(context.Context, *ManageExecutorsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ManageExecutors not implemented")
}
func (UnimplementedLotsServer) ListExecutors(context.Context, *ListExecutorsRequest) (*ListExecutorsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExecutors not implemented")
}
func (UnimplementedLotsServer) GetSummary(context.Context, *GetLotRequest) (*GetLotSummaryReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSummary not implemented")
}
func (UnimplementedLotsServer) GetVisibleLots(context.Context, *GetVisibleLotsRequest) (*GetVisibleLotsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVisibleLots not implemented")
}
func (UnimplementedLotsServer) ExportLotAnnos(context.Context, *ExportLotAnnosRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportLotAnnos not implemented")
}
func (UnimplementedLotsServer) SetLotAnnoResult(context.Context, *SetLotAnnoResultRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetLotAnnoResult not implemented")
}
func (UnimplementedLotsServer) AllowDownloadAnnos(context.Context, *AllowDownloadAnnosRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AllowDownloadAnnos not implemented")
}
func (UnimplementedLotsServer) AddTag(context.Context, *types.TagRequest) (*types.TagList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTag not implemented")
}
func (UnimplementedLotsServer) DeleteTag(context.Context, *types.TagRequest) (*types.TagList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTag not implemented")
}
func (UnimplementedLotsServer) JobCountByLots(context.Context, *JobCountByLotidsRequest) (*JobCountByLotidsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JobCountByLots not implemented")
}
func (UnimplementedLotsServer) mustEmbedUnimplementedLotsServer() {}

// UnsafeLotsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LotsServer will
// result in compilation errors.
type UnsafeLotsServer interface {
	mustEmbedUnimplementedLotsServer()
}

func RegisterLotsServer(s grpc.ServiceRegistrar, srv LotsServer) {
	s.RegisterService(&Lots_ServiceDesc, srv)
}

func _Lots_CreateLot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).CreateLot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_CreateLot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).CreateLot(ctx, req.(*CreateLotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_CloneLot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloneLotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).CloneLot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_CloneLot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).CloneLot(ctx, req.(*CloneLotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_UpdateLot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).UpdateLot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_UpdateLot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).UpdateLot(ctx, req.(*UpdateLotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_DeleteLot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).DeleteLot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_DeleteLot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).DeleteLot(ctx, req.(*DeleteLotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_ListLotsByExecutor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLotsByExecutorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).ListLotsByExecutor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_ListLotsByExecutor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).ListLotsByExecutor(ctx, req.(*ListLotsByExecutorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_GetLot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).GetLot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_GetLot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).GetLot(ctx, req.(*GetLotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_ListLot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).ListLot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_ListLot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).ListLot(ctx, req.(*ListLotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_StartLot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).StartLot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_StartLot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).StartLot(ctx, req.(*GetLotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_PauseLot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).PauseLot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_PauseLot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).PauseLot(ctx, req.(*GetLotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_CancelLot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).CancelLot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_CancelLot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).CancelLot(ctx, req.(*GetLotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_ListExecteams_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListExecteamsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).ListExecteams(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_ListExecteams_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).ListExecteams(ctx, req.(*ListExecteamsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_AssignExecteam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignExecteamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).AssignExecteam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_AssignExecteam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).AssignExecteam(ctx, req.(*AssignExecteamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_ManageExecutors_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ManageExecutorsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).ManageExecutors(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_ManageExecutors_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).ManageExecutors(ctx, req.(*ManageExecutorsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_ListExecutors_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListExecutorsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).ListExecutors(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_ListExecutors_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).ListExecutors(ctx, req.(*ListExecutorsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_GetSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).GetSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_GetSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).GetSummary(ctx, req.(*GetLotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_GetVisibleLots_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVisibleLotsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).GetVisibleLots(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_GetVisibleLots_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).GetVisibleLots(ctx, req.(*GetVisibleLotsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_ExportLotAnnos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportLotAnnosRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).ExportLotAnnos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_ExportLotAnnos_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).ExportLotAnnos(ctx, req.(*ExportLotAnnosRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_SetLotAnnoResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLotAnnoResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).SetLotAnnoResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_SetLotAnnoResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).SetLotAnnoResult(ctx, req.(*SetLotAnnoResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_AllowDownloadAnnos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllowDownloadAnnosRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).AllowDownloadAnnos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_AllowDownloadAnnos_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).AllowDownloadAnnos(ctx, req.(*AllowDownloadAnnosRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_AddTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(types.TagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).AddTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_AddTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).AddTag(ctx, req.(*types.TagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_DeleteTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(types.TagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).DeleteTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_DeleteTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).DeleteTag(ctx, req.(*types.TagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lots_JobCountByLots_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobCountByLotidsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotsServer).JobCountByLots(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lots_JobCountByLots_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotsServer).JobCountByLots(ctx, req.(*JobCountByLotidsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Lots_ServiceDesc is the grpc.ServiceDesc for Lots service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Lots_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "anno.v1.Lots",
	HandlerType: (*LotsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateLot",
			Handler:    _Lots_CreateLot_Handler,
		},
		{
			MethodName: "CloneLot",
			Handler:    _Lots_CloneLot_Handler,
		},
		{
			MethodName: "UpdateLot",
			Handler:    _Lots_UpdateLot_Handler,
		},
		{
			MethodName: "DeleteLot",
			Handler:    _Lots_DeleteLot_Handler,
		},
		{
			MethodName: "ListLotsByExecutor",
			Handler:    _Lots_ListLotsByExecutor_Handler,
		},
		{
			MethodName: "GetLot",
			Handler:    _Lots_GetLot_Handler,
		},
		{
			MethodName: "ListLot",
			Handler:    _Lots_ListLot_Handler,
		},
		{
			MethodName: "StartLot",
			Handler:    _Lots_StartLot_Handler,
		},
		{
			MethodName: "PauseLot",
			Handler:    _Lots_PauseLot_Handler,
		},
		{
			MethodName: "CancelLot",
			Handler:    _Lots_CancelLot_Handler,
		},
		{
			MethodName: "ListExecteams",
			Handler:    _Lots_ListExecteams_Handler,
		},
		{
			MethodName: "AssignExecteam",
			Handler:    _Lots_AssignExecteam_Handler,
		},
		{
			MethodName: "ManageExecutors",
			Handler:    _Lots_ManageExecutors_Handler,
		},
		{
			MethodName: "ListExecutors",
			Handler:    _Lots_ListExecutors_Handler,
		},
		{
			MethodName: "GetSummary",
			Handler:    _Lots_GetSummary_Handler,
		},
		{
			MethodName: "GetVisibleLots",
			Handler:    _Lots_GetVisibleLots_Handler,
		},
		{
			MethodName: "ExportLotAnnos",
			Handler:    _Lots_ExportLotAnnos_Handler,
		},
		{
			MethodName: "SetLotAnnoResult",
			Handler:    _Lots_SetLotAnnoResult_Handler,
		},
		{
			MethodName: "AllowDownloadAnnos",
			Handler:    _Lots_AllowDownloadAnnos_Handler,
		},
		{
			MethodName: "AddTag",
			Handler:    _Lots_AddTag_Handler,
		},
		{
			MethodName: "DeleteTag",
			Handler:    _Lots_DeleteTag_Handler,
		},
		{
			MethodName: "JobCountByLots",
			Handler:    _Lots_JobCountByLots_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "anno/v1/lot.proto",
}
