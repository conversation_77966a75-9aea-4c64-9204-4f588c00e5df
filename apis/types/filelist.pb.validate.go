// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: types/filelist.proto

package types

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Filelist with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Filelist) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Filelist with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FilelistMultiError, or nil
// if none found.
func (m *Filelist) ValidateAll() error {
	return m.validate(true)
}

func (m *Filelist) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FilelistValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FilelistValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FilelistValidationError{
					field:  fmt.Sprintf("Files[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FilelistMultiError(errors)
	}

	return nil
}

// FilelistMultiError is an error wrapping multiple validation errors returned
// by Filelist.ValidateAll() if the designated constraints aren't met.
type FilelistMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilelistMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilelistMultiError) AllErrors() []error { return m }

// FilelistValidationError is the validation error returned by
// Filelist.Validate if the designated constraints aren't met.
type FilelistValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilelistValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilelistValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilelistValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilelistValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilelistValidationError) ErrorName() string { return "FilelistValidationError" }

// Error satisfies the builtin error interface
func (e FilelistValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilelist.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilelistValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilelistValidationError{}

// Validate checks the field values on Filelist_File with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Filelist_File) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Filelist_File with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Filelist_FileMultiError, or
// nil if none found.
func (m *Filelist_File) ValidateAll() error {
	return m.validate(true)
}

func (m *Filelist_File) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	// no validation rules for Size

	// no validation rules for Name

	if len(errors) > 0 {
		return Filelist_FileMultiError(errors)
	}

	return nil
}

// Filelist_FileMultiError is an error wrapping multiple validation errors
// returned by Filelist_File.ValidateAll() if the designated constraints
// aren't met.
type Filelist_FileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Filelist_FileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Filelist_FileMultiError) AllErrors() []error { return m }

// Filelist_FileValidationError is the validation error returned by
// Filelist_File.Validate if the designated constraints aren't met.
type Filelist_FileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Filelist_FileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Filelist_FileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Filelist_FileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Filelist_FileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Filelist_FileValidationError) ErrorName() string { return "Filelist_FileValidationError" }

// Error satisfies the builtin error interface
func (e Filelist_FileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilelist_File.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Filelist_FileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Filelist_FileValidationError{}
