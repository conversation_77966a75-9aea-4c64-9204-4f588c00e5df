// source: anno/v1/role.proto
package biz

import (
	"context"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type Role struct {
	ID    int64  `json:"id" gorm:"default:null"`
	Name  string `json:"name" gorm:"default:null"`
	Langs StrMap `json:"langs" gorm:"default:null"`
}

func (o *Role) GetID() int64 { return o.ID }
func (o *Role) EnsureID() {
	if o.ID == 0 {
		o.ID = kid.NewID()
	}
}
func (o *Role) GetUid() string { return kid.StringID(o.ID) }
func (o *Role) UidFld() string { return RoleSfldName }

const RoleTableName = "roles"

var (
	Role_             = field.RegObject(&Role{})
	RoleUpdatableFlds = field.NewModel(RoleTableName, Role_)

	RoleSfldName  = field.Sname(&Role_.Name)
	RoleSfldLangs = field.Sname(&Role_.Langs)
)

type RoleListFilter struct {
	Uids        []string
	NamePattern string
}

type RolesRepo interface {
	Create(context.Context, *Role) (*Role, error)
	Update(context.Context, *Role, *FieldMask) (*Role, error)
	GetByID(context.Context, int64) (*Role, error)
	GetByUid(context.Context, string) (*Role, error)
	DeleteByID(context.Context, int64) error
	DeleteByUid(context.Context, string) error
	List(context.Context, *RoleListFilter, Pager) ([]*Role, error)
	Count(context.Context, *RoleListFilter) (int, error)
}

type RolesBiz struct {
	repo RolesRepo
	log  *log.Helper
}

func NewRolesBiz(repo RolesRepo, logger log.Logger) *RolesBiz {
	return &RolesBiz{repo: repo, log: log.NewHelper(logger)}
}

func (o *RolesBiz) Create(ctx context.Context, p *Role) (*Role, error) {
	o.log.Info(ctx, "CreateRole", "param", p)
	return o.repo.Create(ctx, p)
}

func (o *RolesBiz) Update(ctx context.Context, p *Role, fldMask *FieldMask) (*Role, error) {
	o.log.Info(ctx, "UpdateRole", "param", p)
	return o.repo.Update(ctx, p, fldMask)
}

func (o *RolesBiz) GetByID(ctx context.Context, id int64) (*Role, error) {
	return o.repo.GetByID(ctx, id)
}

func (o *RolesBiz) GetByUid(ctx context.Context, uid string) (*Role, error) {
	return o.repo.GetByUid(ctx, uid)
}

func (o *RolesBiz) DeleteByID(ctx context.Context, id int64) error {
	o.log.Info(ctx, "DeleteByIDRole", "id", id)
	return o.repo.DeleteByID(ctx, id)
}

func (o *RolesBiz) DeleteByUid(ctx context.Context, uid string) error {
	o.log.Info(ctx, "DeleteByUidRole", "uid", uid)
	return o.repo.DeleteByUid(ctx, uid)
}

func (o *RolesBiz) List(ctx context.Context, filter *RoleListFilter, pager Pager) ([]*Role, error) {
	o.log.Info(ctx, "ListRole", "param", filter)
	return o.repo.List(ctx, filter, pager)
}

func (o *RolesBiz) Count(ctx context.Context, filter *RoleListFilter) (int, error) {
	return o.repo.Count(ctx, filter)
}
