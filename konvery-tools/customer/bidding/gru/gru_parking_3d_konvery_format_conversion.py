import zipfile
import os
import json
import shutil
import sys

sys.path.append('.')
from customer.common import tools


def unzip_file(input_dir):
    unzip_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_unzip')
    result_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_result')
    if os.path.exists(unzip_dir):
        shutil.rmtree(unzip_dir)
    with zipfile.ZipFile(input_dir, 'r') as zip_ref:
        zip_ref.extractall(unzip_dir)

    try:
        os.mkdir(result_dir)
    except FileExistsError:
        shutil.rmtree(result_dir)
        os.mkdir(result_dir)
    return unzip_dir, result_dir


def json_convert(json_dir, track_id_mapping):
    json_data = json.load(open(json_dir))

    # construct result dict
    result_dict = {
        'from': 'semi-outsource'
    }
    object_list = []

    for item in json_data["element_annos"][0]["rawdata_annos"][0]["objects"]:
        if item['track_id'] not in track_id_mapping:
            track_id_mapping[item['track_id']] = len(track_id_mapping) + 1
        params = item["label"]["widget"]["data"]
        euler_angles = tools.convert_quaternion_to_xyz(params[-1:] + params[6:9])
        object_dict = {
            'LABEL': 'HUMAN',
            'bounding_box': {
                'height': params[5],
                'length': params[3],
                'width': params[4],
            },
            'heading': euler_angles[2],
            'id': track_id_mapping[item['track_id']],
            'position': {
                'x': params[0],
                'y': params[1],
                'z': params[2],
            },
            'type': item["label"]["name"],
        }

        # fix additional_properties key error
        additional_properties = 'ADDITIONAL_PROPERTIES'
        attrs = {attr["name"]: attr["values"] for attr in item["label"]["attrs"]}
        if item['label']['name'] == 'BARRIER_GATE_CLOSE':
            additional_properties = 'CAMERA_NOT_VISIBLE'

        if len(attrs[additional_properties]) > 1 or \
                attrs[additional_properties][0] != 'NORMAL':
            if len(attrs[additional_properties]) > 1 and \
                    'NORMAL' in attrs[additional_properties]:
                # if it's a list and NORMAL is an element, remove NORMAL from the list
                object_dict['attribute'] = attrs[additional_properties].remove('NORMAL')
            else:
                object_dict['attribute'] = attrs[additional_properties]

        if item["label"]["name"] == 'SMALLCAR' or item["label"]["name"] == 'BIGCAR':
            object_dict['rearview_mirror'] = \
                attrs['MIRROR_FOLDING'][0]

        object_list.append(object_dict)

    result_dict['objects'] = object_list

    return result_dict


def run(input_dir):
    file_counter = 0
    # unzip file
    unzip_dir, result_dir = unzip_file(input_dir)
    annos_dir = os.path.join(unzip_dir, "annos")
    for collection in tools.listdir(annos_dir):
        track_id_mapping = dict()
        collection_dir = os.path.join(annos_dir, collection)
        out_collection_dir = os.path.join(result_dir, collection)
        os.mkdir(out_collection_dir)
        for element in tools.listdir(collection_dir, sort=True):
            file_counter += 1
            annos_file = os.path.join(collection_dir, element, 'annos.json')

            result_dict = json_convert(annos_file, track_id_mapping)

            res_file = os.path.join(out_collection_dir, element + '.txt')
            tools.write_json_file(result_dict, res_file)

    trans_result = {
        "code": 0,
        "output": result_dir,
        "err_msg": '成功',
        "summary": {
            '生成文件数': file_counter,
        }
    }
    return trans_result


if __name__ == "__main__":
    args = tools.get_args()
    run(args.input)
