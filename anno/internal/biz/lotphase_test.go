package biz

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
)

func Test_getPhasesChange_getEffectiveExecteams(t *testing.T) {
	const oldAnnoTeamUid = "anno_1"
	const newAnnoTeamUid = "anno_2"
	const oldReviewTeamUid = "review_1"
	const newReviewTeamUid = "review_2"

	cases := []struct {
		name                   string
		phases                 []*Lotphase
		updates                []*anno.AssignExecteamRequest_Phase
		expectedPhasesToCreate []*Lotphase
		expectedPhasesToDelete []*Lotphase
		expectedPhasesToCheck  []*PhaseQuota
		expectedTeamsHasUpdate bool
		expectedTeamsToGrant   []string
	}{
		{
			name: "change_a_team",
			phases: []*Lotphase{
				{Number: 1},
				{Number: 1, Execteam: oldAnnoTeamUid},
			},
			updates: []*anno.AssignExecteamRequest_Phase{
				{
					Phase:     1,
					Execteams: []*anno.Lotphase_Execteam{{Execteam: newAnnoTeamUid}},
				},
			},
			expectedPhasesToCreate: []*Lotphase{
				{
					Number:   1,
					Execteam: newAnnoTeamUid,
					Quota:    *serial.New(&ExecteamQuota{Quota: nil}),
				},
			},
			expectedPhasesToDelete: []*Lotphase{
				{
					Number:   1,
					Execteam: oldAnnoTeamUid,
				},
			},
			expectedPhasesToCheck:  nil,
			expectedTeamsHasUpdate: true,
			expectedTeamsToGrant:   []string{newAnnoTeamUid},
		},
		{
			name: "change_team_quota",
			phases: []*Lotphase{
				{Number: 1},
				{
					Number:   1,
					Execteam: oldAnnoTeamUid,
					Quota:    *serial.New(&ExecteamQuota{Quota: &anno.Lotphase_Quota{Min: 0, Max: 50}}),
				},
			},
			updates: []*anno.AssignExecteamRequest_Phase{
				{
					Phase: 1,
					Execteams: []*anno.Lotphase_Execteam{
						{
							Execteam: oldAnnoTeamUid,
							Quota:    &anno.Lotphase_Quota{Min: 0, Max: 60},
						},
					},
				},
			},
			expectedPhasesToCreate: nil,
			expectedPhasesToDelete: nil,
			expectedPhasesToCheck: []*PhaseQuota{
				{
					Phase: &Lotphase{
						Number:   1,
						Execteam: oldAnnoTeamUid,
						Quota:    *serial.New(&ExecteamQuota{Quota: &anno.Lotphase_Quota{Min: 0, Max: 50}}),
					},
					NewQuota: &anno.Lotphase_Quota{Min: 0, Max: 60},
				},
			},
			expectedTeamsHasUpdate: false,
			expectedTeamsToGrant:   []string{oldAnnoTeamUid},
		},
		{
			name: "change_all_teams",
			phases: []*Lotphase{
				{Number: 1},
				{Number: 1, Execteam: oldAnnoTeamUid},
				{Number: 2},
				{Number: 2, Execteam: oldReviewTeamUid},
			},
			updates: []*anno.AssignExecteamRequest_Phase{
				{
					Phase:     1,
					Execteams: []*anno.Lotphase_Execteam{{Execteam: newAnnoTeamUid}},
				},
				{
					Phase:     2,
					Execteams: []*anno.Lotphase_Execteam{{Execteam: newReviewTeamUid}},
				},
			},
			expectedPhasesToCreate: []*Lotphase{
				{
					Number:   1,
					Execteam: newAnnoTeamUid,
					Quota:    *serial.New(&ExecteamQuota{Quota: nil}),
				},
				{
					Number:   2,
					Execteam: newReviewTeamUid,
					Quota:    *serial.New(&ExecteamQuota{Quota: nil}),
				},
			},
			expectedPhasesToDelete: []*Lotphase{
				{
					Number:   1,
					Execteam: oldAnnoTeamUid,
				},
				{
					Number:   2,
					Execteam: oldReviewTeamUid,
				},
			},
			expectedPhasesToCheck:  nil,
			expectedTeamsHasUpdate: true,
			expectedTeamsToGrant:   []string{newAnnoTeamUid, newReviewTeamUid},
		},
		{
			name: "change_a_team_which_appears_in_different_phase",
			phases: []*Lotphase{
				{Number: 1},
				{Number: 1, Execteam: oldAnnoTeamUid},
				{Number: 2},
				{Number: 2, Execteam: oldAnnoTeamUid},
			},
			updates: []*anno.AssignExecteamRequest_Phase{
				{
					Phase:     2,
					Execteams: []*anno.Lotphase_Execteam{{Execteam: newAnnoTeamUid}},
				},
			},
			expectedPhasesToCreate: []*Lotphase{
				{
					Number:   2,
					Execteam: newAnnoTeamUid,
					Quota:    *serial.New(&ExecteamQuota{Quota: nil}),
				},
			},
			expectedPhasesToDelete: []*Lotphase{
				{
					Number:   2,
					Execteam: oldAnnoTeamUid,
					Quota:    ExecteamQuotaW{},
				},
			},
			expectedPhasesToCheck:  nil,
			expectedTeamsHasUpdate: true,
			expectedTeamsToGrant:   []string{oldAnnoTeamUid, newAnnoTeamUid},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			phasesToCreate, phasesToDelete, phasesToCheck, err := getPhasesChange(c.phases, c.updates)
			if err != nil {
				t.Fatalf("failed to calculate phases: %v", err)
			}
			assert.Equal(t, c.expectedPhasesToCreate, phasesToCreate)
			assert.Equal(t, c.expectedPhasesToDelete, phasesToDelete)
			assert.Equal(t, c.expectedPhasesToCheck, phasesToCheck)

			// getEffectiveExecteams
			newTeams, hasUpdate := getEffectiveExecteams(c.phases, phasesToCreate, phasesToDelete)
			assert.Equal(t, c.expectedTeamsHasUpdate, hasUpdate)
			assert.Equal(t, len(c.expectedTeamsToGrant), len(newTeams))
			assert.Subset(t, c.expectedTeamsToGrant, newTeams)
		})
	}
}
