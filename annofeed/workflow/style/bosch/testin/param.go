package boschtestin

import (
	"encoding/json"
	"fmt"
	"os"
	"path"
	"strconv"

	"annofeed/workflow/common"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/kmath"
)

// 矩形框
type BoxData struct {
	X      float64 `json:"x"`
	Y      float64 `json:"y"`
	Width  float64 `json:"width"`
	Height float64 `json:"height"`
	Rotate float64 `json:"rotate"`
}

func (o BoxData) toData() []float64 {
	return []float64{o.X, o.Y, o.Width, o.Height, o.Rotate}
}

// 立体框
type VolumeData struct {
	Position struct {
		X float64 `json:"x"`
		Y float64 `json:"y"`
		Z float64 `json:"z"`
	} `json:"position"`

	Dimension struct {
		L float64 `json:"l"`
		W float64 `json:"w"`
		H float64 `json:"h"`
	} `json:"dimension"`

	Rotation struct {
		X float64 `json:"x"`
		Y float64 `json:"y"`
		Z float64 `json:"z"`
	} `json:"rotation"`
}

func (o VolumeData) tpQuat() []float64 {
	quat := kmath.FromEulerAngles(kmath.RotationOrderXYZ, o.Rotation.X, o.Rotation.Y, o.Rotation.Z)
	return []float64{quat[0], quat[1], quat[2], quat[3]} // x, y, z, w
}

func (o VolumeData) toData() []float64 {
	data := []float64{
		o.Position.X, o.Position.Y, o.Position.Z,
		o.Dimension.L, o.Dimension.W, o.Dimension.H,
	}
	return append(data, o.tpQuat()...)
}

type Annotation struct {
	Type string `json:"type"`
	Data any    `json:"data"`

	BoxData    BoxData
	VolumeData VolumeData
}

func (o *Annotation) parseData() error {
	bytes, err := json.Marshal(o.Data)
	if err != nil {
		return err
	}

	switch o.Type {
	case "box":
		return json.Unmarshal(bytes, &o.BoxData)
	case "volume":
		return json.Unmarshal(bytes, &o.VolumeData)
	}
	return nil
}

func (o *Annotation) toWidget() *anno.Object_Widget {
	switch o.Type {
	case "box":
		return &anno.Object_Widget{
			Name: anno.WidgetName_box2d,
			Data: o.BoxData.toData(),
		}
	case "volume":
		return &anno.Object_Widget{
			Name: anno.WidgetName_cuboid,
			Data: o.VolumeData.toData(),
		}
	}
	return nil
}

type Attrs map[string][]string // attr -> values

func (o Attrs) toAttrs() []*anno.AttrAndValues {
	if o == nil {
		return []*anno.AttrAndValues{}
	}
	attrs := make([]*anno.AttrAndValues, 0)
	for attr, values := range o {
		attrs = append(attrs, &anno.AttrAndValues{Name: attr, Values: values})
	}
	return attrs
}

type LabelMeta struct {
	MarkStatus int `json:"mark_status"`
}

type CameraAnnotation struct {
	Class      string     `json:"class"`
	Attrs      Attrs      `json:"attrs"`
	IsAdd      int        `json:"is_add"`
	IsEdit     int        `json:"is_edit"`
	ID         string     `json:"id"`
	TrackID    int        `json:"track_id"`
	GroupID    int        `json:"group_id"`
	Annotation Annotation `json:"annotation"`
}

type LidarAnnotion struct {
	Class      string     `json:"class"`
	Attrs      Attrs      `json:"attrs"`
	ID         string     `json:"id"`
	TrackID    int        `json:"track_id"`
	PointNum   int        `json:"point_num"`
	GroupID    int        `json:"group_id"`
	Annotation Annotation `json:"annotation"`
}

type Params struct {
	LabelMeta   LabelMeta                     `json:"label_meta"`
	CameraAnnos map[string][]CameraAnnotation `json:"camera"` // camera_name -> CameraAnnotation
	LidarAnnos  []LidarAnnotion               `json:"lidar"`
}

func (p *Params) toAnnos(sourceDir, lidarName, imgName string) (anno.ExportAnnos, error) {
	elementAnno := &anno.ElementAnno{
		Index:  0,
		Name:   sourceDir,
		InsCnt: 0,
	}

	// lidar annotation
	lidarAnno := &anno.RawdataAnno{
		Name:     path.Join(sourceDir, common.LidarPCD),
		OrigName: path.Join(sourceDir, lidarName),
	}
	for _, la := range p.LidarAnnos {
		if err := la.Annotation.parseData(); err != nil {
			return anno.ExportAnnos{}, fmt.Errorf("parsing lidar data: %w", err)
		}
		lidarAnno.Objects = append(lidarAnno.Objects, &anno.Object{
			Uuid:    la.ID,
			TrackId: strconv.Itoa(la.TrackID),
			Label: &anno.Object_Label{
				Name:   la.Class,
				Widget: la.Annotation.toWidget(),
				Attrs:  la.Attrs.toAttrs(),
			},
		})
		elementAnno.InsCnt++
	}
	elementAnno.RawdataAnnos = append(elementAnno.RawdataAnnos, lidarAnno)

	// image annotation
	for _, cameraAnnos := range p.CameraAnnos { // there is only one image
		imgAnno := &anno.RawdataAnno{
			Name:     path.Join(sourceDir, imgName),
			OrigName: path.Join(sourceDir, imgName),
		}
		for _, ca := range cameraAnnos {
			if err := ca.Annotation.parseData(); err != nil {
				return anno.ExportAnnos{}, fmt.Errorf("parsing camera data: %w", err)
			}
			// imgAnno.Objects = append(imgAnno.Objects, &anno.Object{
			//	Uuid:    ca.ID,
			//	TrackId: strconv.Itoa(ca.TrackID),
			//	Label: &anno.Object_Label{
			//		Name:   ca.Class,
			//		Widget: ca.Annotation.toWidget(),
			//		Attrs:  ca.Attrs.toAttrs(),
			//	},
			// })
			// elementAnno.InsCnt++ // no need to increment
		}
		elementAnno.RawdataAnnos = append(elementAnno.RawdataAnnos, imgAnno)
	}

	return anno.ExportAnnos{
		ElementAnnos: []*anno.ElementAnno{elementAnno},
	}, nil
}

func ParseLabels(filePath string) (*Params, error) {
	bytes, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	params := &Params{}
	if err := json.Unmarshal(bytes, params); err != nil {
		return nil, err
	}

	return params, nil
}
