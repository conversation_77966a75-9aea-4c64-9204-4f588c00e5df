syntax = "proto3";

package annostat.v1;

import "google/api/annotations.proto";
// import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";
import "iam/v1/type.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/annostat/v1;annostat";
option java_multiple_files = true;
option java_package = "annostat.v1";

service Stats {
  // count orders
  rpc GetOrderCount (GetOrderCountRequest) returns (GetOrderCountReply) {
    option (google.api.http) = {
      get: "/v1/orders/count"
    };
  }

  // order-to-lot conversion statistics
  rpc GetOrderConversion (GetOrderCountRequest) returns (GetOrderConversionReply) {
    option (google.api.http) = {
      get: "/v1/orders/conversion"
    };
  }

  // count lots
  rpc GetLotCount (GetLotCountRequest) returns (GetLotCountReply) {
    option (google.api.http) = {
      get: "/v1/lots/count"
    };
  }

  // get ongoing lots
  rpc GetOngoingLots (GetOngoingLotsRequest) returns (GetOngoingLotsReply) {
    option (google.api.http) = {
      get: "/v1/lots/ongoing"
    };
  }

  // lot statistics by phase
  rpc GetLotStatus (GetLotStatusRequest) returns (GetLotStatusReply) {
    option (google.api.http) = {
      get: "/v1/lots/{uid}/status"
    };
  }

  // lot statistics by label
  rpc GetLotLabelStat (GetLotStatusRequest) returns (GetLotLabelStatReply) {
    option (google.api.http) = {
      get: "/v1/lots/{uid}/labels"
    };
  }

  // lot statistics by executor
  rpc GetLotStatByExecutor (GetLotStatByExecutorRequest) returns (GetLotStatByExecutorReply) {
    option (google.api.http) = {
      get: "/v1/lots/{uid}/phases/{phase}/stat-by-exec"
    };
  }

  // get production by time
  rpc ProductionByTime (ProductionByTimeRequest) returns (ProductionByTimeReply) {
    option (google.api.http) = {
      get: "/v1/prod-by-time"
    };
  }
}

message GetOrderCountRequest {
  message Filter {
    // filter by creation time range
    TimeRange time_range = 1;
    // filter by owner organization
    repeated string owner_orgs = 2;
  }

  Filter filter = 1;
}

message GetOrderCountReply {
  option (openapi.v3.schema) = {
    required: ["total", "ongoing", "paused", "finished"]
  };

  int32 total = 1;
  int32 ongoing = 2;
  int32 paused = 3;
  int32 finished = 4;
}

message GetOrderConversionReply {
  option (openapi.v3.schema) = {
    required: ["orders", "lots"]
  };

  int32 orders = 1;
  int32 lots = 2;
}

message GetLotCountRequest {
  LotFilter filter = 1;
}

message GetLotCountReply {
  option (openapi.v3.schema) = {
    required: ["total", "ongoing", "paused", "finished"]
  };

  int32 total = 1;
  int32 ongoing = 2;
  int32 paused = 3;
  int32 finished = 4;
}

message GetLotStatusRequest {
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
}

message GetLotStatusReply {
  option (openapi.v3.schema) = {
    required: ["total_elems", "phases"]
  };

  message Phase {
    option (openapi.v3.schema) = {
      required: ["elems_to_work", "estimated_days_left"]
    };

    // number of elements in and before this phase
    int32 elems_to_work = 1;
    // estimated number of days needed to close this phase (days needed to close prior phases are not included);
    // -1 means estimation is unavailable
    float estimated_days_left = 2;
  }

  int32 total_elems = 1;
  // status of each phase
  repeated Phase phases = 2;
}

message GetLotLabelStatReply {
  message Cuboid {
    option (openapi.v3.schema) = {
      required: ["ins_cnt", "scales"]
    };

    // // name of the label
    // string name = 1;

    // number of cuboid objects with this label
    int32 ins_cnt = 2;
    // statistical cuboid scale on each axis: x, y, z
    repeated double scales = 3;
  }

  // cuboid statistics, indexed by cuboid name
  map<string, Cuboid> cuboids = 1;
}

message TimeRange {
  option (openapi.v3.schema) = {
    required: ["from", "to"]
  };

  // time is within [from, to)
  google.protobuf.Timestamp from = 1;
  google.protobuf.Timestamp to = 2;
}

message DataType {
  enum Enum {
    unspecified = 0;
    // one image in an element
    image = 1;
    // one point-cloud file in an element
    pointcloud = 2;
    // multiple image files in an element
    fusion2d = 3;
    // multiple image files and a point-cloud file in an element
    fusion3d = 4;
  }
}

message LotType {
  enum Enum {
    unspecified = 0;
    annotate = 1;
    segment = 2;
    // classify = 3;
  }
}

message LotFilter {
  option (openapi.v3.schema) = {
    required: []
  };

  // filter lot by creation time range
  TimeRange time_range = 1;
  // filter lot by type
  repeated LotType.Enum lot_types = 2 [(validate.rules).repeated.items.enum = {defined_only: true}];
  // filter lot by data type
  repeated DataType.Enum data_types = 3 [(validate.rules).repeated.items.enum = {defined_only: true}];
  // filter lot by uid
  repeated string lot_uids = 4 [(validate.rules).repeated.items.string = {pattern: "^[a-z0-9]{11}$"}];
  // filter lot by owner organization
  repeated string owner_orgs = 5 [(validate.rules).repeated.items.string = {pattern: "^[a-z0-9]{11}$"}];
  // filter lot by executor organization
  repeated string executor_orgs = 6 [(validate.rules).repeated.items.string = {pattern: "^[a-z0-9]{11}$"}];
  // filter lot by executor uid
  repeated string executor_uids = 7 [(validate.rules).repeated.items.string = {pattern: "^[a-z0-9]{11}$"}];
}

message ProductionByTimeRequest {
  // query scope
  LotFilter filter = 1;
  // group results by time_unit (in seconds)
  int32 time_unit = 2 [(validate.rules).int32 = {in: [0, 3600, 86400]}];
  // items to count: anno2d, anno3d, elem, job
  repeated string items = 3 [(validate.rules).repeated.items.string = {in: ["anno2d", "anno3d", "elem", "job"]}];
}

message ProductionByTimeReply {
  option (openapi.v3.schema) = {
    required: ["units"]
  };

  message Unit {
    option (openapi.v3.schema) = {
      required: ["time_range", "items"]
    };

    // time range of this unit
    TimeRange time_range = 1;
    // item name and count
    map<string, float> items = 2;
  }

  // items and count in each time unit
  repeated Unit units = 1;
}

message GetLotStatByExecutorRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "phase"]
  };

  // lot uid
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // phase number, start from 1
  int32 phase = 2 [(validate.rules).int32.gte = 1];
  // true to stats by execution teams, false to stats by individuals
  bool by_execteam = 3;
  // stat time range; if not specified, default to last working day
  TimeRange time_range = 4;

  int32 page = 14;
  int32 pagesz = 15 [(validate.rules).int32 = {gte:0, lte: 100}];
}

message ExecutorCounter {
  option (openapi.v3.schema) = {
    required: ["executor", "jobs", "elems", "ins", "ins_by_widget",
              "jobs_per_hour", "elems_per_hour", "elem_accuracy", "ins_accuracy"]
  };

  // executor can be a user or a team
  iam.v1.BaseUser executor = 1;
  // number of jobs submitted
  int32 jobs = 2;
  // number of elements submitted
  int32 elems = 3;
  // number of annotations submitted
  int32 ins = 4;
  // ins by widget: anno2d/anno3d/...
  map<string, int32> ins_by_widget = 5;
  // number of jobs submitted per hour
  float jobs_per_hour = 6;
  // number of elements submitted per hour
  float elems_per_hour = 7;
  // element accuracy: [0, 1]
  float elem_accuracy = 8;
  // ins accuracy: [0, 1]
  float ins_accuracy = 9;
}

message GetLotStatByExecutorReply {
  option (openapi.v3.schema) = {
    required: ["total", "counters"]
  };

  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  // executor statistics
  repeated ExecutorCounter counters = 2;
}

message GetOngoingLotsRequest {
  // // order by field: '-' prefix means descending order; '+' prefix means ascending order
  // string order = 1;

  int32 page = 14;
  int32 pagesz = 15 [(validate.rules).int32 = {gte:0, lte: 100}];
}

message GetOngoingLotsReply {
  option (openapi.v3.schema) = {
    required: ["total", "lots"]
  };

  message Phase {
    option (openapi.v3.schema) = {
      required: ["number", "name", "type", "estimated_days_left"]
    };

    message Type {
      enum Enum {
        unspecified = 0;
        label = 1;
        review = 2;
      }
    }

    // phase number, starts from 1
    int32 number = 1;
    string name = 2;
    Type.Enum type = 3 [(validate.rules).enum = {defined_only: true, not_in: [0]}];

    // estimated number of days needed to close this phase (days needed to close prior phases are not included);
    // -1 means estimation is unavailable
    float estimated_days_left = 10;
  }
  message Lot {
    option (openapi.v3.schema) = {
      required: ["uid", "name", "data_type", "data_size", "phases", "exp_end_time", "created_at"]
    };

    string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
    string name = 2;
    DataType.Enum data_type = 3 [(validate.rules).enum = {defined_only: true}];
    // number of elements in the lot
    int32 data_size = 4;

    repeated Phase phases = 10;

    // expected end time
    google.protobuf.Timestamp exp_end_time = 14;
    google.protobuf.Timestamp created_at = 15;
  }

  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated Lot lots = 2;
}
