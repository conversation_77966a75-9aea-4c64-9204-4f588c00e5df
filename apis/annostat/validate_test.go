package annostat

import (
	"testing"

	"gitlab.rp.konvery.work/platform/apis/annostat/v1"
)

func Test_GetLotStatByExecutorRequest(t *testing.T) {
	req := annostat.GetLotStatByExecutorRequest{
		Uid:        "uid12345678",
		Phase:      1,
		ByExecteam: false,
		TimeRange:  nil,
		Page:       0,
		Pagesz:     10,
	}

	cases := []struct {
		name       string
		newReqFunc func(req annostat.GetLotStatByExecutorRequest) annostat.GetLotStatByExecutorRequest
		expectErr  bool
	}{
		{
			"happy_flow",
			nil,
			false,
		},
		{
			"Uid_is_wrong",
			func(req annostat.GetLotStatByExecutorRequest) annostat.GetLotStatByExecutorRequest {
				req.Uid = "wrong"
				return req
			},
			true,
		},
		{
			"Pagesz_is_too_big",
			func(req annostat.GetLotStatByExecutorRequest) annostat.GetLotStatByExecutorRequest {
				req.Pagesz = 200
				return req
			},
			true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			newReq := req
			if c.newReqFunc != nil {
				newReq = c.newReqFunc(req)
			}
			if err := newReq.Validate(); (err != nil) != c.expectErr {
				t.Errorf("Validate() expected error: %v, got: %v", c.expectErr, err)
			}
			if err := newReq.ValidateAll(); (err != nil) != c.expectErr {
				t.Errorf("ValidateAll() expected error: %v, got: %v", c.expectErr, err)
			}
		})
	}
}
