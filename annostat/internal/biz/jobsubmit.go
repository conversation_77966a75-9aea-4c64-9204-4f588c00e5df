// source: annostat/v1/Jobsubmit.proto
package biz

import (
	"time"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
)

type Jobsubmit struct {
	ID              int64      `json:"id"`
	JobID           int64      `json:"job_id" gorm:"default:null"`
	LotID           int64      `json:"lot_id" gorm:"default:null"`
	ExecutorUid     string     `json:"executor_uid" gorm:"default:null"`      // executor uid
	ExecteamUid     string     `json:"execteam_uid" gorm:"default:null"`      // executor org uid
	LastExecutorUid string     `json:"last_executor_uid" gorm:"default:null"` // last phase executor uid
	LastExecteamUid string     `json:"last_execteam_uid" gorm:"default:null"` // last phase executor org uid
	Action          string     `json:"action" gorm:"default:null"`
	Elems           int32      `json:"elems" gorm:"default:null"`
	Phase           int32      `json:"phase" gorm:"default:null"` // starts from 1
	InsCnt          int32      `json:"ins_cnt" gorm:"default:null"`
	InsTotal        int32      `json:"ins_total" gorm:"default:null"` // include interpolated objects
	Ins2d           int32      `json:"ins_2d" gorm:"default:null;column:ins_2d"`
	Ins3d           int32      `json:"ins_3d" gorm:"default:null;column:ins_3d"`
	InsByClass      InsByClass `json:"ins_by_class" gorm:"default:null"`
	RrElems         int32      `json:"rr_elems" gorm:"default:null"`
	RrIns           int32      `json:"rr_ins" gorm:"default:null"`
	RrIns2d         int32      `json:"rr_ins_2d" gorm:"default:null;column:rr_ins_2d"`
	RrIns3d         int32      `json:"rr_ins_3d" gorm:"default:null;column:rr_ins_3d"`
	RrInsByClass    InsByClass `json:"rr_ins_by_class" gorm:"default:null"`
	Duration        int32      `json:"duration" gorm:"default:null"`
	Final           bool       `json:"final" gorm:"default:null"`
	// Redo            int32      `json:"redo" gorm:"default:null"`
	CreatedAt time.Time `json:"created_at" gorm:"default:null"`
	ClaimedAt time.Time `json:"claimed_at" gorm:"default:null"` // ClaimedAt = CreatedAt - Duration
}

func (o *Jobsubmit) GetID() int64 { return o.ID }

const JobsubmitTableName = "jobsubmits"

var (
	jobsubmit_ = field.RegObject(&Jobsubmit{})
	// JobsubmitUpdatableFlds = field.NewModel(jobsubmit_)

	// field name in snake case
	JobsubmitSfldLotID           = field.Sname(&jobsubmit_.LotID)
	JobsubmitSfldJobID           = field.Sname(&jobsubmit_.JobID)
	JobsubmitSfldPhase           = field.Sname(&jobsubmit_.Phase)
	JobsubmitSfldAction          = field.Sname(&jobsubmit_.Action)
	JobsubmitSfldExecutorUid     = field.Sname(&jobsubmit_.ExecutorUid)
	JobsubmitSfldExecteamUid     = field.Sname(&jobsubmit_.ExecteamUid)
	JobsubmitSfldLastExecutorUid = field.Sname(&jobsubmit_.LastExecutorUid)
	JobsubmitSfldLastExecteamUid = field.Sname(&jobsubmit_.LastExecteamUid)
	JobsubmitSfldInsCnt          = field.Sname(&jobsubmit_.InsCnt)
	JobsubmitSfldInsTotal        = field.Sname(&jobsubmit_.InsTotal)
	JobsubmitSfldIns2d           = field.Sname(&jobsubmit_.Ins2d)
	JobsubmitSfldIns3d           = field.Sname(&jobsubmit_.Ins3d)
	JobsubmitSfldElems           = field.Sname(&jobsubmit_.Elems)
	JobsubmitSfldRrIns           = field.Sname(&jobsubmit_.RrIns)
	JobsubmitSfldRrIns2d         = field.Sname(&jobsubmit_.RrIns2d)
	JobsubmitSfldRrIns3d         = field.Sname(&jobsubmit_.RrIns3d)
	JobsubmitSfldRrElems         = field.Sname(&jobsubmit_.RrElems)
	JobsubmitSfldCreatedAt       = field.Sname(&jobsubmit_.CreatedAt)
	JobsubmitSfldClaimedAt       = field.Sname(&jobsubmit_.ClaimedAt)
	JobsubmitSfldFinal           = field.Sname(&jobsubmit_.Final)
)

type JobsubmitListFilter struct {
	TimeRange
	IDs     []int64
	LotID   int64
	JobID   int64
	Phases  []int32
	Actions []string
}

func (o *JobsubmitListFilter) Apply(tx Tx) Tx {
	if o == nil {
		return tx
	}
	tbl := "jobsubmits."
	tx = ApplyFieldFilter(tx, tbl+"id", o.IDs)
	tx = ApplyFieldFilter(tx, tbl+JobsubmitSfldLotID, o.LotID)
	tx = ApplyFieldFilter(tx, tbl+JobsubmitSfldJobID, o.JobID)
	tx = ApplyFieldFilter(tx, tbl+JobsubmitSfldPhase, o.Phases)
	tx = ApplyFieldFilter(tx, tbl+JobsubmitSfldAction, o.Actions)
	tx = ApplyTimeRangeFilter(tx, tbl+JobsubmitSfldCreatedAt, &o.TimeRange)
	return tx
}

type JobsubmitsRepo repo.GenericRepo[Jobsubmit]

// type JobsubmitsBiz struct {
// 	repo   JobsubmitsRepo
// 	bgtask BackgroundTask
// 	log    *log.Helper
// }

// func NewJobsubmitsBiz(repo JobsubmitsRepo, bgtask BackgroundTask, logger log.Logger) *JobsubmitsBiz {
// 	return &JobsubmitsBiz{repo: repo, bgtask: bgtask, log: log.NewHelper(logger)}
// }
