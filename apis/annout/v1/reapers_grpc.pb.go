// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: annout/v1/reapers.proto

package annout

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Reapers_CreateReaper_FullMethodName   = "/annout.v1.Reapers/CreateReaper"
	Reapers_GetReaper_FullMethodName      = "/annout.v1.Reapers/GetReaper"
	Reapers_ExportLotAnnos_FullMethodName = "/annout.v1.Reapers/ExportLotAnnos"
)

// ReapersClient is the client API for Reapers service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ReapersClient interface {
	// create a reaper to collect annotations from a lot and export it according to config.
	CreateReaper(ctx context.Context, in *CreateReaperRequest, opts ...grpc.CallOption) (*Reaper, error)
	GetReaper(ctx context.Context, in *GetReaperRequest, opts ...grpc.CallOption) (*Reaper, error)
	ExportLotAnnos(ctx context.Context, in *ExportLotAnnosRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type reapersClient struct {
	cc grpc.ClientConnInterface
}

func NewReapersClient(cc grpc.ClientConnInterface) ReapersClient {
	return &reapersClient{cc}
}

func (c *reapersClient) CreateReaper(ctx context.Context, in *CreateReaperRequest, opts ...grpc.CallOption) (*Reaper, error) {
	out := new(Reaper)
	err := c.cc.Invoke(ctx, Reapers_CreateReaper_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reapersClient) GetReaper(ctx context.Context, in *GetReaperRequest, opts ...grpc.CallOption) (*Reaper, error) {
	out := new(Reaper)
	err := c.cc.Invoke(ctx, Reapers_GetReaper_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reapersClient) ExportLotAnnos(ctx context.Context, in *ExportLotAnnosRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Reapers_ExportLotAnnos_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReapersServer is the server API for Reapers service.
// All implementations must embed UnimplementedReapersServer
// for forward compatibility
type ReapersServer interface {
	// create a reaper to collect annotations from a lot and export it according to config.
	CreateReaper(context.Context, *CreateReaperRequest) (*Reaper, error)
	GetReaper(context.Context, *GetReaperRequest) (*Reaper, error)
	ExportLotAnnos(context.Context, *ExportLotAnnosRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedReapersServer()
}

// UnimplementedReapersServer must be embedded to have forward compatible implementations.
type UnimplementedReapersServer struct {
}

func (UnimplementedReapersServer) CreateReaper(context.Context, *CreateReaperRequest) (*Reaper, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateReaper not implemented")
}
func (UnimplementedReapersServer) GetReaper(context.Context, *GetReaperRequest) (*Reaper, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReaper not implemented")
}
func (UnimplementedReapersServer) ExportLotAnnos(context.Context, *ExportLotAnnosRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportLotAnnos not implemented")
}
func (UnimplementedReapersServer) mustEmbedUnimplementedReapersServer() {}

// UnsafeReapersServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReapersServer will
// result in compilation errors.
type UnsafeReapersServer interface {
	mustEmbedUnimplementedReapersServer()
}

func RegisterReapersServer(s grpc.ServiceRegistrar, srv ReapersServer) {
	s.RegisterService(&Reapers_ServiceDesc, srv)
}

func _Reapers_CreateReaper_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateReaperRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReapersServer).CreateReaper(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reapers_CreateReaper_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReapersServer).CreateReaper(ctx, req.(*CreateReaperRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reapers_GetReaper_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReaperRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReapersServer).GetReaper(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reapers_GetReaper_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReapersServer).GetReaper(ctx, req.(*GetReaperRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Reapers_ExportLotAnnos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportLotAnnosRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReapersServer).ExportLotAnnos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reapers_ExportLotAnnos_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReapersServer).ExportLotAnnos(ctx, req.(*ExportLotAnnosRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Reapers_ServiceDesc is the grpc.ServiceDesc for Reapers service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Reapers_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "annout.v1.Reapers",
	HandlerType: (*ReapersServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateReaper",
			Handler:    _Reapers_CreateReaper_Handler,
		},
		{
			MethodName: "GetReaper",
			Handler:    _Reapers_GetReaper_Handler,
		},
		{
			MethodName: "ExportLotAnnos",
			Handler:    _Reapers_ExportLotAnnos_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "annout/v1/reapers.proto",
}
