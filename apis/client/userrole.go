package client

import (
	"strings"

	"github.com/samber/lo"
)

const (
	TeamRoleOwner   = "owner"
	TeamRoleManager = "manager"
	TeamRoleMember  = "member"

	SysRoleRoot       = "root"
	SysRoleAdmin      = "admin"
	SysRoleService    = "service"
	SysRoleSubAdmin   = "subAdmin"
	SysRoleInspector  = "inspector"
	SysRoleKAM        = "kam"
	SysRolePM         = "pm"
	SysRoleSuperAdmin = "superadmin"
	SysRoleDemo       = "demo"
)

func TeamRoles() []string        { return []string{TeamRoleOwner, TeamRoleManager, TeamRoleMember} }
func TeamManagerRoles() []string { return []string{TeamRoleOwner, TeamRoleManager} }
func SysRoles() []string {
	return []string{SysRoleRoot, SysRoleAdmin, SysRoleService, SysRoleSubAdmin,
		SysRoleInspector, SysRoleKAM, SysRolePM, SysRoleSuperAdmin, SysRoleDemo}
}
func SysRoleAdmins() []string {
	return []string{SysRoleRoot, SysRoleAdmin, SysRoleService, SysRoleSuperAdmin}
}
func IsSysRole(role string) bool { return lo.Contains(SysRoles(), role) }

func IsPrivileged(role string) bool   { return lo.Contains(SysRoleAdmins(), role) }
func IsPrivilegedUser(u *User) bool   { return u != nil && IsPrivileged(u.Role) }
func IsTeamManagers(role string) bool { return lo.Contains(TeamManagerRoles(), role) }

// InScopeOf returns true if hierarchy1 is in the scope of hierarchy2,
func InScopeOf(hierarchy1, hierarchy2 string) bool {
	return strings.HasPrefix(hierarchy1+"/", hierarchy2+"/")
}

const (
	ScopeClsGroup = "IamGroup"
	ScopeClsUser  = "IamUser"
)

func GroupScope(uid string) string { return ScopeClsGroup + ":" + uid }
func UserScope(uid string) string  { return ScopeClsUser + ":" + uid }
