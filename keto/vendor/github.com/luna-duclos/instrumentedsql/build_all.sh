#!/bin/bash

echo "This script uses GVM to build and test this package on each major version of Go it supports"
echo "Please ensure GVM is installed and you have a version of every major Go release installed as well"
source ~/.gvm/scripts/gvm

set -e

gvm use go1.9 && (go get -t ./... || true) && go build ./... && go test ./...
gvm use go1.10 && (go get -t ./... || true) && go build ./... && go test ./...
gvm use go1.11 && go build ./...&& go test ./...
gvm use go1.12 && go build ./...&& go test ./...
gvm use go1.13 && go build ./... && go test ./...

echo "==SUCCESS=="