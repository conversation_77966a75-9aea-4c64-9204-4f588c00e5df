version: "3.2"

services:
  keto:
    image: oryd/keto:v0.7.0-alpha.1-sqlite
    ports:
      - "4466:4466"
      - "4467:4467"
    command: serve -c /home/<USER>/keto.yml
    restart: on-failure
    volumes:
      - type: bind
        source: .
        target: /home/<USER>

  keto-init:
    image: oryd/keto:v0.7.0-alpha.1-sqlite
    environment:
      - KETO_WRITE_REMOTE=keto:4467
    volumes:
      - type: bind
        source: .
        target: /home/<USER>
    command: relation-tuple create /home/<USER>/relation-tuples
    restart: on-failure
