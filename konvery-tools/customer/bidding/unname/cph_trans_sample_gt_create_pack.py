import os
import sys
import shutil
import numpy as np
sys.path.append('.')
from customer.common import tools
from customer.common.merge_pcd import merge_pcds_to_one
from lxml import etree

camera_names = ['fc120', 'front', 'left', 'rear', 'right']

def parse_extrinsic(xml_file):
    with open(xml_file, 'rb') as f:
        xml_data = f.read()
    # 使用lxml解析XML数据
    root = etree.fromstring(xml_data)
    extrinsic = np.array(root.xpath('//extrinsicMatrix/data/text()')[0].split(), dtype=np.float32).reshape(4, 4)
    return extrinsic.tolist()


def parse_intrinsic(xml_file):
    with open(xml_file, 'rb') as f:
        xml_data = f.read()
    # 使用lxml解析XML数据
    root = etree.fromstring(xml_data)
    intrinsic = np.array(root.xpath('//camIntriMatrix/data/text()')[0].split(), dtype=np.float32).reshape(3, 4).tolist()
    distortion = np.array(root.xpath('//distortion_coefficients/data/text()')[0].split(), dtype=np.float32).tolist()
    camera_model = root.xpath('//camera_model/text()')[0]
    return intrinsic, distortion, camera_model


def construct_config(calib_dir, out_file):
    sensor_params = dict()
    for camera_name in camera_names:
        intrinsic_file = os.path.join(calib_dir, camera_name, 'cameraIntrinsic.xml')
        extrinsic_file = os.path.join(calib_dir, camera_name, 'cameraExtrinsic.xml')
        intrinsic, distortion, camera_model = parse_intrinsic(intrinsic_file)
        extrinsic = parse_extrinsic(extrinsic_file)
        if camera_model.lower() == 'fisheye':
            sensor_params[camera_name] = {
                "camera_model": 'fisheye',
                "extrinsic": extrinsic,
                "fx": intrinsic[0][0],
                "fy": intrinsic[1][1],
                "cx": intrinsic[0][2],
                "cy": intrinsic[1][2],
                # "k1": 0,
                # "k2": 0,
                # "k3": 0,
                # "k4": 0,
                "k1": distortion[0],
                "k2": distortion[1],
                "k3": distortion[2],
                "k4": distortion[3],
            }
        else:
            raise ValueError('camera_model is not fisheye')

    config = {
        "data_type": "fusion_pointcloud",
        "sensor_params": sensor_params,
    }
    tools.write_json_file(config, out_file)


def run(input_dir, output_dir):
    out_clip_dir = os.path.join(output_dir, 'clip')
    os.mkdir(out_clip_dir)
    out_lidar_dir = os.path.join(out_clip_dir, 'lidar')
    os.mkdir(out_lidar_dir)
    out_cameras_dir = os.path.join(out_clip_dir, 'camera')
    os.mkdir(out_cameras_dir)
    for camera_name in camera_names:
        os.mkdir(os.path.join(out_cameras_dir, camera_name))
    for frame_dir in tools.listdir(os.path.join(input_dir, 'data'), sort=True, full_path=True):
        files = tools.listdir(frame_dir, full_path=True, sort=True)
        pcd_files = filter(lambda f: f.endswith('.pcd'), files)
        output_pcd = os.path.join(out_lidar_dir, os.path.basename(frame_dir) + '.pcd')
        merge_pcds_to_one(pcd_files, [np.eye(4)] * 3, output_pcd, True)
        for camera_name in camera_names:
            img_file = os.path.join(frame_dir, f'{os.path.basename(frame_dir)}_{camera_name}.png')
            shutil.copyfile(img_file, os.path.join(out_cameras_dir, camera_name, os.path.basename(frame_dir) + '.png'))
    construct_config(os.path.join(input_dir, 'calibration', '20240322_lidar_camera'), os.path.join(out_clip_dir, 'config.json'))


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)