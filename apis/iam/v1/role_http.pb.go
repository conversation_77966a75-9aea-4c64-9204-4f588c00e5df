// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: iam/v1/role.proto

package iam

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationRolesCreateRole = "/iam.v1.Roles/CreateRole"
const OperationRolesDeleteRole = "/iam.v1.Roles/DeleteRole"
const OperationRolesGetRole = "/iam.v1.Roles/GetRole"
const OperationRolesGetRoleFeperm = "/iam.v1.Roles/GetRoleFeperm"
const OperationRolesListRole = "/iam.v1.Roles/ListRole"
const OperationRolesSetRoleFeperm = "/iam.v1.Roles/SetRoleFeperm"
const OperationRolesUpdateRole = "/iam.v1.Roles/UpdateRole"

type RolesHTTPServer interface {
	CreateRole(context.Context, *Role) (*Role, error)
	DeleteRole(context.Context, *DeleteRoleRequest) (*emptypb.Empty, error)
	GetRole(context.Context, *GetRoleRequest) (*GetRoleReply, error)
	GetRoleFeperm(context.Context, *GetRoleRequest) (*GetRoleFepermReply, error)
	ListRole(context.Context, *ListRoleRequest) (*ListRoleReply, error)
	SetRoleFeperm(context.Context, *SetRoleFepermRequest) (*emptypb.Empty, error)
	UpdateRole(context.Context, *UpdateRoleRequest) (*emptypb.Empty, error)
}

func RegisterRolesHTTPServer(s *http.Server, srv RolesHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/roles", _Roles_CreateRole0_HTTP_Handler(srv))
	r.PATCH("/v1/roles/{name}", _Roles_UpdateRole0_HTTP_Handler(srv))
	r.DELETE("/v1/roles/{name}", _Roles_DeleteRole0_HTTP_Handler(srv))
	r.GET("/v1/roles/{name}", _Roles_GetRole0_HTTP_Handler(srv))
	r.GET("/v1/roles", _Roles_ListRole0_HTTP_Handler(srv))
	r.GET("/v1/roles/{name}/feperm", _Roles_GetRoleFeperm0_HTTP_Handler(srv))
	r.PUT("/v1/roles/{name}/feperm", _Roles_SetRoleFeperm0_HTTP_Handler(srv))
}

func _Roles_CreateRole0_HTTP_Handler(srv RolesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in Role
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRolesCreateRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateRole(ctx, req.(*Role))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Role)
		return ctx.Result(200, reply)
	}
}

func _Roles_UpdateRole0_HTTP_Handler(srv RolesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateRoleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRolesUpdateRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateRole(ctx, req.(*UpdateRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Roles_DeleteRole0_HTTP_Handler(srv RolesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteRoleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRolesDeleteRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteRole(ctx, req.(*DeleteRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Roles_GetRole0_HTTP_Handler(srv RolesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRoleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRolesGetRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRole(ctx, req.(*GetRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRoleReply)
		return ctx.Result(200, reply)
	}
}

func _Roles_ListRole0_HTTP_Handler(srv RolesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListRoleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRolesListRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListRole(ctx, req.(*ListRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListRoleReply)
		return ctx.Result(200, reply)
	}
}

func _Roles_GetRoleFeperm0_HTTP_Handler(srv RolesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRoleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRolesGetRoleFeperm)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRoleFeperm(ctx, req.(*GetRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRoleFepermReply)
		return ctx.Result(200, reply)
	}
}

func _Roles_SetRoleFeperm0_HTTP_Handler(srv RolesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetRoleFepermRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRolesSetRoleFeperm)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetRoleFeperm(ctx, req.(*SetRoleFepermRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type RolesHTTPClient interface {
	CreateRole(ctx context.Context, req *Role, opts ...http.CallOption) (rsp *Role, err error)
	DeleteRole(ctx context.Context, req *DeleteRoleRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetRole(ctx context.Context, req *GetRoleRequest, opts ...http.CallOption) (rsp *GetRoleReply, err error)
	GetRoleFeperm(ctx context.Context, req *GetRoleRequest, opts ...http.CallOption) (rsp *GetRoleFepermReply, err error)
	ListRole(ctx context.Context, req *ListRoleRequest, opts ...http.CallOption) (rsp *ListRoleReply, err error)
	SetRoleFeperm(ctx context.Context, req *SetRoleFepermRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateRole(ctx context.Context, req *UpdateRoleRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type RolesHTTPClientImpl struct {
	cc *http.Client
}

func NewRolesHTTPClient(client *http.Client) RolesHTTPClient {
	return &RolesHTTPClientImpl{client}
}

func (c *RolesHTTPClientImpl) CreateRole(ctx context.Context, in *Role, opts ...http.CallOption) (*Role, error) {
	var out Role
	pattern := "/v1/roles"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRolesCreateRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RolesHTTPClientImpl) DeleteRole(ctx context.Context, in *DeleteRoleRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/roles/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRolesDeleteRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RolesHTTPClientImpl) GetRole(ctx context.Context, in *GetRoleRequest, opts ...http.CallOption) (*GetRoleReply, error) {
	var out GetRoleReply
	pattern := "/v1/roles/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRolesGetRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RolesHTTPClientImpl) GetRoleFeperm(ctx context.Context, in *GetRoleRequest, opts ...http.CallOption) (*GetRoleFepermReply, error) {
	var out GetRoleFepermReply
	pattern := "/v1/roles/{name}/feperm"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRolesGetRoleFeperm))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RolesHTTPClientImpl) ListRole(ctx context.Context, in *ListRoleRequest, opts ...http.CallOption) (*ListRoleReply, error) {
	var out ListRoleReply
	pattern := "/v1/roles"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRolesListRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RolesHTTPClientImpl) SetRoleFeperm(ctx context.Context, in *SetRoleFepermRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/roles/{name}/feperm"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRolesSetRoleFeperm))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RolesHTTPClientImpl) UpdateRole(ctx context.Context, in *UpdateRoleRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/roles/{name}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRolesUpdateRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
