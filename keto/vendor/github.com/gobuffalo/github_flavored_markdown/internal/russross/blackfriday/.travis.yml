sudo: false
language: go
go:
  - 1.5.4
  - 1.6.2
  - tip
matrix:
  include:
    - go: 1.2.2
      script:
        - go get -t -v ./...
        - go test -v -race ./...
    - go: 1.3.3
      script:
        - go get -t -v ./...
        - go test -v -race ./...
    - go: 1.4.3
      script:
        - go get -t -v ./...
        - go test -v -race ./...
  allow_failures:
    - go: tip
  fast_finish: true
install:
  - # Do nothing. This is needed to prevent default install action "go get -t -v ./..." from happening here (we want it to happen inside script step).
script:
  - go get -t -v ./...
  - diff -u <(echo -n) <(gofmt -d -s .)
  - go tool vet .
  - go test -v -race ./...
