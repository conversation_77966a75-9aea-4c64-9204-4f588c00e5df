import os
import sys
from pathlib import Path
sys.path.append('.')
from customer.common import tools


err_category_mapping = {
    'all_rank': 'ball_rank',
    'eef': 'reef'
}

category2class = {
    'large_vessel': 'vessel',
    'medium_vessel': 'vessel',
    'small_vessel': 'vessel',
    'sign': 'buoy',
    'ball_rank': 'buoy',
    'lighthouse': 'marker',
    'piers':'marker',
    'reef': 'marker',
    'animals': 'others',
    'garbage': 'others',
    'net': 'others',
    'ranch': 'others'
}


def run(input_dir, output_dir):
    label_dirs = [label_dir for label_dir in tools.find_dir_by_pre_name(input_dir, 'label') if os.path.basename(label_dir) == 'label']
    for label_dir in label_dirs:
        outter_dir = os.path.join(output_dir, Path(label_dir[len(input_dir) + 1:]).parts[0])
        if not os.path.isdir(outter_dir):
            os.mkdir(outter_dir)
        label_files = tools.get_json_files(label_dir)
        for label_file in label_files:
            label_data = tools.get_json_data(label_file)
            results = []
            for obj in label_data.get('lidar', []):
                category = err_category_mapping.get(obj["class"], obj["class"])
                clazz = category2class[category]
                dimension = obj["annotation"]["data"]["dimension"]
                position = obj["annotation"]["data"]["position"]
                rotation = obj["annotation"]["data"]["rotation"]
                results.append(f'{clazz} {category} {obj["track_id"]} {dimension["l"]} {dimension["w"]} {dimension["h"]} {position["x"]} {position["y"]} {position["z"]} {rotation["z"]} {obj["point_num"]}')
            with open(os.path.join(outter_dir, label_file.name.replace(".json", ".txt")), 'w') as f:
                f.write('\n'.join(results))


"""
项目文档：https://s4lt6nme5h.feishu.cn/docx/KRyBdzunUoVzhIxhEgAcsA7zndf
"""
if __name__ == '__main__':
    args = tools.get_args()
    input_dir = args.input
    output_dir = args.out
    tools.check_dir(output_dir)
    run(input_dir, output_dir)
