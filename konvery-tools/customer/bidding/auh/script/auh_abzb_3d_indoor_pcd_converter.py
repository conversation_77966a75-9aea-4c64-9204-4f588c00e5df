import os.path
import struct
import re
import shutil
import zipfile
import sys

sys.path.append('.')
from customer.common import tools

mac_file = ['.DS_Store', '__MACOSX']


def pack_rgb_to_uint32(r, g, b):
    # Pack RGB values into a 32-bit unsigned integer
    rgb_packed = ((int(b) & 0xFF) << 0) | ((int(g) & 0xFF) << 8) | ((int(r) & 0xFF) << 16)
    return rgb_packed


def convert_txt_to_pcd(input_file, output_file):
    # Define the regular expression pattern to match float numbers separated by spaces or commas
    pattern = re.compile(r'(-?\d+\.\d+)\s+(-?\d+\.\d+)\s+(-?\d+\.\d+)\s+(\d+)\s+(\d+)\s+(\d+)')

    # Define the pcd header
    pcd_header = """
    # .PCD v0.7 - Point Cloud Data file format
VERSION 0.7
FIELDS x y z rgb
TYPE F F F U
SIZE 4 4 4 4
COUNT 1 1 1 1
WIDTH {}
HEIGHT 1
VIEWPOINT 0 0 0 1 0 0 0
POINTS {}
DATA binary
"""

    num_points = 0
    pcd_data = bytearray()

    # Open the input .txt file for reading
    with open(input_file, 'r') as txt_file:
        # Process each line and convert data
        for line in txt_file:

            match = pattern.match(line.strip())
            if match:
                x, y, z, r, g, b = map(float, match.groups())

            # Pack the RGB values into a 32-bit integer
            rgb_packed = pack_rgb_to_uint32(r, g, b)

            # Append the data to pcd_data bytearray
            pcd_data.extend(struct.pack('<fffI', x, y, z, rgb_packed))
            num_points += 1

    # Write the pcd header with the number of points
    pcd_header_str = pcd_header.format(num_points, num_points)

    # Write the pcd data to the output .pcd file
    with open(output_file, 'wb') as pcd_file:
        # Write the pcd header
        pcd_file.write(pcd_header_str.encode('utf-8'))

        # Write the binary data
        pcd_file.write(pcd_data)


def run(input_dir):
    try:
        file_counter = 0
        unzip_dir = input_dir

        # unzip file if the extension is .zip
        if os.path.splitext(input_dir)[1] == '.zip':
            unzip_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_unzip')
            if os.path.exists(unzip_dir):
                shutil.rmtree(unzip_dir)
            with zipfile.ZipFile(input_dir, 'r') as zip_ref:
                zip_ref.extractall(unzip_dir)

        for path_1 in os.listdir(unzip_dir):
            path_1_dir = os.path.join(unzip_dir, path_1)
            # convert to .pcd if encounters .txt file
            if os.path.splitext(path_1)[1] == '.txt':
                if not os.path.exists(os.path.join(unzip_dir, 'lidar')):
                    os.mkdir(os.path.join(unzip_dir, 'lidar'))
                convert_txt_to_pcd(path_1_dir,
                                   os.path.join(unzip_dir, 'lidar', path_1.split('.')[0] + '.pcd'))
                os.remove(path_1_dir)
                file_counter += 1
            elif path_1 not in mac_file:
                # e9zR4mvMWw7-┤²╔╧┤½
                for area in os.listdir(path_1_dir):
                    if area not in mac_file:
                        area_dir = os.path.join(path_1_dir, area)
                        for room in os.listdir(area_dir):
                            if room not in mac_file:
                                room_dir = os.path.join(area_dir, room)
                                shutil.rmtree(os.path.join(room_dir, 'Annotions'))
                                os.mkdir(os.path.join(room_dir, 'lidar'))
                                for target_file in os.listdir(room_dir):
                                    if os.path.splitext(target_file)[1] == '.txt':
                                        # convert .txt to .pcd and remove .txt
                                        convert_txt_to_pcd(os.path.join(room_dir, target_file),
                                                           os.path.join(room_dir, 'lidar', target_file.split('.')[0] + '.pcd'))
                                        os.remove(os.path.join(room_dir, target_file))
                                        file_counter += 1

        trans_result = {
            "code": 0,
            "output": input_dir,
            "err_msg": '成功',
            "summary": {
                '生成文件数': file_counter,
            }
        }

    except Exception as e:
        trans_result = {
            "code": 1,
            "err_msg": e,
        }

    for key in trans_result:
        print(key + ': ' + str(trans_result[key]))

    print(trans_result)
    return trans_result


if __name__ == "__main__":
    args = tools.get_args()
    run(args.input)

    # args_input = '/Users/<USER>/Downloads/Library-Sampling2'
    # run(args_input)
