// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/config.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Errors with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Errors) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Errors with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ErrorsMultiError, or nil if none found.
func (m *Errors) ValidateAll() error {
	return m.validate(true)
}

func (m *Errors) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetErrors()))
		i := 0
		for key := range m.GetErrors() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetErrors()[key]
			_ = val

			// no validation rules for Errors[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ErrorsValidationError{
							field:  fmt.Sprintf("Errors[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ErrorsValidationError{
							field:  fmt.Sprintf("Errors[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ErrorsValidationError{
						field:  fmt.Sprintf("Errors[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return ErrorsMultiError(errors)
	}

	return nil
}

// ErrorsMultiError is an error wrapping multiple validation errors returned by
// Errors.ValidateAll() if the designated constraints aren't met.
type ErrorsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ErrorsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ErrorsMultiError) AllErrors() []error { return m }

// ErrorsValidationError is the validation error returned by Errors.Validate if
// the designated constraints aren't met.
type ErrorsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ErrorsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ErrorsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ErrorsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ErrorsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ErrorsValidationError) ErrorName() string { return "ErrorsValidationError" }

// Error satisfies the builtin error interface
func (e ErrorsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sErrors.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ErrorsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ErrorsValidationError{}

// Validate checks the field values on GetVersionReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetVersionReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVersionReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetVersionReplyMultiError, or nil if none found.
func (m *GetVersionReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVersionReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	if len(errors) > 0 {
		return GetVersionReplyMultiError(errors)
	}

	return nil
}

// GetVersionReplyMultiError is an error wrapping multiple validation errors
// returned by GetVersionReply.ValidateAll() if the designated constraints
// aren't met.
type GetVersionReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVersionReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVersionReplyMultiError) AllErrors() []error { return m }

// GetVersionReplyValidationError is the validation error returned by
// GetVersionReply.Validate if the designated constraints aren't met.
type GetVersionReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVersionReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVersionReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVersionReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVersionReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVersionReplyValidationError) ErrorName() string { return "GetVersionReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetVersionReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVersionReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVersionReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVersionReplyValidationError{}

// Validate checks the field values on CommentReasonClass with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CommentReasonClass) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommentReasonClass with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CommentReasonClassMultiError, or nil if none found.
func (m *CommentReasonClass) ValidateAll() error {
	return m.validate(true)
}

func (m *CommentReasonClass) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetClass()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CommentReasonClassValidationError{
					field:  "Class",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CommentReasonClassValidationError{
					field:  "Class",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClass()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CommentReasonClassValidationError{
				field:  "Class",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetReasons() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CommentReasonClassValidationError{
						field:  fmt.Sprintf("Reasons[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CommentReasonClassValidationError{
						field:  fmt.Sprintf("Reasons[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CommentReasonClassValidationError{
					field:  fmt.Sprintf("Reasons[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CommentReasonClassMultiError(errors)
	}

	return nil
}

// CommentReasonClassMultiError is an error wrapping multiple validation errors
// returned by CommentReasonClass.ValidateAll() if the designated constraints
// aren't met.
type CommentReasonClassMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommentReasonClassMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommentReasonClassMultiError) AllErrors() []error { return m }

// CommentReasonClassValidationError is the validation error returned by
// CommentReasonClass.Validate if the designated constraints aren't met.
type CommentReasonClassValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommentReasonClassValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommentReasonClassValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommentReasonClassValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommentReasonClassValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommentReasonClassValidationError) ErrorName() string {
	return "CommentReasonClassValidationError"
}

// Error satisfies the builtin error interface
func (e CommentReasonClassValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommentReasonClass.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommentReasonClassValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommentReasonClassValidationError{}

// Validate checks the field values on ListCommentReasonsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCommentReasonsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCommentReasonsReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCommentReasonsReplyMultiError, or nil if none found.
func (m *ListCommentReasonsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCommentReasonsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClasses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCommentReasonsReplyValidationError{
						field:  fmt.Sprintf("Classes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCommentReasonsReplyValidationError{
						field:  fmt.Sprintf("Classes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCommentReasonsReplyValidationError{
					field:  fmt.Sprintf("Classes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListCommentReasonsReplyMultiError(errors)
	}

	return nil
}

// ListCommentReasonsReplyMultiError is an error wrapping multiple validation
// errors returned by ListCommentReasonsReply.ValidateAll() if the designated
// constraints aren't met.
type ListCommentReasonsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCommentReasonsReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCommentReasonsReplyMultiError) AllErrors() []error { return m }

// ListCommentReasonsReplyValidationError is the validation error returned by
// ListCommentReasonsReply.Validate if the designated constraints aren't met.
type ListCommentReasonsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCommentReasonsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCommentReasonsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCommentReasonsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCommentReasonsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCommentReasonsReplyValidationError) ErrorName() string {
	return "ListCommentReasonsReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListCommentReasonsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCommentReasonsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCommentReasonsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCommentReasonsReplyValidationError{}

// Validate checks the field values on PutCommentReasonsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PutCommentReasonsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PutCommentReasonsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PutCommentReasonsRequestMultiError, or nil if none found.
func (m *PutCommentReasonsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PutCommentReasonsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClasses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PutCommentReasonsRequestValidationError{
						field:  fmt.Sprintf("Classes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PutCommentReasonsRequestValidationError{
						field:  fmt.Sprintf("Classes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PutCommentReasonsRequestValidationError{
					field:  fmt.Sprintf("Classes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PutCommentReasonsRequestMultiError(errors)
	}

	return nil
}

// PutCommentReasonsRequestMultiError is an error wrapping multiple validation
// errors returned by PutCommentReasonsRequest.ValidateAll() if the designated
// constraints aren't met.
type PutCommentReasonsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PutCommentReasonsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PutCommentReasonsRequestMultiError) AllErrors() []error { return m }

// PutCommentReasonsRequestValidationError is the validation error returned by
// PutCommentReasonsRequest.Validate if the designated constraints aren't met.
type PutCommentReasonsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PutCommentReasonsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PutCommentReasonsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PutCommentReasonsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PutCommentReasonsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PutCommentReasonsRequestValidationError) ErrorName() string {
	return "PutCommentReasonsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PutCommentReasonsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPutCommentReasonsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PutCommentReasonsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PutCommentReasonsRequestValidationError{}
