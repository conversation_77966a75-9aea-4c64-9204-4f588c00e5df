export KETO_READ_REMOTE=localhost:4466
export KETO_WRITE_REMOTE=localhost:4467
#opt="--insecure-disable-transport-security --insecure-skip-hostname-verification"

if which keto > /dev/null; then
  KETO=keto
else
  KETO=./keto
fi
export KETO
function ketow() {
	$KETO $@ --insecure-disable-transport-security --insecure-skip-hostname-verification
}

function parse_tuple() {
  #set -x
  local ns=$1
  local obj=$2
  local sub=$3
  local opt
  local rel
  if test -z "$sub" && echo $ns | grep -q @; then
    sub=$(echo $ns | cut -d@ -f2)
    ns=$(echo $ns | cut -d@ -f1)
  fi
  if test -z "$obj"; then
    if ! echo $ns | grep -q :; then
      obj=$ns
      ns=
    else
      obj=$(echo $ns | cut -d: -f2)
      ns=$(echo $ns | cut -d: -f1)
    fi
  fi
  if test -n "$obj" && echo $obj | grep -q '#'; then
    rel=$(echo $obj | cut -d# -f2)
    obj=$(echo $obj | cut -d# -f1)
  fi
  #set +x
  echo "ns=$ns; obj=$obj; rel=$rel; sub=$sub"
}

function tpget() {
  test -z "$1" && echo "please specify ns:object#rel@sub:id" && return 1
  #set -x
  local ns obj rel sub opt
  eval $(parse_tuple $@)
  if test -n "$sub"; then
    if echo $sub | grep -q :; then
      opt="$opt --subject-set $sub"
    else
      opt="$opt --subject-id $sub"
    fi
  fi
  test -n "$ns" && opt="$opt --namespace $ns"
  test -n "$obj" && opt="$opt --object $obj"
  test -n "$rel" && opt="$opt --relation $rel"
  echo ketow relation-tuple get $opt
  eval ketow relation-tuple get $opt
  #set +x
}

function tpcheck() {
  test -z "$1" && echo "please specify ns:object#rel@sub:id" && return 1
  #set -x
  local ns obj rel sub opt
  eval $(parse_tuple $@)
  test -z "$sub" && echo "lack of subject" && return 1
  test -z "$ns" && echo "lack of namespace" && return 1
  test -z "$obj" && echo "lack of object" && return 1
  rel=$(echo $rel | sed "s:^$ns\.::")
  test -z "$rel" && echo "lack of relation" && return 1
  echo ketow check $sub $rel $ns $obj
  eval ketow check $sub $rel $ns $obj
  #set +x
}

function tpexpand() {
  test -z "$1" && echo "please specify ns:object#rel" && return 1
  #set -x
  local ns obj rel
  eval $(parse_tuple $@)
  test -z "$ns" && echo "lack of namespace" && return 1
  test -z "$obj" && echo "lack of object" && return 1
  rel=$(echo $rel | sed "s:^$ns\.::")
  test -z "$rel" && echo "lack of relation" && return 1
  echo ketow expand $rel $ns $obj
  eval ketow expand $rel $ns $obj
  #set +x
}

function tpadd() {
  test -z "$1" && echo "please specify ns:object#rel@sub:id" && return 1
  #set -x
  local ns obj rel sub opt
  eval $(parse_tuple $@)
  test -z "$sub" && echo "lack of subject" && return 1
  test -z "$ns" && echo "lack of namespace" && return 1
  test -z "$obj" && echo "lack of object" && return 1
  rel=$(echo $rel | sed "s:^$ns\.::")
  test -z "$rel" && echo "lack of relation" && return 1

  echo "$1" | $KETO relation-tuple parse - --format json | ketow relation-tuple create -
}

function tpdel() {
  test -z "$1" && echo "please specify ns:object#rel@sub:id" && return 1
  #set -x
  local ns obj rel sub opt
  eval $(parse_tuple $@)
  test -z "$sub" && echo "lack of subject" && return 1
  test -z "$ns" && echo "lack of namespace" && return 1
  test -z "$obj" && echo "lack of object" && return 1
  rel=$(echo $rel | sed "s:^$ns\.::")
  test -z "$rel" && echo "lack of relation" && return 1

  echo "$1" | $KETO relation-tuple parse - --format json | ketow relation-tuple delete -
}
