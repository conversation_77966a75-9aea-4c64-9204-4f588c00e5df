package main

import (
	"bytes"
	"fmt"
	"log"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

const (
	apidocPrefix = "/docs"
)

var startCmd = &cobra.Command{
	Use:   "start",
	Short: "Start the apidocs service",
	RunE: func(cmd *cobra.Command, args []string) error {
		addr = viper.GetString("addr")
		assets = viper.GetString("assets")
		tpldir = viper.GetString("tpldir")
		apiprefix = viper.GetString("prefix")
		log.Println("addr:", addr)
		log.Println("assets:", assets)
		log.Println("tpldir:", tpldir)
		log.Println("prefix:", apiprefix)

		return serve(addr)
	},
}

var (
	addr      string
	assets    string
	tpldir    string
	apiprefix string
)

func init() {
	rootCmd.AddCommand(startCmd)

	pflags := startCmd.Flags()
	pflags.String("addr", ":8080", "service address")
	pflags.String("assets", "assets", "directory to put the uploaded contents")
	pflags.String("tpldir", "tpl", "path to the template dir")
	pflags.String("prefix", "", "API prefix")
	viper.BindPFlags(pflags)
}

func serve(addr string) (err error) {
	r := gin.Default()
	r.Use(gin.Recovery())
	r.Use(gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("%s %s %s %s %d %s %s %s\n",
			param.TimeStamp.Format(time.RFC3339),
			param.Method,
			param.Path,
			param.Request.Proto,
			param.StatusCode,
			param.Latency,
			param.ClientIP,
			// param.Request.UserAgent(),
			param.ErrorMessage,
		)
	}))
	if apiprefix != "" {
		viewerTpl = patchViewerTpl(tpldir, viewerTpl, apiprefix)
	}
	r.LoadHTMLGlob(filepath.Join(tpldir, "*"))
	g := r.Group(apiprefix)

	handler := &httpHandler{
		assets:    assets,
		viewerTpl: tpldir,
		apiPrefix: apiprefix,
		docsHash:  NewDocsHash(assets),
	}

	g.GET("/", func(c *gin.Context) { c.HTML(http.StatusOK, "index.html", nil) })
	g.GET("/healthz", func(c *gin.Context) { c.String(200, time.Now().Format(time.RFC3339+": I feel good")) })
	g.Static("/assets", assets)
	g.Static("/res", "res")
	g.GET("/list/*dir", wrap(handler.handleList))

	openapi := g.Group(path.Join(apidocPrefix, "/:app/:rev"))
	openapi.GET("", wrap(handler.getAPIDoc))
	openapi.PUT("", wrap(handler.uploadAPIDoc))
	openapi.DELETE("", wrap(handler.deleteAPIDoc))
	openapi.PUT("/alias", wrap(handler.setAPIDocAlias))

	viewer := g.Group("/view")
	viewer.GET("/", wrap(handler.viewAPIDoc))
	viewer.GET("/:app", wrap(handler.viewAPIDoc))
	viewer.GET("/:app/:rev", wrap(handler.viewAPIDoc))

	r.Run(addr)
	return
}

func patchViewerTpl(tpldir, viewerTpl, prefix string) string {
	path := filepath.Join(tpldir, viewerTpl)
	data, err := os.ReadFile(path)
	if err != nil {
		log.Println("failed to open file", path)
	}
	orig := []byte(`="/res/`)
	if !bytes.Contains(data, orig) {
		return viewerTpl
	}
	if strings.HasSuffix(prefix, "/") {
		prefix = prefix[:len(prefix)-1]
	}
	data = bytes.ReplaceAll(data, orig, []byte(`="`+prefix+`/res/`))
	err = os.WriteFile(path+".o", data, 0644)
	if err != nil {
		log.Println("failed to write file", path)
		return viewerTpl
	}
	return viewerTpl + ".o"
}
