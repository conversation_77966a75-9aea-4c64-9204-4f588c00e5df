import os
import shutil
import threading
import sys

sys.path.append('.')
from customer.common import tools

pack_count = 0
total_file_count = 0
invalid_pcd_file_count = 0
copied_pcd_file_count = 0
lock = threading.Lock()
input_root = ""
test_in_config = {}
line_count = 0
file_count = 0
package_index = 0
type_map = {"solid": "2", "dash": "4"}
color_map = {'white': "2", 'yellow': "3", 'others': "1"}
color_dict = {}
obj_id = 1


def create_pre_label(input_dir, output_dir):
    global obj_id
    output_dir = output_dir + os.sep + "pre_label"
    img_dir = input_dir + os.sep + "img"

    hao_mo_map = input_dir + os.sep + "haomo-mappings.json"
    pre_label_zip = tools.get_file_by_extension(input_dir, ".zip")[0].path
    pre_label_dir = tools.unzip(pre_label_zip)

    tools.check_dir(output_dir)
    print(pre_label_dir)

    pre_label_files = tools.get_file_by_extension(pre_label_dir, ".json")
    hao_mo_map_json = tools.get_json_data(hao_mo_map)

    pre_label_img_map = []
    for data in hao_mo_map_json:
        target_pre_label_path = output_dir + os.sep + data["original_filename"].replace(".jpeg", ".json")
        pre_label_name = data["clip_id"] + "_" + data["bundle_id"] + ".json"
        for f in pre_label_files:
            if f.name == pre_label_name:
                pre_label_img_map.append({"target_pre_label_path": target_pre_label_path, "src_pre_label_path": f.path})
                break

    for data in pre_label_img_map:
        target_pre_label_path = data["target_pre_label_path"]
        pre_label_file = data["src_pre_label_path"]
        pre_label_data = tools.get_json_data(pre_label_file)
        objects = pre_label_data["labeled_data"]["objects"]
        camera_marks = []
        for obj in objects:
            children = obj["children"]
            child = children[0]
            continuity = child["attributes"]["continuity"]
            if not (continuity in type_map.keys()):
                print(f"Skip type {continuity}")
                continue

            global color_dict
            color_dict[child["attributes"]["color"]] = 1

            class_name = type_map[child["attributes"]["continuity"]]
            color_value = color_map[child["attributes"]["color"]]
            mark = {"class": class_name,
                    "attrs": {"Color": [color_value]},
                    "object_id": str(obj_id),
                    "annotation": {
                        "type": "line",
                        "data": child["geometry"]["points"]}}
            camera_marks.append(mark)
            obj_id += 1
        export_json = {"label_meta": {
            "mark_status": 0}, "labels": camera_marks}
        tools.json_dump_file(target_pre_label_path, export_json, indent=4)


def copy_to_new_dir(input_dir, output_dir):
    global total_file_count
    global pack_count
    global invalid_pcd_file_count

    img_dir = input_dir + os.sep + "img"
    image_files = tools.get_file_by_extension(img_dir, ".jpeg")
    camera_dir = output_dir + os.sep + "img"
    tools.check_dir(camera_dir)

    for f in image_files:
        src_img = f.path
        dst_img = camera_dir + os.sep + f.name
        shutil.copy(src_img, dst_img)

    create_pre_label(input_dir, output_dir)


def create_pack(input_dir, output_dir, pack_size):
    global package_index
    package_index += 1
    output_dir = output_dir + os.sep + "segment_" + str(package_index)
    tools.check_dir(output_dir)
    copy_to_new_dir(input_dir, output_dir)


def run(input_dir, output_dir, pack_size):
    global input_root
    global test_in_config

    input_root = tools.unzip(input_dir)
    tools.check_dir(output_dir)
    sub_dir_list = tools.get_sub_dir(input_dir)

    for sub_dir in sub_dir_list:
        sub_dir = input_root + os.sep + sub_dir
        create_pack(sub_dir, output_dir, pack_size=pack_size)

    trans_result = {"code": 0, "output": output_dir, "err_msg": "成功", }
    print(trans_result)

    print(color_dict)
    return trans_result


"""
    项目： 广汽 项目 试标 带预标注信息
    对接平台：云测新平台 
    功能： 将客户的文件image, 标注结果转换成云测的格式， 并按数量分包
    输出： 分包后的数据
"""
if __name__ == "__main__":

    args = tools.get_args()

    if args.input == "../../input/test":
        args.input = "/Users/<USER>/Downloads/Convert/haomo/test"
        args.out = "/Users/<USER>/Downloads/haomo-output"

    run(input_dir=args.input, output_dir=args.out, pack_size=1000)

# def get_camera_config(camera_params_dir):
#     # 生成除poses外的其它内容
#     camera_dict = {}
#     sensor_params = {}
#
#     config = {
#         "camera": camera_dict,
#         "data_type": "fusion_pointcloud",
#         "sensor_params": sensor_params,
#         "poses": {}
#     }
#
#     yml_file_list = [os.path.join(camera_params_dir, f) for f in os.listdir(camera_params_dir) if
#                      f.endswith(".yml")]
#
#     for yml_file in yml_file_list:
#         camera_name = "img_" + os.path.basename(yml_file).replace(".yml", "")
#         camera_dict[camera_name] = camera_name
#         intrinsic = tools.get_opencv_ymal_field(yml_file, "camera_matrix").flatten().tolist()
#         camera_distortion = tools.get_opencv_ymal_field(yml_file, "distortion_coefficients").flatten().tolist()
#         r_mat = tools.get_opencv_ymal_field(yml_file, "r_mat")
#         t_vec = tools.get_opencv_ymal_field(yml_file, "t_vec")
#
#         extrinsic_matrix = numpy.eye(4)
#         extrinsic_matrix[:3, :3] = r_mat
#         extrinsic_matrix[:3, 3] = t_vec.flatten().tolist()
#
#         sensor_params[camera_name] = {
#             "camera_model": "pinhole",
#             "extrinsic": extrinsic_matrix.tolist(),
#             "fx": intrinsic[0],
#             "fy": intrinsic[4],
#             "cx": intrinsic[2],
#             "cy": intrinsic[5],
#             "k1": camera_distortion[0],
#             "k2": camera_distortion[1],
#             "k3": camera_distortion[2],
#             "p1": camera_distortion[3],
#             "p2": camera_distortion[4]
#         }
#     return config
