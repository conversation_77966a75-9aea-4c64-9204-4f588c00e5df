import os
import sys

sys.path.append('.')
from customer.common import tools

file_count = 0
file_box_dict = {}
current_file = ""
object_count = 0
code = 0
warning_count = 0
label_name_map = {}


def get_label_name_dict(test_in_config):
    global label_name_map
    data = tools.get_json_data(test_in_config)["properties"][0]["class"]

    for label_class in data:
        label_name_map[label_class["pname"]] = label_class["ptitle"]
        if 1 == len(label_class["attrs"]):
            continue
        for menu_label in label_class["attrs"][1]["pvalue"]:
            label_name_map[menu_label["pname"]] = menu_label["ptitle"]

    print(label_name_map)


def convert(data):
    global object_count

    label_list = []
    height = data["label_meta"]["source_info"]["height"]
    width = data["label_meta"]["source_info"]["width"]
    res = {
        "shapes": label_list,
        "imageHeight": height,
        "imageWidth": width
    }
    data = data["labels"]

    for anno in data:
        class_name = [label_name_map[anno["class"]]]
        hard = int(anno["attrs"]["hard"][0])

        for elem in anno["attrs"]["DishName"]:
            class_name.append(label_name_map[elem])

        box = anno["annotation"]["data"]
        rect_width = box["width"]
        rect_height = box["height"]
        rotation = box["rotate"]
        left_top_x = box["x"]  # 矩形框左上点x值
        left_top_y = box["y"]  # 矩形框左上点y值
        bottom_right_x = left_top_x + rect_width
        bottom_right_y = left_top_y + rect_height

        left_top_x, left_top_y = tools.rotate_point(left_top_x, left_top_y, rotation)
        bottom_right_x, bottom_right_y = tools.rotate_point(bottom_right_x, bottom_right_y, rotation)
        points = [[left_top_x, left_top_y], [bottom_right_x, bottom_right_y]]
        mark_res = {
            "label": class_name,
            "points": points,
            "shape_type": "rectangle",
            "hard": hard
        }

        label_list.append(mark_res)

    return res


def run(test_in_dir, test_in_config, output_dir):
    global file_count
    global current_file

    get_label_name_dict(test_in_config)
    tools.check_dir(output_dir)
    test_in_dir = tools.unzip(test_in_dir)

    for f in tools.get_json_files(test_in_dir):
        file_count += 1
        output_name = f.name
        output_file = output_dir + os.sep + output_name
        data = tools.get_json_data(f.path)
        data = convert(data)
        tools.write_json_file(data, output_file, ensure_ascii=False)

    return tools.check_error(output_dir, code=code, file_count=file_count, object_count=object_count, is_zip=False)


"""
    项目： Nas - Nara 2D 
    对接平台：云测新平台 
    功能： 云测导出的数据转换成客户格式
    输入： 云测结果
    输出： json
    文档： 
"""
if __name__ == "__main__":
    args = tools.get_args()
    if args.input == "../../input/test":
        args.input = "/Users/<USER>/Downloads/Convert/Nas"
        args.pcd = "/Users/<USER>/Project/konvery-tools/customer/nas/config/2Dnara菜品检测及分类标注 test_in_config.json"
        args.out = "/Users/<USER>/Downloads/output-nara"

    run(test_in_dir=args.input, test_in_config=args.pcd, output_dir=args.out)

    # label_name_map = {'Thai_Beef_Boat_Noodles': '泰式牛肉船面', 'Beef_Short_Rib_Red_Curry': '红咖喱牛肋肉',
    #      'Homemade_Chicken_Pandan': '班兰香叶包鸡', 'Grilled_Pork_Neck_with_PapayaSalad': '烧猪颈肉配青木瓜沙律',
    #      'Signature_NortheasternAppetizer_Platter': '泰式东北小食拼盘', 'Seafood_lom_Yum_with_Fish_Maw': '花胶海鲜冬阴功',
    #      'Pineapple_Fried_Rice_withSeafood_Pork_Floss': '菠萝肉松海鲜炒饭', 'Soft_Shell_CrabYellow_Curry': '黄咖喱软壳蟹',
    #      'Thai_Egg_Custard_Dumplingswith_Coconut_Milk': '椰香奶皇流心汤丸', 'Prawn_Carpaccio': '泰式生虾',
    #      'Shrimp_Cakes': '香脆虾饼', 'Thai_Fish_Cakes': '泰式鱼饼',
    #      'Northeastern_Crispy_Soft_Shell_Crabwith_Spicy_Thai_Dressing': '泰式东北软壳蟹',
    #      'Mango_Soft_ShellCrab_Rice_Paper_Rollswith_Thai_Spicy_Herb_Sauce': '芒果软壳蟹米纸卷',
    #      'Nara_SignatureAppetizer_Platter': '泰式小食拼盘', 'BBQ_Pork_Satay': '黄姜猪肉沙嗲',
    #      'Deep_fried_Pork_Neck': '香脆芝麻猪颈肉', 'Minced_Pork_with_RiceVermicelli_Lettuce': '肉碎米粉生菜包',
    #      'Crispy_Chicken_Wings': '香脆鱼露鸡翼', 'BBQ_Chicken_Satay': '黄姜鸡肉沙嗲',
    #      'Pork_Salami_Rice_Paper_Rollswith_Thal_Spicy_Herb_Sauce': '扎肉米纸卷',
    #      'Papaya_Salad_withSaited_Egg': '咸蛋青木瓜沙律', 'Pomelo_Saladwith_Prawns': '大虾柚子沙律',
    #      'Green_Mango_Saladwith_Crispy_Pork_Salami': '青芒扎肉沙律',
    #      'Seafood_Minced_Porkwith_Glass_Vermicelli_Salad': '海鲜肉碎粉丝沙律', 'Coconut_Chicken_Soup': '泰式椰子鸡汤',
    #      'Minced_Pork_with_EggTofu__Glass_Vermicelli_Soup': '肉碎玉子豆腐粉丝汤', 'Tiger_Prawns_Yellow_Curry': '黄咖喱大虾',
    #      'Tiger_PrawnsRed_Curry': '红咖喱大虾', 'Fish_Fillet_Yellow_Curry': '黄咖喱鱼柳', 'Chicken_Green_Curry': '青咖喱鸡',
    #      'Beef_ShinMassaman_Curry': '马莎文咖喱牛腱', 'Grilled_Chicken_withPapaya_Salad': '烧鸡扒配青木瓜沙律',
    #      'Steamed_Grey_Mulletwith_Spicy_Lime_Sauce': '酸辣青柠汁蒸乌头',
    #      'Steamed_Grey_Mullet_withPlum__Minced_PorkSoup_in_Stove_Tray': '明炉黄梨肉碎蒸乌头',
    #      'Crispy_Sea_Bass_withFried_Lemongrass_Garlic': '香茅金蒜脆鲈鱼',
    #      'Stir_fried_Mud_Crabwith_Glass_Vermicelli': '粉丝炒肉蟹',
    #      'Stir_fried_Tiger_Prawnswith_Salted_Egg_Curry': '咸蛋咖喱炒大虾',
    #      'Stir_fried_Tiger_Prawns_withGarlic_Peppercorns': '蒜香胡椒炒大虾',
    #      'Clams_with_Fresh_YoungCoconut_Stew': '泰式椰子蚬煲', 'Fried_Rice_Noodleswith_Pork': '泰式猪肉炒河粉',
    #      'Fried_Rice_Noodleswith_Thai_Shrimp_Paste': '泰式虾酱炒河粉', 'Braised_Rice_Noodleswith_Pork': '湿炒猪肉河粉',
    #      'Fried_Egg_Noodleswith_Crab_Meat': '蟹肉炒面', 'Nara_Fried_Rice': 'nara_特色炒饭',
    #      'Fried_Butterfly_Pea_Rice_withCrispy_Fish_Thai_Herbs': '泰式香草脆鱼炒蝶豆花饭',
    #      'Fried_Rice_with_Crab_Meat': '蟹肉炒饭', 'Minced_Pork_FriedEgg_with_Jasmine_Rice': '泰式肉碎煎蛋饭',
    #      'Vegetarian_Spring_Rolls': '泰式脆春卷',
    #      'Minced_OmniPork_Saladwith_Rice_Vermicelli_Lettuce': '够Pork肉碎米粉生菜包', 'Papaya_Salad': '青木瓜沙律',
    #      'OmniPork_Balls_with_EggTofu__Glass_Vermicelli_Soup': '膳良肉丸玉子豆腐粉丝汤',
    #      'Mixed_VegetablesCoconut_Soup': '杂菜椰子汤', 'Stir_fried_OmniPorkwith_Eggplanf': '味来肉碎炒茄子',
    #      'Stir_fried_OmniPork_Tofu_Puff': '辛猪肉碎炒豆卜', 'Pumpkin_fofu_PuffRed_Curry': '红咖喱南瓜豆卜',
    #      'Mixed_VegetablesYellow_Curry': '黄咖喱杂菜', 'Sweet_SourVegetarian_Chicken': '泰式酸甜炒素鸡',
    #      'Minced_OmniPork_FriedEgg_with_Jasmine_Rice': '抛猪引肉碎煎蛋饭',
    #      'Phad_Thai_withMixed_Vegetables': '泰式杂菜炒金边粉', 'Stir_fried_Morning_Glory': '泰式炒通菜',
    #      'Stir_fried_Baby_Cabbagewith_Garlic': '香蒜炒椰菜苗', 'tir_fried_Kale_withSalted_Fish': '泰式咸鱼芥兰',
    #      'Stir_fried_Seasonal_MixedVegetables_with_Garlic': '香蒜炒杂菜', 'Stir_fried_Broccoli_with_Garlic': '香蒜炒西兰花',
    #      'Stir_fried_Sweet_PotatoSprouts_with_Garlic': '香蒜炒蕃薯苗', 'Roti_Prata': '印度薄饼',
    #      'Butterfly_Pea_Rice': '蝶豆花饭', 'Jasmine_Rice': '白饭', 'Mango_Sticky_Ricewith_Coconut_Milk': '芒果糯米饭伴椰奶',
    #      'Thai_Dessert_Platter': '泰式糕点拼盘', 'Durian_lce_Creamwith_Durian_Sticky_Rice': '榴莲雪糕伴榴莲糯米饭',
    #      'other': '其他', 'Phad_Thai_with_Prawns': '泰式炒金边粉'}
