# github.com/gobuffalo/nulls Stands on the Shoulders of Giants

github.com/gobuffalo/nulls does not try to reinvent the wheel! Instead, it uses the already great wheels developed by the Go community and puts them all together in the best way possible. Without these giants, this project would not be possible. Please make sure to check them out and thank them for all of their hard work.

Thank you to the following **GIANTS**:

* [github.com/davecgh/go-spew](https://godoc.org/github.com/davecgh/go-spew)
* [github.com/go-sql-driver/mysql](https://godoc.org/github.com/go-sql-driver/mysql)
* [github.com/gofrs/uuid](https://godoc.org/github.com/gofrs/uuid)
* [github.com/jmoiron/sqlx](https://godoc.org/github.com/jmoiron/sqlx)
* [github.com/lib/pq](https://godoc.org/github.com/lib/pq)
* [github.com/mattn/go-sqlite3](https://godoc.org/github.com/mattn/go-sqlite3)
* [github.com/pmezard/go-difflib](https://godoc.org/github.com/pmezard/go-difflib)
* [github.com/stretchr/objx](https://godoc.org/github.com/stretchr/objx)
* [github.com/stretchr/testify](https://godoc.org/github.com/stretchr/testify)
* [gopkg.in/check.v1](https://godoc.org/gopkg.in/check.v1)
* [gopkg.in/yaml.v3](https://godoc.org/gopkg.in/yaml.v3)
