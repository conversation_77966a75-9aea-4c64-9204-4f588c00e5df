syntax = "proto3";

package anyconn.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";
// import "iam/v1/type.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anyconn/v1;anyconn";
option java_multiple_files = true;
option java_package = "anyconn.v1";

service Secrets {
  rpc CreateSecret (CreateSecretRequest) returns (Secret) {
    option (google.api.http) = {
      post: "/v1/secrets"
      body: "*"
    };
  }

  rpc UpdateSecret (UpdateSecretRequest) returns (Secret) {
    option (google.api.http) = {
      patch: "/v1/secrets/{secret.uid}"
      body: "secret"
    };
  }

  rpc DeleteSecret (DeleteSecretRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/secrets/{uid}"
    };
  }

  rpc GetSecret (GetSecretRequest) returns (Secret) {
    option (google.api.http) = {
      get: "/v1/secrets/{uid}"
    };
  }

  rpc ListSecret (ListSecretRequest) returns (ListSecretReply) {
    option (google.api.http) = {
      get: "/v1/secrets"
    };
  }
}

message CreateSecretRequest {
  option (openapi.v3.schema) = {
    required: []
  };

  // mandatory in update-requests
  string uid = 1;
  // mandatory in create-requests;
  // pattern: ^[\\p{Han}\\w\\d-]{0,20}$
  string name = 2 [(validate.rules).string = {pattern: "^[\\p{Han}\\w\\d-]{0,20}$"}];
  // secret's organization; if empty, this is the requestor's organization
  string org_uid = 3;
}

message UpdateSecretRequest {
  option (openapi.v3.schema) = {
    required: ["secret", "fields"]
  };

  // update contents
  Secret secret = 1;
  // name of fileds to be updated
  repeated string fields = 2;
}

message DeleteSecretRequest {
  // secret UID
  string uid = 1;
}

message GetSecretRequest {
  // secret UID
  string uid = 1;
}

message ListSecretRequest {
  int32 page = 1;
  int32 pagesz = 2;

  // filter by orgnization
  string org_uid = 3;
  // filter by creator
  string creator_uid = 4;
  // filter by name pattern
  string name_pattern = 5;

  // filter by secret state
  repeated Secret.State.Enum states = 6;
  // include secret's orgnization in the reply
  bool with_org = 7;
}

message ListSecretReply {
  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated Secret secrets = 2;
  // // the orgnization that the secret, at the corresponding position, belongs to.
  // repeated iam.v1.BaseUser orgs = 3;
}

message Secret {
  option (openapi.v3.schema) = {
    required: ["uid", "name", "org_uid", "state", "created_at"]
  };

  message State {
    enum Enum {
      unspecified = 0;
      // parsing the data specified in the source
      initializing = 1;
      // waiting for the secret related lot to be started
      waiting = 2;
      ongoing = 3;
      finished = 4;
      canceled = 5;
      // error occurred when parsing data specified by the source
      failed = 6;
    }
  }

  // secret UID
  string uid = 1;
  // secret name
  string name = 2;
  // UID of the organization which the secret belongs to
  string org_uid = 3;

  // UID of the data associated with the secret
  string data_uid = 5;
  // creator UID
  string creator_uid = 6;

  // number of elements in the secret
  int32 size = 10;
  // secret state
  State.Enum state = 11;
  // annotated object count (include interpolated ones); only available after lot is finished
  int32 ins_total = 12;
  // annotation result file URL
  string anno_result_url = 13;
  // when state is failed, this field will contain detailed error message
  string error = 14;

  // secret creation time
  google.protobuf.Timestamp created_at = 15;
}
