# Keto 权限系统工作流程分析

## 1. 系统架构

### 1.1 核心组件
1. 命名空间定义（namespaces.keto.ts）
2. 权限规则定义（tuples.txt）
3. 数据库存储
4. RPC 接口服务
5. 缓存层

### 1.2 数据流向
```
命名空间定义 -> 规则定义 -> 数据库存储 -> 缓存层 -> R<PERSON>接口
```

## 2. 命名空间定义流程

### 2.1 命名空间定义
```typescript
// namespaces.keto.ts
class IamRole implements Namespace {
  related: {
    parents: IamGroup[]
    policies: IamPolicy[]
    perms: (IamPerm | SubjectSet<IamRole, "perms">)[]
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      IamRole:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm))
  }
}
```

### 2.2 命名空间编译
1. TypeScript 编译为 JavaScript
2. 生成命名空间元数据
3. 注册到 Keto 服务

## 3. 权限规则定义流程

### 3.1 规则格式
```plaintext
# tuples.txt
IamRole:AnnoJob.viewer#<EMAIL>
IamRole:AnnoJob.viewer#policies@IamPolicy:sys/admin
```

### 3.2 规则解析
```typescript
function parseTuple(tuple: string) {
  const [subjectPart, objectPart] = tuple.split('@');
  const [subject, relation] = subjectPart.split('#');
  const [subjectNamespace, subjectId] = subject.split(':');
  const [objectNamespace, objectId] = objectPart.split(':');
  
  return {
    subject: { namespace: subjectNamespace, id: subjectId },
    relation: relation,
    object: { namespace: objectNamespace, id: objectId }
  };
}
```

### 3.3 规则验证
1. 检查命名空间是否存在
2. 检查关系类型是否有效
3. 检查对象是否存在
4. 验证规则格式

## 4. 数据库操作

### 4.1 规则存储
```typescript
async function storeTuple(tuple: Tuple) {
  // 1. 验证规则
  await validateTuple(tuple);
  
  // 2. 存储到数据库
  await db.relationTuple.create({
    namespace: tuple.subject.namespace,
    object: tuple.subject.id,
    relation: tuple.relation,
    subject: {
      namespace: tuple.object.namespace,
      object: tuple.object.id
    }
  });
  
  // 3. 更新缓存
  await updateCache(tuple);
}
```

### 4.2 规则查询
```typescript
async function queryTuple(query: Query) {
  // 1. 尝试从缓存获取
  const cached = await getFromCache(query);
  if (cached) return cached;
  
  // 2. 从数据库查询
  const result = await db.relationTuple.query(query);
  
  // 3. 更新缓存
  await updateCache(result);
  
  return result;
}
```

## 5. 缓存机制

### 5.1 缓存结构
```typescript
interface CacheEntry {
  key: string;
  value: any;
  timestamp: number;
  ttl: number;
}
```

### 5.2 缓存操作
```typescript
async function updateCache(data: any) {
  // 1. 生成缓存键
  const key = generateCacheKey(data);
  
  // 2. 更新缓存
  await cache.set(key, {
    value: data,
    timestamp: Date.now(),
    ttl: CACHE_TTL
  });
  
  // 3. 触发缓存更新事件
  await emitCacheUpdateEvent(key);
}
```

### 5.3 缓存更新问题
1. **缓存一致性问题**
   - 多实例间的缓存同步
   - 缓存更新延迟
   - 缓存失效处理

2. **解决方案**
   - 使用分布式缓存（如 Redis）
   - 实现缓存更新广播机制
   - 设置合理的缓存 TTL
   - 实现缓存预热机制

## 6. RPC 接口

### 6.1 接口定义
```protobuf
service KetoService {
  // 权限检查
  rpc CheckPermission(CheckRequest) returns (CheckResponse);
  
  // 规则管理
  rpc CreateTuple(CreateRequest) returns (CreateResponse);
  rpc DeleteTuple(DeleteRequest) returns (DeleteResponse);
  rpc QueryTuple(QueryRequest) returns (QueryResponse);
  
  // 缓存管理
  rpc InvalidateCache(InvalidateRequest) returns (InvalidateResponse);
}
```

### 6.2 权限检查流程
```typescript
async function checkPermission(req: CheckRequest): Promise<CheckResponse> {
  // 1. 获取用户信息
  const user = await getUser(req.userId);
  
  // 2. 创建上下文
  const ctx = new Context({
    subject: `IamUser:${user.id}`
  });
  
  // 3. 检查权限
  const hasPermission = await checkPermissionWithCache(ctx, req.permission);
  
  return {
    allowed: hasPermission,
    timestamp: Date.now()
  };
}
```

### 6.3 规则管理流程
```typescript
async function createTuple(req: CreateRequest): Promise<CreateResponse> {
  // 1. 验证规则
  await validateTuple(req.tuple);
  
  // 2. 存储规则
  await storeTuple(req.tuple);
  
  // 3. 更新缓存
  await updateCache(req.tuple);
  
  return {
    success: true,
    timestamp: Date.now()
  };
}
```

## 7. 系统优化

### 7.1 性能优化
1. 使用连接池管理数据库连接
2. 实现批量操作接口
3. 优化缓存策略
4. 使用异步处理机制

### 7.2 可靠性优化
1. 实现重试机制
2. 添加监控和告警
3. 实现故障转移
4. 数据备份和恢复

### 7.3 可维护性优化
1. 完善的日志记录
2. 清晰的错误处理
3. 模块化设计
4. 版本控制

## 8. 总结

Keto 权限系统的工作流程包括：
1. 命名空间定义和编译
2. 权限规则定义和解析
3. 数据库存储和查询
4. 缓存管理和更新
5. RPC 接口服务

系统的主要挑战在于：
1. 缓存一致性的维护
2. 高并发下的性能优化
3. 分布式环境下的数据同步
4. 系统可靠性的保证

通过合理的架构设计和优化策略，可以构建一个高效、可靠的权限管理系统。 