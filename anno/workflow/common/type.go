package common

import (
	"context"
)

type Heartbeat func(ctx context.Context, details ...interface{})

const (
	EvtConfigChannged = "ConfigChanged"
	EvtLotCreated     = "LotCreated"
	EvtLotStarted     = "LotStarted"
	EvtLotPaused      = "LotPaused"
	EvtLotCompleted   = "LotCompleted"
	EvtLotCanceled    = "LotCanceled"
	EvtLotRevertJobs  = "LotRevertJobs"

	EvtJobCompleted    = "JobCompleted"
	EvtJobChanged      = "JobChanged"
	EvtJobDoing        = "JobDoing"
	EvtJobChecking     = "JobChecking"
	EvtJobPendingMerge = "JobPendingMerge"

	// EvtExtraKeyXxx = ""
)

type Event struct {
	Event     string         `json:"event,omitempty"`
	LotID     int64          `json:"lot_id,omitempty"`
	JobID     int64          `json:"job_id,omitempty"`
	JobAction string         `json:"job_action,omitempty"`
	Details   *Details       `json:"details,omitempty"`
	Extra     map[string]any `json:"extra,omitempty"`
	TraceID   string         `json:"trace_id,omitempty"`
}

func (o *Event) SetDetails(v any) {
	o.Details = NewDetails(v)
}

func GetDetails[T any](e *Event) *T {
	return GetDetailsValue[T](e.Details)
}
