// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: iam/v1/user.proto

package iam

import (
	context "context"
	types "gitlab.rp.konvery.work/platform/apis/types"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Users_CreateUser_FullMethodName       = "/iam.v1.Users/CreateUser"
	Users_BatchCreateUsers_FullMethodName = "/iam.v1.Users/BatchCreateUsers"
	Users_UpdateUser_FullMethodName       = "/iam.v1.Users/UpdateUser"
	Users_DeleteUser_FullMethodName       = "/iam.v1.Users/DeleteUser"
	Users_GetMe_FullMethodName            = "/iam.v1.Users/GetMe"
	Users_GetMe2_FullMethodName           = "/iam.v1.Users/GetMe2"
	Users_GetMyFeperm_FullMethodName      = "/iam.v1.Users/GetMyFeperm"
	Users_GetUser_FullMethodName          = "/iam.v1.Users/GetUser"
	Users_ListUser_FullMethodName         = "/iam.v1.Users/ListUser"
	Users_AddTag_FullMethodName           = "/iam.v1.Users/AddTag"
	Users_DeleteTag_FullMethodName        = "/iam.v1.Users/DeleteTag"
	Users_Login_FullMethodName            = "/iam.v1.Users/Login"
	Users_Logout_FullMethodName           = "/iam.v1.Users/Logout"
	Users_SendAuthCode_FullMethodName     = "/iam.v1.Users/SendAuthCode"
	Users_AssumeUser_FullMethodName       = "/iam.v1.Users/AssumeUser"
	Users_RefreshToken_FullMethodName     = "/iam.v1.Users/RefreshToken"
	Users_IsAllowed_FullMethodName        = "/iam.v1.Users/IsAllowed"
	Users_GetPerms_FullMethodName         = "/iam.v1.Users/GetPerms"
)

// UsersClient is the client API for Users service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UsersClient interface {
	CreateUser(ctx context.Context, in *CreateUserRequest, opts ...grpc.CallOption) (*User, error)
	BatchCreateUsers(ctx context.Context, in *BatchCreateUsersRequest, opts ...grpc.CallOption) (*ListUserReply, error)
	UpdateUser(ctx context.Context, in *UpdateUserRequest, opts ...grpc.CallOption) (*User, error)
	DeleteUser(ctx context.Context, in *DeleteUserRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// retrieve my info
	GetMe(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*User, error)
	// retrieve my info; also returns the real user in an assume context
	GetMe2(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*UserContext, error)
	// get my front-end permissions
	GetMyFeperm(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*Feperm, error)
	GetUser(ctx context.Context, in *GetUserRequest, opts ...grpc.CallOption) (*User, error)
	ListUser(ctx context.Context, in *ListUserRequest, opts ...grpc.CallOption) (*ListUserReply, error)
	AddTag(ctx context.Context, in *types.TagRequest, opts ...grpc.CallOption) (*types.TagList, error)
	DeleteTag(ctx context.Context, in *types.TagRequest, opts ...grpc.CallOption) (*types.TagList, error)
	Login(ctx context.Context, in *LoginRequest, opts ...grpc.CallOption) (*LoginReply, error)
	// clear HTTP cookies
	Logout(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	SendAuthCode(ctx context.Context, in *SendAuthCodeRequest, opts ...grpc.CallOption) (*SendAuthCodeReply, error)
	AssumeUser(ctx context.Context, in *AssumeUserRequest, opts ...grpc.CallOption) (*LoginReply, error)
	RefreshToken(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*LoginReply, error)
	// check if the user is allowed to perform the actions
	IsAllowed(ctx context.Context, in *IsAllowedRequest, opts ...grpc.CallOption) (*IsAllowedReply, error)
	// get the permissions granted on a resource
	GetPerms(ctx context.Context, in *GetPermsRequest, opts ...grpc.CallOption) (*GetPermsReply, error)
}

type usersClient struct {
	cc grpc.ClientConnInterface
}

func NewUsersClient(cc grpc.ClientConnInterface) UsersClient {
	return &usersClient{cc}
}

func (c *usersClient) CreateUser(ctx context.Context, in *CreateUserRequest, opts ...grpc.CallOption) (*User, error) {
	out := new(User)
	err := c.cc.Invoke(ctx, Users_CreateUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usersClient) BatchCreateUsers(ctx context.Context, in *BatchCreateUsersRequest, opts ...grpc.CallOption) (*ListUserReply, error) {
	out := new(ListUserReply)
	err := c.cc.Invoke(ctx, Users_BatchCreateUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usersClient) UpdateUser(ctx context.Context, in *UpdateUserRequest, opts ...grpc.CallOption) (*User, error) {
	out := new(User)
	err := c.cc.Invoke(ctx, Users_UpdateUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usersClient) DeleteUser(ctx context.Context, in *DeleteUserRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Users_DeleteUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usersClient) GetMe(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*User, error) {
	out := new(User)
	err := c.cc.Invoke(ctx, Users_GetMe_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usersClient) GetMe2(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*UserContext, error) {
	out := new(UserContext)
	err := c.cc.Invoke(ctx, Users_GetMe2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usersClient) GetMyFeperm(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*Feperm, error) {
	out := new(Feperm)
	err := c.cc.Invoke(ctx, Users_GetMyFeperm_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usersClient) GetUser(ctx context.Context, in *GetUserRequest, opts ...grpc.CallOption) (*User, error) {
	out := new(User)
	err := c.cc.Invoke(ctx, Users_GetUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usersClient) ListUser(ctx context.Context, in *ListUserRequest, opts ...grpc.CallOption) (*ListUserReply, error) {
	out := new(ListUserReply)
	err := c.cc.Invoke(ctx, Users_ListUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usersClient) AddTag(ctx context.Context, in *types.TagRequest, opts ...grpc.CallOption) (*types.TagList, error) {
	out := new(types.TagList)
	err := c.cc.Invoke(ctx, Users_AddTag_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usersClient) DeleteTag(ctx context.Context, in *types.TagRequest, opts ...grpc.CallOption) (*types.TagList, error) {
	out := new(types.TagList)
	err := c.cc.Invoke(ctx, Users_DeleteTag_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usersClient) Login(ctx context.Context, in *LoginRequest, opts ...grpc.CallOption) (*LoginReply, error) {
	out := new(LoginReply)
	err := c.cc.Invoke(ctx, Users_Login_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usersClient) Logout(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Users_Logout_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usersClient) SendAuthCode(ctx context.Context, in *SendAuthCodeRequest, opts ...grpc.CallOption) (*SendAuthCodeReply, error) {
	out := new(SendAuthCodeReply)
	err := c.cc.Invoke(ctx, Users_SendAuthCode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usersClient) AssumeUser(ctx context.Context, in *AssumeUserRequest, opts ...grpc.CallOption) (*LoginReply, error) {
	out := new(LoginReply)
	err := c.cc.Invoke(ctx, Users_AssumeUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usersClient) RefreshToken(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*LoginReply, error) {
	out := new(LoginReply)
	err := c.cc.Invoke(ctx, Users_RefreshToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usersClient) IsAllowed(ctx context.Context, in *IsAllowedRequest, opts ...grpc.CallOption) (*IsAllowedReply, error) {
	out := new(IsAllowedReply)
	err := c.cc.Invoke(ctx, Users_IsAllowed_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usersClient) GetPerms(ctx context.Context, in *GetPermsRequest, opts ...grpc.CallOption) (*GetPermsReply, error) {
	out := new(GetPermsReply)
	err := c.cc.Invoke(ctx, Users_GetPerms_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UsersServer is the server API for Users service.
// All implementations must embed UnimplementedUsersServer
// for forward compatibility
type UsersServer interface {
	CreateUser(context.Context, *CreateUserRequest) (*User, error)
	BatchCreateUsers(context.Context, *BatchCreateUsersRequest) (*ListUserReply, error)
	UpdateUser(context.Context, *UpdateUserRequest) (*User, error)
	DeleteUser(context.Context, *DeleteUserRequest) (*emptypb.Empty, error)
	// retrieve my info
	GetMe(context.Context, *emptypb.Empty) (*User, error)
	// retrieve my info; also returns the real user in an assume context
	GetMe2(context.Context, *emptypb.Empty) (*UserContext, error)
	// get my front-end permissions
	GetMyFeperm(context.Context, *emptypb.Empty) (*Feperm, error)
	GetUser(context.Context, *GetUserRequest) (*User, error)
	ListUser(context.Context, *ListUserRequest) (*ListUserReply, error)
	AddTag(context.Context, *types.TagRequest) (*types.TagList, error)
	DeleteTag(context.Context, *types.TagRequest) (*types.TagList, error)
	Login(context.Context, *LoginRequest) (*LoginReply, error)
	// clear HTTP cookies
	Logout(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	SendAuthCode(context.Context, *SendAuthCodeRequest) (*SendAuthCodeReply, error)
	AssumeUser(context.Context, *AssumeUserRequest) (*LoginReply, error)
	RefreshToken(context.Context, *emptypb.Empty) (*LoginReply, error)
	// check if the user is allowed to perform the actions
	IsAllowed(context.Context, *IsAllowedRequest) (*IsAllowedReply, error)
	// get the permissions granted on a resource
	GetPerms(context.Context, *GetPermsRequest) (*GetPermsReply, error)
	mustEmbedUnimplementedUsersServer()
}

// UnimplementedUsersServer must be embedded to have forward compatible implementations.
type UnimplementedUsersServer struct {
}

func (UnimplementedUsersServer) CreateUser(context.Context, *CreateUserRequest) (*User, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUser not implemented")
}
func (UnimplementedUsersServer) BatchCreateUsers(context.Context, *BatchCreateUsersRequest) (*ListUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCreateUsers not implemented")
}
func (UnimplementedUsersServer) UpdateUser(context.Context, *UpdateUserRequest) (*User, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUser not implemented")
}
func (UnimplementedUsersServer) DeleteUser(context.Context, *DeleteUserRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUser not implemented")
}
func (UnimplementedUsersServer) GetMe(context.Context, *emptypb.Empty) (*User, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMe not implemented")
}
func (UnimplementedUsersServer) GetMe2(context.Context, *emptypb.Empty) (*UserContext, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMe2 not implemented")
}
func (UnimplementedUsersServer) GetMyFeperm(context.Context, *emptypb.Empty) (*Feperm, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMyFeperm not implemented")
}
func (UnimplementedUsersServer) GetUser(context.Context, *GetUserRequest) (*User, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUser not implemented")
}
func (UnimplementedUsersServer) ListUser(context.Context, *ListUserRequest) (*ListUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUser not implemented")
}
func (UnimplementedUsersServer) AddTag(context.Context, *types.TagRequest) (*types.TagList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTag not implemented")
}
func (UnimplementedUsersServer) DeleteTag(context.Context, *types.TagRequest) (*types.TagList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTag not implemented")
}
func (UnimplementedUsersServer) Login(context.Context, *LoginRequest) (*LoginReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Login not implemented")
}
func (UnimplementedUsersServer) Logout(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Logout not implemented")
}
func (UnimplementedUsersServer) SendAuthCode(context.Context, *SendAuthCodeRequest) (*SendAuthCodeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendAuthCode not implemented")
}
func (UnimplementedUsersServer) AssumeUser(context.Context, *AssumeUserRequest) (*LoginReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AssumeUser not implemented")
}
func (UnimplementedUsersServer) RefreshToken(context.Context, *emptypb.Empty) (*LoginReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshToken not implemented")
}
func (UnimplementedUsersServer) IsAllowed(context.Context, *IsAllowedRequest) (*IsAllowedReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsAllowed not implemented")
}
func (UnimplementedUsersServer) GetPerms(context.Context, *GetPermsRequest) (*GetPermsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPerms not implemented")
}
func (UnimplementedUsersServer) mustEmbedUnimplementedUsersServer() {}

// UnsafeUsersServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UsersServer will
// result in compilation errors.
type UnsafeUsersServer interface {
	mustEmbedUnimplementedUsersServer()
}

func RegisterUsersServer(s grpc.ServiceRegistrar, srv UsersServer) {
	s.RegisterService(&Users_ServiceDesc, srv)
}

func _Users_CreateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).CreateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_CreateUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).CreateUser(ctx, req.(*CreateUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Users_BatchCreateUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCreateUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).BatchCreateUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_BatchCreateUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).BatchCreateUsers(ctx, req.(*BatchCreateUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Users_UpdateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).UpdateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_UpdateUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).UpdateUser(ctx, req.(*UpdateUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Users_DeleteUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).DeleteUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_DeleteUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).DeleteUser(ctx, req.(*DeleteUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Users_GetMe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).GetMe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_GetMe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).GetMe(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Users_GetMe2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).GetMe2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_GetMe2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).GetMe2(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Users_GetMyFeperm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).GetMyFeperm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_GetMyFeperm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).GetMyFeperm(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Users_GetUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).GetUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_GetUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).GetUser(ctx, req.(*GetUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Users_ListUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).ListUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_ListUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).ListUser(ctx, req.(*ListUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Users_AddTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(types.TagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).AddTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_AddTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).AddTag(ctx, req.(*types.TagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Users_DeleteTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(types.TagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).DeleteTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_DeleteTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).DeleteTag(ctx, req.(*types.TagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Users_Login_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).Login(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_Login_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).Login(ctx, req.(*LoginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Users_Logout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).Logout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_Logout_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).Logout(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Users_SendAuthCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendAuthCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).SendAuthCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_SendAuthCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).SendAuthCode(ctx, req.(*SendAuthCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Users_AssumeUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssumeUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).AssumeUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_AssumeUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).AssumeUser(ctx, req.(*AssumeUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Users_RefreshToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).RefreshToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_RefreshToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).RefreshToken(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Users_IsAllowed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsAllowedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).IsAllowed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_IsAllowed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).IsAllowed(ctx, req.(*IsAllowedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Users_GetPerms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPermsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsersServer).GetPerms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Users_GetPerms_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsersServer).GetPerms(ctx, req.(*GetPermsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Users_ServiceDesc is the grpc.ServiceDesc for Users service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Users_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "iam.v1.Users",
	HandlerType: (*UsersServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateUser",
			Handler:    _Users_CreateUser_Handler,
		},
		{
			MethodName: "BatchCreateUsers",
			Handler:    _Users_BatchCreateUsers_Handler,
		},
		{
			MethodName: "UpdateUser",
			Handler:    _Users_UpdateUser_Handler,
		},
		{
			MethodName: "DeleteUser",
			Handler:    _Users_DeleteUser_Handler,
		},
		{
			MethodName: "GetMe",
			Handler:    _Users_GetMe_Handler,
		},
		{
			MethodName: "GetMe2",
			Handler:    _Users_GetMe2_Handler,
		},
		{
			MethodName: "GetMyFeperm",
			Handler:    _Users_GetMyFeperm_Handler,
		},
		{
			MethodName: "GetUser",
			Handler:    _Users_GetUser_Handler,
		},
		{
			MethodName: "ListUser",
			Handler:    _Users_ListUser_Handler,
		},
		{
			MethodName: "AddTag",
			Handler:    _Users_AddTag_Handler,
		},
		{
			MethodName: "DeleteTag",
			Handler:    _Users_DeleteTag_Handler,
		},
		{
			MethodName: "Login",
			Handler:    _Users_Login_Handler,
		},
		{
			MethodName: "Logout",
			Handler:    _Users_Logout_Handler,
		},
		{
			MethodName: "SendAuthCode",
			Handler:    _Users_SendAuthCode_Handler,
		},
		{
			MethodName: "AssumeUser",
			Handler:    _Users_AssumeUser_Handler,
		},
		{
			MethodName: "RefreshToken",
			Handler:    _Users_RefreshToken_Handler,
		},
		{
			MethodName: "IsAllowed",
			Handler:    _Users_IsAllowed_Handler,
		},
		{
			MethodName: "GetPerms",
			Handler:    _Users_GetPerms_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "iam/v1/user.proto",
}
