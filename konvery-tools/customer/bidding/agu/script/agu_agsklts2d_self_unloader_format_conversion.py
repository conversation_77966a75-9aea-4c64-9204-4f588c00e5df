import os
import json
import xml.etree.cElementTree as ET
import shutil
import zipfile
import sys

sys.path.append('.')
from customer.common import tools

mac_file = '.DS_Store'
truncated_map = {
    'true': 1,
    'false': 0
}


def run(input_dir):
    try:
        # unzip file
        unzip_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_unzip')
        result_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_result')
        if os.path.exists(unzip_dir):
            shutil.rmtree(unzip_dir)
        with zipfile.ZipFile(input_dir, 'r') as zip_ref:
            zip_ref.extractall(unzip_dir)

        try:
            os.mkdir(result_dir)
        except FileExistsError:
            shutil.rmtree(result_dir)
            os.mkdir(result_dir)

        process_counter = 0
        output_counter = 0

        for ds in os.listdir(unzip_dir):
            if ds != mac_file:
                # ds_rwzbmm5ft0v8loa2urtn
                ds_dir = os.path.join(unzip_dir, ds)
                for num_folder in os.listdir(ds_dir):
                    if num_folder != mac_file:
                        # 2309256060
                        num_folder_dir = os.path.join(ds_dir, num_folder)
                        for chn_folder in os.listdir(num_folder_dir):
                            if chn_folder != mac_file:
                                # 自卸车进场数据0922 copy
                                chn_folder_dir = os.path.join(num_folder_dir, chn_folder)
                                for batch in os.listdir(chn_folder_dir):
                                    if batch != mac_file:
                                        # batch1
                                        batch_dir = os.path.join(chn_folder_dir, batch)
                                        for label in os.listdir(batch_dir):
                                            if label != mac_file:
                                                # label
                                                label_dir = os.path.join(batch_dir, label)
                                                for json_file in os.listdir(label_dir):
                                                    if json_file != mac_file:
                                                        # frame2685.json
                                                        process_counter += 1

                                                        # load json file
                                                        json_dir = os.path.join(label_dir, json_file)
                                                        json_data = json.load(open(json_dir))

                                                        # build ET file
                                                        annotation = ET.Element('annotation')

                                                        ET.SubElement(annotation, 'folder').text = 'VOC2007'
                                                        ET.SubElement(annotation, 'filename').text = json_data['label_meta']['source_info']['file_name']

                                                        annotation_source = ET.SubElement(annotation, 'source')
                                                        ET.SubElement(annotation_source, 'database').text = 'The VOC2007 Database'
                                                        ET.SubElement(annotation_source, 'annotation').text = 'PASCAL VOC2007'
                                                        ET.SubElement(annotation_source, 'image').text = 'flickr'
                                                        ET.SubElement(annotation_source, 'flickrid').text = '325991873'

                                                        annotation_owner = ET.SubElement(annotation, 'owner')
                                                        ET.SubElement(annotation_owner, 'flickrid').text = 'archintent louisville'
                                                        ET.SubElement(annotation_owner, 'name').text = '?'

                                                        annotation_size = ET.SubElement(annotation, 'size')
                                                        ET.SubElement(annotation_size, 'width').text = str(json_data['label_meta']['source_info']['width'])
                                                        ET.SubElement(annotation_size, 'height').text = str(json_data['label_meta']['source_info']['height'])
                                                        ET.SubElement(annotation_size, 'depth').text = '3'

                                                        ET.SubElement(annotation, 'segmented').text = '0'

                                                        for json_label in json_data['labels']:
                                                            annotation_object = ET.SubElement(annotation, 'object')
                                                            ET.SubElement(annotation_object, 'name').text = json_label['class']
                                                            ET.SubElement(annotation_object, 'pose').text = 'Unspecified'
                                                            #try:
                                                            #    ET.SubElement(annotation_object, 'truncated').text = str(truncated_map[json_label['attrs']['turncated'][0]])
                                                            #    ET.SubElement(annotation_object, 'difficult').text = str(truncated_map[json_label['attrs']['turncated'][0]])
                                                            #except:
                                                            #    ET.SubElement(annotation_object, 'truncated').text = str(truncated_map[json_label['attrs']['truncated'][0]])
                                                            #    ET.SubElement(annotation_object, 'difficult').text = str(truncated_map[json_label['attrs']['truncated'][0]])

                                                            annotation_object_bndbox = ET.SubElement(annotation_object, 'bndbox')
                                                            ET.SubElement(annotation_object_bndbox, 'xmin').text = str(json_label['annotation']['data']['x'])
                                                            ET.SubElement(annotation_object_bndbox, 'ymin').text = str(json_label['annotation']['data']['y'])
                                                            ET.SubElement(annotation_object_bndbox, 'xmax').text = str(json_label['annotation']['data']['x'] + json_label['annotation']['data']['width'])
                                                            ET.SubElement(annotation_object_bndbox, 'ymax').text = str(json_label['annotation']['data']['y'] + json_label['annotation']['data']['height'])

                                                        result_tree = ET.ElementTree(annotation)
                                                        ET.indent(result_tree, space="\t", level=0)
                                                        result_tree.write(os.path.join(result_dir, json_file.split('.json')[0] + '.xml'))
                                                        output_counter += 1

        # compress the result folder
        shutil.make_archive(result_dir,
                            format='zip',
                            root_dir=os.path.dirname(result_dir),
                            base_dir=input_dir.split('/')[-1].split('.')[0] + '_result')

        trans_result = {
            "code": 0,
            "output": result_dir,
            "err_msg": '成功',
            "summary": {
                '读取文件数': process_counter,
                '生成文件数': output_counter
            }
        }

    except Exception as e:
        trans_result = {
            "code": 1,
            "err_msg": e,
        }

    for key in trans_result:
        print(key + ': ' + str(trans_result[key]))

    print(trans_result)
    return trans_result


if __name__ == "__main__":
    args = tools.get_args()
    run(args.input)

    # args_input = '/Users/<USER>/Downloads/AGU-阿瓜斯卡连特斯2D自卸车标注_230925adba4_验收_已通过_JSON标注结果文件_20231007_2804.zip'
    # run(args_input)