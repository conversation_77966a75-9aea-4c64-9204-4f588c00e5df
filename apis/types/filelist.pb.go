// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: types/filelist.proto

package types

import (
	_ "github.com/google/gnostic/openapiv3"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Filelist struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Files []*Filelist_File `protobuf:"bytes,1,rep,name=files,proto3" json:"files,omitempty"`
}

func (x *Filelist) Reset() {
	*x = Filelist{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_filelist_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Filelist) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Filelist) ProtoMessage() {}

func (x *Filelist) ProtoReflect() protoreflect.Message {
	mi := &file_types_filelist_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Filelist.ProtoReflect.Descriptor instead.
func (*Filelist) Descriptor() ([]byte, []int) {
	return file_types_filelist_proto_rawDescGZIP(), []int{0}
}

func (x *Filelist) GetFiles() []*Filelist_File {
	if x != nil {
		return x.Files
	}
	return nil
}

type Filelist_File struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	// file size in bytes
	Size float64 `protobuf:"fixed64,2,opt,name=size,proto3" json:"size,omitempty"`
	// file name in path format without `data` prefix: e.g. frame1/meta/config.json
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *Filelist_File) Reset() {
	*x = Filelist_File{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_filelist_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Filelist_File) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Filelist_File) ProtoMessage() {}

func (x *Filelist_File) ProtoReflect() protoreflect.Message {
	mi := &file_types_filelist_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Filelist_File.ProtoReflect.Descriptor instead.
func (*Filelist_File) Descriptor() ([]byte, []int) {
	return file_types_filelist_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Filelist_File) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Filelist_File) GetSize() float64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Filelist_File) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_types_filelist_proto protoreflect.FileDescriptor

var file_types_filelist_proto_rawDesc = []byte{
	0x0a, 0x14, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x6c, 0x69, 0x73, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x1a, 0x1c, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x97, 0x01, 0x0a, 0x08,
	0x46, 0x69, 0x6c, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x46, 0x69, 0x6c, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x1a, 0x52, 0x0a, 0x04, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x3a, 0x10, 0xba, 0x47, 0x0d, 0xba, 0x01, 0x03, 0x75, 0x72,
	0x6c, 0xba, 0x01, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x3a, 0x0b, 0xba, 0x47, 0x08, 0xba, 0x01, 0x05,
	0x66, 0x69, 0x6c, 0x65, 0x73, 0x42, 0x3b, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x50, 0x01,
	0x5a, 0x30, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x3b, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_types_filelist_proto_rawDescOnce sync.Once
	file_types_filelist_proto_rawDescData = file_types_filelist_proto_rawDesc
)

func file_types_filelist_proto_rawDescGZIP() []byte {
	file_types_filelist_proto_rawDescOnce.Do(func() {
		file_types_filelist_proto_rawDescData = protoimpl.X.CompressGZIP(file_types_filelist_proto_rawDescData)
	})
	return file_types_filelist_proto_rawDescData
}

var file_types_filelist_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_types_filelist_proto_goTypes = []interface{}{
	(*Filelist)(nil),      // 0: types.Filelist
	(*Filelist_File)(nil), // 1: types.Filelist.File
}
var file_types_filelist_proto_depIdxs = []int32{
	1, // 0: types.Filelist.files:type_name -> types.Filelist.File
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_types_filelist_proto_init() }
func file_types_filelist_proto_init() {
	if File_types_filelist_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_types_filelist_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Filelist); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_filelist_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Filelist_File); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_types_filelist_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_types_filelist_proto_goTypes,
		DependencyIndexes: file_types_filelist_proto_depIdxs,
		MessageInfos:      file_types_filelist_proto_msgTypes,
	}.Build()
	File_types_filelist_proto = out.File
	file_types_filelist_proto_rawDesc = nil
	file_types_filelist_proto_goTypes = nil
	file_types_filelist_proto_depIdxs = nil
}
