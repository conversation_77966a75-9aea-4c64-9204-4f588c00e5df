// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: types/lang.proto

package types

import (
	_ "github.com/google/gnostic/openapiv3"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Multilingual struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// language => text
	Langs map[string]string `protobuf:"bytes,1,rep,name=langs,proto3" json:"langs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Multilingual) Reset() {
	*x = Multilingual{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_lang_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Multilingual) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Multilingual) ProtoMessage() {}

func (x *Multilingual) ProtoReflect() protoreflect.Message {
	mi := &file_types_lang_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Multilingual.ProtoReflect.Descriptor instead.
func (*Multilingual) Descriptor() ([]byte, []int) {
	return file_types_lang_proto_rawDescGZIP(), []int{0}
}

func (x *Multilingual) GetLangs() map[string]string {
	if x != nil {
		return x.Langs
	}
	return nil
}

type MultilingualItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// item name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// language => text
	Langs map[string]string `protobuf:"bytes,2,rep,name=langs,proto3" json:"langs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *MultilingualItem) Reset() {
	*x = MultilingualItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_lang_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultilingualItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultilingualItem) ProtoMessage() {}

func (x *MultilingualItem) ProtoReflect() protoreflect.Message {
	mi := &file_types_lang_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultilingualItem.ProtoReflect.Descriptor instead.
func (*MultilingualItem) Descriptor() ([]byte, []int) {
	return file_types_lang_proto_rawDescGZIP(), []int{1}
}

func (x *MultilingualItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MultilingualItem) GetLangs() map[string]string {
	if x != nil {
		return x.Langs
	}
	return nil
}

type DisplayItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// item name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// item display name
	DisplayName string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
}

func (x *DisplayItem) Reset() {
	*x = DisplayItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_lang_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisplayItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisplayItem) ProtoMessage() {}

func (x *DisplayItem) ProtoReflect() protoreflect.Message {
	mi := &file_types_lang_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisplayItem.ProtoReflect.Descriptor instead.
func (*DisplayItem) Descriptor() ([]byte, []int) {
	return file_types_lang_proto_rawDescGZIP(), []int{2}
}

func (x *DisplayItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DisplayItem) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

var File_types_lang_proto protoreflect.FileDescriptor

var file_types_lang_proto_rawDesc = []byte{
	0x0a, 0x10, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8b, 0x01, 0x0a, 0x0c, 0x4d, 0x75, 0x6c, 0x74,
	0x69, 0x6c, 0x69, 0x6e, 0x67, 0x75, 0x61, 0x6c, 0x12, 0x34, 0x0a, 0x05, 0x6c, 0x61, 0x6e, 0x67,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x4d, 0x75, 0x6c, 0x74, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x75, 0x61, 0x6c, 0x2e, 0x4c, 0x61, 0x6e,
	0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x6c, 0x61, 0x6e, 0x67, 0x73, 0x1a, 0x38,
	0x0a, 0x0a, 0x4c, 0x61, 0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x3a, 0x0b, 0xba, 0x47, 0x08, 0xba, 0x01, 0x05,
	0x6c, 0x61, 0x6e, 0x67, 0x73, 0x22, 0xae, 0x01, 0x0a, 0x10, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x6c,
	0x69, 0x6e, 0x67, 0x75, 0x61, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x38,
	0x0a, 0x05, 0x6c, 0x61, 0x6e, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x75,
	0x61, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x4c, 0x61, 0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x05, 0x6c, 0x61, 0x6e, 0x67, 0x73, 0x1a, 0x38, 0x0a, 0x0a, 0x4c, 0x61, 0x6e, 0x67,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x3a, 0x12, 0xba, 0x47, 0x0f, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01,
	0x05, 0x6c, 0x61, 0x6e, 0x67, 0x73, 0x22, 0x5f, 0x0a, 0x0b, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x3a, 0x19, 0xba, 0x47,
	0x16, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x3b, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x50, 0x01, 0x5a, 0x30, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x3b, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_types_lang_proto_rawDescOnce sync.Once
	file_types_lang_proto_rawDescData = file_types_lang_proto_rawDesc
)

func file_types_lang_proto_rawDescGZIP() []byte {
	file_types_lang_proto_rawDescOnce.Do(func() {
		file_types_lang_proto_rawDescData = protoimpl.X.CompressGZIP(file_types_lang_proto_rawDescData)
	})
	return file_types_lang_proto_rawDescData
}

var file_types_lang_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_types_lang_proto_goTypes = []interface{}{
	(*Multilingual)(nil),     // 0: types.Multilingual
	(*MultilingualItem)(nil), // 1: types.MultilingualItem
	(*DisplayItem)(nil),      // 2: types.DisplayItem
	nil,                      // 3: types.Multilingual.LangsEntry
	nil,                      // 4: types.MultilingualItem.LangsEntry
}
var file_types_lang_proto_depIdxs = []int32{
	3, // 0: types.Multilingual.langs:type_name -> types.Multilingual.LangsEntry
	4, // 1: types.MultilingualItem.langs:type_name -> types.MultilingualItem.LangsEntry
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_types_lang_proto_init() }
func file_types_lang_proto_init() {
	if File_types_lang_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_types_lang_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Multilingual); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_lang_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MultilingualItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_lang_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisplayItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_types_lang_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_types_lang_proto_goTypes,
		DependencyIndexes: file_types_lang_proto_depIdxs,
		MessageInfos:      file_types_lang_proto_msgTypes,
	}.Build()
	File_types_lang_proto = out.File
	file_types_lang_proto_rawDesc = nil
	file_types_lang_proto_goTypes = nil
	file_types_lang_proto_depIdxs = nil
}
