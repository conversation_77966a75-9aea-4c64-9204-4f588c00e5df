name: Publish OPL typelib to npm

on:
  release:
    types:
      - created
  workflow_dispatch:
    inputs:
      version:
        required: true
        description: The version to release

jobs:
  btp:
    runs-on: ubuntu-latest
    name: Publish
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: "14"
          registry-url: "https://registry.npmjs.org"
      - run: sudo npm i -g npm@7
      - name: Bump version
        run: |-
          cd contrib/namespace-type-lib
          cat <<< $(jq '.version = (env.RELEASE_VERSION | sub("(^refs/tags/v)|(^v)"; ""))' package.json) > package.json
        env:
          RELEASE_VERSION: ${{ github.event.inputs.version || github.ref }}
      - run: |-
          git config --global user.email "<EMAIL>"
          git config --global user.name "ory-bot"
          git add contrib/namespace-type-lib/package.json
          git commit -m "autogen: bump OPL typelib"
      - run: |-
          cd contrib/namespace-type-lib
          npm publish --access public
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN_AENEASR }}
