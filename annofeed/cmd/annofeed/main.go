package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"strings"

	"annofeed/api/client"
	"annofeed/internal/biz"
	"annofeed/internal/conf"
	"annofeed/internal/data"
	"annofeed/internal/mq"
	"annofeed/workflow"
	"annofeed/workflow/preparedata"

	"gitlab.rp.konvery.work/platform/pkg/k8s"
	ilog "gitlab.rp.konvery.work/platform/pkg/log"
	"gitlab.rp.konvery.work/platform/pkg/otel"
	"gitlab.rp.konvery.work/platform/pkg/wf"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/env"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/google/gops/agent"
)

var (
	// Name is the name of the compiled software.
	Name string = "annofeed"
	// flagconf is the config flag.
	flagconf string

	id, _ = os.Hostname()
)

func init() {
	flag.StringVar(&flagconf, "conf", "../../configs/config-local.yaml", "config path, eg: -conf config.yaml")
}

type App struct {
	*kratos.App
	prepAct   *preparedata.Activities
	eventsBiz *biz.EventsBiz
}

func newApp(logger log.Logger, gs *grpc.Server, hs *http.Server, prepAct *preparedata.Activities,
	eventsbiz *biz.EventsBiz) *App {
	return &App{
		App: kratos.New(
			kratos.ID(id),
			kratos.Name(Name),
			kratos.Version(conf.Version),
			kratos.Metadata(map[string]string{}),
			kratos.Logger(logger),
			kratos.Server(
				gs,
				hs,
			),
		),
		prepAct:   prepAct,
		eventsBiz: eventsbiz,
	}
}

func main() {
	flag.Parse()
	c := config.New(
		config.WithSource(
			file.NewSource(flagconf),
			env.NewSource(),
		),
		config.WithResolver(CustomResolver),
	)
	defer c.Close()

	if err := c.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}
	// removing trailing '/' from AvatarUrl and SvcUrl
	bc.FileServer.AvatarUrl = strings.TrimSuffix(bc.FileServer.AvatarUrl, "/")
	bc.FileServer.SvcUrl = strings.TrimSuffix(bc.FileServer.SvcUrl, "/")

	logger := ilog.GetLogger(&ilog.Config{
		Level:  bc.Otel.Log.Level,
		Format: bc.Otel.Log.Format,
	})
	log.SetLogger(logger)

	switch flag.Arg(0) {
	case "migrate":
		data.Migrate(bc.Data.Database, flag.Arg(1))
		return
	case "create-all-policies": // recreate access policies for all objects
		am := newAccessMgr(&bc, logger)
		if err := am.CreateAllPolicies(); err != nil {
			fmt.Println("failed to recreate policies:", err)
			os.Exit(1)
		}
		am.Close()
		return
	case "emit": // emit an event
		mq.Init(bc.Mq, logger)
		err := mq.PublishEvt(context.Background(), flag.Arg(1), flag.Arg(2), json.RawMessage(flag.Arg(3)))
		if err != nil {
			fmt.Println("failed to emit an event:", err)
		}
		return
	}

	client.Init(&bc)
	app, cleanup, err := wireApp(bc.Workspace, bc.Server, bc.Data, bc.FileServer, logger)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	otelcfg := otel.NewOtelCfg(bc.Otel.GetLog(), bc.Otel.GetMetrics(), bc.Otel.GetTracing())
	shutdown, err := otel.InitOtel(Name, conf.Version, otelcfg, logger)
	if err != nil {
		panic(err)
	}
	defer shutdown()

	if k8s.IsInK8s() {
		k8s.InitInCluster()
	}
	initFileServer(bc.FileServer)
	initDownload(bc.Workspace, bc.FileServer, nil)

	workflow.Init(bc.Temporal, logger) // for EventsBiz.BackgroundTask
	workflow.InitKtwf(bc.Temporal, bc.Ktwf, logger)
	wf.RegisterActivity(app.prepAct)
	if !bc.Temporal.DisableWorker {
		go wf.Run()
	}

	ctx, stopmq := context.WithCancel(context.Background())
	defer stopmq()
	if bc.Mq.Redis == nil {
		bc.Mq.Redis = bc.Data.Redis
	}
	mq.Init(bc.Mq, logger)
	err = mq.StartConsumer(ctx, bc.Mq.Consumer)
	if err != nil {
		panic(fmt.Errorf("failed to start consumer: %w", err))
	}

	DebugSubcmd()
	if err := agent.Listen(agent.Options{}); err != nil {
		panic(fmt.Errorf("failed to start gops: %w", err))
	}

	// start and wait for stop signal
	log.Infof("[main] current version: %s", conf.Version)
	if err := app.Run(); err != nil {
		panic(err)
	}
}
