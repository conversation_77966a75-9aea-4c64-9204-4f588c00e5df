syntax = "proto3";

package types;

import "openapi/v3/annotations.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/types;types";
option java_multiple_files = true;
option java_package = "types";

message Range {
  option (openapi.v3.schema) = {
    required: ["min", "max"]
  };

  // min[0] indicates the minimum value; empty list means unspecified
  repeated float min = 1;
  // max[0] indicates the maximum value; empty list means unspecified
  repeated float max = 2;
}

message RangeInt32 {
  option (openapi.v3.schema) = {
    required: ["min", "max"]
  };

  // min[0] indicates the minimum value; empty list means unspecified
  repeated int32 min = 1;
  // max[0] indicates the maximum value; empty list means unspecified
  repeated int32 max = 2;
}
