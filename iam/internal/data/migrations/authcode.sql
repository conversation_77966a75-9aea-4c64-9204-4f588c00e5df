-- mig:up 2022070101 initial
CREATE TABLE authcodes
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    user_id     BIGINT       NOT NULL UNIQUE,
    sequence    INTEGER      NOT NULL DEFAULT 1,
    channel     VARCHAR(64)  NOT NULL, -- sms/email/...
    code        VARCHAR(64)  NOT NULL,
    status      SMALLINT     NOT NULL DEFAULT 0, -- 0-valid, 1-used, 2-expired
    expired_at  TIMESTAMPTZ  NOT NULL,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_authcodes_channel ON authcodes (channel) INCLUDE (status, expired_at);
CREATE INDEX idx_authcodes_created_at ON authcodes (created_at) INCLUDE (status, expired_at);
-- CREATE INDEX idx_authcodes_user_id_code ON authcodes (user_id, code) INCLUDE (sequence, status, expired_at, created_at);

-- mig:up 2023101101 add column receiver to authcodes
<PERSON><PERSON><PERSON> TABLE authcodes ADD COLUMN receiver VARCHAR(64) <PERSON><PERSON><PERSON><PERSON>;
ALTER TABLE authcodes ALTER COLUMN user_id DROP NOT NULL;

-- mig:down 2023101101 add column receiver to authcodes
ALTER TABLE authcodes DROP COLUMN receiver;
DELETE FROM authcodes WHERE user_id IS NULL;
ALTER TABLE authcodes ALTER COLUMN user_id SET NOT NULL;

-- mig:down 2022070101 initial
DROP TABLE authcodes;
