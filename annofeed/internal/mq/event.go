package mq

import (
	"encoding/json"
	"fmt"

	"gitlab.rp.konvery.work/pkg/mq"
)

const (
	PropKeyType    = "x-meta-type"
	PropKeySubtype = "x-meta-subtype"
	PropKeyTraceID = "x-meta-trace-id"
)

type Event struct {
	Type    string  `json:"type"`    //  e.g. AnnoJob
	Subtype string  `json:"subtype"` // e.g. AnnoJob.Claim
	Body    any     `json:"body"`    // it is string type in a consumer handler
	Raw     Message `json:"-"`       // reference to the original message in a consumer handler
	TraceID string  `json:"trace_id"`
}

func NewEventFromMessage(msg mq.Message) *Event {
	p := msg.GetProps()
	return &Event{
		Type:    p[PropKeyType],
		Subtype: p[PropKeySubtype],
		Body:    msg.GetBody(),
		Raw:     msg,
		TraceID: p[PropKeyTraceID],
	}
}

func (o *Event) Message() (mq.Message, error) {
	data, err := json.Marshal(o.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal body: %w", err)
	}
	msg := &mq.BaseMessage{
		Body: string(data),
		Props: map[string]string{
			PropKeyType:    o.Type,
			PropKeySubtype: o.Subtype,
			PropKeyTraceID: o.TraceID,
		},
	}
	return msg, nil
}

func (o *Event) RawBody() string {
	return o.Body.(string)
}
