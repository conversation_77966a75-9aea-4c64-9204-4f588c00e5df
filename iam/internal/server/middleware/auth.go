package middleware

import (
	"context"
	"strings"

	"iam/internal/biz"

	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/jwt"
	"gitlab.rp.konvery.work/platform/pkg/kparser"
)

const (
	HeaderCookie           = "Cookie"
	HeaderAuthorization    = "Authorization"
	HeaderXmdAuthorization = "x-md-Authorization"

	bearerPrefix = "Bearer "
)

// Auth is a middleware to parse authorization.
func Auth(enabled bool, bz *biz.UsersBiz) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			// if md, ok := metadata.FromServerContext(ctx); enabled && ok {
			if tr, ok := transport.FromServerContext(ctx); enabled && ok {
				md := tr.RequestHeader()
				v := md.Get(HeaderAuthorization)
				if v == "" {
					v = md.Get(HeaderXmdAuthorization)
				}
				if v != "" && !strings.HasPrefix(v, bearerPrefix) {
					v = ""
				}
				if v == "" {
					v = tokenFromCookie(md)
				}
				if v == "" {
					return handler(ctx, req)
				}

				claims, valid := jwt.Default().Valid(v[len(bearerPrefix):])
				if !valid {
					return nil, errors.NewErrUnauthorized(errors.WithMessage("invalid token"))
				}

				user, err := bz.Get(ctx, claims.Subject)
				if err != nil {
					return nil, err
				}
				var assumeBy *biz.User
				if claims.AssumeBy != "" {
					assumeBy, err = bz.Get(ctx, claims.AssumeBy)
					if err != nil {
						return nil, err
					}
				}
				ctxUser := biz.UserFromCtx(ctx)
				if ctxUser == nil {
					ctx = biz.NewCtxWithUser(ctx, user, assumeBy)
				} else {
					// fill into the slot reserved by a previous middleware, e.g. Log
					ctxUser.User, ctxUser.AssumeBy = user, assumeBy
				}
			}
			return handler(ctx, req)
		}
	}
}

func tokenFromCookie(header interface{ Values(key string) []string }) string {
	for _, s := range header.Values(HeaderCookie) {
		kvs := kparser.ParseLineKVs(s, "; ", "=")
		if v := kvs["JWT"]; v != "" {
			return bearerPrefix + v
		}
	}
	return ""
}
