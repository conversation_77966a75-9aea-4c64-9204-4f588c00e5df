// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: annofeed/v1/file.proto

package annofeed

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Files_CreateFile_FullMethodName        = "/annofeed.v1.Files/CreateFile"
	Files_UpdateFile_FullMethodName        = "/annofeed.v1.Files/UpdateFile"
	Files_FinishFileUpload_FullMethodName  = "/annofeed.v1.Files/FinishFileUpload"
	Files_GetFileUploadURLs_FullMethodName = "/annofeed.v1.Files/GetFileUploadURLs"
	Files_DeleteFile_FullMethodName        = "/annofeed.v1.Files/DeleteFile"
	Files_GetFile_FullMethodName           = "/annofeed.v1.Files/GetFile"
	Files_ListFile_FullMethodName          = "/annofeed.v1.Files/ListFile"
	Files_ShareFile_FullMethodName         = "/annofeed.v1.Files/ShareFile"
)

// FilesClient is the client API for Files service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FilesClient interface {
	// create a file record and presigned URLs for upload
	CreateFile(ctx context.Context, in *CreateFileRequest, opts ...grpc.CallOption) (*CreateFileReply, error)
	UpdateFile(ctx context.Context, in *UpdateFileRequest, opts ...grpc.CallOption) (*File, error)
	// mark the file upload as completed
	FinishFileUpload(ctx context.Context, in *FinishFileUploadRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetFileUploadURLs(ctx context.Context, in *GetFileUploadURLsRequest, opts ...grpc.CallOption) (*GetFileUploadURLsReply, error)
	// delete a file record. Ongoing multipart upload will be aborted.
	DeleteFile(ctx context.Context, in *DeleteFileRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetFile(ctx context.Context, in *GetFileRequest, opts ...grpc.CallOption) (*File, error)
	ListFile(ctx context.Context, in *ListFileRequest, opts ...grpc.CallOption) (*ListFileReply, error)
	// get a presigned URL to download the file
	ShareFile(ctx context.Context, in *ShareFileRequest, opts ...grpc.CallOption) (*ShareFileReply, error)
}

type filesClient struct {
	cc grpc.ClientConnInterface
}

func NewFilesClient(cc grpc.ClientConnInterface) FilesClient {
	return &filesClient{cc}
}

func (c *filesClient) CreateFile(ctx context.Context, in *CreateFileRequest, opts ...grpc.CallOption) (*CreateFileReply, error) {
	out := new(CreateFileReply)
	err := c.cc.Invoke(ctx, Files_CreateFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *filesClient) UpdateFile(ctx context.Context, in *UpdateFileRequest, opts ...grpc.CallOption) (*File, error) {
	out := new(File)
	err := c.cc.Invoke(ctx, Files_UpdateFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *filesClient) FinishFileUpload(ctx context.Context, in *FinishFileUploadRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Files_FinishFileUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *filesClient) GetFileUploadURLs(ctx context.Context, in *GetFileUploadURLsRequest, opts ...grpc.CallOption) (*GetFileUploadURLsReply, error) {
	out := new(GetFileUploadURLsReply)
	err := c.cc.Invoke(ctx, Files_GetFileUploadURLs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *filesClient) DeleteFile(ctx context.Context, in *DeleteFileRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Files_DeleteFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *filesClient) GetFile(ctx context.Context, in *GetFileRequest, opts ...grpc.CallOption) (*File, error) {
	out := new(File)
	err := c.cc.Invoke(ctx, Files_GetFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *filesClient) ListFile(ctx context.Context, in *ListFileRequest, opts ...grpc.CallOption) (*ListFileReply, error) {
	out := new(ListFileReply)
	err := c.cc.Invoke(ctx, Files_ListFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *filesClient) ShareFile(ctx context.Context, in *ShareFileRequest, opts ...grpc.CallOption) (*ShareFileReply, error) {
	out := new(ShareFileReply)
	err := c.cc.Invoke(ctx, Files_ShareFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FilesServer is the server API for Files service.
// All implementations must embed UnimplementedFilesServer
// for forward compatibility
type FilesServer interface {
	// create a file record and presigned URLs for upload
	CreateFile(context.Context, *CreateFileRequest) (*CreateFileReply, error)
	UpdateFile(context.Context, *UpdateFileRequest) (*File, error)
	// mark the file upload as completed
	FinishFileUpload(context.Context, *FinishFileUploadRequest) (*emptypb.Empty, error)
	GetFileUploadURLs(context.Context, *GetFileUploadURLsRequest) (*GetFileUploadURLsReply, error)
	// delete a file record. Ongoing multipart upload will be aborted.
	DeleteFile(context.Context, *DeleteFileRequest) (*emptypb.Empty, error)
	GetFile(context.Context, *GetFileRequest) (*File, error)
	ListFile(context.Context, *ListFileRequest) (*ListFileReply, error)
	// get a presigned URL to download the file
	ShareFile(context.Context, *ShareFileRequest) (*ShareFileReply, error)
	mustEmbedUnimplementedFilesServer()
}

// UnimplementedFilesServer must be embedded to have forward compatible implementations.
type UnimplementedFilesServer struct {
}

func (UnimplementedFilesServer) CreateFile(context.Context, *CreateFileRequest) (*CreateFileReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateFile not implemented")
}
func (UnimplementedFilesServer) UpdateFile(context.Context, *UpdateFileRequest) (*File, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateFile not implemented")
}
func (UnimplementedFilesServer) FinishFileUpload(context.Context, *FinishFileUploadRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FinishFileUpload not implemented")
}
func (UnimplementedFilesServer) GetFileUploadURLs(context.Context, *GetFileUploadURLsRequest) (*GetFileUploadURLsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFileUploadURLs not implemented")
}
func (UnimplementedFilesServer) DeleteFile(context.Context, *DeleteFileRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteFile not implemented")
}
func (UnimplementedFilesServer) GetFile(context.Context, *GetFileRequest) (*File, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFile not implemented")
}
func (UnimplementedFilesServer) ListFile(context.Context, *ListFileRequest) (*ListFileReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListFile not implemented")
}
func (UnimplementedFilesServer) ShareFile(context.Context, *ShareFileRequest) (*ShareFileReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShareFile not implemented")
}
func (UnimplementedFilesServer) mustEmbedUnimplementedFilesServer() {}

// UnsafeFilesServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FilesServer will
// result in compilation errors.
type UnsafeFilesServer interface {
	mustEmbedUnimplementedFilesServer()
}

func RegisterFilesServer(s grpc.ServiceRegistrar, srv FilesServer) {
	s.RegisterService(&Files_ServiceDesc, srv)
}

func _Files_CreateFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FilesServer).CreateFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Files_CreateFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FilesServer).CreateFile(ctx, req.(*CreateFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Files_UpdateFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FilesServer).UpdateFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Files_UpdateFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FilesServer).UpdateFile(ctx, req.(*UpdateFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Files_FinishFileUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FinishFileUploadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FilesServer).FinishFileUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Files_FinishFileUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FilesServer).FinishFileUpload(ctx, req.(*FinishFileUploadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Files_GetFileUploadURLs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFileUploadURLsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FilesServer).GetFileUploadURLs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Files_GetFileUploadURLs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FilesServer).GetFileUploadURLs(ctx, req.(*GetFileUploadURLsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Files_DeleteFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FilesServer).DeleteFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Files_DeleteFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FilesServer).DeleteFile(ctx, req.(*DeleteFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Files_GetFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FilesServer).GetFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Files_GetFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FilesServer).GetFile(ctx, req.(*GetFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Files_ListFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FilesServer).ListFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Files_ListFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FilesServer).ListFile(ctx, req.(*ListFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Files_ShareFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShareFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FilesServer).ShareFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Files_ShareFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FilesServer).ShareFile(ctx, req.(*ShareFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Files_ServiceDesc is the grpc.ServiceDesc for Files service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Files_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "annofeed.v1.Files",
	HandlerType: (*FilesServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateFile",
			Handler:    _Files_CreateFile_Handler,
		},
		{
			MethodName: "UpdateFile",
			Handler:    _Files_UpdateFile_Handler,
		},
		{
			MethodName: "FinishFileUpload",
			Handler:    _Files_FinishFileUpload_Handler,
		},
		{
			MethodName: "GetFileUploadURLs",
			Handler:    _Files_GetFileUploadURLs_Handler,
		},
		{
			MethodName: "DeleteFile",
			Handler:    _Files_DeleteFile_Handler,
		},
		{
			MethodName: "GetFile",
			Handler:    _Files_GetFile_Handler,
		},
		{
			MethodName: "ListFile",
			Handler:    _Files_ListFile_Handler,
		},
		{
			MethodName: "ShareFile",
			Handler:    _Files_ShareFile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "annofeed/v1/file.proto",
}
