import os
import sys
import shutil
import numpy as np
import copy

from PIL import Image
from pyproj import Transformer
from customer.common import pypcd
from customer.common.bin2pcd.pcdtool.all import write_pcd
import math
from datetime import datetime
sys.path.append('.')
from customer.common import tools


date_format = '%Y%m%d%H%M%S%f'
camera_names = ['fisheye_rear', 'front_tele', 'right_backward', 'fisheye_front', 'fisheye_right', 'left_backward',
                'right_forward', 'fisheye_left', 'front', 'left_forward', 'rear']


def strip_pcd(pcd_file, out_pcd_file):
    pcd = pypcd.point_cloud_from_path(pcd_file)
    new_pc_data = []
    for row in pcd.pc_data:
        point = np.array([row[0], row[1], row[2], 1])
        point[3] = row[4]
        new_pc_data.append(point)
    write_pcd(
        new_pc_data,
        os.path.join(out_pcd_file),
        intensity=True
    )


def merge_pcds_to_one(input_pcds, lidar2worlds, output_pcd):
    new_pc_data = []
    for input_pcd, lidar2world in zip(input_pcds, lidar2worlds):
        pcd = pypcd.point_cloud_from_path(input_pcd)
        for row in pcd.pc_data:
            if row[3] != 0:
                continue
            point = np.array([row[0], row[1], row[2], 1])
            point = lidar2world @ point
            point[3] = row[4]
            new_pc_data.append(point)
    write_pcd(new_pc_data, output_pcd, True)


def parse_params(params_file, camera_names):
    cameras = dict()
    params_data = tools.get_json_data(params_file)
    lidar2body = np.array(params_data['m1Lidar']['lidar_to_body_paradata']['data']).reshape(4, 4)
    for camera_name in camera_names:
        sensor_params = params_data[camera_name]
        camera2body = np.linalg.inv(np.array(sensor_params['camera_to_body_paradata']['data']).reshape(4, 4))
        camera_model_param = sensor_params['camera_model_param']
        if sensor_params['model_type'] == 'equidistant':
            distortion = [2, camera_model_param['k1'], camera_model_param['k2'], camera_model_param['k3'],
                          camera_model_param['k4']]
        elif sensor_params['model_type'] == 'radtan':
            distortion = [0, camera_model_param['k1'], camera_model_param['k2'], camera_model_param['p1'],
                          camera_model_param['p2'], camera_model_param['k3'], camera_model_param['k4'], 0, 0]
        else:
            raise ValueError(f'Unknown model_type: {sensor_params["model_type"]}')
        cameras[camera_name] = {
            'intrinsic': [
                camera_model_param['fx'], 0, camera_model_param['cx'],
                0, camera_model_param['fy'], camera_model_param['cy'],
                0, 0, 1
            ],
            'extrinsic': camera2body.flatten().tolist(),
            'distortion': distortion,
        }
    return cameras


def get_nearest_frame(src, dsts):
    distance = math.inf
    target = None
    for dst in dsts:
        if abs(dst-src) < distance:
            distance = abs(dst-src)
            target = dst
    return target


def run(input_dir, output_dir):
    out_clip_dir = os.path.join(output_dir, os.path.basename(input_dir))
    os.mkdir(out_clip_dir)
    lidar_dir = os.path.join(input_dir, 'lidar_2Hz')
    lidar_files = sorted(tools.get_file_by_extension(lidar_dir, '.pcd'), key=lambda x: x.name)
    image_mapping = dict()
    image_size_mapping = dict()
    for camera_name in camera_names:
        image_mapping[camera_name] = {os.path.basename(img).split('_')[0]: img for img in tools.listdir(os.path.join(input_dir, 'camera', camera_name), full_path=True, sort=True)}
        image_size_mapping[camera_name] = Image.open(list(image_mapping[camera_name].values())[0]).size
    pose_file = os.path.join(input_dir, 'pose.txt')
    pcd_timestamp2pose_mat = dict()
    with open(pose_file, 'r') as f:
        # header = f.readline()
        lines = f.readlines()
        for line in lines:
            line_parts = line.strip().split(' ')
            # pcd_timestamp2pose_mat[float(line_parts[0])] = tools.get_extrinsic_by_txyz_rxyz_opencv(
            #     np.array(line_parts[1:4], dtype=np.float32),
            #     np.array(line_parts[4:], dtype=np.float32)
            # )
            timestamp = datetime.strptime(line_parts[0].split('.')[0], date_format).timestamp()
            pcd_timestamp2pose_mat[timestamp] = np.array(line_parts[1:], dtype=np.float32).reshape(4, 4)
    pose_mats = []
    first_frame = None
    cameras_params = parse_params(os.path.join(input_dir, 'params.txt'), camera_names)
    for lidar_file in lidar_files:
        out_frame_dir = os.path.join(out_clip_dir, lidar_file.name.split('.')[0])
        if first_frame is None:
            first_frame = out_frame_dir
        os.mkdir(out_frame_dir)
        pcd_datetime = lidar_file.name.rsplit('.', 1)[0]
        pcd_timestamp = datetime.strptime(pcd_datetime, date_format).timestamp()
        nearest_pcd_timestamp = get_nearest_frame(pcd_timestamp, pcd_timestamp2pose_mat.keys())
        pose_mat = pcd_timestamp2pose_mat[nearest_pcd_timestamp]
        pose_mats.append(pose_mat)
        pose_mat_inv = np.linalg.inv(pose_mat)
        pose = tools.mat_to_pose(pose_mat)

        for camera_name in camera_names:
            try:
                img_file = image_mapping[camera_name][pcd_datetime]
                shutil.copyfile(
                    img_file,
                    os.path.join(out_frame_dir, f'{camera_name}.jpg')
                )
            except KeyError:
                print(f'process {lidar_file.name} {camera_name} fail')
                black_img = Image.new('RGB', image_size_mapping[camera_name], (0, 0, 0))
                black_img.save(os.path.join(out_frame_dir, f'{camera_name}.jpg'))
        new_cameras_params = copy.deepcopy(cameras_params)
        for camera_name in camera_names:
            sensor_params = np.array(new_cameras_params[camera_name]['extrinsic']).reshape(4, 4)
            new_cameras_params[camera_name]['extrinsic'] = (sensor_params @ pose_mat_inv).flatten().tolist()
        config = {
            "meta": {
                "version": "v2"
            },
            "lidar": {
                'viewpoint': pose,
            },
            "cameras": new_cameras_params,
        }
        tools.write_json_file(config, os.path.join(out_frame_dir, 'params.json'))
    # merge_pcds_to_one(lidar_files, pose_mats, os.path.join(first_frame, 'lidar.pcd'))


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
