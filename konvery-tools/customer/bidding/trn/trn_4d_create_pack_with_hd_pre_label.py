import os
import sys
import numpy as np
from PIL import Image
from scipy.spatial.transform import Rotation as R
import uuid
sys.path.append('.')
from customer.common import tools
from pathlib import Path


camera_size = {
    "lb": (3840, 2160),
    "lf": (3840, 2160),
    "rb": (3840, 2160),
    "rf": (3840, 2160),
    "supplement1": (3840, 2160),
    "supplement2": (3840, 2160),
    "supplement3": (3840, 2160),
    "supplement4": (1920, 1200),
    "supplement5": (3840, 2160),
    "supplement6": (3840, 2160),
    "supplement7": (3840, 2160),
}

camera_name_mapping = {
    'camera_right_rear': 'rb',
    'camera_left_rear': 'lb',
    'camera_left_front': 'lf',
    'camera_rigth_front': 'rf',
    'camera_front_wide': 'supplement1',
    'camera_rear_up': 'supplement2',
    'camera_front_far': 'supplement3',
    'camera_front_fisheye': 'supplement4',
    'camera_left_fisheye': 'supplement5',
    'camera_right_fisheye': 'supplement6',
    'camera_rear_fisheye': 'supplement7'
}

err_color_mapping = {
    "UNKNOWN_COLOR": "OTHER_COLOR"
}

track_id = 1
id2uuid = dict()


def get_points(obj):
    if 'geometry' in obj:
        return obj['geometry']['coords3d_local']
    else:
        return obj['gemoetry']['coords3d_local']


def _get_lanelines_binded_id(topologys, binded_type):
    lanelines_binded_id_list = []
    for topology in topologys:
        if topology.get('binded_type') == binded_type or topology.get('type') == binded_type:
            lanelines_binded_id_list.append(str(topology['binded_id']))
    lanelines_binded_id = ','.join(lanelines_binded_id_list)
    return lanelines_binded_id


def _construct_lanelines(lanelines):
    global track_id
    global id2uuid
    annos = []
    for laneline in lanelines:
        anno = {
            'uuid': str(uuid.uuid4()),
            'track_id': str(track_id),
            'label': {
                'name': 'lanelines',
                'widget': {
                    'name': 'line3d',
                    'data': np.array(get_points(laneline)).flatten().tolist()
                },
                'attrs': [
                    {
                        'name': 'lane_type',
                        'values': [laneline['category']]
                    },
                    {
                        'name': 'lane_color',
                        'values': [err_color_mapping.get(laneline['color'], laneline['color'])]
                    },
                    {
                        'name': 'boundary_forms',
                        'values': [laneline['boundary_forms']]
                    },
                    {
                        'name': 'is_decelerate',
                        'values': ["true" if laneline['is_decelerate'] else "false"]
                    },
                    {
                        'name': 'longitudinal_deceleration_type',
                        'values': [laneline['longitudinal_deceleration_type'] if laneline['longitudinal_deceleration_type'] else "UNKNOWN"]
                    },
                    {
                        'name': 'is_diversionarea',
                        'values': ['false']
                    },
                    {
                        'name': 'OriginalID',
                        'values': [str(laneline['id'])]
                    }
                ]
            },
            'source': 'manual'
        }
        annos.append(anno)
        id2uuid[laneline['id']] = anno['uuid']
        track_id += 1
    return annos


def _construct_keypoints(keypoints):
    global track_id
    annos = []
    for keypoint in keypoints:
        anno = {
            'uuid': str(uuid.uuid4()),
            'track_id': str(track_id),
            'label': {
                'name': 'keypoints',
                'widget': {
                    'name': 'point3d',
                    'data': get_points(keypoint)
                },
                'attrs': [
                    {
                        'name': 'points_type',
                        'values': [keypoint['category']]
                    },
                    {
                        'name': 'OriginalID',
                        'values': [str(keypoint['id'])]
                    },
                    {
                        'name': 'lanelines_binded_id',
                        'values': [_get_lanelines_binded_id(keypoint['topology'], 'lanelines')]
                    }
                ]
            },
            'source': 'manual'
        }
        annos.append(anno)
        track_id += 1
    return annos


def _construct_poles(poles):
    global track_id
    annos = []
    for obj in poles:
        anno = {
            'uuid': str(uuid.uuid4()),
            'track_id': str(track_id),
            'label': {
                'name': 'Pole',
                'widget': {
                    'name': 'line3d',
                    'data': np.array(get_points(obj)).flatten().tolist()
                },
                'attrs': [
                    {
                        'name': 'OriginalID',
                        'values': [str(obj['id'])]
                    },
                    {
                        'name': 'Pole_type',
                        'values': [obj['category']]
                    },
                    {
                        'name': 'lanes_binded_ID',
                        'values': [_get_lanelines_binded_id(obj['topology'], 'lanes')]
                    }
                ]
            },
            'source': 'manual'
        }
        annos.append(anno)
        track_id += 1
    return annos


def _construct_other_line3d(objs):
    # support type: stoplines, boundaries
    global track_id
    annos = []
    for obj in objs:
        anno = {
            'uuid': str(uuid.uuid4()),
            'track_id': str(track_id),
            'label': {
                'name': obj['category'],
                'widget': {
                    'name': 'line3d',
                    'data': np.array(get_points(obj)).flatten().tolist()
                },
                'attrs': [
                    {
                        'name': 'OriginalID',
                        'values': [str(obj['id'])]
                    },
                    {
                        'name': 'lanes_binded_ID',
                        'values': [_get_lanelines_binded_id(obj['topology'], 'lanes')]
                    }
                ]
            },
            'source': 'manual'
        }
        annos.append(anno)
        track_id += 1
    return annos


def _construct_traffic_marks(traffic_marks):
    global track_id
    annos = []
    for traffic_mark in traffic_marks:
        anno = {
            'uuid': str(uuid.uuid4()),
            'track_id': str(track_id),
            'label': {
                'name': traffic_mark['category'] if isinstance(traffic_mark['category'], str) else '_'.join(traffic_mark['category']),
                'widget': {
                    'name': 'poly3d',
                    'data': np.array(get_points(traffic_mark)[:-1]).flatten().tolist()
                },
                'attrs': [
                    {
                        'name': 'OriginalID',
                        'values': [str(traffic_mark['id'])]
                    },
                    {
                        'name': 'lanes_binded_ID',
                        'values': [_get_lanelines_binded_id(traffic_mark['topology'], 'lanes')]
                    }
                ]
            },
            'source': 'manual'
        }
        if traffic_mark['category'] == 'OTHER':
            anno['label']['name'] = 'traffffic_marks_other'
        annos.append(anno)
        track_id += 1
    return annos


def _construct_traffic_lights(traffic_lights):
    global track_id
    annos = []
    for traffic_light in traffic_lights:
        if len(get_points(traffic_light)) != 5:
            raise ValueError(f"the length of coords3d_local is not equal to 5, {get_points(traffic_light)}")
        anno = {
            'uuid': str(uuid.uuid4()),
            'track_id': str(track_id),
            'label': {
                'name': traffic_light['category'],
                'widget': {
                    'name': 'poly3d',
                    'data': np.array(get_points(traffic_light)[:-1]).flatten().tolist()
                },
                'attrs': [
                    {
                        'name': 'OriginalID',
                        'values': [str(traffic_light['id'])]
                    },
                    {
                        'name': 'lanes_binded_ID',
                        'values': [_get_lanelines_binded_id(traffic_light['topology'], 'lanes')]
                    },
                    {
                        'name': 'roads_binded_ID',
                        'values': [_get_lanelines_binded_id(traffic_light['topology'], 'roads')]
                    }
                ]
            },
            'source': 'manual'
        }
        annos.append(anno)
        track_id += 1
    return annos


def _construct_traffic_signs(traffic_signs):
    global track_id
    annos = []
    for traffic_sign in traffic_signs:
        anno = {
            'uuid': str(uuid.uuid4()),
            'track_id': str(track_id),
            'label': {
                'name': "TrafficSign",
                'widget': {
                    'name': 'poly3d',
                    'data': np.array(get_points(traffic_sign)[:-1]).flatten().tolist()
                },
                'attrs': [
                    {
                        'name': 'shape',
                        'values': [traffic_sign['shape']]
                    },
                    {
                        'name': 'TrafficSign_type',
                        'values': [str(traffic_sign['code'])]
                    },
                    {
                        'name': 'number',
                        'values': [traffic_sign['value']]
                    },
                    {
                        'name': 'direction',
                        'values': [traffic_sign['direction']]
                    },
                    {
                        'name': 'OriginalID',
                        'values': [str(traffic_sign['id'])]
                    },
                    {
                        'name': 'lanes_binded_ID',
                        'values': [_get_lanelines_binded_id(traffic_sign['topology'], 'lanes')]
                    }
                ]
            },
            'source': 'manual'
        }
        annos.append(anno)
        track_id += 1
    return annos


def _construct_lanes(objs):
    global track_id
    global id2uuid
    annos = []
    for obj in objs:
        anno = {
            'uuid': str(uuid.uuid4()),
            'track_id': str(track_id),
            'label': {
                'name': "lanes",
                'widget': {
                    'name': 'line3d',
                    'data': np.array(get_points(obj)).flatten().tolist()
                },
                'attrs': [
                    {
                        'name': 'OriginalID',
                        'values': [str(obj['id'])]
                    },
                    {
                        'name': 'lanes_category',
                        'values': [obj['category']]
                    },
                    {
                        'name': 'is_virtual',
                        'values': ["true" if obj['is_virtual'] else "false"]
                    },
                    {
                        'name': 'transition_type',
                        'values': [obj['transition_type']]
                    },
                    {
                        'name': 'Prev_lane_ids_normal',
                        'values': [_get_lane_relationship(obj['prev_lane_ids']['normal'])]
                    },
                    {
                        'name': 'Prev_lane_ids_straight',
                        'values': [_get_lane_relationship(obj['prev_lane_ids']['straight'])]
                    },
                    {
                        'name': 'Prev_lane_ids_left',
                        'values': [_get_lane_relationship(obj['prev_lane_ids']['left'])]
                    },
                    {
                        'name': 'Prev_lane_ids_right',
                        'values': [_get_lane_relationship(obj['prev_lane_ids']['right'])]
                    },
                    {
                        'name': 'Prev_lane_ids_uturn',
                        'values': [_get_lane_relationship(obj['prev_lane_ids']['uturn'])]
                    },
                    {
                        'name': 'Next_lane_ids_normal',
                        'values': [_get_lane_relationship(obj['next_lane_ids']['normal'])]
                    },
                    {
                        'name': 'Next_lane_ids_straight',
                        'values': [_get_lane_relationship(obj['next_lane_ids']['straight'])]
                    },
                    {
                        'name': 'Next_lane_ids_left',
                        'values': [_get_lane_relationship(obj['next_lane_ids']['left'])]
                    },
                    {
                        'name': 'Next_lane_ids_right',
                        'values': [_get_lane_relationship(obj['next_lane_ids']['right'])]
                    },
                    {
                        'name': 'Next_lane_ids_uturn',
                        'values': [_get_lane_relationship(obj['next_lane_ids']['uturn'])]
                    },
                    {
                        'name': 'Left_bounds',
                        'values': [str(obj['left_bounds'][0])]
                    },
                    {
                        'name': 'right_bounds',
                        'values': [str(obj['right_bounds'][0])]
                    }
                ]
            },
            'source': 'manual'
        }
        annos.append(anno)
        track_id += 1
        # if obj['left_bounds'][0] in id2uuid:
        #     annos.append(_build_group("left_bounds", [anno['uuid'], id2uuid[obj['left_bounds'][0]]]))
        # if obj['right_bounds'][0] in id2uuid:
        #     annos.append(_build_group("right_bounds", [anno['uuid'], id2uuid[obj['right_bounds'][0]]]))
    return annos


def _get_lane_relationship(obj):
    if isinstance(obj, list):
        if len(obj) == 0:
            return 'none'
        elif len(obj) == 1:
            obj = obj[0]
        else:
            raise ValueError(f"the length of obj is not equal to 1, obj id: {obj['id']}")
    if obj['main_bind_lane'] != 'none':
        return obj['main_bind_lane']
    elif obj['bind_lane_list']:
        return ','.join(obj['bind_lane_list'])
    else:
        return 'none'


def _build_group(name, parts):
    global track_id
    group = {
        'uuid': str(uuid.uuid4()),
        'track_id': str(track_id),
        "label": {
            "name": name,
            "widget": None,
            "attrs": []
        },
        "compound": {
            "parts": parts
        },
        "source": "manual"
    }
    track_id += 1
    return group


def construct_annos(pre_label_file, lidar_path):
    annos = []
    pre_label = tools.get_json_data(pre_label_file)
    gt = pre_label['physical_objects']['ground_truth']
    annos.extend(_construct_lanelines(gt['lanelines']))
    annos.extend(_construct_keypoints(gt['keypoints']))
    annos.extend(_construct_other_line3d(gt['boundaries']))
    annos.extend(_construct_other_line3d(gt['stoplines']))
    annos.extend(_construct_traffic_marks(gt['traffic_marks']))
    annos.extend(_construct_traffic_lights(gt['traffic_lights']))
    annos.extend(_construct_traffic_signs(gt['traffic_signs']))
    annos.extend(_construct_poles(gt['poles']))
    annos.extend(_construct_lanes(gt['lanes']))

    config = {
        "element_annos": [
            {
                "name": os.sep.join(Path(lidar_path).parts[-3:-1]),
                "rawdata_annos": [
                    {
                        "name": os.sep.join(Path(lidar_path).parts[-3:]),
                        "objects": annos,
                        "attrs": []
                    }
                ],
                "attrs": []
            }
        ]
    }
    parts = list(Path(lidar_path).parts)
    parts[-4] = 'annos'
    tools.write_json_file(config, os.path.join(*parts[:-1], 'annos.json'))


def run(input_dir, output_dir):
    submap_file = os.path.join(input_dir, 'output_data', '3dannotation_data', 'submaplist.json')
    bevclould_data_file = os.path.join(input_dir, 'output_data', '3dannotation_data', 'bevcloud_data.json')
    submaplist = tools.get_json_data(submap_file)
    lidar_file = tools.get_file_by_extension(os.path.join(input_dir, 'output_data', '3dannotation_data'), '.pcd')[0]
    frames = []
    for submap in submaplist:
        cameras = {}
        lidar_parts = Path(submap['lidar_path']).parts
        if not os.path.exists(os.path.join(input_dir, 'input_data', *lidar_parts[:3])):
            continue
        ele_out_dir = os.path.join(output_dir, 'input_data', *lidar_parts[:3])
        os.makedirs(ele_out_dir)
        ele_anno_out_dir = os.path.join(output_dir, 'input_data', 'annos', *lidar_parts[1:3])
        os.makedirs(ele_anno_out_dir)
        tools.write_json_file({
            "element_annos": [
                {
                    "name": os.path.join(*lidar_parts[1:3]),
                    "rawdata_annos": [],
                    "attrs": []
                }
            ]
        }, os.path.join(ele_anno_out_dir, 'annos.json'))
        frames.append(ele_out_dir)
        lidar2ego_pose = submap['lidar2ego_translation'] + submap['lidar2ego_rotation']
        lidar2ego_mat = tools.pose_to_mat(lidar2ego_pose)
        for cam_name, cam_params in submap['cams'].items():
            cur2center_mat = np.array(cam_params['curbase2centerbase_pose']).reshape(4, -1)
            cam_extrinsic = np.array(cam_params['cam_extrinsic'] + [[0, 0, 0, 1]])
            temp_mat = np.eye(4)
            temp_mat[1, 1] = -1
            temp_mat[2, 2] = -1
            extrinsic = cam_extrinsic @ temp_mat @ cur2center_mat @ np.linalg.inv(lidar2ego_mat)
            if cam_params['cam_distortion_type'] == 'fisheye':
                distortion = [2] + cam_params['cam_distortion'][:2] + cam_params['cam_distortion'][4:6]
            else:
                distortion = [0] + cam_params['cam_distortion'] + [0, 0]
            cameras[camera_name_mapping[cam_name]] = {
                "extrinsic": extrinsic.flatten().tolist(),
                "intrinsic": np.array(cam_params['cam_intrinsic']).flatten().tolist(),
                "distortion": distortion
            }
            if cam_params['img_path']:
                img_parts = Path(cam_params['img_path']).parts
                src_img_path = os.path.join(input_dir, 'input_data', cam_params['img_path'])
                dst_img_path = os.path.join(ele_out_dir, f'{img_parts[3]}.jpg')
                try:
                    tools.copy_file(src_img_path, dst_img_path)
                except FileNotFoundError as e:
                    print(src_img_path + ' not found')
                    black_img = Image.new('RGB', camera_size[camera_name_mapping[cam_name]], (0, 0, 0))
                    black_img.save(os.path.join(ele_out_dir, camera_name_mapping[cam_name] + '.jpg'))
            else:
                black_img = Image.new('RGB', camera_size[camera_name_mapping[cam_name]], (0, 0, 0))
                black_img.save(os.path.join(ele_out_dir, camera_name_mapping[cam_name] + '.jpg'))
        lidar = {
            "viewpoint": lidar2ego_pose[:3] + [0, 0, 0, 1],
            "pose": lidar2ego_pose
        }
        config = {
            "meta": {
                "version": "v2"
            },
            "lidar": lidar,
            "cameras": cameras
        }
        tools.write_json_file(config, os.path.join(ele_out_dir, 'params.json'))

    first_frame = sorted(frames)[0]
    dst_lidar_path = os.path.join(first_frame, 'lidar.pcd')
    tools.copy_file(lidar_file.path, dst_lidar_path)
    construct_annos(bevclould_data_file, dst_lidar_path)


if __name__ == '__main__':
    input_dir = '/Users/<USER>/Downloads/trn/1010/hd'
    output_dir = '/Users/<USER>/Downloads/trn/1010/hd_out'
    tools.check_dir(output_dir)
    for sub_dir in tools.listdir(input_dir, full_path=True):
        out_sub_dir = os.path.join(output_dir, os.path.basename(sub_dir))
        os.mkdir(out_sub_dir)
        run(sub_dir, out_sub_dir)
