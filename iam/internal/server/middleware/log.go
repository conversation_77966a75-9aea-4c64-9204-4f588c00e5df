// copy from kratos
package middleware

import (
	"context"
	"fmt"
	"time"

	"iam/internal/biz"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

// Redacter defines how to log an object
type Redacter interface {
	Redact() string
}

// Log is an server logging middleware.
func Log(logger log.Logger) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			var (
				code      int32
				reason    string
				kind      string
				operation string
			)
			startTime := time.Now()
			if info, ok := transport.FromServerContext(ctx); ok {
				kind = info.Kind().String()
				operation = info.Operation()
			}

			op := biz.UserFromCtx(ctx)
			if op == nil {
				// reserve a slot for a later middleware, e.g. LoadUser, to fill-in
				ctx = biz.NewCtxWithUser(ctx, &biz.User{})
				op = biz.UserFromCtx(ctx)
			}

			reply, err = handler(ctx, req)
			if se := errors.FromError(err); se != nil {
				code = se.Code
				reason = se.Reason
			}

			// retrieve the user info
			operator, assumeBy := "", ""
			if !op.IsEmpty() {
				operator = op.GetUid() + ":" + op.Role + "@" + op.OrgUid
				if by := op.AssumeBy; by != nil {
					assumeBy = by.GetUid() + ":" + by.Role + "@" + by.OrgUid
				}
			}

			level, stack := extractError(err)
			log.Log(logger, ctx, level, "API call "+lo.Ternary(err == nil, "ok", "failed"),
				"kind", "server",
				"component", kind,
				"operation", operation,
				"args", extractArgs(req),
				"code", code,
				"reason", reason,
				"stack", stack,
				"latency", time.Since(startTime).Seconds(),
				"operator", operator,
				"assumeBy", assumeBy,
			)
			return
		}
	}
}

// extractArgs returns the string of the req
func extractArgs(req interface{}) string {
	if redacter, ok := req.(Redacter); ok {
		return redacter.Redact()
	}
	if stringer, ok := req.(fmt.Stringer); ok {
		return stringer.String()
	}
	return fmt.Sprintf("%+v", req)
}

// extractError returns the string of the error
func extractError(err error) (log.LogLevel, string) {
	if err != nil {
		return log.LevelError, fmt.Sprintf("%+v", err)
	}
	return log.LevelInfo, ""
}
