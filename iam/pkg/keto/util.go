package keto

import (
	"fmt"
	"strings"

	keto "github.com/ory/keto/proto/ory/keto/relation_tuples/v1alpha2"
)

// type Pager struct {
// 	Size  int
// 	Token string
// }

// PermClass returns the class name of the permission.
func PermClass(perm string) string {
	cls, _, found := strings.Cut(perm, ".")
	if !found {
		return ""
	}
	return cls
}

// PermName returns the name part of the permission.
func PermName(perm string) string {
	_, name, found := strings.Cut(perm, ".")
	if !found {
		return perm
	}
	return name
}

func MakeScopeObj(ns, obj string) string {
	return ns + "." + obj
}

func MakeObjectName(ns, obj string) string {
	return ns + ":" + obj
}

func ParseObjectName(name string) (ns, obj string) {
	ns, obj, found := strings.Cut(name, ":")
	if !found {
		ns, obj = obj, ns
		// return "", "", fmt.Errorf("bad resource: %v", name)
	}
	return
}

func ParseUserGroupName(name string) (ns, obj string, err error) {
	ns, obj = ParseObjectName(name)
	if ns == "" {
		ns = UserNs
	}
	if ns != UserNs && ns != GroupNs {
		return "", "", fmt.Errorf("bad user: %v", name)
	}
	return
}

// WalkSubjectTree traverses the tree and call fn for each node
func WalkSubjectTree(tree *SubjectTree, fn func(tuple *RelationTuple, isLeaf bool) error) error {
	if tree == nil {
		return nil
	}

	if err := fn(tree.Tuple, tree.NodeType == keto.NodeType_NODE_TYPE_LEAF); err != nil {
		return err
	}
	for _, node := range tree.Children {
		err := WalkSubjectTree(node, fn)
		if err != nil {
			return err
		}
	}
	return nil
}

// Ptr returns the pointer to v
func Ptr[T any](v T) *T {
	return &v
}

// PtrIfNot returns the pointer to v if v != foo
func PtrIfNot[T comparable](v, foo T) *T {
	if v != foo {
		return &v
	}
	return nil
}

func EscapeName(name string) string {
	return strings.ReplaceAll(name, ":", ".")
}
