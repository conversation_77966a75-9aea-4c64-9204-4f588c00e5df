// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: iam/v1/team.proto

package iam

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateTeamRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateTeamRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTeamRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTeamRequestMultiError, or nil if none found.
func (m *CreateTeamRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTeamRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for Desc

	// no validation rules for Avatar

	// no validation rules for Province

	// no validation rules for City

	// no validation rules for ParentUid

	if _, ok := _CreateTeamRequest_Type_NotInLookup[m.GetType()]; ok {
		err := CreateTeamRequestValidationError{
			field:  "Type",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := Team_Type_Enum_name[int32(m.GetType())]; !ok {
		err := CreateTeamRequestValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateTeamRequest_Owner_Pattern.MatchString(m.GetOwner()) {
		err := CreateTeamRequestValidationError{
			field:  "Owner",
			reason: "value does not match regex pattern \"^(|uid:\\\\w+|phone:\\\\+\\\\d+|email:[\\\\w.-]+@[\\\\w.-]+\\\\.[\\\\w.]+)$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateTeamRequestMultiError(errors)
	}

	return nil
}

// CreateTeamRequestMultiError is an error wrapping multiple validation errors
// returned by CreateTeamRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateTeamRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTeamRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTeamRequestMultiError) AllErrors() []error { return m }

// CreateTeamRequestValidationError is the validation error returned by
// CreateTeamRequest.Validate if the designated constraints aren't met.
type CreateTeamRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTeamRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTeamRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTeamRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTeamRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTeamRequestValidationError) ErrorName() string {
	return "CreateTeamRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTeamRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTeamRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTeamRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTeamRequestValidationError{}

var _CreateTeamRequest_Type_NotInLookup = map[Team_Type_Enum]struct{}{
	0: {},
}

var _CreateTeamRequest_Owner_Pattern = regexp.MustCompile("^(|uid:\\w+|phone:\\+\\d+|email:[\\w.-]+@[\\w.-]+\\.[\\w.]+)$")

// Validate checks the field values on UpdateTeamRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateTeamRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTeamRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTeamRequestMultiError, or nil if none found.
func (m *UpdateTeamRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTeamRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTeam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTeamRequestValidationError{
					field:  "Team",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTeamRequestValidationError{
					field:  "Team",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTeam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTeamRequestValidationError{
				field:  "Team",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateTeamRequestMultiError(errors)
	}

	return nil
}

// UpdateTeamRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateTeamRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateTeamRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTeamRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTeamRequestMultiError) AllErrors() []error { return m }

// UpdateTeamRequestValidationError is the validation error returned by
// UpdateTeamRequest.Validate if the designated constraints aren't met.
type UpdateTeamRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTeamRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTeamRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTeamRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTeamRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTeamRequestValidationError) ErrorName() string {
	return "UpdateTeamRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTeamRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTeamRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTeamRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTeamRequestValidationError{}

// Validate checks the field values on DeleteTeamRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteTeamRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteTeamRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteTeamRequestMultiError, or nil if none found.
func (m *DeleteTeamRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteTeamRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return DeleteTeamRequestMultiError(errors)
	}

	return nil
}

// DeleteTeamRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteTeamRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteTeamRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteTeamRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteTeamRequestMultiError) AllErrors() []error { return m }

// DeleteTeamRequestValidationError is the validation error returned by
// DeleteTeamRequest.Validate if the designated constraints aren't met.
type DeleteTeamRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteTeamRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteTeamRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteTeamRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteTeamRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteTeamRequestValidationError) ErrorName() string {
	return "DeleteTeamRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteTeamRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteTeamRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteTeamRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteTeamRequestValidationError{}

// Validate checks the field values on GetTeamsRootRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTeamsRootRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTeamsRootRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTeamsRootRequestMultiError, or nil if none found.
func (m *GetTeamsRootRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTeamsRootRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetTeamsRootRequestMultiError(errors)
	}

	return nil
}

// GetTeamsRootRequestMultiError is an error wrapping multiple validation
// errors returned by GetTeamsRootRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTeamsRootRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTeamsRootRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTeamsRootRequestMultiError) AllErrors() []error { return m }

// GetTeamsRootRequestValidationError is the validation error returned by
// GetTeamsRootRequest.Validate if the designated constraints aren't met.
type GetTeamsRootRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTeamsRootRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTeamsRootRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTeamsRootRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTeamsRootRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTeamsRootRequestValidationError) ErrorName() string {
	return "GetTeamsRootRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTeamsRootRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTeamsRootRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTeamsRootRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTeamsRootRequestValidationError{}

// Validate checks the field values on GetTeamRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetTeamRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTeamRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetTeamRequestMultiError,
// or nil if none found.
func (m *GetTeamRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTeamRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetTeamRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := GetTeamRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetTeamRequestMultiError(errors)
	}

	return nil
}

// GetTeamRequestMultiError is an error wrapping multiple validation errors
// returned by GetTeamRequest.ValidateAll() if the designated constraints
// aren't met.
type GetTeamRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTeamRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTeamRequestMultiError) AllErrors() []error { return m }

// GetTeamRequestValidationError is the validation error returned by
// GetTeamRequest.Validate if the designated constraints aren't met.
type GetTeamRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTeamRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTeamRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTeamRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTeamRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTeamRequestValidationError) ErrorName() string { return "GetTeamRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetTeamRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTeamRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTeamRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTeamRequestValidationError{}

var _GetTeamRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on ListTeamByIDsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTeamByIDsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTeamByIDsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTeamByIDsRequestMultiError, or nil if none found.
func (m *ListTeamByIDsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTeamByIDsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListTeamByIDsRequestMultiError(errors)
	}

	return nil
}

// ListTeamByIDsRequestMultiError is an error wrapping multiple validation
// errors returned by ListTeamByIDsRequest.ValidateAll() if the designated
// constraints aren't met.
type ListTeamByIDsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTeamByIDsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTeamByIDsRequestMultiError) AllErrors() []error { return m }

// ListTeamByIDsRequestValidationError is the validation error returned by
// ListTeamByIDsRequest.Validate if the designated constraints aren't met.
type ListTeamByIDsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTeamByIDsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTeamByIDsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTeamByIDsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTeamByIDsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTeamByIDsRequestValidationError) ErrorName() string {
	return "ListTeamByIDsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListTeamByIDsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTeamByIDsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTeamByIDsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTeamByIDsRequestValidationError{}

// Validate checks the field values on ListTeamRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListTeamRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTeamRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTeamRequestMultiError, or nil if none found.
func (m *ListTeamRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTeamRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListTeamRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ParentUid

	// no validation rules for NamePattern

	// no validation rules for TeamType

	if len(errors) > 0 {
		return ListTeamRequestMultiError(errors)
	}

	return nil
}

// ListTeamRequestMultiError is an error wrapping multiple validation errors
// returned by ListTeamRequest.ValidateAll() if the designated constraints
// aren't met.
type ListTeamRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTeamRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTeamRequestMultiError) AllErrors() []error { return m }

// ListTeamRequestValidationError is the validation error returned by
// ListTeamRequest.Validate if the designated constraints aren't met.
type ListTeamRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTeamRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTeamRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTeamRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTeamRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTeamRequestValidationError) ErrorName() string { return "ListTeamRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListTeamRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTeamRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTeamRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTeamRequestValidationError{}

// Validate checks the field values on ListTeamReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListTeamReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTeamReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListTeamReplyMultiError, or
// nil if none found.
func (m *ListTeamReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTeamReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetTeams() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListTeamReplyValidationError{
						field:  fmt.Sprintf("Teams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListTeamReplyValidationError{
						field:  fmt.Sprintf("Teams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListTeamReplyValidationError{
					field:  fmt.Sprintf("Teams[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListTeamReplyMultiError(errors)
	}

	return nil
}

// ListTeamReplyMultiError is an error wrapping multiple validation errors
// returned by ListTeamReply.ValidateAll() if the designated constraints
// aren't met.
type ListTeamReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTeamReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTeamReplyMultiError) AllErrors() []error { return m }

// ListTeamReplyValidationError is the validation error returned by
// ListTeamReply.Validate if the designated constraints aren't met.
type ListTeamReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTeamReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTeamReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTeamReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTeamReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTeamReplyValidationError) ErrorName() string { return "ListTeamReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListTeamReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTeamReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTeamReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTeamReplyValidationError{}

// Validate checks the field values on ListMembersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMembersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMembersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMembersRequestMultiError, or nil if none found.
func (m *ListMembersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMembersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListMembersRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Uid

	// no validation rules for NamePattern

	// no validation rules for Role

	if len(errors) > 0 {
		return ListMembersRequestMultiError(errors)
	}

	return nil
}

// ListMembersRequestMultiError is an error wrapping multiple validation errors
// returned by ListMembersRequest.ValidateAll() if the designated constraints
// aren't met.
type ListMembersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMembersRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMembersRequestMultiError) AllErrors() []error { return m }

// ListMembersRequestValidationError is the validation error returned by
// ListMembersRequest.Validate if the designated constraints aren't met.
type ListMembersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMembersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMembersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMembersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMembersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMembersRequestValidationError) ErrorName() string {
	return "ListMembersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListMembersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMembersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMembersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMembersRequestValidationError{}

// Validate checks the field values on ListMembersReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListMembersReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMembersReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMembersReplyMultiError, or nil if none found.
func (m *ListMembersReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMembersReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetMembers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListMembersReplyValidationError{
						field:  fmt.Sprintf("Members[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListMembersReplyValidationError{
						field:  fmt.Sprintf("Members[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListMembersReplyValidationError{
					field:  fmt.Sprintf("Members[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetTeam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListMembersReplyValidationError{
					field:  "Team",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListMembersReplyValidationError{
					field:  "Team",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTeam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListMembersReplyValidationError{
				field:  "Team",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListMembersReplyMultiError(errors)
	}

	return nil
}

// ListMembersReplyMultiError is an error wrapping multiple validation errors
// returned by ListMembersReply.ValidateAll() if the designated constraints
// aren't met.
type ListMembersReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMembersReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMembersReplyMultiError) AllErrors() []error { return m }

// ListMembersReplyValidationError is the validation error returned by
// ListMembersReply.Validate if the designated constraints aren't met.
type ListMembersReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMembersReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMembersReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMembersReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMembersReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMembersReplyValidationError) ErrorName() string { return "ListMembersReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListMembersReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMembersReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMembersReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMembersReplyValidationError{}

// Validate checks the field values on AddMembersRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddMembersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddMembersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddMembersRequestMultiError, or nil if none found.
func (m *AddMembersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddMembersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if _, ok := _AddMembersRequest_IdType_NotInLookup[m.GetIdType()]; ok {
		err := AddMembersRequestValidationError{
			field:  "IdType",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := IDType_Enum_name[int32(m.GetIdType())]; !ok {
		err := AddMembersRequestValidationError{
			field:  "IdType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Role

	if len(errors) > 0 {
		return AddMembersRequestMultiError(errors)
	}

	return nil
}

// AddMembersRequestMultiError is an error wrapping multiple validation errors
// returned by AddMembersRequest.ValidateAll() if the designated constraints
// aren't met.
type AddMembersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddMembersRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddMembersRequestMultiError) AllErrors() []error { return m }

// AddMembersRequestValidationError is the validation error returned by
// AddMembersRequest.Validate if the designated constraints aren't met.
type AddMembersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddMembersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddMembersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddMembersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddMembersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddMembersRequestValidationError) ErrorName() string {
	return "AddMembersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddMembersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddMembersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddMembersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddMembersRequestValidationError{}

var _AddMembersRequest_IdType_NotInLookup = map[IDType_Enum]struct{}{
	0: {},
}

// Validate checks the field values on DeleteMembersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteMembersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteMembersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteMembersRequestMultiError, or nil if none found.
func (m *DeleteMembersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteMembersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return DeleteMembersRequestMultiError(errors)
	}

	return nil
}

// DeleteMembersRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteMembersRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteMembersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteMembersRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteMembersRequestMultiError) AllErrors() []error { return m }

// DeleteMembersRequestValidationError is the validation error returned by
// DeleteMembersRequest.Validate if the designated constraints aren't met.
type DeleteMembersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteMembersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteMembersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteMembersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteMembersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteMembersRequestValidationError) ErrorName() string {
	return "DeleteMembersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteMembersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteMembersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteMembersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteMembersRequestValidationError{}

// Validate checks the field values on SetMembersRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetMembersRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetMembersRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetMembersRoleRequestMultiError, or nil if none found.
func (m *SetMembersRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetMembersRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Role

	if len(errors) > 0 {
		return SetMembersRoleRequestMultiError(errors)
	}

	return nil
}

// SetMembersRoleRequestMultiError is an error wrapping multiple validation
// errors returned by SetMembersRoleRequest.ValidateAll() if the designated
// constraints aren't met.
type SetMembersRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetMembersRoleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetMembersRoleRequestMultiError) AllErrors() []error { return m }

// SetMembersRoleRequestValidationError is the validation error returned by
// SetMembersRoleRequest.Validate if the designated constraints aren't met.
type SetMembersRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetMembersRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetMembersRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetMembersRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetMembersRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetMembersRoleRequestValidationError) ErrorName() string {
	return "SetMembersRoleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SetMembersRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetMembersRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetMembersRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetMembersRoleRequestValidationError{}
