{{- if .Values.image.migrateCommand -}}
apiVersion: batch/v1
kind: Job
metadata:
  name: "{{ include "appx.fullname" . }}-migration"
  annotations:
    helm.sh/hook-weight: "1"
    helm.sh/hook: "pre-install, pre-upgrade"
    #helm.sh/hook-delete-policy: "before-hook-creation,hook-succeeded"
    helm.sh/hook-delete-policy: "before-hook-creation"
spec:
  #ttlSecondsAfterFinished: 3600
  template:
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: {{ .Chart.Name }}
        securityContext:
          {{- toYaml .Values.securityContext | nindent 10 }}
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        command:
          {{- toYaml .Values.image.migrateCommand | nindent 10 }}
        env:
          {{- include "helper.secretDBToEnv" . | nindent 10 }}
      restartPolicy: Never
  backoffLimit: 3
{{- end }}
