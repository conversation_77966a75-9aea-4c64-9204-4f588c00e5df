package ruleng_test

import (
	"context"
	"testing"

	"iam/internal/biz"
	"iam/internal/biz/ruleng"

	"github.com/stretchr/testify/assert"
)

func TestEval(t *testing.T) {
	cases := []struct {
		name  string
		rule  string
		env   map[string]any
		exp   string
		error bool
	}{
		{
			name: "empty rule",
			rule: "",
			env: map[string]any{
				"user": &biz.User{ID: 1, OrgID: 1, Province: "北京"},
			},
			exp:   "",
			error: false,
		},
		{
			name: "bad rule",
			rule: "xx",
			env: map[string]any{
				"user": &biz.User{ID: 1, OrgID: 1, Province: "北京"},
			},
			exp:   "",
			error: true,
		},
		{
			name: "bad rule does not return a string",
			rule: "user.ID",
			env: map[string]any{
				"user": &biz.User{ID: 1, OrgID: 1, Province: "北京"},
			},
			exp:   "",
			error: true,
		},
		{
			name: "bad rule due to case-sensitive",
			rule: "user.province",
			env: map[string]any{
				"user": &biz.User{ID: 1, OrgID: 1, Province: "北京"},
			},
			exp:   "",
			error: true,
		},
		{
			name: "test toUid",
			rule: `user.OrgID | toUid()`,
			env: map[string]any{
				"user": &biz.User{ID: 1, OrgID: 1, Province: "北京"},
			},
			exp:   "aaaaaaaaaab",
			error: false,
		},
		{
			name: "routing according to OrgID",
			rule: "user.OrgID | string()",
			env: map[string]any{
				"user": &biz.User{ID: 1, OrgID: 1, Province: "北京"},
			},
			exp:   "1",
			error: false,
		},
		{
			name: "routing according to OrgUid",
			rule: `let tail = split(user.OrgID | toUid(), "") | last(); tail < "n" ? "100" : "200"`,
			env: map[string]any{
				"user": &biz.User{ID: 1, OrgID: 1, Province: "北京"},
			},
			exp:   "100",
			error: false,
		},
		{
			name: "routing according to Province",
			rule: `user.Province in ["北京", "上海"] ? "100" : "200"`,
			env: map[string]any{
				"user": &biz.User{ID: 1, OrgID: 1, Province: "北京"},
			},
			exp:   "100",
			error: false,
		},
		{
			name: "routing according to Province 2",
			rule: `user.Province in ["北京", "上海"] ? "100" : "200"`,
			env: map[string]any{
				"user": &biz.User{ID: 1, OrgID: 1, Province: "河北"},
			},
			exp:   "200",
			error: false,
		},
		{
			name: "complex routing use OrgID",
			rule: "'sys.route.blue' in user.Tags || (user.OrgID | toUid() in ['aaaaaaaaaab']) ? 'blue' : ''",
			env: map[string]any{
				"user": &biz.User{ID: 1, OrgID: 1, Province: "北京"},
			},
			exp:   "blue",
			error: false,
		},
		{
			name: "complex routing use user tags",
			rule: "'sys.route.blue' in user.Tags || (user.OrgID | toUid() in ['aaaaaaaaaab']) ? 'blue' : ''",
			env: map[string]any{
				"user": &biz.User{ID: 1, OrgID: 0, Province: "北京", Tags: []string{"sys.route.blue"}},
			},
			exp:   "blue",
			error: false,
		},
		{
			name: "complex routing no match",
			rule: "'sys.route.blue' in user.Tags || (user.OrgID | toUid() in ['aaaaaaaaaab']) ? 'blue' : ''",
			env: map[string]any{
				"user": &biz.User{ID: 1, OrgID: 0, Province: "北京"},
			},
			exp:   "",
			error: false,
		},
	}

	name := "traffic"
	ruleng.Register(name)
	for _, c := range cases {
		ctx := context.Background()
		r, err := ruleng.Eval[string](ctx, name, c.rule, c.env)
		assert.Equal(t, c.error, err != nil, c.name)
		assert.Equal(t, c.exp, r, c.name)
	}
}
