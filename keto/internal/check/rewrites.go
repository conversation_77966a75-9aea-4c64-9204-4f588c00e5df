// Copyright © 2023 Ory Corp
// SPDX-License-Identifier: Apache-2.0

package check

import (
	"context"
	"fmt"
	"sync/atomic"

	"github.com/pkg/errors"

	"github.com/ory/keto/internal/check/checkgroup"
	"github.com/ory/keto/internal/namespace/ast"
	"github.com/ory/keto/internal/relationtuple"
	"github.com/ory/keto/internal/x"
	"github.com/ory/keto/ketoapi"
)

func checkNotImplemented(_ context.Context, resultCh chan<- checkgroup.Result) {
	resultCh <- checkgroup.Result{Err: errors.WithStack(errors.New("not implemented"))}
}

func toTreeNodeType(op ast.Operator) ketoapi.TreeNodeType {
	switch op {
	case ast.OperatorOr:
		return ketoapi.TreeNodeUnion
	case ast.OperatorAnd:
		return ketoapi.TreeNodeIntersection
	default:
		return ketoapi.TreeNodeUnion
	}
}

func (e *Engine) checkSubjectSetRewrite(
	ctx context.Context,
	tuple *relationTuple,
	rewrite *ast.SubjectSetRewrite,
	restDepth int,
) checkgroup.CheckFunc {
	if restDepth <= 0 {
		e.d.Logger().Debug("reached max-depth, therefore this query will not be further expanded")
		return checkgroup.UnknownMemberFunc
	}

	e.d.Logger().
		WithField("depth", restDepth).
		WithField("request", e.formatWithName(ctx, tuple)).
		Trace("check subject-set rewrite")

	var (
		op      binaryOperator
		checks  []checkgroup.CheckFunc
		handled = make(map[int]struct{})
	)
	switch rewrite.Operation {
	case ast.OperatorOr:
		op = or
	case ast.OperatorAnd:
		op = and
	default:
		return checkNotImplemented
	}

	// Shortcut for ORs of ComputedSubjectSets
	if rewrite.Operation == ast.OperatorOr {
		var computedSubjectSets []string
		for i, child := range rewrite.Children {
			// skip faked relations
			if child.IsFaked() {
				continue
			}
			switch c := child.(type) {
			case *ast.SubjectEqualsObject:
				if sub, ok := tuple.Subject.(*relationtuple.SubjectSet); ok &&
					tuple.Namespace == sub.Namespace && tuple.Object == sub.Object {
					return func(ctx context.Context, resultCh chan<- checkgroup.Result) {
						resultCh <- checkgroup.Result{Membership: checkgroup.IsMember}
					}
				}
			case *ast.ComputedSubjectSet:
				if c.Object != "" {
					// handle implicit global policy checks in the common for path
					continue
				}
				handled[i] = struct{}{}
				computedSubjectSets = append(computedSubjectSets, c.Relation)
			}
		}
		if len(computedSubjectSets) > 0 {
			checks = append(checks, func(ctx context.Context, resultCh chan<- checkgroup.Result) {
				g := checkgroup.New(ctx)
				if e.trySysCache(ctx, g, tuple, restDepth) {
					resultCh <- g.Result()
					return
				}
				e.printRoleQuery(ctx, tuple, restDepth)
				res, err := e.d.Traverser().TraverseSubjectSetRewrite(ctx, tuple, computedSubjectSets)
				addCounter(ctx, len(res), 1)
				if err != nil {
					resultCh <- checkgroup.Result{Err: errors.WithStack(err)}
					return
				}
				defer func() { resultCh <- g.Result() }()
				for _, result := range res {
					if result.Found {
						setRestDepth(ctx, restDepth)
						g.SetIsMember()
						// e.addBriefCache(ctx, tuple, checkgroup.IsMember)
						return
					}
				}
				// If not, we must go another hop:
				var args []ast.Arg
				if w := argsFromCtx(ctx); w != nil {
					args = w.Args
				}
				for _, result := range res {
					g.Add(e.checkIsAllowed(newCtxWithArgs(ctx, &ArgsWrapper{Args: args}),
						result.To, restDepth-1, true))
				}
			})
		}
	}

	for i, child := range rewrite.Children {
		if _, found := handled[i]; found {
			continue
		}

		switch c := child.(type) {

		case *ast.TupleToSubjectSet:
			checks = append(checks, checkgroup.WithEdge(checkgroup.Edge{
				Tuple: *tuple,
				Type:  ketoapi.TreeNodeTupleToSubjectSet,
			}, e.checkTupleToSubjectSet(ctx, tuple, c, restDepth)))

		case *ast.ComputedSubjectSet:
			tpp := tuple
			if c.Object != "" {
				u := e.astConstNames[c.Object]
				if tuple.Namespace == c.Namespace && tuple.Object == u {
					// avoid recursive checks
					continue
				}

				tp := *tuple
				tp.Namespace = c.Namespace
				tp.Object = u
				tpp = &tp
			}
			checks = append(checks, checkgroup.WithEdge(checkgroup.Edge{
				Tuple: *tpp,
				Type:  ketoapi.TreeNodeComputedSubjectSet,
			}, e.checkComputedSubjectSet(ctx, tpp, c, restDepth)))

		case *ast.SubjectSetRewrite:
			checks = append(checks, checkgroup.WithEdge(checkgroup.Edge{
				Tuple: *tuple,
				Type:  toTreeNodeType(c.Operation),
			}, e.checkSubjectSetRewrite(ctx, tuple, c, restDepth-1)))

		case *ast.InvertResult:
			checks = append(checks, checkgroup.WithEdge(checkgroup.Edge{
				Tuple: *tuple,
				Type:  ketoapi.TreeNodeNot,
			}, e.checkInverted(ctx, tuple, c, restDepth)))
		case *ast.SubjectEqualsObject:
			res := checkgroup.Result{Membership: checkgroup.NotMember}
			if sub, ok := tuple.Subject.(*relationtuple.SubjectSet); ok &&
				tuple.Namespace == sub.Namespace && tuple.Object == sub.Object {
				res.Membership = checkgroup.IsMember
			}
			checks = append(checks, func(ctx context.Context, resultCh chan<- checkgroup.Result) {
				resultCh <- res
			})

		default:
			return checkNotImplemented
		}
	}

	return func(ctx context.Context, resultCh chan<- checkgroup.Result) {
		resultCh <- op(ctx, checks)
		setRestDepth(ctx, restDepth)
	}
}

func (e *Engine) checkInverted(
	ctx context.Context,
	tuple *relationTuple,
	inverted *ast.InvertResult,
	restDepth int,
) checkgroup.CheckFunc {
	if restDepth < 0 {
		e.d.Logger().Debug("reached max-depth, therefore this query will not be further expanded")
		return checkgroup.UnknownMemberFunc
	}

	e.d.Logger().
		WithField("depth", restDepth).
		WithField("request", e.formatWithName(ctx, tuple)).
		Trace("invert check")

	var check checkgroup.CheckFunc

	switch c := inverted.Child.(type) {

	case *ast.TupleToSubjectSet:
		check = checkgroup.WithEdge(checkgroup.Edge{
			Tuple: *tuple,
			Type:  ketoapi.TreeNodeTupleToSubjectSet,
		}, e.checkTupleToSubjectSet(ctx, tuple, c, restDepth))

	case *ast.ComputedSubjectSet:
		tpp := tuple
		if c.Object != "" {
			u := e.astConstNames[c.Object]
			if tuple.Namespace == c.Namespace && tuple.Object == u {
				// avoid recursive checks
				return func(ctx context.Context, resultCh chan<- checkgroup.Result) {
					resultCh <- checkgroup.Result{Membership: checkgroup.NotMember}
				}
			}

			tp := *tuple
			tp.Namespace = c.Namespace
			tp.Object = u
			tpp = &tp
		}
		check = checkgroup.WithEdge(checkgroup.Edge{
			Tuple: *tpp,
			Type:  ketoapi.TreeNodeComputedSubjectSet,
		}, e.checkComputedSubjectSet(ctx, tpp, c, restDepth))

	case *ast.SubjectSetRewrite:
		check = checkgroup.WithEdge(checkgroup.Edge{
			Tuple: *tuple,
			Type:  toTreeNodeType(c.Operation),
		}, e.checkSubjectSetRewrite(ctx, tuple, c, restDepth))

	case *ast.InvertResult:
		check = checkgroup.WithEdge(checkgroup.Edge{
			Tuple: *tuple,
			Type:  ketoapi.TreeNodeNot,
		}, e.checkInverted(ctx, tuple, c, restDepth))
	case *ast.SubjectEqualsObject:
		res := checkgroup.Result{Membership: checkgroup.NotMember}
		if sub, ok := tuple.Subject.(*relationtuple.SubjectSet); ok &&
			tuple.Namespace == sub.Namespace && tuple.Object == sub.Object {
			res.Membership = checkgroup.IsMember
		}
		check = func(ctx context.Context, resultCh chan<- checkgroup.Result) {
			resultCh <- res
		}
	default:
		return checkNotImplemented
	}

	return func(ctx context.Context, resultCh chan<- checkgroup.Result) {
		innerCh := make(chan checkgroup.Result)
		go check(ctx, innerCh)
		select {
		case result := <-innerCh:
			// invert result here
			switch result.Membership {
			case checkgroup.IsMember:
				result.Membership = checkgroup.NotMember
			case checkgroup.NotMember:
				result.Membership = checkgroup.IsMember
			}
			resultCh <- result
		case <-ctx.Done():
			resultCh <- checkgroup.Result{Err: errors.WithStack(ctx.Err())}
		}
		setRestDepth(ctx, restDepth)
	}
}

// checkComputedSubjectSet rewrites the relation tuple to use the subject-set relation
// instead of the relation from the tuple.
//
// A relation tuple n:obj#original_rel@user is rewritten to
// n:obj#subject-set@user, where the 'subject-set' relation is taken from the
// subjectSet.Relation.
func (e *Engine) checkComputedSubjectSet(
	ctx context.Context,
	r *relationTuple,
	subjectSet *ast.ComputedSubjectSet,
	restDepth int,
) checkgroup.CheckFunc {
	if restDepth < 0 {
		e.d.Logger().Debug("reached max-depth, therefore this query will not be further expanded")
		return checkgroup.UnknownMemberFunc
	}

	e.d.Logger().
		WithField("depth", restDepth).
		WithField("request", e.formatWithName(ctx, r)).
		WithField("computed subjectSet relation", subjectSet.Relation).
		Trace("check computed subjectSet")

	return e.checkIsAllowed(
		wrapArgs(ctx, subjectSet.Args),
		&relationTuple{
			Namespace: r.Namespace,
			Object:    r.Object,
			Relation:  subjectSet.Relation,
			Subject:   r.Subject,
		}, restDepth, false)
}

// checkTupleToSubjectSet rewrites the relation tuple to use the subject-set relation.
//
// Given a relation tuple like docs:readme#editor@user, and a tuple-to-subject-set
// rewrite with the relation "parent" and the computed subject-set relation
// "owner", the following checks will be performed:
//
//   - query for all tuples like docs:readme#parent@??? to get a list of subjects
//     that have the parent relation on docs:readme
//
// * For each matching subject, then check if subject#owner@user.
func (e *Engine) checkTupleToSubjectSet(ctx context.Context,
	tuple *relationTuple,
	subjectSet *ast.TupleToSubjectSet,
	restDepth int,
) checkgroup.CheckFunc {
	if restDepth < 0 {
		e.d.Logger().Debug("reached max-depth, therefore this query will not be further expanded")
		return checkgroup.UnknownMemberFunc
	}

	e.d.Logger().
		WithField("depth", restDepth).
		WithField("request", e.formatWithName(ctx, tuple)).
		WithField("tuple to subject-set relation", subjectSet.Relation).
		WithField("tuple to subject-set computed", subjectSet.ComputedSubjectSetRelation).
		Trace("check tuple to subjectSet")

	return func(ctx context.Context, resultCh chan<- checkgroup.Result) {
		var (
			prevPage, nextPage string
			tuples             []*relationTuple
			err                error
		)
		args := inferArgs(ctx, subjectSet.Args)
		g := checkgroup.New(newCtxWithArgs(ctx, &ArgsWrapper{Args: args}))
		for nextPage = "x"; nextPage != "" && !g.Done(); prevPage = nextPage {
			{
				r := *tuple
				r.Relation = subjectSet.Relation
				r.Subject = nil
				e.printRoleQuery(ctx, &r, restDepth)
			}
			tuples, nextPage, err = e.d.RelationTupleManager().GetRelationTuples(
				ctx,
				&query{
					Namespace: &tuple.Namespace,
					Object:    &tuple.Object,
					Relation:  &subjectSet.Relation,
				},
				x.WithToken(prevPage))
			addCounter(ctx, len(tuples), 1)
			if err != nil {
				g.Add(checkgroup.ErrorFunc(err))
				return
			}

			for _, t := range tuples {
				if subSet, ok := t.Subject.(*relationtuple.SubjectSet); ok {
					g.Add(e.checkIsAllowed(
						newCtxWithArgs(ctx, &ArgsWrapper{Args: args}),
						&relationTuple{
							Namespace: subSet.Namespace,
							Object:    subSet.Object,
							Relation:  subjectSet.ComputedSubjectSetRelation,
							Subject:   tuple.Subject,
						}, restDepth-1, false))

				}
			}
		}
		resultCh <- g.Result()
		setRestDepth(ctx, restDepth)
	}
}

type argsCtxKey struct{}

func newCtxWithArgs(ctx context.Context, args *ArgsWrapper) context.Context {
	return context.WithValue(ctx, argsCtxKey{}, args)
}

func argsFromCtx(ctx context.Context) *ArgsWrapper {
	args, _ := ctx.Value(argsCtxKey{}).(*ArgsWrapper)
	return args
}

type ArgsWrapper struct {
	Args    []ast.Arg
	Mapping map[string]ast.Arg
}

func inferArgs(ctx context.Context, args []ast.Arg) []ast.Arg {
	w := argsFromCtx(ctx)
	if w == nil {
		return args
	}

	// replace named-args with real values
	a := append([]ast.Arg{}, args...)
	for i, p := range a {
		if _, ok := p.(ast.NamedArg); ok {
			a[i] = w.Mapping[p.Value(ctx)]
		}
	}
	return a
}

func wrapArgs(ctx context.Context, args []ast.Arg) context.Context {
	args = inferArgs(ctx, args)
	return newCtxWithArgs(ctx, &ArgsWrapper{Args: args})
}

type Counter struct {
	Rows      int64
	Queries   int64
	RestDepth int64
}

func (o *Counter) String() string {
	return fmt.Sprintf("queries(%v); rows(%v)", atomic.LoadInt64(&o.Queries), atomic.LoadInt64(&o.Rows))
}

func newCtxWithCounter(ctx context.Context) context.Context {
	return context.WithValue(ctx, Counter{}, &Counter{RestDepth: 100})
}

func counterFromCtx(ctx context.Context) *Counter {
	v := ctx.Value(Counter{})
	c, _ := v.(*Counter)
	return c
}

func setRestDepth(ctx context.Context, d int) {
	d64 := int64(d)
	c := counterFromCtx(ctx)
	for {
		v := atomic.LoadInt64(&c.RestDepth)
		if v <= d64 {
			return
		}
		if atomic.CompareAndSwapInt64(&c.RestDepth, v, d64) {
			return
		}
	}
}

func addCounter(ctx context.Context, rows, queries int) {
	c := counterFromCtx(ctx)
	atomic.AddInt64(&c.Rows, int64(rows))
	atomic.AddInt64(&c.Queries, int64(queries))
}
