import os
import json
import shutil
import zipfile
import sys

sys.path.append('.')
from customer.common import tools

mac_file = ['.DS_Store', '__MACOSX']
mar_status_map = {
    0: 'no',
    1: 'yes'
}


def run(input_dir):
    try:
        file_counter = 0
        # unzip file
        unzip_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_unzip')
        result_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_result')
        if os.path.exists(unzip_dir):
            shutil.rmtree(unzip_dir)
        with zipfile.ZipFile(input_dir, 'r') as zip_ref:
            zip_ref.extractall(unzip_dir)

        try:
            os.mkdir(result_dir)
        except FileExistsError:
            shutil.rmtree(result_dir)
            os.mkdir(result_dir)

        for ds in os.listdir(unzip_dir):
            if ds not in mac_file:
                # ds_zuzonqzhqjapglwpmmrf
                ds_dir = os.path.join(unzip_dir, ds)
                for num_folder in os.listdir(ds_dir):
                    if num_folder not in mac_file:
                        # 2311067344
                        num_folder_dir = os.path.join(ds_dir, num_folder)
                        for fs in os.listdir(num_folder_dir):
                            if fs not in mac_file:
                                # fs_try_keboda_20231101
                                fs_dir = os.path.join(num_folder_dir, fs)
                                for batch in os.listdir(fs_dir):
                                    if batch not in mac_file:
                                        # batch0
                                        label_dir = os.path.join(fs_dir, batch, 'label')
                                        for json_file in os.listdir(label_dir):
                                            if json_file not in mac_file:
                                                # load json file
                                                json_dir = os.path.join(label_dir, json_file)
                                                json_data = json.load(open(json_dir))

                                                result_dict = {
                                                    'image_key': json_data['label_meta']['source_info']['file_name'],
                                                    'width': json_data['label_meta']['source_info']['width'],
                                                    'height': json_data['label_meta']['source_info']['height'],
                                                    'abandoned': mar_status_map[json_data['label_meta']['mark_status']],
                                                    'annotations': {
                                                        'freespace': []
                                                    }
                                                }

                                                for label in json_data['labels']:
                                                    annotations_dict = {
                                                        'category_id': int(label['class']),
                                                        'grounded': label['attrs']['Grounded'][0],
                                                    }

                                                    key_point_list = []
                                                    for data in label['annotation']['data']:
                                                        key_point_list.append([data['x'], data['y']])

                                                    annotations_dict['key_points'] = key_point_list

                                                    result_dict['annotations']['freespace'].append(annotations_dict)

                                                result_dict['fisheye_category_id'] = {
                                                    "Curb": 1,
                                                    "Wall": 2,
                                                    "Pillar": 3,
                                                    "Veh_head": 4,
                                                    "Veh_tail": 5,
                                                    "Veh_side": 6,
                                                    "Vehicles": 7,
                                                    "Pedestrian": 8,
                                                    "Animal": 9,
                                                    "Tricycle": 10,
                                                    "Bicycle": 11,
                                                    "Railing": 12,
                                                    "Cone": 13,
                                                    "Stand": 14,
                                                    "Barrier_on": 15,
                                                    "Barrier_off": 16,
                                                    "Stop": 17,
                                                    "Speed_bump": 18,
                                                    "Pole": 19,
                                                    "Others": 20,
                                                    "Pipe": 21,
                                                    "Hydrant_box": 22,
                                                    "Ignore": 23,
                                                    "Dummy_ped": 24
                                                }

                                                # write as json file
                                                with open(os.path.join(result_dir, json_file), 'w') as f:
                                                    json.dump(result_dict, f,
                                                              indent=4,
                                                              ensure_ascii=False)
                                                file_counter += 1

        trans_result = {
            "code": 0,
            "output": result_dir,
            "err_msg": '成功',
            "summary": {
                '生成文件数': file_counter,
            }
        }

    except Exception as e:
        trans_result = {
            "code": 1,
            "err_msg": e,
        }

    for key in trans_result:
        print(key + ': ' + str(trans_result[key]))

    print(trans_result)
    return trans_result


if __name__ == "__main__":
    args = tools.get_args()
    run(args.input)

    # args_input = '/Users/<USER>/Downloads/maa/MAA-金奈2D视觉感知鱼眼可行驶区域_231106af5db_验收_待处理_JSON标注结果文件_20231113_4286.zip'
    # run(args_input)
