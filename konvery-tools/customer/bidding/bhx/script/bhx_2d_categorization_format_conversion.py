import zipfile
import os
import json
import shutil
import sys

sys.path.append('.')
from customer.common import tools

mac_file = '.DS_Store'

model_pose_map = {
    'front': 1,
    'back': 2,
    'left': 3,
    'right': 4,
    'up': 5,
    'down': 6,
    'uncertain': 7,
    'ignore': 8,
}

model_quality_map = {
    'high': 1,
    'medium': 2,
    'low': 3,
    'ignore': 4,
}

model_type_map = {
    'single_object': 1,
    'scene': 2,
    'object_plane': 3,
    'combined_object': 4,
    'arranged_object': 5,
    'uncertain': 6,
}


def run(input_dir):
    try:
        missing_pose = []
        file_counter = 0
        # unzip file
        root_dir = os.path.join(os.path.dirname(input_dir), 'unzip')
        if os.path.exists(root_dir):
            shutil.rmtree(root_dir)
        with zipfile.ZipFile(input_dir, 'r') as zip_ref:
            zip_ref.extractall(root_dir)

        # create result_folder
        result_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_result')
        try:
            os.mkdir(result_dir)
        except FileExistsError:
            shutil.rmtree(result_dir)
            os.mkdir(result_dir)

        for ds in os.listdir(root_dir):
            if ds != mac_file:
                # ds_f2ucw4bud8wexfvlngmr
                ds_dir = os.path.join(root_dir, ds)
                for num_folder in os.listdir(ds_dir):
                    if num_folder != mac_file:
                        # 2308285088
                        num_folder_dir = os.path.join(ds_dir, num_folder)
                        for result_num in os.listdir(num_folder_dir):
                            if result_num != mac_file:
                                # result8
                                result_num_dir = os.path.join(num_folder_dir, result_num)
                                for batch in os.listdir(result_num_dir):
                                    if batch != mac_file:
                                        # batch46
                                        batch_dir = os.path.join(result_num_dir, batch)
                                        for label in os.listdir(batch_dir):
                                            if label != mac_file:
                                                # label
                                                label_dir = os.path.join(batch_dir, label)
                                                for json_file in os.listdir(label_dir):
                                                    if json_file != mac_file:
                                                        # 107300_annotation.json
                                                        # load json file
                                                        json_dir = os.path.join(label_dir, json_file)
                                                        json_data = json.load(open(json_dir))

                                                        if len(json_data['labels']) != 0:

                                                            # build result dict
                                                            try:
                                                                result_dict = {
                                                                    'model_id': json_file.split('_')[0],
                                                                    'model_quality': {
                                                                        'm_quality': model_quality_map[json_data['labels'][0]['attrs']['quality'][0]],
                                                                        'p_replenish': ''
                                                                    },
                                                                    'model_type': {
                                                                        'm_type': model_type_map[json_data['labels'][0]['class']],
                                                                        'p_replenish': ''
                                                                    },
                                                                    'model_pose': {
                                                                        'm_pose': model_pose_map[json_data['labels'][0]['attrs']['pose'][0]],
                                                                        'p_replenish': ''
                                                                    },
                                                                    'model_description': ''
                                                                }
                                                            except KeyError:
                                                                missing_pose.append(json_dir)

                                                            # if json_data['labels'][0]['class'] not in ['scene', 'arranged_object', 'uncertain']:
                                                            #     result_dict['model_pose'] = {
                                                            #         'm_pose': model_pose_map[json_data['labels'][0]['attrs']['pose'][0]],
                                                            #         'p_replenish': ''
                                                            #     }

                                                            # save the result
                                                            with open(os.path.join(result_dir, json_file), 'w') as f:
                                                                json.dump(result_dict, f)
                                                            file_counter += 1
        trans_result = {
            "code": 0,
            "output": result_dir,
            "err_msg": '成功',
            "summary": {
                '处理组数': file_counter,
                "missing pose": missing_pose,
            }
        }

    except Exception as e:
        trans_result = {
            "code": 1,
            "err_msg": e,
        }

    for key in trans_result:
        print(key + ': ' + str(trans_result[key]))

    print(trans_result)
    return trans_result


if __name__ == "__main__":
    args = tools.get_args()
    # args_input = '/Users/<USER>/Downloads/AGU-阿瓜斯卡连特斯3D挖装运_230915a6652_验收_待处理_JSON标注结果文件_20230919_2339.zip'
    run(args.input)
