// source: gitlab.rp.konvery.work/platform/apis/iam/v1/role.proto
package biz

import (
	"context"
	"time"

	"iam/pkg/keto"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type Role struct {
	ID          int64     `json:"id" gorm:"default:null"`
	Name        string    `json:"name" gorm:"default:null"`
	DisplayName string    `json:"display_name" gorm:"default:null"`
	OrgID       int64     `json:"org_id" gorm:"default:null"`
	CreatedAt   time.Time `json:"created_at" gorm:"default:null"`

	Perms []string `json:"-" gorm:"-"`
}

func (o *Role) GetID() int64     { return o.ID }
func (o *Role) GetUid() string   { return o.Name }
func (o *Role) UidField() string { return Role_Name }

const RoleTableName = "roles"

var (
	role_             = field.RegObject(&Role{})
	RoleUpdatableFlds = field.NewModel(RoleTableName, role_)

	Role_Name        = field.Sname(&role_.Name)
	Role_DisplayName = field.Sname(&role_.DisplayName)
	Role_OrgID       = field.Sname(&role_.OrgID)
)

type RoleListFilter struct {
	Names       []string
	OrgID       int64
	NamePattern string
}

func (o *RoleListFilter) Apply(tx Tx) Tx {
	if o == nil {
		return tx
	}
	tbl := RoleTableName + "."
	tx = repo.ApplyFieldFilter(tx, tbl+Role_Name, o.Names)
	tx = repo.ApplyFieldFilter(tx, tbl+Role_OrgID, o.OrgID)
	tx = repo.ApplyPatternFilter(tx, tbl+Role_Name, o.NamePattern)
	return tx
}

type RolesRepo interface {
	repo.GenericRepo[Role]

	GetFeperm(context.Context, string) (Feperms, error)
	SetFeperm(context.Context, string, Feperms) error
}

type RolesBiz struct {
	repo RolesRepo
	log  *log.Helper
}

func NewRolesBiz(repo RolesRepo, logger log.Logger) *RolesBiz {
	return &RolesBiz{repo: repo, log: log.NewHelper(logger)}
}

func (o *RolesBiz) Create(ctx context.Context, p *Role) (role *Role, err error) {
	o.log.Debug(ctx, "CreateRole", "param", p)
	err = o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		role, err = o.repo.Create(ctx, p)
		if err != nil {
			return err
		}
		parent := ""
		if p.OrgID != 0 {
			parent = kid.StringID(p.OrgID)
		}
		return keto.DefaultAccessMgr().CreateRole(ctx, p.Name, parent, p.Perms)
	})
	if err != nil {
		return nil, err
	}
	return
}

func (o *RolesBiz) Update(ctx context.Context, p *Role, fldMask *FieldMask) (*Role, error) {
	o.log.Debug(ctx, "UpdateRole", "param", p)
	return o.repo.Update(ctx, p, fldMask)
}

func (o *RolesBiz) GetByID(ctx context.Context, id int64) (*Role, error) {
	return o.repo.GetByID(ctx, id)
}

func (o *RolesBiz) GetByUid(ctx context.Context, uid string) (*Role, error) {
	return o.repo.GetByID(ctx, kid.ParseID(uid))
}

// func (o *RolesBiz) DeleteByID(ctx context.Context, id int64) error {
// 	o.log.WithContext(ctx).Infof("DeleteByIDRole: id=%v", id)
// 	return o.repo.DeleteByID(ctx, id)
// }

func (o *RolesBiz) DeleteByUid(ctx context.Context, uid string) error {
	o.log.Debug(ctx, "DeleteRoleByUid", "uid", uid)
	return o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		err := o.repo.DeleteByID(ctx, kid.ParseID(uid))
		if err != nil {
			return err
		}
		return keto.DefaultAccessMgr().DeleteRole(ctx, uid)
	})
}

func (o *RolesBiz) List(ctx context.Context, filter *RoleListFilter, pager Pager) (
	roles []*Role, nextPageToken PageToken, err error) {
	o.log.Debug(ctx, "ListRole", "param", filter)
	return o.repo.List(ctx, filter, pager)
}

func (o *RolesBiz) Count(ctx context.Context, filter *RoleListFilter) (int64, error) {
	return o.repo.Count(ctx, filter)
}

func (o *RolesBiz) GetPerms(ctx context.Context, name string) (perms []string, err error) {
	return keto.DefaultAccessMgr().GetRole(ctx, name)
}
