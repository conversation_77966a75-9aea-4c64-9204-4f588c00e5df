// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: anno/v1/job.proto

package anno

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationJobsAssignJob = "/anno.v1.Jobs/AssignJob"
const OperationJobsBatchRevertJob = "/anno.v1.Jobs/BatchRevertJob"
const OperationJobsClaimJob = "/anno.v1.Jobs/ClaimJob"
const OperationJobsGetJob = "/anno.v1.Jobs/GetJob"
const OperationJobsGetJobDraft = "/anno.v1.Jobs/GetJobDraft"
const OperationJobsGetJobLastCommitLog = "/anno.v1.Jobs/GetJobLastCommitLog"
const OperationJobsGetJoblog = "/anno.v1.Jobs/GetJoblog"
const OperationJobsGetRawJoblog = "/anno.v1.Jobs/GetRawJoblog"
const OperationJobsGetSkipAnnotation = "/anno.v1.Jobs/GetSkipAnnotation"
const OperationJobsGiveupJob = "/anno.v1.Jobs/GiveupJob"
const OperationJobsHasHoldingJobs = "/anno.v1.Jobs/HasHoldingJobs"
const OperationJobsListJob = "/anno.v1.Jobs/ListJob"
const OperationJobsReviewJob = "/anno.v1.Jobs/ReviewJob"
const OperationJobsSaveJobDraft = "/anno.v1.Jobs/SaveJobDraft"
const OperationJobsSetRawdataEmbedding = "/anno.v1.Jobs/SetRawdataEmbedding"
const OperationJobsSkipAnnotation = "/anno.v1.Jobs/SkipAnnotation"
const OperationJobsSubmitJob = "/anno.v1.Jobs/SubmitJob"

type JobsHTTPServer interface {
	// AssignJob Assign a job
	AssignJob(context.Context, *AssignJobRequest) (*emptypb.Empty, error)
	// BatchRevertJob Revert multiple jobs to a previous phase.
	BatchRevertJob(context.Context, *BatchRevertJobRequest) (*BatchRevertJobReply, error)
	// ClaimJob Claim a job
	ClaimJob(context.Context, *ClaimJobRequest) (*ClaimJobResponse, error)
	GetJob(context.Context, *GetJobRequest) (*Job, error)
	// GetJobDraft Get draft annos/comments saved in the server.
	GetJobDraft(context.Context, *GetJobDraftRequest) (*GetJobDraftReply, error)
	// GetJobLastCommitLog get the last commit joblog of a phase
	GetJobLastCommitLog(context.Context, *GetJobLastCommitLogRequest) (*GetJobLastCommitLogReply, error)
	GetJoblog(context.Context, *GetJoblogRequest) (*GetJoblogReply, error)
	GetRawJoblog(context.Context, *GetJoblogRequest) (*GetRawJoblogReply, error)
	GetSkipAnnotation(context.Context, *GetSkipAnnotationRequest) (*GetSkipAnnotationReply, error)
	// GiveupJob giveup a job
	GiveupJob(context.Context, *GiveupJobRequest) (*emptypb.Empty, error)
	// HasHoldingJobs check if there is any holding jobs for users/orgs
	HasHoldingJobs(context.Context, *HasHoldingJobsRequest) (*HasHoldingJobsReply, error)
	ListJob(context.Context, *ListJobRequest) (*ListJobReply, error)
	// ReviewJob Review job annotations; used in 2nd phase onwards.
	ReviewJob(context.Context, *ReviewJobRequest) (*emptypb.Empty, error)
	// SaveJobDraft Save draft annos/comments in the server.
	SaveJobDraft(context.Context, *SaveJobDraftRequest) (*SaveJobDraftReply, error)
	// SetRawdataEmbedding Set rawdata embedding.
	SetRawdataEmbedding(context.Context, *SetRawdataEmbeddingRequest) (*emptypb.Empty, error)
	SkipAnnotation(context.Context, *SkipAnnotationRequest) (*SkipAnnotationReply, error)
	// SubmitJob Submit job annotations; used in the 1st phase only.
	SubmitJob(context.Context, *SubmitJobRequest) (*emptypb.Empty, error)
}

func RegisterJobsHTTPServer(s *http.Server, srv JobsHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/jobs/{uid}", _Jobs_GetJob0_HTTP_Handler(srv))
	r.GET("/v1/jobs", _Jobs_ListJob0_HTTP_Handler(srv))
	r.GET("/v1/jobs/{uid}/log", _Jobs_GetJoblog0_HTTP_Handler(srv))
	r.GET("/v1/jobs/{uid}/rawlog", _Jobs_GetRawJoblog0_HTTP_Handler(srv))
	r.POST("/v1/jobs/claim", _Jobs_ClaimJob0_HTTP_Handler(srv))
	r.POST("/v1/jobs/{uid}/assign", _Jobs_AssignJob0_HTTP_Handler(srv))
	r.POST("/v1/jobs/{uid}/giveup", _Jobs_GiveupJob0_HTTP_Handler(srv))
	r.POST("/v1/jobs/{uid}/submit", _Jobs_SubmitJob0_HTTP_Handler(srv))
	r.POST("/v1/jobs/{uid}/review", _Jobs_ReviewJob0_HTTP_Handler(srv))
	r.POST("/v1/jobs/revert", _Jobs_BatchRevertJob0_HTTP_Handler(srv))
	r.PUT("/v1/jobs/{uid}/elems/{elem_idx}/rawdatas/{rawdata_idx}/embedding", _Jobs_SetRawdataEmbedding0_HTTP_Handler(srv))
	r.PUT("/v1/jobs/{uid}/draft", _Jobs_SaveJobDraft0_HTTP_Handler(srv))
	r.GET("/v1/jobs/{uid}/draft", _Jobs_GetJobDraft0_HTTP_Handler(srv))
	r.GET("/v1/jobs/{uid}/phases/{phase}/last-commit-log", _Jobs_GetJobLastCommitLog0_HTTP_Handler(srv))
	r.GET("/v1/jobs/-/holding", _Jobs_HasHoldingJobs0_HTTP_Handler(srv))
	r.POST("/v1/jobs/{uid}/skip-annotation", _Jobs_SkipAnnotation0_HTTP_Handler(srv))
	r.GET("/v1/jobs/{uid}/get-skip-annotation", _Jobs_GetSkipAnnotation0_HTTP_Handler(srv))
}

func _Jobs_GetJob0_HTTP_Handler(srv JobsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetJobRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobsGetJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetJob(ctx, req.(*GetJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Job)
		return ctx.Result(200, reply)
	}
}

func _Jobs_ListJob0_HTTP_Handler(srv JobsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListJobRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobsListJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListJob(ctx, req.(*ListJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListJobReply)
		return ctx.Result(200, reply)
	}
}

func _Jobs_GetJoblog0_HTTP_Handler(srv JobsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetJoblogRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobsGetJoblog)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetJoblog(ctx, req.(*GetJoblogRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetJoblogReply)
		return ctx.Result(200, reply)
	}
}

func _Jobs_GetRawJoblog0_HTTP_Handler(srv JobsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetJoblogRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobsGetRawJoblog)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRawJoblog(ctx, req.(*GetJoblogRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRawJoblogReply)
		return ctx.Result(200, reply)
	}
}

func _Jobs_ClaimJob0_HTTP_Handler(srv JobsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ClaimJobRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobsClaimJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ClaimJob(ctx, req.(*ClaimJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ClaimJobResponse)
		return ctx.Result(200, reply)
	}
}

func _Jobs_AssignJob0_HTTP_Handler(srv JobsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AssignJobRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobsAssignJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AssignJob(ctx, req.(*AssignJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Jobs_GiveupJob0_HTTP_Handler(srv JobsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GiveupJobRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobsGiveupJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GiveupJob(ctx, req.(*GiveupJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Jobs_SubmitJob0_HTTP_Handler(srv JobsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SubmitJobRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobsSubmitJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SubmitJob(ctx, req.(*SubmitJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Jobs_ReviewJob0_HTTP_Handler(srv JobsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ReviewJobRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobsReviewJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ReviewJob(ctx, req.(*ReviewJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Jobs_BatchRevertJob0_HTTP_Handler(srv JobsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BatchRevertJobRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobsBatchRevertJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BatchRevertJob(ctx, req.(*BatchRevertJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*BatchRevertJobReply)
		return ctx.Result(200, reply)
	}
}

func _Jobs_SetRawdataEmbedding0_HTTP_Handler(srv JobsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetRawdataEmbeddingRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobsSetRawdataEmbedding)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetRawdataEmbedding(ctx, req.(*SetRawdataEmbeddingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Jobs_SaveJobDraft0_HTTP_Handler(srv JobsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SaveJobDraftRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobsSaveJobDraft)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SaveJobDraft(ctx, req.(*SaveJobDraftRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SaveJobDraftReply)
		return ctx.Result(200, reply)
	}
}

func _Jobs_GetJobDraft0_HTTP_Handler(srv JobsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetJobDraftRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobsGetJobDraft)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetJobDraft(ctx, req.(*GetJobDraftRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetJobDraftReply)
		return ctx.Result(200, reply)
	}
}

func _Jobs_GetJobLastCommitLog0_HTTP_Handler(srv JobsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetJobLastCommitLogRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobsGetJobLastCommitLog)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetJobLastCommitLog(ctx, req.(*GetJobLastCommitLogRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetJobLastCommitLogReply)
		return ctx.Result(200, reply)
	}
}

func _Jobs_HasHoldingJobs0_HTTP_Handler(srv JobsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in HasHoldingJobsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobsHasHoldingJobs)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.HasHoldingJobs(ctx, req.(*HasHoldingJobsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*HasHoldingJobsReply)
		return ctx.Result(200, reply)
	}
}

func _Jobs_SkipAnnotation0_HTTP_Handler(srv JobsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SkipAnnotationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobsSkipAnnotation)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SkipAnnotation(ctx, req.(*SkipAnnotationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SkipAnnotationReply)
		return ctx.Result(200, reply)
	}
}

func _Jobs_GetSkipAnnotation0_HTTP_Handler(srv JobsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSkipAnnotationRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobsGetSkipAnnotation)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSkipAnnotation(ctx, req.(*GetSkipAnnotationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetSkipAnnotationReply)
		return ctx.Result(200, reply)
	}
}

type JobsHTTPClient interface {
	AssignJob(ctx context.Context, req *AssignJobRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	BatchRevertJob(ctx context.Context, req *BatchRevertJobRequest, opts ...http.CallOption) (rsp *BatchRevertJobReply, err error)
	ClaimJob(ctx context.Context, req *ClaimJobRequest, opts ...http.CallOption) (rsp *ClaimJobResponse, err error)
	GetJob(ctx context.Context, req *GetJobRequest, opts ...http.CallOption) (rsp *Job, err error)
	GetJobDraft(ctx context.Context, req *GetJobDraftRequest, opts ...http.CallOption) (rsp *GetJobDraftReply, err error)
	GetJobLastCommitLog(ctx context.Context, req *GetJobLastCommitLogRequest, opts ...http.CallOption) (rsp *GetJobLastCommitLogReply, err error)
	GetJoblog(ctx context.Context, req *GetJoblogRequest, opts ...http.CallOption) (rsp *GetJoblogReply, err error)
	GetRawJoblog(ctx context.Context, req *GetJoblogRequest, opts ...http.CallOption) (rsp *GetRawJoblogReply, err error)
	GetSkipAnnotation(ctx context.Context, req *GetSkipAnnotationRequest, opts ...http.CallOption) (rsp *GetSkipAnnotationReply, err error)
	GiveupJob(ctx context.Context, req *GiveupJobRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	HasHoldingJobs(ctx context.Context, req *HasHoldingJobsRequest, opts ...http.CallOption) (rsp *HasHoldingJobsReply, err error)
	ListJob(ctx context.Context, req *ListJobRequest, opts ...http.CallOption) (rsp *ListJobReply, err error)
	ReviewJob(ctx context.Context, req *ReviewJobRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	SaveJobDraft(ctx context.Context, req *SaveJobDraftRequest, opts ...http.CallOption) (rsp *SaveJobDraftReply, err error)
	SetRawdataEmbedding(ctx context.Context, req *SetRawdataEmbeddingRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	SkipAnnotation(ctx context.Context, req *SkipAnnotationRequest, opts ...http.CallOption) (rsp *SkipAnnotationReply, err error)
	SubmitJob(ctx context.Context, req *SubmitJobRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type JobsHTTPClientImpl struct {
	cc *http.Client
}

func NewJobsHTTPClient(client *http.Client) JobsHTTPClient {
	return &JobsHTTPClientImpl{client}
}

func (c *JobsHTTPClientImpl) AssignJob(ctx context.Context, in *AssignJobRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/jobs/{uid}/assign"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobsAssignJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobsHTTPClientImpl) BatchRevertJob(ctx context.Context, in *BatchRevertJobRequest, opts ...http.CallOption) (*BatchRevertJobReply, error) {
	var out BatchRevertJobReply
	pattern := "/v1/jobs/revert"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobsBatchRevertJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobsHTTPClientImpl) ClaimJob(ctx context.Context, in *ClaimJobRequest, opts ...http.CallOption) (*ClaimJobResponse, error) {
	var out ClaimJobResponse
	pattern := "/v1/jobs/claim"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobsClaimJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobsHTTPClientImpl) GetJob(ctx context.Context, in *GetJobRequest, opts ...http.CallOption) (*Job, error) {
	var out Job
	pattern := "/v1/jobs/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobsGetJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobsHTTPClientImpl) GetJobDraft(ctx context.Context, in *GetJobDraftRequest, opts ...http.CallOption) (*GetJobDraftReply, error) {
	var out GetJobDraftReply
	pattern := "/v1/jobs/{uid}/draft"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobsGetJobDraft))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobsHTTPClientImpl) GetJobLastCommitLog(ctx context.Context, in *GetJobLastCommitLogRequest, opts ...http.CallOption) (*GetJobLastCommitLogReply, error) {
	var out GetJobLastCommitLogReply
	pattern := "/v1/jobs/{uid}/phases/{phase}/last-commit-log"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobsGetJobLastCommitLog))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobsHTTPClientImpl) GetJoblog(ctx context.Context, in *GetJoblogRequest, opts ...http.CallOption) (*GetJoblogReply, error) {
	var out GetJoblogReply
	pattern := "/v1/jobs/{uid}/log"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobsGetJoblog))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobsHTTPClientImpl) GetRawJoblog(ctx context.Context, in *GetJoblogRequest, opts ...http.CallOption) (*GetRawJoblogReply, error) {
	var out GetRawJoblogReply
	pattern := "/v1/jobs/{uid}/rawlog"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobsGetRawJoblog))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobsHTTPClientImpl) GetSkipAnnotation(ctx context.Context, in *GetSkipAnnotationRequest, opts ...http.CallOption) (*GetSkipAnnotationReply, error) {
	var out GetSkipAnnotationReply
	pattern := "/v1/jobs/{uid}/get-skip-annotation"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobsGetSkipAnnotation))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobsHTTPClientImpl) GiveupJob(ctx context.Context, in *GiveupJobRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/jobs/{uid}/giveup"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobsGiveupJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobsHTTPClientImpl) HasHoldingJobs(ctx context.Context, in *HasHoldingJobsRequest, opts ...http.CallOption) (*HasHoldingJobsReply, error) {
	var out HasHoldingJobsReply
	pattern := "/v1/jobs/-/holding"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobsHasHoldingJobs))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobsHTTPClientImpl) ListJob(ctx context.Context, in *ListJobRequest, opts ...http.CallOption) (*ListJobReply, error) {
	var out ListJobReply
	pattern := "/v1/jobs"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJobsListJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobsHTTPClientImpl) ReviewJob(ctx context.Context, in *ReviewJobRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/jobs/{uid}/review"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobsReviewJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobsHTTPClientImpl) SaveJobDraft(ctx context.Context, in *SaveJobDraftRequest, opts ...http.CallOption) (*SaveJobDraftReply, error) {
	var out SaveJobDraftReply
	pattern := "/v1/jobs/{uid}/draft"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobsSaveJobDraft))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobsHTTPClientImpl) SetRawdataEmbedding(ctx context.Context, in *SetRawdataEmbeddingRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/jobs/{uid}/elems/{elem_idx}/rawdatas/{rawdata_idx}/embedding"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobsSetRawdataEmbedding))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobsHTTPClientImpl) SkipAnnotation(ctx context.Context, in *SkipAnnotationRequest, opts ...http.CallOption) (*SkipAnnotationReply, error) {
	var out SkipAnnotationReply
	pattern := "/v1/jobs/{uid}/skip-annotation"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobsSkipAnnotation))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobsHTTPClientImpl) SubmitJob(ctx context.Context, in *SubmitJobRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/jobs/{uid}/submit"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobsSubmitJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
