syntax = "proto3";

package iam.v1;

import "google/api/annotations.proto";
//import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";
import "iam/v1/type.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/iam/v1;iam";
option java_multiple_files = true;
option java_package = "iam.v1";

service Teams {
  rpc CreateTeam (CreateTeamRequest) returns (Team) {
    option (google.api.http) = {
      post: "/v1/teams"
      body: "*"
    };
  }

  rpc UpdateTeam (UpdateTeamRequest) returns (Team) {
    option (google.api.http) = {
      patch: "/v1/teams/{team.uid}"
      body: "team"
    };
  }

  rpc DeleteTeam (DeleteTeamRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/teams/{uid}"
    };
  }

  rpc GetTeamsRoot (GetTeamsRootRequest) returns (ListTeamReply) {
    option (google.api.http) = {
      get: "/v1/teams/root"
    };
  }

  rpc GetTeam (GetTeamRequest) returns (Team) {
    option (google.api.http) = {
      get: "/v1/teams/{uid}"
    };
  }

  // rpc GetTeamHierarchy (GetTeamHierarchyRequest) returns (ListTeamReply) {
  //   option (google.api.http) = {
  //     get: "/v1/teams/{uid}/hierarchy"
  //   };
  // }

  // rpc only
  rpc ListTeamByIDs (ListTeamByIDsRequest) returns (ListTeamReply);

  rpc ListTeam (ListTeamRequest) returns (ListTeamReply) {
    option (google.api.http) = {
      get: "/v1/teams"
    };
  }

  // member operations

  rpc ListMembers (ListMembersRequest) returns (ListMembersReply) {
    option (google.api.http) = {
      get: "/v1/teams/{uid}/members"
    };
  }

  // send join-team invitation to mentioned users
  rpc AddMembers (AddMembersRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/teams/{uid}/members"
      body: "*"
    };
  }

  rpc DeleteMembers (DeleteMembersRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/teams/{uid}/members"
    };
  }

  rpc SetMembersRole (SetMembersRoleRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/teams/{uid}/role"
      body: "*"
    };
  }
}

message CreateTeamRequest {
  option (openapi.v3.schema) = {
    required: []
  };

  string uid = 1;
  // mandatory in create-team request
  string name = 2;
  string desc = 3;
  string avatar = 4;

  string province = 5;
  string city = 6;

  // parent team uid
  string parent_uid = 7;
  // mandatory in create-team request
  Team.Type.Enum type = 8 [(validate.rules).enum = {defined_only: true, not_in: [0]}];
  // specify the team owner.
  // supported formats: uid:xxx, phone:+8612345678901 or email:<EMAIL>
  string owner = 9 [(validate.rules).string = {pattern: "^(|uid:\\w+|phone:\\+\\d+|email:[\\w.-]+@[\\w.-]+\\.[\\w.]+)$"}];
                   // allow empty owner in UpdateTeamRequest
}
// message CreateTeamReply {}

message UpdateTeamRequest {
  option (openapi.v3.schema) = {
    required: ["team", "fields"]
  };

  // string uid = 1;
  // // see CreateTeamRequest for valid field names
  // map<string, string> fields = 2;

  CreateTeamRequest team = 1;
  repeated string fields = 2;
}

// message UpdateTeamReply {}

message DeleteTeamRequest {
  string uid = 1;
}

// message DeleteTeamReply {}

message GetTeamsRootRequest {
  option (openapi.v3.schema) = {
    required: ["uids"]
  };

  repeated string uids = 1;
}

message GetTeamRequest {
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
}
// message GetTeamReply {}

// message GetTeamHierarchyRequest {
//   string uid = 1;
//   // how many upper-level teams to return; 0 to return all
//   int32 levels_up = 2;
//   // whether to return team of uid in the result
//   bool include_self = 3;
// }

message ListTeamByIDsRequest {
  repeated int64 ids = 1;
}

message ListTeamRequest {
  int32 page = 1;
  int32 pagesz = 2 [(validate.rules).int32 = {gte:0, lte: 100}];

  // parent team uid; if not specified, only query organizations
  string parent_uid = 3;
  // find teams by name pattern
  string name_pattern = 4;
  repeated string uids = 5;
  Team.Type.Enum team_type = 6;
}

message ListTeamReply {
  option (openapi.v3.schema) = {
    required: ["teams"]
  };

  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated Team teams = 2;
}

message ListMembersRequest {
  int32 page = 1;
  int32 pagesz = 2 [(validate.rules).int32 = {gte:0, lte: 100}];

  // team uid
  string uid = 3;
  // list only members matching the name pattern
  string name_pattern = 4;
  // list only members with the specified role
  string role = 5;
}

message ListMembersReply {
  option (openapi.v3.schema) = {
    required: ["members"]
  };

  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated User members = 2;
  BaseUser team = 3;
}

message AddMembersRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "id_type", "identities", "role"],
  };

  // team uid
  string uid = 1;

  // type of user identities: uid/email/phone/...
  IDType.Enum id_type = 2 [(validate.rules).enum = {defined_only: true, not_in: [0]}];
  // max number of user identities is 100
  repeated string identities = 3;
  // one of owner/manager/member
  string role = 4;
}

message DeleteMembersRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "user_uids"],
  };

  // team uid
  string uid = 1;
  repeated string user_uids = 2;
}

message SetMembersRoleRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "user_uids", "role"],
  };

  // uid of the team
  string uid = 1;
  repeated string user_uids = 2;
  // new role: owner/manager/member
  string role = 3;
}
