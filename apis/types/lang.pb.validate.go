// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: types/lang.proto

package types

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Multilingual with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Multilingual) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Multilingual with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MultilingualMultiError, or
// nil if none found.
func (m *Multilingual) ValidateAll() error {
	return m.validate(true)
}

func (m *Multilingual) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Langs

	if len(errors) > 0 {
		return MultilingualMultiError(errors)
	}

	return nil
}

// MultilingualMultiError is an error wrapping multiple validation errors
// returned by Multilingual.ValidateAll() if the designated constraints aren't met.
type MultilingualMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MultilingualMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MultilingualMultiError) AllErrors() []error { return m }

// MultilingualValidationError is the validation error returned by
// Multilingual.Validate if the designated constraints aren't met.
type MultilingualValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MultilingualValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MultilingualValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MultilingualValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MultilingualValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MultilingualValidationError) ErrorName() string { return "MultilingualValidationError" }

// Error satisfies the builtin error interface
func (e MultilingualValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMultilingual.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MultilingualValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MultilingualValidationError{}

// Validate checks the field values on MultilingualItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MultilingualItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MultilingualItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MultilingualItemMultiError, or nil if none found.
func (m *MultilingualItem) ValidateAll() error {
	return m.validate(true)
}

func (m *MultilingualItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Langs

	if len(errors) > 0 {
		return MultilingualItemMultiError(errors)
	}

	return nil
}

// MultilingualItemMultiError is an error wrapping multiple validation errors
// returned by MultilingualItem.ValidateAll() if the designated constraints
// aren't met.
type MultilingualItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MultilingualItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MultilingualItemMultiError) AllErrors() []error { return m }

// MultilingualItemValidationError is the validation error returned by
// MultilingualItem.Validate if the designated constraints aren't met.
type MultilingualItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MultilingualItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MultilingualItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MultilingualItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MultilingualItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MultilingualItemValidationError) ErrorName() string { return "MultilingualItemValidationError" }

// Error satisfies the builtin error interface
func (e MultilingualItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMultilingualItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MultilingualItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MultilingualItemValidationError{}

// Validate checks the field values on DisplayItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DisplayItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DisplayItem with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DisplayItemMultiError, or
// nil if none found.
func (m *DisplayItem) ValidateAll() error {
	return m.validate(true)
}

func (m *DisplayItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for DisplayName

	if len(errors) > 0 {
		return DisplayItemMultiError(errors)
	}

	return nil
}

// DisplayItemMultiError is an error wrapping multiple validation errors
// returned by DisplayItem.ValidateAll() if the designated constraints aren't met.
type DisplayItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DisplayItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DisplayItemMultiError) AllErrors() []error { return m }

// DisplayItemValidationError is the validation error returned by
// DisplayItem.Validate if the designated constraints aren't met.
type DisplayItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DisplayItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DisplayItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DisplayItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DisplayItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DisplayItemValidationError) ErrorName() string { return "DisplayItemValidationError" }

// Error satisfies the builtin error interface
func (e DisplayItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDisplayItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DisplayItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DisplayItemValidationError{}
