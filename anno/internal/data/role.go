// source: anno/v1/role.proto
package data

import (
	"context"

	"anno/internal/biz"

	"gitlab.rp.konvery.work/platform/pkg/log"

	"gorm.io/gorm"
)

type rolesRepo struct {
	data *Data
	log  *log.Helper
}

func NewRolesRepo(data *Data, logger log.Logger) biz.RolesRepo {
	return &rolesRepo{data: data, log: log.NewHelper(logger)}
}

func (o *rolesRepo) Create(ctx context.Context, p *biz.Role) (*biz.Role, error) {
	return Create(ctx, o.data, p)
}

func (o *rolesRepo) Update(ctx context.Context, p *biz.Role, fldMask *biz.FieldMask) (*biz.Role, error) {
	return Update(ctx, o.data, p, biz.RoleUpdatableFlds, fldMask)
}

func (o *rolesRepo) loadAssociations(ctx context.Context, data *Data, mod *biz.Role) error {
	return nil
}

func (o *rolesRepo) GetByID(ctx context.Context, id int64) (*biz.Role, error) {
	return GetByID[biz.Role](ctx, o.data, id, o.loadAssociations)
}

func (o *rolesRepo) GetByUid(ctx context.Context, uid string) (*biz.Role, error) {
	return GetByUid[biz.Role](ctx, o.data, uid, o.loadAssociations)
}

func (o *rolesRepo) DeleteByID(ctx context.Context, id int64) error {
	return Delete(ctx, o.data, &biz.Role{ID: id})
}

func (o *rolesRepo) DeleteByUid(ctx context.Context, uid string) error {
	return Delete(ctx, o.data, &biz.Role{Name: uid})
}

func (o *rolesRepo) buildListQuery(ctx context.Context, p *biz.RoleListFilter) *gorm.DB {
	q := o.data.WithCtx(ctx)
	if len(p.Uids) > 0 {
		q = q.Where(biz.RoleSfldName+" IN ?", p.Uids)
	}
	if p.NamePattern != "" {
		q = q.Where(biz.RoleSfldName+" LIKE ?", "%"+p.NamePattern+"%")
	}
	return q
}

func (o *rolesRepo) List(ctx context.Context, p *biz.RoleListFilter, pager biz.Pager) ([]*biz.Role, error) {
	datas := []*biz.Role{}
	q := o.buildListQuery(ctx, p).Order("id")
	if len(p.Uids) == 0 {
		q = q.Offset(pager.Offset()).Limit(pager.Pagesz)
	}
	err := q.Find(&datas).Error
	if err != nil {
		return nil, err
	}
	return datas, nil
}

func (o *rolesRepo) Count(ctx context.Context, p *biz.RoleListFilter) (int, error) {
	mod := &biz.Role{}
	var cnt int64
	q := o.buildListQuery(ctx, p)
	err := q.Model(mod).Count(&cnt).Error
	if err != nil {
		return 0, err
	}
	return int(cnt), nil
}
