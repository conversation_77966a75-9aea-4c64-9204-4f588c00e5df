import sys
import os
import shutil
sys.path.append('.')
from customer.common import tools


clothes = {'upperbody', 'lowerbody', 'wholebody', 'sock', 'hat', 'tie', 'glove', 'belt', 'scarf'}
others = {'shoe', 'bag', 'cloth-hanger', 'accessory'}
clothes_parts = {'transparent', 'button', 'zip', 'tag', 'buckle'}
all_labels = clothes.union(others).union(clothes_parts).union({'attribute'})

inner_mapping = {
    "": "",
    "no": 0,
    "yes": 1,
}
close_fitting_mapping = {
    "": "",
    "no": 0,
    "yes": 1,
}


def run(input_dir, output_dir):
    label_dirs = tools.find_dir_by_pre_name(input_dir, 'label')
    for label_dir in label_dirs:
        clip_name = os.path.basename(os.path.dirname(label_dir))
        out_clip_dir = os.path.join(output_dir, clip_name)
        os.mkdir(out_clip_dir)
        out_json_dir = os.path.join(out_clip_dir, 'json')
        os.mkdir(out_json_dir)
        json_files = tools.get_json_files(label_dir)
        for json_file in json_files:
            group_id2instance = dict()
            pseudo_group_id = 0
            json_data = tools.get_json_data(json_file)
            if len(list(filter(lambda label: label['class'] not in all_labels, json_data.get('labels', [])))) > 0:
                raise ValueError(f"{json_file.name} has unknown labels")
            group_id2label_atrrs = {
                label['group_id']: label['attrs'] for label in filter(lambda label: label['class'] == 'attribute', json_data.get('labels', []))
            }
            for obj in filter(lambda label: label['class'] in clothes or label['class'] in others,
                              json_data.get('labels', [])):
                group_id = obj.get('group_id')
                if not group_id:
                    group_id = f'pseudo_{pseudo_group_id}'
                    pseudo_group_id += 1
                if group_id not in group_id2instance:
                    attrs = group_id2label_atrrs.get(group_id, dict())
                    instance = {
                        "label": obj['class'],
                        "attributes": {
                            "inner": inner_mapping[attrs.get('inner', [''])[0]],
                            "close_fitting": close_fitting_mapping[attrs.get('inner', [''])[0]],
                        },
                        "whole": [
                            {
                                "segmentation": [
                                    [[pt['x'], pt['y']] for pt in obj['annotation']['data']]
                                ],
                                "attributes": {
                                    "material": obj['attrs'].get('material', [''])[0]
                                }
                            }
                        ],
                        "parts": []
                    }
                    if 'gaps' in obj['annotation']:
                        for gap in obj['annotation']['gaps']:
                            instance['whole'][0]['segmentation'].append([[pt['x'], pt['y']] for pt in gap])
                    group_id2instance[group_id] = instance
                else:
                    whole = {
                        "segmentation": [
                            [[pt['x'], pt['y']] for pt in obj['annotation']['data']]
                        ],
                        "attributes": {
                            "material": obj['attrs'].get('material', [''])[0]
                        }
                    }
                    if 'gaps' in obj['annotation']:
                        for gap in obj['annotation']['gaps']:
                            whole['segmentation'].append([[pt['x'], pt['y']] for pt in gap])
                    group_id2instance[group_id]['whole'].append(whole)
            for obj in filter(lambda label: label['class'] in clothes_parts, json_data.get('labels', [])):
                group_id = obj.get('group_id')
                if not group_id:
                    raise ValueError(f"cloth part in {json_file.name} has mot whole")
                part = {
                    "label": obj['class'],
                    "segmentation": [[[pt['x'], pt['y']] for pt in obj['annotation']['data']]]
                }
                if 'gaps' in obj['annotation']:
                    for gap in obj['annotation']['gaps']:
                        part['segmentation'].append([[pt['x'], pt['y']] for pt in gap])
                group_id2instance[group_id]['parts'].append(part)

            json_result = {
                "file_name": os.path.basename(json_file).replace('.json', '.jpg'),
                "annotation": {
                    "instances": list(group_id2instance.values())
                }
            }
            tools.write_json_file(json_result, os.path.join(out_json_dir, os.path.basename(json_file)))


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
