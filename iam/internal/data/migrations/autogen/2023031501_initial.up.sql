BEGIN;

-- grant organization biz permissions to a kam
CREATE TABLE bizgrants
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    grantor_id  BIGINT       NOT NULL,
    grantee_id  BIGINT       NOT NULL,
    org_id      BIGINT       NOT NULL,
    biz         VARCHAR(32)  NOT NULL, -- business type, e.g. anno
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_bizgrants_grantee_id_org_id_biz ON bizgrants (grantee_id, org_id, biz);
CREATE INDEX idx_bizgrants_org_id_biz ON bizgrants (org_id, biz) INCLUDE (grantee_id);
CREATE INDEX idx_bizgrants_grantor_id ON bizgrants (grantor_id);

COMMIT;
