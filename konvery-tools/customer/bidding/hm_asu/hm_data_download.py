import json
import sys
import wget
import os

sys.path.append(".")
from customer.common import tools

line_count = 0
file_count = 0
err_files = {}
file_box_dict = {}
current_file = ""
object_count = 0
jpg_count = 0


def download(data, out_dir=None, name=None):
    global jpg_count
    if "camera_synthetic_bev" in data.keys():
        for url in data["camera_synthetic_bev"]:
            wget.download(url["http_path"], out=out_dir)
            jpg_count += 1
    else:
        output_dir = out_dir + os.sep + name
        # camera_dir = out_dir + os.sep + name + os.sep + "camera"
        # fish_eye_dir = out_dir + os.sep + name + os.sep + "fish_eye"
        # tools.check_dir(camera_dir)
        # tools.check_dir(fish_eye_dir)

        tools.check_dir(output_dir)

        # lidar pcd file
        for pcd in data["lidar"]:
            if pcd["name"] == "center_128_lidar_scan_data":
                wget.download(pcd["http_path_pcd_txt"], out=output_dir)
                break

        for img in data["camera_fisheye_mercator"]:
            wget.download(img["http_path"], out=output_dir)


def run(input_dir, output_dir):
    global file_count
    global line_count
    global current_file
    global file_box_dict

    file_count = 0

    tools.check_dir(output_dir)
    input_dir = tools.unzip(input_dir)

    for f in tools.get_json_files(input_dir):

        if f.name.endswith('.json') and not f.name.startswith("."):
            with open(f.path) as json_file:
                data = json.load(json_file)
                download(data, out_dir=output_dir, name=f.name.replace(".json", ""))

    err_title = ""
    err_type_list = []
    return tools.check_error(output_dir, err_files, err_title, jpg_count, object_count, err_type_list, is_zip=False)


"""
    项目： 毫末
    对接平台：云测新平台 
    功能： 下载数据
    输出： json
    输出： 下载的文件
    
    ——自定义目录
      ——segment_000 //序列目录，名称可自定义
        ——config.json // 配置文件，为纯点云标注时为可选文件，文件名为config.json
        ——lidar // 点云文件目录，目录名为lidar
          ——000.pcd // 名称可自定义
          ——001.pcd
        ——camera // 图像目录，目录名为camera,无图像则不含此目录。
          ——front_camera // 多视角摄像头名称，名称可自定义
            ——000.jpg // 通过文件名与点云对应
            ——001.jpg
        ——pre_label // 可选，预导入目录，通过文件名与原始点云文件对应，目录名为pre_label
          ——000.json
          ——001.json    
"""
if __name__ == "__main__":
    args = tools.get_args()
    run(args.input, args.out)
