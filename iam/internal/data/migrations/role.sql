-- mig:up ********** initial

CREATE TABLE roles
(
    id           BIGSERIAL    NOT NULL PRIMARY KEY,
    name         <PERSON><PERSON><PERSON><PERSON>(64)  NOT NULL UNIQUE,
    display_name VARCHAR(64)  NOT NULL DEFAULT '',
    -- avatar       VARCHAR(4096) NOT NULL DEFAULT '',
    -- titles       JSONB        NOT NULL DEFAULT '{}'::JSONB, -- display name
    org_id       BIGINT,
    created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_roles_org_id ON roles (org_id);
CREATE INDEX idx_roles_display_name ON roles ((lower(display_name)));

-- predefined roles
INSERT INTO roles (name, display_name) VALUES ('root', 'root');
INSERT INTO roles (name, display_name) VALUES ('admin', 'admin');
INSERT INTO roles (name, display_name) VALUES ('service', 'service');
INSERT INTO roles (name, display_name) VALUES ('subAdmin', 'sub admin');
INSERT INTO roles (name, display_name) VALUES ('pm', 'Project Manager');
INSERT INTO roles (name, display_name) VALUES ('kam', 'Key Account Manager');
INSERT INTO roles (name, display_name) VALUES ('inspector', 'inspector');
INSERT INTO roles (name, display_name) VALUES ('owner', 'owner');
INSERT INTO roles (name, display_name) VALUES ('manager', 'manager');
INSERT INTO roles (name, display_name) VALUES ('member', 'member');

-- mig:down ********** initial
DROP TABLE roles;

