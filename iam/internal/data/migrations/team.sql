-- mig:up 2022070101 initial
CREATE TABLE teams
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    -- uid         VARCHAR(32)  NOT NULL UNIQUE,
    name        VA<PERSON><PERSON><PERSON>(64)  NOT NULL,
    "desc"      VARCHAR(512) NOT NULL DEFAULT '',
    type        SMALLINT     NOT NULL DEFAULT 0, -- 0-unknown, 1-demander, 2-operator, 3-supplier
    province    VARCHAR(256) NOT NULL DEFAULT '',
    city        VARCHAR(256) NOT NULL DEFAULT '',
    avatar      VARCHAR(512) NOT NULL DEFAULT '',
    -- org_id      BIGINT       NOT NULL DEFAULT 0, -- ID of top-level team
    parent_id   BIGINT       NOT NULL DEFAULT 0, -- ID of upper-level team
    hierarchy   JSONB, -- team hierarchy: [team-level-1, team-level-2]
    status      SMALLINT     NOT NULL DEFAULT 0, -- 0-valid, 1-
    creator_id  BIGINT       NOT NULL DEFAULT 0,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    deleted_at  TIMESTAMPTZ
);
-- CREATE INDEX idx_teams_creator_id ON teams (creator_id) INCLUDE (status, deleted_at);
CREATE INDEX idx_teams_created_at ON teams (created_at) INCLUDE (status, deleted_at);
CREATE INDEX idx_teams_parent_id ON teams (parent_id) INCLUDE (status, deleted_at);
-- CREATE INDEX idx_teams_org_id ON teams (org_id) INCLUDE (status, deleted_at);
-- CREATE INDEX idx_teams_hierarchy ON users USING GIN (hierarchy); --INCLUDE (status, deleted_at);
CREATE INDEX idx_teams_name ON teams ((lower(name))) INCLUDE (status, deleted_at);

CREATE TABLE team_members
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    team_id     BIGINT       NOT NULL DEFAULT 0, -- ID of team
    user_id     BIGINT       NOT NULL DEFAULT 0, -- ID of user
    role        VARCHAR(32)  NOT NULL DEFAULT 'member', -- owner/manager/member
    status      SMALLINT     NOT NULL DEFAULT 0, -- 0-valid, 1-
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_team_members_team_id_user_id ON team_members (team_id, user_id) INCLUDE (role, status);
CREATE INDEX idx_team_members_user_id ON team_members (user_id) INCLUDE (team_id, role, status);

-- mig:down 2022070101 initial
DROP TABLE teams;
DROP TABLE team_members;
