package cron

import (
	"fmt"
	"time"

	"annostat/internal/biz"

	"gitlab.rp.konvery.work/platform/pkg/wf/tracing"
	"go.temporal.io/sdk/log"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

var fooa *Activities

type sumWorkflow struct {
	logger log.Logger
	retry  *temporal.RetryPolicy
}

func newSumWorkflow(ctx workflow.Context) *sumWorkflow {
	return &sumWorkflow{
		logger: workflow.GetLogger(ctx),
		retry: &temporal.RetryPolicy{
			InitialInterval:    time.Second,
			BackoffCoefficient: 2.0,
			MaximumInterval:    time.Second * 100,
			MaximumAttempts:    10,
		},
	}
}

func (o *sumWorkflow) SumProductions(ctx workflow.Context, period biz.TimeRange) (err error) {
	ctx = workflow.WithActivityOptions(ctx, workflow.ActivityOptions{
		StartToCloseTimeout: 60 * time.Minute,
		RetryPolicy:         o.retry,
	})

	// lot phase executor production
	f := workflow.ExecuteActivity(ctx, fooa.SumProductions, period)
	err = f.Get(ctx, nil)
	return
}

func (o *sumWorkflow) CalAccuracy(ctx workflow.Context, groupFlds []string, period biz.TimeRange) (err error) {
	ctx = workflow.WithActivityOptions(ctx, workflow.ActivityOptions{
		StartToCloseTimeout: 60 * time.Minute,
		RetryPolicy:         o.retry,
	})

	// lot phase executor production
	f := workflow.ExecuteActivity(ctx, fooa.CalAccuracy, groupFlds, period)
	err = f.Get(ctx, nil)
	return
}

const HourlySumWfID = "annostat-hourly-sum"

func HourlySumWorkflow(ctx workflow.Context) (err error) {
	now := workflow.Now(ctx)
	offset := time.Duration(now.Minute())*time.Minute + time.Duration(now.Second())*time.Second + time.Duration(now.Nanosecond())
	now = now.Add(-offset)
	period := biz.TimeRange{From: now.Add(-time.Hour), To: now}

	// l := workflow.GetLogger(ctx)
	// l.Info("HourlySumWorkflow is started")

	ctx = withTraceID(ctx, HourlySumWfID, now)
	w := newSumWorkflow(ctx)
	return w.SumProductions(ctx, period)
}

const DailySumWfID = "annostat-daily-sum"

func DailySumWorkflow(ctx workflow.Context) (err error) {
	now := workflow.Now(ctx)
	offset := time.Duration(now.Minute())*time.Minute + time.Duration(now.Second())*time.Second + time.Duration(now.Nanosecond())
	now = now.Add(-offset)
	period := biz.TimeRange{From: now.Add(-24 * time.Hour), To: now}

	ctx = withTraceID(ctx, DailySumWfID, now)
	w := newSumWorkflow(ctx)
	if err := w.SumProductions(ctx, period); err != nil {
		return fmt.Errorf("failed to sum production: %w", err)
	}

	groupFlds := []string{biz.JobsubmitSfldLotID, biz.JobsubmitSfldPhase, biz.JobsubmitSfldLastExecteamUid, biz.JobsubmitSfldLastExecutorUid}
	// cal individual accuracy
	if err := w.CalAccuracy(ctx, groupFlds, period); err != nil {
		return fmt.Errorf("failed to calculate individual accuracy: %w", err)
	}
	// cal team accuracy
	if err := w.CalAccuracy(ctx, groupFlds[:len(groupFlds)-1], period); err != nil {
		return fmt.Errorf("failed to calculate team accuracy: %w", err)
	}

	return nil
}

func withTraceID(ctx workflow.Context, wfid string, now time.Time) workflow.Context {
	nowLocal := now.Local()
	_, month, day := nowLocal.Date()
	hour := nowLocal.Hour()
	traceID := fmt.Sprintf("%s-%d-%d-%d", wfid, month, day, hour)
	tracing.PrintTraceID(ctx, traceID)
	return tracing.WithCustomTraceID(ctx, traceID)
}
