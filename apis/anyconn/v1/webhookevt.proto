syntax = "proto3";

package anyconn.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";
// import "iam/v1/type.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anyconn/v1;anyconn";
option java_multiple_files = true;
option java_package = "anyconn.v1";

service WebhookEvts {
  rpc CreateWebhookEvt (CreateWebhookEvtRequest) returns (WebhookEvt) {
    option (google.api.http) = {
      post: "/v1/webhookevts"
      body: "*"
    };
  }

  rpc UpdateWebhookEvt (UpdateWebhookEvtRequest) returns (WebhookEvt) {
    option (google.api.http) = {
      patch: "/v1/webhookevts/{uid}"
      body: "evt"
    };
  }

  rpc DeleteWebhookEvt (DeleteWebhookEvtRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/webhookevts/{uid}"
    };
  }

  rpc GetWebhookEvt (GetWebhookEvtRequest) returns (WebhookEvt) {
    option (google.api.http) = {
      get: "/v1/webhookevts/{uid}"
    };
  }

  rpc ListWebhookEvt (ListWebhookEvtRequest) returns (ListWebhookEvtReply) {
    option (google.api.http) = {
      get: "/v1/webhookevts"
    };
  }
}

message CreateWebhookEvtRequest {
  option (openapi.v3.schema) = {
    required: []
  };

  // platform if it is from a third-party platform
  string platform = 1;
  // unique event id within the platform
  string evt_id = 2;
  // UID of the project which the webhookevt belongs to
  string project_uid = 3;
  // UID of the task which the webhookevt belongs to
  string task_uid = 4;
  // UID of the job which the webhookevt belongs to
  string job_uid = 5;
  // event body
  string body = 6;
}

message UpdateWebhookEvtRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "evt", "fields"]
  };

  string uid = 1;
  // update contents
  CreateWebhookEvtRequest evt = 2;
  // name of fileds to be updated
  repeated string fields = 3;
}

message DeleteWebhookEvtRequest {
  // webhookevt UID
  string uid = 1;
}

message GetWebhookEvtRequest {
  // webhookevt UID
  string uid = 1;
}

message WebhookEvtFilter {
  // filter by task
  string task_uid = 1;
  // filter by job
  string job_uid = 2;
}

message ListWebhookEvtRequest {
  int32 pagesz = 1 [(validate.rules).int32 = {gte:0, lte: 100}];
  // An opaque pagination token returned from a previous call.
  // An empty token denotes the first page.
  string page_token = 2;

  WebhookEvtFilter filter = 3;
}

message ListWebhookEvtReply {
  option (openapi.v3.schema) = {
    required: ["webhookevts", "next_page_token"]
  };

  repeated WebhookEvt webhookevts = 1;
  // An opaque pagination token, if not empty, to be used to fetch the next page of results
  string next_page_token = 2;
  // total number of items found; valid only in the first page reply.
  int32 total = 3;
}

message WebhookEvt {
  option (openapi.v3.schema) = {
    required: ["uid", "webhookevted", "created_at"]
  };

  // webhookevt UID
  string uid = 1;
  // platform if it is from a third-party platform
  string platform = 2;
  // unique event id within the platform
  string evt_id = 3;
  // UID of the project which the webhookevt belongs to
  string project_uid = 4;
  // UID of the task which the webhookevt belongs to
  string task_uid = 5;
  // UID of the job which the webhookevt belongs to
  string job_uid = 6;

  // if the webhookevt is done
  bool done = 7;
  // extra info about the webhookevt
  string extra = 8;
  // event body
  string body = 9;
  // converted annotations
  string converted_annos = 10;
  // errors if any
  string errors = 11;

  // webhookevt creation time
  google.protobuf.Timestamp created_at = 15;
}
