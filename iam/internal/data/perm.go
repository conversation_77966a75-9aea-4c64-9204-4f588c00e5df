// source: gitlab.rp.konvery.work/platform/apis/iam/v1/perm.proto
package data

import (
	"context"

	"iam/internal/biz"

	"gitlab.rp.konvery.work/platform/pkg/data"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type permsRepo struct {
	repo.GenericRepo[biz.Perm]
	data *Data
}

func NewPermsRepo(d *Data, logger log.Logger) biz.PermsRepo {
	return &permsRepo{GenericRepo: data.NewGenericRepo[biz.Perm](d, logger, biz.PermTableName), data: d}
}

func (o *permsRepo) Delete(ctx context.Context, perms []string) error {
	err := o.data.WithCtx(ctx).Delete(&biz.Perm{}, biz.Perm_Name+" IN ?", perms).Error
	return Convert(&biz.Perm{}, err)
}

func (o *permsRepo) ListPermClass(ctx context.Context) (classes []string, err error) {
	err = o.data.WithCtx(ctx).Model(&biz.Perm{}).
		Select("DISTINCT(" + biz.Perm_Class + ")").Order(biz.Perm_Class).
		Scan(&classes).Error
	err = Convert(&biz.Perm{}, err)
	return
}
