{{- if .Values.service.metrics.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "appx.fullname" . }}-metrics
  {{- if .Release.Namespace }}
  namespace: {{ .Release.Namespace }}
  {{- end }}
  labels:
    app.kubernetes.io/component: metrics
    {{- include "appx.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.metrics.port }}
      targetPort: {{ .Values.service.metrics.name }}
      protocol: TCP
      name: {{ .Values.service.metrics.name }}
  selector:
    app.kubernetes.io/name: {{ include "appx.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
{{ end }}
