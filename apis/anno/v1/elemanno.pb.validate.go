// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/elemanno.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Element with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Element) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Element with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ElementMultiError, or nil if none found.
func (m *Element) ValidateAll() error {
	return m.validate(true)
}

func (m *Element) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Index

	// no validation rules for Name

	if _, ok := _Element_Type_NotInLookup[m.GetType()]; ok {
		err := ElementValidationError{
			field:  "Type",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := Element_Type_Enum_name[int32(m.GetType())]; !ok {
		err := ElementValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetDatas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ElementValidationError{
						field:  fmt.Sprintf("Datas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ElementValidationError{
						field:  fmt.Sprintf("Datas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ElementValidationError{
					field:  fmt.Sprintf("Datas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetAnno()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ElementValidationError{
					field:  "Anno",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ElementValidationError{
					field:  "Anno",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnno()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ElementValidationError{
				field:  "Anno",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ElementMultiError(errors)
	}

	return nil
}

// ElementMultiError is an error wrapping multiple validation errors returned
// by Element.ValidateAll() if the designated constraints aren't met.
type ElementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ElementMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ElementMultiError) AllErrors() []error { return m }

// ElementValidationError is the validation error returned by Element.Validate
// if the designated constraints aren't met.
type ElementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ElementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ElementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ElementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ElementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ElementValidationError) ErrorName() string { return "ElementValidationError" }

// Error satisfies the builtin error interface
func (e ElementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sElement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ElementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ElementValidationError{}

var _Element_Type_NotInLookup = map[Element_Type_Enum]struct{}{
	0: {},
}

// Validate checks the field values on RawdataParam with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RawdataParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RawdataParam with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RawdataParamMultiError, or
// nil if none found.
func (m *RawdataParam) ValidateAll() error {
	return m.validate(true)
}

func (m *RawdataParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := RawdataParam_Type_Enum_name[int32(m.GetType())]; !ok {
		err := RawdataParamValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ColumnCnt

	if len(errors) > 0 {
		return RawdataParamMultiError(errors)
	}

	return nil
}

// RawdataParamMultiError is an error wrapping multiple validation errors
// returned by RawdataParam.ValidateAll() if the designated constraints aren't met.
type RawdataParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RawdataParamMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RawdataParamMultiError) AllErrors() []error { return m }

// RawdataParamValidationError is the validation error returned by
// RawdataParam.Validate if the designated constraints aren't met.
type RawdataParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RawdataParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RawdataParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RawdataParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RawdataParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RawdataParamValidationError) ErrorName() string { return "RawdataParamValidationError" }

// Error satisfies the builtin error interface
func (e RawdataParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRawdataParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RawdataParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RawdataParamValidationError{}

// Validate checks the field values on Rawdata with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Rawdata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Rawdata with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in RawdataMultiError, or nil if none found.
func (m *Rawdata) ValidateAll() error {
	return m.validate(true)
}

func (m *Rawdata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if _, ok := _Rawdata_Type_NotInLookup[m.GetType()]; ok {
		err := RawdataValidationError{
			field:  "Type",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := Rawdata_Type_Enum_name[int32(m.GetType())]; !ok {
		err := RawdataValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _Rawdata_Format_NotInLookup[m.GetFormat()]; ok {
		err := RawdataValidationError{
			field:  "Format",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := Rawdata_Format_Enum_name[int32(m.GetFormat())]; !ok {
		err := RawdataValidationError{
			field:  "Format",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Url

	// no validation rules for Size

	// no validation rules for Sha256

	// no validation rules for Title

	for idx, item := range m.GetTransform() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RawdataValidationError{
						field:  fmt.Sprintf("Transform[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RawdataValidationError{
						field:  fmt.Sprintf("Transform[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RawdataValidationError{
					field:  fmt.Sprintf("Transform[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RawdataValidationError{
					field:  "Meta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RawdataValidationError{
					field:  "Meta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RawdataValidationError{
				field:  "Meta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetIns() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RawdataValidationError{
						field:  fmt.Sprintf("Ins[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RawdataValidationError{
						field:  fmt.Sprintf("Ins[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RawdataValidationError{
					field:  fmt.Sprintf("Ins[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for OrigName

	if all {
		switch v := interface{}(m.GetEmbedding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RawdataValidationError{
					field:  "Embedding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RawdataValidationError{
					field:  "Embedding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmbedding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RawdataValidationError{
				field:  "Embedding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RawdataMultiError(errors)
	}

	return nil
}

// RawdataMultiError is an error wrapping multiple validation errors returned
// by Rawdata.ValidateAll() if the designated constraints aren't met.
type RawdataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RawdataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RawdataMultiError) AllErrors() []error { return m }

// RawdataValidationError is the validation error returned by Rawdata.Validate
// if the designated constraints aren't met.
type RawdataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RawdataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RawdataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RawdataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RawdataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RawdataValidationError) ErrorName() string { return "RawdataValidationError" }

// Error satisfies the builtin error interface
func (e RawdataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRawdata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RawdataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RawdataValidationError{}

var _Rawdata_Type_NotInLookup = map[Rawdata_Type_Enum]struct{}{
	0: {},
}

var _Rawdata_Format_NotInLookup = map[Rawdata_Format_Enum]struct{}{
	0: {},
}

// Validate checks the field values on Direction with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Direction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Direction with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DirectionMultiError, or nil
// if none found.
func (m *Direction) ValidateAll() error {
	return m.validate(true)
}

func (m *Direction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DirectionMultiError(errors)
	}

	return nil
}

// DirectionMultiError is an error wrapping multiple validation errors returned
// by Direction.ValidateAll() if the designated constraints aren't met.
type DirectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectionMultiError) AllErrors() []error { return m }

// DirectionValidationError is the validation error returned by
// Direction.Validate if the designated constraints aren't met.
type DirectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectionValidationError) ErrorName() string { return "DirectionValidationError" }

// Error satisfies the builtin error interface
func (e DirectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectionValidationError{}

// Validate checks the field values on AttrAndValues with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AttrAndValues) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttrAndValues with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AttrAndValuesMultiError, or
// nil if none found.
func (m *AttrAndValues) ValidateAll() error {
	return m.validate(true)
}

func (m *AttrAndValues) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return AttrAndValuesMultiError(errors)
	}

	return nil
}

// AttrAndValuesMultiError is an error wrapping multiple validation errors
// returned by AttrAndValues.ValidateAll() if the designated constraints
// aren't met.
type AttrAndValuesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttrAndValuesMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttrAndValuesMultiError) AllErrors() []error { return m }

// AttrAndValuesValidationError is the validation error returned by
// AttrAndValues.Validate if the designated constraints aren't met.
type AttrAndValuesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttrAndValuesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttrAndValuesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttrAndValuesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttrAndValuesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttrAndValuesValidationError) ErrorName() string { return "AttrAndValuesValidationError" }

// Error satisfies the builtin error interface
func (e AttrAndValuesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttrAndValues.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttrAndValuesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttrAndValuesValidationError{}

// Validate checks the field values on Object with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Object) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Object with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ObjectMultiError, or nil if none found.
func (m *Object) ValidateAll() error {
	return m.validate(true)
}

func (m *Object) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uuid

	// no validation rules for TrackId

	if all {
		switch v := interface{}(m.GetLabel()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ObjectValidationError{
					field:  "Label",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ObjectValidationError{
					field:  "Label",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLabel()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ObjectValidationError{
				field:  "Label",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VanishLater

	if all {
		switch v := interface{}(m.GetCompound()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ObjectValidationError{
					field:  "Compound",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ObjectValidationError{
					field:  "Compound",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompound()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ObjectValidationError{
				field:  "Compound",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := Object_Source_Enum_name[int32(m.GetSource())]; !ok {
		err := ObjectValidationError{
			field:  "Source",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ObjectMultiError(errors)
	}

	return nil
}

// ObjectMultiError is an error wrapping multiple validation errors returned by
// Object.ValidateAll() if the designated constraints aren't met.
type ObjectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ObjectMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ObjectMultiError) AllErrors() []error { return m }

// ObjectValidationError is the validation error returned by Object.Validate if
// the designated constraints aren't met.
type ObjectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ObjectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ObjectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ObjectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ObjectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ObjectValidationError) ErrorName() string { return "ObjectValidationError" }

// Error satisfies the builtin error interface
func (e ObjectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sObject.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ObjectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ObjectValidationError{}

// Validate checks the field values on RawdataAnno with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RawdataAnno) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RawdataAnno with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RawdataAnnoMultiError, or
// nil if none found.
func (m *RawdataAnno) ValidateAll() error {
	return m.validate(true)
}

func (m *RawdataAnno) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	for idx, item := range m.GetObjects() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RawdataAnnoValidationError{
						field:  fmt.Sprintf("Objects[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RawdataAnnoValidationError{
						field:  fmt.Sprintf("Objects[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RawdataAnnoValidationError{
					field:  fmt.Sprintf("Objects[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RawdataAnnoValidationError{
						field:  fmt.Sprintf("Attrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RawdataAnnoValidationError{
						field:  fmt.Sprintf("Attrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RawdataAnnoValidationError{
					field:  fmt.Sprintf("Attrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetSegmentation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RawdataAnnoValidationError{
					field:  "Segmentation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RawdataAnnoValidationError{
					field:  "Segmentation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSegmentation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RawdataAnnoValidationError{
				field:  "Segmentation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrigName

	// no validation rules for Url

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RawdataAnnoValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RawdataAnnoValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RawdataAnnoValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RawdataAnnoMultiError(errors)
	}

	return nil
}

// RawdataAnnoMultiError is an error wrapping multiple validation errors
// returned by RawdataAnno.ValidateAll() if the designated constraints aren't met.
type RawdataAnnoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RawdataAnnoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RawdataAnnoMultiError) AllErrors() []error { return m }

// RawdataAnnoValidationError is the validation error returned by
// RawdataAnno.Validate if the designated constraints aren't met.
type RawdataAnnoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RawdataAnnoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RawdataAnnoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RawdataAnnoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RawdataAnnoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RawdataAnnoValidationError) ErrorName() string { return "RawdataAnnoValidationError" }

// Error satisfies the builtin error interface
func (e RawdataAnnoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRawdataAnno.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RawdataAnnoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RawdataAnnoValidationError{}

// Validate checks the field values on Segmentation with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Segmentation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Segmentation with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SegmentationMultiError, or
// nil if none found.
func (m *Segmentation) ValidateAll() error {
	return m.validate(true)
}

func (m *Segmentation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClasses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SegmentationValidationError{
						field:  fmt.Sprintf("Classes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SegmentationValidationError{
						field:  fmt.Sprintf("Classes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SegmentationValidationError{
					field:  fmt.Sprintf("Classes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetRle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SegmentationValidationError{
					field:  "Rle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SegmentationValidationError{
					field:  "Rle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SegmentationValidationError{
				field:  "Rle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RlePack

	if len(errors) > 0 {
		return SegmentationMultiError(errors)
	}

	return nil
}

// SegmentationMultiError is an error wrapping multiple validation errors
// returned by Segmentation.ValidateAll() if the designated constraints aren't met.
type SegmentationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SegmentationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SegmentationMultiError) AllErrors() []error { return m }

// SegmentationValidationError is the validation error returned by
// Segmentation.Validate if the designated constraints aren't met.
type SegmentationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SegmentationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SegmentationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SegmentationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SegmentationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SegmentationValidationError) ErrorName() string { return "SegmentationValidationError" }

// Error satisfies the builtin error interface
func (e SegmentationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSegmentation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SegmentationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SegmentationValidationError{}

// Validate checks the field values on ElementAnno with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ElementAnno) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ElementAnno with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ElementAnnoMultiError, or
// nil if none found.
func (m *ElementAnno) ValidateAll() error {
	return m.validate(true)
}

func (m *ElementAnno) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Index

	// no validation rules for Name

	for idx, item := range m.GetRawdataAnnos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ElementAnnoValidationError{
						field:  fmt.Sprintf("RawdataAnnos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ElementAnnoValidationError{
						field:  fmt.Sprintf("RawdataAnnos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ElementAnnoValidationError{
					field:  fmt.Sprintf("RawdataAnnos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ElementAnnoValidationError{
						field:  fmt.Sprintf("Attrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ElementAnnoValidationError{
						field:  fmt.Sprintf("Attrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ElementAnnoValidationError{
					field:  fmt.Sprintf("Attrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for InsCnt

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ElementAnnoValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ElementAnnoValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ElementAnnoValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSegmentation3D()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ElementAnnoValidationError{
					field:  "Segmentation3D",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ElementAnnoValidationError{
					field:  "Segmentation3D",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSegmentation3D()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ElementAnnoValidationError{
				field:  "Segmentation3D",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ElementAnnoMultiError(errors)
	}

	return nil
}

// ElementAnnoMultiError is an error wrapping multiple validation errors
// returned by ElementAnno.ValidateAll() if the designated constraints aren't met.
type ElementAnnoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ElementAnnoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ElementAnnoMultiError) AllErrors() []error { return m }

// ElementAnnoValidationError is the validation error returned by
// ElementAnno.Validate if the designated constraints aren't met.
type ElementAnnoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ElementAnnoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ElementAnnoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ElementAnnoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ElementAnnoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ElementAnnoValidationError) ErrorName() string { return "ElementAnnoValidationError" }

// Error satisfies the builtin error interface
func (e ElementAnnoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sElementAnno.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ElementAnnoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ElementAnnoValidationError{}

// Validate checks the field values on Segmentation3D with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Segmentation3D) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Segmentation3D with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Segmentation3DMultiError,
// or nil if none found.
func (m *Segmentation3D) ValidateAll() error {
	return m.validate(true)
}

func (m *Segmentation3D) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResult()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Segmentation3DValidationError{
					field:  "Result",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Segmentation3DValidationError{
					field:  "Result",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResult()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Segmentation3DValidationError{
				field:  "Result",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetStatistic() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, Segmentation3DValidationError{
						field:  fmt.Sprintf("Statistic[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, Segmentation3DValidationError{
						field:  fmt.Sprintf("Statistic[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return Segmentation3DValidationError{
					field:  fmt.Sprintf("Statistic[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return Segmentation3DMultiError(errors)
	}

	return nil
}

// Segmentation3DMultiError is an error wrapping multiple validation errors
// returned by Segmentation3D.ValidateAll() if the designated constraints
// aren't met.
type Segmentation3DMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Segmentation3DMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Segmentation3DMultiError) AllErrors() []error { return m }

// Segmentation3DValidationError is the validation error returned by
// Segmentation3D.Validate if the designated constraints aren't met.
type Segmentation3DValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Segmentation3DValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Segmentation3DValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Segmentation3DValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Segmentation3DValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Segmentation3DValidationError) ErrorName() string { return "Segmentation3DValidationError" }

// Error satisfies the builtin error interface
func (e Segmentation3DValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSegmentation3D.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Segmentation3DValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Segmentation3DValidationError{}

// Validate checks the field values on Segmentation3DResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Segmentation3DResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Segmentation3DResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Segmentation3DResultMultiError, or nil if none found.
func (m *Segmentation3DResult) ValidateAll() error {
	return m.validate(true)
}

func (m *Segmentation3DResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Segmentation3DResultMultiError(errors)
	}

	return nil
}

// Segmentation3DResultMultiError is an error wrapping multiple validation
// errors returned by Segmentation3DResult.ValidateAll() if the designated
// constraints aren't met.
type Segmentation3DResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Segmentation3DResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Segmentation3DResultMultiError) AllErrors() []error { return m }

// Segmentation3DResultValidationError is the validation error returned by
// Segmentation3DResult.Validate if the designated constraints aren't met.
type Segmentation3DResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Segmentation3DResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Segmentation3DResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Segmentation3DResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Segmentation3DResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Segmentation3DResultValidationError) ErrorName() string {
	return "Segmentation3DResultValidationError"
}

// Error satisfies the builtin error interface
func (e Segmentation3DResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSegmentation3DResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Segmentation3DResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Segmentation3DResultValidationError{}

// Validate checks the field values on Segmentation3DStatistic with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Segmentation3DStatistic) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Segmentation3DStatistic with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Segmentation3DStatisticMultiError, or nil if none found.
func (m *Segmentation3DStatistic) ValidateAll() error {
	return m.validate(true)
}

func (m *Segmentation3DStatistic) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CategoryName

	// no validation rules for Num

	for idx, item := range m.GetInstances() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, Segmentation3DStatisticValidationError{
						field:  fmt.Sprintf("Instances[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, Segmentation3DStatisticValidationError{
						field:  fmt.Sprintf("Instances[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return Segmentation3DStatisticValidationError{
					field:  fmt.Sprintf("Instances[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return Segmentation3DStatisticMultiError(errors)
	}

	return nil
}

// Segmentation3DStatisticMultiError is an error wrapping multiple validation
// errors returned by Segmentation3DStatistic.ValidateAll() if the designated
// constraints aren't met.
type Segmentation3DStatisticMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Segmentation3DStatisticMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Segmentation3DStatisticMultiError) AllErrors() []error { return m }

// Segmentation3DStatisticValidationError is the validation error returned by
// Segmentation3DStatistic.Validate if the designated constraints aren't met.
type Segmentation3DStatisticValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Segmentation3DStatisticValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Segmentation3DStatisticValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Segmentation3DStatisticValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Segmentation3DStatisticValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Segmentation3DStatisticValidationError) ErrorName() string {
	return "Segmentation3DStatisticValidationError"
}

// Error satisfies the builtin error interface
func (e Segmentation3DStatisticValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSegmentation3DStatistic.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Segmentation3DStatisticValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Segmentation3DStatisticValidationError{}

// Validate checks the field values on Segmentation3DInstance with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Segmentation3DInstance) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Segmentation3DInstance with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Segmentation3DInstanceMultiError, or nil if none found.
func (m *Segmentation3DInstance) ValidateAll() error {
	return m.validate(true)
}

func (m *Segmentation3DInstance) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InstanceId

	// no validation rules for Num

	if len(errors) > 0 {
		return Segmentation3DInstanceMultiError(errors)
	}

	return nil
}

// Segmentation3DInstanceMultiError is an error wrapping multiple validation
// errors returned by Segmentation3DInstance.ValidateAll() if the designated
// constraints aren't met.
type Segmentation3DInstanceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Segmentation3DInstanceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Segmentation3DInstanceMultiError) AllErrors() []error { return m }

// Segmentation3DInstanceValidationError is the validation error returned by
// Segmentation3DInstance.Validate if the designated constraints aren't met.
type Segmentation3DInstanceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Segmentation3DInstanceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Segmentation3DInstanceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Segmentation3DInstanceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Segmentation3DInstanceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Segmentation3DInstanceValidationError) ErrorName() string {
	return "Segmentation3DInstanceValidationError"
}

// Error satisfies the builtin error interface
func (e Segmentation3DInstanceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSegmentation3DInstance.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Segmentation3DInstanceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Segmentation3DInstanceValidationError{}

// Validate checks the field values on ExportAnnos with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExportAnnos) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExportAnnos with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ExportAnnosMultiError, or
// nil if none found.
func (m *ExportAnnos) ValidateAll() error {
	return m.validate(true)
}

func (m *ExportAnnos) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetElementAnnos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExportAnnosValidationError{
						field:  fmt.Sprintf("ElementAnnos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExportAnnosValidationError{
						field:  fmt.Sprintf("ElementAnnos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExportAnnosValidationError{
					field:  fmt.Sprintf("ElementAnnos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for JobUid

	if len(errors) > 0 {
		return ExportAnnosMultiError(errors)
	}

	return nil
}

// ExportAnnosMultiError is an error wrapping multiple validation errors
// returned by ExportAnnos.ValidateAll() if the designated constraints aren't met.
type ExportAnnosMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExportAnnosMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExportAnnosMultiError) AllErrors() []error { return m }

// ExportAnnosValidationError is the validation error returned by
// ExportAnnos.Validate if the designated constraints aren't met.
type ExportAnnosValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExportAnnosValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExportAnnosValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExportAnnosValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExportAnnosValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExportAnnosValidationError) ErrorName() string { return "ExportAnnosValidationError" }

// Error satisfies the builtin error interface
func (e ExportAnnosValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExportAnnos.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExportAnnosValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExportAnnosValidationError{}

// Validate checks the field values on Element_Type with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Element_Type) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Element_Type with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Element_TypeMultiError, or
// nil if none found.
func (m *Element_Type) ValidateAll() error {
	return m.validate(true)
}

func (m *Element_Type) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Element_TypeMultiError(errors)
	}

	return nil
}

// Element_TypeMultiError is an error wrapping multiple validation errors
// returned by Element_Type.ValidateAll() if the designated constraints aren't met.
type Element_TypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Element_TypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Element_TypeMultiError) AllErrors() []error { return m }

// Element_TypeValidationError is the validation error returned by
// Element_Type.Validate if the designated constraints aren't met.
type Element_TypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Element_TypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Element_TypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Element_TypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Element_TypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Element_TypeValidationError) ErrorName() string { return "Element_TypeValidationError" }

// Error satisfies the builtin error interface
func (e Element_TypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sElement_Type.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Element_TypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Element_TypeValidationError{}

// Validate checks the field values on RawdataParam_Type with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RawdataParam_Type) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RawdataParam_Type with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RawdataParam_TypeMultiError, or nil if none found.
func (m *RawdataParam_Type) ValidateAll() error {
	return m.validate(true)
}

func (m *RawdataParam_Type) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return RawdataParam_TypeMultiError(errors)
	}

	return nil
}

// RawdataParam_TypeMultiError is an error wrapping multiple validation errors
// returned by RawdataParam_Type.ValidateAll() if the designated constraints
// aren't met.
type RawdataParam_TypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RawdataParam_TypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RawdataParam_TypeMultiError) AllErrors() []error { return m }

// RawdataParam_TypeValidationError is the validation error returned by
// RawdataParam_Type.Validate if the designated constraints aren't met.
type RawdataParam_TypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RawdataParam_TypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RawdataParam_TypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RawdataParam_TypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RawdataParam_TypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RawdataParam_TypeValidationError) ErrorName() string {
	return "RawdataParam_TypeValidationError"
}

// Error satisfies the builtin error interface
func (e RawdataParam_TypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRawdataParam_Type.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RawdataParam_TypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RawdataParam_TypeValidationError{}

// Validate checks the field values on RawdataParam_DistortionType with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RawdataParam_DistortionType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RawdataParam_DistortionType with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RawdataParam_DistortionTypeMultiError, or nil if none found.
func (m *RawdataParam_DistortionType) ValidateAll() error {
	return m.validate(true)
}

func (m *RawdataParam_DistortionType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return RawdataParam_DistortionTypeMultiError(errors)
	}

	return nil
}

// RawdataParam_DistortionTypeMultiError is an error wrapping multiple
// validation errors returned by RawdataParam_DistortionType.ValidateAll() if
// the designated constraints aren't met.
type RawdataParam_DistortionTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RawdataParam_DistortionTypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RawdataParam_DistortionTypeMultiError) AllErrors() []error { return m }

// RawdataParam_DistortionTypeValidationError is the validation error returned
// by RawdataParam_DistortionType.Validate if the designated constraints
// aren't met.
type RawdataParam_DistortionTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RawdataParam_DistortionTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RawdataParam_DistortionTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RawdataParam_DistortionTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RawdataParam_DistortionTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RawdataParam_DistortionTypeValidationError) ErrorName() string {
	return "RawdataParam_DistortionTypeValidationError"
}

// Error satisfies the builtin error interface
func (e RawdataParam_DistortionTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRawdataParam_DistortionType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RawdataParam_DistortionTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RawdataParam_DistortionTypeValidationError{}

// Validate checks the field values on Rawdata_ImageMeta with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *Rawdata_ImageMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Rawdata_ImageMeta with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Rawdata_ImageMetaMultiError, or nil if none found.
func (m *Rawdata_ImageMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *Rawdata_ImageMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Width

	// no validation rules for Height

	// no validation rules for Camera

	if len(errors) > 0 {
		return Rawdata_ImageMetaMultiError(errors)
	}

	return nil
}

// Rawdata_ImageMetaMultiError is an error wrapping multiple validation errors
// returned by Rawdata_ImageMeta.ValidateAll() if the designated constraints
// aren't met.
type Rawdata_ImageMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Rawdata_ImageMetaMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Rawdata_ImageMetaMultiError) AllErrors() []error { return m }

// Rawdata_ImageMetaValidationError is the validation error returned by
// Rawdata_ImageMeta.Validate if the designated constraints aren't met.
type Rawdata_ImageMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Rawdata_ImageMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Rawdata_ImageMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Rawdata_ImageMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Rawdata_ImageMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Rawdata_ImageMetaValidationError) ErrorName() string {
	return "Rawdata_ImageMetaValidationError"
}

// Error satisfies the builtin error interface
func (e Rawdata_ImageMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRawdata_ImageMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Rawdata_ImageMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Rawdata_ImageMetaValidationError{}

// Validate checks the field values on Rawdata_PCDMeta with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *Rawdata_PCDMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Rawdata_PCDMeta with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Rawdata_PCDMetaMultiError, or nil if none found.
func (m *Rawdata_PCDMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *Rawdata_PCDMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Points

	if all {
		switch v := interface{}(m.GetViewpoint()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Rawdata_PCDMetaValidationError{
					field:  "Viewpoint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Rawdata_PCDMetaValidationError{
					field:  "Viewpoint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetViewpoint()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Rawdata_PCDMetaValidationError{
				field:  "Viewpoint",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return Rawdata_PCDMetaMultiError(errors)
	}

	return nil
}

// Rawdata_PCDMetaMultiError is an error wrapping multiple validation errors
// returned by Rawdata_PCDMeta.ValidateAll() if the designated constraints
// aren't met.
type Rawdata_PCDMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Rawdata_PCDMetaMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Rawdata_PCDMetaMultiError) AllErrors() []error { return m }

// Rawdata_PCDMetaValidationError is the validation error returned by
// Rawdata_PCDMeta.Validate if the designated constraints aren't met.
type Rawdata_PCDMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Rawdata_PCDMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Rawdata_PCDMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Rawdata_PCDMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Rawdata_PCDMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Rawdata_PCDMetaValidationError) ErrorName() string { return "Rawdata_PCDMetaValidationError" }

// Error satisfies the builtin error interface
func (e Rawdata_PCDMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRawdata_PCDMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Rawdata_PCDMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Rawdata_PCDMetaValidationError{}

// Validate checks the field values on Rawdata_Meta with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Rawdata_Meta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Rawdata_Meta with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Rawdata_MetaMultiError, or
// nil if none found.
func (m *Rawdata_Meta) ValidateAll() error {
	return m.validate(true)
}

func (m *Rawdata_Meta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Rawdata_MetaValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Rawdata_MetaValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Rawdata_MetaValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPcd()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Rawdata_MetaValidationError{
					field:  "Pcd",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Rawdata_MetaValidationError{
					field:  "Pcd",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPcd()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Rawdata_MetaValidationError{
				field:  "Pcd",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return Rawdata_MetaMultiError(errors)
	}

	return nil
}

// Rawdata_MetaMultiError is an error wrapping multiple validation errors
// returned by Rawdata_Meta.ValidateAll() if the designated constraints aren't met.
type Rawdata_MetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Rawdata_MetaMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Rawdata_MetaMultiError) AllErrors() []error { return m }

// Rawdata_MetaValidationError is the validation error returned by
// Rawdata_Meta.Validate if the designated constraints aren't met.
type Rawdata_MetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Rawdata_MetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Rawdata_MetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Rawdata_MetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Rawdata_MetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Rawdata_MetaValidationError) ErrorName() string { return "Rawdata_MetaValidationError" }

// Error satisfies the builtin error interface
func (e Rawdata_MetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRawdata_Meta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Rawdata_MetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Rawdata_MetaValidationError{}

// Validate checks the field values on Rawdata_Type with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Rawdata_Type) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Rawdata_Type with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Rawdata_TypeMultiError, or
// nil if none found.
func (m *Rawdata_Type) ValidateAll() error {
	return m.validate(true)
}

func (m *Rawdata_Type) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Rawdata_TypeMultiError(errors)
	}

	return nil
}

// Rawdata_TypeMultiError is an error wrapping multiple validation errors
// returned by Rawdata_Type.ValidateAll() if the designated constraints aren't met.
type Rawdata_TypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Rawdata_TypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Rawdata_TypeMultiError) AllErrors() []error { return m }

// Rawdata_TypeValidationError is the validation error returned by
// Rawdata_Type.Validate if the designated constraints aren't met.
type Rawdata_TypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Rawdata_TypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Rawdata_TypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Rawdata_TypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Rawdata_TypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Rawdata_TypeValidationError) ErrorName() string { return "Rawdata_TypeValidationError" }

// Error satisfies the builtin error interface
func (e Rawdata_TypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRawdata_Type.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Rawdata_TypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Rawdata_TypeValidationError{}

// Validate checks the field values on Rawdata_Format with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Rawdata_Format) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Rawdata_Format with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Rawdata_FormatMultiError,
// or nil if none found.
func (m *Rawdata_Format) ValidateAll() error {
	return m.validate(true)
}

func (m *Rawdata_Format) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Rawdata_FormatMultiError(errors)
	}

	return nil
}

// Rawdata_FormatMultiError is an error wrapping multiple validation errors
// returned by Rawdata_Format.ValidateAll() if the designated constraints
// aren't met.
type Rawdata_FormatMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Rawdata_FormatMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Rawdata_FormatMultiError) AllErrors() []error { return m }

// Rawdata_FormatValidationError is the validation error returned by
// Rawdata_Format.Validate if the designated constraints aren't met.
type Rawdata_FormatValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Rawdata_FormatValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Rawdata_FormatValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Rawdata_FormatValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Rawdata_FormatValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Rawdata_FormatValidationError) ErrorName() string { return "Rawdata_FormatValidationError" }

// Error satisfies the builtin error interface
func (e Rawdata_FormatValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRawdata_Format.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Rawdata_FormatValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Rawdata_FormatValidationError{}

// Validate checks the field values on Rawdata_Embedding with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *Rawdata_Embedding) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Rawdata_Embedding with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Rawdata_EmbeddingMultiError, or nil if none found.
func (m *Rawdata_Embedding) ValidateAll() error {
	return m.validate(true)
}

func (m *Rawdata_Embedding) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	if len(errors) > 0 {
		return Rawdata_EmbeddingMultiError(errors)
	}

	return nil
}

// Rawdata_EmbeddingMultiError is an error wrapping multiple validation errors
// returned by Rawdata_Embedding.ValidateAll() if the designated constraints
// aren't met.
type Rawdata_EmbeddingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Rawdata_EmbeddingMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Rawdata_EmbeddingMultiError) AllErrors() []error { return m }

// Rawdata_EmbeddingValidationError is the validation error returned by
// Rawdata_Embedding.Validate if the designated constraints aren't met.
type Rawdata_EmbeddingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Rawdata_EmbeddingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Rawdata_EmbeddingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Rawdata_EmbeddingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Rawdata_EmbeddingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Rawdata_EmbeddingValidationError) ErrorName() string {
	return "Rawdata_EmbeddingValidationError"
}

// Error satisfies the builtin error interface
func (e Rawdata_EmbeddingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRawdata_Embedding.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Rawdata_EmbeddingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Rawdata_EmbeddingValidationError{}

// Validate checks the field values on Object_Widget with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Object_Widget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Object_Widget with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Object_WidgetMultiError, or
// nil if none found.
func (m *Object_Widget) ValidateAll() error {
	return m.validate(true)
}

func (m *Object_Widget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _Object_Widget_Name_NotInLookup[m.GetName()]; ok {
		err := Object_WidgetValidationError{
			field:  "Name",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := WidgetName_Enum_name[int32(m.GetName())]; !ok {
		err := Object_WidgetValidationError{
			field:  "Name",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetGaps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, Object_WidgetValidationError{
						field:  fmt.Sprintf("Gaps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, Object_WidgetValidationError{
						field:  fmt.Sprintf("Gaps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return Object_WidgetValidationError{
					field:  fmt.Sprintf("Gaps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Uri

	if all {
		switch v := interface{}(m.GetForward()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Object_WidgetValidationError{
					field:  "Forward",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Object_WidgetValidationError{
					field:  "Forward",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetForward()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Object_WidgetValidationError{
				field:  "Forward",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PointCnt

	// no validation rules for SegClassId

	if _, ok := WidgetLineType_Enum_name[int32(m.GetLineType())]; !ok {
		err := Object_WidgetValidationError{
			field:  "LineType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return Object_WidgetMultiError(errors)
	}

	return nil
}

// Object_WidgetMultiError is an error wrapping multiple validation errors
// returned by Object_Widget.ValidateAll() if the designated constraints
// aren't met.
type Object_WidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Object_WidgetMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Object_WidgetMultiError) AllErrors() []error { return m }

// Object_WidgetValidationError is the validation error returned by
// Object_Widget.Validate if the designated constraints aren't met.
type Object_WidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Object_WidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Object_WidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Object_WidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Object_WidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Object_WidgetValidationError) ErrorName() string { return "Object_WidgetValidationError" }

// Error satisfies the builtin error interface
func (e Object_WidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sObject_Widget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Object_WidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Object_WidgetValidationError{}

var _Object_Widget_Name_NotInLookup = map[WidgetName_Enum]struct{}{
	0: {},
}

// Validate checks the field values on Object_Label with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Object_Label) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Object_Label with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Object_LabelMultiError, or
// nil if none found.
func (m *Object_Label) ValidateAll() error {
	return m.validate(true)
}

func (m *Object_Label) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if all {
		switch v := interface{}(m.GetWidget()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Object_LabelValidationError{
					field:  "Widget",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Object_LabelValidationError{
					field:  "Widget",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWidget()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Object_LabelValidationError{
				field:  "Widget",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, Object_LabelValidationError{
						field:  fmt.Sprintf("Attrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, Object_LabelValidationError{
						field:  fmt.Sprintf("Attrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return Object_LabelValidationError{
					field:  fmt.Sprintf("Attrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return Object_LabelMultiError(errors)
	}

	return nil
}

// Object_LabelMultiError is an error wrapping multiple validation errors
// returned by Object_Label.ValidateAll() if the designated constraints aren't met.
type Object_LabelMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Object_LabelMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Object_LabelMultiError) AllErrors() []error { return m }

// Object_LabelValidationError is the validation error returned by
// Object_Label.Validate if the designated constraints aren't met.
type Object_LabelValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Object_LabelValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Object_LabelValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Object_LabelValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Object_LabelValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Object_LabelValidationError) ErrorName() string { return "Object_LabelValidationError" }

// Error satisfies the builtin error interface
func (e Object_LabelValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sObject_Label.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Object_LabelValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Object_LabelValidationError{}

// Validate checks the field values on Object_Compound with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *Object_Compound) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Object_Compound with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Object_CompoundMultiError, or nil if none found.
func (m *Object_Compound) ValidateAll() error {
	return m.validate(true)
}

func (m *Object_Compound) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Object_CompoundMultiError(errors)
	}

	return nil
}

// Object_CompoundMultiError is an error wrapping multiple validation errors
// returned by Object_Compound.ValidateAll() if the designated constraints
// aren't met.
type Object_CompoundMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Object_CompoundMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Object_CompoundMultiError) AllErrors() []error { return m }

// Object_CompoundValidationError is the validation error returned by
// Object_Compound.Validate if the designated constraints aren't met.
type Object_CompoundValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Object_CompoundValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Object_CompoundValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Object_CompoundValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Object_CompoundValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Object_CompoundValidationError) ErrorName() string { return "Object_CompoundValidationError" }

// Error satisfies the builtin error interface
func (e Object_CompoundValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sObject_Compound.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Object_CompoundValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Object_CompoundValidationError{}

// Validate checks the field values on Object_Source with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Object_Source) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Object_Source with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Object_SourceMultiError, or
// nil if none found.
func (m *Object_Source) ValidateAll() error {
	return m.validate(true)
}

func (m *Object_Source) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Object_SourceMultiError(errors)
	}

	return nil
}

// Object_SourceMultiError is an error wrapping multiple validation errors
// returned by Object_Source.ValidateAll() if the designated constraints
// aren't met.
type Object_SourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Object_SourceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Object_SourceMultiError) AllErrors() []error { return m }

// Object_SourceValidationError is the validation error returned by
// Object_Source.Validate if the designated constraints aren't met.
type Object_SourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Object_SourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Object_SourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Object_SourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Object_SourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Object_SourceValidationError) ErrorName() string { return "Object_SourceValidationError" }

// Error satisfies the builtin error interface
func (e Object_SourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sObject_Source.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Object_SourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Object_SourceValidationError{}

// Validate checks the field values on RawdataAnno_Metadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RawdataAnno_Metadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RawdataAnno_Metadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RawdataAnno_MetadataMultiError, or nil if none found.
func (m *RawdataAnno_Metadata) ValidateAll() error {
	return m.validate(true)
}

func (m *RawdataAnno_Metadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Camera

	if len(errors) > 0 {
		return RawdataAnno_MetadataMultiError(errors)
	}

	return nil
}

// RawdataAnno_MetadataMultiError is an error wrapping multiple validation
// errors returned by RawdataAnno_Metadata.ValidateAll() if the designated
// constraints aren't met.
type RawdataAnno_MetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RawdataAnno_MetadataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RawdataAnno_MetadataMultiError) AllErrors() []error { return m }

// RawdataAnno_MetadataValidationError is the validation error returned by
// RawdataAnno_Metadata.Validate if the designated constraints aren't met.
type RawdataAnno_MetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RawdataAnno_MetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RawdataAnno_MetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RawdataAnno_MetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RawdataAnno_MetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RawdataAnno_MetadataValidationError) ErrorName() string {
	return "RawdataAnno_MetadataValidationError"
}

// Error satisfies the builtin error interface
func (e RawdataAnno_MetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRawdataAnno_Metadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RawdataAnno_MetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RawdataAnno_MetadataValidationError{}

// Validate checks the field values on Segmentation_Class with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Segmentation_Class) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Segmentation_Class with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Segmentation_ClassMultiError, or nil if none found.
func (m *Segmentation_Class) ValidateAll() error {
	return m.validate(true)
}

func (m *Segmentation_Class) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if len(errors) > 0 {
		return Segmentation_ClassMultiError(errors)
	}

	return nil
}

// Segmentation_ClassMultiError is an error wrapping multiple validation errors
// returned by Segmentation_Class.ValidateAll() if the designated constraints
// aren't met.
type Segmentation_ClassMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Segmentation_ClassMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Segmentation_ClassMultiError) AllErrors() []error { return m }

// Segmentation_ClassValidationError is the validation error returned by
// Segmentation_Class.Validate if the designated constraints aren't met.
type Segmentation_ClassValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Segmentation_ClassValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Segmentation_ClassValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Segmentation_ClassValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Segmentation_ClassValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Segmentation_ClassValidationError) ErrorName() string {
	return "Segmentation_ClassValidationError"
}

// Error satisfies the builtin error interface
func (e Segmentation_ClassValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSegmentation_Class.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Segmentation_ClassValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Segmentation_ClassValidationError{}

// Validate checks the field values on Segmentation_RLE with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *Segmentation_RLE) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Segmentation_RLE with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Segmentation_RLEMultiError, or nil if none found.
func (m *Segmentation_RLE) ValidateAll() error {
	return m.validate(true)
}

func (m *Segmentation_RLE) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Segmentation_RLEMultiError(errors)
	}

	return nil
}

// Segmentation_RLEMultiError is an error wrapping multiple validation errors
// returned by Segmentation_RLE.ValidateAll() if the designated constraints
// aren't met.
type Segmentation_RLEMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Segmentation_RLEMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Segmentation_RLEMultiError) AllErrors() []error { return m }

// Segmentation_RLEValidationError is the validation error returned by
// Segmentation_RLE.Validate if the designated constraints aren't met.
type Segmentation_RLEValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Segmentation_RLEValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Segmentation_RLEValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Segmentation_RLEValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Segmentation_RLEValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Segmentation_RLEValidationError) ErrorName() string { return "Segmentation_RLEValidationError" }

// Error satisfies the builtin error interface
func (e Segmentation_RLEValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSegmentation_RLE.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Segmentation_RLEValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Segmentation_RLEValidationError{}

// Validate checks the field values on ElementAnno_Metadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ElementAnno_Metadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ElementAnno_Metadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ElementAnno_MetadataMultiError, or nil if none found.
func (m *ElementAnno_Metadata) ValidateAll() error {
	return m.validate(true)
}

func (m *ElementAnno_Metadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetExecutors() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ElementAnno_MetadataValidationError{
						field:  fmt.Sprintf("Executors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ElementAnno_MetadataValidationError{
						field:  fmt.Sprintf("Executors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ElementAnno_MetadataValidationError{
					field:  fmt.Sprintf("Executors[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ElementAnno_MetadataMultiError(errors)
	}

	return nil
}

// ElementAnno_MetadataMultiError is an error wrapping multiple validation
// errors returned by ElementAnno_Metadata.ValidateAll() if the designated
// constraints aren't met.
type ElementAnno_MetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ElementAnno_MetadataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ElementAnno_MetadataMultiError) AllErrors() []error { return m }

// ElementAnno_MetadataValidationError is the validation error returned by
// ElementAnno_Metadata.Validate if the designated constraints aren't met.
type ElementAnno_MetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ElementAnno_MetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ElementAnno_MetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ElementAnno_MetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ElementAnno_MetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ElementAnno_MetadataValidationError) ErrorName() string {
	return "ElementAnno_MetadataValidationError"
}

// Error satisfies the builtin error interface
func (e ElementAnno_MetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sElementAnno_Metadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ElementAnno_MetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ElementAnno_MetadataValidationError{}

// Validate checks the field values on ElementAnno_Metadata_Executor with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ElementAnno_Metadata_Executor) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ElementAnno_Metadata_Executor with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ElementAnno_Metadata_ExecutorMultiError, or nil if none found.
func (m *ElementAnno_Metadata_Executor) ValidateAll() error {
	return m.validate(true)
}

func (m *ElementAnno_Metadata_Executor) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ElementAnno_Metadata_ExecutorValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ElementAnno_Metadata_ExecutorValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ElementAnno_Metadata_ExecutorValidationError{
				field:  "User",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubmitAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ElementAnno_Metadata_ExecutorValidationError{
					field:  "SubmitAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ElementAnno_Metadata_ExecutorValidationError{
					field:  "SubmitAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubmitAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ElementAnno_Metadata_ExecutorValidationError{
				field:  "SubmitAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Phase

	if len(errors) > 0 {
		return ElementAnno_Metadata_ExecutorMultiError(errors)
	}

	return nil
}

// ElementAnno_Metadata_ExecutorMultiError is an error wrapping multiple
// validation errors returned by ElementAnno_Metadata_Executor.ValidateAll()
// if the designated constraints aren't met.
type ElementAnno_Metadata_ExecutorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ElementAnno_Metadata_ExecutorMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ElementAnno_Metadata_ExecutorMultiError) AllErrors() []error { return m }

// ElementAnno_Metadata_ExecutorValidationError is the validation error
// returned by ElementAnno_Metadata_Executor.Validate if the designated
// constraints aren't met.
type ElementAnno_Metadata_ExecutorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ElementAnno_Metadata_ExecutorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ElementAnno_Metadata_ExecutorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ElementAnno_Metadata_ExecutorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ElementAnno_Metadata_ExecutorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ElementAnno_Metadata_ExecutorValidationError) ErrorName() string {
	return "ElementAnno_Metadata_ExecutorValidationError"
}

// Error satisfies the builtin error interface
func (e ElementAnno_Metadata_ExecutorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sElementAnno_Metadata_Executor.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ElementAnno_Metadata_ExecutorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ElementAnno_Metadata_ExecutorValidationError{}
