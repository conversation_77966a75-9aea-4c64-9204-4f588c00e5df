syntax = "proto3";

package types;

import "openapi/v3/annotations.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/types;types";
option java_multiple_files = true;
option java_package = "types";

message TagRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "tags"]
  };

  string uid = 1;
  repeated string tags = 2;
}

message TagList {
  option (openapi.v3.schema) = {
    required: ["tags"]
  };

  repeated string tags = 1;
}
