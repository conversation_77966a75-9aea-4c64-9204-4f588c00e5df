// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/widget.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on WidgetName with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WidgetName) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WidgetName with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WidgetNameMultiError, or
// nil if none found.
func (m *WidgetName) ValidateAll() error {
	return m.validate(true)
}

func (m *WidgetName) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return WidgetNameMultiError(errors)
	}

	return nil
}

// WidgetNameMultiError is an error wrapping multiple validation errors
// returned by WidgetName.ValidateAll() if the designated constraints aren't met.
type WidgetNameMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WidgetNameMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WidgetNameMultiError) AllErrors() []error { return m }

// WidgetNameValidationError is the validation error returned by
// WidgetName.Validate if the designated constraints aren't met.
type WidgetNameValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WidgetNameValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WidgetNameValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WidgetNameValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WidgetNameValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WidgetNameValidationError) ErrorName() string { return "WidgetNameValidationError" }

// Error satisfies the builtin error interface
func (e WidgetNameValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWidgetName.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WidgetNameValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WidgetNameValidationError{}

// Validate checks the field values on WidgetLineType with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WidgetLineType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WidgetLineType with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WidgetLineTypeMultiError,
// or nil if none found.
func (m *WidgetLineType) ValidateAll() error {
	return m.validate(true)
}

func (m *WidgetLineType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return WidgetLineTypeMultiError(errors)
	}

	return nil
}

// WidgetLineTypeMultiError is an error wrapping multiple validation errors
// returned by WidgetLineType.ValidateAll() if the designated constraints
// aren't met.
type WidgetLineTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WidgetLineTypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WidgetLineTypeMultiError) AllErrors() []error { return m }

// WidgetLineTypeValidationError is the validation error returned by
// WidgetLineType.Validate if the designated constraints aren't met.
type WidgetLineTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WidgetLineTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WidgetLineTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WidgetLineTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WidgetLineTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WidgetLineTypeValidationError) ErrorName() string { return "WidgetLineTypeValidationError" }

// Error satisfies the builtin error interface
func (e WidgetLineTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWidgetLineType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WidgetLineTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WidgetLineTypeValidationError{}
