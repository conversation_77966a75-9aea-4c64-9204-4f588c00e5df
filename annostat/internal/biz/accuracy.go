// source: annostat/v1/Accuracy.proto
package biz

import (
	"time"

	"annostat/internal/data/serial"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
)

type RateByClass = serial.Map[string, float32]

type Accuracy struct {
	ID          int64       `json:"id"`
	LotID       int64       `json:"lot_id" gorm:"default:null"`
	Phase       int32       `json:"phase" gorm:"default:null"`
	ExecutorUid string      `json:"executor_uid" gorm:"default:null"` // the user which the job is assigned to
	ExecteamUid string      `json:"execteam_uid" gorm:"default:null"` // the team which the executor belongs to
	RateElems   float32     `json:"rate_elems" gorm:"default:null"`
	RateIns     float32     `json:"ins" gorm:"default:null"`
	RateIns2d   float32     `json:"ins_2d" gorm:"default:null;column:rate_ins_2d"`
	RateIns3d   float32     `json:"ins_3d" gorm:"default:null;column:rate_ins_3d"`
	RateByClass RateByClass `json:"rate_by_class" gorm:"default:null"`
	Duration    int32       `json:"duration" gorm:"default:null"`
	PeriodStart time.Time   `json:"period_start" gorm:"default:null"`
	CreatedAt   time.Time   `json:"created_at" gorm:"default:null"`
}

func (o *Accuracy) GetID() int64 { return o.ID }

const AccuracyTableName = "accuracies"

var (
	accuracy_ = field.RegObject(&Accuracy{})
	// AccuracyUpdatableFlds = field.NewModel(Accuracy_)

	// field name in snake case
	AccuracySfldLotID       = field.Sname(&accuracy_.LotID)
	AccuracySfldPhase       = field.Sname(&accuracy_.Phase)
	AccuracySfldRateIns     = field.Sname(&accuracy_.RateIns)
	AccuracySfldRateIns2d   = field.Sname(&accuracy_.RateIns2d)
	AccuracySfldRateIns3d   = field.Sname(&accuracy_.RateIns3d)
	AccuracySfldRateByClass = field.Sname(&accuracy_.RateByClass)
	AccuracySfldExecutorUid = field.Sname(&accuracy_.ExecutorUid)
	AccuracySfldExecteamUid = field.Sname(&accuracy_.ExecteamUid)
	AccuracySfldDuration    = field.Sname(&accuracy_.Duration)
	AccuracySfldPeriodStart = field.Sname(&accuracy_.PeriodStart)
)

type AccuracyListFilter struct {
	TimeRange
	LotIDs []int64
	IDs    []int64
	Phases []int32

	ExecutorUids []string
	ExecteamUids []string

	Duration int32
}

func (o *AccuracyListFilter) Apply(tx Tx) Tx {
	if o == nil {
		return tx
	}

	tbl := "accuracies."
	tx = ApplyFieldFilter(tx, tbl+"id", o.IDs)
	tx = ApplyFieldFilter(tx, tbl+AccuracySfldLotID, o.LotIDs)
	tx = ApplyFieldFilter(tx, tbl+AccuracySfldPhase, o.Phases)
	tx = ApplyFieldFilter(tx, tbl+AccuracySfldExecutorUid, o.ExecutorUids)
	tx = ApplyFieldFilter(tx, tbl+AccuracySfldExecteamUid, o.ExecteamUids)
	tx = ApplyFieldFilter(tx, tbl+AccuracySfldDuration, o.Duration)
	tx = ApplyTimeRangeFilter(tx, tbl+AccuracySfldPeriodStart, &o.TimeRange)
	return tx
}

type LastAccuracyListFilter struct {
	Filter ListFilter
}

func (o *LastAccuracyListFilter) Apply(tx Tx) Tx {
	if o == nil {
		return tx
	}

	// TODO: handle ExecutorUids/ExecteamUids
	subq := CleanTx(tx).Model(&Accuracy{}).
		Select(field.Join(AccuracySfldLotID, AccuracySfldPhase, "MAX(id) AS id")).
		Group(field.Join(AccuracySfldLotID, AccuracySfldPhase))
	if o.Filter != nil {
		subq = o.Filter.Apply(subq)
	}
	tx = tx.Joins("JOIN (?) AS subq ON accuracies.id = subq.id", subq)

	return tx
}

type AccuraciesRepo repo.GenericRepo[Accuracy]

// type AccuracysBiz struct {
// 	repo   AccuraciesRepo
// 	bgtask BackgroundTask
// 	log    *log.Helper
// }

// func NewAccuracysBiz(repo AccuraciesRepo, bgtask BackgroundTask, logger log.Logger) *AccuracysBiz {
// 	return &AccuracysBiz{repo: repo, bgtask: bgtask, log: log.NewHelper(logger)}
// }
