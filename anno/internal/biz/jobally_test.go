package biz

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
)

func TestProcessComments(t *testing.T) {
	elems := Elements{
		{Datas: []*anno.Rawdata{
			{},
		}},
	}
	elemAnnos := []*anno.ElementAnno{
		{RawdataAnnos: []*anno.RawdataAnno{
			{Objects: []*anno.Object{
				{Uuid: "car-1"},
				{Uuid: "car-2"},
				{Uuid: "car-3"},
			}},
		}},
	}
	reqAnnos := &anno.JobAnno{ElementAnnos: []*anno.ElementAnno{
		{RawdataAnnos: []*anno.RawdataAnno{
			{Objects: []*anno.Object{
				{Uuid: "car-1"},
				{Uuid: "car-2"},
				{Uuid: "car-3"},
			}},
		}},
	}}
	comments := []*anno.AnnoComment{
		{
			Uuid:     "1",
			ObjUuids: []string{"car-1"},
			Scope:    anno.AnnoComment_Scope_object,
			Reasons: &anno.AnnoCommentReason_Reasons{
				Class:   commentClassFlawed,
				Reasons: []string{"off_center"},
			},
		},
		{
			Uuid:  "2",
			Scope: anno.AnnoComment_Scope_object,
			Reasons: &anno.AnnoCommentReason_Reasons{
				Class: commentClassMissed,
			},
			ExtraInfo: &anno.AnnoComment_ExtraInfo{
				Labels:   []string{"car"},
				Position: []float64{1, 2, 3},
			},
		},
		{
			Uuid: "3",
			Reasons: &anno.AnnoCommentReason_Reasons{
				Class: commentClassOther,
			},
			Scope: anno.AnnoComment_Scope_element,
		},
	}
	cases := []struct {
		name            string
		job             *Job
		reqAnnos        *anno.JobAnno
		jobAnnos        *anno.JobAnno
		jobComments     Comments
		resolveComments []*anno.ResolveAnnoComment
		addComments     []*anno.AnnoComment
		expResult       ValidValue[Comments]
		expErr          bool
	}{
		{
			name: "reject ok",
			job: &Job{
				Phase: 2,
				Ally: &Jobally{
					Elements: elems,
					Annotations: *serial.New(&anno.JobAnno{
						ElementAnnos: elemAnnos,
					}),
					Comments: nil,
				},
			},
			reqAnnos: nil,
			addComments: []*anno.AnnoComment{
				comments[0],
				comments[1],
				comments[2],
			},
			resolveComments: nil,
			expResult: NewValidValue(Comments{
				comments[0],
				comments[1],
				comments[2],
			}),
			expErr: false,
		},
		{
			name: "bad comments with an invalid ElemIdx",
			job: &Job{
				Phase: 2,
				Ally: &Jobally{
					Elements: elems,
					Annotations: *serial.New(&anno.JobAnno{
						ElementAnnos: elemAnnos,
					}),
					Comments: nil,
				},
			},
			reqAnnos: nil,
			addComments: []*anno.AnnoComment{
				{
					Uuid:     "1",
					ElemIdx:  999,
					ObjUuids: []string{"car-1"},
					Scope:    anno.AnnoComment_Scope_object,
					Reasons: &anno.AnnoCommentReason_Reasons{
						Class:   commentClassFlawed,
						Reasons: []string{"off_center"},
					},
				},
			},
			resolveComments: nil,
			expResult:       ValidValue[Comments]{},
			expErr:          true,
		},
		{
			name: "bad comments without uuids",
			job: &Job{
				Phase: 2,
				Ally: &Jobally{
					Elements: elems,
					Annotations: *serial.New(&anno.JobAnno{
						ElementAnnos: elemAnnos,
					}),
					Comments: nil,
				},
			},
			reqAnnos: nil,
			addComments: []*anno.AnnoComment{
				{
					Uuid:     "",
					ObjUuids: []string{"car-1"},
					Scope:    anno.AnnoComment_Scope_object,
					Reasons: &anno.AnnoCommentReason_Reasons{
						Class:   commentClassFlawed,
						Reasons: []string{"off_center"},
					},
				},
			},
			resolveComments: nil,
			expResult:       ValidValue[Comments]{},
			expErr:          true,
		},
		{
			name: "comment to unknown objects",
			job: &Job{
				Phase: 2,
				Ally: &Jobally{
					Elements: elems,
					Annotations: *serial.New(&anno.JobAnno{
						ElementAnnos: elemAnnos,
					}),
					Comments: nil,
				},
			},
			reqAnnos: nil,
			addComments: []*anno.AnnoComment{
				{
					Uuid:     "1",
					ObjUuids: []string{"nonexisting"},
					Scope:    anno.AnnoComment_Scope_object,
					Reasons: &anno.AnnoCommentReason_Reasons{
						Class:   commentClassFlawed,
						Reasons: []string{"off_center"},
					},
				},
			},
			resolveComments: nil,
			expResult:       ValidValue[Comments]{},
			expErr:          true,
		},
		{
			name: "resolve ok",
			job: &Job{
				Phase: 2,
				Ally: &Jobally{
					Elements: elems,
					Annotations: *serial.New(&anno.JobAnno{
						ElementAnnos: elemAnnos,
					}),
					Comments: Comments{
						testCopyComments(comments[0], 2), // final resolve
						testCopyComments(comments[1], 3),
						testCopyComments(comments[2], 2), // unresolved
					},
				},
			},
			reqAnnos:    reqAnnos,
			addComments: nil,
			resolveComments: []*anno.ResolveAnnoComment{
				{Uuid: "1"},
				{Uuid: "2"},
			},
			expResult: NewValidValue[Comments](Comments{
				testResolve(testCopyComments(comments[1], 3), 2),
				testCopyComments(comments[2], 2),
			}),
			expErr: false,
		},
		{
			name: "resolve unknown comments",
			job: &Job{
				Phase: 2,
				Ally: &Jobally{
					Elements: elems,
					Annotations: *serial.New(&anno.JobAnno{
						ElementAnnos: elemAnnos,
					}),
					Comments: Comments{
						testCopyComments(comments[0], 2),
					},
				},
			},
			reqAnnos:    reqAnnos,
			addComments: nil,
			resolveComments: []*anno.ResolveAnnoComment{
				{Uuid: "nonexisting"},
			},
			expResult: ValidValue[Comments]{},
			expErr:    true,
		},
	}

	for _, c := range cases {
		bz := NewJobsBiz(nil, nil, nil, nil)
		res, err := bz.processComments(context.Background(), c.job, c.reqAnnos, c.resolveComments, c.addComments, "")
		assert.Equal(t, c.expErr, err != nil, c.name)
		assert.Equal(t, c.expResult, res, c.name)
	}
}

func testCopyComments(c *AnnoComment, addPhase int32) *AnnoComment {
	return &AnnoComment{
		Uuid:         c.Uuid,
		ObjUuids:     c.ObjUuids,
		Reasons:      c.Reasons,
		AddPhase:     addPhase,
		ResolvePhase: c.ResolvePhase,
		Scope:        c.Scope,
		ExtraInfo:    c.ExtraInfo,
		ElemIdx:      c.ElemIdx,
		RdIdx:        c.RdIdx,
	}
}

func testResolve(c *AnnoComment, resolvePhase int32) *AnnoComment {
	c.ResolvePhase = resolvePhase
	return c
}
