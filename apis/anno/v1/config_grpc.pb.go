// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: anno/v1/config.proto

package anno

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Configs_ListErrors_FullMethodName         = "/anno.v1.Configs/ListErrors"
	Configs_GetVersion_FullMethodName         = "/anno.v1.Configs/GetVersion"
	Configs_ListCommentReasons_FullMethodName = "/anno.v1.Configs/ListCommentReasons"
	Configs_PutCommentReasons_FullMethodName  = "/anno.v1.Configs/PutCommentReasons"
)

// ConfigsClient is the client API for Configs service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConfigsClient interface {
	// List Errors info
	ListErrors(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*Errors, error)
	GetVersion(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetVersionReply, error)
	// List comment reasons
	ListCommentReasons(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListCommentReasonsReply, error)
	// Modify comment reasons
	PutCommentReasons(ctx context.Context, in *PutCommentReasonsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type configsClient struct {
	cc grpc.ClientConnInterface
}

func NewConfigsClient(cc grpc.ClientConnInterface) ConfigsClient {
	return &configsClient{cc}
}

func (c *configsClient) ListErrors(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*Errors, error) {
	out := new(Errors)
	err := c.cc.Invoke(ctx, Configs_ListErrors_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configsClient) GetVersion(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetVersionReply, error) {
	out := new(GetVersionReply)
	err := c.cc.Invoke(ctx, Configs_GetVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configsClient) ListCommentReasons(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListCommentReasonsReply, error) {
	out := new(ListCommentReasonsReply)
	err := c.cc.Invoke(ctx, Configs_ListCommentReasons_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configsClient) PutCommentReasons(ctx context.Context, in *PutCommentReasonsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Configs_PutCommentReasons_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConfigsServer is the server API for Configs service.
// All implementations must embed UnimplementedConfigsServer
// for forward compatibility
type ConfigsServer interface {
	// List Errors info
	ListErrors(context.Context, *emptypb.Empty) (*Errors, error)
	GetVersion(context.Context, *emptypb.Empty) (*GetVersionReply, error)
	// List comment reasons
	ListCommentReasons(context.Context, *emptypb.Empty) (*ListCommentReasonsReply, error)
	// Modify comment reasons
	PutCommentReasons(context.Context, *PutCommentReasonsRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedConfigsServer()
}

// UnimplementedConfigsServer must be embedded to have forward compatible implementations.
type UnimplementedConfigsServer struct {
}

func (UnimplementedConfigsServer) ListErrors(context.Context, *emptypb.Empty) (*Errors, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListErrors not implemented")
}
func (UnimplementedConfigsServer) GetVersion(context.Context, *emptypb.Empty) (*GetVersionReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVersion not implemented")
}
func (UnimplementedConfigsServer) ListCommentReasons(context.Context, *emptypb.Empty) (*ListCommentReasonsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCommentReasons not implemented")
}
func (UnimplementedConfigsServer) PutCommentReasons(context.Context, *PutCommentReasonsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PutCommentReasons not implemented")
}
func (UnimplementedConfigsServer) mustEmbedUnimplementedConfigsServer() {}

// UnsafeConfigsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConfigsServer will
// result in compilation errors.
type UnsafeConfigsServer interface {
	mustEmbedUnimplementedConfigsServer()
}

func RegisterConfigsServer(s grpc.ServiceRegistrar, srv ConfigsServer) {
	s.RegisterService(&Configs_ServiceDesc, srv)
}

func _Configs_ListErrors_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigsServer).ListErrors(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Configs_ListErrors_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigsServer).ListErrors(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Configs_GetVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigsServer).GetVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Configs_GetVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigsServer).GetVersion(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Configs_ListCommentReasons_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigsServer).ListCommentReasons(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Configs_ListCommentReasons_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigsServer).ListCommentReasons(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Configs_PutCommentReasons_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutCommentReasonsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigsServer).PutCommentReasons(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Configs_PutCommentReasons_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigsServer).PutCommentReasons(ctx, req.(*PutCommentReasonsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Configs_ServiceDesc is the grpc.ServiceDesc for Configs service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Configs_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "anno.v1.Configs",
	HandlerType: (*ConfigsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListErrors",
			Handler:    _Configs_ListErrors_Handler,
		},
		{
			MethodName: "GetVersion",
			Handler:    _Configs_GetVersion_Handler,
		},
		{
			MethodName: "ListCommentReasons",
			Handler:    _Configs_ListCommentReasons_Handler,
		},
		{
			MethodName: "PutCommentReasons",
			Handler:    _Configs_PutCommentReasons_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "anno/v1/config.proto",
}
