language: go

go:
  - 1.6
  - 1.7
  - 1.8
  - tip

matrix:
  allow_failures:
    - go: tip

gobuild_args: -race

before_install:
  - if [[ $TRAVIS_GO_VERSION == 1.6* ]]; then go get -u github.com/kisielk/errcheck; fi
  - if [[ $TRAVIS_GO_VERSION == 1.6* ]]; then go get -u golang.org/x/lint/golint; fi

before_script:
  - '! gofmt -s -l . | read'
  - echo $TRAVIS_GO_VERSION
  - if [[ $TRAVIS_GO_VERSION == 1.6* ]]; then golint ./...; fi
  - if [[ $TRAVIS_GO_VERSION == 1.6* ]]; then errcheck ./...; fi
  - if [[ $TRAVIS_GO_VERSION == 1.6* ]]; then go tool vet .; fi
  - if [[ $TRAVIS_GO_VERSION == 1.6* ]]; then go tool vet --shadow .; fi

script:
  - go test -bench . -v ./...
  - go test -race -bench . -v ./...
