package preparedata

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"io"
	"os"
	"path"
	"path/filepath"
	"sync"

	"annofeed/api/client"
	"annofeed/internal/biz"
	"annofeed/internal/conf"
	"annofeed/internal/data/serial"
	"annofeed/workflow/common"
	commonpcd "annofeed/workflow/common/pointcloud"

	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kmath"
	"gitlab.rp.konvery.work/platform/pkg/s3"
	"gitlab.rp.konvery.work/platform/pkg/upload"
	"gitlab.rp.konvery.work/platform/pkg/wf/wfutil"
	"go.temporal.io/sdk/activity"
)

// with heartbeat
func (o *Activities) PrepareDataUploadRawdatas(ctx context.Context, basedir string, data *biz.Data) (err error) {
	fmt.Println("---> prepare data upload rawdatas")
	fmt.Println("---> before data size: ", data.Size)
	data, err = o.databiz.GetByID(ctx, data.ID)
	if err != nil {
		return fixError(err, "")
	}
	fmt.Println("---> after data size: ", data.Size)
	if data.BaseOn > 0 {
		return nil
	}

	elemInfo := &ElemInfo{IdxInClip: -1}
	startIdx := wfutil.GetActivtityStartIndex(ctx, elemInfo)
	maxUploader := conf.GetMaxUploader()
	uploadRawdataChan := make(chan UploadRawdata, maxUploader)
	var wg sync.WaitGroup
	progress := &UploadRawdataProgress{Elems: make(map[int]*ElemProgress, data.Size)}
	defer func() {
		close(uploadRawdataChan)
		wg.Wait()
		err = lo.Ternary(err == nil, progress.err, err)
	}()
	for i := 0; i < maxUploader; i++ {
		wg.Add(1)
		go o.uploadRawdataWorker(ctx, basedir, uploadRawdataChan, progress, &wg)
	}

	for i := startIdx; i < int(data.Size); i++ {
		rds, err := o.rawdatabiz.List(ctx, &biz.RawdataListFilter{
			DataID:      data.ID,
			FromElemIdx: int32(i),
			ElemCnt:     1,
		})
		if err != nil {
			return fmt.Errorf("failed to list rawdata %v-%v: %w", data.ID, i, err)
		}
		elemInfo.IdxInClip++
		if len(rds) == 0 {
			continue
		}
		if clip := path.Dir(rds[0].Folder); elemInfo.Clip != clip {
			elemInfo.Clip = clip
			elemInfo.IdxInClip = 0
		}
		progress.setElemInfo(i, len(rds), elemInfo)

		basekey := path.Join("data", data.CreatedAt.Format("06/0102"), data.GetUid())
		for _, rd := range rds {
			switch rd.Type {
			case biz.RawdataTypePointcloud:
				if data.Type == biz.DataTypeFusion4D && elemInfo.IdxInClip > 0 {
					// there is no pcd file in non-1st element of a clip
					progress.updateHeartbeat(ctx, rd, nil)
					continue
				}
			case biz.RawdataTypeImage:
			case biz.RawdataTypeMeta:
				if rd.Format != common.RawdataFormatFilelist {
					o.log.Warn(ctx, "rawdata meta is not filelist", "rawdata", rd.Name)
					progress.updateHeartbeat(ctx, rd, nil)
					continue
				}
			default:
				progress.updateHeartbeat(ctx, rd, nil)
				continue
			}

			// process rawdata
			uploadRawdataChan <- UploadRawdata{Rawdata: rd, Basekey: basekey}
		}
	}
	return
}

type UploadRawdata struct {
	Rawdata *biz.Rawdata
	Basekey string
}

func (o *Activities) uploadRawdataWorker(ctx context.Context, basedir string, uploadRawdataChan <-chan UploadRawdata,
	progress *UploadRawdataProgress, wg *sync.WaitGroup) {
	defer wg.Done()

	for rd := range uploadRawdataChan {
		if progress.getErr() != nil {
			continue
		}

		fieldsToUpdate, err := o.checkAndProcessRawdata(ctx, rd.Rawdata, basedir, rd.Basekey)
		if err == nil {
			_, err = o.rawdatabiz.Update(ctx, rd.Rawdata, field.NewMask(fieldsToUpdate...))
		}
		progress.updateHeartbeat(ctx, rd.Rawdata, err)
	}
}

type ElemProgress struct {
	RdLeft   int // number of rawdatas not uploaded yet
	ElemInfo *ElemInfo
}

type UploadRawdataProgress struct {
	mu            sync.RWMutex
	Elems         map[int]*ElemProgress // key is ElemIdxInData
	NextHeartbeat int                   // the minimum elem not uploaded yet
	err           error
}

func (o *UploadRawdataProgress) getErr() error {
	o.mu.RLock()
	defer o.mu.RUnlock()
	return o.err
}

func (o *UploadRawdataProgress) setElemInfo(elemIdx int, rawdataCount int, elemInfo *ElemInfo) {
	o.mu.Lock()
	defer o.mu.Unlock()

	o.Elems[elemIdx] = &ElemProgress{RdLeft: rawdataCount, ElemInfo: elemInfo}
}

// updateHeartbeat updates the progress if necessary after uploading a rawdata.
func (o *UploadRawdataProgress) updateHeartbeat(ctx context.Context, rd *biz.Rawdata, err error) {
	o.mu.Lock()
	defer o.mu.Unlock()

	if err != nil {
		o.err = err
		return
	}

	uploadedElemIdx := int(rd.ElemIdxInData)
	o.Elems[uploadedElemIdx].RdLeft -= 1
	if o.NextHeartbeat < uploadedElemIdx {
		return
	}
	i := o.NextHeartbeat
	for o.Elems[i] != nil && o.Elems[i].RdLeft == 0 {
		i++
	}
	if i > o.NextHeartbeat {
		o.NextHeartbeat = i
		e := o.Elems[i-1]
		activity.RecordHeartbeat(ctx, i-1, e.ElemInfo)
	}
}

type ElemInfo struct { // fields should be exported as it will be attached to heartbeat
	Clip      string
	IdxInClip int
}

func (o *Activities) checkAndProcessRawdata(ctx context.Context, rd *biz.Rawdata, basedir, basekey string) ([]string, error) {
	if rd.Type == biz.RawdataTypeMeta {
		for _, file := range rd.ExtraData.E.Metafiles {
			fpath := filepath.Join(basedir, common.DataDir, file.Name)
			key := path.Join(basekey, file.Name)
			resp, err := uploadRawdata(ctx, fpath, rd.Type, 0, key)
			if err != nil {
				return nil, err
			}
			file.URI = resp.URI
		}
		fieldsToUpdate := []string{biz.RawdataSfldExtraData}
		return fieldsToUpdate, nil
	}

	// rd itself has already been inserted to DB in `ParseRawdatas` phase
	// so we can query 2 rawdata to check whether to upload or not.
	rdmatch, err := o.rawdatabiz.List(ctx, &biz.RawdataListFilter{
		Sha256:        rd.Sha256,
		Count:         2,
		Type:          rd.Type,
		OrderByIDDesc: true,
	})
	if err != nil {
		return nil, err
	}
	for _, rdm := range rdmatch {
		if rdm.Size == rd.Size && rdm.URI != "" {
			rd.URI = rdm.URI
			rd.Format = rdm.Format
			rd.ExtraData = rdm.ExtraData
			break
		}
	}
	if rd.URI == "" {
		o.log.Debug(ctx, "uploading rawdata", "rawdata", rd.Name, "type", rd.Type)
		fpath := filepath.Join(basedir, common.DataDir, rd.Name)
		err := processAndUploadRawdata(ctx, rd, fpath, basekey)
		if err != nil {
			if errors.Is(err, os.ErrNotExist) {
				return nil, wfutil.NewNonRetryableError("failed to upload file", err)
			}
			return nil, fmt.Errorf("failed to upload file %v: %w", rd.Name, err)
		}
	}

	fieldsToUpdate := []string{biz.RawdataSfldURI}
	if rd.Format == common.RawdataFormatFilelist {
		fieldsToUpdate = append(fieldsToUpdate, biz.RawdataSfldFormat, biz.RawdataSfldExtraData)
	}
	return fieldsToUpdate, nil
}

func processAndUploadRawdata(ctx context.Context, rd *biz.Rawdata, fpath, basekey string) (err error) {
	key := path.Join(basekey, rd.Name)
	resp, err := uploadRawdata(ctx, fpath, rd.Type, rd.Size, key)
	if err != nil {
		return err
	}
	rd.URI = resp.URI

	var splitResults []*commonpcd.SplitResult
	if rd.Type == biz.RawdataTypePointcloud {
		splitResults, err = commonpcd.SplitPCD(fpath)
		if err != nil {
			return fmt.Errorf("failed to split pcd: %w", err)
		}
		if len(splitResults) > 0 {
			rd.Format = common.RawdataFormatFilelist
		}
	}

	rdDir := path.Dir(rd.Name)
	for _, result := range splitResults {
		pcd := result.Path
		key := path.Join(basekey, rdDir, path.Base(pcd))
		resp, err := uploadRawdata(ctx, pcd, biz.RawdataTypePointcloud, int64(result.Size), key)
		if err != nil {
			return err
		}

		if rd.ExtraData.E == nil {
			rd.ExtraData = *serial.New(&biz.ExtraData{})
		}
		rd.ExtraData.E.PCDSegments = append(rd.ExtraData.E.PCDSegments, &biz.PCDSegment{
			Points: int64(result.Points),
			Size:   int64(result.Size),
			URI:    resp.URI,
		})

		if err := os.RemoveAll(pcd); err != nil {
			return fmt.Errorf("failed to remove pcd: %w", err)
		}
	}

	return nil
}

func uploadRawdata(ctx context.Context, fpath, rdType string, rdSize int64, key string) (*upload.UploadResult, error) {
	f, err := os.Open(fpath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer f.Close() // try to close the file

	var reader io.Reader = f
	ops := &upload.Options{Size: rdSize}
	if rdType == biz.RawdataTypePointcloud {
		ops.ContentType = "application/octet-stream"
		ops.ContentEncoding = "gzip"
		reader, err = s3.ReaderToGzipReader(reader, int(ops.Size))
		if err != nil {
			return nil, err
		}
	}

	resp, err := upload.Upload(ctx, key, reader, ops)
	return resp, err
}

func (o *Activities) PrepareDataWrapup(ctx context.Context, basedir string, data *biz.Data, errMsg string) (err error) {
	data, err = o.databiz.GetByID(ctx, data.ID)
	if err != nil {
		return fixError(err, "")
	}
	if basedir != "" {
		if err := os.RemoveAll(basedir); err != nil {
			o.log.Error(ctx, "failed to remove dir", err, "dir", basedir)
		}
	}

	// update data and order state
	orderState := anno.Order_State_waiting
	data.State = biz.DataStateReady
	data.Error = ""
	if errMsg != "" {
		orderState = anno.Order_State_failed
		data.State = biz.DataStateFailed
		data.Error = errMsg[:kmath.Min(len(errMsg), 512)]
	}
	spew.Dump(data)
	_, err = o.databiz.Update(ctx, data, field.NewMask(biz.DataSfldSize.String(), biz.DataSfldState.String(), biz.DataSfldError.String()))
	if err != nil {
		if errors.IsNotFound(err) {
			err = wfutil.NewNonRetryableError("data not found", err)
		} else {
			return fmt.Errorf("failed to update data %s: %w", data.GetUid(), err)
		}
	}

	if data.OrderUid != "" {
		fmt.Println("---> data order uid: ", data.OrderUid, data.Size)
		_, errOrder := client.UpdateOrderState(client.NewCtxUseSvcAccount(ctx), data.OrderUid, orderState, data.Size, data.Error)
		if errOrder != nil {
			if !errors.IsNotFound(errOrder) {
				return fmt.Errorf("failed to UpdateOrderState: %w", errOrder)
			}
		}
	}
	return
}
