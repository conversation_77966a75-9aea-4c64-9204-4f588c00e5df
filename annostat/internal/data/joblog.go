// source: anno/v1/job.proto
package data

import (
	"context"

	"annostat/internal/biz"

	"gitlab.rp.konvery.work/platform/pkg/data"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type joblogsRepo struct {
	repo.GenericRepo[biz.Joblog]
	data *data.Data
}

func NewJoblogsRepo(d *Data, logger log.Logger) biz.JoblogsRepo {
	return &joblogsRepo{
		data:        d,
		GenericRepo: data.NewGenericRepo[biz.Joblog](d, logger, biz.JoblogTableName),
	}
}

func (o *joblogsRepo) GetPhaseLastExecutor(ctx context.Context, jobID int64, phase int) (user, team string, err error) {
	data := &biz.Joblog{}
	q := o.data.WithCtx(ctx).
		Where(biz.JoblogSfldJobID+" = ?", jobID).
		Where(biz.JoblogSfldToPhase+" = ?", phase).
		Where(biz.JoblogSfldToExecutorUid + " <> ''")
	err = q.Last(data).Error
	if err != nil {
		return "", "", Convert(data, err)
	}
	return data.ToExecutorUid, data.ToExecteam, nil
}

func (o *joblogsRepo) GetPhaseLastSubmitter(ctx context.Context, jobID int64, phase int) (user, team string, err error) {
	data := &biz.Joblog{}
	q := o.data.WithCtx(ctx).
		Where(biz.JoblogSfldJobID+" = ?", jobID).
		Where(biz.JoblogSfldFromPhase+" = ?", phase).
		Where(biz.JoblogSfldOperatorUid + " <> ''")
	err = q.Last(data).Error
	if err != nil {
		return "", "", Convert(data, err)
	}
	return data.OperatorUid, data.OpOrgUid, nil
}
