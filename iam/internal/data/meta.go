package data

import (
	"context"

	"iam/internal/biz"

	"gitlab.rp.konvery.work/platform/pkg/data"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"gorm.io/gorm/clause"
)

type metasRepo struct {
	repo.GenericRepo[biz.Meta]
	data *Data
}

func NewMetasRepo(d *Data, logger log.Logger) biz.MetasRepo {
	return &metasRepo{GenericRepo: data.NewGenericRepo[biz.Meta](d, logger, biz.MetaTableName), data: d}
}

func (o *metasRepo) Get(ctx context.Context, name string) (string, error) {
	m, err := data.GetByUid[biz.Meta](ctx, o.data, name)
	if err != nil {
		if errors.IsNotFound(err) {
			err = nil
		}
		return "", err
	}

	return m.Value, nil
}

func (o *metasRepo) Is(ctx context.Context, name, value string) (bool, error) {
	v, err := o.Get(ctx, name)
	if err != nil {
		return false, err
	}
	return v == value, nil
}

func (o *metasRepo) Set(ctx context.Context, name, value string) error {
	err := o.data.WithCtx(ctx).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: biz.Meta_Name}},
			DoUpdates: clause.AssignmentColumns([]string{biz.Meta_Value}),
		}).
		Create(&biz.Meta{Name: name, Value: value}).Error
	return Convert(&biz.Meta{}, err)
}

func (o *metasRepo) Delete(ctx context.Context, name string) error {
	return data.Delete(ctx, o.data, &biz.Meta{Name: name})
}
