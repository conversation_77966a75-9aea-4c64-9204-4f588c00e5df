package model

import (
	"testing"

	"gitlab.rp.konvery.work/platform/apis/model/v1"
)

// ------ Run Test ------

func Test_Run(t *testing.T) {
	req := &model.CreateRunRequest{
		Name: "train od",
		Type: "od",
		Dataset: &model.Run_Dataset{
			Uris: []string{"s3://bucket/key"},
		},
	}

	cases := []struct {
		name       string
		newReqFunc func(*model.CreateRunRequest) *model.CreateRunRequest
		expectErr  bool
	}{
		{
			name:       "happy_flow",
			newReqFunc: nil,
			expectErr:  false,
		},
		{
			name: "bad_url_in_Datasets",
			newReqFunc: func(req *model.CreateRunRequest) *model.CreateRunRequest {
				req.Dataset.Uris[0] = "foo\nbar"
				return req
			},
			expectErr: true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			newReq := *req
			req := &newReq
			if c.newReqFunc != nil {
				req = c.newReqFunc(req)
			}
			if err := req.ValidateAll(); (err != nil) != c.expectErr {
				t.Errorf("ValidateAll() expect error: %v, got: %v", c.expectErr, err)
			}
		})
	}
}

// ------ Model Test ------

func Test_Model(t *testing.T) {
	req := &model.CreateModelRequest{
		Name:    "train od",
		Type:    "od",
		Uri:     "s3://bucket/key",
		Version: "v1.0.0",
	}

	cases := []struct {
		name       string
		newReqFunc func(*model.CreateModelRequest) *model.CreateModelRequest
		expectErr  bool
	}{
		{
			name:       "happy_flow",
			newReqFunc: nil,
			expectErr:  false,
		},
		{
			name: "bad_uri_in_model",
			newReqFunc: func(req *model.CreateModelRequest) *model.CreateModelRequest {
				req.Uri = "foo\nbar"
				return req
			},
			expectErr: true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			newReq := *req
			req := &newReq
			if c.newReqFunc != nil {
				req = c.newReqFunc(req)
			}
			if err := req.ValidateAll(); (err != nil) != c.expectErr {
				t.Errorf("ValidateAll() expect error: %v, got: %v", c.expectErr, err)
			}
		})
	}
}
