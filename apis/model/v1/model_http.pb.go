// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: model/v1/model.proto

package model

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	types "gitlab.rp.konvery.work/platform/apis/types"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationModelsAddTag = "/model.v1.Models/AddTag"
const OperationModelsCreate = "/model.v1.Models/Create"
const OperationModelsDelete = "/model.v1.Models/Delete"
const OperationModelsDeleteTag = "/model.v1.Models/DeleteTag"
const OperationModelsGet = "/model.v1.Models/Get"
const OperationModelsList = "/model.v1.Models/List"
const OperationModelsUpdate = "/model.v1.Models/Update"

type ModelsHTTPServer interface {
	AddTag(context.Context, *types.TagRequest) (*types.TagList, error)
	Create(context.Context, *CreateModelRequest) (*Model, error)
	Delete(context.Context, *DeleteModelRequest) (*emptypb.Empty, error)
	DeleteTag(context.Context, *types.TagRequest) (*types.TagList, error)
	Get(context.Context, *GetModelRequest) (*Model, error)
	List(context.Context, *ListModelRequest) (*ListModelReply, error)
	Update(context.Context, *UpdateModelRequest) (*Model, error)
}

func RegisterModelsHTTPServer(s *http.Server, srv ModelsHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/models", _Models_Create1_HTTP_Handler(srv))
	r.PATCH("/v1/models/{uid}", _Models_Update1_HTTP_Handler(srv))
	r.DELETE("/v1/models/{uid}", _Models_Delete1_HTTP_Handler(srv))
	r.GET("/v1/models/{uid}", _Models_Get1_HTTP_Handler(srv))
	r.GET("/v1/models", _Models_List1_HTTP_Handler(srv))
	r.PUT("/v1/models/{uid}/tag", _Models_AddTag1_HTTP_Handler(srv))
	r.DELETE("/v1/models/{uid}/tag", _Models_DeleteTag1_HTTP_Handler(srv))
}

func _Models_Create1_HTTP_Handler(srv ModelsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateModelRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelsCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Create(ctx, req.(*CreateModelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Model)
		return ctx.Result(200, reply)
	}
}

func _Models_Update1_HTTP_Handler(srv ModelsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateModelRequest
		if err := ctx.Bind(&in.Data); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelsUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Update(ctx, req.(*UpdateModelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Model)
		return ctx.Result(200, reply)
	}
}

func _Models_Delete1_HTTP_Handler(srv ModelsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteModelRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelsDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Delete(ctx, req.(*DeleteModelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Models_Get1_HTTP_Handler(srv ModelsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetModelRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelsGet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Get(ctx, req.(*GetModelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Model)
		return ctx.Result(200, reply)
	}
}

func _Models_List1_HTTP_Handler(srv ModelsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListModelRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelsList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.List(ctx, req.(*ListModelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListModelReply)
		return ctx.Result(200, reply)
	}
}

func _Models_AddTag1_HTTP_Handler(srv ModelsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in types.TagRequest
		if err := ctx.Bind(&in.Tags); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelsAddTag)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddTag(ctx, req.(*types.TagRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*types.TagList)
		return ctx.Result(200, reply)
	}
}

func _Models_DeleteTag1_HTTP_Handler(srv ModelsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in types.TagRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationModelsDeleteTag)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteTag(ctx, req.(*types.TagRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*types.TagList)
		return ctx.Result(200, reply)
	}
}

type ModelsHTTPClient interface {
	AddTag(ctx context.Context, req *types.TagRequest, opts ...http.CallOption) (rsp *types.TagList, err error)
	Create(ctx context.Context, req *CreateModelRequest, opts ...http.CallOption) (rsp *Model, err error)
	Delete(ctx context.Context, req *DeleteModelRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteTag(ctx context.Context, req *types.TagRequest, opts ...http.CallOption) (rsp *types.TagList, err error)
	Get(ctx context.Context, req *GetModelRequest, opts ...http.CallOption) (rsp *Model, err error)
	List(ctx context.Context, req *ListModelRequest, opts ...http.CallOption) (rsp *ListModelReply, err error)
	Update(ctx context.Context, req *UpdateModelRequest, opts ...http.CallOption) (rsp *Model, err error)
}

type ModelsHTTPClientImpl struct {
	cc *http.Client
}

func NewModelsHTTPClient(client *http.Client) ModelsHTTPClient {
	return &ModelsHTTPClientImpl{client}
}

func (c *ModelsHTTPClientImpl) AddTag(ctx context.Context, in *types.TagRequest, opts ...http.CallOption) (*types.TagList, error) {
	var out types.TagList
	pattern := "/v1/models/{uid}/tag"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelsAddTag))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in.Tags, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelsHTTPClientImpl) Create(ctx context.Context, in *CreateModelRequest, opts ...http.CallOption) (*Model, error) {
	var out Model
	pattern := "/v1/models"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelsCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelsHTTPClientImpl) Delete(ctx context.Context, in *DeleteModelRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/models/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelsDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelsHTTPClientImpl) DeleteTag(ctx context.Context, in *types.TagRequest, opts ...http.CallOption) (*types.TagList, error) {
	var out types.TagList
	pattern := "/v1/models/{uid}/tag"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelsDeleteTag))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelsHTTPClientImpl) Get(ctx context.Context, in *GetModelRequest, opts ...http.CallOption) (*Model, error) {
	var out Model
	pattern := "/v1/models/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelsGet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelsHTTPClientImpl) List(ctx context.Context, in *ListModelRequest, opts ...http.CallOption) (*ListModelReply, error) {
	var out ListModelReply
	pattern := "/v1/models"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationModelsList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ModelsHTTPClientImpl) Update(ctx context.Context, in *UpdateModelRequest, opts ...http.CallOption) (*Model, error) {
	var out Model
	pattern := "/v1/models/{uid}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationModelsUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in.Data, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
