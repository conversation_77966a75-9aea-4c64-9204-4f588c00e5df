import os
import shutil
import glob
import zipfile
import json
import sys

sys.path.append('.')
from customer.common import tools


label_map = {
    'unlabeled': 0,
    'ground': 1,
    'person': 2,
    'loader': 3,
    'self': 4,
    'materialA': 5,
    'materialB': 6,
    'dumper': 7,
    'obstacle': 8,
    'other': 9,
    'highwall': 10,
    '': 0
}


def run(input_dir):
    line_counter = 0

    # unzip file
    unzip_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_unzip')
    result_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_result')
    if os.path.exists(unzip_dir):
        shutil.rmtree(unzip_dir)
    with zipfile.ZipFile(input_dir, 'r') as zip_ref:
        zip_ref.extractall(unzip_dir)

    try:
        os.mkdir(result_dir)
    except FileExistsError:
        shutil.rmtree(result_dir)
        os.mkdir(result_dir)

    for json_dir in glob.glob(os.path.join(unzip_dir, '**', '*.json'), recursive=True):
        if os.path.basename(json_dir) != 'config.json':
            file_name = os.path.basename(json_dir)
            result_list = []
            # load json file
            json_data = json.load(open(json_dir))

            # get the data to be modified
            json_result = json_data['segmentation']['result']
            for record in json_result[0]:
                result_list.append(label_map[record])

            # save the result
            with open(os.path.join(result_dir, file_name.split('.json')[0] + '.label'), 'w') as f:
                for result in result_list:
                    f.write(f"{result}\n")

    trans_result = {
        "code": 0,
        "output": result_dir,
        "err_msg": '成功',
        "summary": {
            '写入行数': line_counter
        }
    }

    for key in trans_result:
        print(key + ': ' + str(trans_result[key]))

    print(trans_result)
    return trans_result


if __name__ == "__main__":
    args = tools.get_args()
    run(args.input)

    # args_input = '/Users/<USER>/Downloads/seg.zip'
    # run(args_input)