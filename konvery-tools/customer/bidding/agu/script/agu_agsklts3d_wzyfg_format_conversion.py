import os
import glob
import json
import shutil
import zipfile
import sys

sys.path.append('.')
from customer.common import tools


result_map = {
    'unlabeled': '0',
    'unknown': '1',
    'stockpile': '2',
    'truck': '3',
    'hopper': '4',
    'boom': '5',
    'stick': '6',
    'bucket': '7',
    'person': '8',
    'earth_in_hopper': '9',
    'earth_in_bucket': '10',
    'carport': '11',
}


def run(input_dir):
    line_counter = 0

    # unzip file
    unzip_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_unzip')
    result_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_result')
    if os.path.exists(unzip_dir):
        shutil.rmtree(unzip_dir)
    with zipfile.ZipFile(input_dir, 'r') as zip_ref:
        zip_ref.extractall(unzip_dir)

    try:
        os.mkdir(result_dir)
    except FileExistsError:
        shutil.rmtree(result_dir)
        os.mkdir(result_dir)

    for json_dir in glob.glob(os.path.join(unzip_dir, '**', '*.json'), recursive=True):
        if os.path.basename(os.path.dirname(json_dir)) == 'label':
            file_name = os.path.basename(json_dir)
            json_data = json.load(open(json_dir))
            result_list = json_data['segmentation']['result'][0]

            for index, item in enumerate(result_list):
                if item != '':
                    err_file = json_dir
                    err_line = index
                    err_content = item
                    result_list[index] = result_map[item]
                else:
                    result_list[index] = '0'

            with open(os.path.join(result_dir, file_name.split('.json')[0] + '.label'), 'w') as file:
                for line in result_list[:-1]:
                    # file.write(line + '\n')
                    print(line, end='\n', file=file)
                print(result_list[-1], end='', file=file)
            line_counter += 1

    trans_result = {
        "code": 0,
        "output": result_dir,
        "err_msg": '成功',
        "summary": {
            '写入行数': line_counter
        }
    }

    for key in trans_result:
        print(key + ': ' + str(trans_result[key]))

    print(trans_result)
    return trans_result


if __name__ == "__main__":
    args = tools.get_args()
    run(args.input)

    # args_input = '/Users/<USER>/Downloads/AGU-3D封闭场景分割-室外2_231215a0445_验收_已通过_原文件带JSON标注结果文件_20231228_6227.zip'
    # run(args_input)
