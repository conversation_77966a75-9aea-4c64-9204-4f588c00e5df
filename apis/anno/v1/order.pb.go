// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anno/v1/order.proto

package anno

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	v1 "gitlab.rp.konvery.work/platform/apis/iam/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Source_ParseErrorHandler_Error_Enum int32

const (
	Source_ParseErrorHandler_Error_unspecified  Source_ParseErrorHandler_Error_Enum = 0
	Source_ParseErrorHandler_Error_file_unknown Source_ParseErrorHandler_Error_Enum = 1
	Source_ParseErrorHandler_Error_file_missing Source_ParseErrorHandler_Error_Enum = 2
)

// Enum value maps for Source_ParseErrorHandler_Error_Enum.
var (
	Source_ParseErrorHandler_Error_Enum_name = map[int32]string{
		0: "unspecified",
		1: "file_unknown",
		2: "file_missing",
	}
	Source_ParseErrorHandler_Error_Enum_value = map[string]int32{
		"unspecified":  0,
		"file_unknown": 1,
		"file_missing": 2,
	}
)

func (x Source_ParseErrorHandler_Error_Enum) Enum() *Source_ParseErrorHandler_Error_Enum {
	p := new(Source_ParseErrorHandler_Error_Enum)
	*p = x
	return p
}

func (x Source_ParseErrorHandler_Error_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Source_ParseErrorHandler_Error_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_order_proto_enumTypes[0].Descriptor()
}

func (Source_ParseErrorHandler_Error_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_order_proto_enumTypes[0]
}

func (x Source_ParseErrorHandler_Error_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Source_ParseErrorHandler_Error_Enum.Descriptor instead.
func (Source_ParseErrorHandler_Error_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{0, 1, 0, 0}
}

type Source_ParseErrorHandler_Handler_Enum int32

const (
	Source_ParseErrorHandler_Handler_unspecified Source_ParseErrorHandler_Handler_Enum = 0
	Source_ParseErrorHandler_Handler_fail        Source_ParseErrorHandler_Handler_Enum = 1 // if a rawdata like image has error, fail the total data workflow
	Source_ParseErrorHandler_Handler_ignore      Source_ParseErrorHandler_Handler_Enum = 2 // if a rawdata like image has error, just ignore this error
)

// Enum value maps for Source_ParseErrorHandler_Handler_Enum.
var (
	Source_ParseErrorHandler_Handler_Enum_name = map[int32]string{
		0: "unspecified",
		1: "fail",
		2: "ignore",
	}
	Source_ParseErrorHandler_Handler_Enum_value = map[string]int32{
		"unspecified": 0,
		"fail":        1,
		"ignore":      2,
	}
)

func (x Source_ParseErrorHandler_Handler_Enum) Enum() *Source_ParseErrorHandler_Handler_Enum {
	p := new(Source_ParseErrorHandler_Handler_Enum)
	*p = x
	return p
}

func (x Source_ParseErrorHandler_Handler_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Source_ParseErrorHandler_Handler_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_order_proto_enumTypes[1].Descriptor()
}

func (Source_ParseErrorHandler_Handler_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_order_proto_enumTypes[1]
}

func (x Source_ParseErrorHandler_Handler_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Source_ParseErrorHandler_Handler_Enum.Descriptor instead.
func (Source_ParseErrorHandler_Handler_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{0, 1, 1, 0}
}

type Order_State_Enum int32

const (
	Order_State_unspecified Order_State_Enum = 0
	// parsing the data specified in the source
	Order_State_initializing Order_State_Enum = 1
	// waiting for the order related lot to be started
	Order_State_waiting  Order_State_Enum = 2
	Order_State_ongoing  Order_State_Enum = 3
	Order_State_finished Order_State_Enum = 4
	Order_State_canceled Order_State_Enum = 5
	// error occurred when parsing data specified by the source
	Order_State_failed Order_State_Enum = 6
)

// Enum value maps for Order_State_Enum.
var (
	Order_State_Enum_name = map[int32]string{
		0: "unspecified",
		1: "initializing",
		2: "waiting",
		3: "ongoing",
		4: "finished",
		5: "canceled",
		6: "failed",
	}
	Order_State_Enum_value = map[string]int32{
		"unspecified":  0,
		"initializing": 1,
		"waiting":      2,
		"ongoing":      3,
		"finished":     4,
		"canceled":     5,
		"failed":       6,
	}
)

func (x Order_State_Enum) Enum() *Order_State_Enum {
	p := new(Order_State_Enum)
	*p = x
	return p
}

func (x Order_State_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Order_State_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_order_proto_enumTypes[2].Descriptor()
}

func (Order_State_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_order_proto_enumTypes[2]
}

func (x Order_State_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Order_State_Enum.Descriptor instead.
func (Order_State_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{7, 0, 0}
}

type ExportOrderAnnosRequest_Option_Enum int32

const (
	ExportOrderAnnosRequest_Option_unspecified ExportOrderAnnosRequest_Option_Enum = 0 // all anno results
	ExportOrderAnnosRequest_Option_finished    ExportOrderAnnosRequest_Option_Enum = 1 // anno result of jobs that have been accepted by customer
)

// Enum value maps for ExportOrderAnnosRequest_Option_Enum.
var (
	ExportOrderAnnosRequest_Option_Enum_name = map[int32]string{
		0: "unspecified",
		1: "finished",
	}
	ExportOrderAnnosRequest_Option_Enum_value = map[string]int32{
		"unspecified": 0,
		"finished":    1,
	}
)

func (x ExportOrderAnnosRequest_Option_Enum) Enum() *ExportOrderAnnosRequest_Option_Enum {
	p := new(ExportOrderAnnosRequest_Option_Enum)
	*p = x
	return p
}

func (x ExportOrderAnnosRequest_Option_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExportOrderAnnosRequest_Option_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_order_proto_enumTypes[3].Descriptor()
}

func (ExportOrderAnnosRequest_Option_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_order_proto_enumTypes[3]
}

func (x ExportOrderAnnosRequest_Option_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExportOrderAnnosRequest_Option_Enum.Descriptor instead.
func (ExportOrderAnnosRequest_Option_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{11, 0, 0}
}

type Source struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// package file (.zip) URIs
	Uris []string `protobuf:"bytes,1,rep,name=uris,proto3" json:"uris,omitempty"`
	// access config when the files are hosted in a third-party platform
	Proprietary *Source_Proprietary `protobuf:"bytes,2,opt,name=proprietary,proto3" json:"proprietary,omitempty"`
	// folder layout style within package files if not conform to Konvery standard
	Style string `protobuf:"bytes,3,opt,name=style,proto3" json:"style,omitempty"`
	// element type
	ElemType Element_Type_Enum `protobuf:"varint,4,opt,name=elem_type,json=elemType,proto3,enum=anno.v1.Element_Type_Enum" json:"elem_type,omitempty"`
	// if source contains consecutive frames
	IsFrameSeries bool `protobuf:"varint,5,opt,name=is_frame_series,json=isFrameSeries,proto3" json:"is_frame_series,omitempty"`
	// size of the unpacked data in GB
	PlainSizeGb int32 `protobuf:"varint,6,opt,name=plain_size_gb,json=plainSizeGb,proto3" json:"plain_size_gb,omitempty"`
	// define parser error handlers; it will fail the parser if no handler is specified.
	// max size is count(RawdataErrorAction.Error) x count(Rawdata.Type).
	ErrorHandlers []*Source_ParseErrorHandler `protobuf:"bytes,7,rep,name=error_handlers,json=errorHandlers,proto3" json:"error_handlers,omitempty"`
	// whether to automatically parse data in annofeed service
	AutoParse bool `protobuf:"varint,8,opt,name=auto_parse,json=autoParse,proto3" json:"auto_parse,omitempty"`
	// define single file names and their corresponding expected names;
	NamedUris map[string]string `protobuf:"bytes,9,rep,name=named_uris,json=namedUris,proto3" json:"named_uris,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// converter is a piece of script to convert the source data to the platform accepted format
	Converter *DataConverter `protobuf:"bytes,10,opt,name=converter,proto3" json:"converter,omitempty"`
	// metadata about original data, which might be used in parsing data and exporting annos
	Metadata map[string]string `protobuf:"bytes,11,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Source) Reset() {
	*x = Source{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Source) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source) ProtoMessage() {}

func (x *Source) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source.ProtoReflect.Descriptor instead.
func (*Source) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{0}
}

func (x *Source) GetUris() []string {
	if x != nil {
		return x.Uris
	}
	return nil
}

func (x *Source) GetProprietary() *Source_Proprietary {
	if x != nil {
		return x.Proprietary
	}
	return nil
}

func (x *Source) GetStyle() string {
	if x != nil {
		return x.Style
	}
	return ""
}

func (x *Source) GetElemType() Element_Type_Enum {
	if x != nil {
		return x.ElemType
	}
	return Element_Type_unspecified
}

func (x *Source) GetIsFrameSeries() bool {
	if x != nil {
		return x.IsFrameSeries
	}
	return false
}

func (x *Source) GetPlainSizeGb() int32 {
	if x != nil {
		return x.PlainSizeGb
	}
	return 0
}

func (x *Source) GetErrorHandlers() []*Source_ParseErrorHandler {
	if x != nil {
		return x.ErrorHandlers
	}
	return nil
}

func (x *Source) GetAutoParse() bool {
	if x != nil {
		return x.AutoParse
	}
	return false
}

func (x *Source) GetNamedUris() map[string]string {
	if x != nil {
		return x.NamedUris
	}
	return nil
}

func (x *Source) GetConverter() *DataConverter {
	if x != nil {
		return x.Converter
	}
	return nil
}

func (x *Source) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type CreateOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory in update-requests
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// mandatory in create-requests;
	// pattern: ^[\\p{Han}\\w\\d_-]{0,256}$
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// order's organization; if empty, this is the requestor's organization
	OrgUid string `protobuf:"bytes,3,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// files attached to the order
	Source *Source `protobuf:"bytes,4,opt,name=source,proto3" json:"source,omitempty"`
}

func (x *CreateOrderRequest) Reset() {
	*x = CreateOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderRequest) ProtoMessage() {}

func (x *CreateOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateOrderRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{1}
}

func (x *CreateOrderRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *CreateOrderRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateOrderRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *CreateOrderRequest) GetSource() *Source {
	if x != nil {
		return x.Source
	}
	return nil
}

type UpdateOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update contents
	Order *Order `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
	// name of fileds to be updated
	Fields []string `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *UpdateOrderRequest) Reset() {
	*x = UpdateOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrderRequest) ProtoMessage() {}

func (x *UpdateOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrderRequest.ProtoReflect.Descriptor instead.
func (*UpdateOrderRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateOrderRequest) GetOrder() *Order {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *UpdateOrderRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type DeleteOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *DeleteOrderRequest) Reset() {
	*x = DeleteOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteOrderRequest) ProtoMessage() {}

func (x *DeleteOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteOrderRequest.ProtoReflect.Descriptor instead.
func (*DeleteOrderRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteOrderRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *GetOrderRequest) Reset() {
	*x = GetOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderRequest) ProtoMessage() {}

func (x *GetOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderRequest.ProtoReflect.Descriptor instead.
func (*GetOrderRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{4}
}

func (x *GetOrderRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type ListOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page   int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Pagesz int32 `protobuf:"varint,2,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// filter by orgnization
	OrgUid string `protobuf:"bytes,3,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// filter by creator
	CreatorUid string `protobuf:"bytes,4,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	// filter by name pattern
	NamePattern string `protobuf:"bytes,5,opt,name=name_pattern,json=namePattern,proto3" json:"name_pattern,omitempty"`
	// filter by order state
	States []Order_State_Enum `protobuf:"varint,6,rep,packed,name=states,proto3,enum=anno.v1.Order_State_Enum" json:"states,omitempty"`
	// include order's organization in the reply
	WithOrg bool `protobuf:"varint,7,opt,name=with_org,json=withOrg,proto3" json:"with_org,omitempty"`
}

func (x *ListOrderRequest) Reset() {
	*x = ListOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrderRequest) ProtoMessage() {}

func (x *ListOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrderRequest.ProtoReflect.Descriptor instead.
func (*ListOrderRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{5}
}

func (x *ListOrderRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListOrderRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListOrderRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *ListOrderRequest) GetCreatorUid() string {
	if x != nil {
		return x.CreatorUid
	}
	return ""
}

func (x *ListOrderRequest) GetNamePattern() string {
	if x != nil {
		return x.NamePattern
	}
	return ""
}

func (x *ListOrderRequest) GetStates() []Order_State_Enum {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *ListOrderRequest) GetWithOrg() bool {
	if x != nil {
		return x.WithOrg
	}
	return false
}

type ListOrderReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total  int32    `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Orders []*Order `protobuf:"bytes,2,rep,name=orders,proto3" json:"orders,omitempty"`
	// the organization that the order, at the corresponding position, belongs to.
	Orgs []*v1.BaseUser `protobuf:"bytes,3,rep,name=orgs,proto3" json:"orgs,omitempty"`
}

func (x *ListOrderReply) Reset() {
	*x = ListOrderReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrderReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrderReply) ProtoMessage() {}

func (x *ListOrderReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrderReply.ProtoReflect.Descriptor instead.
func (*ListOrderReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{6}
}

func (x *ListOrderReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListOrderReply) GetOrders() []*Order {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ListOrderReply) GetOrgs() []*v1.BaseUser {
	if x != nil {
		return x.Orgs
	}
	return nil
}

type Order struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// order name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// UID of the organization which the order belongs to
	OrgUid string `protobuf:"bytes,3,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// files attached to the order
	Source *Source `protobuf:"bytes,4,opt,name=source,proto3" json:"source,omitempty"`
	// UID of the data associated with the order
	DataUid string `protobuf:"bytes,5,opt,name=data_uid,json=dataUid,proto3" json:"data_uid,omitempty"`
	// creator UID
	CreatorUid string `protobuf:"bytes,6,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	// number of elements in the order
	Size int32 `protobuf:"varint,10,opt,name=size,proto3" json:"size,omitempty"`
	// order state
	State Order_State_Enum `protobuf:"varint,11,opt,name=state,proto3,enum=anno.v1.Order_State_Enum" json:"state,omitempty"`
	// annotated object count (include interpolated ones); only available after lot is finished
	InsTotal int32 `protobuf:"varint,12,opt,name=ins_total,json=insTotal,proto3" json:"ins_total,omitempty"`
	// annotation result file URL
	AnnoResultUrl string `protobuf:"bytes,13,opt,name=anno_result_url,json=annoResultUrl,proto3" json:"anno_result_url,omitempty"`
	// when state is failed, this field will contain detailed error message
	Error string `protobuf:"bytes,14,opt,name=error,proto3" json:"error,omitempty"`
	// order creation time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// data validation summary
	DataSummary *DataValidationSummary `protobuf:"bytes,16,opt,name=data_summary,json=dataSummary,proto3" json:"data_summary,omitempty"`
	// whether the demander can export annos:
	// 1) if false, anno_result_url will be always empty and the demander cannot export annos;
	// 2) if true,
	// 2.1) if anno_result_url is not empty, the demander can use it to download anno result;
	// 2.2) otherwise, the demander can export annos;
	CanExportAnnos bool `protobuf:"varint,17,opt,name=can_export_annos,json=canExportAnnos,proto3" json:"can_export_annos,omitempty"`
}

func (x *Order) Reset() {
	*x = Order{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Order) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Order) ProtoMessage() {}

func (x *Order) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Order.ProtoReflect.Descriptor instead.
func (*Order) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{7}
}

func (x *Order) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Order) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Order) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *Order) GetSource() *Source {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *Order) GetDataUid() string {
	if x != nil {
		return x.DataUid
	}
	return ""
}

func (x *Order) GetCreatorUid() string {
	if x != nil {
		return x.CreatorUid
	}
	return ""
}

func (x *Order) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Order) GetState() Order_State_Enum {
	if x != nil {
		return x.State
	}
	return Order_State_unspecified
}

func (x *Order) GetInsTotal() int32 {
	if x != nil {
		return x.InsTotal
	}
	return 0
}

func (x *Order) GetAnnoResultUrl() string {
	if x != nil {
		return x.AnnoResultUrl
	}
	return ""
}

func (x *Order) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *Order) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Order) GetDataSummary() *DataValidationSummary {
	if x != nil {
		return x.DataSummary
	}
	return nil
}

func (x *Order) GetCanExportAnnos() bool {
	if x != nil {
		return x.CanExportAnnos
	}
	return false
}

type SetOrderAnnoResultRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// URL to the result
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *SetOrderAnnoResultRequest) Reset() {
	*x = SetOrderAnnoResultRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetOrderAnnoResultRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetOrderAnnoResultRequest) ProtoMessage() {}

func (x *SetOrderAnnoResultRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetOrderAnnoResultRequest.ProtoReflect.Descriptor instead.
func (*SetOrderAnnoResultRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{8}
}

func (x *SetOrderAnnoResultRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *SetOrderAnnoResultRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type GetOrderAnnoResultReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the estimated time when the result is ready;
	// valid only when the order is in the finished state
	WillReadyAt *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=will_ready_at,json=willReadyAt,proto3" json:"will_ready_at,omitempty"`
	// URL to the result if available
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *GetOrderAnnoResultReply) Reset() {
	*x = GetOrderAnnoResultReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderAnnoResultReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderAnnoResultReply) ProtoMessage() {}

func (x *GetOrderAnnoResultReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderAnnoResultReply.ProtoReflect.Descriptor instead.
func (*GetOrderAnnoResultReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{9}
}

func (x *GetOrderAnnoResultReply) GetWillReadyAt() *timestamppb.Timestamp {
	if x != nil {
		return x.WillReadyAt
	}
	return nil
}

func (x *GetOrderAnnoResultReply) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type DataValidationSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of validation errors found, includes those unsaved errors
	TotalErrors int32 `protobuf:"varint,1,opt,name=total_errors,json=totalErrors,proto3" json:"total_errors,omitempty"`
	// validation errors; only the first 10 errors are saved
	Errors []*DataValidationSummary_Error `protobuf:"bytes,2,rep,name=errors,proto3" json:"errors,omitempty"`
}

func (x *DataValidationSummary) Reset() {
	*x = DataValidationSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataValidationSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataValidationSummary) ProtoMessage() {}

func (x *DataValidationSummary) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataValidationSummary.ProtoReflect.Descriptor instead.
func (*DataValidationSummary) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{10}
}

func (x *DataValidationSummary) GetTotalErrors() int32 {
	if x != nil {
		return x.TotalErrors
	}
	return 0
}

func (x *DataValidationSummary) GetErrors() []*DataValidationSummary_Error {
	if x != nil {
		return x.Errors
	}
	return nil
}

type ExportOrderAnnosRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// option
	Option ExportOrderAnnosRequest_Option_Enum `protobuf:"varint,2,opt,name=option,proto3,enum=anno.v1.ExportOrderAnnosRequest_Option_Enum" json:"option,omitempty"`
}

func (x *ExportOrderAnnosRequest) Reset() {
	*x = ExportOrderAnnosRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportOrderAnnosRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportOrderAnnosRequest) ProtoMessage() {}

func (x *ExportOrderAnnosRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportOrderAnnosRequest.ProtoReflect.Descriptor instead.
func (*ExportOrderAnnosRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{11}
}

func (x *ExportOrderAnnosRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ExportOrderAnnosRequest) GetOption() ExportOrderAnnosRequest_Option_Enum {
	if x != nil {
		return x.Option
	}
	return ExportOrderAnnosRequest_Option_unspecified
}

type Source_Proprietary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 3rd-party file host service type
	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	// 3rd-party file host service access config
	Config string `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *Source_Proprietary) Reset() {
	*x = Source_Proprietary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Source_Proprietary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source_Proprietary) ProtoMessage() {}

func (x *Source_Proprietary) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source_Proprietary.ProtoReflect.Descriptor instead.
func (*Source_Proprietary) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Source_Proprietary) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Source_Proprietary) GetConfig() string {
	if x != nil {
		return x.Config
	}
	return ""
}

type Source_ParseErrorHandler struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawdataType Rawdata_Type_Enum                     `protobuf:"varint,1,opt,name=rawdata_type,json=rawdataType,proto3,enum=anno.v1.Rawdata_Type_Enum" json:"rawdata_type,omitempty"` // image or pcd
	Error       Source_ParseErrorHandler_Error_Enum   `protobuf:"varint,2,opt,name=error,proto3,enum=anno.v1.Source_ParseErrorHandler_Error_Enum" json:"error,omitempty"`
	Handler     Source_ParseErrorHandler_Handler_Enum `protobuf:"varint,3,opt,name=handler,proto3,enum=anno.v1.Source_ParseErrorHandler_Handler_Enum" json:"handler,omitempty"`
}

func (x *Source_ParseErrorHandler) Reset() {
	*x = Source_ParseErrorHandler{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Source_ParseErrorHandler) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source_ParseErrorHandler) ProtoMessage() {}

func (x *Source_ParseErrorHandler) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source_ParseErrorHandler.ProtoReflect.Descriptor instead.
func (*Source_ParseErrorHandler) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Source_ParseErrorHandler) GetRawdataType() Rawdata_Type_Enum {
	if x != nil {
		return x.RawdataType
	}
	return Rawdata_Type_unspecified
}

func (x *Source_ParseErrorHandler) GetError() Source_ParseErrorHandler_Error_Enum {
	if x != nil {
		return x.Error
	}
	return Source_ParseErrorHandler_Error_unspecified
}

func (x *Source_ParseErrorHandler) GetHandler() Source_ParseErrorHandler_Handler_Enum {
	if x != nil {
		return x.Handler
	}
	return Source_ParseErrorHandler_Handler_unspecified
}

type Source_ParseErrorHandler_Error struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Source_ParseErrorHandler_Error) Reset() {
	*x = Source_ParseErrorHandler_Error{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Source_ParseErrorHandler_Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source_ParseErrorHandler_Error) ProtoMessage() {}

func (x *Source_ParseErrorHandler_Error) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source_ParseErrorHandler_Error.ProtoReflect.Descriptor instead.
func (*Source_ParseErrorHandler_Error) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{0, 1, 0}
}

type Source_ParseErrorHandler_Handler struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Source_ParseErrorHandler_Handler) Reset() {
	*x = Source_ParseErrorHandler_Handler{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Source_ParseErrorHandler_Handler) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source_ParseErrorHandler_Handler) ProtoMessage() {}

func (x *Source_ParseErrorHandler_Handler) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source_ParseErrorHandler_Handler.ProtoReflect.Descriptor instead.
func (*Source_ParseErrorHandler_Handler) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{0, 1, 1}
}

type Order_State struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Order_State) Reset() {
	*x = Order_State{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Order_State) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Order_State) ProtoMessage() {}

func (x *Order_State) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Order_State.ProtoReflect.Descriptor instead.
func (*Order_State) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{7, 0}
}

type DataValidationSummary_Error struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Error       Source_ParseErrorHandler_Error_Enum `protobuf:"varint,1,opt,name=error,proto3,enum=anno.v1.Source_ParseErrorHandler_Error_Enum" json:"error,omitempty"`
	ElemIndex   int32                               `protobuf:"varint,2,opt,name=elem_index,json=elemIndex,proto3" json:"elem_index,omitempty"`
	RawdataName string                              `protobuf:"bytes,3,opt,name=rawdata_name,json=rawdataName,proto3" json:"rawdata_name,omitempty"`
}

func (x *DataValidationSummary_Error) Reset() {
	*x = DataValidationSummary_Error{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataValidationSummary_Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataValidationSummary_Error) ProtoMessage() {}

func (x *DataValidationSummary_Error) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataValidationSummary_Error.ProtoReflect.Descriptor instead.
func (*DataValidationSummary_Error) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{10, 0}
}

func (x *DataValidationSummary_Error) GetError() Source_ParseErrorHandler_Error_Enum {
	if x != nil {
		return x.Error
	}
	return Source_ParseErrorHandler_Error_unspecified
}

func (x *DataValidationSummary_Error) GetElemIndex() int32 {
	if x != nil {
		return x.ElemIndex
	}
	return 0
}

func (x *DataValidationSummary_Error) GetRawdataName() string {
	if x != nil {
		return x.RawdataName
	}
	return ""
}

type ExportOrderAnnosRequest_Option struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ExportOrderAnnosRequest_Option) Reset() {
	*x = ExportOrderAnnosRequest_Option{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_order_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportOrderAnnosRequest_Option) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportOrderAnnosRequest_Option) ProtoMessage() {}

func (x *ExportOrderAnnosRequest_Option) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_order_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportOrderAnnosRequest_Option.ProtoReflect.Descriptor instead.
func (*ExportOrderAnnosRequest_Option) Descriptor() ([]byte, []int) {
	return file_anno_v1_order_proto_rawDescGZIP(), []int{11, 0}
}

var File_anno_v1_order_proto protoreflect.FileDescriptor

var file_anno_v1_order_proto_rawDesc = []byte{
	0x0a, 0x13, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62,
	0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x11, 0x69, 0x61, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x65,
	0x6c, 0x65, 0x6d, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61,
	0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x6f, 0x74, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc9, 0x08, 0x0a, 0x06,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x72, 0x69, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x75, 0x72, 0x69, 0x73, 0x12, 0x3d, 0x0a, 0x0b, 0x70, 0x72,
	0x6f, 0x70, 0x72, 0x69, 0x65, 0x74, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x50, 0x72, 0x6f, 0x70, 0x72, 0x69, 0x65, 0x74, 0x61, 0x72, 0x79, 0x52, 0x0b, 0x70, 0x72,
	0x6f, 0x70, 0x72, 0x69, 0x65, 0x74, 0x61, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x79,
	0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x12,
	0x37, 0x0a, 0x09, 0x65, 0x6c, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08,
	0x65, 0x6c, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x66,
	0x72, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0d, 0x69, 0x73, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x12, 0x22, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x67,
	0x62, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x53, 0x69,
	0x7a, 0x65, 0x47, 0x62, 0x12, 0x48, 0x0a, 0x0e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x68, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x50, 0x61,
	0x72, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x52,
	0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x70, 0x61, 0x72, 0x73, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x61, 0x75, 0x74, 0x6f, 0x50, 0x61, 0x72, 0x73, 0x65, 0x12, 0x3d, 0x0a,
	0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x64, 0x5f, 0x75, 0x72, 0x69, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x55, 0x72, 0x69, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x64, 0x55, 0x72, 0x69, 0x73, 0x12, 0x34, 0x0a, 0x09,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x74, 0x65, 0x72, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74,
	0x65, 0x72, 0x12, 0x39, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0b,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x4e, 0x0a,
	0x0b, 0x50, 0x72, 0x6f, 0x70, 0x72, 0x69, 0x65, 0x74, 0x61, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x3a, 0x13, 0xba, 0x47, 0x10, 0xba, 0x01, 0x04,
	0x74, 0x79, 0x70, 0x65, 0xba, 0x01, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0xe0, 0x02,
	0x0a, 0x11, 0x50, 0x61, 0x72, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x72, 0x12, 0x3d, 0x0a, 0x0c, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x79, 0x70, 0x65,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0b, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x42, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x72, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x48, 0x0a, 0x07, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x65, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x72, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x07, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72,
	0x1a, 0x44, 0x0a, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x3b, 0x0a, 0x04, 0x45, 0x6e, 0x75,
	0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x6e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6e, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x10, 0x02, 0x1a, 0x38, 0x0a, 0x07, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x72, 0x22, 0x2d, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x66, 0x61,
	0x69, 0x6c, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x10, 0x02,
	0x1a, 0x3c, 0x0a, 0x0e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x55, 0x72, 0x69, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3b,
	0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x3a, 0x0a, 0xba, 0x47, 0x07,
	0xba, 0x01, 0x04, 0x75, 0x72, 0x69, 0x73, 0x22, 0xa2, 0x01, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x12, 0x33, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1f,
	0xfa, 0x42, 0x1c, 0x72, 0x1a, 0x32, 0x18, 0x5e, 0x5b, 0x5c, 0x70, 0x7b, 0x48, 0x61, 0x6e, 0x7d,
	0x5c, 0x77, 0x5c, 0x64, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x32, 0x35, 0x36, 0x7d, 0x24, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64, 0x12, 0x27,
	0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x3a, 0x03, 0xba, 0x47, 0x00, 0x22, 0x68, 0x0a, 0x12,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x24, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73,
	0x3a, 0x14, 0xba, 0x47, 0x11, 0xba, 0x01, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0xba, 0x01, 0x06,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22, 0x26, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x23,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x22, 0x85, 0x02, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x06,
	0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x12,
	0x17, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x61, 0x6d,
	0x65, 0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x42, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73,
	0x12, 0x19, 0x0a, 0x08, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x6f, 0x72, 0x67, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x77, 0x69, 0x74, 0x68, 0x4f, 0x72, 0x67, 0x22, 0x74, 0x0a, 0x0e, 0x4c,
	0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x12, 0x26, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x24, 0x0a, 0x04, 0x6f,
	0x72, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x6f, 0x72, 0x67,
	0x73, 0x22, 0xae, 0x05, 0x0a, 0x05, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x55, 0x69, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x39, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x69, 0x6e, 0x73, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x61,
	0x6e, 0x6e, 0x6f, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x6e, 0x6e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x46, 0x0a, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52,
	0x0b, 0x64, 0x61, 0x74, 0x61, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x2d, 0x0a, 0x10,
	0x63, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x73,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0e, 0x63, 0x61, 0x6e,
	0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x1a, 0x74, 0x0a, 0x05, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x22, 0x6b, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b,
	0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x10, 0x0a,
	0x0c, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12,
	0x0b, 0x0a, 0x07, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07,
	0x6f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x66, 0x69, 0x6e,
	0x69, 0x73, 0x68, 0x65, 0x64, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x63, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x65, 0x64, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10,
	0x06, 0x3a, 0x2f, 0xba, 0x47, 0x2c, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0xba, 0x01, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0xba, 0x01, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0xba, 0x01, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x22, 0x49, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x41, 0x6e,
	0x6e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69,
	0x64, 0x12, 0x1a, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x72, 0x03, 0x90, 0x01, 0x01, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x6b, 0x0a,
	0x17, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3e, 0x0a, 0x0d, 0x77, 0x69, 0x6c, 0x6c,
	0x5f, 0x72, 0x65, 0x61, 0x64, 0x79, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x77, 0x69, 0x6c,
	0x6c, 0x52, 0x65, 0x61, 0x64, 0x79, 0x41, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x88, 0x02, 0x0a, 0x15, 0x44,
	0x61, 0x74, 0x61, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x3c, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x06, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x1a, 0x8d, 0x01, 0x0a, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12,
	0x42, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e,
	0x50, 0x61, 0x72, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x72, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6c, 0x65, 0x6d, 0x5f, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x6c, 0x65, 0x6d, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74,
	0x61, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xad, 0x01, 0x0a, 0x17, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x12, 0x44, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6e, 0x75,
	0x6d, 0x52, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x2f, 0x0a, 0x06, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x25, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08,
	0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x10, 0x01, 0x3a, 0x09, 0xba, 0x47, 0x06, 0xba,
	0x01, 0x03, 0x75, 0x69, 0x64, 0x32, 0x87, 0x07, 0x0a, 0x06, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x12, 0x51, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12,
	0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0e, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x15, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x0f, 0x3a, 0x01, 0x2a, 0x22, 0x0a, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x12, 0x61, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x12, 0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x0e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x22,
	0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x32, 0x16,
	0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x63, 0x0a, 0x0b, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a,
	0x01, 0x2a, 0x1a, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x7b,
	0x75, 0x69, 0x64, 0x7d, 0x2f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x12, 0x5c, 0x0a, 0x0b, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x1b, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x2a, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x4e, 0x0a, 0x08, 0x47, 0x65, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x0e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x22,
	0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x53, 0x0a, 0x09, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x17, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x12, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x0c, 0x12, 0x0a, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x74,
	0x0a, 0x0d, 0x53, 0x65, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x22, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x1a, 0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x2d, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x71, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x20, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x12, 0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x2d, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x76, 0x0a, 0x10, 0x45, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x12, 0x20, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a,
	0x1a, 0x1d, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x75, 0x69,
	0x64, 0x7d, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x2d, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x42,
	0x3e, 0x0a, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x31, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e,
	0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e, 0x6e, 0x6f, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_anno_v1_order_proto_rawDescOnce sync.Once
	file_anno_v1_order_proto_rawDescData = file_anno_v1_order_proto_rawDesc
)

func file_anno_v1_order_proto_rawDescGZIP() []byte {
	file_anno_v1_order_proto_rawDescOnce.Do(func() {
		file_anno_v1_order_proto_rawDescData = protoimpl.X.CompressGZIP(file_anno_v1_order_proto_rawDescData)
	})
	return file_anno_v1_order_proto_rawDescData
}

var file_anno_v1_order_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_anno_v1_order_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_anno_v1_order_proto_goTypes = []interface{}{
	(Source_ParseErrorHandler_Error_Enum)(0),   // 0: anno.v1.Source.ParseErrorHandler.Error.Enum
	(Source_ParseErrorHandler_Handler_Enum)(0), // 1: anno.v1.Source.ParseErrorHandler.Handler.Enum
	(Order_State_Enum)(0),                      // 2: anno.v1.Order.State.Enum
	(ExportOrderAnnosRequest_Option_Enum)(0),   // 3: anno.v1.ExportOrderAnnosRequest.Option.Enum
	(*Source)(nil),                             // 4: anno.v1.Source
	(*CreateOrderRequest)(nil),                 // 5: anno.v1.CreateOrderRequest
	(*UpdateOrderRequest)(nil),                 // 6: anno.v1.UpdateOrderRequest
	(*DeleteOrderRequest)(nil),                 // 7: anno.v1.DeleteOrderRequest
	(*GetOrderRequest)(nil),                    // 8: anno.v1.GetOrderRequest
	(*ListOrderRequest)(nil),                   // 9: anno.v1.ListOrderRequest
	(*ListOrderReply)(nil),                     // 10: anno.v1.ListOrderReply
	(*Order)(nil),                              // 11: anno.v1.Order
	(*SetOrderAnnoResultRequest)(nil),          // 12: anno.v1.SetOrderAnnoResultRequest
	(*GetOrderAnnoResultReply)(nil),            // 13: anno.v1.GetOrderAnnoResultReply
	(*DataValidationSummary)(nil),              // 14: anno.v1.DataValidationSummary
	(*ExportOrderAnnosRequest)(nil),            // 15: anno.v1.ExportOrderAnnosRequest
	(*Source_Proprietary)(nil),                 // 16: anno.v1.Source.Proprietary
	(*Source_ParseErrorHandler)(nil),           // 17: anno.v1.Source.ParseErrorHandler
	nil,                                        // 18: anno.v1.Source.NamedUrisEntry
	nil,                                        // 19: anno.v1.Source.MetadataEntry
	(*Source_ParseErrorHandler_Error)(nil),     // 20: anno.v1.Source.ParseErrorHandler.Error
	(*Source_ParseErrorHandler_Handler)(nil),   // 21: anno.v1.Source.ParseErrorHandler.Handler
	(*Order_State)(nil),                        // 22: anno.v1.Order.State
	(*DataValidationSummary_Error)(nil),        // 23: anno.v1.DataValidationSummary.Error
	(*ExportOrderAnnosRequest_Option)(nil),     // 24: anno.v1.ExportOrderAnnosRequest.Option
	(Element_Type_Enum)(0),                     // 25: anno.v1.Element.Type.Enum
	(*DataConverter)(nil),                      // 26: anno.v1.DataConverter
	(*v1.BaseUser)(nil),                        // 27: iam.v1.BaseUser
	(*timestamppb.Timestamp)(nil),              // 28: google.protobuf.Timestamp
	(Rawdata_Type_Enum)(0),                     // 29: anno.v1.Rawdata.Type.Enum
	(*emptypb.Empty)(nil),                      // 30: google.protobuf.Empty
}
var file_anno_v1_order_proto_depIdxs = []int32{
	16, // 0: anno.v1.Source.proprietary:type_name -> anno.v1.Source.Proprietary
	25, // 1: anno.v1.Source.elem_type:type_name -> anno.v1.Element.Type.Enum
	17, // 2: anno.v1.Source.error_handlers:type_name -> anno.v1.Source.ParseErrorHandler
	18, // 3: anno.v1.Source.named_uris:type_name -> anno.v1.Source.NamedUrisEntry
	26, // 4: anno.v1.Source.converter:type_name -> anno.v1.DataConverter
	19, // 5: anno.v1.Source.metadata:type_name -> anno.v1.Source.MetadataEntry
	4,  // 6: anno.v1.CreateOrderRequest.source:type_name -> anno.v1.Source
	11, // 7: anno.v1.UpdateOrderRequest.order:type_name -> anno.v1.Order
	2,  // 8: anno.v1.ListOrderRequest.states:type_name -> anno.v1.Order.State.Enum
	11, // 9: anno.v1.ListOrderReply.orders:type_name -> anno.v1.Order
	27, // 10: anno.v1.ListOrderReply.orgs:type_name -> iam.v1.BaseUser
	4,  // 11: anno.v1.Order.source:type_name -> anno.v1.Source
	2,  // 12: anno.v1.Order.state:type_name -> anno.v1.Order.State.Enum
	28, // 13: anno.v1.Order.created_at:type_name -> google.protobuf.Timestamp
	14, // 14: anno.v1.Order.data_summary:type_name -> anno.v1.DataValidationSummary
	28, // 15: anno.v1.GetOrderAnnoResultReply.will_ready_at:type_name -> google.protobuf.Timestamp
	23, // 16: anno.v1.DataValidationSummary.errors:type_name -> anno.v1.DataValidationSummary.Error
	3,  // 17: anno.v1.ExportOrderAnnosRequest.option:type_name -> anno.v1.ExportOrderAnnosRequest.Option.Enum
	29, // 18: anno.v1.Source.ParseErrorHandler.rawdata_type:type_name -> anno.v1.Rawdata.Type.Enum
	0,  // 19: anno.v1.Source.ParseErrorHandler.error:type_name -> anno.v1.Source.ParseErrorHandler.Error.Enum
	1,  // 20: anno.v1.Source.ParseErrorHandler.handler:type_name -> anno.v1.Source.ParseErrorHandler.Handler.Enum
	0,  // 21: anno.v1.DataValidationSummary.Error.error:type_name -> anno.v1.Source.ParseErrorHandler.Error.Enum
	5,  // 22: anno.v1.Orders.CreateOrder:input_type -> anno.v1.CreateOrderRequest
	6,  // 23: anno.v1.Orders.UpdateOrder:input_type -> anno.v1.UpdateOrderRequest
	8,  // 24: anno.v1.Orders.CancelOrder:input_type -> anno.v1.GetOrderRequest
	7,  // 25: anno.v1.Orders.DeleteOrder:input_type -> anno.v1.DeleteOrderRequest
	8,  // 26: anno.v1.Orders.GetOrder:input_type -> anno.v1.GetOrderRequest
	9,  // 27: anno.v1.Orders.ListOrder:input_type -> anno.v1.ListOrderRequest
	12, // 28: anno.v1.Orders.SetAnnoResult:input_type -> anno.v1.SetOrderAnnoResultRequest
	8,  // 29: anno.v1.Orders.GetAnnoResult:input_type -> anno.v1.GetOrderRequest
	15, // 30: anno.v1.Orders.ExportOrderAnnos:input_type -> anno.v1.ExportOrderAnnosRequest
	11, // 31: anno.v1.Orders.CreateOrder:output_type -> anno.v1.Order
	11, // 32: anno.v1.Orders.UpdateOrder:output_type -> anno.v1.Order
	30, // 33: anno.v1.Orders.CancelOrder:output_type -> google.protobuf.Empty
	30, // 34: anno.v1.Orders.DeleteOrder:output_type -> google.protobuf.Empty
	11, // 35: anno.v1.Orders.GetOrder:output_type -> anno.v1.Order
	10, // 36: anno.v1.Orders.ListOrder:output_type -> anno.v1.ListOrderReply
	30, // 37: anno.v1.Orders.SetAnnoResult:output_type -> google.protobuf.Empty
	13, // 38: anno.v1.Orders.GetAnnoResult:output_type -> anno.v1.GetOrderAnnoResultReply
	30, // 39: anno.v1.Orders.ExportOrderAnnos:output_type -> google.protobuf.Empty
	31, // [31:40] is the sub-list for method output_type
	22, // [22:31] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_anno_v1_order_proto_init() }
func file_anno_v1_order_proto_init() {
	if File_anno_v1_order_proto != nil {
		return
	}
	file_anno_v1_elemanno_proto_init()
	file_anno_v1_type_lotconfig_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_anno_v1_order_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Source); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrderReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Order); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetOrderAnnoResultRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderAnnoResultReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataValidationSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportOrderAnnosRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Source_Proprietary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Source_ParseErrorHandler); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Source_ParseErrorHandler_Error); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Source_ParseErrorHandler_Handler); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Order_State); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataValidationSummary_Error); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_order_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportOrderAnnosRequest_Option); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anno_v1_order_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_anno_v1_order_proto_goTypes,
		DependencyIndexes: file_anno_v1_order_proto_depIdxs,
		EnumInfos:         file_anno_v1_order_proto_enumTypes,
		MessageInfos:      file_anno_v1_order_proto_msgTypes,
	}.Build()
	File_anno_v1_order_proto = out.File
	file_anno_v1_order_proto_rawDesc = nil
	file_anno_v1_order_proto_goTypes = nil
	file_anno_v1_order_proto_depIdxs = nil
}
