// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anno/v1/skill.proto

package anno

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Skill_Type_Enum int32

const (
	Skill_Type_unspecified Skill_Type_Enum = 0
	Skill_Type_task_type   Skill_Type_Enum = 1
	Skill_Type_label_class Skill_Type_Enum = 2
	Skill_Type_widget      Skill_Type_Enum = 3
)

// Enum value maps for Skill_Type_Enum.
var (
	Skill_Type_Enum_name = map[int32]string{
		0: "unspecified",
		1: "task_type",
		2: "label_class",
		3: "widget",
	}
	Skill_Type_Enum_value = map[string]int32{
		"unspecified": 0,
		"task_type":   1,
		"label_class": 2,
		"widget":      3,
	}
)

func (x Skill_Type_Enum) Enum() *Skill_Type_Enum {
	p := new(Skill_Type_Enum)
	*p = x
	return p
}

func (x Skill_Type_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Skill_Type_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_skill_proto_enumTypes[0].Descriptor()
}

func (Skill_Type_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_skill_proto_enumTypes[0]
}

func (x Skill_Type_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Skill_Type_Enum.Descriptor instead.
func (Skill_Type_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{5, 0, 0}
}

type UpdateSkillRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Skill  *Skill   `protobuf:"bytes,1,opt,name=skill,proto3" json:"skill,omitempty"`
	Fields []string `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *UpdateSkillRequest) Reset() {
	*x = UpdateSkillRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_skill_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSkillRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSkillRequest) ProtoMessage() {}

func (x *UpdateSkillRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_skill_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSkillRequest.ProtoReflect.Descriptor instead.
func (*UpdateSkillRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateSkillRequest) GetSkill() *Skill {
	if x != nil {
		return x.Skill
	}
	return nil
}

func (x *UpdateSkillRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type DeleteSkillRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteSkillRequest) Reset() {
	*x = DeleteSkillRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_skill_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSkillRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSkillRequest) ProtoMessage() {}

func (x *DeleteSkillRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_skill_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSkillRequest.ProtoReflect.Descriptor instead.
func (*DeleteSkillRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{1}
}

func (x *DeleteSkillRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetSkillRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetSkillRequest) Reset() {
	*x = GetSkillRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_skill_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSkillRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSkillRequest) ProtoMessage() {}

func (x *GetSkillRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_skill_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSkillRequest.ProtoReflect.Descriptor instead.
func (*GetSkillRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{2}
}

func (x *GetSkillRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListSkillRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page        int32           `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Pagesz      int32           `protobuf:"varint,2,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	Type        Skill_Type_Enum `protobuf:"varint,3,opt,name=type,proto3,enum=anno.v1.Skill_Type_Enum" json:"type,omitempty"`
	NamePattern string          `protobuf:"bytes,4,opt,name=name_pattern,json=namePattern,proto3" json:"name_pattern,omitempty"`
}

func (x *ListSkillRequest) Reset() {
	*x = ListSkillRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_skill_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSkillRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSkillRequest) ProtoMessage() {}

func (x *ListSkillRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_skill_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSkillRequest.ProtoReflect.Descriptor instead.
func (*ListSkillRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{3}
}

func (x *ListSkillRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListSkillRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListSkillRequest) GetType() Skill_Type_Enum {
	if x != nil {
		return x.Type
	}
	return Skill_Type_unspecified
}

func (x *ListSkillRequest) GetNamePattern() string {
	if x != nil {
		return x.NamePattern
	}
	return ""
}

type ListSkillReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total  int32    `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Skills []*Skill `protobuf:"bytes,2,rep,name=skills,proto3" json:"skills,omitempty"`
}

func (x *ListSkillReply) Reset() {
	*x = ListSkillReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_skill_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSkillReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSkillReply) ProtoMessage() {}

func (x *ListSkillReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_skill_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSkillReply.ProtoReflect.Descriptor instead.
func (*ListSkillReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{4}
}

func (x *ListSkillReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListSkillReply) GetSkills() []*Skill {
	if x != nil {
		return x.Skills
	}
	return nil
}

type Skill struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type     Skill_Type_Enum `protobuf:"varint,1,opt,name=type,proto3,enum=anno.v1.Skill_Type_Enum" json:"type,omitempty"`
	Name     string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	MaxLevel int32           `protobuf:"varint,3,opt,name=max_level,json=maxLevel,proto3" json:"max_level,omitempty"`
	// language => name
	Langs map[string]string `protobuf:"bytes,4,rep,name=langs,proto3" json:"langs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Skill) Reset() {
	*x = Skill{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_skill_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Skill) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Skill) ProtoMessage() {}

func (x *Skill) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_skill_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Skill.ProtoReflect.Descriptor instead.
func (*Skill) Descriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{5}
}

func (x *Skill) GetType() Skill_Type_Enum {
	if x != nil {
		return x.Type
	}
	return Skill_Type_unspecified
}

func (x *Skill) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Skill) GetMaxLevel() int32 {
	if x != nil {
		return x.MaxLevel
	}
	return 0
}

func (x *Skill) GetLangs() map[string]string {
	if x != nil {
		return x.Langs
	}
	return nil
}

type UserSkill struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user/team uid
	Uid   string          `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Type  Skill_Type_Enum `protobuf:"varint,2,opt,name=type,proto3,enum=anno.v1.Skill_Type_Enum" json:"type,omitempty"`
	Name  string          `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Level int32           `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`
}

func (x *UserSkill) Reset() {
	*x = UserSkill{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_skill_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserSkill) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSkill) ProtoMessage() {}

func (x *UserSkill) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_skill_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSkill.ProtoReflect.Descriptor instead.
func (*UserSkill) Descriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{6}
}

func (x *UserSkill) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *UserSkill) GetType() Skill_Type_Enum {
	if x != nil {
		return x.Type
	}
	return Skill_Type_unspecified
}

func (x *UserSkill) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserSkill) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type GetUserSkillRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user uid
	Uid   string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Scope *Scope `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`
}

func (x *GetUserSkillRequest) Reset() {
	*x = GetUserSkillRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_skill_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserSkillRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSkillRequest) ProtoMessage() {}

func (x *GetUserSkillRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_skill_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSkillRequest.ProtoReflect.Descriptor instead.
func (*GetUserSkillRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{7}
}

func (x *GetUserSkillRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *GetUserSkillRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

type GetUserSkillReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Skills []*Skill `protobuf:"bytes,1,rep,name=skills,proto3" json:"skills,omitempty"`
}

func (x *GetUserSkillReply) Reset() {
	*x = GetUserSkillReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_skill_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserSkillReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSkillReply) ProtoMessage() {}

func (x *GetUserSkillReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_skill_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSkillReply.ProtoReflect.Descriptor instead.
func (*GetUserSkillReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{8}
}

func (x *GetUserSkillReply) GetSkills() []*Skill {
	if x != nil {
		return x.Skills
	}
	return nil
}

type ListUsersSkillRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Scope    *Scope   `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	TeamUid  string   `protobuf:"bytes,2,opt,name=team_uid,json=teamUid,proto3" json:"team_uid,omitempty"`
	UserUids []string `protobuf:"bytes,3,rep,name=user_uids,json=userUids,proto3" json:"user_uids,omitempty"`
	Skills   []*Skill `protobuf:"bytes,4,rep,name=skills,proto3" json:"skills,omitempty"`
}

func (x *ListUsersSkillRequest) Reset() {
	*x = ListUsersSkillRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_skill_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUsersSkillRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUsersSkillRequest) ProtoMessage() {}

func (x *ListUsersSkillRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_skill_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUsersSkillRequest.ProtoReflect.Descriptor instead.
func (*ListUsersSkillRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{9}
}

func (x *ListUsersSkillRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *ListUsersSkillRequest) GetTeamUid() string {
	if x != nil {
		return x.TeamUid
	}
	return ""
}

func (x *ListUsersSkillRequest) GetUserUids() []string {
	if x != nil {
		return x.UserUids
	}
	return nil
}

func (x *ListUsersSkillRequest) GetSkills() []*Skill {
	if x != nil {
		return x.Skills
	}
	return nil
}

type ListUsersSkillReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users []*UserSkill `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *ListUsersSkillReply) Reset() {
	*x = ListUsersSkillReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_skill_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUsersSkillReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUsersSkillReply) ProtoMessage() {}

func (x *ListUsersSkillReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_skill_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUsersSkillReply.ProtoReflect.Descriptor instead.
func (*ListUsersSkillReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{10}
}

func (x *ListUsersSkillReply) GetUsers() []*UserSkill {
	if x != nil {
		return x.Users
	}
	return nil
}

type AddUsersSkillRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Scope  *Scope       `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	Skills []*UserSkill `protobuf:"bytes,2,rep,name=skills,proto3" json:"skills,omitempty"`
}

func (x *AddUsersSkillRequest) Reset() {
	*x = AddUsersSkillRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_skill_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUsersSkillRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUsersSkillRequest) ProtoMessage() {}

func (x *AddUsersSkillRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_skill_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUsersSkillRequest.ProtoReflect.Descriptor instead.
func (*AddUsersSkillRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{11}
}

func (x *AddUsersSkillRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *AddUsersSkillRequest) GetSkills() []*UserSkill {
	if x != nil {
		return x.Skills
	}
	return nil
}

type DeleteUsersSkillRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Scope *Scope                               `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	Users []*DeleteUsersSkillRequest_UserSkill `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *DeleteUsersSkillRequest) Reset() {
	*x = DeleteUsersSkillRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_skill_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteUsersSkillRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUsersSkillRequest) ProtoMessage() {}

func (x *DeleteUsersSkillRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_skill_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUsersSkillRequest.ProtoReflect.Descriptor instead.
func (*DeleteUsersSkillRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteUsersSkillRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *DeleteUsersSkillRequest) GetUsers() []*DeleteUsersSkillRequest_UserSkill {
	if x != nil {
		return x.Users
	}
	return nil
}

type AddTeamSkillRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// team uid
	Uid    string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Scope  *Scope   `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`
	Skills []*Skill `protobuf:"bytes,3,rep,name=skills,proto3" json:"skills,omitempty"`
}

func (x *AddTeamSkillRequest) Reset() {
	*x = AddTeamSkillRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_skill_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddTeamSkillRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTeamSkillRequest) ProtoMessage() {}

func (x *AddTeamSkillRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_skill_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTeamSkillRequest.ProtoReflect.Descriptor instead.
func (*AddTeamSkillRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{13}
}

func (x *AddTeamSkillRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *AddTeamSkillRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *AddTeamSkillRequest) GetSkills() []*Skill {
	if x != nil {
		return x.Skills
	}
	return nil
}

type DeleteTeamSkillRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// team uid
	Uid    string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Scope  *Scope   `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`
	Skills []*Skill `protobuf:"bytes,3,rep,name=skills,proto3" json:"skills,omitempty"`
}

func (x *DeleteTeamSkillRequest) Reset() {
	*x = DeleteTeamSkillRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_skill_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteTeamSkillRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTeamSkillRequest) ProtoMessage() {}

func (x *DeleteTeamSkillRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_skill_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTeamSkillRequest.ProtoReflect.Descriptor instead.
func (*DeleteTeamSkillRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{14}
}

func (x *DeleteTeamSkillRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *DeleteTeamSkillRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *DeleteTeamSkillRequest) GetSkills() []*Skill {
	if x != nil {
		return x.Skills
	}
	return nil
}

type Skill_Type struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Skill_Type) Reset() {
	*x = Skill_Type{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_skill_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Skill_Type) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Skill_Type) ProtoMessage() {}

func (x *Skill_Type) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_skill_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Skill_Type.ProtoReflect.Descriptor instead.
func (*Skill_Type) Descriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{5, 0}
}

type DeleteUsersSkillRequest_UserSkill struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid  string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteUsersSkillRequest_UserSkill) Reset() {
	*x = DeleteUsersSkillRequest_UserSkill{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_skill_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteUsersSkillRequest_UserSkill) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUsersSkillRequest_UserSkill) ProtoMessage() {}

func (x *DeleteUsersSkillRequest_UserSkill) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_skill_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUsersSkillRequest_UserSkill.ProtoReflect.Descriptor instead.
func (*DeleteUsersSkillRequest_UserSkill) Descriptor() ([]byte, []int) {
	return file_anno_v1_skill_proto_rawDescGZIP(), []int{12, 0}
}

func (x *DeleteUsersSkillRequest_UserSkill) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *DeleteUsersSkillRequest_UserSkill) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DeleteUsersSkillRequest_UserSkill) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_anno_v1_skill_proto protoreflect.FileDescriptor

var file_anno_v1_skill_proto_rawDesc = []byte{
	0x0a, 0x13, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x61, 0x6e, 0x6e, 0x6f, 0x2f,
	0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x52, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x05,
	0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x05, 0x73, 0x6b, 0x69,
	0x6c, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22, 0x28, 0x0a, 0x12, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0x25, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x53, 0x6b, 0x69, 0x6c, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xa4, 0x01, 0x0a, 0x10,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52,
	0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x12, 0x36, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x74, 0x74, 0x65,
	0x72, 0x6e, 0x22, 0x4e, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x26, 0x0a, 0x06, 0x73, 0x6b,
	0x69, 0x6c, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x06, 0x73, 0x6b, 0x69, 0x6c,
	0x6c, 0x73, 0x22, 0xa8, 0x02, 0x0a, 0x05, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x36, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e,
	0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x61, 0x78,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x2f, 0x0a, 0x05, 0x6c, 0x61, 0x6e, 0x67, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x6b, 0x69, 0x6c, 0x6c, 0x2e, 0x4c, 0x61, 0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x05, 0x6c, 0x61, 0x6e, 0x67, 0x73, 0x1a, 0x4b, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x22, 0x43,
	0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x10, 0x03, 0x1a, 0x38, 0x0a, 0x0a, 0x4c, 0x61, 0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x7f, 0x0a,
	0x09, 0x55, 0x73, 0x65, 0x72, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e,
	0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x4d,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x22, 0x3b, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x26, 0x0a, 0x06, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6b, 0x69,
	0x6c, 0x6c, 0x52, 0x06, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x22, 0x9d, 0x01, 0x0a, 0x15, 0x4c,
	0x69, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63,
	0x6f, 0x70, 0x65, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x65,
	0x61, 0x6d, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x65,
	0x61, 0x6d, 0x55, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x75, 0x69,
	0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x55, 0x69,
	0x64, 0x73, 0x12, 0x26, 0x0a, 0x06, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6b, 0x69,
	0x6c, 0x6c, 0x52, 0x06, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x22, 0x3f, 0x0a, 0x13, 0x4c, 0x69,
	0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x28, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53,
	0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22, 0x68, 0x0a, 0x14, 0x41,
	0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x73, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x6f,
	0x70, 0x65, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x6b, 0x69,
	0x6c, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x06, 0x73,
	0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x22, 0xc8, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x6f, 0x70, 0x65,
	0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x53, 0x6b, 0x69, 0x6c,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x6b, 0x69,
	0x6c, 0x6c, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x1a, 0x45, 0x0a, 0x09, 0x55, 0x73, 0x65,
	0x72, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0x75, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x54, 0x65, 0x61, 0x6d, 0x53, 0x6b, 0x69, 0x6c, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x05, 0x73, 0x63, 0x6f,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12,
	0x26, 0x0a, 0x06, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52,
	0x06, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x22, 0x78, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x54, 0x65, 0x61, 0x6d, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x6f,
	0x70, 0x65, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x06, 0x73, 0x6b, 0x69,
	0x6c, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x06, 0x73, 0x6b, 0x69, 0x6c, 0x6c,
	0x73, 0x32, 0xad, 0x08, 0x0a, 0x06, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x12, 0x44, 0x0a, 0x0b,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x0e, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x1a, 0x0e, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x22, 0x15, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x0f, 0x3a, 0x01, 0x2a, 0x22, 0x0a, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x6b, 0x69, 0x6c,
	0x6c, 0x73, 0x12, 0x62, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6b, 0x69, 0x6c,
	0x6c, 0x12, 0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0e,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x22, 0x26,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x05, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x32, 0x17, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x2f, 0x7b, 0x73, 0x6b, 0x69, 0x6c, 0x6c,
	0x2e, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x5d, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x13, 0x2a, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x2f, 0x7b,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x4f, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x53, 0x6b, 0x69, 0x6c,
	0x6c, 0x12, 0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0e, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x22, 0x19, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x13, 0x12, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x2f,
	0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x53, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x6b,
	0x69, 0x6c, 0x6c, 0x12, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x6b, 0x69,
	0x6c, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x12, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0c, 0x12,
	0x0a, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x12, 0x68, 0x0a, 0x0c, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x1c, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x6b, 0x69,
	0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x6b, 0x69, 0x6c, 0x6c,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12, 0x16, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2f,
	0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x68, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x73, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x53, 0x6b, 0x69, 0x6c, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x53, 0x6b, 0x69, 0x6c, 0x6c,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12,
	0x63, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x73, 0x53, 0x6b, 0x69, 0x6c, 0x6c,
	0x12, 0x1d, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x73,
	0x65, 0x72, 0x73, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x3a,
	0x01, 0x2a, 0x22, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x73, 0x12, 0x66, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x73, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x20, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x53, 0x6b,
	0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x2a, 0x10, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12, 0x67, 0x0a, 0x0c,
	0x41, 0x64, 0x64, 0x54, 0x65, 0x61, 0x6d, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x1c, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x54, 0x65, 0x61, 0x6d, 0x53, 0x6b,
	0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x2f,
	0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x6a, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54,
	0x65, 0x61, 0x6d, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x1f, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x53, 0x6b, 0x69,
	0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x2a, 0x16, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64,
	0x7d, 0x42, 0x3e, 0x0a, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x31,
	0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e, 0x6e,
	0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_anno_v1_skill_proto_rawDescOnce sync.Once
	file_anno_v1_skill_proto_rawDescData = file_anno_v1_skill_proto_rawDesc
)

func file_anno_v1_skill_proto_rawDescGZIP() []byte {
	file_anno_v1_skill_proto_rawDescOnce.Do(func() {
		file_anno_v1_skill_proto_rawDescData = protoimpl.X.CompressGZIP(file_anno_v1_skill_proto_rawDescData)
	})
	return file_anno_v1_skill_proto_rawDescData
}

var file_anno_v1_skill_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_anno_v1_skill_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_anno_v1_skill_proto_goTypes = []interface{}{
	(Skill_Type_Enum)(0),                      // 0: anno.v1.Skill.Type.Enum
	(*UpdateSkillRequest)(nil),                // 1: anno.v1.UpdateSkillRequest
	(*DeleteSkillRequest)(nil),                // 2: anno.v1.DeleteSkillRequest
	(*GetSkillRequest)(nil),                   // 3: anno.v1.GetSkillRequest
	(*ListSkillRequest)(nil),                  // 4: anno.v1.ListSkillRequest
	(*ListSkillReply)(nil),                    // 5: anno.v1.ListSkillReply
	(*Skill)(nil),                             // 6: anno.v1.Skill
	(*UserSkill)(nil),                         // 7: anno.v1.UserSkill
	(*GetUserSkillRequest)(nil),               // 8: anno.v1.GetUserSkillRequest
	(*GetUserSkillReply)(nil),                 // 9: anno.v1.GetUserSkillReply
	(*ListUsersSkillRequest)(nil),             // 10: anno.v1.ListUsersSkillRequest
	(*ListUsersSkillReply)(nil),               // 11: anno.v1.ListUsersSkillReply
	(*AddUsersSkillRequest)(nil),              // 12: anno.v1.AddUsersSkillRequest
	(*DeleteUsersSkillRequest)(nil),           // 13: anno.v1.DeleteUsersSkillRequest
	(*AddTeamSkillRequest)(nil),               // 14: anno.v1.AddTeamSkillRequest
	(*DeleteTeamSkillRequest)(nil),            // 15: anno.v1.DeleteTeamSkillRequest
	(*Skill_Type)(nil),                        // 16: anno.v1.Skill.Type
	nil,                                       // 17: anno.v1.Skill.LangsEntry
	(*DeleteUsersSkillRequest_UserSkill)(nil), // 18: anno.v1.DeleteUsersSkillRequest.UserSkill
	(*Scope)(nil),                             // 19: anno.v1.Scope
	(*emptypb.Empty)(nil),                     // 20: google.protobuf.Empty
}
var file_anno_v1_skill_proto_depIdxs = []int32{
	6,  // 0: anno.v1.UpdateSkillRequest.skill:type_name -> anno.v1.Skill
	0,  // 1: anno.v1.ListSkillRequest.type:type_name -> anno.v1.Skill.Type.Enum
	6,  // 2: anno.v1.ListSkillReply.skills:type_name -> anno.v1.Skill
	0,  // 3: anno.v1.Skill.type:type_name -> anno.v1.Skill.Type.Enum
	17, // 4: anno.v1.Skill.langs:type_name -> anno.v1.Skill.LangsEntry
	0,  // 5: anno.v1.UserSkill.type:type_name -> anno.v1.Skill.Type.Enum
	19, // 6: anno.v1.GetUserSkillRequest.scope:type_name -> anno.v1.Scope
	6,  // 7: anno.v1.GetUserSkillReply.skills:type_name -> anno.v1.Skill
	19, // 8: anno.v1.ListUsersSkillRequest.scope:type_name -> anno.v1.Scope
	6,  // 9: anno.v1.ListUsersSkillRequest.skills:type_name -> anno.v1.Skill
	7,  // 10: anno.v1.ListUsersSkillReply.users:type_name -> anno.v1.UserSkill
	19, // 11: anno.v1.AddUsersSkillRequest.scope:type_name -> anno.v1.Scope
	7,  // 12: anno.v1.AddUsersSkillRequest.skills:type_name -> anno.v1.UserSkill
	19, // 13: anno.v1.DeleteUsersSkillRequest.scope:type_name -> anno.v1.Scope
	18, // 14: anno.v1.DeleteUsersSkillRequest.users:type_name -> anno.v1.DeleteUsersSkillRequest.UserSkill
	19, // 15: anno.v1.AddTeamSkillRequest.scope:type_name -> anno.v1.Scope
	6,  // 16: anno.v1.AddTeamSkillRequest.skills:type_name -> anno.v1.Skill
	19, // 17: anno.v1.DeleteTeamSkillRequest.scope:type_name -> anno.v1.Scope
	6,  // 18: anno.v1.DeleteTeamSkillRequest.skills:type_name -> anno.v1.Skill
	6,  // 19: anno.v1.Skills.CreateSkill:input_type -> anno.v1.Skill
	1,  // 20: anno.v1.Skills.UpdateSkill:input_type -> anno.v1.UpdateSkillRequest
	2,  // 21: anno.v1.Skills.DeleteSkill:input_type -> anno.v1.DeleteSkillRequest
	3,  // 22: anno.v1.Skills.GetSkill:input_type -> anno.v1.GetSkillRequest
	4,  // 23: anno.v1.Skills.ListSkill:input_type -> anno.v1.ListSkillRequest
	8,  // 24: anno.v1.Skills.GetUserSkill:input_type -> anno.v1.GetUserSkillRequest
	10, // 25: anno.v1.Skills.ListUsersSkill:input_type -> anno.v1.ListUsersSkillRequest
	12, // 26: anno.v1.Skills.AddUsersSkill:input_type -> anno.v1.AddUsersSkillRequest
	13, // 27: anno.v1.Skills.DeleteUsersSkill:input_type -> anno.v1.DeleteUsersSkillRequest
	14, // 28: anno.v1.Skills.AddTeamSkill:input_type -> anno.v1.AddTeamSkillRequest
	15, // 29: anno.v1.Skills.DeleteTeamSkill:input_type -> anno.v1.DeleteTeamSkillRequest
	6,  // 30: anno.v1.Skills.CreateSkill:output_type -> anno.v1.Skill
	6,  // 31: anno.v1.Skills.UpdateSkill:output_type -> anno.v1.Skill
	20, // 32: anno.v1.Skills.DeleteSkill:output_type -> google.protobuf.Empty
	6,  // 33: anno.v1.Skills.GetSkill:output_type -> anno.v1.Skill
	5,  // 34: anno.v1.Skills.ListSkill:output_type -> anno.v1.ListSkillReply
	9,  // 35: anno.v1.Skills.GetUserSkill:output_type -> anno.v1.GetUserSkillReply
	11, // 36: anno.v1.Skills.ListUsersSkill:output_type -> anno.v1.ListUsersSkillReply
	20, // 37: anno.v1.Skills.AddUsersSkill:output_type -> google.protobuf.Empty
	20, // 38: anno.v1.Skills.DeleteUsersSkill:output_type -> google.protobuf.Empty
	20, // 39: anno.v1.Skills.AddTeamSkill:output_type -> google.protobuf.Empty
	20, // 40: anno.v1.Skills.DeleteTeamSkill:output_type -> google.protobuf.Empty
	30, // [30:41] is the sub-list for method output_type
	19, // [19:30] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_anno_v1_skill_proto_init() }
func file_anno_v1_skill_proto_init() {
	if File_anno_v1_skill_proto != nil {
		return
	}
	file_anno_v1_type_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_anno_v1_skill_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSkillRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_skill_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSkillRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_skill_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSkillRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_skill_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSkillRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_skill_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSkillReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_skill_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Skill); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_skill_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserSkill); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_skill_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserSkillRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_skill_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserSkillReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_skill_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListUsersSkillRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_skill_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListUsersSkillReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_skill_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUsersSkillRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_skill_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteUsersSkillRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_skill_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddTeamSkillRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_skill_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteTeamSkillRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_skill_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Skill_Type); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_skill_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteUsersSkillRequest_UserSkill); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anno_v1_skill_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_anno_v1_skill_proto_goTypes,
		DependencyIndexes: file_anno_v1_skill_proto_depIdxs,
		EnumInfos:         file_anno_v1_skill_proto_enumTypes,
		MessageInfos:      file_anno_v1_skill_proto_msgTypes,
	}.Build()
	File_anno_v1_skill_proto = out.File
	file_anno_v1_skill_proto_rawDesc = nil
	file_anno_v1_skill_proto_goTypes = nil
	file_anno_v1_skill_proto_depIdxs = nil
}
