# This file contains all available configuration options
# with their default values.

# options for analysis running
run:
  # default concurrency is a available CPU number
  concurrency: 4

  # timeout for analysis, e.g. 30s, 5m, default is 1m
  timeout: 1m

  # exit code when at least one issue was found, default is 1
  issues-exit-code: 1

  # include test files or not, default is true
  tests: true

  # list of build tags, all linters use it. Default is empty list.
  build-tags:
    - mytag

  # which dirs to skip: issues from them won't be reported;
  # can use regexp here: generated.*, regexp is applied on full path;
  # default value is empty list, but default dirs are skipped independently
  # from this option's value (see skip-dirs-use-default).
  # "/" will be replaced by current OS file path separator to properly work
  # on Windows.
  skip-dirs:
    - src/external_libs
    - autogenerated_by_my_lib

  # default is true. Enables skipping of directories:
  #   vendor$, third_party$, testdata$, examples$, Godeps$, builtin$
  skip-dirs-use-default: true

  # which files to skip: they will be analyzed, but issues from them
  # won't be reported. Default value is empty list, but there is
  # no need to include all autogenerated files, we confidently recognize
  # autogenerated files. If it's not please let us know.
  # "/" will be replaced by current OS file path separator to properly work
  # on Windows.
  skip-files:
#    - ".*\\.test\\.go$"
    - ".*\\.*_test\\.go$"
    - "test/.*\\.go$"
    - lib/bad.go

  # by default isn't set. If set we pass it to "go list -mod={option}". From "go help modules":
  # If invoked with -mod=readonly, the go command is disallowed from the implicit
  # automatic updating of go.mod described above. Instead, it fails when any changes
  # to go.mod are needed. This setting is most useful to check that go.mod does
  # not need updates, such as in a continuous integration and testing system.
  # If invoked with -mod=vendor, the go command assumes that the vendor
  # directory holds the correct copies of dependencies and ignores
  # the dependency descriptions in go.mod.
  # modules-download-mode: release

  # Allow multiple parallel golangci-lint instances running.
  # If false (default) - golangci-lint acquires file lock on start.
  allow-parallel-runners: false


# output configuration options
output:
  # colored-line-number|line-number|json|tab|checkstyle|code-climate|junit-xml|github-actions
  # default is "colored-line-number"
  format: colored-line-number

  # print lines of code with issue, default is true
  print-issued-lines: true

  # print linter name in the end of issue text, default is true
  print-linter-name: true

  # make issues output unique by line, default is true
  uniq-by-line: true

  # add a prefix to the output file references; default is no prefix
  path-prefix: ""

  # sorts results by: filepath, line and column
  sort-results: false


# all available settings of specific linters
linters-settings:

  cyclop:
    # the maximal code complexity to report
    max-complexity: 10
    # the maximal average package complexity. If it's higher than 0.0 (float) the check is enabled (default 0.0)
    package-average: 0.0
    # should ignore tests (default false)
    skip-tests: false

  dogsled:
    # checks assignments with too many blank identifiers; default is 2
    max-blank-identifiers: 2

  dupl:
    # tokens count to trigger issue, 150 by default
    threshold: 100

  errcheck:
    # report about not checking of errors in type assertions: `a := b.(MyStruct)`;
    # default is false: such cases aren't reported by default.
    check-type-assertions: false

    # report about assignment of errors to blank identifier: `num, _ := strconv.Atoi(numStr)`;
    # default is false: such cases aren't reported by default.
    check-blank: false

    # [deprecated] comma-separated list of pairs of the form pkg:regex
    # the regex is used to ignore names within pkg. (default "fmt:.*").
    # see https://github.com/kisielk/errcheck#the-deprecated-method for details
    ignore: fmt:.*,io/ioutil:^Read.*

    # path to a file containing a list of functions to exclude from checking
    # see https://github.com/kisielk/errcheck#excluding-functions for details
    # exclude: /path/to/file.txt
  errorlint:
    # Check whether fmt.Errorf uses the %w verb for formatting errors. See the readme for caveats
    errorf: true
    # Check for plain type assertions and type switches
    asserts: true
    # Check for plain error comparisons
    comparison: true

  exhaustive:
    # check switch statements in generated files also
    check-generated: false
    # indicates that switch statements are to be considered exhaustive if a
    # 'default' case is present, even if all enum members aren't listed in the
    # switch
    default-signifies-exhaustive: true

  exhaustivestruct:
    # Struct Patterns is list of expressions to match struct packages and names
    # The struct packages have the form example.com/package.ExampleStruct
    # The matching patterns can use matching syntax from https://pkg.go.dev/path#Match
    # If this list is empty, all structs are tested.
    struct-patterns:
      - '*.Test'
      - 'example.com/package.ExampleStruct'

  forbidigo:
    # Forbid the following identifiers (identifiers are written using regexp):
    forbid:
      - ^print.*$
      - 'fmt\.Print.*'
    # Exclude godoc examples from forbidigo checks.  Default is true.
    exclude_godoc_examples: false

  funlen:
    lines: 100
    statements: 60

  gci:
    # put imports beginning with prefix after 3rd-party packages;
    # only support one prefix
    # if not set, use goimports.local-prefixes
    local-prefixes: github.com/org/project

  gocognit:
    # minimal code complexity to report, 30 by default (but we recommend 10-20)
    min-complexity: 10

  nestif:
    # minimal complexity of if statements to report, 5 by default
    min-complexity: 4

  goconst:
    # minimal length of string constant, 3 by default
    min-len: 3
    # minimal occurrences count to trigger, 3 by default
    min-occurrences: 3

  gocritic:
    # Which checks should be enabled; can't be combined with 'disabled-checks';
    # See https://go-critic.github.io/overview#checks-overview
    # To check which checks are enabled run `GL_DEBUG=gocritic golangci-lint run`
    # By default list of stable checks is used.
    # enabled-checks:
      # - rangeValCopy

    # Which checks should be disabled; can't be combined with 'enabled-checks'; default is empty
    disabled-checks:
      - regexpMust

    # Enable multiple checks by tags, run `GL_DEBUG=gocritic golangci-lint run` to see all tags and checks.
    # Empty list by default. See https://github.com/go-critic/go-critic#usage -> section "Tags".
    enabled-tags:
      - performance
    disabled-tags:
      - experimental

    # Settings passed to gocritic.
    # The settings key is the name of a supported gocritic checker.
    # The list of supported checkers can be find in https://go-critic.github.io/overview.
    settings:
      captLocal: # must be valid enabled check name
        # whether to restrict checker to params only (default true)
        paramsOnly: true
      elseif:
        # whether to skip balanced if-else pairs (default true)
        skipBalanced: true
      hugeParam:
        # size in bytes that makes the warning trigger (default 80)
        sizeThreshold: 80
      # nestingReduce:
      #   # min number of statements inside a branch to trigger a warning (default 5)
      #   bodyWidth: 5
      rangeExprCopy:
        # size in bytes that makes the warning trigger (default 512)
        sizeThreshold: 512
        # whether to check test functions (default true)
        skipTestFuncs: true
      rangeValCopy:
        # size in bytes that makes the warning trigger (default 128)
        sizeThreshold: 32
        # whether to check test functions (default true)
        skipTestFuncs: true
      # ruleguard:
      #   # path to a gorules file for the ruleguard checker
      #   rules: ''
      # truncateCmp:
      #   # whether to skip int/uint/uintptr types (default true)
      #   skipArchDependent: true
      underef:
        # whether to skip (*x).method() calls where x is a pointer receiver (default true)
        skipRecvDeref: true
      # unnamedResult:
      #   # whether to check exported functions
      #   checkExported: true

  gocyclo:
    # minimal code complexity to report, 30 by default (but we recommend 10-20)
    min-complexity: 30

  godot:
    # comments to be checked: `declarations`, `toplevel`, or `all`
    scope: declarations
    # list of regexps for excluding particular comment lines from check
    exclude:
      # example: exclude comments which contain numbers
      # - '[0-9]+'
    # check that each sentence starts with a capital letter
    capital: false

  godox:
    # report any comments starting with keywords, this is useful for TODO or FIXME comments that
    # might be left in the code accidentally and should be resolved before merging
    keywords: # default keywords are TODO, BUG, and FIXME, these can be overwritten by this setting
      - NOTE
      - OPTIMIZE # marks code that should be optimized before merging
      - HACK # marks hack-arounds that should be removed before merging

  gofmt:
    # simplify code: gofmt with `-s` option, true by default
    simplify: true

  gofumpt:
    # Choose whether or not to use the extra rules that are disabled
    # by default
    extra-rules: false

  # goheader:
    # values:
      # const:
        # define here const type values in format k:v, for example:
        # COMPANY: MY COMPANY
      # regexp:
        # define here regexp type values, for example
        # AUTHOR: .*@mycompany\.com
    # template: # |-
      # put here copyright header template for source code files, for example:
      # Note: {{ YEAR }} is a builtin value that returns the year relative to the current machine time.
      #
      # {{ AUTHOR }} {{ COMPANY }} {{ YEAR }}
      # SPDX-License-Identifier: Apache-2.0

      # Licensed under the Apache License, Version 2.0 (the "License");
      # you may not use this file except in compliance with the License.
      # You may obtain a copy of the License at:

      #   http://www.apache.org/licenses/LICENSE-2.0

      # Unless required by applicable law or agreed to in writing, software
      # distributed under the License is distributed on an "AS IS" BASIS,
      # WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
      # See the License for the specific language governing permissions and
      # limitations under the License.
    # template-path:
      # also as alternative of directive 'template' you may put the path to file with the template source

  goimports:
    # put imports beginning with prefix after 3rd-party packages;
    # it's a comma-separated list of prefixes
    local-prefixes: github.com/org/project

  golint:
    # minimal confidence for issues, default is 0.8
    min-confidence: 0.8

  gomnd:
    settings:
      mnd:
        # the list of enabled checks, see https://github.com/tommy-muehle/go-mnd/#checks for description.
        checks:
          - argument
          - case
          - condition
          - operation
          - return
          - assign
        # ignored-numbers: 1000
        # ignored-files: magic_.*.go
        # ignored-functions: math.*

  gomoddirectives:
    # Allow local `replace` directives. Default is false.
    replace-local: false
    # List of allowed `replace` directives. Default is empty.
    replace-allow-list:
      - launchpad.net/gocheck
    # Allow to not explain why the version has been retracted in the `retract` directives. Default is false.
    retract-allow-no-explanation: false
    # Forbid the use of the `exclude` directives. Default is false.
    exclude-forbidden: false

  gomodguard:
    # allowed:
      # modules:                                                        # List of allowed modules
        # - gopkg.in/yaml.v2
      # domains:                                                        # List of allowed module domains
        # - golang.org
    blocked:
      # modules:                                                        # List of blocked modules
        # - github.com/uudashr/go-module:                             # Blocked module
        #     recommendations:                                        # Recommended modules that should be used instead (Optional)
        #       - golang.org/x/mod
        #     reason: "`mod` is the official go.mod parser library."  # Reason why the recommended module should be used (Optional)
      # versions:                                                       # List of blocked module version constraints
        # - github.com/mitchellh/go-homedir:                          # Blocked module with version constraint
        #     version: "< 1.1.0"                                      # Version constraint, see https://github.com/Masterminds/semver#basic-comparisons
        #     reason: "testing if blocked version constraint works."  # Reason why the version constraint exists. (Optional)
      local_replace_directives: false                                 # Set to true to raise lint issues for packages that are loaded from a local path via replace directive

  gosec:
    # To select a subset of rules to run.
    # Available rules: https://github.com/securego/gosec#available-rules
    includes:
      - G401
      - G306
      - G101
    # To specify a set of rules to explicitly exclude.
    # Available rules: https://github.com/securego/gosec#available-rules
    excludes:
      - G204
    # To specify the configuration of rules.
    # The configuration of rules is not fully documented by gosec:
    # https://github.com/securego/gosec#configuration
    # https://github.com/securego/gosec/blob/569328eade2ccbad4ce2d0f21ee158ab5356a5cf/rules/rulelist.go#L60-L102
    config:
      G306: "0600"
      G101:
        pattern: "(?i)example"
        ignore_entropy: false
        entropy_threshold: "80.0"
        per_char_threshold: "3.0"
        truncate: "32"

  govet:
    # report about shadowed variables
    check-shadowing: true

    # settings per analyzer
    settings:
      printf: # analyzer name, run `go tool vet help` to see all analyzers
        funcs: # run `go tool vet help printf` to see available settings for `printf` analyzer
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Infof
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Warnf
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Errorf
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Fatalf

    # enable or disable analyzers by name
    # run `go tool vet help` to see all analyzers
    enable:
      - atomicalign
    enable-all: false
    disable:
      - shadow
    disable-all: false

  depguard:
    list-type: blacklist
    include-go-root: false
    packages:
      - github.com/sirupsen/logrus
    packages-with-error-message:
      # specify an error message to output when a blacklisted package is used
      - github.com/sirupsen/logrus: "logging is allowed only by logutils.Log"

  ifshort:
    # Maximum length of variable declaration measured in number of lines, after which linter won't suggest using short syntax.
    # Has higher priority than max-decl-chars.
    max-decl-lines: 1
    # Maximum length of variable declaration measured in number of characters, after which linter won't suggest using short syntax.
    max-decl-chars: 30

  importas:
    # if set to `true`, force to use alias.
    no-unaliased: true
    # List of aliases
    alias:
      # using `servingv1` alias for `knative.dev/serving/pkg/apis/serving/v1` package
      - pkg: knative.dev/serving/pkg/apis/serving/v1
        alias: servingv1
      # using `autoscalingv1alpha1` alias for `knative.dev/serving/pkg/apis/autoscaling/v1alpha1` package
      - pkg: knative.dev/serving/pkg/apis/autoscaling/v1alpha1
        alias: autoscalingv1alpha1
      # You can specify the package path by regular expression,
      # and alias by regular expression expansion syntax like below.
      # see https://github.com/julz/importas#use-regular-expression for details
      - pkg: knative.dev/serving/pkg/apis/(\w+)/(v[\w\d]+)
        alias: $1$2

  lll:
    # max line length, lines longer will be reported. Default is 120.
    # '\t' is counted as 1 character by default, and can be changed with the tab-width option
    line-length: 120
    # tab width in spaces. Default to 1.
    tab-width: 1

  makezero:
    # Allow only slices initialized with a length of zero. Default is false.
    always: false

  maligned:
    # print struct with more effective memory layout or not, false by default
    suggest-new: true

  misspell:
    # Correct spellings using locale preferences for US or UK.
    # Default is to use a neutral variety of English.
    # Setting locale to US will correct the British spelling of 'colour' to 'color'.
    locale: US
    ignore-words:
      - someword

  nakedret:
    # make an issue if func has more lines of code than this setting and it has naked returns; default is 30
    max-func-lines: 30

  prealloc:
    # XXX: we don't recommend using this linter before doing performance profiling.
    # For most programs usage of prealloc will be a premature optimization.

    # Report preallocation suggestions only on simple loops that have no returns/breaks/continues/gotos in them.
    # True by default.
    simple: true
    range-loops: true # Report preallocation suggestions on range loops, true by default
    for-loops: false # Report preallocation suggestions on for loops, false by default

  promlinter:
    # Promlinter cannot infer all metrics name in static analysis.
    # Enable strict mode will also include the errors caused by failing to parse the args.
    strict: false
    # Please refer to https://github.com/yeya24/promlinter#usage for detailed usage.
    disabled-linters:
    #  - "Help"
    #  - "MetricUnits"
    #  - "Counter"
    #  - "HistogramSummaryReserved"
    #  - "MetricTypeInName"
    #  - "ReservedChars"
    #  - "CamelCase"
    #  - "lintUnitAbbreviations"

  predeclared:
    # comma-separated list of predeclared identifiers to not report on
    ignore: ""
    # include method names and field names (i.e., qualified names) in checks
    q: false

  nolintlint:
    # Enable to ensure that nolint directives are all used. Default is true.
    allow-unused: false
    # Disable to ensure that nolint directives don't have a leading space. Default is true.
    allow-leading-space: true
    # Exclude following linters from requiring an explanation.  Default is [].
    allow-no-explanation: []
    # Enable to require an explanation of nonzero length after each nolint directive. Default is false.
    require-explanation: true
    # Enable to require nolint directives to mention the specific linter being suppressed. Default is false.
    require-specific: true

  rowserrcheck:
    packages:
      - github.com/jmoiron/sqlx

  # revive:
  #   # see https://github.com/mgechev/revive#available-rules for details.
  #   ignore-generated-header: true
  #   severity: warning
  #   rules:
  #     - name: indent-error-flow
  #       severity: warning
  #     - name: add-constant
  #       severity: warning
  #       arguments:
  #         - maxLitCount: "3"
  #           allowStrs: '""'
  #           allowInts: "0,1,2"
  #           allowFloats: "0.0,0.,1.0,1.,2.0,2."

  tagliatelle:
    # check the struck tag name case
    case:
      # use the struct field name to check the name of the struct tag
      use-field-name: true
      rules:
        # any struct tag type can be used.
        # support string case: `camel`, `pascal`, `kebab`, `snake`, `goCamel`, `goPascal`, `goKebab`, `goSnake`, `upper`, `lower`
        json: camel
        yaml: camel
        xml: camel
        bson: camel
        avro: snake
        mapstructure: kebab

  testpackage:
    # regexp pattern to skip files
    skip-regexp: (export|internal)_test\.go

  thelper:
    # The following configurations enable all checks. It can be omitted because all checks are enabled by default.
    # You can enable only required checks deleting unnecessary checks.
    test:
      first: true
      name: true
      begin: true
    benchmark:
      first: true
      name: true
      begin: true
    tb:
      first: true
      name: true
      begin: true

  unparam:
    # Inspect exported functions, default is false. Set to true if no external program/library imports your code.
    # XXX: if you enable this setting, unparam will report a lot of false-positives in text editors:
    # if it's called for subdir of a project it can't find external interfaces. All text editor integrations
    # with golangci-lint call it on a directory with the changed file.
    check-exported: false

  whitespace:
    multi-if: false   # Enforces newlines (or comments) after every multi-line if statement
    multi-func: false # Enforces newlines (or comments) after every multi-line function signature

  wrapcheck:
    # An array of strings that specify substrings of signatures to ignore.
    # If this set, it will override the default set of ignored signatures.
    # See https://github.com/tomarrell/wrapcheck#configuration for more information.
    ignoreSigs:
      - .Errorf(
      - errors.New(
      - errors.Unwrap(
      - .Wrap(
      - .Wrapf(
      - .WithMessage(

  wsl:
    # See https://github.com/bombsimon/wsl/blob/master/doc/configuration.md for
    # documentation of available settings. These are the defaults for
    # `golangci-lint`.
    allow-assign-and-anything: false
    allow-assign-and-call: true
    allow-cuddle-declarations: false
    allow-multiline-assign: true
    allow-separated-leading-comment: false
    allow-trailing-comment: false
    force-case-trailing-whitespace: 0
    force-err-cuddling: false
    force-short-decl-cuddling: false
    strict-append: true

  # The custom section can be used to define linter plugins to be loaded at runtime.
  # See README doc for more info.
  # custom:
    # Each custom linter should have a unique name.
    #  example:
    #   # The path to the plugin *.so. Can be absolute or local. Required for each custom linter
    #   path: /path/to/example.so
    #   # The description of the linter. Optional, just for documentation purposes.
    #   description: This is an example usage of a plugin linter.
    #   # Intended to point to the repo location of the linter. Optional, just for documentation purposes.
    #   original-url: github.com/golangci/example-linter

linters:
  #disable-all: true
  enable:
    - bodyclose
    - deadcode
    - depguard
    - dogsled
    - dupl
    - errcheck
    - exportloopref
    - exhaustive
    - funlen
    - goconst
    - gocritic
    - gocyclo
    - gofmt
    - goimports
    - goprintffuncname
    - gosec
    - gosimple
    - govet
    - ineffassign
    - lll
    - megacheck
    - misspell
    - nakedret
    - noctx
    - nolintlint
    - rowserrcheck
    - staticcheck
    - structcheck
    - stylecheck
    - typecheck
    - unconvert
    - unparam
    - unused
    - varcheck
    - whitespace

    # don't enable:
    # - asciicheck
    # - scopelint
    # - gochecknoglobals
    # - gocognit
    # - gochecknoinits
    # - godot
    # - godox
    # - goerr113
    # - gomnd
    # - interfacer
    # - maligned
    # - nestif
    # - prealloc
    # - testpackage
    # - revive
    # - wsl
  disable:
    - maligned
    - prealloc
    - scopelint
  disable-all: false
  presets:
    - bugs
    - unused
  fast: false


issues:
  # List of regexps of issue texts to exclude, empty list by default.
  # But independently from this option we use default exclude patterns,
  # it can be disabled by `exclude-use-default: false`. To list all
  # excluded by default patterns execute `golangci-lint run --help`
  exclude:
    - abcdef

  # Excluding configuration per-path, per-linter, per-text and per-source
  exclude-rules:
    # Exclude some linters from running on tests files.
    - path: _test\.go
      linters:
        - gocyclo
        - errcheck
        - dupl
        - gosec

    # Exclude known linters from partially hard-vendored code,
    # which is impossible to exclude via "nolint" comments.
    - path: internal/hmac/
      text: "weak cryptographic primitive"
      linters:
        - gosec

    # Exclude some staticcheck messages
    - linters:
        - staticcheck
      text: "SA9003:"

    # Exclude lll issues for long lines with go:generate
    - linters:
        - lll
      source: "^//go:generate "

  # Independently from option `exclude` we use default exclude patterns,
  # it can be disabled by this option. To list all
  # excluded by default patterns execute `golangci-lint run --help`.
  # Default value for this option is true.
  exclude-use-default: false

  # The default value is false. If set to true exclude and exclude-rules
  # regular expressions become case sensitive.
  exclude-case-sensitive: false

  # The list of ids of default excludes to include or disable. By default it's empty.
  include:
    - EXC0002 # disable excluding of issues about comments from golint

  # Maximum issues count per one linter. Set to 0 to disable. Default is 50.
  max-issues-per-linter: 0

  # Maximum count of issues with the same text. Set to 0 to disable. Default is 3.
  max-same-issues: 0

  # Show only new issues: if there are unstaged changes or untracked files,
  # only those changes are analyzed, else only changes in HEAD~ are analyzed.
  # It's a super-useful option for integration of golangci-lint into existing
  # large codebase. It's not practical to fix all existing issues at the moment
  # of integration: much better don't allow issues in new code.
  # Default is false.
  # new: false

  # Show only new issues created after git revision `REV`
  # new-from-rev: REV

  # Show only new issues created in git patch with set file path.
  # new-from-patch: path/to/patch/file

  # Fix found issues (if it's supported by the linter)
  # fix: true

severity:
  # Default value is empty string.
  # Set the default severity for issues. If severity rules are defined and the issues
  # do not match or no severity is provided to the rule this will be the default
  # severity applied. Severities should match the supported severity names of the
  # selected out format.
  # - Code climate: https://docs.codeclimate.com/docs/issues#issue-severity
  # -   Checkstyle: https://checkstyle.sourceforge.io/property_types.html#severity
  # -       Github: https://help.github.com/en/actions/reference/workflow-commands-for-github-actions#setting-an-error-message
  default-severity: error

  # The default value is false.
  # If set to true severity-rules regular expressions become case sensitive.
  case-sensitive: false

  # Default value is empty list.
  # When a list of severity rules are provided, severity information will be added to lint
  # issues. Severity rules have the same filtering capability as exclude rules except you
  # are allowed to specify one matcher per severity rule.
  # Only affects out formats that support setting severity information.
  rules:
    - linters:
      - dupl
      severity: info
