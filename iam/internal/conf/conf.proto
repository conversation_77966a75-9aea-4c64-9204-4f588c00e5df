syntax = "proto3";
package kratos.api;

option go_package = "iam/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
  JWT jwt = 3;
  AliSMS alisms = 4;
  DangerTest danger_test = 5;
  Keto keto = 6;
  Temporal temporal = 7;
  Otel otel = 8;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
  bool enable_auth = 3;
  // non-empty comma-separated list of origins will enable CORS
  string cors_origins = 4;
  // pass JWT tokens via cookie
  string cookie_domain = 5;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
    string endpoint = 3;
    int32 port = 4;
    string database = 5;
    string username = 6;
    string password = 7;
    string options = 8;
    int32 max_idle_conns = 9;
    int32 max_open_conns = 10;
    google.protobuf.Duration conn_max_idle_time = 11;
  }
  message Redis {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration read_timeout = 3;
    google.protobuf.Duration write_timeout = 4;
  }
  Database database = 1;
  Redis redis = 2;
}

message Service {
  string addr = 1;
  // account used to authenticate RPC calls
  string user_uid = 2;
}

message JWT {
  string sign_method = 1;
	string sign_key = 2;
	string issuer = 3;
	string audience = 4;
	google.protobuf.Duration ttl = 5;
}

message AliSMS {
  string access_key = 1;
  string secret_key = 2;
  string region_id = 3;
  string sign_name = 4;
  string template_id = 5;
}

// DANGEROUS configurations for test purpose only
message DangerTest {
  message FakeLogin {
    string phone_regexp = 1;
    string auth_code = 2;
  }

  // define a map here because list cannot be overriden by env variables;
  // group-name -> FakeLogin
  map<string, FakeLogin> fake_logins = 1;
}

message Keto {
  string read_addr = 1;
  string write_addr = 2;
}

message Temporal {
  bool disable_worker = 1;
  string addr = 2;
  string namespace = 3;
  string task_queue = 4;
}

message Otel {
  Tracing tracing = 1;
  Metrics metrics = 2;
  Log log = 3;
}

message Tracing {
  // OTLP GRPC endpoint of the tracing service
  string endpoint = 1;
}

message Metrics {
  // prometheus HTTP exporter listening address
  string serve_addr = 1;
}

message Log {
  // log level: debug, info, warn, error, fatal
  string level = 1;
  // log format: default, json
  string format = 2;
}
