{"noEdit": false, "task_label": "point_cloud", "task_type": 902, "dotLimitLine": "", "dotLimit": "", "template_name": "", "is_online": true, "per_type": false, "task_key": "", "template_key": "", "deny_reason": "", "postil_config": [{"name": "漏标", "value": 1, "status": 1}, {"name": "多标", "value": 2, "status": 1}, {"name": "错标", "value": 3, "status": 1}], "is_ocr_auto_fill": 0, "is_open_camera_tag": 0, "selection_method": 0, "fuse_configure_method": 0, "merge_point_cloud": 0, "boxsize": [], "sub_round": [], "dataclass_list": [], "is_pre_mark_lock": 0, "is_mark_region": false, "mark_region": [], "save_mark_region": {}, "properties": [{"toolName": "立体框", "toolType": "volume", "fuse_2d_tools_checkbox": [], "min_points_num": "5", "attrs_same": 1, "is_attrs_sync": 0, "fuse_tool": "", "class": [{"ptitle": "汽车", "pname": "Car", "color": "rgb(97, 234, 103)", "pcode": "e91d37f0", "is_instance": 0, "size_same": 1}, {"ptitle": "巴士", "pname": "Bus", "color": "rgb(204, 101, 241)", "pcode": "9c08af2a", "is_instance": 0, "size_same": 1}, {"ptitle": "货车", "pname": "Truck", "color": "rgb(176, 241, 111)", "pcode": "0300655c", "is_instance": 0, "size_same": 1}, {"ptitle": "拖车", "pname": "Trailer", "color": "rgb(167, 82, 235)", "pcode": "3cb1eb31", "is_instance": 0, "size_same": 1}, {"ptitle": "行人", "pname": "Pedestrian", "color": "rgb(53, 207, 227)", "pcode": "e67d210a", "is_instance": 0, "size_same": 1}, {"ptitle": "有人骑行的两轮车", "pname": "Cyclist", "color": "rgb(186, 242, 115)", "pcode": "0a5cbfdd", "is_instance": 0, "size_same": 1}, {"ptitle": "无人骑行的两轮车", "pname": "Cyclist_Stopped", "color": "rgb(239, 149, 203)", "pcode": "bb7045c2", "is_instance": 0, "size_same": 1}, {"ptitle": "成排紧靠停放两轮车", "pname": "<PERSON><PERSON><PERSON>_Rack", "color": "rgb(127, 245, 231)", "pcode": "55c05b9f", "is_instance": 0, "size_same": 0}, {"ptitle": "三轮车", "pname": "Tricycle", "color": "rgb(29, 71, 180)", "pcode": "55034c3d", "is_instance": 0, "size_same": 1}, {"ptitle": "自动驾驶配送车", "pname": "Auto_Car", "color": "rgb(68, 233, 114)", "pcode": "e855edf4", "is_instance": 0, "size_same": 1}, {"ptitle": "交通锥", "pname": "Traffic_Cone", "color": "rgb(183, 223, 22)", "pcode": "c85ceccb", "is_instance": 0, "size_same": 1}, {"ptitle": "施工车辆", "pname": "Construction", "color": "rgb(88, 238, 139)", "pcode": "cd0fabed", "is_instance": 0, "size_same": 1}], "attrs": [{"ptitle": "2D可见度", "pname": "camera_visible", "code": "ede3cc76", "type": 0, "pvalue": [{"pname": "1", "ptitle": "可见", "code": "3f2314cd"}, {"pname": "0", "ptitle": "不可见", "code": "374447d4"}], "default_value": "3f2314cd"}, {"ptitle": "是否需要修改", "pname": "flag", "code": "8e123c22", "type": 0, "pvalue": [{"pname": "True", "ptitle": "不修改", "code": "759ee40a"}, {"pname": "False", "ptitle": "修改", "code": "5bd0a6c6"}], "default_value": ""}], "preset": [{"name": "小型车", "length": 4.6, "width": 1.8, "height": 1.5}, {"name": "卡车", "length": 10, "width": 2.8, "height": 3}, {"name": "大巴车", "length": 12, "width": 2.5, "height": 3.5}, {"name": "中巴车", "length": 7, "width": 2.4, "height": 3.3}, {"name": "三轮车", "length": 1.8, "width": 1.1, "height": 1.6}, {"name": "摩托车", "length": 1.7, "width": 0.6, "height": 1.5}, {"name": "自行车", "length": 1.6, "width": 0.6, "height": 1.7}, {"name": "行人", "length": 0.4, "width": 0.5, "height": 1.7}, {"name": "交通锥", "length": 0.5, "width": 0.5, "height": 0.6}]}], "attributeSeparation": 0, "properties2D": [], "global": {"attrs": [], "class": []}, "price": {"channel_price": {}, "customer_price": {}}, "train_desc": "", "train_num": "", "train_max_num": "", "train_rate": "", "package_type": 1, "submit_at": "", "update_time": "", "check_limit": "", "rotate_picture_export_type": 1, "is_panoramic": 0, "is_cover": 0, "is_class_type": 1, "group_type": 2, "assist": {"box": [{"name": "12像素", "width": 12, "height": 12}]}, "is_vsample_interval": 0, "vsample_interval": "", "isCutting": 0, "splitChannels": 0, "minNum": "", "maxNum": "", "isConversion": 0, "conversion_type": 0, "isPinyin": 0, "task_mode": 1, "cut_type": 0, "isAudioProps": 1, "isAddTime": 0, "addTime": "", "is_prop_check": 0, "is_escape": 0, "appropriate_types": 1, "props_save": 0, "labeling_method": 0, "add_attr": 0, "modify_corpus": 0, "is_pre_labeled": 0, "pro_type": 0, "num_of_task": 1, "num_of_pack": "", "articleTitle_type": 0, "article_titleArr": [], "corpus_type": 0, "text_title": [], "ocr_content_configure": [], "camera_properties": [], "textadd": false, "add_tag": 0, "tag_type": 0, "reference_tag": 0, "min_words_length": "", "max_words_length": "", "min_num_pieces": "", "max_num_pieces": "", "regStatus": 0, "is_expression": 0, "expression_content": "", "generate_mode": 0, "cue_word": [], "check_rule_name": 0, "translate": 0, "discrete": 0, "relationship_definition": [], "events_definition": [], "attribute_definition": [], "entity_premark": 0, "entity_library_id": "", "force_submit": 0, "cleaning_type": "1", "cleaningTypeArr": [], "type_list": [], "preset_type": "", "pass_total_limit": 0, "boxConsistent": 0, "is_private": 1, "project_type": {"pic": [{"task_type": 1001, "title": "图像通用标注", "imgsrc": "/images/show_1001.png", "icon": "#icontongyongbiaozhu", "is_allow": 1}, {"task_type": 1002, "title": "OCR文字转写", "imgsrc": "/images/show_1002.png", "icon": "#iconwenzizhuanxie", "is_allow": 1}, {"task_type": 1003, "title": "REID目标跟踪", "imgsrc": "/images/show_1003.png", "icon": "#iconmubiaogenzong", "is_allow": 1}, {"task_type": 1004, "title": "图像语义分割", "imgsrc": "/images/show_1004.png", "icon": "#iconyuyifenge1", "is_allow": 1}, {"task_type": 1006, "title": "人体关键点", "imgsrc": "/images/show_1006.png", "icon": "#<PERSON>rentiguanjiandian", "is_allow": 1}], "point_cloud": [{"task_type": 902, "title": "点云", "imgsrc": "/images/show_902.png", "icon": "#icondianyun2d3dronghe", "is_allow": 1}], "audio": [{"task_type": 704, "title": "音频标注", "imgsrc": "/images/show_704.png", "icon": "#iconyin<PERSON><PERSON><PERSON>hu", "is_allow": 1}], "text": [{"task_type": 1111, "title": "意图及实体标注", "imgsrc": "/images/show_1111.png", "icon": "#iconyi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_allow": 1}, {"task_type": 1202, "title": "命名实体识别与关系标注", "imgsrc": "/images/show_1202.png", "icon": "#iconmingmingshitishibieyuguanxibiaozhu", "is_allow": 1}, {"task_type": 1103, "title": "文本生成", "imgsrc": "/images/show_1103.png", "icon": "#iconwenben", "is_allow": 1}, {"task_type": 1112, "title": "文章判断", "imgsrc": "/images/show_1112.png", "icon": "#icon<PERSON>zhangpanduan", "is_allow": 1}, {"task_type": 1113, "title": "相似文本判断", "des": "给定参考语料，从一组句子中选择单条或多条相似文本", "imgsrc": "/images/show_1113.png", "icon": "#icon<PERSON>zhangpanduan", "is_allow": 1}], "video": [{"task_type": 1201, "title": "视频标注", "imgsrc": "/images/show_1201.png", "icon": "#icons<PERSON><PERSON><PERSON><PERSON><PERSON>", "is_allow": 1}]}, "issues": [{"name": "漏标", "value": 1, "status": 1}, {"name": "多标", "value": 2, "status": 1}, {"name": "错标", "value": 3, "status": 1}]}