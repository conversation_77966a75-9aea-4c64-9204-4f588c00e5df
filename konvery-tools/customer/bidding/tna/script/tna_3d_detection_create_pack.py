import os
import shutil
import sys

sys.path.append('.')

from customer.common import tools


def run(input_dir, output_dir):
    for sub_dir in tools.listdir(input_dir, sort=True, full_path=True):
        if not os.path.isdir(sub_dir):
            continue
        out_clip_dir = os.path.join(output_dir, os.path.basename(sub_dir))
        os.mkdir(out_clip_dir)
        out_lidar_dir = os.path.join(out_clip_dir, 'lidar')
        os.mkdir(out_lidar_dir)
        out_cameras_dir = os.path.join(out_clip_dir, 'camera')
        os.mkdir(out_cameras_dir)
        pcd = tools.get_file_by_extension(sub_dir, '.pcd')[0]
        shutil.copy(pcd, os.path.join(out_lidar_dir, pcd.name))
        imgs = tools.get_file_by_extension(sub_dir, '.jpg')
        for img in imgs:
            cam_name = '_'.join(img.name.split('_')[:3])
            out_camera_dir = os.path.join(out_cameras_dir, cam_name)
            os.mkdir(out_camera_dir)
            shutil.copyfile(img, os.path.join(out_camera_dir, pcd.name.replace('.pcd', '.jpg')))


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
