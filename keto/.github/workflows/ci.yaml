name: CI
on:
  push:
    branches:
      - master
    tags:
      - "*"
  pull_request:
  merge_group:

# Cancel in-progress runs in current workflow.
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  validate:
    name: Run lints and checks
    runs-on: ubuntu-latest
    steps:
      - uses: ory/ci/checkout@master
      - uses: actions/setup-go@v3
        with:
          go-version: 1.19
      - name: Update npm to a non-buggy version
        run: sudo npm i -g npm@9.5.0
      - name: Lint buf
        run: make buf-lint
      - run: go list -json > go.list
      - name: Run nancy
        uses: zepatrik/nancy-github-action@3b0a989791377b50913568102c0ed5539e4eb29c
        with:
          githubToken: ${{ secrets.GITHUB_TOKEN }}
      - name: Run golangci-lint
        uses: golangci/golangci-lint-action@v3
        env:
          GOGC: 100
        with:
          args: --timeout 10m0s --issues-exit-code=0
          skip-pkg-cache: true
      - name: Test documentation examples
        run: make test-docs-samples
      - name: Test OPL typelib
        run: cd contrib/namespace-type-lib && npm ci && npm test

  test:
    name: Run tests
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:11.8
        env:
          POSTGRES_DB: keto
          POSTGRES_PASSWORD: test
          POSTGRES_USER: test
        ports:
          - 5432:5432
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test
        ports:
          - 3306:3306
    env:
      TEST_DATABASE_POSTGRESQL: postgres://test:test@localhost:5432/keto?sslmode=disable
      TEST_DATABASE_MYSQL: mysql://root:test@(localhost:3306)/mysql?parseTime=true&multiStatements=true
      TEST_DATABASE_COCKROACHDB: cockroach://root@localhost:26257/defaultdb?sslmode=disable
    steps:
      - run: |
          docker create --name cockroach -p 26257:26257 \
            cockroachdb/cockroach:v20.2.5 start-single-node --insecure
          docker start cockroach
        name: Start CockroachDB
      - uses: ory/ci/checkout@master
      - uses: actions/setup-go@v3
        with:
          go-version: 1.19
      - name: Prepare Dependencies
        run: |
          make .bin/goveralls .bin/go-acc
      - name: Test proto package
        run: |
          cd proto
          go mod tidy
          go test ./...
      - name: Test Keto
        run: |
          source scripts/local-dependencies.sh
          go-acc --ignore "internal/httpclient" -o coverage.txt ./... -- -tags sqlite
      - run: |
          source scripts/local-dependencies.sh
          goveralls -service=github -coverprofile=coverage.txt
        env:
          COVERALLS_TOKEN: ${{ github.token }}

  test-race:
    name: Run race tests
    runs-on: ubuntu-latest
    steps:
      - uses: ory/ci/checkout@master
      - uses: actions/setup-go@v3
        with:
          go-version: 1.19
      - run: |
          go test -tags sqlite -race -short ./...

  docs-cli:
    runs-on: ubuntu-latest
    name: Build CLI docs
    needs:
      - test
      - test-race
      - validate
    steps:
      - uses: ory/ci/docs/cli-next@master
        with:
          token: ${{ secrets.ORY_BOT_PAT }}
          output-dir: docs/keto/cli

  changelog:
    name: Generate changelog
    runs-on: ubuntu-latest
    if:
      ${{ startsWith(github.ref, 'refs/tags/v') || github.ref_name == 'master'
      }}
    needs:
      - test
      - test-race
      - validate
    steps:
      - uses: ory/ci/checkout@master
        with:
          fetch-depth: 0
      - uses: ory/ci/changelog@master
        with:
          token: ${{ secrets.ORY_BOT_PAT }}

  sdk-generate:
    name: Generate SDKs
    runs-on: ubuntu-latest
    if:
      ${{ github.ref_type == 'tag' || github.ref_name != 'master' ||
      github.event_name == 'pull_request' }}
    needs:
      - test
      - test-race
      - validate
    steps:
      - uses: ory/ci/sdk/generate@master
        with:
          token: ${{ secrets.ORY_BOT_PAT }}

  sdk-release:
    name: Release SDKs
    runs-on: ubuntu-latest
    if: ${{ startsWith(github.ref, 'refs/tags/v') }}
    needs:
      - test
      - test-race
      - validate
      - sdk-generate
      - release
    steps:
      - uses: ory/ci/sdk/release@master
        with:
          token: ${{ secrets.ORY_BOT_PAT }}
          swag-spec-location: spec/api.json

  release:
    name: Generate release
    runs-on: ubuntu-latest
    if: ${{ startsWith(github.ref, 'refs/tags/v') }}
    needs:
      - test
      - test-race
      - validate
      - changelog
    steps:
      - uses: ory/ci/releaser@master
        with:
          token: ${{ secrets.ORY_BOT_PAT }}
          goreleaser_key: ${{ secrets.GORELEASER_KEY }}
          cosign_pwd: ${{ secrets.COSIGN_PWD }}
          docker_username: ${{ secrets.DOCKERHUB_USERNAME }}
          docker_password: ${{ secrets.DOCKERHUB_PASSWORD }}

  render-version-schema:
    name: Render version schema
    runs-on: ubuntu-latest
    if: ${{ startsWith(github.ref, 'refs/tags/v') }}
    needs:
      - release
    steps:
      - uses: ory/ci/releaser/render-version-schema@master
        with:
          token: ${{ secrets.ORY_BOT_PAT }}
          schema-path: .schema/config.schema.json

  newsletter-draft:
    name: Draft newsletter
    runs-on: ubuntu-latest
    if: ${{ startsWith(github.ref, 'refs/tags/v') }}
    needs:
      - release
    steps:
      - uses: ory/ci/newsletter@master
        with:
          mailchimp_list_id: f605a41b53
          mailchmip_segment_id: 6479489
          mailchimp_api_key: ${{ secrets.MAILCHIMP_API_KEY }}
          draft: "true"
          ssh_key: ${{ secrets.ORY_BOT_SSH_KEY }}

  slack-approval-notification:
    name: Pending approval Slack notification
    runs-on: ubuntu-latest
    if: ${{ startsWith(github.ref, 'refs/tags/v') }}
    needs:
      - newsletter-draft
    steps:
      - uses: ory/ci/newsletter/slack-notify@master
        with:
          slack-webhook-url: ${{ secrets.SLACK_WEBHOOK_URL }}

  newsletter-send:
    name: Send newsletter
    runs-on: ubuntu-latest
    needs:
      - newsletter-draft
    if: ${{ startsWith(github.ref, 'refs/tags/v') }}
    environment: production
    steps:
      - uses: ory/ci/newsletter@master
        with:
          mailchimp_list_id: f605a41b53
          mailchmip_segment_id: 6479489
          mailchimp_api_key: ${{ secrets.MAILCHIMP_API_KEY }}
          draft: "false"
          ssh_key: ${{ secrets.ORY_BOT_SSH_KEY }}

  buf:
    name: Run buf actions
    runs-on: ubuntu-latest
    steps:
      - uses: ory/ci/checkout@master
        with:
          path: current-repo
      - uses: actions/setup-go@v3
        with:
          go-version: 1.19
      - uses: actions/setup-node@v3
        with:
          node-version: "15"
      - name: Lint and Build
        run: |
          make buf
        working-directory: current-repo
      - name: Format Docs (required because buf generates the proto docs)
        run: |
          make format
        working-directory: current-repo
      - name: Push build
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "ory-bot"
          git add proto
          git commit -a -m "autogen: build buf" && git push "https://${GITHUB_ACTOR}:${{ secrets.ORY_BOT_PAT }}@github.com/${GITHUB_REPOSITORY}.git" HEAD:${GITHUB_REF#"refs/heads/"} || true
        working-directory: current-repo
      - if: ${{ github.ref_name == 'master' || github.ref_type == 'tag' }}
        uses: ory/ci/checkout@master
        with:
          repository: ory/docs
          path: docs
          fetch-depth: 0
          token: ${{ secrets.ORY_BOT_PAT }}
      - if: ${{ github.ref_name == 'master' || github.ref_type == 'tag' }}
        name: Push generated files to ory/docs
        env:
          GITHUB_TOKEN: ${{ secrets.ORY_BOT_PAT }}
        run: |
          echo "---
          title: Protocol buffers API
          ---

          <!-- This file is generated. Please edit the .proto files instead to update the documentation -->" > "docs/docs/keto/reference/proto-api.mdx"
          cat "current-repo/proto/buf.md" >> "docs/docs/keto/reference/proto-api.mdx"
          cd docs
          if git diff --exit-code; then
            echo "No changes to commit"
            exit 0
          fi

          git config --local user.email "<EMAIL>"
          git config --local user.name "ory-bot"

          git stash
          git pull --rebase origin master
          git stash apply
          git add docs/keto/reference/proto-api.mdx
          git commit -m "autogen: update Keto protobuf docs"
          git push origin HEAD:master
        shell: bash
