#!/bin/sh

sed --version 2>&1 | grep -q 'sed (GNU sed)' || (echo 'please use GNU sed' && exit 1)

# add upload APIs
cat << EOF | sed -i "/^components:$/r /dev/stdin" openapi.yaml
    /annofeed/v1/upload:
        post:
            tags:
                - Files
            operationId: Files_Upload
            parameters:
                - name: name
                  in: query
                  schema:
                    type: string
            requestBody:
                content:
                    application/octet-stream:
                        # any media type is accepted, functionally equivalent to '*/*'
                        schema:
                            # a binary file of any type
                            type: string
                            format: binary
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                \$ref: '#/components/schemas/UploadFileReply'
    /annofeed/v1/avatar:
        post:
            tags:
                - Files
            operationId: Files_UploadAvatar
            parameters:
                - name: name
                  in: query
                  schema:
                    type: string
                - name: type
                  in: query
                  schema:
                    enum:
                        - user
                        - prj
                        - res
                    type: string
                    format: enum
                - name: class
                  in: query
                  schema:
                    type: string
            requestBody:
                content:
                    application/octet-stream:
                        schema:
                            type: string
                            format: binary
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                \$ref: '#/components/schemas/UploadFileReply'
    /annofeed/v1/rawdata/{uid}:
        get:
            tags:
                - Files
            operationId: Files_GetRawdata
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "302":
                    description: Redirect to the actual URL.
    /annofeed/v1/pfile/{share_id}:
        get:
            tags:
                - Files
            operationId: Files_GetPfile
            description: get access URL to a shared file; authorization is required
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "302":
                    description: Redirect to the actual URL.
components:
EOF

cat << EOF | sed -i "/^tags:$/r /dev/stdin" openapi.yaml
        UploadFileReply:
            type: object
            properties:
                uri:
                    type: string
                url:
                    type: string
                    description: only available when uploading avatars
tags:
EOF

sed -i "0,/^tags:$/{//d}" openapi.yaml
sed -i "0,/^components:$/{//d}" openapi.yaml

# add security description
sed -i "s/^paths:$/security:\n    - jwt: []\npaths:/" openapi.yaml
cat << EOF | sed -i "/^components:$/r /dev/stdin" openapi.yaml
    securitySchemes:
        jwt:
            type: http
            scheme: bearer
            bearerFormat: JWT
EOF
for op in "Files_GetRawdata"; do
  sed -i "s/^\( \+\)\(operationId: $op\)$/\1\2\n\1security: [{}, {\"jwt\": []}]/" openapi.yaml
done
