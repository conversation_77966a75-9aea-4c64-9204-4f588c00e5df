package boschtestin

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"path/filepath"
	"strings"

	"annofeed/internal/biz"
	"annofeed/workflow/common"
	"github.com/go-kratos/kratos/v2/log"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/kfs"
)

const (
	ParamsDir = "params" // 相机雷达参数
)

func Transform(ctx context.Context, data *biz.Data, dir string, startIdx int, hb common.Heartbeat) (dataType string, err error) {
	newdir := dir + "-1"
	o := newXparser(dir, newdir)

	o.sourcePath = "/Users/<USER>/work/data/testin/bosch/labeling20230601024/m1_detect"
	err = kfs.IterateDir(o.sourcePath, func(entry os.DirEntry, subdir string, depth int) error {
		// skip directories
		if entry.IsDir() {
			return nil
		}
		if !strings.HasSuffix(entry.Name(), ".pcd") {
			return nil
		}
		return o.parseSourceFiles(ctx, subdir, entry.Name())
	})

	err = kfs.IterateDir(dir, func(entry os.DirEntry, subdir string, depth int) error {
		// skip directories
		if entry.IsDir() {
			return nil
		}
		hb(ctx)
		return o.parseFile(ctx, subdir, entry.Name())
	})
	if err != nil {
		return "", err
	}

	return biz.DataTypeFusion3D, nil
}

type fileParser func(ctx context.Context, subdir, name string) (match bool, err error)

type xparser struct {
	params     *Params
	elemParam  *biz.ElemParamV1
	frmIdx     int
	fileIdx    int
	lidarName  string
	curSubdir  string
	oldBasedir string
	newBasedir string
	parsers    []fileParser
	log        *log.Helper

	createNewClip bool

	sourcePath    string
	sourceMapping map[string]*SourceFile

	needAnnos           bool
	keepTestinStructure bool
}

func (o *xparser) getSourcePath(dir, name string) string {
	return path.Join(o.sourcePath, dir, name)
}

func (o *xparser) getNewSourcePath(dir, name string) string {
	return path.Join(o.sourcePath+"-kw", dir, name)
}

type SourceFile struct {
	Subdir    string
	PcdName   string
	ImageName string
}

func (s *SourceFile) getPcdPath() string   { return path.Join(s.Subdir, s.PcdName) }
func (s *SourceFile) getImagePath() string { return path.Join(s.Subdir, s.ImageName) }

func newXparser(olddir, newdir string) *xparser {
	o := &xparser{
		frmIdx:     -1,
		oldBasedir: olddir,
		newBasedir: newdir,
		// log: log.NewHelper(opts.Logger),
		sourceMapping: make(map[string]*SourceFile),
		// needAnnos:     true, // enable this if annotations are needed
		keepTestinStructure: true,
	}

	o.initParsers()
	return o
}

func (o *xparser) initParsers() {
	o.parsers = []fileParser{
		o.ignoreFiles,
		o.parseSourceFilesAndLabels,
	}
}

func (o *xparser) parseSourceFiles(ctx context.Context, subdir, name string) (err error) {
	if o.sourceMapping[name] != nil {
		// panic("pcd name conflict!") // seems that they are the same files
		return nil
	}
	o.sourceMapping[name] = &SourceFile{Subdir: subdir, PcdName: name}

	entries, err := os.ReadDir(filepath.Join(o.sourcePath, subdir))
	if err != nil {
		return fmt.Errorf("failed to read dir %v: %w", subdir, err)
	}
	for _, e := range entries {
		if strings.HasSuffix(e.Name(), ".jpeg") {
			o.sourceMapping[name].ImageName = e.Name()
			break
		}
	}
	return nil
}

func (o *xparser) parseFile(ctx context.Context, subdir, name string) (err error) {
	for _, p := range o.parsers {
		match, err := p(ctx, subdir, name)
		if match {
			return err
		}
	}
	return fmt.Errorf("unparsed file: %v", filepath.Join(subdir, name))
}

func (o *xparser) ignoreFiles(ctx context.Context, subdir, name string) (match bool, err error) {
	match = strings.HasPrefix(filepath.Base(name), ".") ||
		strings.HasSuffix(name, ".yaml") ||
		strings.HasSuffix(name, ".jpeg") ||
		strings.HasSuffix(name, ".pcd")
	// o.log.WithContext(ctx).Warnf("ignore a file %v", name)
	return
}

func (o *xparser) parseSourceFilesAndLabels(ctx context.Context, subdir, name string) (match bool, err error) {
	if !strings.HasSuffix(name, ".json") {
		return false, nil
	}
	if name == "config.json" || name == "params.json" {
		return true, nil
	}

	// move source pcd and image
	lidarName := name[:len(name)-len(".json")] + ".pcd"
	source := o.sourceMapping[lidarName]
	if source == nil {
		panic(fmt.Sprintf("source file %v not found", lidarName))
	}
	sourceDir := source.Subdir
	newDir := sourceDir
	if o.keepTestinStructure {
		newDir = filepath.Join(filepath.Dir(subdir), kfs.FileBareName(lidarName))
	}

	oldPcdPath := o.getSourcePath(sourceDir, lidarName)
	newPcdPath := o.getNewSourcePath(newDir, common.LidarPCD)
	if err := o.moveFile(ctx, oldPcdPath, newPcdPath, path.Join(sourceDir, lidarName)); err != nil {
		return true, fmt.Errorf("failed to copy %v to %v: %w", oldPcdPath, newPcdPath, err)
	}
	entries, err := os.ReadDir(filepath.Join(o.sourcePath, sourceDir))
	if err != nil {
		return true, fmt.Errorf("failed to read dir %v: %w", sourceDir, err)
	}
	imageName := ""
	for _, entry := range entries { // there is only one image
		name := entry.Name()
		if !strings.HasSuffix(name, ".jpeg") {
			continue
		}
		imageName = strings.Split(name, "_")[1]
		oldImgPath := o.getSourcePath(sourceDir, name)
		newImgPath := o.getNewSourcePath(newDir, imageName)
		if err := o.moveFile(ctx, oldImgPath, newImgPath, path.Join(sourceDir, entry.Name())); err != nil {
			return true, fmt.Errorf("failed to copy %v to %v: %w", oldImgPath, newImgPath, err)
		}
	}

	if o.needAnnos {
		// parse labeling results and write to annos.json
		labelFile := filepath.Join(o.oldBasedir, subdir, name)
		annosPath := o.getNewSourcePath(newDir, common.AnnosFile)
		if err := o.loadLabelsAndWriteAnnos(labelFile, annosPath, sourceDir, lidarName, imageName); err != nil {
			return true, fmt.Errorf("failed to load labels from %v: %w", labelFile, err)
		}
	}

	// move params file
	paramsPath := filepath.Join(o.oldBasedir, "params.json") // make sure the `params.json` exists
	newParamsPath := o.getNewSourcePath(newDir, "params.json")
	if err := o.moveFile(ctx, paramsPath, newParamsPath, ""); err != nil {
		return true, fmt.Errorf("failed to copy %v to %v: %w", oldPcdPath, newPcdPath, err)
	}

	return true, nil
}

func (o *xparser) moveFile(ctx context.Context, oldPath, newPath string, sourceOrigDir string) error {
	// duplicate the image file instead of moving it, otherwise we will probably miss some camera params
	// when retrying the process if it is interrupted due to a temporary error
	newPathDir := filepath.Dir(newPath)
	if _, err := os.Stat(newPathDir); err != nil {
		if !os.IsNotExist(err) {
			return err
		}
		if err := os.MkdirAll(newPathDir, 0755); err != nil {
			return err
		}
	}
	err := os.Link(oldPath, newPath)
	if err != nil {
		return fmt.Errorf("failed to link file (%v -> %v): %w", newPath, oldPath, err)
	}

	// record its original name
	if sourceOrigDir == "" {
		return nil
	}
	err = os.WriteFile(newPath+common.OrigExt, []byte(sourceOrigDir), 0644)
	if err != nil {
		return fmt.Errorf("failed to write file %v: %w", newPath+common.OrigExt, err)
	}

	return nil
}

func (o *xparser) loadLabelsAndWriteAnnos(labelFilePath, annosFilePath, sourceDir, lidarName, imgName string) error {
	params, err := ParseLabels(labelFilePath)
	if err != nil {
		return fmt.Errorf("failed to read params file %v: %w", labelFilePath, err)
	}

	annos, err := params.toAnnos(sourceDir, lidarName, imgName)
	if err != nil {
		return fmt.Errorf("failed to convert params to annos: %w", err)
	}
	annosBytes, err := json.Marshal(&anno.ExportAnnos{ElementAnnos: annos.ElementAnnos})
	if err != nil {
		return fmt.Errorf("failed to marshal annos: %w", err)
	}
	if err := os.WriteFile(annosFilePath, annosBytes, 0644); err != nil {
		return fmt.Errorf("failed to write annos file %v: %w", annosFilePath, err)
	}

	return nil
}
