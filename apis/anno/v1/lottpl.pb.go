// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anno/v1/lottpl.proto

package anno

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateLottplRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory in update-requests
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// mandatory in create-requests
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	// mandatory in create-requests;
	// lot type
	Type Lot_Type_Enum `protobuf:"varint,4,opt,name=type,proto3,enum=anno.v1.Lot_Type_Enum" json:"type,omitempty"`
	// lot ontologies
	Ontologies *Lotontologies `protobuf:"bytes,5,opt,name=ontologies,proto3" json:"ontologies,omitempty"`
	// execution phases; phase number starts from 1
	Phases []*Lotphase `protobuf:"bytes,6,rep,name=phases,proto3" json:"phases,omitempty"`
	// execution instructions in format of HTML or Markdown
	Instruction string `protobuf:"bytes,7,opt,name=instruction,proto3" json:"instruction,omitempty"`
	// annotation result output config
	Out *OutConfig `protobuf:"bytes,8,opt,name=out,proto3" json:"out,omitempty"`
	// number of elements in a job
	JobSize int32 `protobuf:"varint,9,opt,name=job_size,json=jobSize,proto3" json:"job_size,omitempty"`
	// make annotations within the radius, unit is meter
	WorkRange float32 `protobuf:"fixed32,10,opt,name=work_range,json=workRange,proto3" json:"work_range,omitempty"` // repeated string reject_reasons = 10;
}

func (x *CreateLottplRequest) Reset() {
	*x = CreateLottplRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lottpl_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLottplRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLottplRequest) ProtoMessage() {}

func (x *CreateLottplRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lottpl_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLottplRequest.ProtoReflect.Descriptor instead.
func (*CreateLottplRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lottpl_proto_rawDescGZIP(), []int{0}
}

func (x *CreateLottplRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *CreateLottplRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateLottplRequest) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *CreateLottplRequest) GetType() Lot_Type_Enum {
	if x != nil {
		return x.Type
	}
	return Lot_Type_unspecified
}

func (x *CreateLottplRequest) GetOntologies() *Lotontologies {
	if x != nil {
		return x.Ontologies
	}
	return nil
}

func (x *CreateLottplRequest) GetPhases() []*Lotphase {
	if x != nil {
		return x.Phases
	}
	return nil
}

func (x *CreateLottplRequest) GetInstruction() string {
	if x != nil {
		return x.Instruction
	}
	return ""
}

func (x *CreateLottplRequest) GetOut() *OutConfig {
	if x != nil {
		return x.Out
	}
	return nil
}

func (x *CreateLottplRequest) GetJobSize() int32 {
	if x != nil {
		return x.JobSize
	}
	return 0
}

func (x *CreateLottplRequest) GetWorkRange() float32 {
	if x != nil {
		return x.WorkRange
	}
	return 0
}

type UpdateLottplRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update contents
	Lottpl *CreateLottplRequest `protobuf:"bytes,1,opt,name=lottpl,proto3" json:"lottpl,omitempty"`
	// name of fields to be updated
	Fields []string `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *UpdateLottplRequest) Reset() {
	*x = UpdateLottplRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lottpl_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLottplRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLottplRequest) ProtoMessage() {}

func (x *UpdateLottplRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lottpl_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLottplRequest.ProtoReflect.Descriptor instead.
func (*UpdateLottplRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lottpl_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateLottplRequest) GetLottpl() *CreateLottplRequest {
	if x != nil {
		return x.Lottpl
	}
	return nil
}

func (x *UpdateLottplRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type DeleteLottplRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *DeleteLottplRequest) Reset() {
	*x = DeleteLottplRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lottpl_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLottplRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLottplRequest) ProtoMessage() {}

func (x *DeleteLottplRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lottpl_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLottplRequest.ProtoReflect.Descriptor instead.
func (*DeleteLottplRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lottpl_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteLottplRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetLottplRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *GetLottplRequest) Reset() {
	*x = GetLottplRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lottpl_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLottplRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLottplRequest) ProtoMessage() {}

func (x *GetLottplRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lottpl_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLottplRequest.ProtoReflect.Descriptor instead.
func (*GetLottplRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lottpl_proto_rawDescGZIP(), []int{3}
}

func (x *GetLottplRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type ListLottplRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page   int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Pagesz int32 `protobuf:"varint,2,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// filter by organization
	OrgUid string `protobuf:"bytes,3,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// filter by creator
	CreatorUid string `protobuf:"bytes,4,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	// filter by name pattern
	NamePattern string `protobuf:"bytes,5,opt,name=name_pattern,json=namePattern,proto3" json:"name_pattern,omitempty"`
	// filter by lot type
	Type Lot_Type_Enum `protobuf:"varint,6,opt,name=type,proto3,enum=anno.v1.Lot_Type_Enum" json:"type,omitempty"`
}

func (x *ListLottplRequest) Reset() {
	*x = ListLottplRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lottpl_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLottplRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLottplRequest) ProtoMessage() {}

func (x *ListLottplRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lottpl_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLottplRequest.ProtoReflect.Descriptor instead.
func (*ListLottplRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lottpl_proto_rawDescGZIP(), []int{4}
}

func (x *ListLottplRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListLottplRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListLottplRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *ListLottplRequest) GetCreatorUid() string {
	if x != nil {
		return x.CreatorUid
	}
	return ""
}

func (x *ListLottplRequest) GetNamePattern() string {
	if x != nil {
		return x.NamePattern
	}
	return ""
}

func (x *ListLottplRequest) GetType() Lot_Type_Enum {
	if x != nil {
		return x.Type
	}
	return Lot_Type_unspecified
}

type ListLottplReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total   int32     `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Lottpls []*Lottpl `protobuf:"bytes,2,rep,name=lottpls,proto3" json:"lottpls,omitempty"`
}

func (x *ListLottplReply) Reset() {
	*x = ListLottplReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lottpl_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLottplReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLottplReply) ProtoMessage() {}

func (x *ListLottplReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lottpl_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLottplReply.ProtoReflect.Descriptor instead.
func (*ListLottplReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_lottpl_proto_rawDescGZIP(), []int{5}
}

func (x *ListLottplReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListLottplReply) GetLottpls() []*Lottpl {
	if x != nil {
		return x.Lottpls
	}
	return nil
}

type Lottpl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lot template UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// lot template name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// lot template description
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	// task type
	Type Lot_Type_Enum `protobuf:"varint,4,opt,name=type,proto3,enum=anno.v1.Lot_Type_Enum" json:"type,omitempty"`
	// lot ontologies
	Ontologies *Lotontologies `protobuf:"bytes,5,opt,name=ontologies,proto3" json:"ontologies,omitempty"`
	// execution phases; phase number starts from 1
	Phases []*Lotphase `protobuf:"bytes,6,rep,name=phases,proto3" json:"phases,omitempty"`
	// execution instructions in format of HTML or Markdown
	Instruction string `protobuf:"bytes,7,opt,name=instruction,proto3" json:"instruction,omitempty"`
	// annotation result output config
	Out *OutConfig `protobuf:"bytes,8,opt,name=out,proto3" json:"out,omitempty"`
	// number of elements in a job
	JobSize int32 `protobuf:"varint,9,opt,name=job_size,json=jobSize,proto3" json:"job_size,omitempty"`
	// make annotations within the radius, unit is meter
	WorkRange float32                `protobuf:"fixed32,10,opt,name=work_range,json=workRange,proto3" json:"work_range,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *Lottpl) Reset() {
	*x = Lottpl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lottpl_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lottpl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lottpl) ProtoMessage() {}

func (x *Lottpl) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lottpl_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lottpl.ProtoReflect.Descriptor instead.
func (*Lottpl) Descriptor() ([]byte, []int) {
	return file_anno_v1_lottpl_proto_rawDescGZIP(), []int{6}
}

func (x *Lottpl) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Lottpl) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Lottpl) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Lottpl) GetType() Lot_Type_Enum {
	if x != nil {
		return x.Type
	}
	return Lot_Type_unspecified
}

func (x *Lottpl) GetOntologies() *Lotontologies {
	if x != nil {
		return x.Ontologies
	}
	return nil
}

func (x *Lottpl) GetPhases() []*Lotphase {
	if x != nil {
		return x.Phases
	}
	return nil
}

func (x *Lottpl) GetInstruction() string {
	if x != nil {
		return x.Instruction
	}
	return ""
}

func (x *Lottpl) GetOut() *OutConfig {
	if x != nil {
		return x.Out
	}
	return nil
}

func (x *Lottpl) GetJobSize() int32 {
	if x != nil {
		return x.JobSize
	}
	return 0
}

func (x *Lottpl) GetWorkRange() float32 {
	if x != nil {
		return x.WorkRange
	}
	return 0
}

func (x *Lottpl) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Lottpl) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

var File_anno_v1_lottpl_proto protoreflect.FileDescriptor

var file_anno_v1_lottpl_proto_rawDesc = []byte{
	0x0a, 0x14, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x74, 0x70, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65,
	0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x61, 0x6e, 0x6e, 0x6f,
	0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61,
	0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x6f, 0x74, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xec, 0x02, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c,
	0x6f, 0x74, 0x74, 0x70, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x36, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x6f, 0x74, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x36,
	0x0a, 0x0a, 0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74,
	0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x6f, 0x6e, 0x74, 0x6f,
	0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x12, 0x29, 0x0a, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x6f, 0x74, 0x70, 0x68, 0x61, 0x73, 0x65, 0x52, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65,
	0x73, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x03, 0x6f, 0x75, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x03, 0x6f, 0x75, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6a, 0x6f, 0x62,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6a, 0x6f, 0x62,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x72, 0x61, 0x6e,
	0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x22, 0x7a, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x74,
	0x74, 0x70, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x6c, 0x6f,
	0x74, 0x74, 0x70, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x74, 0x74, 0x70,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x06, 0x6c, 0x6f, 0x74, 0x74, 0x70, 0x6c,
	0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x3a, 0x15, 0xba, 0x47, 0x12, 0xba, 0x01, 0x06,
	0x6c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0xba, 0x01, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22,
	0x27, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x24, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x74, 0x74, 0x70, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0xd3,
	0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65,
	0x73, 0x7a, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18,
	0x64, 0x28, 0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x12, 0x17, 0x0a, 0x07, 0x6f,
	0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72,
	0x67, 0x55, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x55, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61,
	0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x61, 0x6d,
	0x65, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x2a, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x6f, 0x74, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x22, 0x52, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x74, 0x74,
	0x70, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x29, 0x0a,
	0x07, 0x6c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x52,
	0x07, 0x6c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x73, 0x22, 0xbd, 0x04, 0x0a, 0x06, 0x4c, 0x6f, 0x74,
	0x74, 0x70, 0x6c, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x12, 0x36, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74,
	0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x0a,
	0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x6f, 0x6e,
	0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f,
	0x67, 0x69, 0x65, 0x73, 0x12, 0x29, 0x0a, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x6f, 0x74, 0x70, 0x68, 0x61, 0x73, 0x65, 0x52, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x12,
	0x20, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x24, 0x0a, 0x03, 0x6f, 0x75, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x03, 0x6f, 0x75, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6a, 0x6f, 0x62, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x3e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x3e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x3a, 0x45, 0xba, 0x47, 0x42, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0xba, 0x01, 0x04, 0x74, 0x79, 0x70, 0x65, 0xba, 0x01, 0x08, 0x6a, 0x6f, 0x62,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0xba, 0x01, 0x0a, 0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69,
	0x65, 0x73, 0xba, 0x01, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0xba, 0x01, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x32, 0xd7, 0x03, 0x0a, 0x07, 0x4c, 0x6f, 0x74,
	0x74, 0x70, 0x6c, 0x73, 0x12, 0x55, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f,
	0x74, 0x74, 0x70, 0x6c, 0x12, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74,
	0x74, 0x70, 0x6c, 0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x3a, 0x01, 0x2a, 0x22, 0x0b,
	0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x73, 0x12, 0x67, 0x0a, 0x0c, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x12, 0x1c, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x74, 0x74,
	0x70, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x22, 0x3a, 0x06, 0x6c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x32, 0x18, 0x2f, 0x76, 0x31, 0x2f,
	0x6c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x73, 0x2f, 0x7b, 0x6c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x2e,
	0x75, 0x69, 0x64, 0x7d, 0x12, 0x5f, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f,
	0x74, 0x74, 0x70, 0x6c, 0x12, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x13, 0x2a, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x73, 0x2f,
	0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x52, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74,
	0x70, 0x6c, 0x12, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x22, 0x19,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x12, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x74,
	0x70, 0x6c, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x57, 0x0a, 0x0a, 0x4c, 0x69, 0x73,
	0x74, 0x4c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x12, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x70, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x13, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x0d, 0x12, 0x0b, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x74, 0x70,
	0x6c, 0x73, 0x42, 0x3e, 0x0a, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x31, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e,
	0x6e, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_anno_v1_lottpl_proto_rawDescOnce sync.Once
	file_anno_v1_lottpl_proto_rawDescData = file_anno_v1_lottpl_proto_rawDesc
)

func file_anno_v1_lottpl_proto_rawDescGZIP() []byte {
	file_anno_v1_lottpl_proto_rawDescOnce.Do(func() {
		file_anno_v1_lottpl_proto_rawDescData = protoimpl.X.CompressGZIP(file_anno_v1_lottpl_proto_rawDescData)
	})
	return file_anno_v1_lottpl_proto_rawDescData
}

var file_anno_v1_lottpl_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_anno_v1_lottpl_proto_goTypes = []interface{}{
	(*CreateLottplRequest)(nil),   // 0: anno.v1.CreateLottplRequest
	(*UpdateLottplRequest)(nil),   // 1: anno.v1.UpdateLottplRequest
	(*DeleteLottplRequest)(nil),   // 2: anno.v1.DeleteLottplRequest
	(*GetLottplRequest)(nil),      // 3: anno.v1.GetLottplRequest
	(*ListLottplRequest)(nil),     // 4: anno.v1.ListLottplRequest
	(*ListLottplReply)(nil),       // 5: anno.v1.ListLottplReply
	(*Lottpl)(nil),                // 6: anno.v1.Lottpl
	(Lot_Type_Enum)(0),            // 7: anno.v1.Lot.Type.Enum
	(*Lotontologies)(nil),         // 8: anno.v1.Lotontologies
	(*Lotphase)(nil),              // 9: anno.v1.Lotphase
	(*OutConfig)(nil),             // 10: anno.v1.OutConfig
	(*timestamppb.Timestamp)(nil), // 11: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),         // 12: google.protobuf.Empty
}
var file_anno_v1_lottpl_proto_depIdxs = []int32{
	7,  // 0: anno.v1.CreateLottplRequest.type:type_name -> anno.v1.Lot.Type.Enum
	8,  // 1: anno.v1.CreateLottplRequest.ontologies:type_name -> anno.v1.Lotontologies
	9,  // 2: anno.v1.CreateLottplRequest.phases:type_name -> anno.v1.Lotphase
	10, // 3: anno.v1.CreateLottplRequest.out:type_name -> anno.v1.OutConfig
	0,  // 4: anno.v1.UpdateLottplRequest.lottpl:type_name -> anno.v1.CreateLottplRequest
	7,  // 5: anno.v1.ListLottplRequest.type:type_name -> anno.v1.Lot.Type.Enum
	6,  // 6: anno.v1.ListLottplReply.lottpls:type_name -> anno.v1.Lottpl
	7,  // 7: anno.v1.Lottpl.type:type_name -> anno.v1.Lot.Type.Enum
	8,  // 8: anno.v1.Lottpl.ontologies:type_name -> anno.v1.Lotontologies
	9,  // 9: anno.v1.Lottpl.phases:type_name -> anno.v1.Lotphase
	10, // 10: anno.v1.Lottpl.out:type_name -> anno.v1.OutConfig
	11, // 11: anno.v1.Lottpl.updated_at:type_name -> google.protobuf.Timestamp
	11, // 12: anno.v1.Lottpl.created_at:type_name -> google.protobuf.Timestamp
	0,  // 13: anno.v1.Lottpls.CreateLottpl:input_type -> anno.v1.CreateLottplRequest
	1,  // 14: anno.v1.Lottpls.UpdateLottpl:input_type -> anno.v1.UpdateLottplRequest
	2,  // 15: anno.v1.Lottpls.DeleteLottpl:input_type -> anno.v1.DeleteLottplRequest
	3,  // 16: anno.v1.Lottpls.GetLottpl:input_type -> anno.v1.GetLottplRequest
	4,  // 17: anno.v1.Lottpls.ListLottpl:input_type -> anno.v1.ListLottplRequest
	6,  // 18: anno.v1.Lottpls.CreateLottpl:output_type -> anno.v1.Lottpl
	6,  // 19: anno.v1.Lottpls.UpdateLottpl:output_type -> anno.v1.Lottpl
	12, // 20: anno.v1.Lottpls.DeleteLottpl:output_type -> google.protobuf.Empty
	6,  // 21: anno.v1.Lottpls.GetLottpl:output_type -> anno.v1.Lottpl
	5,  // 22: anno.v1.Lottpls.ListLottpl:output_type -> anno.v1.ListLottplReply
	18, // [18:23] is the sub-list for method output_type
	13, // [13:18] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_anno_v1_lottpl_proto_init() }
func file_anno_v1_lottpl_proto_init() {
	if File_anno_v1_lottpl_proto != nil {
		return
	}
	file_anno_v1_lot_proto_init()
	file_anno_v1_type_lotconfig_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_anno_v1_lottpl_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLottplRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lottpl_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLottplRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lottpl_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLottplRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lottpl_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLottplRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lottpl_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLottplRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lottpl_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLottplReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lottpl_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lottpl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anno_v1_lottpl_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_anno_v1_lottpl_proto_goTypes,
		DependencyIndexes: file_anno_v1_lottpl_proto_depIdxs,
		MessageInfos:      file_anno_v1_lottpl_proto_msgTypes,
	}.Build()
	File_anno_v1_lottpl_proto = out.File
	file_anno_v1_lottpl_proto_rawDesc = nil
	file_anno_v1_lottpl_proto_goTypes = nil
	file_anno_v1_lottpl_proto_depIdxs = nil
}
