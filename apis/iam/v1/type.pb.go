// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: iam/v1/type.proto

package iam

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Team_Type_Enum int32

const (
	Team_Type_unspecified Team_Type_Enum = 0
	Team_Type_demander    Team_Type_Enum = 1
	Team_Type_operator    Team_Type_Enum = 2
	Team_Type_supplier    Team_Type_Enum = 3
)

// Enum value maps for Team_Type_Enum.
var (
	Team_Type_Enum_name = map[int32]string{
		0: "unspecified",
		1: "demander",
		2: "operator",
		3: "supplier",
	}
	Team_Type_Enum_value = map[string]int32{
		"unspecified": 0,
		"demander":    1,
		"operator":    2,
		"supplier":    3,
	}
)

func (x Team_Type_Enum) Enum() *Team_Type_Enum {
	p := new(Team_Type_Enum)
	*p = x
	return p
}

func (x Team_Type_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Team_Type_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_iam_v1_type_proto_enumTypes[0].Descriptor()
}

func (Team_Type_Enum) Type() protoreflect.EnumType {
	return &file_iam_v1_type_proto_enumTypes[0]
}

func (x Team_Type_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Team_Type_Enum.Descriptor instead.
func (Team_Type_Enum) EnumDescriptor() ([]byte, []int) {
	return file_iam_v1_type_proto_rawDescGZIP(), []int{0, 0, 0}
}

type User_Gender_Enum int32

const (
	User_Gender_unspecified User_Gender_Enum = 0
	User_Gender_male        User_Gender_Enum = 1
	User_Gender_female      User_Gender_Enum = 2
)

// Enum value maps for User_Gender_Enum.
var (
	User_Gender_Enum_name = map[int32]string{
		0: "unspecified",
		1: "male",
		2: "female",
	}
	User_Gender_Enum_value = map[string]int32{
		"unspecified": 0,
		"male":        1,
		"female":      2,
	}
)

func (x User_Gender_Enum) Enum() *User_Gender_Enum {
	p := new(User_Gender_Enum)
	*p = x
	return p
}

func (x User_Gender_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (User_Gender_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_iam_v1_type_proto_enumTypes[1].Descriptor()
}

func (User_Gender_Enum) Type() protoreflect.EnumType {
	return &file_iam_v1_type_proto_enumTypes[1]
}

func (x User_Gender_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use User_Gender_Enum.Descriptor instead.
func (User_Gender_Enum) EnumDescriptor() ([]byte, []int) {
	return file_iam_v1_type_proto_rawDescGZIP(), []int{1, 0, 0}
}

type IDType_Enum int32

const (
	IDType_unspecified IDType_Enum = 0
	IDType_uid         IDType_Enum = 1
	IDType_phone       IDType_Enum = 2
	IDType_email       IDType_Enum = 3
)

// Enum value maps for IDType_Enum.
var (
	IDType_Enum_name = map[int32]string{
		0: "unspecified",
		1: "uid",
		2: "phone",
		3: "email",
	}
	IDType_Enum_value = map[string]int32{
		"unspecified": 0,
		"uid":         1,
		"phone":       2,
		"email":       3,
	}
)

func (x IDType_Enum) Enum() *IDType_Enum {
	p := new(IDType_Enum)
	*p = x
	return p
}

func (x IDType_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IDType_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_iam_v1_type_proto_enumTypes[2].Descriptor()
}

func (IDType_Enum) Type() protoreflect.EnumType {
	return &file_iam_v1_type_proto_enumTypes[2]
}

func (x IDType_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IDType_Enum.Descriptor instead.
func (IDType_Enum) EnumDescriptor() ([]byte, []int) {
	return file_iam_v1_type_proto_rawDescGZIP(), []int{4, 0}
}

type EditAction_Enum int32

const (
	EditAction_unspecified EditAction_Enum = 0
	EditAction_add         EditAction_Enum = 1
	EditAction_delete      EditAction_Enum = 2
)

// Enum value maps for EditAction_Enum.
var (
	EditAction_Enum_name = map[int32]string{
		0: "unspecified",
		1: "add",
		2: "delete",
	}
	EditAction_Enum_value = map[string]int32{
		"unspecified": 0,
		"add":         1,
		"delete":      2,
	}
)

func (x EditAction_Enum) Enum() *EditAction_Enum {
	p := new(EditAction_Enum)
	*p = x
	return p
}

func (x EditAction_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EditAction_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_iam_v1_type_proto_enumTypes[3].Descriptor()
}

func (EditAction_Enum) Type() protoreflect.EnumType {
	return &file_iam_v1_type_proto_enumTypes[3]
}

func (x EditAction_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EditAction_Enum.Descriptor instead.
func (EditAction_Enum) EnumDescriptor() ([]byte, []int) {
	return file_iam_v1_type_proto_rawDescGZIP(), []int{6, 0}
}

type Team struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid      string         `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name     string         `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc     string         `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Avatar   string         `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Type     Team_Type_Enum `protobuf:"varint,5,opt,name=type,proto3,enum=iam.v1.Team_Type_Enum" json:"type,omitempty"`
	Province string         `protobuf:"bytes,6,opt,name=province,proto3" json:"province,omitempty"`
	City     string         `protobuf:"bytes,7,opt,name=city,proto3" json:"city,omitempty"`
	// parent team uid
	// string parent_uid = 11 [(google.api.field_behavior) = INPUT_ONLY];
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *Team) Reset() {
	*x = Team{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_type_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Team) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Team) ProtoMessage() {}

func (x *Team) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_type_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Team.ProtoReflect.Descriptor instead.
func (*Team) Descriptor() ([]byte, []int) {
	return file_iam_v1_type_proto_rawDescGZIP(), []int{0}
}

func (x *Team) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Team) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Team) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Team) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *Team) GetType() Team_Type_Enum {
	if x != nil {
		return x.Type
	}
	return Team_Type_unspecified
}

func (x *Team) GetProvince() string {
	if x != nil {
		return x.Province
	}
	return ""
}

func (x *Team) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *Team) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid  string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// phone number: +8613412345678
	Phone string `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone,omitempty"`
	// email: <EMAIL>
	Email  string `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	Avatar string `protobuf:"bytes,5,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// user's role: admin/manager/member
	Role string `protobuf:"bytes,6,opt,name=role,proto3" json:"role,omitempty"`
	// user's gender
	Gender User_Gender_Enum `protobuf:"varint,7,opt,name=gender,proto3,enum=iam.v1.User_Gender_Enum" json:"gender,omitempty"`
	// user's birthday: 2010-06-07
	Birthday string `protobuf:"bytes,8,opt,name=birthday,proto3" json:"birthday,omitempty"`
	Province string `protobuf:"bytes,9,opt,name=province,proto3" json:"province,omitempty"`
	City     string `protobuf:"bytes,10,opt,name=city,proto3" json:"city,omitempty"`
	// top level team the user belongs to
	OrgUid string `protobuf:"bytes,11,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// type of the user's organization
	OrgType Team_Type_Enum `protobuf:"varint,12,opt,name=org_type,json=orgType,proto3,enum=iam.v1.Team_Type_Enum" json:"org_type,omitempty"`
	// string hierarchy = 13;
	// indicates if the user's information should be improved
	Imperfect bool                   `protobuf:"varint,14,opt,name=imperfect,proto3" json:"imperfect,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// tags attached to the user
	Tags []string `protobuf:"bytes,16,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_type_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_type_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_iam_v1_type_proto_rawDescGZIP(), []int{1}
}

func (x *User) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *User) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *User) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *User) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *User) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *User) GetGender() User_Gender_Enum {
	if x != nil {
		return x.Gender
	}
	return User_Gender_unspecified
}

func (x *User) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *User) GetProvince() string {
	if x != nil {
		return x.Province
	}
	return ""
}

func (x *User) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *User) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *User) GetOrgType() Team_Type_Enum {
	if x != nil {
		return x.OrgType
	}
	return Team_Type_unspecified
}

func (x *User) GetImperfect() bool {
	if x != nil {
		return x.Imperfect
	}
	return false
}

func (x *User) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *User) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

type UserContext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the effective user; it may be an assumed user in an assume context
	User *User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	// the real user in an assume context
	AssumeBy *User `protobuf:"bytes,2,opt,name=assume_by,json=assumeBy,proto3" json:"assume_by,omitempty"`
}

func (x *UserContext) Reset() {
	*x = UserContext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_type_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserContext) ProtoMessage() {}

func (x *UserContext) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_type_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserContext.ProtoReflect.Descriptor instead.
func (*UserContext) Descriptor() ([]byte, []int) {
	return file_iam_v1_type_proto_rawDescGZIP(), []int{2}
}

func (x *UserContext) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UserContext) GetAssumeBy() *User {
	if x != nil {
		return x.AssumeBy
	}
	return nil
}

type BaseUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user/team uid
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// user/team name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// user/team avatar url
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
}

func (x *BaseUser) Reset() {
	*x = BaseUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_type_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseUser) ProtoMessage() {}

func (x *BaseUser) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_type_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseUser.ProtoReflect.Descriptor instead.
func (*BaseUser) Descriptor() ([]byte, []int) {
	return file_iam_v1_type_proto_rawDescGZIP(), []int{3}
}

func (x *BaseUser) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *BaseUser) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BaseUser) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

type IDType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *IDType) Reset() {
	*x = IDType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_type_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IDType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IDType) ProtoMessage() {}

func (x *IDType) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_type_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IDType.ProtoReflect.Descriptor instead.
func (*IDType) Descriptor() ([]byte, []int) {
	return file_iam_v1_type_proto_rawDescGZIP(), []int{4}
}

type Feperm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pages visible to the user
	VisiblePages []string `protobuf:"bytes,1,rep,name=visible_pages,json=visiblePages,proto3" json:"visible_pages,omitempty"`
}

func (x *Feperm) Reset() {
	*x = Feperm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_type_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Feperm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Feperm) ProtoMessage() {}

func (x *Feperm) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_type_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Feperm.ProtoReflect.Descriptor instead.
func (*Feperm) Descriptor() ([]byte, []int) {
	return file_iam_v1_type_proto_rawDescGZIP(), []int{5}
}

func (x *Feperm) GetVisiblePages() []string {
	if x != nil {
		return x.VisiblePages
	}
	return nil
}

type EditAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EditAction) Reset() {
	*x = EditAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_type_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditAction) ProtoMessage() {}

func (x *EditAction) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_type_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditAction.ProtoReflect.Descriptor instead.
func (*EditAction) Descriptor() ([]byte, []int) {
	return file_iam_v1_type_proto_rawDescGZIP(), []int{6}
}

type Team_Type struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Team_Type) Reset() {
	*x = Team_Type{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_type_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Team_Type) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Team_Type) ProtoMessage() {}

func (x *Team_Type) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_type_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Team_Type.ProtoReflect.Descriptor instead.
func (*Team_Type) Descriptor() ([]byte, []int) {
	return file_iam_v1_type_proto_rawDescGZIP(), []int{0, 0}
}

type User_Gender struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *User_Gender) Reset() {
	*x = User_Gender{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_type_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User_Gender) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User_Gender) ProtoMessage() {}

func (x *User_Gender) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_type_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User_Gender.ProtoReflect.Descriptor instead.
func (*User_Gender) Descriptor() ([]byte, []int) {
	return file_iam_v1_type_proto_rawDescGZIP(), []int{1, 0}
}

var File_iam_v1_type_proto protoreflect.FileDescriptor

var file_iam_v1_type_proto_rawDesc = []byte{
	0x0a, 0x11, 0x69, 0x61, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62,
	0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x96, 0x03, 0x0a, 0x04, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x15, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52,
	0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x12, 0x34, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x61, 0x6d,
	0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x3e, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0x49, 0x0a, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x41, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x64,
	0x65, 0x6d, 0x61, 0x6e, 0x64, 0x65, 0x72, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x73, 0x75, 0x70, 0x70, 0x6c,
	0x69, 0x65, 0x72, 0x10, 0x03, 0x3a, 0x46, 0xba, 0x47, 0x43, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64,
	0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x04, 0x64, 0x65, 0x73, 0x63, 0xba, 0x01,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0xba, 0x01, 0x04, 0x74, 0x79, 0x70, 0x65, 0xba, 0x01,
	0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0xba, 0x01, 0x04, 0x63, 0x69, 0x74, 0x79,
	0xba, 0x01, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0xe8, 0x04,
	0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x72, 0x6f, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x17, 0x0a, 0x07,
	0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x72, 0x67, 0x55, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x08, 0x6f, 0x72, 0x67, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x65, 0x61, 0x6d, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x6f, 0x72, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x21, 0x0a, 0x09, 0x69, 0x6d, 0x70, 0x65, 0x72, 0x66, 0x65, 0x63, 0x74, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x08, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x09, 0x69, 0x6d, 0x70, 0x65,
	0x72, 0x66, 0x65, 0x63, 0x74, 0x12, 0x3e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x10, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x1a, 0x37, 0x0a, 0x06, 0x47, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x22, 0x2d, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04,
	0x6d, 0x61, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x66, 0x65, 0x6d, 0x61, 0x6c, 0x65,
	0x10, 0x02, 0x3a, 0x54, 0xba, 0x47, 0x51, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0xba, 0x01, 0x04,
	0x72, 0x6f, 0x6c, 0x65, 0xba, 0x01, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0xba, 0x01, 0x08,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0xba, 0x01, 0x04, 0x63, 0x69, 0x74, 0x79, 0xba,
	0x01, 0x09, 0x69, 0x6d, 0x70, 0x65, 0x72, 0x66, 0x65, 0x63, 0x74, 0xba, 0x01, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0x5a, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x20, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x09, 0x61, 0x73, 0x73,
	0x75, 0x6d, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x08, 0x61, 0x73, 0x73, 0x75,
	0x6d, 0x65, 0x42, 0x79, 0x22, 0x63, 0x0a, 0x08, 0x42, 0x61, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x3a, 0x19,
	0xba, 0x47, 0x16, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0xba, 0x01, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x22, 0x40, 0x0a, 0x06, 0x49, 0x44, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x36, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03,
	0x75, 0x69, 0x64, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x10, 0x02,
	0x12, 0x09, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x10, 0x03, 0x22, 0x42, 0x0a, 0x06, 0x46,
	0x65, 0x70, 0x65, 0x72, 0x6d, 0x12, 0x23, 0x0a, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65,
	0x5f, 0x70, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x69,
	0x73, 0x69, 0x62, 0x6c, 0x65, 0x50, 0x61, 0x67, 0x65, 0x73, 0x3a, 0x13, 0xba, 0x47, 0x10, 0xba,
	0x01, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x73, 0x22,
	0x3a, 0x0a, 0x0a, 0x45, 0x64, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x2c, 0x0a,
	0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x61, 0x64, 0x64, 0x10, 0x01, 0x12,
	0x0a, 0x0a, 0x06, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x10, 0x02, 0x42, 0x3b, 0x0a, 0x06, 0x69,
	0x61, 0x6d, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e,
	0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x69, 0x61,
	0x6d, 0x2f, 0x76, 0x31, 0x3b, 0x69, 0x61, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_iam_v1_type_proto_rawDescOnce sync.Once
	file_iam_v1_type_proto_rawDescData = file_iam_v1_type_proto_rawDesc
)

func file_iam_v1_type_proto_rawDescGZIP() []byte {
	file_iam_v1_type_proto_rawDescOnce.Do(func() {
		file_iam_v1_type_proto_rawDescData = protoimpl.X.CompressGZIP(file_iam_v1_type_proto_rawDescData)
	})
	return file_iam_v1_type_proto_rawDescData
}

var file_iam_v1_type_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_iam_v1_type_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_iam_v1_type_proto_goTypes = []interface{}{
	(Team_Type_Enum)(0),           // 0: iam.v1.Team.Type.Enum
	(User_Gender_Enum)(0),         // 1: iam.v1.User.Gender.Enum
	(IDType_Enum)(0),              // 2: iam.v1.IDType.Enum
	(EditAction_Enum)(0),          // 3: iam.v1.EditAction.Enum
	(*Team)(nil),                  // 4: iam.v1.Team
	(*User)(nil),                  // 5: iam.v1.User
	(*UserContext)(nil),           // 6: iam.v1.UserContext
	(*BaseUser)(nil),              // 7: iam.v1.BaseUser
	(*IDType)(nil),                // 8: iam.v1.IDType
	(*Feperm)(nil),                // 9: iam.v1.Feperm
	(*EditAction)(nil),            // 10: iam.v1.EditAction
	(*Team_Type)(nil),             // 11: iam.v1.Team.Type
	(*User_Gender)(nil),           // 12: iam.v1.User.Gender
	(*timestamppb.Timestamp)(nil), // 13: google.protobuf.Timestamp
}
var file_iam_v1_type_proto_depIdxs = []int32{
	0,  // 0: iam.v1.Team.type:type_name -> iam.v1.Team.Type.Enum
	13, // 1: iam.v1.Team.created_at:type_name -> google.protobuf.Timestamp
	1,  // 2: iam.v1.User.gender:type_name -> iam.v1.User.Gender.Enum
	0,  // 3: iam.v1.User.org_type:type_name -> iam.v1.Team.Type.Enum
	13, // 4: iam.v1.User.created_at:type_name -> google.protobuf.Timestamp
	5,  // 5: iam.v1.UserContext.user:type_name -> iam.v1.User
	5,  // 6: iam.v1.UserContext.assume_by:type_name -> iam.v1.User
	7,  // [7:7] is the sub-list for method output_type
	7,  // [7:7] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_iam_v1_type_proto_init() }
func file_iam_v1_type_proto_init() {
	if File_iam_v1_type_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_iam_v1_type_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Team); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_type_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_type_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserContext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_type_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_type_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IDType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_type_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Feperm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_type_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_type_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Team_Type); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_type_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*User_Gender); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_iam_v1_type_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_iam_v1_type_proto_goTypes,
		DependencyIndexes: file_iam_v1_type_proto_depIdxs,
		EnumInfos:         file_iam_v1_type_proto_enumTypes,
		MessageInfos:      file_iam_v1_type_proto_msgTypes,
	}.Build()
	File_iam_v1_type_proto = out.File
	file_iam_v1_type_proto_rawDesc = nil
	file_iam_v1_type_proto_goTypes = nil
	file_iam_v1_type_proto_depIdxs = nil
}
