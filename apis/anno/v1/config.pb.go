// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anno/v1/config.proto

package anno

import (
	_ "github.com/google/gnostic/openapiv3"
	types "gitlab.rp.konvery.work/platform/apis/types"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Errors struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// error-reason -> (language -> display-name)
	Errors map[string]*types.Multilingual `protobuf:"bytes,1,rep,name=errors,proto3" json:"errors,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Errors) Reset() {
	*x = Errors{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Errors) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Errors) ProtoMessage() {}

func (x *Errors) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Errors.ProtoReflect.Descriptor instead.
func (*Errors) Descriptor() ([]byte, []int) {
	return file_anno_v1_config_proto_rawDescGZIP(), []int{0}
}

func (x *Errors) GetErrors() map[string]*types.Multilingual {
	if x != nil {
		return x.Errors
	}
	return nil
}

type GetVersionReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *GetVersionReply) Reset() {
	*x = GetVersionReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVersionReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVersionReply) ProtoMessage() {}

func (x *GetVersionReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVersionReply.ProtoReflect.Descriptor instead.
func (*GetVersionReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_config_proto_rawDescGZIP(), []int{1}
}

func (x *GetVersionReply) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type CommentReasonClass struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reason class
	Class *types.DisplayItem `protobuf:"bytes,1,opt,name=class,proto3" json:"class,omitempty"`
	// reasons in the class
	Reasons []*types.DisplayItem `protobuf:"bytes,2,rep,name=reasons,proto3" json:"reasons,omitempty"`
}

func (x *CommentReasonClass) Reset() {
	*x = CommentReasonClass{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommentReasonClass) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommentReasonClass) ProtoMessage() {}

func (x *CommentReasonClass) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommentReasonClass.ProtoReflect.Descriptor instead.
func (*CommentReasonClass) Descriptor() ([]byte, []int) {
	return file_anno_v1_config_proto_rawDescGZIP(), []int{2}
}

func (x *CommentReasonClass) GetClass() *types.DisplayItem {
	if x != nil {
		return x.Class
	}
	return nil
}

func (x *CommentReasonClass) GetReasons() []*types.DisplayItem {
	if x != nil {
		return x.Reasons
	}
	return nil
}

type ListCommentReasonsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Classes []*CommentReasonClass `protobuf:"bytes,1,rep,name=classes,proto3" json:"classes,omitempty"`
}

func (x *ListCommentReasonsReply) Reset() {
	*x = ListCommentReasonsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCommentReasonsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCommentReasonsReply) ProtoMessage() {}

func (x *ListCommentReasonsReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCommentReasonsReply.ProtoReflect.Descriptor instead.
func (*ListCommentReasonsReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_config_proto_rawDescGZIP(), []int{3}
}

func (x *ListCommentReasonsReply) GetClasses() []*CommentReasonClass {
	if x != nil {
		return x.Classes
	}
	return nil
}

type PutCommentReasonsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// corresponding classes will be replaced by this request
	Classes []*CommentReasonClass `protobuf:"bytes,1,rep,name=classes,proto3" json:"classes,omitempty"`
}

func (x *PutCommentReasonsRequest) Reset() {
	*x = PutCommentReasonsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_config_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PutCommentReasonsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PutCommentReasonsRequest) ProtoMessage() {}

func (x *PutCommentReasonsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_config_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PutCommentReasonsRequest.ProtoReflect.Descriptor instead.
func (*PutCommentReasonsRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_config_proto_rawDescGZIP(), []int{4}
}

func (x *PutCommentReasonsRequest) GetClasses() []*CommentReasonClass {
	if x != nil {
		return x.Classes
	}
	return nil
}

var File_anno_v1_config_proto protoreflect.FileDescriptor

var file_anno_v1_config_proto_rawDesc = []byte{
	0x0a, 0x14, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f,
	0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8d, 0x01, 0x0a, 0x06, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x33, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x1a, 0x4e, 0x0a, 0x0b, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x29, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x75, 0x61, 0x6c, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x2b, 0x0a, 0x0f, 0x47, 0x65,
	0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x83, 0x01, 0x0a, 0x12, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x28,
	0x0a, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x2c, 0x0a, 0x07, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x3a, 0x15, 0xba, 0x47, 0x12, 0xba, 0x01, 0x05, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0xba, 0x01, 0x07, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x22, 0x50, 0x0a,
	0x17, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x35, 0x0a, 0x07, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x22,
	0x51, 0x0a, 0x18, 0x50, 0x75, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x07, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x65, 0x73, 0x32, 0x84, 0x03, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x49,
	0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x0f, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x22, 0x12, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0c, 0x12, 0x0a, 0x2f,
	0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x53, 0x0a, 0x0a, 0x47, 0x65, 0x74,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a,
	0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x13, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x0d, 0x12, 0x0b, 0x2f, 0x76, 0x31, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x6a,
	0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x20, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1a,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x12, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x12, 0x6d, 0x0a, 0x11, 0x50, 0x75,
	0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x12,
	0x21, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x74, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x17, 0x3a, 0x01, 0x2a, 0x1a, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x42, 0x3e, 0x0a, 0x07, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x31, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72,
	0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e, 0x6e, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_anno_v1_config_proto_rawDescOnce sync.Once
	file_anno_v1_config_proto_rawDescData = file_anno_v1_config_proto_rawDesc
)

func file_anno_v1_config_proto_rawDescGZIP() []byte {
	file_anno_v1_config_proto_rawDescOnce.Do(func() {
		file_anno_v1_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_anno_v1_config_proto_rawDescData)
	})
	return file_anno_v1_config_proto_rawDescData
}

var file_anno_v1_config_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_anno_v1_config_proto_goTypes = []interface{}{
	(*Errors)(nil),                   // 0: anno.v1.Errors
	(*GetVersionReply)(nil),          // 1: anno.v1.GetVersionReply
	(*CommentReasonClass)(nil),       // 2: anno.v1.CommentReasonClass
	(*ListCommentReasonsReply)(nil),  // 3: anno.v1.ListCommentReasonsReply
	(*PutCommentReasonsRequest)(nil), // 4: anno.v1.PutCommentReasonsRequest
	nil,                              // 5: anno.v1.Errors.ErrorsEntry
	(*types.DisplayItem)(nil),        // 6: types.DisplayItem
	(*types.Multilingual)(nil),       // 7: types.Multilingual
	(*emptypb.Empty)(nil),            // 8: google.protobuf.Empty
}
var file_anno_v1_config_proto_depIdxs = []int32{
	5,  // 0: anno.v1.Errors.errors:type_name -> anno.v1.Errors.ErrorsEntry
	6,  // 1: anno.v1.CommentReasonClass.class:type_name -> types.DisplayItem
	6,  // 2: anno.v1.CommentReasonClass.reasons:type_name -> types.DisplayItem
	2,  // 3: anno.v1.ListCommentReasonsReply.classes:type_name -> anno.v1.CommentReasonClass
	2,  // 4: anno.v1.PutCommentReasonsRequest.classes:type_name -> anno.v1.CommentReasonClass
	7,  // 5: anno.v1.Errors.ErrorsEntry.value:type_name -> types.Multilingual
	8,  // 6: anno.v1.Configs.ListErrors:input_type -> google.protobuf.Empty
	8,  // 7: anno.v1.Configs.GetVersion:input_type -> google.protobuf.Empty
	8,  // 8: anno.v1.Configs.ListCommentReasons:input_type -> google.protobuf.Empty
	4,  // 9: anno.v1.Configs.PutCommentReasons:input_type -> anno.v1.PutCommentReasonsRequest
	0,  // 10: anno.v1.Configs.ListErrors:output_type -> anno.v1.Errors
	1,  // 11: anno.v1.Configs.GetVersion:output_type -> anno.v1.GetVersionReply
	3,  // 12: anno.v1.Configs.ListCommentReasons:output_type -> anno.v1.ListCommentReasonsReply
	8,  // 13: anno.v1.Configs.PutCommentReasons:output_type -> google.protobuf.Empty
	10, // [10:14] is the sub-list for method output_type
	6,  // [6:10] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_anno_v1_config_proto_init() }
func file_anno_v1_config_proto_init() {
	if File_anno_v1_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_anno_v1_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Errors); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVersionReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommentReasonClass); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCommentReasonsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_config_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PutCommentReasonsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anno_v1_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_anno_v1_config_proto_goTypes,
		DependencyIndexes: file_anno_v1_config_proto_depIdxs,
		MessageInfos:      file_anno_v1_config_proto_msgTypes,
	}.Build()
	File_anno_v1_config_proto = out.File
	file_anno_v1_config_proto_rawDesc = nil
	file_anno_v1_config_proto_goTypes = nil
	file_anno_v1_config_proto_depIdxs = nil
}
