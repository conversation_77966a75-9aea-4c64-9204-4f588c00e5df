// source: anno/v1/lot.proto
package biz

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"math/rand"
	"os"
	"time"

	"anno/api/client"
	"anno/workflow/common"

	"github.com/samber/lo"
	"github.com/spf13/cast"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"gitlab.rp.konvery.work/platform/pkg/sampler"
)

type LotState string

func (o LotState) IsCanceled() bool { return o == LotStateCanceled }
func (o LotState) IsFinal() bool    { return o == LotStateFinished || o == LotStateCanceled }
func (o LotState) IsUnstart() bool  { return o == LotStateUnstart }
func (o LotState) String() string   { return string(o) }

const (
	LotStateUnspecified  LotState = "unspecified"
	LotStateUnstart      LotState = "unstart" // wait for manual start
	LotStateInitializing LotState = "initializing"
	LotStateOngoing      LotState = "ongoing"
	LotStateFinished     LotState = "finished"
	LotStatePaused       LotState = "paused"
	LotStateCanceled     LotState = "canceled"
)

type LotExtraData struct {
	AnnoResultUrl string `json:"anno_result_url"`
}

type LotExtraDataType = serial.Type[*LotExtraData]

type Lot struct {
	ID int64 `json:"id" gorm:"default:null"`
	// Uid        string   `json:"uid" gorm:"default:null"`
	Name          string   `json:"name" gorm:"default:null"`
	Desc          string   `json:"desc" gorm:"default:null"`
	Type          string   `json:"type" gorm:"default:null"`
	ProjectID     int64    `json:"project_id" gorm:"default:null"`
	OrderID       int64    `json:"order_id" gorm:"default:null"`
	DataUid       string   `json:"data_uid" gorm:"default:null"`
	DataType      string   `json:"data_type" gorm:"default:null"`
	Autostart     bool     `json:"autostart" gorm:"default:null"`
	Priority      int32    `json:"priority" gorm:"default:null"`
	JobSize       int32    `json:"job_size" gorm:"default:null"`
	IsFrameSeries bool     `json:"is_frame_series" gorm:"default:null"`
	PhaseCount    int      `json:"phase_count" gorm:"default:null"`
	State         LotState `json:"state" gorm:"default:null"`
	DataSize      int32    `json:"data_size" gorm:"default:null"`
	Subtypes      int32    `json:"subtypes" gorm:"default:null"`
	InsCnt        int32    `json:"ins_cnt" gorm:"default:null"`
	InsTotal      int32    `json:"ins_total" gorm:"default:null"` // include interpolated objects
	Error         LotError `json:"error" gorm:"default:null"`
	HasError      bool     `json:"has_error" gorm:"default:null"`
	JobReady      bool     `json:"job_ready" gorm:"default:null"`
	JobCount      int      `json:"job_count" gorm:"default:null"` // major job count

	CreatorUid string    `json:"creator_uid" gorm:"default:null"`
	OrgUid     string    `json:"org_uid" gorm:"default:null"`
	Hierarchy  string    `json:"hierarchy" gorm:"default:null"`
	ExpEndTime time.Time `json:"exp_end_time" gorm:"default:null"`
	UpdatedAt  time.Time `json:"updated_at" gorm:"default:null"`
	CreatedAt  time.Time `json:"created_at" gorm:"default:null"`
	DeletedAt  DeletedAt `json:"deleted_at" gorm:"default:null"`

	ExtraData      LotExtraDataType `json:"extra_data" gorm:"default:null"`
	CanExportAnnos bool             `json:"can_export_annos" gorm:"default:null"`
	Tags           repo.StringArray `json:"tags" gorm:"default:null;type:text[]"`

	Ally    *Lotally    `json:"-" gorm:"-"`
	Project *Project    `json:"-" gorm:"-"`
	Phases  []*Lotphase `json:"-" gorm:"-"`
	// Template *Lottpl        `json:"-" gorm:"-"`
	// TplID      int64  `json:"tpl_id" gorm:"default:null"`
}

func (o *Lot) GetID() int64   { return o.ID }
func (o *Lot) GetUid() string { return kid.StringID(o.ID) }
func (o *Lot) UidFld() string { panic("code error") }
func (o *Lot) EnsureID() {
	if o.ID == 0 {
		o.ID = kid.NewID()
	}
}

const LotTableName = "lots"

type LotsSlfd string

func (o LotsSlfd) String() string { return string(o) }

func (o LotsSlfd) WithTable() string { return LotTableName + "." + string(o) }

var (
	Lot_             = field.RegObject(&Lot{})
	LotUpdatableFlds = field.NewModel(LotTableName, Lot_, "Name", "Desc", "Priority", "DataSize", "DataType", "OrderID",
		"PhaseCount", "State", "InsCnt", "InsTotal", "Error", "HasError", "JobReady", "JobCount", "IsFrameSeries", "ExtraData", "CanExportAnnos")

	// field name in snake case
	LotSfldID            = LotsSlfd(field.Sname(&Lot_.ID))
	LotSfldName          = LotsSlfd(field.Sname(&Lot_.Name))
	LotSfldDesc          = LotsSlfd(field.Sname(&Lot_.Desc))
	LotSfldType          = LotsSlfd(field.Sname(&Lot_.Type))
	LotSfldProjectID     = LotsSlfd(field.Sname(&Lot_.ProjectID))
	LotSfldDataUid       = LotsSlfd(field.Sname(&Lot_.DataUid))
	LotSfldAutostart     = LotsSlfd(field.Sname(&Lot_.Autostart))
	LotSfldPriority      = LotsSlfd(field.Sname(&Lot_.Priority))
	LotSfldJobSize       = LotsSlfd(field.Sname(&Lot_.JobSize))
	LotSfldPhaseCount    = LotsSlfd(field.Sname(&Lot_.PhaseCount))
	LotSfldState         = LotsSlfd(field.Sname(&Lot_.State))
	LotSfldDataSize      = LotsSlfd(field.Sname(&Lot_.DataSize))
	LotSfldDataType      = LotsSlfd(field.Sname(&Lot_.DataType))
	LotSfldSubtypes      = LotsSlfd(field.Sname(&Lot_.Subtypes))
	LotSfldInsCnt        = LotsSlfd(field.Sname(&Lot_.InsCnt))
	LotSfldInsTotal      = LotsSlfd(field.Sname(&Lot_.InsTotal))
	LotSfldError         = LotsSlfd(field.Sname(&Lot_.Error))
	LotSfldHasError      = LotsSlfd(field.Sname(&Lot_.HasError))
	LotSfldJobReady      = LotsSlfd(field.Sname(&Lot_.JobReady))
	LotSfldJobCount      = LotsSlfd(field.Sname(&Lot_.JobCount))
	LotSfldCreatorUid    = LotsSlfd(field.Sname(&Lot_.CreatorUid))
	LotSfldOrgUid        = LotsSlfd(field.Sname(&Lot_.OrgUid))
	LotSfldOrderID       = LotsSlfd(field.Sname(&Lot_.OrderID))
	LotSfldHierarchy     = LotsSlfd(field.Sname(&Lot_.Hierarchy))
	LotSfldExpEndTime    = LotsSlfd(field.Sname(&Lot_.ExpEndTime))
	LotSfldIsFrameSeries = LotsSlfd(field.Sname(&Lot_.IsFrameSeries))
	LotSfldExtraData     = LotsSlfd(field.Sname(&Lot_.ExtraData))

	LotSfldCanExportAnnos = LotsSlfd(field.Sname(&Lot_.CanExportAnnos))
	LotSfldTags           = LotsSlfd(field.Sname(&Lot_.Tags))
)

type LotListFilter struct {
	IDs         []int64
	OrgUid      string
	CreatorUid  string
	NamePattern string
	Type        string
	States      []LotState
	OrderID     int64
	// ProjectID   int64

	BizgranteeUid  string // to check kam's grants
	SpecgranteeUid string // to check pm's grants
}

type LotFilterByExecutor struct {
	UserUid      string
	ExecteamUids []string
	NamePattern  string
	Type         string
	States       []LotState
	Claimable    bool // to query the lot that can be claimed
}

type LotJobQuota struct {
	*ExecteamQuota
	LotphaseID  int64
	ClaimedJobs int32
}

type LotsRepo interface {
	DoTx(ctx context.Context, fn func(ctx context.Context, tx Tx) error) (err error)

	Create(context.Context, *Lot) (*Lot, error)
	Update(context.Context, *Lot, *FieldMask) (*Lot, error)
	GetByID(ctx context.Context, id int64, loadAsso bool) (*Lot, error)
	DeleteByID(context.Context, int64) error
	List(context.Context, *LotListFilter, Pager) ([]*Lot, error)
	Count(context.Context, *LotListFilter) (int, error)
	GetLotIDsByFilter(ctx context.Context, filter *LotListFilter) ([]int64, error)
	HasHoldingLots(ctx context.Context, orgUid string) (bool, error)

	// query lots by the assigned executor or execteams
	QueryByExecutor(context.Context, *LotFilterByExecutor, Pager) ([]*Lot, error)
	CountByExecutor(context.Context, *LotFilterByExecutor) (int, error)
	GetLotIDsByExecutor(ctx context.Context, filter *LotFilterByExecutor) ([]int64, error)
	GetLotCountByExecutor(ctx context.Context, filter *LotFilterByExecutor) (map[LotState]int32, error)

	ChangeState(ctx context.Context, lot *Lot, state LotState) (success bool, err error)
	GetSampleBits(ctx context.Context, id int64) (SampleBits, error)

	GetPhase(ctx context.Context, lotID int64, phase int, subtype string) (*Lotphase, error)
	LoadPhases(ctx context.Context, lotID int64, phase int, execteams ...string) (phases []*Lotphase, err error)
	BatchCreatePhase(ctx context.Context, phase []*Lotphase) ([]*Lotphase, error)
	UpdatePhase(ctx context.Context, p *Lotphase, fldMask *FieldMask) (*Lotphase, error)
	UpdatePhaseWithIncrement(ctx context.Context, p *Lotphase, increments map[string]int, fldMask *FieldMask) (*Lotphase, error)
	DeletePhases(ctx context.Context, p []*Lotphase) error
	GetExecteamQuota(ctx context.Context, lotID int64, executorUid string) (*LotJobQuota, error)

	AddPhaseExecutors(ctx context.Context, phase *Lotphase, teamUid string, userUids []string) error
	DeletePhaseExecutors(ctx context.Context, phase *Lotphase, teamUid string, userUids []string) error
	ListPhaseExecutors(ctx context.Context, phase *Lotphase, teamUid string, pager Pager) ([]string, error)
	CountPhaseExecutors(ctx context.Context, phase *Lotphase, teamUid string) (int, error)
	ListExecutorsByLotID(ctx context.Context, lotID int64) ([]*Lotexecutor, error)
	AddExecutors(ctx context.Context, executors []*Lotexecutor) error
}

type LotsBiz struct {
	repo    LotsRepo
	jobrepo JobsRepo
	bgtask  BackgroundTask
	log     *log.Helper
}

func NewLotsBiz(repo LotsRepo, jobrepo JobsRepo, bgtask BackgroundTask, logger log.Logger) *LotsBiz {
	return &LotsBiz{repo: repo, jobrepo: jobrepo, bgtask: bgtask, log: log.NewHelper(logger)}
}

func (o *LotsBiz) Repo() LotsRepo { return o.repo }

func (o *LotsBiz) Create(ctx context.Context, p *Lot) (lot *Lot, err error) {
	o.log.Info(ctx, "CreateLot", "lot", p)

	p.State = LotStateUnstart
	err = o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		lot, err = o.repo.Create(ctx, p)
		if err != nil {
			return err
		}

		// TODO: what if it is a sub-lot?
		err = client.CreateAccessPolicies(ctx, PermClsLot, p.GetUid(),
			[]string{client.UserScope(p.CreatorUid)}, []string{client.GroupScope(p.OrgUid)})
		if err != nil {
			return err
		}
		err = o.grantLotExecteamAccess(ctx, lot)
		if err != nil {
			return err
		}

		return o.bgtask.SignalEvent(ctx, &common.Event{LotID: lot.ID, Event: common.EvtLotCreated})
	})
	if err != nil {
		return nil, err
	}
	return
}

func (o *LotsBiz) grantLotExecteamAccess(ctx context.Context, lot *Lot) error {
	teams := lo.Map(lot.Phases, func(v *Lotphase, _ int) string { return v.Execteam })
	teams = lo.WithoutEmpty(teams)
	if len(teams) > 0 {
		teams = lo.Uniq(teams)
		err := o.GrantExecteamAccess(ctx, lot.ID, teams...)
		if err != nil {
			return err
		}
	}
	return nil
}

func (o *LotsBiz) Update(ctx context.Context, p *Lot, fldMask *FieldMask, actions ...TxAction) (lot *Lot, err error) {
	o.log.Info(ctx, "UpdateLot", "lot", p)
	if len(actions) == 0 {
		return o.repo.Update(ctx, p, fldMask)
	}

	err = o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		lot, err = o.repo.Update(ctx, p, fldMask)
		if err != nil {
			return err
		}
		for _, fn := range actions {
			if err = fn(ctx, lot); err != nil {
				return err
			}
		}
		// check if there is really any config update affecting jobs execution
		// cfgChanged := false
		// for _, fld := range []string{"JobSize"} {
		// 	if fldMask.HasField(fld) {
		// 		cfgChanged = true
		// 		break
		// 	}
		// }
		// if cfgChanged {
		// 	err = o.bgtask.SignalLotEvent(ctx, lot, common.EvtConfigChannged)
		// }
		return err
	})
	if err != nil {
		return nil, err
	}
	return
}

func (o *LotsBiz) GetByID(ctx context.Context, id int64, loadAsso bool) (*Lot, error) {
	return o.repo.GetByID(ctx, id, loadAsso)
}

func (o *LotsBiz) GetByUid(ctx context.Context, uid string) (*Lot, error) {
	return o.GetByID(ctx, kid.ParseID(uid), true)
}

func (o *LotsBiz) GetExtraDataByID(ctx context.Context, id int64) (*LotExtraData, error) {
	lot, err := o.GetByID(ctx, id, false)
	if err != nil {
		return nil, err
	}
	return lot.ExtraData.E, nil
}

func (o *LotsBiz) DeleteByID(ctx context.Context, id int64) error {
	o.log.Info(ctx, "DeleteByID", "id", id)
	return o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		if err := o.repo.DeleteByID(ctx, id); err != nil {
			return err
		}
		return client.DeleteAccessPolicies(ctx, PermClsLot, kid.StringID(id))
	})
}

func (o *LotsBiz) DeleteByUid(ctx context.Context, uid string) error {
	o.log.Info(ctx, "DeleteByUidLot", "uid", uid)
	return o.DeleteByID(ctx, kid.ParseID(uid))
}

// func (o *LotsBiz) GetUids(ctx context.Context, ids ...int64) (map[int64]string, error) {
// 	return o.repo.GetUids(ctx, ids...)
// }

func (o *LotsBiz) List(ctx context.Context, filter *LotListFilter, pager Pager) ([]*Lot, error) {
	o.log.Info(ctx, "ListLot", "param", filter)
	return o.repo.List(ctx, filter, pager)
}

func (o *LotsBiz) Count(ctx context.Context, filter *LotListFilter) (int, error) {
	return o.repo.Count(ctx, filter)
}

func (o *LotsBiz) GetSampleBits(ctx context.Context, id int64) (SampleBits, error) {
	return o.repo.GetSampleBits(ctx, id)
}

func (o *LotsBiz) UpdateState(ctx context.Context, lot *Lot, state LotState,
	precond func(lot *Lot) bool, actions ...TxAction) (success bool, err error) {
	if lot.State.IsCanceled() {
		return false, errors.NewErrFailedPrecondition(errors.WithMessage("lot is already canceled"))
	}
	if precond != nil && !precond(lot) {
		return false, errors.NewErrForbidden(errors.WithMessage("lot is in wrong state"),
			errors.WithModel("lot"), errors.WithFields("state"))
	}
	err = o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		success, err = o.repo.ChangeState(ctx, lot, state)
		if !success {
			return err
		}
		for _, fn := range actions {
			if err = fn(ctx, lot); err != nil {
				return err
			}
		}
		return nil
	})
	return
}

func (o *LotsBiz) StartLot(ctx context.Context, lot *Lot) error {
	success, err := o.UpdateState(ctx, lot, LotStateOngoing,
		func(lot *Lot) bool {
			return lot.State == LotStatePaused || lot.State == LotStateUnstart
		},
		func(ctx context.Context, v any) error {
			lot := v.(*Lot)
			return o.bgtask.SignalEvent(ctx, &common.Event{
				Event: common.EvtLotStarted,
				LotID: lot.ID,
			})
		},
	)
	if err == nil && !success {
		return errors.NewErrInProcess(errors.WithMessage("lot state changed before processing"))
	}
	return err
}

func (o *LotsBiz) RestartLot(ctx context.Context, lot *Lot) error {
	success, err := o.UpdateState(ctx, lot, LotStateOngoing,
		func(lot *Lot) bool {
			return lot.State == LotStateFinished
		},
	)
	if err == nil && !success {
		return errors.NewErrInProcess(errors.WithMessage("lot state changed before processing"))
	}
	return err
}

func (o *LotsBiz) PauseLot(ctx context.Context, lot *Lot) error {
	success, err := o.UpdateState(ctx, lot, LotStatePaused,
		func(lot *Lot) bool {
			return lot.State == LotStateOngoing
		},
		func(ctx context.Context, v any) error {
			lot := v.(*Lot)
			return o.bgtask.SignalEvent(ctx, &common.Event{
				Event: common.EvtLotPaused,
				LotID: lot.ID,
			})
		},
	)
	if err == nil && !success {
		return errors.NewErrInProcess(errors.WithMessage("lot state changed before processing"))
	}
	return err
}

func (o *LotsBiz) CancelLot(ctx context.Context, lot *Lot) error {
	success, err := o.UpdateState(ctx, lot, LotStateCanceled, nil,
		func(ctx context.Context, v any) error {
			lot := v.(*Lot)
			// TODO: remove execteam access policies
			return o.bgtask.SignalEvent(ctx, &common.Event{
				Event: common.EvtLotCanceled,
				LotID: lot.ID,
			})
		})
	if err == nil && !success {
		return errors.NewErrInProcess(errors.WithMessage("lot state changed before processing"))
	}
	return err
}

func (o *LotsBiz) RenewSampleBits(ctx context.Context, lot *Lot) (err error) {
	fmt.Println("---> generate sample bits")
	if lot.Ally == nil || lot.Phases == nil {
		lot, err = o.GetByID(ctx, lot.ID, true)
		if err != nil {
			return fmt.Errorf("failed to load lot: %w", err)
		}
	}
	if lot.JobCount == 0 || len(lot.Phases) == 0 {
		return nil
	}
	rnd := rand.New(rand.NewSource(lot.CreatedAt.Unix()))
	lotphaseList := LotphaseGroups(lot.Phases)
	spew.Dump("---> lot phase list: ", lotphaseList, len(lotphaseList))
	for i := LotphaseStartNumber; i < len(lotphaseList); i++ {
		p := lotphaseList[i][0] // using this first one is ok
		fmt.Println("gen args: ", p.SamplePercent, lot.JobCount)
		bits := sampler.GenSampleBits(p.SamplePercent, lot.JobCount, rnd.Intn)
		fmt.Println("gen bits: ", bits)
		lot.Ally.SampleBits = append(lot.Ally.SampleBits, bits)
	}
	_, err = o.Update(ctx, lot, field.NewMask(LotallySfldSampleBits))
	return
}

func (o *LotsBiz) GrantExecteamAccess(ctx context.Context, lotID int64, teamUids ...string) (err error) {
	// revoke access policy from old team and add access policy for the new team
	resource := LotScope(kid.StringID(lotID))
	users := lo.Map(teamUids, func(v string, _ int) string { return client.GroupScope(v) })
	o.log.Info(ctx, "grant access to execteam", "lot", resource, "users", users)
	policies, err := client.GetAttachedPolicies(ctx, resource, RoleJobViewer)
	if err != nil {
		return err
	}
	if len(policies) == 0 {
		_, err = client.CreatePolicy(ctx, resource, RoleJobViewer, users)
	} else {
		err = client.UpdatePolicy(ctx, policies[0], users)
	}
	return err
}

func (o *LotsBiz) AddPhaseExecutors(ctx context.Context, phase *Lotphase, teamUid string, userUids []string) error {
	return o.repo.AddPhaseExecutors(ctx, phase, teamUid, userUids)
}

func (o *LotsBiz) DeletePhaseExecutors(ctx context.Context, phase *Lotphase, teamUid string, userUids []string) error {
	return o.repo.DeletePhaseExecutors(ctx, phase, teamUid, userUids)
}

func (o *LotsBiz) ListPhaseExecutors(ctx context.Context, phase *Lotphase, teamUid string, pager Pager) ([]string, error) {
	return o.repo.ListPhaseExecutors(ctx, phase, teamUid, pager)
}

func (o *LotsBiz) ListExecutorsByLotID(ctx context.Context, lotID int64) ([]*Lotexecutor, error) {
	return o.repo.ListExecutorsByLotID(ctx, lotID)
}

func (o *LotsBiz) AddExecutors(ctx context.Context, executors []*Lotexecutor) error {
	return o.repo.AddExecutors(ctx, executors)
}

func (o *LotsBiz) ListPhaseExecutorsAll(ctx context.Context, phase *Lotphase, teamUid string) ([]string, error) {
	var allUids []string
	pager := Pager{Pagesz: 100}
	for {
		uids, err := o.ListPhaseExecutors(ctx, phase, teamUid, pager)
		if err != nil {
			return nil, err
		}
		if len(uids) == 0 {
			break
		}

		allUids = append(allUids, uids...)
		if len(uids) < pager.Pagesz {
			break
		} else {
			pager.Page += 1
		}
	}
	return allUids, nil
}

func (o *LotsBiz) CountPhaseExecutors(ctx context.Context, phase *Lotphase, teamUid string) (int, error) {
	return o.repo.CountPhaseExecutors(ctx, phase, teamUid)
}

func (o *LotsBiz) GetVisibleLotIDs(ctx context.Context, executorUid string, lotStates []LotState, claimable bool) ([]int64, error) {
	filter := &LotFilterByExecutor{
		UserUid:   executorUid,
		States:    lotStates,
		Claimable: claimable,
	}
	return o.repo.GetLotIDsByExecutor(ctx, filter)
}

func (o *LotsBiz) GetVisibleLotCount(ctx context.Context, executorUid string, lotStates []LotState, claimable bool) (map[LotState]int32, error) {
	filter := &LotFilterByExecutor{
		UserUid:   executorUid,
		States:    lotStates,
		Claimable: claimable,
	}
	return o.repo.GetLotCountByExecutor(ctx, filter)
}

func ShouldStripInterpolatedIns() bool {
	return cast.ToBool(os.Getenv("LOT_STRIP_INTERPOLATED_INS"))
}

func (o *LotsBiz) GetLotIDsByOrderID(ctx context.Context, orderID int64) ([]int64, error) {
	filter := &LotListFilter{
		OrderID: orderID,
	}
	return o.repo.GetLotIDsByFilter(ctx, filter)
}

func EnableJobCamParams() bool {
	return cast.ToBool(os.Getenv("LOT_ENABLE_JOB_CAM_PARAMS"))
}

func (o *LotsBiz) ClearAnnoResult(ctx context.Context, lot *Lot) error {
	if lot == nil || lot.ExtraData.E == nil {
		return nil
	}

	if e := lot.ExtraData.E; e.AnnoResultUrl != "" {
		lot.ExtraData.E.AnnoResultUrl = ""
		_, err := o.Update(ctx, lot, field.NewMask(string(LotSfldExtraData)))
		if err != nil {
			return fmt.Errorf("failed to clear lot AnnoResultUrl: %w", err)
		}
	}
	return nil
}
