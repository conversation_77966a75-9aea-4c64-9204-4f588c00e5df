// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: anno/v1/skill.proto

package anno

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Skills_CreateSkill_FullMethodName      = "/anno.v1.Skills/CreateSkill"
	Skills_UpdateSkill_FullMethodName      = "/anno.v1.Skills/UpdateSkill"
	Skills_DeleteSkill_FullMethodName      = "/anno.v1.Skills/DeleteSkill"
	Skills_GetSkill_FullMethodName         = "/anno.v1.Skills/GetSkill"
	Skills_ListSkill_FullMethodName        = "/anno.v1.Skills/ListSkill"
	Skills_GetUserSkill_FullMethodName     = "/anno.v1.Skills/GetUserSkill"
	Skills_ListUsersSkill_FullMethodName   = "/anno.v1.Skills/ListUsersSkill"
	Skills_AddUsersSkill_FullMethodName    = "/anno.v1.Skills/AddUsersSkill"
	Skills_DeleteUsersSkill_FullMethodName = "/anno.v1.Skills/DeleteUsersSkill"
	Skills_AddTeamSkill_FullMethodName     = "/anno.v1.Skills/AddTeamSkill"
	Skills_DeleteTeamSkill_FullMethodName  = "/anno.v1.Skills/DeleteTeamSkill"
)

// SkillsClient is the client API for Skills service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SkillsClient interface {
	CreateSkill(ctx context.Context, in *Skill, opts ...grpc.CallOption) (*Skill, error)
	UpdateSkill(ctx context.Context, in *UpdateSkillRequest, opts ...grpc.CallOption) (*Skill, error)
	DeleteSkill(ctx context.Context, in *DeleteSkillRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetSkill(ctx context.Context, in *GetSkillRequest, opts ...grpc.CallOption) (*Skill, error)
	ListSkill(ctx context.Context, in *ListSkillRequest, opts ...grpc.CallOption) (*ListSkillReply, error)
	// users skills
	GetUserSkill(ctx context.Context, in *GetUserSkillRequest, opts ...grpc.CallOption) (*GetUserSkillReply, error)
	ListUsersSkill(ctx context.Context, in *ListUsersSkillRequest, opts ...grpc.CallOption) (*ListUsersSkillReply, error)
	AddUsersSkill(ctx context.Context, in *AddUsersSkillRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	DeleteUsersSkill(ctx context.Context, in *DeleteUsersSkillRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// add skills to users in a team
	AddTeamSkill(ctx context.Context, in *AddTeamSkillRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// delete skills from users in a team
	DeleteTeamSkill(ctx context.Context, in *DeleteTeamSkillRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type skillsClient struct {
	cc grpc.ClientConnInterface
}

func NewSkillsClient(cc grpc.ClientConnInterface) SkillsClient {
	return &skillsClient{cc}
}

func (c *skillsClient) CreateSkill(ctx context.Context, in *Skill, opts ...grpc.CallOption) (*Skill, error) {
	out := new(Skill)
	err := c.cc.Invoke(ctx, Skills_CreateSkill_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *skillsClient) UpdateSkill(ctx context.Context, in *UpdateSkillRequest, opts ...grpc.CallOption) (*Skill, error) {
	out := new(Skill)
	err := c.cc.Invoke(ctx, Skills_UpdateSkill_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *skillsClient) DeleteSkill(ctx context.Context, in *DeleteSkillRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Skills_DeleteSkill_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *skillsClient) GetSkill(ctx context.Context, in *GetSkillRequest, opts ...grpc.CallOption) (*Skill, error) {
	out := new(Skill)
	err := c.cc.Invoke(ctx, Skills_GetSkill_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *skillsClient) ListSkill(ctx context.Context, in *ListSkillRequest, opts ...grpc.CallOption) (*ListSkillReply, error) {
	out := new(ListSkillReply)
	err := c.cc.Invoke(ctx, Skills_ListSkill_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *skillsClient) GetUserSkill(ctx context.Context, in *GetUserSkillRequest, opts ...grpc.CallOption) (*GetUserSkillReply, error) {
	out := new(GetUserSkillReply)
	err := c.cc.Invoke(ctx, Skills_GetUserSkill_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *skillsClient) ListUsersSkill(ctx context.Context, in *ListUsersSkillRequest, opts ...grpc.CallOption) (*ListUsersSkillReply, error) {
	out := new(ListUsersSkillReply)
	err := c.cc.Invoke(ctx, Skills_ListUsersSkill_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *skillsClient) AddUsersSkill(ctx context.Context, in *AddUsersSkillRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Skills_AddUsersSkill_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *skillsClient) DeleteUsersSkill(ctx context.Context, in *DeleteUsersSkillRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Skills_DeleteUsersSkill_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *skillsClient) AddTeamSkill(ctx context.Context, in *AddTeamSkillRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Skills_AddTeamSkill_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *skillsClient) DeleteTeamSkill(ctx context.Context, in *DeleteTeamSkillRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Skills_DeleteTeamSkill_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SkillsServer is the server API for Skills service.
// All implementations must embed UnimplementedSkillsServer
// for forward compatibility
type SkillsServer interface {
	CreateSkill(context.Context, *Skill) (*Skill, error)
	UpdateSkill(context.Context, *UpdateSkillRequest) (*Skill, error)
	DeleteSkill(context.Context, *DeleteSkillRequest) (*emptypb.Empty, error)
	GetSkill(context.Context, *GetSkillRequest) (*Skill, error)
	ListSkill(context.Context, *ListSkillRequest) (*ListSkillReply, error)
	// users skills
	GetUserSkill(context.Context, *GetUserSkillRequest) (*GetUserSkillReply, error)
	ListUsersSkill(context.Context, *ListUsersSkillRequest) (*ListUsersSkillReply, error)
	AddUsersSkill(context.Context, *AddUsersSkillRequest) (*emptypb.Empty, error)
	DeleteUsersSkill(context.Context, *DeleteUsersSkillRequest) (*emptypb.Empty, error)
	// add skills to users in a team
	AddTeamSkill(context.Context, *AddTeamSkillRequest) (*emptypb.Empty, error)
	// delete skills from users in a team
	DeleteTeamSkill(context.Context, *DeleteTeamSkillRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedSkillsServer()
}

// UnimplementedSkillsServer must be embedded to have forward compatible implementations.
type UnimplementedSkillsServer struct {
}

func (UnimplementedSkillsServer) CreateSkill(context.Context, *Skill) (*Skill, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSkill not implemented")
}
func (UnimplementedSkillsServer) UpdateSkill(context.Context, *UpdateSkillRequest) (*Skill, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSkill not implemented")
}
func (UnimplementedSkillsServer) DeleteSkill(context.Context, *DeleteSkillRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSkill not implemented")
}
func (UnimplementedSkillsServer) GetSkill(context.Context, *GetSkillRequest) (*Skill, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSkill not implemented")
}
func (UnimplementedSkillsServer) ListSkill(context.Context, *ListSkillRequest) (*ListSkillReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSkill not implemented")
}
func (UnimplementedSkillsServer) GetUserSkill(context.Context, *GetUserSkillRequest) (*GetUserSkillReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserSkill not implemented")
}
func (UnimplementedSkillsServer) ListUsersSkill(context.Context, *ListUsersSkillRequest) (*ListUsersSkillReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUsersSkill not implemented")
}
func (UnimplementedSkillsServer) AddUsersSkill(context.Context, *AddUsersSkillRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddUsersSkill not implemented")
}
func (UnimplementedSkillsServer) DeleteUsersSkill(context.Context, *DeleteUsersSkillRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUsersSkill not implemented")
}
func (UnimplementedSkillsServer) AddTeamSkill(context.Context, *AddTeamSkillRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTeamSkill not implemented")
}
func (UnimplementedSkillsServer) DeleteTeamSkill(context.Context, *DeleteTeamSkillRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTeamSkill not implemented")
}
func (UnimplementedSkillsServer) mustEmbedUnimplementedSkillsServer() {}

// UnsafeSkillsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SkillsServer will
// result in compilation errors.
type UnsafeSkillsServer interface {
	mustEmbedUnimplementedSkillsServer()
}

func RegisterSkillsServer(s grpc.ServiceRegistrar, srv SkillsServer) {
	s.RegisterService(&Skills_ServiceDesc, srv)
}

func _Skills_CreateSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Skill)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SkillsServer).CreateSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Skills_CreateSkill_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SkillsServer).CreateSkill(ctx, req.(*Skill))
	}
	return interceptor(ctx, in, info, handler)
}

func _Skills_UpdateSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SkillsServer).UpdateSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Skills_UpdateSkill_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SkillsServer).UpdateSkill(ctx, req.(*UpdateSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Skills_DeleteSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SkillsServer).DeleteSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Skills_DeleteSkill_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SkillsServer).DeleteSkill(ctx, req.(*DeleteSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Skills_GetSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SkillsServer).GetSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Skills_GetSkill_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SkillsServer).GetSkill(ctx, req.(*GetSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Skills_ListSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SkillsServer).ListSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Skills_ListSkill_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SkillsServer).ListSkill(ctx, req.(*ListSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Skills_GetUserSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SkillsServer).GetUserSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Skills_GetUserSkill_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SkillsServer).GetUserSkill(ctx, req.(*GetUserSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Skills_ListUsersSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUsersSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SkillsServer).ListUsersSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Skills_ListUsersSkill_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SkillsServer).ListUsersSkill(ctx, req.(*ListUsersSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Skills_AddUsersSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUsersSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SkillsServer).AddUsersSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Skills_AddUsersSkill_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SkillsServer).AddUsersSkill(ctx, req.(*AddUsersSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Skills_DeleteUsersSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUsersSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SkillsServer).DeleteUsersSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Skills_DeleteUsersSkill_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SkillsServer).DeleteUsersSkill(ctx, req.(*DeleteUsersSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Skills_AddTeamSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTeamSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SkillsServer).AddTeamSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Skills_AddTeamSkill_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SkillsServer).AddTeamSkill(ctx, req.(*AddTeamSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Skills_DeleteTeamSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTeamSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SkillsServer).DeleteTeamSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Skills_DeleteTeamSkill_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SkillsServer).DeleteTeamSkill(ctx, req.(*DeleteTeamSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Skills_ServiceDesc is the grpc.ServiceDesc for Skills service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Skills_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "anno.v1.Skills",
	HandlerType: (*SkillsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSkill",
			Handler:    _Skills_CreateSkill_Handler,
		},
		{
			MethodName: "UpdateSkill",
			Handler:    _Skills_UpdateSkill_Handler,
		},
		{
			MethodName: "DeleteSkill",
			Handler:    _Skills_DeleteSkill_Handler,
		},
		{
			MethodName: "GetSkill",
			Handler:    _Skills_GetSkill_Handler,
		},
		{
			MethodName: "ListSkill",
			Handler:    _Skills_ListSkill_Handler,
		},
		{
			MethodName: "GetUserSkill",
			Handler:    _Skills_GetUserSkill_Handler,
		},
		{
			MethodName: "ListUsersSkill",
			Handler:    _Skills_ListUsersSkill_Handler,
		},
		{
			MethodName: "AddUsersSkill",
			Handler:    _Skills_AddUsersSkill_Handler,
		},
		{
			MethodName: "DeleteUsersSkill",
			Handler:    _Skills_DeleteUsersSkill_Handler,
		},
		{
			MethodName: "AddTeamSkill",
			Handler:    _Skills_AddTeamSkill_Handler,
		},
		{
			MethodName: "DeleteTeamSkill",
			Handler:    _Skills_DeleteTeamSkill_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "anno/v1/skill.proto",
}
