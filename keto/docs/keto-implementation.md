# Keto 权限系统实现分析

本文档详细分析了 Keto 权限系统的实现机制，包括数据模型、转换流程、权限检查机制等核心组件。

## 1. 数据模型定义

在 `internal/persistence/sql/relationtuples.go` 中定义了关系元组的数据模型：

```go
type RelationTuple struct {
    ID                  uuid.UUID      `db:"shard_id"`
    NetworkID           uuid.UUID      `db:"nid"`
    Namespace           string         `db:"namespace"`
    Object              uuid.UUID      `db:"object"`
    Relation            string         `db:"relation"`
    SubjectID           uuid.NullUUID  `db:"subject_id"`
    SubjectSetNamespace sql.NullString `db:"subject_set_namespace"`
    SubjectSetObject    uuid.NullUUID  `db:"subject_set_object"`
    SubjectSetRelation  sql.NullString `db:"subject_set_relation"`
    CommitTime          time.Time      `db:"commit_time"`
}
```

## 2. 数据转换流程

### 2.1 UUID 转换
当关系元组被写入数据库时，会进行以下转换：

- 使用 `uuid.NewV5()` 函数生成 UUID
- 基于 NetworkID 和原始字符串生成确定性 UUID
- 确保相同的字符串 ID 总是生成相同的 UUID

### 2.2 关系元组格式
系统支持两种类型的主体：

1. SubjectID
   - 直接存储用户 ID
   - 用于直接的用户权限分配

2. SubjectSet
   - 存储命名空间、对象和关系
   - 用于组或角色权限分配

## 3. 权限检查流程

在 `internal/check/engine.go` 中实现了权限检查的核心逻辑：

```go
func (e *Engine) checkIsAllowed(ctx context.Context, r *relationTuple, restDepth int, skipDirect bool) checkgroup.CheckFunc {
    // 1. 检查深度限制
    if restDepth <= 0 {
        return checkgroup.UnknownMemberFunc
    }

    // 2. 获取关系定义
    relation, err := e.astRelationFor(ctx, r)
    
    // 3. 处理参数
    ctx, err = e.processArgs(ctx, r, relation)
    
    // 4. 尝试系统缓存
    if e.trySysCache(ctx, g, r, restDepth) {
        return g.CheckFunc()
    }

    // 5. 检查权限
    if hasRewrite {
        // 5.1 检查主体集重写
        g.Add(e.checkSubjectSetRewrite(ctx, r, relation.SubjectSetRewrite, restDepth))
    }
    if (!strictMode || !hasRewrite) && !skipDirect && !fakeRelation {
        // 5.2 直接检查
        g.Add(e.checkDirect(r, restDepth-1))
    }
    if canHaveSubjectSets && !fakeRelation {
        // 5.3 展开主体集
        g.Add(e.checkExpandSubject(r, restDepth-1))
    }
}
```

## 4. 权限继承机制

在 `namespaces.keto.ts` 中定义了权限继承规则：

```typescript
class IamRole implements Namespace {
  related: {
    parents: IamGroup[]
    policies: IamPolicy[]
    perms: (IamPerm | SubjectSet<IamRole, "perms">)[]
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      IamRole:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),
  }
}
```

权限继承通过以下方式实现：

1. 直接继承：通过 `parents` 关系
2. 策略继承：通过 `policies` 关系
3. 全局权限：通过 `IamRole:"*"` 检查

## 5. 查询深度控制

在 `internal/persistence/sql/traverser.go` 中实现了查询深度控制：

```go
func (t *Traverser) TraverseSubjectSetExpansion(ctx context.Context, start *relationtuple.RelationTuple) (res []*relationtuple.TraversalResult, err error) {
    // 使用分页查询控制深度
    var (
        rows  []*subjectExpandedRelationTupleRow
        limit = 1000
    )
    
    // 查询关系元组
    err = t.conn.WithContext(ctx).RawQuery(`
        SELECT current.shard_id AS shard_id,
               current.subject_set_namespace AS namespace,
               current.subject_set_object AS object,
               current.subject_set_relation AS relation,
               EXISTS(...) AS found
        FROM keto_relation_tuples AS current
        WHERE ...
        LIMIT ?
    `).All(&rows)
}
```

## 6. 权限检查示例

### 6.1 定义文档权限规则
```typescript
class Document implements Namespace {
  related: {
    parents: Document[]
    viewers: (User | SubjectSet<Group, "members">)[]
    owners: (User | SubjectSet<Group, "members">)[]
  }

  permits = {
    view: (ctx: Context): boolean =>
      this.related.parents.traverse((p) =>
        p.related.viewers.includes(ctx.subject),
      ) ||
      this.related.viewers.includes(ctx.subject) ||
      this.related.owners.includes(ctx.subject),
  }
}
```

### 6.2 执行权限检查
```typescript
const result = await keto.checkPermission({
  namespace: "document",
  object: "doc1",
  relation: "view",
  subject_id: "user1"
});
```

权限检查流程：
1. 检查用户是否直接有查看权限
2. 检查用户是否通过父文档继承权限
3. 检查用户是否通过组继承权限
4. 检查用户是否是文档所有者

## 7. 系统特点

1. **复杂的权限继承关系**
   - 支持多级继承
   - 支持组和角色权限
   - 支持策略继承

2. **数据一致性**
   - 使用 UUID 确保数据一致性
   - 支持事务操作
   - 支持并发控制

3. **性能优化**
   - 查询深度控制
   - 缓存机制
   - 分页查询

4. **灵活性**
   - 支持自定义命名空间
   - 支持自定义关系定义
   - 支持复杂的主体集定义

5. **安全性**
   - 严格的类型检查
   - 权限验证
   - 访问控制 