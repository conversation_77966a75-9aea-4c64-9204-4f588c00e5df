package ecarx

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"annofeed/internal/biz"
	"annofeed/workflow/common"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/apis/annofeed/v1"
	"gitlab.rp.konvery.work/platform/pkg/kfs"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/seqsense/pcgol/pc"
)

const (
	ParamsFile = "params.txt" // 雷达外参，相机的内外参，外参均为IMU坐标系
)

func Transform(ctx context.Context, data *biz.Data, dir string, startIdx int, hb common.Heartbeat) (dataType string, err error) {
	newdir := dir + "-1"
	o := newXparser(dir, newdir)
	err = kfs.IterateDir(dir, func(entry os.DirEntry, subdir string, depth int) error {
		// skip directories
		if entry.IsDir() {
			return nil
		}
		hb(ctx)
		return o.parseFile(ctx, subdir, entry.Name())
	})
	if err != nil {
		return "", err
	}

	if err = o.closeFrame(ctx); err != nil {
		return "", fmt.Errorf("failed to close last frame when handling %v: %w", dir, err)
	}

	tmp := dir + "-tmp"
	if err = os.Rename(dir, tmp); err != nil {
		return "", fmt.Errorf("failed to backup %s to %s: %w", dir, tmp, err)
	}
	if err = os.Rename(newdir, dir); err != nil {
		os.Rename(tmp, dir)
		return "", fmt.Errorf("failed to rename %s to %s: %w", newdir, dir, err)
	}
	if err = os.RemoveAll(tmp); err != nil {
		// fmt.Errorf("failed to delete backup dir %s: %w", tmp, err)
	}
	return biz.DataTypeFusion3D, nil
}

type fileParser func(ctx context.Context, subdir, name string) (match bool, err error)

type xparser struct {
	params     *Params
	elemParam  *biz.ElemParamV1
	frmIdx     int
	fileIdx    int
	curSubdir  string
	oldBasedir string
	newBasedir string
	parsers    []fileParser
	log        *log.Helper
}

func newXparser(olddir, newdir string) *xparser {
	o := &xparser{
		frmIdx:     -1,
		oldBasedir: olddir,
		newBasedir: newdir,
		// log: log.NewHelper(opts.Logger),
	}

	o.initParsers()
	return o
}

func (o *xparser) initParsers() {
	o.parsers = []fileParser{
		o.ignoreDotFiles,
		o.ignoreParamsFile,
		o.parseImageFile,
		o.parsePcdFile,
		// o.defaultParser,
	}
}

func (o *xparser) parseFile(ctx context.Context, subdir, name string) (err error) {
	for _, p := range o.parsers {
		match, err := p(ctx, subdir, name)
		if match {
			return err
		}
	}
	return fmt.Errorf("unparsed file: %v", filepath.Join(subdir, name))
}

func (o *xparser) ignoreDotFiles(ctx context.Context, subdir, name string) (match bool, err error) {
	if !strings.HasPrefix(filepath.Base(name), ".") {
		return false, nil
	}
	// o.log.WithContext(ctx).Warnf("ignore a dot file %v", name)
	return true, nil
}

func (o *xparser) ignoreParamsFile(ctx context.Context, subdir, name string) (match bool, err error) {
	switch name {
	case ParamsFile:
		// o.log.WithContext(ctx).Warnf("skip file %v", name)
		return true, nil
	}
	return false, nil
}

func (o *xparser) defaultParser(ctx context.Context, subdir, name string) (match bool, err error) {
	// o.log.WithContext(ctx).Warnf("ignore an unknown file %v", name)
	return true, nil
}

func parseNumbers(line string) ([]float64, error) {
	vv := strings.Split(line, " ")
	if len(vv) < 2 {
		return nil, fmt.Errorf("invalid line: %v", line)
	}

	vf := make([]float64, len(vv)-1)
	for i, v := range vv[1:] {
		f, err := strconv.ParseFloat(v, 64)
		if err != nil {
			return nil, fmt.Errorf("failed to conver number in line: %v", line)
		}
		vf[i] = f
	}
	return vf, nil
}

func (o *xparser) loadParams() error {
	fpath := filepath.Join(o.oldBasedir, o.curSubdir, "params", ParamsFile)
	data, err := os.ReadFile(fpath)
	if err != nil {
		return fmt.Errorf("failed to read params file %v: %w", fpath, err)
	}
	lines := bytes.Split(data, []byte("\n"))
	o.params = &Params{
		CameraExtrinsic:  make(map[string][]float64, 8),
		CameraIntrinsic:  make(map[string][]float64, 8),
		CameraDistortion: make(map[string][]float64, 8),
	}
	for _, ln := range lines {
		if len(ln) > 0 && ln[len(ln)-1] == '\r' {
			ln = ln[:len(ln)-1]
		}
		if len(ln) == 0 {
			continue
		}
		s := string(ln)
		var vf []float64
		switch {
		case strings.HasPrefix(s, "calib_"):
			cam, _, _ := strings.Cut(s[6:], "_")
			cam = fixCamName(cam)
			o.params.CameraExtrinsic[cam], err = parseNumbers(s)
		case strings.Contains(s, "_D:"):
			cam, _, _ := strings.Cut(s, "_")
			cam = fixCamName(cam)
			vf, err = parseNumbers(s)
			if err == nil {
				vf = append([]float64{0}, vf...)
				switch len(vf) - 1 {
				case 4: // fisheye cameras
					vf[0] = float64(anno.RawdataParam_DistortionType_kb)
				case 5: // pinhole cameras
					vf[0] = float64(anno.RawdataParam_DistortionType_pinhole)
					// convert t,k1,k2,k3,p1,p2 into t,k1,k2,p1,p2,k3
					vf[4], vf[5] = vf[5], vf[4]
					vf[3], vf[5] = vf[5], vf[3]
				default:
					return fmt.Errorf("unsupported camera distortion params: %v", s)
				}
				o.params.CameraDistortion[cam] = vf
			}
		case strings.Contains(s, "_K:"):
			cam, _, _ := strings.Cut(s, "_")
			cam = fixCamName(cam)
			vf, err = parseNumbers(s)
			if err == nil && len(vf) != 9 {
				err = fmt.Errorf("wrong camera intrinsic params: %v", s)
			}
			if err == nil {
				// convert fx,0,cx,0,fy,cy,0,0,1 to fx,fy,cx,cy
				vf[1] = vf[4]
				vf[3] = vf[5]
				o.params.CameraIntrinsic[cam] = vf[:4]
			}
		case strings.Contains(s, "_to_IMU:"):
			o.params.LidarExtrinsic, err = parseNumbers(s)
		default:
			// ignore other lines
		}
		if err != nil {
			return fmt.Errorf("failed to parse line %s: %w", s, err)
		}
	}

	return nil
}

func (o *xparser) addCamParams(cam string) {
	trans := o.params.GetImageTransforms(cam)
	o.elemParam.Cameras[cam] = &annofeed.CameraParam{
		Pose: &annofeed.Pose{
			CoordSys: "",
			Pose:     o.params.CameraExtrinsic[cam],
		},
		Transforms: trans,
	}
}

func (o *xparser) newFilePath(name string) string {
	return filepath.Join(o.newBasedir, o.curSubdir, name)
}

func (o *xparser) parseImageFile(ctx context.Context, subdir, name string) (match bool, err error) {
	if !common.IsSupportedImageFormat(name) {
		return false, nil
	}

	// parse camera name
	pdir, camName := filepath.Split(subdir)
	camName = fixCamName(camName)
	if pdir != "" {
		pdir = pdir[:len(pdir)-1]
	}
	pdir = filepath.Dir(pdir)
	if pdir == "" {
		return true, fmt.Errorf("invalid directory structure: %v", subdir)
	}

	if pdir != o.curSubdir {
		err = o.startFrame(ctx, pdir)
		if err != nil {
			return true, err
		}
	}
	o.addCamParams(camName)

	// duplicate the image file instead of moving it, otherwise we will probably miss some camera params
	// when retrying the process if it is interrupted due to a temporary error
	old := filepath.Join(o.oldBasedir, subdir, name)
	newf := o.newFilePath(camName + filepath.Ext(name))
	err = os.Link(old, newf)
	if err != nil {
		return true, fmt.Errorf("failed to link file (%v -> %v): %w", newf, old, err)
	}

	// record its original name
	err = os.WriteFile(newf+common.OrigExt, []byte(filepath.Join(subdir, name)), 0644)
	if err != nil {
		return true, fmt.Errorf("failed to write file %v: %w", newf+common.OrigExt, err)
	}

	o.fileIdx++
	return true, nil
}

func (o *xparser) closeFrame(ctx context.Context) error {
	if o.elemParam != nil {
		data, err := json.Marshal(o.elemParam)
		if err != nil {
			return fmt.Errorf("failed to marshal elemParam: %w", err)
		}
		fpath := o.newFilePath(common.ParamsFile)
		err = os.WriteFile(fpath, data, 0644)
		if err != nil {
			return fmt.Errorf("failed to write file %v: %w", fpath, err)
		}
	}
	return nil
}

func (o *xparser) startFrame(ctx context.Context, subdir string) error {
	err := o.closeFrame(ctx)
	if err != nil {
		return fmt.Errorf("failed to close frame: %w", err)
	}

	o.fileIdx = 0
	o.frmIdx++
	o.curSubdir = subdir

	// make frame directory
	fpath := o.newFilePath("")
	err = os.MkdirAll(fpath, 0755)
	if err != nil {
		return fmt.Errorf("failed to create directory %v: %w", fpath, err)
	}

	err = o.loadParams()
	if err != nil {
		return fmt.Errorf("failed to load params: %w", err)
	}
	o.elemParam = &biz.ElemParamV1{
		Cameras: map[string]*annofeed.CameraParam{},
	}
	return nil
}

func (o *xparser) parsePcdFile(ctx context.Context, subdir, name string) (match bool, err error) {
	if common.FileFormat(name) != common.RawdataFormatPCD {
		return false, nil
	}

	// load pcd file
	origin := filepath.Join(o.oldBasedir, subdir, name)
	of, err := os.Open(origin)
	if err != nil {
		return true, fmt.Errorf("failed to open file %v: %w", origin, err)
	}
	defer of.Close()
	pp, err := pc.Unmarshal(of)
	if err != nil {
		return true, fmt.Errorf("failed to parse pcd file %v: %w", origin, err)
	}

	// record its original name
	fpath := o.newFilePath(common.LidarPCD)
	err = os.WriteFile(fpath+common.OrigExt, []byte(filepath.Join(subdir, name)), 0644)
	if err != nil {
		return true, fmt.Errorf("failed to write file %v: %w", fpath+common.OrigExt, err)
	}

	// save in pcd binary format
	nf, err := os.Create(fpath)
	if err != nil {
		return true, fmt.Errorf("failed to create file %v: %w", fpath, err)
	}
	defer nf.Close()
	err = pc.Marshal(pp, nf)
	if err != nil {
		err = fmt.Errorf("failed to save pcd file %v: %w", fpath, err)
	}

	o.fileIdx++
	return true, err
}
