// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: anno/v1/role.proto

package anno

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationRolesCreateRole = "/anno.v1.Roles/CreateRole"
const OperationRolesDeleteRole = "/anno.v1.Roles/DeleteRole"
const OperationRolesDeleteUsersRole = "/anno.v1.Roles/DeleteUsersRole"
const OperationRolesGetRole = "/anno.v1.Roles/GetRole"
const OperationRolesGetUserRole = "/anno.v1.Roles/GetUserRole"
const OperationRolesListRole = "/anno.v1.Roles/ListRole"
const OperationRolesListUsersRole = "/anno.v1.Roles/ListUsersRole"
const OperationRolesSetUsersRole = "/anno.v1.Roles/SetUsersRole"
const OperationRolesUpdateRole = "/anno.v1.Roles/UpdateRole"

type RolesHTTPServer interface {
	CreateRole(context.Context, *Role) (*Role, error)
	DeleteRole(context.Context, *DeleteRoleRequest) (*emptypb.Empty, error)
	DeleteUsersRole(context.Context, *DeleteUsersRoleRequest) (*emptypb.Empty, error)
	GetRole(context.Context, *GetRoleRequest) (*Role, error)
	GetUserRole(context.Context, *GetUserRoleRequest) (*GetUserRoleReply, error)
	ListRole(context.Context, *ListRoleRequest) (*ListRoleReply, error)
	ListUsersRole(context.Context, *ListUsersRoleRequest) (*ListUsersRoleReply, error)
	SetUsersRole(context.Context, *SetUsersRoleRequest) (*emptypb.Empty, error)
	UpdateRole(context.Context, *UpdateRoleRequest) (*Role, error)
}

func RegisterRolesHTTPServer(s *http.Server, srv RolesHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/roles", _Roles_CreateRole0_HTTP_Handler(srv))
	r.PATCH("/v1/roles/{role.name}", _Roles_UpdateRole0_HTTP_Handler(srv))
	r.DELETE("/v1/roles/{name}", _Roles_DeleteRole0_HTTP_Handler(srv))
	r.GET("/v1/roles/{name}", _Roles_GetRole0_HTTP_Handler(srv))
	r.GET("/v1/roles", _Roles_ListRole0_HTTP_Handler(srv))
	r.GET("/v1/roles/users", _Roles_ListUsersRole0_HTTP_Handler(srv))
	r.GET("/v1/roles/users/{uid}", _Roles_GetUserRole0_HTTP_Handler(srv))
	r.PUT("/v1/roles/users", _Roles_SetUsersRole0_HTTP_Handler(srv))
	r.DELETE("/v1/roles/users", _Roles_DeleteUsersRole0_HTTP_Handler(srv))
}

func _Roles_CreateRole0_HTTP_Handler(srv RolesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in Role
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRolesCreateRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateRole(ctx, req.(*Role))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Role)
		return ctx.Result(200, reply)
	}
}

func _Roles_UpdateRole0_HTTP_Handler(srv RolesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateRoleRequest
		if err := ctx.Bind(&in.Role); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRolesUpdateRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateRole(ctx, req.(*UpdateRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Role)
		return ctx.Result(200, reply)
	}
}

func _Roles_DeleteRole0_HTTP_Handler(srv RolesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteRoleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRolesDeleteRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteRole(ctx, req.(*DeleteRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Roles_GetRole0_HTTP_Handler(srv RolesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRoleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRolesGetRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRole(ctx, req.(*GetRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Role)
		return ctx.Result(200, reply)
	}
}

func _Roles_ListRole0_HTTP_Handler(srv RolesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListRoleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRolesListRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListRole(ctx, req.(*ListRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListRoleReply)
		return ctx.Result(200, reply)
	}
}

func _Roles_ListUsersRole0_HTTP_Handler(srv RolesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListUsersRoleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRolesListUsersRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUsersRole(ctx, req.(*ListUsersRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListUsersRoleReply)
		return ctx.Result(200, reply)
	}
}

func _Roles_GetUserRole0_HTTP_Handler(srv RolesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserRoleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRolesGetUserRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserRole(ctx, req.(*GetUserRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserRoleReply)
		return ctx.Result(200, reply)
	}
}

func _Roles_SetUsersRole0_HTTP_Handler(srv RolesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetUsersRoleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRolesSetUsersRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetUsersRole(ctx, req.(*SetUsersRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Roles_DeleteUsersRole0_HTTP_Handler(srv RolesHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteUsersRoleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRolesDeleteUsersRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteUsersRole(ctx, req.(*DeleteUsersRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type RolesHTTPClient interface {
	CreateRole(ctx context.Context, req *Role, opts ...http.CallOption) (rsp *Role, err error)
	DeleteRole(ctx context.Context, req *DeleteRoleRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteUsersRole(ctx context.Context, req *DeleteUsersRoleRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetRole(ctx context.Context, req *GetRoleRequest, opts ...http.CallOption) (rsp *Role, err error)
	GetUserRole(ctx context.Context, req *GetUserRoleRequest, opts ...http.CallOption) (rsp *GetUserRoleReply, err error)
	ListRole(ctx context.Context, req *ListRoleRequest, opts ...http.CallOption) (rsp *ListRoleReply, err error)
	ListUsersRole(ctx context.Context, req *ListUsersRoleRequest, opts ...http.CallOption) (rsp *ListUsersRoleReply, err error)
	SetUsersRole(ctx context.Context, req *SetUsersRoleRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateRole(ctx context.Context, req *UpdateRoleRequest, opts ...http.CallOption) (rsp *Role, err error)
}

type RolesHTTPClientImpl struct {
	cc *http.Client
}

func NewRolesHTTPClient(client *http.Client) RolesHTTPClient {
	return &RolesHTTPClientImpl{client}
}

func (c *RolesHTTPClientImpl) CreateRole(ctx context.Context, in *Role, opts ...http.CallOption) (*Role, error) {
	var out Role
	pattern := "/v1/roles"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRolesCreateRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RolesHTTPClientImpl) DeleteRole(ctx context.Context, in *DeleteRoleRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/roles/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRolesDeleteRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RolesHTTPClientImpl) DeleteUsersRole(ctx context.Context, in *DeleteUsersRoleRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/roles/users"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRolesDeleteUsersRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RolesHTTPClientImpl) GetRole(ctx context.Context, in *GetRoleRequest, opts ...http.CallOption) (*Role, error) {
	var out Role
	pattern := "/v1/roles/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRolesGetRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RolesHTTPClientImpl) GetUserRole(ctx context.Context, in *GetUserRoleRequest, opts ...http.CallOption) (*GetUserRoleReply, error) {
	var out GetUserRoleReply
	pattern := "/v1/roles/users/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRolesGetUserRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RolesHTTPClientImpl) ListRole(ctx context.Context, in *ListRoleRequest, opts ...http.CallOption) (*ListRoleReply, error) {
	var out ListRoleReply
	pattern := "/v1/roles"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRolesListRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RolesHTTPClientImpl) ListUsersRole(ctx context.Context, in *ListUsersRoleRequest, opts ...http.CallOption) (*ListUsersRoleReply, error) {
	var out ListUsersRoleReply
	pattern := "/v1/roles/users"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRolesListUsersRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RolesHTTPClientImpl) SetUsersRole(ctx context.Context, in *SetUsersRoleRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/roles/users"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRolesSetUsersRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RolesHTTPClientImpl) UpdateRole(ctx context.Context, in *UpdateRoleRequest, opts ...http.CallOption) (*Role, error) {
	var out Role
	pattern := "/v1/roles/{role.name}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRolesUpdateRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in.Role, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
