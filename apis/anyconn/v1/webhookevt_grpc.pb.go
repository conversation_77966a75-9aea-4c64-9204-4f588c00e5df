// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: anyconn/v1/webhookevt.proto

package anyconn

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	WebhookEvts_CreateWebhookEvt_FullMethodName = "/anyconn.v1.WebhookEvts/CreateWebhookEvt"
	WebhookEvts_UpdateWebhookEvt_FullMethodName = "/anyconn.v1.WebhookEvts/UpdateWebhookEvt"
	WebhookEvts_DeleteWebhookEvt_FullMethodName = "/anyconn.v1.WebhookEvts/DeleteWebhookEvt"
	WebhookEvts_GetWebhookEvt_FullMethodName    = "/anyconn.v1.WebhookEvts/GetWebhookEvt"
	WebhookEvts_ListWebhookEvt_FullMethodName   = "/anyconn.v1.WebhookEvts/ListWebhookEvt"
)

// WebhookEvtsClient is the client API for WebhookEvts service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WebhookEvtsClient interface {
	CreateWebhookEvt(ctx context.Context, in *CreateWebhookEvtRequest, opts ...grpc.CallOption) (*WebhookEvt, error)
	UpdateWebhookEvt(ctx context.Context, in *UpdateWebhookEvtRequest, opts ...grpc.CallOption) (*WebhookEvt, error)
	DeleteWebhookEvt(ctx context.Context, in *DeleteWebhookEvtRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetWebhookEvt(ctx context.Context, in *GetWebhookEvtRequest, opts ...grpc.CallOption) (*WebhookEvt, error)
	ListWebhookEvt(ctx context.Context, in *ListWebhookEvtRequest, opts ...grpc.CallOption) (*ListWebhookEvtReply, error)
}

type webhookEvtsClient struct {
	cc grpc.ClientConnInterface
}

func NewWebhookEvtsClient(cc grpc.ClientConnInterface) WebhookEvtsClient {
	return &webhookEvtsClient{cc}
}

func (c *webhookEvtsClient) CreateWebhookEvt(ctx context.Context, in *CreateWebhookEvtRequest, opts ...grpc.CallOption) (*WebhookEvt, error) {
	out := new(WebhookEvt)
	err := c.cc.Invoke(ctx, WebhookEvts_CreateWebhookEvt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webhookEvtsClient) UpdateWebhookEvt(ctx context.Context, in *UpdateWebhookEvtRequest, opts ...grpc.CallOption) (*WebhookEvt, error) {
	out := new(WebhookEvt)
	err := c.cc.Invoke(ctx, WebhookEvts_UpdateWebhookEvt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webhookEvtsClient) DeleteWebhookEvt(ctx context.Context, in *DeleteWebhookEvtRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, WebhookEvts_DeleteWebhookEvt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webhookEvtsClient) GetWebhookEvt(ctx context.Context, in *GetWebhookEvtRequest, opts ...grpc.CallOption) (*WebhookEvt, error) {
	out := new(WebhookEvt)
	err := c.cc.Invoke(ctx, WebhookEvts_GetWebhookEvt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webhookEvtsClient) ListWebhookEvt(ctx context.Context, in *ListWebhookEvtRequest, opts ...grpc.CallOption) (*ListWebhookEvtReply, error) {
	out := new(ListWebhookEvtReply)
	err := c.cc.Invoke(ctx, WebhookEvts_ListWebhookEvt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WebhookEvtsServer is the server API for WebhookEvts service.
// All implementations must embed UnimplementedWebhookEvtsServer
// for forward compatibility
type WebhookEvtsServer interface {
	CreateWebhookEvt(context.Context, *CreateWebhookEvtRequest) (*WebhookEvt, error)
	UpdateWebhookEvt(context.Context, *UpdateWebhookEvtRequest) (*WebhookEvt, error)
	DeleteWebhookEvt(context.Context, *DeleteWebhookEvtRequest) (*emptypb.Empty, error)
	GetWebhookEvt(context.Context, *GetWebhookEvtRequest) (*WebhookEvt, error)
	ListWebhookEvt(context.Context, *ListWebhookEvtRequest) (*ListWebhookEvtReply, error)
	mustEmbedUnimplementedWebhookEvtsServer()
}

// UnimplementedWebhookEvtsServer must be embedded to have forward compatible implementations.
type UnimplementedWebhookEvtsServer struct {
}

func (UnimplementedWebhookEvtsServer) CreateWebhookEvt(context.Context, *CreateWebhookEvtRequest) (*WebhookEvt, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWebhookEvt not implemented")
}
func (UnimplementedWebhookEvtsServer) UpdateWebhookEvt(context.Context, *UpdateWebhookEvtRequest) (*WebhookEvt, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWebhookEvt not implemented")
}
func (UnimplementedWebhookEvtsServer) DeleteWebhookEvt(context.Context, *DeleteWebhookEvtRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteWebhookEvt not implemented")
}
func (UnimplementedWebhookEvtsServer) GetWebhookEvt(context.Context, *GetWebhookEvtRequest) (*WebhookEvt, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWebhookEvt not implemented")
}
func (UnimplementedWebhookEvtsServer) ListWebhookEvt(context.Context, *ListWebhookEvtRequest) (*ListWebhookEvtReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWebhookEvt not implemented")
}
func (UnimplementedWebhookEvtsServer) mustEmbedUnimplementedWebhookEvtsServer() {}

// UnsafeWebhookEvtsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WebhookEvtsServer will
// result in compilation errors.
type UnsafeWebhookEvtsServer interface {
	mustEmbedUnimplementedWebhookEvtsServer()
}

func RegisterWebhookEvtsServer(s grpc.ServiceRegistrar, srv WebhookEvtsServer) {
	s.RegisterService(&WebhookEvts_ServiceDesc, srv)
}

func _WebhookEvts_CreateWebhookEvt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWebhookEvtRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebhookEvtsServer).CreateWebhookEvt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WebhookEvts_CreateWebhookEvt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebhookEvtsServer).CreateWebhookEvt(ctx, req.(*CreateWebhookEvtRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebhookEvts_UpdateWebhookEvt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWebhookEvtRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebhookEvtsServer).UpdateWebhookEvt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WebhookEvts_UpdateWebhookEvt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebhookEvtsServer).UpdateWebhookEvt(ctx, req.(*UpdateWebhookEvtRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebhookEvts_DeleteWebhookEvt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteWebhookEvtRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebhookEvtsServer).DeleteWebhookEvt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WebhookEvts_DeleteWebhookEvt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebhookEvtsServer).DeleteWebhookEvt(ctx, req.(*DeleteWebhookEvtRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebhookEvts_GetWebhookEvt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWebhookEvtRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebhookEvtsServer).GetWebhookEvt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WebhookEvts_GetWebhookEvt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebhookEvtsServer).GetWebhookEvt(ctx, req.(*GetWebhookEvtRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebhookEvts_ListWebhookEvt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWebhookEvtRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebhookEvtsServer).ListWebhookEvt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WebhookEvts_ListWebhookEvt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebhookEvtsServer).ListWebhookEvt(ctx, req.(*ListWebhookEvtRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WebhookEvts_ServiceDesc is the grpc.ServiceDesc for WebhookEvts service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WebhookEvts_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "anyconn.v1.WebhookEvts",
	HandlerType: (*WebhookEvtsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateWebhookEvt",
			Handler:    _WebhookEvts_CreateWebhookEvt_Handler,
		},
		{
			MethodName: "UpdateWebhookEvt",
			Handler:    _WebhookEvts_UpdateWebhookEvt_Handler,
		},
		{
			MethodName: "DeleteWebhookEvt",
			Handler:    _WebhookEvts_DeleteWebhookEvt_Handler,
		},
		{
			MethodName: "GetWebhookEvt",
			Handler:    _WebhookEvts_GetWebhookEvt_Handler,
		},
		{
			MethodName: "ListWebhookEvt",
			Handler:    _WebhookEvts_ListWebhookEvt_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "anyconn/v1/webhookevt.proto",
}
