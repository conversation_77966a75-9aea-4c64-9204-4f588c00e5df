syntax = "proto3";

package annofeed.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/annofeed/v1;annofeed";
option java_multiple_files = true;
option java_package = "annofeed.v1";

service Files {
  // create a file record and presigned URLs for upload
  rpc CreateFile (CreateFileRequest) returns (CreateFileReply) {
    option (google.api.http) = {
      post: "/v1/files"
      body: "*"
    };
  }

  rpc UpdateFile (UpdateFileRequest) returns (File) {
    option (google.api.http) = {
      patch: "/v1/files/{file.uid}"
      body: "file"
    };
  }

  // mark the file upload as completed
  rpc FinishFileUpload (FinishFileUploadRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/files/{uid}/finish-upload"
      body: "*"
    };
  }

  rpc GetFileUploadURLs (GetFileUploadURLsRequest) returns (GetFileUploadURLsReply) {
    option (google.api.http) = {
      get: "/v1/files/{uid}/upload-urls"
    };
  }

  // delete a file record. Ongoing multipart upload will be aborted.
  rpc DeleteFile (DeleteFileRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/files/{uid}"
    };
  }

  rpc GetFile (GetFileRequest) returns (File) {
    option (google.api.http) = {
      get: "/v1/files/{uid}"
    };
  }

  rpc ListFile (ListFileRequest) returns (ListFileReply) {
    option (google.api.http) = {
      get: "/v1/files"
    };
  }

  // get a presigned URL to download the file
  rpc ShareFile (ShareFileRequest) returns (ShareFileReply) {
    option (google.api.http) = {
      put: "/v1/files/{uid}/share"
      body: "*"
    };
  }
}

message CreateFileRequest {
  option (openapi.v3.schema) = {
    required: ["name", "size", "parts", "mime"]
  };

  // name the file to be created
  string name = 1;
  // number of bytes of the file
  double size = 2;
  // set number of parts in a multi-part upload;
  // if unspecified, which is 0, server will choose a proper number based on file size.
  // Part size should be between 5 MiB to 5 GiB. There is no minimum size limit on the
  // last part of your multipart upload.
  int32 parts = 3;
  // MIME type of the file: e.g. "application/zip",
  // or file name extention: e.g. ".zip"
  string mime = 4;
  // SHA256 digest of the file; it also accepts MD5 digest in the format: md5:xxx
  string sha256 = 5;
  // organization that the file belongs to; if omitted, it is the user's organization
  string org_uid = 6;
}
message CreateFileReply {
  option (openapi.v3.schema) = {
    required: ["file", "upload_urls", "url_expires_at"]
  };

  File file = 1;
  // if len(upload_urls) > 1, file should be uploaded as multi-parts
  repeated string upload_urls = 2;
  // presigned URL expire time. upload should be done before that
  google.protobuf.Timestamp url_expires_at = 3;
  // if true, a previously created file is returned due to they have the same hash and size
  bool pre_existing = 4;
}

message UpdateFileRequest {
  option (openapi.v3.schema) = {
    required: ["file", "fields"]
  };

  // only allow to change file name
  File file = 1;
  // name of fields to update
  repeated string fields = 2;
}

message GetFileUploadURLsRequest {
  // file uid
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // set number of parts in a multi-part upload
  int32 parts = 2;
}
message GetFileUploadURLsReply {
  option (openapi.v3.schema) = {
    required: ["upload_urls", "url_expires_at"]
  };

  // if len(upload_urls) > 1, file should be uploaded as multi-parts
  repeated string upload_urls = 1;
  // presigned URL expire time. upload should be done before that
  google.protobuf.Timestamp url_expires_at = 2;
}

message FinishFileUploadRequest {
  // file uid
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // ETAG returned from each part upload, in the part number order;
  // required in a multipart upload
  repeated string etags = 2;
}

message DeleteFileRequest {
  // file UID
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
}

message GetFileRequest {
  // file UID
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
}

message ListFileRequest {
  int32 pagesz = 1 [(validate.rules).int32 = {gte:0, lte: 100}];
  string page_token = 2;

  // filter by organization
  string org_uid = 3;
  // filter by creator
  string creator_uid = 4;
  // filter by name pattern
  string name_pattern = 5;
  // filter by URI
  repeated string uris = 6;
}

message ListFileReply {
  option (openapi.v3.schema) = {
    required: ["files", "next_page_token"]
  };

  repeated File files = 1;
  string next_page_token = 2;
}

message ShareFileRequest {
  // file uid
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // set URL's valid duration in seconds; if it is -1, it will return a permanent URL
  int32 timeout = 7;
}

message ShareFileReply {
  option (openapi.v3.schema) = {
    required: ["url", "expires_at"]
  };

  // URL to download the file
  string url = 1;
  // presigned URL expires time.
  google.protobuf.Timestamp expires_at = 2;
}

message File {
  option (openapi.v3.schema) = {
    required: ["uid", "uri", "name", "size", "sha256", "state", "org_uid", "created_at"]
  };

  message State {
    enum Enum {
      unspecified = 0;
      // file uploading is not finished yet
      uploading = 1;
      // file has been successfully uploaded
      uploaded = 2;
    }
  }

  // file UID
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // file URI
  string uri = 2;
  // file name
  string name = 3;
  // file size in number of bytes
  double size = 4;
  // file MIME type
  string mime = 5;
  // SHA256/MD5 digest of the file
  string sha256 = 6;
  // UID of the organization which the file belongs to
  string org_uid = 7;
  // file state
  State.Enum state = 8 [(validate.rules).enum = {defined_only: true}];
  // UID of the creator
  string creator_uid = 9;

  // file creation time
  google.protobuf.Timestamp created_at = 15;
}
