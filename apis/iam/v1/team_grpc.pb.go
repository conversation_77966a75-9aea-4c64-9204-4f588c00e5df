// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: iam/v1/team.proto

package iam

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Teams_CreateTeam_FullMethodName     = "/iam.v1.Teams/CreateTeam"
	Teams_UpdateTeam_FullMethodName     = "/iam.v1.Teams/UpdateTeam"
	Teams_DeleteTeam_FullMethodName     = "/iam.v1.Teams/DeleteTeam"
	Teams_GetTeamsRoot_FullMethodName   = "/iam.v1.Teams/GetTeamsRoot"
	Teams_GetTeam_FullMethodName        = "/iam.v1.Teams/GetTeam"
	Teams_ListTeamByIDs_FullMethodName  = "/iam.v1.Teams/ListTeamByIDs"
	Teams_ListTeam_FullMethodName       = "/iam.v1.Teams/ListTeam"
	Teams_ListMembers_FullMethodName    = "/iam.v1.Teams/ListMembers"
	Teams_AddMembers_FullMethodName     = "/iam.v1.Teams/AddMembers"
	Teams_DeleteMembers_FullMethodName  = "/iam.v1.Teams/DeleteMembers"
	Teams_SetMembersRole_FullMethodName = "/iam.v1.Teams/SetMembersRole"
)

// TeamsClient is the client API for Teams service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TeamsClient interface {
	CreateTeam(ctx context.Context, in *CreateTeamRequest, opts ...grpc.CallOption) (*Team, error)
	UpdateTeam(ctx context.Context, in *UpdateTeamRequest, opts ...grpc.CallOption) (*Team, error)
	DeleteTeam(ctx context.Context, in *DeleteTeamRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetTeamsRoot(ctx context.Context, in *GetTeamsRootRequest, opts ...grpc.CallOption) (*ListTeamReply, error)
	GetTeam(ctx context.Context, in *GetTeamRequest, opts ...grpc.CallOption) (*Team, error)
	// rpc only
	ListTeamByIDs(ctx context.Context, in *ListTeamByIDsRequest, opts ...grpc.CallOption) (*ListTeamReply, error)
	ListTeam(ctx context.Context, in *ListTeamRequest, opts ...grpc.CallOption) (*ListTeamReply, error)
	ListMembers(ctx context.Context, in *ListMembersRequest, opts ...grpc.CallOption) (*ListMembersReply, error)
	// send join-team invitation to mentioned users
	AddMembers(ctx context.Context, in *AddMembersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	DeleteMembers(ctx context.Context, in *DeleteMembersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	SetMembersRole(ctx context.Context, in *SetMembersRoleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type teamsClient struct {
	cc grpc.ClientConnInterface
}

func NewTeamsClient(cc grpc.ClientConnInterface) TeamsClient {
	return &teamsClient{cc}
}

func (c *teamsClient) CreateTeam(ctx context.Context, in *CreateTeamRequest, opts ...grpc.CallOption) (*Team, error) {
	out := new(Team)
	err := c.cc.Invoke(ctx, Teams_CreateTeam_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamsClient) UpdateTeam(ctx context.Context, in *UpdateTeamRequest, opts ...grpc.CallOption) (*Team, error) {
	out := new(Team)
	err := c.cc.Invoke(ctx, Teams_UpdateTeam_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamsClient) DeleteTeam(ctx context.Context, in *DeleteTeamRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Teams_DeleteTeam_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamsClient) GetTeamsRoot(ctx context.Context, in *GetTeamsRootRequest, opts ...grpc.CallOption) (*ListTeamReply, error) {
	out := new(ListTeamReply)
	err := c.cc.Invoke(ctx, Teams_GetTeamsRoot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamsClient) GetTeam(ctx context.Context, in *GetTeamRequest, opts ...grpc.CallOption) (*Team, error) {
	out := new(Team)
	err := c.cc.Invoke(ctx, Teams_GetTeam_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamsClient) ListTeamByIDs(ctx context.Context, in *ListTeamByIDsRequest, opts ...grpc.CallOption) (*ListTeamReply, error) {
	out := new(ListTeamReply)
	err := c.cc.Invoke(ctx, Teams_ListTeamByIDs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamsClient) ListTeam(ctx context.Context, in *ListTeamRequest, opts ...grpc.CallOption) (*ListTeamReply, error) {
	out := new(ListTeamReply)
	err := c.cc.Invoke(ctx, Teams_ListTeam_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamsClient) ListMembers(ctx context.Context, in *ListMembersRequest, opts ...grpc.CallOption) (*ListMembersReply, error) {
	out := new(ListMembersReply)
	err := c.cc.Invoke(ctx, Teams_ListMembers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamsClient) AddMembers(ctx context.Context, in *AddMembersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Teams_AddMembers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamsClient) DeleteMembers(ctx context.Context, in *DeleteMembersRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Teams_DeleteMembers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamsClient) SetMembersRole(ctx context.Context, in *SetMembersRoleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Teams_SetMembersRole_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TeamsServer is the server API for Teams service.
// All implementations must embed UnimplementedTeamsServer
// for forward compatibility
type TeamsServer interface {
	CreateTeam(context.Context, *CreateTeamRequest) (*Team, error)
	UpdateTeam(context.Context, *UpdateTeamRequest) (*Team, error)
	DeleteTeam(context.Context, *DeleteTeamRequest) (*emptypb.Empty, error)
	GetTeamsRoot(context.Context, *GetTeamsRootRequest) (*ListTeamReply, error)
	GetTeam(context.Context, *GetTeamRequest) (*Team, error)
	// rpc only
	ListTeamByIDs(context.Context, *ListTeamByIDsRequest) (*ListTeamReply, error)
	ListTeam(context.Context, *ListTeamRequest) (*ListTeamReply, error)
	ListMembers(context.Context, *ListMembersRequest) (*ListMembersReply, error)
	// send join-team invitation to mentioned users
	AddMembers(context.Context, *AddMembersRequest) (*emptypb.Empty, error)
	DeleteMembers(context.Context, *DeleteMembersRequest) (*emptypb.Empty, error)
	SetMembersRole(context.Context, *SetMembersRoleRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedTeamsServer()
}

// UnimplementedTeamsServer must be embedded to have forward compatible implementations.
type UnimplementedTeamsServer struct {
}

func (UnimplementedTeamsServer) CreateTeam(context.Context, *CreateTeamRequest) (*Team, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTeam not implemented")
}
func (UnimplementedTeamsServer) UpdateTeam(context.Context, *UpdateTeamRequest) (*Team, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTeam not implemented")
}
func (UnimplementedTeamsServer) DeleteTeam(context.Context, *DeleteTeamRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTeam not implemented")
}
func (UnimplementedTeamsServer) GetTeamsRoot(context.Context, *GetTeamsRootRequest) (*ListTeamReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTeamsRoot not implemented")
}
func (UnimplementedTeamsServer) GetTeam(context.Context, *GetTeamRequest) (*Team, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTeam not implemented")
}
func (UnimplementedTeamsServer) ListTeamByIDs(context.Context, *ListTeamByIDsRequest) (*ListTeamReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTeamByIDs not implemented")
}
func (UnimplementedTeamsServer) ListTeam(context.Context, *ListTeamRequest) (*ListTeamReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTeam not implemented")
}
func (UnimplementedTeamsServer) ListMembers(context.Context, *ListMembersRequest) (*ListMembersReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMembers not implemented")
}
func (UnimplementedTeamsServer) AddMembers(context.Context, *AddMembersRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddMembers not implemented")
}
func (UnimplementedTeamsServer) DeleteMembers(context.Context, *DeleteMembersRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMembers not implemented")
}
func (UnimplementedTeamsServer) SetMembersRole(context.Context, *SetMembersRoleRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetMembersRole not implemented")
}
func (UnimplementedTeamsServer) mustEmbedUnimplementedTeamsServer() {}

// UnsafeTeamsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TeamsServer will
// result in compilation errors.
type UnsafeTeamsServer interface {
	mustEmbedUnimplementedTeamsServer()
}

func RegisterTeamsServer(s grpc.ServiceRegistrar, srv TeamsServer) {
	s.RegisterService(&Teams_ServiceDesc, srv)
}

func _Teams_CreateTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamsServer).CreateTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Teams_CreateTeam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamsServer).CreateTeam(ctx, req.(*CreateTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Teams_UpdateTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamsServer).UpdateTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Teams_UpdateTeam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamsServer).UpdateTeam(ctx, req.(*UpdateTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Teams_DeleteTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamsServer).DeleteTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Teams_DeleteTeam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamsServer).DeleteTeam(ctx, req.(*DeleteTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Teams_GetTeamsRoot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTeamsRootRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamsServer).GetTeamsRoot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Teams_GetTeamsRoot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamsServer).GetTeamsRoot(ctx, req.(*GetTeamsRootRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Teams_GetTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamsServer).GetTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Teams_GetTeam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamsServer).GetTeam(ctx, req.(*GetTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Teams_ListTeamByIDs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTeamByIDsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamsServer).ListTeamByIDs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Teams_ListTeamByIDs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamsServer).ListTeamByIDs(ctx, req.(*ListTeamByIDsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Teams_ListTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamsServer).ListTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Teams_ListTeam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamsServer).ListTeam(ctx, req.(*ListTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Teams_ListMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamsServer).ListMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Teams_ListMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamsServer).ListMembers(ctx, req.(*ListMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Teams_AddMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamsServer).AddMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Teams_AddMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamsServer).AddMembers(ctx, req.(*AddMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Teams_DeleteMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamsServer).DeleteMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Teams_DeleteMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamsServer).DeleteMembers(ctx, req.(*DeleteMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Teams_SetMembersRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMembersRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamsServer).SetMembersRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Teams_SetMembersRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamsServer).SetMembersRole(ctx, req.(*SetMembersRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Teams_ServiceDesc is the grpc.ServiceDesc for Teams service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Teams_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "iam.v1.Teams",
	HandlerType: (*TeamsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateTeam",
			Handler:    _Teams_CreateTeam_Handler,
		},
		{
			MethodName: "UpdateTeam",
			Handler:    _Teams_UpdateTeam_Handler,
		},
		{
			MethodName: "DeleteTeam",
			Handler:    _Teams_DeleteTeam_Handler,
		},
		{
			MethodName: "GetTeamsRoot",
			Handler:    _Teams_GetTeamsRoot_Handler,
		},
		{
			MethodName: "GetTeam",
			Handler:    _Teams_GetTeam_Handler,
		},
		{
			MethodName: "ListTeamByIDs",
			Handler:    _Teams_ListTeamByIDs_Handler,
		},
		{
			MethodName: "ListTeam",
			Handler:    _Teams_ListTeam_Handler,
		},
		{
			MethodName: "ListMembers",
			Handler:    _Teams_ListMembers_Handler,
		},
		{
			MethodName: "AddMembers",
			Handler:    _Teams_AddMembers_Handler,
		},
		{
			MethodName: "DeleteMembers",
			Handler:    _Teams_DeleteMembers_Handler,
		},
		{
			MethodName: "SetMembersRole",
			Handler:    _Teams_SetMembersRole_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "iam/v1/team.proto",
}
