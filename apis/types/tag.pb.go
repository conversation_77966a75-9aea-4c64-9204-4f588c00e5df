// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: types/tag.proto

package types

import (
	_ "github.com/google/gnostic/openapiv3"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TagRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Tags []string `protobuf:"bytes,2,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *TagRequest) Reset() {
	*x = TagRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_tag_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagRequest) ProtoMessage() {}

func (x *TagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_types_tag_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagRequest.ProtoReflect.Descriptor instead.
func (*TagRequest) Descriptor() ([]byte, []int) {
	return file_types_tag_proto_rawDescGZIP(), []int{0}
}

func (x *TagRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *TagRequest) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

type TagList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tags []string `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *TagList) Reset() {
	*x = TagList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_tag_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TagList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagList) ProtoMessage() {}

func (x *TagList) ProtoReflect() protoreflect.Message {
	mi := &file_types_tag_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagList.ProtoReflect.Descriptor instead.
func (*TagList) Descriptor() ([]byte, []int) {
	return file_types_tag_proto_rawDescGZIP(), []int{1}
}

func (x *TagList) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

var File_types_tag_proto protoreflect.FileDescriptor

var file_types_tag_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x74, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x44, 0x0a, 0x0a, 0x54, 0x61, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x3a, 0x10, 0xba, 0x47, 0x0d, 0xba,
	0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x04, 0x74, 0x61, 0x67, 0x73, 0x22, 0x29, 0x0a, 0x07,
	0x54, 0x61, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x3a, 0x0a, 0xba, 0x47, 0x07,
	0xba, 0x01, 0x04, 0x74, 0x61, 0x67, 0x73, 0x42, 0x3b, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x50, 0x01, 0x5a, 0x30, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x3b, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_types_tag_proto_rawDescOnce sync.Once
	file_types_tag_proto_rawDescData = file_types_tag_proto_rawDesc
)

func file_types_tag_proto_rawDescGZIP() []byte {
	file_types_tag_proto_rawDescOnce.Do(func() {
		file_types_tag_proto_rawDescData = protoimpl.X.CompressGZIP(file_types_tag_proto_rawDescData)
	})
	return file_types_tag_proto_rawDescData
}

var file_types_tag_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_types_tag_proto_goTypes = []interface{}{
	(*TagRequest)(nil), // 0: types.TagRequest
	(*TagList)(nil),    // 1: types.TagList
}
var file_types_tag_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_types_tag_proto_init() }
func file_types_tag_proto_init() {
	if File_types_tag_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_types_tag_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TagRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_tag_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TagList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_types_tag_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_types_tag_proto_goTypes,
		DependencyIndexes: file_types_tag_proto_depIdxs,
		MessageInfos:      file_types_tag_proto_msgTypes,
	}.Build()
	File_types_tag_proto = out.File
	file_types_tag_proto_rawDesc = nil
	file_types_tag_proto_goTypes = nil
	file_types_tag_proto_depIdxs = nil
}
