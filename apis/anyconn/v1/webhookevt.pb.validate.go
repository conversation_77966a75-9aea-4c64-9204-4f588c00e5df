// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anyconn/v1/webhookevt.proto

package anyconn

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateWebhookEvtRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateWebhookEvtRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateWebhookEvtRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateWebhookEvtRequestMultiError, or nil if none found.
func (m *CreateWebhookEvtRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateWebhookEvtRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Platform

	// no validation rules for EvtId

	// no validation rules for ProjectUid

	// no validation rules for TaskUid

	// no validation rules for JobUid

	// no validation rules for Body

	if len(errors) > 0 {
		return CreateWebhookEvtRequestMultiError(errors)
	}

	return nil
}

// CreateWebhookEvtRequestMultiError is an error wrapping multiple validation
// errors returned by CreateWebhookEvtRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateWebhookEvtRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateWebhookEvtRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateWebhookEvtRequestMultiError) AllErrors() []error { return m }

// CreateWebhookEvtRequestValidationError is the validation error returned by
// CreateWebhookEvtRequest.Validate if the designated constraints aren't met.
type CreateWebhookEvtRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateWebhookEvtRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateWebhookEvtRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateWebhookEvtRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateWebhookEvtRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateWebhookEvtRequestValidationError) ErrorName() string {
	return "CreateWebhookEvtRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateWebhookEvtRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateWebhookEvtRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateWebhookEvtRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateWebhookEvtRequestValidationError{}

// Validate checks the field values on UpdateWebhookEvtRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateWebhookEvtRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateWebhookEvtRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateWebhookEvtRequestMultiError, or nil if none found.
func (m *UpdateWebhookEvtRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateWebhookEvtRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if all {
		switch v := interface{}(m.GetEvt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateWebhookEvtRequestValidationError{
					field:  "Evt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateWebhookEvtRequestValidationError{
					field:  "Evt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEvt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateWebhookEvtRequestValidationError{
				field:  "Evt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateWebhookEvtRequestMultiError(errors)
	}

	return nil
}

// UpdateWebhookEvtRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateWebhookEvtRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateWebhookEvtRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateWebhookEvtRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateWebhookEvtRequestMultiError) AllErrors() []error { return m }

// UpdateWebhookEvtRequestValidationError is the validation error returned by
// UpdateWebhookEvtRequest.Validate if the designated constraints aren't met.
type UpdateWebhookEvtRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateWebhookEvtRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateWebhookEvtRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateWebhookEvtRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateWebhookEvtRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateWebhookEvtRequestValidationError) ErrorName() string {
	return "UpdateWebhookEvtRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateWebhookEvtRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateWebhookEvtRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateWebhookEvtRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateWebhookEvtRequestValidationError{}

// Validate checks the field values on DeleteWebhookEvtRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteWebhookEvtRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteWebhookEvtRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteWebhookEvtRequestMultiError, or nil if none found.
func (m *DeleteWebhookEvtRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteWebhookEvtRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return DeleteWebhookEvtRequestMultiError(errors)
	}

	return nil
}

// DeleteWebhookEvtRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteWebhookEvtRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteWebhookEvtRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteWebhookEvtRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteWebhookEvtRequestMultiError) AllErrors() []error { return m }

// DeleteWebhookEvtRequestValidationError is the validation error returned by
// DeleteWebhookEvtRequest.Validate if the designated constraints aren't met.
type DeleteWebhookEvtRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteWebhookEvtRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteWebhookEvtRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteWebhookEvtRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteWebhookEvtRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteWebhookEvtRequestValidationError) ErrorName() string {
	return "DeleteWebhookEvtRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteWebhookEvtRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteWebhookEvtRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteWebhookEvtRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteWebhookEvtRequestValidationError{}

// Validate checks the field values on GetWebhookEvtRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetWebhookEvtRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetWebhookEvtRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetWebhookEvtRequestMultiError, or nil if none found.
func (m *GetWebhookEvtRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWebhookEvtRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return GetWebhookEvtRequestMultiError(errors)
	}

	return nil
}

// GetWebhookEvtRequestMultiError is an error wrapping multiple validation
// errors returned by GetWebhookEvtRequest.ValidateAll() if the designated
// constraints aren't met.
type GetWebhookEvtRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWebhookEvtRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWebhookEvtRequestMultiError) AllErrors() []error { return m }

// GetWebhookEvtRequestValidationError is the validation error returned by
// GetWebhookEvtRequest.Validate if the designated constraints aren't met.
type GetWebhookEvtRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWebhookEvtRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetWebhookEvtRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetWebhookEvtRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWebhookEvtRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWebhookEvtRequestValidationError) ErrorName() string {
	return "GetWebhookEvtRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetWebhookEvtRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWebhookEvtRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWebhookEvtRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWebhookEvtRequestValidationError{}

// Validate checks the field values on WebhookEvtFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *WebhookEvtFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WebhookEvtFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WebhookEvtFilterMultiError, or nil if none found.
func (m *WebhookEvtFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *WebhookEvtFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskUid

	// no validation rules for JobUid

	if len(errors) > 0 {
		return WebhookEvtFilterMultiError(errors)
	}

	return nil
}

// WebhookEvtFilterMultiError is an error wrapping multiple validation errors
// returned by WebhookEvtFilter.ValidateAll() if the designated constraints
// aren't met.
type WebhookEvtFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebhookEvtFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebhookEvtFilterMultiError) AllErrors() []error { return m }

// WebhookEvtFilterValidationError is the validation error returned by
// WebhookEvtFilter.Validate if the designated constraints aren't met.
type WebhookEvtFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebhookEvtFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebhookEvtFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebhookEvtFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebhookEvtFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebhookEvtFilterValidationError) ErrorName() string { return "WebhookEvtFilterValidationError" }

// Error satisfies the builtin error interface
func (e WebhookEvtFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebhookEvtFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebhookEvtFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebhookEvtFilterValidationError{}

// Validate checks the field values on ListWebhookEvtRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListWebhookEvtRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListWebhookEvtRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListWebhookEvtRequestMultiError, or nil if none found.
func (m *ListWebhookEvtRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListWebhookEvtRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListWebhookEvtRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListWebhookEvtRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListWebhookEvtRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListWebhookEvtRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListWebhookEvtRequestMultiError(errors)
	}

	return nil
}

// ListWebhookEvtRequestMultiError is an error wrapping multiple validation
// errors returned by ListWebhookEvtRequest.ValidateAll() if the designated
// constraints aren't met.
type ListWebhookEvtRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListWebhookEvtRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListWebhookEvtRequestMultiError) AllErrors() []error { return m }

// ListWebhookEvtRequestValidationError is the validation error returned by
// ListWebhookEvtRequest.Validate if the designated constraints aren't met.
type ListWebhookEvtRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListWebhookEvtRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListWebhookEvtRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListWebhookEvtRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListWebhookEvtRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListWebhookEvtRequestValidationError) ErrorName() string {
	return "ListWebhookEvtRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListWebhookEvtRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListWebhookEvtRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListWebhookEvtRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListWebhookEvtRequestValidationError{}

// Validate checks the field values on ListWebhookEvtReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListWebhookEvtReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListWebhookEvtReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListWebhookEvtReplyMultiError, or nil if none found.
func (m *ListWebhookEvtReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListWebhookEvtReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetWebhookevts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListWebhookEvtReplyValidationError{
						field:  fmt.Sprintf("Webhookevts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListWebhookEvtReplyValidationError{
						field:  fmt.Sprintf("Webhookevts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListWebhookEvtReplyValidationError{
					field:  fmt.Sprintf("Webhookevts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for Total

	if len(errors) > 0 {
		return ListWebhookEvtReplyMultiError(errors)
	}

	return nil
}

// ListWebhookEvtReplyMultiError is an error wrapping multiple validation
// errors returned by ListWebhookEvtReply.ValidateAll() if the designated
// constraints aren't met.
type ListWebhookEvtReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListWebhookEvtReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListWebhookEvtReplyMultiError) AllErrors() []error { return m }

// ListWebhookEvtReplyValidationError is the validation error returned by
// ListWebhookEvtReply.Validate if the designated constraints aren't met.
type ListWebhookEvtReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListWebhookEvtReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListWebhookEvtReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListWebhookEvtReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListWebhookEvtReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListWebhookEvtReplyValidationError) ErrorName() string {
	return "ListWebhookEvtReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListWebhookEvtReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListWebhookEvtReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListWebhookEvtReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListWebhookEvtReplyValidationError{}

// Validate checks the field values on WebhookEvt with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WebhookEvt) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WebhookEvt with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WebhookEvtMultiError, or
// nil if none found.
func (m *WebhookEvt) ValidateAll() error {
	return m.validate(true)
}

func (m *WebhookEvt) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Platform

	// no validation rules for EvtId

	// no validation rules for ProjectUid

	// no validation rules for TaskUid

	// no validation rules for JobUid

	// no validation rules for Done

	// no validation rules for Extra

	// no validation rules for Body

	// no validation rules for ConvertedAnnos

	// no validation rules for Errors

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WebhookEvtValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WebhookEvtValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WebhookEvtValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WebhookEvtMultiError(errors)
	}

	return nil
}

// WebhookEvtMultiError is an error wrapping multiple validation errors
// returned by WebhookEvt.ValidateAll() if the designated constraints aren't met.
type WebhookEvtMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebhookEvtMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebhookEvtMultiError) AllErrors() []error { return m }

// WebhookEvtValidationError is the validation error returned by
// WebhookEvt.Validate if the designated constraints aren't met.
type WebhookEvtValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebhookEvtValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebhookEvtValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebhookEvtValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebhookEvtValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebhookEvtValidationError) ErrorName() string { return "WebhookEvtValidationError" }

// Error satisfies the builtin error interface
func (e WebhookEvtValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebhookEvt.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebhookEvtValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebhookEvtValidationError{}
