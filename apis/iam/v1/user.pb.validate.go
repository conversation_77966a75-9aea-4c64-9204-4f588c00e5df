// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: iam/v1/user.proto

package iam

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateUserRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUserRequestMultiError, or nil if none found.
func (m *CreateUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for Phone

	// no validation rules for Email

	// no validation rules for Avatar

	// no validation rules for Role

	if _, ok := User_Gender_Enum_name[int32(m.GetGender())]; !ok {
		err := CreateUserRequestValidationError{
			field:  "Gender",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Birthday

	// no validation rules for Province

	// no validation rules for City

	if len(errors) > 0 {
		return CreateUserRequestMultiError(errors)
	}

	return nil
}

// CreateUserRequestMultiError is an error wrapping multiple validation errors
// returned by CreateUserRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUserRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUserRequestMultiError) AllErrors() []error { return m }

// CreateUserRequestValidationError is the validation error returned by
// CreateUserRequest.Validate if the designated constraints aren't met.
type CreateUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUserRequestValidationError) ErrorName() string {
	return "CreateUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUserRequestValidationError{}

// Validate checks the field values on BatchCreateUsersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchCreateUsersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchCreateUsersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchCreateUsersRequestMultiError, or nil if none found.
func (m *BatchCreateUsersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchCreateUsersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchCreateUsersRequestValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchCreateUsersRequestValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchCreateUsersRequestValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchCreateUsersRequestMultiError(errors)
	}

	return nil
}

// BatchCreateUsersRequestMultiError is an error wrapping multiple validation
// errors returned by BatchCreateUsersRequest.ValidateAll() if the designated
// constraints aren't met.
type BatchCreateUsersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchCreateUsersRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchCreateUsersRequestMultiError) AllErrors() []error { return m }

// BatchCreateUsersRequestValidationError is the validation error returned by
// BatchCreateUsersRequest.Validate if the designated constraints aren't met.
type BatchCreateUsersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchCreateUsersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchCreateUsersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchCreateUsersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchCreateUsersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchCreateUsersRequestValidationError) ErrorName() string {
	return "BatchCreateUsersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchCreateUsersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchCreateUsersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchCreateUsersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchCreateUsersRequestValidationError{}

// Validate checks the field values on UpdateUserRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserRequestMultiError, or nil if none found.
func (m *UpdateUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateUserRequestValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateUserRequestValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateUserRequestValidationError{
				field:  "User",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateUserRequestMultiError(errors)
	}

	return nil
}

// UpdateUserRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateUserRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserRequestMultiError) AllErrors() []error { return m }

// UpdateUserRequestValidationError is the validation error returned by
// UpdateUserRequest.Validate if the designated constraints aren't met.
type UpdateUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserRequestValidationError) ErrorName() string {
	return "UpdateUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserRequestValidationError{}

// Validate checks the field values on DeleteUserRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteUserRequestMultiError, or nil if none found.
func (m *DeleteUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return DeleteUserRequestMultiError(errors)
	}

	return nil
}

// DeleteUserRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteUserRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteUserRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteUserRequestMultiError) AllErrors() []error { return m }

// DeleteUserRequestValidationError is the validation error returned by
// DeleteUserRequest.Validate if the designated constraints aren't met.
type DeleteUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteUserRequestValidationError) ErrorName() string {
	return "DeleteUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteUserRequestValidationError{}

// Validate checks the field values on GetUserRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetUserRequestMultiError,
// or nil if none found.
func (m *GetUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return GetUserRequestMultiError(errors)
	}

	return nil
}

// GetUserRequestMultiError is an error wrapping multiple validation errors
// returned by GetUserRequest.ValidateAll() if the designated constraints
// aren't met.
type GetUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserRequestMultiError) AllErrors() []error { return m }

// GetUserRequestValidationError is the validation error returned by
// GetUserRequest.Validate if the designated constraints aren't met.
type GetUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserRequestValidationError) ErrorName() string { return "GetUserRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserRequestValidationError{}

// Validate checks the field values on ListUserRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListUserRequestMultiError, or nil if none found.
func (m *ListUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	if val := m.GetPagesz(); val < 0 || val > 500 {
		err := ListUserRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 500]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for NamePattern

	for idx, item := range m.GetUids() {
		_, _ = idx, item

		if !_ListUserRequest_Uids_Pattern.MatchString(item) {
			err := ListUserRequestValidationError{
				field:  fmt.Sprintf("Uids[%v]", idx),
				reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	for idx, item := range m.GetPhones() {
		_, _ = idx, item

		if !_ListUserRequest_Phones_Pattern.MatchString(item) {
			err := ListUserRequestValidationError{
				field:  fmt.Sprintf("Phones[%v]", idx),
				reason: "value does not match regex pattern \"^\\\\+\\\\d+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	for idx, item := range m.GetEmails() {
		_, _ = idx, item

		if !_ListUserRequest_Emails_Pattern.MatchString(item) {
			err := ListUserRequestValidationError{
				field:  fmt.Sprintf("Emails[%v]", idx),
				reason: "value does not match regex pattern \"^[\\\\w.-]+@[\\\\w.-]+\\\\.[\\\\w.]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for WithOrg

	// no validation rules for OrgUid

	if len(errors) > 0 {
		return ListUserRequestMultiError(errors)
	}

	return nil
}

// ListUserRequestMultiError is an error wrapping multiple validation errors
// returned by ListUserRequest.ValidateAll() if the designated constraints
// aren't met.
type ListUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserRequestMultiError) AllErrors() []error { return m }

// ListUserRequestValidationError is the validation error returned by
// ListUserRequest.Validate if the designated constraints aren't met.
type ListUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUserRequestValidationError) ErrorName() string { return "ListUserRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserRequestValidationError{}

var _ListUserRequest_Uids_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

var _ListUserRequest_Phones_Pattern = regexp.MustCompile("^\\+\\d+$")

var _ListUserRequest_Emails_Pattern = regexp.MustCompile("^[\\w.-]+@[\\w.-]+\\.[\\w.]+$")

// Validate checks the field values on ListUserReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListUserReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUserReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListUserReplyMultiError, or
// nil if none found.
func (m *ListUserReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUserReplyValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUserReplyValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUserReplyValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetOrgs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUserReplyValidationError{
						field:  fmt.Sprintf("Orgs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUserReplyValidationError{
						field:  fmt.Sprintf("Orgs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUserReplyValidationError{
					field:  fmt.Sprintf("Orgs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListUserReplyMultiError(errors)
	}

	return nil
}

// ListUserReplyMultiError is an error wrapping multiple validation errors
// returned by ListUserReply.ValidateAll() if the designated constraints
// aren't met.
type ListUserReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserReplyMultiError) AllErrors() []error { return m }

// ListUserReplyValidationError is the validation error returned by
// ListUserReply.Validate if the designated constraints aren't met.
type ListUserReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUserReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUserReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUserReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUserReplyValidationError) ErrorName() string { return "ListUserReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListUserReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserReplyValidationError{}

// Validate checks the field values on LoginRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoginRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoginRequestMultiError, or
// nil if none found.
func (m *LoginRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _LoginRequest_IdType_NotInLookup[m.GetIdType()]; ok {
		err := LoginRequestValidationError{
			field:  "IdType",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := IDType_Enum_name[int32(m.GetIdType())]; !ok {
		err := LoginRequestValidationError{
			field:  "IdType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Identity

	if _, ok := _LoginRequest_AuthType_NotInLookup[m.GetAuthType()]; ok {
		err := LoginRequestValidationError{
			field:  "AuthType",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := LoginRequest_AuthType_Enum_name[int32(m.GetAuthType())]; !ok {
		err := LoginRequestValidationError{
			field:  "AuthType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Credential

	// no validation rules for Agreement

	if len(errors) > 0 {
		return LoginRequestMultiError(errors)
	}

	return nil
}

// LoginRequestMultiError is an error wrapping multiple validation errors
// returned by LoginRequest.ValidateAll() if the designated constraints aren't met.
type LoginRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginRequestMultiError) AllErrors() []error { return m }

// LoginRequestValidationError is the validation error returned by
// LoginRequest.Validate if the designated constraints aren't met.
type LoginRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginRequestValidationError) ErrorName() string { return "LoginRequestValidationError" }

// Error satisfies the builtin error interface
func (e LoginRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginRequestValidationError{}

var _LoginRequest_IdType_NotInLookup = map[IDType_Enum]struct{}{
	0: {},
}

var _LoginRequest_AuthType_NotInLookup = map[LoginRequest_AuthType_Enum]struct{}{
	0: {},
}

// Validate checks the field values on LoginReply with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoginReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginReply with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoginReplyMultiError, or
// nil if none found.
func (m *LoginReply) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoginReplyValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoginReplyValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoginReplyValidationError{
				field:  "User",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	if all {
		switch v := interface{}(m.GetExpireTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoginReplyValidationError{
					field:  "ExpireTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoginReplyValidationError{
					field:  "ExpireTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpireTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoginReplyValidationError{
				field:  "ExpireTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFeperm()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoginReplyValidationError{
					field:  "Feperm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoginReplyValidationError{
					field:  "Feperm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFeperm()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoginReplyValidationError{
				field:  "Feperm",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAssumeBy()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoginReplyValidationError{
					field:  "AssumeBy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoginReplyValidationError{
					field:  "AssumeBy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAssumeBy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoginReplyValidationError{
				field:  "AssumeBy",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoginReplyMultiError(errors)
	}

	return nil
}

// LoginReplyMultiError is an error wrapping multiple validation errors
// returned by LoginReply.ValidateAll() if the designated constraints aren't met.
type LoginReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginReplyMultiError) AllErrors() []error { return m }

// LoginReplyValidationError is the validation error returned by
// LoginReply.Validate if the designated constraints aren't met.
type LoginReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginReplyValidationError) ErrorName() string { return "LoginReplyValidationError" }

// Error satisfies the builtin error interface
func (e LoginReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginReplyValidationError{}

// Validate checks the field values on SendAuthCodeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendAuthCodeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendAuthCodeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendAuthCodeRequestMultiError, or nil if none found.
func (m *SendAuthCodeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendAuthCodeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _SendAuthCodeRequest_Purpose_NotInLookup[m.GetPurpose()]; ok {
		err := SendAuthCodeRequestValidationError{
			field:  "Purpose",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := SendAuthCodeRequest_Purpose_Enum_name[int32(m.GetPurpose())]; !ok {
		err := SendAuthCodeRequestValidationError{
			field:  "Purpose",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _SendAuthCodeRequest_Channel_NotInLookup[m.GetChannel()]; ok {
		err := SendAuthCodeRequestValidationError{
			field:  "Channel",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := SendAuthCodeRequest_Channel_Enum_name[int32(m.GetChannel())]; !ok {
		err := SendAuthCodeRequestValidationError{
			field:  "Channel",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Receiver

	// no validation rules for Locale

	if len(errors) > 0 {
		return SendAuthCodeRequestMultiError(errors)
	}

	return nil
}

// SendAuthCodeRequestMultiError is an error wrapping multiple validation
// errors returned by SendAuthCodeRequest.ValidateAll() if the designated
// constraints aren't met.
type SendAuthCodeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendAuthCodeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendAuthCodeRequestMultiError) AllErrors() []error { return m }

// SendAuthCodeRequestValidationError is the validation error returned by
// SendAuthCodeRequest.Validate if the designated constraints aren't met.
type SendAuthCodeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendAuthCodeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendAuthCodeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendAuthCodeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendAuthCodeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendAuthCodeRequestValidationError) ErrorName() string {
	return "SendAuthCodeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SendAuthCodeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendAuthCodeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendAuthCodeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendAuthCodeRequestValidationError{}

var _SendAuthCodeRequest_Purpose_NotInLookup = map[SendAuthCodeRequest_Purpose_Enum]struct{}{
	0: {},
}

var _SendAuthCodeRequest_Channel_NotInLookup = map[SendAuthCodeRequest_Channel_Enum]struct{}{
	0: {},
}

// Validate checks the field values on SendAuthCodeReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SendAuthCodeReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendAuthCodeReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendAuthCodeReplyMultiError, or nil if none found.
func (m *SendAuthCodeReply) ValidateAll() error {
	return m.validate(true)
}

func (m *SendAuthCodeReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sequence

	// no validation rules for Agreement

	if len(errors) > 0 {
		return SendAuthCodeReplyMultiError(errors)
	}

	return nil
}

// SendAuthCodeReplyMultiError is an error wrapping multiple validation errors
// returned by SendAuthCodeReply.ValidateAll() if the designated constraints
// aren't met.
type SendAuthCodeReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendAuthCodeReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendAuthCodeReplyMultiError) AllErrors() []error { return m }

// SendAuthCodeReplyValidationError is the validation error returned by
// SendAuthCodeReply.Validate if the designated constraints aren't met.
type SendAuthCodeReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendAuthCodeReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendAuthCodeReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendAuthCodeReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendAuthCodeReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendAuthCodeReplyValidationError) ErrorName() string {
	return "SendAuthCodeReplyValidationError"
}

// Error satisfies the builtin error interface
func (e SendAuthCodeReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendAuthCodeReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendAuthCodeReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendAuthCodeReplyValidationError{}

// Validate checks the field values on AssumeUserRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AssumeUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssumeUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AssumeUserRequestMultiError, or nil if none found.
func (m *AssumeUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AssumeUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Identity

	if len(errors) > 0 {
		return AssumeUserRequestMultiError(errors)
	}

	return nil
}

// AssumeUserRequestMultiError is an error wrapping multiple validation errors
// returned by AssumeUserRequest.ValidateAll() if the designated constraints
// aren't met.
type AssumeUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssumeUserRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssumeUserRequestMultiError) AllErrors() []error { return m }

// AssumeUserRequestValidationError is the validation error returned by
// AssumeUserRequest.Validate if the designated constraints aren't met.
type AssumeUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssumeUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssumeUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssumeUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssumeUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssumeUserRequestValidationError) ErrorName() string {
	return "AssumeUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AssumeUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssumeUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssumeUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssumeUserRequestValidationError{}

// Validate checks the field values on IsAllowedRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *IsAllowedRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsAllowedRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsAllowedRequestMultiError, or nil if none found.
func (m *IsAllowedRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IsAllowedRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if all {
		switch v := interface{}(m.GetAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsAllowedRequestValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsAllowedRequestValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsAllowedRequestValidationError{
				field:  "Action",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IsAllowedRequestMultiError(errors)
	}

	return nil
}

// IsAllowedRequestMultiError is an error wrapping multiple validation errors
// returned by IsAllowedRequest.ValidateAll() if the designated constraints
// aren't met.
type IsAllowedRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsAllowedRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsAllowedRequestMultiError) AllErrors() []error { return m }

// IsAllowedRequestValidationError is the validation error returned by
// IsAllowedRequest.Validate if the designated constraints aren't met.
type IsAllowedRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsAllowedRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsAllowedRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsAllowedRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsAllowedRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsAllowedRequestValidationError) ErrorName() string { return "IsAllowedRequestValidationError" }

// Error satisfies the builtin error interface
func (e IsAllowedRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsAllowedRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsAllowedRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsAllowedRequestValidationError{}

// Validate checks the field values on IsAllowedReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IsAllowedReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsAllowedReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IsAllowedReplyMultiError,
// or nil if none found.
func (m *IsAllowedReply) ValidateAll() error {
	return m.validate(true)
}

func (m *IsAllowedReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Allowed

	if len(errors) > 0 {
		return IsAllowedReplyMultiError(errors)
	}

	return nil
}

// IsAllowedReplyMultiError is an error wrapping multiple validation errors
// returned by IsAllowedReply.ValidateAll() if the designated constraints
// aren't met.
type IsAllowedReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsAllowedReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsAllowedReplyMultiError) AllErrors() []error { return m }

// IsAllowedReplyValidationError is the validation error returned by
// IsAllowedReply.Validate if the designated constraints aren't met.
type IsAllowedReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsAllowedReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsAllowedReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsAllowedReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsAllowedReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsAllowedReplyValidationError) ErrorName() string { return "IsAllowedReplyValidationError" }

// Error satisfies the builtin error interface
func (e IsAllowedReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsAllowedReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsAllowedReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsAllowedReplyValidationError{}

// Validate checks the field values on GetPermsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetPermsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPermsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPermsRequestMultiError, or nil if none found.
func (m *GetPermsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPermsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetPermsRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := GetPermsRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetPermsRequest_Resource_Pattern.MatchString(m.GetResource()) {
		err := GetPermsRequestValidationError{
			field:  "Resource",
			reason: "value does not match regex pattern \"^\\\\w+:([\\\\w.-]+)?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetPermsRequest_Scope_Pattern.MatchString(m.GetScope()) {
		err := GetPermsRequestValidationError{
			field:  "Scope",
			reason: "value does not match regex pattern \"^(IamGroup:[\\\\w.-]+)?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetPerms() {
		_, _ = idx, item

		if !_GetPermsRequest_Perms_Pattern.MatchString(item) {
			err := GetPermsRequestValidationError{
				field:  fmt.Sprintf("Perms[%v]", idx),
				reason: "value does not match regex pattern \"^\\\\w+\\\\.\\\\w+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return GetPermsRequestMultiError(errors)
	}

	return nil
}

// GetPermsRequestMultiError is an error wrapping multiple validation errors
// returned by GetPermsRequest.ValidateAll() if the designated constraints
// aren't met.
type GetPermsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPermsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPermsRequestMultiError) AllErrors() []error { return m }

// GetPermsRequestValidationError is the validation error returned by
// GetPermsRequest.Validate if the designated constraints aren't met.
type GetPermsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPermsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPermsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPermsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPermsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPermsRequestValidationError) ErrorName() string { return "GetPermsRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetPermsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPermsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPermsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPermsRequestValidationError{}

var _GetPermsRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

var _GetPermsRequest_Resource_Pattern = regexp.MustCompile("^\\w+:([\\w.-]+)?$")

var _GetPermsRequest_Scope_Pattern = regexp.MustCompile("^(IamGroup:[\\w.-]+)?$")

var _GetPermsRequest_Perms_Pattern = regexp.MustCompile("^\\w+\\.\\w+$")

// Validate checks the field values on GetPermsReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetPermsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPermsReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetPermsReplyMultiError, or
// nil if none found.
func (m *GetPermsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPermsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetPermsReplyMultiError(errors)
	}

	return nil
}

// GetPermsReplyMultiError is an error wrapping multiple validation errors
// returned by GetPermsReply.ValidateAll() if the designated constraints
// aren't met.
type GetPermsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPermsReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPermsReplyMultiError) AllErrors() []error { return m }

// GetPermsReplyValidationError is the validation error returned by
// GetPermsReply.Validate if the designated constraints aren't met.
type GetPermsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPermsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPermsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPermsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPermsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPermsReplyValidationError) ErrorName() string { return "GetPermsReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetPermsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPermsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPermsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPermsReplyValidationError{}

// Validate checks the field values on LoginRequest_AuthType with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoginRequest_AuthType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginRequest_AuthType with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoginRequest_AuthTypeMultiError, or nil if none found.
func (m *LoginRequest_AuthType) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginRequest_AuthType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return LoginRequest_AuthTypeMultiError(errors)
	}

	return nil
}

// LoginRequest_AuthTypeMultiError is an error wrapping multiple validation
// errors returned by LoginRequest_AuthType.ValidateAll() if the designated
// constraints aren't met.
type LoginRequest_AuthTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginRequest_AuthTypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginRequest_AuthTypeMultiError) AllErrors() []error { return m }

// LoginRequest_AuthTypeValidationError is the validation error returned by
// LoginRequest_AuthType.Validate if the designated constraints aren't met.
type LoginRequest_AuthTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginRequest_AuthTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginRequest_AuthTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginRequest_AuthTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginRequest_AuthTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginRequest_AuthTypeValidationError) ErrorName() string {
	return "LoginRequest_AuthTypeValidationError"
}

// Error satisfies the builtin error interface
func (e LoginRequest_AuthTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginRequest_AuthType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginRequest_AuthTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginRequest_AuthTypeValidationError{}

// Validate checks the field values on SendAuthCodeRequest_Purpose with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendAuthCodeRequest_Purpose) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendAuthCodeRequest_Purpose with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendAuthCodeRequest_PurposeMultiError, or nil if none found.
func (m *SendAuthCodeRequest_Purpose) ValidateAll() error {
	return m.validate(true)
}

func (m *SendAuthCodeRequest_Purpose) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SendAuthCodeRequest_PurposeMultiError(errors)
	}

	return nil
}

// SendAuthCodeRequest_PurposeMultiError is an error wrapping multiple
// validation errors returned by SendAuthCodeRequest_Purpose.ValidateAll() if
// the designated constraints aren't met.
type SendAuthCodeRequest_PurposeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendAuthCodeRequest_PurposeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendAuthCodeRequest_PurposeMultiError) AllErrors() []error { return m }

// SendAuthCodeRequest_PurposeValidationError is the validation error returned
// by SendAuthCodeRequest_Purpose.Validate if the designated constraints
// aren't met.
type SendAuthCodeRequest_PurposeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendAuthCodeRequest_PurposeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendAuthCodeRequest_PurposeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendAuthCodeRequest_PurposeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendAuthCodeRequest_PurposeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendAuthCodeRequest_PurposeValidationError) ErrorName() string {
	return "SendAuthCodeRequest_PurposeValidationError"
}

// Error satisfies the builtin error interface
func (e SendAuthCodeRequest_PurposeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendAuthCodeRequest_Purpose.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendAuthCodeRequest_PurposeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendAuthCodeRequest_PurposeValidationError{}

// Validate checks the field values on SendAuthCodeRequest_Channel with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendAuthCodeRequest_Channel) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendAuthCodeRequest_Channel with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendAuthCodeRequest_ChannelMultiError, or nil if none found.
func (m *SendAuthCodeRequest_Channel) ValidateAll() error {
	return m.validate(true)
}

func (m *SendAuthCodeRequest_Channel) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SendAuthCodeRequest_ChannelMultiError(errors)
	}

	return nil
}

// SendAuthCodeRequest_ChannelMultiError is an error wrapping multiple
// validation errors returned by SendAuthCodeRequest_Channel.ValidateAll() if
// the designated constraints aren't met.
type SendAuthCodeRequest_ChannelMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendAuthCodeRequest_ChannelMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendAuthCodeRequest_ChannelMultiError) AllErrors() []error { return m }

// SendAuthCodeRequest_ChannelValidationError is the validation error returned
// by SendAuthCodeRequest_Channel.Validate if the designated constraints
// aren't met.
type SendAuthCodeRequest_ChannelValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendAuthCodeRequest_ChannelValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendAuthCodeRequest_ChannelValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendAuthCodeRequest_ChannelValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendAuthCodeRequest_ChannelValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendAuthCodeRequest_ChannelValidationError) ErrorName() string {
	return "SendAuthCodeRequest_ChannelValidationError"
}

// Error satisfies the builtin error interface
func (e SendAuthCodeRequest_ChannelValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendAuthCodeRequest_Channel.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendAuthCodeRequest_ChannelValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendAuthCodeRequest_ChannelValidationError{}

// Validate checks the field values on IsAllowedRequest_Action with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsAllowedRequest_Action) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsAllowedRequest_Action with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsAllowedRequest_ActionMultiError, or nil if none found.
func (m *IsAllowedRequest_Action) ValidateAll() error {
	return m.validate(true)
}

func (m *IsAllowedRequest_Action) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_IsAllowedRequest_Action_Resource_Pattern.MatchString(m.GetResource()) {
		err := IsAllowedRequest_ActionValidationError{
			field:  "Resource",
			reason: "value does not match regex pattern \"^\\\\w+:([\\\\w:.-]+)*$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_IsAllowedRequest_Action_Perm_Pattern.MatchString(m.GetPerm()) {
		err := IsAllowedRequest_ActionValidationError{
			field:  "Perm",
			reason: "value does not match regex pattern \"^\\\\w+\\\\.\\\\w+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return IsAllowedRequest_ActionMultiError(errors)
	}

	return nil
}

// IsAllowedRequest_ActionMultiError is an error wrapping multiple validation
// errors returned by IsAllowedRequest_Action.ValidateAll() if the designated
// constraints aren't met.
type IsAllowedRequest_ActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsAllowedRequest_ActionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsAllowedRequest_ActionMultiError) AllErrors() []error { return m }

// IsAllowedRequest_ActionValidationError is the validation error returned by
// IsAllowedRequest_Action.Validate if the designated constraints aren't met.
type IsAllowedRequest_ActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsAllowedRequest_ActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsAllowedRequest_ActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsAllowedRequest_ActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsAllowedRequest_ActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsAllowedRequest_ActionValidationError) ErrorName() string {
	return "IsAllowedRequest_ActionValidationError"
}

// Error satisfies the builtin error interface
func (e IsAllowedRequest_ActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsAllowedRequest_Action.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsAllowedRequest_ActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsAllowedRequest_ActionValidationError{}

var _IsAllowedRequest_Action_Resource_Pattern = regexp.MustCompile("^\\w+:([\\w:.-]+)*$")

var _IsAllowedRequest_Action_Perm_Pattern = regexp.MustCompile("^\\w+\\.\\w+$")
