{"noEdit": true, "task_label": "point_cloud", "task_type": 902, "dotLimitLine": "", "dotLimit": "", "template_name": "", "is_online": true, "per_type": false, "task_key": "", "template_key": "", "deny_reason": "", "postil_config": [{"name": "漏标", "value": 1, "status": 1}, {"name": "多标", "value": 2, "status": 1}, {"name": "错标", "value": 3, "status": 1}], "is_ocr_auto_fill": 0, "is_open_camera_tag": 0, "selection_method": 0, "fuse_configure_method": 0, "merge_point_cloud": 0, "boxsize": [], "sub_round": [], "dataclass_list": [], "mark_region": {"circle": []}, "properties": [{"toolName": "立体框", "toolType": "volume", "fuse_2d_tools_checkbox": ["rect"], "min_points_num": 0, "attrs_same": 1, "is_attrs_sync": 0, "fuse_tool": "rect", "class": [{"ptitle": "车辆", "pname": "car", "color": "rgb(234, 120, 113)", "pcode": "66d15da1", "is_instance": 0, "size_same": 0}, {"ptitle": "骑行者", "pname": "Cyclist", "color": "rgb(196, 50, 220)", "pcode": "906927b4", "is_instance": 0, "size_same": 0}, {"ptitle": "行人", "pname": "Person", "color": "rgb(124, 233, 181)", "pcode": "ea52f007", "is_instance": 0, "size_same": 0}], "attrs": [], "preset": []}], "attributeSeparation": 0, "properties2D": [{"toolName": "矩形", "toolType": "rect", "class": [{"ptitle": "车辆", "pname": "car", "color": "rgb(234, 120, 113)", "pcode": "66d15da1", "is_instance": 0, "size_same": 0}, {"ptitle": "骑行者", "pname": "Cyclist", "color": "rgb(196, 50, 220)", "pcode": "906927b4", "is_instance": 0, "size_same": 0}, {"ptitle": "行人", "pname": "Person", "color": "rgb(124, 233, 181)", "pcode": "ea52f007", "is_instance": 0, "size_same": 0}], "attrs": []}], "global": {"attrs": [], "class": []}, "price": {"channel_price": {}, "customer_price": {}}, "train_desc": "", "train_num": "", "train_max_num": "", "train_rate": "", "package_type": 1, "submit_at": "", "update_time": "", "check_limit": "", "rotate_picture_export_type": 1, "is_panoramic": 0, "is_class_type": 1, "group_type": 0, "assist": [], "is_vsample_interval": 0, "vsample_interval": "", "isCutting": 0, "splitChannels": 0, "minNum": "", "maxNum": "", "isConversion": 0, "conversion_type": 0, "isPinyin": 0, "task_mode": 1, "cut_type": 0, "isAudioProps": 1, "isAddTime": 0, "addTime": "", "is_prop_check": 0, "is_escape": 0, "appropriate_types": 1, "props_save": 0, "labeling_method": 0, "add_attr": 0, "modify_corpus": 0, "is_pre_labeled": 0, "pro_type": 0, "num_of_task": 1, "num_of_pack": "", "articleTitle_type": 0, "article_titleArr": [], "corpus_type": 0, "text_title": [], "ocr_content_configure": [], "camera_properties": [], "textadd": false, "add_tag": 0, "tag_type": 0, "reference_tag": 0, "min_words_length": "", "max_words_length": "", "min_num_pieces": "", "max_num_pieces": "", "regStatus": 0, "is_expression": 0, "expression_content": "", "generate_mode": 0, "cue_word": [], "check_rule_name": 0, "translate": 0, "discrete": 0, "relationship_definition": [], "events_definition": [], "attribute_definition": [], "entity_premark": 0, "entity_library_id": "", "force_submit": 0, "cleaning_type": "1", "cleaningTypeArr": [], "type_list": [], "preset_type": "", "pass_total_limit": 0, "boxConsistent": 0, "is_private": 1, "project_type": {"pic": [{"task_type": 1001, "title": "图像通用标注", "imgsrc": "/images/show_1001.png", "icon": "#icontongyongbiaozhu", "is_allow": 1}, {"task_type": 1002, "title": "OCR文字转写", "imgsrc": "/images/show_1002.png", "icon": "#iconwenzizhuanxie", "is_allow": 1}, {"task_type": 1003, "title": "REID目标跟踪", "imgsrc": "/images/show_1003.png", "icon": "#iconmubiaogenzong", "is_allow": 1}, {"task_type": 1004, "title": "图像语义分割", "imgsrc": "/images/show_1004.png", "icon": "#iconyuyifenge1", "is_allow": 1}, {"task_type": 1006, "title": "人体关键点", "imgsrc": "/images/show_1006.png", "icon": "#<PERSON>rentiguanjiandian", "is_allow": 1}], "point_cloud": [{"task_type": 902, "title": "点云", "imgsrc": "/images/show_902.png", "icon": "#icondianyun2d3dronghe", "is_allow": 1}], "audio": [{"task_type": 704, "title": "音频标注", "imgsrc": "/images/show_704.png", "icon": "#iconyin<PERSON><PERSON><PERSON>hu", "is_allow": 1}], "text": [{"task_type": 1111, "title": "意图及实体标注", "imgsrc": "/images/show_1111.png", "icon": "#iconyi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_allow": 1}, {"task_type": 1202, "title": "命名实体识别与关系标注", "imgsrc": "/images/show_1202.png", "icon": "#iconmingmingshitishibieyuguanxibiaozhu", "is_allow": 1}, {"task_type": 1103, "title": "文本生成", "imgsrc": "/images/show_1103.png", "icon": "#iconwenben", "is_allow": 1}, {"task_type": 1112, "title": "文章判断", "imgsrc": "/images/show_1112.png", "icon": "#icon<PERSON>zhangpanduan", "is_allow": 1}, {"task_type": 1113, "title": "相似文本判断", "des": "给定参考语料，从一组句子中选择单条或多条相似文本", "imgsrc": "/images/show_1113.png", "icon": "#icon<PERSON>zhangpanduan", "is_allow": 1}], "video": [{"task_type": 1201, "title": "视频标注", "imgsrc": "/images/show_1201.png", "icon": "#icons<PERSON><PERSON><PERSON><PERSON><PERSON>", "is_allow": 1}]}, "issues": [{"name": "漏标", "value": 1, "status": 1}, {"name": "多标", "value": 2, "status": 1}, {"name": "错标", "value": 3, "status": 1}]}