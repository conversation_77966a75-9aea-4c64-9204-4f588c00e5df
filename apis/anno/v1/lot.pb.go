// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anno/v1/lot.proto

package anno

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	v1 "gitlab.rp.konvery.work/platform/apis/iam/v1"
	types "gitlab.rp.konvery.work/platform/apis/types"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Lot_Type_Enum int32

const (
	Lot_Type_unspecified Lot_Type_Enum = 0
	// object detection
	Lot_Type_annotate         Lot_Type_Enum = 1 // classify = 3;
	Lot_Type_segment_instance Lot_Type_Enum = 11
	Lot_Type_segment_semantic Lot_Type_Enum = 12
	Lot_Type_segment_panoptic Lot_Type_Enum = 13
)

// Enum value maps for Lot_Type_Enum.
var (
	Lot_Type_Enum_name = map[int32]string{
		0:  "unspecified",
		1:  "annotate",
		11: "segment_instance",
		12: "segment_semantic",
		13: "segment_panoptic",
	}
	Lot_Type_Enum_value = map[string]int32{
		"unspecified":      0,
		"annotate":         1,
		"segment_instance": 11,
		"segment_semantic": 12,
		"segment_panoptic": 13,
	}
)

func (x Lot_Type_Enum) Enum() *Lot_Type_Enum {
	p := new(Lot_Type_Enum)
	*p = x
	return p
}

func (x Lot_Type_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Lot_Type_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_lot_proto_enumTypes[0].Descriptor()
}

func (Lot_Type_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_lot_proto_enumTypes[0]
}

func (x Lot_Type_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Lot_Type_Enum.Descriptor instead.
func (Lot_Type_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{15, 0, 0}
}

type Lot_State_Enum int32

const (
	Lot_State_unspecified  Lot_State_Enum = 0
	Lot_State_initializing Lot_State_Enum = 1
	Lot_State_unstart      Lot_State_Enum = 2
	Lot_State_ongoing      Lot_State_Enum = 3
	Lot_State_finished     Lot_State_Enum = 4
	Lot_State_paused       Lot_State_Enum = 5
	Lot_State_canceled     Lot_State_Enum = 6
)

// Enum value maps for Lot_State_Enum.
var (
	Lot_State_Enum_name = map[int32]string{
		0: "unspecified",
		1: "initializing",
		2: "unstart",
		3: "ongoing",
		4: "finished",
		5: "paused",
		6: "canceled",
	}
	Lot_State_Enum_value = map[string]int32{
		"unspecified":  0,
		"initializing": 1,
		"unstart":      2,
		"ongoing":      3,
		"finished":     4,
		"paused":       5,
		"canceled":     6,
	}
)

func (x Lot_State_Enum) Enum() *Lot_State_Enum {
	p := new(Lot_State_Enum)
	*p = x
	return p
}

func (x Lot_State_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Lot_State_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_lot_proto_enumTypes[1].Descriptor()
}

func (Lot_State_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_lot_proto_enumTypes[1]
}

func (x Lot_State_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Lot_State_Enum.Descriptor instead.
func (Lot_State_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{15, 1, 0}
}

type Lot_Range_Shape_Enum int32

const (
	Lot_Range_Shape_unspecified Lot_Range_Shape_Enum = 0
	Lot_Range_Shape_circle      Lot_Range_Shape_Enum = 1
	Lot_Range_Shape_rectangle   Lot_Range_Shape_Enum = 2
)

// Enum value maps for Lot_Range_Shape_Enum.
var (
	Lot_Range_Shape_Enum_name = map[int32]string{
		0: "unspecified",
		1: "circle",
		2: "rectangle",
	}
	Lot_Range_Shape_Enum_value = map[string]int32{
		"unspecified": 0,
		"circle":      1,
		"rectangle":   2,
	}
)

func (x Lot_Range_Shape_Enum) Enum() *Lot_Range_Shape_Enum {
	p := new(Lot_Range_Shape_Enum)
	*p = x
	return p
}

func (x Lot_Range_Shape_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Lot_Range_Shape_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_lot_proto_enumTypes[2].Descriptor()
}

func (Lot_Range_Shape_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_lot_proto_enumTypes[2]
}

func (x Lot_Range_Shape_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Lot_Range_Shape_Enum.Descriptor instead.
func (Lot_Range_Shape_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{15, 2, 0, 0}
}

type CreateLotRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory in update-requests
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// mandatory in create-requests
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	// mandatory in create-requests
	ProjectUid string `protobuf:"bytes,4,opt,name=project_uid,json=projectUid,proto3" json:"project_uid,omitempty"`
	// task type; mandatory in create-requests
	Type Lot_Type_Enum `protobuf:"varint,5,opt,name=type,proto3,enum=anno.v1.Lot_Type_Enum" json:"type,omitempty"`
	// larger number indicates higher priority
	Priority int32 `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`
	// whether to automatically start
	Autostart bool `protobuf:"varint,7,opt,name=autostart,proto3" json:"autostart,omitempty"`
	// number of elements in a job
	JobSize int32 `protobuf:"varint,8,opt,name=job_size,json=jobSize,proto3" json:"job_size,omitempty"`
	// lot ontologies
	Ontologies *Lotontologies `protobuf:"bytes,9,opt,name=ontologies,proto3" json:"ontologies,omitempty"`
	// execution phases; phase number starts from 1
	Phases []*Lotphase `protobuf:"bytes,10,rep,name=phases,proto3" json:"phases,omitempty"`
	// execution instructions in format of HTML or Markdown
	Instruction string `protobuf:"bytes,11,opt,name=instruction,proto3" json:"instruction,omitempty"` // string tpl_uid = 6;
	OrgUid      string `protobuf:"bytes,12,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// mandatory in create-requests
	DataUid string `protobuf:"bytes,13,opt,name=data_uid,json=dataUid,proto3" json:"data_uid,omitempty"`
	// annotation result output config
	Out *OutConfig `protobuf:"bytes,14,opt,name=out,proto3" json:"out,omitempty"`
	// expected end time
	ExpEndTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=exp_end_time,json=expEndTime,proto3" json:"exp_end_time,omitempty"`
	// if to treat job as consecutive frames
	IsFrameSeries bool `protobuf:"varint,16,opt,name=is_frame_series,json=isFrameSeries,proto3" json:"is_frame_series,omitempty"`
	// order UID
	OrderUid string `protobuf:"bytes,18,opt,name=order_uid,json=orderUid,proto3" json:"order_uid,omitempty"`
	// annotation tool configuration
	ToolCfg *Lot_ToolConfig `protobuf:"bytes,19,opt,name=tool_cfg,json=toolCfg,proto3" json:"tool_cfg,omitempty"`
	// allowed comment reason list
	CommentReasons []*CommentReasonClass `protobuf:"bytes,20,rep,name=comment_reasons,json=commentReasons,proto3" json:"comment_reasons,omitempty"`
}

func (x *CreateLotRequest) Reset() {
	*x = CreateLotRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLotRequest) ProtoMessage() {}

func (x *CreateLotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLotRequest.ProtoReflect.Descriptor instead.
func (*CreateLotRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{0}
}

func (x *CreateLotRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *CreateLotRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateLotRequest) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *CreateLotRequest) GetProjectUid() string {
	if x != nil {
		return x.ProjectUid
	}
	return ""
}

func (x *CreateLotRequest) GetType() Lot_Type_Enum {
	if x != nil {
		return x.Type
	}
	return Lot_Type_unspecified
}

func (x *CreateLotRequest) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *CreateLotRequest) GetAutostart() bool {
	if x != nil {
		return x.Autostart
	}
	return false
}

func (x *CreateLotRequest) GetJobSize() int32 {
	if x != nil {
		return x.JobSize
	}
	return 0
}

func (x *CreateLotRequest) GetOntologies() *Lotontologies {
	if x != nil {
		return x.Ontologies
	}
	return nil
}

func (x *CreateLotRequest) GetPhases() []*Lotphase {
	if x != nil {
		return x.Phases
	}
	return nil
}

func (x *CreateLotRequest) GetInstruction() string {
	if x != nil {
		return x.Instruction
	}
	return ""
}

func (x *CreateLotRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *CreateLotRequest) GetDataUid() string {
	if x != nil {
		return x.DataUid
	}
	return ""
}

func (x *CreateLotRequest) GetOut() *OutConfig {
	if x != nil {
		return x.Out
	}
	return nil
}

func (x *CreateLotRequest) GetExpEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpEndTime
	}
	return nil
}

func (x *CreateLotRequest) GetIsFrameSeries() bool {
	if x != nil {
		return x.IsFrameSeries
	}
	return false
}

func (x *CreateLotRequest) GetOrderUid() string {
	if x != nil {
		return x.OrderUid
	}
	return ""
}

func (x *CreateLotRequest) GetToolCfg() *Lot_ToolConfig {
	if x != nil {
		return x.ToolCfg
	}
	return nil
}

func (x *CreateLotRequest) GetCommentReasons() []*CommentReasonClass {
	if x != nil {
		return x.CommentReasons
	}
	return nil
}

type CloneLotRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source lot uid
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// modified fields
	Updates *UpdateLotRequest `protobuf:"bytes,2,opt,name=updates,proto3" json:"updates,omitempty"`
	// if to copy executors
	CopyExecutors bool `protobuf:"varint,3,opt,name=copy_executors,json=copyExecutors,proto3" json:"copy_executors,omitempty"`
}

func (x *CloneLotRequest) Reset() {
	*x = CloneLotRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloneLotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloneLotRequest) ProtoMessage() {}

func (x *CloneLotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloneLotRequest.ProtoReflect.Descriptor instead.
func (*CloneLotRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{1}
}

func (x *CloneLotRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *CloneLotRequest) GetUpdates() *UpdateLotRequest {
	if x != nil {
		return x.Updates
	}
	return nil
}

func (x *CloneLotRequest) GetCopyExecutors() bool {
	if x != nil {
		return x.CopyExecutors
	}
	return false
}

type UpdateLotRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update contents
	Lot *CreateLotRequest `protobuf:"bytes,1,opt,name=lot,proto3" json:"lot,omitempty"`
	// name of fields to be updated
	Fields []string `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *UpdateLotRequest) Reset() {
	*x = UpdateLotRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLotRequest) ProtoMessage() {}

func (x *UpdateLotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLotRequest.ProtoReflect.Descriptor instead.
func (*UpdateLotRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateLotRequest) GetLot() *CreateLotRequest {
	if x != nil {
		return x.Lot
	}
	return nil
}

func (x *UpdateLotRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type DeleteLotRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *DeleteLotRequest) Reset() {
	*x = DeleteLotRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLotRequest) ProtoMessage() {}

func (x *DeleteLotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLotRequest.ProtoReflect.Descriptor instead.
func (*DeleteLotRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteLotRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetLotRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *GetLotRequest) Reset() {
	*x = GetLotRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotRequest) ProtoMessage() {}

func (x *GetLotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotRequest.ProtoReflect.Descriptor instead.
func (*GetLotRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{4}
}

func (x *GetLotRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type ListLotRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page   int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Pagesz int32 `protobuf:"varint,2,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// filter by orgnization
	OrgUid string `protobuf:"bytes,3,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// filter by creator
	CreatorUid string `protobuf:"bytes,4,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	// filter by name pattern
	NamePattern string `protobuf:"bytes,5,opt,name=name_pattern,json=namePattern,proto3" json:"name_pattern,omitempty"` // string project_uid = 3;
	// filter by lot state
	States []Lot_State_Enum `protobuf:"varint,6,rep,packed,name=states,proto3,enum=anno.v1.Lot_State_Enum" json:"states,omitempty"`
	// filter by lot type
	Type Lot_Type_Enum `protobuf:"varint,7,opt,name=type,proto3,enum=anno.v1.Lot_Type_Enum" json:"type,omitempty"`
	// filter by order
	OrderUid string `protobuf:"bytes,8,opt,name=order_uid,json=orderUid,proto3" json:"order_uid,omitempty"`
	// find by attached tags
	Tags []string `protobuf:"bytes,9,rep,name=tags,proto3" json:"tags,omitempty"`
	// include lot's orgnization in the reply
	WithOrg bool `protobuf:"varint,15,opt,name=with_org,json=withOrg,proto3" json:"with_org,omitempty"`
}

func (x *ListLotRequest) Reset() {
	*x = ListLotRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLotRequest) ProtoMessage() {}

func (x *ListLotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLotRequest.ProtoReflect.Descriptor instead.
func (*ListLotRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{5}
}

func (x *ListLotRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListLotRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListLotRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *ListLotRequest) GetCreatorUid() string {
	if x != nil {
		return x.CreatorUid
	}
	return ""
}

func (x *ListLotRequest) GetNamePattern() string {
	if x != nil {
		return x.NamePattern
	}
	return ""
}

func (x *ListLotRequest) GetStates() []Lot_State_Enum {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *ListLotRequest) GetType() Lot_Type_Enum {
	if x != nil {
		return x.Type
	}
	return Lot_Type_unspecified
}

func (x *ListLotRequest) GetOrderUid() string {
	if x != nil {
		return x.OrderUid
	}
	return ""
}

func (x *ListLotRequest) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ListLotRequest) GetWithOrg() bool {
	if x != nil {
		return x.WithOrg
	}
	return false
}

type ListLotReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total int32  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Lots  []*Lot `protobuf:"bytes,2,rep,name=lots,proto3" json:"lots,omitempty"`
	// the organization that the lot, at the corresponding position, belongs to.
	Orgs []*v1.BaseUser `protobuf:"bytes,3,rep,name=orgs,proto3" json:"orgs,omitempty"`
}

func (x *ListLotReply) Reset() {
	*x = ListLotReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLotReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLotReply) ProtoMessage() {}

func (x *ListLotReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLotReply.ProtoReflect.Descriptor instead.
func (*ListLotReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{6}
}

func (x *ListLotReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListLotReply) GetLots() []*Lot {
	if x != nil {
		return x.Lots
	}
	return nil
}

func (x *ListLotReply) GetOrgs() []*v1.BaseUser {
	if x != nil {
		return x.Orgs
	}
	return nil
}

type ListLotsByExecutorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page   int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Pagesz int32 `protobuf:"varint,2,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// list lots assigned to the user;
	// requestor should have manager role in the user's organization, if it is not oneself.
	// if omitted, default to the requestor.
	UserUid string `protobuf:"bytes,3,opt,name=user_uid,json=userUid,proto3" json:"user_uid,omitempty"`
	// list lots assigned to the team;
	// requestor should have manager role in this team.
	TeamUid string `protobuf:"bytes,4,opt,name=team_uid,json=teamUid,proto3" json:"team_uid,omitempty"`
	// list lots assigned to teams within this organization;
	// requestor should have IamGroup.list permission in this organization, e.g. a manager,.
	OrgUid string `protobuf:"bytes,5,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// filter by lot state
	States []Lot_State_Enum `protobuf:"varint,6,rep,packed,name=states,proto3,enum=anno.v1.Lot_State_Enum" json:"states,omitempty"`
	// filter by lot type
	Type Lot_Type_Enum `protobuf:"varint,7,opt,name=type,proto3,enum=anno.v1.Lot_Type_Enum" json:"type,omitempty"`
	// filter by name pattern
	NamePattern string `protobuf:"bytes,8,opt,name=name_pattern,json=namePattern,proto3" json:"name_pattern,omitempty"`
	// if to skip lots that a claim request will return no job for an executor; available only to executor query.
	Claimable bool `protobuf:"varint,9,opt,name=claimable,proto3" json:"claimable,omitempty"`
}

func (x *ListLotsByExecutorRequest) Reset() {
	*x = ListLotsByExecutorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLotsByExecutorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLotsByExecutorRequest) ProtoMessage() {}

func (x *ListLotsByExecutorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLotsByExecutorRequest.ProtoReflect.Descriptor instead.
func (*ListLotsByExecutorRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{7}
}

func (x *ListLotsByExecutorRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListLotsByExecutorRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListLotsByExecutorRequest) GetUserUid() string {
	if x != nil {
		return x.UserUid
	}
	return ""
}

func (x *ListLotsByExecutorRequest) GetTeamUid() string {
	if x != nil {
		return x.TeamUid
	}
	return ""
}

func (x *ListLotsByExecutorRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *ListLotsByExecutorRequest) GetStates() []Lot_State_Enum {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *ListLotsByExecutorRequest) GetType() Lot_Type_Enum {
	if x != nil {
		return x.Type
	}
	return Lot_Type_unspecified
}

func (x *ListLotsByExecutorRequest) GetNamePattern() string {
	if x != nil {
		return x.NamePattern
	}
	return ""
}

func (x *ListLotsByExecutorRequest) GetClaimable() bool {
	if x != nil {
		return x.Claimable
	}
	return false
}

type ListLotsByExecutorReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total int32  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Lots  []*Lot `protobuf:"bytes,2,rep,name=lots,proto3" json:"lots,omitempty"`
	// the extra info that the lot has
	Extras map[string]*ListLotsByExecutorReply_Extra `protobuf:"bytes,3,rep,name=extras,proto3" json:"extras,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ListLotsByExecutorReply) Reset() {
	*x = ListLotsByExecutorReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLotsByExecutorReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLotsByExecutorReply) ProtoMessage() {}

func (x *ListLotsByExecutorReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLotsByExecutorReply.ProtoReflect.Descriptor instead.
func (*ListLotsByExecutorReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{8}
}

func (x *ListLotsByExecutorReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListLotsByExecutorReply) GetLots() []*Lot {
	if x != nil {
		return x.Lots
	}
	return nil
}

func (x *ListLotsByExecutorReply) GetExtras() map[string]*ListLotsByExecutorReply_Extra {
	if x != nil {
		return x.Extras
	}
	return nil
}

type ListExecteamsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lot uid
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// if to include team info in the reply
	WithExecteams bool `protobuf:"varint,2,opt,name=with_execteams,json=withExecteams,proto3" json:"with_execteams,omitempty"`
	// if to include executors in the reply
	WithExecutors bool `protobuf:"varint,3,opt,name=with_executors,json=withExecutors,proto3" json:"with_executors,omitempty"`
}

func (x *ListExecteamsRequest) Reset() {
	*x = ListExecteamsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExecteamsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExecteamsRequest) ProtoMessage() {}

func (x *ListExecteamsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExecteamsRequest.ProtoReflect.Descriptor instead.
func (*ListExecteamsRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{9}
}

func (x *ListExecteamsRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ListExecteamsRequest) GetWithExecteams() bool {
	if x != nil {
		return x.WithExecteams
	}
	return false
}

func (x *ListExecteamsRequest) GetWithExecutors() bool {
	if x != nil {
		return x.WithExecutors
	}
	return false
}

type ListExecteamsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// execution teams and executors info for each phase
	Phases []*ListExecteamsReply_Phase `protobuf:"bytes,1,rep,name=phases,proto3" json:"phases,omitempty"`
}

func (x *ListExecteamsReply) Reset() {
	*x = ListExecteamsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExecteamsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExecteamsReply) ProtoMessage() {}

func (x *ListExecteamsReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExecteamsReply.ProtoReflect.Descriptor instead.
func (*ListExecteamsReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{10}
}

func (x *ListExecteamsReply) GetPhases() []*ListExecteamsReply_Phase {
	if x != nil {
		return x.Phases
	}
	return nil
}

type AssignExecteamRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lot uid
	Uid    string                         `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Phases []*AssignExecteamRequest_Phase `protobuf:"bytes,2,rep,name=phases,proto3" json:"phases,omitempty"`
}

func (x *AssignExecteamRequest) Reset() {
	*x = AssignExecteamRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssignExecteamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignExecteamRequest) ProtoMessage() {}

func (x *AssignExecteamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignExecteamRequest.ProtoReflect.Descriptor instead.
func (*AssignExecteamRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{11}
}

func (x *AssignExecteamRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *AssignExecteamRequest) GetPhases() []*AssignExecteamRequest_Phase {
	if x != nil {
		return x.Phases
	}
	return nil
}

type ManageExecutorsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lot uid
	Uid    string                          `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Phases []*ManageExecutorsRequest_Phase `protobuf:"bytes,2,rep,name=phases,proto3" json:"phases,omitempty"`
}

func (x *ManageExecutorsRequest) Reset() {
	*x = ManageExecutorsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManageExecutorsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManageExecutorsRequest) ProtoMessage() {}

func (x *ManageExecutorsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManageExecutorsRequest.ProtoReflect.Descriptor instead.
func (*ManageExecutorsRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{12}
}

func (x *ManageExecutorsRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ManageExecutorsRequest) GetPhases() []*ManageExecutorsRequest_Phase {
	if x != nil {
		return x.Phases
	}
	return nil
}

type ListExecutorsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lot uid
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// phase number, starts from 1
	Phase   int32  `protobuf:"varint,2,opt,name=phase,proto3" json:"phase,omitempty"`
	Subtype string `protobuf:"bytes,3,opt,name=subtype,proto3" json:"subtype,omitempty"`
	// mandatory in a multi-team configuration
	TeamUid string `protobuf:"bytes,4,opt,name=team_uid,json=teamUid,proto3" json:"team_uid,omitempty"`
	Page    int32  `protobuf:"varint,11,opt,name=page,proto3" json:"page,omitempty"`
	Pagesz  int32  `protobuf:"varint,12,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
}

func (x *ListExecutorsRequest) Reset() {
	*x = ListExecutorsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExecutorsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExecutorsRequest) ProtoMessage() {}

func (x *ListExecutorsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExecutorsRequest.ProtoReflect.Descriptor instead.
func (*ListExecutorsRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{13}
}

func (x *ListExecutorsRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ListExecutorsRequest) GetPhase() int32 {
	if x != nil {
		return x.Phase
	}
	return 0
}

func (x *ListExecutorsRequest) GetSubtype() string {
	if x != nil {
		return x.Subtype
	}
	return ""
}

func (x *ListExecutorsRequest) GetTeamUid() string {
	if x != nil {
		return x.TeamUid
	}
	return ""
}

func (x *ListExecutorsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListExecutorsRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

type ListExecutorsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// repeated string user_uids = 2;
	Users []*v1.BaseUser `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *ListExecutorsReply) Reset() {
	*x = ListExecutorsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExecutorsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExecutorsReply) ProtoMessage() {}

func (x *ListExecutorsReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExecutorsReply.ProtoReflect.Descriptor instead.
func (*ListExecutorsReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{14}
}

func (x *ListExecutorsReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListExecutorsReply) GetUsers() []*v1.BaseUser {
	if x != nil {
		return x.Users
	}
	return nil
}

type Lot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lot UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// lot name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// lot description
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	// lot state
	State Lot_State_Enum `protobuf:"varint,4,opt,name=state,proto3,enum=anno.v1.Lot_State_Enum" json:"state,omitempty"`
	// lot type
	Type Lot_Type_Enum `protobuf:"varint,5,opt,name=type,proto3,enum=anno.v1.Lot_Type_Enum" json:"type,omitempty"`
	// data type
	DataType Element_Type_Enum `protobuf:"varint,24,opt,name=data_type,json=dataType,proto3,enum=anno.v1.Element_Type_Enum" json:"data_type,omitempty"`
	// if to treat job as consecutive frames
	IsFrameSeries bool `protobuf:"varint,25,opt,name=is_frame_series,json=isFrameSeries,proto3" json:"is_frame_series,omitempty"`
	// larger number indicates higher priority
	Priority int32 `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`
	// whether to automatically start
	Autostart bool `protobuf:"varint,7,opt,name=autostart,proto3" json:"autostart,omitempty"`
	// maximum number of elements in a job.
	// if it is 0, a job is created per subfolder
	JobSize int32 `protobuf:"varint,8,opt,name=job_size,json=jobSize,proto3" json:"job_size,omitempty"`
	// lot ontologies
	Ontologies *Lotontologies `protobuf:"bytes,9,opt,name=ontologies,proto3" json:"ontologies,omitempty"`
	// execution phases; phase number starts from 1
	Phases []*Lotphase `protobuf:"bytes,10,rep,name=phases,proto3" json:"phases,omitempty"`
	// execution instructions in format of Markdown?
	Instruction string `protobuf:"bytes,11,opt,name=instruction,proto3" json:"instruction,omitempty"`
	// creator UID
	CreatorUid string `protobuf:"bytes,12,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	// number of elements in this lot
	DataSize int32 `protobuf:"varint,13,opt,name=data_size,json=dataSize,proto3" json:"data_size,omitempty"`
	// expected end time
	ExpEndTime *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=exp_end_time,json=expEndTime,proto3" json:"exp_end_time,omitempty"`
	UpdatedAt  *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	CreatedAt  *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// error info if state is ls_error
	Error *Error `protobuf:"bytes,17,opt,name=error,proto3" json:"error,omitempty"`
	// UID of the organization which the lot belongs to
	OrgUid string `protobuf:"bytes,18,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// UID of the data associated with the lot
	DataUid string `protobuf:"bytes,19,opt,name=data_uid,json=dataUid,proto3" json:"data_uid,omitempty"`
	// annotation result output config
	Out *OutConfig `protobuf:"bytes,20,opt,name=out,proto3" json:"out,omitempty"`
	// number of jobs in this lot
	JobCount int32 `protobuf:"varint,21,opt,name=job_count,json=jobCount,proto3" json:"job_count,omitempty"`
	// if jobs are created
	JobReady bool `protobuf:"varint,22,opt,name=job_ready,json=jobReady,proto3" json:"job_ready,omitempty"`
	// order UID
	OrderUid string `protobuf:"bytes,26,opt,name=order_uid,json=orderUid,proto3" json:"order_uid,omitempty"`
	// manually annotated objects count; only available after lot is finished
	InsCnt int32 `protobuf:"varint,27,opt,name=ins_cnt,json=insCnt,proto3" json:"ins_cnt,omitempty"`
	// annotated objects count (including interpolated ones); only available after lot is finished
	InsTotal int32 `protobuf:"varint,28,opt,name=ins_total,json=insTotal,proto3" json:"ins_total,omitempty"`
	// annotation tool configuration
	ToolCfg *Lot_ToolConfig `protobuf:"bytes,29,opt,name=tool_cfg,json=toolCfg,proto3" json:"tool_cfg,omitempty"`
	// anno result url
	AnnoResultUrl string `protobuf:"bytes,30,opt,name=anno_result_url,json=annoResultUrl,proto3" json:"anno_result_url,omitempty"`
	// indicate if the demander can export annos
	CanExportAnnos bool `protobuf:"varint,31,opt,name=can_export_annos,json=canExportAnnos,proto3" json:"can_export_annos,omitempty"`
	// allowed comment reason list
	CommentReasons []*CommentReasonClass `protobuf:"bytes,32,rep,name=comment_reasons,json=commentReasons,proto3" json:"comment_reasons,omitempty"`
	// tags attached to the lot
	Tags []string `protobuf:"bytes,33,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *Lot) Reset() {
	*x = Lot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lot) ProtoMessage() {}

func (x *Lot) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lot.ProtoReflect.Descriptor instead.
func (*Lot) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{15}
}

func (x *Lot) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Lot) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Lot) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Lot) GetState() Lot_State_Enum {
	if x != nil {
		return x.State
	}
	return Lot_State_unspecified
}

func (x *Lot) GetType() Lot_Type_Enum {
	if x != nil {
		return x.Type
	}
	return Lot_Type_unspecified
}

func (x *Lot) GetDataType() Element_Type_Enum {
	if x != nil {
		return x.DataType
	}
	return Element_Type_unspecified
}

func (x *Lot) GetIsFrameSeries() bool {
	if x != nil {
		return x.IsFrameSeries
	}
	return false
}

func (x *Lot) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *Lot) GetAutostart() bool {
	if x != nil {
		return x.Autostart
	}
	return false
}

func (x *Lot) GetJobSize() int32 {
	if x != nil {
		return x.JobSize
	}
	return 0
}

func (x *Lot) GetOntologies() *Lotontologies {
	if x != nil {
		return x.Ontologies
	}
	return nil
}

func (x *Lot) GetPhases() []*Lotphase {
	if x != nil {
		return x.Phases
	}
	return nil
}

func (x *Lot) GetInstruction() string {
	if x != nil {
		return x.Instruction
	}
	return ""
}

func (x *Lot) GetCreatorUid() string {
	if x != nil {
		return x.CreatorUid
	}
	return ""
}

func (x *Lot) GetDataSize() int32 {
	if x != nil {
		return x.DataSize
	}
	return 0
}

func (x *Lot) GetExpEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpEndTime
	}
	return nil
}

func (x *Lot) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Lot) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Lot) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *Lot) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *Lot) GetDataUid() string {
	if x != nil {
		return x.DataUid
	}
	return ""
}

func (x *Lot) GetOut() *OutConfig {
	if x != nil {
		return x.Out
	}
	return nil
}

func (x *Lot) GetJobCount() int32 {
	if x != nil {
		return x.JobCount
	}
	return 0
}

func (x *Lot) GetJobReady() bool {
	if x != nil {
		return x.JobReady
	}
	return false
}

func (x *Lot) GetOrderUid() string {
	if x != nil {
		return x.OrderUid
	}
	return ""
}

func (x *Lot) GetInsCnt() int32 {
	if x != nil {
		return x.InsCnt
	}
	return 0
}

func (x *Lot) GetInsTotal() int32 {
	if x != nil {
		return x.InsTotal
	}
	return 0
}

func (x *Lot) GetToolCfg() *Lot_ToolConfig {
	if x != nil {
		return x.ToolCfg
	}
	return nil
}

func (x *Lot) GetAnnoResultUrl() string {
	if x != nil {
		return x.AnnoResultUrl
	}
	return ""
}

func (x *Lot) GetCanExportAnnos() bool {
	if x != nil {
		return x.CanExportAnnos
	}
	return false
}

func (x *Lot) GetCommentReasons() []*CommentReasonClass {
	if x != nil {
		return x.CommentReasons
	}
	return nil
}

func (x *Lot) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

// contains number of jobs in various phases
type GetLotSummaryReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of jobs
	TotalJobs int32 `protobuf:"varint,1,opt,name=total_jobs,json=totalJobs,proto3" json:"total_jobs,omitempty"`
	// jobs at each phase 1~n, the last element means the finished jobs.
	// if the lot has n phases, the length of jobs_at_phase is n+1.
	JobsAtPhase []int32 `protobuf:"varint,2,rep,packed,name=jobs_at_phase,json=jobsAtPhase,proto3" json:"jobs_at_phase,omitempty"`
}

func (x *GetLotSummaryReply) Reset() {
	*x = GetLotSummaryReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotSummaryReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotSummaryReply) ProtoMessage() {}

func (x *GetLotSummaryReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotSummaryReply.ProtoReflect.Descriptor instead.
func (*GetLotSummaryReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{16}
}

func (x *GetLotSummaryReply) GetTotalJobs() int32 {
	if x != nil {
		return x.TotalJobs
	}
	return 0
}

func (x *GetLotSummaryReply) GetJobsAtPhase() []int32 {
	if x != nil {
		return x.JobsAtPhase
	}
	return nil
}

type GetLotAnnosReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Annotations []*ElementAnno `protobuf:"bytes,1,rep,name=annotations,proto3" json:"annotations,omitempty"`
	// number of objects annotated in the job
	InsCnt int32 `protobuf:"varint,2,opt,name=ins_cnt,json=insCnt,proto3" json:"ins_cnt,omitempty"`
}

func (x *GetLotAnnosReply) Reset() {
	*x = GetLotAnnosReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotAnnosReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotAnnosReply) ProtoMessage() {}

func (x *GetLotAnnosReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotAnnosReply.ProtoReflect.Descriptor instead.
func (*GetLotAnnosReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{17}
}

func (x *GetLotAnnosReply) GetAnnotations() []*ElementAnno {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *GetLotAnnosReply) GetInsCnt() int32 {
	if x != nil {
		return x.InsCnt
	}
	return 0
}

type GetVisibleLotsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExecutorUid string           `protobuf:"bytes,1,opt,name=executor_uid,json=executorUid,proto3" json:"executor_uid,omitempty"`
	States      []Lot_State_Enum `protobuf:"varint,2,rep,packed,name=states,proto3,enum=anno.v1.Lot_State_Enum" json:"states,omitempty"`
	CountOnly   bool             `protobuf:"varint,3,opt,name=count_only,json=countOnly,proto3" json:"count_only,omitempty"`
	// if to skip lots that a claim request will return no job for an executor; available only to executor query.
	Claimable bool `protobuf:"varint,4,opt,name=claimable,proto3" json:"claimable,omitempty"`
}

func (x *GetVisibleLotsRequest) Reset() {
	*x = GetVisibleLotsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVisibleLotsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVisibleLotsRequest) ProtoMessage() {}

func (x *GetVisibleLotsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVisibleLotsRequest.ProtoReflect.Descriptor instead.
func (*GetVisibleLotsRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{18}
}

func (x *GetVisibleLotsRequest) GetExecutorUid() string {
	if x != nil {
		return x.ExecutorUid
	}
	return ""
}

func (x *GetVisibleLotsRequest) GetStates() []Lot_State_Enum {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *GetVisibleLotsRequest) GetCountOnly() bool {
	if x != nil {
		return x.CountOnly
	}
	return false
}

func (x *GetVisibleLotsRequest) GetClaimable() bool {
	if x != nil {
		return x.Claimable
	}
	return false
}

type GetVisibleLotsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotUids  []string         `protobuf:"bytes,1,rep,name=lot_uids,json=lotUids,proto3" json:"lot_uids,omitempty"`
	LotCount map[string]int32 `protobuf:"bytes,2,rep,name=lotCount,proto3" json:"lotCount,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // valid if count_only = true. key is LotState, value is count.
}

func (x *GetVisibleLotsReply) Reset() {
	*x = GetVisibleLotsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVisibleLotsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVisibleLotsReply) ProtoMessage() {}

func (x *GetVisibleLotsReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVisibleLotsReply.ProtoReflect.Descriptor instead.
func (*GetVisibleLotsReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{19}
}

func (x *GetVisibleLotsReply) GetLotUids() []string {
	if x != nil {
		return x.LotUids
	}
	return nil
}

func (x *GetVisibleLotsReply) GetLotCount() map[string]int32 {
	if x != nil {
		return x.LotCount
	}
	return nil
}

type ExportLotAnnosRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lot UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// option
	Option ExportOrderAnnosRequest_Option_Enum `protobuf:"varint,2,opt,name=option,proto3,enum=anno.v1.ExportOrderAnnosRequest_Option_Enum" json:"option,omitempty"`
	Phases []int32                             `protobuf:"varint,3,rep,packed,name=phases,proto3" json:"phases,omitempty"`
}

func (x *ExportLotAnnosRequest) Reset() {
	*x = ExportLotAnnosRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportLotAnnosRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportLotAnnosRequest) ProtoMessage() {}

func (x *ExportLotAnnosRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportLotAnnosRequest.ProtoReflect.Descriptor instead.
func (*ExportLotAnnosRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{20}
}

func (x *ExportLotAnnosRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ExportLotAnnosRequest) GetOption() ExportOrderAnnosRequest_Option_Enum {
	if x != nil {
		return x.Option
	}
	return ExportOrderAnnosRequest_Option_unspecified
}

func (x *ExportLotAnnosRequest) GetPhases() []int32 {
	if x != nil {
		return x.Phases
	}
	return nil
}

type SetLotAnnoResultRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lot UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// URL to the result
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *SetLotAnnoResultRequest) Reset() {
	*x = SetLotAnnoResultRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetLotAnnoResultRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetLotAnnoResultRequest) ProtoMessage() {}

func (x *SetLotAnnoResultRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetLotAnnoResultRequest.ProtoReflect.Descriptor instead.
func (*SetLotAnnoResultRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{21}
}

func (x *SetLotAnnoResultRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *SetLotAnnoResultRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type AllowDownloadAnnosRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lot UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// whether to allow demanders to download order annos: if false, means we have allowed it before but we want to disallow it now
	Allow bool `protobuf:"varint,2,opt,name=allow,proto3" json:"allow,omitempty"`
}

func (x *AllowDownloadAnnosRequest) Reset() {
	*x = AllowDownloadAnnosRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllowDownloadAnnosRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllowDownloadAnnosRequest) ProtoMessage() {}

func (x *AllowDownloadAnnosRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllowDownloadAnnosRequest.ProtoReflect.Descriptor instead.
func (*AllowDownloadAnnosRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{22}
}

func (x *AllowDownloadAnnosRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *AllowDownloadAnnosRequest) GetAllow() bool {
	if x != nil {
		return x.Allow
	}
	return false
}

type JobCountByLotidsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
}

func (x *JobCountByLotidsRequest) Reset() {
	*x = JobCountByLotidsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobCountByLotidsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobCountByLotidsRequest) ProtoMessage() {}

func (x *JobCountByLotidsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobCountByLotidsRequest.ProtoReflect.Descriptor instead.
func (*JobCountByLotidsRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{23}
}

func (x *JobCountByLotidsRequest) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type JobCountByLotidsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lots []*JobCountByLotidsReply_LotInfo `protobuf:"bytes,1,rep,name=lots,proto3" json:"lots,omitempty"`
}

func (x *JobCountByLotidsReply) Reset() {
	*x = JobCountByLotidsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobCountByLotidsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobCountByLotidsReply) ProtoMessage() {}

func (x *JobCountByLotidsReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobCountByLotidsReply.ProtoReflect.Descriptor instead.
func (*JobCountByLotidsReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{24}
}

func (x *JobCountByLotidsReply) GetLots() []*JobCountByLotidsReply_LotInfo {
	if x != nil {
		return x.Lots
	}
	return nil
}

type ListLotsByExecutorReply_Extra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HasRejectedJobs bool `protobuf:"varint,1,opt,name=has_rejected_jobs,json=hasRejectedJobs,proto3" json:"has_rejected_jobs,omitempty"`
}

func (x *ListLotsByExecutorReply_Extra) Reset() {
	*x = ListLotsByExecutorReply_Extra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLotsByExecutorReply_Extra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLotsByExecutorReply_Extra) ProtoMessage() {}

func (x *ListLotsByExecutorReply_Extra) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLotsByExecutorReply_Extra.ProtoReflect.Descriptor instead.
func (*ListLotsByExecutorReply_Extra) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ListLotsByExecutorReply_Extra) GetHasRejectedJobs() bool {
	if x != nil {
		return x.HasRejectedJobs
	}
	return false
}

type ListExecteamsReply_Execteam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// execution team info
	Team *v1.BaseUser `protobuf:"bytes,1,opt,name=team,proto3" json:"team,omitempty"`
	// executors info
	Executors []*v1.BaseUser  `protobuf:"bytes,2,rep,name=executors,proto3" json:"executors,omitempty"`
	Quota     *Lotphase_Quota `protobuf:"bytes,3,opt,name=quota,proto3" json:"quota,omitempty"`
}

func (x *ListExecteamsReply_Execteam) Reset() {
	*x = ListExecteamsReply_Execteam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExecteamsReply_Execteam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExecteamsReply_Execteam) ProtoMessage() {}

func (x *ListExecteamsReply_Execteam) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExecteamsReply_Execteam.ProtoReflect.Descriptor instead.
func (*ListExecteamsReply_Execteam) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{10, 0}
}

func (x *ListExecteamsReply_Execteam) GetTeam() *v1.BaseUser {
	if x != nil {
		return x.Team
	}
	return nil
}

func (x *ListExecteamsReply_Execteam) GetExecutors() []*v1.BaseUser {
	if x != nil {
		return x.Executors
	}
	return nil
}

func (x *ListExecteamsReply_Execteam) GetQuota() *Lotphase_Quota {
	if x != nil {
		return x.Quota
	}
	return nil
}

type ListExecteamsReply_Phase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// execution teams and executors info in this phase
	Teams []*ListExecteamsReply_Execteam `protobuf:"bytes,1,rep,name=teams,proto3" json:"teams,omitempty"`
}

func (x *ListExecteamsReply_Phase) Reset() {
	*x = ListExecteamsReply_Phase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExecteamsReply_Phase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExecteamsReply_Phase) ProtoMessage() {}

func (x *ListExecteamsReply_Phase) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExecteamsReply_Phase.ProtoReflect.Descriptor instead.
func (*ListExecteamsReply_Phase) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{10, 1}
}

func (x *ListExecteamsReply_Phase) GetTeams() []*ListExecteamsReply_Execteam {
	if x != nil {
		return x.Teams
	}
	return nil
}

type AssignExecteamRequest_Phase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// phase number, starts from 1
	Phase int32 `protobuf:"varint,1,opt,name=phase,proto3" json:"phase,omitempty"`
	// support multiple teams in one phase
	Execteams []*Lotphase_Execteam `protobuf:"bytes,3,rep,name=execteams,proto3" json:"execteams,omitempty"`
}

func (x *AssignExecteamRequest_Phase) Reset() {
	*x = AssignExecteamRequest_Phase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssignExecteamRequest_Phase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignExecteamRequest_Phase) ProtoMessage() {}

func (x *AssignExecteamRequest_Phase) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignExecteamRequest_Phase.ProtoReflect.Descriptor instead.
func (*AssignExecteamRequest_Phase) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{11, 0}
}

func (x *AssignExecteamRequest_Phase) GetPhase() int32 {
	if x != nil {
		return x.Phase
	}
	return 0
}

func (x *AssignExecteamRequest_Phase) GetExecteams() []*Lotphase_Execteam {
	if x != nil {
		return x.Execteams
	}
	return nil
}

type ManageExecutorsRequest_Phase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// phase number, starts from 1
	Phase int32 `protobuf:"varint,1,opt,name=phase,proto3" json:"phase,omitempty"`
	// the executors' team
	TeamUid string `protobuf:"bytes,3,opt,name=team_uid,json=teamUid,proto3" json:"team_uid,omitempty"`
	// user uids to add
	Add []string `protobuf:"bytes,4,rep,name=add,proto3" json:"add,omitempty"`
	// user uids to remove
	Delete []string `protobuf:"bytes,5,rep,name=delete,proto3" json:"delete,omitempty"` // string subtype = 2;
}

func (x *ManageExecutorsRequest_Phase) Reset() {
	*x = ManageExecutorsRequest_Phase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManageExecutorsRequest_Phase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManageExecutorsRequest_Phase) ProtoMessage() {}

func (x *ManageExecutorsRequest_Phase) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManageExecutorsRequest_Phase.ProtoReflect.Descriptor instead.
func (*ManageExecutorsRequest_Phase) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{12, 0}
}

func (x *ManageExecutorsRequest_Phase) GetPhase() int32 {
	if x != nil {
		return x.Phase
	}
	return 0
}

func (x *ManageExecutorsRequest_Phase) GetTeamUid() string {
	if x != nil {
		return x.TeamUid
	}
	return ""
}

func (x *ManageExecutorsRequest_Phase) GetAdd() []string {
	if x != nil {
		return x.Add
	}
	return nil
}

func (x *ManageExecutorsRequest_Phase) GetDelete() []string {
	if x != nil {
		return x.Delete
	}
	return nil
}

type Lot_Type struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Lot_Type) Reset() {
	*x = Lot_Type{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lot_Type) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lot_Type) ProtoMessage() {}

func (x *Lot_Type) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lot_Type.ProtoReflect.Descriptor instead.
func (*Lot_Type) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{15, 0}
}

type Lot_State struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Lot_State) Reset() {
	*x = Lot_State{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lot_State) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lot_State) ProtoMessage() {}

func (x *Lot_State) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lot_State.ProtoReflect.Descriptor instead.
func (*Lot_State) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{15, 1}
}

type Lot_Range struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// shape type
	Shape Lot_Range_Shape_Enum `protobuf:"varint,1,opt,name=shape,proto3,enum=anno.v1.Lot_Range_Shape_Enum" json:"shape,omitempty"`
	// the params of the shape; specific to shape type.
	// circle: [radius], the unit is meter (in pointclouds), or pixel (in images);
	// rectangle: [width, height], the unit is meter (in pointclouds), or pixel (in images);
	Data []float32 `protobuf:"fixed32,2,rep,packed,name=data,proto3" json:"data,omitempty"`
	// range on the z-axis, the unit is meter
	Zrange *types.Range `protobuf:"bytes,3,opt,name=zrange,proto3" json:"zrange,omitempty"`
	// which rawdata types the range is applicable to
	RawdataTypes []Rawdata_Type_Enum `protobuf:"varint,4,rep,packed,name=rawdata_types,json=rawdataTypes,proto3,enum=anno.v1.Rawdata_Type_Enum" json:"rawdata_types,omitempty"`
}

func (x *Lot_Range) Reset() {
	*x = Lot_Range{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lot_Range) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lot_Range) ProtoMessage() {}

func (x *Lot_Range) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lot_Range.ProtoReflect.Descriptor instead.
func (*Lot_Range) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{15, 2}
}

func (x *Lot_Range) GetShape() Lot_Range_Shape_Enum {
	if x != nil {
		return x.Shape
	}
	return Lot_Range_Shape_unspecified
}

func (x *Lot_Range) GetData() []float32 {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Lot_Range) GetZrange() *types.Range {
	if x != nil {
		return x.Zrange
	}
	return nil
}

func (x *Lot_Range) GetRawdataTypes() []Rawdata_Type_Enum {
	if x != nil {
		return x.RawdataTypes
	}
	return nil
}

type Lot_ToolConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// work range indicators
	Ranges []*Lot_Range `protobuf:"bytes,1,rep,name=ranges,proto3" json:"ranges,omitempty"`
	// if projected objects are editable in a 23D fusion task
	ProjectedEditable bool `protobuf:"varint,2,opt,name=projected_editable,json=projectedEditable,proto3" json:"projected_editable,omitempty"`
	// if to regenerate 2D projected objects according to 3D objects
	RedoProjection bool `protobuf:"varint,3,opt,name=redo_projection,json=redoProjection,proto3" json:"redo_projection,omitempty"`
	// 定义各种类型和大小的量尺，方便比对标志物的尺寸是否合规
	Rulers []*Lot_ToolConfig_Ruler `protobuf:"bytes,4,rep,name=rulers,proto3" json:"rulers,omitempty"`
	// whether to save 2D projected objects
	SaveProjected bool `protobuf:"varint,5,opt,name=save_projected,json=saveProjected,proto3" json:"save_projected,omitempty"`
	// the order of images in a single frame, specified with images' rawdata.meta.image.camera
	ImageOrder             []string                 `protobuf:"bytes,6,rep,name=image_order,json=imageOrder,proto3" json:"image_order,omitempty"`
	Segmentation_3DEnabled bool                     `protobuf:"varint,7,opt,name=segmentation_3d_enabled,json=segmentation3dEnabled,proto3" json:"segmentation_3d_enabled,omitempty"`
	PreBox                 []*Lot_ToolConfig_PreBox `protobuf:"bytes,8,rep,name=pre_box,json=preBox,proto3" json:"pre_box,omitempty"`
}

func (x *Lot_ToolConfig) Reset() {
	*x = Lot_ToolConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lot_ToolConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lot_ToolConfig) ProtoMessage() {}

func (x *Lot_ToolConfig) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lot_ToolConfig.ProtoReflect.Descriptor instead.
func (*Lot_ToolConfig) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{15, 3}
}

func (x *Lot_ToolConfig) GetRanges() []*Lot_Range {
	if x != nil {
		return x.Ranges
	}
	return nil
}

func (x *Lot_ToolConfig) GetProjectedEditable() bool {
	if x != nil {
		return x.ProjectedEditable
	}
	return false
}

func (x *Lot_ToolConfig) GetRedoProjection() bool {
	if x != nil {
		return x.RedoProjection
	}
	return false
}

func (x *Lot_ToolConfig) GetRulers() []*Lot_ToolConfig_Ruler {
	if x != nil {
		return x.Rulers
	}
	return nil
}

func (x *Lot_ToolConfig) GetSaveProjected() bool {
	if x != nil {
		return x.SaveProjected
	}
	return false
}

func (x *Lot_ToolConfig) GetImageOrder() []string {
	if x != nil {
		return x.ImageOrder
	}
	return nil
}

func (x *Lot_ToolConfig) GetSegmentation_3DEnabled() bool {
	if x != nil {
		return x.Segmentation_3DEnabled
	}
	return false
}

func (x *Lot_ToolConfig) GetPreBox() []*Lot_ToolConfig_PreBox {
	if x != nil {
		return x.PreBox
	}
	return nil
}

type Lot_Range_Shape struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Lot_Range_Shape) Reset() {
	*x = Lot_Range_Shape{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lot_Range_Shape) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lot_Range_Shape) ProtoMessage() {}

func (x *Lot_Range_Shape) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lot_Range_Shape.ProtoReflect.Descriptor instead.
func (*Lot_Range_Shape) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{15, 2, 0}
}

type Lot_ToolConfig_Ruler struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// a friendly name for the ruler
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// shape of the ruler
	Type Lot_Range_Shape_Enum `protobuf:"varint,2,opt,name=type,proto3,enum=anno.v1.Lot_Range_Shape_Enum" json:"type,omitempty"`
	// the params of the shape; specific to shape type.
	// rectangle: [width, height], the unit is pixel;
	// circle: [radius], the unit is pixel;
	Data []float64 `protobuf:"fixed64,3,rep,packed,name=data,proto3" json:"data,omitempty"`
	// which rawdata types the ruler is applicable to
	RawdataTypes []Rawdata_Type_Enum `protobuf:"varint,4,rep,packed,name=rawdata_types,json=rawdataTypes,proto3,enum=anno.v1.Rawdata_Type_Enum" json:"rawdata_types,omitempty"`
}

func (x *Lot_ToolConfig_Ruler) Reset() {
	*x = Lot_ToolConfig_Ruler{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lot_ToolConfig_Ruler) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lot_ToolConfig_Ruler) ProtoMessage() {}

func (x *Lot_ToolConfig_Ruler) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lot_ToolConfig_Ruler.ProtoReflect.Descriptor instead.
func (*Lot_ToolConfig_Ruler) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{15, 3, 0}
}

func (x *Lot_ToolConfig_Ruler) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Lot_ToolConfig_Ruler) GetType() Lot_Range_Shape_Enum {
	if x != nil {
		return x.Type
	}
	return Lot_Range_Shape_unspecified
}

func (x *Lot_ToolConfig_Ruler) GetData() []float64 {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Lot_ToolConfig_Ruler) GetRawdataTypes() []Rawdata_Type_Enum {
	if x != nil {
		return x.RawdataTypes
	}
	return nil
}

type Lot_ToolConfig_PreBox struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Length float32 `protobuf:"fixed32,2,opt,name=length,proto3" json:"length,omitempty"`
	Width  float32 `protobuf:"fixed32,3,opt,name=width,proto3" json:"width,omitempty"`
	Height float32 `protobuf:"fixed32,4,opt,name=height,proto3" json:"height,omitempty"`
}

func (x *Lot_ToolConfig_PreBox) Reset() {
	*x = Lot_ToolConfig_PreBox{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lot_ToolConfig_PreBox) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lot_ToolConfig_PreBox) ProtoMessage() {}

func (x *Lot_ToolConfig_PreBox) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lot_ToolConfig_PreBox.ProtoReflect.Descriptor instead.
func (*Lot_ToolConfig_PreBox) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{15, 3, 1}
}

func (x *Lot_ToolConfig_PreBox) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Lot_ToolConfig_PreBox) GetLength() float32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *Lot_ToolConfig_PreBox) GetWidth() float32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *Lot_ToolConfig_PreBox) GetHeight() float32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type JobCountByLotidsReply_PhaseCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Phase int32 `protobuf:"varint,1,opt,name=phase,proto3" json:"phase,omitempty"`
	Count int32 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *JobCountByLotidsReply_PhaseCount) Reset() {
	*x = JobCountByLotidsReply_PhaseCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobCountByLotidsReply_PhaseCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobCountByLotidsReply_PhaseCount) ProtoMessage() {}

func (x *JobCountByLotidsReply_PhaseCount) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobCountByLotidsReply_PhaseCount.ProtoReflect.Descriptor instead.
func (*JobCountByLotidsReply_PhaseCount) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{24, 0}
}

func (x *JobCountByLotidsReply_PhaseCount) GetPhase() int32 {
	if x != nil {
		return x.Phase
	}
	return 0
}

func (x *JobCountByLotidsReply_PhaseCount) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type JobCountByLotidsReply_LotInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotId string                              `protobuf:"bytes,1,opt,name=lot_id,json=lotId,proto3" json:"lot_id,omitempty"`
	Count []*JobCountByLotidsReply_PhaseCount `protobuf:"bytes,2,rep,name=count,proto3" json:"count,omitempty"`
}

func (x *JobCountByLotidsReply_LotInfo) Reset() {
	*x = JobCountByLotidsReply_LotInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_lot_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobCountByLotidsReply_LotInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobCountByLotidsReply_LotInfo) ProtoMessage() {}

func (x *JobCountByLotidsReply_LotInfo) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_lot_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobCountByLotidsReply_LotInfo.ProtoReflect.Descriptor instead.
func (*JobCountByLotidsReply_LotInfo) Descriptor() ([]byte, []int) {
	return file_anno_v1_lot_proto_rawDescGZIP(), []int{24, 1}
}

func (x *JobCountByLotidsReply_LotInfo) GetLotId() string {
	if x != nil {
		return x.LotId
	}
	return ""
}

func (x *JobCountByLotidsReply_LotInfo) GetCount() []*JobCountByLotidsReply_PhaseCount {
	if x != nil {
		return x.Count
	}
	return nil
}

var File_anno_v1_lot_proto protoreflect.FileDescriptor

var file_anno_v1_lot_proto_rawDesc = []byte{
	0x0a, 0x11, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76,
	0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x11, 0x69, 0x61, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x61, 0x6e, 0x6e, 0x6f, 0x2f,
	0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12,
	0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x16, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6c, 0x65, 0x6d,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x6e, 0x6e, 0x6f,
	0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x6f, 0x74, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f,
	0x72, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2f, 0x74, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xda, 0x05, 0x0a,
	0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1f, 0x0a, 0x0b, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x69, 0x64, 0x12, 0x34, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e,
	0x75, 0x6d, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x1c,
	0x0a, 0x09, 0x61, 0x75, 0x74, 0x6f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x61, 0x75, 0x74, 0x6f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x6a, 0x6f, 0x62, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x6a, 0x6f, 0x62, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x36, 0x0a, 0x0a, 0x6f, 0x6e, 0x74, 0x6f, 0x6c,
	0x6f, 0x67, 0x69, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67,
	0x69, 0x65, 0x73, 0x52, 0x0a, 0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x12,
	0x29, 0x0a, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x70, 0x68, 0x61,
	0x73, 0x65, 0x52, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07,
	0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x72, 0x67, 0x55, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x69,
	0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x55, 0x69, 0x64,
	0x12, 0x24, 0x0a, 0x03, 0x6f, 0x75, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x03, 0x6f, 0x75, 0x74, 0x12, 0x3c, 0x0a, 0x0c, 0x65, 0x78, 0x70, 0x5f, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x45, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65,
	0x5f, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69,
	0x73, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x69, 0x64, 0x12, 0x32, 0x0a, 0x08, 0x74, 0x6f, 0x6f,
	0x6c, 0x5f, 0x63, 0x66, 0x67, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x07, 0x74, 0x6f, 0x6f, 0x6c, 0x43, 0x66, 0x67, 0x12, 0x44, 0x0a,
	0x0f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73,
	0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x73, 0x4a, 0x04, 0x08, 0x11, 0x10, 0x12, 0x22, 0x96, 0x01, 0x0a, 0x0f, 0x43, 0x6c,
	0x6f, 0x6e, 0x65, 0x4c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72,
	0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d,
	0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x63,
	0x6f, 0x70, 0x79, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x63, 0x6f, 0x70, 0x79, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f,
	0x72, 0x73, 0x22, 0x6b, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x03, 0x6c, 0x6f, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x03,
	0x6c, 0x6f, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x3a, 0x12, 0xba, 0x47, 0x0f,
	0xba, 0x01, 0x03, 0x6c, 0x6f, 0x74, 0xba, 0x01, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22,
	0x3b, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x38, 0x0a, 0x0d,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72,
	0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d,
	0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x98, 0x03, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x4c,
	0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a,
	0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a,
	0x12, 0x30, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x72, 0x12, 0x32, 0x10, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x7c, 0x5e, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x55,
	0x69, 0x64, 0x12, 0x38, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x72, 0x12, 0x32, 0x10,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x7c, 0x5e,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12,
	0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x17, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73,
	0x12, 0x2a, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x2e, 0x54, 0x79, 0x70,
	0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x09,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x17, 0xfa, 0x42, 0x14, 0x72, 0x12, 0x32, 0x10, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x7c, 0x5e, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x55,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x6f,
	0x72, 0x67, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x77, 0x69, 0x74, 0x68, 0x4f, 0x72,
	0x67, 0x22, 0x78, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x20, 0x0a, 0x04, 0x6c, 0x6f, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x6f, 0x74, 0x52, 0x04, 0x6c, 0x6f, 0x74, 0x73, 0x12, 0x24, 0x0a, 0x04, 0x6f, 0x72, 0x67,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x61, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x6f, 0x72, 0x67, 0x73, 0x3a,
	0x0a, 0xba, 0x47, 0x07, 0xba, 0x01, 0x04, 0x6c, 0x6f, 0x74, 0x73, 0x22, 0x8a, 0x03, 0x0a, 0x19,
	0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x74, 0x73, 0x42, 0x79, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a,
	0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a,
	0x12, 0x32, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x72, 0x12, 0x32, 0x10, 0x5e, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x7c, 0x5e, 0x52, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x55, 0x69, 0x64, 0x12, 0x32, 0x0a, 0x08, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x75, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x72, 0x12, 0x32, 0x10, 0x5e,
	0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x7c, 0x5e, 0x52,
	0x07, 0x74, 0x65, 0x61, 0x6d, 0x55, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f,
	0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x72, 0x12,
	0x32, 0x10, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24,
	0x7c, 0x5e, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75,
	0x6d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f,
	0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e,
	0x61, 0x6d, 0x65, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c,
	0x61, 0x69, 0x6d, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x63,
	0x6c, 0x61, 0x69, 0x6d, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xbb, 0x02, 0x0a, 0x17, 0x4c, 0x69, 0x73,
	0x74, 0x4c, 0x6f, 0x74, 0x73, 0x42, 0x79, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x20, 0x0a, 0x04, 0x6c, 0x6f,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x52, 0x04, 0x6c, 0x6f, 0x74, 0x73, 0x12, 0x44, 0x0a, 0x06,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x74, 0x73, 0x42,
	0x79, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x73, 0x1a, 0x33, 0x0a, 0x05, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x2a, 0x0a, 0x11, 0x68,
	0x61, 0x73, 0x5f, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6a, 0x6f, 0x62, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x68, 0x61, 0x73, 0x52, 0x65, 0x6a, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x73, 0x1a, 0x61, 0x0a, 0x0b, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x74, 0x73, 0x42, 0x79, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x3a, 0x0a, 0xba, 0x47, 0x07, 0xba,
	0x01, 0x04, 0x6c, 0x6f, 0x74, 0x73, 0x22, 0x8d, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x45,
	0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42,
	0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31,
	0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x77, 0x69, 0x74, 0x68,
	0x5f, 0x65, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0d, 0x77, 0x69, 0x74, 0x68, 0x45, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x12,
	0x25, 0x0a, 0x0e, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x77, 0x69, 0x74, 0x68, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x22, 0xd9, 0x02, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x45,
	0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x39, 0x0a,
	0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x65, 0x63,
	0x74, 0x65, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x50, 0x68, 0x61, 0x73, 0x65,
	0x52, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x1a, 0xa7, 0x01, 0x0a, 0x08, 0x45, 0x78, 0x65,
	0x63, 0x74, 0x65, 0x61, 0x6d, 0x12, 0x24, 0x0a, 0x04, 0x74, 0x65, 0x61, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x73,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x74, 0x65, 0x61, 0x6d, 0x12, 0x2e, 0x0a, 0x09, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x2d, 0x0a, 0x05, 0x71,
	0x75, 0x6f, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x70, 0x68, 0x61, 0x73, 0x65, 0x2e, 0x51, 0x75,
	0x6f, 0x74, 0x61, 0x52, 0x05, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x3a, 0x16, 0xba, 0x47, 0x13, 0xba,
	0x01, 0x04, 0x74, 0x65, 0x61, 0x6d, 0xba, 0x01, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f,
	0x72, 0x73, 0x1a, 0x50, 0x0a, 0x05, 0x50, 0x68, 0x61, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x05, 0x74,
	0x65, 0x61, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61,
	0x6d, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d,
	0x52, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x3a, 0x0b, 0xba, 0x47, 0x08, 0xba, 0x01, 0x05, 0x74,
	0x65, 0x61, 0x6d, 0x73, 0x3a, 0x0c, 0xba, 0x47, 0x09, 0xba, 0x01, 0x06, 0x70, 0x68, 0x61, 0x73,
	0x65, 0x73, 0x22, 0x8a, 0x02, 0x0a, 0x15, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x45, 0x78, 0x65,
	0x63, 0x74, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10,
	0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24,
	0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x3c, 0x0a, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x68, 0x61, 0x73, 0x65, 0x52, 0x06, 0x70, 0x68, 0x61,
	0x73, 0x65, 0x73, 0x1a, 0x76, 0x0a, 0x05, 0x50, 0x68, 0x61, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x68, 0x61,
	0x73, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x65, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x6f, 0x74, 0x70, 0x68, 0x61, 0x73, 0x65, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61,
	0x6d, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x3a, 0x17, 0xba, 0x47,
	0x14, 0xba, 0x01, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0xba, 0x01, 0x09, 0x65, 0x78, 0x65, 0x63,
	0x74, 0x65, 0x61, 0x6d, 0x73, 0x4a, 0x04, 0x08, 0x02, 0x10, 0x03, 0x3a, 0x12, 0xba, 0x47, 0x0f,
	0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x22,
	0x90, 0x02, 0x0a, 0x16, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x6f, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x12, 0x3d, 0x0a, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x68, 0x61, 0x73, 0x65, 0x52, 0x06, 0x70, 0x68, 0x61, 0x73,
	0x65, 0x73, 0x1a, 0x7a, 0x0a, 0x05, 0x50, 0x68, 0x61, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70,
	0x68, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x68, 0x61, 0x73,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x55, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03,
	0x61, 0x64, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x61, 0x64, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x3a, 0x16, 0xba, 0x47, 0x13, 0xba, 0x01, 0x05, 0x70, 0x68,
	0x61, 0x73, 0x65, 0xba, 0x01, 0x08, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x75, 0x69, 0x64, 0x3a, 0x12,
	0xba, 0x47, 0x0f, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x06, 0x70, 0x68, 0x61, 0x73,
	0x65, 0x73, 0x22, 0xd2, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1d, 0x0a,
	0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x1a, 0x02, 0x28, 0x01, 0x52, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x75, 0x62, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x75, 0x62, 0x74, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x75,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x55, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x22, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xf4, 0x03, 0x28,
	0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x3a, 0x1c, 0xba, 0x47, 0x19, 0xba, 0x01,
	0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0xba, 0x01, 0x08, 0x74,
	0x65, 0x61, 0x6d, 0x5f, 0x75, 0x69, 0x64, 0x22, 0x52, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x12, 0x26, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x73, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22, 0xf8, 0x13, 0x0a, 0x03,
	0x4c, 0x6f, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x2d, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x79,
	0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x46, 0x72,
	0x61, 0x6d, 0x65, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69,
	0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69,
	0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x6f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x61, 0x75, 0x74, 0x6f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6a, 0x6f, 0x62, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x36,
	0x0a, 0x0a, 0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74,
	0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x6f, 0x6e, 0x74, 0x6f,
	0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x12, 0x29, 0x0a, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73,
	0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x6f, 0x74, 0x70, 0x68, 0x61, 0x73, 0x65, 0x52, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65,
	0x73, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x75,
	0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x55, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x08, 0x64, 0x61,
	0x74, 0x61, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x65, 0x78, 0x70, 0x5f, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x45, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x3e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x24, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72,
	0x67, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x67,
	0x55, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x69, 0x64, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x55, 0x69, 0x64, 0x12, 0x24,
	0x0a, 0x03, 0x6f, 0x75, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x03, 0x6f, 0x75, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6a, 0x6f, 0x62, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6a, 0x6f, 0x62, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6a, 0x6f, 0x62, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x79, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6a, 0x6f, 0x62, 0x52, 0x65, 0x61, 0x64, 0x79, 0x12, 0x1b,
	0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x69,
	0x6e, 0x73, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x6e,
	0x73, 0x43, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6e, 0x73, 0x5f, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x54, 0x6f, 0x74, 0x61,
	0x6c, 0x12, 0x32, 0x0a, 0x08, 0x74, 0x6f, 0x6f, 0x6c, 0x5f, 0x63, 0x66, 0x67, 0x18, 0x1d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f,
	0x74, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x07, 0x74, 0x6f,
	0x6f, 0x6c, 0x43, 0x66, 0x67, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x6e, 0x6e, 0x6f, 0x5f, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x6e, 0x6e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a,
	0x10, 0x63, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x61, 0x6e, 0x6e, 0x6f,
	0x73, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x63, 0x61, 0x6e, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x12, 0x44, 0x0a, 0x0f, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x18, 0x20, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x0e, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x21, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67,
	0x73, 0x1a, 0x6f, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x22, 0x67, 0x0a, 0x04, 0x45, 0x6e, 0x75,
	0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x10, 0x01,
	0x12, 0x14, 0x0a, 0x10, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x10, 0x0b, 0x12, 0x14, 0x0a, 0x10, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x73, 0x65, 0x6d, 0x61, 0x6e, 0x74, 0x69, 0x63, 0x10, 0x0c, 0x12, 0x14, 0x0a, 0x10,
	0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x6e, 0x6f, 0x70, 0x74, 0x69, 0x63,
	0x10, 0x0d, 0x1a, 0x74, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x6b, 0x0a, 0x04, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69,
	0x7a, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x75, 0x6e, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x6f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x10, 0x03,
	0x12, 0x0c, 0x0a, 0x08, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x10, 0x04, 0x12, 0x0a,
	0x0a, 0x06, 0x70, 0x61, 0x75, 0x73, 0x65, 0x64, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x63, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64, 0x10, 0x06, 0x1a, 0x88, 0x02, 0x0a, 0x05, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x33, 0x0a, 0x05, 0x73, 0x68, 0x61, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x2e,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x53, 0x68, 0x61, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x05, 0x73, 0x68, 0x61, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x02, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x24, 0x0a, 0x06, 0x7a,
	0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x06, 0x7a, 0x72, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0c, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x1a, 0x3b, 0x0a, 0x05, 0x53, 0x68, 0x61, 0x70, 0x65, 0x22, 0x32, 0x0a, 0x04, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x63, 0x69, 0x72, 0x63, 0x6c, 0x65, 0x10, 0x01,
	0x12, 0x0d, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x74, 0x61, 0x6e, 0x67, 0x6c, 0x65, 0x10, 0x02, 0x3a,
	0x12, 0xba, 0x47, 0x0f, 0xba, 0x01, 0x05, 0x73, 0x68, 0x61, 0x70, 0x65, 0xba, 0x01, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x1a, 0xcb, 0x05, 0x0a, 0x0a, 0x54, 0x6f, 0x6f, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x2a, 0x0a, 0x06, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74,
	0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x06, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12, 0x2d,
	0x0a, 0x12, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x64, 0x69, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x45, 0x64, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x27, 0x0a,
	0x0f, 0x72, 0x65, 0x64, 0x6f, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x72, 0x65, 0x64, 0x6f, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x72, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x6f, 0x74, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x52, 0x75, 0x6c, 0x65, 0x72, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x72, 0x73, 0x12, 0x25, 0x0a,
	0x0e, 0x73, 0x61, 0x76, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x61, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x17, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x33, 0x64, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x33, 0x64, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x37, 0x0a,
	0x07, 0x70, 0x72, 0x65, 0x5f, 0x62, 0x6f, 0x78, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x2e, 0x54, 0x6f, 0x6f,
	0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x50, 0x72, 0x65, 0x42, 0x6f, 0x78, 0x52, 0x06,
	0x70, 0x72, 0x65, 0x42, 0x6f, 0x78, 0x1a, 0xbd, 0x01, 0x0a, 0x05, 0x52, 0x75, 0x6c, 0x65, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74,
	0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x53, 0x68, 0x61, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75,
	0x6d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x01, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3f, 0x0a, 0x0d, 0x72,
	0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x77,
	0x64, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0c,
	0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x73, 0x3a, 0x18, 0xba, 0x47,
	0x15, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x04, 0x74, 0x79, 0x70, 0x65, 0xba,
	0x01, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x88, 0x01, 0x0a, 0x06, 0x50, 0x72, 0x65, 0x42, 0x6f,
	0x78, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x14, 0x0a,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x3a, 0x24, 0xba, 0x47, 0x21,
	0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68,
	0xba, 0x01, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0xba, 0x01, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x3a, 0x6d, 0xba, 0x47, 0x6a, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0xba, 0x01, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0xba, 0x01, 0x04, 0x74, 0x79,
	0x70, 0x65, 0xba, 0x01, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0xba, 0x01, 0x08,
	0x6a, 0x6f, 0x62, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0xba, 0x01, 0x0a, 0x6f, 0x6e, 0x74, 0x6f, 0x6c,
	0x6f, 0x67, 0x69, 0x65, 0x73, 0xba, 0x01, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0xba, 0x01,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0xba, 0x01, 0x07, 0x6f, 0x72,
	0x67, 0x5f, 0x75, 0x69, 0x64, 0xba, 0x01, 0x08, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x69, 0x64,
	0x4a, 0x04, 0x08, 0x17, 0x10, 0x18, 0x22, 0x79, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1d, 0x0a, 0x0a,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x6a,
	0x6f, 0x62, 0x73, 0x5f, 0x61, 0x74, 0x5f, 0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x0b, 0x6a, 0x6f, 0x62, 0x73, 0x41, 0x74, 0x50, 0x68, 0x61, 0x73, 0x65, 0x3a,
	0x20, 0xba, 0x47, 0x1d, 0xba, 0x01, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6a, 0x6f, 0x62,
	0x73, 0xba, 0x01, 0x0d, 0x6a, 0x6f, 0x62, 0x73, 0x5f, 0x61, 0x74, 0x5f, 0x70, 0x68, 0x61, 0x73,
	0x65, 0x22, 0x63, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x73,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x36, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6e, 0x6e, 0x6f,
	0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x17, 0x0a,
	0x07, 0x69, 0x6e, 0x73, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x69, 0x6e, 0x73, 0x43, 0x6e, 0x74, 0x22, 0xb7, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x56, 0x69,
	0x73, 0x69, 0x62, 0x6c, 0x65, 0x4c, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72,
	0x55, 0x69, 0x64, 0x12, 0x3e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f,
	0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0d, 0xfa, 0x42,
	0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6f, 0x6e, 0x6c,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x6e,
	0x6c, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x61, 0x62, 0x6c, 0x65,
	0x22, 0xd1, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x4c,
	0x6f, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x35, 0x0a, 0x08, 0x6c, 0x6f, 0x74, 0x5f,
	0x75, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x92,
	0x01, 0x14, 0x22, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x07, 0x6c, 0x6f, 0x74, 0x55, 0x69, 0x64, 0x73, 0x12,
	0x46, 0x0a, 0x08, 0x6c, 0x6f, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56,
	0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x4c, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e,
	0x4c, 0x6f, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6c,
	0x6f, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x3b, 0x0a, 0x0d, 0x4c, 0x6f, 0x74, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0xa8, 0x01, 0x0a, 0x15, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4c,
	0x6f, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27,
	0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12,
	0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31,
	0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x4e, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x41, 0x6e, 0x6e,
	0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x22,
	0x6f, 0x0a, 0x17, 0x53, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x90, 0x01, 0x01, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x3a,
	0x0f, 0xba, 0x47, 0x0c, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x03, 0x75, 0x72, 0x6c,
	0x22, 0x5a, 0x0a, 0x19, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72,
	0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d,
	0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x22, 0x36, 0x0a, 0x17,
	0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x4c, 0x6f, 0x74, 0x69, 0x64, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x3a, 0x09, 0xba, 0x47, 0x06, 0xba, 0x01,
	0x03, 0x69, 0x64, 0x73, 0x22, 0xf0, 0x01, 0x0a, 0x15, 0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x42, 0x79, 0x4c, 0x6f, 0x74, 0x69, 0x64, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3a,
	0x0a, 0x04, 0x6c, 0x6f, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42,
	0x79, 0x4c, 0x6f, 0x74, 0x69, 0x64, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x4c, 0x6f, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x6f, 0x74, 0x73, 0x1a, 0x38, 0x0a, 0x0a, 0x50, 0x68,
	0x61, 0x73, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x61, 0x73,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x61, 0x0a, 0x07, 0x4c, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x15, 0x0a, 0x06, 0x6c, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6c, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x4c, 0x6f, 0x74, 0x69, 0x64, 0x73,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x50, 0x68, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x32, 0xbe, 0x10, 0x0a, 0x04, 0x4c, 0x6f, 0x74, 0x73,
	0x12, 0x49, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x74, 0x12, 0x19, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x22, 0x13, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0d, 0x3a, 0x01,
	0x2a, 0x22, 0x08, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x12, 0x53, 0x0a, 0x08, 0x43,
	0x6c, 0x6f, 0x6e, 0x65, 0x4c, 0x6f, 0x74, 0x12, 0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x4c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x0c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x22,
	0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x76, 0x31, 0x2f,
	0x6c, 0x6f, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x63, 0x6c, 0x6f, 0x6e, 0x65,
	0x12, 0x55, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x74, 0x12, 0x19, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x03,
	0x6c, 0x6f, 0x74, 0x32, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f, 0x7b, 0x6c,
	0x6f, 0x74, 0x2e, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x56, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x4c, 0x6f, 0x74, 0x12, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x2a,
	0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12,
	0x78, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x74, 0x73, 0x42, 0x79, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x6f, 0x72, 0x12, 0x22, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x74, 0x73, 0x42, 0x79, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x74, 0x73, 0x42, 0x79, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1c, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f, 0x62, 0x79,
	0x2d, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x12, 0x46, 0x0a, 0x06, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x74, 0x12, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x4c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0c, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x74, 0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x10, 0x12, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64,
	0x7d, 0x12, 0x4b, 0x0a, 0x07, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x74, 0x12, 0x17, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x10, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x0a, 0x12, 0x08, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x12, 0x5b,
	0x0a, 0x08, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4c, 0x6f, 0x74, 0x12, 0x16, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x19, 0x3a, 0x01, 0x2a, 0x1a, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f,
	0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x5b, 0x0a, 0x08, 0x50,
	0x61, 0x75, 0x73, 0x65, 0x4c, 0x6f, 0x74, 0x12, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a,
	0x01, 0x2a, 0x1a, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69,
	0x64, 0x7d, 0x2f, 0x70, 0x61, 0x75, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x09, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x4c, 0x6f, 0x74, 0x12, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a,
	0x1a, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d,
	0x2f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x12, 0x6d, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x45,
	0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x1d, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f, 0x76,
	0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x65, 0x78, 0x65,
	0x63, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x6c, 0x0a, 0x0e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e,
	0x45, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x12, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61,
	0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x1a, 0x17, 0x2f, 0x76, 0x31,
	0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x65, 0x78, 0x65, 0x63,
	0x74, 0x65, 0x61, 0x6d, 0x12, 0x6f, 0x0a, 0x0f, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x1f, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x76, 0x31,
	0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x6d, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x1d, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f, 0x76, 0x31, 0x2f,
	0x6c, 0x6f, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x6f, 0x72, 0x73, 0x12, 0x61, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x12, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12,
	0x16, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f,
	0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x4e, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x56, 0x69,
	0x73, 0x69, 0x62, 0x6c, 0x65, 0x4c, 0x6f, 0x74, 0x73, 0x12, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x4c, 0x6f,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x4c, 0x6f,
	0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x70, 0x0a, 0x0e, 0x45, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x4c, 0x6f, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x12, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x74, 0x41, 0x6e, 0x6e,
	0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x01, 0x2a, 0x1a, 0x1b, 0x2f, 0x76,
	0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x65, 0x78, 0x70,
	0x6f, 0x72, 0x74, 0x2d, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x12, 0x4c, 0x0a, 0x10, 0x53, 0x65, 0x74,
	0x4c, 0x6f, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x20, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x41, 0x6e,
	0x6e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x80, 0x01, 0x0a, 0x12, 0x41, 0x6c, 0x6c, 0x6f,
	0x77, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x12, 0x22,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2e, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x28, 0x3a, 0x01, 0x2a, 0x1a, 0x23, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f,
	0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x2d, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x2d, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x12, 0x4d, 0x0a, 0x06, 0x41, 0x64,
	0x64, 0x54, 0x61, 0x67, 0x12, 0x11, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x61, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0e, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x54, 0x61, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a,
	0x04, 0x74, 0x61, 0x67, 0x73, 0x1a, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f,
	0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x74, 0x61, 0x67, 0x12, 0x4a, 0x0a, 0x09, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x54, 0x61, 0x67, 0x12, 0x11, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54,
	0x61, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0e, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x54, 0x61, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x14, 0x2a, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64,
	0x7d, 0x2f, 0x74, 0x61, 0x67, 0x12, 0x71, 0x0a, 0x0e, 0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x42, 0x79, 0x4c, 0x6f, 0x74, 0x73, 0x12, 0x20, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x4c, 0x6f, 0x74, 0x69,
	0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x4c, 0x6f,
	0x74, 0x69, 0x64, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x17, 0x12, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x2d, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2d, 0x62, 0x79, 0x2d, 0x6c, 0x6f, 0x74, 0x73, 0x42, 0x3e, 0x0a, 0x07, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x31, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70,
	0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e, 0x6e, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_anno_v1_lot_proto_rawDescOnce sync.Once
	file_anno_v1_lot_proto_rawDescData = file_anno_v1_lot_proto_rawDesc
)

func file_anno_v1_lot_proto_rawDescGZIP() []byte {
	file_anno_v1_lot_proto_rawDescOnce.Do(func() {
		file_anno_v1_lot_proto_rawDescData = protoimpl.X.CompressGZIP(file_anno_v1_lot_proto_rawDescData)
	})
	return file_anno_v1_lot_proto_rawDescData
}

var file_anno_v1_lot_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_anno_v1_lot_proto_msgTypes = make([]protoimpl.MessageInfo, 41)
var file_anno_v1_lot_proto_goTypes = []interface{}{
	(Lot_Type_Enum)(0),                       // 0: anno.v1.Lot.Type.Enum
	(Lot_State_Enum)(0),                      // 1: anno.v1.Lot.State.Enum
	(Lot_Range_Shape_Enum)(0),                // 2: anno.v1.Lot.Range.Shape.Enum
	(*CreateLotRequest)(nil),                 // 3: anno.v1.CreateLotRequest
	(*CloneLotRequest)(nil),                  // 4: anno.v1.CloneLotRequest
	(*UpdateLotRequest)(nil),                 // 5: anno.v1.UpdateLotRequest
	(*DeleteLotRequest)(nil),                 // 6: anno.v1.DeleteLotRequest
	(*GetLotRequest)(nil),                    // 7: anno.v1.GetLotRequest
	(*ListLotRequest)(nil),                   // 8: anno.v1.ListLotRequest
	(*ListLotReply)(nil),                     // 9: anno.v1.ListLotReply
	(*ListLotsByExecutorRequest)(nil),        // 10: anno.v1.ListLotsByExecutorRequest
	(*ListLotsByExecutorReply)(nil),          // 11: anno.v1.ListLotsByExecutorReply
	(*ListExecteamsRequest)(nil),             // 12: anno.v1.ListExecteamsRequest
	(*ListExecteamsReply)(nil),               // 13: anno.v1.ListExecteamsReply
	(*AssignExecteamRequest)(nil),            // 14: anno.v1.AssignExecteamRequest
	(*ManageExecutorsRequest)(nil),           // 15: anno.v1.ManageExecutorsRequest
	(*ListExecutorsRequest)(nil),             // 16: anno.v1.ListExecutorsRequest
	(*ListExecutorsReply)(nil),               // 17: anno.v1.ListExecutorsReply
	(*Lot)(nil),                              // 18: anno.v1.Lot
	(*GetLotSummaryReply)(nil),               // 19: anno.v1.GetLotSummaryReply
	(*GetLotAnnosReply)(nil),                 // 20: anno.v1.GetLotAnnosReply
	(*GetVisibleLotsRequest)(nil),            // 21: anno.v1.GetVisibleLotsRequest
	(*GetVisibleLotsReply)(nil),              // 22: anno.v1.GetVisibleLotsReply
	(*ExportLotAnnosRequest)(nil),            // 23: anno.v1.ExportLotAnnosRequest
	(*SetLotAnnoResultRequest)(nil),          // 24: anno.v1.SetLotAnnoResultRequest
	(*AllowDownloadAnnosRequest)(nil),        // 25: anno.v1.AllowDownloadAnnosRequest
	(*JobCountByLotidsRequest)(nil),          // 26: anno.v1.JobCountByLotidsRequest
	(*JobCountByLotidsReply)(nil),            // 27: anno.v1.JobCountByLotidsReply
	(*ListLotsByExecutorReply_Extra)(nil),    // 28: anno.v1.ListLotsByExecutorReply.Extra
	nil,                                      // 29: anno.v1.ListLotsByExecutorReply.ExtrasEntry
	(*ListExecteamsReply_Execteam)(nil),      // 30: anno.v1.ListExecteamsReply.Execteam
	(*ListExecteamsReply_Phase)(nil),         // 31: anno.v1.ListExecteamsReply.Phase
	(*AssignExecteamRequest_Phase)(nil),      // 32: anno.v1.AssignExecteamRequest.Phase
	(*ManageExecutorsRequest_Phase)(nil),     // 33: anno.v1.ManageExecutorsRequest.Phase
	(*Lot_Type)(nil),                         // 34: anno.v1.Lot.Type
	(*Lot_State)(nil),                        // 35: anno.v1.Lot.State
	(*Lot_Range)(nil),                        // 36: anno.v1.Lot.Range
	(*Lot_ToolConfig)(nil),                   // 37: anno.v1.Lot.ToolConfig
	(*Lot_Range_Shape)(nil),                  // 38: anno.v1.Lot.Range.Shape
	(*Lot_ToolConfig_Ruler)(nil),             // 39: anno.v1.Lot.ToolConfig.Ruler
	(*Lot_ToolConfig_PreBox)(nil),            // 40: anno.v1.Lot.ToolConfig.PreBox
	nil,                                      // 41: anno.v1.GetVisibleLotsReply.LotCountEntry
	(*JobCountByLotidsReply_PhaseCount)(nil), // 42: anno.v1.JobCountByLotidsReply.PhaseCount
	(*JobCountByLotidsReply_LotInfo)(nil),    // 43: anno.v1.JobCountByLotidsReply.LotInfo
	(*Lotontologies)(nil),                    // 44: anno.v1.Lotontologies
	(*Lotphase)(nil),                         // 45: anno.v1.Lotphase
	(*OutConfig)(nil),                        // 46: anno.v1.OutConfig
	(*timestamppb.Timestamp)(nil),            // 47: google.protobuf.Timestamp
	(*CommentReasonClass)(nil),               // 48: anno.v1.CommentReasonClass
	(*v1.BaseUser)(nil),                      // 49: iam.v1.BaseUser
	(Element_Type_Enum)(0),                   // 50: anno.v1.Element.Type.Enum
	(*Error)(nil),                            // 51: anno.v1.Error
	(*ElementAnno)(nil),                      // 52: anno.v1.ElementAnno
	(ExportOrderAnnosRequest_Option_Enum)(0), // 53: anno.v1.ExportOrderAnnosRequest.Option.Enum
	(*Lotphase_Quota)(nil),                   // 54: anno.v1.Lotphase.Quota
	(*Lotphase_Execteam)(nil),                // 55: anno.v1.Lotphase.Execteam
	(*types.Range)(nil),                      // 56: types.Range
	(Rawdata_Type_Enum)(0),                   // 57: anno.v1.Rawdata.Type.Enum
	(*types.TagRequest)(nil),                 // 58: types.TagRequest
	(*emptypb.Empty)(nil),                    // 59: google.protobuf.Empty
	(*types.TagList)(nil),                    // 60: types.TagList
}
var file_anno_v1_lot_proto_depIdxs = []int32{
	0,  // 0: anno.v1.CreateLotRequest.type:type_name -> anno.v1.Lot.Type.Enum
	44, // 1: anno.v1.CreateLotRequest.ontologies:type_name -> anno.v1.Lotontologies
	45, // 2: anno.v1.CreateLotRequest.phases:type_name -> anno.v1.Lotphase
	46, // 3: anno.v1.CreateLotRequest.out:type_name -> anno.v1.OutConfig
	47, // 4: anno.v1.CreateLotRequest.exp_end_time:type_name -> google.protobuf.Timestamp
	37, // 5: anno.v1.CreateLotRequest.tool_cfg:type_name -> anno.v1.Lot.ToolConfig
	48, // 6: anno.v1.CreateLotRequest.comment_reasons:type_name -> anno.v1.CommentReasonClass
	5,  // 7: anno.v1.CloneLotRequest.updates:type_name -> anno.v1.UpdateLotRequest
	3,  // 8: anno.v1.UpdateLotRequest.lot:type_name -> anno.v1.CreateLotRequest
	1,  // 9: anno.v1.ListLotRequest.states:type_name -> anno.v1.Lot.State.Enum
	0,  // 10: anno.v1.ListLotRequest.type:type_name -> anno.v1.Lot.Type.Enum
	18, // 11: anno.v1.ListLotReply.lots:type_name -> anno.v1.Lot
	49, // 12: anno.v1.ListLotReply.orgs:type_name -> iam.v1.BaseUser
	1,  // 13: anno.v1.ListLotsByExecutorRequest.states:type_name -> anno.v1.Lot.State.Enum
	0,  // 14: anno.v1.ListLotsByExecutorRequest.type:type_name -> anno.v1.Lot.Type.Enum
	18, // 15: anno.v1.ListLotsByExecutorReply.lots:type_name -> anno.v1.Lot
	29, // 16: anno.v1.ListLotsByExecutorReply.extras:type_name -> anno.v1.ListLotsByExecutorReply.ExtrasEntry
	31, // 17: anno.v1.ListExecteamsReply.phases:type_name -> anno.v1.ListExecteamsReply.Phase
	32, // 18: anno.v1.AssignExecteamRequest.phases:type_name -> anno.v1.AssignExecteamRequest.Phase
	33, // 19: anno.v1.ManageExecutorsRequest.phases:type_name -> anno.v1.ManageExecutorsRequest.Phase
	49, // 20: anno.v1.ListExecutorsReply.users:type_name -> iam.v1.BaseUser
	1,  // 21: anno.v1.Lot.state:type_name -> anno.v1.Lot.State.Enum
	0,  // 22: anno.v1.Lot.type:type_name -> anno.v1.Lot.Type.Enum
	50, // 23: anno.v1.Lot.data_type:type_name -> anno.v1.Element.Type.Enum
	44, // 24: anno.v1.Lot.ontologies:type_name -> anno.v1.Lotontologies
	45, // 25: anno.v1.Lot.phases:type_name -> anno.v1.Lotphase
	47, // 26: anno.v1.Lot.exp_end_time:type_name -> google.protobuf.Timestamp
	47, // 27: anno.v1.Lot.updated_at:type_name -> google.protobuf.Timestamp
	47, // 28: anno.v1.Lot.created_at:type_name -> google.protobuf.Timestamp
	51, // 29: anno.v1.Lot.error:type_name -> anno.v1.Error
	46, // 30: anno.v1.Lot.out:type_name -> anno.v1.OutConfig
	37, // 31: anno.v1.Lot.tool_cfg:type_name -> anno.v1.Lot.ToolConfig
	48, // 32: anno.v1.Lot.comment_reasons:type_name -> anno.v1.CommentReasonClass
	52, // 33: anno.v1.GetLotAnnosReply.annotations:type_name -> anno.v1.ElementAnno
	1,  // 34: anno.v1.GetVisibleLotsRequest.states:type_name -> anno.v1.Lot.State.Enum
	41, // 35: anno.v1.GetVisibleLotsReply.lotCount:type_name -> anno.v1.GetVisibleLotsReply.LotCountEntry
	53, // 36: anno.v1.ExportLotAnnosRequest.option:type_name -> anno.v1.ExportOrderAnnosRequest.Option.Enum
	43, // 37: anno.v1.JobCountByLotidsReply.lots:type_name -> anno.v1.JobCountByLotidsReply.LotInfo
	28, // 38: anno.v1.ListLotsByExecutorReply.ExtrasEntry.value:type_name -> anno.v1.ListLotsByExecutorReply.Extra
	49, // 39: anno.v1.ListExecteamsReply.Execteam.team:type_name -> iam.v1.BaseUser
	49, // 40: anno.v1.ListExecteamsReply.Execteam.executors:type_name -> iam.v1.BaseUser
	54, // 41: anno.v1.ListExecteamsReply.Execteam.quota:type_name -> anno.v1.Lotphase.Quota
	30, // 42: anno.v1.ListExecteamsReply.Phase.teams:type_name -> anno.v1.ListExecteamsReply.Execteam
	55, // 43: anno.v1.AssignExecteamRequest.Phase.execteams:type_name -> anno.v1.Lotphase.Execteam
	2,  // 44: anno.v1.Lot.Range.shape:type_name -> anno.v1.Lot.Range.Shape.Enum
	56, // 45: anno.v1.Lot.Range.zrange:type_name -> types.Range
	57, // 46: anno.v1.Lot.Range.rawdata_types:type_name -> anno.v1.Rawdata.Type.Enum
	36, // 47: anno.v1.Lot.ToolConfig.ranges:type_name -> anno.v1.Lot.Range
	39, // 48: anno.v1.Lot.ToolConfig.rulers:type_name -> anno.v1.Lot.ToolConfig.Ruler
	40, // 49: anno.v1.Lot.ToolConfig.pre_box:type_name -> anno.v1.Lot.ToolConfig.PreBox
	2,  // 50: anno.v1.Lot.ToolConfig.Ruler.type:type_name -> anno.v1.Lot.Range.Shape.Enum
	57, // 51: anno.v1.Lot.ToolConfig.Ruler.rawdata_types:type_name -> anno.v1.Rawdata.Type.Enum
	42, // 52: anno.v1.JobCountByLotidsReply.LotInfo.count:type_name -> anno.v1.JobCountByLotidsReply.PhaseCount
	3,  // 53: anno.v1.Lots.CreateLot:input_type -> anno.v1.CreateLotRequest
	4,  // 54: anno.v1.Lots.CloneLot:input_type -> anno.v1.CloneLotRequest
	5,  // 55: anno.v1.Lots.UpdateLot:input_type -> anno.v1.UpdateLotRequest
	6,  // 56: anno.v1.Lots.DeleteLot:input_type -> anno.v1.DeleteLotRequest
	10, // 57: anno.v1.Lots.ListLotsByExecutor:input_type -> anno.v1.ListLotsByExecutorRequest
	7,  // 58: anno.v1.Lots.GetLot:input_type -> anno.v1.GetLotRequest
	8,  // 59: anno.v1.Lots.ListLot:input_type -> anno.v1.ListLotRequest
	7,  // 60: anno.v1.Lots.StartLot:input_type -> anno.v1.GetLotRequest
	7,  // 61: anno.v1.Lots.PauseLot:input_type -> anno.v1.GetLotRequest
	7,  // 62: anno.v1.Lots.CancelLot:input_type -> anno.v1.GetLotRequest
	12, // 63: anno.v1.Lots.ListExecteams:input_type -> anno.v1.ListExecteamsRequest
	14, // 64: anno.v1.Lots.AssignExecteam:input_type -> anno.v1.AssignExecteamRequest
	15, // 65: anno.v1.Lots.ManageExecutors:input_type -> anno.v1.ManageExecutorsRequest
	16, // 66: anno.v1.Lots.ListExecutors:input_type -> anno.v1.ListExecutorsRequest
	7,  // 67: anno.v1.Lots.GetSummary:input_type -> anno.v1.GetLotRequest
	21, // 68: anno.v1.Lots.GetVisibleLots:input_type -> anno.v1.GetVisibleLotsRequest
	23, // 69: anno.v1.Lots.ExportLotAnnos:input_type -> anno.v1.ExportLotAnnosRequest
	24, // 70: anno.v1.Lots.SetLotAnnoResult:input_type -> anno.v1.SetLotAnnoResultRequest
	25, // 71: anno.v1.Lots.AllowDownloadAnnos:input_type -> anno.v1.AllowDownloadAnnosRequest
	58, // 72: anno.v1.Lots.AddTag:input_type -> types.TagRequest
	58, // 73: anno.v1.Lots.DeleteTag:input_type -> types.TagRequest
	26, // 74: anno.v1.Lots.JobCountByLots:input_type -> anno.v1.JobCountByLotidsRequest
	18, // 75: anno.v1.Lots.CreateLot:output_type -> anno.v1.Lot
	18, // 76: anno.v1.Lots.CloneLot:output_type -> anno.v1.Lot
	18, // 77: anno.v1.Lots.UpdateLot:output_type -> anno.v1.Lot
	59, // 78: anno.v1.Lots.DeleteLot:output_type -> google.protobuf.Empty
	11, // 79: anno.v1.Lots.ListLotsByExecutor:output_type -> anno.v1.ListLotsByExecutorReply
	18, // 80: anno.v1.Lots.GetLot:output_type -> anno.v1.Lot
	9,  // 81: anno.v1.Lots.ListLot:output_type -> anno.v1.ListLotReply
	59, // 82: anno.v1.Lots.StartLot:output_type -> google.protobuf.Empty
	59, // 83: anno.v1.Lots.PauseLot:output_type -> google.protobuf.Empty
	59, // 84: anno.v1.Lots.CancelLot:output_type -> google.protobuf.Empty
	13, // 85: anno.v1.Lots.ListExecteams:output_type -> anno.v1.ListExecteamsReply
	59, // 86: anno.v1.Lots.AssignExecteam:output_type -> google.protobuf.Empty
	59, // 87: anno.v1.Lots.ManageExecutors:output_type -> google.protobuf.Empty
	17, // 88: anno.v1.Lots.ListExecutors:output_type -> anno.v1.ListExecutorsReply
	19, // 89: anno.v1.Lots.GetSummary:output_type -> anno.v1.GetLotSummaryReply
	22, // 90: anno.v1.Lots.GetVisibleLots:output_type -> anno.v1.GetVisibleLotsReply
	59, // 91: anno.v1.Lots.ExportLotAnnos:output_type -> google.protobuf.Empty
	59, // 92: anno.v1.Lots.SetLotAnnoResult:output_type -> google.protobuf.Empty
	59, // 93: anno.v1.Lots.AllowDownloadAnnos:output_type -> google.protobuf.Empty
	60, // 94: anno.v1.Lots.AddTag:output_type -> types.TagList
	60, // 95: anno.v1.Lots.DeleteTag:output_type -> types.TagList
	27, // 96: anno.v1.Lots.JobCountByLots:output_type -> anno.v1.JobCountByLotidsReply
	75, // [75:97] is the sub-list for method output_type
	53, // [53:75] is the sub-list for method input_type
	53, // [53:53] is the sub-list for extension type_name
	53, // [53:53] is the sub-list for extension extendee
	0,  // [0:53] is the sub-list for field type_name
}

func init() { file_anno_v1_lot_proto_init() }
func file_anno_v1_lot_proto_init() {
	if File_anno_v1_lot_proto != nil {
		return
	}
	file_anno_v1_config_proto_init()
	file_anno_v1_order_proto_init()
	file_anno_v1_type_proto_init()
	file_anno_v1_elemanno_proto_init()
	file_anno_v1_type_lotconfig_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_anno_v1_lot_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLotRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloneLotRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLotRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLotRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLotRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLotReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLotsByExecutorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLotsByExecutorReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExecteamsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExecteamsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssignExecteamRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManageExecutorsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExecutorsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExecutorsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotSummaryReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotAnnosReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVisibleLotsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVisibleLotsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportLotAnnosRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetLotAnnoResultRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AllowDownloadAnnosRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobCountByLotidsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobCountByLotidsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLotsByExecutorReply_Extra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExecteamsReply_Execteam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExecteamsReply_Phase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssignExecteamRequest_Phase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManageExecutorsRequest_Phase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lot_Type); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lot_State); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lot_Range); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lot_ToolConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lot_Range_Shape); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lot_ToolConfig_Ruler); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lot_ToolConfig_PreBox); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobCountByLotidsReply_PhaseCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_lot_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobCountByLotidsReply_LotInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anno_v1_lot_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   41,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_anno_v1_lot_proto_goTypes,
		DependencyIndexes: file_anno_v1_lot_proto_depIdxs,
		EnumInfos:         file_anno_v1_lot_proto_enumTypes,
		MessageInfos:      file_anno_v1_lot_proto_msgTypes,
	}.Build()
	File_anno_v1_lot_proto = out.File
	file_anno_v1_lot_proto_rawDesc = nil
	file_anno_v1_lot_proto_goTypes = nil
	file_anno_v1_lot_proto_depIdxs = nil
}
