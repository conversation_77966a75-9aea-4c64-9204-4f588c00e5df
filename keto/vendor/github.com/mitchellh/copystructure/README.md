# copystructure

copystructure is a Go library for deep copying values in Go.

This allows you to copy Go values that may contain reference values
such as maps, slices, or pointers, and copy their data as well instead
of just their references.

## Installation

Standard `go get`:

```
$ go get github.com/mitchellh/copystructure
```

## Usage & Example

For usage and examples see the [Godoc](http://godoc.org/github.com/mitchellh/copystructure).

The `Copy` function has examples associated with it there.
