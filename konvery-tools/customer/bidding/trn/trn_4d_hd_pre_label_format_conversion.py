import os
import sys
import glob

sys.path.append('.')
import json
import numpy as np
from customer.common import tools


def get_value_from_kw_attrs(attrs, name):
    for atrr in attrs:
        if atrr['name'] == name:
            return atrr['values'][0]
    return None


def process_lanelines(obj, max_used_id):
    obj_id = int(get_value_from_kw_attrs(obj['label']['attrs'], 'OriginalID'))
    if obj_id == 0:
        obj_id = int(obj['track_id'].rsplit('.', 1)[-1]) + max_used_id
    boundary_forms = get_value_from_kw_attrs(obj['label']['attrs'], 'boundary_forms')
    category = get_value_from_kw_attrs(obj['label']['attrs'], 'lane_type')
    color = get_value_from_kw_attrs(obj['label']['attrs'], 'lane_color')
    is_decelerate = tools.is_true(get_value_from_kw_attrs(obj['label']['attrs'], 'is_decelerate'))
    longitudinal_deceleration_type = get_value_from_kw_attrs(obj['label']['attrs'], 'longitudinal_deceleration_type')
    is_diversionarea = tools.is_true(get_value_from_kw_attrs(obj['label']['attrs'], 'is_diversionarea'))
    geometry = {
        "type": "linestring",
        "coords3d_local": np.array(obj['label']['widget']['data']).reshape(-1, 3).tolist()
    }
    result = {
        "id": obj_id,
        "boundary_forms": boundary_forms,
        "category": category,
        "color": color,
        "is_decelerate": is_decelerate,
        "longitudinal_deceleration_type": longitudinal_deceleration_type,
        "is_diversionarea": is_diversionarea,
        "geometry": geometry
    }
    return result


def process_keypoints(obj, max_used_id):
    obj_id = int(get_value_from_kw_attrs(obj['label']['attrs'], 'OriginalID'))
    if obj_id == 0:
        obj_id = int(obj['track_id'].rsplit('.', 1)[-1]) + max_used_id
    category = get_value_from_kw_attrs(obj['label']['attrs'], 'points_type')
    geometry = {
        "type": "point",
        "coords3d_local": obj['label']['widget']['data']
    }
    result = {
        "id": obj_id,
        "category": category,
        "geometry": geometry
    }
    return result


def process_boundaries(obj, max_used_id):
    obj_id = int(get_value_from_kw_attrs(obj['label']['attrs'], 'OriginalID'))
    if obj_id == 0:
        obj_id = int(obj['track_id'].rsplit('.', 1)[-1]) + max_used_id
    category = obj['label']['name']
    if category == 'boundaries_others':
        category = 'others'
    geometry = {
        "type": "linestring",
        "coords3d_local": np.array(obj['label']['widget']['data']).reshape(-1, 3).tolist()
    }
    result = {
        "id": obj_id,
        "category": category,
        "geometry": geometry
    }
    return result


def process_stoplines(obj, max_used_id):
    return process_boundaries(obj, max_used_id)


def process_traffic_marks(obj, max_used_id):
    obj_id = int(get_value_from_kw_attrs(obj['label']['attrs'], 'OriginalID'))
    if obj_id == 0:
        obj_id = int(obj['track_id'].rsplit('.', 1)[-1]) + max_used_id
    category = obj['label']['name']
    if category == 'Traffffic_marks_other':
        category = 'OTHER'
    if obj['label']['widget']['name'] == 'cuboid':
        coords3d_local = tools.get_cuboid_vertices(np.array(obj['label']['widget']['data'][:3]),
                                                   np.array(obj['label']['widget']['data'][3:6]),
                                                   np.array(obj['label']['widget']['data'][6:]))[4:]

        coords3d_local += coords3d_local[:1]
    else:
        coords3d_local = np.array(obj['label']['widget']['data']).reshape(-1, 3).tolist() + np.array(
            obj['label']['widget']['data']).reshape(-1, 3).tolist()[:1]
    geometry = {
        "type": "polygon",
        "coords3d_local": coords3d_local
    }
    limit_speed = ''
    if category == 'limit_speed':
        limit_speed = get_value_from_kw_attrs(obj['label']['attrs'], 'speed')
    result = {
        "id": obj_id,
        "category": category,
        "limit_speed": limit_speed,
        "geometry": geometry
    }
    return result


def process_traffic_lights(obj, max_used_id):
    obj_id = int(get_value_from_kw_attrs(obj['label']['attrs'], 'OriginalID'))
    if obj_id == 0:
        obj_id = int(obj['track_id'].rsplit('.', 1)[-1]) + max_used_id
    category = obj['label']['name']
    geometry = {
        "type": "polygon",
        "coords3d_local": np.array(obj['label']['widget']['data']).reshape(-1, 3).tolist() + np.array(
            obj['label']['widget']['data']).reshape(-1, 3).tolist()[:1]
    }
    result = {
        "id": obj_id,
        "category": category,
        "geometry": geometry
    }
    return result


def process_traffic_signs(obj, max_used_id):
    obj_id = int(get_value_from_kw_attrs(obj['label']['attrs'], 'OriginalID'))
    if obj_id == 0:
        obj_id = int(obj['track_id'].rsplit('.', 1)[-1]) + max_used_id
    shape = get_value_from_kw_attrs(obj['label']['attrs'], 'shape')
    code = get_value_from_kw_attrs(obj['label']['attrs'], 'TrafficSign_type')
    number = get_value_from_kw_attrs(obj['label']['attrs'], 'number')
    direction = get_value_from_kw_attrs(obj['label']['attrs'], 'direction')
    geometry = {
        "type": "polygon",
        "coords3d_local": np.array(obj['label']['widget']['data']).reshape(-1, 3).tolist() + np.array(
            obj['label']['widget']['data']).reshape(-1, 3).tolist()[:1]
    }
    result = {
        "id": obj_id,
        "shape": shape,
        "code": int(code) if code else '',
        "value": number,
        "direction": direction,
        "geometry": geometry
    }
    return result


def process_poles(obj, max_used_id):
    obj_id = int(get_value_from_kw_attrs(obj['label']['attrs'], 'OriginalID'))
    if obj_id == 0:
        obj_id = int(obj['track_id'].rsplit('.', 1)[-1]) + max_used_id
    category = get_value_from_kw_attrs(obj['label']['attrs'], 'Pole_type')
    geometry = {
        "type": "linestring",
        "coords3d_local": np.array(obj['label']['widget']['data']).reshape(-1, 3).tolist()
    }
    result = {
        "id": obj_id,
        "category": category,
        "geometry": geometry
    }
    return result

def update_obj_with_dt_topology(dt_objs, objs):
    """
    dt_objs: 原始bevclould_data中的数据，预标文件
    objs: 标注结果
    """
    result = []
    for obj in objs:
        target_dt_objs = list(filter(lambda dt_obj: dt_obj['id'] == obj['id'], dt_objs))
        if len(target_dt_objs) == 1:
            target_gt_obj = target_dt_objs[0]
            topology = target_gt_obj['topology']
            obj['topology'] = topology
        result.append(obj)
    return result


def get_max_used_id(bev_clould_data):
    used_ids = set()
    for gt_objs in bev_clould_data['physical_objects']['ground_truth'].values():
        for gt_obj in gt_objs:
            used_ids.add(gt_obj['id'])
    max_used_id = max(used_ids)
    return max_used_id


def run(input_dir, output_dir, bev_file):
    annos_dir = os.path.join(input_dir, 'annos')
    clip_dirs = tools.listdir(annos_dir, full_path=True, sort=True)
    assert len(clip_dirs) == 1, 'not support multi clip dirs'

    bev_clould_data = tools.get_json_data(bev_file)
    max_used_id = get_max_used_id(bev_clould_data)

    lanelines = []
    keypoints = []
    boundaries = []
    stoplines = []
    traffic_marks = []
    traffic_lights = []
    traffic_signs = []
    poles = []
    for clip_dir in clip_dirs:
        first_frame_dir = tools.listdir(clip_dir, full_path=True, sort=True)[0]
        anno_file = os.path.join(first_frame_dir, 'annos.json')
        anno_data = tools.get_json_data(anno_file)
        for rawdata_anno in anno_data['element_annos'][0]['rawdata_annos']:
            if not rawdata_anno['name'].endswith('.pcd'):
                continue
            for obj in rawdata_anno['objects']:
                if obj['label']['name'] == 'lanelines':
                    lanelines.append(process_lanelines(obj, max_used_id))
                elif obj['label']['name'] == 'keypoints':
                    keypoints.append(process_keypoints(obj, max_used_id))
                elif obj['label']['name'] in {'WALL', 'barrier', 'curb', 'ditch', 'water_barrier', 'cone',
                                            'separation_pile', 'virtual_bound', 'greenbelt', 'barrier_bar', 'boundaries_others'}:
                    boundaries.append(process_boundaries(obj, max_used_id))
                elif obj['label']['name'] in {'STOP_LINE', 'STOP_LINE_DOUBLE', 'STOP_LINE_DOUBLE_DASH',
                                            'STOP_LINE_VIRTUAL'}:
                    stoplines.append(process_stoplines(obj, max_used_id))
                elif obj['label']['name'] in {'CROSSWALK', 'GO_STRAIGHT', 'GO_STRAIGHT_LEFT_TURN', 'GO_STRAIGHT_RIGHT_TURN',
                                            'GO_STRAIGHT_U_TURN', 'INTERSECTION',  'LEFT_INTER_FLOW', 'LEFT_TURN',
                                            'LEFT_TURN_GO_STRAIGHT_RIGHT_TURN', 'LEFT_TURN_RIGHT_TURN',
                                            'LEFT_U_TURN', 'MAX_SPEED_LIMIT', 'MIN_SPEED_LIMIT',
                                            'NO_PARKING_AREA', 'NO_U_TURN', 'RIGHT_INTER_FLOW', 'RIGHT_TURN',
                                            'RIGHT_U_TURN', 'SPEED_BUMP', 'TEXT', 'Traffffic_marks_other', 'U_TURNU',
                                            'U_TURN_LEFT_TURN_GO_STRAIGHT', 'U_TURN_RIGHT_TURN', 'U_TURN_RIGHT_TURN_GO_STRAIGHT',
                                            'U_TURN_RIGHT_TURN_LEFT_TURN', 'U_TURN_RIGHT_TURN_LEFT_TURN_GO_STRAIGHT'}:
                    traffic_marks.append(process_traffic_marks(obj, max_used_id))
                elif obj['label']['name'] in {'MOTOR', 'COUNTDOWN', 'LANE_STATATATUS', 'FLASHING_WARNING', 'BUS',
                                              'PEDESTRIAN', 'NON_MOTOR', 'INTERSECTION', 'DIRECTION'}:
                    traffic_lights.append(process_traffic_lights(obj, max_used_id))
                elif obj['label']['name'] == 'TrafficSign':
                    traffic_signs.append(process_traffic_signs(obj, max_used_id))
                elif obj['label']['name'] == 'Pole':
                    poles.append(process_poles(obj, max_used_id))
                else:
                    raise Exception('not support {}'.format(obj['label']['name']))

    bev_clould_data['physical_objects']['ground_truth']['lanelines'] = lanelines
    bev_clould_data['physical_objects']['ground_truth']['keypoints'] = update_obj_with_dt_topology(bev_clould_data['physical_objects']['ground_truth']['keypoints'], keypoints)
    bev_clould_data['physical_objects']['ground_truth']['boundaries'] = update_obj_with_dt_topology(bev_clould_data['physical_objects']['ground_truth']['boundaries'], boundaries)
    bev_clould_data['physical_objects']['ground_truth']['stoplines'] = update_obj_with_dt_topology(bev_clould_data['physical_objects']['ground_truth']['stoplines'], stoplines)
    bev_clould_data['physical_objects']['ground_truth']['traffic_marks'] = update_obj_with_dt_topology(bev_clould_data['physical_objects']['ground_truth']['traffic_marks'], traffic_marks)
    bev_clould_data['physical_objects']['ground_truth']['traffic_lights'] = update_obj_with_dt_topology(bev_clould_data['physical_objects']['ground_truth']['traffic_lights'], traffic_lights)
    bev_clould_data['physical_objects']['ground_truth']['traffic_signs'] = update_obj_with_dt_topology(bev_clould_data['physical_objects']['ground_truth']['traffic_signs'], traffic_signs)
    bev_clould_data['physical_objects']['ground_truth']['poles'] = update_obj_with_dt_topology(bev_clould_data['physical_objects']['ground_truth']['poles'], poles)

    tools.write_json_file(bev_clould_data, os.path.join(output_dir, 'bevcloud_data.json'))


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out, args.txt)
