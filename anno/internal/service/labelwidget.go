// source: anno/v1/labelwidget.proto
package service

import (
	"context"

	"anno/internal/biz"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
)

func FromBizLabelwidget(d *biz.Labelwidget) *anno.Labelwidget {
	if d == nil {
		return nil
	}
	o := &anno.Labelwidget{
		Name:  d.Name,
		<PERSON>s: <PERSON><PERSON>,
	}
	return o
}

func ToBizLabelwidget(d *anno.Labelwidget) *biz.Labelwidget {
	if d == nil {
		return nil
	}
	o := &biz.Labelwidget{
		Name:  d.Name,
		<PERSON>s: <PERSON><PERSON>,
	}
	return o
}

type LabelwidgetsService struct {
	anno.UnimplementedLabelwidgetsServer
	bz *biz.LabelwidgetsBiz
}

func NewLabelwidgetsService(bz *biz.LabelwidgetsBiz) *LabelwidgetsService {
	// TODO: implement access control
	bz = nil // fail all calls
	return &LabelwidgetsService{bz: bz}
}

func (o *LabelwidgetsService) ListLabelwidget(ctx context.Context, req *anno.ListLabelwidgetRequest) (*anno.ListLabelwidgetReply, error) {
	pager := biz.Pager{
		// Pagesz: int(req.Pagesz),
		// Page:   int(req.Page),
	}
	filter := &biz.LabelwidgetListFilter{
		// Uids:   req.Uids,
		// NamePattern: req.NamePattern,
	}

	var cnt int
	if pager.Page == 0 {
		var err error
		cnt, err = o.bz.Count(ctx, filter)
		if err != nil {
			return nil, err
		}
	}
	datas, err := o.bz.List(ctx, filter, pager)
	if err != nil {
		return nil, err
	}
	return &anno.ListLabelwidgetReply{Total: int32(cnt), Widgets: kslice.Map(FromBizLabelwidget, datas)}, err
}
