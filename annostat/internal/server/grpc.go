package server

import (
	"annostat/internal/conf"
	"annostat/internal/service"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"gitlab.rp.konvery.work/platform/apis/annostat/v1"
	"gitlab.rp.konvery.work/platform/apis/middleware"
	"gitlab.rp.konvery.work/platform/apis/middleware/authfilter"
	"gitlab.rp.konvery.work/platform/apis/middleware/common"
)

var unauthWhitelist = []string{
	"/annostat.v1.Configs/ListErrors",
}
var noauthFilters = []authfilter.NoauthFilter{authfilter.WhitelistNoauthFilter(unauthWhitelist...)}

// NewGRPCServer new a gRPC server.
func NewGRPCServer(c *conf.Server,
	cfgs *service.ConfigsService,
	stats *service.StatsService,
	logger log.Logger) *grpc.Server {
	mws := common.Middlewares(logger,
		middleware.LoadUser(noauthFilters),
	)
	var opts = []grpc.ServerOption{
		grpc.Middleware(mws...),
	}
	if c.Grpc.Network != "" {
		opts = append(opts, grpc.Network(c.Grpc.Network))
	}
	if c.Grpc.Addr != "" {
		opts = append(opts, grpc.Address(c.Grpc.Addr))
	}
	if c.Grpc.Timeout != nil {
		opts = append(opts, grpc.Timeout(c.Grpc.Timeout.AsDuration()))
	}
	srv := grpc.NewServer(opts...)
	annostat.RegisterConfigsServer(srv, cfgs)
	annostat.RegisterStatsServer(srv, stats)
	return srv
}
