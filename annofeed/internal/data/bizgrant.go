package data

import (
	"annofeed/internal/biz"

	"gitlab.rp.konvery.work/platform/pkg/data"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type bizgrantsRepo struct {
	repo.GenericRepo[biz.Bizgrant]
	data *Data
	log  *log.Helper
}

func NewBizgrantsRepo(d *Data, logger log.Logger) biz.BizgrantsRepo {
	return &bizgrantsRepo{
		GenericRepo: data.NewGenericRepo[biz.Bizgrant](d, logger, biz.BizgrantTableName),
		data:        d,
		log:         log.<PERSON>Helper(logger),
	}
}
