syntax = "proto3";

package anno.v1;

import "openapi/v3/annotations.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anno/v1;anno";
option java_multiple_files = true;
option java_package = "anno.v1";

message Scope {
  option (openapi.v3.schema) = {
    required: ["uid", "type"]
  };

  // global/project/lot
  string type = 1;
  // project-uid/lot-uid
  string uid = 2;
}

// message SimpleLot {
//   option (openapi.v3.schema) = {
//     required: ["uid", "name"]
//   };

//   string uid = 1;
//   string name = 2;
// }

message Error {
  option (openapi.v3.schema) = {
    required: ["reason", "message"]
  };

  string reason = 1;
  string message = 2;
}
