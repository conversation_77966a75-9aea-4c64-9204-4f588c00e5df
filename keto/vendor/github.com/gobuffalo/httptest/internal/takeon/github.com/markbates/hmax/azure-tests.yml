steps:
  - task: GoTool@0
    inputs:
      version: $(go_version)
  - task: Bash@3
    inputs:
      targetType: inline
      script: |
        mkdir -p "$(GOBIN)"
        mkdir -p "$(GOPATH)/pkg"
        mkdir -p "$(modulePath)"
        shopt -s extglob
        mv !(gopath) "$(modulePath)"
    displayName: "Setup Go Workspace"
  - script: |
      go get -t -v ./...
      go test -race ./...
    workingDirectory: "$(modulePath)"
    displayName: "Tests"
