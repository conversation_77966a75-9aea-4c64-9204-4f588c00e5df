package fizz

import (
	"os"
	"strings"

	"github.com/gobuffalo/plush/v4"
)

type BubbleType int

type Bubbler struct {
	Translator
	data []string
}

func NewBubbler(t Translator) *Bubbler {
	return &Bubbler{
		Translator: t,
		data:       []string{},
	}
}

func (b *Bubbler) String() string {
	return strings.Join(b.data, "\n")
}

func (b *Bubbler) Bubble(s string) (string, error) {
	f := fizzer{b}
	ctx := plush.NewContextWith(map[string]interface{}{
		"exec":             f.Exec(os.Stdout),
		"create_table":     f.CreateTable,
		"change_column":    f.ChangeColumn,
		"add_column":       f.<PERSON>d<PERSON>umn,
		"drop_column":      f.<PERSON>umn,
		"rename_column":    f.RenameColumn,
		"raw":              f.RawSQL,
		"sql":              f.Raw<PERSON>,
		"add_index":        f.<PERSON>d<PERSON>ndex,
		"drop_index":       f.Drop<PERSON>ndex,
		"rename_index":     f.RenameIndex,
		"add_foreign_key":  f.<PERSON>,
		"drop_foreign_key": f.<PERSON>,
		"drop_table":       f.DropTable,
		"rename_table":     f.RenameTable,
	})

	err := plush.RunScript(s, ctx)

	return b.String(), err
}
