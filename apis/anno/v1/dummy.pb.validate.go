// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/dummy.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on DummyReply with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DummyReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DummyReply with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DummyReplyMultiError, or
// nil if none found.
func (m *DummyReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DummyReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetElementDataRef()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DummyReplyValidationError{
					field:  "ElementDataRef",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DummyReplyValidationError{
					field:  "ElementDataRef",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetElementDataRef()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DummyReplyValidationError{
				field:  "ElementDataRef",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAnnotationDataRef()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DummyReplyValidationError{
					field:  "AnnotationDataRef",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DummyReplyValidationError{
					field:  "AnnotationDataRef",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnotationDataRef()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DummyReplyValidationError{
				field:  "AnnotationDataRef",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCommentDataRef()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DummyReplyValidationError{
					field:  "CommentDataRef",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DummyReplyValidationError{
					field:  "CommentDataRef",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommentDataRef()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DummyReplyValidationError{
				field:  "CommentDataRef",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFilelistRef()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DummyReplyValidationError{
					field:  "FilelistRef",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DummyReplyValidationError{
					field:  "FilelistRef",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilelistRef()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DummyReplyValidationError{
				field:  "FilelistRef",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DummyReplyMultiError(errors)
	}

	return nil
}

// DummyReplyMultiError is an error wrapping multiple validation errors
// returned by DummyReply.ValidateAll() if the designated constraints aren't met.
type DummyReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DummyReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DummyReplyMultiError) AllErrors() []error { return m }

// DummyReplyValidationError is the validation error returned by
// DummyReply.Validate if the designated constraints aren't met.
type DummyReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DummyReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DummyReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DummyReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DummyReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DummyReplyValidationError) ErrorName() string { return "DummyReplyValidationError" }

// Error satisfies the builtin error interface
func (e DummyReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDummyReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DummyReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DummyReplyValidationError{}
