# Keto 关系元组分析

本文档对 `tuples.txt` 中的关系元组进行详细注释，帮助理解 Keto 的权限设计。

## 1. 服务账号配置

```text
# 服务账号配置
# 创建服务账号 anno 并加入 service 组
IamGroup:sys/service#members@IamUser:aaaaaaaanno
# 为服务账号分配管理员策略
IamUser:aaaaaaaanno#policies@IamPolicy:sys/admin
```

## 2. 全局组配置

```text
# 全局组配置（已注释，未启用）
#IamGroup:sys/allUsers#members@IamUser:xxx    # 所有用户组
#IamGroup:sys/admin#members@IamUser:xxx       # 管理员组
#IamGroup:sys/root#members@IamUser:xxx        # 根用户组
#IamGroup:sys/inspector#members@IamUser:xxx   # 检查员组
#IamGroup:sys/kam#members@IamUser:xxx         # KAM 组
#IamGroup:sys/pm#members@IamUser:xxx          # 项目经理组
```

## 3. 全局角色配置

```text
# 全局角色配置
# 根角色继承管理员权限
IamRole:root#perms@IamRole:admin#perms
IamRole:root#policies@IamPolicy:sys/admin

# 服务角色继承管理员权限
IamRole:service#perms@IamRole:admin#perms
IamRole:service#policies@IamPolicy:sys/admin

# 检查员角色继承查看者权限
IamRole:inspector#perms@IamRole:viewer#perms
IamRole:inspector#policies@IamPolicy:sys/admin

# KAM 角色权限
IamRole:kam#perms@IamRole:iam.editor#perms    # 继承 IAM 编辑权限
IamRole:kam#perms@IamRole:anno.kam#perms      # 继承标注 KAM 权限
#IamRole:kam#policies@IamPolicy:sys/admin     # 管理员策略（已注释）

# 项目经理角色权限
IamRole:pm#perms@IamRole:iam.viewer#perms     # 继承 IAM 查看权限
IamRole:pm#<EMAIL>              # 部署权限
#IamRole:pm#perms@IamRole:pm#perms@IamRole:anno.editor#perms   # 标注编辑权限（已注释）
#IamRole:pm#policies@IamPolicy:sys/admin      # 管理员策略（已注释）

# 管理员角色权限
IamRole:admin#perms@IamRole:iam.owner#perms   # 继承 IAM 所有者权限
IamRole:admin#perms@IamRole:anno.owner#perms  # 继承标注所有者权限
IamRole:admin#perms@IamRole:anno.regulator#perms  # 继承标注监管者权限
IamRole:admin#perms@IamRole:annofeed.owner#perms  # 继承标注数据所有者权限
IamRole:admin#perms@IamRole:fe.owner#perms    # 继承前端所有者权限
IamRole:admin#policies@IamPolicy:sys/admin    # 管理员策略

# 子管理员角色权限
IamRole:subAdmin#perms@IamRole:iam.owner#perms    # 继承 IAM 所有者权限
IamRole:subAdmin#perms@IamRole:anno.owner#perms   # 继承标注所有者权限
IamRole:subAdmin#perms@IamRole:anno.regulator#perms  # 继承标注监管者权限
IamRole:subAdmin#perms@IamRole:annofeed.owner#perms  # 继承标注数据所有者权限
IamRole:subAdmin#perms@IamRole:fe.owner#perms    # 继承前端所有者权限
IamRole:subAdmin#policies@IamPolicy:sys/admin    # 管理员策略

# 查看者角色权限
IamRole:viewer#perms@IamRole:iam.viewer#perms    # 继承 IAM 查看权限
IamRole:viewer#perms@IamRole:anno.viewer#perms   # 继承标注查看权限
IamRole:viewer#perms@IamRole:annofeed.viewer#perms  # 继承标注数据查看权限
IamRole:viewer#perms@IamRole:fe.viewer#perms    # 继承前端查看权限
IamRole:viewer#policies@IamPolicy:sys/admin     # 管理员策略
```

## 4. 全局策略配置

```text
# 全局策略配置
# 管理员策略
IamPolicy:sys/admin#roles@IamRole:admin          # 绑定管理员角色
IamPolicy:sys/admin#users@IamGroup:sys/admin#members  # 绑定管理员组成员
IamPolicy:sys/admin#users@IamGroup:sys/service#members  # 绑定服务组成员
IamPolicy:sys/admin#users@IamGroup:sys/root#members  # 绑定根用户组成员

# 检查员策略
IamPolicy:sys/inspector#roles@IamRole:inspector  # 绑定检查员角色
IamPolicy:sys/inspector#users@IamGroup:sys/inspector#members  # 绑定检查员组成员

# 公共查看策略
IamPolicy:sys/publicView#roles@IamRole:viewer    # 绑定查看者角色
IamPolicy:sys/publicView#users@IamGroup:sys/allUsers#members  # 绑定所有用户组成员

# KAM 策略
IamPolicy:sys/kam#roles@IamRole:kam              # 绑定 KAM 角色
IamPolicy:sys/kam#users@IamGroup:sys/kam#members  # 绑定 KAM 组成员

# 项目经理策略
IamPolicy:sys/pm#roles@IamRole:pm                # 绑定项目经理角色
IamPolicy:sys/pm#users@IamGroup:sys/pm#members   # 绑定项目经理组成员
```

## 5. 团队角色配置

```text
# 团队角色配置
# 成员角色
IamRole:member#perms@IamRole:iam.viewer#perms    # 继承 IAM 查看权限
IamRole:member#perms@IamRole:anno.viewer#perms   # 继承标注查看权限
IamRole:member#perms@IamRole:annofeed.viewer#perms  # 继承标注数据查看权限
IamRole:member#policies@IamPolicy:sys/admin      # 管理员策略
IamRole:member#policies@IamPolicy:sys/publicView # 公共查看策略

# 管理者角色
IamRole:manager#perms@IamRole:iam.editor#perms   # 继承 IAM 编辑权限
IamRole:manager#perms@IamRole:anno.editor#perms  # 继承标注编辑权限
IamRole:manager#perms@IamRole:annofeed.editor#perms  # 继承标注数据编辑权限
IamRole:manager#policies@IamPolicy:sys/admin     # 管理员策略
IamRole:manager#policies@IamPolicy:sys/publicView # 公共查看策略

# 所有者角色
IamRole:owner#perms@IamRole:iam.owner#perms      # 继承 IAM 所有者权限
IamRole:owner#perms@IamRole:anno.owner#perms     # 继承标注所有者权限
IamRole:owner#perms@IamRole:annofeed.owner#perms # 继承标注数据所有者权限
IamRole:owner#policies@IamPolicy:sys/admin       # 管理员策略
IamRole:owner#policies@IamPolicy:sys/publicView  # 公共查看策略
```

## 6. IAM 角色和权限配置

```text
# IAM 角色和权限配置
# 角色查看者权限
IamRole:IamRole.viewer#<EMAIL>         # 获取角色权限
IamRole:IamRole.viewer#<EMAIL>        # 列出角色权限

# 角色编辑者权限
IamRole:IamRole.editor#perms@IamRole:IamRole.viewer#perms  # 继承查看者权限
IamRole:IamRole.editor#<EMAIL>      # 创建角色权限
IamRole:IamRole.editor#<EMAIL>      # 更新角色权限

# 角色所有者权限
IamRole:IamRole.owner#perms@IamRole:IamRole.editor#perms  # 继承编辑者权限
IamRole:IamRole.owner#<EMAIL>       # 删除角色权限
IamRole:IamRole.owner#<EMAIL>    # 获取策略权限
IamRole:IamRole.owner#<EMAIL>    # 设置策略权限
IamRole:IamRole.owner#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:IamRole.owner#policies@IamPolicy:sys/publicView  # 公共查看策略
```

## 7. 用户角色和权限配置

```text
# 用户角色和权限配置
# 用户查看者权限
IamRole:IamUser.viewer#<EMAIL>         # 获取用户权限
IamRole:IamUser.viewer#<EMAIL>        # 列出用户权限

# 用户编辑者权限
IamRole:IamUser.editor#perms@IamRole:IamUser.viewer#perms  # 继承查看者权限
IamRole:IamUser.editor#<EMAIL>      # 创建用户权限
IamRole:IamUser.editor#<EMAIL>      # 更新用户权限
IamRole:IamUser.editor#<EMAIL>  # 获取隐私权限
IamRole:IamUser.editor#<EMAIL>        # 统计权限

# 用户所有者权限
IamRole:IamUser.owner#perms@IamRole:IamUser.editor#perms  # 继承编辑者权限
IamRole:IamUser.owner#<EMAIL>       # 删除用户权限
IamRole:IamUser.owner#<EMAIL>    # 获取策略权限
IamRole:IamUser.owner#<EMAIL>    # 设置策略权限
IamRole:IamUser.owner#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:IamUser.owner#policies@IamPolicy:sys/publicView  # 公共查看策略
```

## 8. 组角色和权限配置

```text
# 组角色和权限配置
# 组查看者权限
IamRole:IamGroup.viewer#<EMAIL>       # 获取组权限
IamRole:IamGroup.viewer#<EMAIL>      # 列出组权限
IamRole:IamGroup.viewer#<EMAIL>  # 列出成员权限
IamRole:IamGroup.viewer#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:IamGroup.viewer#policies@IamPolicy:sys/publicView  # 公共查看策略

# 组编辑者权限
IamRole:IamGroup.editor#perms@IamRole:IamGroup.viewer#perms  # 继承查看者权限
IamRole:IamGroup.editor#<EMAIL>    # 创建组权限
IamRole:IamGroup.editor#<EMAIL>    # 更新组权限
IamRole:IamGroup.editor#<EMAIL> # 添加成员权限
IamRole:IamGroup.editor#<EMAIL>  # 删除成员权限
IamRole:IamGroup.editor#<EMAIL>  # 设置成员角色权限
IamRole:IamGroup.editor#<EMAIL>      # 统计权限
IamRole:IamGroup.editor#<EMAIL>    # 部署权限
IamRole:IamGroup.editor#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:IamGroup.editor#policies@IamPolicy:sys/publicView  # 公共查看策略

# 组所有者权限
IamRole:IamGroup.owner#perms@IamRole:IamGroup.editor#perms  # 继承编辑者权限
IamRole:IamGroup.owner#<EMAIL>     # 删除组权限
IamRole:IamGroup.owner#<EMAIL>  # 获取策略权限
IamRole:IamGroup.owner#<EMAIL>  # 设置策略权限
IamRole:IamGroup.owner#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:IamGroup.owner#policies@IamPolicy:sys/publicView  # 公共查看策略
```

## 9. IAM 权限继承关系

```text
# IAM 权限继承关系
# IAM 查看者权限
IamRole:iam.viewer#perms@IamRole:IamRole.viewer#perms  # 继承角色查看者权限
IamRole:iam.viewer#perms@IamRole:IamUser.viewer#perms  # 继承用户查看者权限
IamRole:iam.viewer#perms@IamRole:IamGroup.viewer#perms # 继承组查看者权限
IamRole:iam.viewer#policies@IamPolicy:sys/admin       # 管理员策略
IamRole:iam.viewer#policies@IamPolicy:sys/publicView  # 公共查看策略

# IAM 编辑者权限
IamRole:iam.editor#perms@IamRole:IamRole.editor#perms  # 继承角色编辑者权限
IamRole:iam.editor#perms@IamRole:IamUser.editor#perms  # 继承用户编辑者权限
IamRole:iam.editor#perms@IamRole:IamGroup.editor#perms # 继承组编辑者权限
IamRole:iam.editor#policies@IamPolicy:sys/admin       # 管理员策略
IamRole:iam.editor#policies@IamPolicy:sys/publicView  # 公共查看策略

# IAM 所有者权限
IamRole:iam.owner#perms@IamRole:IamRole.owner#perms    # 继承角色所有者权限
IamRole:iam.owner#perms@IamRole:IamUser.owner#perms    # 继承用户所有者权限
IamRole:iam.owner#perms@IamRole:IamGroup.owner#perms   # 继承组所有者权限
IamRole:iam.owner#policies@IamPolicy:sys/admin         # 管理员策略
IamRole:iam.owner#policies@IamPolicy:sys/publicView    # 公共查看策略
```

## 10. 标注系统权限配置

```text
# 标注系统权限配置
# 标注批次查看者权限
IamRole:AnnoLot.viewer#<EMAIL>        # 获取批次权限
IamRole:AnnoLot.viewer#<EMAIL>       # 列出批次权限
IamRole:AnnoLot.viewer#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:AnnoLot.viewer#policies@IamPolicy:sys/publicView  # 公共查看策略

# 标注批次编辑者权限
IamRole:AnnoLot.editor#perms@IamRole:AnnoLot.viewer#perms  # 继承查看者权限
IamRole:AnnoLot.editor#<EMAIL>     # 创建批次权限
IamRole:AnnoLot.editor#<EMAIL>     # 更新批次权限
IamRole:AnnoLot.editor#<EMAIL>    # 列出任务权限
IamRole:AnnoLot.editor#<EMAIL> # 导出标注权限
IamRole:AnnoLot.editor#<EMAIL>       # 统计权限
IamRole:AnnoLot.editor#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:AnnoLot.editor#policies@IamPolicy:sys/publicView  # 公共查看策略

# 标注批次所有者权限
IamRole:AnnoLot.owner#perms@IamRole:AnnoLot.editor#perms  # 继承编辑者权限
IamRole:AnnoLot.owner#<EMAIL>      # 删除批次权限
IamRole:AnnoLot.owner#<EMAIL>   # 获取策略权限
IamRole:AnnoLot.owner#<EMAIL>   # 设置策略权限
IamRole:AnnoLot.owner#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:AnnoLot.owner#policies@IamPolicy:sys/publicView  # 公共查看策略

# 标注批次执行者权限
IamRole:AnnoLot.executor#<EMAIL>  # 分配执行团队权限
IamRole:AnnoLot.executor#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:AnnoLot.executor#policies@IamPolicy:sys/publicView  # 公共查看策略
```

## 11. 标注任务权限配置

```text
# 标注任务权限配置
# 标注任务查看者权限
IamRole:AnnoJob.viewer#<EMAIL>        # 获取批次权限
IamRole:AnnoJob.viewer#<EMAIL>        # 获取任务权限
IamRole:AnnoJob.viewer#<EMAIL>       # 列出任务权限
IamRole:AnnoJob.viewer#<EMAIL>        # 查看日志权限
IamRole:AnnoJob.viewer#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:AnnoJob.viewer#policies@IamPolicy:sys/publicView  # 公共查看策略

# 标注任务编辑者权限
IamRole:AnnoJob.editor#perms@IamRole:AnnoJob.viewer#perms  # 继承查看者权限
IamRole:AnnoJob.editor#<EMAIL>     # 创建任务权限
IamRole:AnnoJob.editor#<EMAIL>     # 更新任务权限
IamRole:AnnoJob.editor#<EMAIL>       # 统计权限
IamRole:AnnoJob.editor#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:AnnoJob.editor#policies@IamPolicy:sys/publicView  # 公共查看策略

# 标注任务所有者权限
IamRole:AnnoJob.owner#perms@IamRole:AnnoJob.editor#perms  # 继承编辑者权限
IamRole:AnnoJob.owner#<EMAIL>      # 删除任务权限
IamRole:AnnoJob.owner#<EMAIL>   # 获取策略权限
IamRole:AnnoJob.owner#<EMAIL>   # 设置策略权限
IamRole:AnnoJob.owner#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:AnnoJob.owner#policies@IamPolicy:sys/publicView  # 公共查看策略

# 标注任务监管者权限
IamRole:AnnoJob.regulator#<EMAIL>  # 分配任务权限
IamRole:AnnoJob.regulator#<EMAIL>  # 拒绝任务权限
IamRole:AnnoJob.regulator#<EMAIL>     # 查看日志权限
IamRole:AnnoJob.regulator#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:AnnoJob.regulator#policies@IamPolicy:sys/publicView  # 公共查看策略
```

## 12. 标注订单权限配置

```text
# 标注订单权限配置
# 标注订单查看者权限
IamRole:AnnoOrder.viewer#<EMAIL>    # 获取订单权限
IamRole:AnnoOrder.viewer#<EMAIL>   # 列出订单权限
IamRole:AnnoOrder.viewer#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:AnnoOrder.viewer#policies@IamPolicy:sys/publicView  # 公共查看策略

# 标注订单编辑者权限
IamRole:AnnoOrder.editor#perms@IamRole:AnnoOrder.viewer#perms  # 继承查看者权限
IamRole:AnnoOrder.editor#<EMAIL>  # 创建订单权限
IamRole:AnnoOrder.editor#<EMAIL>  # 更新订单权限
IamRole:AnnoOrder.editor#<EMAIL>  # 导出标注权限
IamRole:AnnoOrder.editor#<EMAIL>    # 统计权限
IamRole:AnnoOrder.editor#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:AnnoOrder.editor#policies@IamPolicy:sys/publicView  # 公共查看策略

# 标注订单所有者权限
IamRole:AnnoOrder.owner#perms@IamRole:AnnoOrder.editor#perms  # 继承编辑者权限
IamRole:AnnoOrder.owner#<EMAIL>   # 删除订单权限
IamRole:AnnoOrder.owner#<EMAIL>  # 获取策略权限
IamRole:AnnoOrder.owner#<EMAIL>  # 设置策略权限
IamRole:AnnoOrder.owner#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:AnnoOrder.owner#policies@IamPolicy:sys/publicView  # 公共查看策略
```

## 13. 标注系统权限继承关系

```text
# 标注系统权限继承关系
# 标注查看者权限
IamRole:anno.viewer#perms@IamRole:AnnoLot.viewer#perms  # 继承批次查看者权限
IamRole:anno.viewer#perms@IamRole:AnnoJob.viewer#perms  # 继承任务查看者权限
IamRole:anno.viewer#perms@IamRole:AnnoOrder.viewer#perms  # 继承订单查看者权限
IamRole:anno.viewer#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:anno.viewer#policies@IamPolicy:sys/publicView  # 公共查看策略

# 标注编辑者权限
IamRole:anno.editor#perms@IamRole:AnnoLot.editor#perms  # 继承批次编辑者权限
IamRole:anno.editor#perms@IamRole:AnnoJob.editor#perms  # 继承任务编辑者权限
IamRole:anno.editor#perms@IamRole:AnnoOrder.editor#perms  # 继承订单编辑者权限
IamRole:anno.editor#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:anno.editor#policies@IamPolicy:sys/publicView  # 公共查看策略

# 标注所有者权限
IamRole:anno.owner#perms@IamRole:AnnoLot.owner#perms  # 继承批次所有者权限
IamRole:anno.owner#perms@IamRole:AnnoJob.owner#perms  # 继承任务所有者权限
IamRole:anno.owner#perms@IamRole:AnnoOrder.owner#perms  # 继承订单所有者权限
IamRole:anno.owner#policies@IamPolicy:sys/admin  # 管理员策略
IamRole:anno.owner#policies@IamPolicy:sys/publicView  # 公共查看策略
``` 