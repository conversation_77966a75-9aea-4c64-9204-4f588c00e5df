{{- if .Values.service.write.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "keto.fullname" . }}-write
  {{- if .Release.Namespace }}
  namespace: {{ .Release.Namespace }}
  {{- end }}
  labels:
    app.kubernetes.io/component: write
    {{- include "keto.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.write.type }}
  ports:
    - port: {{ .Values.service.write.port }}
      targetPort: {{ .Values.service.write.name }}
      protocol: TCP
      name: {{ .Values.service.write.name }}
  selector:
    app.kubernetes.io/name: {{ include "keto.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
{{ end }}