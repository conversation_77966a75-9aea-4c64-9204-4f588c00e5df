import os
import sys
import cv2
import numpy as np
import shutil
from PIL import Image
import imagehash


def scantree(path, is_file_only=True):
    for entry in os.scandir(path):
        is_dir = entry.is_dir(follow_symlinks=False)

        if is_dir:
            if is_file_only:
                yield from scantree(entry.path)
            else:
                yield entry
        else:
            if is_file_only:
                yield entry


def get_file_by_extension(directory, extension=".json"):
    return [f for f in scantree(directory) if f.name.endswith(extension) and not f.name.startswith(".")]


def check_dir(directory, create_if_not_exist=True, delete_if_exist=True):
    if os.path.exists(directory) and delete_if_exist:
        # print(f"Warring:{directory} existed, removed!")
        shutil.rmtree(directory)
    if not os.path.exists(directory) and create_if_not_exist:
        os.makedirs(directory)


def rename_mask(mask_dir, yzgt_dir):
    mask_imgs = {i.name for i in get_file_by_extension(mask_dir, ".jpg")}
    yzgt_imgs = {i.name for i in get_file_by_extension(yzgt_dir, ".jpg")}
    print('origin: mask_imgs length:', len(mask_imgs), 'yzgt_imgs length:', len(yzgt_imgs))
    # 获取不重复部分
    repeat_imgs = mask_imgs.intersection(yzgt_imgs)
    mask_imgs = mask_imgs - repeat_imgs
    yzgt_imgs = yzgt_imgs - repeat_imgs
    print('diff: mask_imgs length:', len(mask_imgs), 'yzgt_imgs length:', len(yzgt_imgs))
    mask_hash = {}
    yzgt_hash = {}
    for mask_img in mask_imgs:
        image = Image.open(os.path.join(mask_dir, mask_img))
        mask_hash[mask_img] = imagehash.phash(image)
    for yzgt_img in yzgt_imgs:
        image = Image.open(os.path.join(yzgt_dir, yzgt_img))
        yzgt_hash[yzgt_img] = imagehash.phash(image)

    for mask_key, mask_value in mask_hash.items():
        min = 100
        for yzgt_key, yzgt_value in yzgt_hash.items():
            if (mask_value - yzgt_value) < min:
                min = mask_value - yzgt_value
                mask_path = os.path.join(mask_dir, mask_key)
                yzgt_path = os.path.join(yzgt_dir, yzgt_key)
        if min <= 10:
            try:
                os.rename(mask_path, os.path.join(mask_dir, os.path.basename(yzgt_path)))
                print(f"rename {mask_path} to {os.path.join(mask_dir, os.path.basename(yzgt_path))}")
            except Exception as e:
                print("rename error", e)


def read_cv_image(img_path):
    if not os.path.exists(img_path):
        return None
    img = Image.open(img_path)
    img_cv2 = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
    return img_cv2


def write_cv_image(img_path, img_cv2_bgr):
    img_cv2_rgb = cv2.cvtColor(img_cv2_bgr, cv2.COLOR_BGR2RGB)
    img_pil = Image.fromarray(img_cv2_rgb)
    img_pil.save(img_path)


def run(input_dir):
    root_dir = input_dir
    src_dir = os.path.join(root_dir, "src")
    old_mask_dir = os.path.join(root_dir, "原遮盖图")
    new_mask_dir = os.path.join(root_dir, "mask")
    save_dir = os.path.join(root_dir, "拼接")
    final_save_dir = os.path.join(root_dir, "合并")
    check_dir(final_save_dir)
    rename_mask(new_mask_dir, old_mask_dir)
    name_list = os.listdir(src_dir)
    for name in name_list:
        if not name.lower().endswith((".jpg", ".jpeg", ".png")):
            continue
        src_path = os.path.join(src_dir, name)
        save_path = os.path.join(save_dir, name)
        if os.path.exists(save_path):
            continue
        if "_src_" in name:
            mask_name = name.replace("_src_", "_mask_")
        elif "_images_" in name:
            mask_name = name.replace("_images_", "_masks_")
        else:
            print("图片命名格式有误：", name)

        #检查原图和原遮盖图名称是否匹配
        if os.path.exists(os.path.join(old_mask_dir, name)):
            old_mask_path = os.path.join(old_mask_dir, name)
        elif os.path.exists(os.path.join(old_mask_dir, mask_name)):
            old_mask_path = os.path.join(old_mask_dir, mask_name)
        else:
            print(f"not old mask {name}")
            continue

        # 检查原图和mask图像名称是否匹配
        if os.path.exists(os.path.join(new_mask_dir, name)):
            new_mask_path = os.path.join(new_mask_dir, name)
        elif os.path.exists(os.path.join(new_mask_dir, mask_name)):
            new_mask_path = os.path.join(new_mask_dir, mask_name)
        else:
            print(f"not new mask {name}")
            continue

        src_img = read_cv_image(src_path)
        old_mask = read_cv_image(old_mask_path)
        new_mask = read_cv_image(new_mask_path)
        if new_mask is None :
            print("无法打开：", new_mask_path)
            continue
        if old_mask is None:
            print("无法打开：", old_mask_path)
            continue
        h, w, _ = src_img.shape
        old_mask = cv2.resize(old_mask, (w, h))
        new_mask = cv2.resize(new_mask, (w, h))
        hsv_image = cv2.cvtColor(old_mask, cv2.COLOR_BGR2HSV)
        h_old1 = cv2.addWeighted(new_mask, 0.5, hsv_image, 0.5, 0)

        row1 = np.hstack((new_mask, h_old1))  #mask图与合并图
        row2 = np.hstack((old_mask, src_img))  #原遮盖图与原图
        final_image = np.vstack((row1, row2))
        write_cv_image(os.path.join(final_save_dir, name), final_image)  #输出合并后的结果
        write_cv_image(save_path, h_old1)  #输出拼接后的结果


if __name__ == "__main__":
    input_dir = sys.argv[1]
    if os.name == 'posix':
        os.system(f"chmod -R u+w {input_dir}")
    run(input_dir)
