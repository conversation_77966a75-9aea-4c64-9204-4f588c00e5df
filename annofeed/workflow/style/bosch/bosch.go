package bosch

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"path/filepath"
	"strings"

	"annofeed/internal/biz"
	"annofeed/workflow/common"
	"gitlab.rp.konvery.work/platform/apis/annofeed/v1"
	"gitlab.rp.konvery.work/platform/pkg/kfs"
	"gitlab.rp.konvery.work/platform/pkg/kmath"

	"github.com/go-kratos/kratos/v2/log"
)

const (
	ParamsDir = "params" // 相机雷达参数
)

func Transform(ctx context.Context, data *biz.Data, dir string, startIdx int, hb common.Heartbeat) (dataType string, err error) {
	newdir := dir + "-1"
	o := newXparser(dir, newdir)
	err = kfs.IterateDir(dir, func(entry os.DirEntry, subdir string, depth int) error {
		// skip directories
		if entry.IsDir() {
			return nil
		}
		hb(ctx)
		return o.parseFile(ctx, subdir, entry.Name())
	})
	if err != nil {
		return "", err
	}

	if err = o.closeFrame(ctx); err != nil {
		return "", fmt.Errorf("failed to close last frame when handling %v: %w", dir, err)
	}

	tmp := dir + "-tmp"
	if err = os.Rename(dir, tmp); err != nil {
		return "", fmt.Errorf("failed to backup %s to %s: %w", dir, tmp, err)
	}
	if err = os.Rename(newdir, dir); err != nil {
		os.Rename(tmp, dir)
		return "", fmt.Errorf("failed to rename %s to %s: %w", newdir, dir, err)
	}
	if err = os.RemoveAll(tmp); err != nil {
		// fmt.Errorf("failed to delete backup dir %s: %w", tmp, err)
	}
	return biz.DataTypeFusion3D, nil
}

type fileParser func(ctx context.Context, subdir, name string) (match bool, err error)

type xparser struct {
	params     *Params
	elemParam  *biz.ElemParamV1
	frmIdx     int
	fileIdx    int
	curSubdir  string
	oldBasedir string
	newBasedir string
	lidarNames map[string]bool
	parsers    []fileParser
	log        *log.Helper

	createNewClip bool
}

func newXparser(olddir, newdir string) *xparser {
	o := &xparser{
		frmIdx:     -1,
		oldBasedir: olddir,
		newBasedir: newdir,
		// log: log.NewHelper(opts.Logger),
		lidarNames: make(map[string]bool),
	}

	o.initParsers()
	return o
}

func (o *xparser) initParsers() {
	o.parsers = []fileParser{
		o.ignoreFiles,
		o.parseImageFile,
		o.parsePCDFile,
		// o.defaultParser,
	}
}

func (o *xparser) parseFile(ctx context.Context, subdir, name string) (err error) {
	if err != nil {
		return fmt.Errorf("failed to load params: %w", err)
	}

	for _, p := range o.parsers {
		match, err := p(ctx, subdir, name)
		if match {
			return err
		}
	}
	return fmt.Errorf("unparsed file: %v", filepath.Join(subdir, name))
}

func (o *xparser) ignoreFiles(ctx context.Context, subdir, name string) (match bool, err error) {
	match = strings.HasPrefix(filepath.Base(name), ".") ||
		strings.HasSuffix(name, ".yaml")
	// o.log.WithContext(ctx).Warnf("ignore a file %v", name)
	return
}

func (o *xparser) defaultParser(ctx context.Context, subdir, name string) (match bool, err error) {
	// o.log.WithContext(ctx).Warnf("ignore an unknown file %v", name)
	return true, nil
}

func (o *xparser) loadParams(subdir string) error {
	fpath := filepath.Join(o.oldBasedir, subdir, "..", ParamsDir)
	params, err := ParseParams(fpath)
	if err != nil {
		return fmt.Errorf("failed to read params file %v: %w", fpath, err)
	}

	o.params = params
	return nil
}

func (o *xparser) newFilePath(dir, name string) string {
	return filepath.Join(o.newBasedir, dir, name)
}

// clipName returns the father directory name of the current elem
func (o *xparser) clipName() string {
	return path.Dir(o.curSubdir)
}

func (o *xparser) elemBaseName() string {
	return path.Base(o.curSubdir)
}

func (o *xparser) parseImageFile(ctx context.Context, subdir, name string) (match bool, err error) {
	if !common.IsSupportedImageFormat(name) {
		return false, nil
	}

	if subdir != o.curSubdir {
		err = o.startFrame(ctx, subdir)
		if err != nil {
			return true, err
		}
	}

	// move file
	// name is like 20230502130234.030957_SurCam01.jpeg, we need to get the camera name
	newPath := ""
	bareName := kfs.FileBareName(name)
	names := strings.Split(bareName, "_")
	camName := names[1]
	if o.createNewClip {
		newClip := bareName[len(bareName)-2:] // use the last 2 digits as the new clip name
		newName := filepath.Join(newClip, o.elemBaseName(), camName+filepath.Ext(name))
		newPath = o.newFilePath(o.clipName(), newName)
	} else {
		newPath = o.newFilePath(o.curSubdir, camName+filepath.Ext(name))
	}

	o.fileIdx++
	err = o.moveFile(ctx, filepath.Join(subdir, name), newPath)
	return true, err
}

func (o *xparser) parsePCDFile(ctx context.Context, subdir, name string) (match bool, err error) {
	if common.FileFormat(name) != common.RawdataFormatPCD {
		return false, nil
	}

	if subdir != o.curSubdir {
		err = o.startFrame(ctx, subdir)
		if err != nil {
			return true, err
		}
	}

	// move file
	newPath := ""
	if o.createNewClip {
		// name is like 20230502130233.958308_BlindLidar01.pcd
		bareName := kfs.FileBareName(name)
		newClip := bareName[len(bareName)-2:] // use the last 2 digits as the new clip name
		newName := filepath.Join(newClip, o.elemBaseName(), common.LidarPCD)
		newPath = o.newFilePath(o.clipName(), newName)
	} else {
		newPath = o.newFilePath(o.curSubdir, common.LidarPCD)
	}

	o.fileIdx++
	err = o.moveFile(ctx, filepath.Join(subdir, name), newPath)
	return true, err
}

func (o *xparser) moveFile(ctx context.Context, origPathName, newPath string) error {
	// duplicate the image file instead of moving it, otherwise we will probably miss some camera params
	// when retrying the process if it is interrupted due to a temporary error
	old := filepath.Join(o.oldBasedir, origPathName)
	newPathDir := filepath.Dir(newPath)
	if err := os.MkdirAll(newPathDir, 0755); err != nil {
		return err
	}
	err := os.Link(old, newPath)
	if err != nil {
		return fmt.Errorf("failed to link file (%v -> %v): %w", newPath, old, err)
	}

	// record its original name
	err = os.WriteFile(newPath+common.OrigExt, []byte(origPathName), 0644)
	if err != nil {
		return fmt.Errorf("failed to write file %v: %w", newPath+common.OrigExt, err)
	}

	return nil
}

func (o *xparser) startFrame(ctx context.Context, subdir string) error {
	err := o.closeFrame(ctx)
	if err != nil {
		return fmt.Errorf("failed to close frame: %w", err)
	}

	err = o.loadParams(subdir)
	if err != nil {
		return err
	}

	o.fileIdx = 0
	o.frmIdx++
	o.curSubdir = subdir

	// find lidar count
	lidarCount := 0
	entries, err := os.ReadDir(filepath.Join(o.oldBasedir, subdir))
	if err != nil {
		return fmt.Errorf("failed to read dir %v: %w", subdir, err)
	}
	for _, e := range entries {
		if strings.HasSuffix(strings.ToLower(e.Name()), ".pcd") {
			lidarCount++
			bareName := kfs.FileBareName(e.Name())
			lidarName := strings.Split(bareName, "_")[1]
			o.lidarNames[lidarName] = true
			if lidarCount > 1 {
				o.createNewClip = true
			}
		}
	}

	// make frame directory
	// fpath := o.newFilePath("", "")
	// err = os.MkdirAll(fpath, 0755)
	// if err != nil {
	//	return fmt.Errorf("failed to create directory %v: %w", fpath, err)
	// }

	// err = o.loadParams()
	// if err != nil {
	// 	return fmt.Errorf("failed to load params: %w", err)
	// }
	o.elemParam = &biz.ElemParamV1{
		Cameras: map[string]*annofeed.CameraParam{},
	}
	return nil
}

func (o *xparser) closeFrame(ctx context.Context) error {
	if o.elemParam == nil {
		return nil
	}

	toPosQuaternion := func(data []float64) []float64 {
		posQuaternion := kmath.MatrixToPose(data, true)
		return posQuaternion[:]
	}

	for camera := range o.params.Cameras {
		newClip := camera[len(camera)-2:] // use the last two digits as the new clip name
		lidar := ""
		for lidarName := range o.lidarNames { // find the corresponding lidar
			if lidarName[len(lidarName)-2:] == newClip {
				lidar = lidarName
				break
			}
		}
		// lidar is not found if lidarName and camera do not have the same suffix (the last 2 digits)
		// we need to find the corresponding lidar in the following block
		if lidar == "" {
			for lidarName := range o.params.Lidars {
				if o.lidarNames[lidarName] {
					lidar = lidarName
					break
				}
			}
		}

		trans := o.params.GetImageTransforms(camera, lidar)
		pose := toPosQuaternion(trans[0].Data)
		newElemParam := &biz.ElemParamV1{
			Lidars: map[string]*annofeed.LidarParam{
				common.KeyLidar: {Pose: &annofeed.Pose{Pose: pose}},
			},
			Cameras: map[string]*annofeed.CameraParam{
				camera: {Transforms: trans},
			},
		}
		data, err := json.Marshal(newElemParam)
		if err != nil {
			return fmt.Errorf("failed to marshal elemParam: %w", err)
		}

		filePath := ""
		if o.createNewClip {
			filePath = o.newFilePath(o.clipName(), filepath.Join(newClip, o.elemBaseName(), common.ParamsFile))
		} else {
			filePath = o.newFilePath(o.curSubdir, common.ParamsFile)
		}
		err = os.WriteFile(filePath, data, 0644)
		if err != nil {
			return fmt.Errorf("failed to write file %v: %w", filePath, err)
		}
	}

	o.lidarNames = make(map[string]bool)

	return nil
}
