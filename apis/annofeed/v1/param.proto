syntax = "proto3";

package annofeed.v1;

import "anno/v1/elemanno.proto";
import "validate/validate.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/annofeed/v1;annofeed";
option java_multiple_files = true;
option java_package = "annofeed.v1";

message CoordSys {
  message Type {
    enum Enum {
      unspecified = 0;
      // Universal Transverse Mercator
      utm = 1;
      // World Geodetic System (Latitude, Longitude, Altitude)
      wgs84 = 2;

      // vehicle coordinate system, direction of XYZ: FLU(Forwards, Leftwards, Upwards)
      iso8855 = 10;
      // vehicle coordinate system, direction of XYZ: RFU(Rightwards, Forwards, Upwards)
      imu = 11;
      // vehicle coordinate system, direction of XYZ: FRD(Forwards, Rightwards, Downwards)
      sae = 12;
    }
  }

  string name = 1;
  Type.Enum type = 2 [(validate.rules).enum = {defined_only: true}];
  // name of parent coordinate system
  string parent = 3;
  // [x,y,z,rx,ry,rz,rw] in parent coordinate system; if it is empty, it is [0,0,0,0,0,0,1]
  repeated double origin = 4;
}

message Pose {
  // coordinate system
  string coord_sys = 1;
  // location and rotation in the format [x,y,z,rx,ry,rz,rw]
  repeated double pose = 2;
}

message VehicleParam {
  Pose pose = 1;
  // location and rotation varies according to frame index: [x1,y1,z1,rx1,ry1,rz1,rw1, x2,y2,z2,rx2,ry2,rz2,rw2, ...]
  repeated double vary_pose = 3;
}

message LidarParam {
  Pose pose = 1;
  repeated anno.v1.RawdataParam transforms = 2;
  string name = 3;
}

message CameraParam {
  message Dimensions {
    int32 width = 1;
    int32 height = 2;
  }

  Pose pose = 1;
  // intrinsic params, distortion params and other transformations
  repeated anno.v1.RawdataParam transforms = 2;
  string name = 3;
  Dimensions dimensions = 4;
  // used to display in anno platform if not empty, preferably in local language
  string title = 5;
}

message ParamsFile {
  VehicleParam vehicle = 1;
  map<string, LidarParam> lidars = 2;
  map<string, CameraParam> cameras = 3;
  map<string, CoordSys> coord_systems = 4;
  // null, or version = "v1"
  ParamFileMeta meta = 5;
}

message LidarParamV2 {
  // point cloud viewpoint (in the pointcloud's coordinate system), in the form of position and quaternion ([x,y,z,qx,qy,qz,qw])
  repeated double viewpoint = 1;
  // origin point of the point cloud in world coordinate system when point cloud is not in world coordinate system,
  // either in the form of position and quaternion ([x,y,z,qx,qy,qz,qw]), or
  // in the form of 4x4 row-major matrix ([r00,r01,r02,r03,...,r30,r31,r32,r33]).
  // In the 1st format, it is the pose of the point cloud's origin point in world coordinate system;
  // in the 2nd format, it is a matrix to convert point cloud into world coordinate system.
  // <2nd format> = to_matrix(<1st format>)
  repeated double pose = 2;
  // UNIX timestamp in seconds
  double timestamp = 3;
  // transforms the point cloud into lidar coordinate system when the point cloud is not in lidar coordinate system,
  // either in the form of position and quaternion ([x,y,z,qx,qy,qz,qw]), or
  // in the form of 4x4 row-major matrix ([r00,r01,r02,r03,...,r30,r31,r32,r33]).
  // In the 1st format, it is the position of the lidar in the point cloud's coordinate system;
  // in the 2nd format, it is a matrix to convert point cloud into lidar coordinate system.
  // <2nd format> = inverse(to_matrix(<1st format>))
  repeated double transform = 4;
}

message CameraParamV2 {
  // name of the camera
  string name = 1;
  // extrinsic, either in the form of position and quaternion ([x,y,z,qx,qy,qz,qw]), or
  // in the form of 4x4 row-major matrix ([r00,r01,r02,r03,...,r30,r31,r32,r33]).
  // In the 1st format, it is the pose of the camera in the point cloud's coordinate system;
  // in the 2nd format, it is a matrix to convert point cloud into camera's coordinate system.
  // <2nd format> = inverse(to_matrix(<1st format>))
  repeated double extrinsic = 2;
  // intrinsic, either in the form of [fx,fy,cx,cy], or
  // in the form of 3x3 row-major matrix ([fx,0,cx,0,fy,cy,0,0,1])
  repeated double intrinsic = 3;
  // distortion params: distortion_type,k1,k2,...
  repeated double distortion = 4;
  // used to display in anno platform if not empty, preferably in local language
  string title = 5;
}

message ParamFileMeta {
  // version of the param file schema
  string version = 1;
}

message ParamFileV2 {
  // version = "v2"
  ParamFileMeta meta = 1;
  // define elem param
  LidarParamV2 lidar = 2;
  // define clip param
  repeated LidarParamV2 lidars = 3;
  map<string, CameraParamV2> cameras = 4;
}
