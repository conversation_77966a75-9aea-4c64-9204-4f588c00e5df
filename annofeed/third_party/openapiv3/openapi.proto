// Copyright 2020 Google LLC. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// THIS FILE IS AUTOMATICALLY GENERATED.

syntax = "proto3";

package openapi.v3;

import "google/protobuf/any.proto";

// This option lets the proto compiler generate Java code inside the package
// name (see below) instead of inside an outer class. It creates a simpler
// developer experience by reducing one-level of name nesting and be
// consistent with most programming languages that don't support outer classes.
option java_multiple_files = true;

// The Java outer classname should be the filename in UpperCamelCase. This
// class is only used to hold proto descriptor, so developers don't need to
// work with it directly.
option java_outer_classname = "OpenAPIProto";

// The Java package name must be proto package name with proper prefix.
option java_package = "org.openapi_v3";

// A reasonable prefix for the Objective-C symbols generated from the package.
// It should at a minimum be 3 characters long, all uppercase, and convention
// is to use an abbreviation of the package name. Something short, but
// hopefully unique enough to not conflict with things that may come along in
// the future. 'GPB' is reserved for the protocol buffer implementation itself.
option objc_class_prefix = "OAS";

// The Go package name.
option go_package = "github.com/google/gnostic/openapiv3;openapi_v3";

message AdditionalPropertiesItem {
  oneof oneof {
    SchemaOrReference schema_or_reference = 1;
    bool boolean = 2;
  }
}

message Any {
  google.protobuf.Any value = 1;
  string yaml = 2;
}

message AnyOrExpression {
  oneof oneof {
    Any any = 1;
    Expression expression = 2;
  }
}

// A map of possible out-of band callbacks related to the parent operation. Each value in the map is a Path Item Object that describes a set of requests that may be initiated by the API provider and the expected responses. The key value used to identify the callback object is an expression, evaluated at runtime, that identifies a URL to use for the callback operation.
message Callback {
  repeated NamedPathItem path = 1;
  repeated NamedAny specification_extension = 2;
}

message CallbackOrReference {
  oneof oneof {
    Callback callback = 1;
    Reference reference = 2;
  }
}

message CallbacksOrReferences {
  repeated NamedCallbackOrReference additional_properties = 1;
}

// Holds a set of reusable objects for different aspects of the OAS. All objects defined within the components object will have no effect on the API unless they are explicitly referenced from properties outside the components object.
message Components {
  SchemasOrReferences schemas = 1;
  ResponsesOrReferences responses = 2;
  ParametersOrReferences parameters = 3;
  ExamplesOrReferences examples = 4;
  RequestBodiesOrReferences request_bodies = 5;
  HeadersOrReferences headers = 6;
  SecuritySchemesOrReferences security_schemes = 7;
  LinksOrReferences links = 8;
  CallbacksOrReferences callbacks = 9;
  repeated NamedAny specification_extension = 10;
}

// Contact information for the exposed API.
message Contact {
  string name = 1;
  string url = 2;
  string email = 3;
  repeated NamedAny specification_extension = 4;
}

message DefaultType {
  oneof oneof {
    double number = 1;
    bool boolean = 2;
    string string = 3;
  }
}

// When request bodies or response payloads may be one of a number of different schemas, a `discriminator` object can be used to aid in serialization, deserialization, and validation.  The discriminator is a specific object in a schema which is used to inform the consumer of the specification of an alternative schema based on the value associated with it.  When using the discriminator, _inline_ schemas will not be considered.
message Discriminator {
  string property_name = 1;
  Strings mapping = 2;
  repeated NamedAny specification_extension = 3;
}

message Document {
  string openapi = 1;
  Info info = 2;
  repeated Server servers = 3;
  Paths paths = 4;
  Components components = 5;
  repeated SecurityRequirement security = 6;
  repeated Tag tags = 7;
  ExternalDocs external_docs = 8;
  repeated NamedAny specification_extension = 9;
}

// A single encoding definition applied to a single schema property.
message Encoding {
  string content_type = 1;
  HeadersOrReferences headers = 2;
  string style = 3;
  bool explode = 4;
  bool allow_reserved = 5;
  repeated NamedAny specification_extension = 6;
}

message Encodings {
  repeated NamedEncoding additional_properties = 1;
}

message Example {
  string summary = 1;
  string description = 2;
  Any value = 3;
  string external_value = 4;
  repeated NamedAny specification_extension = 5;
}

message ExampleOrReference {
  oneof oneof {
    Example example = 1;
    Reference reference = 2;
  }
}

message ExamplesOrReferences {
  repeated NamedExampleOrReference additional_properties = 1;
}

message Expression {
  repeated NamedAny additional_properties = 1;
}

// Allows referencing an external resource for extended documentation.
message ExternalDocs {
  string description = 1;
  string url = 2;
  repeated NamedAny specification_extension = 3;
}

// The Header Object follows the structure of the Parameter Object with the following changes:  1. `name` MUST NOT be specified, it is given in the corresponding `headers` map. 1. `in` MUST NOT be specified, it is implicitly in `header`. 1. All traits that are affected by the location MUST be applicable to a location of `header` (for example, `style`).
message Header {
  string description = 1;
  bool required = 2;
  bool deprecated = 3;
  bool allow_empty_value = 4;
  string style = 5;
  bool explode = 6;
  bool allow_reserved = 7;
  SchemaOrReference schema = 8;
  Any example = 9;
  ExamplesOrReferences examples = 10;
  MediaTypes content = 11;
  repeated NamedAny specification_extension = 12;
}

message HeaderOrReference {
  oneof oneof {
    Header header = 1;
    Reference reference = 2;
  }
}

message HeadersOrReferences {
  repeated NamedHeaderOrReference additional_properties = 1;
}

// The object provides metadata about the API. The metadata MAY be used by the clients if needed, and MAY be presented in editing or documentation generation tools for convenience.
message Info {
  string title = 1;
  string description = 2;
  string terms_of_service = 3;
  Contact contact = 4;
  License license = 5;
  string version = 6;
  repeated NamedAny specification_extension = 7;
  string summary = 8;
}

message ItemsItem {
  repeated SchemaOrReference schema_or_reference = 1;
}

// License information for the exposed API.
message License {
  string name = 1;
  string url = 2;
  repeated NamedAny specification_extension = 3;
}

// The `Link object` represents a possible design-time link for a response. The presence of a link does not guarantee the caller's ability to successfully invoke it, rather it provides a known relationship and traversal mechanism between responses and other operations.  Unlike _dynamic_ links (i.e. links provided **in** the response payload), the OAS linking mechanism does not require link information in the runtime response.  For computing links, and providing instructions to execute them, a runtime expression is used for accessing values in an operation and using them as parameters while invoking the linked operation.
message Link {
  string operation_ref = 1;
  string operation_id = 2;
  AnyOrExpression parameters = 3;
  AnyOrExpression request_body = 4;
  string description = 5;
  Server server = 6;
  repeated NamedAny specification_extension = 7;
}

message LinkOrReference {
  oneof oneof {
    Link link = 1;
    Reference reference = 2;
  }
}

message LinksOrReferences {
  repeated NamedLinkOrReference additional_properties = 1;
}

// Each Media Type Object provides schema and examples for the media type identified by its key.
message MediaType {
  SchemaOrReference schema = 1;
  Any example = 2;
  ExamplesOrReferences examples = 3;
  Encodings encoding = 4;
  repeated NamedAny specification_extension = 5;
}

message MediaTypes {
  repeated NamedMediaType additional_properties = 1;
}

// Automatically-generated message used to represent maps of Any as ordered (name,value) pairs.
message NamedAny {
  // Map key
  string name = 1;
  // Mapped value
  Any value = 2;
}

// Automatically-generated message used to represent maps of CallbackOrReference as ordered (name,value) pairs.
message NamedCallbackOrReference {
  // Map key
  string name = 1;
  // Mapped value
  CallbackOrReference value = 2;
}

// Automatically-generated message used to represent maps of Encoding as ordered (name,value) pairs.
message NamedEncoding {
  // Map key
  string name = 1;
  // Mapped value
  Encoding value = 2;
}

// Automatically-generated message used to represent maps of ExampleOrReference as ordered (name,value) pairs.
message NamedExampleOrReference {
  // Map key
  string name = 1;
  // Mapped value
  ExampleOrReference value = 2;
}

// Automatically-generated message used to represent maps of HeaderOrReference as ordered (name,value) pairs.
message NamedHeaderOrReference {
  // Map key
  string name = 1;
  // Mapped value
  HeaderOrReference value = 2;
}

// Automatically-generated message used to represent maps of LinkOrReference as ordered (name,value) pairs.
message NamedLinkOrReference {
  // Map key
  string name = 1;
  // Mapped value
  LinkOrReference value = 2;
}

// Automatically-generated message used to represent maps of MediaType as ordered (name,value) pairs.
message NamedMediaType {
  // Map key
  string name = 1;
  // Mapped value
  MediaType value = 2;
}

// Automatically-generated message used to represent maps of ParameterOrReference as ordered (name,value) pairs.
message NamedParameterOrReference {
  // Map key
  string name = 1;
  // Mapped value
  ParameterOrReference value = 2;
}

// Automatically-generated message used to represent maps of PathItem as ordered (name,value) pairs.
message NamedPathItem {
  // Map key
  string name = 1;
  // Mapped value
  PathItem value = 2;
}

// Automatically-generated message used to represent maps of RequestBodyOrReference as ordered (name,value) pairs.
message NamedRequestBodyOrReference {
  // Map key
  string name = 1;
  // Mapped value
  RequestBodyOrReference value = 2;
}

// Automatically-generated message used to represent maps of ResponseOrReference as ordered (name,value) pairs.
message NamedResponseOrReference {
  // Map key
  string name = 1;
  // Mapped value
  ResponseOrReference value = 2;
}

// Automatically-generated message used to represent maps of SchemaOrReference as ordered (name,value) pairs.
message NamedSchemaOrReference {
  // Map key
  string name = 1;
  // Mapped value
  SchemaOrReference value = 2;
}

// Automatically-generated message used to represent maps of SecuritySchemeOrReference as ordered (name,value) pairs.
message NamedSecuritySchemeOrReference {
  // Map key
  string name = 1;
  // Mapped value
  SecuritySchemeOrReference value = 2;
}

// Automatically-generated message used to represent maps of ServerVariable as ordered (name,value) pairs.
message NamedServerVariable {
  // Map key
  string name = 1;
  // Mapped value
  ServerVariable value = 2;
}

// Automatically-generated message used to represent maps of string as ordered (name,value) pairs.
message NamedString {
  // Map key
  string name = 1;
  // Mapped value
  string value = 2;
}

// Automatically-generated message used to represent maps of StringArray as ordered (name,value) pairs.
message NamedStringArray {
  // Map key
  string name = 1;
  // Mapped value
  StringArray value = 2;
}

// Configuration details for a supported OAuth Flow
message OauthFlow {
  string authorization_url = 1;
  string token_url = 2;
  string refresh_url = 3;
  Strings scopes = 4;
  repeated NamedAny specification_extension = 5;
}

// Allows configuration of the supported OAuth Flows.
message OauthFlows {
  OauthFlow implicit = 1;
  OauthFlow password = 2;
  OauthFlow client_credentials = 3;
  OauthFlow authorization_code = 4;
  repeated NamedAny specification_extension = 5;
}

message Object {
  repeated NamedAny additional_properties = 1;
}

// Describes a single API operation on a path.
message Operation {
  repeated string tags = 1;
  string summary = 2;
  string description = 3;
  ExternalDocs external_docs = 4;
  string operation_id = 5;
  repeated ParameterOrReference parameters = 6;
  RequestBodyOrReference request_body = 7;
  Responses responses = 8;
  CallbacksOrReferences callbacks = 9;
  bool deprecated = 10;
  repeated SecurityRequirement security = 11;
  repeated Server servers = 12;
  repeated NamedAny specification_extension = 13;
}

// Describes a single operation parameter.  A unique parameter is defined by a combination of a name and location.
message Parameter {
  string name = 1;
  string in = 2;
  string description = 3;
  bool required = 4;
  bool deprecated = 5;
  bool allow_empty_value = 6;
  string style = 7;
  bool explode = 8;
  bool allow_reserved = 9;
  SchemaOrReference schema = 10;
  Any example = 11;
  ExamplesOrReferences examples = 12;
  MediaTypes content = 13;
  repeated NamedAny specification_extension = 14;
}

message ParameterOrReference {
  oneof oneof {
    Parameter parameter = 1;
    Reference reference = 2;
  }
}

message ParametersOrReferences {
  repeated NamedParameterOrReference additional_properties = 1;
}

// Describes the operations available on a single path. A Path Item MAY be empty, due to ACL constraints. The path itself is still exposed to the documentation viewer but they will not know which operations and parameters are available.
message PathItem {
  string _ref = 1;
  string summary = 2;
  string description = 3;
  Operation get = 4;
  Operation put = 5;
  Operation post = 6;
  Operation delete = 7;
  Operation options = 8;
  Operation head = 9;
  Operation patch = 10;
  Operation trace = 11;
  repeated Server servers = 12;
  repeated ParameterOrReference parameters = 13;
  repeated NamedAny specification_extension = 14;
}

// Holds the relative paths to the individual endpoints and their operations. The path is appended to the URL from the `Server Object` in order to construct the full URL.  The Paths MAY be empty, due to ACL constraints.
message Paths {
  repeated NamedPathItem path = 1;
  repeated NamedAny specification_extension = 2;
}

message Properties {
  repeated NamedSchemaOrReference additional_properties = 1;
}

// A simple object to allow referencing other components in the specification, internally and externally.  The Reference Object is defined by JSON Reference and follows the same structure, behavior and rules.   For this specification, reference resolution is accomplished as defined by the JSON Reference specification and not by the JSON Schema specification.
message Reference {
  string _ref = 1;
  string summary = 2;
  string description = 3;
}

message RequestBodiesOrReferences {
  repeated NamedRequestBodyOrReference additional_properties = 1;
}

// Describes a single request body.
message RequestBody {
  string description = 1;
  MediaTypes content = 2;
  bool required = 3;
  repeated NamedAny specification_extension = 4;
}

message RequestBodyOrReference {
  oneof oneof {
    RequestBody request_body = 1;
    Reference reference = 2;
  }
}

// Describes a single response from an API Operation, including design-time, static  `links` to operations based on the response.
message Response {
  string description = 1;
  HeadersOrReferences headers = 2;
  MediaTypes content = 3;
  LinksOrReferences links = 4;
  repeated NamedAny specification_extension = 5;
}

message ResponseOrReference {
  oneof oneof {
    Response response = 1;
    Reference reference = 2;
  }
}

// A container for the expected responses of an operation. The container maps a HTTP response code to the expected response.  The documentation is not necessarily expected to cover all possible HTTP response codes because they may not be known in advance. However, documentation is expected to cover a successful operation response and any known errors.  The `default` MAY be used as a default response object for all HTTP codes  that are not covered individually by the specification.  The `Responses Object` MUST contain at least one response code, and it  SHOULD be the response for a successful operation call.
message Responses {
  ResponseOrReference default = 1;
  repeated NamedResponseOrReference response_or_reference = 2;
  repeated NamedAny specification_extension = 3;
}

message ResponsesOrReferences {
  repeated NamedResponseOrReference additional_properties = 1;
}

// The Schema Object allows the definition of input and output data types. These types can be objects, but also primitives and arrays. This object is an extended subset of the JSON Schema Specification Wright Draft 00.  For more information about the properties, see JSON Schema Core and JSON Schema Validation. Unless stated otherwise, the property definitions follow the JSON Schema.
message Schema {
  bool nullable = 1;
  Discriminator discriminator = 2;
  bool read_only = 3;
  bool write_only = 4;
  Xml xml = 5;
  ExternalDocs external_docs = 6;
  Any example = 7;
  bool deprecated = 8;
  string title = 9;
  double multiple_of = 10;
  double maximum = 11;
  bool exclusive_maximum = 12;
  double minimum = 13;
  bool exclusive_minimum = 14;
  int64 max_length = 15;
  int64 min_length = 16;
  string pattern = 17;
  int64 max_items = 18;
  int64 min_items = 19;
  bool unique_items = 20;
  int64 max_properties = 21;
  int64 min_properties = 22;
  repeated string required = 23;
  repeated Any enum = 24;
  string type = 25;
  repeated SchemaOrReference all_of = 26;
  repeated SchemaOrReference one_of = 27;
  repeated SchemaOrReference any_of = 28;
  Schema not = 29;
  ItemsItem items = 30;
  Properties properties = 31;
  AdditionalPropertiesItem additional_properties = 32;
  DefaultType default = 33;
  string description = 34;
  string format = 35;
  repeated NamedAny specification_extension = 36;
}

message SchemaOrReference {
  oneof oneof {
    Schema schema = 1;
    Reference reference = 2;
  }
}

message SchemasOrReferences {
  repeated NamedSchemaOrReference additional_properties = 1;
}

// Lists the required security schemes to execute this operation. The name used for each property MUST correspond to a security scheme declared in the Security Schemes under the Components Object.  Security Requirement Objects that contain multiple schemes require that all schemes MUST be satisfied for a request to be authorized. This enables support for scenarios where multiple query parameters or HTTP headers are required to convey security information.  When a list of Security Requirement Objects is defined on the OpenAPI Object or Operation Object, only one of the Security Requirement Objects in the list needs to be satisfied to authorize the request.
message SecurityRequirement {
  repeated NamedStringArray additional_properties = 1;
}

// Defines a security scheme that can be used by the operations. Supported schemes are HTTP authentication, an API key (either as a header, a cookie parameter or as a query parameter), mutual TLS (use of a client certificate), OAuth2's common flows (implicit, password, application and access code) as defined in RFC6749, and OpenID Connect.   Please note that currently (2019) the implicit flow is about to be deprecated OAuth 2.0 Security Best Current Practice. Recommended for most use case is Authorization Code Grant flow with PKCE.
message SecurityScheme {
  string type = 1;
  string description = 2;
  string name = 3;
  string in = 4;
  string scheme = 5;
  string bearer_format = 6;
  OauthFlows flows = 7;
  string open_id_connect_url = 8;
  repeated NamedAny specification_extension = 9;
}

message SecuritySchemeOrReference {
  oneof oneof {
    SecurityScheme security_scheme = 1;
    Reference reference = 2;
  }
}

message SecuritySchemesOrReferences {
  repeated NamedSecuritySchemeOrReference additional_properties = 1;
}

// An object representing a Server.
message Server {
  string url = 1;
  string description = 2;
  ServerVariables variables = 3;
  repeated NamedAny specification_extension = 4;
}

// An object representing a Server Variable for server URL template substitution.
message ServerVariable {
  repeated string enum = 1;
  string default = 2;
  string description = 3;
  repeated NamedAny specification_extension = 4;
}

message ServerVariables {
  repeated NamedServerVariable additional_properties = 1;
}

// Any property starting with x- is valid.
message SpecificationExtension {
  oneof oneof {
    double number = 1;
    bool boolean = 2;
    string string = 3;
  }
}

message StringArray {
  repeated string value = 1;
}

message Strings {
  repeated NamedString additional_properties = 1;
}

// Adds metadata to a single tag that is used by the Operation Object. It is not mandatory to have a Tag Object per tag defined in the Operation Object instances.
message Tag {
  string name = 1;
  string description = 2;
  ExternalDocs external_docs = 3;
  repeated NamedAny specification_extension = 4;
}

// A metadata object that allows for more fine-tuned XML model definitions.  When using arrays, XML element names are *not* inferred (for singular/plural forms) and the `name` property SHOULD be used to add that information. See examples for expected behavior.
message Xml {
  string name = 1;
  string namespace = 2;
  string prefix = 3;
  bool attribute = 4;
  bool wrapped = 5;
  repeated NamedAny specification_extension = 6;
}

