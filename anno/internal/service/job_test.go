package service

import (
	"testing"
	"time"

	"anno/api/client"
	"anno/internal/biz"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func Test_buildExecutors(t *testing.T) {
	users := map[string]*client.BaseUser{
		"anno1":   {Name: "anno1"},
		"review1": {Name: "review1"},
		"review2": {Name: "review2"},
	}

	cases := []struct {
		name     string
		joblogs  []*biz.Joblog
		expected []*anno.ElementAnno_Metadata_Executor
	}{
		{
			name: "phase_1",
			joblogs: []*biz.Joblog{
				{
					ID:            1,
					Action:        "claim",
					ToPhase:       1,
					ToExecutorUid: "anno1",
				},
			},
			expected: []*anno.ElementAnno_Metadata_Executor{
				{
					User:     users["anno1"],
					Phase:    1,
					SubmitAt: timestamppb.New(time.Time{}),
				},
			},
		},
		{
			name: "phase_1_2_3",
			joblogs: []*biz.Joblog{
				{
					ID:            1,
					Action:        "claim",
					ToPhase:       1,
					ToExecutorUid: "anno1",
				},
				{
					ID:            2,
					Action:        "claim",
					ToPhase:       2,
					ToExecutorUid: "review1",
				},
				{
					ID:            3,
					Action:        "claim",
					ToPhase:       3,
					ToExecutorUid: "review2",
				},
			},
			expected: []*anno.ElementAnno_Metadata_Executor{
				{
					User:     users["anno1"],
					Phase:    1,
					SubmitAt: timestamppb.New(time.Time{}),
				},
				{
					User:     users["review1"],
					Phase:    2,
					SubmitAt: timestamppb.New(time.Time{}),
				},
				{
					User:     users["review2"],
					Phase:    3,
					SubmitAt: timestamppb.New(time.Time{}),
				},
			},
		},
		{
			name: "phase_1_2_3_2",
			joblogs: []*biz.Joblog{
				{
					ID:            1,
					Action:        "claim",
					ToPhase:       1,
					ToExecutorUid: "anno1",
				},
				{
					ID:            2,
					Action:        "claim",
					ToPhase:       2,
					ToExecutorUid: "review1",
				},
				{
					ID:            3,
					Action:        "claim",
					ToPhase:       3,
					ToExecutorUid: "review2",
				},
				{
					ID:            4,
					Action:        "claim",
					ToPhase:       2,
					ToExecutorUid: "review1",
				},
			},
			expected: []*anno.ElementAnno_Metadata_Executor{
				{
					User:     users["anno1"],
					Phase:    1,
					SubmitAt: timestamppb.New(time.Time{}),
				},
				{
					User:     users["review1"],
					Phase:    2,
					SubmitAt: timestamppb.New(time.Time{}),
				},
			},
		},
		{
			name: "phase_1_2_3_2_3",
			joblogs: []*biz.Joblog{
				{
					ID:            1,
					Action:        "claim",
					ToPhase:       1,
					ToExecutorUid: "anno1",
				},
				{
					ID:            2,
					Action:        "claim",
					ToPhase:       2,
					ToExecutorUid: "review1",
				},
				{
					ID:            3,
					Action:        "claim",
					ToPhase:       3,
					ToExecutorUid: "review2",
				},
				{
					ID:            4,
					Action:        "claim",
					ToPhase:       2,
					ToExecutorUid: "review1",
				},
				{
					ID:            5,
					Action:        "claim",
					ToPhase:       3,
					ToExecutorUid: "review2",
				},
			},
			expected: []*anno.ElementAnno_Metadata_Executor{
				{
					User:     users["anno1"],
					Phase:    1,
					SubmitAt: timestamppb.New(time.Time{}),
				},
				{
					User:     users["review1"],
					Phase:    2,
					SubmitAt: timestamppb.New(time.Time{}),
				},
				{
					User:     users["review2"],
					Phase:    3,
					SubmitAt: timestamppb.New(time.Time{}),
				},
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			got := buildExecutors(c.joblogs, users)
			// fmt.Println(got)
			assert.Equal(t, c.expected, got)
		})
	}
}
