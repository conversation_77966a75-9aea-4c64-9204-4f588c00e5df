import os
import sys
sys.path.append('.')
from customer.common import tools


def run(input_dir, output_dir):
    label_dirs = tools.find_dir_by_pre_name(input_dir, 'label')
    for label_dir in label_dirs:
        out_label_dir = os.path.join(output_dir, os.path.basename(os.path.dirname(os.path.dirname(label_dir))))
        json_files = tools.get_json_files(label_dir)
        for json_file in json_files:
            json_data = tools.get_json_data(json_file.path)
            label_groups = {}
            for label in json_data['labels']:
                group_id = label['group_id']
                corner_type = label['class']
                if group_id not in label_groups:
                    label_groups[group_id] = {}
                label_groups[group_id][corner_type] = label
            objs = []
            for group_id, label_group in label_groups.items():
                obj = {
                    'id': int(group_id),
                    'category': 'corner_points',
                    'color': '#C806DA',
                    'feature_type': 'Brokenline',
                    'tracking_id': -1,
                    'point_list': [],
                    'attributes': {},
                    'point_attributes': []
                }
                obj['point_list'].append(
                    [float(label_group['0']['annotation']['data']['x']), float(label_group['0']['annotation']['data']['y'])]
                )
                obj['attributes'] = {
                    'slot_type': label_group['0']['attrs']['slot_type'][0],
                    'completeness': label_group['0']['attrs']['completeness'][0],
                    'status': label_group['0']['attrs']['status'][0]
                }
                obj['point_attributes'].append({
                    "index": 0,
                    "corner_type": label_group['0']['attrs']['corner_type'][0]
                })
                for idx in ['1', '2', '3']:
                    obj['point_list'].append(
                        [float(label_group[idx]['annotation']['data']['x']), float(label_group[idx]['annotation']['data']['y'])]
                    )
                    obj['point_attributes'].append({
                        "index": int(idx),
                        "corner_type": label_group['0']['attrs']['corner_type'][0]
                    })
                objs.append(obj)

            result = {}
            result['metadata'] = {
                "file_name": json_data['label_meta']['source_info']['file_name'],
                "image_width": json_data['label_meta']['source_info']['width'],
                "image_height": json_data['label_meta']['source_info']['height']
            }
            result['objects'] = objs

            out_json_file = os.path.join(out_label_dir, os.path.basename(json_file))
            tools.write_json_file(result, out_json_file, indent=4)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
