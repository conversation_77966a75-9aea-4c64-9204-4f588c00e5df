package keto

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPermClass(t *testing.T) {
	cases := []struct {
		perm  string
		class string
	}{
		{perm: "create", class: ""},
		{perm: "IamUser.create", class: "IamUser"},
	}

	for _, c := range cases {
		assert.Equal(t, c.class, PermClass(c.perm))
	}
}

func TestParseObjectName(t *testing.T) {
	cases := []struct {
		name string
		typ  string
		id   string
	}{
		{name: "admin", typ: "", id: "admin"},
		{name: "IamRole:", typ: "IamRole", id: ""},
		{name: "IamRole:admin", typ: "IamRole", id: "admin"},
	}

	for _, c := range cases {
		typ, id := ParseObjectName(c.name)
		assert.Equal(t, c.typ, typ)
		assert.Equal(t, c.id, id)
	}
}
