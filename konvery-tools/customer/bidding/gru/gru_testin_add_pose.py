import json
import os
import shutil
import sys
import csv
import re
import numpy as np

sys.path.append(".")
from customer.common import tools


def get_pose_data(csv_path):
    data = []
    with open(csv_path, mode='r') as csvfile:
        csv_reader = csv.reader(csvfile)
        next(csv_reader)
        for row in csv_reader:
            data.append([float(value) for value in row])
    data = np.array(data)

    return data


def run(input_dir, output_dir):
    config_list = []
    folder_list = []
    target_list = []
    txt_dir = tools.get_file_by_extension(input_dir, "txt")[0].path
    pose_dir = tools.find_dir_by_pre_name(input_dir, "pose")[0]
    cfg_dir = tools.find_dir_by_pre_name(input_dir, "config")[0]
    input_dir = tools.find_dir_by_pre_name(input_dir, "ds")[0]
    with open(txt_dir, 'r', encoding='utf-8') as file:
        for line in file:
            # 去掉每行末尾的换行符
            target_list.append(line.strip())

    for root, dirs, files in os.walk(input_dir):
        for file in files:
            if file.startswith("config.json"):
                folder_list.append(root)
    tools.check_dir(output_dir)
    for folder in folder_list:
        if os.path.basename(folder) in target_list:
            dst = os.path.join(output_dir, os.path.basename(folder))
            tools.check_dir(dst)
            shutil.copytree(folder, dst, dirs_exist_ok=True)
    labels_dir = tools.find_dir_by_pre_name(output_dir, "label")
    for label in labels_dir:
        os.rename(label, os.path.join(os.path.dirname(label), "pre_label"))
    for root, dirs, files in os.walk(output_dir):
        for file in files:
            if file.startswith("config.json"):
                config_list.append(os.path.join(root, file))
    for config in config_list:
        pcd_list = tools.get_file_by_extension_sorted_list(os.path.dirname(config))
        pcd_list = [os.path.basename(pcd).split('.')[0].replace("_", "") for pcd in pcd_list]  #获取需要的帧数
        json_data = json.load(open(config))
        json_data.update({'poses': {}})
        pose_csv = os.path.join(pose_dir, os.path.basename(os.path.dirname(config)), "pose.csv")
        pose_data = get_pose_data(pose_csv)
        cfg_file = os.path.join(cfg_dir, os.path.basename(os.path.dirname(config)), "lidars.cfg")
        with open(cfg_file, 'r') as file:
            cfg = file.read()
        vehicle_to_sensing = re.findall(
            r'vehicle_to_sensing\s+{\s+position\s+{\s+x:\s+([\d\.\-+e]+)\s+y:\s+([\d\.\-+e]+)\s+z:\s+([\d\.\-+e]+)\s+}\s+orientation\s+{\s+qx:\s+([\d\.\-+e]+)\s+qy:\s+([\d\.\-+e]+)\s+qz:\s+([\d\.\-+e]+)\s+qw:\s+([\d\.\-+e]+)\s+}',
            cfg
        )
        for row in pose_data:
            if str(int(row[0])) not in pcd_list:  #筛选出对应的帧数
                continue
            v2s_mat = tools.pose_to_mat(vehicle_to_sensing[0])
            mat = tools.get_extrinsic_by_txyz_rxyz(row[1:], row_number=4) @ v2s_mat
            mat = mat.T.reshape(-1).tolist()
            json_data["poses"].update({str(int(row[0]))[:-6] + "_" + str(int(row[0]))[-6:]: mat})
        with open(config, 'w') as f:
            json.dump(json_data, f, indent=4)


"""
把csv文件中的pose信息补充到config.json中
输入input_dir目录下应包含“ds”开头的数据 “file_list.txt” 和“pose”文件夹
"""
if __name__ == '__main__':
    args = tools.get_args()
    input_dir = args.input
    output_dir = args.out
    run(input_dir=input_dir, output_dir=output_dir)

    # v2s_mat = tools.pose_to_mat([1.5195, 0, 1.445, 0, 0, 0, 1])
    # s2v_mat = np.linalg.inv(v2s_mat)
    # pose_a = np.array('77.8214574724671,11.3754149198296,-49.7517695046071,-0.0262338359704652,0.025566776110276,0.109244168889908'.split(','), dtype=np.float32)
    # pose_b = np.array('90.25935127369,10.3972711027334,-49.7395971209323,-0.0203245688863469,0.00484437057332994,-0.862729899563262'.split(','), dtype=np.float32)
    # mat_a = tools.get_extrinsic_by_txyz_rxyz(pose_a, row_number=4)
    # mat_b = tools.get_extrinsic_by_txyz_rxyz(pose_b, row_number=4)
    # rotation_mat_a = mat_a.copy()
    # rotation_mat_a[:3, 3] = 0
    # rotation_mat_b = mat_b.copy()
    # rotation_mat_b[:3, 3] = 0
    #
    # xyz_b = [-10.0556, 1.50478, -0.98, 1]
    # # 将B帧点对齐到世界坐标系旋转
    # world_rotation_b = rotation_mat_b @ v2s_mat @ xyz_b
    # # 平移到目标帧A坐标系
    # b2a_translation = world_rotation_b + (mat_b - mat_a)[:4, 3]
    # # 对齐到到目标帧A坐标系旋转轴
    # b2a = s2v_mat @ np.linalg.inv(rotation_mat_a) @ b2a_translation
    #
    #
    # b2a_new = np.linalg.inv(mat_a @ v2s_mat) @ mat_b @ v2s_mat @ xyz_b
    #
    # print(f"{b2a}\n{b2a_new}")
