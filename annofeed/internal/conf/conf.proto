syntax = "proto3";
package kratos.api;

option go_package = "annofeed/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
  string workspace = 3; // directory to temporarily store the downloaded compressed dataset package file
  FileServer file_server = 4;
  DangerTest danger_test = 5;
  RPC rpc = 6;
  Temporal temporal = 7;
  Ktwf ktwf = 8;
  Otel otel = 9;
  MQ mq = 10;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
  // non-empty comma-separated list of origins will enable CORS
  string cors_origins = 3;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
    string endpoint = 3;
    int32 port = 4;
    string database = 5;
    string username = 6;
    string password = 7;
    string options = 8;
    int32 max_idle_conns = 9;
    int32 max_open_conns = 10;
    google.protobuf.Duration conn_max_idle_time = 11;
  }
  message Redis {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration read_timeout = 3;
    google.protobuf.Duration write_timeout = 4;
  }
  Database database = 1;
  Redis redis = 2;
}

message Service {
  string addr = 1;
}

message RPC {
  string svc_account = 1;
  Service iam = 2;
  Service anno = 3;
}

// DANGEROUS configurations for test purpose only
message DangerTest {
}

message FileServer {
  string storage = 1; // "s3": store the uploaded files into S3; use local storage if not specified

  // local storage config
  string base_dir = 2; // directory in local filesystem to put the uploaded files

  // s3 storage config
  string bucket = 3; // store normal data
  string public_bucket = 4; // store public resources: user/project/label avatars
  string avatar_url = 5; // base URL for public resources

  bool public_rawdata = 6; // allow anonymous to access rawdatas
  google.protobuf.Duration presign_expires = 7;
  string svc_url = 8; // service URL

  S3Config s3 = 9;
  CloudFront cloudfront = 10; // CDN config
}

message S3Config {
	string access_key = 1;
	string secret_key = 2;
}

message CloudFront {
  message Distribution {
    // matching origin, including the s3 scheme, bucket name and the path if any: s3://bucket/path
    string origin = 1;
    // access URL prefix, including scheme and domain name: https://example.com
    string url_prefix = 2;
    // if it is a public distribution
    bool public = 3;
  }

  bool enabled = 1;
  map<string, Distribution> distributions = 2;
  // signer key ID
  string sign_key_id = 3;
  // signer private key (RSA) in PEM format
  string sign_key = 4;
  // default expires duration for signed URLs
  google.protobuf.Duration expires = 5;
}

message Temporal {
  bool disable_worker = 1;
  string addr = 2;
  string namespace = 3;
  string task_queue = 4;
  // default worker storage size in GB
  int64 worker_storage_gb = 5;
}

message Ktwf {
  message Master {
    string image = 1;
    repeated string command = 2;
    repeated string args = 3;
    string service_account = 4;
    string storage_class = 5;
  }
  Master master = 1;

  message Worker {
    string namespace = 1;
    string service_account = 2;
  }
  Worker worker = 2;
}

message Otel {
  Tracing tracing = 1;
  Metrics metrics = 2;
  Log log = 3;
}

message Tracing {
  // OTLP GRPC endpoint of the tracing service
  string endpoint = 1;
}

message Metrics {
  // prometheus HTTP exporter listening address
  string serve_addr = 1;
}

message Log {
  // log level: debug, info, warn, error, fatal
  string level = 1;
  // log format: default, json
  string format = 2;
}

message MQ {
  enum Provider {
    unspecified = 0;
    redis_pubsub = 1;
    // redis_stream = 2;

    // producer only
    aws_sns = 3;
    aws_sqs = 4;
  }
  message Producer {
    Provider provider = 1;
    // default topic
    string topic = 2;
  }
  message Consumer {
    Provider provider = 1;
    // comma seperated topics list (not for AWS SQS consumers)
    string topics = 2;
    // number of handler threads
    int32 handler_threads = 3;
  }
  message SQS {
    // queue name or URL
    string name = 1;
  }

  Producer producer = 1;
  Consumer consumer = 2;
  Data.Redis redis = 3;
  SQS sqs = 4;
}
