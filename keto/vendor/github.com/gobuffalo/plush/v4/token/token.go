package token

// Type represents each type of token.
type Type string

// Token of a section of input source.
type Token struct {
	Type       Type
	Literal    string
	LineNumber int
}

var keywords = map[string]Type{
	"fn":       FUNCTION,
	"func":     FUNCTION,
	"let":      LET,
	"true":     TRUE,
	"false":    <PERSON>LSE,
	"if":       IF,
	"else":     <PERSON>LS<PERSON>,
	"return":   RETUR<PERSON>,
	"for":      FOR,
	"in":       IN,
	"continue": CONTINUE,
	"break":    BREAK,
}

// LookupIdent an ident and return a keyword type, or a plain ident
func LookupIdent(ident string) Type {
	if tok, ok := keywords[ident]; ok {
		return tok
	}
	return IDENT
}
