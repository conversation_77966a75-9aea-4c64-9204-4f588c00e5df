package interpolate

import (
	"strconv"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
)

// CountIns returns numbers of annotated objects in a job.
// cnt means the number of objects before interpolation.
// total will count in objects after interpolation.
func CountIns(job *anno.JobAnno) (cnt, total int) {
	// count non-interpolated objects
	for _, e := range job.ElementAnnos {
		for _, r := range e.RawdataAnnos {
			cnt += len(r.Objects)
		}
	}
	total = cnt
	if !job.NeedInterpolation {
		return
	}

	// count interpolated objects
	lastElemIdx := len(job.ElementAnnos) - 1
	for _, index := range calObjIndex(job) {
		pre := index[0]
		for i := 1; i < len(index); i++ {
			cur := index[i]
			if k := cur.ElemIdx - pre.ElemIdx - 1; !pre.VanishLater {
				total += k
			}
			pre = cur
		}
		if !pre.VanishLater && pre.ElemIdx < lastElemIdx {
			total += lastElemIdx - pre.ElemIdx
		}
	}

	return
}

// Interpolate interpolates objects in a job.
func Interpolate(job *anno.JobAnno) {
	if !job.NeedInterpolation {
		return
	}
	lastElemIdx := len(job.ElementAnnos) - 1
	for _, index := range calObjIndex(job) {
		pre := index[0]
		for i := 1; i < len(index); i++ {
			cur := index[i]
			doInterpolate(job, pre, cur)
			pre = cur
		}
		if pre.ElemIdx < lastElemIdx {
			doInterpolate(job, pre, &objIndex{
				ElemIdx:    lastElemIdx + 1,
				RawdataIdx: pre.RawdataIdx,
			})
		}
	}
	job.NeedInterpolation = false
}

type objIndex struct {
	ElemIdx     int
	RawdataIdx  int
	VanishLater bool
	Object      *anno.Object
}

func calObjIndex(job *anno.JobAnno) map[string][]*objIndex {
	m := make(map[string][]*objIndex, len(job.ElementAnnos))
	for i, e := range job.ElementAnnos {
		for j, r := range e.RawdataAnnos {
			for _, o := range r.Objects {
				if o.TrackId != "" {
					// in case the same object has occurrences in different rawdatas
					key := o.TrackId + ":rd=" + strconv.Itoa(j)
					m[key] = append(m[key], &objIndex{
						ElemIdx:     i,
						RawdataIdx:  j,
						VanishLater: o.VanishLater,
						Object:      o,
					})
				}
			}
		}
	}
	return m
}

func doInterpolate(job *anno.JobAnno, o1, o2 *objIndex) {
	if o1.VanishLater {
		return
	}

	name := o1.Object.Label.Widget.Name
	fn := GetInterpolator(name)
	if fn == nil {
		panic("interpolation for " + name.String() + " is not supported")
	}

	rdIdx := o1.RawdataIdx
	span := o2.ElemIdx - o1.ElemIdx
	for j := 1; j < span; j++ {
		elem := job.ElementAnnos[j+o1.ElemIdx]
		for len(elem.RawdataAnnos) <= rdIdx {
			elem.RawdataAnnos = append(elem.RawdataAnnos, &anno.RawdataAnno{})
		}
		o := fn(o1.Object, o2.Object, float64(span), float64(j))
		elem.RawdataAnnos[rdIdx].Objects = append(elem.RawdataAnnos[rdIdx].Objects, o)
		elem.InsCnt++
		job.InsCnt++
	}
}
