package interpolate

import (
	"gitlab.rp.konvery.work/platform/apis/anno/v1"

	"github.com/google/uuid"
)

// Interpolator generates a new object using linear interpolation.
// span indicates the distance between o1 and o2.
// distance indicates the distance from o1 to the new object.
// if o2 is nil, a copy of o1 is returned.
type Interpolator func(o1, o2 *anno.Object, span, distance float64) *anno.Object

var interpolators = map[anno.WidgetName_Enum]Interpolator{}

func RegisterInterpolator(name anno.WidgetName_Enum, fn Interpolator) { interpolators[name] = fn }

func GetInterpolator(name anno.WidgetName_Enum) Interpolator {
	return interpolators[name]
}

func cloneDirection(dir *anno.Direction) *anno.Direction {
	return &anno.Direction{
		Origin: append([]float64{}, dir.GetOrigin()...),
		Toward: append([]float64{}, dir.GetToward()...),
	}
}

func cloneSingleGap(gap *anno.Object_Widget) *anno.Object_Widget {
	if gap == nil {
		return nil
	}
	return &anno.Object_Widget{
		Name:     gap.Name,
		Data:     append([]float64{}, gap.Data...),
		Uri:      gap.Uri,
		Forward:  cloneDirection(gap.Forward),
		PointCnt: gap.PointCnt,
	}
}
func cloneGaps(gaps []*anno.Object_Widget) []*anno.Object_Widget {
	newGaps := make([]*anno.Object_Widget, len(gaps))
	for i, gap := range gaps {
		newGaps[i] = cloneSingleGap(gap)
	}
	return newGaps
}

func cloneObject(o *anno.Object) *anno.Object {
	w := o.Label.Widget
	return &anno.Object{
		Uuid:        uuid.NewString(), // assign a new ID
		TrackId:     o.TrackId,
		VanishLater: o.VanishLater,
		Label: &anno.Object_Label{
			Name:  o.Label.Name,
			Attrs: o.Label.Attrs, // attrs is shared
			Widget: &anno.Object_Widget{
				Name:     w.Name,
				Data:     append([]float64{}, w.Data...),
				Gaps:     cloneGaps(o.Label.Widget.Gaps),
				Uri:      w.Uri,
				Forward:  cloneDirection(w.Forward),
				PointCnt: w.PointCnt,
			},
		},
	}
}
