package check

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"log"
	"os"
	"sync"
	"time"

	"github.com/gofrs/uuid"
	"github.com/orca-zhang/ecache"
	"github.com/ory/keto/internal/check/checkgroup"
	"github.com/ory/keto/internal/namespace"
	"github.com/ory/keto/internal/namespace/ast"
	"github.com/ory/keto/internal/relationtuple"
	"github.com/ory/keto/internal/x"
	"github.com/ory/keto/ketoapi"
	"github.com/ory/x/pointerx"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

type Set[T comparable] map[T]struct{}

func NewSet[T comparable]() Set[T] { return make(Set[T]) }
func (o Set[T]) Add(v T)           { o[v] = struct{}{} }
func (o Set[T]) Has(v T) bool      { _, ok := o[v]; return ok }
func (o Set[T]) Union(s Set[T]) {
	for k := range s {
		o[k] = struct{}{}
	}
}
func (o Set[T]) Len() int   { return len(o) }
func (o Set[T]) Slice() []T { return lo.Keys(o) }

type Policy struct {
	Perms   Set[uuid.UUID]
	Members Set[uuid.UUID]
}

type Cache struct {
	sysNames    map[uuid.UUID]string
	sysUUIDs    map[string]uuid.UUID
	sysRoles    map[uuid.UUID]Set[uuid.UUID]
	sysGroups   map[uuid.UUID]Set[uuid.UUID]
	sysPolicies map[uuid.UUID]*Policy
	allUsers    uuid.UUID // UUID of sys/allUsers
	permUUIDs   map[string]uuid.UUID
}

func ObjectOf(subject relationtuple.Subject) uuid.UUID {
	if set, ok := subject.(*relationtuple.SubjectSet); ok {
		return set.Object
	}
	return subject.UniqueID()
}

func (o *Cache) Get(ctx context.Context, r *relationTuple) (matchObj, matchSubject bool) {
	switch {
	case r.Namespace == "IamRole" && r.Relation == "perms":
		obj, ok1 := o.sysRoles[r.Object]
		_, ok2 := obj[ObjectOf(r.Subject)]
		return ok1, ok2
	case r.Namespace == "IamGroup" && r.Relation == "members":
		if r.Object == o.allUsers {
			return true, true
		}
		obj, ok1 := o.sysGroups[r.Object]
		_, ok2 := obj[ObjectOf(r.Subject)]
		return ok1, ok2
	case r.Namespace == "IamPolicy" && r.Relation == "check":
		p, ok1 := o.sysPolicies[r.Object]
		if !ok1 {
			return false, false
		}
		w := argsFromCtx(ctx)
		permName := w.Args[1].Value(ctx)
		permUUID := o.permUUIDs[permName]
		if permUUID == uuid.Nil {
			// TODO: logging using configured logger
			log.Println("permission is NOT found:", permName)
		}
		ok2 := p.Perms.Has(permUUID)
		sub, _ := r.Subject.(*relationtuple.SubjectSet)
		ok3 := p.Members.Has(sub.Object) || p.Members.Has(o.allUsers)
		return ok1, ok2 && ok3
	}
	return false, false
}

func (o *Cache) dump(e *Engine) {
	ctx := context.Background()
	log.Println("start dumping cache")

	roles := lo.Keys(o.sysRoles)
	names, _ := e.d.Persister().MapUUIDsToStrings(ctx, roles...)
	roleNames := map[uuid.UUID]string{}
	for i, name := range names {
		roleNames[roles[i]] = name
	}
	for u, role := range o.sysRoles {
		names, err := e.d.Persister().MapUUIDsToStrings(ctx, role.Slice()...)
		log.Println("role", roleNames[u], ":", names, "error", err)
	}

	for u, group := range o.sysGroups {
		names, err := e.d.Persister().MapUUIDsToStrings(ctx, group.Slice()...)
		log.Println("group", o.sysNames[u], ":", names, "error", err)
	}

	for u, p := range o.sysPolicies {
		names, err := e.d.Persister().MapUUIDsToStrings(ctx, p.Perms.Slice()...)
		log.Println("policy", o.sysNames[u], " perms:", names, "error", err)
		names, err = e.d.Persister().MapUUIDsToStrings(ctx, p.Members.Slice()...)
		log.Println("policy", o.sysNames[u], " members:", names, "error", err)
	}
	log.Println("finished dumping cache")
}

type BriefCache struct {
	allow     *ecache.Cache
	deny      *ecache.Cache
	relations map[string]*ast.Relation
}

func NewBriefCache() *BriefCache {
	return &BriefCache{
		allow:     ecache.NewLRUCache(1024, 1024, 10*time.Minute),
		deny:      ecache.NewLRUCache(128, 1024, 2*time.Minute),
		relations: make(map[string]*ast.Relation),
	}
}

func (o *BriefCache) Get(key string) checkgroup.Membership {
	_, ok := o.allow.GetInt64(key)
	if ok {
		return checkgroup.IsMember
	}
	_, ok = o.deny.GetInt64(key)
	if ok {
		return checkgroup.NotMember
	}
	return checkgroup.MembershipUnknown
}

func (o *BriefCache) Set(key string, v checkgroup.Membership) {
	switch v {
	case checkgroup.IsMember:
		o.allow.PutInt64(key, int64(v))
	case checkgroup.NotMember:
		o.deny.PutInt64(key, int64(v))
	}
}

func (o *BriefCache) getKey(ctx context.Context, r *relationtuple.RelationTuple) string {
	key := r.String()
	rel := o.getRelation(r)
	if rel == nil {
		// TODO: logging using configured logger
		log.Printf("relation is not found: tp=%v", r.String())
		return ""
	}

	w := argsFromCtx(ctx)
	if w == nil {
		if rel.IsFaked() {
			// TODO: logging using configured logger
			log.Printf("arg is not resolved: tp=%v", r.String())
			return ""
		}
		return key
	}
	for _, arg := range w.Args {
		switch arg.(type) {
		case ast.StringLiteralArg:
			key = arg.Value(ctx) + ":" + key
		case ast.NamedArg:
			// TODO: logging using configured logger
			log.Printf("arg is not resolved: arg=%v, tp=%v", arg, r.String())
			return ""
		}
	}
	return key
}

func (o *BriefCache) GetTuple(ctx context.Context, r *relationtuple.RelationTuple) checkgroup.Membership {
	key := o.getKey(ctx, r)
	if key == "" {
		return checkgroup.MembershipUnknown
	}
	// log.Printf("get cache: tp=%v", r.String())
	return o.Get(key)
}

func (o *BriefCache) SetTuple(ctx context.Context, r *relationtuple.RelationTuple, v checkgroup.Membership) {
	key := o.getKey(ctx, r)
	if key == "" {
		return
	}
	o.Set(r.String(), v)
}

func (o *BriefCache) getRelation(r *relationtuple.RelationTuple) *ast.Relation {
	return o.relations[r.Namespace+":"+r.Relation]
}

func (o *BriefCache) loadRelations(nm namespace.Manager) {
	nss, err := nm.Namespaces(context.Background())
	if err != nil {
		panic(err)
	}
	for _, ns := range nss {
		for _, r := range ns.Relations {
			r := r
			o.relations[ns.Name+":"+r.Name] = &r
		}
	}
}

var (
	globalCache = &sync.Map{}
)

const (
	sysCacheKey = "SysCache"
)

func (e *Engine) loadRelations() {
	nm, err := e.d.Config(context.Background()).NamespaceManager()
	if err != nil {
		panic(err)
	}
	e.briefCache.loadRelations(nm)
}

func (e *Engine) startExpansion() {
	du := cast.ToInt(os.Getenv("DBG_CACHE_REFRESH_SECONDS"))
	du = lo.Ternary(du >= 600, du, 1800)
	for {
		time.Sleep(time.Duration(du) * time.Second)
		e.fillGlobalCache(context.Background())
	}
}

func RefreshCache() {
	fmt.Println("refresh cache start")
}

func (e *Engine) fillGlobalCache(ctx context.Context) {
	c := &Cache{}

	// load system names: sys/*
	sysNames, err := e.d.Persister().LoadSysObjNameMap(ctx, nil)
	spew.Dump("keto-1", sysNames, err)
	if err != nil {
		e.d.Logger().WithError(err).Error("failed to LoadSysObjNameMap")
		return
	}
	c.sysNames = sysNames
	c.sysUUIDs = lo.MapEntries(sysNames, func(k uuid.UUID, v string) (string, uuid.UUID) { return v, k })
	c.allUsers = c.sysUUIDs["sys/allUsers"]
	e.d.Logger().WithField("count", len(sysNames)).Info("finished loading sysObjNames")
	// if c.allUsers == uuid.Nil {
	// 	e.d.Logger().Error("sys/allUsers is not found")
	// 	// return
	// }

	// loading predefined roles
	perms := Set[uuid.UUID]{}
	roles := map[uuid.UUID]Set[uuid.UUID]{}
	err = e.expandQuery(ctx, &relationtuple.RelationQuery{
		Namespace: pointerx.String("IamRole"),
		Relation:  pointerx.String("perms"),
	}, nil,
		// TODO: check namespace and relation?
		func(subset *relationtuple.SubjectSet) bool { _, ok := roles[subset.Object]; return ok },
		func(ctx context.Context, subset *relationtuple.SubjectSet, rs []relationtuple.Subject) error {
			set := NewSet[uuid.UUID]()
			roles[subset.Object] = set
			for _, r := range rs {
				set.Add(ObjectOf(r))
				perms.Add(ObjectOf(r))
			}
			return nil
		})
	if err != nil {
		e.d.Logger().WithError(err).Error("failed to load predefined roles")
		return
	}
	c.sysRoles = roles
	e.d.Logger().WithField("count", len(roles)).Info("finished loading roles")

	// map perm names to UUIDs
	permUUIDs := lo.Keys(perms)
	permNames, err := e.d.Persister().MapUUIDsToStrings(ctx, permUUIDs...)
	if err != nil {
		e.d.Logger().WithError(err).Error("failed to load perm mappings")
		return
	}
	c.permUUIDs = make(map[string]uuid.UUID, len(permUUIDs))
	for i, name := range permNames {
		c.permUUIDs[name] = permUUIDs[i]
	}
	e.d.Logger().WithField("count", len(perms)).Info("finished loading perms")

	// load sys groups: sys/*
	groups := map[uuid.UUID]Set[uuid.UUID]{}
	err = e.expandQuery(ctx, &relationtuple.RelationQuery{
		Namespace: pointerx.String("IamGroup"),
		Relation:  pointerx.String("members"),
	},
		// TODO: check namespace and relation?
		func(r *relationtuple.RelationTuple) bool { return sysNames[r.Object] != "" && r.Object != c.allUsers },
		func(subset *relationtuple.SubjectSet) bool { _, ok := groups[subset.Object]; return ok },
		func(ctx context.Context, subset *relationtuple.SubjectSet, rs []relationtuple.Subject) error {
			set := NewSet[uuid.UUID]()
			groups[subset.Object] = set
			for _, r := range rs {
				set.Add(ObjectOf(r))
			}
			return nil
		})
	if err != nil {
		e.d.Logger().WithError(err).Error("failed to load predefined groups")
		return
	}
	c.sysGroups = groups
	e.d.Logger().WithField("count", len(groups)).Info("finished loading sys groups")

	// load sys policies: sys/*
	var token string = uuid.Nil.String()
	var rs []*relationtuple.RelationTuple
	policies := map[uuid.UUID]*Policy{}
	for token != "" {
		rs, token, err = e.d.RelationTupleManager().GetRelationTuples(ctx, &relationtuple.RelationQuery{
			Namespace: pointerx.String("IamPolicy"),
		}, x.WithToken(token))
		if err != nil {
			e.d.Logger().WithError(err).Error("failed to load predefined policies")
			return
		}

		for _, r := range rs {
			if sysNames[r.Object] == "" {
				continue
			}
			p := policies[r.Object]
			if p == nil {
				p = &Policy{
					Perms:   make(Set[uuid.UUID]),
					Members: make(Set[uuid.UUID]),
				}
				policies[r.Object] = p
			}
			sub, _ := r.Subject.(*relationtuple.SubjectSet)
			if sub == nil {
				e.d.Logger().WithField("relation", r.String()).Error("should not happen, policy subject is not a subject set")
				continue
			}
			switch r.Relation {
			case "roles":
				// merge roles
				if sub.Namespace != "IamRole" || sub.Relation != "" {
					e.d.Logger().WithField("relation", r.String()).Error("should not happen, not a role")
					continue
				}
				role := roles[sub.Object]
				if role == nil {
					e.d.Logger().WithField("relation", r.String()).Warn("role not found")
					continue
				}
				p.Perms.Union(role)
			case "users":
				// merge users
				switch {
				case sub.Namespace == "IamUser" && sub.Relation == "":
					p.Members.Add(sub.Object)
				case sub.Namespace == "IamGroup" && sub.Relation == "members":
					if sub.Object == c.allUsers {
						p.Members.Add(c.allUsers)
						continue
					}

					g := groups[sub.Object]
					if g == nil {
						e.d.Logger().WithField("relation", r.String()).Error("group not found or is not a sys group")
						continue
					}
					p.Members.Union(g)
				default:
					e.d.Logger().WithField("relation", r.String()).Error("should not happen, not a user or group members")
					continue
				}
			default:
				e.d.Logger().WithField("relation", r.String()).Error("should not happen, unknown relation type")
				continue
			}
		}
		time.Sleep(10 * time.Millisecond)
	}
	c.sysPolicies = policies
	e.d.Logger().WithField("count", len(policies)).Info("finished loading sys policies")

	if cast.ToBool(os.Getenv("DUMP_CACHE")) {
		c.dump(e)
	}
	globalCache.Store(sysCacheKey, c)
}

type ctxKeyCache struct{}

func ctxWithCache(ctx context.Context) context.Context {
	v, _ := globalCache.Load(sysCacheKey)
	c, _ := v.(*Cache)
	return context.WithValue(ctx, ctxKeyCache{}, c)
}

func cacheFromCtx(ctx context.Context) *Cache {
	c, _ := ctx.Value(ctxKeyCache{}).(*Cache)
	return c
}

func useCache(ctx context.Context) bool {
	return cacheFromCtx(ctx) != nil
}

func isSysObj(ctx context.Context, obj uuid.UUID) bool {
	c := cacheFromCtx(ctx)
	if c != nil {
		return c.sysNames[obj] != ""
	}
	return false
}

func getFromCache(ctx context.Context, r *relationtuple.RelationTuple) (matchObj, matchSubject bool) {
	c := cacheFromCtx(ctx)
	if c != nil {
		return c.Get(ctx, r)
	}
	return
}

type Filter func(*relationtuple.RelationTuple) bool
type StoreExpansion func(context.Context, *relationtuple.SubjectSet, []relationtuple.Subject) error
type SkipExpansion func(*relationtuple.SubjectSet) bool

func (e *Engine) expandQuery(ctx context.Context, q *relationtuple.RelationQuery, filter Filter,
	skip SkipExpansion, store StoreExpansion) error {
	m := e.d.RelationTupleManager()

	var wfilter func(*relationtuple.RelationTuple, int) bool
	if filter != nil {
		wfilter = func(t *relationtuple.RelationTuple, _ int) bool {
			return filter(t)
		}
	}

	var (
		err        error
		res        []*relationtuple.RelationTuple
		token      = uuid.Nil.String()
		relations  int
		expansions int
	)
	for token != "" {
		res, token, err = m.GetRelationTuples(ctx, q, x.WithToken(token))
		if err != nil {
			return err
		}
		if wfilter != nil {
			res = lo.Filter(res, wfilter)
		}
		relations += len(res)
		if len(res) == 0 {
			continue
		}

		for _, r := range res {
			subset := &relationtuple.SubjectSet{Namespace: r.Namespace, Object: r.Object, Relation: r.Relation}
			if skip(subset) {
				continue
			}
			rs, err := e.expandSubjectSet(ctx, subset, skip, store)
			if err != nil {
				return err
			}
			// e.d.Logger().WithField("relation", r.String()).WithField("expansions", len(rs)).Info("relation")
			err = store(ctx, subset, rs)
			if err != nil {
				return err
			}
			expansions += len(rs)
		}
		time.Sleep(10 * time.Millisecond)
	}
	e.d.Logger().WithField("relations", relations).WithField("expansions", expansions).Info("summary")
	return nil
}

func (e *Engine) expandSubjectSet(ctx context.Context, subset *relationtuple.SubjectSet, skip SkipExpansion, store StoreExpansion) (
	[]relationtuple.Subject, error) {
	tree, err := e.expander.BuildTree(ctx, subset, 0)
	if tree == nil {
		return nil, err
	}

	return walkTree(ctx, tree, nil, skip, store), nil
}

// func (e *Engine) expandTuple(ctx context.Context, rt *relationtuple.RelationTuple, history map[uuid.UUID]Set[uuid.UUID]) (
// 	[]*relationtuple.RelationTuple, error) {
// 	if set, _ := rt.Subject.(*relationtuple.SubjectSet); set != nil && set.Relation == "" {
// 		return []*relationtuple.RelationTuple{rt}, nil
// 	}
// 	tree, err := e.expander.BuildTree(ctx, rt.Subject, 0)
// 	if tree == nil {
// 		return nil, err
// 	}

// 	subs := walkTree(tree, nil, history)
// 	dedup := make(map[string]struct{}, len(subs))
// 	rs := make([]*relationtuple.RelationTuple, 0, len(subs))
// 	for _, sub := range subs {
// 		if sub == nil {
// 			continue
// 		}
// 		if _, ok := dedup[sub.String()]; ok {
// 			continue
// 		}
// 		dedup[sub.String()] = struct{}{}
// 		rs = append(rs, &relationtuple.RelationTuple{
// 			Namespace: rt.Namespace,
// 			Relation:  rt.Relation,
// 			Object:    rt.Object,
// 			Subject:   sub,
// 		})
// 	}
// 	return rs, nil
// }

func walkTree(ctx context.Context, t *relationtuple.Tree, subjects []relationtuple.Subject, skip SkipExpansion, store StoreExpansion) []relationtuple.Subject {
	if t.Type == ketoapi.TreeNodeLeaf {
		return append(subjects, t.Subject)
	}
	for _, c := range t.Children {
		if c.Type == ketoapi.TreeNodeLeaf {
			subjects = append(subjects, c.Subject)
			continue
		}
		leafs := walkTree(ctx, c, nil, skip, store)
		subjects = append(subjects, leafs...)
		if subset, _ := c.Subject.(*relationtuple.SubjectSet); !skip(subset) {
			store(ctx, subset, leafs)
		}
	}
	return subjects
}
