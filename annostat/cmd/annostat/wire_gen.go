// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"annostat/internal/conf"
	"annostat/internal/data"
	"annostat/internal/event"
	"annostat/internal/server"
	"annostat/internal/service"
	"annostat/workflow"
	"annostat/workflow/cron"
	"annostat/workflow/jobstat"
	"github.com/go-kratos/kratos/v2/log"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, logger log.Logger) (*App, func(), error) {
	configsService := service.NewConfigsService()
	dataData, cleanup, err := data.NewData(confData, logger)
	if err != nil {
		return nil, nil, err
	}
	lotsRepo := data.NewLotsRepo(dataData, logger)
	lotphasesRepo := data.NewLotphasesRepo(dataData, logger)
	jobsRepo := data.NewJobsRepo(dataData, logger)
	jobstatsRepo := data.NewJobstatsRepo(dataData, logger)
	ordersRepo := data.NewOrdersRepo(dataData, logger)
	productionsRepo := data.NewProductionsRepo(dataData, logger)
	accuraciesRepo := data.NewAccuraciesRepo(dataData, logger)
	statsService := service.NewStatsService(lotsRepo, lotphasesRepo, jobsRepo, jobstatsRepo, ordersRepo, productionsRepo, accuraciesRepo, logger)
	grpcServer := server.NewGRPCServer(confServer, configsService, statsService, logger)
	httpServer := server.NewHTTPServer(confServer, configsService, statsService, logger)
	jobsubmitsRepo := data.NewJobsubmitsRepo(dataData, logger)
	activities := cron.NewActivities(jobsRepo, jobsubmitsRepo, productionsRepo, accuraciesRepo, logger)
	jobstatActivities := jobstat.NewActivities(jobstatsRepo, logger)
	workflowActivities := workflow.NewActivities(activities, jobstatActivities)
	bizgrantsRepo := data.NewBizgrantsRepo(dataData, logger)
	joblogsRepo := data.NewJoblogsRepo(dataData, logger)
	specgrantsRepo := data.NewSpecgrantsRepo(dataData, logger)
	backgroundTask := workflow.NewWorkflowStarter()
	eventsBiz := event.NewEventsBiz(bizgrantsRepo, jobsRepo, joblogsRepo, jobsubmitsRepo, lotsRepo, lotphasesRepo, ordersRepo, specgrantsRepo, backgroundTask, logger)
	app := newApp(logger, grpcServer, httpServer, workflowActivities, eventsBiz)
	return app, func() {
		cleanup()
	}, nil
}
