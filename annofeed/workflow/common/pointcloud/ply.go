package pointcloud

import (
	"bufio"
	"encoding/binary"
	"fmt"
	"io"
	"os"
	"strconv"
	"strings"

	"github.com/samber/lo"
)

// PLY wiki: https://en.wikipedia.org/wiki/PLY_(file_format)

type PLYFormat string

const (
	PLYFormatAscii    PLYFormat = "ascii"
	PLYFormatBinaryLE PLYFormat = "binary_little_endian"
	PLYFormatBinaryBE PLYFormat = "binary_big_endian"
)

var vertexTypeMap = map[string]string{
	"char":   "int8",
	"uchar":  "uint8",
	"short":  "int16",
	"ushort": "uint16",
	"int":    "int32",
	"uint":   "uint32",
	"float":  "float32",
	"double": "float64",
	// readable type
	"int8":    "int8",
	"uint8":   "uint8",
	"int16":   "int16",
	"uint16":  "uint16",
	"int32":   "int32",
	"uint32":  "uint32",
	"float32": "float32",
	"float64": "float64",
}

var vertexTypeSizeMap = map[string]int{
	"int8":    1,
	"uint8":   1,
	"int16":   2,
	"uint16":  2,
	"int32":   4,
	"uint32":  4,
	"float32": 4,
	"float64": 8,
}

type PLYHeader struct {
	Format   PLYFormat
	Version  float64
	Comments []string

	VertexCount  int
	VertexTypes  []string
	VertexFields []string
	RGBFlags     []bool // indicate RGB fields

	FaceCount         int
	FaceVertexIndices [][]int

	Bytes int // the size of the read content while parsing a ply file header
}

func (o *PLYHeader) ToPCDHeader() string {
	intToStringSlice := func(d []int) []string {
		return lo.Map(d, func(item int, index int) string { return strconv.Itoa(item) })
	}
	floatToStringSlice := func(d []float32) []string {
		return lo.Map(d, func(item float32, index int) string { return strconv.FormatFloat(float64(item), 'f', 4, 32) })
	}

	return fmt.Sprintf(
		`VERSION %0.1f
FIELDS %s
SIZE %s
TYPE %s
COUNT %s
WIDTH %d
HEIGHT %d
VIEWPOINT %s
POINTS %d
DATA binary
`,
		0.7, // use pcd version
		strings.Join(o.toPCDHeaderFields(), " "),
		strings.Join(intToStringSlice(o.toPCDHeaderSize()), " "),
		strings.Join(o.toPCDHeaderType(), " "),
		strings.Join(intToStringSlice(o.toPCDHeaderCount()), " "),
		o.VertexCount,
		1, // indicate the data is unorganized
		strings.Join(floatToStringSlice([]float32{0, 0, 0, 1, 0, 0, 0}), " "),
		o.VertexCount,
	)
}

func (o *PLYHeader) hasRGBFields() bool {
	rgbFieldsCount := lo.Count(o.RGBFlags, true)
	return rgbFieldsCount == 3
}

// filterOutRGBFields filters out the corresponding fields in slice `s` according to rgb flags.
func filterOutRGBFields[T any](s []T, rgbFlags []bool) []T {
	newS := lo.Filter(s, func(_ T, index int) bool { return !rgbFlags[index] })
	return newS
}

func (o *PLYHeader) toPCDHeaderFields() []string {
	fields := o.VertexFields
	if o.hasRGBFields() {
		fields = filterOutRGBFields(fields, o.RGBFlags)
		return append(fields, "rgb") // put rgb field in the last
	}
	return fields
}

func (o *PLYHeader) toPCDHeaderSize() []int {
	size := lo.Map(o.VertexTypes, func(item string, index int) int { return vertexTypeSizeMap[item] })
	if o.hasRGBFields() {
		size = filterOutRGBFields(size, o.RGBFlags)
		return append(size, 4) // rgb field uses 4 bytes
	}
	return size
}

func (o *PLYHeader) toPCDHeaderType() []string {
	var vertexTypeToPCDTypeMap = map[string]string{
		"int8":    "I",
		"uint8":   "U",
		"int16":   "I",
		"uint16":  "U",
		"int32":   "I",
		"uint32":  "U",
		"float32": "F",
		"float64": "F",
	}

	pcdType := lo.Map(o.VertexTypes, func(item string, index int) string { return vertexTypeToPCDTypeMap[item] })
	if o.hasRGBFields() {
		pcdType = filterOutRGBFields(pcdType, o.RGBFlags)
		return append(pcdType, "U") // rgb field uses uint32
	}
	return pcdType
}

func (o *PLYHeader) toPCDHeaderCount() []int {
	count := lo.Map(o.VertexTypes, func(item string, index int) int { return 1 })
	if o.hasRGBFields() {
		count = filterOutRGBFields(count, o.RGBFlags)
		return append(count, 1)
	}
	return count
}

func (o *PLYHeader) vertexSize() []int {
	return lo.Map(o.VertexTypes, func(item string, index int) int { return vertexTypeSizeMap[item] })
}

// ParsePLYHeader parses a reader to get ply header.
func ParsePLYHeader(reader *bufio.Reader) (*PLYHeader, error) {
	header := &PLYHeader{}
	firstLine, err := reader.ReadString('\n')
	if err != nil {
		return nil, err
	}
	header.Bytes += len(firstLine)
	if strings.TrimSpace(firstLine) != "ply" {
		return nil, fmt.Errorf("the first line is not 'ply'")
	}

	propertyBelongsToVertex := false
HEADER:
	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			return nil, err
		}
		header.Bytes += len(line)

		args := strings.Fields(strings.TrimSpace(line))
		if len(args) < 2 {
			if len(args) > 0 && args[0] == "end_header" {
				break HEADER
			}
			return nil, fmt.Errorf("header field must have value")
		}
		switch args[0] {
		case "format":
			if !lo.Contains([]PLYFormat{PLYFormatAscii, PLYFormatBinaryLE, PLYFormatBinaryBE}, PLYFormat(args[1])) {
				return nil, fmt.Errorf("unknown data format")
			}
			header.Format = PLYFormat(args[1])
			header.Version, err = strconv.ParseFloat(args[2], 64)
			if err != nil {
				return nil, err
			}
		case "comment":
			header.Comments = append(header.Comments, strings.Join(args[1:], " "))
		case "element":
			switch args[1] {
			case "vertex":
				count, err := strconv.Atoi(args[2])
				if err != nil {
					return nil, err
				}
				header.VertexCount = count
				propertyBelongsToVertex = true
			case "face":
				propertyBelongsToVertex = false
			default:
				// ignore unknown fields
				// return nil, fmt.Errorf("unknown element field: %s", args[1])
			}
		case "property":
			if propertyBelongsToVertex { // vertex
				standardType, ok := vertexTypeMap[args[1]]
				if !ok {
					return nil, fmt.Errorf("vertex: unsupported vertex type: %s", args[1])
				} else {
					header.VertexTypes = append(header.VertexTypes, standardType)
				}

				lowerField := strings.ToLower(args[2])
				header.VertexFields = append(header.VertexFields, lowerField)
				switch lowerField {
				case "r", "g", "b", "red", "green", "blue":
					header.RGBFlags = append(header.RGBFlags, true)
				default:
					header.RGBFlags = append(header.RGBFlags, false)
				}
			} else { // face
				// no need to process since pcd does not need this info
			}
		default:
			// ignore unknown fields
			// return nil, fmt.Errorf("unknown field: %s", args[0])
		}
	}
	return header, nil
}

// TransformPLYToPCD transforms a PLY file to PCD in binary format.
func TransformPLYToPCD(plyPath string, dstPcd string) error {
	fr, err := os.Open(plyPath)
	if err != nil {
		return err
	}
	defer fr.Close()

	fw, err := os.Create(dstPcd)
	if err != nil {
		return err
	}
	defer fw.Close()

	if err := transformPLYToPCD(fr, fw); err != nil {
		return err
	}
	return nil
}

func transformPLYToPCD(fr *os.File, fw io.Writer) error {
	reader := bufio.NewReader(fr)
	header, err := ParsePLYHeader(reader)
	if err != nil {
		return err
	}
	headerStr := header.ToPCDHeader()
	if _, err := fw.Write([]byte(headerStr)); err != nil {
		return err
	}

	switch header.Format {
	case PLYFormatAscii:
		err = transformAsciiPLYToPCD(reader, fw, header)
	case PLYFormatBinaryLE, PLYFormatBinaryBE:
		err = transformBinaryPLYToPCD(reader, fw, header, header.Format == PLYFormatBinaryLE)
	}
	return err
}

func transformAsciiPLYToPCD(reader *bufio.Reader, fw io.Writer, header *PLYHeader) error {
	pcdHeader := &PointCloudHeader{
		Fields: header.toPCDHeaderFields(),
		Size:   header.toPCDHeaderSize(),
		Type:   header.toPCDHeaderType(),
		Count:  header.toPCDHeaderCount(),
	}

	// parse data
	const batch = 8192
	lines := make([]string, batch)
	hasRGBFields := header.hasRGBFields()
	for i := 0; ; {
		count := 0
		for i < header.VertexCount+1 {
			i++

			lineBytes, _, err := reader.ReadLine()
			if err == io.EOF { // will not touch this line since we only read the lines of the quantity same as header.VertexCount
				break
			}
			if err != nil {
				return err
			}

			line := string(lineBytes)
			if hasRGBFields {
				fields := strings.Fields(line)
				newFields := parseFieldsAndRGB(fields, header.VertexFields, header.RGBFlags)
				line = strings.Join(newFields, " ")
			}

			lines[count] = line
			count++
			if count >= batch { // reach batch limit
				break
			}
		}

		if count == 0 {
			break
		}
		binaryData, err := transform(lines[:count], pcdHeader)
		if err != nil {
			return err
		}
		if _, err := fw.Write(binaryData); err != nil {
			return err
		}
	}
	return nil
}

func parseFieldsAndRGB(fields []string, fieldNames []string, rgbFlags []bool) []string {
	var red, green, blue int
	var newFields []string

	for i, field := range fields {
		if rgbFlags[i] {
			switch fieldNames[i] {
			case "r", "red":
				red, _ = strconv.Atoi(field)
			case "g", "green":
				green, _ = strconv.Atoi(field)
			case "b", "blue":
				blue, _ = strconv.Atoi(field)
			}
		} else {
			newFields = append(newFields, field)
		}
	}

	rgb := binary.LittleEndian.Uint32([]byte{byte(blue), byte(green), byte(red), 0xFF})
	return append(newFields, strconv.Itoa(int(rgb)))
}

func transformBinaryPLYToPCD(reader io.Reader, fw io.Writer, header *PLYHeader, littleEndian bool) error {
	toLittleEndian := func(data []byte, sizes []int, pointSize int) {
		for i := 0; i < len(data); i += pointSize {
			for _, size := range sizes {
				lo.Reverse(data[i : i+size])
			}
		}
	}

	const batchPoints = 1024
	hasRGBFields := header.hasRGBFields()
	sizes := header.vertexSize()
	pointSize := lo.Sum(sizes)
	buffer := make([]byte, pointSize*batchPoints)
	for {
		n, err := io.ReadFull(reader, buffer)
		if err != nil && err != io.ErrUnexpectedEOF && err != io.EOF {
			return err
		}
		if n == 0 {
			break
		}
		if n%pointSize != 0 {
			return fmt.Errorf("the quantity of data cannot be divided by point size: %d, %d", n, pointSize)
		}

		data := buffer[:n]
		if !littleEndian {
			toLittleEndian(data, sizes, pointSize)
		}

		if hasRGBFields {
			var writtenData = make([]byte, 0, pointSize*batchPoints)
			for i := 0; i < n; i += pointSize {
				pointData := parsePointAndRGB(data[i:i+pointSize], header.VertexFields, sizes, header.RGBFlags)
				writtenData = append(writtenData, pointData...)
			}
			data = writtenData
		}

		if _, err := fw.Write(data); err != nil {
			return err
		}
		if n < pointSize*batchPoints {
			break
		}
	}
	return nil
}

func parsePointAndRGB(pointBytes []byte, fields []string, sizes []int, rgbFlags []bool) []byte {
	var red, green, blue byte
	var newPoint []byte

	offset := 0
	for i, size := range sizes {
		bytes := pointBytes[offset : offset+size]
		offset += size
		if rgbFlags[i] {
			color := bytes[0] // size should be equal to 1
			switch fields[i] {
			case "r", "red":
				red = color
			case "g", "green":
				green = color
			case "b", "blue":
				blue = color
			}
		} else {
			newPoint = append(newPoint, bytes...)
		}
	}

	rgb := []byte{blue, green, red, 0xFF} // little endian: blue, green, red, 0xFF
	return append(newPoint, rgb...)
}
