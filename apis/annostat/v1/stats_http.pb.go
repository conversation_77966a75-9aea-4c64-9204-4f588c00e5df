// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: annostat/v1/stats.proto

package annostat

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationStatsGetLotCount = "/annostat.v1.Stats/GetLotCount"
const OperationStatsGetLotLabelStat = "/annostat.v1.Stats/GetLotLabelStat"
const OperationStatsGetLotStatByExecutor = "/annostat.v1.Stats/GetLotStatByExecutor"
const OperationStatsGetLotStatus = "/annostat.v1.Stats/GetLotStatus"
const OperationStatsGetOngoingLots = "/annostat.v1.Stats/GetOngoingLots"
const OperationStatsGetOrderConversion = "/annostat.v1.Stats/GetOrderConversion"
const OperationStatsGetOrderCount = "/annostat.v1.Stats/GetOrderCount"
const OperationStatsProductionByTime = "/annostat.v1.Stats/ProductionByTime"

type StatsHTTPServer interface {
	// GetLotCount count lots
	GetLotCount(context.Context, *GetLotCountRequest) (*GetLotCountReply, error)
	// GetLotLabelStat lot statistics by label
	GetLotLabelStat(context.Context, *GetLotStatusRequest) (*GetLotLabelStatReply, error)
	// GetLotStatByExecutor lot statistics by executor
	GetLotStatByExecutor(context.Context, *GetLotStatByExecutorRequest) (*GetLotStatByExecutorReply, error)
	// GetLotStatus lot statistics by phase
	GetLotStatus(context.Context, *GetLotStatusRequest) (*GetLotStatusReply, error)
	// GetOngoingLots get ongoing lots
	GetOngoingLots(context.Context, *GetOngoingLotsRequest) (*GetOngoingLotsReply, error)
	// GetOrderConversion order-to-lot conversion statistics
	GetOrderConversion(context.Context, *GetOrderCountRequest) (*GetOrderConversionReply, error)
	// GetOrderCount count orders
	GetOrderCount(context.Context, *GetOrderCountRequest) (*GetOrderCountReply, error)
	// ProductionByTime get production by time
	ProductionByTime(context.Context, *ProductionByTimeRequest) (*ProductionByTimeReply, error)
}

func RegisterStatsHTTPServer(s *http.Server, srv StatsHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/orders/count", _Stats_GetOrderCount0_HTTP_Handler(srv))
	r.GET("/v1/orders/conversion", _Stats_GetOrderConversion0_HTTP_Handler(srv))
	r.GET("/v1/lots/count", _Stats_GetLotCount0_HTTP_Handler(srv))
	r.GET("/v1/lots/ongoing", _Stats_GetOngoingLots0_HTTP_Handler(srv))
	r.GET("/v1/lots/{uid}/status", _Stats_GetLotStatus0_HTTP_Handler(srv))
	r.GET("/v1/lots/{uid}/labels", _Stats_GetLotLabelStat0_HTTP_Handler(srv))
	r.GET("/v1/lots/{uid}/phases/{phase}/stat-by-exec", _Stats_GetLotStatByExecutor0_HTTP_Handler(srv))
	r.GET("/v1/prod-by-time", _Stats_ProductionByTime0_HTTP_Handler(srv))
}

func _Stats_GetOrderCount0_HTTP_Handler(srv StatsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetOrderCountRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatsGetOrderCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOrderCount(ctx, req.(*GetOrderCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetOrderCountReply)
		return ctx.Result(200, reply)
	}
}

func _Stats_GetOrderConversion0_HTTP_Handler(srv StatsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetOrderCountRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatsGetOrderConversion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOrderConversion(ctx, req.(*GetOrderCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetOrderConversionReply)
		return ctx.Result(200, reply)
	}
}

func _Stats_GetLotCount0_HTTP_Handler(srv StatsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLotCountRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatsGetLotCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLotCount(ctx, req.(*GetLotCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetLotCountReply)
		return ctx.Result(200, reply)
	}
}

func _Stats_GetOngoingLots0_HTTP_Handler(srv StatsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetOngoingLotsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatsGetOngoingLots)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOngoingLots(ctx, req.(*GetOngoingLotsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetOngoingLotsReply)
		return ctx.Result(200, reply)
	}
}

func _Stats_GetLotStatus0_HTTP_Handler(srv StatsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLotStatusRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatsGetLotStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLotStatus(ctx, req.(*GetLotStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetLotStatusReply)
		return ctx.Result(200, reply)
	}
}

func _Stats_GetLotLabelStat0_HTTP_Handler(srv StatsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLotStatusRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatsGetLotLabelStat)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLotLabelStat(ctx, req.(*GetLotStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetLotLabelStatReply)
		return ctx.Result(200, reply)
	}
}

func _Stats_GetLotStatByExecutor0_HTTP_Handler(srv StatsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLotStatByExecutorRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatsGetLotStatByExecutor)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLotStatByExecutor(ctx, req.(*GetLotStatByExecutorRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetLotStatByExecutorReply)
		return ctx.Result(200, reply)
	}
}

func _Stats_ProductionByTime0_HTTP_Handler(srv StatsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ProductionByTimeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatsProductionByTime)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ProductionByTime(ctx, req.(*ProductionByTimeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ProductionByTimeReply)
		return ctx.Result(200, reply)
	}
}

type StatsHTTPClient interface {
	GetLotCount(ctx context.Context, req *GetLotCountRequest, opts ...http.CallOption) (rsp *GetLotCountReply, err error)
	GetLotLabelStat(ctx context.Context, req *GetLotStatusRequest, opts ...http.CallOption) (rsp *GetLotLabelStatReply, err error)
	GetLotStatByExecutor(ctx context.Context, req *GetLotStatByExecutorRequest, opts ...http.CallOption) (rsp *GetLotStatByExecutorReply, err error)
	GetLotStatus(ctx context.Context, req *GetLotStatusRequest, opts ...http.CallOption) (rsp *GetLotStatusReply, err error)
	GetOngoingLots(ctx context.Context, req *GetOngoingLotsRequest, opts ...http.CallOption) (rsp *GetOngoingLotsReply, err error)
	GetOrderConversion(ctx context.Context, req *GetOrderCountRequest, opts ...http.CallOption) (rsp *GetOrderConversionReply, err error)
	GetOrderCount(ctx context.Context, req *GetOrderCountRequest, opts ...http.CallOption) (rsp *GetOrderCountReply, err error)
	ProductionByTime(ctx context.Context, req *ProductionByTimeRequest, opts ...http.CallOption) (rsp *ProductionByTimeReply, err error)
}

type StatsHTTPClientImpl struct {
	cc *http.Client
}

func NewStatsHTTPClient(client *http.Client) StatsHTTPClient {
	return &StatsHTTPClientImpl{client}
}

func (c *StatsHTTPClientImpl) GetLotCount(ctx context.Context, in *GetLotCountRequest, opts ...http.CallOption) (*GetLotCountReply, error) {
	var out GetLotCountReply
	pattern := "/v1/lots/count"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationStatsGetLotCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *StatsHTTPClientImpl) GetLotLabelStat(ctx context.Context, in *GetLotStatusRequest, opts ...http.CallOption) (*GetLotLabelStatReply, error) {
	var out GetLotLabelStatReply
	pattern := "/v1/lots/{uid}/labels"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationStatsGetLotLabelStat))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *StatsHTTPClientImpl) GetLotStatByExecutor(ctx context.Context, in *GetLotStatByExecutorRequest, opts ...http.CallOption) (*GetLotStatByExecutorReply, error) {
	var out GetLotStatByExecutorReply
	pattern := "/v1/lots/{uid}/phases/{phase}/stat-by-exec"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationStatsGetLotStatByExecutor))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *StatsHTTPClientImpl) GetLotStatus(ctx context.Context, in *GetLotStatusRequest, opts ...http.CallOption) (*GetLotStatusReply, error) {
	var out GetLotStatusReply
	pattern := "/v1/lots/{uid}/status"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationStatsGetLotStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *StatsHTTPClientImpl) GetOngoingLots(ctx context.Context, in *GetOngoingLotsRequest, opts ...http.CallOption) (*GetOngoingLotsReply, error) {
	var out GetOngoingLotsReply
	pattern := "/v1/lots/ongoing"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationStatsGetOngoingLots))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *StatsHTTPClientImpl) GetOrderConversion(ctx context.Context, in *GetOrderCountRequest, opts ...http.CallOption) (*GetOrderConversionReply, error) {
	var out GetOrderConversionReply
	pattern := "/v1/orders/conversion"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationStatsGetOrderConversion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *StatsHTTPClientImpl) GetOrderCount(ctx context.Context, in *GetOrderCountRequest, opts ...http.CallOption) (*GetOrderCountReply, error) {
	var out GetOrderCountReply
	pattern := "/v1/orders/count"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationStatsGetOrderCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *StatsHTTPClientImpl) ProductionByTime(ctx context.Context, in *ProductionByTimeRequest, opts ...http.CallOption) (*ProductionByTimeReply, error) {
	var out ProductionByTimeReply
	pattern := "/v1/prod-by-time"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationStatsProductionByTime))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
