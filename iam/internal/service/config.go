// source: gitlab.rp.konvery.work/platform/apis/iam/v1/config.proto
package service

import (
	"context"

	"iam/internal/biz"
	"iam/internal/conf"

	"gitlab.rp.konvery.work/platform/apis/iam/v1"
	"gitlab.rp.konvery.work/platform/apis/types"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"google.golang.org/protobuf/types/known/emptypb"
)

type ConfigsService struct {
	iam.UnimplementedConfigsServer
	metarepo biz.MetasRepo
	log      *log.Helper
}

func NewConfigsService(metarepo biz.MetasRepo, logger log.Logger) *ConfigsService {
	return &ConfigsService{metarepo: metarepo, log: log.NewHelper(logger)}
}

func (o *ConfigsService) ListErrors(ctx context.Context, req *emptypb.Empty) (*iam.Errors, error) {
	locales := map[string]*types.Multilingual{}
	for k, v := range errors.LocalizedErrors {
		locales[k] = &types.Multilingual{Langs: v}
	}
	return &iam.Errors{Errors: locales}, nil
}

func (o *ConfigsService) GetVersion(ctx context.Context, req *emptypb.Empty) (*iam.GetVersionReply, error) {
	return &iam.GetVersionReply{Version: conf.Version}, nil
}

func (o *ConfigsService) GetConf(ctx context.Context, req *types.Name) (*types.NameValue, error) {
	op := biz.UserFromCtx(ctx)
	if !biz.IsPrivilegedUser(op.User) {
		return nil, errors.NewErrForbidden()
	}
	val, err := o.metarepo.Get(ctx, req.Name)
	if err != nil {
		return nil, err
	}
	return &types.NameValue{Name: req.Name, Value: val}, nil
}

func (o *ConfigsService) SetConf(ctx context.Context, req *types.NameValue) (*emptypb.Empty, error) {
	op := biz.UserFromCtx(ctx)
	if !biz.IsPrivilegedUser(op.User) {
		return nil, errors.NewErrForbidden()
	}
	if req.Value == "" {
		return &emptypb.Empty{}, o.metarepo.Delete(ctx, req.Name)
	}
	return &emptypb.Empty{}, o.metarepo.Set(ctx, req.Name, req.Value)
}
