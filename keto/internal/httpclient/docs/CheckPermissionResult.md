# CheckPermissionResult

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Allowed** | **bool** | whether the relation tuple is allowed | 

## Methods

### NewCheckPermissionResult

`func NewCheckPermissionResult(allowed bool, ) *CheckPermissionResult`

NewCheckPermissionResult instantiates a new CheckPermissionResult object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewCheckPermissionResultWithDefaults

`func NewCheckPermissionResultWithDefaults() *CheckPermissionResult`

NewCheckPermissionResultWithDefaults instantiates a new CheckPermissionResult object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetAllowed

`func (o *CheckPermissionResult) GetAllowed() bool`

GetAllowed returns the Allowed field if non-nil, zero value otherwise.

### GetAllowedOk

`func (o *CheckPermissionResult) GetAllowedOk() (*bool, bool)`

GetAllowedOk returns a tuple with the Allowed field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetAllowed

`func (o *CheckPermissionResult) SetAllowed(v bool)`

SetAllowed sets Allowed field to given value.



[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


