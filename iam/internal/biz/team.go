// source: gitlab.rp.konvery.work/platform/apis/iam/v1/team.proto
package biz

import (
	"context"
	"time"

	"iam/pkg/keto"

	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

const (
	TeamTypeUnknown  = 0
	TeamTypeDemander = 1
	TeamTypeOperator = 2
	TeamTypeSupplier = 3
)

type Hierarchy = serial.Slice[int64]

type Team struct {
	ID int64 `json:"id" gorm:"default:null"`
	// Uid       string    `json:"uid" gorm:"default:null"`
	Name      string    `json:"name" gorm:"default:null"`
	Desc      string    `json:"desc" gorm:"default:null"`
	Type      int       `json:"type" gorm:"default:null"`
	Avatar    string    `json:"avatar" gorm:"default:null"`
	Province  string    `json:"province" gorm:"default:null"`
	City      string    `json:"city" gorm:"default:null"`
	ParentID  int64     `json:"parent_id" gorm:"default:null"`
	Hierarchy Hierarchy `json:"hierarchy" gorm:"default:null"`
	Status    int       `json:"status" gorm:"default:null"`
	CreatorID int64     `json:"creator_id" gorm:"default:null"`
	CreatedAt time.Time `json:"created_at" gorm:"default:null"`
	UpdatedAt time.Time `json:"updated_at" gorm:"default:null"`
	DeletedAt DeletedAt `json:"deleted_at" gorm:"default:null"`
}

func (o *Team) GetID() int64   { return o.ID }
func (o *Team) GetUid() string { return kid.StringID(o.ID) }
func (o *Team) UidFld() string { panic("code error") }
func (o *Team) EnsureID() {
	if o.ID == 0 {
		o.ID = kid.NewID()
	}
}

func (o *Team) IsOrg() bool    { return o.ParentID == 0 }
func (o *Team) IsActive() bool { return o.Status == UserStatusActive && o.DeletedAt.Valid }

// func (o *Team) GetHierarchy() []string {
// 	h := append(o.Hierarchy, o.ID)
// 	return util.Map(func(v int64) string { return strconv.Itoa(int(v)) }, h)
// }

const TeamTableName = "teams"

var (
	team_             = field.RegObject(&Team{})
	TeamUpdatableFlds = field.NewModel(TeamTableName, team_, "Name", "Avatar", "Province", "City", "Desc", "Status")

	// field name in snake case
	Team_Name     = field.Sname(&team_.Name)
	Team_Avatar   = field.Sname(&team_.Avatar)
	Team_Province = field.Sname(&team_.Province)
	Team_City     = field.Sname(&team_.City)
	Team_Desc     = field.Sname(&team_.Desc)
	Team_Status   = field.Sname(&team_.Status)
	Team_Type     = field.Sname(&team_.Type)
	Team_ParentID = field.Sname(&team_.ParentID)
)

type TeamListFilter struct {
	Pagesz int
	Page   int

	IDs         []int64
	ParentID    *int64
	NamePattern string
	TeamType    int
}

func (o *TeamListFilter) Apply(tx Tx) Tx {
	if tx == nil {
		return nil
	}

	tbl := TeamTableName + "."
	tx = repo.ApplyFieldFilter(tx, tbl+"id", o.IDs)
	if o.ParentID != nil {
		tx = repo.ApplyFieldFilter(tx, tbl+Team_ParentID, *o.ParentID)
	}
	tx = repo.ApplyPatternFilter(tx, tbl+Team_Name, o.NamePattern)
	if o.TeamType != TeamTypeUnknown {
		tx = repo.ApplyFieldFilter(tx, tbl+Team_Type, o.TeamType)
	}
	return tx
}

type MemberListFilter struct {
	Pagesz int
	Page   int
	Role   string

	NamePattern string
}

type TeamsRepo interface {
	repo.GenericRepo[Team]

	// team member operations
	CountMembers(ctx context.Context, id int64, filter *MemberListFilter) (int, error)
	ListMembers(ctx context.Context, id int64, filter *MemberListFilter) ([]*User, error)
	GetMembership(ctx context.Context, id int64, users []int64) ([]*TeamMember, error)
	SetMembersRole(ctx context.Context, id int64, role string, userIDs []int64) error
	EditMembers(ctx context.Context, isAdd bool, teamID int64, role string, userIDs []int64) error
	DeleteAllMembers(ctx context.Context, teamID int64) error
}

type TeamsBiz struct {
	repo     TeamsRepo
	userRepo UsersRepo
	log      *log.Helper
}

func NewTeamsBiz(repo TeamsRepo, userRepo UsersRepo, logger log.Logger) *TeamsBiz {
	return &TeamsBiz{repo: repo, userRepo: userRepo, log: log.NewHelper(logger)}
}

func (o *TeamsBiz) Create(ctx context.Context, p *Team, owner *User, parent *Team) (t *Team, err error) {
	o.log.Debug(ctx, "CreateTeam", "param", p)
	err = o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		t, err = o.repo.Create(ctx, p)
		if err != nil {
			return err
		}
		ouid, puid := "", ""
		if owner != nil && owner.ID > 0 {
			ouid = owner.GetUid()
			err = o.repo.EditMembers(ctx, true, t.ID, TeamRoleOwner, []int64{owner.ID})
			if err != nil {
				return err
			}
			if t.IsOrg() {
				// this is an organization
				err = o.userRepo.JoinOrg(ctx, t.ID, TeamRoleOwner, owner.ID)
			}
		}
		if err != nil {
			return err
		}
		if parent != nil {
			puid = parent.GetUid()
		}
		return keto.DefaultAccessMgr().CreateGroup(ctx, t.GetUid(), ouid, puid)
	})
	return
}

func (o *TeamsBiz) Update(ctx context.Context, p *Team, fldMask *FieldMask) (*Team, error) {
	o.log.Debug(ctx, "UpdateTeam", "param", p)
	return o.repo.Update(ctx, p, fldMask)
}

func (o *TeamsBiz) GetByUid(ctx context.Context, uid string) (*Team, error) {
	return o.GetByID(ctx, kid.ParseID(uid))
}

func (o *TeamsBiz) GetByID(ctx context.Context, id int64) (*Team, error) {
	return o.repo.GetByID(ctx, id)
}

func (o *TeamsBiz) DeleteByUid(ctx context.Context, uid string) error {
	o.log.Debug(ctx, "DeleteTeam", "uid", uid)
	return o.DeleteByID(ctx, kid.ParseID(uid))
}

func (o *TeamsBiz) DeleteByID(ctx context.Context, teamID int64) error {
	o.log.Debug(ctx, "DeleteTeam", "id", teamID)

	// do not delete team with subTeams
	cnt, err := o.repo.Count(ctx, &TeamListFilter{ParentID: &teamID})
	if err != nil {
		return err
	}
	if cnt > 0 {
		return errors.NewErrFailedPreConditionNotEmpty(errors.WithMessage("cannot delete team with sub_teams"))
	}

	team, err := o.repo.GetByID(ctx, teamID)
	if err != nil {
		return err
	}
	return o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		if team.IsOrg() {
			// this is an organization
			if err := o.userRepo.DisbandOrg(ctx, teamID); err != nil {
				return err
			}
		}
		err = o.repo.DeleteByID(ctx, teamID)
		if err != nil {
			return err
		}
		return keto.DefaultAccessMgr().DeleteGroup(ctx, team.GetUid())
	})
}

func (o *TeamsBiz) ListByIDs(ctx context.Context, ids []int64) ([]*Team, error) {
	teams, _, err := o.repo.List(ctx, &TeamListFilter{IDs: ids}, repo.Pager{})
	return teams, err
}

func (o *TeamsBiz) List(ctx context.Context, filter *TeamListFilter) ([]*Team, error) {
	// o.log.WithContext(ctx).Infof("ListTeam: param={%+v}", filter)
	teams, _, err := o.repo.List(ctx, filter, repo.Pager{Pagesz: filter.Pagesz, Page: filter.Page})
	return teams, err
}

// Count returns the number of items matching the filter, ignore the Page and Pagesz.
func (o *TeamsBiz) Count(ctx context.Context, filter *TeamListFilter) (int64, error) {
	return o.repo.Count(ctx, filter)
}

func (o *TeamsBiz) ListMembers(ctx context.Context, id int64, filter *MemberListFilter) ([]*User, error) {
	// o.log.WithContext(ctx).Infof("GetMembers: team-id=%v", id)
	return o.repo.ListMembers(ctx, id, filter)
}

// CountMembers returns the number of items matching the filter, ignore the Page and Pagesz.
func (o *TeamsBiz) CountMembers(ctx context.Context, id int64, filter *MemberListFilter) (int, error) {
	return o.repo.CountMembers(ctx, id, filter)
}

// AddMembers moves users to the team.
func (o *TeamsBiz) AddMembers(ctx context.Context, team *Team, role string, userIDs ...int64) error {
	teamID := team.ID
	o.log.Debug(ctx, "AddMembers", "team-id", teamID, "user-ids", userIDs)
	if role == "" {
		role = TeamRoleMember
	}
	if !lo.Contains(TeamRoles(), role) {
		return errors.NewErrInvalidField(errors.WithFields("role"))
	}
	return o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		if team.IsOrg() {
			// this is an organization
			if err := o.userRepo.JoinOrg(ctx, teamID, role, userIDs...); err != nil {
				return err
			}
		}
		err := o.repo.EditMembers(ctx, true, teamID, role, userIDs)
		if err != nil {
			return err
		}
		return keto.DefaultAccessMgr().AddGroupMembers(ctx, team.GetUid(), role, team.IsOrg(), GetUids(userIDs))
	})
}

// DeleteMembers deletes members from the team.
func (o *TeamsBiz) DeleteMembers(ctx context.Context, team *Team, userIDs []int64) error {
	teamID := team.ID
	o.log.Debug(ctx, "DeleteMembers", "team-id", teamID, "user-ids", userIDs)
	return o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		if team.IsOrg() {
			// this is an organization
			if err := o.userRepo.LeaveOrg(ctx, userIDs...); err != nil {
				return err
			}
		}
		err := o.repo.EditMembers(ctx, false, teamID, "", userIDs)
		if err != nil {
			return err
		}
		teamUid := team.GetUid()
		userUids := GetUids(userIDs)
		for _, role := range TeamRoles() {
			err = keto.DefaultAccessMgr().DeleteGroupMembers(ctx, teamUid, role, team.IsOrg(), userUids)
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// SetMemberRole changes team members' role.
func (o *TeamsBiz) SetMembersRole(ctx context.Context, team *Team, role string, userIDs ...int64) error {
	teamID := team.ID
	o.log.Debug(ctx, "SetMemberRole", "team-id", teamID, "user-ids", userIDs, "role", role)

	mems, err := o.repo.GetMembership(ctx, teamID, userIDs)
	if err != nil {
		return err
	}
	if len(mems) != len(userIDs) {
		return errors.NewErrFailedPreConditionNotMatch(errors.WithMessage("not all users are members of the team"))
	}

	return o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		if team.IsOrg() {
			// this is an organization
			if err := o.userRepo.SetRole(ctx, role, userIDs...); err != nil {
				return err
			}
		}
		err := o.repo.SetMembersRole(ctx, teamID, role, userIDs)
		if err != nil {
			return err
		}
		teamUid := team.GetUid()
		userUids := GetUids(userIDs)
		for _, role := range TeamEditorRoles() {
			err = keto.DefaultAccessMgr().DeleteGroupMembers(ctx, teamUid, role, false, userUids)
			if err != nil {
				return err
			}
		}
		return keto.DefaultAccessMgr().AddGroupMembers(ctx, teamUid, role, false, userUids)
	})
}
