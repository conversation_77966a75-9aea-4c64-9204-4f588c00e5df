package interpolate

import (
	"gitlab.rp.konvery.work/platform/apis/anno/v1"

	go3dquat "github.com/ungerik/go3d/float64/quaternion"
	go3dvec3 "github.com/ungerik/go3d/float64/vec3"
)

func init() {
	RegisterInterpolator(anno.WidgetName_cuboid, cuboid3dInterpolate)
}

// cuboid3dInterpolate generates a new object using linear interpolation.
func cuboid3dInterpolate(o1, o2 *anno.Object, span, distance float64) *anno.Object {
	o := cloneObject(o1)
	o.Source = anno.Object_Source_interpolation
	if o2 == nil {
		return o
	}

	// x, y, z, sx, sy, sz, qx, qy, qz, qw
	const quatOffset = 6
	o.Label.Widget.Data = calLinearInterpolation(
		o1.Label.Widget.Data[:quatOffset],
		o2.Label.Widget.Data[:quatOffset],
		distance/span,
	)
	o.Label.Widget.Data = append(o.Label.Widget.Data, 0, 0, 0, 0)
	data := o.Label.Widget.Data

	interQuat := slerp(Quat(o1.Label.Widget.Data[quatOffset:]), Quat(o2.Label.Widget.Data[quatOffset:]), distance/span)
	copy(data[quatOffset:], interQuat[:])

	// do interpolation for internal gaps
	for i, gap := range o.Label.Widget.Gaps {
		switch gap.Name {
		case anno.WidgetName_cuboid:
			gap1 := o1.Label.Widget.Gaps[i]
			gap2 := o2.Label.Widget.Gaps[i]
			gap.Data = calLinearInterpolation(gap1.Data[:quatOffset], gap2.Data[:quatOffset], distance/span)

			interQuat := slerp(Quat(gap1.Data[quatOffset:]), Quat(gap2.Data[quatOffset:]), distance/span)
			copy(gap.Data[quatOffset:], interQuat[:])
		default:
			panic("unsupported gap widget: " + gap.Name.String())
		}
	}

	// do interpolation for forward
	data1 := o1.Label.Widget.Data       // o1 data
	forward1 := o1.Label.Widget.Forward // o1 forward
	newForward := calDirection3D(
		forward1,
		Point3D(data1[:3]),
		[3]float64{data[0] - data1[0], data[1] - data1[1], data[2] - data1[2]},
		Quat(substractSlice(data[quatOffset:], data1[quatOffset:])[:4]),
	)
	o.Label.Widget.Forward = newForward

	return o
}

func substractSlice(a []float64, b []float64) []float64 {
	res := make([]float64, len(a))
	for i := range a {
		res[i] = a[i] - b[i]
	}
	return res
}

// calDirection3D calculates a new direction for an interpolated 3D cuboid.
func calDirection3D(dir *anno.Direction, center Point3D, offset [3]float64, rotation Quat) *anno.Direction {
	// rotate
	origin := dir.Origin
	toward := dir.Toward
	rotateCenter := center
	var rotatedOrigin = Point3D{}
	if len(origin) != 0 {
		rotatedOrigin = rotateByQuaternion(Point3D(origin[:3]), rotateCenter, rotation)
	}
	rotatedToward := rotateByQuaternion(Point3D(toward[:3]), rotateCenter, rotation)

	// translation by offset
	newDir := &anno.Direction{}
	offsetP := Point3D(offset)
	if len(origin) != 0 {
		newDir.Origin = rotatedOrigin.Add(&offsetP)[:3]
	}
	newDir.Toward = rotatedToward.Add(&offsetP)[:3]

	return newDir
}

type Point3D = go3dvec3.T

// rotateByQuaternion rotates a 3d vector around center by a quaternion.
func rotateByQuaternion(point, center Point3D, quat Quat) Point3D {
	if quat == (Quat{}) { // if quat is empty, return point unchanged
		return point
	}

	quatN := quat.Normalize() // normalize quaternion
	rotated := quatN.RotatedVec3(point.Sub(&center))
	return Point3D(rotated.Add(&center)[:3])
}

type Quat = go3dquat.T // x, y, z, w

func slerp(qa, qb Quat, t float64) Quat {
	if qa == qb { // if qa == qb, no need to interpolate
		return qa
	}
	interQ := go3dquat.Slerp(&qa, &qb, t)
	return interQ
}
