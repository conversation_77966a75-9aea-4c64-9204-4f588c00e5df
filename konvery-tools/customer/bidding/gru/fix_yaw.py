import json
import os
import sys
import glob
import pandas as pd
import numpy as np

sys.path.append(".")
from customer.common import tools


def get_ego_yaw_map(pose_file):
    df = pd.read_csv(pose_file)
    df.columns = [i.strip().lower() for i in df.columns]
    timestamp2ego_yaw = {}
    for row in df.itertuples():
        timestamp2ego_yaw[row.timestamp] = row.yaw
    return timestamp2ego_yaw


def process_clip(clip_dir, pose_file):
    timestamp2ego_yaw = get_ego_yaw_map(pose_file)

    label_dir = os.path.join(clip_dir, 'label')
    track_id2yaw = dict()
    # step one, find max yaw for every obj
    for label_file in tools.listdir(label_dir):
        label_data = tools.get_json_data(os.path.join(label_dir, label_file))
        timestamp = int(label_file.replace('.json', '').replace('_', ''))
        ego_yaw = timestamp2ego_yaw[timestamp]
        for obj in label_data['lidar']:
            track_id = obj['track_id']
            point_num = obj['point_num']
            if track_id not in track_id2yaw or point_num > track_id2yaw[track_id]['point_num']:
                track_id2yaw[track_id] = {
                    'yaw': obj['annotation']['data']['rotation']['z'],
                    'point_num': point_num,
                    'ego_yaw': ego_yaw
                }
    # step two, modify other obj
    for label_file in tools.listdir(label_dir):
        label_data = tools.get_json_data(os.path.join(label_dir, label_file))
        timestamp = int(label_file.replace('.json', '').replace('_', ''))
        for obj in label_data['lidar']:
            if not obj['attrs']['Is_it_a_stationary_object'][0].lower() == 'true':
                continue
            track_id = obj['track_id']

            max_yaw = track_id2yaw[track_id]['yaw']
            max_ego_yaw = track_id2yaw[track_id]['ego_yaw']
            cur_ego_yaw = timestamp2ego_yaw[timestamp]
            obj['annotation']['data']['rotation']['z'] = max_yaw + max_ego_yaw - cur_ego_yaw

        tools.write_json_file(label_data, os.path.join(label_dir, label_file), indent=4)


def run(input_dir):
    poses_dir = os.path.join(input_dir, 'pose')
    ds_dirs = [ds for ds in tools.listdir(input_dir, full_path=True) if os.path.isdir(ds) and os.path.basename(ds).startswith("ds_")]
    for ds_dir in ds_dirs:
        config_files = glob.glob(os.path.join(ds_dir, '**', 'config.json'), recursive=True)
        for config_file in config_files:
            clip_dir = os.path.dirname(config_file)
            pose_dir = os.path.join(poses_dir, os.path.basename(clip_dir))
            pose_file = os.path.join(pose_dir, 'pose.csv')
            process_clip(clip_dir, pose_file)


if __name__ == '__main__':
    args = tools.get_args()
    run(args.input)
