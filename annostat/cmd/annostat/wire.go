//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"annostat/internal/conf"
	"annostat/internal/data"
	"annostat/internal/event"
	"annostat/internal/server"
	"annostat/internal/service"
	"annostat/workflow"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init kratos application.
func wireApp(*conf.Server, *conf.Data, log.Logger) (*App, func(), error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, service.ProviderSet, workflow.ProviderSet, //biz.ProviderSet,
		event.ProviderSet,
		newApp))
}
