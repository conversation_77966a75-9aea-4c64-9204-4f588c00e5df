{{- if .Values.ingress.write.enabled -}}
{{- $fullName := include "keto.fullname" . -}}
{{- if semverCompare ">=1.19-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: networking.k8s.io/v1
{{- else -}}
apiVersion: networking.k8s.io/v1beta1
{{- end }}
kind: Ingress
metadata:
  name: {{ $fullName }}-write
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "keto.labels" . | nindent 4 }}
  {{- with .Values.ingress.write.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  ingressClassName: {{ .Values.ingress.write.className }}
  {{- if .Values.ingress.write.tls }}
  tls:
    {{- range .Values.ingress.write.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      secretName: {{ .secretName }}
    {{- end }}
  {{- end }}
  rules:
    {{- range .Values.ingress.write.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}
            {{- if .pathType }}
            pathType: {{ .pathType }}
            {{- end }}
            backend:
              {{- if semverCompare ">=1.19-0" $.Capabilities.KubeVersion.GitVersion }}
              service:
                name: {{ $fullName }}-write
                port:
                  name: {{ $.Values.service.write.name }}
              {{- else }}
              serviceName: {{ $fullName }}
              servicePort: {{ $.Values.service.write.name }}
              {{- end }}
          {{- end }}
    {{- end }}
{{- end }}
