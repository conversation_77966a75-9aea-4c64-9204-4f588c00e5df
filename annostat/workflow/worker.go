package workflow

import (
	"context"
	"fmt"
	"time"

	"annostat/internal/biz"
	"annostat/internal/conf"
	"annostat/workflow/common"
	"annostat/workflow/cron"
	"annostat/workflow/jobstat"

	"github.com/google/wire"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"gitlab.rp.konvery.work/platform/pkg/wf/tracing"
	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/worker"
)

var ProviderSet = wire.NewSet(NewWorkflowStarter, NewActivities, cron.NewActivities, jobstat.NewActivities)
var (
	cfg    *conf.Temporal
	defcli client.Client
)

func Init(c *conf.Temporal, logger log.Logger) {
	cfg = c
	var err error
	defcli, err = client.Dial(client.Options{
		HostPort:           cfg.Addr,
		Namespace:          cfg.Namespace,
		Logger:             log.NewTemporalLogger(logger),
		ContextPropagators: tracing.GetContextPropagators(),
	})
	if err != nil {
		panic(fmt.Errorf("failed to create client: %v", err))
	}
	if err := startCronJobs(context.Background()); err != nil {
		panic(err)
	}
}

type Activities struct {
	cron    *cron.Activities
	lotstat *jobstat.Activities
}

func NewActivities(cronact *cron.Activities, lsact *jobstat.Activities) *Activities {
	return &Activities{cron: cronact, lotstat: lsact}
}

func StartWorker(act *Activities) {
	cron.SetTemporalClient(act.cron, defcli)
	w := worker.New(defcli, cfg.TaskQueue, worker.Options{})
	w.RegisterActivity(act.cron)
	w.RegisterActivity(act.lotstat)
	w.RegisterWorkflow(cron.HourlySumWorkflow)
	w.RegisterWorkflow(cron.DailySumWorkflow)
	w.RegisterWorkflow(jobstat.JobstatWorkflow)
	err := w.Run(worker.InterruptCh())
	if err != nil {
		panic(fmt.Errorf("worker exited unexpectedly: %w", err))
	}
}

func startCronJobs(ctx context.Context) error {
	err := StartCronJob(ctx, "5 * * * *", cron.HourlySumWfID, cron.HourlySumWorkflow)
	if err != nil {
		return err
	}
	return StartCronJob(ctx, "30 20 * * *", cron.DailySumWfID, cron.DailySumWorkflow) // UTC time (04:30 Beijing time)
}

func StartCronJob(ctx context.Context, schedule, name string, wffunc any, args ...any) error {
	retrypolicy := &temporal.RetryPolicy{
		InitialInterval:    time.Second,
		BackoffCoefficient: 2.0,
		MaximumInterval:    time.Second * 100,
		MaximumAttempts:    10,
	}
	options := client.StartWorkflowOptions{
		ID:           name,
		TaskQueue:    cfg.TaskQueue,
		CronSchedule: schedule,
		RetryPolicy:  retrypolicy,
	}

	ctx = tracing.PropagateTraceID(ctx)
	_, err := defcli.ExecuteWorkflow(ctx, options, wffunc)
	if err != nil {
		return fmt.Errorf("failed to StartCronJob %v: %w", name, err)
	}
	return nil
}

type workflowStarter struct{}

func NewWorkflowStarter() biz.BackgroundTask {
	return &workflowStarter{}
}

func (o *workflowStarter) StartWorkflow(ctx context.Context, wfid string, wffunc any, args ...any) error {
	retrypolicy := &temporal.RetryPolicy{
		InitialInterval:    time.Second,
		BackoffCoefficient: 1.0,
		MaximumInterval:    time.Second * 100,
		MaximumAttempts:    20,
	}
	options := client.StartWorkflowOptions{
		ID:          wfid,
		TaskQueue:   cfg.TaskQueue,
		RetryPolicy: retrypolicy,
	}
	_, err := defcli.ExecuteWorkflow(ctx, options, wffunc, args...)
	if err != nil {
		return fmt.Errorf("failed to StartWorkflow: %w", err)
	}
	return nil
}

func (o *workflowStarter) SignalEvent(ctx context.Context, wfid string, ev *common.Event) error {
	err := defcli.SignalWorkflow(ctx, wfid, "", common.DefaultSignalName, ev)
	if err != nil {
		return fmt.Errorf("failed to signal workflow %v: %w", wfid, err)
	}
	return nil
}

func (o *workflowStarter) SignalEventWithStart(ctx context.Context, wfid string, ev *common.Event, wffunc any,
	wfArgs ...any) error {
	retrypolicy := &temporal.RetryPolicy{
		InitialInterval:    time.Second,
		BackoffCoefficient: 1.0,
		MaximumInterval:    time.Second * 100,
		MaximumAttempts:    20,
	}
	options := client.StartWorkflowOptions{
		ID:          wfid,
		TaskQueue:   cfg.TaskQueue,
		RetryPolicy: retrypolicy,
	}
	_, err := defcli.SignalWithStartWorkflow(ctx, wfid, common.DefaultSignalName, ev, options, wffunc, wfArgs...)
	if err != nil {
		return fmt.Errorf("failed to signal workflow %v: %w", wfid, err)
	}
	return nil
}
