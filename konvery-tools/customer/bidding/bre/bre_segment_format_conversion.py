import os
import sys
import numpy as np
import json

sys.path.append(".")
from customer.common import tools
from customer.common import pypcd


def run(input_dir, output_dir):
    label_mark_data = {
        'fields': ['label'],
        'count': [1],
        'size': [4],
        'type': ['U'],
    }
    category_mapping = {
        0: "obj",
        1: "ground",
        2: "curb",
        3: "vege",
        4: "fence",
        6: "unknown",
        7: "noise",
        8: "wallcolumn"
    }
    word_to_number = {v: k for k, v in category_mapping.items()}
    label_subdir_list = tools.find_dir_by_pre_name(input_dir, "label")
    for label_subdir in label_subdir_list:
        json_files = tools.get_json_files(label_subdir)
        for j in json_files:
            with open(j.path, 'r') as file:
                data = json.load(file)
            label = [word_to_number[word] for word in data["segmentation"]["result"][0]]
            label = np.array(label, dtype=np.dtype([('label', np.uint32)]))
            parts = str(j.path).split('/')
            parts[-2] = "lidar"
            parts[-1] = os.path.basename(j)[:-5] + ".pcd"
            pcd = '/'.join(parts)
            pc = pypcd.point_cloud_from_path(pcd)
            pc = pypcd.delete_fields(pc, ['label'])
            pc = pypcd.add_fields(pc, label_mark_data, label)
            os.makedirs(os.path.join(output_dir, label_subdir.split('/')[-4], label_subdir.split('/')[-2], "labeled"),
                        exist_ok=True)
            out_pcd_file = os.path.join(
                os.path.join(output_dir, label_subdir.split('/')[-4], label_subdir.split('/')[-2], "labeled"),
                parts[-1])
            pc.save_pcd(out_pcd_file)


if __name__ == '__main__':
    args = tools.get_args()
    input_dir = args.input
    output_dir = args.out
    run(input_dir, output_dir)
