import json
import os
import yaml
import sys

import numpy as np
from scipy.spatial.transform import Rotation as R

sys.path.append(".")
from customer.common import tools

ROOT = "/Users/<USER>/work/data/Dongfeng/raw/中海庭4D试标数据"
DATA_PATH = os.path.join(ROOT, "4d_test/yunce_lh_1_20231121_chunsun_part1")
PARAM_PATH = os.path.join(ROOT, "标定参数")
LIDAR_CONFIG_FILE = os.path.join(PARAM_PATH, "lidar_cfg_bus_16_20231110")
CAMERA_CONFIG_FILE = os.path.join(PARAM_PATH, "cam_cfg_bus_16_20231110")
OUTPUT = "/Users/<USER>/work/data/Dongfeng/output/all"

lidar_configs = {}
camara_configs = {}
# 暂时用第一帧自车高斯坐标作为原点
origin_translation = np.array([337035695, 830892, 28.3660283023])


def read_yaml_file(file):
    with open(file, 'r') as fr:
        data = yaml.safe_load(fr)
        return data


def read_json_file(file):
    with open(file, "r") as fr:
        data = json.load(fr)
        return data


def filter_os_file(files):
    return [f for f in files if not f.startswith(".")]


def pose_to_mat(pose):
    # pose is like [x, y, z, yaw, pitch, roll] or [x, y, z, qx, qy, qy, qw]
    if len(pose) == 6:
        rotation = R.from_euler("ZYX", pose[3:], degrees=True)
    elif len(pose) == 7:
        rotation = R.from_quat(pose[3:])
    else:
        raise Exception("wrong pose" + str(pose))
    mat = rotation.as_matrix()
    mat44 = np.zeros((4, 4))
    mat44[:3, :3] = mat
    mat44[0, 3] = pose[0]
    mat44[1, 3] = pose[1]
    mat44[2, 3] = pose[2]
    mat44[3, 3] = 1
    return mat44


def parse_lidar_configs():
    data = read_json_file(LIDAR_CONFIG_FILE)
    lidar_cfg = data["lidar_cfg"]
    for cfg in lidar_cfg:
        lidar_pos = cfg["lidarPos"]
        lidar_type = cfg["lidarType"],
        liar_to_vehicle = cfg["lidar2VehicleCalibParam"]
        lidar_configs[lidar_pos] = {
            "type": lidar_type,
            "pose": [
                liar_to_vehicle["translationX"],
                liar_to_vehicle["translationY"],
                liar_to_vehicle["translationZ"],
                liar_to_vehicle["yaw"],
                liar_to_vehicle["pitch"],
                liar_to_vehicle["roll"],
            ]
        }


def parse_camera_config():
    data = read_json_file(CAMERA_CONFIG_FILE)
    cam_cfg = data["cam_cfg"]
    for cfg in cam_cfg:
        cam_pos = cfg["camPos"]
        cam_type = cfg["camType"]
        internal_mat = cfg["internalMat"]
        external_mat = cfg["externalMat"]
        distortion_mat = cfg["distortionMat"]
        camara_configs[cam_pos] = {
            "type": cam_type,
            "intrinsic": [internal_mat[0], internal_mat[4], internal_mat[2], internal_mat[5]],
            "extrinsic": external_mat,
            "distortion": distortion_mat,
        }


def init_configs():
    configs = {
        "data_type": "fusion_pointcloud",
        "sensor_params": {},
        "poses": {},
    }
    return configs


def get_camera_configs(camera_no):
    cam_configs = camara_configs[camera_no]
    extrinsic = cam_configs["extrinsic"]
    intrinsic = cam_configs["intrinsic"]
    distortion = cam_configs["distortion"]
    return {
        "camera_model": "pinhole",
        "extrinsic": [extrinsic[0:4], extrinsic[4:8], extrinsic[8:12], extrinsic[12:16]],
        "fx": intrinsic[0],  # x方向的焦距（单位/像素）
        "fy": intrinsic[1],  # y方向的焦距（单位/像素）
        "cx": intrinsic[2],  # 主点x值
        "cy": intrinsic[3],  # 主点y值
        "k1": distortion[0],  # 第一径向畸变系数
        "k2": distortion[1],  # 第二径向畸变系数
        "k3": distortion[2],  # 第三径向畸变系数
        "p1": distortion[3],  # 第一切向畸变系数
        "p2": distortion[4],  # 第二切向畸变系数
    }


def remap_angle(angle):
    if angle > 180:
        angle -= 360
    elif angle < -180:
        angle += 360
    return angle


def get_pose_mat_from_pos_configs(pos_configs):
    gaussX = (pos_configs["gaussX"] - origin_translation[0]) / 100
    gaussY = (pos_configs["gaussY"] - origin_translation[1]) / 100
    height = pos_configs["height"] - origin_translation[2]
    position = [gaussX, gaussY, height]

    yaw = pos_configs["azimuth"] / 100
    pitch = pos_configs["pitch"] / 100
    roll = pos_configs["roll"] / 100

    yaw = remap_angle(yaw)
    pitch = remap_angle(pitch)
    roll = remap_angle(roll)

    eular_angles = [yaw, pitch, roll]
    pose = position + eular_angles
    return pose_to_mat(pose)


def get_lidar_to_vehicle():
    lidar_pose = 0
    pose = lidar_configs[lidar_pose]["pose"]
    return pose_to_mat(pose)


def convert():
    datas = os.listdir(DATA_PATH)
    segments = filter_os_file(datas)
    segments.sort()
    for seg in segments:
        print("Processing", seg)
        configs = init_configs()
        # pcd
        pcd_dir_path = os.path.join(DATA_PATH, seg, "pcd")
        pcds = os.listdir(pcd_dir_path)
        pcds = filter_os_file(pcds)
        if len(pcds) == 0: continue
        pcds.sort()
        # check pose
        position_dir_path = os.path.join(DATA_PATH, seg, "bestposition")
        position_files = os.listdir(position_dir_path)
        pose_timestamps = set()
        for pos_file in position_files:
            pose_time = pos_file[len("bestposition_"):-len(".yaml")].split(".")[0][:10]
            pose_timestamps.add(pose_time)
        pcd_timestamps = set()
        for pcd in pcds:
            src_pcd = os.path.join(pcd_dir_path, pcd)
            pcd_time = pcd[:-len(".pcd")][:-3]
            # check if pcd has pose
            if pcd_time not in pose_timestamps:
                print("[WARN] ignore pcd-{} because of missing pose".format(pcd_time))
                continue
            pcd_timestamps.add(pcd_time)
            dst_pcd = os.path.join(OUTPUT, seg, "lidar", pcd_time + ".pcd")
            # print(src_pcd, dst_pcd)
            tools.copy_file_v2(src_pcd, dst_pcd)
        # images
        img_dir_path = os.path.join(DATA_PATH, seg, "img")
        cameras = os.listdir(img_dir_path)
        cameras = filter_os_file(cameras)
        cameras.sort()
        for camera in cameras:
            camera_path = os.path.join(img_dir_path, camera)
            camera_imgs = os.listdir(camera_path)
            camera_imgs = filter_os_file(camera_imgs)
            camera_imgs.sort()
            err_fmt = "camera-{} images quantity=%d does not equal to pcd quantity=%d"
            assert len(camera_imgs) == len(pcds), Exception(err_fmt.format(camera, len(camera_imgs), len(pcds)))
            for i, camera_img in enumerate(camera_imgs):
                camera_time = camera_img.split("_")[-1][:10]
                if camera_time not in pcd_timestamps:
                    continue
                src_img = os.path.join(camera_path, camera_img)
                dst_img = os.path.join(OUTPUT, seg, "camera", camera, pcds[i][:-len(".pcd")][:-3] + ".jpg")
                # print(src_img, dst_img)
                tools.copy_file_v2(src_img, dst_img)
            # camera params
            cam_no = int(camera[len("cam"):])
            cam_configs = get_camera_configs(cam_no)
            configs["sensor_params"][camera] = cam_configs
        # position
        position_dir_path = os.path.join(DATA_PATH, seg, "bestposition")
        position_files = os.listdir(position_dir_path)
        position_files = filter_os_file(position_files)
        position_files.sort()
        for pos_file in position_files:
            pose_time = pos_file[len("bestposition_"):-len(".yaml")].split(".")[0][:10]
            if pose_time not in pcd_timestamps:
                # print("[WARN] ignore posetion file-{} because of missing pcd".format(pos_file))
                continue
            pos_file_path = os.path.join(position_dir_path, pos_file)
            pos_configs = read_yaml_file(pos_file_path)
            pose_mat = get_pose_mat_from_pos_configs(pos_configs)
            pose_mat = pose_mat.tolist()
            if "poses" not in configs:
                configs["poses"] = {}
            else:
                configs["poses"][pose_time] = [num for row in pose_mat for num in row]
        # write params
        dst_param = os.path.join(OUTPUT, seg, "config.json")
        with open(dst_param, "w") as fw:
            json.dump(configs, fw, ensure_ascii=False)


if __name__ == "__main__":
    parse_lidar_configs()
    parse_camera_config()
    convert()
