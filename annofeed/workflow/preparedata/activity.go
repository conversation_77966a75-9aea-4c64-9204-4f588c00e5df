package preparedata

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"io/fs"
	"os"
	"path"
	"path/filepath"
	"strings"

	"annofeed/internal/biz"
	"annofeed/workflow/common"
	"annofeed/workflow/fetcher"
	"annofeed/workflow/style"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/apis/annofeed/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/kfs"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"gitlab.rp.konvery.work/platform/pkg/wf/wfutil"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/samber/lo"
	"go.temporal.io/sdk/activity"
)

type Activities struct {
	workdir       string
	databiz       *biz.DatasBiz
	filebiz       *biz.FilesBiz
	rawdatabiz    *biz.RawdataBiz
	rawdataParser *rawdataParser
	log           *log.Helper
}

func NewActivities(
	workdir string,
	databiz *biz.DatasBiz,
	filebiz *biz.FilesBiz,
	rawdatabiz *biz.RawdataBiz,
	logger log.Logger,
) *Activities {
	if workdir == "" {
		workdir = os.TempDir()
	}
	return &Activities{
		workdir:       filepath.Join(workdir, "feedwf"),
		databiz:       databiz,
		filebiz:       filebiz,
		rawdatabiz:    rawdatabiz,
		rawdataParser: newRawdataParser(rawdatabiz, logger),
		log:           log.NewHelper(logger),
	}
}

func (o *Activities) PrepareDataGetVolumeSizeGB(ctx context.Context, data *biz.Data) (sizeGB int, err error) {
	plainGB := int64(data.Source.E.PlainSizeGb)
	fmt.Println("PrepareDataGetVolumeSizeGB plainGB", plainGB)
	if plainGB == 0 {
		n := len(data.Source.E.Uris)
		files, _, err := o.filebiz.List(ctx, &biz.FileListFilter{URIs: data.Source.E.Uris}, biz.Pager{Pagesz: n})
		if err != nil {
			return 0, fmt.Errorf("failed to query files: %w", err)
		}
		if len(files) == 0 {
			// default to 100 GB
			return 100, nil
		}
		for _, f := range files {
			plainGB += f.Size
			if f.IsCompressed() {
				plainGB += f.Size
			}
		}
		plainGB /= 1024 * 1024 * 1024
	}

	if plainGB < 1 {
		plainGB = 1
	}
	return int(plainGB+1) * 2, nil
}

func (o *Activities) PrepareDataFetch(ctx context.Context, dir string, data *biz.Data) (err error) {
	startIdx := wfutil.GetActivtityStartIndex(ctx)
	fmt.Println("---> wf start index: ", startIdx)
	fmt.Println("---> PrepareDataFetch: ", dir, o.workdir)
	if startIdx == 0 {
		_, err = o.databiz.SetState(ctx, data, biz.DataStateFetching) // ignore returned value and error
		if errors.IsNotFound(err) {
			return wfutil.NewNonRetryableError("data not found", err)
		}
	}
	if err := fetcher.Fetch(ctx, data, dir, startIdx, activity.RecordHeartbeat); err != nil {
		return err
	}

	if err := normalizeDataFolderStructure(dir); err != nil {
		// This is a critical error in data structure, so it should be non-retryable.
		return wfutil.NewNonRetryableError("failed to normalize directory structure", err)
	}
	fmt.Println("---> 格式化为标注结构后：")
	_ = PrintTree(dir)
	return nil
}

func (o *Activities) PrepareDataParseMeta(ctx context.Context, dir string, data *biz.Data) (err error) {
	defer func() {
		if err == nil {
			os.RemoveAll(filepath.Join(dir, common.MetaFile))
		}
	}()
	fmt.Println("---> prepare data parse meta dir: ", dir, o.workdir)
	_, meta, err := LoadJSONFile[annofeed.Meta](dir, common.MetaFile)
	if err != nil {
		return wfutil.NewNonRetryableError("failed to parse meta", err)
	}
	if meta == nil {
		return nil
	}
	flds := []string{}
	if data.BaseOn == 0 && meta.BaseOnUid != "" {
		data.BaseOn = kid.ParseID(meta.BaseOnUid)
		err := o.databiz.AlignBaseOn(ctx, data)
		if err != nil {
			if errors.IsNotFound(err) || errors.IsBadRequest(err) {
				return wfutil.NewNonRetryableError("data is gone or bad configuration", err)
			}
			return err
		}
		flds = append(flds, biz.DataSfldBaseOn.String(), biz.DataSfldType.String(), biz.DataSfldSize.String(), biz.DataSfldOrgUid.String())
	}
	if data.BaseOn == 0 {
		if data.Type == "" && meta.Type != anno.Element_Type_unspecified {
			data.Type = meta.Type.String()
			flds = append(flds, biz.DataSfldType.String())
		}
		if data.Source.E.Style == "" && meta.Style != "" {
			data.Source.E.Style = meta.Style
			flds = append(flds, biz.DataSfldSource.String())
		}
		if len(meta.Metadata) > 0 {
			newMeta := meta.Metadata
			for key, value := range data.Source.E.GetMetadata() {
				newMeta[key] = value
			}
			if len(data.Source.E.GetMetadata()) > 0 {
				flds = append(flds, biz.DataSfldSource.String())
			}
			data.Source.E.Metadata = newMeta
		}
	}
	if len(flds) == 0 {
		return nil
	}

	flds = lo.Uniq(flds)
	spew.Dump(flds)
	_, err = o.databiz.Update(ctx, data, field.NewMask(flds...))
	return fixError(err, "failed to save data meta")
}

func fixError(err error, msg string) error {
	if err == nil {
		return nil
	}
	if errors.IsNotFound(err) {
		return wfutil.NewNonRetryableError("data is gone", err)
	}
	if msg != "" {
		return fmt.Errorf("%v: %w", msg, err)
	}
	return err
}

func (o *Activities) PrepareDataConverterScript(ctx context.Context, dir string, data *biz.Data) (uri string, err error) {
	data, err = o.databiz.GetByID(ctx, data.ID)
	if err != nil {
		return "", fixError(err, "")
	}
	if data.ID == 2644768139758692 {
		data.Source.E.Converter = &anno.DataConverter{
			Uri:     "https://konvery-images-public.s3.cn-northwest-1.amazonaws.com.cn/cust/dazhuo/scripts/dz_bev_lane_dataconv-x64.zip",
			Runtime: "python3.11",
		}
	}
	fmt.Println("---> prepare data converter script dir: ", o.workdir)
	spew.Dump(data.Source.E)
	fmt.Println("---> convert script:", data.Source.E.GetConverter().GetUri())
	return data.Source.E.GetConverter().GetUri(), nil
}

func (o *Activities) PrepareDataTransform(ctx context.Context, dir string, data *biz.Data) (err error) {
	fmt.Println("---> prepare data transform dir: ", o.workdir)
	startIdx := wfutil.GetActivtityStartIndex(ctx)

	data, err = o.databiz.GetByID(ctx, data.ID)
	if err != nil {
		return fixError(err, "")
	}
	if data.BaseOn > 0 {
		return nil
	}

	dataType, err := style.Transform(ctx, data, dir, startIdx, activity.RecordHeartbeat)
	if err != nil || dataType == "" || data.Type != "" {
		return err
	}

	data.Type = dataType
	_, err = o.databiz.Update(ctx, data, field.NewMask(biz.DataSfldType.String()))
	return fixError(err, "failed to save data type")
}

func (o *Activities) PrepareDataDetectDataType(ctx context.Context, basedir string, data *biz.Data) (err error) {
	data, err = o.databiz.GetByID(ctx, data.ID)
	if err != nil {
		return fixError(err, "")
	}
	if data.Type != "" {
		return nil
	}

	type Elem struct {
		Path   string
		ImgCnt int
		PCDCnt int
	}
	typ := ""
	elem := Elem{}
	changeElem := func(dir string) {
		elemType := calElemType(elem.ImgCnt, elem.PCDCnt)
		if elemType != "" {
			typ = calDataType(typ, elemType)
		}
		elem = Elem{Path: dir}
	}

	// maximum number of elements to check
	rounds := 10
	errStop := fmt.Errorf("stop")
	err = kfs.IterateDir(basedir, func(entry os.DirEntry, subdir string, depth int) error {
		// skip directories
		if entry.IsDir() {
			return nil
		}

		if subdir != elem.Path {
			changeElem(subdir)
			if rounds--; rounds == 0 || typ == biz.DataTypeFusion3D {
				return errStop
			}
		}

		switch name := entry.Name(); name {
		case common.LidarPCD:
			elem.PCDCnt++
		default:
			if common.IsSupportedImageFormat(name) {
				elem.ImgCnt++
			}
		}
		return nil
	})
	if errors.Is(err, errStop) {
		err = nil
	}
	if err != nil {
		typ = ""
		return
	}
	changeElem("")

	o.log.Info(ctx, "inferred data type", "uid", data.GetUid(), "type", typ)
	if typ == "" {
		return wfutil.NewNonRetryableError("failed to infer data type", nil)
	}
	data.Type = typ
	_, err = o.databiz.Update(ctx, data, field.NewMask(biz.DataSfldType.String()))
	return fixError(err, "failed to save data type")
}

func isImgType(dataType string) bool {
	return dataType == biz.DataTypeImage || dataType == biz.DataTypeFusion2D
}

func calElemType(imageCnt, pcdCnt int) string {
	switch {
	case imageCnt > 0 && pcdCnt > 0:
		return biz.DataTypeFusion3D
	case imageCnt > 1:
		return biz.DataTypeFusion2D
	case imageCnt == 1:
		return biz.DataTypeImage
	case pcdCnt > 0:
		return biz.DataTypePointcloud
	default: // imageCnt == 0 && pcdCnt == 0
		return ""
	}
}

func calDataType(dataType, elemType string) string {
	switch {
	case dataType == biz.DataTypeFusion3D || dataType == elemType:
		return dataType
	case isImgType(dataType) && elemType == biz.DataTypePointcloud ||
		isImgType(elemType) && dataType == biz.DataTypePointcloud:
		return biz.DataTypeFusion3D
	case elemType == biz.DataTypeFusion2D || dataType == "":
		return elemType
	default:
		return dataType
	}
}

func (o *Activities) PrepareDataParseAnnos(ctx context.Context, basedir string, data *biz.Data) (err error) {
	fmt.Println()
	fmt.Println("---> PrepareDataParseAnnos: ", basedir, data.ID)
	data, err = o.databiz.GetByID(ctx, data.ID)
	if err != nil {
		return fixError(err, "")
	}

	startIdx := wfutil.GetActivtityStartIndex(ctx)
	_, annos, err := LoadJSONFile[anno.ExportAnnos](basedir, common.AnnosFile)
	if err != nil {
		fmt.Println("---> load json err: ", err.Error())
		return wfutil.NewNonRetryableError("failed to parse annos", err)
	}
	var elemAnnos []*anno.ElementAnno
	if annos != nil {
		elemAnnos = annos.ElementAnnos
	}
	if len(elemAnnos) > 0 && len(elemAnnos) < int(data.Size) {
		return wfutil.NewNonRetryableError("mismatched number of annotations and elments", nil)
	}
	hasAnnosFolder := !kfs.PathNotExist(filepath.Join(basedir, common.AnnosPrefix))
	fmt.Println("---> hasAnnosFolder: ", hasAnnosFolder)
	if len(elemAnnos) == 0 && !hasAnnosFolder {
		return nil
	}

	i := -1
	dataID := data.ID
	if data.BaseOn > 0 {
		dataID = data.BaseOn
	}
	for page := 0; i+1 < int(data.Size); page++ {
		elems, err := o.rawdatabiz.ListElement(ctx, dataID, biz.Pager{Page: page, Pagesz: 100})
		if err != nil {
			return err
		}

		for _, elem := range elems {
			if i++; i < startIdx {
				continue
			}
			var rd *biz.Rawdata
			if hasAnnosFolder {
				subdir := filepath.Join(common.AnnosPrefix, elem.Folder)
				rd, err = o.rawdataParser.parseElemAnnos(basedir, subdir, common.AnnosFile)
				if err != nil {
					return wfutil.NewNonRetryableError("failed to parseElemAnnos", err)
				}
			} else if len(elemAnnos) > 0 {
				data, _ := json.Marshal(elemAnnos[elem.ElemIdxInData])
				rd = &biz.Rawdata{
					Type:   biz.RawdataTypeAnnos,
					Format: common.RawdataFormatJSON,
					Info:   data,
				}
			}
			if rd != nil {
				rd.Name = path.Join(elem.Folder, common.AnnosFile)
				rd.DataID = data.ID
				rd.Folder = elem.Folder
				rd.ElemIdxInData = elem.ElemIdxInData
				rd.Size = int64(len(rd.Info))
				if _, err = o.rawdatabiz.Create(ctx, rd); err != nil {
					return fmt.Errorf("failed to save rawdata annotations: %w", err)
				}
			}
			activity.RecordHeartbeat(ctx, i) // Report progress.
		}
	}
	return nil
}

// PrintTree 函数以树状结构带缩进的方式打印给定路径的目录结构。
func PrintTree(dirPath string) error {
	fmt.Printf("正在生成目录 '%s' 的树状结构...\n", dirPath)

	// 获取 dirPath 的绝对路径以确保正确的相对深度计算
	absDirPath, err := filepath.Abs(dirPath)
	if err != nil {
		return fmt.Errorf("获取目录 '%s' 的绝对路径失败: %w", dirPath, err)
	}

	err = filepath.Walk(absDirPath, func(path string, info fs.FileInfo, err error) error {
		if err != nil {
			// 如果是权限错误，可能需要特殊处理，比如打印警告但继续
			if os.IsPermission(err) {
				fmt.Printf("无法访问 '%s' (权限不足): %v\n", path, err)
				return nil // 返回 nil 允许 Walk 继续遍历其他路径
			}
			return fmt.Errorf("遍历目录 '%s' 时出错: %w", path, err)
		}

		// 计算当前路径相对于起始目录的深度
		// 使用 filepath.Rel 获取相对路径，更容易计算深度
		relPath, err := filepath.Rel(absDirPath, path)
		if err != nil {
			return fmt.Errorf("计算相对路径失败: %w", err)
		}

		// 根据相对路径的层级计算缩进深度
		depth := 0
		if relPath != "." { // 如果不是根目录本身
			// 每一层级对应一个分隔符，所以需要加上1（表示第一层子目录）
			depth = strings.Count(relPath, string(filepath.Separator)) + 1
		}

		// 如果当前路径是起始目录本身，特殊处理深度为 0
		if path == absDirPath {
			depth = 0
		}

		indent := strings.Repeat("    ", depth) // 每个层级使用4个空格缩进

		// 打印带有缩进的文件或目录名称
		if path == absDirPath { // 根目录特殊处理，不缩进，显示其名称
			if info.IsDir() {
				fmt.Println(info.Name() + "/")
			} else {
				// 理论上，传入的 dirPath 应该是一个目录，但以防万一
				fmt.Println(info.Name())
			}
		} else if info.IsDir() {
			fmt.Printf("%s📁 %s/\n", indent, info.Name())
		} else {
			fmt.Printf("%s📄 %s\n", indent, info.Name())
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("生成目录树失败: %w", err)
	}

	fmt.Println("目录树已生成完毕。")
	return nil
}

// normalizeDirectoryStructure pre-processes the directory structure after extraction
// to handle common non-standard packaging formats.
// It should be called after fetching and extracting the data package.
//
// basedir corresponds to the workflow's session directory, e.g., /sessionDir/data
func normalizeDirectoryStructure_v1(basedir string) error {
	entries, err := os.ReadDir(basedir)
	if err != nil {
		return fmt.Errorf("failed to read base directory %s: %w", basedir, err)
	}

	// Logic for single top-level directory
	if len(entries) == 1 && entries[0].IsDir() {
		singleDir := entries[0]
		singleDirName := singleDir.Name()
		singleDirPath := filepath.Join(basedir, singleDirName)
		fmt.Printf("---> singleDirName: %s, singleDirPath: %s\n", singleDirName, singleDirPath)

		// If the single directory is already 'data', do nothing (handles Scene 1 correctly)
		if singleDirName == common.DataDir {
			return nil
		}

		// Check if this directory contains a 'data' subdirectory (Scene 4)
		nestedDataPath := filepath.Join(singleDirPath, common.DataDir)
		if kfs.PathExist(nestedDataPath) {
			// Move all content from the nested directory up one level
			fmt.Printf("---> Detected nested structure. Moving contents from %s up.\n", singleDirPath)

			nestedEntries, err := os.ReadDir(singleDirPath)
			if err != nil {
				return fmt.Errorf("failed to read nested directory %s: %w", singleDirPath, err)
			}

			for _, nestedEntry := range nestedEntries {
				oldPath := filepath.Join(singleDirPath, nestedEntry.Name())
				newPath := filepath.Join(basedir, nestedEntry.Name())

				if err := os.Rename(oldPath, newPath); err != nil {
					return fmt.Errorf("failed to move %s to %s: %w", oldPath, newPath, err)
				}
			}

			// Remove the now-empty container directory
			if err := os.Remove(singleDirPath); err != nil {
				// Log a warning, as this is not a critical failure
				fmt.Printf("Warning: failed to remove empty container directory %s: %v\n", singleDirPath, err)
			}

			return nil
		}

		// This is Scene 2: single directory with a non-'data' name. Just rename it.
		fmt.Printf("---> Detected single top-level directory '%s'. Renaming to '%s'.\n", singleDirName, common.DataDir)

		expectedDataPath := filepath.Join(basedir, common.DataDir)
		if err := os.Rename(singleDirPath, expectedDataPath); err != nil {
			return fmt.Errorf("failed to rename %s to %s: %w", singleDirPath, expectedDataPath, err)
		}

		return nil
	}

	// Logic for no top-level directory (handles Scene 3)
	// This case is where we have multiple files/dirs like 'clip_1', 'clip_2' at the root.
	// The existing `PrepareDataStandardize` function already handles this correctly by moving them
	// into a new 'data' directory. So, we don't need to do anything here for Scene 3.

	return nil
}

// findDeepestDataDir recursively searches for all directories named "data"
// and returns the path of the one with the greatest depth (longest path)
func findDeepestDataDir(dir string, currentDeepest string) (string, error) {
	fmt.Printf("---> dir %s, currentDeepest: %s\n ", dir, currentDeepest)
	entries, err := os.ReadDir(dir)
	if err != nil {
		return currentDeepest, err
	}

	for _, entry := range entries {
		fmt.Println("---> entry name: ", entry.Name())
		if !entry.IsDir() {
			continue
		}

		currentPath := filepath.Join(dir, entry.Name())
		fmt.Println("---> current path: ", currentPath)
		fmt.Println()
		if entry.Name() == common.DataDir {
			// If this path is deeper than the current deepest, update it.
			if len(currentPath) > len(currentDeepest) {
				currentDeepest = currentPath
			}
		}
		// Recurse into subdirectory, passing along the deepest path found so far.
		deeperResult, err := findDeepestDataDir(currentPath, currentDeepest)
		if err != nil {
			return currentDeepest, err
		}
		// Update deepest if the recursive call found a deeper one.
		if len(deeperResult) > len(currentDeepest) {
			currentDeepest = deeperResult
		}
	}
	fmt.Println()
	return currentDeepest, nil
}

// normalizeDataFolderStructure pre-processes the directory structure to handle arbitrary nesting.
func normalizeDataFolderStructure(basedir string) error {
	fmt.Println("---> normalizeDataFolderStructure: ", basedir)
	// Step 1: Find the deepest 'data' directory.
	deepestDataDir, err := findDeepestDataDir(basedir, "")
	if err != nil {
		return fmt.Errorf("error while searching for data directory: %w", err)
	}
	fmt.Printf("---> find deepestDataDir: %s\n", deepestDataDir)

	// If no 'data' directory is found, or it's already the top-level one we expect,
	// then there's nothing to do.
	expectedDataDir := filepath.Join(basedir, common.DataDir)
	if deepestDataDir == "" || deepestDataDir == expectedDataDir {
		fmt.Printf("---> expectedDataDir: %s\n", expectedDataDir)
		fmt.Printf("---> deepestDataDir: %s\n", deepestDataDir)
		// This also handles cases where the structure is already correct.
		return nil
	}
	fmt.Printf("---> Deepest 'data' directory found at: %s. Standardizing structure.\n", deepestDataDir)

	// Step 2: Create a temporary directory to hold the "true" data.
	// This avoids conflicts if we move directly to the expectedDataDir.
	tempDataHolder, err := os.MkdirTemp(basedir, "temp_data_holder_*")
	fmt.Printf("---> tempDataHolder: %s\n", tempDataHolder)
	if err != nil {
		return fmt.Errorf("failed to create temporary data holder: %w", err)
	}
	// Clean up temp holder in the end
	defer os.RemoveAll(tempDataHolder)

	// Step 3: Move the contents of the deepest 'data' directory to the temp holder.
	// The "true" data root is the PARENT of the deepest 'data' dir, as it contains 'annos' etc.
	trueDataRoot := filepath.Dir(deepestDataDir)
	fmt.Printf("---> trueDataRoot: %s\n", trueDataRoot)

	// Let's refine: The true root might contain more than just 'data'. It might have 'annos'.
	// So we move the *contents* of the true data root.
	entries, err := os.ReadDir(trueDataRoot)
	if err != nil {
		return fmt.Errorf("failed to read true data root '%s': %w", trueDataRoot, err)
	}

	for _, entry := range entries {
		oldPath := filepath.Join(trueDataRoot, entry.Name())
		newPath := filepath.Join(tempDataHolder, entry.Name())
		fmt.Printf("---> move from: %s - %s\n", oldPath, newPath)
		if err := os.Rename(oldPath, newPath); err != nil {
			return fmt.Errorf("failed to move content '%s' to temp holder: %w", oldPath, err)
		}
	}
	fmt.Println("--- tempDataHolder ---")
	_ = PrintTree(tempDataHolder)
	fmt.Println("--- left basedir ---")
	_ = PrintTree(basedir)

	// Step 4: Clean up all original directories by removing the top-level junk.
	// We can do this by deleting everything in basedir except our temp holder.
	baseEntries, err := os.ReadDir(basedir)
	if err != nil {
		return fmt.Errorf("failed to read basedir for cleanup: %w", err)
	}
	for _, entry := range baseEntries {
		// Do not delete our precious data
		if filepath.Join(basedir, entry.Name()) == tempDataHolder {
			continue
		}
		fmt.Println("---> cleanup entry: ", filepath.Join(basedir, entry.Name()))
		if err := os.RemoveAll(filepath.Join(basedir, entry.Name())); err != nil {
			return fmt.Errorf("failed to clean up original directory '%s': %w", entry.Name(), err)
		}
	}

	// Step 5: Move the data from the temp holder to the final, correct structure.
	// Now, we move the contents of the temp holder to the basedir.
	finalEntries, err := os.ReadDir(tempDataHolder)
	if err != nil {
		return fmt.Errorf("failed to read temp holder for final move: %w", err)
	}
	for _, entry := range finalEntries {
		oldPath := filepath.Join(tempDataHolder, entry.Name())
		newPath := filepath.Join(basedir, entry.Name())
		if err := os.Rename(oldPath, newPath); err != nil {
			return fmt.Errorf("failed to move content '%s' to final destination: %w", oldPath, err)
		}
	}
	return nil
}
