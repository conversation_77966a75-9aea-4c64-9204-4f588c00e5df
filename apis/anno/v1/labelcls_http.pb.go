// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: anno/v1/labelcls.proto

package anno

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationLabelclzCreateLabelcls = "/anno.v1.Labelclz/CreateLabelcls"
const OperationLabelclzDeleteLabelcls = "/anno.v1.Labelclz/DeleteLabelcls"
const OperationLabelclzGetLabelcls = "/anno.v1.Labelclz/GetLabelcls"
const OperationLabelclzListLabelcls = "/anno.v1.Labelclz/ListLabelcls"
const OperationLabelclzUpdateLabelcls = "/anno.v1.Labelclz/UpdateLabelcls"

type LabelclzHTTPServer interface {
	CreateLabelcls(context.Context, *Labelcls) (*Labelcls, error)
	DeleteLabelcls(context.Context, *DeleteLabelclsRequest) (*emptypb.Empty, error)
	GetLabelcls(context.Context, *GetLabelclsRequest) (*Labelcls, error)
	ListLabelcls(context.Context, *ListLabelclsRequest) (*ListLabelclsReply, error)
	UpdateLabelcls(context.Context, *Labelcls) (*Labelcls, error)
}

func RegisterLabelclzHTTPServer(s *http.Server, srv LabelclzHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/labelcls", _Labelclz_CreateLabelcls0_HTTP_Handler(srv))
	r.PUT("/v1/labelcls/{name}", _Labelclz_UpdateLabelcls0_HTTP_Handler(srv))
	r.DELETE("/v1/labelcls/{name}", _Labelclz_DeleteLabelcls0_HTTP_Handler(srv))
	r.GET("/v1/labelcls/{name}", _Labelclz_GetLabelcls0_HTTP_Handler(srv))
	r.GET("/v1/labelcls", _Labelclz_ListLabelcls0_HTTP_Handler(srv))
}

func _Labelclz_CreateLabelcls0_HTTP_Handler(srv LabelclzHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in Labelcls
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLabelclzCreateLabelcls)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateLabelcls(ctx, req.(*Labelcls))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Labelcls)
		return ctx.Result(200, reply)
	}
}

func _Labelclz_UpdateLabelcls0_HTTP_Handler(srv LabelclzHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in Labelcls
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLabelclzUpdateLabelcls)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateLabelcls(ctx, req.(*Labelcls))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Labelcls)
		return ctx.Result(200, reply)
	}
}

func _Labelclz_DeleteLabelcls0_HTTP_Handler(srv LabelclzHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteLabelclsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLabelclzDeleteLabelcls)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteLabelcls(ctx, req.(*DeleteLabelclsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Labelclz_GetLabelcls0_HTTP_Handler(srv LabelclzHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLabelclsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLabelclzGetLabelcls)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLabelcls(ctx, req.(*GetLabelclsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Labelcls)
		return ctx.Result(200, reply)
	}
}

func _Labelclz_ListLabelcls0_HTTP_Handler(srv LabelclzHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListLabelclsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLabelclzListLabelcls)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListLabelcls(ctx, req.(*ListLabelclsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListLabelclsReply)
		return ctx.Result(200, reply)
	}
}

type LabelclzHTTPClient interface {
	CreateLabelcls(ctx context.Context, req *Labelcls, opts ...http.CallOption) (rsp *Labelcls, err error)
	DeleteLabelcls(ctx context.Context, req *DeleteLabelclsRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetLabelcls(ctx context.Context, req *GetLabelclsRequest, opts ...http.CallOption) (rsp *Labelcls, err error)
	ListLabelcls(ctx context.Context, req *ListLabelclsRequest, opts ...http.CallOption) (rsp *ListLabelclsReply, err error)
	UpdateLabelcls(ctx context.Context, req *Labelcls, opts ...http.CallOption) (rsp *Labelcls, err error)
}

type LabelclzHTTPClientImpl struct {
	cc *http.Client
}

func NewLabelclzHTTPClient(client *http.Client) LabelclzHTTPClient {
	return &LabelclzHTTPClientImpl{client}
}

func (c *LabelclzHTTPClientImpl) CreateLabelcls(ctx context.Context, in *Labelcls, opts ...http.CallOption) (*Labelcls, error) {
	var out Labelcls
	pattern := "/v1/labelcls"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLabelclzCreateLabelcls))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LabelclzHTTPClientImpl) DeleteLabelcls(ctx context.Context, in *DeleteLabelclsRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/labelcls/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLabelclzDeleteLabelcls))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LabelclzHTTPClientImpl) GetLabelcls(ctx context.Context, in *GetLabelclsRequest, opts ...http.CallOption) (*Labelcls, error) {
	var out Labelcls
	pattern := "/v1/labelcls/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLabelclzGetLabelcls))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LabelclzHTTPClientImpl) ListLabelcls(ctx context.Context, in *ListLabelclsRequest, opts ...http.CallOption) (*ListLabelclsReply, error) {
	var out ListLabelclsReply
	pattern := "/v1/labelcls"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLabelclzListLabelcls))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LabelclzHTTPClientImpl) UpdateLabelcls(ctx context.Context, in *Labelcls, opts ...http.CallOption) (*Labelcls, error) {
	var out Labelcls
	pattern := "/v1/labelcls/{name}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLabelclzUpdateLabelcls))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
