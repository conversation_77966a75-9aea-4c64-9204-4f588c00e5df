syntax = "proto3";

package anno.v1;

import "google/api/annotations.proto";
// import "google/protobuf/empty.proto";
// import "google/protobuf/timestamp.proto";
// import "google/protobuf/field_mask.proto";
// import "google/api/field_behavior.proto";
// import "anno/v1/type.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anno/v1;anno";
option java_multiple_files = true;
option java_package = "anno.v1";

service Labelwidgets {
  rpc ListLabelwidget (ListLabelwidgetRequest) returns (ListLabelwidgetReply) {
    option (google.api.http) = {
      get: "/v1/labelwidgets"
    };
  }
}

message ListLabelwidgetRequest {
}

message ListLabelwidgetReply {
  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated Labelwidget widgets = 2;
}

message Labelwidget {
  string name = 1;
  // language => name
  map<string, string> langs = 2;
}
