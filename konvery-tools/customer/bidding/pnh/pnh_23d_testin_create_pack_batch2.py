import os
import sys
import shutil
import numpy as np
import pandas as pd
from PIL import Image
from pyproj import Transformer
from customer.common import pypcd
from customer.common.bin2pcd.pcdtool.all import write_pcd
import math
from datetime import datetime
sys.path.append('.')
from customer.common import tools


date_format = '%Y%m%d%H%M%S%f'
camera_names = ['fisheye_rear', 'front_tele', 'right_backward', 'fisheye_front', 'fisheye_right', 'left_backward',
                'right_forward', 'fisheye_left', 'front', 'left_forward', 'rear']


def strip_pcd(pcd_file, out_pcd_file):
    pcd = pypcd.point_cloud_from_path(pcd_file)
    new_pc_data = []
    for row in pcd.pc_data:
        point = np.array([row[0], row[1], row[2], 1])
        point[3] = row[4]
        new_pc_data.append(point)
    write_pcd(
        new_pc_data,
        os.path.join(out_pcd_file),
        intensity=True
    )


def wgs84_to_epsg(lon, lat):
    """
    待转换的经纬度点lon, lat
    epsg编码对照：https://www.cnblogs.com/tangjielin/p/16561258.html
    """
    transformer = Transformer.from_crs("EPSG:4326", "EPSG:4528")
    x, y = transformer.transform(lat, lon)
    return x, y


def parse_params(params_file):
    cameras = dict()
    params_data = tools.get_json_data(params_file)
    lidar2body = np.array(params_data['m1Lidar']['lidar_to_body_paradata']['data']).reshape(4, 4)
    for camera_name in camera_names:
        sensor_params = params_data[camera_name]
        camera2body = np.linalg.inv(np.array(sensor_params['camera_to_body_paradata']['data']).reshape(4, 4))
        camera_model_param = sensor_params['camera_model_param']
        if sensor_params['model_type'] == 'equidistant':
            camera_model = 'fisheye'
        elif sensor_params['model_type'] == 'radtan':
            camera_model = 'pinhole'
        else:
            raise ValueError(f'Unknown model_type: {sensor_params["model_type"]}')
        cameras[camera_name] = {
            'camera_model': camera_model,
            'fx': camera_model_param['fx'],
            'fy': camera_model_param['fy'],
            'cx': camera_model_param['cx'],
            'cy': camera_model_param['cy'],
            'k1': camera_model_param.get('k1', 0),
            'k2': camera_model_param.get('k2', 0),
            'p1': camera_model_param.get('p1', 0),
            'p2': camera_model_param.get('p2', 0),
            'k3': camera_model_param.get('k3', 0),
            'k4': camera_model_param.get('k4', 0),
            'k5': camera_model_param.get('k5', 0),
            'k6': camera_model_param.get('k6', 0),
            # 'extrinsic': (np.linalg.inv(camera2body) @ lidar2body).tolist(),
            'extrinsic': camera2body.tolist()
        }
    return cameras


def get_nearest_frame(src, dsts):
    distance = math.inf
    target = None
    for dst in dsts:
        if abs(dst-src) < distance:
            distance = abs(dst-src)
            target = dst
    return target


def run(input_dir, output_dir):
    out_clip_dir = os.path.join(output_dir, os.path.basename(input_dir))
    os.mkdir(out_clip_dir)
    out_lidar_dir = os.path.join(out_clip_dir, 'lidar')
    os.mkdir(out_lidar_dir)
    out_cameras_dir = os.path.join(out_clip_dir, 'camera')
    os.mkdir(out_cameras_dir)
    lidar_dir = os.path.join(input_dir, 'lidar_2Hz')
    lidar_files = sorted(tools.get_file_by_extension(lidar_dir, '.pcd'), key=lambda x: x.name)
    image_mapping = dict()
    image_size_mapping = dict()
    for camera_name in camera_names:
        os.mkdir(os.path.join(out_clip_dir, 'camera', camera_name))
        image_mapping[camera_name] = {os.path.basename(img).split('_')[0]: img for img in tools.listdir(os.path.join(input_dir, 'camera', camera_name), full_path=True, sort=True)}
        image_size_mapping[camera_name] = Image.open(list(image_mapping[camera_name].values())[0]).size
    pose_file = os.path.join(input_dir, 'pose_12_600.txt')
    pcd_timestamp2pose_mat = dict()
    with open(pose_file, 'r') as f:
        header = f.readline()
        lines = f.readlines()
        for line in lines:
            line_parts = line.strip().split(', ')
            pcd_timestamp2pose_mat[float(line_parts[0])] = tools.get_extrinsic_by_txyz_rxyz_opencv(
                np.array(line_parts[1:4], dtype=np.float32),
                np.array(line_parts[4:], dtype=np.float32)
            )

    poses = dict()
    for lidar_file in lidar_files:
        pcd_datetime = os.path.basename(lidar_file.name).split('.')[0]
        pcd_timestamp = datetime.strptime(pcd_datetime, date_format).timestamp()
        nearest_pcd_timestamp = get_nearest_frame(pcd_timestamp, pcd_timestamp2pose_mat.keys())
        poses[pcd_datetime] = pcd_timestamp2pose_mat[nearest_pcd_timestamp].T.flatten().tolist()
        shutil.copyfile(lidar_file, os.path.join(out_lidar_dir, lidar_file.name))
        strip_pcd(lidar_file, os.path.join(out_lidar_dir, lidar_file.name))
        for camera_name in camera_names:
            try:
                img_file = image_mapping[camera_name][pcd_datetime]
                shutil.copyfile(
                    img_file,
                    os.path.join(out_cameras_dir, camera_name, lidar_file.name.replace('.pcd', '.jpg'))
                )
            except KeyError:
                print(f'process {lidar_file.name} {camera_name} fail')
                black_img = Image.new('RGB', image_size_mapping[camera_name], (0, 0, 0))
                black_img.save(os.path.join(out_cameras_dir, camera_name, lidar_file.name.replace('.pcd', '.jpg')))
    config = {
        "data_type": "fusion_pointcloud",
        "sensor_params": parse_params(os.path.join(input_dir, 'params.txt')),
        "poses": poses,
        "camera": {cam: cam for cam in camera_names}
    }
    tools.write_json_file(config, os.path.join(out_clip_dir, 'config.json'))


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
