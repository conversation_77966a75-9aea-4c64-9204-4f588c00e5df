package server

import (
	"iam/internal/biz"
	"iam/internal/conf"
	"iam/internal/server/middleware"
	"iam/internal/service"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"gitlab.rp.konvery.work/platform/apis/iam/v1"
	"gitlab.rp.konvery.work/platform/apis/middleware/authfilter"
)

var unauthWhitelist = []string{
	"/iam.v1.Configs/ListErrors",
	"/iam.v1.Users/SendAuthCode",
	"/iam.v1.Users/Login",
}
var noauthFilters = []authfilter.NoauthFilter{authfilter.WhitelistNoauthFilter(unauthWhitelist...)}

// NewGRPCServer new a gRPC server.
func NewGRPCServer(c *conf.Server,
	cfgsvc *service.ConfigsService,
	perm *service.PermsService,
	policy *service.PoliciesService,
	role *service.RolesService,
	team *service.TeamsService,
	user *service.UsersService,
	userBiz *biz.UsersBiz,
	grant *service.BizgrantsService,
	logger log.Logger) *grpc.Server {
	mws := getMiddlewares(logger,
		middleware.Auth(c.EnableAuth, userBiz),
		middleware.LoadUser(userBiz, noauthFilters),
	)
	var opts = []grpc.ServerOption{
		grpc.Middleware(mws...),
	}
	if c.Grpc.Network != "" {
		opts = append(opts, grpc.Network(c.Grpc.Network))
	}
	if c.Grpc.Addr != "" {
		opts = append(opts, grpc.Address(c.Grpc.Addr))
	}
	if c.Grpc.Timeout != nil {
		opts = append(opts, grpc.Timeout(c.Grpc.Timeout.AsDuration()))
	}
	srv := grpc.NewServer(opts...)
	iam.RegisterConfigsServer(srv, cfgsvc)
	iam.RegisterUsersServer(srv, user)
	iam.RegisterTeamsServer(srv, team)
	iam.RegisterRolesServer(srv, role)
	iam.RegisterPermsServer(srv, perm)
	iam.RegisterPoliciesServer(srv, policy)
	iam.RegisterBizgrantsServer(srv, grant)
	return srv
}
