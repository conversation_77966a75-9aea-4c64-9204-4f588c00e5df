variables:
  REPO_NAME: kon-apidocs
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_VERIFY: 1
  DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  CI_APIDOCS: $CI_APIDOCS
  CI_ARTIFACTORY_REGISTRY: $CI_ARTIFACTORY_REGISTRY
  CI_ARTIFACTORY_DOCKER_REPO: $CI_ARTIFACTORY_DOCKER_REPO
  CI_ARTIFACTORY_GO_REPO: $CI_ARTIFACTORY_GO_REPO
  CI_ARTIFACTORY_HELM_REPO: $CI_ARTIFACTORY_HELM_REPO
  CI_ARTIFACTORY_USER: $CI_ARTIFACTORY_USER
  CI_ARTIFACTORY_TOKEN: $CI_ARTIFACTORY_TOKEN
  IMAGE_PREFIX: $CI_ARTIFACTORY_DOCKER_REPO
  COMMIT_NAME: "${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}"
  DOCKER_TAG_LOGIC: '(if [ "$${CI_COMMIT_TAG}" == "" ]; then echo "$$COMMIT_NAME"; else echo "$${CI_COMMIT_TAG}"; fi);'
  GOPRIVATE: $CI_GOPRIVATE
  GOPROXY: $CI_GOPROXY

stages:
  - lint
  - test
  - build
  - deploy

.lint:
  stage: lint
  tags: ["k8s"]
  image: golangci/golangci-lint:latest
  script:
    - golangci-lint run -v

.test:
  stage: test
  tags: ["k8s"]
  image: golang:alpine
  script:
    - go vet ./...
    - go test  -v -cover ./...

build:
  stage: build
  tags: ["k8s"]
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  before_script:
    - until docker info > /dev/null; do sleep 1; done #https://gitlab.com/gitlab-org/gitlab-runner/-/issues/27384
    - docker login -u $CI_ARTIFACTORY_USER -p $CI_ARTIFACTORY_TOKEN $CI_ARTIFACTORY_REGISTRY
  script:
    - TAG=$(eval $DOCKER_TAG_LOGIC)
    - BUILD_ARGS="--build-arg GIT_TAG=$TAG --build-arg GOPROXY --build-arg GOPRIVATE"
    - IMAGE_TAG=$IMAGE_PREFIX/$REPO_NAME:$TAG
    - docker build $BUILD_ARGS -t $IMAGE_TAG .
    - docker push $IMAGE_TAG

helm-charts:
  stage: deploy
  tags: ["k8s"]
  only: ["tags"]
  image:
    name: alpine/k8s:1.20.7
    entrypoint: [""]
  script:
    - cd helm-charts
    - echo "build helm charts Triggered by a tag $CI_COMMIT_TAG"
    - SEMANTIC_VERSION=$(echo $CI_COMMIT_TAG | sed 's/^v//')
    - sed -i "s/0.1.0/$SEMANTIC_VERSION/" Chart.yaml
    - helm lint .
    - helm package .
    - CHART_TAG=$REPO_NAME-$SEMANTIC_VERSION
    - curl --fail-with-body -u "$CI_ARTIFACTORY_USER:$CI_ARTIFACTORY_TOKEN" -T $CHART_TAG.tgz "$CI_ARTIFACTORY_HELM_REPO/$CHART_TAG.tgz"

openapi:
  stage: deploy
  tags: ["k8s"]
  only: ["tags"]
  image:
    name: alpine/k8s:1.20.7
    entrypoint: [""]
  script:
    - |
      sed -i "s/^\(    version: \)0.0.1\$/\1$CI_COMMIT_TAG/" openapi.yaml
      curl $CI_APIDOCS/docs/apidocs/$CI_COMMIT_TAG -T openapi.yaml

deploy:
  stage: deploy
  tags: ["k8s"]
  when: manual
  script:
    - export
    - echo "deployed to development"
  environment:
    name: development
    url: https://apidocs.np.konvery.work
    kubernetes:
      namespace: development
