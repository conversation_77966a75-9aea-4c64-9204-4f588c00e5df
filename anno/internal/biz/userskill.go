// source: anno/v1/skill.proto
package biz

import (
	"time"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
)

type Userskill struct {
	ID         int64  `json:"id" gorm:"default:null"`
	UserUid    string `json:"user_uid" gorm:"default:null"`
	SkillType  string `json:"skill_type" gorm:"default:null"`
	SkillName  string `json:"skill_name" gorm:"default:null"`
	SkillLevel int    `json:"skill_level" gorm:"default:null"`

	CreatorUid string    `json:"creator_uid" gorm:"default:null"`
	UpdatedAt  time.Time `json:"updated_at" gorm:"default:null"`
	CreatedAt  time.Time `json:"created_at" gorm:"default:null"`
}

func (o *Userskill) GetID() int64 { return o.ID }

const UserskillTableName = "userskills"

var (
	Userskill_             = field.RegObject(&Userskill{})
	UserskillUpdatableFlds = field.NewModel(UserskillTableName, Userskill_)

	UserskillSfldUserUid    = field.Sname(&Userskill_.UserUid)
	UserskillSfldSkillType  = field.Sname(&Userskill_.SkillType)
	UserskillSfldSkillName  = field.Sname(&Userskill_.SkillName)
	UserskillSfldSkillLevel = field.Sname(&Userskill_.SkillLevel)
	UserskillSfldCreatorUid = field.Sname(&Userskill_.CreatorUid)
)
