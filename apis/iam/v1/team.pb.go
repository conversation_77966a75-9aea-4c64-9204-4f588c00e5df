// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: iam/v1/team.proto

package iam

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateTeamRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// mandatory in create-team request
	Name     string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc     string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Avatar   string `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Province string `protobuf:"bytes,5,opt,name=province,proto3" json:"province,omitempty"`
	City     string `protobuf:"bytes,6,opt,name=city,proto3" json:"city,omitempty"`
	// parent team uid
	ParentUid string `protobuf:"bytes,7,opt,name=parent_uid,json=parentUid,proto3" json:"parent_uid,omitempty"`
	// mandatory in create-team request
	Type Team_Type_Enum `protobuf:"varint,8,opt,name=type,proto3,enum=iam.v1.Team_Type_Enum" json:"type,omitempty"`
	// specify the team owner.
	// supported formats: uid:xxx, phone:+8612345678901 or email:<EMAIL>
	Owner string `protobuf:"bytes,9,opt,name=owner,proto3" json:"owner,omitempty"` // allow empty owner in UpdateTeamRequest
}

func (x *CreateTeamRequest) Reset() {
	*x = CreateTeamRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_team_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTeamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTeamRequest) ProtoMessage() {}

func (x *CreateTeamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_team_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTeamRequest.ProtoReflect.Descriptor instead.
func (*CreateTeamRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_team_proto_rawDescGZIP(), []int{0}
}

func (x *CreateTeamRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *CreateTeamRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateTeamRequest) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *CreateTeamRequest) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *CreateTeamRequest) GetProvince() string {
	if x != nil {
		return x.Province
	}
	return ""
}

func (x *CreateTeamRequest) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *CreateTeamRequest) GetParentUid() string {
	if x != nil {
		return x.ParentUid
	}
	return ""
}

func (x *CreateTeamRequest) GetType() Team_Type_Enum {
	if x != nil {
		return x.Type
	}
	return Team_Type_unspecified
}

func (x *CreateTeamRequest) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

type UpdateTeamRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Team   *CreateTeamRequest `protobuf:"bytes,1,opt,name=team,proto3" json:"team,omitempty"`
	Fields []string           `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *UpdateTeamRequest) Reset() {
	*x = UpdateTeamRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_team_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTeamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTeamRequest) ProtoMessage() {}

func (x *UpdateTeamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_team_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTeamRequest.ProtoReflect.Descriptor instead.
func (*UpdateTeamRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_team_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateTeamRequest) GetTeam() *CreateTeamRequest {
	if x != nil {
		return x.Team
	}
	return nil
}

func (x *UpdateTeamRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type DeleteTeamRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *DeleteTeamRequest) Reset() {
	*x = DeleteTeamRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_team_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteTeamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTeamRequest) ProtoMessage() {}

func (x *DeleteTeamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_team_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTeamRequest.ProtoReflect.Descriptor instead.
func (*DeleteTeamRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_team_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteTeamRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetTeamsRootRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uids []string `protobuf:"bytes,1,rep,name=uids,proto3" json:"uids,omitempty"`
}

func (x *GetTeamsRootRequest) Reset() {
	*x = GetTeamsRootRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_team_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTeamsRootRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTeamsRootRequest) ProtoMessage() {}

func (x *GetTeamsRootRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_team_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTeamsRootRequest.ProtoReflect.Descriptor instead.
func (*GetTeamsRootRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_team_proto_rawDescGZIP(), []int{3}
}

func (x *GetTeamsRootRequest) GetUids() []string {
	if x != nil {
		return x.Uids
	}
	return nil
}

type GetTeamRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *GetTeamRequest) Reset() {
	*x = GetTeamRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_team_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTeamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTeamRequest) ProtoMessage() {}

func (x *GetTeamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_team_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTeamRequest.ProtoReflect.Descriptor instead.
func (*GetTeamRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_team_proto_rawDescGZIP(), []int{4}
}

func (x *GetTeamRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type ListTeamByIDsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *ListTeamByIDsRequest) Reset() {
	*x = ListTeamByIDsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_team_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTeamByIDsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTeamByIDsRequest) ProtoMessage() {}

func (x *ListTeamByIDsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_team_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTeamByIDsRequest.ProtoReflect.Descriptor instead.
func (*ListTeamByIDsRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_team_proto_rawDescGZIP(), []int{5}
}

func (x *ListTeamByIDsRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type ListTeamRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page   int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Pagesz int32 `protobuf:"varint,2,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// parent team uid; if not specified, only query organizations
	ParentUid string `protobuf:"bytes,3,opt,name=parent_uid,json=parentUid,proto3" json:"parent_uid,omitempty"`
	// find teams by name pattern
	NamePattern string         `protobuf:"bytes,4,opt,name=name_pattern,json=namePattern,proto3" json:"name_pattern,omitempty"`
	Uids        []string       `protobuf:"bytes,5,rep,name=uids,proto3" json:"uids,omitempty"`
	TeamType    Team_Type_Enum `protobuf:"varint,6,opt,name=team_type,json=teamType,proto3,enum=iam.v1.Team_Type_Enum" json:"team_type,omitempty"`
}

func (x *ListTeamRequest) Reset() {
	*x = ListTeamRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_team_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTeamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTeamRequest) ProtoMessage() {}

func (x *ListTeamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_team_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTeamRequest.ProtoReflect.Descriptor instead.
func (*ListTeamRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_team_proto_rawDescGZIP(), []int{6}
}

func (x *ListTeamRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListTeamRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListTeamRequest) GetParentUid() string {
	if x != nil {
		return x.ParentUid
	}
	return ""
}

func (x *ListTeamRequest) GetNamePattern() string {
	if x != nil {
		return x.NamePattern
	}
	return ""
}

func (x *ListTeamRequest) GetUids() []string {
	if x != nil {
		return x.Uids
	}
	return nil
}

func (x *ListTeamRequest) GetTeamType() Team_Type_Enum {
	if x != nil {
		return x.TeamType
	}
	return Team_Type_unspecified
}

type ListTeamReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total int32   `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Teams []*Team `protobuf:"bytes,2,rep,name=teams,proto3" json:"teams,omitempty"`
}

func (x *ListTeamReply) Reset() {
	*x = ListTeamReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_team_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTeamReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTeamReply) ProtoMessage() {}

func (x *ListTeamReply) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_team_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTeamReply.ProtoReflect.Descriptor instead.
func (*ListTeamReply) Descriptor() ([]byte, []int) {
	return file_iam_v1_team_proto_rawDescGZIP(), []int{7}
}

func (x *ListTeamReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListTeamReply) GetTeams() []*Team {
	if x != nil {
		return x.Teams
	}
	return nil
}

type ListMembersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page   int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Pagesz int32 `protobuf:"varint,2,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// team uid
	Uid string `protobuf:"bytes,3,opt,name=uid,proto3" json:"uid,omitempty"`
	// list only members matching the name pattern
	NamePattern string `protobuf:"bytes,4,opt,name=name_pattern,json=namePattern,proto3" json:"name_pattern,omitempty"`
	// list only members with the specified role
	Role string `protobuf:"bytes,5,opt,name=role,proto3" json:"role,omitempty"`
}

func (x *ListMembersRequest) Reset() {
	*x = ListMembersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_team_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMembersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMembersRequest) ProtoMessage() {}

func (x *ListMembersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_team_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMembersRequest.ProtoReflect.Descriptor instead.
func (*ListMembersRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_team_proto_rawDescGZIP(), []int{8}
}

func (x *ListMembersRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListMembersRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListMembersRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ListMembersRequest) GetNamePattern() string {
	if x != nil {
		return x.NamePattern
	}
	return ""
}

func (x *ListMembersRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

type ListMembersReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total   int32     `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Members []*User   `protobuf:"bytes,2,rep,name=members,proto3" json:"members,omitempty"`
	Team    *BaseUser `protobuf:"bytes,3,opt,name=team,proto3" json:"team,omitempty"`
}

func (x *ListMembersReply) Reset() {
	*x = ListMembersReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_team_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMembersReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMembersReply) ProtoMessage() {}

func (x *ListMembersReply) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_team_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMembersReply.ProtoReflect.Descriptor instead.
func (*ListMembersReply) Descriptor() ([]byte, []int) {
	return file_iam_v1_team_proto_rawDescGZIP(), []int{9}
}

func (x *ListMembersReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListMembersReply) GetMembers() []*User {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *ListMembersReply) GetTeam() *BaseUser {
	if x != nil {
		return x.Team
	}
	return nil
}

type AddMembersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// team uid
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// type of user identities: uid/email/phone/...
	IdType IDType_Enum `protobuf:"varint,2,opt,name=id_type,json=idType,proto3,enum=iam.v1.IDType_Enum" json:"id_type,omitempty"`
	// max number of user identities is 100
	Identities []string `protobuf:"bytes,3,rep,name=identities,proto3" json:"identities,omitempty"`
	// one of owner/manager/member
	Role string `protobuf:"bytes,4,opt,name=role,proto3" json:"role,omitempty"`
}

func (x *AddMembersRequest) Reset() {
	*x = AddMembersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_team_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddMembersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddMembersRequest) ProtoMessage() {}

func (x *AddMembersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_team_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddMembersRequest.ProtoReflect.Descriptor instead.
func (*AddMembersRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_team_proto_rawDescGZIP(), []int{10}
}

func (x *AddMembersRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *AddMembersRequest) GetIdType() IDType_Enum {
	if x != nil {
		return x.IdType
	}
	return IDType_unspecified
}

func (x *AddMembersRequest) GetIdentities() []string {
	if x != nil {
		return x.Identities
	}
	return nil
}

func (x *AddMembersRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

type DeleteMembersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// team uid
	Uid      string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UserUids []string `protobuf:"bytes,2,rep,name=user_uids,json=userUids,proto3" json:"user_uids,omitempty"`
}

func (x *DeleteMembersRequest) Reset() {
	*x = DeleteMembersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_team_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteMembersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMembersRequest) ProtoMessage() {}

func (x *DeleteMembersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_team_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMembersRequest.ProtoReflect.Descriptor instead.
func (*DeleteMembersRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_team_proto_rawDescGZIP(), []int{11}
}

func (x *DeleteMembersRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *DeleteMembersRequest) GetUserUids() []string {
	if x != nil {
		return x.UserUids
	}
	return nil
}

type SetMembersRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// uid of the team
	Uid      string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UserUids []string `protobuf:"bytes,2,rep,name=user_uids,json=userUids,proto3" json:"user_uids,omitempty"`
	// new role: owner/manager/member
	Role string `protobuf:"bytes,3,opt,name=role,proto3" json:"role,omitempty"`
}

func (x *SetMembersRoleRequest) Reset() {
	*x = SetMembersRoleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_team_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetMembersRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMembersRoleRequest) ProtoMessage() {}

func (x *SetMembersRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_team_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMembersRoleRequest.ProtoReflect.Descriptor instead.
func (*SetMembersRoleRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_team_proto_rawDescGZIP(), []int{12}
}

func (x *SetMembersRoleRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *SetMembersRoleRequest) GetUserUids() []string {
	if x != nil {
		return x.UserUids
	}
	return nil
}

func (x *SetMembersRoleRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

var File_iam_v1_team_proto protoreflect.FileDescriptor

var file_iam_v1_team_proto_rawDesc = []byte{
	0x0a, 0x11, 0x69, 0x61, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x69,
	0x61, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xc6, 0x02, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x55, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x65, 0x61, 0x6d, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x53, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3d,
	0xfa, 0x42, 0x3a, 0x72, 0x38, 0x32, 0x36, 0x5e, 0x28, 0x7c, 0x75, 0x69, 0x64, 0x3a, 0x5c, 0x77,
	0x2b, 0x7c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x3a, 0x5c, 0x2b, 0x5c, 0x64, 0x2b, 0x7c, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x3a, 0x5b, 0x5c, 0x77, 0x2e, 0x2d, 0x5d, 0x2b, 0x40, 0x5b, 0x5c, 0x77, 0x2e,
	0x2d, 0x5d, 0x2b, 0x5c, 0x2e, 0x5b, 0x5c, 0x77, 0x2e, 0x5d, 0x2b, 0x29, 0x24, 0x52, 0x05, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x3a, 0x03, 0xba, 0x47, 0x00, 0x22, 0x6f, 0x0a, 0x11, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d,
	0x0a, 0x04, 0x74, 0x65, 0x61, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x74, 0x65, 0x61, 0x6d, 0x12, 0x16, 0x0a,
	0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x3a, 0x13, 0xba, 0x47, 0x10, 0xba, 0x01, 0x04, 0x74, 0x65, 0x61,
	0x6d, 0xba, 0x01, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22, 0x25, 0x0a, 0x11, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69,
	0x64, 0x22, 0x35, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x52, 0x6f, 0x6f,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x75, 0x69, 0x64, 0x73, 0x3a, 0x0a, 0xba, 0x47,
	0x07, 0xba, 0x01, 0x04, 0x75, 0x69, 0x64, 0x73, 0x22, 0x39, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54,
	0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x22, 0x28, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x42,
	0x79, 0x49, 0x44, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0xd3, 0x01,
	0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x00,
	0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x55, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f,
	0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e,
	0x61, 0x6d, 0x65, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x69,
	0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x75, 0x69, 0x64, 0x73, 0x12, 0x33,
	0x0a, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x74, 0x65, 0x61, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x56, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x22, 0x0a, 0x05, 0x74, 0x65,
	0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x3a, 0x0b,
	0xba, 0x47, 0x08, 0xba, 0x01, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x22, 0x94, 0x01, 0x0a, 0x12,
	0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x64, 0x28,
	0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6e,
	0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f,
	0x6c, 0x65, 0x22, 0x85, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x26, 0x0a,
	0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c,
	0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x07, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x24, 0x0a, 0x04, 0x74, 0x65, 0x61, 0x6d, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x73,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x74, 0x65, 0x61, 0x6d, 0x3a, 0x0d, 0xba, 0x47, 0x0a,
	0xba, 0x01, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x22, 0xbc, 0x01, 0x0a, 0x11, 0x41,
	0x64, 0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x12, 0x38, 0x0a, 0x07, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x44, 0x54,
	0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x06, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x72, 0x6f, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65,
	0x3a, 0x27, 0xba, 0x47, 0x24, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x07, 0x69, 0x64,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0xba, 0x01, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x69,
	0x65, 0x73, 0xba, 0x01, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x22, 0x5c, 0x0a, 0x14, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x55, 0x69, 0x64, 0x73,
	0x3a, 0x15, 0xba, 0x47, 0x12, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x09, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x73, 0x22, 0x78, 0x0a, 0x15, 0x53, 0x65, 0x74, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x55, 0x69, 0x64, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72,
	0x6f, 0x6c, 0x65, 0x3a, 0x1c, 0xba, 0x47, 0x19, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01,
	0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x73, 0xba, 0x01, 0x04, 0x72, 0x6f, 0x6c,
	0x65, 0x32, 0xe1, 0x07, 0x0a, 0x05, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x4b, 0x0a, 0x0a, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x19, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x0c, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65,
	0x61, 0x6d, 0x22, 0x14, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0e, 0x3a, 0x01, 0x2a, 0x22, 0x09, 0x2f,
	0x76, 0x31, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x59, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x19, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x0c, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x22,
	0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x04, 0x74, 0x65, 0x61, 0x6d, 0x32, 0x14, 0x2f,
	0x76, 0x31, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x2f, 0x7b, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x75,
	0x69, 0x64, 0x7d, 0x12, 0x58, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x65, 0x61,
	0x6d, 0x12, 0x19, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x2a, 0x0f, 0x2f, 0x76,
	0x31, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x5a, 0x0a,
	0x0c, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x52, 0x6f, 0x6f, 0x74, 0x12, 0x1b, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x52,
	0x6f, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x12, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x74,
	0x65, 0x61, 0x6d, 0x73, 0x2f, 0x72, 0x6f, 0x6f, 0x74, 0x12, 0x48, 0x0a, 0x07, 0x47, 0x65, 0x74,
	0x54, 0x65, 0x61, 0x6d, 0x12, 0x16, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0c, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x11, 0x12, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x2f, 0x7b, 0x75,
	0x69, 0x64, 0x7d, 0x12, 0x44, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x42,
	0x79, 0x49, 0x44, 0x73, 0x12, 0x1c, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x42, 0x79, 0x49, 0x44, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x15, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x4d, 0x0a, 0x08, 0x4c, 0x69, 0x73,
	0x74, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x17, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15,
	0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x61, 0x6d,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x11, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0b, 0x12, 0x09, 0x2f,
	0x76, 0x31, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x64, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x1a, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1f, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x19, 0x12, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x73,
	0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x63,
	0x0a, 0x0a, 0x41, 0x64, 0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x19, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x22, 0x17, 0x2f, 0x76, 0x31, 0x2f,
	0x74, 0x65, 0x61, 0x6d, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x12, 0x66, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x12, 0x1c, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x19, 0x2a, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x2f, 0x7b, 0x75,
	0x69, 0x64, 0x7d, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x68, 0x0a, 0x0e, 0x53,
	0x65, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x1d, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a, 0x1a,
	0x14, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d,
	0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x42, 0x3b, 0x0a, 0x06, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x76, 0x31, 0x3b, 0x69,
	0x61, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_iam_v1_team_proto_rawDescOnce sync.Once
	file_iam_v1_team_proto_rawDescData = file_iam_v1_team_proto_rawDesc
)

func file_iam_v1_team_proto_rawDescGZIP() []byte {
	file_iam_v1_team_proto_rawDescOnce.Do(func() {
		file_iam_v1_team_proto_rawDescData = protoimpl.X.CompressGZIP(file_iam_v1_team_proto_rawDescData)
	})
	return file_iam_v1_team_proto_rawDescData
}

var file_iam_v1_team_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_iam_v1_team_proto_goTypes = []interface{}{
	(*CreateTeamRequest)(nil),     // 0: iam.v1.CreateTeamRequest
	(*UpdateTeamRequest)(nil),     // 1: iam.v1.UpdateTeamRequest
	(*DeleteTeamRequest)(nil),     // 2: iam.v1.DeleteTeamRequest
	(*GetTeamsRootRequest)(nil),   // 3: iam.v1.GetTeamsRootRequest
	(*GetTeamRequest)(nil),        // 4: iam.v1.GetTeamRequest
	(*ListTeamByIDsRequest)(nil),  // 5: iam.v1.ListTeamByIDsRequest
	(*ListTeamRequest)(nil),       // 6: iam.v1.ListTeamRequest
	(*ListTeamReply)(nil),         // 7: iam.v1.ListTeamReply
	(*ListMembersRequest)(nil),    // 8: iam.v1.ListMembersRequest
	(*ListMembersReply)(nil),      // 9: iam.v1.ListMembersReply
	(*AddMembersRequest)(nil),     // 10: iam.v1.AddMembersRequest
	(*DeleteMembersRequest)(nil),  // 11: iam.v1.DeleteMembersRequest
	(*SetMembersRoleRequest)(nil), // 12: iam.v1.SetMembersRoleRequest
	(Team_Type_Enum)(0),           // 13: iam.v1.Team.Type.Enum
	(*Team)(nil),                  // 14: iam.v1.Team
	(*User)(nil),                  // 15: iam.v1.User
	(*BaseUser)(nil),              // 16: iam.v1.BaseUser
	(IDType_Enum)(0),              // 17: iam.v1.IDType.Enum
	(*emptypb.Empty)(nil),         // 18: google.protobuf.Empty
}
var file_iam_v1_team_proto_depIdxs = []int32{
	13, // 0: iam.v1.CreateTeamRequest.type:type_name -> iam.v1.Team.Type.Enum
	0,  // 1: iam.v1.UpdateTeamRequest.team:type_name -> iam.v1.CreateTeamRequest
	13, // 2: iam.v1.ListTeamRequest.team_type:type_name -> iam.v1.Team.Type.Enum
	14, // 3: iam.v1.ListTeamReply.teams:type_name -> iam.v1.Team
	15, // 4: iam.v1.ListMembersReply.members:type_name -> iam.v1.User
	16, // 5: iam.v1.ListMembersReply.team:type_name -> iam.v1.BaseUser
	17, // 6: iam.v1.AddMembersRequest.id_type:type_name -> iam.v1.IDType.Enum
	0,  // 7: iam.v1.Teams.CreateTeam:input_type -> iam.v1.CreateTeamRequest
	1,  // 8: iam.v1.Teams.UpdateTeam:input_type -> iam.v1.UpdateTeamRequest
	2,  // 9: iam.v1.Teams.DeleteTeam:input_type -> iam.v1.DeleteTeamRequest
	3,  // 10: iam.v1.Teams.GetTeamsRoot:input_type -> iam.v1.GetTeamsRootRequest
	4,  // 11: iam.v1.Teams.GetTeam:input_type -> iam.v1.GetTeamRequest
	5,  // 12: iam.v1.Teams.ListTeamByIDs:input_type -> iam.v1.ListTeamByIDsRequest
	6,  // 13: iam.v1.Teams.ListTeam:input_type -> iam.v1.ListTeamRequest
	8,  // 14: iam.v1.Teams.ListMembers:input_type -> iam.v1.ListMembersRequest
	10, // 15: iam.v1.Teams.AddMembers:input_type -> iam.v1.AddMembersRequest
	11, // 16: iam.v1.Teams.DeleteMembers:input_type -> iam.v1.DeleteMembersRequest
	12, // 17: iam.v1.Teams.SetMembersRole:input_type -> iam.v1.SetMembersRoleRequest
	14, // 18: iam.v1.Teams.CreateTeam:output_type -> iam.v1.Team
	14, // 19: iam.v1.Teams.UpdateTeam:output_type -> iam.v1.Team
	18, // 20: iam.v1.Teams.DeleteTeam:output_type -> google.protobuf.Empty
	7,  // 21: iam.v1.Teams.GetTeamsRoot:output_type -> iam.v1.ListTeamReply
	14, // 22: iam.v1.Teams.GetTeam:output_type -> iam.v1.Team
	7,  // 23: iam.v1.Teams.ListTeamByIDs:output_type -> iam.v1.ListTeamReply
	7,  // 24: iam.v1.Teams.ListTeam:output_type -> iam.v1.ListTeamReply
	9,  // 25: iam.v1.Teams.ListMembers:output_type -> iam.v1.ListMembersReply
	18, // 26: iam.v1.Teams.AddMembers:output_type -> google.protobuf.Empty
	18, // 27: iam.v1.Teams.DeleteMembers:output_type -> google.protobuf.Empty
	18, // 28: iam.v1.Teams.SetMembersRole:output_type -> google.protobuf.Empty
	18, // [18:29] is the sub-list for method output_type
	7,  // [7:18] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_iam_v1_team_proto_init() }
func file_iam_v1_team_proto_init() {
	if File_iam_v1_team_proto != nil {
		return
	}
	file_iam_v1_type_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_iam_v1_team_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTeamRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_team_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTeamRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_team_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteTeamRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_team_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTeamsRootRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_team_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTeamRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_team_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTeamByIDsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_team_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTeamRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_team_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTeamReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_team_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMembersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_team_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMembersReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_team_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddMembersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_team_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteMembersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_team_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetMembersRoleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_iam_v1_team_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_iam_v1_team_proto_goTypes,
		DependencyIndexes: file_iam_v1_team_proto_depIdxs,
		MessageInfos:      file_iam_v1_team_proto_msgTypes,
	}.Build()
	File_iam_v1_team_proto = out.File
	file_iam_v1_team_proto_rawDesc = nil
	file_iam_v1_team_proto_goTypes = nil
	file_iam_v1_team_proto_depIdxs = nil
}
