{"File": [{"name": "parents", "types": [{"namespace": "File"}, {"namespace": "Folder"}], "params": ["subject"]}, {"name": "viewers", "types": [{"namespace": "User"}, {"namespace": "Group", "relation": "members"}], "params": ["subject"]}, {"name": "owners", "types": [{"namespace": "User"}, {"namespace": "Group", "relation": "members"}], "params": ["subject"]}, {"name": "siblings", "types": [{"namespace": "File"}], "params": ["subject"]}, {"name": "view", "rewrite": {"operator": "or", "children": [{"operator": "and", "children": [{"operator": "or", "children": [{"relation": "parents", "computed_subject_set_relation": "viewers", "args": [1]}]}, {"relation": "parents", "computed_subject_set_relation": "view", "args": [0]}]}, {"relation": "viewers", "args": [1]}, {"relation": "viewers", "args": [1]}, {"relation": "viewers", "args": [1]}, {"relation": "owners", "args": [1]}]}, "params": ["ctx"]}, {"name": "edit", "rewrite": {"operator": "or", "children": [{"relation": "owners", "args": [1]}]}, "params": ["ctx"]}, {"name": "not", "rewrite": {"operator": "or", "children": [{"inverted": {"relation": "owners", "args": [1]}}]}, "params": ["ctx"]}, {"name": "rename", "rewrite": {"operator": "or", "children": [{"relation": "siblings", "computed_subject_set_relation": "edit", "args": [0]}]}, "params": ["ctx"]}], "Folder": [{"name": "parents", "types": [{"namespace": "File"}], "params": ["subject"]}, {"name": "viewers", "types": [{"namespace": "Group", "relation": "members"}], "params": ["subject"]}, {"name": "view", "rewrite": {"operator": "or", "children": [{"relation": "viewers", "args": [1]}]}, "params": ["ctx"]}], "Group": [{"name": "members", "types": [{"namespace": "User"}, {"namespace": "Group"}], "params": ["subject"]}], "User": [{"name": "manager", "types": [{"namespace": "User"}], "params": ["subject"]}]}