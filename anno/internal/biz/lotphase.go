package biz

import (
	"context"
	"fmt"
	"time"

	"anno/internal/mq"

	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/kmath"
)

type ExecteamQuota struct {
	Quota   *anno.Lotphase_Quota
	MinJobs int32
	MaxJobs int32
}

func (q *ExecteamQuota) NoLimit() bool {
	if q == nil || q.Quota == nil {
		return true
	}

	return q.Quota.Min == 0 && q.Quota.Max == 100
}

func (q *ExecteamQuota) GetQuota() *anno.Lotphase_Quota {
	if q == nil {
		return nil
	}
	return q.Quota
}

func (q *ExecteamQuota) WithJobCount(jobCount int) *ExecteamQuota {
	quota := q.GetQuota()
	if quota == nil || jobCount == 0 {
		return q
	}

	if quota.Min != 0 {
		q.MinJobs = int32(jobCount) * quota.Min / 100
	}
	if quota.Max != 0 {
		q.MaxJobs = int32(jobCount)*quota.Max/100 + 1
		if q.MaxJobs > int32(jobCount) {
			q.MaxJobs = int32(jobCount)
		}
	}
	return q
}

type ExecteamQuotaW = serial.Type[*ExecteamQuota]

type Lotphase struct {
	ID            int64          `json:"id" gorm:"default:null"`
	LotID         int64          `json:"lot_id" gorm:"default:null"`
	Number        int32          `json:"number" gorm:"default:null"` // starts from 1
	Name          string         `json:"name" gorm:"default:null"`
	Type          string         `json:"type" gorm:"default:null"`    // label/review/...
	Subtype       string         `json:"subtype" gorm:"default:null"` // labelcls
	Editable      bool           `json:"editable" gorm:"default:null"`
	SamplePercent float32        `json:"sample_percent" gorm:"default:null"`
	MinSkillLevel int32          `json:"min_skill_level" gorm:"default:null"`
	Timeout       int32          `json:"timeout" gorm:"default:null"`
	Merge         bool           `json:"merge" gorm:"default:null"`
	Execteam      string         `json:"execteam" gorm:"default:null"`     // the team assigned to execute the phase
	Quota         ExecteamQuotaW `json:"quota" gorm:"default:null"`        // quota of the execteam in this phase
	ClaimedJobs   int32          `json:"claimed_jobs" gorm:"default:null"` // the number of claimed jobs by the execteam
	ClaimPolicy   string         `json:"claim_policy" gorm:"default:null"`
	UpdatedAt     time.Time      `json:"updated_at" gorm:"default:null"`
	CreatedAt     time.Time      `json:"created_at" gorm:"default:null"`
}

func (o *Lotphase) GetID() int64 { return o.ID }

const LotphaseStartNumber = 1
const LotphaseTableName = "lotphases"

type LotphaseSfld string

func (o LotphaseSfld) String() string    { return string(o) }
func (o LotphaseSfld) WithTable() string { return LotphaseTableName + "." + string(o) }

var (
	Lotphase_             = field.RegObject(&Lotphase{})
	LotphaseUpdatableFlds = field.NewModel(LotphaseTableName, Lotphase_,
		"Name", "Type", "Editable", "SamplePercent",
		"MinSkillLevel", "Timeout", "Merge", "Execteam", "Quota", "ClaimedJobs")

	LotphaseSfldID            = LotphaseSfld(field.Sname(&Lotphase_.ID))
	LotphaseSfldLotID         = LotphaseSfld(field.Sname(&Lotphase_.LotID))
	LotphaseSfldNumber        = LotphaseSfld(field.Sname(&Lotphase_.Number))
	LotphaseSfldName          = LotphaseSfld(field.Sname(&Lotphase_.Name))
	LotphaseSfldType          = LotphaseSfld(field.Sname(&Lotphase_.Type))
	LotphaseSfldSubtype       = LotphaseSfld(field.Sname(&Lotphase_.Subtype))
	LotphaseSfldEditable      = LotphaseSfld(field.Sname(&Lotphase_.Editable))
	LotphaseSfldSamplePercent = LotphaseSfld(field.Sname(&Lotphase_.SamplePercent))
	LotphaseSfldMinSkillLevel = LotphaseSfld(field.Sname(&Lotphase_.MinSkillLevel))
	LotphaseSfldTimeout       = LotphaseSfld(field.Sname(&Lotphase_.Timeout))
	LotphaseSfldMerge         = LotphaseSfld(field.Sname(&Lotphase_.Merge))
	LotphaseSfldExecteam      = LotphaseSfld(field.Sname(&Lotphase_.Execteam))
	LotphaseSfldQuota         = LotphaseSfld(field.Sname(&Lotphase_.Quota))
	LotphaseSfldClaimedJobs   = LotphaseSfld(field.Sname(&Lotphase_.ClaimedJobs))
)

type PhaseID struct {
	LotID    int64
	Number   int32
	Execteam string
	Subtype  string
}

// LotphaseGroups builds a lotphase group, whose index means Lotphase.Number.
// For the returned list,
//   - the element at index 0 is empty and is not used.
//   - the element at index i contains all the teams in phase i (i >= 1). It contains one record with an emtpy Execteam
//     when no teams are assigned for the phase.
//   - the length will be <max-phase-number> + 1.
func LotphaseGroups(phases []*Lotphase) [][]*Lotphase {
	phasesList := make([][]*Lotphase, len(phases)+1)   // enough to handle all phases
	phaseTemplates := make([]*Lotphase, len(phases)+1) // lotphase.Number -> lotphase
	maxPhase := int32(0)
	for _, p := range phases {
		maxPhase = kmath.Max(maxPhase, p.Number)
		if p.Execteam == "" { // filter out the phases with empty Execteam and save the template for later use
			phaseTemplates[p.Number] = p
			continue
		}
		phasesList[p.Number] = append(phasesList[p.Number], p)
	}

	newPhaseList := phasesList[:maxPhase+1]
	for phase := LotphaseStartNumber; phase < len(newPhaseList); phase++ {
		if len(newPhaseList[phase]) == 0 { // if this element is empty, need to set a template
			newPhaseList[phase] = []*Lotphase{phaseTemplates[int32(phase)]}
		}
	}

	return newPhaseList
}

// LotphaseGroupedTeamMap builds a list, whose index means Lotphase.Number.
// For the returned list,
// - the element at index 0 is empty and is not used.
// - the element at index i contains all the teams in phase i (i >= 1), plus one record with an emtpy Execteam.
// - the length will be <max-phase-number> + 1.
func LotphaseGroupedTeamMap(phases []*Lotphase) []map[string]*Lotphase {
	phaseMapList := make([]map[string]*Lotphase, len(phases)+1) // enough to handle all phases
	maxPhase := int32(0)
	for _, p := range phases {
		if phaseMapList[p.Number] == nil {
			phaseMapList[p.Number] = make(map[string]*Lotphase)
			phaseMapList[p.Number][""] = cloneLotphase(p, "", nil) // compatible with old data and p will be used as a template
		}
		phaseMapList[p.Number][p.Execteam] = p // p.Execteam can be empty and p will be used as a template
		maxPhase = kmath.Max(maxPhase, p.Number)
	}
	return phaseMapList[:maxPhase+1]
}

func (o *LotsBiz) GetLotphase(ctx context.Context, lotID int64, phase int, execteam string) (*Lotphase, error) {
	return o.repo.GetPhase(ctx, lotID, phase, execteam)
}

func (o *LotsBiz) LoadLotphase(ctx context.Context, lotID int64, phase int, execteams ...string) ([]*Lotphase, error) {
	return o.repo.LoadPhases(ctx, lotID, phase, execteams...)
}

func cloneLotphase(src *Lotphase, newExecteam string, newQuota *anno.Lotphase_Quota) *Lotphase {
	return &Lotphase{
		ID:            0,
		LotID:         src.LotID,
		Number:        src.Number,
		Name:          src.Name,
		Type:          src.Type,
		Subtype:       src.Subtype,
		Editable:      src.Editable,
		SamplePercent: src.SamplePercent,
		MinSkillLevel: src.MinSkillLevel,
		Timeout:       src.Timeout,
		Merge:         src.Merge,
		Execteam:      newExecteam,
		Quota:         *serial.New(&ExecteamQuota{Quota: newQuota}),
	}
}

type PhaseQuota struct {
	Phase    *Lotphase
	NewQuota *anno.Lotphase_Quota
}

func (o *LotsBiz) AssignPhaseExecteam(ctx context.Context, req *anno.AssignExecteamRequest) error {
	lotID := kid.ParseID(req.Uid)
	lot, err := o.repo.GetByID(ctx, lotID, false)
	if err != nil {
		return err
	}
	phases, err := o.repo.LoadPhases(ctx, lotID, 0)
	if err != nil {
		return err
	}

	// calculate the phases to create, delete and check
	phasesToCreate, phasesToDelete, phasesToCheck, err := getPhasesChange(phases, req.Phases)
	if err != nil {
		return err
	}

	return o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		lotJobCount := lot.JobCount
		// check the phase quota
		for _, phaseQuota := range phasesToCheck {
			if err := o.updateLotphaseQuota(ctx, phaseQuota.Phase, phaseQuota.NewQuota, lotJobCount); err != nil {
				return err
			}
		}

		// create and delete phases if any
		phasesToCreate, err = o.batchCreateLotphase(ctx, phasesToCreate, lotJobCount)
		if err != nil {
			return err
		}
		if err := o.deleteLotphases(ctx, phasesToDelete); err != nil {
			return err
		}

		// check and grant permissions
		newTeams, hasUpdate := getEffectiveExecteams(phases, phasesToCreate, phasesToDelete)
		if hasUpdate {
			if err := o.GrantExecteamAccess(ctx, lotID, newTeams...); err != nil {
				return err
			}
		}

		// send messages, put this in the end
		if lot.State.IsUnstart() {
			return nil
		}
		if len(phasesToCreate) > 0 {
			if err := mq.PublishEvt(ctx, EvtTypeAnnoLotphase, EvtSubtypeBatchCreate, phasesToCreate); err != nil {
				return err
			}
		}
		if len(phasesToDelete) > 0 {
			lotphaseIDs := lo.Map(phasesToDelete, func(v *Lotphase, _ int) int64 { return v.ID })
			if err := mq.PublishEvt(ctx, EvtTypeAnnoLotphase, EvtSubtypeDelete, lotphaseIDs); err != nil {
				return err
			}
		}
		return nil
	})
}

func getPhasesChange(phases []*Lotphase, updates []*anno.AssignExecteamRequest_Phase) (
	phasesToCreate, phasesToDelete []*Lotphase, phasesToCheck []*PhaseQuota, err error) {
	phaseTeamMapList := LotphaseGroupedTeamMap(phases)
	origTeams := lo.Map(phases, func(v *Lotphase, _ int) string { return v.Execteam })
	teamsCounts := make(map[string]int)
	for _, teamUid := range origTeams {
		if teamUid != "" {
			teamsCounts[teamUid] += 1
		}
	}

	maxPhase := len(phaseTeamMapList) - 1
	for _, p := range updates {
		// check if the phase is in DB
		if int(p.Phase) < LotphaseStartNumber || int(p.Phase) > maxPhase {
			return nil, nil, nil, errors.NewErrNotFound(errors.WithMessage(fmt.Sprintf("phase-%d is not found", p.Phase)),
				errors.WithModel("lotphase"))
		}

		phaseTeams := phaseTeamMapList[p.Phase]
		toCreate, toDelete, toCheck := checkPhase(p, phaseTeams, teamsCounts)
		phasesToCreate = append(phasesToCreate, toCreate...)
		phasesToDelete = append(phasesToDelete, toDelete...)
		phasesToCheck = append(phasesToCheck, toCheck...)
	}
	return
}

// checkPhase checks the phase in request and returns the phases to create and delete.
func checkPhase(reqPhase *anno.AssignExecteamRequest_Phase,
	curPhaseTeams map[string]*Lotphase, origTeamsCounts map[string]int,
) (phasesToCreate, phasesToDelete []*Lotphase, phasesToCheck []*PhaseQuota) {
	for _, execteam := range reqPhase.Execteams {
		teamUid := execteam.Execteam // not empty, already checked in service layer
		phase := curPhaseTeams[teamUid]
		if phase == nil { // not found in DB, so we need to create a new lotphase
			newLotphase := cloneLotphase(curPhaseTeams[""], execteam.Execteam, execteam.Quota)
			phasesToCreate = append(phasesToCreate, newLotphase)
			origTeamsCounts[teamUid] += 1
		} else { // found in DB, so we might need to update this lotphase
			phasesToCheck = append(phasesToCheck, &PhaseQuota{Phase: phase, NewQuota: execteam.Quota})
		}

		delete(curPhaseTeams, teamUid)
	}

	// collect the phases to delete
	for teamUid := range curPhaseTeams {
		if teamUid != "" {
			origTeamsCounts[teamUid] -= 1
			phasesToDelete = append(phasesToDelete, curPhaseTeams[teamUid])
		}
	}
	return
}

func getEffectiveExecteams(phases []*Lotphase, toCreate, toDelete []*Lotphase) (teams []string, hasUpdate bool) {
	origTeams := lo.Map(phases, func(v *Lotphase, _ int) string { return v.Execteam })
	teamsCounts := make(map[string]int)
	for _, teamUid := range origTeams {
		if teamUid != "" {
			teamsCounts[teamUid] += 1
		}
	}

	for _, p := range toCreate {
		teamsCounts[p.Execteam] += 1
	}

	for _, p := range toDelete {
		teamsCounts[p.Execteam] -= 1
	}

	for teamUid, count := range teamsCounts {
		if count > 0 {
			teams = append(teams, teamUid)
		}
	}

	origTeams = lo.Uniq(lo.WithoutEmpty(origTeams))
	teamsHasUpdate := !(len(origTeams) == len(teams) && lo.Every(origTeams, teams))
	return teams, teamsHasUpdate
}

// updateLotphaseQuota updates lotphase quota according to new team quota and might also update `lotJobCount` param.
func (o *LotsBiz) updateLotphaseQuota(ctx context.Context, phase *Lotphase, newQuota *anno.Lotphase_Quota, lotJobCount int) error {
	shouldUpdateQuota := false
	oldQuota := &phase.Quota
	if oldQuota.E == nil || oldQuota.E.Quota == nil {
		shouldUpdateQuota = newQuota != nil
	} else {
		shouldUpdateQuota = oldQuota.E.Quota.Min != newQuota.Min || oldQuota.E.Quota.Max != newQuota.Max
	}
	if !shouldUpdateQuota {
		return nil
	}

	phase.Quota = *serial.New(&ExecteamQuota{Quota: newQuota})
	phase.Quota.E.WithJobCount(lotJobCount)
	fldMask := field.NewMask(LotphaseSfldQuota.String())
	if _, err := o.repo.UpdatePhase(ctx, phase, fldMask); err != nil {
		return err
	}

	return nil
}

func (o *LotsBiz) batchCreateLotphase(ctx context.Context, phases []*Lotphase, lotJobCount int) ([]*Lotphase, error) {
	if len(phases) == 0 {
		return nil, nil
	}

	for _, phase := range phases {
		phase.Quota.E.WithJobCount(lotJobCount)
	}

	phasesNew, err := o.repo.BatchCreatePhase(ctx, phases)
	if err != nil {
		return nil, err
	}

	return phasesNew, nil
}

func (o *LotsBiz) deleteLotphases(ctx context.Context, phases []*Lotphase) error {
	if len(phases) == 0 {
		return nil
	}

	// remove assigned users from the original executor team
	for _, p := range phases {
		if err := o.jobrepo.ClearExecutor(ctx, p.LotID, p.Number, p.Execteam); err != nil {
			return err
		}
		if err := o.repo.DeletePhaseExecutors(ctx, p, p.Execteam, nil); err != nil {
			return err
		}
	}

	// delete lotphase itself
	if err := o.repo.DeletePhases(ctx, phases); err != nil {
		return err
	}

	return nil
}
