/*
 * Ory <PERSON> API
 *
 * Documentation for all of Ory <PERSON>'s REST APIs. gRPC is documented separately.
 *
 * API version: 1.0.0
 * Contact: <EMAIL>
 */

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package client

import (
	"encoding/json"
)

// SubjectSet struct for SubjectSet
type SubjectSet struct {
	// Namespace of the Subject Set
	Namespace string `json:"namespace"`
	// Object of the Subject Set
	Object string `json:"object"`
	// Relation of the Subject Set
	Relation string `json:"relation"`
}

// NewSubjectSet instantiates a new SubjectSet object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewSubjectSet(namespace string, object string, relation string) *SubjectSet {
	this := SubjectSet{}
	this.Namespace = namespace
	this.Object = object
	this.Relation = relation
	return &this
}

// NewSubjectSetWithDefaults instantiates a new SubjectSet object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewSubjectSetWithDefaults() *SubjectSet {
	this := SubjectSet{}
	return &this
}

// GetNamespace returns the Namespace field value
func (o *SubjectSet) GetNamespace() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Namespace
}

// GetNamespaceOk returns a tuple with the Namespace field value
// and a boolean to check if the value has been set.
func (o *SubjectSet) GetNamespaceOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Namespace, true
}

// SetNamespace sets field value
func (o *SubjectSet) SetNamespace(v string) {
	o.Namespace = v
}

// GetObject returns the Object field value
func (o *SubjectSet) GetObject() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Object
}

// GetObjectOk returns a tuple with the Object field value
// and a boolean to check if the value has been set.
func (o *SubjectSet) GetObjectOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Object, true
}

// SetObject sets field value
func (o *SubjectSet) SetObject(v string) {
	o.Object = v
}

// GetRelation returns the Relation field value
func (o *SubjectSet) GetRelation() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Relation
}

// GetRelationOk returns a tuple with the Relation field value
// and a boolean to check if the value has been set.
func (o *SubjectSet) GetRelationOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Relation, true
}

// SetRelation sets field value
func (o *SubjectSet) SetRelation(v string) {
	o.Relation = v
}

func (o SubjectSet) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if true {
		toSerialize["namespace"] = o.Namespace
	}
	if true {
		toSerialize["object"] = o.Object
	}
	if true {
		toSerialize["relation"] = o.Relation
	}
	return json.Marshal(toSerialize)
}

type NullableSubjectSet struct {
	value *SubjectSet
	isSet bool
}

func (v NullableSubjectSet) Get() *SubjectSet {
	return v.value
}

func (v *NullableSubjectSet) Set(val *SubjectSet) {
	v.value = val
	v.isSet = true
}

func (v NullableSubjectSet) IsSet() bool {
	return v.isSet
}

func (v *NullableSubjectSet) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableSubjectSet(val *SubjectSet) *NullableSubjectSet {
	return &NullableSubjectSet{value: val, isSet: true}
}

func (v NullableSubjectSet) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableSubjectSet) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
