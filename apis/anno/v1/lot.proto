syntax = "proto3";

package anno.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
// import "google/protobuf/field_mask.proto";
import "google/api/field_behavior.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";
import "iam/v1/type.proto";
import "anno/v1/config.proto";
import "anno/v1/order.proto";
import "anno/v1/type.proto";
import "anno/v1/elemanno.proto";
import "anno/v1/type_lotconfig.proto";
import "types/range.proto";
import "types/tag.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anno/v1;anno";
option java_multiple_files = true;
option java_package = "anno.v1";

service Lots {
  rpc CreateLot (CreateLotRequest) returns (Lot) {
    option (google.api.http) = {
      post: "/v1/lots"
      body: "*"
    };
  }

  // create a new lot by cloning an existing lot.
  rpc CloneLot (CloneLotRequest) returns (Lot) {
    option (google.api.http) = {
      post: "/v1/lots/{uid}/clone"
      body: "*"
    };
  }

  // update lot. only simple information like name or desc update are allowed.
  rpc UpdateLot (UpdateLotRequest) returns (Lot) {
    option (google.api.http) = {
      patch: "/v1/lots/{lot.uid}"
      body: "lot"
    };
  }

  rpc DeleteLot (DeleteLotRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/lots/{uid}"
    };
  }

  // get lots assigned to a user or a team.
  // if the phase's execteam is empty, the phase is not assigned to the user or team.
  rpc ListLotsByExecutor (ListLotsByExecutorRequest) returns (ListLotsByExecutorReply) {
    option (google.api.http) = {
      get: "/v1/lots/by-executor"
    };
  }

  // // get lots assigned to a team
  // rpc GetLotsByExecteam (GetLotsByExecteamRequest) returns (ListLotReply) {
  //   option (google.api.http) = {
  //     get: "/v1/lots/by-execteam"
  //   };
  // }

  rpc GetLot (GetLotRequest) returns (Lot) {
    option (google.api.http) = {
      get: "/v1/lots/{uid}"
    };
  }

  rpc ListLot (ListLotRequest) returns (ListLotReply) {
    option (google.api.http) = {
      get: "/v1/lots"
    };
  }

  rpc StartLot (GetLotRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/lots/{uid}/start"
      body: "*"
    };
  }

  rpc PauseLot (GetLotRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/lots/{uid}/pause"
      body: "*"
    };
  }

  rpc CancelLot (GetLotRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/lots/{uid}/cancel"
      body: "*"
    };
  }

  // list assigned execution teams and executors
  rpc ListExecteams (ListExecteamsRequest) returns (ListExecteamsReply) {
    option (google.api.http) = {
      get: "/v1/lots/{uid}/execteams"
    };
  }

  // assign execution team, update or delete an execution team
  rpc AssignExecteam (AssignExecteamRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/lots/{uid}/execteam"
      body: "*"
    };
  }

  // add or remove executors
  rpc ManageExecutors (ManageExecutorsRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/lots/{uid}/executors"
      body: "*"
    };
  }

  // list executors at a phase
  rpc ListExecutors (ListExecutorsRequest) returns (ListExecutorsReply) {
    option (google.api.http) = {
      get: "/v1/lots/{uid}/executors"
    };
  }

  // Get summary of a lot
  rpc GetSummary (GetLotRequest) returns (GetLotSummaryReply) {
    option (google.api.http) = {
      get: "/v1/lots/{uid}/summary"
    };
  }

  // // Get annotation result of a lot, only return the first 1000 jobs' annotation results
  // rpc GetAnnos (GetLotRequest) returns (GetLotAnnosReply) {
  //   option (google.api.http) = {
  //     get: "/v1/lots/{uid}/annos"
  //   };
  // }

  // GetVisibleLots is used to query visible lots for a user (e.g. anno member)
  rpc GetVisibleLots(GetVisibleLotsRequest) returns (GetVisibleLotsReply);

  // ExportLotAnnos exports lot annotations
  rpc ExportLotAnnos(ExportLotAnnosRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/lots/{uid}/export-annos"
      body: "*"
    };
  };

  // SetLotAnnoResult sets the annotation result in lot
  rpc SetLotAnnoResult(SetLotAnnoResultRequest) returns (google.protobuf.Empty);

  // allows demander to download annos
  rpc AllowDownloadAnnos(AllowDownloadAnnosRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/lots/{uid}/allow-download-annos"
      body: "*"
    };
  }

  rpc AddTag (types.TagRequest) returns (types.TagList) {
    option (google.api.http) = {
      put: "/v1/lots/{uid}/tag"
      body: "tags"
    };
  }

  rpc DeleteTag (types.TagRequest) returns (types.TagList) {
    option (google.api.http) = {
      delete: "/v1/lots/{uid}/tag"
    };
  }
  rpc JobCountByLots(JobCountByLotidsRequest) returns (JobCountByLotidsReply){
    option (google.api.http) = {
      get: "/v1/job-count-by-lots"
    };
  }
}

message CreateLotRequest {
  // mandatory in update-requests
  string uid = 1;
  // mandatory in create-requests
  string name = 2;
  string desc = 3;

  // mandatory in create-requests
  string project_uid = 4;

  // task type; mandatory in create-requests
  Lot.Type.Enum type = 5 [(validate.rules).enum = {defined_only: true}];
  // larger number indicates higher priority
  int32 priority = 6;
  // whether to automatically start
  bool autostart = 7;
  // number of elements in a job
  int32 job_size = 8;

  // lot ontologies
  Lotontologies ontologies = 9;
  // execution phases; phase number starts from 1
  repeated Lotphase phases = 10;
  // execution instructions in format of HTML or Markdown
  string instruction = 11;
  // string tpl_uid = 6;

  string org_uid = 12;
  // mandatory in create-requests
  string data_uid = 13;
  // annotation result output config
  OutConfig out = 14;

  // expected end time
  google.protobuf.Timestamp exp_end_time = 15;
  // if to treat job as consecutive frames
  bool is_frame_series = 16;

  // // [DEPRECATED] make annotations within the radius, unit is meter
  // float work_range = 17;
  reserved 17;

  // order UID
  string order_uid = 18;
  // annotation tool configuration
  Lot.ToolConfig tool_cfg = 19;
  // allowed comment reason list
  repeated CommentReasonClass comment_reasons = 20;
}
//message CreateLotReply {}

message CloneLotRequest {
  // source lot uid
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // modified fields
  UpdateLotRequest updates = 2;
  // if to copy executors
  bool copy_executors = 3;
}

message UpdateLotRequest {
  option (openapi.v3.schema) = {
    required: ["lot", "fields"]
  };

  // update contents
  CreateLotRequest lot = 1;
  // name of fields to be updated
  repeated string fields = 2;
}
//message UpdateLotReply {}

message DeleteLotRequest {
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
}
//message DeleteLotReply {}

message GetLotRequest {
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
}
//message GetLotReply {}

message ListLotRequest {
  int32 page = 1;
  int32 pagesz = 2 [(validate.rules).int32 = {gte:0, lte: 100}];

  // filter by orgnization
  string org_uid = 3 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|^"}];
  // filter by creator
  string creator_uid = 4 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|^"}];
  // filter by name pattern
  string name_pattern = 5;
  // string project_uid = 3;

  // filter by lot state
  repeated Lot.State.Enum states = 6;
  // filter by lot type
  Lot.Type.Enum type = 7;
  // filter by order
  string order_uid = 8 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|^"}];
  // find by attached tags
  repeated string tags = 9;

  // include lot's orgnization in the reply
  bool with_org = 15;
}

message ListLotReply {
  option (openapi.v3.schema) = {
    required: ["lots"]
  };

  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated Lot lots = 2;
  // the organization that the lot, at the corresponding position, belongs to.
  repeated iam.v1.BaseUser orgs = 3;
}

message ListLotsByExecutorRequest {
  int32 page = 1;
  int32 pagesz = 2 [(validate.rules).int32 = {gte:0, lte: 100}];

  // list lots assigned to the user;
  // requestor should have manager role in the user's organization, if it is not oneself.
  // if omitted, default to the requestor.
  string user_uid = 3 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|^"}];
  // list lots assigned to the team;
  // requestor should have manager role in this team.
  string team_uid = 4 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|^"}];
  // list lots assigned to teams within this organization;
  // requestor should have IamGroup.list permission in this organization, e.g. a manager,.
  string org_uid = 5 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|^"}];
  // filter by lot state
  repeated Lot.State.Enum states = 6;
  // filter by lot type
  Lot.Type.Enum type = 7;
  // filter by name pattern
  string name_pattern = 8;
  // if to skip lots that a claim request will return no job for an executor; available only to executor query.
  bool claimable = 9;
}

message ListLotsByExecutorReply {
  option (openapi.v3.schema) = {
    required: ["lots"]
  };

  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated Lot lots = 2;

  message Extra {
    bool has_rejected_jobs = 1;
  }
  // the extra info that the lot has
  map<string, Extra> extras = 3;
}

message ListExecteamsRequest {
  // lot uid
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // if to include team info in the reply
  bool with_execteams = 2;
  // if to include executors in the reply
  bool with_executors = 3;
}

message ListExecteamsReply {
  option (openapi.v3.schema) = {
    required: ["phases"]
  };

  message Execteam {
    option (openapi.v3.schema) = {
      required: ["team", "executors"]
    };

    // execution team info
    iam.v1.BaseUser team = 1;
    // executors info
    repeated iam.v1.BaseUser executors = 2;
    Lotphase.Quota quota = 3;
  }
  message Phase {
    option (openapi.v3.schema) = {
      required: ["teams"]
    };

    // execution teams and executors info in this phase
    repeated Execteam teams = 1;
  }

  // execution teams and executors info for each phase
  repeated Phase phases = 1;
}

message AssignExecteamRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "phases"]
  };

  message Phase {
    option (openapi.v3.schema) = {
      required: ["phase", "execteams"]
    };

    // phase number, starts from 1
    int32 phase = 1;
    // // [DEPRECATED] set execution teams; currently, only support one team
    // repeated string team_uids = 2;
    reserved 2;

    // support multiple teams in one phase
    repeated Lotphase.Execteam execteams = 3;
  }

  // lot uid
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  repeated Phase phases = 2;
}

message ManageExecutorsRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "phases"]
  };

  message Phase {
    option (openapi.v3.schema) = {
      required: ["phase", "team_uid"]
    };

    // phase number, starts from 1
    int32 phase = 1;
    // the executors' team
    string team_uid = 3;
    // user uids to add
    repeated string add = 4;
    // user uids to remove
    repeated string delete = 5;
    // string subtype = 2;
  }

  // lot uid
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  repeated Phase phases = 2;
}

message ListExecutorsRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "phase", "team_uid"]
  };

  // lot uid
  string uid = 1;
  // phase number, starts from 1
  int32 phase = 2 [(validate.rules).int32 = {gte: 1}];
  string subtype = 3;
  // mandatory in a multi-team configuration
  string team_uid = 4;

  int32 page = 11;
  int32 pagesz = 12 [(validate.rules).int32 = {gte:0, lte: 500}];
}

message ListExecutorsReply {
  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  // repeated string user_uids = 2;
  repeated iam.v1.BaseUser users = 2;

  // message PhaseExecutor {
  //   int32 phase = 2;
  //   string subtype = 3;
  //   string team_uid = 4;
  //   iam.v1.BaseUser user = 5;
  // }
  // repeated PhaseExecutor executors = 2;
}

message Lot {
  option (openapi.v3.schema) = {
    required: ["uid", "name", "state", "type", "priority", "job_size", "ontologies", "phases",
      "created_at", "org_uid", "data_uid"]
  };

  message Type {
    enum Enum {
      unspecified = 0;
      // object detection
      annotate = 1;
      // classify = 3;

      segment_instance = 11;
      segment_semantic = 12;
      segment_panoptic = 13;
    }
  }

  message State {
    enum Enum {
      unspecified = 0;
      initializing = 1;
      unstart = 2;
      ongoing = 3;
      finished = 4;
      paused = 5;
      canceled = 6;
    }
  }

  message Range {
    option (openapi.v3.schema) = {
      required: ["shape", "data"]
    };

    message Shape {
      enum Enum {
        unspecified = 0;
        circle = 1;
        rectangle = 2;
      }
    }

    // shape type
    Shape.Enum shape = 1;
    // the params of the shape; specific to shape type.
    // circle: [radius], the unit is meter (in pointclouds), or pixel (in images);
    // rectangle: [width, height], the unit is meter (in pointclouds), or pixel (in images);
    repeated float data = 2;
    // range on the z-axis, the unit is meter
    types.Range zrange = 3;
    // which rawdata types the range is applicable to
    repeated Rawdata.Type.Enum rawdata_types = 4;
  }

  // lot UID
  string uid = 1;
  // lot name
  string name = 2;
  // lot description
  string desc = 3;
  // lot state
  State.Enum state = 4;
  // // lot template uid
  // string tpl_uid = 5;

  // lot type
  Type.Enum type = 5;
  // data type
  Element.Type.Enum data_type = 24;
  // if to treat job as consecutive frames
  bool is_frame_series = 25;
  // larger number indicates higher priority
  int32 priority = 6;
  // whether to automatically start
  bool autostart = 7;
  // maximum number of elements in a job.
  // if it is 0, a job is created per subfolder
  int32 job_size = 8;

  // lot ontologies
  Lotontologies ontologies = 9;
  // execution phases; phase number starts from 1
  repeated Lotphase phases = 10;
  // execution instructions in format of Markdown?
  string instruction = 11;

  // creator UID
  string creator_uid = 12;
  // number of elements in this lot
  int32 data_size = 13 [(google.api.field_behavior) = OUTPUT_ONLY];
  // expected end time
  google.protobuf.Timestamp exp_end_time = 14;
  google.protobuf.Timestamp updated_at = 15 [(google.api.field_behavior) = OUTPUT_ONLY];
  google.protobuf.Timestamp created_at = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // error info if state is ls_error
  Error error = 17;

  // UID of the organization which the lot belongs to
  string org_uid = 18;
  // UID of the data associated with the lot
  string data_uid = 19;
  // annotation result output config
  OutConfig out = 20;

  // number of jobs in this lot
  int32 job_count = 21;
  // if jobs are created
  bool job_ready = 22;

  // // [DEPRECATED] make annotations within the radius, unit is meter
  // float work_range = 23;
  reserved 23;

  // order UID
  string order_uid = 26;
  // manually annotated objects count; only available after lot is finished
  int32 ins_cnt = 27;
  // annotated objects count (including interpolated ones); only available after lot is finished
  int32 ins_total = 28;

  message ToolConfig {
    // work range indicators
    repeated Range ranges = 1;
    // if projected objects are editable in a 23D fusion task
    bool projected_editable = 2;
    // if to regenerate 2D projected objects according to 3D objects
    bool redo_projection = 3;

    message Ruler {
      option (openapi.v3.schema) = {
        required: ["name", "type", "data"]
      };

      // a friendly name for the ruler
      string name = 1;
      // shape of the ruler
      Range.Shape.Enum type = 2;
      // the params of the shape; specific to shape type.
      // rectangle: [width, height], the unit is pixel;
      // circle: [radius], the unit is pixel;
      repeated double data = 3;
      // which rawdata types the ruler is applicable to
      repeated Rawdata.Type.Enum rawdata_types = 4;
    }
    // 定义各种类型和大小的量尺，方便比对标志物的尺寸是否合规
    repeated Ruler rulers = 4;

    // whether to save 2D projected objects
    bool save_projected = 5;

    // the order of images in a single frame, specified with images' rawdata.meta.image.camera
    repeated string image_order = 6;

    bool segmentation_3d_enabled = 7;

    message PreBox {
      option (openapi.v3.schema) = {
        required: ["name", "length", "width","height"]
      };

      string name = 1;
      float length = 2;
      float width = 3;
      float height = 4;
    }
    repeated PreBox pre_box = 8;
  }
  // annotation tool configuration
  ToolConfig tool_cfg = 29;

  // anno result url
  string anno_result_url = 30;
  // indicate if the demander can export annos
  bool can_export_annos = 31;
  // allowed comment reason list
  repeated CommentReasonClass comment_reasons = 32;
  // tags attached to the lot
  repeated string tags = 33;
}

// contains number of jobs in various phases
message GetLotSummaryReply {
  option (openapi.v3.schema) = {
    required: ["total_jobs", "jobs_at_phase"]
  };

  // total number of jobs
  int32 total_jobs = 1;
  // jobs at each phase 1~n, the last element means the finished jobs.
  // if the lot has n phases, the length of jobs_at_phase is n+1.
  repeated int32 jobs_at_phase = 2;
}

message GetLotAnnosReply {
  // URL to the labeling result file
  // string url = 1;

  repeated ElementAnno annotations = 1;
  // number of objects annotated in the job
  int32 ins_cnt = 2;
}

message GetVisibleLotsRequest {
  string executor_uid = 1;
  repeated Lot.State.Enum states = 2 [(validate.rules).repeated.items.enum = {defined_only: true}];
  bool count_only = 3;
  // if to skip lots that a claim request will return no job for an executor; available only to executor query.
  bool claimable = 4;
}

message GetVisibleLotsReply {
  repeated string lot_uids = 1 [(validate.rules).repeated.items.string = {pattern: "^[a-z0-9]{11}$"}];
  map<string, int32> lotCount = 2; // valid if count_only = true. key is LotState, value is count.
}

message ExportLotAnnosRequest {
  // lot UID
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // option
  ExportOrderAnnosRequest.Option.Enum option = 2 [(validate.rules).enum = {defined_only: true}];

  repeated int32 phases = 3;
}

message SetLotAnnoResultRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "url"]
  };

  // lot UID
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // URL to the result
  string url = 2 [(validate.rules).string.uri_ref = true];
}

message AllowDownloadAnnosRequest {
  // lot UID
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // whether to allow demanders to download order annos: if false, means we have allowed it before but we want to disallow it now
  bool allow = 2;
}
message JobCountByLotidsRequest{
  option (openapi.v3.schema) = {
    required: ["ids"]
  };
  repeated string ids = 1;
}

message JobCountByLotidsReply{
  message PhaseCount {
    int32 phase = 1;
    int32 count = 2;
  }
  message LotInfo {
    string lot_id = 1;
    repeated PhaseCount count = 2;
  }
  repeated LotInfo lots = 1;
}
