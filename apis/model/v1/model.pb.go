// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: model/v1/model.proto

package model

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	types "gitlab.rp.konvery.work/platform/apis/types"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateModelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [mandatory for create] model name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// [mandatory for create] model type: od/segmentation
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	// base model name
	Base string `protobuf:"bytes,3,opt,name=base,proto3" json:"base,omitempty"`
	// run uid
	RunUid string `protobuf:"bytes,4,opt,name=run_uid,json=runUid,proto3" json:"run_uid,omitempty"`
	// uri
	Uri string `protobuf:"bytes,5,opt,name=uri,proto3" json:"uri,omitempty"`
	// [mandatory for create] version
	Version string `protobuf:"bytes,6,opt,name=version,proto3" json:"version,omitempty"`
	// the size of the model in bytes
	Bytes float64 `protobuf:"fixed64,7,opt,name=bytes,proto3" json:"bytes,omitempty"`
	// metric of the model: precision/recall/accuracy/f1/mae/rmse/...
	Metric map[string]float64 `protobuf:"bytes,8,rep,name=metric,proto3" json:"metric,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	// tags attached to the model
	Tags []string `protobuf:"bytes,9,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *CreateModelRequest) Reset() {
	*x = CreateModelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateModelRequest) ProtoMessage() {}

func (x *CreateModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateModelRequest.ProtoReflect.Descriptor instead.
func (*CreateModelRequest) Descriptor() ([]byte, []int) {
	return file_model_v1_model_proto_rawDescGZIP(), []int{0}
}

func (x *CreateModelRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateModelRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CreateModelRequest) GetBase() string {
	if x != nil {
		return x.Base
	}
	return ""
}

func (x *CreateModelRequest) GetRunUid() string {
	if x != nil {
		return x.RunUid
	}
	return ""
}

func (x *CreateModelRequest) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *CreateModelRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CreateModelRequest) GetBytes() float64 {
	if x != nil {
		return x.Bytes
	}
	return 0
}

func (x *CreateModelRequest) GetMetric() map[string]float64 {
	if x != nil {
		return x.Metric
	}
	return nil
}

func (x *CreateModelRequest) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

type UpdateModelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid    string              `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Data   *CreateModelRequest `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Fields []string            `protobuf:"bytes,3,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *UpdateModelRequest) Reset() {
	*x = UpdateModelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_model_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateModelRequest) ProtoMessage() {}

func (x *UpdateModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_model_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateModelRequest.ProtoReflect.Descriptor instead.
func (*UpdateModelRequest) Descriptor() ([]byte, []int) {
	return file_model_v1_model_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateModelRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *UpdateModelRequest) GetData() *CreateModelRequest {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateModelRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type Model struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// model uid
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// model name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// model type: od/segmentation
	Type string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	// base model name
	Base string `protobuf:"bytes,4,opt,name=base,proto3" json:"base,omitempty"`
	// run uid
	RunUid string `protobuf:"bytes,5,opt,name=run_uid,json=runUid,proto3" json:"run_uid,omitempty"`
	// uri
	Uri string `protobuf:"bytes,6,opt,name=uri,proto3" json:"uri,omitempty"`
	// version
	Version string `protobuf:"bytes,7,opt,name=version,proto3" json:"version,omitempty"`
	// the size of the model in bytes
	Bytes float64 `protobuf:"fixed64,8,opt,name=bytes,proto3" json:"bytes,omitempty"`
	// metric of the model: precision/recall/accuracy/f1/mae/rmse/...
	Metric map[string]float64 `protobuf:"bytes,9,rep,name=metric,proto3" json:"metric,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	// tags attached to the model
	Tags      []string               `protobuf:"bytes,10,rep,name=tags,proto3" json:"tags,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *Model) Reset() {
	*x = Model{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_model_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Model) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Model) ProtoMessage() {}

func (x *Model) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_model_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Model.ProtoReflect.Descriptor instead.
func (*Model) Descriptor() ([]byte, []int) {
	return file_model_v1_model_proto_rawDescGZIP(), []int{2}
}

func (x *Model) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Model) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Model) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Model) GetBase() string {
	if x != nil {
		return x.Base
	}
	return ""
}

func (x *Model) GetRunUid() string {
	if x != nil {
		return x.RunUid
	}
	return ""
}

func (x *Model) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *Model) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Model) GetBytes() float64 {
	if x != nil {
		return x.Bytes
	}
	return 0
}

func (x *Model) GetMetric() map[string]float64 {
	if x != nil {
		return x.Metric
	}
	return nil
}

func (x *Model) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Model) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Model) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type ModelFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// run uid
	RunUid string `protobuf:"bytes,1,opt,name=run_uid,json=runUid,proto3" json:"run_uid,omitempty"`
	// type
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	// name pattern
	NamePattern string `protobuf:"bytes,4,opt,name=name_pattern,json=namePattern,proto3" json:"name_pattern,omitempty"`
	// model version
	Version string `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`
	// base model pattern
	BasePattern string `protobuf:"bytes,6,opt,name=base_pattern,json=basePattern,proto3" json:"base_pattern,omitempty"`
	// attached tags
	Tags []string `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *ModelFilter) Reset() {
	*x = ModelFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_model_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelFilter) ProtoMessage() {}

func (x *ModelFilter) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_model_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelFilter.ProtoReflect.Descriptor instead.
func (*ModelFilter) Descriptor() ([]byte, []int) {
	return file_model_v1_model_proto_rawDescGZIP(), []int{3}
}

func (x *ModelFilter) GetRunUid() string {
	if x != nil {
		return x.RunUid
	}
	return ""
}

func (x *ModelFilter) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ModelFilter) GetNamePattern() string {
	if x != nil {
		return x.NamePattern
	}
	return ""
}

func (x *ModelFilter) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ModelFilter) GetBasePattern() string {
	if x != nil {
		return x.BasePattern
	}
	return ""
}

func (x *ModelFilter) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

type GetModelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *GetModelRequest) Reset() {
	*x = GetModelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_model_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelRequest) ProtoMessage() {}

func (x *GetModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_model_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelRequest.ProtoReflect.Descriptor instead.
func (*GetModelRequest) Descriptor() ([]byte, []int) {
	return file_model_v1_model_proto_rawDescGZIP(), []int{4}
}

func (x *GetModelRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type DeleteModelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid    string       `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Filter *ModelFilter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *DeleteModelRequest) Reset() {
	*x = DeleteModelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_model_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteModelRequest) ProtoMessage() {}

func (x *DeleteModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_model_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteModelRequest.ProtoReflect.Descriptor instead.
func (*DeleteModelRequest) Descriptor() ([]byte, []int) {
	return file_model_v1_model_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteModelRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *DeleteModelRequest) GetFilter() *ModelFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type ListModelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagesz int32 `protobuf:"varint,1,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// An opaque pagination token returned from a previous call.
	// An empty token denotes the first page.
	PageToken string       `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	Filter    *ModelFilter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListModelRequest) Reset() {
	*x = ListModelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_model_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListModelRequest) ProtoMessage() {}

func (x *ListModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_model_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListModelRequest.ProtoReflect.Descriptor instead.
func (*ListModelRequest) Descriptor() ([]byte, []int) {
	return file_model_v1_model_proto_rawDescGZIP(), []int{6}
}

func (x *ListModelRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListModelRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListModelRequest) GetFilter() *ModelFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type ListModelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Models []*Model `protobuf:"bytes,1,rep,name=models,proto3" json:"models,omitempty"`
	// An opaque pagination token, if not empty, to be used to fetch the next page of results
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// total number of items found; valid only in the first page reply.
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListModelReply) Reset() {
	*x = ListModelReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_model_v1_model_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListModelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListModelReply) ProtoMessage() {}

func (x *ListModelReply) ProtoReflect() protoreflect.Message {
	mi := &file_model_v1_model_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListModelReply.ProtoReflect.Descriptor instead.
func (*ListModelReply) Descriptor() ([]byte, []int) {
	return file_model_v1_model_proto_rawDescGZIP(), []int{7}
}

func (x *ListModelReply) GetModels() []*Model {
	if x != nil {
		return x.Models
	}
	return nil
}

func (x *ListModelReply) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListModelReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

var File_model_v1_model_proto protoreflect.FileDescriptor

var file_model_v1_model_proto_rawDesc = []byte{
	0x0a, 0x14, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x0f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x74, 0x61, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xff, 0x02, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x07, 0x72, 0x75, 0x6e, 0x5f, 0x75,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x72, 0x13, 0x32,
	0x11, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x7c,
	0x5e, 0x24, 0x52, 0x06, 0x72, 0x75, 0x6e, 0x55, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x03, 0x75, 0x72,
	0x69, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x90, 0x01,
	0x01, 0x52, 0x03, 0x75, 0x72, 0x69, 0x12, 0x32, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x72, 0x13, 0x32, 0x11,
	0x5e, 0x5b, 0x5c, 0x77, 0x2e, 0x2d, 0x5d, 0x7b, 0x33, 0x2c, 0x33, 0x32, 0x7d, 0x24, 0x7c, 0x5e,
	0x24, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x79,
	0x74, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x62, 0x79, 0x74, 0x65, 0x73,
	0x12, 0x40, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x3a, 0x03, 0xba, 0x47, 0x00, 0x22, 0x8b, 0x01, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12,
	0x30, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x3a, 0x19, 0xba, 0x47, 0x16, 0xba, 0x01,
	0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x04, 0x64, 0x61, 0x74, 0x61, 0xba, 0x01, 0x06, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x22, 0xb4, 0x04, 0x0a, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x27,
	0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12,
	0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31,
	0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62,
	0x61, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x07, 0x72, 0x75, 0x6e, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x72, 0x13, 0x32, 0x11, 0x5e, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x7c, 0x5e, 0x24, 0x52, 0x06,
	0x72, 0x75, 0x6e, 0x55, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x03, 0x75, 0x72, 0x69, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x90, 0x01, 0x01, 0x52, 0x03, 0x75,
	0x72, 0x69, 0x12, 0x31, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x72, 0x12, 0x32, 0x10, 0x5e, 0x5b, 0x5c, 0x77,
	0x2e, 0x2d, 0x5d, 0x7b, 0x33, 0x2d, 0x33, 0x32, 0x7d, 0x7c, 0x5e, 0x24, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x62, 0x79, 0x74, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x06, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x3a, 0x34, 0xba, 0x47, 0x31, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64,
	0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x04, 0x74, 0x79, 0x70, 0x65, 0xba, 0x01,
	0x03, 0x75, 0x72, 0x69, 0xba, 0x01, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0xba, 0x01,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0xc8, 0x01, 0x0a, 0x0b,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x31, 0x0a, 0x07, 0x72,
	0x75, 0x6e, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xfa, 0x42,
	0x15, 0x72, 0x13, 0x32, 0x11, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31,
	0x31, 0x7d, 0x24, 0x7c, 0x5e, 0x24, 0x52, 0x06, 0x72, 0x75, 0x6e, 0x55, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x74, 0x65,
	0x72, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x50, 0x61,
	0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x21, 0x0a, 0x0c, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x73, 0x65, 0x50, 0x61, 0x74, 0x74, 0x65,
	0x72, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x22, 0x3a, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e,
	0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x22, 0x75, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72, 0x19, 0x32, 0x17, 0x5e, 0x5b,
	0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x7c, 0x62, 0x79, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x83, 0x01, 0x0a, 0x10, 0x4c, 0x69,
	0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21,
	0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73,
	0x7a, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x2d, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22,
	0x97, 0x01, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x27, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e,
	0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x3a, 0x1e, 0xba, 0x47, 0x1b, 0xba, 0x01,
	0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0xba, 0x01, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x32, 0xc9, 0x04, 0x0a, 0x06, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x1c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x15, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x3a, 0x01, 0x2a, 0x22, 0x0a, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x12, 0x57, 0x0a, 0x06, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x1e, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x18, 0x3a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0x10, 0x2f, 0x76, 0x31,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x58, 0x0a,
	0x06, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x1c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x18, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x12, 0x2a, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x4b, 0x0a, 0x03, 0x47, 0x65, 0x74, 0x12, 0x19,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x12, 0x12, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x7b,
	0x75, 0x69, 0x64, 0x7d, 0x12, 0x50, 0x0a, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x12, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0c, 0x12, 0x0a, 0x2f, 0x76, 0x31, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12, 0x4f, 0x0a, 0x06, 0x41, 0x64, 0x64, 0x54, 0x61, 0x67,
	0x12, 0x11, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x61, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x0e, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x61, 0x67, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x04, 0x74, 0x61, 0x67,
	0x73, 0x1a, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x7b, 0x75,
	0x69, 0x64, 0x7d, 0x2f, 0x74, 0x61, 0x67, 0x12, 0x4c, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x54, 0x61, 0x67, 0x12, 0x11, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x61, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0e, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x54, 0x61, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x2a,
	0x14, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64,
	0x7d, 0x2f, 0x74, 0x61, 0x67, 0x42, 0x35, 0x5a, 0x33, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e,
	0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_model_v1_model_proto_rawDescOnce sync.Once
	file_model_v1_model_proto_rawDescData = file_model_v1_model_proto_rawDesc
)

func file_model_v1_model_proto_rawDescGZIP() []byte {
	file_model_v1_model_proto_rawDescOnce.Do(func() {
		file_model_v1_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_model_v1_model_proto_rawDescData)
	})
	return file_model_v1_model_proto_rawDescData
}

var file_model_v1_model_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_model_v1_model_proto_goTypes = []interface{}{
	(*CreateModelRequest)(nil),    // 0: model.v1.CreateModelRequest
	(*UpdateModelRequest)(nil),    // 1: model.v1.UpdateModelRequest
	(*Model)(nil),                 // 2: model.v1.Model
	(*ModelFilter)(nil),           // 3: model.v1.ModelFilter
	(*GetModelRequest)(nil),       // 4: model.v1.GetModelRequest
	(*DeleteModelRequest)(nil),    // 5: model.v1.DeleteModelRequest
	(*ListModelRequest)(nil),      // 6: model.v1.ListModelRequest
	(*ListModelReply)(nil),        // 7: model.v1.ListModelReply
	nil,                           // 8: model.v1.CreateModelRequest.MetricEntry
	nil,                           // 9: model.v1.Model.MetricEntry
	(*timestamppb.Timestamp)(nil), // 10: google.protobuf.Timestamp
	(*types.TagRequest)(nil),      // 11: types.TagRequest
	(*emptypb.Empty)(nil),         // 12: google.protobuf.Empty
	(*types.TagList)(nil),         // 13: types.TagList
}
var file_model_v1_model_proto_depIdxs = []int32{
	8,  // 0: model.v1.CreateModelRequest.metric:type_name -> model.v1.CreateModelRequest.MetricEntry
	0,  // 1: model.v1.UpdateModelRequest.data:type_name -> model.v1.CreateModelRequest
	9,  // 2: model.v1.Model.metric:type_name -> model.v1.Model.MetricEntry
	10, // 3: model.v1.Model.updated_at:type_name -> google.protobuf.Timestamp
	10, // 4: model.v1.Model.created_at:type_name -> google.protobuf.Timestamp
	3,  // 5: model.v1.DeleteModelRequest.filter:type_name -> model.v1.ModelFilter
	3,  // 6: model.v1.ListModelRequest.filter:type_name -> model.v1.ModelFilter
	2,  // 7: model.v1.ListModelReply.models:type_name -> model.v1.Model
	0,  // 8: model.v1.Models.Create:input_type -> model.v1.CreateModelRequest
	1,  // 9: model.v1.Models.Update:input_type -> model.v1.UpdateModelRequest
	5,  // 10: model.v1.Models.Delete:input_type -> model.v1.DeleteModelRequest
	4,  // 11: model.v1.Models.Get:input_type -> model.v1.GetModelRequest
	6,  // 12: model.v1.Models.List:input_type -> model.v1.ListModelRequest
	11, // 13: model.v1.Models.AddTag:input_type -> types.TagRequest
	11, // 14: model.v1.Models.DeleteTag:input_type -> types.TagRequest
	2,  // 15: model.v1.Models.Create:output_type -> model.v1.Model
	2,  // 16: model.v1.Models.Update:output_type -> model.v1.Model
	12, // 17: model.v1.Models.Delete:output_type -> google.protobuf.Empty
	2,  // 18: model.v1.Models.Get:output_type -> model.v1.Model
	7,  // 19: model.v1.Models.List:output_type -> model.v1.ListModelReply
	13, // 20: model.v1.Models.AddTag:output_type -> types.TagList
	13, // 21: model.v1.Models.DeleteTag:output_type -> types.TagList
	15, // [15:22] is the sub-list for method output_type
	8,  // [8:15] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_model_v1_model_proto_init() }
func file_model_v1_model_proto_init() {
	if File_model_v1_model_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_model_v1_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateModelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_model_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateModelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_model_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Model); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_model_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_model_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_model_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteModelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_model_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListModelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_model_v1_model_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListModelReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_model_v1_model_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_model_v1_model_proto_goTypes,
		DependencyIndexes: file_model_v1_model_proto_depIdxs,
		MessageInfos:      file_model_v1_model_proto_msgTypes,
	}.Build()
	File_model_v1_model_proto = out.File
	file_model_v1_model_proto_rawDesc = nil
	file_model_v1_model_proto_goTypes = nil
	file_model_v1_model_proto_depIdxs = nil
}
