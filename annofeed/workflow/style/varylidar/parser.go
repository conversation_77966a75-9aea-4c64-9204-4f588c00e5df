package varylidar

import (
	"context"
	"fmt"
	"os"
	"path"

	"annofeed/internal/biz"
	"annofeed/workflow/common"
	"gitlab.rp.konvery.work/platform/pkg/kfs"
)

func Transform(ctx context.Context, data *biz.Data, dir string, startIdx int, hb common.Heartbeat) (dataType string, err error) {
	newdir := dir + "-1"
	o := newParser(dir, newdir)
	err = kfs.IterateDir(dir, func(entry os.DirEntry, subdir string, depth int) error {
		// skip directories
		if entry.IsDir() {
			return nil
		}

		hb(ctx)
		return o.parseFile(subdir, entry.Name())
	})
	if err != nil {
		return
	}

	tmp := dir + "-tmp"
	if err := os.Rename(dir, tmp); err != nil {
		return "", fmt.Errorf("failed to backup %s to %s: %w", dir, tmp, err)
	}
	if err := os.Rename(newdir, dir); err != nil {
		os.Rename(tmp, dir)
		return "", fmt.Errorf("failed to rename %s to %s: %w", newdir, dir, err)
	}
	if err := os.RemoveAll(tmp); err != nil {
		// fmt.Errorf("failed to delete backup dir %s: %w", tmp, err)
	}

	return "", nil
}

type parser struct {
	oldDir string
	newDir string
}

func newParser(oldDir, newDir string) *parser {
	o := &parser{
		oldDir: oldDir,
		newDir: newDir,
	}
	return o
}

func (o *parser) parseFile(subDir, name string) error {
	newName := name
	if common.FileExt(name) == common.RawdataFormatPCD {
		newName = common.LidarPCD
	}

	oldPath := path.Join(o.oldDir, subDir, name)
	newPath := path.Join(o.newDir, subDir, newName)
	newPathBase := path.Join(o.newDir, subDir)
	if err := os.MkdirAll(newPathBase, 0755); err != nil {
		return fmt.Errorf("failed to create dir %s: %w", newPathBase, err)
	}
	if err := os.Link(oldPath, newPath); err != nil {
		return fmt.Errorf("failed to link %s to %s: %w", newPath, oldPath, err)
	}
	if newName != name && kfs.PathNotExist(oldPath+common.OrigExt) { // only for pcd files that do not have xxx.orig files
		if err := os.WriteFile(newPath+common.OrigExt, []byte(path.Join(subDir, name)), 0644); err != nil {
			return fmt.Errorf("failed to write file %v: %w", newPath+common.OrigExt, err)
		}
	}

	return nil
}
