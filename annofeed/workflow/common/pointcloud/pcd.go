package pointcloud

import (
	"bufio"
	"encoding/binary"
	"fmt"
	"io"
	"math"
	"os"
	"strconv"
	"strings"

	"annofeed/internal/conf"

	"github.com/seqsense/pcgol/pc"
)

// Note: some code is copied from https://github.com/seqsense/pcgol/blob/master/pc/io.go

type PCDFormat string

const (
	PCDFormatAscii            PCDFormat = "ascii"
	PCDFormatBinary           PCDFormat = "binary"
	PCDFormatBinaryCompressed PCDFormat = "binary_compressed"
)

type PointCloudHeader struct {
	Version   float32
	Fields    []string
	Size      []int
	Type      []string
	Count     []int
	Width     int
	Height    int
	Viewpoint []float32
	Points    int
	Format    PCDFormat
	Bytes     int // the size of the read content while parsing a pcd file to get header
}

// PointSize returns the size of a single point in bytes.
func (o *PointCloudHeader) PointSize() int {
	pointSize := 0
	for i, size := range o.Size {
		pointSize += size * o.Count[i]
	}
	return pointSize
}

func (o *PointCloudHeader) String() string {
	intToStringSlice := func(d []int) []string {
		var ret []string
		for _, v := range d {
			ret = append(ret, strconv.Itoa(v))
		}
		return ret
	}
	floatToStringSlice := func(d []float32) []string {
		var ret []string
		for _, v := range d {
			ret = append(ret, strconv.FormatFloat(float64(v), 'f', 4, 32))
		}
		return ret
	}

	return fmt.Sprintf(
		`VERSION %0.1f
FIELDS %s
SIZE %s
TYPE %s
COUNT %s
WIDTH %d
HEIGHT %d
VIEWPOINT %s
POINTS %d
DATA binary
`,
		o.Version,
		strings.Join(o.Fields, " "),
		strings.Join(intToStringSlice(o.Size), " "),
		strings.Join(o.Type, " "),
		strings.Join(intToStringSlice(o.Count), " "),
		o.Width,
		o.Height,
		strings.Join(floatToStringSlice(o.Viewpoint), " "),
		o.Points,
	)
}

func (o *PointCloudHeader) Duplicate() PointCloudHeader {
	return *o
}

// ParsePCDHeader parses a reader to get pcd header.
// https://pointclouds.org/documentation/tutorials/pcd_file_format.html
// pcd file format requires that the header entries must be specified precisely in the following order
func ParsePCDHeader(reader *bufio.Reader) (*PointCloudHeader, error) {
	header := &PointCloudHeader{}

HEADER:
	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			return nil, err
		}
		header.Bytes += len([]byte(line))

		args := strings.Fields(strings.TrimSpace(line))
		if len(args) < 2 {
			return nil, fmt.Errorf("header field must have value")
		}
		switch args[0] {
		case "VERSION":
			f, err := strconv.ParseFloat(args[1], 32)
			if err != nil {
				return nil, err
			}
			header.Version = float32(f)
		case "FIELDS":
			header.Fields = args[1:]
		case "SIZE":
			header.Size = make([]int, len(args)-1)
			for i, s := range args[1:] {
				header.Size[i], err = strconv.Atoi(s)
				if err != nil {
					return nil, err
				}
			}
		case "TYPE":
			header.Type = args[1:]
		case "COUNT":
			header.Count = make([]int, len(args)-1)
			for i, s := range args[1:] {
				header.Count[i], err = strconv.Atoi(s)
				if err != nil {
					return nil, err
				}
			}
		case "WIDTH":
			header.Width, err = strconv.Atoi(args[1])
			if err != nil {
				return nil, err
			}
		case "HEIGHT":
			header.Height, err = strconv.Atoi(args[1])
			if err != nil {
				return nil, err
			}
		case "VIEWPOINT":
			header.Viewpoint = make([]float32, len(args)-1)
			for i, s := range args[1:] {
				f, err := strconv.ParseFloat(s, 32)
				if err != nil {
					return nil, err
				}
				header.Viewpoint[i] = float32(f)
			}
		case "POINTS":
			header.Points, err = strconv.Atoi(args[1])
			if err != nil {
				return nil, err
			}
		case "DATA":
			switch args[1] {
			case "ascii":
				header.Format = PCDFormatAscii
			case "binary":
				header.Format = PCDFormatBinary
			case "binary_compressed":
				header.Format = PCDFormatBinaryCompressed
			default:
				return nil, fmt.Errorf("unknown data format")
			}
			break HEADER
		}
	}
	// validate
	if header.Version == 0 {
		return nil, fmt.Errorf("header version must be specified")
	}
	if len(header.Fields) != len(header.Size) {
		return nil, fmt.Errorf("size field size is wrong")
	}
	if len(header.Fields) != len(header.Type) {
		return nil, fmt.Errorf("type field size is wrong")
	}
	if len(header.Fields) != len(header.Count) {
		return nil, fmt.Errorf("count field size is wrong")
	}
	return header, nil
}

// TransformPCDToBinary transforms a PCD file to binary format.
func TransformPCDToBinary(pcdPath string) error {
	fr, err := os.Open(pcdPath)
	if err != nil {
		return err
	}
	defer fr.Close()

	header, err := ParsePCDHeader(bufio.NewReader(fr))
	if err != nil {
		return fmt.Errorf("invalid pcd format: %w", err)
	}
	if header.Format == PCDFormatBinary {
		return nil
	}

	if _, err := fr.Seek(0, io.SeekStart); err != nil {
		return err
	}

	tempPath := pcdPath + ".temp"
	fw, err := os.Create(tempPath)
	if err != nil {
		return err
	}
	defer fw.Close()

	if err := transformPCDToBinary(fr, fw, header.Format); err != nil {
		return err
	}
	if err := os.Rename(tempPath, pcdPath); err != nil {
		return err
	}
	return nil
}

func transformPCDToBinary(fr io.Reader, fw io.Writer, format PCDFormat) error {
	switch format {
	case PCDFormatAscii:
		return transformAsciiPCD(fr, fw)
	case PCDFormatBinaryCompressed:
		pointCloud, err := pc.Unmarshal(fr)
		if err != nil {
			return fmt.Errorf("failed to unmarshal pcd: %w", err)
		}

		return pc.Marshal(pointCloud, fw)
	}
	return fmt.Errorf("unexpected pcd format: %s", format)
}

// transformAsciiPCD transforms a PCD file in ascii format to binary format.
func transformAsciiPCD(fr io.Reader, fw io.Writer) error {
	reader := bufio.NewReader(fr)
	pp, err := ParsePCDHeader(reader)
	if err != nil {
		return err
	}
	// Set a default value to Viewpoint if it was not provided.
	// Viewpoint is optional for all computation so it might not be always filled in
	// when manually creating PointCloud object but it is required by pcl_viewer and
	// pcdeditor to successfully load a pcd file.
	if len(pp.Viewpoint) == 0 {
		pp.Viewpoint = []float32{0, 0, 0, 1, 0, 0, 0}
	}
	header := pp.String()
	if _, err := fw.Write([]byte(header)); err != nil {
		return err
	}

	// parse data
	const batch = 8192
	lines := make([]string, batch)
	for i := 0; ; {
		count := 0
		for i < pp.Points+1 {
			i++

			line, _, err := reader.ReadLine()
			if err == io.EOF { // will not touch this line since we only read the lines of the quantity same as pp.Points
				break
			}
			if err != nil {
				return err
			}

			lines[count] = string(line)
			count++
			if count >= batch { // reach batch limit
				break
			}
		}

		if count == 0 {
			break
		}
		binaryData, err := transform(lines[:count], pp)
		if err != nil {
			return err
		}
		if _, err := fw.Write(binaryData); err != nil {
			return err
		}
	}

	return nil
}

func transform(lines []string, pp *PointCloudHeader) ([]byte, error) {
	binaryData := make([]byte, 0, len(lines)*len(pp.Fields)*4) // estimated binary data size
	for _, line := range lines {
		pointData := strings.Fields(line)
		lineOffset := 0
		for i, f := range pp.Type {
			size := pp.Size[i]
			for j := 0; j < pp.Count[i]; j++ {
				bytes := make([]byte, size)
				switch f {
				case "F":
					v, err := strconv.ParseFloat(pointData[lineOffset+j], 64)
					if err != nil {
						return nil, err
					}
					switch size {
					case 4:
						b := math.Float32bits(float32(v))
						binary.LittleEndian.PutUint32(bytes, b)
					case 8:
						b := math.Float64bits(v)
						binary.LittleEndian.PutUint64(bytes, b)
					default:
						return nil, fmt.Errorf("unexpected float size: %d", size)
					}
				case "U":
					v, err := strconv.ParseUint(pointData[lineOffset+j], 10, 32)
					if err != nil {
						return nil, err
					}
					switch size {
					case 1:
						bytes[0] = byte(v)
					case 2:
						binary.LittleEndian.PutUint16(bytes, uint16(v))
					case 4:
						binary.LittleEndian.PutUint32(bytes, uint32(v))
					default:
						return nil, fmt.Errorf("unexpected unsigned size: %d", size)
					}
				case "I":
					v, err := strconv.ParseInt(pointData[lineOffset+j], 10, 32)
					if err != nil {
						return nil, err
					}
					switch size {
					case 1:
						bytes[0] = byte(v)
					case 2:
						binary.LittleEndian.PutUint16(bytes, uint16(v))
					case 4:
						binary.LittleEndian.PutUint32(bytes, uint32(v))
					default:
						return nil, fmt.Errorf("unexpected signed size: %d", size)
					}
				default:
					return nil, fmt.Errorf("unexpected type: %s", f)
				}
				binaryData = append(binaryData, bytes[:]...)
			}
			lineOffset += pp.Count[i]
		}
	}
	return binaryData, nil
}

const (
	defaultSplitThreshold = 1000_0000 // 1000W points
	defaultMaxSegments    = 100       // working with defaultSplitThreshold can support at max 10_0000_0000 points
	thresholdFactor       = 2
)

func getSplitThreshold() int {
	v := conf.GetPcdSplitThreshold()
	if v < 0 {
		return 0
	} else if v == 0 {
		return defaultSplitThreshold
	}
	return v * 100_0000
}

func getMaxSegments() int {
	maxSegments := conf.GetPcdSplitMaxSegments()
	if maxSegments <= 0 || maxSegments > defaultMaxSegments {
		maxSegments = defaultMaxSegments
	}
	return maxSegments
}

func calSegments(points int, splitThreshold int) int {
	if points == 0 {
		return 0
	}

	count := points / splitThreshold
	remainder := points % splitThreshold
	if remainder < splitThreshold/2 {
		if count == 0 {
			count = 1
		}
		return count
	}
	return count + 1
}

// reducesSegments makes sure that the count of segments is not more than the preset max segments.
func reduceSegments(totalPoints, threshold, segmentCnt int) (newThreshold, newSegmentCnt int) {
	maxSegments := getMaxSegments()
	for segmentCnt > maxSegments {
		threshold *= thresholdFactor
		segmentCnt = calSegments(totalPoints, threshold)
	}
	return threshold, segmentCnt
}

type SplitResult struct {
	Path   string
	Size   int
	Points int
}

// SplitPCD splits a PCD file into a few segments, one of which is valid pcd file.
func SplitPCD(fpath string) ([]*SplitResult, error) {
	file, err := os.Open(fpath)
	if err != nil {
		return nil, fmt.Errorf("failed to open pcd file :%w", err)
	}
	defer file.Close()

	header, err := ParsePCDHeader(bufio.NewReader(file))
	if err != nil {
		return nil, fmt.Errorf("failed to parse header: %w", err)
	}
	splitThreshold := getSplitThreshold()
	if splitThreshold == 0 || header.Points < splitThreshold {
		return nil, nil
	}

	totalPoints := header.Points
	segmentCnt := calSegments(totalPoints, splitThreshold)
	splitThreshold, segmentCnt = reduceSegments(totalPoints, splitThreshold, segmentCnt)
	if segmentCnt == 1 {
		return nil, nil
	}

	if _, err := file.Seek(int64(header.Bytes), io.SeekStart); err != nil {
		return nil, fmt.Errorf("failed to seek file: %w", err)
	}

	const batchPoints = 512
	results := make([]*SplitResult, segmentCnt)
	pointSize := header.PointSize()
	buffer := make([]byte, pointSize*batchPoints)
	batchCnt := splitThreshold / batchPoints
	points := batchPoints * batchCnt
outer:
	for seg := 0; seg < segmentCnt; seg++ {
		childPCD := fmt.Sprintf("%s-%d.pcd", fpath, seg+1)
		fw, err := os.Create(childPCD)
		if err != nil {
			return nil, err
		}
		results[seg] = &SplitResult{Path: childPCD}

		if seg == segmentCnt-1 {
			points = totalPoints
			batchCnt = (points / batchPoints) + 1 // make sure that all the data will be read
		}
		totalPoints -= points
		results[seg].Points = points
		results[seg].Size += pointSize * points // add data size
		curHeader := header.Duplicate()
		curHeader.Width = points
		curHeader.Points = points
		n, err := fw.WriteString(curHeader.String())
		if err != nil {
			return nil, err
		}
		results[seg].Size += n // add header size

		for i := 0; i < batchCnt; i++ {
			n, err := file.Read(buffer)
			if err == io.EOF {
				break outer
			}
			if err != nil {
				return nil, err
			}

			if _, err := fw.Write(buffer[:n]); err != nil {
				return nil, err
			}
		}
		fw.Close() // try to close file
	}

	return results, nil
}
