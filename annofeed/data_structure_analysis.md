# 数据结构分析文档

## 1. 解压和移动流程分析
从 `workflow/fetcher/fetch.go` 可以看到：

```plaintext
1. 创建临时目录:
/work/wf-123456/data/.tmp-xxxxx/  (os.MkdirTemp)

2. 下载并解压ZIP到临时目录:
/work/wf-123456/data/.tmp-xxxxx/
└── 35-2/                      # 解压后的原始数据
    ├── data/                  # 可能原本就有的data目录
    │   ├── 1.jpg
    │   └── lidar.pcd
    └── other_files/
```

## 2. 目录标准化流程
从 `workflow/preparedata/standardize.go` 的 `standardizeDataFormat` 函数可以看到关键逻辑：

```go
dataDir := filepath.Join(basedir, common.DataDir)  // basedir/data
dataDirExists := kfs.PathExist(dataDir)

if dataDirExists {
    // 如果data目录已存在，会把data目录下的文件移动到 data/data 目录
    // basedir/data/* -> basedir/data/data/*
}

// 然后遍历basedir下的所有文件和目录
// 如果是目录且不在StandardTopDirs中，移动到 data/
// 如果是文件，移动到 data/data/
```

## 3. 实际可能的数据格式

假设输入ZIP包格式为：
```plaintext
35-2.zip
├── data/                      # 原始数据可能就带data目录
│   ├── scene_001/
│   │   ├── images/
│   │   │   ├── front.jpg
│   │   │   └── back.jpg
│   │   └── lidar/
│   │       └── frame.pcd
│   └── scene_002/
│       └── ...
└── meta.json
```

处理后的标准格式应该是：
```plaintext
/work/wf-123456/data/         # basedir
├── data/                     # 第一层data (common.DataDir)
│   ├── data/                 # 第二层data (common.DataPrefix)
│   │   ├── scene_001/
│   │   │   ├── images/
│   │   │   │   ├── front.jpg
│   │   │   │   └── back.jpg
│   │   │   └── lidar/
│   │   │       └── frame.pcd
│   │   └── scene_002/
│   │       └── ...
│   └── meta.json            # 移动到第一层data下
└── annos/                   # 标注相关文件目录
    └── ...
```

## 4. 关键路径分析

```go
const (
    DataDir    = "data"      // 第一层data目录
    DataPrefix = "data"      // 第二层data目录
    AnnosDir   = "annos"     // 标注目录
)

// 标准顶层目录映射
StandardTopDirs = map[string]bool{
    "data":  true,
    "annos": true,
}
```

## 5. 目录层级变化

- **原有目录**:
  - `/data` - ZIP包中可能原本就有的目录
  
- **新建目录**:
  - `basedir/data` - 第一层标准data目录
  - `basedir/data/data` - 第二层存放实际数据的目录
  - `basedir/annos` - 标注相关文件目录

- **移动规则**:
  1. 如果原本就有data目录:
     - data目录下的文件 → `basedir/data/data/`
  2. 其他目录:
     - 普通目录 → `basedir/data/`
     - 普通文件 → `basedir/data/data/`
  3. 特殊目录(如annos)保持在顶层

这样看来，确实会出现两层data目录的情况，这可能是为了保持原始数据的组织结构同时又要满足标准化的要求。第一层data作为标准化的顶层目录，第二层data用于保持原始数据的组织结构。