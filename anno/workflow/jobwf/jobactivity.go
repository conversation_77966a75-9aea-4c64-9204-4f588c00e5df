package jobwf

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"

	"anno/api/client"
	"anno/internal/biz"
	"anno/workflow/common"
	"anno/workflow/lotwf"

	"gitlab.rp.konvery.work/platform/pkg/log"
)

type Activities struct {
	lotbiz *biz.LotsBiz
	jobbiz *biz.JobsBiz
	log    *log.Helper
}

func NewActivities(lotbiz *biz.LotsBiz, jobbiz *biz.JobsBiz, logger log.Logger) *Activities {
	return &Activities{
		lotbiz: lotbiz, jobbiz: jobbiz,
		log: log.NewHelper(logger),
	}
}

func (o *Activities) JobCheckAnnos(ctx context.Context, jobID int64) error {
	// TODO: check job's annotations
	return nil
}

// JobRollPhase rolls the job to next phase
func (o *Activities) JobRollPhase(ctx context.Context, jobID int64, ev *common.Event) error {
	fmt.Println("---> job roll phase: ", jobID)
	ctx = client.NewCtxUseSvcAccount(ctx)
	jlog := detailsAsJoblog(ev)
	job, err := o.jobbiz.GetByID(ctx, jobID, true)
	if err != nil {
		return fmt.Errorf("failed to query job %v: %w", jobID, err)
	}
	if job.State != biz.JobStateChecking || job.Phase != jlog.FromPhase {
		// job state has changed or been rolled back, do nothing
		return nil
	}

	sampleBits, err := o.lotbiz.GetSampleBits(ctx, job.LotID)
	if err != nil {
		return fmt.Errorf("failed to query samplebits: %w", err)
	}

	if biz.IsJobActionReject(ev.JobAction) || biz.IsJobActionRecycle(ev.JobAction) {
		return o.rollPhaseBack(ctx, ev, job, sampleBits)
	}

	act := &biz.JobAction{
		NewState: biz.JobStateTransition{
			Phase: job.Phase,
			State: job.State,
		}}
	state := &act.NewState
	spew.Dump("---> job roll phase:", state)
	fmt.Println("-------------------------")
	for {
		fmt.Println("---> before increment: ", state.Phase, "PhaseCount: ", job.Lot.PhaseCount)
		state.Phase++
		fmt.Println("---> after increment: ", state.Phase, "PhaseCount: ", job.Lot.PhaseCount)

		// check if it is the last phase
		if state.Phase > int32(job.Lot.PhaseCount) {
			fmt.Println("finished phase")
			fmt.Println("----> last phase: ", state.Phase)
			state.State = biz.JobStateFinished
			evt := common.EvtJobCompleted
			ok, err := o.jobbiz.FixJobStateAndLog(ctx, job, jlog, act, false,
				func(ctx context.Context, v any) error {
					ev := &common.Event{
						Event: evt,
						JobID: job.ID,
					}
					spew.Dump("job roll phase event: ", ev)
					err = lotwf.SignalLotWf(ctx, job.LotID, ev)
					if err != nil {
						return err
					}
					return nil
				})
			if !ok && err == nil {
				o.log.Warn(ctx, "job state changed before migration", "uid", job.GetUid(), "newState", state.State)
			}
			return err
		}
		spew.Dump("---> sampleBits: ", sampleBits)
		fmt.Println(biz.IsJobSampled(sampleBits, int(state.Phase), int(job.IdxInLot)))

		if biz.IsJobSampled(sampleBits, int(state.Phase), int(job.IdxInLot)) {
			fmt.Println("---> job sampled: ", ev.JobAction, job.IdxInLot)
			state.ExecutorUid = ""
			state.State = biz.JobStateUnstart
			// while submitting or accepting a job, reassign it to previous reviewer if possible
			toPreExecutor := ev.JobAction == biz.JobActionSubmit || ev.JobAction == biz.JobActionAccept
			fmt.Println("---> toPreExecutor: ", toPreExecutor)
			ok, err := o.jobbiz.FixJobStateAndLog(ctx, job, jlog, act, toPreExecutor)
			if !ok && err == nil {
				o.log.Warn(ctx, "job state changed before migration", "uid", job.GetUid(), "newState", state.State)
			}
			return err
		}
	}
}

func (o *Activities) rollPhaseBack(ctx context.Context, ev *common.Event, job *biz.Job, sampleBits biz.SampleBits,
) error {
	jlog := detailsAsJoblog(ev)
	act := &biz.JobAction{
		NewState: biz.JobStateTransition{
			Phase:       job.Phase,
			State:       biz.JobStateUnstart,
			Execteam:    "",
			ExecutorUid: "",
			Cause:       job.Cause,
		}}
	state := &act.NewState

	phase := state.Phase
	for phase > 1 {
		phase--
		if biz.IsJobSampled(sampleBits, int(phase), int(job.IdxInLot)) {
			state.Phase = phase
			if biz.IsJobActionReject(ev.JobAction) {
				break
			}
		}
	}

	// reassign to previous executor if the job is rejected
	ok, err := o.jobbiz.FixJobStateAndLog(ctx, job, jlog, act, biz.IsJobActionReject(ev.JobAction))
	if !ok && err == nil {
		o.log.Warn(ctx, "job state changed before migration", "uid", job.GetUid(), "newState", state.State)
	}
	return err
}

func detailsAsJoblog(ev *common.Event) *biz.Joblog {
	return common.GetDetails[biz.Joblog](ev)
}
