package signer

import (
	"context"
	"fmt"
	"net/url"
	"strings"
	"time"

	"annofeed/internal/conf"

	"gitlab.rp.konvery.work/platform/pkg/cloudfront"
	"gitlab.rp.konvery.work/platform/pkg/s3"
)

var (
	cfclis []*cfSigner
	s3cli  *s3.Client
)

type cfSigner struct {
	origin string
	cli    *cloudfront.Signer
}

func Init(fscfg *conf.FileServer) {
	cf := fscfg.Cloudfront
	if cf != nil && cf.Enabled {
		cfclis = make([]*cfSigner, 0, len(cf.Distributions))
		for _, d := range cf.Distributions {
			c := &cloudfront.SignerConfig{
				Origin:    d.Origin,
				URLPrefix: d.UrlPrefix,
				Public:    d.Public,
			}
			if !d.Public {
				c.Expires = cf.Expires.AsDuration()
				c.KeyID = cf.SignKeyId
				c.KeyPEM = cf.Sign<PERSON><PERSON>
			}
			cfcli, err := cloudfront.NewSigner(c)
			if err != nil {
				panic(err)
			}
			cfclis = append(cfclis, &cfSigner{origin: d.Origin, cli: cfcli})
		}
	} else {
		fmt.Println("init s3 client")
		s3cli = NewS3Client(fscfg)
	}
}

func NewS3Client(fscfg *conf.FileServer) *s3.Client {
	cred := s3.Credential{}
	if s3cfg := fscfg.S3; s3cfg != nil {
		cred = s3.Credential{
			AccessKey: s3cfg.AccessKey,
			SecretKey: s3cfg.SecretKey,
		}
	}
	expires := fscfg.PresignExpires.AsDuration()
	cli, err := s3.New(fscfg.Bucket, &s3.Config{
		PresignExpires: expires,
		Profile: &s3.Profile{
			Credential: cred,
		},
	})
	if err != nil {
		panic(err)
	}
	return cli
}

func SignGetURL(ctx context.Context, uri string, expires time.Duration) (*s3.PresignResult, error) {
	u, err := url.Parse(uri)
	if err != nil {
		return nil, fmt.Errorf("failed to parse uri %v: %w", uri, err)
	}
	key := u.Path
	if len(cfclis) > 0 {
		for _, cf := range cfclis {
			if strings.HasPrefix(uri, cf.origin) {
				return cf.cli.SignURL(uri, expires)
			}
		}
		return nil, fmt.Errorf("uri %v does not match any distribution", uri)
	} else {
		key = strings.TrimPrefix(key, "/")
		opt := &s3.PresignOpts{Bucket: u.Host, Expires: expires}
		return s3cli.PresignGetObj(ctx, key, opt)
	}
}
