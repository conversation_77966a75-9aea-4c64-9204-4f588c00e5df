{"noEdit": true, "task_label": "pic", "task_type": 1001, "dotLimitLine": "", "dotLimit": "", "template_name": "", "is_online": true, "per_type": false, "task_key": "", "template_key": "", "deny_reason": "", "postil_config": [{"name": "漏标", "value": 1, "status": 1}, {"name": "多标", "value": 2, "status": 1}, {"name": "错标", "value": 3, "status": 1}], "outside": 0, "is_ocr_auto_fill": 0, "is_open_camera_tag": 0, "selection_method": 0, "fuse_configure_method": 0, "merge_point_cloud": 0, "picture_quality": "75", "boxsize": [], "sub_round": [], "dataclass_list": [], "is_pre_mark_lock": 0, "is_mark_region": false, "mark_region": {"circle": [], "arc": [], "box": [], "rect": []}, "save_mark_region": {}, "properties": [{"toolName": "矩形", "toolType": "rect", "is_polygon_filled": 1, "is_open_magic": 0, "is_open_color": 1, "dotLimit": 0, "dotLimitLine": 0, "dotLimitPoint": 0, "is_dot_limit": 0, "is_dot_limit_line": 0, "is_point_dot_limit": 0, "toolColor": "#cccccc", "class": [{"ptitle": "食物容器", "pname": "foodcontainer", "color": "rgb(138, 238, 129)", "pcode": "4d4384c9", "is_instance": 0, "pre_type": false, "is_track_prop": false, "attrs": [{"ptitle": "置信度", "pname": "hard", "type": 0, "pvalue": [{"pname": "0", "ptitle": "可信", "code": "7c5592fe"}, {"pname": "1", "ptitle": "不可信", "code": "bfc9bf59"}], "default_value": ""}, {"ptitle": "菜品名称", "pname": "DishName", "type": 0, "pvalue": [{"pname": "Thai_Beef_Boat_Noodles", "ptitle": "泰式牛肉船面", "code": "d90476e5"}, {"pname": "Beef_<PERSON>_<PERSON><PERSON>_<PERSON>_Curry", "ptitle": "红咖喱牛肋肉", "code": "283dcd35"}, {"pname": "Homemade_Chicken_Pandan", "ptitle": "班兰香叶包鸡", "code": "e1c46eab"}, {"pname": "Grilled_Pork_Neck_with_PapayaSalad", "ptitle": "烧猪颈肉配青木瓜沙律", "code": "a39ca83b"}, {"pname": "Signature_NortheasternAppetizer_Platter", "ptitle": "泰式东北小食拼盘", "code": "35c62610"}, {"pname": "Seafood_lom_Yum_with_Fish_Maw", "ptitle": "花胶海鲜冬阴功", "code": "1fc151b4"}, {"pname": "Pineapple_Fried_Rice_withSeafood_Pork_Floss", "ptitle": "菠萝肉松海鲜炒饭", "code": "7b0babb5"}, {"pname": "Soft_Shell_CrabYellow_Curry", "ptitle": "黄咖喱软壳蟹", "code": "76aa4fe1"}, {"pname": "<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>with_Coconut_Milk", "ptitle": "椰香奶皇流心汤丸", "code": "68428454"}, {"pname": "Prawn_Carpaccio", "ptitle": "泰式生虾", "code": "148aa639"}, {"pname": "Shrimp_Cakes", "ptitle": "香脆虾饼", "code": "f17ba3fd"}, {"pname": "Thai_Fish_Cakes", "ptitle": "泰式鱼饼", "code": "0538c1e0"}, {"pname": "Northeastern_Crispy_Soft_<PERSON>_Crabwith_Spicy_Thai_Dressing", "ptitle": "泰式东北软壳蟹", "code": "71b09c77"}, {"pname": "Mango_Soft_ShellCrab_Rice_Paper_Rollswith_Thai_Spicy_<PERSON>_<PERSON>uce", "ptitle": "芒果软壳蟹米纸卷", "code": "45aa7409"}, {"pname": "Nara_SignatureAppetizer_Platter", "ptitle": "泰式小食拼盘", "code": "de50cb16"}, {"pname": "BBQ_Pork_Satay", "ptitle": "黄姜猪肉沙嗲", "code": "e94b3d27"}, {"pname": "Deep_fried_Pork_Neck", "ptitle": "香脆芝麻猪颈肉", "code": "4991099d"}, {"pname": "<PERSON><PERSON>_<PERSON><PERSON>_with_<PERSON><PERSON><PERSON><PERSON><PERSON>_Lettuce", "ptitle": "肉碎米粉生菜包", "code": "5aeab154"}, {"pname": "<PERSON><PERSON><PERSON>_Chicken_Wings", "ptitle": "香脆鱼露鸡翼", "code": "a3958440"}, {"pname": "BBQ_Chicken_Satay", "ptitle": "黄姜鸡肉沙嗲", "code": "90078e34"}, {"pname": "Pork_<PERSON>ami_Rice_Paper_Rollswith_<PERSON><PERSON>_Spicy_<PERSON>_<PERSON>uce", "ptitle": "扎肉米纸卷", "code": "c4714bf8"}, {"pname": "<PERSON><PERSON>_Sal<PERSON>_with<PERSON><PERSON>ed_Egg", "ptitle": "咸蛋青木瓜沙律", "code": "e88851ac"}, {"pname": "Pomelo_Saladwith_Prawns", "ptitle": "大虾柚子沙律", "code": "f2949f3f"}, {"pname": "<PERSON>_<PERSON><PERSON>_Saladwith_<PERSON><PERSON><PERSON>_Pork_Salami", "ptitle": "青芒扎肉沙律", "code": "8b7a6ce3"}, {"pname": "Seafood_Minced_Porkwith_Glass_Vermicelli_Salad", "ptitle": "海鲜肉碎粉丝沙律", "code": "19d3343f"}, {"pname": "Coconut_Chicken_Soup", "ptitle": "泰式椰子鸡汤", "code": "ca997fd1"}, {"pname": "<PERSON><PERSON>_<PERSON><PERSON>_with_<PERSON><PERSON><PERSON><PERSON>__<PERSON>_Vermicelli_Soup", "ptitle": "肉碎玉子豆腐粉丝汤", "code": "5d4a779f"}, {"pname": "Tiger_Prawns_Yellow_Curry", "ptitle": "黄咖喱大虾", "code": "4bdf7133"}, {"pname": "Tiger_PrawnsRed_Curry", "ptitle": "红咖喱大虾", "code": "cd35ee75"}, {"pname": "<PERSON>_<PERSON>llet_Yellow_Curry", "ptitle": "黄咖喱鱼柳", "code": "4e74b607"}, {"pname": "<PERSON>_<PERSON>_Curry", "ptitle": "青咖喱鸡", "code": "af8d101e"}, {"pname": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>", "ptitle": "马莎文咖喱牛腱", "code": "a4bd9fda"}, {"pname": "Grilled_Chicken_withPapaya_Salad", "ptitle": "烧鸡扒配青木瓜沙律", "code": "086c41cc"}, {"pname": "Steamed_<PERSON>_<PERSON><PERSON><PERSON><PERSON>_Spicy_Lime_Sauce", "ptitle": "酸辣青柠汁蒸乌头", "code": "6d8a61ff"}, {"pname": "Steamed_<PERSON>_<PERSON><PERSON><PERSON>_with<PERSON><PERSON>__Min<PERSON>_PorkSoup_in_Stove_Tray", "ptitle": "明炉黄梨肉碎蒸乌头", "code": "ed19615c"}, {"pname": "<PERSON><PERSON><PERSON>_<PERSON>_Bass_with<PERSON><PERSON>_Lemongrass_Garlic", "ptitle": "香茅金蒜脆鲈鱼", "code": "11f6f261"}, {"pname": "<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_Vermicelli", "ptitle": "粉丝炒肉蟹", "code": "03e96143"}, {"pname": "Stir_fried_<PERSON>_Prawnswith_Salted_Egg_Curry", "ptitle": "咸蛋咖喱炒大虾", "code": "574aff2a"}, {"pname": "Stir_fried_Tiger_Prawns_withGarlic_Peppercorns", "ptitle": "蒜香胡椒炒大虾", "code": "f941ef08"}, {"pname": "Clams_with_Fresh_YoungCoconut_Stew", "ptitle": "泰式椰子蚬煲", "code": "e94efd47"}, {"pname": "Fried_Rice_Noodleswith_Pork", "ptitle": "泰式猪肉炒河粉", "code": "3c8ff193"}, {"pname": "Fried_Rice_Noodleswith_Thai_Shrimp_Paste", "ptitle": "泰式虾酱炒河粉", "code": "5da70401"}, {"pname": "Braised_Rice_Noodleswith_Pork", "ptitle": "湿炒猪肉河粉", "code": "7915d334"}, {"pname": "Fried_Egg_Noodleswith_Crab_Meat", "ptitle": "蟹肉炒面", "code": "00f02166"}, {"pname": "<PERSON><PERSON>_<PERSON>_<PERSON>", "ptitle": "nara_特色炒饭", "code": "91388e48"}, {"pname": "<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_Rice_with<PERSON><PERSON><PERSON>_<PERSON>_Thai_Herbs", "ptitle": "泰式香草脆鱼炒蝶豆花饭", "code": "0048a390"}, {"pname": "Fried_Rice_with_<PERSON><PERSON>_Meat", "ptitle": "蟹肉炒饭", "code": "0051bc82"}, {"pname": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_with_<PERSON>_<PERSON>", "ptitle": "泰式肉碎煎蛋饭", "code": "ffbcf4bb"}, {"pname": "Vegetarian_Spring_Rolls", "ptitle": "泰式脆春卷", "code": "785adf93"}, {"pname": "<PERSON><PERSON>_OmniPork_Saladwith_Rice_Vermicelli_Lettuce", "ptitle": "够Pork肉碎米粉生菜包", "code": "0d3eb4fa"}, {"pname": "<PERSON><PERSON>_<PERSON>", "ptitle": "青木瓜沙律", "code": "933b6198"}, {"pname": "OmniPork_Balls_with_EggTofu__Glass_Vermicelli_Soup", "ptitle": "膳良肉丸玉子豆腐粉丝汤", "code": "60ad82e6"}, {"pname": "Mixed_VegetablesCoconut_Soup", "ptitle": "杂菜椰子汤", "code": "11ab281f"}, {"pname": "Stir_fried_OmniPorkwith_Eggplanf", "ptitle": "味来肉碎炒茄子", "code": "8d367b64"}, {"pname": "Stir_fried_OmniPork_Tofu_Puff", "ptitle": "辛猪肉碎炒豆卜", "code": "8c0b014b"}, {"pname": "Pumpkin_fofu_PuffRed_Curry", "ptitle": "红咖喱南瓜豆卜", "code": "3232a2fc"}, {"pname": "Mixed_VegetablesYellow_Curry", "ptitle": "黄咖喱杂菜", "code": "c96152b0"}, {"pname": "Sweet_SourVegetarian_Chicken", "ptitle": "泰式酸甜炒素鸡", "code": "e23c5085"}, {"pname": "Min<PERSON>_OmniPork_FriedEgg_with_<PERSON>_<PERSON>", "ptitle": "抛猪引肉碎煎蛋饭", "code": "2d4c4c8c"}, {"pname": "Phad_Thai_withMixed_Vegetables", "ptitle": "泰式杂菜炒金边粉", "code": "947c00c2"}, {"pname": "Stir_fried_Morning_Glory", "ptitle": "泰式炒通菜", "code": "15ea08de"}, {"pname": "Stir_fried_Baby_Cabbagewith_Garlic", "ptitle": "香蒜炒椰菜苗", "code": "159ffa5a"}, {"pname": "tir_fried_<PERSON><PERSON>_with<PERSON><PERSON><PERSON>_<PERSON>", "ptitle": "泰式咸鱼芥兰", "code": "ad426319"}, {"pname": "Stir_fried_Seasonal_MixedVegetables_with_<PERSON><PERSON>lic", "ptitle": "香蒜炒杂菜", "code": "f03f0a00"}, {"pname": "Stir_<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>", "ptitle": "香蒜炒西兰花", "code": "5cf1a715"}, {"pname": "Stir_fried_Sweet_PotatoSprouts_with_<PERSON><PERSON><PERSON>", "ptitle": "香蒜炒蕃薯苗", "code": "474b8991"}, {"pname": "Roti_Prata", "ptitle": "印度薄饼", "code": "bbfbcb87"}, {"pname": "<PERSON>_<PERSON><PERSON>_Rice", "ptitle": "蝶豆花饭", "code": "ec7271a4"}, {"pname": "<PERSON>_Rice", "ptitle": "白饭", "code": "646617b1"}, {"pname": "<PERSON><PERSON>_<PERSON>y_Ricewith_Coconut_Milk", "ptitle": "芒果糯米饭伴椰奶", "code": "143ad290"}, {"pname": "Thai_Dessert_Platter", "ptitle": "泰式糕点拼盘", "code": "82b764bd"}, {"pname": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>", "ptitle": "榴莲雪糕伴榴莲糯米饭", "code": "0d406b02"}, {"pname": "other", "ptitle": "其他", "code": "87092d57"}, {"pname": "Phad_Thai_with_Prawns", "ptitle": "泰式炒金边粉", "code": "49ffaf14"}], "default_value": ""}]}, {"ptitle": "食物", "pname": "food", "color": "rgb(138, 238, 129)", "pcode": "03fd5d9e", "is_instance": 0, "pre_type": false, "is_track_prop": false, "attrs": [{"ptitle": "置信度", "pname": "hard", "type": 0, "pvalue": [{"pname": "0", "ptitle": "可信", "code": "7c5592fe"}, {"pname": "1", "ptitle": "不可信", "code": "bfc9bf59"}], "default_value": ""}, {"ptitle": "菜品名称", "pname": "DishName", "type": 0, "pvalue": [{"pname": "Thai_Beef_Boat_Noodles", "ptitle": "泰式牛肉船面", "code": "d90476e5"}, {"pname": "Beef_<PERSON>_<PERSON><PERSON>_<PERSON>_Curry", "ptitle": "红咖喱牛肋肉", "code": "283dcd35"}, {"pname": "Homemade_Chicken_Pandan", "ptitle": "班兰香叶包鸡", "code": "e1c46eab"}, {"pname": "Grilled_Pork_Neck_with_PapayaSalad", "ptitle": "烧猪颈肉配青木瓜沙律", "code": "a39ca83b"}, {"pname": "Signature_NortheasternAppetizer_Platter", "ptitle": "泰式东北小食拼盘", "code": "35c62610"}, {"pname": "Seafood_lom_Yum_with_Fish_Maw", "ptitle": "花胶海鲜冬阴功", "code": "1fc151b4"}, {"pname": "Pineapple_Fried_Rice_withSeafood_Pork_Floss", "ptitle": "菠萝肉松海鲜炒饭", "code": "7b0babb5"}, {"pname": "Soft_Shell_CrabYellow_Curry", "ptitle": "黄咖喱软壳蟹", "code": "76aa4fe1"}, {"pname": "<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>with_Coconut_Milk", "ptitle": "椰香奶皇流心汤丸", "code": "68428454"}, {"pname": "Prawn_Carpaccio", "ptitle": "泰式生虾", "code": "148aa639"}, {"pname": "Shrimp_Cakes", "ptitle": "香脆虾饼", "code": "f17ba3fd"}, {"pname": "Thai_Fish_Cakes", "ptitle": "泰式鱼饼", "code": "0538c1e0"}, {"pname": "Northeastern_Crispy_Soft_<PERSON>_Crabwith_Spicy_Thai_Dressing", "ptitle": "泰式东北软壳蟹", "code": "71b09c77"}, {"pname": "Mango_Soft_ShellCrab_Rice_Paper_Rollswith_Thai_Spicy_<PERSON>_<PERSON>uce", "ptitle": "芒果软壳蟹米纸卷", "code": "45aa7409"}, {"pname": "Nara_SignatureAppetizer_Platter", "ptitle": "泰式小食拼盘", "code": "de50cb16"}, {"pname": "BBQ_Pork_Satay", "ptitle": "黄姜猪肉沙嗲", "code": "e94b3d27"}, {"pname": "Deep_fried_Pork_Neck", "ptitle": "香脆芝麻猪颈肉", "code": "4991099d"}, {"pname": "<PERSON><PERSON>_<PERSON><PERSON>_with_<PERSON><PERSON><PERSON><PERSON><PERSON>_Lettuce", "ptitle": "肉碎米粉生菜包", "code": "5aeab154"}, {"pname": "<PERSON><PERSON><PERSON>_Chicken_Wings", "ptitle": "香脆鱼露鸡翼", "code": "a3958440"}, {"pname": "BBQ_Chicken_Satay", "ptitle": "黄姜鸡肉沙嗲", "code": "90078e34"}, {"pname": "Pork_<PERSON>ami_Rice_Paper_Rollswith_<PERSON><PERSON>_Spicy_<PERSON>_<PERSON>uce", "ptitle": "扎肉米纸卷", "code": "c4714bf8"}, {"pname": "<PERSON><PERSON>_Sal<PERSON>_with<PERSON><PERSON>ed_Egg", "ptitle": "咸蛋青木瓜沙律", "code": "e88851ac"}, {"pname": "Pomelo_Saladwith_Prawns", "ptitle": "大虾柚子沙律", "code": "f2949f3f"}, {"pname": "<PERSON>_<PERSON><PERSON>_Saladwith_<PERSON><PERSON><PERSON>_Pork_Salami", "ptitle": "青芒扎肉沙律", "code": "8b7a6ce3"}, {"pname": "Seafood_Minced_Porkwith_Glass_Vermicelli_Salad", "ptitle": "海鲜肉碎粉丝沙律", "code": "19d3343f"}, {"pname": "Coconut_Chicken_Soup", "ptitle": "泰式椰子鸡汤", "code": "ca997fd1"}, {"pname": "<PERSON><PERSON>_<PERSON><PERSON>_with_<PERSON><PERSON><PERSON><PERSON>__<PERSON>_Vermicelli_Soup", "ptitle": "肉碎玉子豆腐粉丝汤", "code": "5d4a779f"}, {"pname": "Tiger_Prawns_Yellow_Curry", "ptitle": "黄咖喱大虾", "code": "4bdf7133"}, {"pname": "Tiger_PrawnsRed_Curry", "ptitle": "红咖喱大虾", "code": "cd35ee75"}, {"pname": "<PERSON>_<PERSON>llet_Yellow_Curry", "ptitle": "黄咖喱鱼柳", "code": "4e74b607"}, {"pname": "<PERSON>_<PERSON>_Curry", "ptitle": "青咖喱鸡", "code": "af8d101e"}, {"pname": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>", "ptitle": "马莎文咖喱牛腱", "code": "a4bd9fda"}, {"pname": "Grilled_Chicken_withPapaya_Salad", "ptitle": "烧鸡扒配青木瓜沙律", "code": "086c41cc"}, {"pname": "Steamed_<PERSON>_<PERSON><PERSON><PERSON><PERSON>_Spicy_Lime_Sauce", "ptitle": "酸辣青柠汁蒸乌头", "code": "6d8a61ff"}, {"pname": "Steamed_<PERSON>_<PERSON><PERSON><PERSON>_with<PERSON><PERSON>__Min<PERSON>_PorkSoup_in_Stove_Tray", "ptitle": "明炉黄梨肉碎蒸乌头", "code": "ed19615c"}, {"pname": "<PERSON><PERSON><PERSON>_<PERSON>_Bass_with<PERSON><PERSON>_Lemongrass_Garlic", "ptitle": "香茅金蒜脆鲈鱼", "code": "11f6f261"}, {"pname": "<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_Vermicelli", "ptitle": "粉丝炒肉蟹", "code": "03e96143"}, {"pname": "Stir_fried_<PERSON>_Prawnswith_Salted_Egg_Curry", "ptitle": "咸蛋咖喱炒大虾", "code": "574aff2a"}, {"pname": "Stir_fried_Tiger_Prawns_withGarlic_Peppercorns", "ptitle": "蒜香胡椒炒大虾", "code": "f941ef08"}, {"pname": "Clams_with_Fresh_YoungCoconut_Stew", "ptitle": "泰式椰子蚬煲", "code": "e94efd47"}, {"pname": "Fried_Rice_Noodleswith_Pork", "ptitle": "泰式猪肉炒河粉", "code": "3c8ff193"}, {"pname": "Fried_Rice_Noodleswith_Thai_Shrimp_Paste", "ptitle": "泰式虾酱炒河粉", "code": "5da70401"}, {"pname": "Braised_Rice_Noodleswith_Pork", "ptitle": "湿炒猪肉河粉", "code": "7915d334"}, {"pname": "Fried_Egg_Noodleswith_Crab_Meat", "ptitle": "蟹肉炒面", "code": "00f02166"}, {"pname": "<PERSON><PERSON>_<PERSON>_<PERSON>", "ptitle": "nara_特色炒饭", "code": "91388e48"}, {"pname": "<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_Rice_with<PERSON><PERSON><PERSON>_<PERSON>_Thai_Herbs", "ptitle": "泰式香草脆鱼炒蝶豆花饭", "code": "0048a390"}, {"pname": "Fried_Rice_with_<PERSON><PERSON>_Meat", "ptitle": "蟹肉炒饭", "code": "0051bc82"}, {"pname": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_with_<PERSON>_<PERSON>", "ptitle": "泰式肉碎煎蛋饭", "code": "ffbcf4bb"}, {"pname": "Vegetarian_Spring_Rolls", "ptitle": "泰式脆春卷", "code": "785adf93"}, {"pname": "<PERSON><PERSON>_OmniPork_Saladwith_Rice_Vermicelli_Lettuce", "ptitle": "够Pork肉碎米粉生菜包", "code": "0d3eb4fa"}, {"pname": "<PERSON><PERSON>_<PERSON>", "ptitle": "青木瓜沙律", "code": "933b6198"}, {"pname": "OmniPork_Balls_with_EggTofu__Glass_Vermicelli_Soup", "ptitle": "膳良肉丸玉子豆腐粉丝汤", "code": "60ad82e6"}, {"pname": "Mixed_VegetablesCoconut_Soup", "ptitle": "杂菜椰子汤", "code": "11ab281f"}, {"pname": "Stir_fried_OmniPorkwith_Eggplanf", "ptitle": "味来肉碎炒茄子", "code": "8d367b64"}, {"pname": "Stir_fried_OmniPork_Tofu_Puff", "ptitle": "辛猪肉碎炒豆卜", "code": "8c0b014b"}, {"pname": "Pumpkin_fofu_PuffRed_Curry", "ptitle": "红咖喱南瓜豆卜", "code": "3232a2fc"}, {"pname": "Mixed_VegetablesYellow_Curry", "ptitle": "黄咖喱杂菜", "code": "c96152b0"}, {"pname": "Sweet_SourVegetarian_Chicken", "ptitle": "泰式酸甜炒素鸡", "code": "e23c5085"}, {"pname": "Min<PERSON>_OmniPork_FriedEgg_with_<PERSON>_<PERSON>", "ptitle": "抛猪引肉碎煎蛋饭", "code": "2d4c4c8c"}, {"pname": "Phad_Thai_withMixed_Vegetables", "ptitle": "泰式杂菜炒金边粉", "code": "947c00c2"}, {"pname": "Stir_fried_Morning_Glory", "ptitle": "泰式炒通菜", "code": "15ea08de"}, {"pname": "Stir_fried_Baby_Cabbagewith_Garlic", "ptitle": "香蒜炒椰菜苗", "code": "159ffa5a"}, {"pname": "tir_fried_<PERSON><PERSON>_with<PERSON><PERSON><PERSON>_<PERSON>", "ptitle": "泰式咸鱼芥兰", "code": "ad426319"}, {"pname": "Stir_fried_Seasonal_MixedVegetables_with_<PERSON><PERSON>lic", "ptitle": "香蒜炒杂菜", "code": "f03f0a00"}, {"pname": "Stir_<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>", "ptitle": "香蒜炒西兰花", "code": "5cf1a715"}, {"pname": "Stir_fried_Sweet_PotatoSprouts_with_<PERSON><PERSON><PERSON>", "ptitle": "香蒜炒蕃薯苗", "code": "474b8991"}, {"pname": "Roti_Prata", "ptitle": "印度薄饼", "code": "bbfbcb87"}, {"pname": "<PERSON>_<PERSON><PERSON>_Rice", "ptitle": "蝶豆花饭", "code": "ec7271a4"}, {"pname": "<PERSON>_Rice", "ptitle": "白饭", "code": "646617b1"}, {"pname": "<PERSON><PERSON>_<PERSON>y_Ricewith_Coconut_Milk", "ptitle": "芒果糯米饭伴椰奶", "code": "143ad290"}, {"pname": "Thai_Dessert_Platter", "ptitle": "泰式糕点拼盘", "code": "82b764bd"}, {"pname": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>", "ptitle": "榴莲雪糕伴榴莲糯米饭", "code": "0d406b02"}, {"pname": "other", "ptitle": "其他", "code": "87092d57"}, {"pname": "Phad_Thai_with_Prawns", "ptitle": "泰式炒金边粉", "code": "49ffaf14"}], "default_value": ""}]}, {"ptitle": "托盘", "pname": "tray", "color": "rgb(138, 238, 129)", "pcode": "ca9b23ed", "is_instance": 0, "pre_type": false, "is_track_prop": false, "attrs": [{"ptitle": "置信度", "pname": "hard", "type": 0, "pvalue": [{"pname": "0", "ptitle": "可信", "code": "7c5592fe"}, {"pname": "1", "ptitle": "不可信", "code": "bfc9bf59"}], "default_value": ""}]}], "attrs": [], "amount": 1, "isIntro": false, "is_check_all": 0, "is_open_rect_rotate": 0, "fast_mark_edit_size": 0}], "attributeSeparation": 0, "properties2D": [], "global": {"attrs": [], "class": []}, "price": {"channel_price": {}, "customer_price": {}}, "train_desc": "", "train_num": "", "train_max_num": "", "train_rate": "", "package_type": 1, "submit_at": "", "update_time": "", "check_limit": "", "rotate_picture_export_type": 1, "is_train": 0, "fast_mark": 0, "is_body_orientation": 0, "is_rotate_picture": 0, "is_point": 0, "point_desc": "", "coco_desc": "", "is_ocr": 0, "is_track": 0, "is_semantic": 0, "segmentation_type": 0, "is_panoramic": 0, "is_cover": 0, "is_class_type": 1, "boxsize3d": [], "group_type": 0, "assist": {}, "is_vsample_interval": 0, "vsample_interval": "", "isCutting": 0, "splitChannels": 0, "minNum": "", "maxNum": "", "isConversion": 0, "conversion_type": 0, "isPinyin": 0, "task_mode": 1, "cut_type": 0, "isAudioProps": 1, "isAddTime": 0, "addTime": "", "is_prop_check": 0, "is_escape": 0, "appropriate_types": 1, "props_save": 0, "labeling_method": 0, "add_attr": 0, "modify_corpus": 0, "is_pre_labeled": 0, "pro_type": 0, "num_of_task": 1, "num_of_pack": "", "articleTitle_type": 0, "article_titleArr": [], "corpus_type": 0, "text_title": [], "ocr_content_configure": [], "camera_properties": [], "textadd": false, "add_tag": 0, "tag_type": 0, "reference_tag": 0, "min_words_length": "", "max_words_length": "", "min_num_pieces": "", "max_num_pieces": "", "regStatus": 0, "is_expression": 0, "expression_content": "", "generate_mode": 0, "cue_word": [], "check_rule_name": 0, "translate": 0, "discrete": 0, "relationship_definition": [], "events_definition": [], "attribute_definition": [], "entity_premark": 0, "entity_library_id": "", "force_submit": 0, "cleaning_type": "1", "cleaningTypeArr": [], "type_list": [], "preset_type": "", "pass_total_limit": 0, "command_params": "", "boxConsistent": 0, "is_private": 1, "project_type": {"pic": [{"task_type": 1001, "title": "图像通用标注", "imgsrc": "/images/show_1001.png", "icon": "#icontongyongbiaozhu", "is_allow": 1}, {"task_type": 1002, "title": "OCR文字转写", "imgsrc": "/images/show_1002.png", "icon": "#iconwenzizhuanxie", "is_allow": 1}, {"task_type": 1003, "title": "REID目标跟踪", "imgsrc": "/images/show_1003.png", "icon": "#iconmubiaogenzong", "is_allow": 1}, {"task_type": 1004, "title": "图像语义分割", "imgsrc": "/images/show_1004.png", "icon": "#iconyuyifenge1", "is_allow": 1}, {"task_type": 1006, "title": "人体关键点", "imgsrc": "/images/show_1006.png", "icon": "#<PERSON>rentiguanjiandian", "is_allow": 1}], "point_cloud": [{"task_type": 902, "title": "点云", "imgsrc": "/images/show_902.png", "icon": "#icondianyun2d3dronghe", "is_allow": 1}], "audio": [{"task_type": 704, "title": "音频标注", "imgsrc": "/images/show_704.png", "icon": "#iconyin<PERSON><PERSON><PERSON>hu", "is_allow": 1}], "text": [{"task_type": 1111, "title": "意图及实体标注", "imgsrc": "/images/show_1111.png", "icon": "#iconyi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_allow": 1}, {"task_type": 1202, "title": "命名实体识别与关系标注", "imgsrc": "/images/show_1202.png", "icon": "#iconmingmingshitishibieyuguanxibiaozhu", "is_allow": 1}, {"task_type": 1103, "title": "文本生成", "imgsrc": "/images/show_1103.png", "icon": "#iconwenben", "is_allow": 1}, {"task_type": 1112, "title": "文章判断", "imgsrc": "/images/show_1112.png", "icon": "#icon<PERSON>zhangpanduan", "is_allow": 1}, {"task_type": 1113, "title": "相似文本判断", "des": "给定参考语料，从一组句子中选择单条或多条相似文本", "imgsrc": "/images/show_1113.png", "icon": "#icon<PERSON>zhangpanduan", "is_allow": 1}], "video": [{"task_type": 1201, "title": "视频标注", "imgsrc": "/images/show_1201.png", "icon": "#icons<PERSON><PERSON><PERSON><PERSON><PERSON>", "is_allow": 1}]}, "issues": [{"name": "漏标", "value": 1, "status": 1}, {"name": "多标", "value": 2, "status": 1}, {"name": "错标", "value": 3, "status": 1}], "is_track_prop": false}