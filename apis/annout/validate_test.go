package annout

import (
	"testing"

	"gitlab.rp.konvery.work/platform/apis/annout/v1"
)

func Test_CreateReaperRequest(t *testing.T) {
	req := annout.CreateReaperRequest{
		LotUid: "lot12345678",
	}

	cases := []struct {
		name       string
		newReqFunc func(req annout.CreateReaperRequest) annout.CreateReaperRequest
		expectErr  bool
	}{
		{
			"happy_flow",
			nil,
			false,
		},
		{
			"LotUid_is_wrong",
			func(req annout.CreateReaperRequest) annout.CreateReaperRequest {
				req.LotUid = "wrong"
				return req
			},
			true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			newReq := req
			if c.newReqFunc != nil {
				newReq = c.newReqFunc(req)
			}
			if err := newReq.Validate(); (err != nil) != c.expectErr {
				t.<PERSON><PERSON>("Validate() expected error: %v, got: %v", c.expectErr, err)
			}
			if err := newReq.ValidateAll(); (err != nil) != c.expectErr {
				t.Errorf("ValidateAll() expected error: %v, got: %v", c.expectErr, err)
			}
		})
	}
}
