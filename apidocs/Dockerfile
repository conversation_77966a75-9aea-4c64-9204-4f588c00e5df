# build program
FROM golang:1.19-alpine AS builder

ARG VERSION
ARG GOPROXY
ARG GOPRIVATE
ENV LDFLAGS="-s"

COPY . /src
WORKDIR /src

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk add make git
RUN make build

# create image
FROM alpine:3.16

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk update && apk add --no-cache ca-certificates tzdata && update-ca-certificates

COPY --from=builder /src/bin /app
COPY --from=builder /src/tpl /app/tpl
COPY --from=builder /src/res /app/res
WORKDIR /app

EXPOSE 8080
VOLUME /app/assets

CMD ["./apidocs", "start"]
