# Keto 权限系统分析

## 1. 项目结构分析

项目基于开源项目 Keto 进行了二次开发，主要包含以下关键文件：

1. `namespaces.keto.ts` - 命名空间定义文件
2. `tuples.txt` - 权限规则文件
3. `namespaces.keto-local.ts` - 本地命名空间定义文件
4. `tuples-local.txt` - 本地权限规则文件

## 2. 权限规则定义分析

### 2.1 命名空间定义
命名空间是权限系统的基本构建块，用于定义系统中的各种实体及其关系。主要命名空间包括：

- `IamUser`: 用户实体
- `IamGroup`: 用户组
- `IamRole`: 角色
- `IamPolicy`: 策略
- `AnnoLot`: 标注批次
- `AnnoJob`: 标注任务
- `AnnoOrder`: 标注订单
- `AnnofeedData`: 标注数据
- `AnnofeedFile`: 标注文件
- `FePage`: 前端页面

### 2.2 关系类型
系统定义了多种关系类型：

- `members`: 成员关系
- `perms`: 权限关系
- `policies`: 策略关系
- `parents`: 父级关系
- `roles`: 角色关系
- `users`: 用户关系

### 2.3 规则格式
权限规则的基本格式为：`Namespace:ID#relation@Namespace:ID`

例如：
```plaintext
# 用户组关系
IamGroup:sys/admin#members@IamUser:user123

# 角色权限关系
IamRole:AnnoJob.viewer#<EMAIL>

# 策略绑定关系
IamRole:AnnoJob.viewer#policies@IamPolicy:sys/admin
```

## 3. 权限系统实现分析

### 3.1 命名空间实现
以 AnnoJob 为例，命名空间的实现如下：

```typescript
class AnnoJob implements Namespace {
  related: {
    parents: AnnoLot[]  // 父级是标注批次
    policies: IamPolicy[]  // 关联的策略
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      AnnoJob:"*".permits.check(ctx, perm) ||  // 全局权限检查
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||  // 父级权限检查
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),  // 策略权限检查

    // 具体权限方法
    get: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.get"),
    list: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.list"),
    // ... 其他权限方法
  }
}
```

### 3.2 权限规则解析
权限规则的解析过程如下：

```typescript
function parseTuple(tuple: string) {
  // 1. 分割主体和对象部分
  const [subjectPart, objectPart] = tuple.split('@');
  
  // 2. 分割主体和关系
  const [subject, relation] = subjectPart.split('#');
  
  // 3. 解析主体
  const [subjectNamespace, subjectId] = subject.split(':');
  
  // 4. 解析对象
  const [objectNamespace, objectId] = objectPart.split(':');
  
  return {
    subject: {
      namespace: subjectNamespace,
      id: subjectId
    },
    relation: relation,
    object: {
      namespace: objectNamespace,
      id: objectId
    }
  };
}
```

### 3.3 权限检查流程
权限检查的完整流程如下：

```typescript
async function checkPermission(userId: string, permission: string) {
  // 1. 获取用户所属的组
  const userGroups = await getGroups(userId);
  
  // 2. 获取组的角色
  const groupRoles = await getRoles(userGroups);
  
  // 3. 获取角色的权限
  const rolePermissions = await getPermissions(groupRoles);
  
  // 4. 检查权限
  return rolePermissions.includes(permission);
}
```

## 4. 权限继承机制分析

### 4.1 继承关系
权限继承关系示例：

```plaintext
AnnoJob.owner
  └── AnnoJob.editor
       └── AnnoJob.viewer
            └── AnnoJob.get
            └── AnnoJob.list

AnnoJob.regulator
  └── AnnoJob.assign
  └── AnnoJob.reject
  └── AnnoJob.log
```

### 4.2 权限检查顺序
权限检查按照以下顺序进行：

1. 检查直接权限
2. 检查全局权限
3. 检查父级权限
4. 检查策略权限
5. 检查所有者权限

## 5. 实际应用场景分析

### 5.1 API 权限控制
API 权限控制示例：

```typescript
async function getAnnoJob(req, res) {
  const jobId = req.params.jobId;
  const userId = req.user.id;
  
  // 创建权限检查上下文
  const ctx = new Context({
    subject: `IamUser:${userId}`
  });
  
  // 获取标注任务实例
  const annoJob = new AnnoJob(jobId);
  
  // 检查权限
  if (!await annoJob.permits.get(ctx)) {
    return res.status(403).json({ error: "Permission denied" });
  }
  
  // 获取任务数据
  const jobData = await annoJob.getData();
  return res.json(jobData);
}
```

### 5.2 权限更新
权限更新示例：

```typescript
async function updateUserRole(userId: string, role: string) {
  // 1. 创建新的权限规则
  const tuple = {
    namespace: "IamRole",
    object: role,
    relation: "users",
    subject: `IamUser:${userId}`
  };
  
  // 2. 添加到数据库
  await ketow.relationTuple.create(tuple);
}
```

## 6. 系统特点分析

### 6.1 优点
1. 清晰的结构：使用 `@` 和 `#` 分隔符使规则结构清晰
2. 灵活的继承：支持多级权限继承
3. 易于扩展：可以轻松添加新的关系类型
4. 可读性好：规则格式直观，易于理解
5. 便于维护：规则可以独立管理，易于修改

### 6.2 使用建议
1. 使用有意义的命名
2. 保持层次结构
3. 使用策略进行批量授权
4. 使用组进行用户管理

## 7. 注意事项

1. 关系类型不是 Keto 预定义的，而是在项目中根据需求定义的
2. 关系类型在 `namespaces.keto.ts` 中通过 TypeScript 接口定义
3. 可以根据项目需求扩展新的关系类型
4. 关系类型的使用需要遵循一定的命名和格式规范
5. 关系类型支持查询和验证操作

## 8. 总结

Keto 权限系统提供了一个灵活且强大的权限管理框架，通过命名空间和关系元组的方式，可以定义复杂的权限规则和继承关系。系统支持多级权限继承、策略绑定和实时权限检查，适合用于需要细粒度权限控制的场景。 