// Code generated by Thrift Compiler (0.14.1). DO NOT EDIT.

package agent

import (
	"bytes"
	"context"
	"fmt"
	"time"

	"go.opentelemetry.io/otel/exporters/jaeger/internal/gen-go/jaeger"
	"go.opentelemetry.io/otel/exporters/jaeger/internal/gen-go/zipkincore"
	"go.opentelemetry.io/otel/exporters/jaeger/internal/third_party/thrift/lib/go/thrift"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

var _ = jaeger.GoUnusedProtection__
var _ = zipkincore.GoUnusedProtection__

func init() {
}
