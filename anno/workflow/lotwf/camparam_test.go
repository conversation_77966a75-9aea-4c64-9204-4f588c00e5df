package lotwf

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
)

func Test_aggCamParams(t *testing.T) {
	extrinsic := &anno.RawdataParam{
		Type:      anno.RawdataParam_Type_extrinsic,
		ColumnCnt: 1,
		Data:      []float64{1, 2, 3, 4, 5, 6, 7},
	}
	extrinsicDiff := &anno.RawdataParam{
		Type:      anno.RawdataParam_Type_extrinsic,
		ColumnCnt: 1,
		Data:      []float64{1, 2, 3, 4, 5, 6, 70},
	}
	intrinsic := &anno.RawdataParam{
		Type:      anno.RawdataParam_Type_intrinsic,
		ColumnCnt: 1,
		Data:      []float64{1, 2, 3, 4},
	}
	distortion := &anno.RawdataParam{
		Type:      anno.RawdataParam_Type_distortion,
		ColumnCnt: 1,
		Data:      []float64{5, 6, 7, 8},
	}

	const frontCamera = "front"
	const backCamera = "back"
	cases := []struct {
		name             string
		elems            []*anno.Element
		expectedElems    []*anno.Element
		expectedCamParam map[string]*anno.Job_CamParam
	}{
		{
			name: "happy_flow",
			elems: []*anno.Element{
				{
					Name: "elem_1",
					Datas: []*anno.Rawdata{
						{
							Type: anno.Rawdata_Type_pointcloud,
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: frontCamera}},
							Transform: []*anno.RawdataParam{
								extrinsic,
								intrinsic,
								distortion,
							},
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: backCamera}},
							Transform: []*anno.RawdataParam{
								extrinsic,
								intrinsic,
								distortion,
							},
						},
					},
				},
				{
					Name: "elem_2",
					Datas: []*anno.Rawdata{
						{
							Type: anno.Rawdata_Type_pointcloud,
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: frontCamera}},
							Transform: []*anno.RawdataParam{
								extrinsic,
								intrinsic,
								distortion,
							},
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: backCamera}},
							Transform: []*anno.RawdataParam{
								extrinsic,
								intrinsic,
								distortion,
							},
						},
					},
				},
			},
			expectedElems: []*anno.Element{
				{
					Name: "elem_1",
					Datas: []*anno.Rawdata{
						{
							Type: anno.Rawdata_Type_pointcloud,
						},
						{
							Type:      anno.Rawdata_Type_image,
							Meta:      &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: frontCamera}},
							Transform: nil,
						},
						{
							Type:      anno.Rawdata_Type_image,
							Meta:      &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: backCamera}},
							Transform: nil,
						},
					},
				},
				{
					Name: "elem_2",
					Datas: []*anno.Rawdata{
						{
							Type: anno.Rawdata_Type_pointcloud,
						},
						{
							Type:      anno.Rawdata_Type_image,
							Meta:      &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: frontCamera}},
							Transform: nil,
						},
						{
							Type:      anno.Rawdata_Type_image,
							Meta:      &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: backCamera}},
							Transform: nil,
						},
					},
				},
			},
			expectedCamParam: map[string]*anno.Job_CamParam{
				frontCamera: {
					Extrinsic:  extrinsic.Data,
					Intrinsic:  intrinsic.Data,
					Distortion: distortion.Data,
				},
				backCamera: {
					Extrinsic:  extrinsic.Data,
					Intrinsic:  intrinsic.Data,
					Distortion: distortion.Data,
				},
			},
		},
		{
			name: "extrinsic_is_different",
			elems: []*anno.Element{
				{
					Name: "elem_1",
					Datas: []*anno.Rawdata{
						{
							Type: anno.Rawdata_Type_pointcloud,
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: frontCamera}},
							Transform: []*anno.RawdataParam{
								extrinsic,
								intrinsic,
								distortion,
							},
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: backCamera}},
							Transform: []*anno.RawdataParam{
								extrinsic,
								intrinsic,
								distortion,
							},
						},
					},
				},
				{
					Name: "elem_2",
					Datas: []*anno.Rawdata{
						{
							Type: anno.Rawdata_Type_pointcloud,
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: frontCamera}},
							Transform: []*anno.RawdataParam{
								extrinsic,
								intrinsic,
								distortion,
							},
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: backCamera}},
							Transform: []*anno.RawdataParam{
								extrinsicDiff,
								intrinsic,
								distortion,
							},
						},
					},
				},
			},
			expectedElems: []*anno.Element{
				{
					Name: "elem_1",
					Datas: []*anno.Rawdata{
						{
							Type: anno.Rawdata_Type_pointcloud,
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: frontCamera}},
							Transform: []*anno.RawdataParam{
								extrinsic,
							},
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: backCamera}},
							Transform: []*anno.RawdataParam{
								extrinsic,
							},
						},
					},
				},
				{
					Name: "elem_2",
					Datas: []*anno.Rawdata{
						{
							Type: anno.Rawdata_Type_pointcloud,
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: frontCamera}},
							Transform: []*anno.RawdataParam{
								extrinsic,
							},
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: backCamera}},
							Transform: []*anno.RawdataParam{
								extrinsicDiff,
							},
						},
					},
				},
			},
			expectedCamParam: map[string]*anno.Job_CamParam{
				frontCamera: {
					Intrinsic:  intrinsic.Data,
					Distortion: distortion.Data,
				},
				backCamera: {
					Intrinsic:  intrinsic.Data,
					Distortion: distortion.Data,
				},
			},
		},
		{
			name: "no_extrinsic",
			elems: []*anno.Element{
				{
					Name: "elem_1",
					Datas: []*anno.Rawdata{
						{
							Type: anno.Rawdata_Type_pointcloud,
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: frontCamera}},
							Transform: []*anno.RawdataParam{
								intrinsic,
								distortion,
							},
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: backCamera}},
							Transform: []*anno.RawdataParam{
								intrinsic,
								distortion,
							},
						},
					},
				},
				{
					Name: "elem_2",
					Datas: []*anno.Rawdata{
						{
							Type: anno.Rawdata_Type_pointcloud,
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: frontCamera}},
							Transform: []*anno.RawdataParam{
								intrinsic,
								distortion,
							},
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: backCamera}},
							Transform: []*anno.RawdataParam{
								intrinsic,
								distortion,
							},
						},
					},
				},
			},
			expectedElems: []*anno.Element{
				{
					Name: "elem_1",
					Datas: []*anno.Rawdata{
						{
							Type: anno.Rawdata_Type_pointcloud,
						},
						{
							Type:      anno.Rawdata_Type_image,
							Meta:      &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: frontCamera}},
							Transform: nil,
						},
						{
							Type:      anno.Rawdata_Type_image,
							Meta:      &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: backCamera}},
							Transform: nil,
						},
					},
				},
				{
					Name: "elem_2",
					Datas: []*anno.Rawdata{
						{
							Type: anno.Rawdata_Type_pointcloud,
						},
						{
							Type:      anno.Rawdata_Type_image,
							Meta:      &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: frontCamera}},
							Transform: nil,
						},
						{
							Type:      anno.Rawdata_Type_image,
							Meta:      &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: backCamera}},
							Transform: nil,
						},
					},
				},
			},
			expectedCamParam: map[string]*anno.Job_CamParam{
				frontCamera: {
					Intrinsic:  intrinsic.Data,
					Distortion: distortion.Data,
				},
				backCamera: {
					Intrinsic:  intrinsic.Data,
					Distortion: distortion.Data,
				},
			},
		},
		{
			name: "no_transforms",
			elems: []*anno.Element{
				{
					Name: "elem_1",
					Datas: []*anno.Rawdata{
						{
							Type: anno.Rawdata_Type_pointcloud,
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: frontCamera}},
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: backCamera}},
						},
					},
				},
				{
					Name: "elem_2",
					Datas: []*anno.Rawdata{
						{
							Type: anno.Rawdata_Type_pointcloud,
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: frontCamera}},
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: backCamera}},
						},
					},
				},
			},
			expectedElems: []*anno.Element{
				{
					Name: "elem_1",
					Datas: []*anno.Rawdata{
						{
							Type: anno.Rawdata_Type_pointcloud,
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: frontCamera}},
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: backCamera}},
						},
					},
				},
				{
					Name: "elem_2",
					Datas: []*anno.Rawdata{
						{
							Type: anno.Rawdata_Type_pointcloud,
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: frontCamera}},
						},
						{
							Type: anno.Rawdata_Type_image,
							Meta: &anno.Rawdata_Meta{Image: &anno.Rawdata_ImageMeta{Camera: backCamera}},
						},
					},
				},
			},
			expectedCamParam: nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			camParams := aggCamParams(c.elems)
			// for key, value := range camParams {
			// 	t.Logf("key: %+v", key)
			// 	t.Logf("Intrinsic: %+v", value.Intrinsic)
			// 	t.Logf("Extrinsic: %+v", value.Extrinsic)
			// 	t.Logf("Distortion: %+v", value.Distortion)
			// }

			assert.EqualValues(t, c.expectedCamParam, camParams)
			assert.EqualValues(t, c.expectedElems, c.elems)
		})
	}

}
