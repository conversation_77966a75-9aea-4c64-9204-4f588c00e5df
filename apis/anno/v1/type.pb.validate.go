// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/type.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Scope with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Scope) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on <PERSON>ope with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ScopeMultiError, or nil if none found.
func (m *Scope) ValidateAll() error {
	return m.validate(true)
}

func (m *Scope) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Uid

	if len(errors) > 0 {
		return ScopeMultiError(errors)
	}

	return nil
}

// ScopeMultiError is an error wrapping multiple validation errors returned by
// Scope.ValidateAll() if the designated constraints aren't met.
type ScopeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScopeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScopeMultiError) AllErrors() []error { return m }

// ScopeValidationError is the validation error returned by Scope.Validate if
// the designated constraints aren't met.
type ScopeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScopeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScopeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScopeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScopeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScopeValidationError) ErrorName() string { return "ScopeValidationError" }

// Error satisfies the builtin error interface
func (e ScopeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScope.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScopeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScopeValidationError{}

// Validate checks the field values on Error with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Error) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Error with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ErrorMultiError, or nil if none found.
func (m *Error) ValidateAll() error {
	return m.validate(true)
}

func (m *Error) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Reason

	// no validation rules for Message

	if len(errors) > 0 {
		return ErrorMultiError(errors)
	}

	return nil
}

// ErrorMultiError is an error wrapping multiple validation errors returned by
// Error.ValidateAll() if the designated constraints aren't met.
type ErrorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ErrorMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ErrorMultiError) AllErrors() []error { return m }

// ErrorValidationError is the validation error returned by Error.Validate if
// the designated constraints aren't met.
type ErrorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ErrorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ErrorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ErrorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ErrorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ErrorValidationError) ErrorName() string { return "ErrorValidationError" }

// Error satisfies the builtin error interface
func (e ErrorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sError.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ErrorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ErrorValidationError{}
