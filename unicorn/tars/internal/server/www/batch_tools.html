<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量任务工具</title>
    <style>
        /* 通用样式 */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 700px;
        }
        h1 {
            font-size: 1.875rem; /* 30px */
            font-weight: 700;
            text-align: center;
            color: #1f2937;
            margin-bottom: 10px; /* 调整标题下方间距，为提示留出空间 */
        }
        .warning-tip {
            text-align: center;
            color: #ef4444; /* 红色警告 */
            font-size: 0.9rem;
            margin-bottom: 30px; /* 提示下方间距 */
            font-weight: 500;
        }
        .form-group {
            margin-bottom: 20px; /* 增加表单组间距 */
        }
        .form-group label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            display: block;
        }
        .form-group input[type="text"],
        .form-group input[type="number"],
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.2s ease-in-out;
            resize: vertical;
            box-sizing: border-box;
        }
        .form-group input[type="text"]:focus,
        .form-group input[type="number"]:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
        }
        .form-group input[type="text"]:disabled,
        .form-group input[type="number"]:disabled,
        .form-group textarea:disabled,
        .form-group select:disabled {
            background-color: #e5e7eb;
            cursor: not-allowed;
            opacity: 0.7;
        }
        .text-sm {
            font-size: 0.8rem; /* 调整说明文字大小 */
            color: #9ca3af; /* 调整说明文字颜色，更浅 */
            font-style: italic; /* 斜体 */
        }
        .text-gray-500 { /* 保留原类名，但实际颜色由 .text-sm 覆盖 */
            /* color: #6b7280; */
        }
        .mt-1 {
            margin-top: 4px;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .checkbox-group input[type="checkbox"] {
            margin-right: 10px;
            width: 18px;
            height: 18px;
            border-radius: 4px;
            border: 1px solid #d1d5db;
            appearance: none;
            -webkit-appearance: none;
            cursor: pointer;
            position: relative;
            background-color: #fff;
        }
        .checkbox-group input[type="checkbox"]:checked {
            background-color: #2563eb;
            border-color: #2563eb;
        }
        .checkbox-group input[type="checkbox"]:checked::after {
            content: '✔';
            color: #fff;
            font-size: 12px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .checkbox-group input[type="checkbox"]:disabled {
            background-color: #e5e7eb; /* 禁用时的背景色 */
            cursor: not-allowed;
            opacity: 0.7; /* 稍微降低透明度 */
        }
        .checkbox-group input[type="checkbox"]:disabled:checked {
            background-color: #93c5fd; /* 禁用且选中时的背景色 */
            border-color: #93c5fd;
        }
        .checkbox-group input[type="checkbox"]:disabled:checked::after {
            color: #6b7280; /* 禁用且选中时勾的颜色 */
        }
        .checkbox-group label {
            margin-bottom: 0;
            cursor: pointer;
            color: #4b5563; /* 禁用时标签颜色稍微变浅 */
        }
        .checkbox-group input[type="checkbox"]:disabled + label {
            cursor: not-allowed;
            color: #6b7280; /* 禁用时标签颜色 */
        }
        .mb-6 {
            margin-bottom: 24px;
        }
        .btn-primary {
            background-color: #2563eb;
            color: #ffffff;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            border: none;
            width: 100%;
        }
        .btn-primary:hover {
            background-color: #1d4ed8;
            box-shadow: 0 6px 15px rgba(37, 99, 235, 0.3);
        }
        .btn-primary:disabled {
            background-color: #93c5fd;
            cursor: not-allowed;
            box-shadow: none;
        }
        .message-box {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-size: 0.95rem;
            line-height: 1.5;
        }
        .message-box.error {
            background-color: #fee2e2;
            color: #dc2626;
            border: 1px solid #ef4444;
        }
        .message-box.success {
            background-color: #ecfdf5;
            color: #059669;
            border: 1px solid #10b981;
        }
        .message-box.info {
            background-color: #fffbeb; /* Yellow/Amber background */
            color: #92400e; /* Darker Amber text */
            border: 1px solid #fbbf24; /* Amber border */
        }
        .hidden {
            display: none;
        }
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
            display: none;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .text-green-700 {
            color: #047857;
        }
        .text-red-700 {
            color: #b91c1c;
        }
        .ml-4 {
            margin-left: 16px;
        }
        .mt-4 {
            margin-top: 16px;
        }
        .text-orange-500 { /* 为提示信息添加样式 */
            color: #f97316;
        }
        /* Radio button group styling */
        .radio-group {
            display: flex;
            gap: 20px;
            margin-bottom: 25px;
            justify-content: center;
        }
        .radio-group label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-weight: 600;
            color: #374151;
        }
        .radio-group input[type="radio"] {
            margin-right: 8px;
            /* Custom radio button styling */
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border: 2px solid #d1d5db;
            border-radius: 50%;
            outline: none;
            transition: border-color 0.2s ease-in-out, background-color 0.2s ease-in-out;
            position: relative;
            top: 1px; /* Align with text */
        }
        .radio-group input[type="radio"]:checked {
            border-color: #2563eb;
            background-color: #2563eb;
        }
        .radio-group input[type="radio"]:checked::before {
            content: '';
            display: block;
            width: 8px;
            height: 8px;
            background-color: #ffffff;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        /* 选项卡样式 */
        .tabs {
            display: flex;
            margin-top: 25px; /* 增加与上方域名选择的间距 */
            margin-bottom: 25px; /* 增加选项卡下方间距 */
            border-bottom: 2px solid #e5e7eb; /* 加粗边框使其更明显 */
            padding-bottom: 5px; /* 增加选项卡按钮与下边框的间距 */
        }
        .tab-button {
            padding: 10px 18px; /* 稍微增加内边距 */
            cursor: pointer;
            border: none;
            background-color: transparent;
            font-weight: 700; /* 字体加粗 */
            color: #6b7280;
            transition: color 0.2s ease-in-out, border-bottom-color 0.2s ease-in-out;
            border-bottom: 3px solid transparent; /* 加粗激活下划线 */
            margin-right: 15px; /* 增加选项卡按钮之间的间距 */
        }
        .tab-button:hover {
            color: #2563eb;
        }
        .tab-button.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }

        /* 域名选择组的间距 */
        .domain-select-group {
            margin-bottom: 0; /* 移除原有底部间距，让登录提示紧随其后 */
        }

        /* 确认弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex; /* 默认设置为 flex，通过 JS 控制 display: none */
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .modal-content {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
            width: 90%;
            max-width: 400px;
            text-align: center;
        }
        .modal-content h3 {
            font-size: 1.25rem;
            margin-bottom: 20px;
            color: #1f2937;
        }
        .modal-buttons {
            display: flex;
            justify-content: space-around;
            gap: 15px;
            margin-top: 25px;
        }
        .modal-button {
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            border: none;
            flex: 1;
        }
        .modal-button.confirm {
            background-color: #ef4444; /* Red for destructive action */
            color: #ffffff;
        }
        .modal-button.confirm:hover {
            background-color: #dc2626;
            box-shadow: 0 4px 10px rgba(239, 68, 68, 0.3);
        }
        .modal-button.cancel {
            background-color: #e5e7eb;
            color: #4b5563;
        }
        .modal-button.cancel:hover {
            background-color: #d1d5db;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
<div class="container">
    <h1>批量任务工具</h1>
    <p class="warning-tip">⚠️ 提醒：本工具包含高风险操作，请谨慎使用！</p>

    <div class="form-group domain-select-group">
        <label for="serviceDomain">服务域名 (Service Domain):</label>
        <select id="serviceDomain">
            <option value="anno.d.konvery.com">[旧线上环境]anno.d.konvery.com</option>
            <option value="online-anno.d.konvery.com">[新线上环境]online-anno.d.konvery.com</option>
            <option value="test-anno.np.konvery.work">[测试环境]test-anno.np.konvery.work</option>
            <option value="local.konvery.work">[本地环境]local.konvery.work</option>
        </select>
        <p class="text-sm text-gray-500 mt-1">选择要操作的后端服务域名。</p>
    </div>

    <!-- 登录状态消息框移到这里 -->
    <div id="loginStatusMessage" class="message-box hidden" style="margin-bottom: 20px;"></div>

    <div class="tabs">
        <button class="tab-button active" data-tab="revert">批量打回</button>
        <button class="tab-button" data-tab="assign">批量分配</button>
    </div>

    <!-- 批量打回 Tab Content -->
    <div id="revert-tab-content" class="tab-content active">
        <div class="radio-group">
            <label>
                <input type="radio" name="revertMode" id="revertModeLot" value="lot" checked>
                按 Lot UID 打回
            </label>
            <label>
                <input type="radio" name="revertMode" id="revertModeJob" value="job">
                按 Job UID 打回
            </label>
        </div>

        <div id="lotRevertGroup" class="form-group">
            <label for="lotUidsInputRevert">任务 - Lot UID(s) (逗号分隔):</label>
            <textarea id="lotUidsInputRevert" rows="3" placeholder="请输入一个或多个Lot UID，例如: ci854qaf6h6, ci854qaf6h7"></textarea>
            <p class="text-sm text-gray-500 mt-1">输入一个或多个Lot UID。</p>
        </div>

        <div id="jobRevertGroup" class="form-group hidden">
            <label for="jobUidsInputRevert">任务包 - Job UID(s) (逗号分隔):</label>
            <textarea id="jobUidsInputRevert" rows="5" placeholder="请输入一个或多个Job UID，例如: cjevkubi5o6, cjevkubi5o7"></textarea>
            <p class="text-sm text-gray-500 mt-1">输入一个或多个Job UID。所有Job UID必须属于同一个Lot。</p>
        </div>

        <div id="phaseInputGroup" class="form-group"> <!-- New wrapper for phase input -->
            <label for="filterCurrentPhasesInput">要处理阶段 (Current Phase(s)) (逗号分隔):</label>
            <input type="text" id="filterCurrentPhasesInput" placeholder="请输入一个或多个当前阶段，例如: 2,3,4">
            <p class="text-sm text-gray-500 mt-1" id="phaseHint">此项在“按 Lot UID 打回”模式下为必须。</p>
        </div>

        <div class="form-group">
            <label for="toPhaseInput">打回到阶段 (To Phase):</label>
            <input type="number" id="toPhaseInput" value="1" min="1" placeholder="目标阶段，最小为1">
            <p class="text-sm text-gray-500 mt-1">只能从高阶段打回到低阶段且两个阶段不能相同。</p>
        </div>

        <div class="mb-6">
            <div class="checkbox-group">
                <input type="checkbox" id="keepAnnosCheckbox" checked disabled>
                <label for="keepAnnosCheckbox">保留标注结果 (Keep Annotations)</label>
            </div>
            <div class="checkbox-group">
                <input type="checkbox" id="keepCommentsCheckbox" checked disabled>
                <label for="keepCommentsCheckbox">保留批注 (Keep Comments)</label>
            </div>
            <div class="checkbox-group">
                <input type="checkbox" id="toPreviousExecutorCheckbox" checked>
                <label for="toPreviousExecutorCheckbox">分配到上一个操作人 (To Previous Executor)</label>
            </div>
        </div>

        <button id="revertButton" class="btn-primary">开始打回</button>
    </div>

    <!-- 批量分配 Tab Content -->
    <div id="assign-tab-content" class="tab-content">
        <div class="form-group">
            <label for="jobUidsInputAssign">任务包 - Job UID(s) (逗号分隔):</label>
            <textarea id="jobUidsInputAssign" rows="5" placeholder="请输入一个或多个Job UID，例如: cjevkubi5o6, cjevkubi5o7"></textarea>
            <p class="text-sm text-gray-500 mt-1">将这些Job UID分配给指定的处理人。</p>
        </div>

        <div class="form-group">
            <label for="executorUidInput">处理人 UID (Executor UID):</label>
            <input type="text" id="executorUidInput" placeholder="请输入处理人UID">
            <p class="text-sm text-gray-500 mt-1">要分配到的处理人UID。</p>
        </div>

        <button id="assignButton" class="btn-primary">开始分配</button>
    </div>

    <div id="loadingSpinner" class="loading-spinner"></div>
    <div id="messageBox" class="message-box hidden"></div>
</div>

<!-- 确认弹窗 -->
<div id="confirmationModal" class="modal-overlay">
    <div class="modal-content">
        <h3 id="modalTitle"></h3>
        <p id="modalMessage"></p>
        <div class="modal-buttons">
            <button id="confirmBtn" class="modal-button confirm">确认</button>
            <button id="cancelBtn" class="modal-button cancel">取消</button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        // 获取所有需要的DOM元素
        const serviceDomainSelect = document.getElementById('serviceDomain');
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');
        const messageBox = document.getElementById('messageBox');
        const loadingSpinner = document.getElementById('loadingSpinner');
        const loginStatusMessage = document.getElementById('loginStatusMessage');

        // 打回工具的元素
        const revertModeLotRadio = document.getElementById('revertModeLot');
        const revertModeJobRadio = document.getElementById('revertModeJob');
        const lotRevertGroup = document.getElementById('lotRevertGroup');
        const jobRevertGroup = document.getElementById('jobRevertGroup');
        const phaseInputGroup = document.getElementById('phaseInputGroup'); // New: Phase input container

        const lotUidsInputRevert = document.getElementById('lotUidsInputRevert');
        const jobUidsInputRevert = document.getElementById('jobUidsInputRevert');
        const filterCurrentPhasesInput = document.getElementById('filterCurrentPhasesInput');
        const phaseHint = document.getElementById('phaseHint'); // For dynamic hint text
        const toPhaseInput = document.getElementById('toPhaseInput');
        const keepAnnosCheckbox = document.getElementById('keepAnnosCheckbox');
        const keepCommentsCheckbox = document.getElementById('keepCommentsCheckbox');
        const toPreviousExecutorCheckbox = document.getElementById('toPreviousExecutorCheckbox');
        const revertButton = document.getElementById('revertButton');

        // 分配工具的元素
        const jobUidsInputAssign = document.getElementById('jobUidsInputAssign');
        const executorUidInput = document.getElementById('executorUidInput');
        const assignButton = document.getElementById('assignButton');

        // 确认弹窗元素
        const confirmationModal = document.getElementById('confirmationModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalMessage = document.getElementById('modalMessage');
        const confirmBtn = document.getElementById('confirmBtn');
        const cancelBtn = document.getElementById('cancelBtn');

        // 基础API路径
        const REVERT_API_PATH = '/anno/v1/jobs/revert';
        const ASSIGN_API_BASE_PATH = '/anno/v1/jobs/';
        const REFRESH_TOKEN_PATH = '/iam/v1/users/token/refresh';
        const ME_API_PATH = '/iam/v1/users/me';
        let baseURL = `https://${serviceDomainSelect.value}`;

        // 确保弹窗在页面加载时是隐藏的
        confirmationModal.style.display = 'none';

        /**
         * 获取当前选定的基础URL
         * @returns {string} 当前服务域名的完整URL
         */
        function getBaseURL() {
            return `https://${serviceDomainSelect.value}`;
        }

        /**
         * 根据当前页面域名自动选择服务域名
         */
        function autoSelectServiceDomain() {
            const currentHostname = window.location.hostname;
            let selectedValue = serviceDomainSelect.options[0].value;

            if (currentHostname.includes('local.konvery.work')) {
                selectedValue = 'local.konvery.work';
            } else if (currentHostname.includes('test-tars') || currentHostname.includes('test-anno.np.konvery.work')) {
                selectedValue = 'test-anno.np.konvery.work';
            } else if (currentHostname.includes('online-tars') || currentHostname.includes('online-anno.d.konvery.com')) {
                selectedValue = 'online-anno.d.konvery.com';
            } else if (currentHostname.includes('tars') || currentHostname.includes('anno.d.konvery.com')) {
                selectedValue = 'anno.d.konvery.com';
            }

            serviceDomainSelect.value = selectedValue;
            baseURL = getBaseURL();
        }

        /**
         * 更新baseURL
         */
        serviceDomainSelect.addEventListener('change', () => {
            baseURL = getBaseURL();
            testLoginStatus();
        });

        /**
         * 选项卡切换逻辑
         */
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.dataset.tab;

                tabButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                tabContents.forEach(content => content.classList.remove('active'));
                document.getElementById(`${tabId}-tab-content`).classList.add('active');

                hideMessage();
                hideLoading();
            });
        });


        /**
         * 显示消息框
         * @param {string} message 要显示的消息（可以是HTML字符串）
         * @param {string} type 消息类型 ('info', 'success', 'error')
         * @param {HTMLElement} targetMessageBox 目标消息框元素，默认为主消息框
         */
        function showMessage(message, type = 'info', targetMessageBox = messageBox) {
            targetMessageBox.innerHTML = message;
            targetMessageBox.className = `message-box ${type}`;
            targetMessageBox.classList.remove('hidden');
        }

        /**
         * 隐藏消息框
         * @param {HTMLElement} targetMessageBox = messageBox 目标消息框元素，默认为主消息框
         */
        function hideMessage(targetMessageBox = messageBox) {
            targetMessageBox.classList.add('hidden');
            targetMessageBox.innerHTML = '';
        }

        /**
         * 显示加载指示器并禁用按钮
         */
        function showLoading() {
            loadingSpinner.style.display = 'block';
            revertButton.disabled = true;
            assignButton.disabled = true;
            hideMessage();
        }

        /**
         * 隐藏加载指示器并启用按钮
         */
        function hideLoading() {
            loadingSpinner.style.display = 'none';
            revertButton.disabled = false;
            assignButton.disabled = false;
        }

        /**
         * 发送API请求
         * @param {string} url 请求URL
         * @param {Object} payload 请求体数据
         * @param {string} method 请求方法 (POST)
         * @returns {Promise<Object>} 包含成功状态和数据或错误信息的Promise
         */
        async function sendApiRequest(url, payload, method = 'POST') {
            try {
                console.log('Making API request to:', url, 'with payload:', payload);
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': '*/*',
                        'Origin': getBaseURL(),
                        'Referer': getBaseURL() + '/',
                    },
                    credentials: 'include',
                    body: JSON.stringify(payload)
                });

                const data = await response.json();
                console.log('API response received:', data);
                if (!response.ok) {
                    if (response.status === 401) {
                        showMessage('会话过期或未登录，请刷新页面或检查登录状态。', 'error', loginStatusMessage);
                    }
                    throw new Error(data.message || `HTTP error! status: ${response.status}`);
                }
                return { success: true, data: data };
            } catch (error) {
                console.error('API请求失败:', error);
                return { success: false, error: error.message };
            }
        }

        /**
         * 检查用户登录状态并显示提示
         */
        async function testLoginStatus() {
            hideMessage(loginStatusMessage);
            try {
                const response = await fetch(getBaseURL() + ME_API_PATH, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Accept': '*/*',
                        'Origin': getBaseURL(),
                        'Referer': getBaseURL() + '/',
                    }
                });

                if (response.ok) {
                    console.log('用户已登录。');
                    hideMessage(loginStatusMessage);
                } else if (response.status === 401) {
                    showMessage('当前未登录或会话已过期。请确保您已在主系统登录。', 'info', loginStatusMessage);
                    console.warn('未登录或会话过期。');
                } else {
                    const errorData = await response.json().catch(() => ({ message: '未知错误' }));
                    showMessage(`检查登录状态失败: ${errorData.message || response.statusText}`, 'error', loginStatusMessage);
                    console.error('检查登录状态失败:', response.status, errorData);
                }
            } catch (error) {
                showMessage('无法连接到服务，请检查域名或网络连接。', 'error', loginStatusMessage);
                console.error('检查登录状态时发生网络错误:', error);
            }
        }


        /**
         * 自动刷新Token的逻辑
         */
        function autoRefreshToken() {
            function refreshToken() {
                const delay = Math.random() * 300;

                fetch(getBaseURL() + REFRESH_TOKEN_PATH, {
                    method: 'GET',
                    credentials: 'include',
                })
                    .then(response => {
                        if(response.ok) {
                            console.log('Token refreshed successfully.');
                            setTimeout(refreshToken, (delay + 4 * 3600) * 1000);
                            hideMessage(loginStatusMessage);
                        } else if(response.status === 401) {
                            console.warn('Token refresh failed: 401 Unauthorized. Session expired or not logged in.');
                            showMessage('会话过期或未登录，请刷新页面或检查登录状态。', 'info', loginStatusMessage);
                        }
                        else if(response.status >= 500) {
                            console.error('Failed to refresh token, server error:', response.status);
                            setTimeout(refreshToken, delay * 1000);
                        } else {
                            console.warn('Token refresh failed with status:', response.status);
                            setTimeout(refreshToken, delay * 1000);
                        }
                    })
                    .catch(error => {
                        console.error('Failed to refresh token (network error or CORS):', error);
                        setTimeout(refreshToken, delay * 1000);
                    });
            }

            let initialDelay = Math.random() * 300;
            setTimeout(refreshToken, initialDelay * 1000);
        }

        /**
         * 显示确认弹窗
         * @param {string} title 弹窗标题
         * @param {string} message 弹窗消息
         * @returns {Promise<boolean>} 用户是否确认
         */
        function showConfirmationModal(title, message) {
            return new Promise(resolve => {
                modalTitle.textContent = title;
                modalMessage.textContent = message;
                confirmationModal.style.display = 'flex';

                const onConfirm = () => {
                    confirmationModal.style.display = 'none';
                    confirmBtn.removeEventListener('click', onConfirm);
                    cancelBtn.removeEventListener('click', onCancel);
                    resolve(true);
                };

                const onCancel = () => {
                    confirmationModal.style.display = 'none';
                    confirmBtn.removeEventListener('click', onConfirm);
                    cancelBtn.removeEventListener('click', onCancel);
                    resolve(false);
                };

                confirmBtn.addEventListener('click', onConfirm);
                cancelBtn.addEventListener('click', onCancel);
            });
        }

        /**
         * 根据选择的打回模式更新UI显示
         */
        function updateRevertModeUI() {
            const isLotMode = revertModeLotRadio.checked;

            if (isLotMode) {
                lotRevertGroup.classList.remove('hidden');
                jobRevertGroup.classList.add('hidden');
                phaseInputGroup.classList.remove('hidden'); // Show phase input for Lot mode
                jobUidsInputRevert.value = ''; // Clear job input when hidden
                phaseHint.textContent = '此项在“按 Lot UID 打回”模式下为必须。';
            } else { // Job mode
                lotRevertGroup.classList.add('hidden');
                jobRevertGroup.classList.remove('hidden');
                phaseInputGroup.classList.add('hidden'); // Hide phase input for Job mode
                lotUidsInputRevert.value = ''; // Clear lot input when hidden
                filterCurrentPhasesInput.value = ''; // Clear phases when hidden
            }
        }

        // =====================================
        // 批量打回功能逻辑
        // =====================================
        revertButton.addEventListener('click', async () => {
            console.log('Revert button clicked. Starting validation...');
            hideMessage();

            // --- 校验 ---
            const isLotMode = revertModeLotRadio.checked;

            // Parse inputs based on selected mode
            const lotUidsRaw = isLotMode ? lotUidsInputRevert.value.split(/[\n,，]/).map(uid => uid.trim()).filter(uid => uid !== '') : [];
            const jobUidsRaw = !isLotMode ? jobUidsInputRevert.value.split(/[\n,，]/).map(uid => uid.trim()).filter(uid => uid !== '') : [];

            // Only parse filterCurrentPhasesRaw if in Lot mode
            const filterCurrentPhasesRaw = isLotMode ? filterCurrentPhasesInput.value.split(/[\n,，]/).map(p => parseInt(p.trim(), 10)).filter(p => !isNaN(p) && p > 0) : [];

            const toPhase = parseInt(toPhaseInput.value, 10);

            // Declare variables for results
            let totalSuccessJobs = 0;
            let totalFailedJobs = 0;
            let lotPhaseCombinationsProcessed = 0;
            let lotPhaseCombinationsFailed = 0;
            let resultsHtml = '<h3>打回结果:</h3><ul>';

            // 1. Core validation: Must specify Lot UID(s) or Job UID(s) based on mode
            if (isLotMode && lotUidsRaw.length === 0) {
                showMessage('请在“按 Lot UID 打回”模式下输入Lot UID(s)。', 'error');
                return;
            }
            if (!isLotMode && jobUidsRaw.length === 0) {
                showMessage('请在“按 Job UID 打回”模式下输入Job UID(s)。', 'error');
                return;
            }

            // 2. Rule: If reverting by Lot UID(s), Current Phase(s) is mandatory
            if (isLotMode && filterCurrentPhasesRaw.length === 0) {
                showMessage('当选择“按 Lot UID 打回”时，必须指定“要处理阶段”。', 'error');
                return;
            }

            // 3. Validate 'To Phase'
            if (isNaN(toPhase) || toPhase < 1) {
                showMessage('打回阶段 (To Phase) 必须是大于0的整数。', 'error');
                return;
            }

            // 4. Validate 'To Phase' cannot be same as 'Current Phase'
            // This check only applies if Current Phase(s) are provided AND in Lot Mode
            if (isLotMode && filterCurrentPhasesRaw.length > 0) {
                if (filterCurrentPhasesRaw.includes(toPhase)) {
                    showMessage('打回阶段不能与当前阶段相同。', 'error');
                    return;
                }
                // 5. Validate 'To Phase' must be less than all 'Current Phases'
                for (const fPhase of filterCurrentPhasesRaw) {
                    if (toPhase >= fPhase) {
                        showMessage(`目标阶段 (To Phase: ${toPhase}) 必须小于所有要处理阶段 (Current Phase: ${fPhase})。`, 'error');
                        return;
                    }
                }
            }

            // 6. Validate Service Domain
            if (!serviceDomainSelect.value) {
                showMessage('请选择一个服务域名。', 'error');
                return;
            }

            console.log('Validation passed. Showing confirmation modal.');
            // Show confirmation modal
            const confirmed = await showConfirmationModal('确认打回操作', '您确定要执行批量打回操作吗？此操作不可逆！');
            if (!confirmed) {
                showMessage('批量打回操作已取消。', 'info');
                return;
            }

            console.log('User confirmed. Showing loading spinner and preparing API calls.');
            showLoading();

            // Get actual checked state for toPreviousExecutorCheckbox
            const keepAnnos = keepAnnosCheckbox.checked;
            const keepComments = keepCommentsCheckbox.checked;
            const toPreviousExecutor = toPreviousExecutorCheckbox.checked; // Now reads the actual state

            const basePayload = {
                action: { to_phase: toPhase },
                options: {
                    keep_annos: keepAnnos,
                    keep_comments: keepComments,
                    to_previous_executor: toPreviousExecutor
                },
                filter: {}
            };

            if (isLotMode) { // Revert by Lot UID(s)
                console.log('Executing API calls for Lot UID(s) based reversion.');
                // Iterate through each lot UID and each phase
                for (const lotUid of lotUidsRaw) {
                    for (const filterPhase of filterCurrentPhasesRaw) { // filterCurrentPhasesRaw is guaranteed to have content by validation
                        lotPhaseCombinationsProcessed++;
                        const payload = { ...basePayload };
                        payload.filter = { lot_uid: lotUid, phase: filterPhase };
                        resultsHtml += `<li>正在处理 Lot UID: <strong>${lotUid}</strong>, 当前阶段: <strong>${filterPhase}</strong>...</li>`;
                        const result = await sendApiRequest(baseURL + REVERT_API_PATH, payload);
                        if (result.success) {
                            totalSuccessJobs += (result.data.ok_job_uids ? result.data.ok_job_uids.length : 0);
                            totalFailedJobs += (result.data.fail_job_uids ? result.data.fail_job_uids.length : 0);
                            resultsHtml += `<li class="text-green-700">Lot UID: <strong>${lotUid}</strong>, 阶段 ${filterPhase} - 成功打回 ${result.data.ok_job_uids.length} 个Job, 失败 ${result.data.fail_job_uids.length} 个Job。</li>`;
                        } else {
                            lotPhaseCombinationsFailed++;
                            resultsHtml += `<li class="text-red-700">Lot UID: <strong>${lotUid}</strong>, 阶段 ${filterPhase} - 请求失败: ${result.error}</li>`;
                        }
                    }
                }
                resultsHtml += '</ul>';
                resultsHtml += `<p class="mt-4 font-bold">总计：尝试处理 ${lotPhaseCombinationsProcessed} 个Lot-阶段组合，其中 ${lotPhaseCombinationsProcessed - lotPhaseCombinationsFailed} 个请求成功，成功打回 ${totalSuccessJobs} 个Job，失败 ${totalFailedJobs} 个Job。</p>`;
                if (lotPhaseCombinationsFailed > 0) {
                    resultsHtml += `<p class="mt-2 font-bold text-red-700">有 ${lotPhaseCombinationsFailed} 个请求因网络或服务器错误而失败。</p>`;
                }
            } else { // Revert by Job UID(s)
                console.log('Executing API calls for Job UID(s) based reversion (batch requests).');
                // No phase filter is used in this mode, as the input field is hidden.
                // We will batch job UIDs if they exceed JOB_UID_BATCH_SIZE.
                const JOB_UID_BATCH_SIZE = 100; // Define locally for clarity

                const numChunks = Math.ceil(jobUidsRaw.length / JOB_UID_BATCH_SIZE);
                for (let i = 0; i < numChunks; i++) {
                    const chunk = jobUidsRaw.slice(i * JOB_UID_BATCH_SIZE, (i + 1) * JOB_UID_BATCH_SIZE);
                    const payload = { ...basePayload };
                    payload.filter = { uids: chunk }; // Send a chunk of job UIDs
                    // No phase filter is added to the payload in this mode

                    resultsHtml += `<li>正在处理 Job UIDs 批次 ${i + 1}/${numChunks} (${chunk.length} 个)...</li>`;
                    const result = await sendApiRequest(baseURL + REVERT_API_PATH, payload);
                    if (result.success) {
                        totalSuccessJobs += (result.data.ok_job_uids ? result.data.ok_job_uids.length : 0);
                        totalFailedJobs += (result.data.fail_job_uids ? result.data.fail_job_uids.length : 0);
                        resultsHtml += `<li class="text-green-700">批次 ${i + 1} - 成功打回 ${result.data.ok_job_uids.length} 个Job, 失败 ${result.data.fail_job_uids.length} 个Job。</li>`;
                        if (result.data.fail_job_uids && result.data.fail_job_uids.length > 0) {
                            resultsHtml += `<li class="text-red-700 ml-4">失败的Job UIDs: ${result.data.fail_job_uids.join(', ')}</li>`;
                        }
                    } else {
                        totalFailedJobs += chunk.length; // If request fails, assume all jobs in this batch failed
                        resultsHtml += `<li class="text-red-700">批次 ${i + 1} - 请求失败: ${result.error}</li>`;
                    }
                }
                resultsHtml += '</ul>';
                resultsHtml += `<p class="mt-4 font-bold">总计：成功打回 ${totalSuccessJobs} 个Job，失败 ${totalFailedJobs} 个Job。</p>`;
            }

            console.log('API calls finished. Hiding loading spinner.');
            hideLoading();
            showMessage(resultsHtml, totalFailedJobs > 0 || lotPhaseCombinationsFailed > 0 ? 'error' : 'success');
        });

        // =====================================
        // 批量分配功能逻辑
        // =====================================
        assignButton.addEventListener('click', async () => {
            hideMessage();

            // 支持中英文逗号分隔以及换行符
            const jobUids = jobUidsInputAssign.value.split(/[\n,，]/).map(uid => uid.trim()).filter(uid => uid !== '');
            const executorUid = executorUidInput.value.trim();

            if (jobUids.length === 0) {
                showMessage('请输入要分配的Job UID(s)。', 'error');
                return;
            }
            if (!executorUid) {
                showMessage('请输入处理人UID。', 'error');
                return;
            }
            if (!serviceDomainSelect.value) {
                showMessage('请选择一个服务域名。', 'error');
                return;
            }

            // 显示确认弹窗
            const confirmed = await showConfirmationModal('确认分配操作', `您确定要将 ${jobUids.length} 个Job分配给 ${executorUid} 吗？`);
            if (!confirmed) {
                showMessage('批量分配操作已取消。', 'info');
                return;
            }

            showLoading();

            let totalSuccess = 0;
            let totalFailed = 0;
            let resultsHtml = '<h3>分配结果:</h3><ul>';

            // 循环发送单个Job的分配请求
            for (const jobUid of jobUids) {
                const assignUrl = `${baseURL}${ASSIGN_API_BASE_PATH}${jobUid}/assign`;
                const payload = {
                    uid: jobUid,
                    executor_uid: executorUid
                };
                resultsHtml += `<li>正在分配 Job UID: <strong>${jobUid}</strong> 给 <strong>${executorUid}</strong>...</li>`;

                const result = await sendApiRequest(assignUrl, payload);
                if (result.success) {
                    totalSuccess++;
                    resultsHtml += `<li class="text-green-700">Job UID: <strong>${jobUid}</strong> - 分配成功。</li>`;
                } else {
                    totalFailed++;
                    resultsHtml += `<li class="text-red-700">Job UID: <strong>${jobUid}</strong> - 分配失败: ${result.error}</li>`;
                }
            }

            resultsHtml += '</ul>';
            resultsHtml += `<p class="mt-4 font-bold">总计：成功分配 ${totalSuccess} 个Job，失败 ${totalFailed} 个Job。</p>`;

            hideLoading();
            showMessage(resultsHtml, totalFailed > 0 ? 'error' : 'success');
        });

        // 页面加载时启动Token自动刷新和登录状态检查
        autoRefreshToken();
        autoSelectServiceDomain();
        testLoginStatus();

        // 绑定单选按钮事件，实时更新UI
        revertModeLotRadio.addEventListener('change', updateRevertModeUI);
        revertModeJobRadio.addEventListener('change', updateRevertModeUI);

        // 页面加载时执行一次，确保初始状态正确
        updateRevertModeUI();
    });
</script>
</body>
</html>
