package middleware

import (
	"context"

	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
)

const (
	HeaderDebug = "x-md-Debug"
)

type ctxKeyDebug struct{}

// DebugEnabler is a middleware to enable debug for a specific request.
func DebugEnabler() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			debug := false
			if tr, ok := transport.FromServerContext(ctx); ok {
				header := tr.RequestHeader()
				debug = header.Get(HeaderDebug) == "true"
			}
			if debug {
				ctx = NewCtxWithDebug(ctx)
			}
			return handler(ctx, req)
		}
	}
}

func NewCtxWithDebug(ctx context.Context) context.Context {
	return context.WithValue(ctx, ctxKeyDebug{}, true)
}

func DebugFromCtx(ctx context.Context) bool {
	debug, _ := ctx.Value(ctxKeyDebug{}).(bool)
	return debug
}
