import json
import os

import sys

sys.path.append(".")
from customer.common import tools

PI = 3.1415926

line_count = 0
file_count = 0
err_files = {}
file_box_dict = {}
current_file = ""
object_count = 0


def convert(data):
    global current_file
    global err_files
    res = {
        "label_meta": {"mark_status": 0, "global": {}},
        "lidar": []
    }

    label_list = []

    for index, obj in enumerate(data):
        mark_data = {
            "class": "AUTO",
            "attrs": {},
            # "id": "",
            "track_id": 0,
            "point_num": 0,
            "no_maps": [],
            "group_id": 0,
            "annotation": {}
        }
        annotation = {"type": "volume",
                      "data": {"position": {}, "dimension": {}, "rotation": {"x": 0, "y": 0, "z": 0}}}
        annotation["data"]["position"] = {"x": obj["position"]["x"], "y": obj["position"]["y"],
                                          "z": obj["position"]["z"]}
        annotation["data"]["dimension"] = {"l": obj["bounding_box"]["length"], "w": obj["bounding_box"]["width"],
                                           "h": obj["bounding_box"]["height"]}
        z = obj["heading"]

        if z < 0:
            z = z + 2 * PI

        annotation["data"]["rotation"]["z"] = z
        mark_data["annotation"] = annotation
        # mark_data["id"] = str(index)
        mark_data["track_id"] = index + 1
        label_list.append(mark_data)

    res["lidar"] = label_list
    return res


def run(input_dir, output_dir):
    global file_count
    global line_count
    global current_file
    global file_box_dict

    file_count = 0
    invalid_file_count = 0
    invalid_file_list = []

    input_dir = tools.unzip(input_dir)
    for directory in os.listdir(output_dir):
        d = os.path.join(output_dir, directory)
        if os.path.isdir(d):
            tools.check_dir(d + os.sep + "pre_label")

    for f in tools.get_file_by_extension(input_dir, "txt"):

        if f.name.endswith('.txt') and not f.name.startswith("."):
            convert_data = {}
            output_file_name = ""

            with open(f.path) as json_file:
                output_file_name = f.path.replace(input_dir, output_dir)
                output_file_name = output_file_name.replace(f.name, "pre_label" + os.sep + f.name).replace(".txt",
                                                                                                           ".json")
                data = json.load(json_file)
                convert_data = convert(data["objects"])
                file_count += 1

            with open(output_file_name, "w") as outfile:
                json.dump(convert_data, outfile, indent=4)

    err_title = "错误信息"
    err_type_list = ["无效值"]
    return tools.check_error(output_dir, err_files, err_title, file_count, object_count, err_type_list, is_zip=False)


"""
    项目： YR 3D项目
    对接平台：云测新平台 
    功能： 客户的格式导入到云测预标注格式
    输入： txt
    输出： json
    云测3D输出格式： http://label-docs.testin.cn/dataset/pointcloud/
"""
if __name__ == "__main__":
    args = tools.get_args()
    args.input = "../../input/test/gru/auto"
    args.out = "../../input/test/gru/out"
    run(args.input, args.out)
