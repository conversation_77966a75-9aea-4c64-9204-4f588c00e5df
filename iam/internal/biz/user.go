// source: gitlab.rp.konvery.work/platform/apis/iam/v1/user.proto
package biz

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"regexp"
	"strconv"
	"strings"
	"sync/atomic"
	"time"

	"iam/internal/biz/ruleng"
	"iam/internal/conf"
	"iam/pkg/keto"

	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/apis/iam/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/jwt"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"gitlab.rp.konvery.work/platform/pkg/notice"
)

const (
	IDTypeEmail = notice.ChannelEmail
	IDTypePhone = "phone"
	IDTypeUid   = "uid"
)

func ParseUserIdentity(identity string) (string, string) {
	typ, id, found := strings.Cut(identity, ":")
	if !found {
		return "", ""
	}
	switch typ {
	case IDTypeEmail, IDTypePhone, IDTypeUid:
		return typ, id
	}
	return "", ""
}

const (
	UserStatusActive = 0
	// UserStatusDeleted = 1
)

const (
	GenderUnknown = 0
	GenderMale    = 1
	GenderFemale  = 2
)

var (
	UserTagPinAuthcode = NewSysTag("iam.pin_authcode")
)

type User struct {
	ID int64 `json:"id" gorm:"default:null"`
	// Uid       string    `json:"uid" gorm:"default:null"`
	Name      string           `json:"name" gorm:"default:null"`
	Phone     string           `json:"phone" gorm:"default:null"`
	Email     string           `json:"email" gorm:"default:null"`
	Avatar    string           `json:"avatar" gorm:"default:null"`
	OrgID     int64            `json:"org_id" gorm:"default:null"`
	Role      string           `json:"role" gorm:"default:null"`
	Status    int              `json:"status" gorm:"default:null"`
	Province  string           `json:"province" gorm:"default:null"`
	City      string           `json:"city" gorm:"default:null"`
	Gender    int              `json:"gender" gorm:"default:null"`
	Agreement int              `json:"agreement" gorm:"default:null"`
	Birthday  time.Time        `json:"birthday" gorm:"default:null"`
	Tags      repo.StringArray `json:"tags" gorm:"default:null;type:text[]"`
	CreatedAt time.Time        `json:"created_at" gorm:"default:null"`
	UpdatedAt time.Time        `json:"updated_at" gorm:"default:null"`
	DeletedAt DeletedAt        `json:"deleted_at" gorm:"default:null"`

	Org    *Team  `json:"-" gorm:"-"`
	OrgUid string `json:"-" gorm:"-"`
	// Imperfect bool      `json:"imperfect" gorm:"default:null"`
	// WorkTeamID int64     `json:"work_team_id" gorm:"default:null"`
	LastLoginAt time.Time `json:"last_login_at" gorm:"column:last_login_at"`
}

func (o *User) GetID() int64 {
	if o == nil {
		return 0
	}
	return o.ID
}
func (o *User) GetUid() string {
	if o == nil || o.ID == 0 {
		return ""
	}
	return kid.StringID(o.ID)
}
func (o *User) UidFld() string { panic("code error") }
func (o *User) EnsureID() {
	if o.ID == 0 {
		o.ID = kid.NewID()
	}
}

func (o *User) IsActive() bool     { return o.Status == UserStatusActive && o.DeletedAt.Valid }
func (o *User) IsImperfect() bool  { return o.Name == "" }
func (o *User) IsRoot() bool       { return o.Role == SysRoleRoot }
func (o *User) IsPrivileged() bool { return lo.Contains(SysRoleAdmins(), o.Role) }

// TODO: remove GetHierarchy as it is meaningless now
// func (o *User) GetHierarchy() []string {
// 	if o.Org == nil {
// 		return nil
// 	}
// 	return o.Org.GetHierarchy()
// }

func (o *User) SimpleUser() *SimpleUser {
	return &SimpleUser{
		Uid:    o.GetUid(),
		Name:   o.Name,
		Avatar: o.Avatar,
		Role:   o.Role,
	}
}

const UserTableName = "users"

var (
	user_             = field.RegObject(&User{})
	UserUpdatableFlds = field.NewModel(UserTableName, user_, "Name", "Avatar", "Province", "City", "Gender",
		"Birthday", "Role", "Status", "OrgID", "Agreement")

	// field name in snake case
	User_Name      = field.Sname(&user_.Name)
	User_Phone     = field.Sname(&user_.Phone)
	User_Email     = field.Sname(&user_.Email)
	User_Avatar    = field.Sname(&user_.Avatar)
	User_Province  = field.Sname(&user_.Province)
	User_City      = field.Sname(&user_.City)
	User_Gender    = field.Sname(&user_.Gender)
	User_Birthday  = field.Sname(&user_.Birthday)
	User_Role      = field.Sname(&user_.Role)
	User_Status    = field.Sname(&user_.Status)
	User_OrgID     = field.Sname(&user_.OrgID)
	User_Agreement = field.Sname(&user_.Agreement)
	User_Tags      = field.Sname(&user_.Tags)
	// User_WorkTeamID = field.Sname(&user_.WorkTeamID)
)

type SimpleUser struct {
	Uid    string `json:"uid"`
	Name   string `json:"name"`
	Avatar string `json:"avatar"`
	Role   string `json:"role"`
}

type UserListFilter struct {
	Pagesz int
	Page   int
	IDs    []int64
	OrgID  int64
	Roles  []string
	Phones []string
	Emails []string
	Tags   []string

	NamePattern string
}

func (o *UserListFilter) Apply(tx Tx) Tx {
	if tx == nil {
		return nil
	}

	tbl := UserTableName + "."
	tx = repo.ApplyFieldFilter(tx, tbl+"id", o.IDs)
	tx = repo.ApplyFieldFilter(tx, tbl+User_OrgID, o.OrgID)
	tx = repo.ApplyFieldFilter(tx, tbl+User_Role, o.Roles)
	tx = repo.ApplyFieldFilter(tx, tbl+User_Phone, o.Phones)
	tx = repo.ApplyFieldFilter(tx, tbl+User_Email, o.Emails)
	tx = repo.ApplyPatternFilter(tx, tbl+User_Name, o.NamePattern)
	tx = repo.ApplyPGArrayContainsAllFilter(tx, tbl+User_Tags, o.Tags...)
	return tx
}

type LoginReply struct {
	User      *User
	Token     string
	ExpiresAt time.Time
}

type UsersRepo interface {
	repo.GenericRepo[User]

	GetBy(ctx context.Context, idType, identity string) (*User, error)

	JoinOrg(ctx context.Context, orgID int64, role string, users ...int64) error
	SetRole(ctx context.Context, role string, users ...int64) error
	LeaveOrg(ctx context.Context, users ...int64) error
	DisbandOrg(ctx context.Context, orgID int64) error

	SaveAuthcode(ctx context.Context, p *Authcode, minInterval time.Duration) (*Authcode, error)
	GetAuthcode(ctx context.Context, userID int64, identity string) (*Authcode, error)
	UpdateAuthcode(context.Context, *Authcode, *FieldMask) (*Authcode, error)
	UpdateLastLoginTime(ctx context.Context, userID int64) error
}

type fakeLogin struct {
	PhoneRegexp *regexp.Regexp
	AuthCode    string
}

type UsersBiz struct {
	repo UsersRepo
	meta MetasRepo
	log  *log.Helper

	autoCreateRoot int32

	fakeLogins []fakeLogin
}

func NewUsersBiz(repo UsersRepo, meta MetasRepo, logger log.Logger, dangerTest *conf.DangerTest) *UsersBiz {
	o := &UsersBiz{repo: repo, meta: meta, log: log.NewHelper(logger)}

	for _, l := range dangerTest.GetFakeLogins() {
		if l.PhoneRegexp == "" {
			panic("bad fake login config")
		}
		o.fakeLogins = append(o.fakeLogins, fakeLogin{
			PhoneRegexp: regexp.MustCompile(l.PhoneRegexp),
			AuthCode:    l.AuthCode,
		})
	}

	// set autoRoot when no user is manually created
	val, _ := o.meta.Is(context.Background(), MetaNameAutoCreateRoot, MetaValueTrue)
	if val {
		o.autoCreateRoot = 1
	}
	return o
}

func (o *UsersBiz) Create(ctx context.Context, p *User) (user *User, err error) {
	o.log.Debug(ctx, "CreateUser", "param", p)
	err = o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		user, err = o.repo.Create(ctx, p)
		if err != nil {
			return err
		}
		return keto.DefaultAccessMgr().CreateUsers(ctx, user.GetUid())
	})

	// if autoCreateRoot is set, try to make it the super user
	if atomic.LoadInt32(&o.autoCreateRoot) != 1 {
		return
	}
	atomic.StoreInt32(&o.autoCreateRoot, 0)

	// double check
	val, err := o.meta.Is(ctx, MetaNameAutoCreateRoot, MetaValueTrue)
	if !val {
		if err != nil {
			o.log.Error(ctx, "failed to query autoCreateRoot", err)
		}
		return user, nil
	}

	// change the flag
	err = o.meta.Set(ctx, MetaNameAutoCreateRoot, MetaValueFalse)
	if err != nil {
		o.log.Error(ctx, "failed to set autoCreateRoot", err)
		return user, nil
	}

	// make it the super user
	user.Role = SysRoleRoot
	_, err = o.repo.Update(ctx, user, field.NewMask(User_Role))
	if err != nil {
		o.log.Error(ctx, "failed to set first user to super user", err)
	}
	err = keto.DefaultAccessMgr().AddSysGroupMembers(ctx, user.Role, user.GetUid())
	if err != nil {
		o.log.Error(ctx, "failed to grant super user privileges", err)
	}
	return user, nil
}

func (o *UsersBiz) BatchCreateUsers(ctx context.Context, p []*User) (users []*User, err error) {
	o.log.Debug(ctx, "BatchCreateUser", "param", p)
	err = o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		users, err = o.repo.BatchCreate(ctx, p)
		if err != nil {
			return err
		}
		uids := kslice.Map(func(v *User) string { return v.GetUid() }, users)
		return keto.DefaultAccessMgr().CreateUsers(ctx, uids...)
	})
	return
}

func (o *UsersBiz) Update(ctx context.Context, p *User, fldMask *FieldMask) (*User, error) {
	o.log.Debug(ctx, "UpdateUser", "param", p)
	return o.repo.Update(ctx, p, fldMask)
}

func (o *UsersBiz) Get(ctx context.Context, uid string) (*User, error) {
	return o.repo.GetByID(ctx, kid.ParseID(uid))
}

func (o *UsersBiz) GetBy(ctx context.Context, identity string) (idType string, user *User, err error) {
	idType, id := ParseUserIdentity(identity)
	if idType == "" {
		return "", nil, errors.NewErrInvalidField(errors.WithModel("user"), errors.WithFields("id"))
	}
	user, err = o.repo.GetBy(ctx, idType, id)
	return
}

func (o *UsersBiz) Delete(ctx context.Context, uid string) error {
	o.log.Debug(ctx, "DeleteUser", "uid", uid)
	return o.repo.DeleteByID(ctx, kid.ParseID(uid))
}

func (o *UsersBiz) List(ctx context.Context, filter *UserListFilter) ([]*User, error) {
	users, _, err := o.repo.List(ctx, filter, repo.Pager{Page: filter.Page, Pagesz: filter.Pagesz})
	return users, err
}

// Count returns the number of items matching the filter, ignore the Page and Pagesz.
func (o *UsersBiz) Count(ctx context.Context, filter *UserListFilter) (int64, error) {
	return o.repo.Count(ctx, filter)
}

func (o *UsersBiz) Login(ctx context.Context, req *iam.LoginRequest) (*LoginReply, error) {
	// query user
	user, err := o.repo.GetBy(ctx, req.IdType.String(), req.Identity)
	if err != nil && !errors.IsNotFound(err) {
		fmt.Println("---> login failed", "req", req)
		return nil, err
	}

	switch req.AuthType {
	case iam.LoginRequest_AuthType_authcode:
	default:
		return nil, errors.NewErrInvalidField(errors.WithFields("auth_type"))
	}

	// query, verify and update auth code
	userID := user.GetID()
	code, err := o.repo.GetAuthcode(ctx, userID, req.Identity)
	if err != nil {
		if errors.IsNotFound(err) {
			err = errors.NewErrInvalidField(errors.WithFields("authcode"))
		}
		return nil, err
	}
	if !code.IsValid() || code.Code != req.Credential {
		return nil, errors.NewErrInvalidField(errors.WithMessage("credential is expired or wrong"),
			errors.WithFields("authcode"))
	}

	// create user if needed and mark auth code as used
	if err := o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		fields := []string{Authcode_Status}
		if code.UserID == 0 {
			user = &User{Role: TeamRoleMember, Agreement: int(req.Agreement)}
			switch req.IdType.String() {
			case IDTypeEmail:
				user.Email = req.Identity
			case IDTypePhone:
				user.Phone = req.Identity
			}

			user, err = o.Create(ctx, user)
			if err != nil {
				return err
			}
			code.UserID = user.ID
			fields = append(fields, Authcode_UserID)
		} else {
			if ag := int(req.Agreement); ag > user.Agreement {
				user.Agreement = ag
				_, err = o.Update(ctx, user, field.NewMask(User_Agreement))
				if err != nil {
					return fmt.Errorf("failed to update user agreement: %w", err)
				}
			}
		}

		code.Status = AuthcodeStatusUsed
		_, err = o.repo.UpdateAuthcode(ctx, code, field.NewMask(fields...))
		return err
	}); err != nil {
		return nil, err
	}

	// grant access token
	token, expires, err := jwt.Default().New(user.GetUid())
	if err != nil {
		return nil, err
	}
	return &LoginReply{
		User:      user,
		Token:     token,
		ExpiresAt: expires,
	}, nil
}

func (o *UsersBiz) SendAuthCode(ctx context.Context, req *iam.SendAuthCodeRequest) (*iam.SendAuthCodeReply, error) {
	// check if this is a fake phone number
	isFakeNumber := false
	minInterval := time.Minute
	code := kid.NewAuthcode()
	fmt.Println("---check fake login ---")
	spew.Dump(req.Receiver)
	spew.Dump(o.fakeLogins)
	for _, l := range o.fakeLogins {
		if l.PhoneRegexp.MatchString(req.Receiver) {
			spew.Dump(l.PhoneRegexp.FindAllString(req.Receiver, -1))
			code = l.AuthCode
			isFakeNumber = true
			minInterval = 0 // no cool down time for fakeAuthCode
		}
	}
	if !isFakeNumber && strings.HasPrefix(req.Receiver, "+0") {
		return nil, errors.NewErrInvalidField(errors.WithFields("receiver"))
	}
	spew.Dump(isFakeNumber)

	// query user
	idType := AuthChannelToIDType(req.Channel.String())
	user, err := o.repo.GetBy(ctx, idType, req.Receiver)
	if err != nil && (!errors.IsNotFound(err) || isFakeNumber) { // fake users need to be created manually
		return nil, err
	}
	if !isFakeNumber && user != nil && lo.Contains(user.Tags, UserTagPinAuthcode) {
		code = ""
	}

	// create auth authcode
	authcode := &Authcode{
		UserID:    user.GetID(),
		Channel:   req.Channel.String(),
		Receiver:  req.Receiver,
		Code:      code,
		Status:    AuthcodeStatusValid,
		ExpiredAt: time.Now().Add(10 * time.Minute),
	}
	authcode, err = o.repo.SaveAuthcode(ctx, authcode, minInterval)
	if err != nil {
		return nil, err
	}

	// send auth code
	if !isFakeNumber {
		fmt.Println("---> send  real code !!!")
		err = notice.Send(ctx, req.Receiver, req.Channel.String(), "", map[string]string{
			"code": authcode.Code, "sequence": strconv.Itoa(authcode.Sequence)})
		if err != nil {
			return nil, err
		}
	}

	ag := 0
	if user != nil {
		ag = user.Agreement
	}
	return &iam.SendAuthCodeReply{
		Agreement: int32(ag),
		Sequence:  int32(authcode.Sequence),
	}, nil
}

func init() { ruleng.Register(MetaNameTrafficControl) }

// GetRoutingCfg returns a string used for later requests routing.
func (o *UsersBiz) GetRoutingCfg(ctx context.Context, user *User) string {
	// load the routing rule
	rule, err := o.meta.Get(ctx, MetaNameTrafficControl)
	if err != nil && !errors.IsNotFound(err) {
		o.log.Error(ctx, "failed to get traffic control", err)
	}

	env := map[string]any{"user": user}
	r, err := ruleng.Eval[string](ctx, MetaNameTrafficControl, rule, env)
	if err != nil {
		o.log.Error(ctx, "failed to get user routing config", err, "rule", rule)
	}
	return r
}

func (o *UsersBiz) UpdateLastLoginTime(ctx context.Context, userID int64) error {
	return o.repo.UpdateLastLoginTime(ctx, userID)
}
