# SubjectSet

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Namespace** | **string** | Namespace of the Subject Set | 
**Object** | **string** | Object of the Subject Set | 
**Relation** | **string** | Relation of the Subject Set | 

## Methods

### NewSubjectSet

`func NewSubjectSet(namespace string, object string, relation string, ) *SubjectSet`

NewSubjectSet instantiates a new SubjectSet object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewSubjectSetWithDefaults

`func NewSubjectSetWithDefaults() *SubjectSet`

NewSubjectSetWithDefaults instantiates a new SubjectSet object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetNamespace

`func (o *SubjectSet) GetNamespace() string`

GetNamespace returns the Namespace field if non-nil, zero value otherwise.

### GetNamespaceOk

`func (o *SubjectSet) GetNamespaceOk() (*string, bool)`

GetNamespaceOk returns a tuple with the Namespace field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetNamespace

`func (o *SubjectSet) SetNamespace(v string)`

SetNamespace sets Namespace field to given value.


### GetObject

`func (o *SubjectSet) GetObject() string`

GetObject returns the Object field if non-nil, zero value otherwise.

### GetObjectOk

`func (o *SubjectSet) GetObjectOk() (*string, bool)`

GetObjectOk returns a tuple with the Object field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetObject

`func (o *SubjectSet) SetObject(v string)`

SetObject sets Object field to given value.


### GetRelation

`func (o *SubjectSet) GetRelation() string`

GetRelation returns the Relation field if non-nil, zero value otherwise.

### GetRelationOk

`func (o *SubjectSet) GetRelationOk() (*string, bool)`

GetRelationOk returns a tuple with the Relation field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRelation

`func (o *SubjectSet) SetRelation(v string)`

SetRelation sets Relation field to given value.



[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


