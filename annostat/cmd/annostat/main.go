package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"os"

	"annostat/api/client"
	"annostat/internal/conf"
	"annostat/internal/data"
	"annostat/internal/event"
	"annostat/internal/mq"
	"annostat/internal/ostore"
	"annostat/workflow"

	ilog "gitlab.rp.konvery.work/platform/pkg/log"
	"gitlab.rp.konvery.work/platform/pkg/otel"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/env"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
)

var (
	// Name is the name of the compiled software.
	Name string = "annostat"
	// flagconf is the config flag.
	flagconf string

	id, _ = os.Hostname()
)

func init() {
	flag.StringVar(&flagconf, "conf", "../../configs", "config path, eg: -conf config.yaml")
}

type App struct {
	*kratos.App
	activities *workflow.Activities
	evbiz      *event.EventsBiz
}

func newApp(logger log.Logger, gs *grpc.Server, hs *http.Server, a *workflow.Activities, evbiz *event.EventsBiz) *App {
	return &App{
		App: kratos.New(
			kratos.ID(id),
			kratos.Name(Name),
			kratos.Version(conf.Version),
			kratos.Metadata(map[string]string{}),
			kratos.Logger(logger),
			kratos.Server(
				gs,
				hs,
			),
		),
		activities: a,
		evbiz:      evbiz,
	}
}

func main() {
	flag.Parse()
	c := config.New(
		config.WithSource(
			file.NewSource(flagconf),
			env.NewSource(),
		),
		config.WithResolver(CustomResolver),
	)
	defer c.Close()

	if err := c.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}
	logger := ilog.GetLogger(&ilog.Config{
		Level:  bc.Otel.Log.Level,
		Format: bc.Otel.Log.Format,
	})
	log.SetLogger(logger)
	if bc.Mq.Redis == nil {
		bc.Mq.Redis = bc.Data.Redis
	}

	switch flag.Arg(0) {
	case "migrate":
		data.Migrate(bc.Data.Database, flag.Arg(1))
		return
	case "emit": // emit an event
		mq.Init(bc.Mq, logger)
		err := mq.PublishEvt(context.Background(), flag.Arg(1), flag.Arg(2), json.RawMessage(flag.Arg(3)))
		if err != nil {
			fmt.Println("failed to emit an event:", err)
		}
		return
	}

	ostore.Init(bc.ObjectStorage)
	mq.Init(bc.Mq, logger)

	client.Init(&bc)
	app, cleanup, err := wireApp(bc.Server, bc.Data, logger)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	otelcfg := otel.NewOtelCfg(bc.Otel.GetLog(), bc.Otel.GetMetrics(), bc.Otel.GetTracing())
	shutdown, err := otel.InitOtel(Name, conf.Version, otelcfg, logger)
	if err != nil {
		panic(err)
	}
	defer shutdown()

	workflow.Init(bc.Temporal, logger)
	if !bc.Temporal.DisableWorker {
		go workflow.StartWorker(app.activities)
	}

	ctx, stopmq := context.WithCancel(context.Background())
	defer stopmq()
	err = mq.StartConsumer(ctx, bc.Mq.Consumer)
	if err != nil {
		panic(fmt.Errorf("failed to start consumer: %w", err))
	}

	// start and wait for stop signal
	log.Infof("[main] current version: %s", conf.Version)
	if err := app.Run(); err != nil {
		panic(err)
	}
}
