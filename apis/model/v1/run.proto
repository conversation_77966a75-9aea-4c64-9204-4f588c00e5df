syntax = "proto3";

package model.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";
import "types/tag.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/model/v1;model";

service Runs {
  rpc Create (CreateRunRequest) returns (Run) {
    option (google.api.http) = {
      post: "/v1/runs"
      body: "*"
    };
  }

  rpc Update (UpdateRunRequest) returns (Run) {
    option (google.api.http) = {
      patch: "/v1/runs/{uid}"
      body: "data"
    };
  }

  rpc Start (StartRunRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/runs/{uid}/start"
      body: "*"
    };
  }

  rpc Finish (FinishRunRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/runs/{uid}/finish"
      body: "*"
    };
  }

  rpc Cancel (GetRunRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/runs/{uid}/cancel"
      body: "*"
    };
  }

  rpc Delete (DeleteRunRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/runs/{uid}"
    };
  }

  rpc Get (GetRunRequest) returns (Run) {
    option (google.api.http) = {
      get: "/v1/runs/{uid}"
    };
  }

  rpc List (ListRunRequest) returns (ListRunReply) {
    option (google.api.http) = {
      get: "/v1/runs"
    };
  }

  rpc AddTag (types.TagRequest) returns (types.TagList) {
    option (google.api.http) = {
      put: "/v1/runs/{uid}/tag"
      body: "tags"
    };
  }

  rpc DeleteTag (types.TagRequest) returns (types.TagList) {
    option (google.api.http) = {
      delete: "/v1/runs/{uid}/tag"
    };
  }
}

message CreateRunRequest {
  option (openapi.v3.schema) = {
    // required: ["name", "type", "dataset"]
  };

  // [mandatory for create] run name
  string name = 1;
  // [mandatory for create] model type: od/segmentation
  string type = 2;
  // dataset used for this run
  Run.Dataset dataset = 3;
  // define the conditions to stop training
  string target = 4;
  // maximum credits the run can consume
  int32 max_credits = 5;
  // base model name and version
  string base_model = 6;
  // tags attached to the run
  repeated string tags = 7;
  // training pipeline image
  Run.Pipeline pipeline = 8;
  // uid of the model uploaded by this run
  string model_uid = 9;
  // artifacts uploaded by this run
  string artifacts = 10;
}

// message CompleteRunRequest {
//   option (openapi.v3.schema) = {
//     required: ["uid", "model_uid"]
//   };
//   // run uid
//   string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
//   // uid of the model uploaded by this run
//   string model_uid = 2 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|^$"}];
// }

message UpdateRunRequest {
  option (openapi.v3.schema) = {
    required: ["uid", "data", "fields"]
  };

  string uid = 1;
  CreateRunRequest data = 2;
  repeated string fields = 3;
}

message Run {
  option (openapi.v3.schema) = {
    required: ["uid", "name", "type", "operator_uid", "dataset", "state", "created_at"]
  };

  // run uid
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // run name
  string name = 2;
  // model type: od/segmentation
  string type = 3;
  // operator uid
  string operator_uid = 4 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|^$"}];

  message Dataset {
    option (openapi.v3.schema) = {
      required: ["uris", "size_gb"]
    };

    // uris of the dataset
    repeated string uris = 1 [(validate.rules).repeated.items.string = {uri_ref: true}];
    // dataset size in giga bytes
    int32 size_gb = 2;
  }
  // dataset used for this run
  Dataset dataset = 5;

  // define the conditions to stop training
  string target = 6;
  // maximum credits the run can consume
  int32 max_credits = 7;
  // base model name and version
  string base_model = 8;
  // tags attached to the run
  repeated string tags = 9;

  message Pipeline {
    string image = 1;
    repeated string command = 2;
    // repeated string args = 3;
  }
  // training pipeline
  Pipeline pipeline = 10;

  message State {
    enum Enum {
      unspecified = 0;
      unstart = 1;
      running = 2;
      finished = 3;
      failed = 4;
      canceled = 5;
    }
  }
  // state of the run
  State.Enum state = 11;
  // fail reason
  string fail_reason = 12;
  // uid of the model uploaded by this run
  string model_uid = 13 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|^$"}];

  google.protobuf.Timestamp started_at = 14;
  google.protobuf.Timestamp ended_at = 15;
  google.protobuf.Timestamp updated_at = 16;
  google.protobuf.Timestamp created_at = 17;

  // artifacts uploaded by this run
  string artifacts = 18;
}

message RunFilter {
  // operator uid
  string operator_uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|^$"}];
  // type
  string type = 2;
  // states
  repeated Run.State.Enum states = 3;
  // name pattern
  string name_pattern = 4;
  // base model pattern
  string base_model_pattern = 5;
  // attached tags
  repeated string tags = 6;
}

message GetRunRequest {
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
}

message DeleteRunRequest {
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$|byfilter"}];
  RunFilter filter = 2;
}

message ListRunRequest {
  int32 pagesz = 1 [(validate.rules).int32 = {gte:0, lte: 100}];
  // An opaque pagination token returned from a previous call.
  // An empty token denotes the first page.
  string page_token = 2;

  RunFilter filter = 3;
}

message ListRunReply {
  option (openapi.v3.schema) = {
    required: ["runs", "next_page_token"]
  };

  repeated Run runs = 1;
  // An opaque pagination token, if not empty, to be used to fetch the next page of results
  string next_page_token = 2;
  // total number of items found; valid only in the first page reply.
  int32 total = 3;
}

message StartRunRequest {
  message Option {
    enum Enum {
      // only start unstart ones
      unspecified = 0;
      // start unstart/failed/canceled ones
      retry_nok = 1;
    }
  }

  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // start option
  Option.Enum option = 2 [(validate.rules).enum = {defined_only: true}];
}

message FinishRunRequest{
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // fail reason when the run should be failed; empty means the run should be finished successfully
  string fail_reason = 2;
}
