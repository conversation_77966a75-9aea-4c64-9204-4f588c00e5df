apiVersion: v1
kind: Service
metadata:
  name: {{ include "appx.fullname" . }}
  labels:
    {{- include "appx.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.httpPort }}
      targetPort: http
      protocol: TCP
      name: http
    - port: {{ .Values.service.grpcPort }}
      targetPort: grpc
      protocol: TCP
      name: grpc
    # - port: {{ .Values.service.metrics.port }}
    #   targetPort: {{ .Values.service.metrics.name }}
    #   protocol: TCP
    #   name: {{ .Values.service.metrics.name }}
  selector:
    {{- include "appx.selectorLabels" . | nindent 4 }}
