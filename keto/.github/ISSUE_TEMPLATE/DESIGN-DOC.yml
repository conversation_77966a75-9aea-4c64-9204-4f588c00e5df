# AUTO-GENERATED, DO NOT EDIT!
# Please edit the original at https://github.com/ory/meta/blob/master/templates/repository/common/.github/ISSUE_TEMPLATE/DESIGN-DOC.yml

description:
  "A design document is needed for non-trivial changes to the code base."
labels:
  - rfc
name: "Design Document"
body:
  - attributes:
      value: |
        Thank you for writing this design document. 

        One of the key elements of Ory's software engineering culture is the use of defining software designs through design docs. These are relatively informal documents that the primary author or authors of a software system or application create before they embark on the coding project. The design doc documents the high level implementation strategy and key design decisions with emphasis on the trade-offs that were considered during those decisions.

        <PERSON><PERSON> is leaning heavily on [Google's design docs process](https://www.industrialempathy.com/posts/design-docs-at-google/)
        and [Golang Proposals](https://github.com/golang/proposal).

        Writing a design doc before contributing your change ensures that your ideas are checked with
        the community and maintainers. It will save you a lot of time developing things that might need to be changed
        after code reviews, and your pull requests will be merged faster.
    type: markdown
  - attributes:
      label: "Preflight checklist"
      options:
        - label:
            "I could not find a solution in the existing issues, docs, nor
            discussions."
          required: true
        - label:
            "I agree to follow this project's [Code of
            Conduct](https://github.com/ory/keto/blob/master/CODE_OF_CONDUCT.md)."
          required: true
        - label:
            "I have read and am following this repository's [Contribution
            Guidelines](https://github.com/ory/keto/blob/master/CONTRIBUTING.md)."
          required: true
        - label:
            "This issue affects my [Ory Network](https://www.ory.sh/) project."
        - label:
            "I have joined the [Ory Community Slack](https://slack.ory.sh)."
        - label:
            "I am signed up to the [Ory Security Patch
            Newsletter](https://ory.us10.list-manage.com/subscribe?u=ffb1a878e4ec6c0ed312a3480&id=f605a41b53)."
    id: checklist
    type: checkboxes
  - attributes:
      description: |
        This section gives the reader a very rough overview of the landscape in which the new system is being built and what is actually being built. This isn’t a requirements doc. Keep it succinct! The goal is that readers are brought up to speed but some previous knowledge can be assumed and detailed info can be linked to. This section should be entirely focused on objective background facts.
      label: "Context and scope"
    id: scope
    type: textarea
    validations:
      required: true

  - attributes:
      description: |
        A short list of bullet points of what the goals of the system are, and, sometimes more importantly, what non-goals are. Note, that non-goals aren’t negated goals like “The system shouldn’t crash”, but rather things that could reasonably be goals, but are explicitly chosen not to be goals. A good example would be “ACID compliance”; when designing a database, you’d certainly want to know whether that is a goal or non-goal. And if it is a non-goal you might still select a solution that provides it, if it doesn’t introduce trade-offs that prevent achieving the goals.
      label: "Goals and non-goals"
    id: goals
    type: textarea
    validations:
      required: true

  - attributes:
      description: |
        This section should start with an overview and then go into details.
        The design doc is the place to write down the trade-offs you made in designing your software. Focus on those trade-offs to produce a useful document with long-term value. That is, given the context (facts), goals and non-goals (requirements), the design doc is the place to suggest solutions and show why a particular solution best satisfies those goals.

        The point of writing a document over a more formal medium is to provide the flexibility to express the problem at hand in an appropriate manner. Because of this, there is no explicit guidance on how to actually describe the design.
      label: "The design"
    id: design
    type: textarea
    validations:
      required: true

  - attributes:
      description: |
        If the system under design exposes an API, then sketching out that API is usually a good idea. In most cases, however, one should withstand the temptation to copy-paste formal interface or data definitions into the doc as these are often verbose, contain unnecessary detail and quickly get out of date. Instead, focus on the parts that are relevant to the design and its trade-offs.
      label: "APIs"
    id: apis
    type: textarea

  - attributes:
      description: |
        Systems that store data should likely discuss how and in what rough form this happens. Similar to the advice on APIs, and for the same reasons, copy-pasting complete schema definitions should be avoided. Instead, focus on the parts that are relevant to the design and its trade-offs.
      label: "Data storage"
    id: persistence
    type: textarea

  - attributes:
      description: |
        Design docs should rarely contain code, or pseudo-code except in situations where novel algorithms are described. As appropriate, link to prototypes that show the feasibility of the design.
      label: "Code and pseudo-code"
    id: pseudocode
    type: textarea

  - attributes:
      description: |
        One of the primary factors that would influence the shape of a software design and hence the design doc, is the degree of constraint of the solution space.

        On one end of the extreme is the “greenfield software project”, where all we know are the goals, and the solution can be whatever makes the most sense. Such a document may be wide-ranging, but it also needs to quickly define a set of rules that allow zooming in on a manageable set of solutions.

        On the other end are systems where the possible solutions are very well defined, but it isn't at all obvious how they could even be combined to achieve the goals. This may be a legacy system that is difficult to change and wasn't designed to do what you want it to do or a library design that needs to operate within the constraints of the host programming language.

        In this situation, you may be able to enumerate all the things you can do relatively easily, but you need to creatively put those things together to achieve the goals. There may be multiple solutions, and none of them are great, and hence such a document should focus on selecting the best way given all identified trade-offs.
      label: "Degree of constraint"
    id: constrait
    type: textarea

  - attributes:
      description: |
        This section lists alternative designs that would have reasonably achieved similar outcomes. The focus should be on the trade-offs that each respective design makes and how those trade-offs led to the decision to select the design that is the primary topic of the document.

        While it is fine to be succinct about a solution that ended up not being selected, this section is one of the most important ones as it shows very explicitly why the selected solution is the best given the project goals and how other solutions, that the reader may be wondering about, introduce trade-offs that are less desirable given the goals.

      label: Alternatives considered
    id: alternatives
    type: textarea
