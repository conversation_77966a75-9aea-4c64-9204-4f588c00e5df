package biz

import (
	"anno/internal/protoconv"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
)

type JobState int32

const (
	JobStateUnknown   JobState = -1
	JobStateUnstart   JobState = 0 // phase "unstart"
	JobStateDoing     JobState = 1 // phase "doing"
	JobStateChecking  JobState = 2 // phase background "checking"
	JobStateCommitted JobState = 3 // phase "committed"
	JobStateFinished  JobState = 4 // job "finished"
)

func (o JobState) String() string { return JobStateNames[o] }
func (o JobState) Value() int32   { return int32(o) }

var JobStateNames = map[JobState]string{
	JobStateUnstart:   "unstart",
	JobStateDoing:     "doing",
	JobStateChecking:  "checking",
	JobStateCommitted: "committed",
	JobStateFinished:  "finished",
}
var jobStateValues = map[string]JobState{}

func ToJobState(name string) JobState {
	if v, ok := jobStateValues[name]; ok {
		return v
	}
	return JobStateUnknown
}

func init() {
	for k, v := range JobStateNames {
		jobStateValues[v] = k
	}
}

const (
	ForceActionPrefix = "force_"

	JobActionClaim        = "claim"                              // claim a job for oneself
	JobActionAssign       = "assign"                             // assign a job to a user
	JobActionSubmit       = "submit"                             // submit annotations
	JobActionGiveup       = "giveup"                             // job executor give up a job
	JobActionAccept       = "accept"                             // reviewer accept job annotations
	JobActionReject       = "reject"                             // reviewer reject job annotations
	JobActionForceReject  = ForceActionPrefix + JobActionReject  //
	JobActionRecycle      = "recycle"                            // reviewer reject job annotations and put it back to the initial stage
	JobActionForceRecycle = ForceActionPrefix + JobActionRecycle //
	JobActionTimeout      = "timeout"                            // assignment timeout
	JobActionEnd          = "end"                                // termination
	// JobActionCascadeTerm  = "cascade_term"                       // cascaded termination from lot
	// JobActionFix          = "fix"                                // reviewer fix job annotations and accept
)

var (
	// JobRealSubmitActions means that executors/reviewers move (submit/accept/reject) a job to a higher/lower phase
	JobRealSubmitActions = []string{JobActionSubmit, JobActionAccept, JobActionReject}
)

func IsJobActionReject(act string) bool { return act == JobActionReject || act == JobActionForceReject }
func IsJobActionRecycle(act string) bool {
	return act == JobActionRecycle || act == JobActionForceRecycle
}

var ReviewDecisionConv = protoconv.NewProtoEnumStringConverter(anno.ReviewJobRequest_Decision_Enum_value, anno.ReviewJobRequest_Decision_accept, JobActionAccept)

func JobReviewDecisionToAction(d anno.ReviewJobRequest_Decision_Enum) string {
	return ReviewDecisionConv.FromProto(d)
}

func init() {
	// ensure conversion between proto values and biz values work well
	ReviewDecisionConv.MustConv(true, JobActionAccept, JobActionReject, JobActionRecycle, "unspecified")
}

func FromBizLotState(state LotState) anno.Lot_State_Enum {
	return LotStateConv.ToProto(state)
}

func ToBizLotState(state anno.Lot_State_Enum) LotState {
	return LotStateConv.FromProto(state)
}

var LotStateConv = protoconv.NewProtoEnumConverter(anno.Lot_State_Enum_value, func(v string) LotState { return LotState(v) },
	anno.Lot_State_ongoing, LotStateOngoing)

func init() {
	// ensure conversion between proto values and biz values work well
	LotStateConv.MustConv(false,
		LotStateUnspecified,
		LotStateUnstart,
		LotStateInitializing,
		LotStateOngoing,
		LotStateFinished,
		LotStatePaused,
		LotStateCanceled,
	)
}

type ClaimJobPrefer string

const (
	ClaimJobPreferUnspecified   ClaimJobPrefer = "unspecified"
	ClaimJobPreferRejectedFirst ClaimJobPrefer = "rejected_first"
)

func ClaimJobPreferFromPb(prefer anno.ClaimJobRequest_Prefer_Enum) ClaimJobPrefer {
	return ClaimJobPrefer(prefer.String())
}
