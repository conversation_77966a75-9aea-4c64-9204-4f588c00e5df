# Keto 源码分析

## 一、命名空间生效机制分析

基于源码分析，我可以详细解释命名空间是如何生效的：

1. **命名空间加载流程**

当 Keto 启动时，会通过 `internal/driver/config/provider.go` 中的 `NamespaceManager()` 方法加载命名空间：

```go
func (k *Config) NamespaceManager() (namespace.Manager, error) {
    // 1. 获取命名空间配置
    nnCfg, err := k.namespaceConfig()
    if err != nil {
        return nil, err
    }

    // 2. 创建命名空间管理器
    k.nm, err = nnCfg.newManager()(ctx, k)
    if err != nil {
        return nil, err
    }

    return k.nm, nil
}
```

2. **命名空间管理器类型**

Keto 支持三种类型的命名空间管理器：

1. **内存管理器** (`memoryNamespaceManager`)
   ```go
   type memoryNamespaceManager struct {
       byName map[string]*namespace.Namespace
       sync.RWMutex
   }
   ```
   - 用于直接加载命名空间定义
   - 存储在内存中

2. **文件监视器** (`NamespaceWatcher`)
   ```go
   type NamespaceWatcher struct {
       sync.RWMutex
       namespaces map[string]*NamespaceFile
       logger     *logrusx.Logger
       target     string
   }
   ```
   - 监视命名空间文件的变化
   - 自动重新加载更新的定义

3. **OPL 配置监视器** (`oplConfigWatcher`)
   ```go
   type oplConfigWatcher struct {
       logger *logrusx.Logger
       target string
       files  configFiles
       memoryNamespaceManager
   }
   ```
   - 专门用于处理 Ory Permission Language 配置
   - 支持从文件、HTTP 或 base64 加载配置

3. **命名空间解析流程**

当 `namespaces.keto.ts` 被加载时：

1. 文件被读取并解析为 AST
2. 解析器检查命名空间定义的有效性
3. 生成内部命名空间对象
4. 存储在命名空间管理器中

4. **实际应用示例**

让我们看一个具体的例子：

```typescript
// 1. 定义命名空间
class IamRole implements Namespace {
  related: {
    parents: IamGroup[]
    policies: IamPolicy[]
    perms: (IamPerm | SubjectSet<IamRole, "perms">)[]
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      IamRole:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm))
  }
}

// 2. 定义关系元组
IamRole:admin#perms@IamRole:iam.owner#perms
IamRole:admin#policies@IamPolicy:sys/admin
```

当进行权限检查时：

1. 系统首先从命名空间管理器中获取 `IamRole` 的定义
2. 解析 `check` 方法的逻辑
3. 检查关系元组：
   - 检查全局权限 (`IamRole:"*"`)
   - 检查父组权限
   - 检查策略权限

5. **命名空间和关系元组的关系**

- 命名空间定义（`namespaces.keto.ts`）：
  - 定义权限模型的结构
  - 定义权限检查的逻辑
  - 存储在内存中，用于权限检查

- 关系元组（`tuples.txt`）：
  - 定义具体的权限关系
  - 存储在数据库中
  - 用于实际的权限检查

6. **数据流向**

```
namespaces.keto.ts -> 解析器 -> AST -> 命名空间管理器（内存）
tuples.txt -> 解析器 -> 关系元组 -> 数据库
```

7. **权限检查流程**

```go
// 1. 获取命名空间定义
ns, err := nm.GetNamespaceByName(ctx, namespace)
if err != nil {
    return nil, nil
}

// 2. 获取关系定义
rel, err := ASTRelationFor(ctx, nm, namespace, relation)
if err != nil {
    return nil, err
}

// 3. 检查权限
if rel != nil {
    // 使用关系定义进行权限检查
    return checkPermission(ctx, rel, subject)
}

// 4. 如果没有关系定义，检查关系元组
return checkRelationTuple(ctx, namespace, object, relation, subject)
```

所以，`namespaces.keto.ts` 和 `tuples.txt` 是相互配合的：
- `namespaces.keto.ts` 定义了权限模型和检查逻辑
- `tuples.txt` 定义了具体的权限关系
- 两者共同工作来实现完整的权限系统

这种设计使得权限系统既灵活（可以定义复杂的权限逻辑）又高效（可以快速检查具体的权限关系）。

## 二、源码解析和校验流程分析

让我们用一个具体的示例来详细分析源码的解析和校验流程。

假设我们有这样一个权限检查场景：

```typescript
// 1. 命名空间定义 (namespaces.keto.ts)
class IamRole implements Namespace {
  related: {
    parents: IamGroup[]
    policies: IamPolicy[]
    perms: (IamPerm | SubjectSet<IamRole, "perms">)[]
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      IamRole:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm))
  }
}

// 2. 关系元组 (tuples.txt)
IamRole:admin#perms@IamRole:iam.owner#perms
IamRole:admin#policies@IamPolicy:sys/admin
```

让我们逐步分析源码中的解析和校验流程：

1. **命名空间文件加载**

首先，在 `internal/driver/config/namespace_watcher.go` 中：

```go
func NewNamespaceWatcher(ctx context.Context, l *logrusx.Logger, target string) (*NamespaceWatcher, error) {
    nw := NamespaceWatcher{
        logger:     l,
        target:     target,
        namespaces: make(map[string]*NamespaceFile),
    }

    return &nw, watchTarget(ctx, target, &nw, l)
}

func (nw *NamespaceWatcher) handleChange(e *watcherx.ChangeEvent) {
    nw.Lock()
    defer nw.Unlock()

    // 读取并解析命名空间文件
    n := nw.readNamespaceFile(e.Reader(), e.Source())
    if n == nil {
        return
    } else if n.namespace == nil {
        // 解析失败，回滚到之前的版本
        if existing, ok := nw.namespaces[e.Source()]; ok {
            existing.Contents = n.Contents
        } else {
            nw.namespaces[e.Source()] = n
        }
    } else {
        nw.namespaces[e.Source()] = n
    }
}
```

2. **命名空间解析**

在 `internal/schema/parser.go` 中：

```go
func (p *parser) parseClass() {
    var name string
    p.match(&name, "implements", "Namespace", "{")
    p.namespace = namespace{Name: name}

    for !p.fatal {
        switch item := p.next(); {
        case item.Typ == itemBraceRight:
            p.namespaces = append(p.namespaces, p.namespace)
            return
        case item.Val == "related":
            p.parseRelated()
        case item.Val == "permits":
            p.parsePermits()
        case item.Typ == itemSemicolon:
            continue
        default:
            p.addFatal(item, "expected 'permits' or 'related', got %q", item.Val)
            return
        }
    }
}

func (p *parser) parseRelated() {
    p.match(":", "{")
    for !p.fatal {
        switch item := p.next(); item.Typ {
        case itemSemicolon:
            continue
        case itemBraceRight:
            return
        case itemIdentifier, itemStringLiteral:
            relation := item.Val
            var types []ast.RelationType
            p.match(":")

            // 解析关系类型
            switch item := p.next(); {
            case item.Val == "Array":
                p.match("<")
                types = append(types, p.parseTypeUnion(itemAngledRight)...)
            case item.Val == "SubjectSet":
                types = append(types, p.matchSubjectSet())
                p.match("[", "]", optional(","))
            case item.Typ == itemParenLeft:
                types = append(types, p.parseTypeUnion(itemParenRight)...)
                p.match("[", "]", optional(","))
            default:
                types = append(types, ast.RelationType{Namespace: item.Val})
                p.addCheck(checkNamespaceExists(item))
                p.match("[", "]", optional(","))
            }

            p.namespace.Relations = append(p.namespace.Relations, ast.Relation{
                Name:   relation,
                Types:  types,
                Params: []string{"subject"},
            })
        }
    }
}
```

3. **类型检查**

在 `internal/schema/typechecks.go` 中：

```go
func (p *parser) typeCheck() {
    for _, check := range p.checks {
        check(p)
    }
}

// 检查命名空间是否存在
func checkNamespaceExists(namespace item) typeCheck {
    return func(p *parser) {
        if _, ok := namespaceQuery(p.namespaces).find(namespace.Val); ok {
            return
        }
        p.addErr(namespace, "namespace %q was not declared", namespace.Val)
    }
}

// 检查命名空间是否有指定的关系
func checkNamespaceHasRelation(namespace, relation item) typeCheck {
    return func(p *parser) {
        if n, ok := namespaceQuery(p.namespaces).find(namespace.Val); ok {
            if _, ok := relationQuery(n.Relations).find(relation.Val); ok {
                return
            }
            p.addErr(relation,
                "namespace %q did not declare relation %q",
                namespace.Val, relation.Val)
            return
        }
        p.addErr(namespace, "namespace %q was not declared", namespace.Val)
    }
}
```

4. **关系元组解析**

在 `internal/relationtuple/definitions.go` 中：

```go
type RelationTuple struct {
    Namespace string    `json:"namespace"`
    Object    uuid.UUID `json:"object"`
    Relation  string    `json:"relation"`
    Subject   Subject   `json:"subject"`
    Formula   *ast.Relation `json:"-"`
}

type Manager interface {
    GetRelationTuples(ctx context.Context, query *RelationQuery, options ...x.PaginationOptionSetter) ([]*RelationTuple, string, error)
    ExistsRelationTuples(ctx context.Context, query *RelationQuery) (bool, error)
    WriteRelationTuples(ctx context.Context, rs ...*RelationTuple) error
    DeleteRelationTuples(ctx context.Context, rs ...*RelationTuple) error
    DeleteAllRelationTuples(ctx context.Context, query *RelationQuery) error
    TransactRelationTuples(ctx context.Context, insert []*RelationTuple, delete []*RelationTuple) error
}
```

5. **权限检查实现**

在 `internal/check/engine.go` 中：

```go
func (e *Engine) checkIsAllowed(ctx context.Context, r *relationTuple, restDepth int, skipDirect bool) checkgroup.CheckFunc {
    if restDepth <= 0 {
        return checkgroup.UnknownMemberFunc
    }

    // 获取关系定义
    relation, err := e.astRelationFor(ctx, r)
    if err != nil {
        g.Add(checkgroup.ErrorFunc(err))
        return g.CheckFunc()
    }

    // 处理参数
    ctx, err = e.processArgs(ctx, r, relation)
    if err != nil {
        g.Add(checkgroup.ErrorFunc(err))
        return g.CheckFunc()
    }

    // 尝试系统缓存
    if e.trySysCache(ctx, g, r, restDepth) {
        return g.CheckFunc()
    }

    // 检查权限
    if hasRewrite {
        // 检查主体集重写
        g.Add(e.checkSubjectSetRewrite(ctx, r, relation.SubjectSetRewrite, restDepth))
    }
    if (!strictMode || !hasRewrite) && !skipDirect && !fakeRelation {
        // 直接检查
        g.Add(e.checkDirect(r, restDepth-1))
    }
    if canHaveSubjectSets && !fakeRelation {
        // 展开主体集
        g.Add(e.checkExpandSubject(r, restDepth-1))
    }

    return g.CheckFunc()
}
```

6. **实际执行流程**

当执行权限检查时：

1. 首先从 `namespaces.keto.ts` 加载命名空间定义：
   ```go
   // internal/driver/config/namespace_watcher.go
   n := nw.readNamespaceFile(e.Reader(), e.Source())
   ```

2. 解析命名空间定义：
   ```go
   // internal/schema/parser.go
   p.parseClass()
   p.parseRelated()
   p.parsePermits()
   ```

3. 进行类型检查：
   ```go
   // internal/schema/typechecks.go
   p.typeCheck()
   ```

4. 解析关系元组：
   ```go
   // internal/relationtuple/definitions.go
   rt := &RelationTuple{
       Namespace: "IamRole",
       Object:    uuid.NewV5(networkID, "admin"),
       Relation:  "perms",
       Subject:   &SubjectSet{...},
   }
   ```

5. 执行权限检查：
   ```go
   // internal/check/engine.go
   e.checkIsAllowed(ctx, r, restDepth, skipDirect)
   ```

这个流程展示了 Keto 如何将 TypeScript 定义转换为可执行的权限检查逻辑，以及如何将关系元组转换为数据库记录。整个过程是类型安全的，并且支持复杂的权限继承和检查逻辑。 