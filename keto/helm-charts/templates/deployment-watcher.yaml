{{- if .Values.watcher.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "keto.fullname" . }}-watcher
  {{- if .Release.Namespace }}
  namespace: {{ .Release.Namespace }}
  {{- end }}
  labels:
    app.kubernetes.io/name: {{ include "keto.name" . }}-watcher
    app.kubernetes.io/instance: {{ .Release.Name }}
    {{- with .Values.deployment.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  annotations:
    {{- with .Values.deployment.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "keto.name" . }}-watcher
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "keto.name" . }}-watcher
        app.kubernetes.io/instance: {{ .Release.Name }}
        {{- with .Values.deployment.labels }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with .Values.watcher.podMetadata.labels }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      annotations:
        {{- with .Values.watcher.podMetadata.annotations }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      automountServiceAccountToken: {{ .Values.deployment.automountServiceAccountToken }}
      serviceAccountName: {{ include "keto.serviceAccountName" . }}-watcher
      containers:
        - name: watcher
          {{- if .Values.securityContext }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          {{- end }}
          image: {{ .Values.watcher.image }}
          command:
            - /bin/bash
            - -c
            - |
              {{- .Files.Get "files/watch.sh" | printf "%s" | nindent 14 }}
          env:
            - name: NAMESPACE
              value: {{ .Release.Namespace | quote }}
            - name: WATCH_FILE
              value: {{ .Values.watcher.mountFile | quote }}
            - name: LABEL_SELECTOR
              value: '{{ $.Values.watcher.watchLabelKey }}={{ include "keto.name" . }}'
          volumeMounts:
          {{- with .Values.deployment.extraVolumeMounts }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
      volumes:
        {{- if .Values.deployment.extraVolumes }}
          {{- toYaml .Values.deployment.extraVolumes | nindent 8 }}
        {{- end }}
{{- end }}
