// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: types/range.proto

package types

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Range with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Range) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Range with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in RangeMultiError, or nil if none found.
func (m *Range) ValidateAll() error {
	return m.validate(true)
}

func (m *Range) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return RangeMultiError(errors)
	}

	return nil
}

// RangeMultiError is an error wrapping multiple validation errors returned by
// Range.ValidateAll() if the designated constraints aren't met.
type RangeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RangeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RangeMultiError) AllErrors() []error { return m }

// RangeValidationError is the validation error returned by Range.Validate if
// the designated constraints aren't met.
type RangeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RangeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RangeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RangeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RangeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RangeValidationError) ErrorName() string { return "RangeValidationError" }

// Error satisfies the builtin error interface
func (e RangeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRange.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RangeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RangeValidationError{}

// Validate checks the field values on RangeInt32 with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RangeInt32) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RangeInt32 with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RangeInt32MultiError, or
// nil if none found.
func (m *RangeInt32) ValidateAll() error {
	return m.validate(true)
}

func (m *RangeInt32) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return RangeInt32MultiError(errors)
	}

	return nil
}

// RangeInt32MultiError is an error wrapping multiple validation errors
// returned by RangeInt32.ValidateAll() if the designated constraints aren't met.
type RangeInt32MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RangeInt32MultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RangeInt32MultiError) AllErrors() []error { return m }

// RangeInt32ValidationError is the validation error returned by
// RangeInt32.Validate if the designated constraints aren't met.
type RangeInt32ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RangeInt32ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RangeInt32ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RangeInt32ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RangeInt32ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RangeInt32ValidationError) ErrorName() string { return "RangeInt32ValidationError" }

// Error satisfies the builtin error interface
func (e RangeInt32ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRangeInt32.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RangeInt32ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RangeInt32ValidationError{}
