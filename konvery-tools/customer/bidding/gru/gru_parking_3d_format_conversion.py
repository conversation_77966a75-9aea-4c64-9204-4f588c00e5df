import zipfile
import os
import json
import shutil
import sys

sys.path.append('.')
from customer.common import tools


def run(input_dir, output_dir):
    file_counter = 0

    # unzip file
    unzip_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_unzip')
    result_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_result')
    if os.path.exists(unzip_dir):
        shutil.rmtree(unzip_dir)
    with zipfile.ZipFile(input_dir, 'r') as zip_ref:
        zip_ref.extractall(unzip_dir)

    tools.check_dir(result_dir)
    ds_dirs = tools.find_dir_by_pre_name(unzip_dir, "ds")
    ds_dirs = [ds for ds in ds_dirs if "MAC" not in ds]
    for ds in ds_dirs:
        # ds_na8yun8etbrr2yngxtbr
        ds_dir = ds
        for num_folder in tools.listdir(ds_dir):
            # 2309065303
            num_folder_dir = os.path.join(ds_dir, num_folder)
            for chn_folder in tools.listdir(num_folder_dir):
                # 3D泊车点云检测
                chn_folder_dir = os.path.join(num_folder_dir, chn_folder)
                for test_folder in tools.listdir(chn_folder_dir):
                    # test_image_desen_20230831
                    # create clip result folder
                    result_clip_dir = os.path.join(result_dir, test_folder)
                    os.mkdir(result_clip_dir)
                    test_folder_dir = os.path.join(chn_folder_dir, test_folder)
                    for label in tools.listdir(test_folder_dir):
                        # label
                        label_dir = os.path.join(test_folder_dir, label)
                        for json_file in tools.listdir(label_dir):
                            # 1690016202_600000.json
                            json_dir = os.path.join(label_dir, json_file)
                            json_data = json.load(open(json_dir))

                            try:
                                # construct result dict
                                result_dict = {
                                    'from': 'semi-outsource'
                                }
                                object_list = []

                                for item in json_data['lidar']:
                                    object_dict = {
                                        'LABEL': 'HUMAN',
                                        'bounding_box': {
                                            'height': item['annotation']['data']['dimension'][
                                                'h'],
                                            'length': item['annotation']['data']['dimension'][
                                                'l'],
                                            'width': item['annotation']['data']['dimension'][
                                                'w'],
                                        },
                                        'heading': item['annotation']['data']['rotation']['z'],
                                        'id': item['track_id'],
                                        'position': {
                                            'x': item['annotation']['data']['position']['x'],
                                            'y': item['annotation']['data']['position']['y'],
                                            'z': item['annotation']['data']['position']['z'],
                                        },
                                        'type': item['class'],
                                    }

                                    # fix additional_properties key error
                                    additional_properties = 'ADDITIONAL_PROPERTIES'
                                    if item['class'] == 'BARRIER_GATE_CLOSE':
                                        additional_properties = 'CAMERA_NOT_VISIBLE'

                                    if len(item['attrs'][additional_properties]) > 1 or \
                                            item['attrs'][additional_properties][0] != 'NORMAL':
                                        if len(item['attrs'][additional_properties]) > 1 and \
                                                'NORMAL' in item['attrs'][
                                            additional_properties]:
                                            # if it's a list and NORMAL is an element, remove NORMAL from the list
                                            object_dict['attribute'] = item['attrs'][
                                                additional_properties].remove('NORMAL')
                                        else:
                                            object_dict['attribute'] = item['attrs'][
                                                additional_properties]

                                    if item['class'] == 'SMALLCAR' or item['class'] == 'BIGCAR':
                                        object_dict['rearview_mirror'] = \
                                            item['attrs']['MIRROR_FOLDING'][0]

                                    object_list.append(object_dict)

                                result_dict['objects'] = object_list

                            except KeyError as e:
                                result_dict = {
                                    'from': 'semi-outsource',
                                    'objects': []
                                }

                            # save the result
                            target_directory = os.path.join(output_dir,
                                                            os.path.basename(result_clip_dir),
                                                            json_file.split('.json')[
                                                                0] + '.txt')
                            os.makedirs(
                                os.path.join(output_dir, os.path.basename(result_clip_dir)),
                                exist_ok=True)

                            with open(target_directory, 'w') as f:
                                json.dump(result_dict, f, indent=4)
                            file_counter += 1

    trans_result = {
        "code": 0,
        "output": result_dir,
        "err_msg": '成功',
        "summary": {
            '生成文件数': file_counter,
        }
    }

    for key in trans_result:
        print(key + ': ' + str(trans_result[key]))

    print(trans_result)
    return trans_result


if __name__ == "__main__":
    args = tools.get_args()
    run(args.input, args.out)
