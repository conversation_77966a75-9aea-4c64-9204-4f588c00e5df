# 工作流系统分析

## 1. 整体架构

项目使用 Temporal 作为工作流引擎，主要由两部分组成：
- 工作流定义
- 活动实现

## 2. 工作流定义

完整的工作流步骤定义在 `workflow/preparedata/wf.yaml` 中：

```yaml
steps:
  - PrepareDataWrapup (defer=true)  # 收尾工作
  - PrepareDataFetch                # 数据获取
  - PrepareDataParseMeta           # 元数据解析
  - PrepareDataConverterScript     # 转换脚本获取
  - WFMkdir (when: convScriptURI != "") # 临时目录创建
  - convertData (kubejob)          # 数据转换
  - WFRemovePath                   # 临时目录清理
  - PrepareDataTransform           # 数据转换
  - PrepareDataStandardize         # 数据标准化
  - PrepareDataDetectDataType      # 数据类型检测
  - PrepareDataValidateRawdatas    # 原始数据验证
  - PrepareDataParseRawdatas       # 原始数据解析
  - PrepareDataParseAnnos          # 标注解析
  - PrepareDataUploadRawdatas      # 原始数据上传
```

## 3. 主要活动实现

### a) 数据获取 (PrepareDataFetch)
- 从指定 URI 下载数据文件
- 支持 zip 文件解压
- 支持断点续传
- 使用心跳机制报告进度
- 关键函数：`defaultFetch` 和 `extractZipFile`

### b) 元数据解析 (PrepareDataParseMeta)
- 解析 meta.json 文件
- 更新基本数据信息（类型、样式等）
- 处理数据依赖（baseOn）
- 关键函数：`LoadJSONFile` 和 `PrepareDataParseMeta`

### c) 数据转换 (PrepareDataTransform)
- 执行类型特定的数据转换
- 支持断点续传
- 使用心跳机制报告进度
- 支持多种转换器：qcraft、ecarx、singlestream、varylidar、bosch
- 关键函数：`Transform`

### d) 数据标准化 (PrepareDataStandardize)
- 标准化数据格式
- 创建数据目录
- 移动文件到数据目录
- 删除不必要的文件
- 标准化 PCD 文件
- 关键函数：`standardizeDataFormat`

### e) 数据类型检测 (PrepareDataDetectDataType)
- 分析文件类型（图像、点云等）
- 推断数据类型（Image、Pointcloud、Fusion2D、Fusion3D）
- 支持断点续传
- 关键函数：`calElemType` 和 `calDataType`

### f) 数据验证 (PrepareDataValidateRawdatas)
- 验证原始数据完整性
- 检查文件命名规范
- 收集验证错误
- 支持断点续传
- 关键函数：`validate` 和 `validateWithErrorHandlers`

### g) 原始数据解析 (PrepareDataParseRawdatas)
- 解析原始数据文件
- 提取元数据
- 创建 Rawdata 记录
- 支持断点续传
- 关键函数：`parseRawdatas`

### h) 标注解析 (PrepareDataParseAnnos)
- 解析标注文件
- 支持多种标注格式
- 创建 Rawdata 记录
- 支持断点续传
- 关键函数：`parseElemAnnos`

### i) 数据上传 (PrepareDataUploadRawdatas)
- 上传原始数据到存储服务
- 支持并发上传
- 使用心跳机制报告进度
- 处理上传失败重试
- 关键函数：`uploadRawdataWorker`

### j) 收尾工作 (PrepareDataWrapup)
- 清理临时文件
- 更新数据状态
- 更新订单状态
- 处理错误信息
- 关键函数：`PrepareDataWrapup`

## 4. 工作流执行流程

### a) 初始化阶段
```go
// 创建工作流实例
spec := newWorkflow()
spec.Workflow.Args["data"] = kparser.ToJSONStr(data)
_, err := ktwf.StartWorkflow(ctx, WorkflowID(data.GetUid()), spec, true)
```

### b) 数据获取阶段
```go
// 下载数据文件
err = defaultFetch(ctx, data, dir, startIdx, hb)
// 解压 zip 文件（如果适用）
err = extractZipFile(ctx, file, dir, hb)
```

### c) 数据处理阶段
```go
// 解析元数据
_, meta, err := LoadJSONFile[annofeed.Meta](dir, common.MetaFile)
// 转换数据
dataType, err := style.Transform(ctx, data, dir, startIdx, activity.RecordHeartbeat)
// 标准化数据
err = o.standardizeDataFormat(ctx, dir)
```

### d) 数据验证阶段
```go
// 验证数据
summaryErrors := rawdataValGroup.validate(elem.Index, name, elem.IdxInClip)
// 处理验证错误
err := validateWithErrorHandlers(summaryErrors, data.Source.E.ErrorHandlers)
```

### e) 数据上传阶段
```go
// 并发数据上传
for i := 0; i < maxUploader; i++ {
    wg.Add(1)
    go o.uploadRawdataWorker(ctx, basedir, uploadRawdataChan, progress, &wg)
}
```

## 5. 关键特性

### a) 断点续传
- 使用 `startIdx` 跟踪处理进度
- 通过心跳机制保存状态
- 支持从断点恢复

### b) 错误处理
- 区分可重试和不可重试错误
- 详细错误信息记录
- 错误状态更新到数据库

### c) 并发处理
- 支持并发上传
- 使用通道和等待组进行并发管理
- 进度同步和状态报告

### d) 状态管理
- 数据状态跟踪
- 订单状态更新
- 详细进度报告

## 6. 监控和日志
- 记录关键操作
- 通过心跳机制报告进度
- 收集和报告错误信息

## 7. 总结

这个工作流系统设计良好，涵盖了数据处理的整个生命周期，具有良好的可扩展性和容错性。每个活动都是独立的，可以单独测试和维护，同时通过工作流引擎进行协调和状态管理。系统的主要特点包括：

1. 模块化设计：每个活动都是独立的模块，便于维护和扩展
2. 可靠性：通过断点续传和错误处理机制确保数据处理的可靠性
3. 性能优化：支持并发处理和进度报告
4. 可维护性：详细的日志记录和状态管理
5. 灵活性：支持多种数据格式和转换方式

这个系统能够有效地处理各种数据处理任务，从数据获取到最终上传，每个步骤都有清晰的职责和错误处理机制。 