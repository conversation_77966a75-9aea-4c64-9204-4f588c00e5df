// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: anno/v1/specgrant.proto

package anno

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationSpecgrantsCreateSpecgrant = "/anno.v1.Specgrants/CreateSpecgrant"
const OperationSpecgrantsDeleteSpecgrant = "/anno.v1.Specgrants/DeleteSpecgrant"
const OperationSpecgrantsListSpecgrant = "/anno.v1.Specgrants/ListSpecgrant"

type SpecgrantsHTTPServer interface {
	CreateSpecgrant(context.Context, *CreateSpecgrantRequest) (*Specgrant, error)
	DeleteSpecgrant(context.Context, *DeleteSpecgrantRequest) (*emptypb.Empty, error)
	ListSpecgrant(context.Context, *ListSpecgrantRequest) (*ListSpecgrantReply, error)
}

func RegisterSpecgrantsHTTPServer(s *http.Server, srv SpecgrantsHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/specgrants", _Specgrants_CreateSpecgrant0_HTTP_Handler(srv))
	r.DELETE("/v1/specgrants", _Specgrants_DeleteSpecgrant0_HTTP_Handler(srv))
	r.GET("/v1/specgrants", _Specgrants_ListSpecgrant0_HTTP_Handler(srv))
}

func _Specgrants_CreateSpecgrant0_HTTP_Handler(srv SpecgrantsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateSpecgrantRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSpecgrantsCreateSpecgrant)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateSpecgrant(ctx, req.(*CreateSpecgrantRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Specgrant)
		return ctx.Result(200, reply)
	}
}

func _Specgrants_DeleteSpecgrant0_HTTP_Handler(srv SpecgrantsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteSpecgrantRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSpecgrantsDeleteSpecgrant)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteSpecgrant(ctx, req.(*DeleteSpecgrantRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Specgrants_ListSpecgrant0_HTTP_Handler(srv SpecgrantsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListSpecgrantRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSpecgrantsListSpecgrant)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListSpecgrant(ctx, req.(*ListSpecgrantRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListSpecgrantReply)
		return ctx.Result(200, reply)
	}
}

type SpecgrantsHTTPClient interface {
	CreateSpecgrant(ctx context.Context, req *CreateSpecgrantRequest, opts ...http.CallOption) (rsp *Specgrant, err error)
	DeleteSpecgrant(ctx context.Context, req *DeleteSpecgrantRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	ListSpecgrant(ctx context.Context, req *ListSpecgrantRequest, opts ...http.CallOption) (rsp *ListSpecgrantReply, err error)
}

type SpecgrantsHTTPClientImpl struct {
	cc *http.Client
}

func NewSpecgrantsHTTPClient(client *http.Client) SpecgrantsHTTPClient {
	return &SpecgrantsHTTPClientImpl{client}
}

func (c *SpecgrantsHTTPClientImpl) CreateSpecgrant(ctx context.Context, in *CreateSpecgrantRequest, opts ...http.CallOption) (*Specgrant, error) {
	var out Specgrant
	pattern := "/v1/specgrants"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSpecgrantsCreateSpecgrant))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SpecgrantsHTTPClientImpl) DeleteSpecgrant(ctx context.Context, in *DeleteSpecgrantRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/specgrants"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSpecgrantsDeleteSpecgrant))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SpecgrantsHTTPClientImpl) ListSpecgrant(ctx context.Context, in *ListSpecgrantRequest, opts ...http.CallOption) (*ListSpecgrantReply, error) {
	var out ListSpecgrantReply
	pattern := "/v1/specgrants"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSpecgrantsListSpecgrant))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
