package middleware

import (
	"context"

	kerrors "github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/middleware"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"google.golang.org/grpc/status"
)

// HandleError wraps unnormalized errors to avoid disclosing internal information.
func HandleError() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			reply, err = handler(ctx, req)
			if err == nil {
				return
			}

			var e *errors.Error
			if errors.As(err, &e) {
				cause := e.Unwrap()
				if errors.Is(cause, context.Canceled) {
					err = kerrors.ClientClosed("client-closed", "client closed request").
						WithCause(cause).WithMetadata(e.Metadata)
				}
				return
			}
			if _, ok := err.(interface{ GRPCStatus() *status.Status }); ok {
				return
			}

			err = errors.NewErrServerError().WithCause(err)
			return
		}
	}
}
