// source: anno/v1/labelcls.proto
package data

import (
	"context"

	"anno/internal/biz"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

type labelclzRepo struct {
	data *Data
	log  *log.Helper
}

func NewLabelclzRepo(data *Data, logger log.Logger) biz.LabelclzRepo {
	return &labelclzRepo{data: data, log: log.NewHelper(logger)}
}

func (o *labelclzRepo) Create(ctx context.Context, p *biz.Labelcls) (*biz.Labelcls, error) {
	return Create(ctx, o.data, p)
}

func (o *labelclzRepo) Update(ctx context.Context, p *biz.Labelcls, fldMask *biz.FieldMask) (*biz.Labelcls, error) {
	return Update(ctx, o.data, p, biz.LabelclsUpdatableFlds, fldMask)
}

func (o *labelclzRepo) loadAssociations(ctx context.Context, data *Data, mod *biz.Labelcls) error {
	return nil
}

func (o *labelclzRepo) GetByID(ctx context.Context, id int64) (*biz.Labelcls, error) {
	return GetByID[biz.Labelcls](ctx, o.data, id, o.loadAssociations)
}

func (o *labelclzRepo) GetByUid(ctx context.Context, uid string) (*biz.Labelcls, error) {
	return GetByUid[biz.Labelcls](ctx, o.data, uid, o.loadAssociations)
}

func (o *labelclzRepo) DeleteByID(ctx context.Context, id int64) error {
	return Delete(ctx, o.data, &biz.Labelcls{ID: id})
}

func (o *labelclzRepo) DeleteByUid(ctx context.Context, uid string) error {
	return Delete(ctx, o.data, &biz.Labelcls{Name: uid})
}

func (o *labelclzRepo) buildListQuery(ctx context.Context, p *biz.LabelclsListFilter) *gorm.DB {
	q := o.data.WithCtx(ctx)
	if len(p.Uids) > 0 {
		q = q.Where(biz.LabelclsSfldName+" IN ?", p.Uids)
	}
	if p.NamePattern != "" {
		q = q.Where(biz.LabelclsSfldName+" LIKE ?", "%"+p.NamePattern+"%")
	}
	return q
}

func (o *labelclzRepo) List(ctx context.Context, p *biz.LabelclsListFilter, pager biz.Pager) ([]*biz.Labelcls, error) {
	datas := []*biz.Labelcls{}
	q := o.buildListQuery(ctx, p).Order("id")
	if len(p.Uids) == 0 {
		q = q.Offset(pager.Offset()).Limit(pager.Pagesz)
	}
	err := q.Find(&datas).Error
	if err != nil {
		return nil, err
	}
	return datas, nil
}

func (o *labelclzRepo) Count(ctx context.Context, p *biz.LabelclsListFilter) (int, error) {
	mod := &biz.Labelcls{}
	var cnt int64
	q := o.buildListQuery(ctx, p)
	err := q.Model(mod).Count(&cnt).Error
	if err != nil {
		return 0, err
	}
	return int(cnt), nil
}
