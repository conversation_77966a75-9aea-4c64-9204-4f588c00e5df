import os
import sys
import numpy as np
from PIL import Image

sys.path.append('.')
from customer.common import tools
from pathlib import Path


camera_size = {
    "lb": (3840, 2160),
    "lf": (3840, 2160),
    "rb": (3840, 2160),
    "rf": (3840, 2160),
    "supplement1": (3840, 2160),
    "supplement2": (3840, 2160),
    "supplement3": (3840, 2160),
    "supplement4": (1920, 1200),
    "supplement5": (3840, 2160),
    "supplement6": (3840, 2160),
    "supplement7": (3840, 2160),
}

camera_name_mapping = {
    'camera_right_rear': 'rb',
    'camera_left_rear': 'lb',
    'camera_left_front': 'lf',
    'camera_rigth_front': 'rf',
    'camera_front_wide': 'supplement1',
    'camera_rear_up': 'supplement2',
    'camera_front_far': 'supplement3',
    'camera_front_fisheye': 'supplement4',
    'camera_left_fisheye': 'supplement5',
    'camera_right_fisheye': 'supplement6',
    'camera_rear_fisheye': 'supplement7'
}


def run(input_dir, output_dir):
    submap_file = os.path.join(input_dir, 'output_data', '3dannotation_data', 'submaplist.json')
    submaplist = tools.get_json_data(submap_file)
    lidar_file = tools.get_file_by_extension(os.path.join(input_dir, 'output_data', '3dannotation_data'), '.pcd')[0]
    frames = []
    for submap in submaplist:
        cameras = {}
        lidar_parts = Path(submap['lidar_path']).parts
        if not os.path.exists(os.path.join(input_dir, 'input_data', *lidar_parts[:3])):
            continue
        ele_out_dir = os.path.join(output_dir, 'input_data', *lidar_parts[:3])
        os.makedirs(ele_out_dir)
        frames.append(ele_out_dir)
        lidar2ego_pose = submap['lidar2ego_translation'] + submap['lidar2ego_rotation']
        lidar2ego_mat = tools.pose_to_mat(lidar2ego_pose)
        for cam_name, cam_params in submap['cams'].items():
            cur2center_mat = np.array(cam_params['curbase2centerbase_pose']).reshape(4, -1)
            cam_extrinsic = np.array(cam_params['cam_extrinsic'] + [[0, 0, 0, 1]])
            temp_mat = np.eye(4)
            temp_mat[1, 1] = -1
            temp_mat[2, 2] = -1
            extrinsic = cam_extrinsic @ temp_mat @ cur2center_mat @ np.linalg.inv(lidar2ego_mat)
            if cam_params['cam_distortion_type'] == 'fisheye':
                distortion = [2] + cam_params['cam_distortion'][:2] + cam_params['cam_distortion'][4:6]
            else:
                distortion = [0] + cam_params['cam_distortion'] + [0, 0]
            cameras[camera_name_mapping[cam_name]] = {
                "extrinsic": extrinsic.flatten().tolist(),
                "intrinsic": np.array(cam_params['cam_intrinsic']).flatten().tolist(),
                "distortion": distortion
            }
            if cam_params['img_path']:
                img_parts = Path(cam_params['img_path']).parts
                src_img_path = os.path.join(input_dir, 'input_data', cam_params['img_path'])
                dst_img_path = os.path.join(ele_out_dir, f'{img_parts[3]}.jpg')
                tools.copy_file(src_img_path, dst_img_path)
            else:
                black_img = Image.new('RGB', camera_size[camera_name_mapping[cam_name]], (0, 0, 0))
                black_img.save(os.path.join(ele_out_dir, camera_name_mapping[cam_name] + '.jpg'))
        lidar = {
            "viewpoint": lidar2ego_pose[:3] + [0, 0, 0, 1],
            "pose": lidar2ego_pose
        }
        config = {
            "meta": {
                "version": "v2"
            },
            "lidar": lidar,
            "cameras": cameras
        }
        tools.write_json_file(config, os.path.join(ele_out_dir, 'params.json'))

    first_frame = sorted(frames)[0]
    tools.copy_file(lidar_file.path, os.path.join(first_frame, 'lidar.pcd'))


if __name__ == '__main__':
    input_dir = '/Users/<USER>/Downloads/trn/全量/BEV_498'
    output_dir = '/Users/<USER>/Downloads/trn/全量/BEV_498_out'
    tools.check_dir(output_dir)
    run(input_dir, output_dir)
