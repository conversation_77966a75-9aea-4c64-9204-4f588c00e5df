package mq

import (
	"context"
	"time"
)

type EventHandler func(context.Context, *Event) error

// Middleware is a MQ consumer middleware.
type Middleware func(EventHandler) EventHandler

// Chain returns a Middleware that chains the given middlewares together.
func Chain(m ...Middleware) Middleware {
	return func(next EventHandler) EventHandler {
		for i := len(m) - 1; i >= 0; i-- {
			next = m[i](next)
		}
		return next
	}
}

// Log is a MQ consumer middleware that logs the events.
func Log(h EventHandler) EventHandler {
	return func(ctx context.Context, ev *Event) error {
		ts0 := time.Now()
		err := h(ctx, ev)
		args := []any{"type", ev.Type, "subtype", ev.Subtype, "body", ev.Body, "latency", time.Since(ts0).Seconds()}
		if err == nil {
			lg.Info(ctx, "successfully consumed an event", args...)
		} else {
			lg.Error(ctx, "failed to consume an event", err, args...)
		}
		return err
	}
}

func init() {
	SetConsumerMws(Log)
}
