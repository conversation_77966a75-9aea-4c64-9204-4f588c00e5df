version: '2'

services:
  mysql:
    image: mysql:5.7
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=pop_test
      - MYSQL_USER=pop
      - MYSQL_PASSWORD=pop
    #volumes:
      #- ./_vol/mysql:/docker-entrypoint-initdb.d
    ports:
      - "3307:3306"
    healthcheck:
      test: ["CMD", "mysqladmin" ,"ping", "-h", "localhost"]
      interval: 5s
      timeout: 5s
      retries: 10
      start_period: 3s

  postgres:
    image: postgres:10
    environment:
      - POSTGRES_DB=pop_test
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5433:5432"
    #volumes:
      #- ./_vol/postgres:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD", "pg_isready"]
      interval: 5s
      timeout: 5s
      retries: 10
      start_period: 3s

  cockroach:
    image: cockroachdb/cockroach:latest-v21.1
    ports:
      - "26258:26257"
      - "8081:8080"
    #volumes:
      #- ./_vol/cockroach:/cockroach/cockroach-data
    command: start-single-node --insecure
    healthcheck:
      test: ["CMD", "curl", "http://localhost:8080/health"]
      interval: 5s
      timeout: 5s
      retries: 10
      start_period: 3s

#  mssqlserver:
#    image: "microsoft/mssql-server-linux"
#    environment:
#      - SA_PASSWORD=Tt@12345678
#      - MSSQLSERVER_PASSWORD=Tt@12345678
#      - ACCEPT_EULA=Y
#    ports:
#      - "1433:1433"
