import os
import sys
import shutil
import numpy as np

sys.path.append('.')
from customer.common import tools


def construct_config(ins_dir, sensor_params_dir):
    ins_files = tools.get_json_files(ins_dir)
    sensor_params = dict()
    sensor_params_files = tools.get_json_files(sensor_params_dir)
    for sensor_params_file in sensor_params_files:
        if not sensor_params_file.name.startswith('cam_'):
            continue
        sensor_params_data = tools.get_json_data(sensor_params_file)
        distortion = sensor_params_data['distortion']
        intrinsic = sensor_params_data['intrinsic']
        rotation = np.array(sensor_params_data['rotation']).reshape(3, 3)
        translation = np.array(sensor_params_data['translation'])
        extrinsic = np.eye(4)
        extrinsic[:3, :3] = rotation
        extrinsic[:3, 3] = translation
        sensor_params[sensor_params_file.name.rsplit('_', 1)[0]] = {
            "camera_model": 'fisheye',
            "extrinsic": np.linalg.inv(extrinsic).tolist(),
            "fx": intrinsic[0],
            "fy": intrinsic[4],
            "cx": intrinsic[2],
            "cy": intrinsic[5],
            "k1": distortion[0],
            "k2": distortion[1],
            "k3": distortion[2],
            "k4": distortion[3],
        }
    poses = dict()
    for ins_file in sorted(ins_files, key=lambda x: x.name):
        ins_data = tools.get_json_data(ins_file)
        pose = tools.get_extrinsic_by_txyz_rxyz_opencv(
            np.array([ins_data['utm_position.x'], ins_data['utm_position.y'], ins_data['utm_position.z']]),
            np.array([ins_data['attitude.x'], ins_data['attitude.y'], ins_data['attitude.z']])
        )
        poses[os.path.basename(ins_file).rsplit('.', 1)[0]] = pose.T.flatten().tolist()
    config = {
        "data_type": "fusion_pointcloud",
        "sensor_params": sensor_params,
        "poses": poses,
        "camera": {
            "cam_front": "cam_front"
        }
    }
    return config


def run(input_dir):
    ins_dir = os.path.join(input_dir, 'Ins')
    params_dir = os.path.join(input_dir, 'sensors')
    config = construct_config(ins_dir, params_dir)
    tools.write_json_file(config, os.path.join(input_dir, 'config.json'))


if __name__ == '__main__':
    args = tools.get_args()
    run(args.input)
