syntax = "proto3";

package annout.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/annout/v1;annout";
option java_multiple_files = true;
option java_package = "annout.v1";

service Configs {
  rpc GetVersion (google.protobuf.Empty) returns (GetVersionReply) {
    option (google.api.http) = {
      get: "/v1/version"
    };
  }
}

message GetVersionReply {
  string version = 1;
}
