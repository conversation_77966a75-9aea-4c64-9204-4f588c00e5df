package data

import (
	"annout/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"gitlab.rp.konvery.work/platform/pkg/data"
	"gitlab.rp.konvery.work/platform/pkg/ktypes"
	"gorm.io/datatypes"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(NewData) // , NewReapersRepo)

type JSON = datatypes.JSON
type Data = data.Data

var Convert = data.Convert

func NewData(c *conf.Data, logger log.Logger) (*Data, func(), error) {
	return data.NewData(NewDataCfg(c), logger)
}

func NewDataCfg(c *conf.Data) *data.Config {
	return &data.Config{Database: NewDBCfg(c.Database), Redis: NewRedisCfg(c.Redis)}
}

func NewDBCfg(c *conf.Data_Database) *data.DBCfg {
	if c == nil {
		return nil
	}
	cfg := &data.DBCfg{
		Driver:   c.Driver,
		Source:   c.Source,
		Endpoint: c.Endpoint,
		Port:     c.Port,
		Database: c.Database,
		Username: c.Username,
		Password: c.Password,
		Options:  c.Options,

		MaxOpenConns: int(c.MaxOpenConns),
		MaxIdleConns: int(c.MaxIdleConns),
	}
	if t := c.ConnMaxIdleTime; t != nil {
		cfg.ConnMaxIdleTime = ktypes.PointerOf(t.AsDuration())
	}
	return cfg
}

func NewRedisCfg(c *conf.Data_Redis) *data.RedisCfg {
	if c == nil {
		return nil
	}
	rdc := &data.RedisCfg{
		Network: c.Network,
		Addr:    c.Addr,
	}
	if d := c.ReadTimeout; d != nil {
		rdc.ReadTimeout = ktypes.PointerOf(d.AsDuration())
	}
	if d := c.WriteTimeout; d != nil {
		rdc.WriteTimeout = ktypes.PointerOf(d.AsDuration())
	}
	return rdc
}
