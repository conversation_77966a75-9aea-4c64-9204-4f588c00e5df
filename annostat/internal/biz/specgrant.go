package biz

import (
	"time"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
)

// var SupportedSpecgrantItemType = []string{PermClsLot}

// specific grant
type Specgrant struct {
	ID         int64     `json:"id" gorm:"default:null"`
	GrantorUid string    `json:"grantor_id" gorm:"default:null"`
	GranteeUid string    `json:"grantee_id" gorm:"default:null"`
	ItemID     int64     `json:"item_id" gorm:"default:null"`
	ItemType   string    `json:"item_type" gorm:"default:null"`
	OrgUid     string    `json:"org_id" gorm:"default:null"` // item owner orgnization
	CreatedAt  time.Time `json:"created_at" gorm:"default:null"`
}

type SpecgrantSfld string

func (o SpecgrantSfld) String() string { return string(o) }

func (o SpecgrantSfld) WithTable() string { return "specgrants." + string(o) }

const SpecgrantTableName = "specgrants"

var (
	specgrant_             = field.RegObject(&Specgrant{})
	SpecgrantUpdatableFlds = field.NewModel(SpecgrantTableName, specgrant_)

	SpecgrantSfldID         = SpecgrantSfld(field.Sname(&specgrant_.ID))
	SpecgrantSfldGrantorUid = SpecgrantSfld(field.Sname(&specgrant_.GrantorUid))
	SpecgrantSfldGranteeUid = SpecgrantSfld(field.Sname(&specgrant_.GranteeUid))
	SpecgrantSfldOrgUid     = SpecgrantSfld(field.Sname(&specgrant_.OrgUid))
	SpecgrantSfldItemID     = SpecgrantSfld(field.Sname(&specgrant_.ItemID))
	SpecgrantSfldItemType   = SpecgrantSfld(field.Sname(&specgrant_.ItemType))
)

type SpecgrantFilter struct {
	GrantorUid string
	GranteeUid string
	OrgUid     string
	ItemIDs    []int64
	ItemType   string
}

func (o *SpecgrantFilter) Apply(tx Tx) Tx {
	tx = ApplyFieldFilter(tx, SpecgrantSfldGrantorUid.WithTable(), o.GrantorUid)
	tx = ApplyFieldFilter(tx, SpecgrantSfldGranteeUid.WithTable(), o.GranteeUid)
	tx = ApplyFieldFilter(tx, SpecgrantSfldOrgUid.WithTable(), o.OrgUid)
	tx = ApplyFieldFilter(tx, SpecgrantSfldItemType.WithTable(), o.ItemType)
	tx = ApplyFieldFilter(tx, SpecgrantSfldItemID.WithTable(), o.ItemIDs)
	return tx
}

type SpecgrantsRepo repo.GenericRepo[Specgrant]
