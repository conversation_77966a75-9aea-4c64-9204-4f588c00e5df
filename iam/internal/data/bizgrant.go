// source: gitlab.rp.konvery.work/platform/apis/iam/v1/bizgrant.proto
package data

import (
	"context"

	"iam/internal/biz"

	"gitlab.rp.konvery.work/platform/pkg/data"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type bizgrantsRepo struct {
	repo.GenericRepo[biz.Bizgrant]
	data *Data
}

func NewBizgrantsRepo(d *Data, logger log.Logger) biz.BizgrantsRepo {
	return &bizgrantsRepo{GenericRepo: data.NewGenericRepo[biz.Bizgrant](d, logger, biz.BizgrantTableName), data: d}
}

func (o *bizgrantsRepo) DeleteByFilter(ctx context.Context, f *biz.BizgrantFilter) error {
	mod := &biz.Bizgrant{}
	q := o.data.WithCtx(ctx)
	q = f.Apply(q)
	err := q.Delete(mod).Error
	return Convert(mod, err)
}
