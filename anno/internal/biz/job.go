// source: anno/v1/job.proto
package biz

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"google.golang.org/protobuf/types/known/timestamppb"
	"os"
	"time"

	"anno/api/client"
	"anno/api/interpolate"
	"anno/internal/mq"
	"anno/workflow/common"

	"github.com/samber/lo"
	"github.com/spf13/cast"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kset"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type Job struct {
	ID int64 `json:"id" gorm:"default:null"`
	// Uid         string    `json:"uid" gorm:"default:null"`
	LotID        int64     `json:"lot_id" gorm:"default:null"`
	IdxInLot     int32     `json:"idx_in_lot" gorm:"default:null"`
	Subtype      string    `json:"subtype" gorm:"default:null"`
	ElemsCnt     int32     `json:"elems_cnt" gorm:"default:null"`
	Phase        int32     `json:"phase" gorm:"default:null"` // starts from 1
	State        JobState  `json:"state" gorm:"default:null"`
	Cause        string    `json:"cause" gorm:"default:null"`
	InsCnt       int32     `json:"ins_cnt" gorm:"default:null"`
	InsTotal     int32     `json:"ins_total" gorm:"default:null"` // include interpolated objects
	ExecutorUid  string    `json:"executor_uid" gorm:"default:null"`
	LastExecutor string    `json:"last_executor" gorm:"default:null"`
	LastExecteam string    `json:"last_execteam" gorm:"default:null"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"default:null"`
	CreatedAt    time.Time `json:"created_at" gorm:"default:null"`

	Lot *Lot `json:"-" gorm:"-"`
	// LotUid   string    `json:"-" gorm:"-"`
	LotPhase *Lotphase `json:"-" gorm:"-"`
	Ally     *Jobally  `json:"-" gorm:"-"`
}

func (o *Job) GetID() int64 { return o.ID }
func (o *Job) EnsureID() {
	if o.ID == 0 {
		o.ID = kid.NewID()
	}
}
func (o *Job) GetUid() string { return kid.StringID(o.ID) }
func (o *Job) UidFld() string { panic("code error") }

func (o *Job) IsCompleted() bool { return o.State == JobStateFinished }

// func (o *Job) IsSubjob() bool    { return o.Subtype != "" }

func (o *Job) PhaseID() PhaseID {
	return PhaseID{
		LotID:    o.LotID,
		Number:   o.Phase,
		Execteam: o.LastExecteam,
		Subtype:  o.Subtype,
	}
}

const JobTableName = "jobs"

var (
	Job_             = field.RegObject(&Job{})
	JobUpdatableFlds = field.NewModel(JobTableName, Job_,
		"Phase", "State", "Cause", "InsCnt", "InsTotal", "ExecutorUid", "LastExecutor", "LastExecteam", "UpdatedAt")

	// field name in snake case
	JobSfldLotID        = field.Sname(&Job_.LotID)
	JobSfldIdxInLot     = field.Sname(&Job_.IdxInLot)
	JobSfldSubtype      = field.Sname(&Job_.Subtype)
	JobSfldPhase        = field.Sname(&Job_.Phase)
	JobSfldState        = field.Sname(&Job_.State)
	JobSfldCause        = field.Sname(&Job_.Cause)
	JobSfldInsCnt       = field.Sname(&Job_.InsCnt)
	JobSfldInsTotal     = field.Sname(&Job_.InsTotal)
	JobSfldExecutorUid  = field.Sname(&Job_.ExecutorUid)
	JobSfldLastExecutor = field.Sname(&Job_.LastExecutor)
	JobSfldLastExecteam = field.Sname(&Job_.LastExecteam)
	JobSfldUpdatedAt    = field.Sname(&Job_.UpdatedAt)
)

type JobStateTransition struct {
	Phase       int32
	State       JobState
	Cause       string
	InsCnt      ValidValue[int32]
	InsTotal    ValidValue[int32]
	Execteam    string
	ExecutorUid string
}

type JobAction struct {
	Action         string
	Details        *JoblogDetails
	NewState       JobStateTransition
	Annotations    ValidValue[Annotations]
	Comments       ValidValue[Comments]
	Jobsubmit      *EvtJobsubmit
	LotphaseID     int64
	AnnotationsURI ValidValue[string]
	CommentsURI    ValidValue[string]
}

type JobListFilter struct {
	LotID    int64
	IDs      []int64
	Phases   []int32
	States   []JobState
	Subtypes []string

	LastExecteam  string
	LastExecutors []string

	// if to include Jobally
	FullJob bool
}

type RespJobsPhaseCountByLotIds struct {
	LotId string
	Count int64
	Phase string
}

type JobsRepo interface {
	DoTx(ctx context.Context, fn func(ctx context.Context, tx Tx) error) (err error)

	Create(context.Context, *Job) (*Job, error)
	Update(context.Context, *Job, *FieldMask) (*Job, error)
	GetByID(ctx context.Context, id int64, withAlly bool) (*Job, error)
	GetJoballyByID(ctx context.Context, id int64) (*Jobally, error)
	DeleteByID(context.Context, int64) error
	List(context.Context, *JobListFilter, Pager) ([]*Job, error)
	Count(context.Context, *JobListFilter) (int, error)
	CountIns(context.Context, *JobListFilter) (cnt, total int, err error)
	HasHoldingJobs(ctx context.Context, userUids []string) (returnUserUids []string, err error)
	GetAnnos(context.Context, *JobListFilter, Pager) ([]*Jobally, string, error)
	Summary(context.Context, *JobListFilter) (phases []int, err error)
	ClearExecutor(ctx context.Context, lotID int64, phase int32, execteam string) error

	CreateJoblog(context.Context, *Joblog) (*Joblog, error)
	UpdateJoblog(context.Context, *Joblog, *FieldMask) (*Joblog, error)
	GetJoblog(ctx context.Context, logID int64) (*Joblog, error)
	ListJoblog(ctx context.Context, filter ListFilter, pager Pager) ([]*Joblog, error)
	CountJoblog(ctx context.Context, filter ListFilter) (int, error)
	GetPhaseLastExecutor(ctx context.Context, jobID int64, phase int) (user, team string, err error)
	GetPhaseExecutors(ctx context.Context, jobID []int64, maxPhase int32) ([]*Joblog, error)
	GetToPhaseLastExecutor(ctx context.Context, jobID int64, toPhase int) (user, team string, err error)
	ChangeJobState(ctx context.Context, job *Job, p *JobAction) (bool, error)
	GetExecutorTeamsFromLog(ctx context.Context, jobIDs []int64, executors ...string) (
		executorToTeam map[string]string, err error)

	BatchUpdate(ctx context.Context, mod any, updates map[string]any, ids []int64) error
	BatchCreateJoblog(ctx context.Context, logs []*Joblog) ([]*Joblog, error)

	GetLotLastJob(ctx context.Context, lotID int64) (*Job, error)
	GetJobExecutorTeam(ctx context.Context, jobID int64, executor string) (execteam string, err error)

	GetAssignedJob(ctx context.Context, lotID int64, subtype, executorUid string, prefer ClaimJobPrefer) (*Job, error)
	GetJobsToAssign(ctx context.Context, lotID int64, subtype, executorUid string) ([]*Job, error)
	SnatchJobs(ctx context.Context, lotID int64, subtype, executorUid string) ([]*Job, error)
	CheckRejectedJobs(ctx context.Context, executorUid string, lotIDs []int64) ([]int64, error)

	SaveJobDraft(ctx context.Context, jobID int64, p *JobDraft) error
	GetJobDraft(ctx context.Context, jobID int64) (*JobDraft, error)
	GetJobClips(ctx context.Context, jobIDs []int64) ([]*Jobelem, error)
	GetJobByClip(ctx context.Context, clip string) (*Jobelem, error)
	GetJobsPhaseCountByLotIds(ctx context.Context, lotIds []int64) ([]RespJobsPhaseCountByLotIds, error)
}

type JobsBiz struct {
	Repo    JobsRepo
	lotrepo LotsRepo
	bgtask  BackgroundTask
	log     *log.Helper

	strictCheck bool
}

func NewJobsBiz(repo JobsRepo, lotrepo LotsRepo, bgtask BackgroundTask, logger log.Logger) *JobsBiz {
	strictCheck := !cast.ToBool(os.Getenv("DBG_JOB_NO_STRICT_CHECK"))
	return &JobsBiz{Repo: repo, lotrepo: lotrepo, bgtask: bgtask, log: log.NewHelper(logger), strictCheck: strictCheck}
}

func (o *JobsBiz) StrictCheck() bool { return o.strictCheck }

func (o *JobsBiz) Create(ctx context.Context, p *Job) (job *Job, err error) {
	o.log.Info(ctx, "CreateJob", "job", p)

	p.State = JobStateUnstart
	err = o.Repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		job, err = o.Repo.Create(ctx, p)
		if err != nil {
			fmt.Println("anno-01-CreateJob err", err)
			return err
		}

		// TODO: what if it is a sub-job?
		return client.CreateAccessPolicies(ctx, PermClsJob, p.GetUid(), nil,
			[]string{LotScope(kid.StringID(job.LotID))})
	})
	if err != nil {
		return nil, err
	}
	return
}

func (o *JobsBiz) Update(ctx context.Context, p *Job, fldMask *FieldMask) (*Job, error) {
	o.log.Info(ctx, "UpdateJob", "job", p)
	return o.Repo.Update(ctx, p, fldMask)
}

func (o *JobsBiz) GetByID(ctx context.Context, id int64, withAlly bool, options ...JoballyOption) (*Job, error) {
	if len(options) > 0 {
		withAlly = true
	}

	job, err := o.Repo.GetByID(ctx, id, withAlly)
	if err != nil {
		return nil, err
	}

	for _, opt := range options {
		if err := opt(ctx, job); err != nil {
			return nil, err
		}
	}
	return job, nil
}

func (o *JobsBiz) GetByUid(ctx context.Context, uid string, withAlly bool, options ...JoballyOption) (*Job, error) {
	return o.GetByID(ctx, kid.ParseID(uid), withAlly, options...)
}

func (o *JobsBiz) GetJoballyByID(ctx context.Context, id int64) (*Jobally, error) {
	return o.Repo.GetJoballyByID(ctx, id)
}

func (o *JobsBiz) DeleteByID(ctx context.Context, id int64) error {
	o.log.Info(ctx, "DeleteByIDJob", "id", id)
	return o.Repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		if err := o.Repo.DeleteByID(ctx, id); err != nil {
			return err
		}
		return client.DeleteAccessPolicies(ctx, PermClsJob, kid.StringID(id))
	})
}

func (o *JobsBiz) DeleteByUid(ctx context.Context, uid string) error {
	o.log.Info(ctx, "DeleteByUidJob", "uid", uid)
	return o.DeleteByID(ctx, kid.ParseID(uid))
}

func (o *JobsBiz) List(ctx context.Context, filter *JobListFilter, pager Pager) ([]*Job, error) {
	// o.log.Info(ctx, "ListJob", "filter", filter)
	return o.Repo.List(ctx, filter, pager)
}

func (o *JobsBiz) Count(ctx context.Context, filter *JobListFilter) (int, error) {
	return o.Repo.Count(ctx, filter)
}

func (o *JobsBiz) CountIns(ctx context.Context, p *JobListFilter) (cnt, total int, err error) {
	return o.Repo.CountIns(ctx, p)
}

func (o *JobsBiz) GetAnnos(ctx context.Context, p *JobListFilter, pager Pager) ([]*Jobally, string, error) {
	annos, nextPageToken, err := o.Repo.GetAnnos(ctx, p, pager)
	if err != nil {
		return nil, "", fmt.Errorf("failed to query annos: %w", err)
	}
	// TODO: do interpolation if e.E.NeedInterpolation is true
	return annos, nextPageToken, nil
}

// Summary returns number of jobs at each phase. 1st element means phase 1.
// It may returns less phases if there are no jobs at last several phases.
// It may returns an extra last_phase+1 element to indicate finished jobs count.
func (o *JobsBiz) Summary(ctx context.Context, filter *JobListFilter) (phases []int, err error) {
	return o.Repo.Summary(ctx, filter)
}

func (o *JobsBiz) ListJoblogByID(ctx context.Context, jobID int64, pager Pager) ([]*Joblog, error) {
	return o.ListJoblog(ctx, &JoblogFilter{JobID: jobID}, pager)
}

func (o *JobsBiz) ListJoblog(ctx context.Context, filter *JoblogFilter, pager Pager) ([]*Joblog, error) {
	return o.Repo.ListJoblog(ctx, filter, pager)
}

func (o *JobsBiz) CountJoblog(ctx context.Context, jobID int64) (int, error) {
	return o.Repo.CountJoblog(ctx, &JoblogFilter{JobID: jobID})
}

func (o *JobsBiz) ChangeJobState(ctx context.Context, job *Job, p *JobAction, fillExecteam bool,
	actions ...TxAction) (success bool, err error) {
	t := time.Now()
	t1 := time.Now()
	fmt.Println("---> enter change job state : ", t1.String())
	o.log.Warn(ctx, "=========== review job step 7 进入change", "job:", job.ID, "time", time.Now())
	if executor := p.NewState.ExecutorUid; executor != "" && p.NewState.Execteam == "" && fillExecteam {
		p.NewState.Execteam, err = o.Repo.GetJobExecutorTeam(ctx, job.ID, executor)
		if err != nil {
			return false, err
		}
	}
	fmt.Println("---> annos valid: ", p.Annotations.Valid, ShouldSaveBigValuesInDB())
	fmt.Println("---> start upload annos: ", time.Now().Sub(t), time.Now().String())
	t = time.Now()
	if p.Annotations.Valid {
		annoData := &AnnotationData{
			ElementAnnos: p.Annotations.Value.E.GetElementAnnos(),
			JobAttrs:     p.Annotations.Value.E.GetAttrs(),
		}
		o.log.Warn(ctx, "=========== upload annotation 准备上传", "time", time.Now())
		if annoData.ElementAnnos != nil && len(annoData.ElementAnnos) > 0 {
			if lo.ContainsBy(annoData.ElementAnnos, func(item *anno.ElementAnno) bool {
				return item != nil && item.Segmentation3D != nil
			}) {
				fmt.Println("---> segmentation3d")
				ctx = WithSegmentation3dFlag(ctx, true)
			}
		}
		fmt.Println("---> hasSegmentation3d: ", GetSegmentation3dFlag(ctx))
		uploaded, err := job.UploadAnnotations(ctx, annoData, job.Lot.OrgUid, job.Lot.GetUid())

		if err != nil {
			o.log.Warn(ctx, "=========== upload annotation 上传失败", "err", err.Error(), "time", time.Now())
			return false, err
		}
		if uploaded {
			p.Annotations.Value = Annotations{} // clear annotation in DB
			p.AnnotationsURI = NewValidValue(job.Ally.AnnotationsURI)
		}
	}
	fmt.Println("---> end upload annos: ", time.Now().Sub(t), time.Now().String())

	fmt.Println("---> start upload comments: ", time.Now().Sub(t), time.Now().String())
	t = time.Now()
	if p.Comments.Valid {
		commentData := &CommentData{Comments: p.Comments.Value}
		o.log.Warn(ctx, "=========== upload comment 准备上传")
		uploaded, err := job.UploadComments(ctx, commentData, job.Lot.OrgUid, job.Lot.GetUid())
		if err != nil {
			o.log.Warn(ctx, "=========== upload comment 上传失败", "err", err.Error())
			return false, err
		}
		if uploaded {
			p.Comments.Value = Comments{} // clear comments in DB
			p.CommentsURI = NewValidValue(job.Ally.CommentsURI)
		}
	}
	fmt.Println("---> end upload comments: ", time.Now().Sub(t), time.Now().String())

	// clear existing job draft to prevent another executor from getting it
	if p.Action == JobActionClaim {
		o.log.Warn(ctx, "=========== 开始 进行保存 draft")
		if err := o.Repo.SaveJobDraft(ctx, job.ID, nil); err != nil {
			return false, err
		}
	}
	fmt.Println("---> start change db: ", time.Now().Sub(t), time.Now().String())
	t = time.Now()
	spew.Dump("change-state-ctx: ", ctx)
	op := UserFromCtx(ctx)
	err = o.Repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		spew.Dump("change-state-ctx-tx: ", ctx)
		o.log.Warn(ctx, "=========== 开始 执行数据库操作")
		jlog := &Joblog{
			JobID:         job.ID,
			OperatorUid:   op.GetUid(),
			OpOrgUid:      op.GetOrgUid(),
			Action:        p.Action,
			Details:       *serial.New(p.Details),
			FromPhase:     job.Phase,
			FromState:     job.State.Value(),
			ToPhase:       p.NewState.Phase,
			ToState:       p.NewState.State.Value(),
			ToExecutorUid: p.NewState.ExecutorUid,
			ToExecteam:    p.NewState.Execteam,
		}
		preCause := job.Cause

		o.log.Warn(ctx, "=========== 开始 执行数据库操作1")
		success, err = o.Repo.ChangeJobState(ctx, job, p)
		if !success {
			if err != nil {
				o.log.Warn(ctx, "=========== sueccess 失败", "err", err.Error())
			}
			return err
		}
		if job.State != JobStateChecking {
			jlog, err = o.Repo.CreateJoblog(ctx, jlog)
			if err != nil {
				return fmt.Errorf("failed to create joblog: %w", err)
			}
		}

		o.log.Warn(ctx, "=========== 开始 执行数据库操作2")
		if err := o.updateTeamClaimedJobs(ctx, p.Action, preCause, p.LotphaseID); err != nil {
			return err
		}
		fmt.Println("---> end change db: ", time.Now().Sub(t), time.Now().String())
		switch p.Action {
		case JobActionSubmit, JobActionAccept, JobActionReject, JobActionRecycle:
			js := &EvtJobsubmit{
				// ID:          jlog.ID, // dont pass ID
				JobID:       job.ID,
				LotID:       job.LotID,
				ExecutorUid: op.GetUid(),
				ExecteamUid: op.GetOrgUid(),
				Action:      p.Action,
				Elems:       job.ElemsCnt,
				Phase:       job.Phase,
				InsCnt:      p.Jobsubmit.InsCnt,
				InsTotal:    p.Jobsubmit.InsTotal,
				Ins2d:       p.Jobsubmit.Ins2d,
				Ins3d:       p.Jobsubmit.Ins3d,
				RrElems:     p.Jobsubmit.RrElems,
				RrIns:       p.Jobsubmit.RrIns,
				RrIns2d:     p.Jobsubmit.RrIns2d,
				RrIns3d:     p.Jobsubmit.RrIns3d,
				Duration:    int32(time.Since(job.UpdatedAt) / time.Second),
				CreatedAt:   time.Now(),
				ClaimedAt:   job.UpdatedAt,
			}
			err = mq.PublishEvt(ctx, EvtTypeAnnoJobsubmit, EvtSubtypeCreate, js)
			if err != nil {
				return fmt.Errorf("failed to publish jobsubmit event: %w", err)
			}
		}
		o.log.Warn(ctx, "=========== 开始 执行数据库操作3")
		for _, fn := range actions {
			if err = fn(ctx, job); err != nil {
				return err
			}
		}
		o.log.Warn(ctx, "=========== 开始 执行数据库操作4")

		// notify job workflow
		fmt.Println("---> job state: ", job.State)
		ev := ""
		var details *common.Details
		switch job.State {
		case JobStateDoing:
			ev = common.EvtJobDoing
		case JobStateChecking:
			ev = common.EvtJobChecking
			details = common.NewDetails(jlog)
		default:
			// no need to inform job workflow
			// ev = common.EvtJobChanged
			return nil
		}
		o.log.Warn(ctx, "=========== 开始 执行数据库操作 - 最后一步")
		return o.bgtask.SignalEvent(ctx, &common.Event{
			Event:     ev,
			JobID:     job.ID,
			JobAction: p.Action,
			Details:   details,
		})
	})
	fmt.Println("---> end change job state : ", time.Now().Sub(t1), time.Now().String())
	return
}

func (o *JobsBiz) FixJobStateAndLog(ctx context.Context, job *Job, jlog *Joblog, p *JobAction, toPreExecutor bool,
	actions ...TxAction) (success bool, err error) {
	fmt.Println("---> workflow fix job state enter: ", toPreExecutor)
	if toPreExecutor {
		executor := p.NewState.ExecutorUid
		if executor == "" {
			// find previous executor
			executor, _, err = o.Repo.GetPhaseLastExecutor(ctx, job.ID, int(p.NewState.Phase))
			if err != nil && !errors.IsNotFound(err) {
				return false, fmt.Errorf("failed to get job previous executor: %w", err)
			}
		}
		if executor != "" {
			// get executor's team and check if the executor is still assigned in the lot
			p.NewState.Execteam, err = o.Repo.GetJobExecutorTeam(ctx, job.ID, executor)
			if err != nil {
				if !errors.IsNotFound(err) {
					return false, fmt.Errorf("failed to get executor team: %w", err)
				}
				// The previous executor is probably unassigned from this lot.
				// Make it claimable by everybody.
				executor = ""
			}
		}
		p.NewState.ExecutorUid = executor
	}

	spew.Dump(p)
	err = o.Repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		success, err = o.Repo.ChangeJobState(ctx, job, p)
		if !success {
			return err
		}

		jlog.ToPhase = p.NewState.Phase
		jlog.ToState = p.NewState.State.Value()
		jlog.ToExecteam = p.NewState.Execteam
		jlog.ToExecutorUid = p.NewState.ExecutorUid
		jlog, err = o.Repo.CreateJoblog(ctx, jlog)
		if err != nil {
			return err
		}

		for _, fn := range actions {
			if err = fn(ctx, job); err != nil {
				return err
			}
		}
		return nil
	})
	return
}
func (o *JobsBiz) tryClaim(ctx context.Context, lotID int64, subtype string, executorUid, execteamUid string,
	prefer ClaimJobPrefer) (*Job, error) {
	// check if there is any assigned ongoing job
	candi, err := o.Repo.GetAssignedJob(ctx, lotID, subtype, executorUid, prefer)
	spew.Dump("---> try claim: ", candi, err)
	if err != nil && !errors.IsNotFound(err) {
		return nil, err
	}
	var jobs []*Job
	if candi != nil {
		fmt.Println("get assigned job success: ", candi.LotID, candi.ID)
		jobs = []*Job{candi}
	} else {
		// try to find unassigned and unclaimed jobs
		jobs, err = o.Repo.GetJobsToAssign(ctx, lotID, subtype, executorUid)
		fmt.Println("get unassigned jobs: ", jobs, err)
		if len(jobs) > 0 {
			fmt.Println("---> job id: ", jobs[0].ID)
		}
		if err != nil {
			return nil, err
		}
	}
	if len(jobs) == 0 {
		// try to snatch jobs from others
		jobs, err = o.Repo.SnatchJobs(ctx, lotID, subtype, executorUid)
		fmt.Println("try to snatch jobs: ", jobs, err)
		if err != nil {
			return nil, err
		}
	}
	fmt.Println("---> claim job success: ", jobs)
	var quota *LotJobQuota
	spew.Dump("---> quota check 1: ", quota)
	for _, candi := range jobs {
		if candi.State == JobStateDoing && candi.ExecutorUid == executorUid {
			fmt.Println("---> found a claimed job. state: ", candi.State)
			// found a claimed job
			return candi, nil
		}

		// check team quota (a user can only be assigned to one phase in a lot)
		if quota == nil || candi.LotID != lotID {
			quota, err = o.lotrepo.GetExecteamQuota(ctx, candi.LotID, executorUid)
			spew.Dump("---> quota check 2: ", quota, candi.LotID)
			if err != nil {
				return nil, err
			}
			lotID = candi.LotID
		}
		if !quota.NoLimit() {
			if quota.ClaimedJobs >= quota.MaxJobs {
				fmt.Println("oh !, max jobs", quota.ClaimedJobs, quota.MaxJobs)
				continue
			}
			// TODO 检查其他团队的 quota，避免此次领取影响其他团队无法达到 min quota
		}

		// make the original executor yield if this is a snatched timeout job
		if candi.State == JobStateDoing {
			// yield to current user and decrease original team's ClaimedJobs (caused by JobActionTimeout).
			ok, err := o.yield(ctx, candi, quota.LotphaseID, JobActionTimeout, "snatch", "", executorUid)
			if err != nil {
				return nil, err
			}
			if !ok {
				continue
			}
			candi.ExecutorUid = executorUid
			candi.State = JobStateUnstart
		}

		// The job is not assigned or was assigned by admins or managers.
		// Mark the job as assigned and doing. Increase team's ClaimedJobs.
		fmt.Println("---> mark claim job: ", candi.ID)
		ok, err := o.ChangeJobState(ctx, candi, &JobAction{
			Action: JobActionClaim,
			NewState: JobStateTransition{
				Phase:       candi.Phase,
				State:       JobStateDoing,
				Cause:       candi.Cause, // keep Cause
				Execteam:    execteamUid,
				ExecutorUid: executorUid,
			},
			LotphaseID: quota.LotphaseID,
		}, true)
		if err != nil {
			return nil, err
		}
		if ok {
			return candi, nil
		}
		// background tasks or somebody else changed the job state, retry with another one
	}
	return nil, nil
}

func (o *JobsBiz) ClaimJob(ctx context.Context, req *anno.ClaimJobRequest) (job *Job, err error) {
	user := UserFromCtx(ctx)
	lotID := kid.ParseID(req.LotUid)
	subtype := req.Subtype
	prefer := ClaimJobPreferFromPb(req.Prefer)

	for {
		job, err = o.tryClaim(ctx, lotID, subtype, user.GetUid(), user.GetOrgUid(), prefer)
		fmt.Println("---> try claim err: ", err)
		if err != nil || job != nil || !req.Fallback {
			if job != nil && job.Ally == nil {
				fmt.Println("---> try claim get job ally.")
				job, err = o.Repo.GetByID(ctx, job.ID, true)
			}
			return
		}

		// fallback to all lot and subtype
		if lotID > 0 {
			lotID = 0
			continue
		}
		if subtype != "" {
			subtype = ""
			continue
		}
		break
	}

	// there is really no job to do
	return nil, nil
}

func (o *JobsBiz) AssignJob(ctx context.Context, job *Job, req *anno.AssignJobRequest) error {
	if job.ExecutorUid == req.ExecutorUid {
		return nil
	}
	if job.State == JobStateChecking {
		return errors.NewErrInProcess(errors.WithMessage("cannot operate on jobs in the checking state"))
	}

	success, err := o.ChangeJobState(ctx, job, &JobAction{
		Action: JobActionAssign,
		NewState: JobStateTransition{
			Phase:       job.Phase,
			State:       JobStateUnstart,
			Execteam:    "",
			ExecutorUid: req.ExecutorUid,
		},
		LotphaseID: job.LotPhase.ID,
	}, true)
	if err == nil && !success {
		return errors.NewErrInProcess(errors.WithMessage("please retry"))
	}
	return err
}

func (o *JobsBiz) yield(ctx context.Context, job *Job, phaseID int64, action, reason, details, toExecutor string) (
	bool, error) {
	var txActions []TxAction
	if action == JobActionGiveup {
		// get the last executor and execteam and set it in the job in order to support claim policy
		toLastExecutor, toLastExecteam, err := o.Repo.GetToPhaseLastExecutor(ctx, job.ID, int(job.Phase))
		if err != nil && !errors.IsNotFound(err) {
			return false, err
		}

		txAction := func(ctx context.Context, v any) error {
			innerJob, _ := v.(*Job)
			innerJob.LastExecutor = toLastExecutor
			innerJob.LastExecteam = toLastExecteam
			_, err := o.Repo.Update(ctx, innerJob, field.NewMask(JobSfldLastExecutor, JobSfldLastExecteam))
			return err
		}
		txActions = append(txActions, txAction)
	}

	return o.ChangeJobState(ctx, job, &JobAction{
		Action: action,
		Details: &JoblogDetails{
			GiveupReason: &GiveupReason{
				Reason:  reason,
				Details: details,
			},
		},
		NewState: JobStateTransition{
			Phase:       job.Phase,
			State:       JobStateUnstart,
			ExecutorUid: toExecutor,
		},
		LotphaseID: phaseID,
	}, false, txActions...)
}

func (o *JobsBiz) GiveupJob(ctx context.Context, job *Job, req *anno.GiveupJobRequest) error {
	if job.ExecutorUid != UserFromCtx(ctx).GetUid() {
		return errors.NewErrForbidden(errors.WithMessage("job is not assigned to you"))
	}
	if job.State != JobStateDoing && job.State != JobStateUnstart {
		return errors.NewErrFailedPrecondition(errors.WithModel("Job"), errors.WithFields("state"))
	}

	ok, err := o.yield(ctx, job, job.LotPhase.ID, JobActionGiveup, req.Reason, req.Details, "")
	if err == nil && !ok {
		return errors.NewErrInProcess(errors.WithMessage("please retry"))
	}

	return err
}

func (o *JobsBiz) SubmitJob(ctx context.Context, job *Job, req *anno.SubmitJobRequest) error {
	t1 := time.Now()
	t := time.Now()
	fmt.Println("---> enter submit job: ", t1.String())
	if job.Phase != 1 {
		return errors.NewErrFailedPrecondition(errors.WithMessage("can only submit jobs at phase 1"),
			errors.WithModel("Job"), errors.WithFields("phase"))
	}
	if job.ExecutorUid != UserFromCtx(ctx).GetUid() {
		return errors.NewErrForbidden(errors.WithMessage("job is not assigned to you"))
	}
	if job.State != JobStateDoing {
		return errors.NewErrFailedPrecondition(errors.WithModel("Job"), errors.WithFields("state"))
	}

	fmt.Println("---> begin comment: ", time.Now().Sub(t), t.String())
	t = time.Now()
	// process resolved comments
	//comments, err := o.processComments(ctx, job, req.Annotations, req.Resolves, nil, "")
	comments, err := o.processCommentsV2(ctx, &ProcessCommentsReq{
		Job:              job,
		Annos:            req.Annotations,
		ResolvedComments: req.Resolves,
	})
	if err != nil {
		return err
	}
	if comments.Valid {
		o.dropCommentsToDeletedAnnos(req.Annotations, &comments)
		if lo.SomeBy(comments.Value, func(v *anno.AnnoComment) bool {
			if v.AddPhase <= job.Phase || v.ResolvePhase < job.Phase {
				fmt.Println("---> hit unresolved comment: ", v)
			}
			return v.AddPhase <= job.Phase || v.ResolvePhase < job.Phase
		}) {
			return errors.NewErrUnresolvedComments(errors.WithMessage("not all comments are resolved"))
		}
	}
	var details *JoblogDetails
	if len(req.Resolves) > 0 {
		details = &JoblogDetails{Resolves: req.Resolves}
	}

	fmt.Println("---> begin process job annos", time.Now().Sub(t), t.String())
	t = time.Now()
	// check annotations
	insCnt, insTotal, annos, err := o.processJobAnnos(job, req.Annotations)
	if err != nil {
		return err
	}
	fmt.Println("---> end process job annos: ", time.Now().Sub(t), t.String())

	fmt.Println("---> begin count job annos: ", time.Now().Sub(t), t.String())
	t = time.Now()
	js := CountAnnos(job, &anno.ReviewJobRequest{Annotations: req.Annotations, Resolves: req.Resolves})
	fmt.Println("---> end count job annos: ", time.Now().Sub(t), t.String())

	fmt.Println("---> begin change job annos state: ", time.Now().Sub(t), t.String())
	t = time.Now()
	success, err := o.ChangeJobState(ctx, job, &JobAction{
		Action:  JobActionSubmit,
		Details: details,
		NewState: JobStateTransition{
			State:       JobStateChecking, // let background task to decide new phase and state
			Cause:       "",
			Phase:       job.Phase,
			InsCnt:      insCnt,
			InsTotal:    insTotal,
			Execteam:    "",
			ExecutorUid: "",
		},
		Annotations: annos,
		Comments:    comments,
		Jobsubmit:   js,
	}, false)
	fmt.Println("---> end change job annos state: ", time.Now().Sub(t1), t.String())
	if err == nil && !success {
		return errors.NewErrInProcess()
	}
	fmt.Println("---> end submit job: ", time.Now().Sub(t1), t.String())
	return err
}

// matchCommentsWithAnnos returns comments having corresponding anno objects.
func (o *JobsBiz) matchCommentsWithAnnos(comments []*AnnoComment, annos *anno.JobAnno, keepMissed bool) []*AnnoComment {
	matches := kset.NewSet[*AnnoComment]()
	for _, c := range comments {
		// all job/element comments are considered matching
		if c.Scope == anno.AnnoComment_Scope_element || c.Scope == anno.AnnoComment_Scope_job ||
			(c.ObjUuids == nil && c.Scope == anno.AnnoComment_Scope_unspecified) {
			matches.Add(c)
		}
	}

	walkAnnoObjs(annos, func(elemIdx, rdIdx int, obj *anno.Object) bool {
		c, ok := lo.Find(comments, func(v *anno.AnnoComment) bool { return lo.Contains(v.ObjUuids, obj.Uuid) })
		if ok {
			matches.Add(c)
		}
		return true
	})
	return matches.Slice()
}

// dropCommentsToDeletedAnnos drops comments if the corresponding objects are not found in reqAnnos.
func (o *JobsBiz) dropCommentsToDeletedAnnos(reqAnnos *anno.JobAnno, comments *ValidValue[Comments]) {
	if reqAnnos == nil || len(comments.Value) == 0 {
		return
	}
	comments.Value = o.matchCommentsWithAnnos(comments.Value, reqAnnos, false)
}

func (o *JobsBiz) processJobAnnos(job *Job, reqAnnos *anno.JobAnno) (insCnt, insTotal ValidValue[int32],
	vannos ValidValue[Annotations], err error) {
	if reqAnnos == nil {
		return
	}

	if len(reqAnnos.ElementAnnos) > 0 && len(reqAnnos.ElementAnnos) != len(job.Ally.Elements) {
		err = errors.NewErrBadRequest(errors.WithMessage("incorrect number of element_annos"),
			errors.WithFields("element_annos", "job.ally.elements"))
		return
	}

	for i, e := range reqAnnos.ElementAnnos {
		elemCnt := 0
		for _, rd := range e.RawdataAnnos {
			elemCnt += len(rd.Objects)
			// TODO: check each object
		}
		e.InsCnt = int32(elemCnt)
		if len(e.RawdataAnnos) > 0 && len(e.RawdataAnnos) != len(job.Ally.Elements[i].Datas) {
			err = errors.NewErrBadRequest(errors.WithMessage("incorrect number of element rawdata_annos"),
				errors.WithFields("rawdata_annos", "job.ally.elements[*].datas"))
			return
		}
	}

	cnt, total, _ := interpolate.CountIns(reqAnnos)
	insCnt = NewValidValue(int32(cnt))
	insTotal = NewValidValue(int32(total))
	reqAnnos.InsCnt = int32(cnt)
	reqAnnos.JobIndex = job.IdxInLot
	vannos = NewValidValue(*serial.New(reqAnnos))
	return
}

type ProcessCommentsReq struct {
	Job              *Job
	Annos            *anno.JobAnno
	AddComments      []*AnnoComment
	UpdatedComments  []*AnnoComment
	ResolvedComments []*ResolveComment
	DeletedComments  []*ResolveComment
	Commenter        string
}

func (o *JobsBiz) processCommentsV2(ctx context.Context, req *ProcessCommentsReq) (res ValidValue[Comments], err error) {
	if err = req.Job.Ally.DownloadComments(ctx); err != nil {
		return
	}
	if len(req.AddComments) == 0 && len(req.ResolvedComments) == 0 && len(req.UpdatedComments) == 0 &&
		len(req.DeletedComments) == 0 && len(req.Job.Ally.Comments) == 0 {
		return
	}

	for _, v := range req.AddComments {
		if v.Uuid == "" {
			return res, errors.NewErrEmptyField(errors.WithFields("comments.uuid"))
		}
	}
	for _, v := range req.ResolvedComments {
		if v.Uuid == "" {
			return res, errors.NewErrEmptyField(errors.WithFields("resolves.uuid"))
		}
	}
	for _, v := range req.UpdatedComments {
		if v.Uuid == "" {
			return res, errors.NewErrEmptyField(errors.WithFields("updatedComments.uuid"))
		}
	}
	for _, v := range req.DeletedComments {
		if v.Uuid == "" {
			return res, errors.NewErrEmptyField(errors.WithFields("deletedComments.uuid"))
		}
	}
	fmt.Println("#######")
	spew.Dump("---> add comments: ", req.AddComments)
	spew.Dump("---> resolved comments: ", req.ResolvedComments)
	spew.Dump("---> updated comments: ", req.UpdatedComments)
	spew.Dump("---> deleted comments: ", req.DeletedComments)
	spew.Dump("---> ally comments: ", req.Job.Ally.Comments)
	fmt.Println("#######")

	// all comments
	comments := make(Comments, 0, len(req.Job.Ally.Comments)+len(req.AddComments))
	// map value means if it is visited (resolves an existing comment)
	resolveMap := lo.SliceToMap(req.ResolvedComments, func(v *ResolveComment) (string, *anno.ResolveAnnoComment) {
		return v.Uuid, v
	})
	deletedMap := lo.SliceToMap(req.DeletedComments, func(v *ResolveComment) (string, *anno.ResolveAnnoComment) {
		return v.Uuid, v
	})
	updatedMap := lo.SliceToMap(req.UpdatedComments, func(v *AnnoComment) (string, *anno.AnnoComment) {
		return v.Uuid, v
	})

	fmt.Println("-------------")
	spew.Dump("resolveMap: ", resolveMap)
	spew.Dump("deletedMap: ", deletedMap)
	spew.Dump("updatedMap: ", updatedMap)
	fmt.Println("-------------")
	for _, c := range req.Job.Ally.Comments {
		cid := c.Uuid
		fmt.Println("uuid: ", cid)
		// updated comment
		if v, ok := updatedMap[cid]; ok {
			c = v
			if c.ResolvePhase > 0 {
				c.ResolvePhase--
			}
			comments = append(comments, c)
			continue
		}
		// deleted comment. drop it
		if _, ok := deletedMap[cid]; ok {
			continue
		}

		// resolved comment
		if _, ok := resolveMap[cid]; ok {
			if c.Reasons.GetClass() == commentClassMissed {
				c.ObjUuids = resolveMap[cid].ObjUuids
			}
			resolveMap[cid] = nil
			if c.ResolvePhase < req.Job.Phase {
				c.ResolvePhase = req.Job.Phase
			}
		}
		comments = append(comments, c)
	}
	spew.Dump("comments:", comments)
	for id, v := range resolveMap {
		if v != nil {
			if o.StrictCheck() {
				return res, errors.NewErrBadRequest(errors.WithMessage("resolved comments are not found"))
			}
			o.log.Warn(ctx, "resolved comments are not found", "comment_id", id)
		}
	}
	if len(req.AddComments) == 0 {
		return NewValidValue(comments), nil
	}
	// ensure newly added object comments have associated anno objects
	var checkComments []*AnnoComment
	for _, c := range req.AddComments {
		if c.Reasons == nil {
			return res, errors.NewErrEmptyField(errors.WithFields("comments.reasons"))
		}
		if c.ElemIdx < 0 || int(c.ElemIdx) >= len(req.Job.Ally.Elements) ||
			c.RdIdx < 0 || int(c.RdIdx) >= len(req.Job.Ally.Elements[c.ElemIdx].Datas) {
			return res, errors.NewErrBadRequest(errors.WithMessage("element or rawdata index in comments"))
		}
		if c.Scope == anno.AnnoComment_Scope_object && len(c.ObjUuids) > 0 {
			checkComments = append(checkComments, c)
		}
	}
	var goodComments []*AnnoComment
	fmt.Println("reqAnnos == nil", req.Annos == nil)
	if req.Annos == nil {
		req.Annos = req.Job.Ally.Annotations.E
	}
	spew.Dump("---> reqAnnos == nil: ", req.Annos == nil, checkComments)
	if req.Annos != nil {
		spew.Dump("---> 12321")
		goodComments = o.matchCommentsWithAnnos(checkComments, req.Annos, false)
	}
	fmt.Println("strictCheck: ", o.strictCheck)
	spew.Dump(goodComments, checkComments)
	if req.Annos == nil || len(goodComments) < len(checkComments) {
		if o.StrictCheck() {
			return res, errors.NewErrBadRequest(errors.WithMessage("attempt to comment to unknown object"))
		}
		_, illComments := lo.Difference(goodComments, checkComments)
		o.log.Warn(ctx, "attempt to comment to unknown object", "comments", illComments)

		// drop ill comments
		req.AddComments = lo.Filter(req.AddComments, func(v *AnnoComment, _ int) bool { return !lo.Contains(illComments, v) })
	}

	spew.Dump("addComments: ", req.AddComments, "comments: ", comments)
	// process new comments
	for _, c := range req.AddComments {
		c.ResolvePhase = 0
		c.AddPhase = req.Job.Phase
		c.Commenter = &client.BaseUser{Uid: req.Commenter}
		c.CreatedAt = timestamppb.Now()
		comments = append(comments, c)
	}
	spew.Dump("---> 111: ", comments)
	if len(comments) == 1 {
		return NewValidValue(comments), nil
	}

	// check if there are multiple commentedObjs comments
	commentedObjs := make(map[string]bool, len(comments))
	for i, c := range comments {
		fmt.Println("phase: ", c.ResolvePhase, req.Job.Phase, c.ObjUuids)
		// Even though, this can only happen when a reviewer rejects an annotation, to ensure we don't run into
		// multiple active comments when the job reaches current phase again, we compare the ResolvePhase with
		// job.Phase instead of job.Phase - 1.
		if c.ResolvePhase < req.Job.Phase {
			fmt.Println("---> check multiple comments")
			for _, id := range c.ObjUuids {
				if commentedObjs[id] {
					if o.StrictCheck() {
						return res, errors.NewErrBadRequest(errors.WithMessage("multiple active comments to one object"))
					}
					o.log.Warn(ctx, "multiple active comments to one object", "object_id", id)
					comments[i] = nil
				}
				commentedObjs[id] = true
			}
		}
	}
	if lo.Some(comments, []*anno.AnnoComment{nil}) {
		comments = lo.Filter(comments, func(c *anno.AnnoComment, _ int) bool { return c != nil })
	}
	spew.Dump("---> 222: ", comments)
	return NewValidValue(comments), nil
}

func (o *JobsBiz) processComments(ctx context.Context, job *Job, reqAnnos *anno.JobAnno,
	resolveComments []*ResolveComment, addComments []*AnnoComment, commenter string) (
	res ValidValue[Comments], err error) {
	if err = job.Ally.DownloadComments(ctx); err != nil {
		return
	}
	if len(addComments) == 0 && len(resolveComments) == 0 && len(job.Ally.Comments) == 0 {
		return
	}

	for _, v := range addComments {
		if v.Uuid == "" {
			return res, errors.NewErrEmptyField(errors.WithFields("comments.uuid"))
		}
	}
	for _, v := range resolveComments {
		if v.Uuid == "" {
			return res, errors.NewErrEmptyField(errors.WithFields("resolves.uuid"))
		}
	}

	// resolve comments
	comments := make(Comments, 0, len(job.Ally.Comments)+len(addComments))
	// map value means if it is visited (resolves an existing comment)
	resolves := lo.SliceToMap(resolveComments, func(v *ResolveComment) (string, *anno.ResolveAnnoComment) {
		return v.Uuid, v
	})
	fmt.Println("#######")
	spew.Dump("---> add comments: ", addComments)
	spew.Dump("---> resolves: ", resolves)
	spew.Dump("---> ally comments: ", job.Ally.Comments)
	fmt.Println("#######")
	for _, c := range job.Ally.Comments {
		cid := c.Uuid
		_, ok := resolves[cid]
		fmt.Println("---> resolved: ", ok)
		fmt.Println("---> reset phase: resolvePhase -> jobPhase: ", c.ResolvePhase, job.Phase)
		if _, ok := resolves[cid]; !ok {
			if c.ResolvePhase < job.Phase {
				fmt.Println("---> sub reset phase: ", c.ResolvePhase, job.Phase)
				// It is OK to reset ResolvePhase because:
				// * If decision is accept, both ResolvePhase before and after change will fail the test,
				//     c.AddPhase > job.Phase && c.ResolvePhase >= job.Phase
				//   and the request will be refused.
				// * If decision is reject, this will make the comment reveal to executors in previous phases.
				c.ResolvePhase = 0
			} else {
				// no need to touch
			}
			comments = append(comments, c)
			continue
		}
		if c.Reasons.GetClass() == commentClassMissed {
			c.ObjUuids = resolves[cid].ObjUuids
		}
		fmt.Println("---> cap - crp - jp :", c.AddPhase, c.ResolvePhase, job.Phase)
		// Mark it as visited.
		// If we delete it, and there are multiple comments to one object, when the comment that the user intends to
		// resolve is not in the 1st place, it will not be resolved
		resolves[cid] = nil
		if c.AddPhase <= job.Phase {
			fmt.Println("---> sub-1: ", c.AddPhase, job.Phase)
			// final resolve; drop it
			continue
		}

		if c.ResolvePhase < job.Phase {
			fmt.Println("---> sub-2: ", c.ResolvePhase, job.Phase)
			c.ResolvePhase = job.Phase
		} else {
			// A comment was resolved at this or a higher phase and then the job was rolled back to a lower phase.
			// Lower phase executors need not resolve it again.
		}
		comments = append(comments, c)
	}
	spew.Dump("---> resolves-222 : ", resolves, comments, addComments)
	for id, v := range resolves {
		fmt.Println("---> v: ", v)
		if v != nil {
			if o.StrictCheck() {
				return res, errors.NewErrBadRequest(errors.WithMessage("resolved comments are not found"))
			}
			o.log.Warn(ctx, "resolved comments are not found", "comment_id", id)
		}
	}
	fmt.Println("------------")
	if len(addComments) == 0 {
		return NewValidValue(comments), nil
	}
	spew.Dump("---> addcomments: ", addComments, addComments[0].Scope.String())
	// ensure newly added object comments have associated anno objects
	checkComments := []*AnnoComment{}
	for _, c := range addComments {
		if c.Reasons == nil {
			return res, errors.NewErrEmptyField(errors.WithFields("comments.reasons"))
		}
		if c.ElemIdx < 0 || int(c.ElemIdx) >= len(job.Ally.Elements) ||
			c.RdIdx < 0 || int(c.RdIdx) >= len(job.Ally.Elements[c.ElemIdx].Datas) {
			return res, errors.NewErrBadRequest(errors.WithMessage("element or rawdata index in comments"))
		}
		if c.Scope == anno.AnnoComment_Scope_object && (len(c.ObjUuids) > 0 || c.Reasons.Class != commentClassMissed) {
			checkComments = append(checkComments, c)
		}
	}
	goodComments := []*AnnoComment{}
	fmt.Println("reqAnnos == nil", reqAnnos == nil)
	if reqAnnos == nil {
		reqAnnos = job.Ally.Annotations.E
	}
	spew.Dump("---> reqAnnos == nil: ", reqAnnos == nil, checkComments)
	if reqAnnos != nil {
		spew.Dump("---> 12321")
		goodComments = o.matchCommentsWithAnnos(checkComments, reqAnnos, false)
	}
	fmt.Println("strictCheck: ", o.strictCheck)
	spew.Dump(goodComments, checkComments)
	if reqAnnos == nil || len(goodComments) < len(checkComments) {
		if o.StrictCheck() {
			return res, errors.NewErrBadRequest(errors.WithMessage("attempt to comment to unknown object"))
		}
		_, illComments := lo.Difference(goodComments, checkComments)
		o.log.Warn(ctx, "attempt to comment to unknown object", "comments", illComments)

		// drop ill comments
		addComments = lo.Filter(addComments, func(v *AnnoComment, _ int) bool { return !lo.Contains(illComments, v) })
	}

	spew.Dump(addComments, comments)
	// process new comments
	for _, c := range addComments {
		c.ResolvePhase = 0
		c.AddPhase = job.Phase
		c.Commenter = &client.BaseUser{Uid: commenter}
		c.CreatedAt = timestamppb.Now()
		//if c.ResolvePhase < 0 {
		//	c.ResolvePhase = 0
		//	c.AddPhase = job.Phase
		//	c.Commenter = &client.BaseUser{Uid: commenter}
		//	c.CreatedAt = timestamppb.Now()
		//}
		comments = append(comments, c)
	}
	spew.Dump("---> 111: ", comments)
	if len(comments) == 1 {
		return NewValidValue(comments), nil
	}

	// check if there are multiple commentedObjs comments
	commentedObjs := make(map[string]bool, len(comments))
	for i, c := range comments {
		fmt.Println("phase: ", c.ResolvePhase, job.Phase, c.ObjUuids)
		// Even though, this can only happen when a reviewer rejects an annotation, to ensure we don't run into
		// multiple active comments when the job reaches current phase again, we compare the ResolvePhase with
		// job.Phase instead of job.Phase - 1.
		if c.ResolvePhase < job.Phase {
			fmt.Println("000")
			for _, id := range c.ObjUuids {
				if commentedObjs[id] {
					if o.StrictCheck() {
						return res, errors.NewErrBadRequest(errors.WithMessage("multiple active comments to one object"))
					}
					o.log.Warn(ctx, "multiple active comments to one object", "object_id", id)
					comments[i] = nil
				}
				commentedObjs[id] = true
			}
		}
	}
	if lo.Some(comments, []*anno.AnnoComment{nil}) {
		comments = lo.Filter(comments, func(c *anno.AnnoComment, _ int) bool { return c != nil })
	}
	spew.Dump("---> 222: ", comments)

	return NewValidValue(comments), nil
}

func (o *JobsBiz) ReviewJob(ctx context.Context, job *Job, req *anno.ReviewJobRequest) error {
	t1 := time.Now()
	t := time.Now()
	fmt.Println("---> begin review job: ", t1.String())
	spew.Dump("enter job ctx : ", ctx)
	op := UserFromCtx(ctx)
	if job.Phase == 1 {
		return errors.NewErrFailedPrecondition(errors.WithMessage("cannot review jobs at phase 1"))
	}
	if job.ExecutorUid != op.GetUid() {
		return errors.NewErrForbidden(errors.WithMessage("job is not assigned to you"))
	}
	o.log.Warn(ctx, "=========== review job step 1", "job:", job.ID)
	if job.State != JobStateDoing {
		return errors.NewErrFailedPrecondition(errors.WithMessage("job is not doing"))
	}
	fmt.Println("---> begin review decision: ", time.Now().String())
	t = time.Now()
	decision := JobReviewDecisionToAction(req.Decision)
	fmt.Println("---> decision: ", decision)
	if decision == JobActionReject || decision == JobActionRecycle {
		if job.Phase == 1 {
			return errors.NewErrForbidden(errors.WithMessage("cannot reject jobs at phase 1"))
		}
	}
	fmt.Println("---> end review decision: ", time.Now().Sub(t), time.Now().String())
	// process comments
	fmt.Println("---> begin process comment: ", time.Now().String())
	t = time.Now()
	var err error
	act := &JobAction{}
	//act.Comments, err = o.processComments(ctx, job, req.Annotations, req.Resolves, req.Comments, op.GetUid())
	act.Comments, err = o.processCommentsV2(ctx, &ProcessCommentsReq{
		Job:              job,
		Annos:            req.Annotations,
		ResolvedComments: req.Resolves,
		AddComments:      req.Comments,
		UpdatedComments:  req.UpdatedComments,
		DeletedComments:  req.DeletedComments,
		Commenter:        op.GetUid(),
	})
	o.log.Warn(ctx, "=========== review job step 4", "job:", job.ID)
	if err != nil {
		return err
	}
	if len(req.Resolves) > 0 || len(req.Comments) > 0 || len(req.UpdatedComments) > 0 || len(req.DeletedComments) > 0 {
		act.Details = &JoblogDetails{
			AddComments:     req.Comments,
			Resolves:        req.Resolves,
			UpdatedComments: req.UpdatedComments,
			DeletedComments: req.DeletedComments,
		}
	}
	fmt.Println("---> end process comment: ", time.Now().Sub(t), time.Now().String())

	// check annotations
	fmt.Println("---> begin process annos: ", time.Now().String())
	t = time.Now()
	insCnt, insTotal, annos, err := o.processJobAnnos(job, req.Annotations)
	o.log.Warn(ctx, "=========== review job step 5", "job:", job.ID, "time", time.Now())
	if err != nil {
		return err
	}
	if req.Annotations != nil {
		if !job.LotPhase.Editable {
			return errors.NewErrUnsupportedField(errors.WithMessage("job is not allowed to edit in this phase"))
		}
		act.Annotations = annos
	}
	fmt.Println("---> end process annos: ", time.Now().Sub(t), time.Now().String())

	fmt.Println("---> begin count annos: ", time.Now().String())
	t = time.Now()
	cause := ""
	js := CountAnnos(job, req)
	spew.Dump("---> js: ", js)
	switch decision {
	case JobActionAccept:
		if len(req.Comments) > 0 {
			return errors.NewErrUnresolvedComments(errors.WithMessage("cannot accept with new comments"))
		}
		if act.Comments.Valid {
			o.dropCommentsToDeletedAnnos(req.Annotations, &act.Comments)
			//if lo.SomeBy(act.Comments.Value, func(v *anno.AnnoComment) bool {
			//	return v.AddPhase <= job.Phase || v.ResolvePhase < job.Phase
			//}) {
			//	return errors.NewErrUnresolvedComments(errors.WithMessage("not all comments are resolved"))
			//}
		}
	case JobActionReject, JobActionForceReject:
		cause = JobActionReject
		if act.Comments.Valid {
			o.dropCommentsToDeletedAnnos(req.Annotations, &act.Comments)
		}
	case JobActionRecycle, JobActionForceRecycle:
		if req.Annotations != nil || len(req.Comments) > 0 || len(req.Resolves) > 0 {
			return errors.NewErrBadRequest(
				errors.WithMessage("cannot recycle with annotations, new comments or resolved comments"))
		}
		// clear annotations and comments
		insCnt = NewValidValue(int32(0))
		insTotal = NewValidValue(int32(0))
		act.Annotations = NewValidValue(*serial.New((*anno.JobAnno)(nil)))
		act.Comments = NewValidValue(Comments(nil))

		// set jobsubmit
		js.RrElems = js.Elems
		js.RrIns = js.InsTotal
		js.RrIns2d = js.Ins2d
		js.RrIns3d = js.Ins3d
		js.Elems = 0
		js.InsCnt = 0
		js.InsTotal = 0
		js.Ins2d = 0
		js.Ins3d = 0
	default:
		return errors.NewErrInvalidField(errors.WithFields("decision"))
	}
	//return errors.NewErrEmptyField()
	fmt.Println("---> end count annos: ", time.Now().Sub(t), time.Now().String())

	fmt.Println("---> begin change job state: ", time.Now().String())
	t = time.Now()
	act.Action = decision
	act.NewState = JobStateTransition{
		Phase:       job.Phase,
		State:       JobStateChecking, // let background task to decide new phase and state
		Cause:       cause,
		InsCnt:      insCnt,
		InsTotal:    insTotal,
		Execteam:    "",
		ExecutorUid: "",
	}
	act.Jobsubmit = js
	act.LotphaseID = job.LotPhase.ID
	spew.Dump("---> 666: ", act)
	//return errors.NewErrEmptyField()
	success, err := o.ChangeJobState(ctx, job, act, false)
	if err == nil && !success {
		return errors.NewErrInProcess()
	}
	fmt.Println("---> end change job state: ", time.Now().Sub(t), time.Now().String())

	fmt.Println("end review job: ", time.Now().Sub(t1), time.Now().String())
	return err
}

type RevertJobsParam struct {
	ToPreviousExecutor bool `json:",omitempty"`
	KeepComments       bool `json:",omitempty"`
	KeepAnnos          bool `json:",omitempty"`
	LogIDs             []int64
}

func (o *JobsBiz) BatchRevertJob(ctx context.Context, jobs []*Job, toPhase int32,
	opt *anno.BatchRevertJobRequest_Options) (failJobs []*Job, err error) {
	failJobs = lo.Filter(jobs, func(v *Job, _ int) bool { return v.Phase < toPhase })
	spew.Dump("---> fail jobs: ", failJobs)
	if len(failJobs) > 0 {
		jobs = lo.Filter(jobs, func(v *Job, _ int) bool { return v.Phase >= toPhase })
	}
	spew.Dump("---> jobs: ", len(jobs))
	if len(jobs) == 0 {
		return
	}

	ids := lo.Map(jobs, func(v *Job, _ int) int64 { return v.ID })
	err = o.Repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		cause := ""
		if opt.ToPreviousExecutor {
			cause = JobActionReject
		}
		jobUpdates := map[string]any{
			JobSfldState:       JobStateChecking,
			JobSfldPhase:       toPhase,
			JobSfldExecutorUid: "",
			JobSfldCause:       cause,
		}
		// allyUpdates := map[string]any{}
		if !opt.KeepAnnos {
			// allyUpdates[JoballySfldComments] = nil
			// allyUpdates[JoballySfldAnnotations] = nil
			jobUpdates[JobSfldInsCnt] = 0
			jobUpdates[JobSfldInsTotal] = 0
		}
		// if !opt.KeepComments {
		// 	allyUpdates[JoballySfldComments] = nil
		// }
		spew.Dump(jobUpdates)
		err = o.Repo.BatchUpdate(ctx, &Job{}, jobUpdates, ids)
		if err != nil {
			return err
		}
		// comments and annotations will be udpated in workflow
		// if len(allyUpdates) > 0 {
		// 	err = o.repo.BatchUpdate(ctx, &Jobally{}, allyUpdates, ids)
		// 	if err != nil {
		// 		return err
		// 	}
		// }

		// write log
		action := JobActionForceReject
		if !opt.KeepAnnos {
			action = JobActionForceRecycle
		}
		op := UserFromCtx(ctx)
		logs := lo.Map(jobs, func(v *Job, _ int) *Joblog {
			return &Joblog{
				JobID:         v.ID,
				OperatorUid:   op.GetUid(),
				OpOrgUid:      op.GetOrgUid(),
				Action:        action,
				FromPhase:     v.Phase,
				FromState:     v.State.Value(),
				ToPhase:       toPhase,
				ToState:       JobStateUnstart.Value(),
				ToExecteam:    "",
				ToExecutorUid: "",
			}
		})
		// keep BatchCreateJoblog here to avoid slowing down workflow replays
		// due to large parameter process.
		logs, err = o.Repo.BatchCreateJoblog(ctx, logs)
		if err != nil {
			return err
		}

		logIDs := lo.Map(logs, func(v *Joblog, _ int) int64 { return v.ID })
		return o.bgtask.SignalEvent(ctx, &common.Event{
			Event: common.EvtLotRevertJobs,
			LotID: jobs[0].LotID,
			Details: common.NewDetails(&RevertJobsParam{
				ToPreviousExecutor: opt.ToPreviousExecutor,
				KeepComments:       opt.KeepComments,
				KeepAnnos:          opt.KeepAnnos,
				LogIDs:             logIDs,
			}),
		})
	})
	return
}

func (o *JobsBiz) updateTeamClaimedJobs(ctx context.Context, jobAction, preCause string, lotphaseID int64) error {
	if lotphaseID == 0 {
		return nil
	}
	switch preCause {
	case JobActionReject, JobActionForceReject:
		// no need to update claimed jobs since the executor is claiming a rejected job from higher phase
		return nil
	}

	var increment int
	switch jobAction {
	case JobActionClaim, JobActionAssign:
		increment = 1
	case JobActionReject, JobActionForceReject, JobActionGiveup, JobActionTimeout, JobActionEnd, JobActionRecycle:
		increment = -1
	default:
		return nil // ignore other actions
	}

	_, err := o.lotrepo.UpdatePhaseWithIncrement(ctx,
		&Lotphase{ID: lotphaseID},
		map[string]int{
			LotphaseSfldClaimedJobs.String(): increment,
		},
		nil, // no need to pass filed mask
	)
	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("failed to update claimed_jobs: %w", err)
	}

	return nil
}

func (o *JobsBiz) GetPhaseExecutors(ctx context.Context, jobIDs []int64, maxPhase int32) (map[int64][]*Joblog, error) {
	jobIDs = lo.Uniq(jobIDs)
	joblogs, err := o.Repo.GetPhaseExecutors(ctx, jobIDs, maxPhase)
	if err != nil {
		return nil, err
	}

	joblogGroup := make(map[int64][]*Joblog)
	for _, jl := range joblogs {
		joblogGroup[jl.JobID] = append(joblogGroup[jl.JobID], jl)
	}

	return joblogGroup, nil
}

func (o *JobsBiz) CheckRejectedJobs(ctx context.Context, executorUid string, lotIDs []int64) (kset.Set[int64], error) {
	rejectedLots, err := o.Repo.CheckRejectedJobs(ctx, executorUid, lotIDs)
	if err != nil {
		return nil, err
	}

	rejectedLotSet := kset.NewSetFrom(rejectedLots...)
	return rejectedLotSet, nil
}

func (o *JobsBiz) CheckJobQualification(ctx context.Context, jobID int64) error {
	op := UserFromCtx(ctx).User
	job, err := o.Repo.GetByID(ctx, jobID, false)
	if err != nil {
		return err
	}
	if op.Uid != job.ExecutorUid {
		return errors.NewErrForbidden(errors.WithMessage("job is not assigned to you"))
	}
	if job.State != JobStateDoing {
		return errors.NewErrFailedPrecondition(errors.WithModel("Job"), errors.WithFields("state"))
	}
	return nil
}

func (o *JobsBiz) GetJobsPhaseCountByLotIds(ctx context.Context, lotIds []int64) ([]RespJobsPhaseCountByLotIds, error) {
	return o.Repo.GetJobsPhaseCountByLotIds(ctx, lotIds)
}
