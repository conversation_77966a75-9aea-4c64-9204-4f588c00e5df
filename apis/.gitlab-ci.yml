variables:
  REPOS: iam anno annout annofeed annostat unicorn
  APPS: iam anno annout annofeed annostat anyconn model
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_VERIFY: 1
  DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  CI_ARTIFACTORY_REGISTRY: $CI_ARTIFACTORY_REGISTRY
  CI_ARTIFACTORY_DOCKER_REPO: $CI_ARTIFACTORY_DOCKER_REPO
  CI_ARTIFACTORY_GO_REPO: $CI_ARTIFACTORY_GO_REPO
  CI_ARTIFACTORY_HELM_REPO: $CI_ARTIFACTORY_HELM_REPO
  CI_ARTIFACTORY_USER: $CI_ARTIFACTORY_USER
  CI_ARTIFACTORY_TOKEN: $CI_ARTIFACTORY_TOKEN
  CI_GITLAB_BOT: $CI_GITLAB_BOT
  CI_GITLAB_BOT_TOKEN: $CI_GITLAB_BOT_TOKEN
  IMAGE_PREFIX: $CI_ARTIFACTORY_DOCKER_REPO/
  COMMIT_NAME: "${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}"
  DOCKER_TAG_LOGIC: '(if [ "$${CI_COMMIT_TAG}" == "" ]; then echo "$$COMMIT_NAME"; else echo "$${CI_COMMIT_TAG}"; fi);'
  GOPRIVATE: $CI_GOPRIVATE
  GOPROXY: $CI_GOPROXY

stages:
  - lint
  - test
  - deploy

.lint:
  stage: lint
  tags: ["k8s"]
  image: ${IMAGE_PREFIX}golangci/golangci-lint:latest
  script:
    - golangci-lint run -v

test:
  stage: test
  tags: ["k8s"]
  image: ${IMAGE_PREFIX}golang:1.22-alpine
  script:
    - |
      sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
      apk update && apk add --no-cache make git protoc sed
      make init
      make test

auto-update-api:
  stage: deploy
  tags: [ "k8s" ]
  only: ["tags"]
  image: ${IMAGE_PREFIX}golang:alpine
  before_script:
    - |
      sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
      apk update && apk add --no-cache git
      git config --global user.name $CI_GITLAB_BOT
      git config --global user.email "$<EMAIL>"
  script:
    - |
      echo "current tag: $CI_COMMIT_TAG"
      git config --global user.name $CI_GITLAB_BOT
      git config --global user.email "$<EMAIL>"
      work_dir=$(pwd)
      branch="bot/auto-update-api"
      for app in $REPOS; do
        cd ..
        repo_link=https://$CI_GITLAB_BOT:$<EMAIL>/platform/$app.git
        git clone --depth 1 $repo_link
        cd $app
        git remote set-url origin $repo_link
        git checkout -b $branch
        sed -i "s|gitlab.rp.konvery.work/platform/apis v.*$|gitlab.rp.konvery.work/platform/apis $CI_COMMIT_TAG|g" go.mod
        if git status --porcelain | grep -q '.'; then
          go mod tidy || true
          git add .
          git commit -m "chore: update api to $CI_COMMIT_TAG"   
          git push origin $branch -f -o merge_request.create -o merge_request.target=main -o merge_request.merge_when_pipeline_succeeds
        fi
        cd $work_dir
      done

openapi:
  stage: deploy
  tags: ["k8s"]
  only: ["tags"]
  image:
    name: ${IMAGE_PREFIX}alpine/k8s:1.25.3
    entrypoint: [""]
  script:
    - |
      for app in $APPS; do
        sed -i "s/^\(    version: \)0.0.1\$/\1$CI_COMMIT_TAG/" $app/openapi.yaml
        curl --fail-with-body $CI_APIDOCS/docs/$app/$CI_COMMIT_TAG?alias=v1 -T $app/openapi.yaml
      done
