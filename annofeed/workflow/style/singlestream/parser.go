package singlestream

import (
	"context"
	"fmt"
	"os"
	"path"
	"path/filepath"
	"strings"

	"annofeed/internal/biz"
	"annofeed/workflow/common"
	"gitlab.rp.konvery.work/platform/pkg/kfs"
)

func Transform(ctx context.Context, data *biz.Data, dir string, startIdx int, hb common.Heartbeat) (dataType string, err error) {
	newdir := dir + "-1"
	o := newXparser(dir, newdir)
	err = kfs.IterateDir(dir, func(entry os.DirEntry, subdir string, depth int) error {
		// skip directories
		if entry.IsDir() {
			return nil
		}
		hb(ctx)
		return o.parseFile(ctx, subdir, entry.Name())
	})
	if err != nil {
		return
	}

	if err := o.closeFrame(ctx); err != nil {
		return "", fmt.Errorf("failed to close last frame when handling %v: %w", dir, err)
	}

	tmp := dir + "-tmp"
	if err := os.Rename(dir, tmp); err != nil {
		return "", fmt.Errorf("failed to backup %s to %s: %w", dir, tmp, err)
	}
	if err := os.Rename(newdir, dir); err != nil {
		os.Rename(tmp, dir)
		return "", fmt.Errorf("failed to rename %s to %s: %w", newdir, dir, err)
	}
	if err := os.RemoveAll(tmp); err != nil {
		// fmt.Errorf("failed to delete backup dir %s: %w", tmp, err)
	}
	dataType = biz.DataTypeImage
	if !o.imgStream {
		dataType = biz.DataTypePointcloud
	}
	return
}

type fileParser func(ctx context.Context, subdir, name string) (match bool, err error)

type xparser struct {
	elemParam  *biz.ElemParamV1
	frmIdx     int
	fileIdx    int
	curSubdir  string
	oldBasedir string
	newBasedir string
	parsers    []fileParser
	imgStream  bool
	// log        *log.Helper
}

func newXparser(olddir, newdir string) *xparser {
	o := &xparser{
		frmIdx:     -1,
		oldBasedir: olddir,
		newBasedir: newdir,
		// log: log.NewHelper(opts.Logger),
	}

	o.initParsers()
	return o
}

func (o *xparser) initParsers() {
	o.parsers = []fileParser{
		o.ignoreDotFiles,
		o.parseStreamFile,
		// o.defaultParser,
	}
}

func (o *xparser) parseFile(ctx context.Context, subdir, name string) (err error) {
	for _, p := range o.parsers {
		match, err := p(ctx, subdir, name)
		if match {
			return err
		}
	}
	return fmt.Errorf("unparsed file: %v", filepath.Join(subdir, name))
}

func (o *xparser) ignoreDotFiles(ctx context.Context, subdir, name string) (match bool, err error) {
	if !strings.HasPrefix(filepath.Base(name), ".") {
		return false, nil
	}
	// o.log.WithContext(ctx).Warnf("ignore a dot file %v", name)
	return true, nil
}

func (o *xparser) defaultParser(ctx context.Context, subdir, name string) (match bool, err error) {
	// o.log.WithContext(ctx).Warnf("ignore an unknown file %v", name)
	return true, nil
}

func (o *xparser) newFilePath(name string) string {
	return filepath.Join(o.newBasedir, o.curSubdir, name)
}

func (o *xparser) parseStreamFile(ctx context.Context, subdir, name string) (match bool, err error) {
	o.imgStream = common.IsSupportedImageFormat(name)
	ext := "." + common.FileExt(name)
	if !common.IsSupportedRawdataFormat(name) {
		return false, nil
	}

	baseName := strings.TrimSuffix(name, path.Ext(name))
	frmDir := filepath.Join(subdir, baseName)
	if frmDir != o.curSubdir {
		err = o.startFrame(ctx, frmDir)
		if err != nil {
			return true, err
		}
	}

	newBaseName := "cam"
	if !o.imgStream {
		newBaseName = common.KeyLidar
	}
	// duplicate the file instead of moving it, otherwise we will probably miss some params
	// when retrying the process if it is interrupted due to a temporary error
	old := filepath.Join(o.oldBasedir, subdir, name)
	newf := o.newFilePath(newBaseName + ext)
	err = os.Link(old, newf)
	if err != nil {
		return true, fmt.Errorf("failed to link file (%v -> %v): %w", newf, old, err)
	}

	// record its original name
	err = os.WriteFile(newf+common.OrigExt, []byte(filepath.Join(subdir, name)), 0644)
	if err != nil {
		return true, fmt.Errorf("failed to write file %v: %w", newf+common.OrigExt, err)
	}

	o.fileIdx++
	return true, nil
}

func (o *xparser) closeFrame(ctx context.Context) error {
	return nil
}

func (o *xparser) startFrame(ctx context.Context, subdir string) error {
	err := o.closeFrame(ctx)
	if err != nil {
		return fmt.Errorf("failed to close frame: %w", err)
	}

	o.fileIdx = 0
	o.frmIdx++
	o.curSubdir = subdir

	// make frame directory
	fpath := o.newFilePath("")
	err = os.MkdirAll(fpath, 0755)
	if err != nil {
		return fmt.Errorf("failed to create directory %v: %w", fpath, err)
	}

	return nil
}
