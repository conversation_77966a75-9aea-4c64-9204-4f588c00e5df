// source: anno/v1/job.proto
package biz

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"gitlab.rp.konvery.work/platform/pkg/upload"
	"path"
	"time"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/ktime"
)

type (
	ResolveComment = anno.ResolveAnnoComment
	AnnoComment    = anno.AnnoComment
)

type Elements = serial.Slice[*anno.Element]
type Annotations = serial.Type[*anno.JobAnno]
type Comments = serial.Slice[*anno.AnnoComment]

type CameraParams map[string]*anno.Job_CamParam
type RawdataParams struct {
	CamParams CameraParams
}

type RawdataParamsType = serial.Type[*RawdataParams]
type ElementData = anno.Job_ElementData       // data related to element is stored in S3
type AnnotationData = anno.Job_AnnotationData // data related to annotation is stored in S3
type CommentData = anno.Job_CommentData       // data related to comment is stored in S3

type ElementsSegment struct {
	URI   string `json:"uri,omitempty"`
	Count int    `json:"count,omitempty"`
}
type ElementsSegments = serial.Slice[*ElementsSegment] // elements segments

type JobDraft struct {
	Version string `json:"version,omitempty"` // timestamp from FE
	URI     string `json:"uri,omitempty"`     // remote data is `*anno.SaveJobDraftRequest`
	Content []byte `json:"content,omitempty"` // raw SaveJobDraftRequest
}
type JobDraftType = serial.Type[*JobDraft]

// Jobally contains large fields of jobs
type Jobally struct {
	ID               int64             `json:"id" gorm:"default:null"` // same as job ID
	Elements         Elements          `json:"elements" gorm:"default:null"`
	RawdataParams    RawdataParamsType `json:"rawdata_params" gorm:"default:"`
	Annotations      Annotations       `json:"annotations" gorm:"default:null"`
	Comments         Comments          `json:"comments" gorm:"default:null"`
	ElementsSegments ElementsSegments  `json:"elements_segments" gorm:"default:null"`
	AnnotationsURI   string            `json:"annotations_uri" gorm:"default:null"`
	CommentsURI      string            `json:"comments_uri" gorm:"default:null"`
	Draft            JobDraftType      `json:"draft" gorm:"default:null"`
	UpdatedAt        time.Time         `json:"updated_at" gorm:"default:null"`
	CreatedAt        time.Time         `json:"created_at" gorm:"default:null"`
}

func (o *Jobally) PatchAnnos() {
	if o == nil || o.Annotations.E == nil {
		return
	}
	for i, e := range o.Annotations.E.ElementAnnos {
		elem := o.Elements[i]
		for j, rd := range e.RawdataAnnos {
			rd.Name = elem.Datas[j].Name
			rd.OrigName = elem.Datas[j].OrigName
		}
		e.Name = elem.Name
		e.Index = elem.Index
	}
}

func (o *Jobally) GetID() int64 { return o.ID }

// BuildUploadKey builds upload key. If orgUid is empty, job.Lot.OrgUid will be used.
func (o *Job) BuildUploadKey(fname string, orgUid, lotUid string) string {
	if orgUid == "" {
		orgUid = o.Lot.OrgUid
	}
	if lotUid == "" {
		lotUid = o.Lot.GetUid()
	}
	basename := path.Join("cust", orgUid, "lots", lotUid, "jobs", o.GetUid(), fname)
	return basename
}

// UploadElements saves the elements data into the object storage service.
// orgUid is used to build the object key. If it is empty, job.Lot.OrgUid will be used.
func (o *Job) UploadElements(ctx context.Context, elementData *ElementData, orgUid, lotUid string) (bool, error) {
	fmt.Println("---> build job elements segment")
	fmt.Printf("=== UploadElements Context Timeout Debug ===\n")
	if deadline, ok := ctx.Deadline(); ok {
		fmt.Printf("!!!!!! Context has a deadline. Time remaining: %v\n", time.Until(deadline))
	} else {
		fmt.Printf("!!!!!! Context has NO deadline.\n")
	}
	if ShouldSaveBigValuesInDB() {
		return false, nil
	}

	// TODO support splitting elements to upload
	fname := fmt.Sprintf("job-%s-elems.json", kid.StringID(o.ID))
	fmt.Println("---> upload job elements name: ", fname)
	key := o.BuildUploadKey(fname, orgUid, lotUid)
	result, err := UploadData(ctx, elementData, key)
	if err != nil {
		return false, err
	}

	o.Ally.ElementsSegments = []*ElementsSegment{{
		URI:   result.URI,
		Count: len(elementData.GetElements()),
	}}
	return true, nil
}

func (o *Jobally) DownloadElements(ctx context.Context) error {
	t := time.Now()
	fmt.Println("---> Begin Downloading elements", t.String())
	if len(o.Elements) > 0 || len(o.ElementsSegments) == 0 {
		return nil
	}

	segments := o.ElementsSegments
	spew.Dump("segments: ", segments)
	for _, seg := range segments {
		elementData, err := DownloadData[ElementData](ctx, seg.URI)
		if err != nil {
			return err
		}

		o.Elements = append(o.Elements, elementData.GetElements()...)
		if len(elementData.GetCamParams()) > 0 {
			if o.RawdataParams.E == nil {
				o.RawdataParams = *serial.New(&RawdataParams{})
			}
			o.RawdataParams.E.CamParams = elementData.GetCamParams()
		}
	}
	fmt.Println("---> End Downloading elements", time.Now().Sub(t), time.Now().String())
	return nil
}

// UploadAnnotations saves the annos data into the object storage service.
// orgUid is used to build the object key. If it is empty, job.Lot.OrgUid will be used.
func (o *Job) UploadAnnotations(ctx context.Context, annoData *AnnotationData, orgUid, lotUId string) (bool, error) {
	if ShouldSaveBigValuesInDB() {
		return false, nil
	}

	if len(annoData.GetElementAnnos()) == 0 {
		o.Ally.AnnotationsURI = "" // clear annos url and the outer layer will update it into DB
		return true, nil
	}

	fname := path.Join("annos", fmt.Sprintf("%s.json", ktime.TodayCST("060102-150405")))
	key := o.BuildUploadKey(fname, orgUid, lotUId)
	fmt.Println("============")
	fmt.Println("lotid", lotUId)
	fmt.Println("upload-key", key, "time", time.Now())
	//result, err := UploadData(ctx, annoData, key)
	opt := &upload.Options{
		ContentType: "text/plain; charset=utf-8",
		Upload2:     true,
	}
	result, err := UploadData2(ctx, annoData, key, opt)
	fmt.Println("upload-res", result, "time", time.Now())
	fmt.Println("============")
	if err != nil {
		fmt.Println("upload-err", err.Error())
		return false, err
	}

	o.Ally.AnnotationsURI = result.URI
	return true, nil
}

func (o *Jobally) DownloadAnnotations(ctx context.Context, insCnt, idxInLot int32) error {
	if o.Annotations.E != nil && len(o.Annotations.E.ElementAnnos) > 0 || o.AnnotationsURI == "" {
		return nil
	}
	fmt.Println("---> download annotation uri: ", o.AnnotationsURI)
	annoData, err := DownloadData[AnnotationData](ctx, o.AnnotationsURI)
	if err != nil {
		return err
	}

	if o.Annotations.E == nil {
		o.Annotations = *serial.New(&anno.JobAnno{})
	}
	o.Annotations.E.ElementAnnos = annoData.GetElementAnnos()
	o.Annotations.E.Attrs = annoData.GetJobAttrs()
	o.Annotations.E.InsCnt = insCnt
	o.Annotations.E.JobIndex = idxInLot

	return nil
}

// UploadComments saves the comments data into the object storage service.
// orgUid is used to build the object key. If it is empty, job.Lot.OrgUid will be used.
func (o *Job) UploadComments(ctx context.Context, commentData *CommentData, orgUid, lotUId string) (bool, error) {
	if ShouldSaveBigValuesInDB() {
		return false, nil
	}

	if len(commentData.GetComments()) == 0 {
		o.Ally.CommentsURI = "" // clear comments url and the outer layer will update it into DB
		return true, nil
	}

	fname := path.Join("comments", fmt.Sprintf("%s.json", ktime.TodayCST("060102-150405")))
	key := o.BuildUploadKey(fname, orgUid, lotUId)
	result, err := UploadData(ctx, commentData, key)
	if err != nil {
		return false, err
	}

	o.Ally.CommentsURI = result.URI
	return true, nil
}

func (o *Jobally) DownloadComments(ctx context.Context) error {
	t := time.Now()
	fmt.Println("---> Begin Downloading comments", t.String())
	if len(o.Comments) > 0 || o.CommentsURI == "" {
		return nil
	}
	spew.Dump(o.CommentsURI)
	commentData, err := DownloadData[CommentData](ctx, o.CommentsURI)
	if err != nil {
		return err
	}
	o.Comments = commentData.GetComments()
	fmt.Println("---> End Downloading comments", time.Now().Sub(t), time.Now().String())
	return nil
}

type JoballyOption = func(ctx context.Context, job *Job) error

func WithElements() JoballyOption {
	return func(ctx context.Context, job *Job) error {
		return job.Ally.DownloadElements(ctx)
	}
}

func WithAnnotations() JoballyOption {
	return func(ctx context.Context, job *Job) error {
		t := time.Now()
		fmt.Println("---> Begin With Annotations", t.String())
		err := job.Ally.DownloadAnnotations(ctx, job.InsCnt, job.IdxInLot)
		job.Ally.PatchAnnos()
		fmt.Println("---> Finish With Annotations", time.Now().Sub(t), time.Now().String())
		return err
	}
}

func WithComments() JoballyOption {
	return func(ctx context.Context, job *Job) error {
		return job.Ally.DownloadComments(ctx)
	}
}

const JoballyTableName = "joballies"

type JoballySfld string

func (o JoballySfld) String() string    { return string(o) }
func (o JoballySfld) WithTable() string { return JoballyTableName + "." + string(o) }

var (
	Jobally_             = field.RegObject(&Jobally{})
	JoballyUpdatableFlds = field.NewModel(JoballyTableName, Jobally_,
		"Elements", "Annotations", "Comments", "ElementsSegments", "AnnotationsURI", "CommentsURI", "Draft")

	JoballySfldID               = JoballySfld(field.Sname(&Jobally_.ID))
	JoballySfldElements         = JoballySfld(field.Sname(&Jobally_.Elements))
	JoballySfldAnnotations      = JoballySfld(field.Sname(&Jobally_.Annotations))
	JoballySfldComments         = JoballySfld(field.Sname(&Jobally_.Comments))
	JoballySfldElementsSegments = JoballySfld(field.Sname(&Jobally_.ElementsSegments))
	JoballySfldAnnotationsURI   = JoballySfld(field.Sname(&Jobally_.AnnotationsURI))
	JoballySfldCommentsURI      = JoballySfld(field.Sname(&Jobally_.CommentsURI))
	JoballySfldDraft            = JoballySfld(field.Sname(&Jobally_.Draft))
)

// anno comment classes
const (
	commentClassUnwanted = "unwanted" // （多标）
	commentClassMissed   = "missed"   // （漏标）
	commentClassFlawed   = "flawed"   // （错标）
	commentClassOther    = "other"    // （其他）
)
