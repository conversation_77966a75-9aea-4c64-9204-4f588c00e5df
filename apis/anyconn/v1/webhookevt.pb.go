// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anyconn/v1/webhookevt.proto

package anyconn

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateWebhookEvtRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// platform if it is from a third-party platform
	Platform string `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`
	// unique event id within the platform
	EvtId string `protobuf:"bytes,2,opt,name=evt_id,json=evtId,proto3" json:"evt_id,omitempty"`
	// UID of the project which the webhookevt belongs to
	ProjectUid string `protobuf:"bytes,3,opt,name=project_uid,json=projectUid,proto3" json:"project_uid,omitempty"`
	// UID of the task which the webhookevt belongs to
	TaskUid string `protobuf:"bytes,4,opt,name=task_uid,json=taskUid,proto3" json:"task_uid,omitempty"`
	// UID of the job which the webhookevt belongs to
	JobUid string `protobuf:"bytes,5,opt,name=job_uid,json=jobUid,proto3" json:"job_uid,omitempty"`
	// event body
	Body string `protobuf:"bytes,6,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *CreateWebhookEvtRequest) Reset() {
	*x = CreateWebhookEvtRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_webhookevt_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWebhookEvtRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWebhookEvtRequest) ProtoMessage() {}

func (x *CreateWebhookEvtRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_webhookevt_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWebhookEvtRequest.ProtoReflect.Descriptor instead.
func (*CreateWebhookEvtRequest) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_webhookevt_proto_rawDescGZIP(), []int{0}
}

func (x *CreateWebhookEvtRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *CreateWebhookEvtRequest) GetEvtId() string {
	if x != nil {
		return x.EvtId
	}
	return ""
}

func (x *CreateWebhookEvtRequest) GetProjectUid() string {
	if x != nil {
		return x.ProjectUid
	}
	return ""
}

func (x *CreateWebhookEvtRequest) GetTaskUid() string {
	if x != nil {
		return x.TaskUid
	}
	return ""
}

func (x *CreateWebhookEvtRequest) GetJobUid() string {
	if x != nil {
		return x.JobUid
	}
	return ""
}

func (x *CreateWebhookEvtRequest) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

type UpdateWebhookEvtRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// update contents
	Evt *CreateWebhookEvtRequest `protobuf:"bytes,2,opt,name=evt,proto3" json:"evt,omitempty"`
	// name of fileds to be updated
	Fields []string `protobuf:"bytes,3,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *UpdateWebhookEvtRequest) Reset() {
	*x = UpdateWebhookEvtRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_webhookevt_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWebhookEvtRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWebhookEvtRequest) ProtoMessage() {}

func (x *UpdateWebhookEvtRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_webhookevt_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWebhookEvtRequest.ProtoReflect.Descriptor instead.
func (*UpdateWebhookEvtRequest) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_webhookevt_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateWebhookEvtRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *UpdateWebhookEvtRequest) GetEvt() *CreateWebhookEvtRequest {
	if x != nil {
		return x.Evt
	}
	return nil
}

func (x *UpdateWebhookEvtRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type DeleteWebhookEvtRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// webhookevt UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *DeleteWebhookEvtRequest) Reset() {
	*x = DeleteWebhookEvtRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_webhookevt_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteWebhookEvtRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWebhookEvtRequest) ProtoMessage() {}

func (x *DeleteWebhookEvtRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_webhookevt_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWebhookEvtRequest.ProtoReflect.Descriptor instead.
func (*DeleteWebhookEvtRequest) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_webhookevt_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteWebhookEvtRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetWebhookEvtRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// webhookevt UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *GetWebhookEvtRequest) Reset() {
	*x = GetWebhookEvtRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_webhookevt_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWebhookEvtRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWebhookEvtRequest) ProtoMessage() {}

func (x *GetWebhookEvtRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_webhookevt_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWebhookEvtRequest.ProtoReflect.Descriptor instead.
func (*GetWebhookEvtRequest) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_webhookevt_proto_rawDescGZIP(), []int{3}
}

func (x *GetWebhookEvtRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type WebhookEvtFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter by task
	TaskUid string `protobuf:"bytes,1,opt,name=task_uid,json=taskUid,proto3" json:"task_uid,omitempty"`
	// filter by job
	JobUid string `protobuf:"bytes,2,opt,name=job_uid,json=jobUid,proto3" json:"job_uid,omitempty"`
}

func (x *WebhookEvtFilter) Reset() {
	*x = WebhookEvtFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_webhookevt_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebhookEvtFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebhookEvtFilter) ProtoMessage() {}

func (x *WebhookEvtFilter) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_webhookevt_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebhookEvtFilter.ProtoReflect.Descriptor instead.
func (*WebhookEvtFilter) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_webhookevt_proto_rawDescGZIP(), []int{4}
}

func (x *WebhookEvtFilter) GetTaskUid() string {
	if x != nil {
		return x.TaskUid
	}
	return ""
}

func (x *WebhookEvtFilter) GetJobUid() string {
	if x != nil {
		return x.JobUid
	}
	return ""
}

type ListWebhookEvtRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagesz int32 `protobuf:"varint,1,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// An opaque pagination token returned from a previous call.
	// An empty token denotes the first page.
	PageToken string            `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	Filter    *WebhookEvtFilter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListWebhookEvtRequest) Reset() {
	*x = ListWebhookEvtRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_webhookevt_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWebhookEvtRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhookEvtRequest) ProtoMessage() {}

func (x *ListWebhookEvtRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_webhookevt_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhookEvtRequest.ProtoReflect.Descriptor instead.
func (*ListWebhookEvtRequest) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_webhookevt_proto_rawDescGZIP(), []int{5}
}

func (x *ListWebhookEvtRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListWebhookEvtRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListWebhookEvtRequest) GetFilter() *WebhookEvtFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type ListWebhookEvtReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Webhookevts []*WebhookEvt `protobuf:"bytes,1,rep,name=webhookevts,proto3" json:"webhookevts,omitempty"`
	// An opaque pagination token, if not empty, to be used to fetch the next page of results
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// total number of items found; valid only in the first page reply.
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListWebhookEvtReply) Reset() {
	*x = ListWebhookEvtReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_webhookevt_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWebhookEvtReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhookEvtReply) ProtoMessage() {}

func (x *ListWebhookEvtReply) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_webhookevt_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhookEvtReply.ProtoReflect.Descriptor instead.
func (*ListWebhookEvtReply) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_webhookevt_proto_rawDescGZIP(), []int{6}
}

func (x *ListWebhookEvtReply) GetWebhookevts() []*WebhookEvt {
	if x != nil {
		return x.Webhookevts
	}
	return nil
}

func (x *ListWebhookEvtReply) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListWebhookEvtReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type WebhookEvt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// webhookevt UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// platform if it is from a third-party platform
	Platform string `protobuf:"bytes,2,opt,name=platform,proto3" json:"platform,omitempty"`
	// unique event id within the platform
	EvtId string `protobuf:"bytes,3,opt,name=evt_id,json=evtId,proto3" json:"evt_id,omitempty"`
	// UID of the project which the webhookevt belongs to
	ProjectUid string `protobuf:"bytes,4,opt,name=project_uid,json=projectUid,proto3" json:"project_uid,omitempty"`
	// UID of the task which the webhookevt belongs to
	TaskUid string `protobuf:"bytes,5,opt,name=task_uid,json=taskUid,proto3" json:"task_uid,omitempty"`
	// UID of the job which the webhookevt belongs to
	JobUid string `protobuf:"bytes,6,opt,name=job_uid,json=jobUid,proto3" json:"job_uid,omitempty"`
	// if the webhookevt is done
	Done bool `protobuf:"varint,7,opt,name=done,proto3" json:"done,omitempty"`
	// extra info about the webhookevt
	Extra string `protobuf:"bytes,8,opt,name=extra,proto3" json:"extra,omitempty"`
	// event body
	Body string `protobuf:"bytes,9,opt,name=body,proto3" json:"body,omitempty"`
	// converted annotations
	ConvertedAnnos string `protobuf:"bytes,10,opt,name=converted_annos,json=convertedAnnos,proto3" json:"converted_annos,omitempty"`
	// errors if any
	Errors string `protobuf:"bytes,11,opt,name=errors,proto3" json:"errors,omitempty"`
	// webhookevt creation time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *WebhookEvt) Reset() {
	*x = WebhookEvt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anyconn_v1_webhookevt_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebhookEvt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebhookEvt) ProtoMessage() {}

func (x *WebhookEvt) ProtoReflect() protoreflect.Message {
	mi := &file_anyconn_v1_webhookevt_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebhookEvt.ProtoReflect.Descriptor instead.
func (*WebhookEvt) Descriptor() ([]byte, []int) {
	return file_anyconn_v1_webhookevt_proto_rawDescGZIP(), []int{7}
}

func (x *WebhookEvt) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *WebhookEvt) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *WebhookEvt) GetEvtId() string {
	if x != nil {
		return x.EvtId
	}
	return ""
}

func (x *WebhookEvt) GetProjectUid() string {
	if x != nil {
		return x.ProjectUid
	}
	return ""
}

func (x *WebhookEvt) GetTaskUid() string {
	if x != nil {
		return x.TaskUid
	}
	return ""
}

func (x *WebhookEvt) GetJobUid() string {
	if x != nil {
		return x.JobUid
	}
	return ""
}

func (x *WebhookEvt) GetDone() bool {
	if x != nil {
		return x.Done
	}
	return false
}

func (x *WebhookEvt) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

func (x *WebhookEvt) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *WebhookEvt) GetConvertedAnnos() string {
	if x != nil {
		return x.ConvertedAnnos
	}
	return ""
}

func (x *WebhookEvt) GetErrors() string {
	if x != nil {
		return x.Errors
	}
	return ""
}

func (x *WebhookEvt) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

var File_anyconn_v1_webhookevt_proto protoreflect.FileDescriptor

var file_anyconn_v1_webhookevt_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x65, 0x62,
	0x68, 0x6f, 0x6f, 0x6b, 0x65, 0x76, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x61,
	0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xba, 0x01, 0x0a,
	0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x76, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x76, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x74, 0x61, 0x73, 0x6b, 0x55, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x5f, 0x75,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f, 0x62, 0x55, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x62, 0x6f, 0x64, 0x79, 0x3a, 0x03, 0xba, 0x47, 0x00, 0x22, 0x94, 0x01, 0x0a, 0x17, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x35, 0x0a, 0x03, 0x65, 0x76, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45,
	0x76, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x03, 0x65, 0x76, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x3a, 0x18, 0xba, 0x47, 0x15, 0xba, 0x01, 0x03, 0x75, 0x69,
	0x64, 0xba, 0x01, 0x03, 0x65, 0x76, 0x74, 0xba, 0x01, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73,
	0x22, 0x2b, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x45, 0x76, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x28, 0x0a,
	0x14, 0x47, 0x65, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x46, 0x0a, 0x10, 0x57, 0x65, 0x62, 0x68, 0x6f,
	0x6f, 0x6b, 0x45, 0x76, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74,
	0x61, 0x73, 0x6b, 0x55, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x5f, 0x75, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f, 0x62, 0x55, 0x69, 0x64, 0x22,
	0x8f, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45,
	0x76, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x06, 0x70, 0x61, 0x67,
	0x65, 0x73, 0x7a, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04,
	0x18, 0x64, 0x28, 0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x34, 0x0a, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x6e,
	0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x45, 0x76, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x22, 0xb2, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x45, 0x76, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x38, 0x0a, 0x0b, 0x77, 0x65, 0x62,
	0x68, 0x6f, 0x6f, 0x6b, 0x65, 0x76, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68,
	0x6f, 0x6f, 0x6b, 0x45, 0x76, 0x74, 0x52, 0x0b, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x65,
	0x76, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65,
	0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x3a, 0x23, 0xba, 0x47, 0x20, 0xba, 0x01, 0x0b, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x65, 0x76, 0x74, 0x73, 0xba, 0x01, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x87, 0x03, 0x0a, 0x0a, 0x57, 0x65, 0x62, 0x68, 0x6f,
	0x6f, 0x6b, 0x45, 0x76, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x76, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x76, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74,
	0x61, 0x73, 0x6b, 0x55, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x5f, 0x75, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f, 0x62, 0x55, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x6f, 0x6e, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x64,
	0x6f, 0x6e, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64,
	0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x27, 0x0a,
	0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x73,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x65,
	0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x3a, 0x25, 0xba, 0x47, 0x22, 0xba, 0x01,
	0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x0c, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x65, 0x76,
	0x74, 0x65, 0x64, 0xba, 0x01, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x32, 0xb8, 0x04, 0x0a, 0x0b, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76, 0x74, 0x73,
	0x12, 0x6b, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x45, 0x76, 0x74, 0x12, 0x23, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45,
	0x76, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x61, 0x6e, 0x79, 0x63,
	0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76,
	0x74, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x3a, 0x01, 0x2a, 0x22, 0x0f, 0x2f, 0x76,
	0x31, 0x2f, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x65, 0x76, 0x74, 0x73, 0x12, 0x73, 0x0a,
	0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76,
	0x74, 0x12, 0x23, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76, 0x74, 0x22, 0x22,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x03, 0x65, 0x76, 0x74, 0x32, 0x15, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x65, 0x76, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69,
	0x64, 0x7d, 0x12, 0x6e, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68,
	0x6f, 0x6f, 0x6b, 0x45, 0x76, 0x74, 0x12, 0x23, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x45, 0x76, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x2a, 0x15, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x65, 0x76, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69,
	0x64, 0x7d, 0x12, 0x68, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x45, 0x76, 0x74, 0x12, 0x20, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76, 0x74, 0x22, 0x1d, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x17, 0x12, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x65, 0x62, 0x68, 0x6f,
	0x6f, 0x6b, 0x65, 0x76, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x6d, 0x0a, 0x0e,
	0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76, 0x74, 0x12, 0x21,
	0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x12, 0x0f, 0x2f, 0x76, 0x31, 0x2f,
	0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x65, 0x76, 0x74, 0x73, 0x42, 0x47, 0x0a, 0x0a, 0x61,
	0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x37, 0x67, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77,
	0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x61, 0x6e, 0x79, 0x63, 0x6f, 0x6e, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e, 0x79,
	0x63, 0x6f, 0x6e, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_anyconn_v1_webhookevt_proto_rawDescOnce sync.Once
	file_anyconn_v1_webhookevt_proto_rawDescData = file_anyconn_v1_webhookevt_proto_rawDesc
)

func file_anyconn_v1_webhookevt_proto_rawDescGZIP() []byte {
	file_anyconn_v1_webhookevt_proto_rawDescOnce.Do(func() {
		file_anyconn_v1_webhookevt_proto_rawDescData = protoimpl.X.CompressGZIP(file_anyconn_v1_webhookevt_proto_rawDescData)
	})
	return file_anyconn_v1_webhookevt_proto_rawDescData
}

var file_anyconn_v1_webhookevt_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_anyconn_v1_webhookevt_proto_goTypes = []interface{}{
	(*CreateWebhookEvtRequest)(nil), // 0: anyconn.v1.CreateWebhookEvtRequest
	(*UpdateWebhookEvtRequest)(nil), // 1: anyconn.v1.UpdateWebhookEvtRequest
	(*DeleteWebhookEvtRequest)(nil), // 2: anyconn.v1.DeleteWebhookEvtRequest
	(*GetWebhookEvtRequest)(nil),    // 3: anyconn.v1.GetWebhookEvtRequest
	(*WebhookEvtFilter)(nil),        // 4: anyconn.v1.WebhookEvtFilter
	(*ListWebhookEvtRequest)(nil),   // 5: anyconn.v1.ListWebhookEvtRequest
	(*ListWebhookEvtReply)(nil),     // 6: anyconn.v1.ListWebhookEvtReply
	(*WebhookEvt)(nil),              // 7: anyconn.v1.WebhookEvt
	(*timestamppb.Timestamp)(nil),   // 8: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),           // 9: google.protobuf.Empty
}
var file_anyconn_v1_webhookevt_proto_depIdxs = []int32{
	0, // 0: anyconn.v1.UpdateWebhookEvtRequest.evt:type_name -> anyconn.v1.CreateWebhookEvtRequest
	4, // 1: anyconn.v1.ListWebhookEvtRequest.filter:type_name -> anyconn.v1.WebhookEvtFilter
	7, // 2: anyconn.v1.ListWebhookEvtReply.webhookevts:type_name -> anyconn.v1.WebhookEvt
	8, // 3: anyconn.v1.WebhookEvt.created_at:type_name -> google.protobuf.Timestamp
	0, // 4: anyconn.v1.WebhookEvts.CreateWebhookEvt:input_type -> anyconn.v1.CreateWebhookEvtRequest
	1, // 5: anyconn.v1.WebhookEvts.UpdateWebhookEvt:input_type -> anyconn.v1.UpdateWebhookEvtRequest
	2, // 6: anyconn.v1.WebhookEvts.DeleteWebhookEvt:input_type -> anyconn.v1.DeleteWebhookEvtRequest
	3, // 7: anyconn.v1.WebhookEvts.GetWebhookEvt:input_type -> anyconn.v1.GetWebhookEvtRequest
	5, // 8: anyconn.v1.WebhookEvts.ListWebhookEvt:input_type -> anyconn.v1.ListWebhookEvtRequest
	7, // 9: anyconn.v1.WebhookEvts.CreateWebhookEvt:output_type -> anyconn.v1.WebhookEvt
	7, // 10: anyconn.v1.WebhookEvts.UpdateWebhookEvt:output_type -> anyconn.v1.WebhookEvt
	9, // 11: anyconn.v1.WebhookEvts.DeleteWebhookEvt:output_type -> google.protobuf.Empty
	7, // 12: anyconn.v1.WebhookEvts.GetWebhookEvt:output_type -> anyconn.v1.WebhookEvt
	6, // 13: anyconn.v1.WebhookEvts.ListWebhookEvt:output_type -> anyconn.v1.ListWebhookEvtReply
	9, // [9:14] is the sub-list for method output_type
	4, // [4:9] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_anyconn_v1_webhookevt_proto_init() }
func file_anyconn_v1_webhookevt_proto_init() {
	if File_anyconn_v1_webhookevt_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_anyconn_v1_webhookevt_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWebhookEvtRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_webhookevt_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWebhookEvtRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_webhookevt_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteWebhookEvtRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_webhookevt_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWebhookEvtRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_webhookevt_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebhookEvtFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_webhookevt_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWebhookEvtRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_webhookevt_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWebhookEvtReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anyconn_v1_webhookevt_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebhookEvt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anyconn_v1_webhookevt_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_anyconn_v1_webhookevt_proto_goTypes,
		DependencyIndexes: file_anyconn_v1_webhookevt_proto_depIdxs,
		MessageInfos:      file_anyconn_v1_webhookevt_proto_msgTypes,
	}.Build()
	File_anyconn_v1_webhookevt_proto = out.File
	file_anyconn_v1_webhookevt_proto_rawDesc = nil
	file_anyconn_v1_webhookevt_proto_goTypes = nil
	file_anyconn_v1_webhookevt_proto_depIdxs = nil
}
