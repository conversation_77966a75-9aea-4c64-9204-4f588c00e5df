// source: anno/v1/lot.proto
package data

import (
	"anno/internal/biz"
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/errors"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

type lotsRepo struct {
	data *Data
	log  *log.Helper
}

func NewLotsRepo(data *Data, logger log.Logger) biz.LotsRepo {
	return &lotsRepo{data: data, log: log.NewHelper(logger)}
}

func (o *lotsRepo) DoTx(ctx context.Context, fn func(ctx context.Context, tx *gorm.DB) error) (err error) {
	return o.data.DoTx(ctx, fn)
}

func (o *lotsRepo) Create(ctx context.Context, p *biz.Lot) (lot *biz.Lot, err error) {
	err = o.DoTx(ctx, func(ctx context.Context, tx *gorm.DB) error {
		lot, err = Create(ctx, o.data, p)
		if err != nil {
			return err
		}

		// create ally
		if p.Ally == nil {
			p.Ally = &biz.Lotally{}
		}
		p.Ally.ID = lot.ID
		_, err = Create(ctx, o.data, p.Ally)
		if err != nil {
			return err
		}

		// create phases
		for i := range p.Phases {
			p.Phases[i].LotID = lot.ID
		}
		if _, err = BatchCreate(ctx, o.data, p.Phases); err != nil {
			return err
		}
		return nil
	})
	return
}

func (o *lotsRepo) Update(ctx context.Context, p *biz.Lot, fldMask *biz.FieldMask) (lot *biz.Lot, err error) {
	allyFlds := make([]string, 0, 3)
	for _, fld := range biz.LotallyUpdatableFlds.Snakecase() {
		if fldMask.HasField(fld) {
			allyFlds = append(allyFlds, fld)
			fldMask.Delete(fld)
		}
	}

	if p.ID > 0 {
		lot, err = o.GetByID(ctx, p.ID, true)
	} else {
		return nil, errors.NewErrEmptyField(errors.WithFields("lot_id"))
	}
	if err != nil {
		return nil, err
	}

	err = o.DoTx(ctx, func(ctx context.Context, tx *gorm.DB) error {
		if len(allyFlds) > 0 {
			p.Ally.ID = lot.ID
			lot.Ally, err = Update(ctx, o.data, p.Ally, biz.LotallyUpdatableFlds, field.NewMask(allyFlds...))
		}
		if err == nil && !fldMask.IsEmpty() {
			p, err = Update(ctx, o.data, p, biz.LotUpdatableFlds, fldMask)
			if err == nil {
				p.Ally = lot.Ally
				p.Project = lot.Project
				p.Phases = lot.Phases
				lot = p
			}
		}
		return err
	})
	if err != nil {
		return nil, err
	}
	return
}

func (o *lotsRepo) loadAssociations(ctx context.Context, data *Data, lot *biz.Lot) error {
	ally, err := GetByID[biz.Lotally](ctx, data, lot.ID)
	if err != nil {
		return fmt.Errorf("failed to query Lotally: %w", err)
	}
	lot.Ally = ally

	phases, err := o.loadPhases(ctx, data, lot.ID, 0)
	if err != nil {
		return fmt.Errorf("failed to query Lotphase: %w", err)
	}
	lot.Phases = phases
	return err
}

func (o *lotsRepo) GetByID(ctx context.Context, id int64, loadAsso bool) (*biz.Lot, error) {
	if !loadAsso {
		return GetByID[biz.Lot](ctx, o.data, id)
	}
	return GetByID[biz.Lot](ctx, o.data, id, o.loadAssociations)
}

func (o *lotsRepo) DeleteByID(ctx context.Context, id int64) error {
	return Delete(ctx, o.data, &biz.Lot{ID: id})
}

// func (o *lotsRepo) GetUids(ctx context.Context, ids ...int64) (map[int64]string, error) {
// 	type IDPair struct {
// 		ID  int64
// 		Uid string
// 	}
// 	uids := []IDPair{}
// 	err := o.data.WithCtx(ctx).Model(&biz.Lot{}).Select("id, uid").Where("id IN ?", ids).Scan(&uids).Error
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to query uids: %w", err)
// 	}
// 	return lo.SliceToMap(uids, func(e IDPair) (int64, string) { return e.ID, e.Uid }), nil
// }

func (o *lotsRepo) buildListQuery(ctx context.Context, p *biz.LotListFilter) *gorm.DB {
	q := o.data.WithCtx(ctx)
	if len(p.IDs) > 0 {
		q = q.Where(biz.LotSfldID.WithTable(), " IN ?", p.IDs)
	}
	// if p.ProjectID > 0 {
	// 	q = q.Where("lots."+biz.LotSfldProjectID+" = ?", p.ProjectID)
	// }
	if p.OrderID > 0 {
		q = q.Where(biz.LotSfldOrderID.WithTable()+" = ?", p.OrderID)
	}
	if p.OrgUid != "" {
		q = q.Where(biz.LotSfldOrgUid.WithTable()+" = ?", p.OrgUid)
	}
	if p.CreatorUid != "" {
		q = q.Where(biz.LotSfldCreatorUid.WithTable()+" = ?", p.CreatorUid)
	}
	if p.NamePattern != "" {
		q = q.Where(biz.LotSfldName.WithTable()+" LIKE ?", "%"+p.NamePattern+"%")
	}
	if p.Type != "" {
		q = q.Where(biz.LotSfldType.WithTable()+" = ?", p.Type)
	}
	if len(p.States) > 0 {
		q = q.Where(biz.LotSfldState.WithTable()+" IN ?", p.States)
	}

	if p.BizgranteeUid != "" {
		q = q.Joins("JOIN bizgrants ON "+biz.LotSfldOrgUid.WithTable()+" = "+biz.BizgrantSfldOrgUid.WithTable()).
			Where(biz.BizgrantSfldGranteeUid.WithTable()+" = ?", p.BizgranteeUid)
	}
	if p.SpecgranteeUid != "" {
		q = q.Joins("JOIN specgrants ON "+biz.LotSfldID.WithTable()+" = "+biz.SpecgrantSfldItemID.WithTable()).
			Where(biz.SpecgrantSfldItemType.WithTable()+" = ?", biz.PermClsLot).
			Where(biz.SpecgrantSfldGranteeUid.WithTable()+" = ?", p.SpecgranteeUid)
	}
	return q
}

func (o *lotsRepo) List(ctx context.Context, p *biz.LotListFilter, pager biz.Pager) ([]*biz.Lot, error) {
	datas := []*biz.Lot{}
	q := o.buildListQuery(ctx, p).Order("id DESC")
	if len(p.IDs) == 0 {
		q = q.Offset(pager.Offset()).Limit(pager.Pagesz)
	}
	err := q.Find(&datas).Error
	if err != nil {
		return nil, Convert(&biz.Lot{}, err)
	}
	return datas, nil
}

func (o *lotsRepo) Count(ctx context.Context, p *biz.LotListFilter) (int, error) {
	mod := &biz.Lot{}
	var cnt int64
	q := o.buildListQuery(ctx, p)
	err := q.Model(mod).Count(&cnt).Error
	if err != nil {
		return 0, Convert(mod, err)
	}
	return int(cnt), nil
}

func (o *lotsRepo) GetLotIDsByFilter(ctx context.Context, filter *biz.LotListFilter) ([]int64, error) {
	var lotIDs []int64
	err := o.buildListQuery(ctx, filter).Model(&biz.Lot{}).
		Select(biz.LotSfldID.WithTable()).
		Order(biz.LotSfldID.WithTable()).
		Find(&lotIDs).Error
	if err != nil {
		return nil, Convert(&biz.Lotexecutor{}, err)
	}

	return lotIDs, nil
}

func (o *lotsRepo) HasHoldingLots(ctx context.Context, orgUid string) (bool, error) {
	var id int64
	err := o.data.WithCtx(ctx).Model(&biz.Lot{}).
		Joins(fmt.Sprintf("JOIN %s ON %s = %s", biz.LotexecutorTableName, biz.LotSfldID.WithTable(), biz.LotexecutorSfldLotID.WithTable())).
		Where(biz.LotexecutorSfldTeamUid.WithTable()+" = ?", orgUid).
		Where(biz.LotSfldState.WithTable()+" IN ?",
			[]biz.LotState{biz.LotStateUnstart, biz.LotStateInitializing, biz.LotStateOngoing, biz.LotStatePaused}).
		Select(biz.LotSfldID.WithTable()).
		Limit(1).Find(&id).Error
	if err != nil {
		return false, Convert(&biz.Lotexecutor{}, err)
	}
	return id > 0, nil
}

func (o *lotsRepo) GetSampleBits(ctx context.Context, id int64) (biz.SampleBits, error) {
	ally, err := GetByID[biz.Lotally](ctx, o.data, id)
	if err != nil {
		return nil, fmt.Errorf("failed to load Lotally: %w", err)
	}
	return ally.SampleBits, nil
}

func (o *lotsRepo) ChangeState(ctx context.Context, lot *biz.Lot, state biz.LotState) (success bool, err error) {
	r := o.data.WithCtx(ctx).Model(lot).Where(map[string]any{
		biz.LotSfldState.String(): lot.State,
	}).Updates(map[string]any{
		biz.LotSfldState.String(): state,
	})
	return r.RowsAffected > 0, Convert(lot, r.Error)
}

func (o *lotsRepo) loadPhases(ctx context.Context, data *Data, lotID int64, number int, execteams ...string) (phases []*biz.Lotphase, err error) {
	q := data.WithCtx(ctx).Order(field.Join(biz.LotphaseSfldID.String())).
		Where(biz.LotphaseSfldLotID.WithTable()+" = ?", lotID)
	if number > 0 {
		q = q.Where(biz.LotphaseSfldNumber.WithTable()+" = ?", number)
	}
	if len(execteams) == 1 {
		q = q.Where(biz.LotphaseSfldExecteam.WithTable()+" = ?", execteams[0])
	} else if len(execteams) > 1 {
		q = q.Where(biz.LotphaseSfldExecteam.WithTable()+" IN ?", execteams)
	}
	err = q.Find(&phases).Error
	err = Convert(&biz.Lotphase{}, err)
	return
}

func (o *lotsRepo) LoadPhases(ctx context.Context, lotID int64, phase int, execteams ...string) (phases []*biz.Lotphase, err error) {
	return o.loadPhases(ctx, o.data, lotID, phase, execteams...)
}

func (o *lotsRepo) GetPhase(ctx context.Context, lotID int64, phase int, execteam string) (*biz.Lotphase, error) {
	out := &biz.Lotphase{}
	err := o.data.WithCtx(ctx).First(out, map[string]any{
		biz.LotphaseSfldLotID.String():    lotID,
		biz.LotphaseSfldNumber.String():   phase,
		biz.LotphaseSfldExecteam.String(): execteam,
	}).Error
	if err != nil {
		return nil, Convert(out, err)
	}
	return out, nil
}

func (o *lotsRepo) BatchCreatePhase(ctx context.Context, p []*biz.Lotphase) ([]*biz.Lotphase, error) {
	return BatchCreate(ctx, o.data, p)
}

func (o *lotsRepo) UpdatePhase(ctx context.Context, p *biz.Lotphase, fldMask *biz.FieldMask) (*biz.Lotphase, error) {
	return Update(ctx, o.data, p, biz.LotphaseUpdatableFlds, fldMask)
}

func (o *lotsRepo) UpdatePhaseWithIncrement(ctx context.Context, p *biz.Lotphase, increments map[string]int,
	fldMask *biz.FieldMask) (*biz.Lotphase, error) {
	return Updates(ctx, o.data, p, increments, biz.LotphaseUpdatableFlds, fldMask)
}

func (o *lotsRepo) DeletePhases(ctx context.Context, p []*biz.Lotphase) error {
	ids := lo.Map(p, func(e *biz.Lotphase, _ int) int64 { return e.ID })
	return DeleteByIDs[biz.Lotphase](ctx, o.data, ids)
}

func (o *lotsRepo) GetExecteamQuota(ctx context.Context, lotID int64, executorUid string) (*biz.LotJobQuota, error) {
	var lotphase biz.Lotphase
	q := o.data.WithCtx(ctx).Model(&biz.Lotphase{}).
		Select(field.Join(biz.LotphaseSfldID.WithTable(), biz.LotphaseSfldClaimedJobs.WithTable(), biz.LotphaseSfldQuota.WithTable())).
		Joins("JOIN lotexecutors ON "+biz.LotphaseSfldLotID.WithTable()+" = "+biz.LotexecutorSfldLotID.WithTable()+
			" AND "+biz.LotphaseSfldExecteam.WithTable()+" = "+biz.LotexecutorSfldTeamUid.WithTable()).
		Where(biz.LotphaseSfldLotID.WithTable()+" = ?", lotID).
		Where(biz.LotexecutorSfldUserUid.WithTable()+" = ?", executorUid)

	if err := q.First(&lotphase).Error; err != nil {
		return nil, Convert(&biz.Lotphase{}, err)
	}

	return &biz.LotJobQuota{
		LotphaseID:    lotphase.ID,
		ClaimedJobs:   lotphase.ClaimedJobs,
		ExecteamQuota: lotphase.Quota.E,
	}, nil
}

func (o *lotsRepo) AddPhaseExecutors(ctx context.Context, phase *biz.Lotphase, teamUid string, userUids []string) error {
	// a user can only be added as one phase executor in a lot
	//var cnt int64
	//err := o.data.WithCtx(ctx).Model(&biz.Lotexecutor{}).
	//	Where(biz.LotexecutorSfldLotID.WithTable()+" = ?", phase.LotID).
	//	Where(biz.LotexecutorSfldUserUid.WithTable()+" IN ?", userUids).
	//	Count(&cnt).Error
	//if err != nil {
	//	return Convert(&biz.Lotexecutor{}, err)
	//}
	//if cnt > 0 {
	//	return errors.NewErrConflict(errors.WithMessage("users cannot be assigned multiple times in a lot"))
	//}
	lotExecutors := make([]*biz.Lotexecutor, 0)
	err := o.data.WithCtx(ctx).Model(&biz.Lotexecutor{}).
		Where(biz.LotexecutorSfldLotID.WithTable()+" = ?", phase.LotID).
		Where(biz.LotexecutorSfldUserUid.WithTable()+" IN ?", userUids).
		Find(&lotExecutors).Error
	if err != nil {
		return Convert(&biz.Lotexecutor{}, err)
	}
	for _, executor := range lotExecutors {
		if executor.TeamUid == teamUid {
			return errors.NewErrConflict(errors.WithMessage("users cannot be assigned multiple times in a lot"))
		} else {
			// 如果用户被重新分配到了新团队 可以再次重新分配此任务 需先删除原团队已分配任务
			o.log.Info(ctx, " Delete lot executor from different teams ", " executor: ", executor)
			spew.Dump(executor)
			err = o.data.WithCtx(ctx).Model(&biz.Lotexecutor{}).
				Where(biz.LotexecutorSfldID.WithTable()+" = ?", executor.ID).
				Delete(&biz.Lotexecutor{}).Error
			if err != nil {
				return Convert(&biz.Lotexecutor{}, err)
			}
		}
	}

	users := make([]*biz.Lotexecutor, len(userUids))
	for i, uid := range userUids {
		users[i] = &biz.Lotexecutor{
			LotID:   phase.LotID,
			Phase:   phase.Number,
			Subtype: phase.Subtype,
			TeamUid: teamUid,
			UserUid: uid,
		}
	}
	fmt.Println("adding users", users)
	return Convert(&biz.Lotexecutor{}, o.data.WithCtx(ctx).Create(users).Error)
}

func (o *lotsRepo) DeletePhaseExecutors(ctx context.Context, phase *biz.Lotphase, teamUid string, userUids []string) error {
	cond := map[string]any{
		biz.LotexecutorSfldLotID.String():   phase.LotID,
		biz.LotexecutorSfldPhase.String():   phase.Number,
		biz.LotexecutorSfldSubtype.String(): phase.Subtype,
	}
	if teamUid != "" {
		cond[biz.LotexecutorSfldTeamUid.String()] = teamUid
	}
	if len(userUids) > 0 {
		cond[biz.LotexecutorSfldUserUid.String()] = userUids
	}
	err := o.data.WithCtx(ctx).Delete(&biz.Lotexecutor{}, cond).Error
	return Convert(&biz.Lotexecutor{}, err)
}

func (o *lotsRepo) buildPhaseExecutorsQuery(ctx context.Context, phase *biz.Lotphase, teamUid string) *gorm.DB {
	q := o.data.WithCtx(ctx).
		Where(biz.LotexecutorSfldLotID.WithTable()+" = ? ", phase.LotID).
		Where(biz.LotexecutorSfldPhase.WithTable()+" = ? ", phase.Number).
		Where(biz.LotexecutorSfldSubtype.WithTable()+" = ?", phase.Subtype)
	if teamUid != "" {
		q = q.Where(biz.LotexecutorSfldTeamUid.WithTable()+" = ?", teamUid)
	}
	return q
}

func (o *lotsRepo) ListPhaseExecutors(ctx context.Context, phase *biz.Lotphase, teamUid string,
	pager biz.Pager) ([]string, error) {
	datas := []string{}
	q := o.buildPhaseExecutorsQuery(ctx, phase, teamUid).Order("id").Offset(pager.Offset()).Limit(pager.Pagesz)
	err := q.Model(&biz.Lotexecutor{}).Select(biz.LotexecutorSfldUserUid.WithTable()).Find(&datas).Error
	if err != nil {
		return nil, Convert(&biz.Lotexecutor{}, err)
	}
	return datas, nil
}

func (o *lotsRepo) CountPhaseExecutors(ctx context.Context, phase *biz.Lotphase, teamUid string) (int, error) {
	var cnt int64
	q := o.buildPhaseExecutorsQuery(ctx, phase, teamUid)
	err := q.Model(&biz.Lotexecutor{}).Count(&cnt).Error
	if err != nil {
		return 0, Convert(&biz.Lotexecutor{}, err)
	}
	return int(cnt), nil
}

func (o *lotsRepo) ListExecutorsByLotID(ctx context.Context, lotID int64) ([]*biz.Lotexecutor, error) {
	var executors []*biz.Lotexecutor
	err := o.data.WithCtx(ctx).
		Where(biz.LotexecutorSfldLotID.WithTable()+" = ?", lotID).
		Find(&executors).Error
	if err != nil {
		return nil, Convert(&biz.Lotexecutor{}, err)
	}
	return executors, nil
}

func (o *lotsRepo) AddExecutors(ctx context.Context, executors []*biz.Lotexecutor) error {
	const batchSize = 100
	err := o.data.WithCtx(ctx).
		CreateInBatches(executors, batchSize).Error
	if err != nil {
		return Convert(&biz.Lotexecutor{}, err)
	}
	return nil
}
