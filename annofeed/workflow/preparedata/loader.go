package preparedata

import (
	"fmt"
	"os"
	"path/filepath"

	"annofeed/internal/biz"

	"github.com/go-kratos/kratos/v2/errors"
)

func LoadJSONFile[T any](basedir, fpath string) ([]byte, *T, error) {
	fmt.Println("---> LoadJSONFile", fpath)
	data, err := os.ReadFile(filepath.Join(basedir, fpath))
	if err != nil {
		fmt.Println("---> load json file err: ", err)
		if errors.Is(err, os.ErrNotExist) {
			fmt.Println("---> load json file err: file does not exist")
			return nil, nil, nil
		}
		return nil, nil, fmt.Errorf("failed to read file %v: %w", fpath, err)
	}

	var obj T
	err = biz.JSONCodec().Unmarshal(data, &obj)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to unmarshal file %v into object %T: %w", fpath, obj, err)
	}
	return data, &obj, nil
}
