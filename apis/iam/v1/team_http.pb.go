// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: iam/v1/team.proto

package iam

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationTeamsAddMembers = "/iam.v1.Teams/AddMembers"
const OperationTeamsCreateTeam = "/iam.v1.Teams/CreateTeam"
const OperationTeamsDeleteMembers = "/iam.v1.Teams/DeleteMembers"
const OperationTeamsDeleteTeam = "/iam.v1.Teams/DeleteTeam"
const OperationTeamsGetTeam = "/iam.v1.Teams/GetTeam"
const OperationTeamsGetTeamsRoot = "/iam.v1.Teams/GetTeamsRoot"
const OperationTeamsListMembers = "/iam.v1.Teams/ListMembers"
const OperationTeamsListTeam = "/iam.v1.Teams/ListTeam"
const OperationTeamsSetMembersRole = "/iam.v1.Teams/SetMembersRole"
const OperationTeamsUpdateTeam = "/iam.v1.Teams/UpdateTeam"

type TeamsHTTPServer interface {
	// AddMembers send join-team invitation to mentioned users
	AddMembers(context.Context, *AddMembersRequest) (*emptypb.Empty, error)
	CreateTeam(context.Context, *CreateTeamRequest) (*Team, error)
	DeleteMembers(context.Context, *DeleteMembersRequest) (*emptypb.Empty, error)
	DeleteTeam(context.Context, *DeleteTeamRequest) (*emptypb.Empty, error)
	GetTeam(context.Context, *GetTeamRequest) (*Team, error)
	GetTeamsRoot(context.Context, *GetTeamsRootRequest) (*ListTeamReply, error)
	ListMembers(context.Context, *ListMembersRequest) (*ListMembersReply, error)
	ListTeam(context.Context, *ListTeamRequest) (*ListTeamReply, error)
	SetMembersRole(context.Context, *SetMembersRoleRequest) (*emptypb.Empty, error)
	UpdateTeam(context.Context, *UpdateTeamRequest) (*Team, error)
}

func RegisterTeamsHTTPServer(s *http.Server, srv TeamsHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/teams", _Teams_CreateTeam0_HTTP_Handler(srv))
	r.PATCH("/v1/teams/{team.uid}", _Teams_UpdateTeam0_HTTP_Handler(srv))
	r.DELETE("/v1/teams/{uid}", _Teams_DeleteTeam0_HTTP_Handler(srv))
	r.GET("/v1/teams/root", _Teams_GetTeamsRoot0_HTTP_Handler(srv))
	r.GET("/v1/teams/{uid}", _Teams_GetTeam0_HTTP_Handler(srv))
	r.GET("/v1/teams", _Teams_ListTeam0_HTTP_Handler(srv))
	r.GET("/v1/teams/{uid}/members", _Teams_ListMembers0_HTTP_Handler(srv))
	r.POST("/v1/teams/{uid}/members", _Teams_AddMembers0_HTTP_Handler(srv))
	r.DELETE("/v1/teams/{uid}/members", _Teams_DeleteMembers0_HTTP_Handler(srv))
	r.PUT("/v1/teams/{uid}/role", _Teams_SetMembersRole0_HTTP_Handler(srv))
}

func _Teams_CreateTeam0_HTTP_Handler(srv TeamsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateTeamRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTeamsCreateTeam)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateTeam(ctx, req.(*CreateTeamRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Team)
		return ctx.Result(200, reply)
	}
}

func _Teams_UpdateTeam0_HTTP_Handler(srv TeamsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateTeamRequest
		if err := ctx.Bind(&in.Team); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTeamsUpdateTeam)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateTeam(ctx, req.(*UpdateTeamRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Team)
		return ctx.Result(200, reply)
	}
}

func _Teams_DeleteTeam0_HTTP_Handler(srv TeamsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteTeamRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTeamsDeleteTeam)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteTeam(ctx, req.(*DeleteTeamRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Teams_GetTeamsRoot0_HTTP_Handler(srv TeamsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTeamsRootRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTeamsGetTeamsRoot)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTeamsRoot(ctx, req.(*GetTeamsRootRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListTeamReply)
		return ctx.Result(200, reply)
	}
}

func _Teams_GetTeam0_HTTP_Handler(srv TeamsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTeamRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTeamsGetTeam)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTeam(ctx, req.(*GetTeamRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Team)
		return ctx.Result(200, reply)
	}
}

func _Teams_ListTeam0_HTTP_Handler(srv TeamsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListTeamRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTeamsListTeam)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListTeam(ctx, req.(*ListTeamRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListTeamReply)
		return ctx.Result(200, reply)
	}
}

func _Teams_ListMembers0_HTTP_Handler(srv TeamsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListMembersRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTeamsListMembers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListMembers(ctx, req.(*ListMembersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListMembersReply)
		return ctx.Result(200, reply)
	}
}

func _Teams_AddMembers0_HTTP_Handler(srv TeamsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddMembersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTeamsAddMembers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddMembers(ctx, req.(*AddMembersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Teams_DeleteMembers0_HTTP_Handler(srv TeamsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteMembersRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTeamsDeleteMembers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteMembers(ctx, req.(*DeleteMembersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Teams_SetMembersRole0_HTTP_Handler(srv TeamsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetMembersRoleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTeamsSetMembersRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetMembersRole(ctx, req.(*SetMembersRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type TeamsHTTPClient interface {
	AddMembers(ctx context.Context, req *AddMembersRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CreateTeam(ctx context.Context, req *CreateTeamRequest, opts ...http.CallOption) (rsp *Team, err error)
	DeleteMembers(ctx context.Context, req *DeleteMembersRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteTeam(ctx context.Context, req *DeleteTeamRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetTeam(ctx context.Context, req *GetTeamRequest, opts ...http.CallOption) (rsp *Team, err error)
	GetTeamsRoot(ctx context.Context, req *GetTeamsRootRequest, opts ...http.CallOption) (rsp *ListTeamReply, err error)
	ListMembers(ctx context.Context, req *ListMembersRequest, opts ...http.CallOption) (rsp *ListMembersReply, err error)
	ListTeam(ctx context.Context, req *ListTeamRequest, opts ...http.CallOption) (rsp *ListTeamReply, err error)
	SetMembersRole(ctx context.Context, req *SetMembersRoleRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateTeam(ctx context.Context, req *UpdateTeamRequest, opts ...http.CallOption) (rsp *Team, err error)
}

type TeamsHTTPClientImpl struct {
	cc *http.Client
}

func NewTeamsHTTPClient(client *http.Client) TeamsHTTPClient {
	return &TeamsHTTPClientImpl{client}
}

func (c *TeamsHTTPClientImpl) AddMembers(ctx context.Context, in *AddMembersRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/teams/{uid}/members"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationTeamsAddMembers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TeamsHTTPClientImpl) CreateTeam(ctx context.Context, in *CreateTeamRequest, opts ...http.CallOption) (*Team, error) {
	var out Team
	pattern := "/v1/teams"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationTeamsCreateTeam))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TeamsHTTPClientImpl) DeleteMembers(ctx context.Context, in *DeleteMembersRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/teams/{uid}/members"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationTeamsDeleteMembers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TeamsHTTPClientImpl) DeleteTeam(ctx context.Context, in *DeleteTeamRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/teams/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationTeamsDeleteTeam))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TeamsHTTPClientImpl) GetTeam(ctx context.Context, in *GetTeamRequest, opts ...http.CallOption) (*Team, error) {
	var out Team
	pattern := "/v1/teams/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationTeamsGetTeam))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TeamsHTTPClientImpl) GetTeamsRoot(ctx context.Context, in *GetTeamsRootRequest, opts ...http.CallOption) (*ListTeamReply, error) {
	var out ListTeamReply
	pattern := "/v1/teams/root"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationTeamsGetTeamsRoot))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TeamsHTTPClientImpl) ListMembers(ctx context.Context, in *ListMembersRequest, opts ...http.CallOption) (*ListMembersReply, error) {
	var out ListMembersReply
	pattern := "/v1/teams/{uid}/members"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationTeamsListMembers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TeamsHTTPClientImpl) ListTeam(ctx context.Context, in *ListTeamRequest, opts ...http.CallOption) (*ListTeamReply, error) {
	var out ListTeamReply
	pattern := "/v1/teams"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationTeamsListTeam))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TeamsHTTPClientImpl) SetMembersRole(ctx context.Context, in *SetMembersRoleRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/teams/{uid}/role"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationTeamsSetMembersRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TeamsHTTPClientImpl) UpdateTeam(ctx context.Context, in *UpdateTeamRequest, opts ...http.CallOption) (*Team, error) {
	var out Team
	pattern := "/v1/teams/{team.uid}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationTeamsUpdateTeam))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in.Team, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
