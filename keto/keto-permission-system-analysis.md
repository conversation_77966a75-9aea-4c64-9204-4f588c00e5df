# Keto 权限系统分析

## 1. 系统架构概述

Keto 是一个基于关系元组的权限系统，主要由以下几个核心组件组成：

1. **命名空间定义（namespaces.keto.ts）**：定义权限模型和关系
2. **关系元组（tuples.txt）**：定义具体的权限规则
3. **权限检查引擎**：执行权限验证逻辑
4. **数据存储层**：存储关系元组
5. **API 服务**：提供权限查询接口

## 2. 命名空间定义（namespaces.keto.ts）

```typescript
class IamRole implements Namespace {
  related: {
    parents: IamGroup[]
    policies: IamPolicy[]
    perms: (IamPerm | SubjectSet<IamRole, "perms">)[]
  }

  permits = {
    has_perm: (ctx: Context, perm: string): boolean => this.related.perms.includes(perm),

    check: (ctx: Context, perm: string): boolean =>
      IamRole:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),
```

命名空间定义了权限模型的结构和检查逻辑：

1. **命名空间类型**：项目定义了多种命名空间类型，如 `IamRole`、`IamPolicy`、`IamGroup`、`IamUser` 等
2. **关系定义**：每个命名空间定义了与其他命名空间的关系，如 `parents`、`policies`、`perms` 等
3. **权限检查函数**：每个命名空间定义了 `permits` 对象，包含权限检查逻辑

### 关键命名空间说明

1. **IamRole**：角色定义，包含权限集合
   - 全局角色格式：`<role-name>`（如 `admin`、`viewer`）
   - 组织角色格式：`<org-uid>.<role-name>`（如 `team-1.owner`）

2. **IamPolicy**：策略定义，将角色与用户/组关联
   - 系统策略格式：`sys/<n>`（如 `sys/admin`）
   - 资源策略格式：`<resource-ns>/<resource-name>.<role-name>`

3. **IamGroup**：用户组定义
   - 格式：`<group-uid>`（如 `sys/admin`、`team-1`）

4. **IamUser**：用户定义
   - 格式：`<user-uid>`

## 3. 关系元组定义（tuples.txt）

```
# global roles
IamRole:root#perms@IamRole:admin#perms
IamRole:root#policies@IamPolicy:sys/admin
IamRole:service#perms@IamRole:admin#perms
IamRole:service#policies@IamPolicy:sys/admin

IamRole:admin#perms@IamRole:iam.owner#perms
IamRole:admin#perms@IamRole:anno.owner#perms
IamRole:admin#perms@IamRole:anno.regulator#perms
IamRole:admin#perms@IamRole:annofeed.owner#perms
IamRole:admin#perms@IamRole:fe.owner#perms
IamRole:admin#policies@IamPolicy:sys/admin
```

关系元组定义了具体的权限规则，格式为：`命名空间:对象#关系@主体`

例如：`IamRole:admin#perms@IamRole:iam.owner#perms` 表示：
- `admin` 角色拥有 `iam.owner` 角色的所有权限
- 命名空间：`IamRole`
- 对象：`admin`
- 关系：`perms`
- 主体：`IamRole:iam.owner#perms`（主体集）

### 关系元组类型

1. **角色权限继承**：`IamRole:roleA#perms@IamRole:roleB#perms`
2. **角色策略关联**：`IamRole:roleA#policies@IamPolicy:policyA`
3. **策略角色关联**：`IamPolicy:policyA#roles@IamRole:roleA`
4. **策略用户关联**：`IamPolicy:policyA#users@IamUser:userA`
5. **组成员关联**：`IamGroup:groupA#members@IamUser:userA`

## 4. 权限系统执行流程

### 4.1 系统启动流程

1. **加载命名空间定义**
   - 从 `namespaces.keto.ts` 文件加载命名空间定义
   - 解析命名空间类、关系和权限检查函数

2. **加载关系元组**
   - 从数据库或 `tuples.txt` 文件加载关系元组
   - 构建权限关系图

### 4.2 权限检查流程

当进行权限检查时（例如检查用户是否有某个权限），系统执行以下步骤：

1. **接收权限检查请求**
   - 通过 API 接收权限检查请求
   - 解析请求参数（命名空间、对象、关系、主体）

2. **执行权限检查**
   - 调用 `Engine.CheckIsMember` 方法
   - 该方法调用 `CheckRelationTuple` 进行实际检查

3. **权限检查逻辑**
   - 系统调用 `checkIsAllowed` 方法，该方法执行以下检查：
     - 检查缓存（如果启用）
     - 检查直接关系（`checkDirect`）
     - 检查主体集扩展（`checkExpandSubject`）
     - 检查主体集重写（`checkSubjectSetRewrite`）

4. **递归检查**
   - 系统递归检查权限关系，直到找到匹配或达到最大深度
   - 最大深度由配置文件中的 `limit.max_read_depth` 控制

### 4.3 具体检查示例

以检查用户是否有查看某页面的权限为例：

1. 检查用户是否直接有权限：`FePage:page1#policies@IamPolicy:policy1` 和 `IamPolicy:policy1#users@IamUser:user1`
2. 检查用户所在组是否有权限：`IamGroup:group1#members@IamUser:user1` 和 `FePage:page1#policies@IamPolicy:policy2` 和 `IamPolicy:policy2#users@IamGroup:group1#members`
3. 检查用户角色是否有权限：通过角色继承关系检查

## 5. 权限模型特点

### 5.1 基于关系的权限模型

Keto 使用基于关系的权限模型，而不是传统的基于角色的访问控制（RBAC）或访问控制列表（ACL）。这种模型更灵活，可以表达复杂的权限关系。

### 5.2 权限继承机制

系统支持多种权限继承机制：

1. **角色继承**：角色可以继承其他角色的权限
   ```
   IamRole:viewer#perms@IamRole:iam.viewer#perms
   IamRole:editor#perms@IamRole:viewer#perms
   ```

2. **组继承**：组可以继承父组的权限
   ```
   IamGroup:team-1#parents@IamGroup:department-1
   ```

3. **策略继承**：通过策略关联实现权限继承
   ```
   IamRole:admin#policies@IamPolicy:sys/admin
   IamPolicy:sys/admin#users@IamGroup:sys/admin#members
   ```

### 5.3 通配符和全局权限

系统支持通配符和全局权限检查：

1. **通配符对象**：`IamRole:*#policies@IamPolicy:sys/admin`
2. **全局权限检查**：`IamRole:"*".permits.check(ctx, perm)`

### 5.4 权限检查优化

系统使用多种优化技术提高权限检查性能：

1. **缓存**：使用内存缓存存储常用权限检查结果
2. **预展开**：预先计算和存储权限关系
3. **深度限制**：限制递归检查深度，防止性能问题

## 6. 实际应用场景

该权限系统适用于多种应用场景：

1. **多租户系统**：通过组织和团队隔离权限
2. **复杂业务流程**：支持工作流和审批流程的权限控制
3. **细粒度访问控制**：可以精确控制到资源级别的权限

## 总结

Keto 权限系统是一个基于关系的灵活权限系统，通过 namespaces.keto.ts 定义权限模型，通过 tuples.txt 定义具体权限规则。系统执行权限检查时，会递归检查直接关系、主体集扩展和主体集重写，直到找到匹配或达到最大深度。

该系统的主要优势在于：
1. 灵活的权限模型，可以表达复杂的权限关系
2. 支持多种权限继承机制
3. 高性能的权限检查，通过缓存和预展开优化
4. 适用于多种应用场景，特别是需要细粒度访问控制的系统

您的项目中使用了这个权限系统来控制对各种资源（如用户、组、角色、标注任务等）的访问权限，通过定义复杂的权限关系来满足业务需求。

## 深入分析 namespaces.keto.ts 的执行流程

`namespaces.keto.ts` 文件是 Keto 权限系统的核心配置文件，它定义了权限模型的结构和检查逻辑。让我们更深入地分析这个文件的执行流程：

### 1. 命名空间加载过程

当 Keto 服务启动时，系统会执行以下步骤来加载命名空间定义：

1. **读取配置文件**：从 `config/keto.yml` 中读取命名空间文件位置
   ```yaml
   namespaces:
     location: file:///Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/keto/namespaces.keto.ts
     use_cache: true
   ```

2. **解析命名空间文件**：
   - 系统调用 `readNamespaceFile` 函数读取文件内容
   - 使用 TypeScript 解析器解析文件内容
   - 将解析结果转换为内部命名空间对象

3. **注册命名空间**：
   - 将解析后的命名空间注册到命名空间管理器中
   - 构建命名空间之间的关系图

### 2. 命名空间结构分析

```typescript
class IamRole implements Namespace {
  related: {
    parents: IamGroup[]
    policies: IamPolicy[]
    perms: (IamPerm | SubjectSet<IamRole, "perms">)[]
  }

  permits = {
    has_perm: (ctx: Context, perm: string): boolean => this.related.perms.includes(perm),
    check: (ctx: Context, perm: string): boolean =>
      IamRole:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),
```

每个命名空间类定义包含两个主要部分：

1. **关系定义（related）**：
   - 定义与其他命名空间的关系
   - 例如，`IamRole` 与 `IamGroup`、`IamPolicy` 和 `IamPerm` 的关系

2. **权限检查函数（permits）**：
   - 定义权限检查逻辑
   - 包含通用检查函数（如 `check`）和特定操作函数（如 `get`、`list`、`update` 等）

### 3. 权限检查函数执行流程

以 `IamRole` 的 `check` 函数为例，权限检查的执行流程如下：

1. **全局权限检查**：`IamRole:"*".permits.check(ctx, perm)`
   - 检查是否有全局权限规则适用于所有角色

2. **父级权限检查**：`this.related.parents.traverse((p) => p.permits.check(ctx, perm))`
   - 递归检查父级组是否有相应权限

3. **策略权限检查**：`this.related.policies.traverse((p) => p.permits.check(ctx, perm))`
   - 检查关联策略是否授予相应权限

4. **特定操作权限检查**：如 `get`、`list` 等
   - 这些函数通过调用 `check` 函数并传入特定权限名称来实现
   - 例如：`get: (ctx: Context): boolean => this.permits.check(ctx, "IamRole.get")`

### 4. 权限检查的实际执行

当系统需要检查权限时，会执行以下步骤：

1. **构建关系元组**：
   ```go
   tuple := &relationtuple.RelationTuple{
       Namespace: "IamRole",
       Object:    objectID,
       Relation:  "get",
       Subject:   &SubjectID{ID: userID},
   }
   ```

2. **调用权限引擎**：
   ```go
   allowed, err := engine.CheckIsMember(ctx, tuple, maxDepth)
   ```

3. **执行权限检查**：
   - 系统调用 `checkIsAllowed` 方法
   - 该方法会执行直接检查、主体集扩展和主体集重写
   - 递归检查权限关系，直到找到匹配或达到最大深度

4. **返回检查结果**：
   - 如果找到匹配的权限规则，返回 `true`
   - 否则返回 `false`

## 命名空间与关系元组的交互

`namespaces.keto.ts` 中的命名空间定义与 `tuples.txt` 中的关系元组紧密交互：

1. **命名空间定义权限模型**：
   - 定义可能的关系类型（如 `parents`、`policies`、`perms` 等）
   - 定义权限检查逻辑（如何遍历关系图）

2. **关系元组定义具体权限**：
   - 定义具体的权限规则（谁有什么权限）
   - 例如：`IamRole:admin#perms@IamRole:iam.owner#perms`

3. **权限检查过程**：
   - 系统根据命名空间定义的检查逻辑遍历关系图
   - 使用关系元组作为图的边，检查是否存在从主体到对象的路径

## 实际应用示例

让我们通过一个具体示例来说明整个流程：

假设我们要检查用户 `user1` 是否有查看角色 `role1` 的权限：

1. **构建关系元组**：
   ```
   IamRole:role1#get@IamUser:user1
   ```

2. **执行权限检查**：
   - 系统首先检查是否有直接关系：用户 `user1` 是否直接有查看 `role1` 的权限
   - 然后检查用户所在组：用户 `user1` 是否属于有权限的组
   - 接着检查用户角色：用户 `user1` 是否拥有有权限的角色
   - 最后检查全局规则：是否有适用于所有用户的全局规则

3. **权限检查路径示例**：
   - 用户 `user1` 属于组 `group1`：`IamGroup:group1#members@IamUser:user1`
   - 组 `group1` 关联策略 `policy1`：`IamGroup:group1#policies@IamPolicy:policy1`
   - 策略 `policy1` 关联角色 `viewer`：`IamPolicy:policy1#roles@IamRole:viewer`
   - 角色 `viewer` 有查看权限：`IamRole:viewer#<EMAIL>`

4. **返回结果**：
   - 如果找到有效路径，返回 `true`（允许访问）
   - 否则返回 `false`（拒绝访问）

## 总结

Keto 权限系统通过 `namespaces.keto.ts` 定义权限模型和检查逻辑，通过 `tuples.txt` 定义具体权限规则。系统在执行权限检查时，会根据命名空间定义的逻辑遍历关系图，检查是否存在从主体到对象的有效路径。

这种基于关系的权限模型非常灵活，可以表达复杂的权限关系，适用于需要细粒度访问控制的系统。通过合理设计命名空间和关系元组，可以实现各种复杂的权限控制需求。
