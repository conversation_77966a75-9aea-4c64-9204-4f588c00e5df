package biz

import (
	"context"
	"fmt"
	"time"

	"anno/api/client"
	"anno/internal/mq"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"

	"gorm.io/gorm"
)

// var SupportedSpecgrantItemType = []string{PermClsLot}

// specific grant
type Specgrant struct {
	ID         int64     `json:"id" gorm:"default:null"`
	GrantorUid string    `json:"grantor_id" gorm:"default:null"`
	GranteeUid string    `json:"grantee_id" gorm:"default:null"`
	ItemID     int64     `json:"item_id" gorm:"default:null"`
	ItemType   string    `json:"item_type" gorm:"default:null"`
	OrgUid     string    `json:"org_id" gorm:"default:null"` // item owner orgnization
	CreatedAt  time.Time `json:"created_at" gorm:"default:null"`
}

const SpecgrantTableName = "specgrants"

type SpecgrantSfld string

func (o SpecgrantSfld) String() string { return string(o) }

func (o SpecgrantSfld) WithTable() string { return SpecgrantTableName + "." + string(o) }

var (
	specgrant_             = field.RegObject(&Specgrant{})
	SpecgrantUpdatableFlds = field.NewModel(SpecgrantTableName, specgrant_)

	SpecgrantSfldID         = SpecgrantSfld(field.Sname(&specgrant_.ID))
	SpecgrantSfldGrantorUid = SpecgrantSfld(field.Sname(&specgrant_.GrantorUid))
	SpecgrantSfldGranteeUid = SpecgrantSfld(field.Sname(&specgrant_.GranteeUid))
	SpecgrantSfldOrgUid     = SpecgrantSfld(field.Sname(&specgrant_.OrgUid))
	SpecgrantSfldItemID     = SpecgrantSfld(field.Sname(&specgrant_.ItemID))
	SpecgrantSfldItemType   = SpecgrantSfld(field.Sname(&specgrant_.ItemType))
)

type SpecgrantFilter struct {
	GrantorUid string
	GranteeUid string
	OrgUid     string
	ItemIDs    []int64
	ItemType   string
}

type SpecgrantsRepo interface {
	DoTx(ctx context.Context, fn func(ctx context.Context, tx Tx) error) (err error)

	Create(context.Context, *Specgrant) (*Specgrant, error)
	DeleteByFilter(context.Context, *SpecgrantFilter) ([]int64, error)
	List(context.Context, *SpecgrantFilter, Pager) (grants []*Specgrant, nextPageToken string, err error)
}

type SpecgrantsBiz struct {
	repo SpecgrantsRepo
	log  *log.Helper
}

func NewSpecgrantsBiz(repo SpecgrantsRepo, logger log.Logger) *SpecgrantsBiz {
	return &SpecgrantsBiz{repo: repo, log: log.NewHelper(logger)}
}

func (o *SpecgrantsBiz) Create(ctx context.Context, p *Specgrant) (g *Specgrant, err error) {
	err = o.repo.DoTx(ctx, func(ctx context.Context, tx *gorm.DB) error {
		g, err = o.repo.Create(ctx, p)
		if err != nil {
			return fmt.Errorf("failed to create Specgrant: %w", err)
		}

		// grant anno permissions to KAM
		resource := ResourceScope(p.ItemType, kid.StringID(p.ItemID))
		users := []string{client.UserScope(p.GranteeUid)}
		_, err = client.CreatePolicy(ctx, resource, RoleAnnoPM, users)
		if err != nil {
			return fmt.Errorf("failed to create access policy: %w", err)
		}
		o.log.Info(ctx, "grant PM access on item to user", "item", resource, "user", users[0])

		if err := mq.PublishEvt(ctx, EvtTypeAnnoSpecgrant, EvtSubtypeCreate, g); err != nil {
			return fmt.Errorf("failed to publish specgrant create event: %w", err)
		}

		return err
	})
	return
}

func (o *SpecgrantsBiz) DeleteByFilter(ctx context.Context, f *SpecgrantFilter) error {
	// TODO: query grants with the filter and revoke all grants
	for _, id := range f.ItemIDs {
		err := client.RevokeUsersRole(ctx, f.ItemType, kid.StringID(id), RoleAnnoPM, client.UserScope(f.GranteeUid))
		if err != nil {
			return fmt.Errorf("failed to revoke user role: %w", err)
		}
	}

	return o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		specgrantIDs, err := o.repo.DeleteByFilter(ctx, f)
		if err != nil {
			return fmt.Errorf("failed to delete Specgrant: %w", err)
		}
		if len(specgrantIDs) == 0 {
			return nil
		}

		if err := mq.PublishEvt(ctx, EvtTypeAnnoSpecgrant, EvtSubtypeDelete, specgrantIDs); err != nil {
			return fmt.Errorf("failed to publish specgrant delete event: %w", err)
		}

		return nil
	})
}

func (o *SpecgrantsBiz) List(ctx context.Context, f *SpecgrantFilter, p Pager) (
	grants []*Specgrant, nextPageToken string, err error) {
	return o.repo.List(ctx, f, p)
}
