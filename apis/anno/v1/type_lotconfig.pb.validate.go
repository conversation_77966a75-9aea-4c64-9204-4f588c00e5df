// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/type_lotconfig.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AnnoCommentReason with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AnnoCommentReason) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnnoCommentReason with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AnnoCommentReasonMultiError, or nil if none found.
func (m *AnnoCommentReason) ValidateAll() error {
	return m.validate(true)
}

func (m *AnnoCommentReason) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AnnoCommentReasonMultiError(errors)
	}

	return nil
}

// AnnoCommentReasonMultiError is an error wrapping multiple validation errors
// returned by AnnoCommentReason.ValidateAll() if the designated constraints
// aren't met.
type AnnoCommentReasonMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnoCommentReasonMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnoCommentReasonMultiError) AllErrors() []error { return m }

// AnnoCommentReasonValidationError is the validation error returned by
// AnnoCommentReason.Validate if the designated constraints aren't met.
type AnnoCommentReasonValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnoCommentReasonValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnoCommentReasonValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnoCommentReasonValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnoCommentReasonValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnoCommentReasonValidationError) ErrorName() string {
	return "AnnoCommentReasonValidationError"
}

// Error satisfies the builtin error interface
func (e AnnoCommentReasonValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnoCommentReason.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnoCommentReasonValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnoCommentReasonValidationError{}

// Validate checks the field values on Lotontologies with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Lotontologies) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lotontologies with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LotontologiesMultiError, or
// nil if none found.
func (m *Lotontologies) ValidateAll() error {
	return m.validate(true)
}

func (m *Lotontologies) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetAttrs()))
		i := 0
		for key := range m.GetAttrs() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetAttrs()[key]
			_ = val

			// no validation rules for Attrs[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, LotontologiesValidationError{
							field:  fmt.Sprintf("Attrs[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, LotontologiesValidationError{
							field:  fmt.Sprintf("Attrs[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return LotontologiesValidationError{
						field:  fmt.Sprintf("Attrs[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if all {
		switch v := interface{}(m.GetElemAttrs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LotontologiesValidationError{
					field:  "ElemAttrs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LotontologiesValidationError{
					field:  "ElemAttrs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetElemAttrs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LotontologiesValidationError{
				field:  "ElemAttrs",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRawdataAttrs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LotontologiesValidationError{
					field:  "RawdataAttrs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LotontologiesValidationError{
					field:  "RawdataAttrs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRawdataAttrs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LotontologiesValidationError{
				field:  "RawdataAttrs",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetGroups() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LotontologiesValidationError{
						field:  fmt.Sprintf("Groups[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LotontologiesValidationError{
						field:  fmt.Sprintf("Groups[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LotontologiesValidationError{
					field:  fmt.Sprintf("Groups[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetJobAttrs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LotontologiesValidationError{
					field:  "JobAttrs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LotontologiesValidationError{
					field:  "JobAttrs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJobAttrs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LotontologiesValidationError{
				field:  "JobAttrs",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LotontologiesMultiError(errors)
	}

	return nil
}

// LotontologiesMultiError is an error wrapping multiple validation errors
// returned by Lotontologies.ValidateAll() if the designated constraints
// aren't met.
type LotontologiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LotontologiesMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LotontologiesMultiError) AllErrors() []error { return m }

// LotontologiesValidationError is the validation error returned by
// Lotontologies.Validate if the designated constraints aren't met.
type LotontologiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LotontologiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LotontologiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LotontologiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LotontologiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LotontologiesValidationError) ErrorName() string { return "LotontologiesValidationError" }

// Error satisfies the builtin error interface
func (e LotontologiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLotontologies.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LotontologiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LotontologiesValidationError{}

// Validate checks the field values on Lotphase with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Lotphase) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lotphase with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LotphaseMultiError, or nil
// if none found.
func (m *Lotphase) ValidateAll() error {
	return m.validate(true)
}

func (m *Lotphase) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Number

	// no validation rules for Name

	if _, ok := _Lotphase_Type_NotInLookup[m.GetType()]; ok {
		err := LotphaseValidationError{
			field:  "Type",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := Lotphase_Type_Enum_name[int32(m.GetType())]; !ok {
		err := LotphaseValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Editable

	// no validation rules for SamplePercent

	// no validation rules for MinSkillLevel

	// no validation rules for Timeout

	// no validation rules for Merge

	for idx, item := range m.GetExecteams() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LotphaseValidationError{
						field:  fmt.Sprintf("Execteams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LotphaseValidationError{
						field:  fmt.Sprintf("Execteams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LotphaseValidationError{
					field:  fmt.Sprintf("Execteams[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ClaimPolicy

	if len(errors) > 0 {
		return LotphaseMultiError(errors)
	}

	return nil
}

// LotphaseMultiError is an error wrapping multiple validation errors returned
// by Lotphase.ValidateAll() if the designated constraints aren't met.
type LotphaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LotphaseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LotphaseMultiError) AllErrors() []error { return m }

// LotphaseValidationError is the validation error returned by
// Lotphase.Validate if the designated constraints aren't met.
type LotphaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LotphaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LotphaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LotphaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LotphaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LotphaseValidationError) ErrorName() string { return "LotphaseValidationError" }

// Error satisfies the builtin error interface
func (e LotphaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLotphase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LotphaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LotphaseValidationError{}

var _Lotphase_Type_NotInLookup = map[Lotphase_Type_Enum]struct{}{
	0: {},
}

// Validate checks the field values on DataConverter with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DataConverter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataConverter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DataConverterMultiError, or
// nil if none found.
func (m *DataConverter) ValidateAll() error {
	return m.validate(true)
}

func (m *DataConverter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Runtime

	// no validation rules for Uri

	if len(errors) > 0 {
		return DataConverterMultiError(errors)
	}

	return nil
}

// DataConverterMultiError is an error wrapping multiple validation errors
// returned by DataConverter.ValidateAll() if the designated constraints
// aren't met.
type DataConverterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataConverterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataConverterMultiError) AllErrors() []error { return m }

// DataConverterValidationError is the validation error returned by
// DataConverter.Validate if the designated constraints aren't met.
type DataConverterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataConverterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataConverterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataConverterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataConverterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataConverterValidationError) ErrorName() string { return "DataConverterValidationError" }

// Error satisfies the builtin error interface
func (e DataConverterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataConverter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataConverterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataConverterValidationError{}

// Validate checks the field values on OutConfig with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OutConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OutConfigMultiError, or nil
// if none found.
func (m *OutConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *OutConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetExporter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutConfigValidationError{
					field:  "Exporter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutConfigValidationError{
					field:  "Exporter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExporter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutConfigValidationError{
				field:  "Exporter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := OutConfig_Encoder_Enum_name[int32(m.GetEncoder())]; !ok {
		err := OutConfigValidationError{
			field:  "Encoder",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Layout

	// no validation rules for Style

	if all {
		switch v := interface{}(m.GetConverter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutConfigValidationError{
					field:  "Converter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutConfigValidationError{
					field:  "Converter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConverter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutConfigValidationError{
				field:  "Converter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OutConfigMultiError(errors)
	}

	return nil
}

// OutConfigMultiError is an error wrapping multiple validation errors returned
// by OutConfig.ValidateAll() if the designated constraints aren't met.
type OutConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutConfigMultiError) AllErrors() []error { return m }

// OutConfigValidationError is the validation error returned by
// OutConfig.Validate if the designated constraints aren't met.
type OutConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutConfigValidationError) ErrorName() string { return "OutConfigValidationError" }

// Error satisfies the builtin error interface
func (e OutConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutConfigValidationError{}

// Validate checks the field values on AnnoCommentReason_Reasons with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AnnoCommentReason_Reasons) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnnoCommentReason_Reasons with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AnnoCommentReason_ReasonsMultiError, or nil if none found.
func (m *AnnoCommentReason_Reasons) ValidateAll() error {
	return m.validate(true)
}

func (m *AnnoCommentReason_Reasons) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Class

	if len(errors) > 0 {
		return AnnoCommentReason_ReasonsMultiError(errors)
	}

	return nil
}

// AnnoCommentReason_ReasonsMultiError is an error wrapping multiple validation
// errors returned by AnnoCommentReason_Reasons.ValidateAll() if the
// designated constraints aren't met.
type AnnoCommentReason_ReasonsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnoCommentReason_ReasonsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnoCommentReason_ReasonsMultiError) AllErrors() []error { return m }

// AnnoCommentReason_ReasonsValidationError is the validation error returned by
// AnnoCommentReason_Reasons.Validate if the designated constraints aren't met.
type AnnoCommentReason_ReasonsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnoCommentReason_ReasonsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnoCommentReason_ReasonsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnoCommentReason_ReasonsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnoCommentReason_ReasonsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnoCommentReason_ReasonsValidationError) ErrorName() string {
	return "AnnoCommentReason_ReasonsValidationError"
}

// Error satisfies the builtin error interface
func (e AnnoCommentReason_ReasonsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnoCommentReason_Reasons.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnoCommentReason_ReasonsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnoCommentReason_ReasonsValidationError{}

// Validate checks the field values on Lotontologies_Group with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Lotontologies_Group) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lotontologies_Group with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Lotontologies_GroupMultiError, or nil if none found.
func (m *Lotontologies_Group) ValidateAll() error {
	return m.validate(true)
}

func (m *Lotontologies_Group) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	for idx, item := range m.GetLabels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, Lotontologies_GroupValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, Lotontologies_GroupValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return Lotontologies_GroupValidationError{
					field:  fmt.Sprintf("Labels[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return Lotontologies_GroupMultiError(errors)
	}

	return nil
}

// Lotontologies_GroupMultiError is an error wrapping multiple validation
// errors returned by Lotontologies_Group.ValidateAll() if the designated
// constraints aren't met.
type Lotontologies_GroupMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Lotontologies_GroupMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Lotontologies_GroupMultiError) AllErrors() []error { return m }

// Lotontologies_GroupValidationError is the validation error returned by
// Lotontologies_Group.Validate if the designated constraints aren't met.
type Lotontologies_GroupValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Lotontologies_GroupValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Lotontologies_GroupValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Lotontologies_GroupValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Lotontologies_GroupValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Lotontologies_GroupValidationError) ErrorName() string {
	return "Lotontologies_GroupValidationError"
}

// Error satisfies the builtin error interface
func (e Lotontologies_GroupValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLotontologies_Group.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Lotontologies_GroupValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Lotontologies_GroupValidationError{}

// Validate checks the field values on Lotphase_Type with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Lotphase_Type) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lotphase_Type with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Lotphase_TypeMultiError, or
// nil if none found.
func (m *Lotphase_Type) ValidateAll() error {
	return m.validate(true)
}

func (m *Lotphase_Type) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Lotphase_TypeMultiError(errors)
	}

	return nil
}

// Lotphase_TypeMultiError is an error wrapping multiple validation errors
// returned by Lotphase_Type.ValidateAll() if the designated constraints
// aren't met.
type Lotphase_TypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Lotphase_TypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Lotphase_TypeMultiError) AllErrors() []error { return m }

// Lotphase_TypeValidationError is the validation error returned by
// Lotphase_Type.Validate if the designated constraints aren't met.
type Lotphase_TypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Lotphase_TypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Lotphase_TypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Lotphase_TypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Lotphase_TypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Lotphase_TypeValidationError) ErrorName() string { return "Lotphase_TypeValidationError" }

// Error satisfies the builtin error interface
func (e Lotphase_TypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLotphase_Type.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Lotphase_TypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Lotphase_TypeValidationError{}

// Validate checks the field values on Lotphase_Quota with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Lotphase_Quota) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lotphase_Quota with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Lotphase_QuotaMultiError,
// or nil if none found.
func (m *Lotphase_Quota) ValidateAll() error {
	return m.validate(true)
}

func (m *Lotphase_Quota) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetMin(); val < 0 || val > 100 {
		err := Lotphase_QuotaValidationError{
			field:  "Min",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetMax(); val < 0 || val > 100 {
		err := Lotphase_QuotaValidationError{
			field:  "Max",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return Lotphase_QuotaMultiError(errors)
	}

	return nil
}

// Lotphase_QuotaMultiError is an error wrapping multiple validation errors
// returned by Lotphase_Quota.ValidateAll() if the designated constraints
// aren't met.
type Lotphase_QuotaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Lotphase_QuotaMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Lotphase_QuotaMultiError) AllErrors() []error { return m }

// Lotphase_QuotaValidationError is the validation error returned by
// Lotphase_Quota.Validate if the designated constraints aren't met.
type Lotphase_QuotaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Lotphase_QuotaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Lotphase_QuotaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Lotphase_QuotaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Lotphase_QuotaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Lotphase_QuotaValidationError) ErrorName() string { return "Lotphase_QuotaValidationError" }

// Error satisfies the builtin error interface
func (e Lotphase_QuotaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLotphase_Quota.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Lotphase_QuotaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Lotphase_QuotaValidationError{}

// Validate checks the field values on Lotphase_Execteam with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *Lotphase_Execteam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lotphase_Execteam with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Lotphase_ExecteamMultiError, or nil if none found.
func (m *Lotphase_Execteam) ValidateAll() error {
	return m.validate(true)
}

func (m *Lotphase_Execteam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Execteam

	if all {
		switch v := interface{}(m.GetQuota()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Lotphase_ExecteamValidationError{
					field:  "Quota",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Lotphase_ExecteamValidationError{
					field:  "Quota",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuota()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Lotphase_ExecteamValidationError{
				field:  "Quota",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClaimedJobs

	if len(errors) > 0 {
		return Lotphase_ExecteamMultiError(errors)
	}

	return nil
}

// Lotphase_ExecteamMultiError is an error wrapping multiple validation errors
// returned by Lotphase_Execteam.ValidateAll() if the designated constraints
// aren't met.
type Lotphase_ExecteamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Lotphase_ExecteamMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Lotphase_ExecteamMultiError) AllErrors() []error { return m }

// Lotphase_ExecteamValidationError is the validation error returned by
// Lotphase_Execteam.Validate if the designated constraints aren't met.
type Lotphase_ExecteamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Lotphase_ExecteamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Lotphase_ExecteamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Lotphase_ExecteamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Lotphase_ExecteamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Lotphase_ExecteamValidationError) ErrorName() string {
	return "Lotphase_ExecteamValidationError"
}

// Error satisfies the builtin error interface
func (e Lotphase_ExecteamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLotphase_Execteam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Lotphase_ExecteamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Lotphase_ExecteamValidationError{}

// Validate checks the field values on Lotphase_ClaimPolicy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Lotphase_ClaimPolicy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lotphase_ClaimPolicy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Lotphase_ClaimPolicyMultiError, or nil if none found.
func (m *Lotphase_ClaimPolicy) ValidateAll() error {
	return m.validate(true)
}

func (m *Lotphase_ClaimPolicy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Lotphase_ClaimPolicyMultiError(errors)
	}

	return nil
}

// Lotphase_ClaimPolicyMultiError is an error wrapping multiple validation
// errors returned by Lotphase_ClaimPolicy.ValidateAll() if the designated
// constraints aren't met.
type Lotphase_ClaimPolicyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Lotphase_ClaimPolicyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Lotphase_ClaimPolicyMultiError) AllErrors() []error { return m }

// Lotphase_ClaimPolicyValidationError is the validation error returned by
// Lotphase_ClaimPolicy.Validate if the designated constraints aren't met.
type Lotphase_ClaimPolicyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Lotphase_ClaimPolicyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Lotphase_ClaimPolicyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Lotphase_ClaimPolicyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Lotphase_ClaimPolicyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Lotphase_ClaimPolicyValidationError) ErrorName() string {
	return "Lotphase_ClaimPolicyValidationError"
}

// Error satisfies the builtin error interface
func (e Lotphase_ClaimPolicyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLotphase_ClaimPolicy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Lotphase_ClaimPolicyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Lotphase_ClaimPolicyValidationError{}

// Validate checks the field values on OutConfig_Encoder with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OutConfig_Encoder) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutConfig_Encoder with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutConfig_EncoderMultiError, or nil if none found.
func (m *OutConfig_Encoder) ValidateAll() error {
	return m.validate(true)
}

func (m *OutConfig_Encoder) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return OutConfig_EncoderMultiError(errors)
	}

	return nil
}

// OutConfig_EncoderMultiError is an error wrapping multiple validation errors
// returned by OutConfig_Encoder.ValidateAll() if the designated constraints
// aren't met.
type OutConfig_EncoderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutConfig_EncoderMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutConfig_EncoderMultiError) AllErrors() []error { return m }

// OutConfig_EncoderValidationError is the validation error returned by
// OutConfig_Encoder.Validate if the designated constraints aren't met.
type OutConfig_EncoderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutConfig_EncoderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutConfig_EncoderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutConfig_EncoderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutConfig_EncoderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutConfig_EncoderValidationError) ErrorName() string {
	return "OutConfig_EncoderValidationError"
}

// Error satisfies the builtin error interface
func (e OutConfig_EncoderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutConfig_Encoder.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutConfig_EncoderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutConfig_EncoderValidationError{}

// Validate checks the field values on OutConfig_Exporter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OutConfig_Exporter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutConfig_Exporter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutConfig_ExporterMultiError, or nil if none found.
func (m *OutConfig_Exporter) ValidateAll() error {
	return m.validate(true)
}

func (m *OutConfig_Exporter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Config

	// no validation rules for IncludeDataUid

	if len(errors) > 0 {
		return OutConfig_ExporterMultiError(errors)
	}

	return nil
}

// OutConfig_ExporterMultiError is an error wrapping multiple validation errors
// returned by OutConfig_Exporter.ValidateAll() if the designated constraints
// aren't met.
type OutConfig_ExporterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutConfig_ExporterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutConfig_ExporterMultiError) AllErrors() []error { return m }

// OutConfig_ExporterValidationError is the validation error returned by
// OutConfig_Exporter.Validate if the designated constraints aren't met.
type OutConfig_ExporterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutConfig_ExporterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutConfig_ExporterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutConfig_ExporterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutConfig_ExporterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutConfig_ExporterValidationError) ErrorName() string {
	return "OutConfig_ExporterValidationError"
}

// Error satisfies the builtin error interface
func (e OutConfig_ExporterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutConfig_Exporter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutConfig_ExporterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutConfig_ExporterValidationError{}
