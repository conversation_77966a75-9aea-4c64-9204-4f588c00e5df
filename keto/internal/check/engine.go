// Copyright © 2023 Ory Corp
// SPDX-License-Identifier: Apache-2.0

package check

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"os"
	"path"
	"runtime"
	"strings"

	"github.com/gofrs/uuid"
	"github.com/ory/herodot"
	"github.com/ory/x/otelx"
	"github.com/pkg/errors"
	"github.com/spf13/cast"

	"github.com/ory/keto/internal/check/checkgroup"
	"github.com/ory/keto/internal/driver/config"
	"github.com/ory/keto/internal/expand"
	"github.com/ory/keto/internal/namespace"
	"github.com/ory/keto/internal/namespace/ast"
	"github.com/ory/keto/internal/persistence"
	"github.com/ory/keto/internal/relationtuple"
	"github.com/ory/keto/internal/x"
	"github.com/ory/keto/internal/x/events"
	"github.com/ory/keto/internal/x/graph"
	"github.com/ory/keto/ketoapi"
)

type (
	EngineProvider interface {
		PermissionEngine() *Engine
	}
	Engine struct {
		d             EngineDependencies
		astConstNames map[string]uuid.UUID
		expander      *expand.Engine
		briefCache    *BriefCache
		useCache      bool
		noCounter     bool
		showDBQuery   bool
		showObjName   bool
	}
	EngineDependencies interface {
		relationtuple.ManagerProvider
		persistence.Provider
		config.Provider
		x.LoggerProvider
		Persister() persistence.Persister
		x.TracingProvider
		x.NetworkIDProvider
	}

	EngineOpt func(*Engine)

	// Type aliases for shorter signatures
	relationTuple = relationtuple.RelationTuple
	query         = relationtuple.RelationQuery
)

const WildcardRelation = "..."

func NewEngine(d EngineDependencies, opts ...EngineOpt) *Engine {
	e := &Engine{
		d:        d,
		expander: expand.NewEngine(d),
		// 短缓存
		briefCache: NewBriefCache(),
		useCache:   d.Config(context.Background()).UseCache(),
	}
	for _, opt := range opts {
		opt(e)
	}
	e.mapASTConstNames()
	if e.useCache {
		e.d.Logger().Info("use_cache is enabled")
	}
	e.showDBQuery = cast.ToBool(os.Getenv("SHOW_DB_QUERY"))
	e.showObjName = cast.ToBool(os.Getenv("SHOW_OBJ_NAME"))

	e.loadRelations()
	e.fillGlobalCache(context.Background())
	for _, s := range os.Args {
		if strings.HasPrefix(s, "-test.") {
			// silent goleak in test mode
			return e
		}
	}
	go e.startExpansion()

	return e
}

func (e *Engine) UseCache(v bool) { e.useCache = v }

func (e *Engine) DisableCounter(v bool) { e.noCounter = v }

func collectASTConstNames(c ast.Child, names []string) []string {
	switch r := c.(type) {
	case *ast.SubjectSetRewrite:
		for _, c := range r.Children {
			names = collectASTConstNames(c, names)
		}
	case *ast.InvertResult:
		names = collectASTConstNames(r.Child, names)
	case *ast.ComputedSubjectSet:
		if r.Object != "" {
			names = append(names, r.Object)
		}
	}
	return names
}

func (e *Engine) mapASTConstNames() {
	ctx := context.Background()
	nsm, _ := e.d.Config(ctx).NamespaceManager()
	nss, _ := nsm.Namespaces(ctx)
	names := []string{}
	for _, ns := range nss {
		for _, r := range ns.Relations {
			if r.SubjectSetRewrite == nil {
				continue
			}
			for _, c := range r.SubjectSetRewrite.Children {
				names = collectASTConstNames(c, names)
			}
		}
	}
	uuids, err := e.d.Persister().MapStringsToUUIDs(ctx, names...)
	if err != nil {
		e.d.Logger().WithError(err).Error("failed to map names to UUIDs")
		return
	}
	e.astConstNames = make(map[string]uuid.UUID, len(uuids))
	for i, u := range uuids {
		e.astConstNames[names[i]] = u
	}
}

// CheckIsMember checks if the relation tuple's subject has the relation on the
// object in the namespace either directly or indirectly and returns a boolean
// result.
func (e *Engine) CheckIsMember(ctx context.Context, r *relationTuple, restDepth int) (bool, error) {
	result := e.CheckRelationTuple(ctx, r, restDepth)
	if result.Err != nil {
		return false, result.Err
	}
	return result.Membership == checkgroup.IsMember, nil
}

func (e *Engine) formatWithName(ctx context.Context, r *relationTuple) string {
	if !e.showDBQuery && !e.showObjName {
		return r.String()
	}
	ids := []uuid.UUID{r.Object}
	if r.Object == uuid.Nil {
		ids = ids[:0]
	}
	switch v := r.Subject.(type) {
	case *relationtuple.SubjectID:
		ids = append(ids, v.ID)
	case *relationtuple.SubjectSet:
		ids = append(ids, v.Object)
	}
	s := r.String()
	if ids[len(ids)-1] == uuid.Nil {
		ids = ids[:len(ids)-1]
	}
	if len(ids) > 0 {
		names, err := e.d.Persister().MapUUIDsToStrings(ctx, ids...)
		for i, name := range names {
			s = strings.ReplaceAll(s, ids[i].String(), name)
		}
		if err != nil && errors.Is(err, context.Canceled) {
			s = "canceled: " + s
		}
	}
	return s
}

// CheckRelationTuple checks if the relation tuple's subject has the relation on
// the object in the namespace either directly or indirectly and returns a check
// result.
func (e *Engine) CheckRelationTuple(ctx context.Context, r *relationTuple, restDepth int) (res checkgroup.Result) {
	ctx, span := e.d.Tracer(ctx).Tracer().Start(ctx, "Engine.CheckRelationTuple")
	defer otelx.End(span, &res.Err)

	events.Add(ctx, e.d, events.PermissionsChecked)

	// global max-depth takes precedence when it is the lesser or if the request
	// max-depth is less than or equal to 0
	if globalMaxDepth := e.d.Config(ctx).MaxReadDepth(); restDepth <= 0 || globalMaxDepth < restDepth {
		restDepth = globalMaxDepth
	}
	spew.Dump(restDepth)

	tupstr := e.formatWithName(ctx, r)
	ctx = newCtxWithCounter(ctx)
	if !e.noCounter {
		defer func() {
			setRestDepth(ctx, restDepth)
			c := counterFromCtx(ctx)
			e.d.Logger().WithField("request", tupstr).
				WithField("c-queries", c.Queries).
				WithField("c-rows", c.Rows).
				WithField("c-depth", restDepth-int(c.RestDepth)).
				WithField("result", res.Membership.String()).
				Info("CheckRelationTuple")
		}()
	}

	if e.useCache {
		ctx = ctxWithCache(ctx)
	}
	if res, found := e.getSysCache(ctx, r, restDepth); found {
		return checkgroup.Result{Membership: res}
	} else if res, found := e.getBriefCache(ctx, r, restDepth); found {
		return checkgroup.Result{Membership: res}
	}

	resultCh := make(chan checkgroup.Result)
	go e.checkIsAllowed(ctx, r, restDepth, false)(ctx, resultCh)
	select {
	case result := <-resultCh:
		e.addBriefCache(ctx, r, result.Membership)
		return result
	case <-ctx.Done():
		return checkgroup.Result{Err: errors.WithStack(ctx.Err())}
	}
}

func (e *Engine) printRoleQuery(ctx context.Context, r *relationTuple, restDepth int) {
	if !e.showDBQuery {
		return
	}
	_, file, line, _ := runtime.Caller(1)
	pos := path.Base(file) + ":" + fmt.Sprint(line)
	if r.Namespace == "IamPolicy" && isSysObj(ctx, r.Object) && r.Relation == "check" {
		w := argsFromCtx(ctx)
		e.d.Logger().WithField("args", w).Info("IamPolicy check args")
	}
	tupstr := e.formatWithName(ctx, r)
	e.d.Logger().WithField("restDepth", restDepth).
		WithField("request", tupstr).
		Info(pos + " db query")
}

func IsSysGroupMembers(ctx context.Context, r *relationTuple) bool {
	return r.Namespace == "IamGroup" && r.Relation == "members" && isSysObj(ctx, r.Object)
}

func IsUserCompare(r *relationTuple) bool {
	sub, ok := r.Subject.(*relationtuple.SubjectSet)
	return ok && r.Relation == "" && r.Namespace == "IamUser" && r.Namespace == sub.Namespace
}

func (e *Engine) getSysCache(ctx context.Context, r *relationTuple, restDepth int) (
	res checkgroup.Membership, found bool) {
	if useCache(ctx) {
		matchObj, matchSubject := getFromCache(ctx, r)
		if matchObj {
			setRestDepth(ctx, restDepth)
			if matchSubject {
				return checkgroup.IsMember, true
			} else {
				return checkgroup.NotMember, true
			}
		}
	}
	return
}

func (e *Engine) trySysCache(ctx context.Context, g checkgroup.Checkgroup, r *relationTuple, restDepth int) bool {
	if res, found := e.getSysCache(ctx, r, restDepth); found {
		if res == checkgroup.IsMember {
			g.Add(checkgroup.IsMemberFunc)
		} else {
			g.Add(checkgroup.NotMemberFunc)
		}
		return true
	}
	return false
}

func (e *Engine) getBriefCache(ctx context.Context, r *relationTuple, restDepth int) (
	res checkgroup.Membership, found bool) {
	if useCache(ctx) && !IsUserCompare(r) {
		res := e.briefCache.GetTuple(ctx, r)
		// _, file, line, _ := runtime.Caller(1)
		// pos := path.Base(file) + ":" + fmt.Sprint(line)
		// e.d.Logger().WithField("request", e.briefCache.getKey(ctx, r)).WithField("result", res.String()).Info(pos + " get from briefCache")
		switch res {
		case checkgroup.IsMember:
			return checkgroup.IsMember, true
		case checkgroup.NotMember:
			return checkgroup.NotMember, true
		}
	}
	return
}

func (e *Engine) tryBriefCache(ctx context.Context, g checkgroup.Checkgroup, r *relationTuple, restDepth int) bool {
	if res, found := e.getBriefCache(ctx, r, restDepth); found {
		if res == checkgroup.IsMember {
			g.Add(checkgroup.IsMemberFunc)
		} else {
			g.Add(checkgroup.NotMemberFunc)
		}
		return true
	}
	return false
}

func (e *Engine) addBriefCache(ctx context.Context, r *relationTuple, v checkgroup.Membership) {
	if useCache(ctx) && !IsUserCompare(r) {
		e.briefCache.SetTuple(ctx, r, v)
		// _, file, line, _ := runtime.Caller(1)
		// e.d.Logger().WithField("request", e.briefCache.getKey(ctx, r)).Info(path.Base(file) + ":" + fmt.Sprint(line) + " set briefCache: " + v.String())
	}
}

// checkExpandSubject checks the expansions of the subject set of the tuple.
//
// For a relation tuple n:obj#rel@user, checkExpandSubject first queries for all
// tuples that match n:obj#rel@* (arbitrary subjects), and then for each
// subject set checks subject_set@user.
func (e *Engine) checkExpandSubject(r *relationTuple, restDepth int) checkgroup.CheckFunc {
	if restDepth <= 0 {
		e.d.Logger().
			WithField("request", r.String()).
			Debug("reached max-depth, therefore this query will not be further expanded")
		return checkgroup.UnknownMemberFunc
	}
	return func(ctx context.Context, resultCh chan<- checkgroup.Result) {
		e.d.Logger().
			WithField("depth", restDepth).
			WithField("request", e.formatWithName(ctx, r)).
			Trace("check expand subject")

		g := checkgroup.New(ctx)
		defer func() {
			resultCh <- g.Result()
			setRestDepth(ctx, restDepth)
		}()

		var (
			visited  bool
			innerCtx = graph.InitVisited(ctx)
			args     []ast.Arg
		)
		if w := argsFromCtx(ctx); w != nil {
			args = w.Args
		}

		if IsUserCompare(r) {
			setRestDepth(ctx, restDepth)
			g.Add(checkgroup.NotMemberFunc)
			return
		}

		// if e.usePrexpansion {
		if e.trySysCache(ctx, g, r, restDepth) {
			return
		}
		e.printRoleQuery(ctx, r, restDepth)
		results, err := e.d.Traverser().TraverseSubjectSetExpansion(ctx, r)
		addCounter(ctx, len(results), 1)

		if errors.Is(err, herodot.ErrNotFound) {
			setRestDepth(ctx, restDepth)
			g.Add(checkgroup.NotMemberFunc)
			// e.addBriefCache(ctx, r, checkgroup.NotMember)
			return
		} else if err != nil {
			g.Add(checkgroup.ErrorFunc(err))
			return
		}

		// See if the current hop was already enough to answer the check
		for _, result := range results {
			if result.Found {
				setRestDepth(ctx, restDepth)
				g.Add(checkgroup.IsMemberFunc)
				// e.addBriefCache(ctx, r, checkgroup.IsMember)
				return
			}
		}

		// If not, we must go another hop:
		for _, result := range results {
			sub := &relationtuple.SubjectSet{
				Namespace: result.To.Namespace,
				Object:    result.To.Object,
				Relation:  result.To.Relation,
			}
			innerCtx, visited = graph.CheckAndAddVisited(innerCtx, sub)
			if visited {
				continue
			}
			g.Add(e.checkIsAllowed(newCtxWithArgs(innerCtx, &ArgsWrapper{Args: args}), result.To, restDepth, true))
		}
	}
}

// checkDirect checks if the relation tuple is in the database directly.
func (e *Engine) checkDirect(r *relationTuple, restDepth int) checkgroup.CheckFunc {
	if restDepth <= 0 {
		e.d.Logger().
			WithField("method", "checkDirect").
			Debug("reached max-depth, therefore this query will not be further expanded")
		return checkgroup.UnknownMemberFunc
	}
	return func(ctx context.Context, resultCh chan<- checkgroup.Result) {
		e.d.Logger().
			WithField("depth", restDepth).
			WithField("request", e.formatWithName(ctx, r)).
			Trace("check direct")

		g := checkgroup.New(ctx)
		if e.trySysCache(ctx, g, r, restDepth) {
			resultCh <- g.Result()
			return
		}
		e.printRoleQuery(ctx, r, restDepth)
		q := r.ToQuery()
		found, err := e.d.RelationTupleManager().ExistsRelationTuples(
			ctx,
			q,
		)

		addCounter(ctx, 1, 1)
		switch {
		case err != nil:
			e.d.Logger().
				WithField("method", "checkDirect").
				WithError(err).
				Error("failed to look up direct access in db")
			resultCh <- checkgroup.Result{
				Membership: checkgroup.NotMember,
			}

		case found:
			setRestDepth(ctx, restDepth)
			resultCh <- checkgroup.Result{
				Membership: checkgroup.IsMember,
				Tree: &ketoapi.Tree[*relationtuple.RelationTuple]{
					Type:  ketoapi.TreeNodeLeaf,
					Tuple: r,
				},
			}
			// e.addBriefCache(ctx, r, checkgroup.IsMember)

		default:
			setRestDepth(ctx, restDepth)
			resultCh <- checkgroup.Result{
				Membership: checkgroup.NotMember,
			}
			// e.addBriefCache(ctx, r, checkgroup.NotMember)
		}
	}
}

func (e *Engine) processArgs(ctx context.Context, r *relationTuple, relation *ast.Relation) (context.Context, error) {
	w := argsFromCtx(ctx)
	if w == nil || len(w.Args) == 0 || relation == nil {
		return ctx, nil
	}
	if len(relation.Params) == 0 {
		panic("should not happen: empty relation.Params")
	}

	if v, ok := w.Args[0].(ast.StringLiteralArg); ok && relation.Params[0] == ast.SubjectName {
		// fill subject with the passed argument
		u, err := e.d.Persister().MapStringsToUUIDs(ctx, v.Value(ctx))
		if err != nil {
			return ctx, err
		}
		// we only support string params as subject ids
		sub := &relationtuple.SubjectID{ID: u[0]}
		if sub.String() != r.Subject.String() {
			r.Subject = sub
			ctx = graph.ReinitVisited(ctx)
		}
	}

	if w.Mapping != nil {
		panic("should not happen: ArgsWrapper is reused")
	}
	w.Mapping = make(map[string]ast.Arg)
	// map arg-name to value
	for i, p := range relation.Params {
		if p != ast.CtxName {
			w.Mapping[p] = w.Args[i]
		}
	}

	return ctx, nil
}

// checkIsAllowed checks if the relation tuple is allowed (there is a path from
// the relation tuple subject to the namespace, object and relation) either
// directly (in the database), or through subject-set expansions, or through
// user-set rewrites.
func (e *Engine) checkIsAllowed(ctx context.Context, r *relationTuple, restDepth int, skipDirect bool) checkgroup.CheckFunc {
	if restDepth <= 0 {
		e.d.Logger().
			WithField("method", "checkIsAllowed").
			Debug("reached max-depth, therefore this query will not be further expanded")
		return checkgroup.UnknownMemberFunc
	}

	e.d.Logger().
		WithField("depth", restDepth).
		WithField("request", e.formatWithName(ctx, r)).
		Trace("check is allowed")

	g := checkgroup.New(ctx)

	relation, err := e.astRelationFor(ctx, r)
	if err != nil {
		g.Add(checkgroup.ErrorFunc(err))
		return g.CheckFunc()
	}

	ctx, err = e.processArgs(ctx, r, relation)
	if err != nil {
		g.Add(checkgroup.ErrorFunc(err))
		return g.CheckFunc()
	}

	// ctx2 := ctx
	if e.trySysCache(ctx, g, r, restDepth) { // || e.tryBriefCache(ctx, g, r, restDepth) {
		return g.CheckFunc()
	}

	// should not make checks for faked helper relations
	fakeRelation := relation != nil && relation.IsFaked()
	hasRewrite := relation != nil && relation.SubjectSetRewrite != nil
	strictMode := e.d.Config(ctx).StrictMode()
	canHaveSubjectSets := !strictMode || relation == nil || containsSubjectSetExpand(relation)
	if hasRewrite {
		g.Add(e.checkSubjectSetRewrite(ctx, r, relation.SubjectSetRewrite, restDepth))
	}
	if (!strictMode || !hasRewrite) && !skipDirect && !fakeRelation {
		// In strict mode, add a direct check only if there is no subject set rewrite for this relation.
		// Rewrites are added as 'permits'.
		g.Add(e.checkDirect(r, restDepth-1))
	}
	if canHaveSubjectSets && !fakeRelation {
		g.Add(e.checkExpandSubject(r, restDepth-1))
	}

	return g.CheckFunc()
	// return func(ctx context.Context, resultCh chan<- checkgroup.Result) {
	// 	ch := make(chan checkgroup.Result, 1)
	// 	g.CheckFunc()(ctx, ch)
	// 	res := <-ch
	// 	e.addBriefCache(ctx2, r, res.Membership)
	// 	resultCh <- res
	// }
}

func containsSubjectSetExpand(relation *ast.Relation) bool {
	for _, t := range relation.Types {
		if t.Relation != "" {
			return true
		}
	}
	return false
}

func (e *Engine) astRelationFor(ctx context.Context, r *relationTuple) (*ast.Relation, error) {
	namespaceManager, err := e.d.Config(ctx).NamespaceManager()
	if err != nil {
		return nil, err
	}
	return namespace.ASTRelationFor(ctx, namespaceManager, r.Namespace, r.Relation)
}
