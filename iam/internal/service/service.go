package service

import (
	"fmt"

	"gitlab.rp.konvery.work/platform/pkg/kutil"
	"iam/internal/biz"

	"github.com/google/wire"
)

// ProviderSet is service providers.
var ProviderSet = wire.NewSet(NewRolesService, NewPermsService, NewPoliciesService, NewConfigsService, NewTeamsService,
	NewUsersService, NewBizgrantsService)

func ValidateIdentity(idType, identity string) error {
	switch idType {
	case biz.IDTypeEmail:
		return kutil.ValidateEmail(identity)
	case biz.IDTypePhone:
		return kutil.ValidatePhoneNumber(identity)
	default:
		return fmt.Errorf("invalid identity type: %v", idType)
	}
}

func ValidateEmailPhoneNumber(email, phone string) error {
	if email != "" {
		if err := kutil.ValidateEmail(email); err != nil {
			return err
		}
	}
	if phone != "" {
		if err := kutil.ValidatePhoneNumber(phone); err != nil {
			return err
		}
	}
	return nil
}
