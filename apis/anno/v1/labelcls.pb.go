// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anno/v1/labelcls.proto

package anno

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DeleteLabelclsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteLabelclsRequest) Reset() {
	*x = DeleteLabelclsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_labelcls_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLabelclsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLabelclsRequest) ProtoMessage() {}

func (x *DeleteLabelclsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_labelcls_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLabelclsRequest.ProtoReflect.Descriptor instead.
func (*DeleteLabelclsRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_labelcls_proto_rawDescGZIP(), []int{0}
}

func (x *DeleteLabelclsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetLabelclsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetLabelclsRequest) Reset() {
	*x = GetLabelclsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_labelcls_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLabelclsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLabelclsRequest) ProtoMessage() {}

func (x *GetLabelclsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_labelcls_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLabelclsRequest.ProtoReflect.Descriptor instead.
func (*GetLabelclsRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_labelcls_proto_rawDescGZIP(), []int{1}
}

func (x *GetLabelclsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListLabelclsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListLabelclsRequest) Reset() {
	*x = ListLabelclsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_labelcls_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLabelclsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLabelclsRequest) ProtoMessage() {}

func (x *ListLabelclsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_labelcls_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLabelclsRequest.ProtoReflect.Descriptor instead.
func (*ListLabelclsRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_labelcls_proto_rawDescGZIP(), []int{2}
}

type ListLabelclsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total    int32       `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Labelcls []*Labelcls `protobuf:"bytes,2,rep,name=labelcls,proto3" json:"labelcls,omitempty"`
}

func (x *ListLabelclsReply) Reset() {
	*x = ListLabelclsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_labelcls_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLabelclsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLabelclsReply) ProtoMessage() {}

func (x *ListLabelclsReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_labelcls_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLabelclsReply.ProtoReflect.Descriptor instead.
func (*ListLabelclsReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_labelcls_proto_rawDescGZIP(), []int{3}
}

func (x *ListLabelclsReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListLabelclsReply) GetLabelcls() []*Labelcls {
	if x != nil {
		return x.Labelcls
	}
	return nil
}

type Labelcls struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// language => name
	Langs map[string]string `protobuf:"bytes,2,rep,name=langs,proto3" json:"langs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Labelcls) Reset() {
	*x = Labelcls{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_labelcls_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Labelcls) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Labelcls) ProtoMessage() {}

func (x *Labelcls) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_labelcls_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Labelcls.ProtoReflect.Descriptor instead.
func (*Labelcls) Descriptor() ([]byte, []int) {
	return file_anno_v1_labelcls_proto_rawDescGZIP(), []int{4}
}

func (x *Labelcls) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Labelcls) GetLangs() map[string]string {
	if x != nil {
		return x.Langs
	}
	return nil
}

var File_anno_v1_labelcls_proto protoreflect.FileDescriptor

var file_anno_v1_labelcls_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x63,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2b, 0x0a, 0x15,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x28, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0x15, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x63, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x58, 0x0a, 0x11, 0x4c, 0x69,
	0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2d, 0x0a, 0x08, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c, 0x73, 0x52, 0x08, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x63, 0x6c, 0x73, 0x22, 0x8c, 0x01, 0x0a, 0x08, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x6c, 0x61, 0x6e, 0x67, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c, 0x73, 0x2e, 0x4c, 0x61, 0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x05, 0x6c, 0x61, 0x6e, 0x67, 0x73, 0x1a, 0x38, 0x0a, 0x0a, 0x4c, 0x61, 0x6e,
	0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x32, 0xd6, 0x03, 0x0a, 0x08, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c, 0x7a,
	0x12, 0x4f, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x63,
	0x6c, 0x73, 0x12, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x63, 0x6c, 0x73, 0x1a, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c, 0x73, 0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11,
	0x3a, 0x01, 0x2a, 0x22, 0x0c, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c,
	0x73, 0x12, 0x56, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x63, 0x6c, 0x73, 0x12, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x63, 0x6c, 0x73, 0x1a, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c, 0x73, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x18, 0x3a, 0x01, 0x2a, 0x1a, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x63,
	0x6c, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x65, 0x0a, 0x0e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c, 0x73, 0x12, 0x1e, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x63, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x2a, 0x13, 0x2f, 0x76, 0x31,
	0x2f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x12, 0x5a, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c, 0x73, 0x12,
	0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x63, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x11, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c, 0x73, 0x22,
	0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x63, 0x6c, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x5e, 0x0a, 0x0c,
	0x4c, 0x69, 0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c, 0x73, 0x12, 0x1c, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x63, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c,
	0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x14, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0e, 0x12, 0x0c,
	0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x63, 0x6c, 0x73, 0x42, 0x3e, 0x0a, 0x07,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x31, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72,
	0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e, 0x6e, 0x6f, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_anno_v1_labelcls_proto_rawDescOnce sync.Once
	file_anno_v1_labelcls_proto_rawDescData = file_anno_v1_labelcls_proto_rawDesc
)

func file_anno_v1_labelcls_proto_rawDescGZIP() []byte {
	file_anno_v1_labelcls_proto_rawDescOnce.Do(func() {
		file_anno_v1_labelcls_proto_rawDescData = protoimpl.X.CompressGZIP(file_anno_v1_labelcls_proto_rawDescData)
	})
	return file_anno_v1_labelcls_proto_rawDescData
}

var file_anno_v1_labelcls_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_anno_v1_labelcls_proto_goTypes = []interface{}{
	(*DeleteLabelclsRequest)(nil), // 0: anno.v1.DeleteLabelclsRequest
	(*GetLabelclsRequest)(nil),    // 1: anno.v1.GetLabelclsRequest
	(*ListLabelclsRequest)(nil),   // 2: anno.v1.ListLabelclsRequest
	(*ListLabelclsReply)(nil),     // 3: anno.v1.ListLabelclsReply
	(*Labelcls)(nil),              // 4: anno.v1.Labelcls
	nil,                           // 5: anno.v1.Labelcls.LangsEntry
	(*emptypb.Empty)(nil),         // 6: google.protobuf.Empty
}
var file_anno_v1_labelcls_proto_depIdxs = []int32{
	4, // 0: anno.v1.ListLabelclsReply.labelcls:type_name -> anno.v1.Labelcls
	5, // 1: anno.v1.Labelcls.langs:type_name -> anno.v1.Labelcls.LangsEntry
	4, // 2: anno.v1.Labelclz.CreateLabelcls:input_type -> anno.v1.Labelcls
	4, // 3: anno.v1.Labelclz.UpdateLabelcls:input_type -> anno.v1.Labelcls
	0, // 4: anno.v1.Labelclz.DeleteLabelcls:input_type -> anno.v1.DeleteLabelclsRequest
	1, // 5: anno.v1.Labelclz.GetLabelcls:input_type -> anno.v1.GetLabelclsRequest
	2, // 6: anno.v1.Labelclz.ListLabelcls:input_type -> anno.v1.ListLabelclsRequest
	4, // 7: anno.v1.Labelclz.CreateLabelcls:output_type -> anno.v1.Labelcls
	4, // 8: anno.v1.Labelclz.UpdateLabelcls:output_type -> anno.v1.Labelcls
	6, // 9: anno.v1.Labelclz.DeleteLabelcls:output_type -> google.protobuf.Empty
	4, // 10: anno.v1.Labelclz.GetLabelcls:output_type -> anno.v1.Labelcls
	3, // 11: anno.v1.Labelclz.ListLabelcls:output_type -> anno.v1.ListLabelclsReply
	7, // [7:12] is the sub-list for method output_type
	2, // [2:7] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_anno_v1_labelcls_proto_init() }
func file_anno_v1_labelcls_proto_init() {
	if File_anno_v1_labelcls_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_anno_v1_labelcls_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLabelclsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_labelcls_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLabelclsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_labelcls_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLabelclsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_labelcls_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLabelclsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_labelcls_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Labelcls); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anno_v1_labelcls_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_anno_v1_labelcls_proto_goTypes,
		DependencyIndexes: file_anno_v1_labelcls_proto_depIdxs,
		MessageInfos:      file_anno_v1_labelcls_proto_msgTypes,
	}.Build()
	File_anno_v1_labelcls_proto = out.File
	file_anno_v1_labelcls_proto_rawDesc = nil
	file_anno_v1_labelcls_proto_goTypes = nil
	file_anno_v1_labelcls_proto_depIdxs = nil
}
