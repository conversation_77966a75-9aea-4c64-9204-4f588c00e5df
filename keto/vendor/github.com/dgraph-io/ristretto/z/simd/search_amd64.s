// Code generated by command: go run asm2.go -out search_amd64.s -stubs stub_search_amd64.go. DO NOT EDIT.

#include "textflag.h"

// func Search(xs []uint64, k uint64) int16
TEXT ·Search(SB), NOSPLIT, $0-34
	MOVQ xs_base+0(FP), AX
	MOVQ xs_len+8(FP), CX
	MOVQ k+24(FP), DX

	// Save n
	MOVQ CX, BX

	// Initialize idx register to zero.
	XORL BP, BP

loop:
	// Unroll1
	CMPQ (AX)(BP*8), DX
	JAE  Found

	// Unroll2
	CMPQ 16(AX)(BP*8), DX
	JAE  Found2

	// Unroll3
	CMPQ 32(AX)(BP*8), DX
	JAE  Found3

	// Unroll4
	CMPQ 48(AX)(BP*8), DX
	JAE  Found4

	// plus8
	ADDQ $0x08, BP
	CMPQ BP, CX
	JB   loop
	JMP  NotFound

Found2:
	ADDL $0x02, BP
	JMP  Found

Found3:
	ADDL $0x04, BP
	JMP  Found

Found4:
	ADDL $0x06, BP

Found:
	MOVL BP, BX

NotFound:
	MOVL BX, BP
	SHRL $0x1f, BP
	ADDL BX, BP
	SHRL $0x01, BP
	MOVL BP, ret+32(FP)
	RET
