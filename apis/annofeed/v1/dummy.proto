syntax = "proto3";
package annofeed.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "annofeed/v1/param.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/annofeed/v1;annofeed";
option java_multiple_files = true;
option java_package = "annofeed.v1";

// set reference to inexplicitly referenced data structures, so that
// they can be included in the openapi documentation.
service Dummy {
  rpc Dummy (google.protobuf.Empty) returns (DummyReply) {
    option (google.api.http) = {
      get: "/v1/dummy"
    };
  }
}

message DummyReply {
  ParamFileV2 ParamFileV2Ref = 1;
}
