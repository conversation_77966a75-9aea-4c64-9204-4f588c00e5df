# Keto 权限系统分析文档

## 1. 系统概述

Keto 是一个基于关系元组（Relation Tuple）的权限管理系统，本项目基于 Keto 进行了二次开发，实现了一个灵活且强大的权限控制框架。

## 2. 核心概念

### 2.1 命名空间（Namespace）
命名空间是权限系统的基本构建块，用于定义系统中的各种实体及其关系。

### 2.2 关系元组（Relation Tuple）
关系元组是权限规则的基本单位，格式为：`Namespace:ID#relation@Namespace:ID`

### 2.3 主要实体类型
- `IamUser`: 用户实体
- `IamGroup`: 用户组
- `IamRole`: 角色
- `IamPolicy`: 策略
- `AnnoLot`: 标注批次
- `AnnoJob`: 标注任务
- `AnnoOrder`: 标注订单
- `AnnofeedData`: 标注数据
- `AnnofeedFile`: 标注文件
- `FePage`: 前端页面

## 3. 权限规则定义

### 3.1 规则格式
```
Namespace:ID#relation@Namespace:ID
```

### 3.2 关系类型
- `members`: 成员关系
- `perms`: 权限关系
- `policies`: 策略关系
- `parents`: 父级关系
- `roles`: 角色关系
- `users`: 用户关系

### 3.3 规则示例
```plaintext
# 用户组关系
IamGroup:sys/admin#members@IamUser:user123

# 角色权限关系
IamRole:AnnoJob.viewer#<EMAIL>

# 策略绑定关系
IamRole:AnnoJob.viewer#policies@IamPolicy:sys/admin
```

## 4. 权限系统实现

### 4.1 命名空间定义
```typescript
class AnnoJob implements Namespace {
  related: {
    parents: AnnoLot[]  // 父级是标注批次
    policies: IamPolicy[]  // 关联的策略
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      AnnoJob:"*".permits.check(ctx, perm) ||  // 全局权限检查
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||  // 父级权限检查
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),  // 策略权限检查

    // 具体权限方法
    get: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.get"),
    list: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.list"),
    // ... 其他权限方法
  }
}
```

### 4.2 权限规则解析
```typescript
function parseTuple(tuple: string) {
  // 1. 分割主体和对象部分
  const [subjectPart, objectPart] = tuple.split('@');
  
  // 2. 分割主体和关系
  const [subject, relation] = subjectPart.split('#');
  
  // 3. 解析主体
  const [subjectNamespace, subjectId] = subject.split(':');
  
  // 4. 解析对象
  const [objectNamespace, objectId] = objectPart.split(':');
  
  return {
    subject: {
      namespace: subjectNamespace,
      id: subjectId
    },
    relation: relation,
    object: {
      namespace: objectNamespace,
      id: objectId
    }
  };
}
```

### 4.3 权限检查流程
```typescript
async function checkPermission(userId: string, permission: string) {
  // 1. 获取用户所属的组
  const userGroups = await getGroups(userId);
  
  // 2. 获取组的角色
  const groupRoles = await getRoles(userGroups);
  
  // 3. 获取角色的权限
  const rolePermissions = await getPermissions(groupRoles);
  
  // 4. 检查权限
  return rolePermissions.includes(permission);
}
```

## 5. 权限继承机制

### 5.1 继承关系
```plaintext
AnnoJob.owner
  └── AnnoJob.editor
       └── AnnoJob.viewer
            └── AnnoJob.get
            └── AnnoJob.list

AnnoJob.regulator
  └── AnnoJob.assign
  └── AnnoJob.reject
  └── AnnoJob.log
```

### 5.2 权限检查顺序
1. 检查直接权限
2. 检查全局权限
3. 检查父级权限
4. 检查策略权限
5. 检查所有者权限

## 6. 实际应用场景

### 6.1 API 权限控制
```typescript
async function getAnnoJob(req, res) {
  const jobId = req.params.jobId;
  const userId = req.user.id;
  
  // 创建权限检查上下文
  const ctx = new Context({
    subject: `IamUser:${userId}`
  });
  
  // 获取标注任务实例
  const annoJob = new AnnoJob(jobId);
  
  // 检查权限
  if (!await annoJob.permits.get(ctx)) {
    return res.status(403).json({ error: "Permission denied" });
  }
  
  // 获取任务数据
  const jobData = await annoJob.getData();
  return res.json(jobData);
}
```

### 6.2 权限更新
```typescript
async function updateUserRole(userId: string, role: string) {
  // 1. 创建新的权限规则
  const tuple = {
    namespace: "IamRole",
    object: role,
    relation: "users",
    subject: `IamUser:${userId}`
  };
  
  // 2. 添加到数据库
  await ketow.relationTuple.create(tuple);
}
```

## 7. 系统特点

### 7.1 优点
1. 清晰的结构：使用 `@` 和 `#` 分隔符使规则结构清晰
2. 灵活的继承：支持多级权限继承
3. 易于扩展：可以轻松添加新的关系类型
4. 可读性好：规则格式直观，易于理解
5. 便于维护：规则可以独立管理，易于修改

### 7.2 使用建议
1. 使用有意义的命名
2. 保持层次结构
3. 使用策略进行批量授权
4. 使用组进行用户管理

## 8. 注意事项

1. 关系类型不是 Keto 预定义的，而是在项目中根据需求定义的
2. 关系类型在 `namespaces.keto.ts` 中通过 TypeScript 接口定义
3. 可以根据项目需求扩展新的关系类型
4. 关系类型的使用需要遵循一定的命名和格式规范
5. 关系类型支持查询和验证操作

## 9. 总结

Keto 权限系统提供了一个灵活且强大的权限管理框架，通过命名空间和关系元组的方式，可以定义复杂的权限规则和继承关系。系统支持多级权限继承、策略绑定和实时权限检查，适合用于需要细粒度权限控制的场景。 