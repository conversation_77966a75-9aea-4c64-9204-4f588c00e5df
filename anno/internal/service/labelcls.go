// source: anno/v1/labelcls.proto
package service

import (
	"context"

	"anno/internal/biz"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/data/field"

	"google.golang.org/protobuf/types/known/emptypb"
)

func FromBizLabelcls(d *biz.Labelcls) *anno.Labelcls {
	if d == nil {
		return nil
	}
	o := &anno.Labelcls{
		Name:  d.Name,
		Langs: d.<PERSON>,
	}
	return o
}

func ToBizLabelcls(d *anno.Labelcls) *biz.Labelcls {
	if d == nil {
		return nil
	}
	o := &biz.Labelcls{
		Name:  d.Name,
		Langs: d.<PERSON>,
	}
	return o
}

type LabelclzService struct {
	anno.UnimplementedLabelclzServer
	bz *biz.LabelclzBiz
}

func NewLabelclzService(bz *biz.LabelclzBiz) *LabelclzService {
	// TODO: implement access control
	bz = nil // fail all calls
	return &LabelclzService{bz: bz}
}

func (o *LabelclzService) CreateLabelcls(ctx context.Context, req *anno.Labelcls) (*anno.Labelcls, error) {
	data, err := o.bz.Create(ctx, ToBizLabelcls(req))
	return FromBizLabelcls(data), err
}

func (o *LabelclzService) UpdateLabelcls(ctx context.Context, req *anno.Labelcls) (*anno.Labelcls, error) {
	cls := &biz.Labelcls{
		Name:  req.Name,
		Langs: req.Langs,
	}
	data, err := o.bz.Update(ctx, cls, field.NewMask(biz.LabelclsSfldLangs))
	return FromBizLabelcls(data), err
}

func (o *LabelclzService) DeleteLabelcls(ctx context.Context, req *anno.DeleteLabelclsRequest) (*emptypb.Empty, error) {
	return &emptypb.Empty{}, o.bz.DeleteByUid(ctx, req.Name)
}

func (o *LabelclzService) GetLabelcls(ctx context.Context, req *anno.GetLabelclsRequest) (*anno.Labelcls, error) {
	data, err := o.bz.GetByUid(ctx, req.Name)
	return FromBizLabelcls(data), err
}

func (o *LabelclzService) ListLabelcls(ctx context.Context, req *anno.ListLabelclsRequest) (*anno.ListLabelclsReply, error) {
	pager := biz.Pager{
		// Pagesz: int(req.Pagesz),
		// Page:   int(req.Page),
	}
	filter := &biz.LabelclsListFilter{
		// Uids:   req.Uids,
		// NamePattern: req.NamePattern,
	}

	var cnt int
	if pager.Page == 0 {
		var err error
		cnt, err = o.bz.Count(ctx, filter)
		if err != nil {
			return nil, err
		}
	}
	datas, err := o.bz.List(ctx, filter, pager)
	if err != nil {
		return nil, err
	}
	return &anno.ListLabelclsReply{Total: int32(cnt), Labelcls: kslice.Map(FromBizLabelcls, datas)}, err
}
