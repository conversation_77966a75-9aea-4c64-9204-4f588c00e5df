// source: annofeed/v1/data-.proto
package service

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"

	"gitlab.rp.konvery.work/platform/pkg/container/kslice"

	"annofeed/api/client"
	"annofeed/internal/biz"
	"annofeed/internal/data/serial"

	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/apis/annofeed/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func FromBizData(d *biz.Data) *annofeed.Data {
	if d == nil {
		return nil
	}
	o := &annofeed.Data{
		Uid:        d.GetUid(),
		Name:       d.Name,
		Desc:       d.Desc,
		Type:       anno.Element_Type_Enum(anno.Element_Type_Enum_value[d.Type]),
		Source:     d.Source.E,
		Size:       d.Size,
		State:      d.State.ToPb(),
		Error:      d.Error,
		OrgUid:     d.OrgUid,
		OrderUid:   d.OrderUid,
		CreatorUid: d.CreatorUid,
		CreatedAt:  timestamppb.New(d.CreatedAt),
		// Parser:    d.Parser,
		Summary: d.Summary.E,
	}
	if d.BaseOn > 0 {
		o.BaseOnUid = kid.StringID(d.BaseOn)
	}
	return o
}

func ToBizData(d *annofeed.CreateDataRequest) *biz.Data {
	if d == nil {
		return nil
	}
	typ := ""
	if d.Type != anno.Element_Type_unspecified {
		typ = d.Type.String()
	}
	o := &biz.Data{
		ID:       kid.ParseID(d.Uid),
		BaseOn:   kid.ParseID(d.BaseOnUid),
		Name:     d.Name,
		Desc:     d.Desc,
		Type:     typ,
		Source:   *serial.New(d.Source),
		OrgUid:   d.OrgUid,
		OrderUid: d.OrderUid,
		// Parser:  d.Parser,
	}
	return o
}

type DatasService struct {
	annofeed.UnimplementedDatasServer
	bz   *biz.DatasBiz
	rdbz *biz.RawdataBiz
}

func NewDatasService(bz *biz.DatasBiz, rdbz *biz.RawdataBiz) *DatasService {
	return &DatasService{bz: bz, rdbz: rdbz}
}

// func (o *DatasService) getEffectiveHierarchy(ctx context.Context, teamUid string) (string, error) {
// 	user := biz.UserFromCtx(ctx)
// 	hierarchy := user.Hierarchy
// 	if client.IsPrivileged(user.Role) {
// 		hierarchy = ""
// 	}
// 	if teamUid != "" {
// 		team, err := client.GetTeam(ctx, teamUid)
// 		if err != nil {
// 			return "", err
// 		}
// 		if !client.IsPrivileged(user.Role) && (!strings.HasPrefix(team.Hierarchy, hierarchy) ||
// 			team.Hierarchy != hierarchy && user.Role != client.RoleManager.String()) {
// 			return "", errors.NewErrForbidden("not allowed to operate on team")
// 		}
// 		hierarchy = team.Hierarchy
// 	}
// 	return hierarchy, nil
// }

func (o *DatasService) CreateData(ctx context.Context, req *annofeed.CreateDataRequest) (*annofeed.Data, error) {
	fmt.Println("---> create data")
	spew.Dump(req)
	if req.Source == nil || (len(req.Source.Uris) == 0 && len(req.Source.NamedUris) == 0) {
		return nil, errors.NewErrInvalidField(errors.WithFields("source"))
	}
	if req.Source.ElemType != anno.Element_Type_unspecified {
		req.Type = req.Source.ElemType
	}

	op := biz.UserFromCtx(ctx)
	org, scope := getCreateScope(op.User, req.OrgUid)
	if org == "" {
		return nil, errors.NewErrEmptyField(errors.WithFields("org_uid"))
	}
	if !client.IsAllowed(ctx, "", biz.PermCreate, biz.PermClsData, scope) {
		return nil, errors.NewErrForbidden()
	}

	data := ToBizData(req)
	data.OrgUid = org
	data.CreatorUid = op.GetUid()
	data, err := o.bz.Create(ctx, data)
	return FromBizData(data), err
}

func (o *DatasService) UpdateData(ctx context.Context, req *annofeed.UpdateDataRequest) (*annofeed.Data, error) {
	if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsData, req.Data.Uid) {
		return nil, errors.NewErrForbidden()
	}
	data, err := o.bz.Update(ctx, ToBizData(req.Data), field.NewMask(req.Fields...))
	return FromBizData(data), err
}

func (o *DatasService) DeleteData(ctx context.Context, req *annofeed.DeleteDataRequest) (*emptypb.Empty, error) {
	if !client.IsAllowed(ctx, "", biz.PermDelete, biz.PermClsData, req.Uid) {
		return nil, errors.NewErrForbidden()
	}
	return &emptypb.Empty{}, o.bz.DeleteByUid(ctx, req.Uid)
}

func (o *DatasService) GetData(ctx context.Context, req *annofeed.GetDataRequest) (*annofeed.Data, error) {
	if !req.Simple && !client.IsAllowed(ctx, "", biz.PermGet, biz.PermClsData, req.Uid) {
		return nil, errors.NewErrForbidden()
	}
	data, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}

	respData := FromBizData(data)
	if req.Simple {
		respData.Source = nil
		respData.OrgUid = ""
		respData.OrderUid = ""
		respData.Summary = nil
	}
	return respData, nil
}

func (o *DatasService) ListData(ctx context.Context, req *annofeed.ListDataRequest) (*annofeed.ListDataReply, error) {
	spew.Dump("---> listData req: ", req)
	creator, scope := getListScope(ctx, req.OrgUid, req.CreatorUid)
	if !client.IsAllowed(ctx, "", biz.PermList, biz.PermClsData, scope) {
		return nil, errors.NewErrForbidden()
	}

	op := client.UserFromCtx(ctx)
	pager := biz.Pager{
		Pagesz:  int(req.Pagesz),
		Page:    int(req.Page),
		OrderBy: "-id",
	}
	filter := &biz.DataListFilter{
		OrgUid:      req.OrgUid,
		CreatorUid:  creator,
		OrderUid:    req.OrderUid,
		NamePattern: req.NamePattern,
		States: lo.Map(req.States, func(v annofeed.Data_State_Enum, _ int) biz.DataState {
			return biz.FromPbDataState(v)
		}),
	}
	switch op.GetRole() {
	case client.SysRoleKAM:
		filter.BizgranteeUid = op.GetUid()
		filter.CreatorUid = "" // do not filter by creator_uid for kam
	}

	var cnt int64
	if pager.Page == 0 {
		var err error
		cnt, err = o.bz.Count(ctx, filter)
		if err != nil {
			return nil, err
		}
	}
	datas, err := o.bz.List(ctx, filter, pager)
	if err != nil {
		return nil, err
	}
	return &annofeed.ListDataReply{Total: int32(cnt), Datas: kslice.Map(FromBizData, datas)}, err
}

func (o *DatasService) GetDataElements(ctx context.Context, req *annofeed.GetDataElementsRequest) (*annofeed.GetDataElementsReply, error) {
	if !client.IsAllowed(ctx, "", biz.PermGet, biz.PermClsData, req.Uid) {
		return nil, errors.NewErrForbidden()
	}
	if req.Count == 0 {
		req.Count = biz.MaxElementsPerQuery
	}
	return o.bz.GetDataElements(ctx, req)
}

func (o *DatasService) FindDataElements(ctx context.Context, req *annofeed.FindDataElementsRequest) (*annofeed.GetDataElementsReply, error) {
	if !client.IsAllowed(ctx, "", biz.PermGet, biz.PermClsData, req.Uid) {
		return nil, errors.NewErrForbidden()
	}
	if req.Count == 0 {
		req.Count = biz.MaxElementsPerQuery
	}
	return o.bz.FindDataElements(ctx, req)
}

func (o *DatasService) SetRawdataEmbedding(ctx context.Context, req *annofeed.SetRawdataEmbeddingRequest) (*annofeed.SetRawdataEmbeddingReply, error) {
	// if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsData, req.Data.Uid) {
	// 	return nil, errors.NewErrForbidden()
	// }
	dataID := kid.ParseID(req.Uid)
	if dataID == 0 {
		return nil, errors.NewErrInvalidField(errors.WithFields("uid"))
	}
	rds, err := o.rdbz.List(ctx, &biz.RawdataListFilter{
		DataID: dataID,
		Name:   req.RawdataName,
	})
	if err != nil {
		return nil, err
	}
	if len(rds) == 0 {
		return nil, errors.NewErrNotFound(errors.WithModel("rawdata"))
	}
	rd, err := o.rdbz.Update(ctx, &biz.Rawdata{ID: rds[0].ID, EmbeddingURI: req.EmbeddingUri}, field.NewMask(biz.RawdataSfldEmbeddingURI))
	if err != nil {
		return nil, err
	}
	return &annofeed.SetRawdataEmbeddingReply{EmbeddingUrl: o.rdbz.EmbeddingRoute(rd)}, nil
}

func (o *DatasService) GetDataValidationSummary(ctx context.Context, req *annofeed.GetDataRequest) (*anno.DataValidationSummary, error) {
	data, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if data.Summary.E == nil {
		return &anno.DataValidationSummary{}, nil
	}
	return data.Summary.E, nil
}

func (o *DatasService) ParseData(ctx context.Context, req *annofeed.ParseDataRequest) (*emptypb.Empty, error) {
	if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsData, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	if err := o.bz.ParseData(ctx, req); err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (o *DatasService) GetDataMeta(ctx context.Context, req *annofeed.GetDataMetaRequest) (*annofeed.GetDataMetaReply, error) {
	if !client.IsAllowed(ctx, "", biz.PermGet, biz.PermClsData, req.Uid) {
		return nil, errors.NewErrForbidden()
	}
	return o.bz.GetDataMeta(ctx, req)
}
