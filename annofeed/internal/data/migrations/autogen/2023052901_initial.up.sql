BEGIN;

-- grant organization biz permissions to a kam
CREATE TABLE bizgrants
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    grantor_uid VARCHAR(32)  NOT NULL,
    grantee_uid VARCHAR(32)  NOT NULL,
    org_uid     VARCHAR(32)  NOT NULL,
    -- biz         VARCHAR(32)  NOT NULL, -- business type, e.g. anno
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_bizgrants_grantee_uid_org_uid ON bizgrants (grantee_uid, org_uid);
CREATE INDEX idx_bizgrants_org_uid ON bizgrants (org_uid) INCLUDE (grantee_uid);
CREATE INDEX idx_bizgrants_grantor_uid ON bizgrants (grantor_uid);

COMMIT;
