package middleware

import (
	"fmt"
	"reflect"
	"runtime"

	"github.com/go-kratos/kratos/v2/transport/http"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type HTTPHandler func(ctx http.Context) (data any, err error)
type HTTPResponder func(ctx http.Context, data any, err error) error

func HTTPGenHanlder(logger log.Logger, handler HTTPHandler, rsp HTTPResponder) http.HandlerFunc {
	lg := log.NewHelper(logger)

	// get handler name
	handlerName := runtime.FuncForPC(reflect.ValueOf(handler).Pointer()).Name()

	handler = HTTPRecoveryHandler(logger, handler)
	fn := func(ctx http.Context) (err error) {
		data, err := handler(ctx)
		if err != nil {
			r := ctx.Request()
			lg.Error(ctx, "failed to handle "+handlerName, err, "method", r.Method, "path", r.URL.Path)
		}
		return rsp(ctx, data, err)
	}
	return fn
}

func HTTPRecoveryHandler(logger log.Logger, handler HTTPHandler) HTTPHandler {
	lg := log.NewHelper(logger)
	return func(ctx http.Context) (data any, err error) {
		defer func() {
			if rerr := recover(); rerr != nil {
				buf := make([]byte, 1024)
				n := runtime.Stack(buf, false)
				buf = buf[:n]
				r := ctx.Request()
				err, _ = rerr.(error)
				if err == nil {
					err = fmt.Errorf("%v", rerr)
				}
				lg.Error(ctx, "handler panic", err, "method", r.Method, "path", r.URL.Path, "stack", string(buf))
			}
		}()
		return handler(ctx)
	}
}

func HTTPRESTReply(ctx http.Context, data any, err error) error {
	if err == nil {
		return ctx.JSON(200, data)
	}
	e := errors.FromError(err)
	return ctx.JSON(int(e.Code), e)
}
