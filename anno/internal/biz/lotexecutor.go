package biz

import (
	"time"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
)

// Lotexecutor represents a user who is eligible to execute the lot job in the specified phase.
type Lotexecutor struct {
	ID        int64     `json:"id" gorm:"default:null"`
	LotID     int64     `json:"lot_id" gorm:"default:null"`
	Phase     int32     `json:"phase" gorm:"default:null"` // starts from 1
	Subtype   string    `json:"subtype" gorm:"default:null"`
	UserUid   string    `json:"user_uid" gorm:"default:null"`
	TeamUid   string    `json:"team_uid" gorm:"default:null"` // the team that the user belongs to
	CreatedAt time.Time `json:"created_at" gorm:"default:null"`
}

func (o *Lotexecutor) GetID() int64 { return o.ID }

const LotexecutorTableName = "lotexecutors"

func (o Lotexecutor) TableName() string { return LotexecutorTableName }

type LotexecutorSfld string

func (o LotexecutorSfld) String() string    { return string(o) }
func (o LotexecutorSfld) WithTable() string { return LotexecutorTableName + "." + string(o) }

var (
	Lotexecutor_             = field.RegObject(&Lotexecutor{})
	LotexecutorUpdatableFlds = field.NewModel(LotexecutorTableName, Lotexecutor_)

	LotexecutorSfldID      = LotexecutorSfld(field.Sname(&Lotexecutor_.ID))
	LotexecutorSfldLotID   = LotexecutorSfld(field.Sname(&Lotexecutor_.LotID))
	LotexecutorSfldPhase   = LotexecutorSfld(field.Sname(&Lotexecutor_.Phase))
	LotexecutorSfldSubtype = LotexecutorSfld(field.Sname(&Lotexecutor_.Subtype))
	LotexecutorSfldUserUid = LotexecutorSfld(field.Sname(&Lotexecutor_.UserUid))
	LotexecutorSfldTeamUid = LotexecutorSfld(field.Sname(&Lotexecutor_.TeamUid))
)
