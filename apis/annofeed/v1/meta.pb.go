// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: annofeed/v1/meta.proto

package annofeed

import (
	v1 "gitlab.rp.konvery.work/platform/apis/anno/v1"
	types "gitlab.rp.konvery.work/platform/apis/types"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Meta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  v1.Element_Type_Enum `protobuf:"varint,1,opt,name=type,proto3,enum=anno.v1.Element_Type_Enum" json:"type,omitempty"`
	Style string               `protobuf:"bytes,2,opt,name=style,proto3" json:"style,omitempty"`
	Elems []*v1.Element        `protobuf:"bytes,3,rep,name=elems,proto3" json:"elems,omitempty"`
	// if to create new data based on an existing data
	BaseOnUid string `protobuf:"bytes,4,opt,name=base_on_uid,json=baseOnUid,proto3" json:"base_on_uid,omitempty"`
	// metadata from original data
	Metadata map[string]string `protobuf:"bytes,5,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// metafiles from original data
	Metafiles  *types.Filelist `protobuf:"bytes,6,opt,name=metafiles,proto3" json:"metafiles,omitempty"`
	RelatedLot *Meta_Lot       `protobuf:"bytes,7,opt,name=related_lot,json=relatedLot,proto3" json:"related_lot,omitempty"`
}

func (x *Meta) Reset() {
	*x = Meta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_meta_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Meta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Meta) ProtoMessage() {}

func (x *Meta) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_meta_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Meta.ProtoReflect.Descriptor instead.
func (*Meta) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_meta_proto_rawDescGZIP(), []int{0}
}

func (x *Meta) GetType() v1.Element_Type_Enum {
	if x != nil {
		return x.Type
	}
	return v1.Element_Type_Enum(0)
}

func (x *Meta) GetStyle() string {
	if x != nil {
		return x.Style
	}
	return ""
}

func (x *Meta) GetElems() []*v1.Element {
	if x != nil {
		return x.Elems
	}
	return nil
}

func (x *Meta) GetBaseOnUid() string {
	if x != nil {
		return x.BaseOnUid
	}
	return ""
}

func (x *Meta) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *Meta) GetMetafiles() *types.Filelist {
	if x != nil {
		return x.Metafiles
	}
	return nil
}

func (x *Meta) GetRelatedLot() *Meta_Lot {
	if x != nil {
		return x.RelatedLot
	}
	return nil
}

type Meta_Lot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid  string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *Meta_Lot) Reset() {
	*x = Meta_Lot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_meta_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Meta_Lot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Meta_Lot) ProtoMessage() {}

func (x *Meta_Lot) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_meta_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Meta_Lot.ProtoReflect.Descriptor instead.
func (*Meta_Lot) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_meta_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Meta_Lot) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Meta_Lot) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_annofeed_v1_meta_proto protoreflect.FileDescriptor

var file_annofeed_v1_meta_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65,
	0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65,
	0x65, 0x64, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x65,
	0x6c, 0x65, 0x6d, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xa2, 0x03, 0x0a, 0x04, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x2e, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x79, 0x70,
	0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x79,
	0x6c, 0x65, 0x12, 0x26, 0x0a, 0x05, 0x65, 0x6c, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x05, 0x65, 0x6c, 0x65, 0x6d, 0x73, 0x12, 0x1e, 0x0a, 0x0b, 0x62, 0x61,
	0x73, 0x65, 0x5f, 0x6f, 0x6e, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x62, 0x61, 0x73, 0x65, 0x4f, 0x6e, 0x55, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x2e,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x09, 0x6d, 0x65, 0x74,
	0x61, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x36, 0x0a, 0x0b, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x6c, 0x6f, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x4c,
	0x6f, 0x74, 0x52, 0x0a, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74, 0x1a, 0x3b,
	0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x2b, 0x0a, 0x03, 0x4c,
	0x6f, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x4a, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f,
	0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x39, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72,
	0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e, 0x6e, 0x6f,
	0x66, 0x65, 0x65, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_annofeed_v1_meta_proto_rawDescOnce sync.Once
	file_annofeed_v1_meta_proto_rawDescData = file_annofeed_v1_meta_proto_rawDesc
)

func file_annofeed_v1_meta_proto_rawDescGZIP() []byte {
	file_annofeed_v1_meta_proto_rawDescOnce.Do(func() {
		file_annofeed_v1_meta_proto_rawDescData = protoimpl.X.CompressGZIP(file_annofeed_v1_meta_proto_rawDescData)
	})
	return file_annofeed_v1_meta_proto_rawDescData
}

var file_annofeed_v1_meta_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_annofeed_v1_meta_proto_goTypes = []interface{}{
	(*Meta)(nil),              // 0: annofeed.v1.Meta
	nil,                       // 1: annofeed.v1.Meta.MetadataEntry
	(*Meta_Lot)(nil),          // 2: annofeed.v1.Meta.Lot
	(v1.Element_Type_Enum)(0), // 3: anno.v1.Element.Type.Enum
	(*v1.Element)(nil),        // 4: anno.v1.Element
	(*types.Filelist)(nil),    // 5: types.Filelist
}
var file_annofeed_v1_meta_proto_depIdxs = []int32{
	3, // 0: annofeed.v1.Meta.type:type_name -> anno.v1.Element.Type.Enum
	4, // 1: annofeed.v1.Meta.elems:type_name -> anno.v1.Element
	1, // 2: annofeed.v1.Meta.metadata:type_name -> annofeed.v1.Meta.MetadataEntry
	5, // 3: annofeed.v1.Meta.metafiles:type_name -> types.Filelist
	2, // 4: annofeed.v1.Meta.related_lot:type_name -> annofeed.v1.Meta.Lot
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_annofeed_v1_meta_proto_init() }
func file_annofeed_v1_meta_proto_init() {
	if File_annofeed_v1_meta_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_annofeed_v1_meta_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Meta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_meta_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Meta_Lot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_annofeed_v1_meta_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_annofeed_v1_meta_proto_goTypes,
		DependencyIndexes: file_annofeed_v1_meta_proto_depIdxs,
		MessageInfos:      file_annofeed_v1_meta_proto_msgTypes,
	}.Build()
	File_annofeed_v1_meta_proto = out.File
	file_annofeed_v1_meta_proto_rawDesc = nil
	file_annofeed_v1_meta_proto_goTypes = nil
	file_annofeed_v1_meta_proto_depIdxs = nil
}
