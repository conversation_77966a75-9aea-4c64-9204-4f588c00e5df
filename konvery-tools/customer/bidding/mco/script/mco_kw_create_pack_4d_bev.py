import os
import shutil
import sys

sys.path.append('.')
from customer.common import tools

code = 0
collection_count = 0
element_count = 0
collection_element_map = {}
pcd_img_map = {}

lidar_extrinsic = []
camera_intrinsic = []
camera_extrinsic = []
camera_distortion = []
missing_image_count = 0


def get_code_msg(msg_code):
    return tools.get_code_msg(msg_code)


def create_param_config(output_dir, info):
    config_filename = output_dir + os.sep + "params.json"

    config = {
        "meta": {
            "version": "v2"
        },
        "lidar": {
            "viewpoint": info["viewpoint"],
            "pose": info["pose"],
            "timestamp": info["timestamp"]
        },
        "cameras": {
            "front": {
                "title": "前视",
                "extrinsic": camera_extrinsic,
                "intrinsic": camera_intrinsic,
                "distortion": camera_distortion}
        }
    }

    # for cam in info["cameras"]:
    #     config["cameras"][cam] = {
    #         "title": info["cameras"]["name"],
    #         "extrinsic": info["cameras"]["extrinsic"],
    #         "intrinsic": info["cameras"]["intrinsic"],
    #         "distortion": info["cameras"]["distortion"]
    #     }

    tools.write_json_file(file=config_filename, data=config, ensure_ascii=False, indent=4)


def create_collection(pack_root_dir, pack_dir, output_dir):
    """
        对原始文件进行分包
        把pcd和pose各自按名字排序 ,pcd和pose 一一对应
    """
    global element_count
    global collection_count
    global missing_image_count

    element_index = 0
    pack_root_dir_name = os.path.dirname(pack_root_dir) + os.sep
    scene_name = os.path.basename(pack_dir)
    collection_dir = output_dir + os.sep + scene_name
    tools.check_dir(collection_dir)

    pose_dir = pack_root_dir + os.sep + "pose" + os.sep + scene_name

    pcd_list = tools.get_file_by_extension_sorted_list(pack_dir, ".pcd")
    pose_list = tools.get_file_by_extension_sorted_list(pose_dir, ".txt")

    for index, pcd in enumerate(pcd_list):
        element_dir = collection_dir + os.sep + "element_" + str(element_index)
        tools.check_dir(element_dir)

        # copy_to_element_dir() element_dir, pcd

        # pcd copy
        src_pcd = pcd
        dst_pcd = element_dir + os.sep + "lidar.pcd"
        shutil.copy(src_pcd, dst_pcd)
        lidar_pcd_orig = element_dir + os.sep + "lidar.pcd.orig"
        lidar_pcd_orig_data = pcd.replace(pack_root_dir_name, "")
        tools.write_file(lidar_pcd_orig, lidar_pcd_orig_data)

        # copy images
        if pcd in pcd_img_map.keys():
            src_img = pcd_img_map[pcd]
            dst_img = element_dir + os.sep + "front.jpg"
            shutil.copy(src_img, dst_img)
            front_img_orig = element_dir + os.sep + "front.jpg.orig"
            front_img_orig_data = src_img.replace(pack_root_dir_name, "")
            tools.write_file(front_img_orig, front_img_orig_data)
        else:
            missing_image_count += 1

        # create config
        pose_file = pose_list[index]
        timestamp = float(os.path.basename(pose_file).replace(".txt", ""))  # 1690096225.948.txt --> 1690096225.948
        # 1690096225.948 56 688389.723918145 3486232.0963392025 19.301000000000002 0.009743139418419727
        # -0.0007785995023802907 -0.9577162555784022 0.2875483243177968
        pose_data = tools.read_file(pose_file)[0].split(" ")[2:]  # [x,y,z,qx,qy,qz,qw]
        pose = []

        for data in pose_data:
            pose.append(float(data))

        pose = tools.get_pose(lidar_extrinsic, pose)

        config_info = {
            "viewpoint": [0, 0, 0, 0, 0, 0, 1],
            "pose": pose,
            "timestamp": timestamp,
            "cameras": {}
        }
        create_param_config(element_dir, config_info)
        element_index += 1
        element_count += 1

    collection_element_map[scene_name] = element_index
    collection_count += 1


def get_param(config_dir):
    global lidar_extrinsic
    global camera_extrinsic
    global camera_distortion
    global camera_intrinsic

    ego2imu = tools.load_yaml_file(config_dir + os.sep + "ego2imu.yaml")
    translation = ego2imu["transform"]["translation"]
    rotation = ego2imu["transform"]["rotation"]
    lidar_extrinsic = [translation["x"], translation["y"], translation["z"], rotation["x"], rotation["y"],
                       rotation["z"], rotation["w"]]  # [x,y,z,qx,qy,qz,qw]

    camera2ego = tools.load_yaml_file(config_dir + os.sep + "camera2ego_D2.yaml")
    translation = camera2ego["camera_sensor_params"][0]["translation"]
    quaternion = camera2ego["camera_sensor_params"][0]["quaternion"]
    camera_extrinsic = [translation["x"], translation["y"], translation["z"], quaternion["x"], quaternion["y"],
                        quaternion["z"], quaternion["w"]]

    fx = camera2ego["camera_sensor_params"][0]["focal_u"]
    fy = camera2ego["camera_sensor_params"][0]["focal_v"]
    cx = camera2ego["camera_sensor_params"][0]["center_u"]
    cy = camera2ego["camera_sensor_params"][0]["center_v"]

    camera_intrinsic = [fx, fy, cx, cy]

    camera_distortion = [0] # pinhole
    camera_distortion.extend(camera2ego["camera_sensor_params"][0]["distort"])


def get_pcd_image_map(pack_root, pcd_dir):
    global pcd_img_map
    pcd_img_map = {}

    scene_name = os.path.basename(pcd_dir)
    img_dir = pack_root + os.sep + "img" + os.sep + scene_name
    pcd_list = tools.get_file_by_extension_sorted_list(pcd_dir, ".pcd")
    img_list = tools.get_file_by_extension_sorted_list(img_dir, ".jpg")

    img_timestamp_map = {}
    for img in img_list:
        img_timestamp = os.path.basename(img).replace(".jpg", "")
        img_timestamp = img_timestamp[0:10] + "." + img_timestamp[10]
        img_timestamp = float(img_timestamp)
        img_timestamp_map[img_timestamp] = img

    for pcd in pcd_list:
        pcd_timestamp = os.path.basename(pcd).replace(".pcd", "")
        pcd_timestamp = pcd_timestamp[0:10] + "." + pcd_timestamp[11]
        pcd_timestamp = float(pcd_timestamp)
        if pcd_timestamp in img_timestamp_map.keys():
            pcd_img_map[pcd] = img_timestamp_map[pcd_timestamp]
        else:
            project_root = tools.get_project_root_dir()
            pcd_img_map[pcd] = project_root + os.sep + "data" + os.sep + "black_image_1920X1080.jpg"

    # print(pcd_img_map)


def run(input_dir, output_dir, config_dir):
    """
    把pcd和pose各自按名字排序 ,pcd和pose 一一对应

    目录结构: scene_xxx为分包的单位
      data
          img
              scene_001
              scene_003
          pose
              xxxx.txt
          pcd
              scene_001
              scene_003
    """
    global pcd_img_map

    get_param(config_dir)
    input_dir = tools.unzip(input_dir)
    tools.check_dir(output_dir)

    pcd_dir_list = tools.find_dir_by_pre_name(input_dir, "pcd")

    for pcd_dir in pcd_dir_list:
        pack_root = os.path.dirname(pcd_dir)
        for pack_pcd_dir in tools.find_dir_by_pre_name(pcd_dir, "scene_"):
            get_pcd_image_map(pack_root, pack_pcd_dir)
            create_collection(pack_root, pack_pcd_dir, output_dir)

    trans_result = {"code": code, "err_msg": get_code_msg(code), "output": output_dir,
                    "summary": {"collection count:": collection_count, "element count": element_count,
                                "missing_image_count": missing_image_count,
                                "collection_element_map": collection_element_map}}

    print(trans_result)
    return trans_result


"""
    项目： mco项目 佑驾
    对接平台：KW新平台 
    功能： 将客户的文件, 成KW的格式， 并按数量分包
    输入： 原始数据
    输出： 分包后的数据
    开发： Justin
    doc:  KW 格式说明 ：https://konverydata.feishu.cn/wiki/Jfidw7BP6iKtazkDv8GcFUuUnjg
"""
if __name__ == "__main__":

    args = tools.get_args()

    if args.input == "../../input/test":
        args.input = "/Users/<USER>/Downloads/Convert/MCO"
        args.out = "/Users/<USER>/Downloads/mco-data"
        args.pcd = "/Users/<USER>/Downloads/Convert/MCO/M2"

    run(input_dir=args.input, output_dir=args.out, config_dir=args.pcd)
