import os
import sys
import shutil
import json
import numpy as np

sys.path.append(".")
from customer.common import tools


def _listdir(input_dir):
    """wraps for os.listdir but dose not contains mac os system file"""
    mac_file = ['.DS_Store', '__MACOSX']
    return [os.path.join(input_dir, f) for f in os.listdir(input_dir) if f not in mac_file]


def get_params(seg):
    params_file = tools.get_file_by_extension(seg, extension='txt')[0]
    params = []
    with open(params_file.path) as f:
        params_lines = f.readlines()
        step = 6
        for i in range(0, len(params_lines), step):
            params.append(params_lines[i: i + step])
    return params


def construct_config(seg_dir, output_seg_dir):
    config = {
        "data_type": "fusion_pointcloud",
        "sensor_params": {}
    }
    params = get_params(seg_dir)
    for param in params:
        camera_name = param[0].split(":")[0].split("_")[1].replace("-", "_")
        distortion = [float(d) for d in param[1].split(":")[1].split()]
        intrinsic = np.reshape([float(i) for i in param[2].split(":")[1].split()], (3, 3)).tolist()
        extrinsic = np.reshape([float(e) for e in param[0].split(":")[1].split()], (4, 4)).T.tolist()
        camera_json = {
            "camera_model": "pinhole",
            "fov": 50,
            "extrinsic": extrinsic,
            "fx": intrinsic[0][0],
            "fy": intrinsic[1][1],
            "cx": intrinsic[0][2],
            "cy": intrinsic[1][2],
            "k1": distortion[0],
            "k2": distortion[1],
            "p1": distortion[2],
            "p2": distortion[3],
            "k3": distortion[4],
            "k4": distortion[5],
            "k5": distortion[6],
            "k6": distortion[7]
        }
        config["sensor_params"][camera_name] = camera_json
    with open(os.path.join(output_seg_dir, 'config.json'), 'w') as f:
        json.dump(config, f, indent=4)

def run(input_dir, output_dir):
    tools.check_dir(output_dir)
    for seg in _listdir(input_dir):
        output_seg_dir = os.path.join(output_dir, os.path.basename(seg))
        tools.check_dir(output_seg_dir)

        construct_config(seg, output_seg_dir)

        output_lidar_dir = os.path.join(output_seg_dir, "lidar")
        tools.check_dir(output_lidar_dir)
        output_camera_dir = os.path.join(output_seg_dir, "camera")
        tools.check_dir(output_camera_dir)
        cameras = ['front', 'left_backward', 'rear', 'right_forward', 'front_tele', 'left_forward',
                   'right_backward']
        for camera in cameras:
            tools.check_dir(os.path.join(output_camera_dir, camera))
        for frame in _listdir(seg):
            shutil.copyfile(os.path.join(frame, 'velodyne_points', 'velodyne64.pcd'), os.path.join(output_lidar_dir, f'{os.path.basename(frame)}.pcd'))
            for camera in cameras:
                for img in _listdir(os.path.join(frame, 'images', camera)):
                    shutil.copyfile(img, os.path.join(output_camera_dir, camera, f'{os.path.basename(frame)}.jpg'))


"""
    项目： MKE-密尔沃基 23D融合301行车检测连续帧
    对接平台：云测平台
    功能： 分包
"""
if __name__ == '__main__':
    args = tools.get_args()
    input_dir = args.intput
    output_dir = args.out
    run(input_dir, output_dir)
