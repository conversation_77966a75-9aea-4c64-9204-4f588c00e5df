package client

import (
	"context"

	annoutv1 "gitlab.rp.konvery.work/platform/apis/annout/v1"

	"github.com/go-kratos/kratos/v2/errors"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/emptypb"
)

const (
	ReaperStateOngoing  = "ongoing"
	ReaperStateFinished = "finished"
	ReaperStateFailed   = "failed"
)

type (
	Annout = annoutv1.ReapersClient
)

func NewAnnout(conn *grpc.ClientConn) Annout {
	return annoutv1.NewReapersClient(conn)
}

var outsvc Annout

func InitAnnout(service *Service) {
	outsvc = NewAnnout(newGrpcConn(service))
}

func SetAnnout(annout Annout) {
	outsvc = annout
}

func CreateReaper(ctx context.Context, data *annoutv1.CreateReaperRequest) (*annoutv1.Reaper, error) {
	r, err := outsvc.CreateReaper(ctx, data)
	if err != nil {
		return nil, errors.FromError(err)
	}
	return r, nil
}

func GetReaperState(ctx context.Context, lotUid string) (string, error) {
	r, err := outsvc.GetReaper(ctx, &annoutv1.GetReaperRequest{LotUid: lotUid})
	if err != nil {
		return "", errors.FromError(err)
	}
	return r.State, nil
}

// ExportLotAnnos exports annotations for a lot. This function is used in `anno` service.
func ExportLotAnnos(ctx context.Context, req *annoutv1.ExportLotAnnosRequest) (*emptypb.Empty, error) {
	r, err := outsvc.ExportLotAnnos(ctx, req)
	if err != nil {
		return nil, errors.FromError(err)
	}
	return r, nil
}
