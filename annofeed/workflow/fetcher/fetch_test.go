package fetcher

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsSystemFile(t *testing.T) {
	cases := []struct {
		in  string
		exp bool
	}{
		{"foo", false},
		{".foo", false},
		{"a/b/foo", false},
		{"a/b/.foo", false},
		{"__MACOSX", false},
		{"__MACOSX/foo", true},
		{"a/__MACOSX/foo", true},
	}

	for i, c := range cases {
		assert.Equal(t, c.exp, isSystemFile(c.in), "case %v", i+1)
	}
}
