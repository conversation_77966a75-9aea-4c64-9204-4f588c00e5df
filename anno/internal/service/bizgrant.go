// source: iam/v1/grant.proto
package service

import (
	"context"

	"anno/api/client"
	"anno/internal/biz"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/errors"

	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func FromBizBizgrant(d *biz.Bizgrant) *anno.Bizgrant {
	if d == nil {
		return nil
	}
	o := &anno.Bizgrant{
		GrantorUid: d.<PERSON>,
		GranteeUid: d.<PERSON>,
		OrgUid:     d.OrgUid,
		CreatedAt:  timestamppb.New(d.CreatedAt),
	}
	return o
}

func ToBizBizgrant(d *anno.CreateBizgrantRequest) *biz.Bizgrant {
	if d == nil {
		return nil
	}
	return &biz.Bizgrant{
		GranteeUid: d.<PERSON>,
		OrgUid:     d.<PERSON>,
	}
}

func ToBizBizgrantFilter(f *anno.BizgrantFilter) *biz.BizgrantFilter {
	if f == nil {
		return &biz.BizgrantFilter{}
	}

	return &biz.BizgrantFilter{
		GrantorUid: f.GrantorUid,
		GranteeUid: f.GranteeUid,
		OrgUid:     f.OrgUid,
	}
}

type BizgrantsService struct {
	anno.UnimplementedBizgrantsServer
	bz   *biz.BizgrantsBiz
	repo biz.BizgrantsRepo
}

func NewBizgrantsService(bz *biz.BizgrantsBiz, repo biz.BizgrantsRepo) *BizgrantsService {
	return &BizgrantsService{bz: bz, repo: repo}
}

func (o *BizgrantsService) getOrgUid(op *biz.User, orgUid string) string {
	if orgUid == "" && (client.IsSysRole(op.Role) || op.OrgUid == "") {
		return ""
	}
	if orgUid == "" {
		orgUid = op.OrgUid
	}
	return orgUid
}

func (o *BizgrantsService) CreateBizgrant(ctx context.Context, req *anno.CreateBizgrantRequest) (
	*anno.Bizgrant, error) {
	op := biz.UserFromCtx(ctx)
	req.OrgUid = o.getOrgUid(op.User, req.OrgUid)
	if req.OrgUid == "" {
		return nil, errors.NewErrEmptyField(errors.WithFields("org_uid"))
	}
	if !client.IsAllowed(ctx, "", biz.PermSetPolicy, biz.PermClsGroup, req.OrgUid) {
		return nil, errors.NewErrForbidden()
	}
	g := ToBizBizgrant(req)
	g.GrantorUid = op.GetUid()
	g, err := o.bz.Create(ctx, g)
	if err != nil {
		return nil, err
	}
	return FromBizBizgrant(g), err
}

func (o *BizgrantsService) DeleteBizgrant(ctx context.Context, req *anno.DeleteBizgrantRequest) (*emptypb.Empty, error) {
	filter := req.Filter
	if filter.GranteeUid == "" || filter.OrgUid == "" {
		return nil, errors.NewErrEmptyField(errors.WithFields("grantee_uid", "org_uid"))
	}
	if !client.IsAllowed(ctx, "", biz.PermSetPolicy, biz.PermClsGroup, filter.OrgUid) {
		return nil, errors.NewErrForbidden()
	}

	return &emptypb.Empty{}, o.bz.DeleteByFilter(ctx, ToBizBizgrantFilter(filter))
}

func (o *BizgrantsService) ListBizgrant(ctx context.Context, req *anno.ListBizgrantRequest) (*anno.ListBizgrantReply, error) {
	f := req.Filter
	if f == nil || (f.GrantorUid == "" && f.GranteeUid == "" && f.OrgUid == "") {
		return nil, errors.NewErrEmptyField(errors.WithFields("grantor_uid", "grantee_uid", "org_uid"))
	}
	filter := ToBizBizgrantFilter(f)

	// TODO: check permissions
	// if !client.IsAllowed(ctx, "", biz.PermGetPolicy, biz.PermClsGroup, filter.OrgUid) {
	// 	return nil, errors.NewErrForbidden()
	// }

	pager := biz.Pager{
		Pagesz:    int(req.Pagesz),
		PageToken: biz.PageToken(req.PageToken),
	}
	datas, nextPageToken, err := o.bz.List(ctx, filter, pager)
	if err != nil {
		return nil, err
	}
	return &anno.ListBizgrantReply{NextPageToken: nextPageToken, Grants: kslice.Map(FromBizBizgrant, datas)}, nil
}
