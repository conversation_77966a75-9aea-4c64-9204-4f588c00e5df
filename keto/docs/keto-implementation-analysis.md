# Keto 实现分析

## 1. 初始化流程分析

### 1.1 启动命令
```bash
# 1. 执行数据库迁移
./keto migrate up -c config/keto.yml

# 2. 启动服务
./keto serve --sqa-opt-out -c config/keto.yml
```

### 1.2 配置文件
```yaml
# config/keto.yml
version: v0.8.0-alpha.2

log:
  level: debug

namespaces:
  location: file:///namespaces.keto.ts
  use_cache: true

serve:
  read:
    host: 0.0.0.0
    port: 4466  # 读取/校验端口
  write:
    host: 0.0.0.0
    port: 4467  # 写入端口

dsn: postgres://root:root@localhost:5432/keto?sslmode=disable
```

### 1.3 初始化文件
- `namespaces.keto.ts`: 命名空间定义
- `tuples.txt`: 权限规则定义
- `init-tuples.sh`: 规则初始化脚本
- `ketow.sh`: 规则管理工具

## 2. Relation Tuple 服务分析

### 2.1 服务定义
```go
// internal/relationtuple/definitions.go
type RelationTuple struct {
    Namespace string    `json:"namespace"`
    Object    uuid.UUID `json:"object"`
    Relation  string    `json:"relation"`
    Subject   Subject   `json:"subject"`
    Formula   *ast.Relation `json:"-"`
}
```

### 2.2 服务接口
```protobuf
service WriteService {
    rpc TransactRelationTuples(TransactRelationTuplesRequest) returns (TransactRelationTuplesResponse);
    rpc DeleteRelationTuples(DeleteRelationTuplesRequest) returns (DeleteRelationTuplesResponse);
}

service ReadService {
    rpc Check(CheckRequest) returns (CheckResponse);
    rpc ListRelationTuples(ListRelationTuplesRequest) returns (ListRelationTuplesResponse);
}
```

### 2.3 数据存储
```sql
CREATE TABLE keto_relation_tuples (
    shard_id                 UUID        NOT NULL,
    nid                      UUID        NOT NULL,
    namespace_id             INTEGER     NOT NULL,
    object                   VARCHAR(64) NOT NULL,
    relation                 VARCHAR(64) NOT NULL,
    subject_id               VARCHAR(64) NULL,
    subject_set_namespace_id INTEGER NULL,
    subject_set_object       VARCHAR(64) NULL,
    subject_set_relation     VARCHAR(64) NULL,
    commit_time              TIMESTAMP   NOT NULL,
    PRIMARY KEY (shard_id, nid)
);
```

## 3. UUID 映射分析

### 3.1 映射表结构
```sql
CREATE TABLE keto_uuid_mappings (
    id uuid PRIMARY KEY,
    string_representation text NOT NULL
);
```

### 3.2 UUID 生成
```go
// internal/persistence/sql/uuid_mapping.go
func (p *Persister) batchToUUIDs(ctx context.Context, values []string, readOnly bool) (uuids []uuid.UUID, err error) {
    // 使用 UUID v5 算法生成确定性 UUID
    for i, val := range values {
        uuids[i] = uuid.NewV5(p.NetworkID(ctx), val)
    }
}
```

### 3.3 转换流程
1. **字符串到 UUID**：
   - 使用 UUID v5 算法生成确定性 UUID
   - 基于网络 ID 和字符串值生成
   - 存储映射关系到数据库

2. **UUID 到字符串**：
   - 查询 keto_uuid_mappings 表
   - 获取字符串表示
   - 返回原始值

### 3.4 使用场景
- **分片**：shard_id 用于数据分片
- **命名空间**：nid 用于标识命名空间
- **对象标识**：object 和 subject 的 UUID 映射

## 总结

Keto 的实现主要包含三个关键部分：

1. **初始化流程**：
   - 数据库迁移
   - 服务启动
   - 规则加载

2. **Relation Tuple 服务**：
   - 读写分离
   - 权限管理
   - 数据存储

3. **UUID 映射**：
   - 确定性 UUID 生成
   - 双向映射
   - 分片支持

这种设计实现了：
- 高效的权限管理
- 可扩展的架构
- 数据一致性
- 高性能查询
- 多租户支持 