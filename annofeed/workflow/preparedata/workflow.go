package preparedata

import (
	"context"
	_ "embed"
	"fmt"
	"github.com/davecgh/go-spew/spew"

	"annofeed/internal/biz"

	"gitlab.rp.konvery.work/platform/pkg/kparser"
	"gitlab.rp.konvery.work/platform/pkg/kutil"
	"gitlab.rp.konvery.work/platform/pkg/wf/ktwf"
)

//go:embed wf.yaml
var wfTplRaw []byte
var _ = newWorkflow() // check workflow spec

func newWorkflow() *ktwf.WorkflowSpec {
	spec, err := ktwf.ParseWorkflowSpec(wfTplRaw)
	spew.Dump("wf spec: ", spec)
	fmt.Println("-----------------------")
	kutil.Assert(err, "failed to parse workflow template")
	return spec
}

func WorkflowID(dataUid string) string { return "annofeed-preparedata-" + dataUid }

func StartWorkflow(ctx context.Context, data *biz.Data) error {
	fmt.Println("start dbg workflow", data.ID)
	spew.Dump("args data: ", data)
	spec := newWorkflow()
	spec.Workflow.Args["data"] = kparser.ToJSONStr(data)
	_, err := ktwf.StartWorkflow(ctx, WorkflowID(data.GetUid()), spec, true)
	return err
}
