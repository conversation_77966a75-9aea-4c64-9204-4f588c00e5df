// source: annofeed/v1/data-.proto
package data

import (
	"context"

	"annofeed/internal/biz"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.rp.konvery.work/platform/pkg/data"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
)

type datasRepo struct {
	repo.GenericRepo[biz.Data]
	data *Data
	log  *log.Helper
}

func NewDatasRepo(d *Data, logger log.Logger) biz.DatasRepo {
	return &datasRepo{
		GenericRepo: data.NewGenericRepo[biz.Data](d, logger, biz.Data{}.TableName()),
		data:        d,
		log:         log.<PERSON>Helper(logger),
	}
}

func (o *datasRepo) ListByFilter(ctx context.Context, p *biz.DataListFilter, pager biz.Pager) ([]*biz.Data, error) {
	datas, _, err := o.GenericRepo.List(ctx, p, pager)
	if err != nil {
		return nil, Convert(&biz.Data{}, err)
	}
	return datas, nil
}
