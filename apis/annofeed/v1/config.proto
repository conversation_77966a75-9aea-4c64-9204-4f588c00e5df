syntax = "proto3";

package annofeed.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/annofeed/v1;annofeed";
option java_multiple_files = true;
option java_package = "annofeed.v1";

service Configs {
  rpc GetVersion (google.protobuf.Empty) returns (GetVersionReply) {
    option (google.api.http) = {
      get: "/v1/version"
    };
  }
}

message GetVersionReply {
  string version = 1;
}
