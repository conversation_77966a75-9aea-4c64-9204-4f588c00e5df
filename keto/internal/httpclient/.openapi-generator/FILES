.gitignore
.openapi-generator-ignore
.travis.yml
README.md
api/openapi.yaml
api_metadata.go
api_permission.go
api_relationship.go
client.go
configuration.go
docs/CheckOplSyntaxResult.md
docs/CheckPermissionResult.md
docs/CreateRelationshipBody.md
docs/ErrorGeneric.md
docs/ExpandedPermissionTree.md
docs/GenericError.md
docs/HealthNotReadyStatus.md
docs/HealthStatus.md
docs/InlineResponse200.md
docs/InlineResponse2001.md
docs/InlineResponse503.md
docs/MetadataApi.md
docs/Namespace.md
docs/ParseError.md
docs/PermissionApi.md
docs/PostCheckPermissionBody.md
docs/PostCheckPermissionOrErrorBody.md
docs/RelationQuery.md
docs/Relationship.md
docs/RelationshipApi.md
docs/RelationshipNamespaces.md
docs/RelationshipPatch.md
docs/Relationships.md
docs/SourcePosition.md
docs/SubjectSet.md
docs/Version.md
git_push.sh
go.mod
go.sum
model_check_opl_syntax_result.go
model_check_permission_result.go
model_create_relationship_body.go
model_error_generic.go
model_expanded_permission_tree.go
model_generic_error.go
model_health_not_ready_status.go
model_health_status.go
model_inline_response_200.go
model_inline_response_200_1.go
model_inline_response_503.go
model_namespace.go
model_parse_error.go
model_post_check_permission_body.go
model_post_check_permission_or_error_body.go
model_relation_query.go
model_relationship.go
model_relationship_namespaces.go
model_relationship_patch.go
model_relationships.go
model_source_position.go
model_subject_set.go
model_version.go
response.go
utils.go
