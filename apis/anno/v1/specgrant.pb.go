// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anno/v1/specgrant.proto

package anno

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Specgrant_ItemType_Enum int32

const (
	Specgrant_ItemType_unspecified Specgrant_ItemType_Enum = 0
	Specgrant_ItemType_AnnoLot     Specgrant_ItemType_Enum = 1
)

// Enum value maps for Specgrant_ItemType_Enum.
var (
	Specgrant_ItemType_Enum_name = map[int32]string{
		0: "unspecified",
		1: "AnnoLot",
	}
	Specgrant_ItemType_Enum_value = map[string]int32{
		"unspecified": 0,
		"AnnoLot":     1,
	}
)

func (x Specgrant_ItemType_Enum) Enum() *Specgrant_ItemType_Enum {
	p := new(Specgrant_ItemType_Enum)
	*p = x
	return p
}

func (x Specgrant_ItemType_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Specgrant_ItemType_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_specgrant_proto_enumTypes[0].Descriptor()
}

func (Specgrant_ItemType_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_specgrant_proto_enumTypes[0]
}

func (x Specgrant_ItemType_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Specgrant_ItemType_Enum.Descriptor instead.
func (Specgrant_ItemType_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_specgrant_proto_rawDescGZIP(), []int{1, 0, 0}
}

type CreateSpecgrantRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grantee uid
	GranteeUid string `protobuf:"bytes,1,opt,name=grantee_uid,json=granteeUid,proto3" json:"grantee_uid,omitempty"`
	// type of the item
	ItemType Specgrant_ItemType_Enum `protobuf:"varint,2,opt,name=item_type,json=itemType,proto3,enum=anno.v1.Specgrant_ItemType_Enum" json:"item_type,omitempty"`
	// item uid
	ItemUid string `protobuf:"bytes,3,opt,name=item_uid,json=itemUid,proto3" json:"item_uid,omitempty"`
}

func (x *CreateSpecgrantRequest) Reset() {
	*x = CreateSpecgrantRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_specgrant_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSpecgrantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSpecgrantRequest) ProtoMessage() {}

func (x *CreateSpecgrantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_specgrant_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSpecgrantRequest.ProtoReflect.Descriptor instead.
func (*CreateSpecgrantRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_specgrant_proto_rawDescGZIP(), []int{0}
}

func (x *CreateSpecgrantRequest) GetGranteeUid() string {
	if x != nil {
		return x.GranteeUid
	}
	return ""
}

func (x *CreateSpecgrantRequest) GetItemType() Specgrant_ItemType_Enum {
	if x != nil {
		return x.ItemType
	}
	return Specgrant_ItemType_unspecified
}

func (x *CreateSpecgrantRequest) GetItemUid() string {
	if x != nil {
		return x.ItemUid
	}
	return ""
}

type Specgrant struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grantor uid
	GrantorUid string `protobuf:"bytes,1,opt,name=grantor_uid,json=grantorUid,proto3" json:"grantor_uid,omitempty"`
	// grantee uid
	GranteeUid string `protobuf:"bytes,2,opt,name=grantee_uid,json=granteeUid,proto3" json:"grantee_uid,omitempty"`
	// item type
	ItemType Specgrant_ItemType_Enum `protobuf:"varint,3,opt,name=item_type,json=itemType,proto3,enum=anno.v1.Specgrant_ItemType_Enum" json:"item_type,omitempty"`
	// item uid
	ItemUid string `protobuf:"bytes,4,opt,name=item_uid,json=itemUid,proto3" json:"item_uid,omitempty"`
	// item owner organization uid
	OrgUid    string                 `protobuf:"bytes,5,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *Specgrant) Reset() {
	*x = Specgrant{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_specgrant_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Specgrant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Specgrant) ProtoMessage() {}

func (x *Specgrant) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_specgrant_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Specgrant.ProtoReflect.Descriptor instead.
func (*Specgrant) Descriptor() ([]byte, []int) {
	return file_anno_v1_specgrant_proto_rawDescGZIP(), []int{1}
}

func (x *Specgrant) GetGrantorUid() string {
	if x != nil {
		return x.GrantorUid
	}
	return ""
}

func (x *Specgrant) GetGranteeUid() string {
	if x != nil {
		return x.GranteeUid
	}
	return ""
}

func (x *Specgrant) GetItemType() Specgrant_ItemType_Enum {
	if x != nil {
		return x.ItemType
	}
	return Specgrant_ItemType_unspecified
}

func (x *Specgrant) GetItemUid() string {
	if x != nil {
		return x.ItemUid
	}
	return ""
}

func (x *Specgrant) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *Specgrant) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type SpecgrantFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grantor uid
	GrantorUid string `protobuf:"bytes,1,opt,name=grantor_uid,json=grantorUid,proto3" json:"grantor_uid,omitempty"`
	// grantee uid
	GranteeUid string `protobuf:"bytes,2,opt,name=grantee_uid,json=granteeUid,proto3" json:"grantee_uid,omitempty"`
	// item owner orgnization uid
	OrgUid string `protobuf:"bytes,3,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// item type
	ItemType Specgrant_ItemType_Enum `protobuf:"varint,4,opt,name=item_type,json=itemType,proto3,enum=anno.v1.Specgrant_ItemType_Enum" json:"item_type,omitempty"`
	// item uid list
	ItemUids []string `protobuf:"bytes,5,rep,name=item_uids,json=itemUids,proto3" json:"item_uids,omitempty"`
}

func (x *SpecgrantFilter) Reset() {
	*x = SpecgrantFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_specgrant_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpecgrantFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpecgrantFilter) ProtoMessage() {}

func (x *SpecgrantFilter) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_specgrant_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpecgrantFilter.ProtoReflect.Descriptor instead.
func (*SpecgrantFilter) Descriptor() ([]byte, []int) {
	return file_anno_v1_specgrant_proto_rawDescGZIP(), []int{2}
}

func (x *SpecgrantFilter) GetGrantorUid() string {
	if x != nil {
		return x.GrantorUid
	}
	return ""
}

func (x *SpecgrantFilter) GetGranteeUid() string {
	if x != nil {
		return x.GranteeUid
	}
	return ""
}

func (x *SpecgrantFilter) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *SpecgrantFilter) GetItemType() Specgrant_ItemType_Enum {
	if x != nil {
		return x.ItemType
	}
	return Specgrant_ItemType_unspecified
}

func (x *SpecgrantFilter) GetItemUids() []string {
	if x != nil {
		return x.ItemUids
	}
	return nil
}

type DeleteSpecgrantRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *SpecgrantFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *DeleteSpecgrantRequest) Reset() {
	*x = DeleteSpecgrantRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_specgrant_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSpecgrantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSpecgrantRequest) ProtoMessage() {}

func (x *DeleteSpecgrantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_specgrant_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSpecgrantRequest.ProtoReflect.Descriptor instead.
func (*DeleteSpecgrantRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_specgrant_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteSpecgrantRequest) GetFilter() *SpecgrantFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type ListSpecgrantRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagesz int32 `protobuf:"varint,1,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// An opaque pagination token returned from a previous call.
	// An empty token denotes the first page.
	PageToken string           `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	Filter    *SpecgrantFilter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListSpecgrantRequest) Reset() {
	*x = ListSpecgrantRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_specgrant_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSpecgrantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSpecgrantRequest) ProtoMessage() {}

func (x *ListSpecgrantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_specgrant_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSpecgrantRequest.ProtoReflect.Descriptor instead.
func (*ListSpecgrantRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_specgrant_proto_rawDescGZIP(), []int{4}
}

func (x *ListSpecgrantRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListSpecgrantRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListSpecgrantRequest) GetFilter() *SpecgrantFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type ListSpecgrantReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Grants []*Specgrant `protobuf:"bytes,1,rep,name=grants,proto3" json:"grants,omitempty"`
	// An opaque pagination token, if not empty, to be used to fetch the next page of results
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListSpecgrantReply) Reset() {
	*x = ListSpecgrantReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_specgrant_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSpecgrantReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSpecgrantReply) ProtoMessage() {}

func (x *ListSpecgrantReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_specgrant_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSpecgrantReply.ProtoReflect.Descriptor instead.
func (*ListSpecgrantReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_specgrant_proto_rawDescGZIP(), []int{5}
}

func (x *ListSpecgrantReply) GetGrants() []*Specgrant {
	if x != nil {
		return x.Grants
	}
	return nil
}

func (x *ListSpecgrantReply) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

type Specgrant_ItemType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Specgrant_ItemType) Reset() {
	*x = Specgrant_ItemType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_specgrant_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Specgrant_ItemType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Specgrant_ItemType) ProtoMessage() {}

func (x *Specgrant_ItemType) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_specgrant_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Specgrant_ItemType.ProtoReflect.Descriptor instead.
func (*Specgrant_ItemType) Descriptor() ([]byte, []int) {
	return file_anno_v1_specgrant_proto_rawDescGZIP(), []int{1, 0}
}

var File_anno_v1_specgrant_proto protoreflect.FileDescriptor

var file_anno_v1_specgrant_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x70, 0x65, 0x63, 0x67, 0x72,
	0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc9, 0x01, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x65, 0x65, 0x5f, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x65, 0x65, 0x55, 0x69,
	0x64, 0x12, 0x49, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01,
	0x20, 0x00, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x69, 0x74, 0x65, 0x6d, 0x55, 0x69, 0x64, 0x3a, 0x28, 0xba, 0x47, 0x25, 0xba, 0x01, 0x0b, 0x67,
	0x72, 0x61, 0x6e, 0x74, 0x65, 0x65, 0x5f, 0x75, 0x69, 0x64, 0xba, 0x01, 0x09, 0x69, 0x74, 0x65,
	0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0xba, 0x01, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x75, 0x69,
	0x64, 0x22, 0x88, 0x03, 0x0a, 0x09, 0x53, 0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x65, 0x65, 0x5f, 0x75, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x65, 0x65, 0x55, 0x69,
	0x64, 0x12, 0x49, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01,
	0x20, 0x00, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x69, 0x74, 0x65, 0x6d, 0x55, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64,
	0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0x30, 0x0a, 0x08, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22, 0x24, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12,
	0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00,
	0x12, 0x0b, 0x0a, 0x07, 0x41, 0x6e, 0x6e, 0x6f, 0x4c, 0x6f, 0x74, 0x10, 0x01, 0x3a, 0x4d, 0xba,
	0x47, 0x4a, 0xba, 0x01, 0x0b, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64,
	0xba, 0x01, 0x0b, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x65, 0x65, 0x5f, 0x75, 0x69, 0x64, 0xba, 0x01,
	0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0xba, 0x01, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0xba, 0x01, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x75, 0x69, 0x64, 0xba,
	0x01, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0xe4, 0x01, 0x0a,
	0x0f, 0x53, 0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x6f, 0x72, 0x55, 0x69,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x65, 0x65, 0x5f, 0x75, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x65, 0x65, 0x55,
	0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64, 0x12, 0x3d, 0x0a, 0x09, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x67, 0x72, 0x61,
	0x6e, 0x74, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x75, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1a, 0xfa,
	0x42, 0x17, 0x92, 0x01, 0x14, 0x22, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x55,
	0x69, 0x64, 0x73, 0x22, 0x54, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x70, 0x65,
	0x63, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e,
	0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x8a, 0x01, 0x0a, 0x14, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x21, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52, 0x06, 0x70,
	0x61, 0x67, 0x65, 0x73, 0x7a, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x30, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x88, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2a, 0x0a,
	0x06, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e,
	0x74, 0x52, 0x06, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x3a, 0x1e, 0xba, 0x47, 0x1b, 0xba, 0x01, 0x06, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x73, 0xba,
	0x01, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x32, 0xb8, 0x02, 0x0a, 0x0a, 0x53, 0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x73,
	0x12, 0x61, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x70, 0x65, 0x63, 0x67, 0x72,
	0x61, 0x6e, 0x74, 0x12, 0x1f, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13,
	0x3a, 0x01, 0x2a, 0x22, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x70, 0x65, 0x63, 0x67, 0x72, 0x61,
	0x6e, 0x74, 0x73, 0x12, 0x62, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x70, 0x65,
	0x63, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x12, 0x1f, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x2a, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x70, 0x65,
	0x63, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x73, 0x12, 0x63, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x12, 0x1d, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x12, 0x0e, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x70, 0x65, 0x63, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x73, 0x42, 0x3e, 0x0a, 0x07,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x31, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72,
	0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e, 0x6e, 0x6f, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_anno_v1_specgrant_proto_rawDescOnce sync.Once
	file_anno_v1_specgrant_proto_rawDescData = file_anno_v1_specgrant_proto_rawDesc
)

func file_anno_v1_specgrant_proto_rawDescGZIP() []byte {
	file_anno_v1_specgrant_proto_rawDescOnce.Do(func() {
		file_anno_v1_specgrant_proto_rawDescData = protoimpl.X.CompressGZIP(file_anno_v1_specgrant_proto_rawDescData)
	})
	return file_anno_v1_specgrant_proto_rawDescData
}

var file_anno_v1_specgrant_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_anno_v1_specgrant_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_anno_v1_specgrant_proto_goTypes = []interface{}{
	(Specgrant_ItemType_Enum)(0),   // 0: anno.v1.Specgrant.ItemType.Enum
	(*CreateSpecgrantRequest)(nil), // 1: anno.v1.CreateSpecgrantRequest
	(*Specgrant)(nil),              // 2: anno.v1.Specgrant
	(*SpecgrantFilter)(nil),        // 3: anno.v1.SpecgrantFilter
	(*DeleteSpecgrantRequest)(nil), // 4: anno.v1.DeleteSpecgrantRequest
	(*ListSpecgrantRequest)(nil),   // 5: anno.v1.ListSpecgrantRequest
	(*ListSpecgrantReply)(nil),     // 6: anno.v1.ListSpecgrantReply
	(*Specgrant_ItemType)(nil),     // 7: anno.v1.Specgrant.ItemType
	(*timestamppb.Timestamp)(nil),  // 8: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),          // 9: google.protobuf.Empty
}
var file_anno_v1_specgrant_proto_depIdxs = []int32{
	0,  // 0: anno.v1.CreateSpecgrantRequest.item_type:type_name -> anno.v1.Specgrant.ItemType.Enum
	0,  // 1: anno.v1.Specgrant.item_type:type_name -> anno.v1.Specgrant.ItemType.Enum
	8,  // 2: anno.v1.Specgrant.created_at:type_name -> google.protobuf.Timestamp
	0,  // 3: anno.v1.SpecgrantFilter.item_type:type_name -> anno.v1.Specgrant.ItemType.Enum
	3,  // 4: anno.v1.DeleteSpecgrantRequest.filter:type_name -> anno.v1.SpecgrantFilter
	3,  // 5: anno.v1.ListSpecgrantRequest.filter:type_name -> anno.v1.SpecgrantFilter
	2,  // 6: anno.v1.ListSpecgrantReply.grants:type_name -> anno.v1.Specgrant
	1,  // 7: anno.v1.Specgrants.CreateSpecgrant:input_type -> anno.v1.CreateSpecgrantRequest
	4,  // 8: anno.v1.Specgrants.DeleteSpecgrant:input_type -> anno.v1.DeleteSpecgrantRequest
	5,  // 9: anno.v1.Specgrants.ListSpecgrant:input_type -> anno.v1.ListSpecgrantRequest
	2,  // 10: anno.v1.Specgrants.CreateSpecgrant:output_type -> anno.v1.Specgrant
	9,  // 11: anno.v1.Specgrants.DeleteSpecgrant:output_type -> google.protobuf.Empty
	6,  // 12: anno.v1.Specgrants.ListSpecgrant:output_type -> anno.v1.ListSpecgrantReply
	10, // [10:13] is the sub-list for method output_type
	7,  // [7:10] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_anno_v1_specgrant_proto_init() }
func file_anno_v1_specgrant_proto_init() {
	if File_anno_v1_specgrant_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_anno_v1_specgrant_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSpecgrantRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_specgrant_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Specgrant); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_specgrant_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpecgrantFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_specgrant_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSpecgrantRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_specgrant_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSpecgrantRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_specgrant_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSpecgrantReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_specgrant_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Specgrant_ItemType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anno_v1_specgrant_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_anno_v1_specgrant_proto_goTypes,
		DependencyIndexes: file_anno_v1_specgrant_proto_depIdxs,
		EnumInfos:         file_anno_v1_specgrant_proto_enumTypes,
		MessageInfos:      file_anno_v1_specgrant_proto_msgTypes,
	}.Build()
	File_anno_v1_specgrant_proto = out.File
	file_anno_v1_specgrant_proto_rawDesc = nil
	file_anno_v1_specgrant_proto_goTypes = nil
	file_anno_v1_specgrant_proto_depIdxs = nil
}
