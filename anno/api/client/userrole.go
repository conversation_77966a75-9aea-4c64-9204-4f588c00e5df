package client

import (
	"gitlab.rp.konvery.work/platform/apis/client"
)

const (
	SysRoleKAM  = client.SysRoleKAM
	SysRolePM   = client.SysRolePM
	SysRoleDemo = client.SysRoleDemo

	ScopeClsGroup = client.ScopeClsGroup

	// TeamRoleMember = client.TeamRoleMember
	// TeamRoleOwner  = client.TeamRoleOwner
)

var (
	UserScope        = client.UserScope
	GroupScope       = client.GroupScope
	IsSysRole        = client.IsSysRole
	IsPrivileged     = client.IsPrivileged
	IsPrivilegedUser = client.IsPrivilegedUser
)
