import json
import os
import sys

sys.path.append(".")
from customer.common import tools

line_count = 0
file_count = 0
err_files = {}
file_box_dict = {}
current_file = ""
object_count = 0

PI = 3.1415926


def convert(labels):
    global object_count
    obj_list = []

    for item in labels["lidar"]:
        obj_type = item["class"]

        if obj_type == "AUTO":
            continue

        attribute = []
        for attr in item["attrs"].keys():
            if item["attrs"][attr][0] != "NORMAL":
                attribute.append(item["attrs"][attr][0])

        anno = item["annotation"]["data"]
        heading = anno["rotation"]["z"]

        if heading < 0:
            heading = heading + 2 * PI

        obj = {
            "LABEL": "HUMAN",
            "attribute": attribute,
            "bounding_box": anno["dimension"],
            "heading": heading,
            "position": anno["position"],
            "type": obj_type
        }

        obj_list.append(obj)
        object_count += 1
    return obj_list


def run(test_in_dir, origin_dir, output_dir):
    global file_count
    global current_file
    global file_box_dict

    file_count = 0
    invalid_file_count = 0
    invalid_file_list = []

    tools.check_dir(output_dir)
    test_in_dir = tools.unzip(test_in_dir)

    for f in tools.get_json_files(test_in_dir):
        if f.name.endswith('.json') and not f.name.startswith("."):

            convert_data = {"from": "semi-auto", "objects": []}
            obj_list = []

            # read data from customer origin txt file and add to convert_data
            f_origin = f.path.replace("test_in", "origin").replace(".json", ".txt").replace("label", "")
            with open(f_origin) as json_file:
                origin_data = json.load(json_file)
                for item in origin_data["objects"]:
                    obj = {
                        "LABEL": "AUTO",
                        "bounding_box": item["bounding_box"],
                        "heading": item["heading"],
                        "position": item["position"],
                    }
                    obj_list.append(obj)

            # get manual added labels from test_in json files
            data = {}
            with open(f.path) as json_file:
                data = json.load(json_file)
                file_count += 1
                obj_list.append(convert(data))
                convert_data["objects"] = obj_list

            # write to result files
            output_file_name = f.path.replace(test_in_dir, "output")
            os.makedirs(os.path.dirname(output_file_name), exist_ok=True)

            with open(output_file_name, "w") as outfile:
                #json.dump(convert_data, outfile, indent=4)
                json.dump(convert_data, outfile)

    err_title = "错误信息"
    err_type_list = ["无效值"]
    return tools.check_error(output_dir, err_files, err_title, file_count, object_count, err_type_list, is_zip=False)


"""
    项目： GRU 3D项目
    对接平台：云测新平台 
    功能： 云测导出的数据转换成客户格式
    输入： json
    输出： json
"""
if __name__ == "__main__":
    args = tools.get_args()
    args.test_in = "../../../input/test/gru/test_in"
    args.origin = "../../../input/test/gru/origin"

    run(args.test_in, args.origin, args.out)
