package authfilter

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNoauthWhitelist(t *testing.T) {
	filter := WhitelistNoauthFilter(
		"/iam.v1.Users/SendAuthCode",
		"/iam.v1.Users/Login",
	)
	cases := []struct {
		endpoint string
		expected bool
	}{
		{
			endpoint: "/foo/bar",
			expected: false,
		},
		{
			endpoint: "/iam.v1.users/sendauthcode",
			expected: false,
		},
		{
			endpoint: "/iam.v1.Users/SendAuthCode",
			expected: true,
		},
		{
			endpoint: "/iam.v1.Users/Login",
			expected: true,
		},
	}
	for _, c := range cases {
		assert.Equal(t, c.expected, filter(c.endpoint))
	}
}

func TestPrefixNoauthFilter(t *testing.T) {
	filter := PrefixNoauthFilter(
		"/iam.v1.Configs/",
		"/iam.v1.Noauth/",
	)
	cases := []struct {
		endpoint string
		expected bool
	}{
		{
			endpoint: "/foo/bar",
			expected: false,
		},
		{
			endpoint: "x/iam.v1.Configs/Errors",
			expected: false,
		},
		{
			endpoint: "/iam.v1.Configs/Errors",
			expected: true,
		},
		{
			endpoint: "/iam.v1.Noauth/Foobar",
			expected: true,
		},
	}
	for _, c := range cases {
		assert.Equal(t, c.expected, filter(c.endpoint))
	}
}

func TestSuffixNoauthFilter(t *testing.T) {
	filter := SuffixNoauthFilter(
		"/Login",
		"/Noauth",
	)
	cases := []struct {
		endpoint string
		expected bool
	}{
		{
			endpoint: "/foo/bar",
			expected: false,
		},
		{
			endpoint: "/iam.v1.Users/Loginx",
			expected: false,
		},
		{
			endpoint: "/iam.v1.Users/Login",
			expected: true,
		},
		{
			endpoint: "/iam.v1.Users/Noauth",
			expected: true,
		},
	}
	for _, c := range cases {
		assert.Equal(t, c.expected, filter(c.endpoint))
	}
}

func TestRegexpNoauthFilter(t *testing.T) {
	filter := RegexpNoauthFilter(
		`/users/[^/]+/tag$`,
		`/noauth$`,
	)
	cases := []struct {
		endpoint string
		expected bool
	}{
		{
			endpoint: "/foo/bar",
			expected: false,
		},
		{
			endpoint: "/iam/v1/users/foobar/tagx",
			expected: false,
		},
		{
			endpoint: "/iam/v1/users/foobar/tag",
			expected: true,
		},
		{
			endpoint: "/whatever/noauth/x",
			expected: false,
		},
		{
			endpoint: "/whatever/noauth",
			expected: true,
		},
	}
	for _, c := range cases {
		assert.Equal(t, c.expected, filter(c.endpoint))
	}
}

func TestSkipAuth(t *testing.T) {
	filters := []NoauthFilter{
		WhitelistNoauthFilter("/iam.v1.Users/Login"),
		PrefixNoauthFilter("/iam.v1.Configs/"),
		SuffixNoauthFilter("/Noauth"),
		RegexpNoauthFilter(`/users/[^/]+/tag$`),
	}
	cases := []struct {
		endpoint string
		expected bool
	}{
		{
			endpoint: "/foo/bar",
			expected: false,
		},
		{
			endpoint: "/iam.v1.Users/Login",
			expected: true,
		},
		{
			endpoint: "/iam.v1.Configs/whatever",
			expected: true,
		},
		{
			endpoint: "whatever/Noauth",
			expected: true,
		},
		{
			endpoint: "/iam/v1/users/foobar/tag",
			expected: true,
		},
	}
	for _, c := range cases {
		assert.Equal(t, c.expected, SkipAuth(filters, c.endpoint))
	}
}
