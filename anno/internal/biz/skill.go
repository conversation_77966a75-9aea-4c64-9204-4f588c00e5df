// source: anno/v1/skill.proto
package biz

import (
	"context"
	"time"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type Skill struct {
	ID       int64  `json:"id" gorm:"default:null"`
	Type     string `json:"type" gorm:"default:null"`
	Name     string `json:"name" gorm:"default:null"`
	Desc     string `json:"desc" gorm:"default:null"`
	Avatar   string `json:"avatar" gorm:"default:null"`
	MaxLevel int32  `json:"max_level" gorm:"default:null"`
	Langs    StrMap `json:"langs" gorm:"default:null"`

	CreatorUid string    `json:"creator_uid" gorm:"default:null"`
	UpdatedAt  time.Time `json:"updated_at" gorm:"default:null"`
	CreatedAt  time.Time `json:"created_at" gorm:"default:null"`
	DeletedAt  DeletedAt `json:"deleted_at" gorm:"default:null"`
}

func (o *Skill) GetID() int64 { return o.ID }
func (o *Skill) EnsureID() {
	if o.ID == 0 {
		o.ID = kid.NewID()
	}
}
func (o *Skill) GetUid() string { return kid.StringID(o.ID) }
func (o *Skill) UidFld() string { return SkillSfldName }

const SkillTableName = "skills"

var (
	Skill_             = field.RegObject(&Skill{})
	SkillUpdatableFlds = field.NewModel(SkillTableName, Skill_)

	SkillSfldType       = field.Sname(&Skill_.Type)
	SkillSfldName       = field.Sname(&Skill_.Name)
	SkillSfldDesc       = field.Sname(&Skill_.Desc)
	SkillSfldAvatar     = field.Sname(&Skill_.Avatar)
	SkillSfldMaxLevel   = field.Sname(&Skill_.MaxLevel)
	SkillSfldLangs      = field.Sname(&Skill_.Langs)
	SkillSfldCreatorUid = field.Sname(&Skill_.CreatorUid)
)

type SkillListFilter struct {
	Uids        []string
	NamePattern string
}

type SkillsRepo interface {
	Create(context.Context, *Skill) (*Skill, error)
	Update(context.Context, *Skill, *FieldMask) (*Skill, error)
	GetByID(context.Context, int64) (*Skill, error)
	GetByUid(context.Context, string) (*Skill, error)
	DeleteByID(context.Context, int64) error
	DeleteByUid(context.Context, string) error
	List(context.Context, *SkillListFilter, Pager) ([]*Skill, error)
	Count(context.Context, *SkillListFilter) (int, error)
}

type SkillsBiz struct {
	repo SkillsRepo
	log  *log.Helper
}

func NewSkillsBiz(repo SkillsRepo, logger log.Logger) *SkillsBiz {
	return &SkillsBiz{repo: repo, log: log.NewHelper(logger)}
}

func (o *SkillsBiz) Create(ctx context.Context, p *Skill) (*Skill, error) {
	o.log.Info(ctx, "CreateSkill", "param", p)
	return o.repo.Create(ctx, p)
}

func (o *SkillsBiz) Update(ctx context.Context, p *Skill, fldMask *FieldMask) (*Skill, error) {
	o.log.Info(ctx, "UpdateSkill", "param", p)
	return o.repo.Update(ctx, p, fldMask)
}

func (o *SkillsBiz) GetByID(ctx context.Context, id int64) (*Skill, error) {
	return o.repo.GetByID(ctx, id)
}

func (o *SkillsBiz) GetByUid(ctx context.Context, uid string) (*Skill, error) {
	return o.repo.GetByUid(ctx, uid)
}

func (o *SkillsBiz) DeleteByID(ctx context.Context, id int64) error {
	o.log.Info(ctx, "DeleteByIDSkill", "id", id)
	return o.repo.DeleteByID(ctx, id)
}

func (o *SkillsBiz) DeleteByUid(ctx context.Context, uid string) error {
	o.log.Info(ctx, "DeleteByUidSkill", "uid", uid)
	return o.repo.DeleteByUid(ctx, uid)
}

func (o *SkillsBiz) List(ctx context.Context, filter *SkillListFilter, pager Pager) ([]*Skill, error) {
	o.log.Info(ctx, "ListSkill", "param", filter)
	return o.repo.List(ctx, filter, pager)
}

func (o *SkillsBiz) Count(ctx context.Context, filter *SkillListFilter) (int, error) {
	return o.repo.Count(ctx, filter)
}
