FROM alpine:3.17.2

# Because this image is built for SQLite, we create /home/<USER>/home/<USER>/sqlite which is owned by the ory user
# and declare /home/<USER>/sqlite a volume.
#
# To get SQLite and Docker Volumes working with this image, mount the volume where SQLite should be written to at:
#
#   /home/<USER>/sqlite/some-file.

RUN addgroup -S ory; \
    adduser -S ory -G ory -D  -h /home/<USER>/bin/nologin; \
    chown -R ory:ory /home/<USER>

RUN apk --no-cache --latest upgrade
RUN apk --no-cache --upgrade --latest add ca-certificates

WORKDIR /home/<USER>

COPY keto /usr/bin/keto

# By creating the sqlite folder as the ory user, the mounted volume will be owned by ory:ory, which
# is required for read/write of SQLite.
RUN mkdir -p /var/lib/sqlite
RUN chown ory:ory /var/lib/sqlite
VOLUME /var/lib/sqlite

# Exposing the ory home directory to simplify passing in keto configuration (e.g. if the file $HOME/.keto.yaml
# exists, it will be automatically used as the configuration file).
VOLUME /home/<USER>

# Declare the standard ports used by keto (4433 for read service endpoint, 4434 for write service endpoint)
EXPOSE 4433 4434

USER ory

ENTRYPOINT ["keto"]
CMD ["serve"]
