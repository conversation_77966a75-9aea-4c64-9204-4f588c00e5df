package conf

import (
	"os"

	"github.com/spf13/cast"
)

func GetMaxUploader() int {
	const defaultUploader = 10
	v := cast.ToInt(os.Getenv("DBG_MAX_UPLOADER"))
	if v == 0 {
		return defaultUploader
	}
	return v
}

func ShouldSaveAccessURL() bool {
	return !cast.ToBool(os.Getenv("DBG_NOT_SAVE_ACCESS_URL"))
}

func GetPcdSplitThreshold() int {
	return cast.ToInt(os.Getenv("DBG_PCD_SPLIT_THRESHOLD_IN_MILLION_POINTS"))
}

func GetPcdSplitMaxSegments() int {
	return cast.ToInt(os.Getenv("DBG_PCD_SPLIT_MAX_SEGMENTS"))
}
