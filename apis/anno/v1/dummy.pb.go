// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anno/v1/dummy.proto

package anno

import (
	types "gitlab.rp.konvery.work/platform/apis/types"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DummyReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ElementDataRef    *Job_ElementData    `protobuf:"bytes,1,opt,name=ElementDataRef,proto3" json:"ElementDataRef,omitempty"`
	AnnotationDataRef *Job_AnnotationData `protobuf:"bytes,2,opt,name=AnnotationDataRef,proto3" json:"AnnotationDataRef,omitempty"`
	CommentDataRef    *Job_CommentData    `protobuf:"bytes,3,opt,name=CommentDataRef,proto3" json:"CommentDataRef,omitempty"`
	FilelistRef       *types.Filelist     `protobuf:"bytes,4,opt,name=FilelistRef,proto3" json:"FilelistRef,omitempty"`
}

func (x *DummyReply) Reset() {
	*x = DummyReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_dummy_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DummyReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DummyReply) ProtoMessage() {}

func (x *DummyReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_dummy_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DummyReply.ProtoReflect.Descriptor instead.
func (*DummyReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_dummy_proto_rawDescGZIP(), []int{0}
}

func (x *DummyReply) GetElementDataRef() *Job_ElementData {
	if x != nil {
		return x.ElementDataRef
	}
	return nil
}

func (x *DummyReply) GetAnnotationDataRef() *Job_AnnotationData {
	if x != nil {
		return x.AnnotationDataRef
	}
	return nil
}

func (x *DummyReply) GetCommentDataRef() *Job_CommentData {
	if x != nil {
		return x.CommentDataRef
	}
	return nil
}

func (x *DummyReply) GetFilelistRef() *types.Filelist {
	if x != nil {
		return x.FilelistRef
	}
	return nil
}

var File_anno_v1_dummy_proto protoreflect.FileDescriptor

var file_anno_v1_dummy_proto_rawDesc = []byte{
	0x0a, 0x13, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x75, 0x6d, 0x6d, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x61, 0x6e, 0x6e, 0x6f, 0x2f,
	0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x8e, 0x02, 0x0a, 0x0a, 0x44, 0x75, 0x6d, 0x6d, 0x79, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x40, 0x0a, 0x0e, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x2e, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x0e, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x66, 0x12, 0x49, 0x0a, 0x11, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x2e, 0x41, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x11, 0x41, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x66, 0x12, 0x40,
	0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x66,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x4a, 0x6f, 0x62, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x66,
	0x12, 0x31, 0x0a, 0x0b, 0x46, 0x69, 0x6c, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x66, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x46, 0x69,
	0x6c, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x0b, 0x46, 0x69, 0x6c, 0x65, 0x6c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x66, 0x32, 0x50, 0x0a, 0x05, 0x44, 0x75, 0x6d, 0x6d, 0x79, 0x12, 0x47, 0x0a, 0x05,
	0x44, 0x75, 0x6d, 0x6d, 0x79, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x13, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x75, 0x6d, 0x6d, 0x79, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x11, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0b, 0x12, 0x09, 0x2f, 0x76, 0x31, 0x2f,
	0x64, 0x75, 0x6d, 0x6d, 0x79, 0x42, 0x3e, 0x0a, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x31, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31,
	0x3b, 0x61, 0x6e, 0x6e, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_anno_v1_dummy_proto_rawDescOnce sync.Once
	file_anno_v1_dummy_proto_rawDescData = file_anno_v1_dummy_proto_rawDesc
)

func file_anno_v1_dummy_proto_rawDescGZIP() []byte {
	file_anno_v1_dummy_proto_rawDescOnce.Do(func() {
		file_anno_v1_dummy_proto_rawDescData = protoimpl.X.CompressGZIP(file_anno_v1_dummy_proto_rawDescData)
	})
	return file_anno_v1_dummy_proto_rawDescData
}

var file_anno_v1_dummy_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_anno_v1_dummy_proto_goTypes = []interface{}{
	(*DummyReply)(nil),         // 0: anno.v1.DummyReply
	(*Job_ElementData)(nil),    // 1: anno.v1.Job.ElementData
	(*Job_AnnotationData)(nil), // 2: anno.v1.Job.AnnotationData
	(*Job_CommentData)(nil),    // 3: anno.v1.Job.CommentData
	(*types.Filelist)(nil),     // 4: types.Filelist
	(*emptypb.Empty)(nil),      // 5: google.protobuf.Empty
}
var file_anno_v1_dummy_proto_depIdxs = []int32{
	1, // 0: anno.v1.DummyReply.ElementDataRef:type_name -> anno.v1.Job.ElementData
	2, // 1: anno.v1.DummyReply.AnnotationDataRef:type_name -> anno.v1.Job.AnnotationData
	3, // 2: anno.v1.DummyReply.CommentDataRef:type_name -> anno.v1.Job.CommentData
	4, // 3: anno.v1.DummyReply.FilelistRef:type_name -> types.Filelist
	5, // 4: anno.v1.Dummy.Dummy:input_type -> google.protobuf.Empty
	0, // 5: anno.v1.Dummy.Dummy:output_type -> anno.v1.DummyReply
	5, // [5:6] is the sub-list for method output_type
	4, // [4:5] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_anno_v1_dummy_proto_init() }
func file_anno_v1_dummy_proto_init() {
	if File_anno_v1_dummy_proto != nil {
		return
	}
	file_anno_v1_job_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_anno_v1_dummy_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DummyReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anno_v1_dummy_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_anno_v1_dummy_proto_goTypes,
		DependencyIndexes: file_anno_v1_dummy_proto_depIdxs,
		MessageInfos:      file_anno_v1_dummy_proto_msgTypes,
	}.Build()
	File_anno_v1_dummy_proto = out.File
	file_anno_v1_dummy_proto_rawDesc = nil
	file_anno_v1_dummy_proto_goTypes = nil
	file_anno_v1_dummy_proto_depIdxs = nil
}
