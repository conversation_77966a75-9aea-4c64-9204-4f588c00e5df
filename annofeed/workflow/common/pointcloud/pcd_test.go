package pointcloud

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_ParsePCDHeader(t *testing.T) {
	testCases := []struct {
		name           string
		data           string
		expectError    bool
		expectedHeader *PointCloudHeader
	}{
		{
			name: "PCD_header_is_valid",
			data: `VERSION .7
FIELDS x y z rgb
SIZE 4 4 4 4
TYPE F F F F
COUNT 1 1 1 1
WIDTH 213
HEIGHT 1
VIEWPOINT 0 0 0 1 0 0 0
POINTS 213
DATA ascii
`,
			expectError: false,
			expectedHeader: &PointCloudHeader{
				Version:   0.7,
				Fields:    []string{"x", "y", "z", "rgb"},
				Size:      []int{4, 4, 4, 4},
				Type:      []string{"F", "F", "F", "F"},
				Count:     []int{1, 1, 1, 1},
				Width:     213,
				Height:    1,
				Viewpoint: []float32{0, 0, 0, 1, 0, 0, 0},
				Points:    213,
				Format:    PCDFormatAscii,
				Bytes:     133,
			},
		},
		{
			name: "Wrong first line",
			data: `ERSION .7
FIELDS x y z rgb
SIZE 4 4 4 4
TYPE F F F F
COUNT 1 1 1 1
WIDTH 213
HEIGHT 1
VIEWPOINT 0 0 0 1 0 0 0
POINTS 213
DATA ascii
`,
			expectError: true,
		},
		{
			name: "Wrong header VERSION",
			data: `# .PCD v.7 - Point Cloud Data file format
VERSIO .7
FIELDS x y z rgb
SIZE 4 4 4 4
TYPE F F F F
COUNT 1 1 1 1
WIDTH 213
HEIGHT 1
VIEWPOINT 0 0 0 1 0 0 0
POINTS 213
DATA ascii
`,
			expectError: true,
		},
		{
			name: "Wrong header FIELDS",
			data: `# .PCD v.7 - Point Cloud Data file format
VERSION .7
FIELD x y z rgb
SIZE 4 4 4 4
TYPE F F F F
COUNT 1 1 1 1
WIDTH 213
HEIGHT 1
VIEWPOINT 0 0 0 1 0 0 0
POINTS 213
DATA ascii
`,
			expectError: true,
		},
		{
			name: "Too much headers",
			data: `# .PCD v.7 - Point Cloud Data file format
VERSION .7
FIELD x y z rgb
SIZE 4 4 4 4
TYPE F F F F
COUNT 1 1 1 1
WIDTH 213
HEIGHT 1
VIEWPOINT 0 0 0 1 0 0 0
POINTS 213
POINTS 213
DATA ascii
`,
			expectError: true,
		},
		{
			name: "Missing header DATA",
			data: `# .PCD v.7 - Point Cloud Data file format
VERSION .7
FIELD x y z rgb
SIZE 4 4 4 4
TYPE F F F F
COUNT 1 1 1 1
WIDTH 213
HEIGHT 1
VIEWPOINT 0 0 0 1 0 0 0
POINTS 213
`,
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reader := strings.NewReader(tc.data)
			header, err := ParsePCDHeader(bufio.NewReader(reader))
			if (err != nil) != tc.expectError {
				t.Fatalf("unexpected error: %v", err)
			}
			assert.Equal(t, tc.expectedHeader, header)
		})
	}
}

func Test_TransformPCDToBinary(t *testing.T) {
	testCases := []struct {
		name        string
		fpath       string
		expectError bool
	}{
		{
			name:        "ascii.pcd",
			fpath:       "",
			expectError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if tc.fpath == "" {
				return
			}

			// 1. transform
			err := TransformPCDToBinary(tc.fpath)
			assert.NoError(t, err)
			// 2. open the generated pcd in CloudCompare
		})
	}
}

func Test_transformPCDToBinary(t *testing.T) {
	testCases := []struct {
		name   string
		format PCDFormat
		data   string
	}{
		{
			name:   "PCD_is_Ascii",
			format: PCDFormatAscii,
			data: `# .PCD v0.7 - Point Cloud Data file format
VERSION 0.7
FIELDS x y z intensity
SIZE 4 4 4 4
TYPE U F F F
COUNT 1 1 1 1
WIDTH 1011356
HEIGHT 1
VIEWPOINT 0 0 0 1 0 0 0
POINTS 1
DATA ascii
171 6.228532 3.475136 4.015748
172 6.228532 3.475136 4.015748
`,
		},
		{
			name:   "PCD_file",
			format: PCDFormatAscii,
			data:   "",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if tc.data == "" {
				return
			}

			if strings.HasSuffix(tc.data, ".pcd") {
				fr, err := os.Open(tc.data)
				if err != nil {
					t.Fatal(err)
				}

				output := tc.data + ".binary.pcd"
				fw, err := os.Create(output)
				if err != nil {
					t.Fatal(err)
				}

				if err := transformPCDToBinary(fr, fw, tc.format); err != nil {
					t.Fatal(err)
				}
				return
			}

			reader := strings.NewReader(tc.data)
			err := transformPCDToBinary(reader, os.Stdout, tc.format)
			fmt.Println() // to beautify unit test output
			assert.NoError(t, err)
		})
	}
}

func Test_calSegments(t *testing.T) {
	threshold := getSplitThreshold()

	cases := []struct {
		name     string
		points   int
		expected int
	}{
		{
			name:     "points < threshold",
			points:   threshold - 1,
			expected: 1,
		},
		{
			name:     "points = threshold",
			points:   threshold,
			expected: 1,
		},
		{
			name:     "points = threshold * 1.5 - 1",
			points:   int(float64(threshold)*1.5) - 1,
			expected: 1,
		},
		{
			name:     "points = threshold * 1.5",
			points:   int(float64(threshold) * 1.5),
			expected: 2,
		},
		{
			name:     "points = threshold * 1.5 + 1",
			points:   int(float64(threshold)*1.5) + 1,
			expected: 2,
		},
		{
			name:     "points = threshold * 2.5 + 1",
			points:   int(float64(threshold)*2.5) + 1,
			expected: 3,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			parts := calSegments(c.points, threshold)
			assert.Equal(t, c.expected, parts)
		})
	}
}

func Test_SplitPCD(t *testing.T) {
	cases := []struct {
		name        string
		fpath       string
		expectParts int
	}{
		{
			name:        "pcd is split into a few segments",
			fpath:       "",
			expectParts: 0,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			if c.fpath == "" {
				return
			}

			results, err := SplitPCD(c.fpath)
			assert.NoError(t, err)
			for _, result := range results {
				t.Logf("result: %+v", result)
			}
			assert.Equal(t, c.expectParts, len(results))
		})
	}
}
