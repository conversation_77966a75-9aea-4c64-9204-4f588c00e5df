# 工作流执行流程示例分析

## 1. 输入参数分析

### 1.1 工作流输入参数
```json
{
  "e": {
    "uris": ["s3://non-prod-workload-sansheng/upload/25/0219/clpxs8nrdjj/35-2.zip"],
    "style": "",
    "metadata": {},
    "converter": null,
    "elem_type": "fusion3d",
    "auto_parse": true,
    "named_uris": {},
    "proprietary": {"type": "", "config": ""},
    "plain_size_gb": 0,
    "error_handlers": [{"error": "unspecified", "handler": "ignore", "rawdata_type": "unspecified"}],
    "is_frame_series": false
  }
}
```

### 1.2 本地文件结构
```
data/
  clip/
    1726105510700000000_640_0/
      front_narrow.jpg
      front_wide.jpg
      back.jpg
      left_front.jpg
      left_back.jpg
      right_front.jpg
      right_back.jpg
      lidar.pcd
    ...
    params.json
```

## 2. 工作流执行流程分析

### 2.1 工作流初始化阶段

1. 工作流注册
```go
// workflow/worker.go
func StartWorker(activities ...any) {
    startWorker(false, activities, []any{})
}
```
- 在主进程中注册工作流和活动
- 设置默认配置

2. 工作流启动判断
```go
// vendor/gitlab.rp.konvery.work/platform/pkg/wf/ktwf/init.go
func StartWorkflow(ctx context.Context, name string, spec *WorkflowSpec, shareWorker bool) {
    var wffn any = WFSchedulerWorkflow
    surffix := "-scheduler"
    if shareWorker || dbgmode {
        surffix = ""
        wffn = WFMainWorkflow
    }
}
```
- 由于`plain_size_gb: 0`，判断不需要额外存储空间
- 使用主进程执行工作流（`WFMainWorkflow`）

### 2.2 工作流步骤执行

根据`workflow/preparedata/wf.yaml`定义的步骤：

1. PrepareDataWrapup (defer=true)
- 类型：activity
- 执行环境：主进程
- 功能：收尾工作，最后执行

2. PrepareDataFetch
- 类型：activity
- 执行环境：主进程
- 输入：本地zip文件路径
- 处理：
  - 解压zip文件
  - 创建标准目录结构
  - 移动文件到对应位置

3. PrepareDataParseMeta
- 类型：activity
- 执行环境：主进程
- 输入：解压后的文件结构
- 处理：
  - 解析params.json
  - 提取相机参数
  - 提取激光雷达参数
  - 更新数据类型为"fusion3d"

4. PrepareDataConverterScript
- 类型：activity
- 执行环境：主进程
- 输入：converter配置
- 处理：
  - 由于converter为null，跳过此步骤

5. PrepareDataTransform
- 类型：activity
- 执行环境：主进程
- 输入：标准化后的文件结构
- 处理：
  - 根据elem_type="fusion3d"进行数据转换
  - 更新数据类型信息

6. PrepareDataStandardize
- 类型：activity
- 执行环境：主进程
- 输入：转换后的文件结构
- 处理：
  - 标准化数据格式
  - 创建数据目录
  - 移动文件到数据目录
  - 删除不必要的文件

7. PrepareDataDetectDataType
- 类型：activity
- 执行环境：主进程
- 输入：标准化后的文件结构
- 处理：
  - 分析文件类型（图像、点云）
  - 确认数据类型为"fusion3d"
  - 更新数据类型信息

8. PrepareDataValidateRawdatas
- 类型：activity
- 执行环境：主进程
- 输入：标准化后的文件结构
- 处理：
  - 验证文件完整性
  - 检查命名规范
  - 根据error_handlers处理错误

9. PrepareDataParseRawdatas
- 类型：activity
- 执行环境：主进程
- 输入：验证通过的文件结构
- 处理：
  - 解析原始数据文件
  - 创建Rawdata记录
  - 提取元数据

10. PrepareDataParseAnnos
- 类型：activity
- 执行环境：主进程
- 输入：原始数据记录
- 处理：
  - 由于没有标注文件，跳过此步骤

11. PrepareDataUploadRawdatas
- 类型：activity
- 执行环境：主进程
- 输入：解析完成的数据记录
- 处理：
  - 准备上传数据
  - 并发上传文件
  - 更新文件URI

### 2.3 执行环境判断逻辑

1. 存储需求判断
```go
func newWFInstance(wfc *WorkflowContext, act *Activities) *wfInstance {
    nopod := wfc.Spec.Workflow.Storage.SizeGB == 0
    taskq := lo.Ternary(nopod, "", wfc.Name+"-worker")
    return &wfInstance{wfc: wfc, act: act, taskq: taskq, native: nopod}
}
```
- 由于`plain_size_gb: 0`，`nopod = true`
- 使用主进程执行

2. 步骤类型判断
```go
func (o *wfInstance) ExecuteStep(ctx workflow.Context, step *Step) (err error) {
    switch step.Type {
    case "activity":
        err = o.ExecuteActivity(ctx, step, false)
    case "kubejob":
        err = o.ExecuteKubeJob(ctx, step, step.KubeJob)
    case "task":
        err = o.ExecuteTask(ctx, step.Task)
    }
}
```
- 所有步骤都是"activity"类型
- 在主进程中执行

## 3. 关键数据流

### 3.1 文件处理流程
```
本地zip文件
  → 解压到临时目录
  → 标准化目录结构
  → 数据转换
  → 验证和解析
  → 上传到存储服务
```

### 3.2 数据类型转换
```
原始数据
  → 解析元数据
  → 确认数据类型(fusion3d)
  → 标准化格式
  → 创建数据记录
```

### 3.3 错误处理流程
```
验证错误
  → 检查error_handlers配置
  → 根据配置处理错误(ignore/retry/fail)
  → 更新错误状态
```

## 4. 总结

1. 执行环境：
   - 所有步骤在主进程中执行
   - 不需要创建pod（因为`plain_size_gb: 0`）

2. 数据处理：
   - 完整的文件处理流程
   - 标准化的数据类型转换
   - 灵活的错误处理机制

3. 性能考虑：
   - 使用主进程执行提高效率
   - 避免不必要的pod创建
   - 支持并发上传

4. 可扩展性：
   - 支持不同类型的数据处理
   - 可配置的错误处理
   - 灵活的执行环境选择 