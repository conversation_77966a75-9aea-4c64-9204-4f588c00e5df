import json
import os
import shutil
import sys

import numpy as np
from copy import deepcopy
from scipy.spatial.transform import Rotation as R

sys.path.append(".")
from customer.common import tools
from customer.common.accumulate_frame_mapping import Accumualate_Map

pack_count = 0
total_file_count = 0
invalid_pcd_file_count = 0
copied_pcd_file_count = 0
config_dict_header = {}

# NuScene json files
calibrated_sensor = {}
ego_pose_dict = {}
is_merged_pcd = True
merged_pcd = False
merge_into_one_file = False
root_dir = ""
camera_name_list = []
total_pcd_map = {}
project_dir = ""
black_image_count = 0
is_camara_name_read = False

pack_output_dir = ""


def compute_matrix(translation, rotation):
    # rotation quaternion: w, x, y, z.
    # 通过 translation 和 四元数表示的 rotation 生成外参 4x4 矩阵
    # |  R  t |
    # |  0  1 |
    # (x, y, z, w)
    converted_rotation = rotation[1:] + rotation[:1]
    rotation_matrix = R.from_quat(converted_rotation).as_matrix()
    extrinsic_matrix = np.eye(4)
    extrinsic_matrix[:3, :3] = rotation_matrix
    extrinsic_matrix[:3, 3] = translation
    return extrinsic_matrix


def get_extrinsic_for_single_fusion_point_cloud(camera_matrix, pose_matrix):
    return np.dot(np.linalg.inv(camera_matrix), np.linalg.inv(pose_matrix))


def generate_test_in_config(pcd_key_list, pcd_map, config_file, camera_name="cam_front"):
    global calibrated_sensor

    # 无论是普通融合 还是单帧融合 统一成单帧融合
    data_type = "single_fusion_pointcloud"
    sensor_params = {}
    config = {
        "camera": {},
        "data_type": data_type,
        "sensor_params": sensor_params
    }

    if data_type == "single_fusion_pointcloud":
        pcd_key = pcd_key_list[0]
        for camera_name in pcd_map[pcd_key]["camera"].keys():
            config[camera_name.lower()] = camera_name.lower()

        for pcd_key in pcd_key_list:
            pose = pcd_map[pcd_key]["pose"]
            translation = pose["translation"]
            rotation = pose["rotation"]
            pcd_file_name = pcd_key
            config["sensor_params"][pcd_file_name] = {}

            lost_camera_param_list = camera_name_list.copy()

            for camera_name in pcd_map[pcd_key]["camera"].keys():
                lost_camera_param_list.remove(camera_name)
                camera_matrix = compute_matrix(pcd_map[pcd_key]["camera"][camera_name]["translation"],
                                               pcd_map[pcd_key]["camera"][camera_name]["rotation"])
                pose_matrix = compute_matrix(translation, rotation)
                extrinsic = get_extrinsic_for_single_fusion_point_cloud(camera_matrix, pose_matrix)
                intrinsic = pcd_map[pcd_key]["camera"][camera_name]["camera_intrinsic"]

                config["sensor_params"][pcd_file_name][camera_name] = {
                    "camera_model": "pinhole",
                    "extrinsic": extrinsic.tolist(),
                    "fx": intrinsic[0][0],
                    "fy": intrinsic[1][1],
                    "cx": intrinsic[0][2],
                    "cy": intrinsic[1][2],
                    "k1": 0.0,
                    "k2": 0.0,
                    "k3": 0.0,
                    "k4": 0.0,
                    "k5": 0.0,
                    "k6": 0.0,
                    "p1": 0.0,
                    "p2": 0.0
                }

            for camera_name in lost_camera_param_list:
                config["sensor_params"][pcd_file_name][camera_name] = {
                    "camera_model": "pinhole",
                    "extrinsic": [
                        [
                            0.0,
                            0.0,
                            0.0,
                            0.0
                        ],
                        [
                            0.0,
                            0.0,
                            0.0,
                            0.0
                        ],
                        [
                            0.0,
                            0.0,
                            0.0,
                            0.0
                        ],
                        [
                            0.0,
                            0.0,
                            0.0,
                            0.0
                        ]
                    ],
                    "fx": 0.0,
                    "fy": 0.0,
                    "cx": 0.0,
                    "cy": 0.0,
                    "k1": 0.0,
                    "k2": 0.0,
                    "k3": 0.0,
                    "k4": 0.0,
                    "k5": 0.0,
                    "k6": 0.0,
                    "p1": 0.0,
                    "p2": 0.0
                }

    config["sensor_params"] = sensor_params

    with open(config_file, "w") as f:
        json.dump(config, f, indent=4)

    return config


def create_pack(input_dir, pcd_key_list, pcd_map, output_dir):
    global copied_pcd_file_count
    global black_image_count
    print(input_dir)
    output_dir = output_dir + os.sep
    lidar_dir = output_dir + os.sep + "lidar"
    tools.check_dir(output_dir)
    tools.check_dir(lidar_dir)
    # add_pre_label(input_dir, output_dir, pcd_map)

    for camera_name in camera_name_list:
        camera_dir = output_dir + os.sep + "camera" + os.sep + camera_name
        tools.check_dir(camera_dir)

    config_file = output_dir + os.sep + "config.json"
    generate_test_in_config(pcd_key_list, pcd_map, config_file)

    for pcd_key in pcd_key_list:
        src_pcd_file_name = input_dir + os.sep + "pcd" + os.sep + pcd_key + ".pcd"
        dst_pcd_file_name = lidar_dir + os.sep + pcd_key + ".pcd"
        shutil.copy(src_pcd_file_name, dst_pcd_file_name)

        remain_img_list = camera_name_list.copy()

        for elem in pcd_map[pcd_key]["image"]:

            src_image_name = input_dir + os.sep + elem
            camera_name = elem.split("/")[1]
            camera_dir = output_dir + os.sep + "camera" + os.sep + camera_name
            dst_image_name = camera_dir + os.sep + pcd_key + ".jpg"
            # if camera_name in remain_img_list:
            remain_img_list.remove(camera_name)

            if os.path.exists(src_image_name):
                shutil.copy(src_image_name, dst_image_name)
            else:
                print(f"missing file : {src_image_name}, {pcd_key} \n")

        for camera_name in remain_img_list:
            src_image_name = project_dir + os.sep + "data" + os.sep + "black_image_1920X1080.jpg"
            camera_dir = output_dir + os.sep + "camera" + os.sep + camera_name
            dst_image_name = camera_dir + os.sep + pcd_key + ".jpg"
            shutil.copy(src_image_name, dst_image_name)
            black_image_count += 1
            print(f"copy black image : {src_image_name}, {pcd_key} \n")


def copy_to_new_dir(input_dir, pcd_map, output_dir, pack_size):
    global total_file_count
    global pack_count
    global invalid_pcd_file_count

    pack_id = 0
    pcd_key_list = []
    pcd_count = len(pcd_map)

    for count, pcd_key in enumerate(pcd_map.keys(), start=1):
        pcd_key_list.append(pcd_key)

        if count % pack_size == 0 or count == pcd_count:
            pack_id += 1

            if len(pcd_key_list) != pack_size:
                print({"pack no": pack_id, "pcd_file_count": len(pcd_key_list)})

            create_pack(input_dir, pcd_key_list, pcd_map, output_dir)

            pcd_key_list = []

    total_file_count = pcd_count
    pack_count = pack_id


def main(input_dir, output_dir, pack_size):
    global calibrated_sensor
    global ego_pose_dict
    global total_pcd_map

    # Convert bin to pcd
    accumulate_map = Accumualate_Map(input_dir)
    lidar_sub_path = input_dir + os.sep + "pcd"
    res = accumulate_map.merge_pcd_and_generate_map(error_dir=pack_output_dir, data_folder=input_dir,
                                                    output_dir=lidar_sub_path,
                                                    write_ascii=True,
                                                    merge_into_one_file=merge_into_one_file,
                                                    pcd_column_count=5)

    if res == {}:
        return

    input_dir = tools.unzip(input_dir)

    calibrated_sensor = tools.get_json_data(input_dir + os.sep + "v1.0" + os.sep + "calibrated_sensor.json")
    ego_pose_dict = tools.get_json_data(input_dir + os.sep + "v1.0" + os.sep + "ego_pose.json")

    pcd_map = tools.get_json_data(input_dir + os.sep + "pcd_map.json")

    # 记录所有分包的pcd_map.json里的内容
    total_pcd_map.update(deepcopy(pcd_map))
    pcd_list = []

    first_key = ""
    for pcd_key in pcd_map.keys():
        first_key = pcd_key
        break

    global is_camara_name_read
    global camera_name_list

    if not is_camara_name_read:
        # 获取相机名称列表
        for camera_name in pcd_map[first_key]["camera"].keys():
            camera_name_list.append(camera_name)

    is_camara_name_read = True

    for f in tools.get_json_files(lidar_sub_path, extension=".pcd"):
        pcd_list.append(lidar_sub_path + os.sep + f.name)

    copy_to_new_dir(input_dir, pcd_map, output_dir, pack_size)

    tools.show_run_time()

    trans_result = {"code": 0, "output": output_dir, "err_msg": "成功",
                    "summary": {"pack_size": pack_size, "pack_count": pack_count, "total_file_count": total_file_count,
                                "valid_pcd_file_count": total_file_count - invalid_pcd_file_count,
                                "copied_pcd_file_count": copied_pcd_file_count}}
    print(trans_result)

    return trans_result


def run(input_dir, output_dir, pack_size=100):
    global project_dir
    global pack_output_dir
    pack_output_dir = output_dir
    current_dir = os.getcwd()
    index = current_dir.find("konvery-tools")
    word_len = len("konvery-tools")
    project_dir = current_dir[:index + word_len]
    print(project_dir)
    # input_dir = tools.unzip(input_dir)
    # dir_list = tools.list_all_dirs(input_dir, "v1.0")
    tools.check_dir(output_dir)

    # index = 0
    # for sub_dir in dir_list:
    #     print("sub_dir: " + sub_dir)
    #     if "lidarseg" in sub_dir:
    #         continue
    #     index += 1
    main(input_dir, output_dir, pack_size)

    json.dump(total_pcd_map, open(output_dir + os.sep + "保留这个文件-total_pcd_map.json", "w"), indent=4)

    print("black image count : ", black_image_count)

    trans_result = {"code": 0, "output": output_dir, "err_msg": "成功",
                    "summary": {"pack_size": pack_size, "pack_count": pack_count, "total_file_count": total_file_count,
                                "valid_pcd_file_count": total_file_count - invalid_pcd_file_count,
                                "copied_pcd_file_count": copied_pcd_file_count}}
    print(trans_result)

    if os.path.exists(output_dir + os.sep + "error.txt"):
        print("!!!!!!!Please check error in error.txt, content is listed blow:")
        with open(output_dir + os.sep + "error.txt") as f:
            for line in f.readlines():
                print("\t" * 2 + line.strip())


"""
    项目：KIN 项目
    对接平台：云测新平台 
    输入: NuScene数据集
    功能：将NuScene格式(PCD, JPG), 转换成云测的格式， 并按数量分包, 且pcd和image保持映射关系
    注意事项：
        分包后的结果一定要保留total_pcd_map.json
        pcd在accumulate_frame_mapping.py会转成世界的坐标系
"""
if __name__ == "__main__":

    args = tools.get_args()
    merge_into_one_file = False

    if args.input == "../../input/test":
        args.input = "/Users/<USER>/Downloads/Convert/Kin/demo"
        args.out = "/Users/<USER>/Downloads/kin-output"

    root = args.input

    if args.txt:
        args.txt = int(args.txt)
    else:
        args.txt = 100

    run(input_dir=args.input, output_dir=args.out, pack_size=args.txt)
