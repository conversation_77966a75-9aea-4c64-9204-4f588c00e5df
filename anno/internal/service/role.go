// source: anno/v1/role.proto
package service

import (
	"context"

	"anno/internal/biz"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/data/field"

	"google.golang.org/protobuf/types/known/emptypb"
)

func FromBizRole(d *biz.Role) *anno.Role {
	if d == nil {
		return nil
	}
	o := &anno.Role{
		Name:  d.Name,
		Langs: d.<PERSON>,
	}
	return o
}

func ToBizRole(d *anno.Role) *biz.Role {
	if d == nil {
		return nil
	}
	o := &biz.Role{
		Name:  d.Name,
		Langs: d.<PERSON>,
	}
	return o
}

type RolesService struct {
	anno.UnimplementedRolesServer
	bz *biz.RolesBiz
}

func NewRolesService(bz *biz.RolesBiz) *RolesService {
	// TODO: implement access control
	bz = nil // fail all calls
	return &RolesService{bz: bz}
}

func (o *RolesService) CreateRole(ctx context.Context, req *anno.Role) (*anno.Role, error) {
	data, err := o.bz.Create(ctx, ToBizRole(req))
	return FromBizRole(data), err
}

func (o *RolesService) UpdateRole(ctx context.Context, req *anno.UpdateRoleRequest) (*anno.Role, error) {
	data, err := o.bz.Update(ctx, ToBizRole(req.Role), field.NewMask(req.Fields...))
	return FromBizRole(data), err
}

func (o *RolesService) DeleteRole(ctx context.Context, req *anno.DeleteRoleRequest) (*emptypb.Empty, error) {
	return &emptypb.Empty{}, o.bz.DeleteByUid(ctx, req.Name)
}

func (o *RolesService) GetRole(ctx context.Context, req *anno.GetRoleRequest) (*anno.Role, error) {
	data, err := o.bz.GetByUid(ctx, req.Name)
	return FromBizRole(data), err
}

func (o *RolesService) ListRole(ctx context.Context, req *anno.ListRoleRequest) (*anno.ListRoleReply, error) {
	pager := biz.Pager{
		Pagesz: int(req.Pagesz),
		Page:   int(req.Page),
	}
	filter := &biz.RoleListFilter{
		// Uids:   req.Uids,
		NamePattern: req.NamePattern,
	}

	var cnt int
	if pager.Page == 0 {
		var err error
		cnt, err = o.bz.Count(ctx, filter)
		if err != nil {
			return nil, err
		}
	}
	datas, err := o.bz.List(ctx, filter, pager)
	if err != nil {
		return nil, err
	}
	return &anno.ListRoleReply{Total: int32(cnt), Roles: kslice.Map(FromBizRole, datas)}, err
}

func (o *RolesService) ListUsersRole(ctx context.Context, req *anno.ListUsersRoleRequest) (*anno.ListUsersRoleReply, error) {
	var err error
	// err = o.bz.ListUsersRole(ctx, req)
	return &anno.ListUsersRoleReply{}, err
}

func (o *RolesService) GetUserRole(ctx context.Context, req *anno.GetUserRoleRequest) (*anno.GetUserRoleReply, error) {
	var err error
	// err = o.bz.GetUserRole(ctx, req)
	return &anno.GetUserRoleReply{}, err
}

func (o *RolesService) SetUsersRole(ctx context.Context, req *anno.SetUsersRoleRequest) (*emptypb.Empty, error) {
	var err error
	// err = o.bz.SetUsersRole(ctx, req)
	return &emptypb.Empty{}, err
}

func (o *RolesService) DeleteUsersRole(ctx context.Context, req *anno.DeleteUsersRoleRequest) (*emptypb.Empty, error) {
	var err error
	// err = o.bz.DeleteUsersRole(ctx, req)
	return &emptypb.Empty{}, err
}
