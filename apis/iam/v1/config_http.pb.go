// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: iam/v1/config.proto

package iam

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	types "gitlab.rp.konvery.work/platform/apis/types"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationConfigsGetConf = "/iam.v1.Configs/GetConf"
const OperationConfigsGetVersion = "/iam.v1.Configs/GetVersion"
const OperationConfigsListErrors = "/iam.v1.Configs/ListErrors"
const OperationConfigsSetConf = "/iam.v1.Configs/SetConf"

type ConfigsHTTPServer interface {
	// GetConf get a configuration item
	GetConf(context.Context, *types.Name) (*types.NameValue, error)
	GetVersion(context.Context, *emptypb.Empty) (*GetVersionReply, error)
	// ListErrors List Errors info
	ListErrors(context.Context, *emptypb.Empty) (*Errors, error)
	// SetConf set/change a configuration item
	SetConf(context.Context, *types.NameValue) (*emptypb.Empty, error)
}

func RegisterConfigsHTTPServer(s *http.Server, srv ConfigsHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/errors", _Configs_ListErrors0_HTTP_Handler(srv))
	r.GET("/v1/version", _Configs_GetVersion0_HTTP_Handler(srv))
	r.GET("/v1/conf/{name}", _Configs_GetConf0_HTTP_Handler(srv))
	r.PUT("/v1/conf/{name}", _Configs_SetConf0_HTTP_Handler(srv))
}

func _Configs_ListErrors0_HTTP_Handler(srv ConfigsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationConfigsListErrors)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListErrors(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Errors)
		return ctx.Result(200, reply)
	}
}

func _Configs_GetVersion0_HTTP_Handler(srv ConfigsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationConfigsGetVersion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetVersion(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetVersionReply)
		return ctx.Result(200, reply)
	}
}

func _Configs_GetConf0_HTTP_Handler(srv ConfigsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in types.Name
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationConfigsGetConf)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetConf(ctx, req.(*types.Name))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*types.NameValue)
		return ctx.Result(200, reply)
	}
}

func _Configs_SetConf0_HTTP_Handler(srv ConfigsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in types.NameValue
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationConfigsSetConf)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetConf(ctx, req.(*types.NameValue))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type ConfigsHTTPClient interface {
	GetConf(ctx context.Context, req *types.Name, opts ...http.CallOption) (rsp *types.NameValue, err error)
	GetVersion(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *GetVersionReply, err error)
	ListErrors(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *Errors, err error)
	SetConf(ctx context.Context, req *types.NameValue, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type ConfigsHTTPClientImpl struct {
	cc *http.Client
}

func NewConfigsHTTPClient(client *http.Client) ConfigsHTTPClient {
	return &ConfigsHTTPClientImpl{client}
}

func (c *ConfigsHTTPClientImpl) GetConf(ctx context.Context, in *types.Name, opts ...http.CallOption) (*types.NameValue, error) {
	var out types.NameValue
	pattern := "/v1/conf/{name}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationConfigsGetConf))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ConfigsHTTPClientImpl) GetVersion(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*GetVersionReply, error) {
	var out GetVersionReply
	pattern := "/v1/version"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationConfigsGetVersion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ConfigsHTTPClientImpl) ListErrors(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*Errors, error) {
	var out Errors
	pattern := "/v1/errors"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationConfigsListErrors))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ConfigsHTTPClientImpl) SetConf(ctx context.Context, in *types.NameValue, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/conf/{name}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationConfigsSetConf))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
