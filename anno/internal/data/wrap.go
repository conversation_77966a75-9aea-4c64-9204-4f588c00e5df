package data

import (
	"context"

	"gitlab.rp.konvery.work/platform/pkg/data"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
)

func Create[T any](ctx context.Context, db *Data, p *T) (*T, error) {
	return data.Create(ctx, db, p)
}

func BatchCreate[T any](ctx context.Context, db *Data, p []*T) ([]*T, error) {
	return data.BatchCreate(ctx, db, p)
}

func GetByID[T any](ctx context.Context, db *Data, id int64, setters ...func(context.Context, *Data, *T) error,
) (*T, error) {
	return data.GetByID(ctx, db, id, setters...)
}

func GetByUid[T any](ctx context.Context, db *Data, uid string, setters ...func(context.Context, *Data, *T) error,
) (*T, error) {
	return data.GetByUid(ctx, db, uid, setters...)
}

func Delete[T any](ctx context.Context, db *Data, p *T) error {
	return data.Delete(ctx, db, p)
}

func DeleteByID[T any](ctx context.Context, db *Data, id int64) error {
	return data.DeleteByID[T](ctx, db, id)
}

func DeleteByIDs[T any](ctx context.Context, db *Data, ids []int64) error {
	return data.DeleteByIDs[T](ctx, db, ids)
}

func Update[T any](ctx context.Context, db *Data, p *T, model *field.Model, fldMask *field.Mask) (*T, error) {
	return Updates(ctx, db, p, nil, model, fldMask)
}

func Updates[T any](ctx context.Context, db *Data, p *T, increments map[string]int, model *field.Model,
	fldMask *field.Mask) (*T, error) {
	return data.Updates(ctx, db, p, increments, model, fldMask)
}

var Convert = data.Convert
