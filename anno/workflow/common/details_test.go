package common_test

import (
	"anno/workflow/common"
	"encoding/json"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

type TypeA struct{ A int }
type TypeB struct{ B int }

func TestDetails(t *testing.T) {
	// test marshal and unmarshal
	testFn := func(name string, v any, d1 *common.Details) {
		assert.Equal(t, v, d1.Value(nil), name)
		b1, err := json.Marshal(d1)
		assert.NoError(t, err, name)
		// assert.EqualValues(t, ``, string(b1))
		// t.Log(name, d1.Type, string(b1))

		d2 := &common.Details{}
		err = json.Unmarshal(b1, d2)
		assert.NoError(t, err, name)
		assert.EqualValues(t, d1.Type, d2.Type, name)
		assert.EqualValues(t, v, d2.Value(nil), name)
	}

	cases := []struct {
		v1, v2 any
	}{
		{&TypeA{1}, &TypeA{2}},
		{&TypeA{1}, &TypeB{2}},
		{TypeA{1}, TypeA{2}},
		{TypeA{1}, TypeB{2}},
	}
	for i, c := range cases {
		d1 := common.NewDetails(c.v1)
		testFn(fmt.Sprintf("case %d-1", i+1), c.v1, d1)
		d1.Set(c.v2)
		testFn(fmt.Sprintf("case %d-2", i+1), c.v2, d1)
	}

	// test hint
	type AA struct{ A int }
	cases2 := []struct{ v, hint any }{
		{nil, nil},
		{AA{1}, AA{}},
		{&AA{1}, &AA{}},
		{&AA{1}, (*AA)(nil)},
		{struct{ A int }{1}, struct{ A int }{}},
		{&struct{ A int }{1}, (*struct{ A int })(nil)},
		{map[string]int{"A": 1}, map[string]int(nil)},
	}
	for i, c := range cases2 {
		d := common.Details{ByteValue: []byte(`{"A": 1}`)}
		assert.Equal(t, c.v, d.Value(c.hint), "hint case %d", i+1)
	}

	ev := &common.Event{Details: &common.Details{ByteValue: []byte(`{"A": 1}`)}}
	assert.Equal(t, &AA{1}, common.GetDetails[AA](ev))
}

func TestTypeMap(t *testing.T) {
	undefinedType := struct{}{}
	// assert.Panics(t, func() { common.RegTypeOf(undefinedType) })
	assert.Equal(t, "", common.GetTypeNameOf(undefinedType))
	assert.Equal(t, "anno/workflow/common_test.TypeA", common.GetTypeNameOf(TypeA{}))
	assert.Equal(t, "*anno/workflow/common_test.TypeA", common.GetTypeNameOf(&TypeA{}))

	name := common.RegTypeOf(TypeA{})
	ptrName := common.RegTypeOf(&TypeA{})

	assert.Equal(t, &TypeA{}, common.NewValuePointerOf(common.GetType(name)).Interface())
	assert.Equal(t, &TypeA{}, common.NewValuePointerOf(common.GetType(ptrName)).Interface())
}
