/*
 * Ory <PERSON> API
 *
 * Documentation for all of Ory <PERSON>'s REST APIs. gRPC is documented separately.
 *
 * API version: 1.0.0
 * Contact: <EMAIL>
 */

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package client

import (
	"encoding/json"
)

// Relationships Paginated Relationship List
type Relationships struct {
	// The opaque token to provide in a subsequent request to get the next page. It is the empty string iff this is the last page.
	NextPageToken  *string        `json:"next_page_token,omitempty"`
	RelationTuples []Relationship `json:"relation_tuples,omitempty"`
}

// NewRelationships instantiates a new Relationships object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewRelationships() *Relationships {
	this := Relationships{}
	return &this
}

// NewRelationshipsWithDefaults instantiates a new Relationships object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewRelationshipsWithDefaults() *Relationships {
	this := Relationships{}
	return &this
}

// GetNextPageToken returns the NextPageToken field value if set, zero value otherwise.
func (o *Relationships) GetNextPageToken() string {
	if o == nil || o.NextPageToken == nil {
		var ret string
		return ret
	}
	return *o.NextPageToken
}

// GetNextPageTokenOk returns a tuple with the NextPageToken field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Relationships) GetNextPageTokenOk() (*string, bool) {
	if o == nil || o.NextPageToken == nil {
		return nil, false
	}
	return o.NextPageToken, true
}

// HasNextPageToken returns a boolean if a field has been set.
func (o *Relationships) HasNextPageToken() bool {
	if o != nil && o.NextPageToken != nil {
		return true
	}

	return false
}

// SetNextPageToken gets a reference to the given string and assigns it to the NextPageToken field.
func (o *Relationships) SetNextPageToken(v string) {
	o.NextPageToken = &v
}

// GetRelationTuples returns the RelationTuples field value if set, zero value otherwise.
func (o *Relationships) GetRelationTuples() []Relationship {
	if o == nil || o.RelationTuples == nil {
		var ret []Relationship
		return ret
	}
	return o.RelationTuples
}

// GetRelationTuplesOk returns a tuple with the RelationTuples field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Relationships) GetRelationTuplesOk() ([]Relationship, bool) {
	if o == nil || o.RelationTuples == nil {
		return nil, false
	}
	return o.RelationTuples, true
}

// HasRelationTuples returns a boolean if a field has been set.
func (o *Relationships) HasRelationTuples() bool {
	if o != nil && o.RelationTuples != nil {
		return true
	}

	return false
}

// SetRelationTuples gets a reference to the given []Relationship and assigns it to the RelationTuples field.
func (o *Relationships) SetRelationTuples(v []Relationship) {
	o.RelationTuples = v
}

func (o Relationships) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.NextPageToken != nil {
		toSerialize["next_page_token"] = o.NextPageToken
	}
	if o.RelationTuples != nil {
		toSerialize["relation_tuples"] = o.RelationTuples
	}
	return json.Marshal(toSerialize)
}

type NullableRelationships struct {
	value *Relationships
	isSet bool
}

func (v NullableRelationships) Get() *Relationships {
	return v.value
}

func (v *NullableRelationships) Set(val *Relationships) {
	v.value = val
	v.isSet = true
}

func (v NullableRelationships) IsSet() bool {
	return v.isSet
}

func (v *NullableRelationships) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableRelationships(val *Relationships) *NullableRelationships {
	return &NullableRelationships{value: val, isSet: true}
}

func (v NullableRelationships) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableRelationships) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
