// source: anno/v1/skill.proto
package service

import (
	"context"

	"anno/internal/biz"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/data/field"

	"google.golang.org/protobuf/types/known/emptypb"
)

func FromBizSkill(d *biz.Skill) *anno.Skill {
	if d == nil {
		return nil
	}
	o := &anno.Skill{
		Type:     anno.Skill_Type_Enum(anno.Skill_Type_Enum_value[d.Type]),
		Name:     d.Name,
		MaxLevel: d.<PERSON>,
		<PERSON>s:    d.<PERSON>,
	}
	return o
}

func ToBizSkill(d *anno.Skill) *biz.Skill {
	if d == nil {
		return nil
	}
	o := &biz.Skill{
		Type:     d.Type.String(),
		Name:     d.Name,
		MaxLevel: d.<PERSON>,
		<PERSON>s:    <PERSON><PERSON>,
	}
	return o
}

type SkillsService struct {
	anno.UnimplementedSkillsServer
	bz *biz.SkillsBiz
}

func NewSkillsService(bz *biz.SkillsBiz) *SkillsService {
	// TODO: implement access control
	bz = nil // fail all calls
	return &SkillsService{bz: bz}
}

func (o *SkillsService) CreateSkill(ctx context.Context, req *anno.Skill) (*anno.Skill, error) {
	data, err := o.bz.Create(ctx, ToBizSkill(req))
	return FromBizSkill(data), err
}

func (o *SkillsService) UpdateSkill(ctx context.Context, req *anno.UpdateSkillRequest) (*anno.Skill, error) {
	data, err := o.bz.Update(ctx, ToBizSkill(req.Skill), field.NewMask(req.Fields...))
	return FromBizSkill(data), err
}

func (o *SkillsService) DeleteSkill(ctx context.Context, req *anno.DeleteSkillRequest) (*emptypb.Empty, error) {
	return &emptypb.Empty{}, o.bz.DeleteByUid(ctx, req.Name)
}

func (o *SkillsService) GetSkill(ctx context.Context, req *anno.GetSkillRequest) (*anno.Skill, error) {
	data, err := o.bz.GetByUid(ctx, req.Name)
	return FromBizSkill(data), err
}

func (o *SkillsService) ListSkill(ctx context.Context, req *anno.ListSkillRequest) (*anno.ListSkillReply, error) {
	pager := biz.Pager{
		Pagesz: int(req.Pagesz),
		Page:   int(req.Page),
	}
	filter := &biz.SkillListFilter{
		// Uids:   req.Uids,
		NamePattern: req.NamePattern,
	}

	var cnt int
	if pager.Page == 0 {
		var err error
		cnt, err = o.bz.Count(ctx, filter)
		if err != nil {
			return nil, err
		}
	}
	datas, err := o.bz.List(ctx, filter, pager)
	if err != nil {
		return nil, err
	}
	return &anno.ListSkillReply{Total: int32(cnt), Skills: kslice.Map(FromBizSkill, datas)}, err
}

func (o *SkillsService) GetUserSkill(ctx context.Context, req *anno.GetUserSkillRequest) (*anno.GetUserSkillReply, error) {
	var err error
	// err = o.bz.GetUserSkill(ctx, req)
	return &anno.GetUserSkillReply{}, err
}

func (o *SkillsService) ListUsersSkill(ctx context.Context, req *anno.ListUsersSkillRequest) (*anno.ListUsersSkillReply, error) {
	var err error
	// err = o.bz.ListUsersSkill(ctx, req)
	return &anno.ListUsersSkillReply{}, err
}

func (o *SkillsService) AddUsersSkill(ctx context.Context, req *anno.AddUsersSkillRequest) (*emptypb.Empty, error) {
	var err error
	// err = o.bz.AddUsersSkill(ctx, req)
	return &emptypb.Empty{}, err
}

func (o *SkillsService) DeleteUsersSkill(ctx context.Context, req *anno.DeleteUsersSkillRequest) (*emptypb.Empty, error) {
	var err error
	// err = o.bz.DeleteUsersSkill(ctx, req)
	return &emptypb.Empty{}, err
}

func (o *SkillsService) AddTeamSkill(ctx context.Context, req *anno.AddTeamSkillRequest) (*emptypb.Empty, error) {
	var err error
	// err = o.bz.AddTeamSkill(ctx, req)
	return &emptypb.Empty{}, err
}

func (o *SkillsService) DeleteTeamSkill(ctx context.Context, req *anno.DeleteTeamSkillRequest) (*emptypb.Empty, error) {
	var err error
	// err = o.bz.DeleteTeamSkill(ctx, req)
	return &emptypb.Empty{}, err
}
