// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: model/v1/model.proto

package model

import (
	context "context"
	types "gitlab.rp.konvery.work/platform/apis/types"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Models_Create_FullMethodName    = "/model.v1.Models/Create"
	Models_Update_FullMethodName    = "/model.v1.Models/Update"
	Models_Delete_FullMethodName    = "/model.v1.Models/Delete"
	Models_Get_FullMethodName       = "/model.v1.Models/Get"
	Models_List_FullMethodName      = "/model.v1.Models/List"
	Models_AddTag_FullMethodName    = "/model.v1.Models/AddTag"
	Models_DeleteTag_FullMethodName = "/model.v1.Models/DeleteTag"
)

// ModelsClient is the client API for Models service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ModelsClient interface {
	Create(ctx context.Context, in *CreateModelRequest, opts ...grpc.CallOption) (*Model, error)
	Update(ctx context.Context, in *UpdateModelRequest, opts ...grpc.CallOption) (*Model, error)
	Delete(ctx context.Context, in *DeleteModelRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	Get(ctx context.Context, in *GetModelRequest, opts ...grpc.CallOption) (*Model, error)
	List(ctx context.Context, in *ListModelRequest, opts ...grpc.CallOption) (*ListModelReply, error)
	AddTag(ctx context.Context, in *types.TagRequest, opts ...grpc.CallOption) (*types.TagList, error)
	DeleteTag(ctx context.Context, in *types.TagRequest, opts ...grpc.CallOption) (*types.TagList, error)
}

type modelsClient struct {
	cc grpc.ClientConnInterface
}

func NewModelsClient(cc grpc.ClientConnInterface) ModelsClient {
	return &modelsClient{cc}
}

func (c *modelsClient) Create(ctx context.Context, in *CreateModelRequest, opts ...grpc.CallOption) (*Model, error) {
	out := new(Model)
	err := c.cc.Invoke(ctx, Models_Create_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelsClient) Update(ctx context.Context, in *UpdateModelRequest, opts ...grpc.CallOption) (*Model, error) {
	out := new(Model)
	err := c.cc.Invoke(ctx, Models_Update_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelsClient) Delete(ctx context.Context, in *DeleteModelRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Models_Delete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelsClient) Get(ctx context.Context, in *GetModelRequest, opts ...grpc.CallOption) (*Model, error) {
	out := new(Model)
	err := c.cc.Invoke(ctx, Models_Get_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelsClient) List(ctx context.Context, in *ListModelRequest, opts ...grpc.CallOption) (*ListModelReply, error) {
	out := new(ListModelReply)
	err := c.cc.Invoke(ctx, Models_List_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelsClient) AddTag(ctx context.Context, in *types.TagRequest, opts ...grpc.CallOption) (*types.TagList, error) {
	out := new(types.TagList)
	err := c.cc.Invoke(ctx, Models_AddTag_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelsClient) DeleteTag(ctx context.Context, in *types.TagRequest, opts ...grpc.CallOption) (*types.TagList, error) {
	out := new(types.TagList)
	err := c.cc.Invoke(ctx, Models_DeleteTag_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ModelsServer is the server API for Models service.
// All implementations must embed UnimplementedModelsServer
// for forward compatibility
type ModelsServer interface {
	Create(context.Context, *CreateModelRequest) (*Model, error)
	Update(context.Context, *UpdateModelRequest) (*Model, error)
	Delete(context.Context, *DeleteModelRequest) (*emptypb.Empty, error)
	Get(context.Context, *GetModelRequest) (*Model, error)
	List(context.Context, *ListModelRequest) (*ListModelReply, error)
	AddTag(context.Context, *types.TagRequest) (*types.TagList, error)
	DeleteTag(context.Context, *types.TagRequest) (*types.TagList, error)
	mustEmbedUnimplementedModelsServer()
}

// UnimplementedModelsServer must be embedded to have forward compatible implementations.
type UnimplementedModelsServer struct {
}

func (UnimplementedModelsServer) Create(context.Context, *CreateModelRequest) (*Model, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedModelsServer) Update(context.Context, *UpdateModelRequest) (*Model, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedModelsServer) Delete(context.Context, *DeleteModelRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (UnimplementedModelsServer) Get(context.Context, *GetModelRequest) (*Model, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (UnimplementedModelsServer) List(context.Context, *ListModelRequest) (*ListModelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedModelsServer) AddTag(context.Context, *types.TagRequest) (*types.TagList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTag not implemented")
}
func (UnimplementedModelsServer) DeleteTag(context.Context, *types.TagRequest) (*types.TagList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTag not implemented")
}
func (UnimplementedModelsServer) mustEmbedUnimplementedModelsServer() {}

// UnsafeModelsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ModelsServer will
// result in compilation errors.
type UnsafeModelsServer interface {
	mustEmbedUnimplementedModelsServer()
}

func RegisterModelsServer(s grpc.ServiceRegistrar, srv ModelsServer) {
	s.RegisterService(&Models_ServiceDesc, srv)
}

func _Models_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelsServer).Create(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Models_Create_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelsServer).Create(ctx, req.(*CreateModelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Models_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelsServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Models_Update_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelsServer).Update(ctx, req.(*UpdateModelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Models_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelsServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Models_Delete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelsServer).Delete(ctx, req.(*DeleteModelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Models_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelsServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Models_Get_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelsServer).Get(ctx, req.(*GetModelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Models_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelsServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Models_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelsServer).List(ctx, req.(*ListModelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Models_AddTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(types.TagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelsServer).AddTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Models_AddTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelsServer).AddTag(ctx, req.(*types.TagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Models_DeleteTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(types.TagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelsServer).DeleteTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Models_DeleteTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelsServer).DeleteTag(ctx, req.(*types.TagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Models_ServiceDesc is the grpc.ServiceDesc for Models service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Models_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "model.v1.Models",
	HandlerType: (*ModelsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Create",
			Handler:    _Models_Create_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _Models_Update_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _Models_Delete_Handler,
		},
		{
			MethodName: "Get",
			Handler:    _Models_Get_Handler,
		},
		{
			MethodName: "List",
			Handler:    _Models_List_Handler,
		},
		{
			MethodName: "AddTag",
			Handler:    _Models_AddTag_Handler,
		},
		{
			MethodName: "DeleteTag",
			Handler:    _Models_DeleteTag_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "model/v1/model.proto",
}
