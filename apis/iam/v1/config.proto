syntax = "proto3";

package iam.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "openapi/v3/annotations.proto";
import "types/lang.proto";
import "types/nv.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/iam/v1;iam";
option java_multiple_files = true;
option java_package = "iam.v1";

service Configs {
  // List Errors info
  rpc ListErrors (google.protobuf.Empty) returns (Errors) {
    option (google.api.http) = {
      get: "/v1/errors"
    };
  }

  rpc GetVersion (google.protobuf.Empty) returns (GetVersionReply) {
    option (google.api.http) = {
      get: "/v1/version"
    };
  }

  // get a configuration item
  rpc GetConf (types.Name) returns (types.NameValue) {
    option (google.api.http) = {
      get: "/v1/conf/{name}"
    };
  }

  // set/change a configuration item
  rpc SetConf (types.NameValue) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/conf/{name}"
      body: "*"
    };
  }
}

message Errors {
  option (openapi.v3.schema) = {
    required: ["errors"]
  };

  // error-reason -> (language -> display-name)
  map<string, types.Multilingual> errors = 1;
}

message GetVersionReply {
  string version = 1;
}
