// source: anno/v1/order.proto
package data

import (
	"context"

	"anno/internal/biz"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

type ordersRepo struct {
	data *Data
	log  *log.Helper
}

func NewOrdersRepo(data *Data, logger log.Logger) biz.OrdersRepo {
	return &ordersRepo{data: data, log: log.<PERSON>Helper(logger)}
}

func (o *ordersRepo) DoTx(ctx context.Context, fn func(ctx context.Context, tx *gorm.DB) error) (err error) {
	return o.data.DoTx(ctx, fn)
}

func (o *ordersRepo) Create(ctx context.Context, p *biz.Order) (*biz.Order, error) {
	return Create(ctx, o.data, p)
}

func (o *ordersRepo) Update(ctx context.Context, p *biz.Order, fldMask *biz.FieldMask) (*biz.Order, error) {
	return Update(ctx, o.data, p, biz.OrderUpdatableFlds, fldMask)
}

func (o *ordersRepo) loadAssociations(ctx context.Context, data *Data, mod *biz.Order) error {
	return nil
}

func (o *ordersRepo) GetByID(ctx context.Context, id int64) (*biz.Order, error) {
	return GetByID[biz.Order](ctx, o.data, id, o.loadAssociations)
}

func (o *ordersRepo) DeleteByID(ctx context.Context, id int64) error {
	return Delete(ctx, o.data, &biz.Order{ID: id})
}

func (o *ordersRepo) buildListQuery(ctx context.Context, p *biz.OrderListFilter) *gorm.DB {
	q := o.data.WithCtx(ctx)
	if len(p.IDs) > 0 {
		q = q.Where(biz.OrderSfldID.WithTable()+" IN ?", p.IDs)
	}
	if p.OrgUid != "" {
		q = q.Where(biz.OrderSfldOrgUid.WithTable()+" = ?", p.OrgUid)
	}
	if p.CreatorUid != "" {
		q = q.Where(biz.OrderSfldCreatorUid.WithTable()+" = ?", p.CreatorUid)
	}
	if p.NamePattern != "" {
		q = q.Where(biz.OrderSfldName.WithTable()+" LIKE ?", "%"+p.NamePattern+"%")
	}
	if len(p.States) > 0 {
		q = q.Where(biz.OrderSfldState.WithTable()+" IN ?", p.States)
	}
	if p.BizgranteeUid != "" {
		q = q.Joins("JOIN bizgrants ON "+biz.OrderSfldOrgUid.WithTable()+" = "+biz.BizgrantSfldOrgUid.WithTable()).
			Where(biz.BizgrantSfldGranteeUid.WithTable()+" = ?", p.BizgranteeUid)
	}
	return q
}

func (o *ordersRepo) List(ctx context.Context, p *biz.OrderListFilter, pager biz.Pager) ([]*biz.Order, error) {
	datas := []*biz.Order{}
	q := o.buildListQuery(ctx, p).Order(biz.OrderSfldID.WithTable() + " DESC")
	if len(p.IDs) == 0 {
		q = q.Offset(pager.Offset()).Limit(pager.Pagesz)
	}
	err := q.Find(&datas).Error
	if err != nil {
		return nil, Convert(&biz.Order{}, err)
	}
	return datas, nil
}

func (o *ordersRepo) Count(ctx context.Context, p *biz.OrderListFilter) (int, error) {
	mod := &biz.Order{}
	var cnt int64
	q := o.buildListQuery(ctx, p)
	err := q.Model(mod).Count(&cnt).Error
	if err != nil {
		return 0, Convert(mod, err)
	}
	return int(cnt), nil
}
