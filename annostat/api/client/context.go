package client

import (
	"annostat/internal/conf"
	"gitlab.rp.konvery.work/platform/apis/client"
)

const (
	HeaderUser             = client.HeaderUser
	HeaderUserUid          = client.HeaderUserUid
	HeaderAuthorization    = client.HeaderAuthorization
	HeaderXmdAuthorization = client.HeaderXmdAuthorization
)

type (
	RPCIdentity = client.RPCIdentity
)

func initSvcAccount(c *conf.Bootstrap) {
	client.InitSvcAccount(c.Rpc.SvcAccount)
}

var (
	NewCtxWithUser        = client.NewCtxWithUser
	NewCtxUseSvcAccount   = client.NewCtxUseSvcAccount
	UserFromCtx           = client.UserFromCtx
	NewCtxWithRPCIdentity = client.NewCtxWithRPCIdentity
)
