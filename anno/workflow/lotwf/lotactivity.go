package lotwf

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"

	"anno/api/client"
	"anno/internal/biz"
	"anno/internal/mq"
	"anno/workflow/common"

	"github.com/go-kratos/kratos/v2/errors"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"gitlab.rp.konvery.work/platform/pkg/wf/wfutil"
	"go.temporal.io/sdk/activity"
)

type Activities struct {
	lotbiz   *biz.LotsBiz
	jobbiz   *biz.JobsBiz
	orderbiz *biz.OrdersBiz
	log      *log.Helper

	jobelemrepo biz.JobelemRepo
}

func NewActivities(lotbiz *biz.LotsBiz, jobbiz *biz.JobsBiz, orderbiz *biz.OrdersBiz, jobelemrepo biz.JobelemRepo, logger log.Logger) *Activities {
	return &Activities{
		lotbiz: lotbiz, jobbiz: jobbiz, orderbiz: orderbiz, jobelemrepo: jobelemrepo,
		log: log.NewHelper(logger),
	}
}

func (o *Activities) LotOnCreated(ctx context.Context, lotID int64) error {
	lot, err := o.lotbiz.GetByID(ctx, lotID, false)
	if err != nil {
		return fmt.Errorf("failed to get lot: %w", err)
	}

	err = mq.PublishEvt(ctx, biz.EvtTypeAnnoLot, biz.EvtSubtypeCreate, lot)
	if err != nil {
		return fmt.Errorf("failed to publish lot event: %w", err)
	}
	return nil
}

func (o *Activities) LotIsAllJobsFinished(ctx context.Context, lotID int64) (bool, error) {
	lot, err := o.lotbiz.GetByID(ctx, lotID, true)
	if err != nil {
		return false, fmt.Errorf("failed to get lot: %w", err)
	}
	cnt, err := o.jobbiz.Count(ctx, &biz.JobListFilter{
		LotID:    lotID,
		Subtypes: []string{""},
		States:   []biz.JobState{biz.JobStateFinished},
	})
	if err != nil {
		return false, fmt.Errorf("failed to count finished jobs: %w", err)
	}
	return cnt == lot.JobCount, nil
}

func (o *Activities) LotIsJobReady(ctx context.Context, lotID int64) (bool, error) {
	lot, err := o.lotbiz.GetByID(ctx, lotID, true)
	if err != nil {
		return false, fmt.Errorf("failed to get lot: %w", err)
	}
	return lot.JobReady, nil
}

func (o *Activities) LotChangeState(ctx context.Context, lotID int64, state biz.LotState) (err error) {
	ctx = client.NewCtxUseSvcAccount(ctx)
	lot := &biz.Lot{
		ID:    lotID,
		State: state,
	}
	flds := []string{biz.LotSfldState.String()}
	if state == biz.LotStateFinished {
		flds = append(flds, biz.LotSfldInsCnt.String(), biz.LotSfldInsTotal.String())
		cnt, total, err := o.jobbiz.CountIns(ctx, &biz.JobListFilter{LotID: lotID, Subtypes: []string{""}})
		if err != nil {
			return fmt.Errorf("failed to query ins count: %w", err)
		}
		lot.InsCnt = int32(cnt)
		lot.InsTotal = int32(total)
	}
	_, err = o.lotbiz.Update(ctx, lot, field.NewMask(flds...))
	if err != nil {
		return fmt.Errorf("failed to update lot: %w", err)
	}
	if lot.OrderID > 0 {
		_, err = o.orderbiz.Update(ctx, &biz.Order{
			ID:       lot.OrderID,
			State:    biz.OrderStateFinished,
			InsTotal: lot.InsTotal,
		}, field.NewMask(biz.OrderSfldState.String(), biz.OrderSfldInsTotal.String()))
	}
	return err
}

func (o *Activities) LotRevertJobs(ctx context.Context, lotID int64, ev *common.Event) (err error) {
	ctx = client.NewCtxUseSvcAccount(ctx)
	startIdx := wfutil.GetActivtityStartIndex(ctx)
	p := common.GetDetails[biz.RevertJobsParam](ev)

	for i := startIdx; i < len(p.LogIDs); i++ {
		l, err := o.jobbiz.Repo.GetJoblog(ctx, p.LogIDs[i])
		if err != nil {
			if errors.IsNotFound(err) {
				// the operation is rolled back
				return nil
			}
			return fmt.Errorf("failed to get job log %v: %w", p.LogIDs[i], err)
		}

		job, err := o.jobbiz.Repo.GetByID(ctx, l.JobID, false)
		if err != nil {
			return fmt.Errorf("failed to get job %v: %w", l.JobID, err)
		}
		if job.State != biz.JobStateChecking || job.Phase != l.ToPhase {
			// job state has changed or been rolled back, do nothing
			activity.RecordHeartbeat(ctx, i) // Report progress.
			continue
		}

		jlogFlds := []string{}
		jobFlds := []string{biz.JobSfldState}
		job.State = biz.JobStateUnstart

		// try to reassign to previous executor
		if p.ToPreviousExecutor {
			executor, team, err := o.jobbiz.Repo.GetPhaseLastExecutor(ctx, l.JobID, int(l.ToPhase))
			if err != nil && !errors.IsNotFound(err) {
				return fmt.Errorf("failed to GetPhaseLastExecutor: %w", err)
			}
			if executor != "" {
				// check if the executor is still working on this lot
				_, err = o.jobbiz.Repo.GetJobExecutorTeam(ctx, l.JobID, executor)
				if err != nil {
					if !errors.IsNotFound(err) {
						return fmt.Errorf("failed to GetJobExecutorTeam: %w", err)
					}
					// the executor is unassigned from this lot, do nothing
					executor, team = "", ""
				}
			}

			if executor != "" {
				job.ExecutorUid = executor
				jobFlds = append(jobFlds, biz.JobSfldExecutorUid)
				l.ToExecteam = team
				l.ToExecutorUid = executor
				jlogFlds = append(jlogFlds, []string{biz.JoblogSfldToExecutorUid, biz.JoblogSfldToExecteam}...)
			}
		}

		job.Ally = &biz.Jobally{}
		counter := &biz.EvtJobsubmit{InsCnt: -1} // let annostat know that KeepAnnos is true
		if !p.KeepAnnos {
			job.Ally.Comments = nil
			job.Ally.Annotations.E = nil
			job.Ally.CommentsURI = ""
			job.Ally.AnnotationsURI = ""
			jobFlds = append(jobFlds, biz.JoballySfldComments.String(), biz.JoballySfldAnnotations.String(),
				biz.JoballySfldCommentsURI.String(), biz.JoballySfldAnnotationsURI.String())
			counter.RrIns = -1 // let annostat know that KeepAnnos is false
			counter.InsCnt = 0
		} else if !p.KeepComments {
			job.Ally.Comments = nil
			job.Ally.CommentsURI = ""
			jobFlds = append(jobFlds, biz.JoballySfldComments.String(), biz.JoballySfldCommentsURI.String())
		}

		err = o.jobbiz.Repo.DoTx(ctx, func(ctx context.Context, tx biz.Tx) error {
			spew.Dump("wf update: ", job, jobFlds)
			_, err = o.jobbiz.Repo.Update(ctx, job, field.NewMask(jobFlds...))
			if err != nil {
				return fmt.Errorf("failed to update job: %w", err)
			}
			if len(jlogFlds) > 0 {
				l, err = o.jobbiz.Repo.UpdateJoblog(ctx, l, field.NewMask(jlogFlds...))
				if err != nil {
					return fmt.Errorf("failed to update joblog: %w", err)
				}
			}

			// publish events
			if err = biz.PublishJoblog(ctx, l); err != nil {
				return fmt.Errorf("failed to publish joblog event: %w", err)
			}
			js := &biz.EvtJobsubmit{
				// ID:          l.ID, // dont pass ID
				JobID:       job.ID,
				LotID:       job.LotID,
				ExecutorUid: l.OperatorUid,
				ExecteamUid: l.OpOrgUid,
				Action:      l.Action,
				Phase:       l.FromPhase,
				InsCnt:      counter.InsCnt,
				RrIns:       counter.RrIns,
				Duration:    0,
				CreatedAt:   l.CreatedAt,
				ClaimedAt:   l.CreatedAt,
			}
			err = mq.PublishEvt(ctx, biz.EvtTypeAnnoJobsubmit, biz.EvtSubtypeCreate, js)
			if err != nil {
				return fmt.Errorf("failed to publish jobsubmit event: %w", err)
			}
			return nil
		})
		if err != nil {
			return fmt.Errorf("failed to do update: %w", err)
		}

		activity.RecordHeartbeat(ctx, i) // Report progress.
	}
	return nil
}

func (o *Activities) LotOnEnded(ctx context.Context, lotID int64, ev *common.Event) (err error) {
	var state biz.LotState
	switch ev.Event {
	case common.EvtLotCompleted:
		state = biz.LotStateFinished
	case common.EvtLotCanceled:
		state = biz.LotStateCanceled
	default:
		return
	}
	fmt.Println("---> lot on ended: ", lotID, state)
	lot, err := o.lotbiz.GetByID(ctx, lotID, false)
	if err != nil {
		return fmt.Errorf("failed to get lot: %w", err)
	}

	err = mq.PublishEvt(ctx, biz.EvtTypeAnnoLot, biz.EvtSubtypeStop, map[string]any{
		"uid": kid.StringID(lotID), "state": state, "ins_cnt": lot.InsCnt, "ins_total": lot.InsTotal})
	if err != nil {
		return fmt.Errorf("failed to publish lot event: %w", err)
	}
	return
}

// LotKickAnnout sends a request to annout service to output the annotations.
func (o *Activities) LotKickAnnout(ctx context.Context, lotID int64) (err error) {
	lot, err := o.lotbiz.GetByID(ctx, lotID, true)
	if err != nil {
		return fmt.Errorf("failed to get lot: %w", err)
	}

	// clear old anno result URL
	if err := o.lotbiz.ClearAnnoResult(ctx, lot); err != nil {
		return err
	}
	if err := o.orderbiz.ClearAnnoResult(ctx, lot.OrderID); err != nil {
		return err
	}

	if out := lot.Ally.Out.E; out == nil || out.Exporter == nil || !lot.CanExportAnnos {
		// skip annotation export
		return
	}

	ctx = client.NewCtxUseSvcAccount(ctx)
	if _, err := client.ExportLotAnnos(ctx, &client.ExportLotAnnosRequest{Uid: lot.GetUid()}); err != nil {
		return fmt.Errorf("failed to ExportLotAnnos: %w", err)
	}
	return
}

// LotSyncState syncs lot state to annostat
func (o *Activities) LotSyncState(ctx context.Context, lotID int64, state biz.LotState) (err error) {
	data := &struct {
		Lot    *biz.Lot
		Fields []string
	}{
		Lot:    &biz.Lot{ID: lotID, State: state},
		Fields: []string{biz.LotSfldState.String()},
	}
	return mq.PublishEvt(ctx, biz.EvtTypeAnnoLot, biz.EvtSubtypeUpdate, data)
}
