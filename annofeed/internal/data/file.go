// source: annofeed/v1/file.proto
package data

import (
	"context"

	"annofeed/internal/biz"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.rp.konvery.work/platform/pkg/data"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
)

type filesRepo struct {
	repo.GenericRepo[biz.File]
	data *Data
	log  *log.Helper
}

func NewFilesRepo(d *Data, logger log.Logger) biz.FilesRepo {
	return &filesRepo{
		GenericRepo: data.NewGenericRepo[biz.File](d, logger, biz.FileTableName),
		data:        d,
		log:         log.NewHelper(logger),
	}
}

func (o *filesRepo) loadAssociations(ctx context.Context, data *Data, mod *biz.File) error {
	return nil
}

func (o *filesRepo) GetByShareID(ctx context.Context, id string) (*biz.File, error) {
	file := &biz.File{}
	err := o.data.WithCtx(ctx).First(file, biz.FileSfldShareID+" = ?", id).Error
	if err != nil {
		return nil, Convert(file, err)
	}
	if err := o.loadAssociations(ctx, o.data, file); err != nil {
		return nil, err
	}
	return file, nil
}
