version: '{build}'

build: false
deploy: false

clone_folder: 'c:\gopath\src\github.com\gofrs\flock'

environment:
  GOPATH: 'c:\gopath'
  GOVERSION: '1.15'

init:
  - git config --global core.autocrlf input

install:
  - rmdir c:\go /s /q
  - appveyor DownloadFile https://storage.googleapis.com/golang/go%GOVERSION%.windows-amd64.msi
  - msiexec /i go%GOVERSION%.windows-amd64.msi /q
  - set Path=c:\go\bin;c:\gopath\bin;%Path%
  - go version
  - go env

test_script:
  - go get -t ./...
  - go test -race -v ./...
