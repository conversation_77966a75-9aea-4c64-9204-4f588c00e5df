package ruleng

import (
	"context"
	"fmt"
	"sync"

	"github.com/antonmedv/expr"
	"github.com/antonmedv/expr/vm"
)

var caches = map[string]*cache{}

type cache struct {
	*sync.RWMutex
	rule string
	prog *vm.Program
}

func (o *cache) Get() cache {
	o.RLock()
	defer o.RUnlock()
	return *o
}

func (o *cache) GetProg(rule string) (*vm.Program, error) {
	c := o.Get()
	if rule == c.rule {
		return c.prog, nil
	}

	o.Lock()
	defer o.Unlock()
	if rule == o.rule {
		return o.prog, nil
	}
	prog, err := expr.Compile(rule, funcs...)
	if err != nil {
		return nil, err
	}
	o.rule = rule
	o.prog = prog
	return prog, nil
}

// Register reserves a rule engine slot.
func Register(name string) {
	caches[name] = &cache{RWMutex: &sync.RWMutex{}}
}

// Eval evaluates the given rule.
func Eval[T any](ctx context.Context, name, rule string, env any) (T, error) {
	var v T
	if rule == "" {
		return v, nil
	}

	// get the program from the cache
	c := caches[name]
	if c == nil {
		return v, fmt.Errorf("rule type %v is not registered", name)
	}
	prog, err := c.GetProg(rule)
	if err != nil {
		return v, fmt.Errorf("failed to compile the rule: %w", err)
	}

	// evaluate the rule against the env
	r, err := expr.Run(prog, env)
	if err != nil {
		return v, fmt.Errorf("failed to evaluate the rule: %w", err)
	}
	v, ok := r.(T)
	if !ok {
		return v, fmt.Errorf("rule returns a result of unexpected type")
	}
	return v, nil
}
