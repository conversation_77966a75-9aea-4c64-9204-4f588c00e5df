syntax = "proto3";

package annout.v1;

// import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
// import "google/protobuf/timestamp.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";
// import "anno/v1/element.proto";
import "anno/v1/type_lotconfig.proto";
import "anno/v1/order.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/annout/v1;annout";
option java_multiple_files = true;
option java_package = "annout.v1";

service Reapers {
  // create a reaper to collect annotations from a lot and export it according to config.
  rpc CreateReaper (CreateReaperRequest) returns (Reaper);

  rpc GetReaper (GetReaperRequest) returns (Reaper);

  rpc ExportLotAnnos (ExportLotAnnosRequest) returns (google.protobuf.Empty);
}

message CreateReaperRequest {
  option (openapi.v3.schema) = {
    required: ["lot_uid"]
  };

  // lot UID
  string lot_uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // order UID
  string order_uid = 2;
  // reaper config
  anno.v1.OutConfig config = 3;
  // lot ontologies
  anno.v1.Lotontologies ontologies = 4;
  // data UID
  string data_uid = 5;
}

message GetReaperRequest {
  option (openapi.v3.schema) = {
    required: ["lot_uid"]
  };

  string lot_uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
}

message Reaper {
  string state = 1;
}

message ExportLotAnnosRequest {
  option (openapi.v3.schema) = {
    required: ["uid"]
  };

  // lot UID
  string uid = 1 [(validate.rules).string = {pattern: "^[a-z0-9]{11}$"}];
  // order UID
  string order_uid = 2;
  // option
  anno.v1.ExportOrderAnnosRequest.Option.Enum option = 3 [(validate.rules).enum = {defined_only: true}];

  repeated int32 phases = 4;
}
