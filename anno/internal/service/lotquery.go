package service

import (
	"context"
	"fmt"

	"anno/api/client"
	"anno/internal/biz"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/apis/iam/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kset"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"

	"github.com/samber/lo"
)

func (o *LotsService) ListLotsByExecutor(ctx context.Context, req *anno.ListLotsByExecutorRequest) (*anno.ListLotsByExecutorReply, error) {
	typ := ""
	if req.Type != anno.Lot_Type_unspecified {
		typ = req.Type.String()
	}
	pager := biz.Pager{
		Pagesz: int(req.<PERSON>),
		Page:   int(req.Page),
	}
	filter := &biz.LotFilterByExecutor{
		NamePattern: req.NamePattern,
		Type:        typ,
		States: lo.Map(req.States, func(v anno.Lot_State_Enum, _ int) biz.LotState {
			return biz.ToBizLotState(v)
		}),
	}

	// set query filter and check permissions
	op := biz.UserFromCtx(ctx)
	switch {
	case req.UserUid != "":
		if req.UserUid != op.GetUid() &&
			!client.IsAllowed(ctx, "", biz.PermStat, biz.PermClsUser, req.UserUid) {
			return nil, errors.NewErrForbidden()
		}
		filter.UserUid = req.UserUid
		filter.Claimable = req.Claimable
	case req.TeamUid != "":
		if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsGroup, req.TeamUid) {
			return nil, errors.NewErrForbidden()
		}
		filter.ExecteamUids = []string{req.TeamUid}
	case req.OrgUid != "":
		fmt.Println("req.OrgUid:", req.OrgUid)
		// get teams in org (requires IamGroup.list permission)
		teams, err := client.ListOrgTeams(ctx, req.OrgUid)
		if err != nil {
			fmt.Println("org err:", err)
			return nil, err
		}
		uids := lo.Map(teams, func(v *client.Team, _ int) string { return v.Uid })
		filter.ExecteamUids = append(uids, req.OrgUid)
	default:
		filter.UserUid = op.GetUid()
		filter.Claimable = req.Claimable
	}

	var cnt int
	if pager.Page == 0 {
		var err error
		cnt, err = o.repo.CountByExecutor(ctx, filter)
		if err != nil {
			return nil, err
		}
	}
	datas, err := o.repo.QueryByExecutor(ctx, filter, pager)
	if err != nil {
		return nil, err
	}

	var rejectedLotSet kset.Set[int64]
	if filter.UserUid != "" && len(datas) > 0 {
		lotIDs := lo.Map(datas, func(v *biz.Lot, _ int) int64 { return v.ID })
		rejectedLotSet, err = o.jobbz.CheckRejectedJobs(ctx, filter.UserUid, lotIDs)
		if err != nil {
			return nil, err
		}
	}

	if !client.IsPrivileged(op.GetRole()) {
		lo.ForEach(datas, func(v *biz.Lot, _ int) { ClearLotPrivacy(v) })
	}

	var extra = make(map[string]*anno.ListLotsByExecutorReply_Extra, len(datas))
	for _, v := range datas {
		extra[v.GetUid()] = &anno.ListLotsByExecutorReply_Extra{
			HasRejectedJobs: rejectedLotSet.Has(v.ID),
		}
	}

	return &anno.ListLotsByExecutorReply{
		Total:  int32(cnt),
		Lots:   kslice.Map(FromBizLot, datas),
		Extras: extra,
	}, nil
}

func (o *LotsService) ListExecteams(ctx context.Context, req *anno.ListExecteamsRequest) (*anno.ListExecteamsReply, error) {
	// allow only platform operators
	op := biz.UserFromCtx(ctx)
	if !(client.IsPrivileged(op.GetRole()) || (client.IsSysRole(op.GetRole()) &&
		client.IsAllowed(ctx, "", biz.PermSetPolicy, biz.PermClsLot, req.Uid))) {
		return nil, errors.NewErrForbidden()
	}

	lot, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	lotPhasesList := biz.LotphaseGroups(lot.Phases)
	lotPhasesList = lo.Filter(lotPhasesList, func(v []*biz.Lotphase, _ int) bool { return len(v) > 0 })

	phases := lo.Map(lotPhasesList, func(v []*biz.Lotphase, _ int) *anno.ListExecteamsReply_Phase {
		return &anno.ListExecteamsReply_Phase{}
	})
	// do not bother if no team is assigned
	if (!req.WithExecteams && !req.WithExecutors) || lo.EveryBy(lot.Phases, func(v *biz.Lotphase) bool {
		return v.Execteam == ""
	}) {
		return &anno.ListExecteamsReply{Phases: phases}, nil
	}

	// query team info
	teamUids := lo.Map(lot.Phases, func(v *biz.Lotphase, _ int) string { return v.Execteam })
	teamUids = lo.Filter(teamUids, func(v string, _ int) bool { return v != "" })
	var teams map[string]*iam.BaseUser
	if req.WithExecteams {
		teams, err = client.ListTeamsInMap(client.NewCtxUseSvcAccount(ctx), teamUids)
		if err != nil {
			return nil, err
		}
	}
	for i, lotPhases := range lotPhasesList { // loop each phase
		for _, lp := range lotPhases { // loop each team in this phase
			phaseTeam := &anno.ListExecteamsReply_Execteam{}
			if req.WithExecteams {
				team := teams[lp.Execteam]
				if team == nil {
					continue
				}
				phaseTeam.Team = team
				phaseTeam.Quota = lp.Quota.E.GetQuota()
			}
			phases[i].Teams = append(phases[i].Teams, phaseTeam)

			if req.WithExecutors { // query executor info
				uids, err := o.bz.ListPhaseExecutorsAll(ctx, lp, lp.Execteam)
				if err != nil {
					return nil, err
				}
				if len(uids) == 0 {
					continue
				}
				users, err := client.ListUsersInMap(client.NewCtxUseSvcAccount(ctx), uids)
				if err != nil {
					return nil, err
				}
				phaseTeam.Executors = lo.Values(users)
			}
		}
	}

	return &anno.ListExecteamsReply{Phases: phases}, nil
}

func (o *LotsService) GetVisibleLots(ctx context.Context, request *anno.GetVisibleLotsRequest) (*anno.GetVisibleLotsReply, error) {
	lotStates := lo.Map(request.States, func(v anno.Lot_State_Enum, _ int) biz.LotState {
		return biz.ToBizLotState(v)
	})

	executorUid := request.ExecutorUid
	if executorUid == "" {
		executorUid = client.UserFromCtx(ctx).GetUid()
	}

	if request.CountOnly {
		lotCount, err := o.bz.GetVisibleLotCount(ctx, request.ExecutorUid, lotStates, request.Claimable)
		if err != nil {
			return nil, fmt.Errorf("failed to get visible lot count: %w", err)
		}

		return &anno.GetVisibleLotsReply{
			LotCount: lo.MapEntries(lotCount, func(k1 biz.LotState, v1 int32) (string, int32) { return k1.String(), v1 }),
		}, nil
	}

	lotIDs, err := o.bz.GetVisibleLotIDs(ctx, executorUid, lotStates, request.Claimable)
	if err != nil {
		return nil, fmt.Errorf("failed to get visible lot uids: %w", err)
	}
	return &anno.GetVisibleLotsReply{
		LotUids: lo.Map(lotIDs, func(item int64, _ int) string { return kid.StringID(item) }),
	}, nil
}
