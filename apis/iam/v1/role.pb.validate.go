// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: iam/v1/role.proto

package iam

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Role with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Role) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Role with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in RoleMultiError, or nil if none found.
func (m *Role) ValidateAll() error {
	return m.validate(true)
}

func (m *Role) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetName()); l < 3 || l > 30 {
		err := RoleValidationError{
			field:  "Name",
			reason: "value length must be between 3 and 30 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_Role_Name_Pattern.MatchString(m.GetName()) {
		err := RoleValidationError{
			field:  "Name",
			reason: "value does not match regex pattern \"^[\\\\w-]+(\\\\.[\\\\w-]+)?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetDisplayName()); l < 3 || l > 30 {
		err := RoleValidationError{
			field:  "DisplayName",
			reason: "value length must be between 3 and 30 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetPerms() {
		_, _ = idx, item

		if l := len(item); l < 3 || l > 60 {
			err := RoleValidationError{
				field:  fmt.Sprintf("Perms[%v]", idx),
				reason: "value length must be between 3 and 60 bytes, inclusive",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_Role_Perms_Pattern.MatchString(item) {
			err := RoleValidationError{
				field:  fmt.Sprintf("Perms[%v]", idx),
				reason: "value does not match regex pattern \"^(IamRole:)?\\\\w+(\\\\.\\\\w+)*$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return RoleMultiError(errors)
	}

	return nil
}

// RoleMultiError is an error wrapping multiple validation errors returned by
// Role.ValidateAll() if the designated constraints aren't met.
type RoleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RoleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RoleMultiError) AllErrors() []error { return m }

// RoleValidationError is the validation error returned by Role.Validate if the
// designated constraints aren't met.
type RoleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RoleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RoleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RoleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RoleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RoleValidationError) ErrorName() string { return "RoleValidationError" }

// Error satisfies the builtin error interface
func (e RoleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRole.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RoleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RoleValidationError{}

var _Role_Name_Pattern = regexp.MustCompile("^[\\w-]+(\\.[\\w-]+)?$")

var _Role_Perms_Pattern = regexp.MustCompile("^(IamRole:)?\\w+(\\.\\w+)*$")

// Validate checks the field values on UpdateRoleRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateRoleRequestMultiError, or nil if none found.
func (m *UpdateRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _UpdateRoleRequest_Action_NotInLookup[m.GetAction()]; ok {
		err := UpdateRoleRequestValidationError{
			field:  "Action",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := EditAction_Enum_name[int32(m.GetAction())]; !ok {
		err := UpdateRoleRequestValidationError{
			field:  "Action",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Name

	for idx, item := range m.GetPerms() {
		_, _ = idx, item

		if l := len(item); l < 3 || l > 60 {
			err := UpdateRoleRequestValidationError{
				field:  fmt.Sprintf("Perms[%v]", idx),
				reason: "value length must be between 3 and 60 bytes, inclusive",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_UpdateRoleRequest_Perms_Pattern.MatchString(item) {
			err := UpdateRoleRequestValidationError{
				field:  fmt.Sprintf("Perms[%v]", idx),
				reason: "value does not match regex pattern \"^(IamRole:)?\\\\w+(\\\\.\\\\w+)*$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return UpdateRoleRequestMultiError(errors)
	}

	return nil
}

// UpdateRoleRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateRoleRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateRoleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateRoleRequestMultiError) AllErrors() []error { return m }

// UpdateRoleRequestValidationError is the validation error returned by
// UpdateRoleRequest.Validate if the designated constraints aren't met.
type UpdateRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateRoleRequestValidationError) ErrorName() string {
	return "UpdateRoleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateRoleRequestValidationError{}

var _UpdateRoleRequest_Action_NotInLookup = map[EditAction_Enum]struct{}{
	0: {},
}

var _UpdateRoleRequest_Perms_Pattern = regexp.MustCompile("^(IamRole:)?\\w+(\\.\\w+)*$")

// Validate checks the field values on DeleteRoleRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteRoleRequestMultiError, or nil if none found.
func (m *DeleteRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return DeleteRoleRequestMultiError(errors)
	}

	return nil
}

// DeleteRoleRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteRoleRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteRoleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteRoleRequestMultiError) AllErrors() []error { return m }

// DeleteRoleRequestValidationError is the validation error returned by
// DeleteRoleRequest.Validate if the designated constraints aren't met.
type DeleteRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteRoleRequestValidationError) ErrorName() string {
	return "DeleteRoleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteRoleRequestValidationError{}

// Validate checks the field values on GetRoleRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRoleRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetRoleRequestMultiError,
// or nil if none found.
func (m *GetRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return GetRoleRequestMultiError(errors)
	}

	return nil
}

// GetRoleRequestMultiError is an error wrapping multiple validation errors
// returned by GetRoleRequest.ValidateAll() if the designated constraints
// aren't met.
type GetRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRoleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRoleRequestMultiError) AllErrors() []error { return m }

// GetRoleRequestValidationError is the validation error returned by
// GetRoleRequest.Validate if the designated constraints aren't met.
type GetRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRoleRequestValidationError) ErrorName() string { return "GetRoleRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRoleRequestValidationError{}

// Validate checks the field values on GetRoleReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetRoleReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRoleReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetRoleReplyMultiError, or
// nil if none found.
func (m *GetRoleReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRoleReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for DisplayName

	if len(errors) > 0 {
		return GetRoleReplyMultiError(errors)
	}

	return nil
}

// GetRoleReplyMultiError is an error wrapping multiple validation errors
// returned by GetRoleReply.ValidateAll() if the designated constraints aren't met.
type GetRoleReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRoleReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRoleReplyMultiError) AllErrors() []error { return m }

// GetRoleReplyValidationError is the validation error returned by
// GetRoleReply.Validate if the designated constraints aren't met.
type GetRoleReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRoleReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRoleReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRoleReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRoleReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRoleReplyValidationError) ErrorName() string { return "GetRoleReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetRoleReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRoleReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRoleReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRoleReplyValidationError{}

// Validate checks the field values on ListRoleRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListRoleRequestMultiError, or nil if none found.
func (m *ListRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListRoleRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	// no validation rules for OrgUid

	// no validation rules for NamePattern

	if len(errors) > 0 {
		return ListRoleRequestMultiError(errors)
	}

	return nil
}

// ListRoleRequestMultiError is an error wrapping multiple validation errors
// returned by ListRoleRequest.ValidateAll() if the designated constraints
// aren't met.
type ListRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListRoleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListRoleRequestMultiError) AllErrors() []error { return m }

// ListRoleRequestValidationError is the validation error returned by
// ListRoleRequest.Validate if the designated constraints aren't met.
type ListRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListRoleRequestValidationError) ErrorName() string { return "ListRoleRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListRoleRequestValidationError{}

// Validate checks the field values on ListRoleReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListRoleReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListRoleReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListRoleReplyMultiError, or
// nil if none found.
func (m *ListRoleReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListRoleReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRoles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListRoleReplyValidationError{
						field:  fmt.Sprintf("Roles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListRoleReplyValidationError{
						field:  fmt.Sprintf("Roles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListRoleReplyValidationError{
					field:  fmt.Sprintf("Roles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if len(errors) > 0 {
		return ListRoleReplyMultiError(errors)
	}

	return nil
}

// ListRoleReplyMultiError is an error wrapping multiple validation errors
// returned by ListRoleReply.ValidateAll() if the designated constraints
// aren't met.
type ListRoleReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListRoleReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListRoleReplyMultiError) AllErrors() []error { return m }

// ListRoleReplyValidationError is the validation error returned by
// ListRoleReply.Validate if the designated constraints aren't met.
type ListRoleReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListRoleReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListRoleReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListRoleReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListRoleReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListRoleReplyValidationError) ErrorName() string { return "ListRoleReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListRoleReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListRoleReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListRoleReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListRoleReplyValidationError{}

// Validate checks the field values on FepermItem with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FepermItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FepermItem with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FepermItemMultiError, or
// nil if none found.
func (m *FepermItem) ValidateAll() error {
	return m.validate(true)
}

func (m *FepermItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TeamType

	if all {
		switch v := interface{}(m.GetPerm()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FepermItemValidationError{
					field:  "Perm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FepermItemValidationError{
					field:  "Perm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerm()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FepermItemValidationError{
				field:  "Perm",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FepermItemMultiError(errors)
	}

	return nil
}

// FepermItemMultiError is an error wrapping multiple validation errors
// returned by FepermItem.ValidateAll() if the designated constraints aren't met.
type FepermItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FepermItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FepermItemMultiError) AllErrors() []error { return m }

// FepermItemValidationError is the validation error returned by
// FepermItem.Validate if the designated constraints aren't met.
type FepermItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FepermItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FepermItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FepermItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FepermItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FepermItemValidationError) ErrorName() string { return "FepermItemValidationError" }

// Error satisfies the builtin error interface
func (e FepermItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFepermItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FepermItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FepermItemValidationError{}

// Validate checks the field values on GetRoleFepermReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRoleFepermReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRoleFepermReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRoleFepermReplyMultiError, or nil if none found.
func (m *GetRoleFepermReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRoleFepermReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPerms() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRoleFepermReplyValidationError{
						field:  fmt.Sprintf("Perms[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRoleFepermReplyValidationError{
						field:  fmt.Sprintf("Perms[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRoleFepermReplyValidationError{
					field:  fmt.Sprintf("Perms[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRoleFepermReplyMultiError(errors)
	}

	return nil
}

// GetRoleFepermReplyMultiError is an error wrapping multiple validation errors
// returned by GetRoleFepermReply.ValidateAll() if the designated constraints
// aren't met.
type GetRoleFepermReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRoleFepermReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRoleFepermReplyMultiError) AllErrors() []error { return m }

// GetRoleFepermReplyValidationError is the validation error returned by
// GetRoleFepermReply.Validate if the designated constraints aren't met.
type GetRoleFepermReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRoleFepermReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRoleFepermReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRoleFepermReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRoleFepermReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRoleFepermReplyValidationError) ErrorName() string {
	return "GetRoleFepermReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetRoleFepermReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRoleFepermReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRoleFepermReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRoleFepermReplyValidationError{}

// Validate checks the field values on SetRoleFepermRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetRoleFepermRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetRoleFepermRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetRoleFepermRequestMultiError, or nil if none found.
func (m *SetRoleFepermRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetRoleFepermRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	for idx, item := range m.GetPerms() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SetRoleFepermRequestValidationError{
						field:  fmt.Sprintf("Perms[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SetRoleFepermRequestValidationError{
						field:  fmt.Sprintf("Perms[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SetRoleFepermRequestValidationError{
					field:  fmt.Sprintf("Perms[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SetRoleFepermRequestMultiError(errors)
	}

	return nil
}

// SetRoleFepermRequestMultiError is an error wrapping multiple validation
// errors returned by SetRoleFepermRequest.ValidateAll() if the designated
// constraints aren't met.
type SetRoleFepermRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetRoleFepermRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetRoleFepermRequestMultiError) AllErrors() []error { return m }

// SetRoleFepermRequestValidationError is the validation error returned by
// SetRoleFepermRequest.Validate if the designated constraints aren't met.
type SetRoleFepermRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetRoleFepermRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetRoleFepermRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetRoleFepermRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetRoleFepermRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetRoleFepermRequestValidationError) ErrorName() string {
	return "SetRoleFepermRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SetRoleFepermRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetRoleFepermRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetRoleFepermRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetRoleFepermRequestValidationError{}
