// source: iam/v1/bizgrant.proto
package data

import (
	"context"
	"strconv"

	"anno/internal/biz"

	"gitlab.rp.konvery.work/platform/pkg/errors"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type bizgrantsRepo struct {
	data *Data
	log  *log.Helper
}

func NewBizgrantsRepo(data *Data, logger log.Logger) biz.BizgrantsRepo {
	return &bizgrantsRepo{data: data, log: log.New<PERSON>elper(logger)}
}

func (o *bizgrantsRepo) DoTx(ctx context.Context, fn func(ctx context.Context, tx biz.Tx) error) (err error) {
	return o.data.DoTx(ctx, fn)
}

func (o *bizgrantsRepo) Create(ctx context.Context, p *biz.Bizgrant) (*biz.Bizgrant, error) {
	return Create(ctx, o.data, p)
}

func (o *bizgrantsRepo) DeleteByID(ctx context.Context, id int64) error {
	return Delete(ctx, o.data, &biz.Bizgrant{ID: id})
}

func (o *bizgrantsRepo) DeleteByFilter(ctx context.Context, f *biz.BizgrantFilter) (ids []int64, err error) {
	var mod *biz.Bizgrant
	err = o.buildListQuery(ctx, f).Model(mod).
		Clauses(clause.Returning{Columns: []clause.Column{{Name: biz.BizgrantSfldID.String()}}}).
		Delete(&ids).Error
	return ids, Convert(mod, err)
}

func (o *bizgrantsRepo) buildListQuery(ctx context.Context, p *biz.BizgrantFilter) *gorm.DB {
	q := o.data.WithCtx(ctx)
	if p.GrantorUid != "" {
		q = q.Where(biz.BizgrantSfldGrantorUid.WithTable()+" = ?", p.GrantorUid)
	}
	if p.GranteeUid != "" {
		q = q.Where(biz.BizgrantSfldGranteeUid.WithTable()+" = ?", p.GranteeUid)
	}
	if p.OrgUid != "" {
		q = q.Where(biz.BizgrantSfldOrgUid.WithTable()+" = ?", p.OrgUid)
	}
	return q
}

func (o *bizgrantsRepo) List(ctx context.Context, p *biz.BizgrantFilter, pager biz.Pager) (
	grants []*biz.Bizgrant, nextPageToken string, err error) {
	q := o.buildListQuery(ctx, p).Order("id").Limit(pager.Pagesz)
	if pager.PageToken != "" {
		id, err := strconv.ParseInt(pager.PageToken.String(), 10, 64)
		if err != nil {
			return nil, "", errors.NewErrInvalidField(errors.WithFields("page_token"))
		}
		q = q.Where("id > ?", id)
	}
	err = q.Find(&grants).Error
	err = Convert(&biz.Bizgrant{}, err)
	if err != nil {
		return
	}
	if len(grants) == pager.Pagesz && pager.Pagesz > 0 {
		nextPageToken = strconv.FormatInt(grants[len(grants)-1].ID, 10)
	}
	return
}

func (o *bizgrantsRepo) Count(ctx context.Context, p *biz.BizgrantFilter) (int, error) {
	mod := &biz.Bizgrant{}
	var cnt int64
	q := o.buildListQuery(ctx, p)
	err := q.Model(mod).Count(&cnt).Error
	if err != nil {
		return 0, Convert(mod, err)
	}
	return int(cnt), nil
}
