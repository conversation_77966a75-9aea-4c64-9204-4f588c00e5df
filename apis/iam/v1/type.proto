syntax = "proto3";

package iam.v1;

import "google/protobuf/timestamp.proto";
import "google/api/field_behavior.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/iam/v1;iam";
option java_multiple_files = true;
option java_package = "iam.v1";

message Team {
  option (openapi.v3.schema) = {
    required: ["uid", "name", "desc", "avatar", "type", "province", "city", "created_at"]
  };

  message Type {
    enum Enum {
      unspecified = 0;
      demander = 1;
      operator = 2;
      supplier = 3;
    }
  }

  string uid = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
  string name = 2;
  string desc = 3;
  string avatar = 4;
  Type.Enum type = 5 [(validate.rules).enum = {defined_only: true}];

  string province = 6;
  string city = 7;

  // string hierarchy = 6;
  // // higher level team; not available if in a team list
  // Team parent = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // team uids in its hierarchy, higher level first
  // repeated string hierarchy = 7;
  // repeated User admins = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // parent team uid
  // string parent_uid = 11 [(google.api.field_behavior) = INPUT_ONLY];
  google.protobuf.Timestamp created_at = 15 [(google.api.field_behavior) = OUTPUT_ONLY];
}

message User {
  option (openapi.v3.schema) = {
    required: ["uid", "name", "avatar", "role", "gender", "province", "city", "imperfect", "created_at"]
  };

  message Gender {
    enum Enum {
      unspecified = 0;
      male = 1;
      female = 2;
    }
  }

  string uid = 1;
  string name = 2;
  // phone number: +8613412345678
  string phone = 3;
  // email: <EMAIL>
  string email = 4;
  string avatar = 5;

  // // which team the user belongs to
  // string team_uid = 6; // [(google.api.field_behavior) = INPUT_ONLY]
  // which team the user belongs to; not available if in a user list
  // Team team = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // user's role: admin/manager/member
  string role = 6;

  // user's gender
  Gender.Enum gender = 7 [(validate.rules).enum = {defined_only: true}];
  // user's birthday: 2010-06-07
  string birthday = 8;
  string province = 9;
  string city = 10;
  // top level team the user belongs to
  string org_uid = 11;
  // type of the user's organization
  Team.Type.Enum org_type = 12 [(validate.rules).enum = {defined_only: true}];
  // // user created resources will be put into ones' work team
  // string work_team_uid = 12;

  // // invitation code
  // string invite_code = 10 [(google.api.field_behavior) = INPUT_ONLY];
  // // authentication code
  // string auth_code = 11 [(google.api.field_behavior) = INPUT_ONLY];

  // string hierarchy = 13;
  // indicates if the user's information should be improved
  bool imperfect = 14 [(google.api.field_behavior) = OUTPUT_ONLY];
  google.protobuf.Timestamp created_at = 15 [(google.api.field_behavior) = OUTPUT_ONLY];
  // tags attached to the user
  repeated string tags = 16;
}

message UserContext {
  // the effective user; it may be an assumed user in an assume context
  User user = 1;
  // the real user in an assume context
  User assume_by = 2;
}

message BaseUser {
  option (openapi.v3.schema) = {
    required: ["uid", "name", "avatar"]
  };

  // user/team uid
  string uid = 1;
  // user/team name
  string name = 2;
  // user/team avatar url
  string avatar = 3;
}

message IDType {
  enum Enum {
    unspecified = 0;
    uid = 1;
    phone = 2;
    email = 3;
  }
}

message Feperm {
  option (openapi.v3.schema) = {
    required: ["visible_pages"]
  };

  // pages visible to the user
  repeated string visible_pages = 1;
}

message EditAction {
  enum Enum {
    unspecified = 0;
    add = 1;
    delete = 2;
  }
}
