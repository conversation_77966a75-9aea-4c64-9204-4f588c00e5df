// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: anyconn/v1/secret.proto

package anyconn

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Secrets_CreateSecret_FullMethodName = "/anyconn.v1.Secrets/CreateSecret"
	Secrets_UpdateSecret_FullMethodName = "/anyconn.v1.Secrets/UpdateSecret"
	Secrets_DeleteSecret_FullMethodName = "/anyconn.v1.Secrets/DeleteSecret"
	Secrets_GetSecret_FullMethodName    = "/anyconn.v1.Secrets/GetSecret"
	Secrets_ListSecret_FullMethodName   = "/anyconn.v1.Secrets/ListSecret"
)

// SecretsClient is the client API for Secrets service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SecretsClient interface {
	CreateSecret(ctx context.Context, in *CreateSecretRequest, opts ...grpc.CallOption) (*Secret, error)
	UpdateSecret(ctx context.Context, in *UpdateSecretRequest, opts ...grpc.CallOption) (*Secret, error)
	DeleteSecret(ctx context.Context, in *DeleteSecretRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetSecret(ctx context.Context, in *GetSecretRequest, opts ...grpc.CallOption) (*Secret, error)
	ListSecret(ctx context.Context, in *ListSecretRequest, opts ...grpc.CallOption) (*ListSecretReply, error)
}

type secretsClient struct {
	cc grpc.ClientConnInterface
}

func NewSecretsClient(cc grpc.ClientConnInterface) SecretsClient {
	return &secretsClient{cc}
}

func (c *secretsClient) CreateSecret(ctx context.Context, in *CreateSecretRequest, opts ...grpc.CallOption) (*Secret, error) {
	out := new(Secret)
	err := c.cc.Invoke(ctx, Secrets_CreateSecret_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *secretsClient) UpdateSecret(ctx context.Context, in *UpdateSecretRequest, opts ...grpc.CallOption) (*Secret, error) {
	out := new(Secret)
	err := c.cc.Invoke(ctx, Secrets_UpdateSecret_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *secretsClient) DeleteSecret(ctx context.Context, in *DeleteSecretRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Secrets_DeleteSecret_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *secretsClient) GetSecret(ctx context.Context, in *GetSecretRequest, opts ...grpc.CallOption) (*Secret, error) {
	out := new(Secret)
	err := c.cc.Invoke(ctx, Secrets_GetSecret_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *secretsClient) ListSecret(ctx context.Context, in *ListSecretRequest, opts ...grpc.CallOption) (*ListSecretReply, error) {
	out := new(ListSecretReply)
	err := c.cc.Invoke(ctx, Secrets_ListSecret_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SecretsServer is the server API for Secrets service.
// All implementations must embed UnimplementedSecretsServer
// for forward compatibility
type SecretsServer interface {
	CreateSecret(context.Context, *CreateSecretRequest) (*Secret, error)
	UpdateSecret(context.Context, *UpdateSecretRequest) (*Secret, error)
	DeleteSecret(context.Context, *DeleteSecretRequest) (*emptypb.Empty, error)
	GetSecret(context.Context, *GetSecretRequest) (*Secret, error)
	ListSecret(context.Context, *ListSecretRequest) (*ListSecretReply, error)
	mustEmbedUnimplementedSecretsServer()
}

// UnimplementedSecretsServer must be embedded to have forward compatible implementations.
type UnimplementedSecretsServer struct {
}

func (UnimplementedSecretsServer) CreateSecret(context.Context, *CreateSecretRequest) (*Secret, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSecret not implemented")
}
func (UnimplementedSecretsServer) UpdateSecret(context.Context, *UpdateSecretRequest) (*Secret, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSecret not implemented")
}
func (UnimplementedSecretsServer) DeleteSecret(context.Context, *DeleteSecretRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSecret not implemented")
}
func (UnimplementedSecretsServer) GetSecret(context.Context, *GetSecretRequest) (*Secret, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSecret not implemented")
}
func (UnimplementedSecretsServer) ListSecret(context.Context, *ListSecretRequest) (*ListSecretReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSecret not implemented")
}
func (UnimplementedSecretsServer) mustEmbedUnimplementedSecretsServer() {}

// UnsafeSecretsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SecretsServer will
// result in compilation errors.
type UnsafeSecretsServer interface {
	mustEmbedUnimplementedSecretsServer()
}

func RegisterSecretsServer(s grpc.ServiceRegistrar, srv SecretsServer) {
	s.RegisterService(&Secrets_ServiceDesc, srv)
}

func _Secrets_CreateSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecretsServer).CreateSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Secrets_CreateSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecretsServer).CreateSecret(ctx, req.(*CreateSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Secrets_UpdateSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecretsServer).UpdateSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Secrets_UpdateSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecretsServer).UpdateSecret(ctx, req.(*UpdateSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Secrets_DeleteSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecretsServer).DeleteSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Secrets_DeleteSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecretsServer).DeleteSecret(ctx, req.(*DeleteSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Secrets_GetSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecretsServer).GetSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Secrets_GetSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecretsServer).GetSecret(ctx, req.(*GetSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Secrets_ListSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecretsServer).ListSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Secrets_ListSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecretsServer).ListSecret(ctx, req.(*ListSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Secrets_ServiceDesc is the grpc.ServiceDesc for Secrets service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Secrets_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "anyconn.v1.Secrets",
	HandlerType: (*SecretsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSecret",
			Handler:    _Secrets_CreateSecret_Handler,
		},
		{
			MethodName: "UpdateSecret",
			Handler:    _Secrets_UpdateSecret_Handler,
		},
		{
			MethodName: "DeleteSecret",
			Handler:    _Secrets_DeleteSecret_Handler,
		},
		{
			MethodName: "GetSecret",
			Handler:    _Secrets_GetSecret_Handler,
		},
		{
			MethodName: "ListSecret",
			Handler:    _Secrets_ListSecret_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "anyconn/v1/secret.proto",
}
