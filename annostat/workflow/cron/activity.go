package cron

import (
	"context"
	"fmt"
	"time"

	"annostat/internal/biz"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/log"

	tpclient "go.temporal.io/sdk/client"
)

type Activities struct {
	jobRepo       biz.JobsRepo
	jobsubmitRepo biz.JobsubmitsRepo
	prodRepo      biz.ProductionsRepo
	accuRepo      biz.AccuraciesRepo
	tpcli         tpclient.Client
	log           *log.Helper
}

func NewActivities(
	jobRepo biz.JobsRepo,
	jobsubmitRepo biz.JobsubmitsRepo,
	prodRepo biz.ProductionsRepo,
	accuRepo biz.AccuraciesRepo,
	logger log.Logger,
) *Activities {
	return &Activities{
		jobRepo:       jobRepo,
		jobsubmitRepo: jobsubmitRepo,
		prodRepo:      prodRepo,
		accuRepo:      accuRepo,
		log:           log.NewHelper(logger),
	}
}

func SetTemporalClient(o *Activities, cli tpclient.Client) {
	o.tpcli = cli
}

func (o *Activities) SumProductions(ctx context.Context, period biz.TimeRange) error {
	// TODO: handle failure/retry cases
	err := o.SumSubmits(ctx, period)
	if err != nil {
		return err
	}

	// platform overall production
	flds := []string{}
	filter := biz.NewAndFilters(
		&biz.UnequalFilter{Field: biz.AccuracySfldExecteamUid},
		&biz.UnequalFilter{Field: biz.AccuracySfldExecutorUid},
		&biz.UnequalFilter{Field: biz.JobsubmitSfldLotID},
		&biz.UnequalFilter{Field: biz.JobsubmitSfldPhase},
		&biz.ProductionListFilter{TimeRange: period, Duration: int32(period.Seconds())},
	)
	err = o.sumProductionOnProduction(ctx, flds, filter)
	if err != nil {
		return err
	}

	// team production
	flds = []string{biz.ProductionSfldExecteamUid}
	err = o.sumProductionOnProduction(ctx, flds, filter)
	if err != nil {
		return err
	}

	// lot phase production
	flds = []string{biz.ProductionSfldLotID, biz.ProductionSfldPhase}
	err = o.sumProductionOnProduction(ctx, flds, filter)
	if err != nil {
		return err
	}

	// lot phase production by team
	flds = []string{biz.ProductionSfldLotID, biz.ProductionSfldPhase, biz.ProductionSfldExecteamUid}
	err = o.sumProductionOnProduction(ctx, flds, filter)
	if err != nil {
		return err
	}
	return nil
}

func (o *Activities) SumSubmits(ctx context.Context, period biz.TimeRange) error {
	groupFlds := []string{biz.JobsubmitSfldLotID, biz.JobsubmitSfldPhase, biz.JobsubmitSfldExecteamUid, biz.JobsubmitSfldExecutorUid}
	aggFlds := []string{
		"SUM(" + biz.JobsubmitSfldInsTotal + ") AS " + biz.ProductionSfldIns,
		"SUM(" + biz.JobsubmitSfldIns2d + ") AS " + biz.ProductionSfldIns2d,
		"SUM(" + biz.JobsubmitSfldIns3d + ") AS " + biz.ProductionSfldIns3d,
		"SUM(" + biz.JobsubmitSfldElems + ") AS " + biz.ProductionSfldElems,
		"MIN(" + biz.JobsubmitSfldClaimedAt + ") AS " + biz.ProductionSfldWorkFrom,
		"MAX(" + biz.JobsubmitSfldCreatedAt + ") AS " + biz.ProductionSfldWorkTo,
		"COUNT(*) AS " + biz.ProductionSfldJobs,
		"1 AS " + biz.ProductionSfldExecutorCnt,
	}
	filter := biz.NewAndFilters(
		&biz.JobsubmitListFilter{TimeRange: period},
		&biz.RelationFilter{
			Field:    biz.JobsubmitSfldAction,
			Relation: biz.FilterRelationNotIn,
			Value:    []string{biz.JobActionForceRecycle, biz.JobActionForceReject},
		},
	)
	pager := biz.Pager{Pagesz: 100, OrderBy: field.Join(groupFlds...)}
	for ; ; pager.Page++ {
		prods := []*biz.Production{}
		_, err := o.jobsubmitRepo.GroupBy(ctx, groupFlds, aggFlds, filter, pager, &prods)
		if err != nil {
			return fmt.Errorf("failed to group jobsubmits: %w", err)
		}
		if len(prods) == 0 {
			break
		}

		dur := period.Duration()
		for _, p := range prods {
			p.PeriodStart = period.From
			p.Duration = int32(dur.Seconds())
			switch dur {
			case time.Hour:
				p.WorkDuration = int32(dur.Seconds())
			case time.Hour * 24:
				p.WorkDuration = int32(p.WorkTo.Sub(p.WorkFrom).Seconds())
				if p.WorkDuration == 0 { // avoid WorkDuration=0 because it will cause DB insertion error
					p.WorkDuration = 1
				}
			}
		}

		_, err = o.prodRepo.BatchCreate(ctx, prods)
		if err != nil {
			return fmt.Errorf("failed to batch create productions: %w", err)
		}
	}
	return nil
}

func (o *Activities) sumProductionOnProduction(ctx context.Context, groupFlds []string, filter biz.ListFilter) error {
	commonFlds := []string{biz.ProductionSfldPeriodStart, biz.ProductionSfldDuration}
	groupFlds = append(groupFlds, commonFlds...)
	aggFlds := []string{
		"SUM(" + biz.ProductionSfldIns + ") AS " + biz.ProductionSfldIns,
		"SUM(" + biz.ProductionSfldIns2d + ") AS " + biz.ProductionSfldIns2d,
		"SUM(" + biz.ProductionSfldIns3d + ") AS " + biz.ProductionSfldIns3d,
		"SUM(" + biz.ProductionSfldElems + ") AS " + biz.ProductionSfldElems,
		"SUM(" + biz.ProductionSfldJobs + ") AS " + biz.ProductionSfldJobs,
		"MIN(" + biz.ProductionSfldWorkFrom + ") AS " + biz.ProductionSfldWorkFrom,
		"MAX(" + biz.ProductionSfldWorkTo + ") AS " + biz.ProductionSfldWorkTo,
		"MAX(" + biz.ProductionSfldWorkDuration + ") AS " + biz.ProductionSfldWorkDuration,
		"COUNT(DISTINCT " + biz.ProductionSfldExecutorUid + ") AS " + biz.ProductionSfldExecutorCnt,
	}
	pager := biz.Pager{Pagesz: 100, OrderBy: field.Join(groupFlds...)}
	for ; ; pager.Page++ {
		prods := []*biz.Production{}
		_, err := o.prodRepo.GroupBy(ctx, groupFlds, aggFlds, filter, pager, &prods)
		if err != nil {
			return fmt.Errorf("failed to group productions: %w", err)
		}
		if len(prods) == 0 {
			break
		}

		_, err = o.prodRepo.BatchCreate(ctx, prods)
		if err != nil {
			return fmt.Errorf("failed to batch create productions: %w", err)
		}
	}

	return nil
}

func (o *Activities) CalAccuracy(ctx context.Context, groupFlds []string, period biz.TimeRange) error {
	// groupFlds := []string{biz.JobsubmitSfldLotID, biz.JobsubmitSfldPhase, biz.JobsubmitSfldLastExecteamUid, biz.JobsubmitSfldLastExecutorUid}
	aggFlds := []string{
		"SUM(" + biz.JobsubmitSfldInsTotal + ") AS " + biz.JobsubmitSfldInsTotal,
		"SUM(" + biz.JobsubmitSfldIns2d + ") AS " + biz.JobsubmitSfldIns2d,
		"SUM(" + biz.JobsubmitSfldIns3d + ") AS " + biz.JobsubmitSfldIns3d,
		"SUM(" + biz.JobsubmitSfldElems + ") AS " + biz.JobsubmitSfldElems,

		"SUM(" + biz.JobsubmitSfldRrIns + ") AS " + biz.JobsubmitSfldRrIns,
		"SUM(" + biz.JobsubmitSfldRrIns2d + ") AS " + biz.JobsubmitSfldRrIns2d,
		"SUM(" + biz.JobsubmitSfldRrIns3d + ") AS " + biz.JobsubmitSfldRrIns3d,
		"SUM(" + biz.JobsubmitSfldRrElems + ") AS " + biz.JobsubmitSfldRrElems,
	}
	pager := biz.Pager{Pagesz: 100, OrderBy: field.Join(groupFlds...)}
	filter := biz.NewAndFilters(
		&biz.JobsubmitListFilter{TimeRange: period},
		&biz.RelationFilter{Field: biz.JobsubmitSfldPhase, Relation: biz.FilterRelationGreaterThan, Value: 1},
	)
	for ; ; pager.Page++ {
		submits := []*biz.Jobsubmit{}
		_, err := o.jobsubmitRepo.GroupBy(ctx, groupFlds, aggFlds, filter, pager, &submits)
		if err != nil {
			return fmt.Errorf("failed to group jobsubmits: %w", err)
		}
		if len(submits) == 0 {
			break
		}

		accuracies := make([]*biz.Accuracy, len(submits))
		dur := period.Seconds()
		for i, p := range submits {
			accuracies[i] = &biz.Accuracy{
				LotID:       p.LotID,
				Phase:       p.Phase - 1,
				ExecutorUid: p.LastExecutorUid,
				ExecteamUid: p.LastExecteamUid,
				RateElems:   (float32(p.Elems) - float32(p.RrElems)) / float32(p.Elems),
				RateIns:     (float32(p.InsTotal) - float32(p.RrIns)) / float32(p.InsTotal),
				RateIns2d:   (float32(p.Ins2d) - float32(p.RrIns2d)) / float32(p.Ins2d),
				RateIns3d:   (float32(p.Ins3d) - float32(p.RrIns3d)) / float32(p.Ins3d),
				PeriodStart: period.From,
				Duration:    int32(dur),
			}
		}

		_, err = o.accuRepo.BatchCreate(ctx, accuracies)
		if err != nil {
			return fmt.Errorf("failed to batch create accuracies: %w", err)
		}
	}
	return nil
}
