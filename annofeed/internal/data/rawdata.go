// source: rawdata/v1/rawdata.proto
package data

import (
	"context"

	"annofeed/internal/biz"

	"gitlab.rp.konvery.work/platform/pkg/data"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type rawdataRepo struct {
	repo.GenericRepo[biz.Rawdata]
	data *Data
	log  *log.Helper
}

func NewRawdataRepo(d *Data, logger log.Logger) biz.RawdataRepo {
	return &rawdataRepo{
		GenericRepo: data.NewGenericRepo[biz.Rawdata](d, logger, biz.Rawdata{}.TableName()),
		data:        d,
		log:         log.New<PERSON>per(logger),
	}
}

func (o *rawdataRepo) DeleteByFilter(ctx context.Context, filter *biz.RawdataDeleteFilter) error {
	if filter == nil {
		return nil
	}
	if filter.ID != 0 {
		return data.DeleteByID[biz.Rawdata](ctx, o.data, filter.ID)
	}
	if len(filter.IDs) > 0 {
		return data.DeleteByIDs[biz.Rawdata](ctx, o.data, filter.IDs)
	}
	if filter.DataID == 0 { // avoid deleting all data because of empty condition
		return nil
	}

	var mod biz.Rawdata
	q := o.data.WithCtx(ctx)
	q = q.Where("data_id = ?", filter.DataID)
	err := q.Delete(&mod).Error
	return Convert(mod, err)
}

func (o *rawdataRepo) ListByFilter(ctx context.Context, p *biz.RawdataListFilter) ([]*biz.Rawdata, error) {
	pager := biz.Pager{Pagesz: p.Count}
	orderBy := "data_id, elem_idx_in_data, idx"
	if p.OrderByIDDesc {
		orderBy = "-id" // id DESC
	}
	pager.OrderBy = orderBy

	datas, _, err := o.GenericRepo.List(ctx, p, pager)
	if err != nil {
		return nil, Convert(&biz.Rawdata{}, err)
	}
	return datas, nil
}

func (o *rawdataRepo) ListElement(ctx context.Context, dataID int64, pager biz.Pager) ([]*biz.Element, error) {
	datas := []*biz.Element{}
	err := o.data.WithCtx(ctx).Model(&biz.Rawdata{}).
		Order("elem_idx_in_data").Limit(pager.Pagesz).Offset(pager.Offset()).
		Distinct("data_id", "folder", "elem_idx_in_data").
		Where("data_id = ?", dataID).Scan(&datas).Error
	if err != nil {
		return nil, Convert(&biz.Rawdata{}, err)
	}
	return datas, nil
}
