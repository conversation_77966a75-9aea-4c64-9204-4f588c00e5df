package client

import (
	"annostat/internal/conf"
	"gitlab.rp.konvery.work/platform/apis/client"
	"gitlab.rp.konvery.work/platform/apis/iam/v1"
)

type (
	User     = iam.User
	BaseUser = iam.BaseUser
	Team     = iam.Team
)

func initIAM(c *conf.Bootstrap) {
	client.InitIAM(&client.Service{Addr: c.Rpc.Iam.Addr})
}

var (
	ListUsersInMap = client.ListUsersInMap
	GetUser        = client.GetUser
	GetMe          = client.GetMe
	GetTeam        = client.GetTeam
	ListTeamsInMap = client.ListTeamsInMap
	IsAllowed      = client.IsAllowed
)
