// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/bizgrant.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateBizgrantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateBizgrantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateBizgrantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateBizgrantRequestMultiError, or nil if none found.
func (m *CreateBizgrantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateBizgrantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_CreateBizgrantRequest_GranteeUid_Pattern.MatchString(m.GetGranteeUid()) {
		err := CreateBizgrantRequestValidationError{
			field:  "GranteeUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateBizgrantRequest_OrgUid_Pattern.MatchString(m.GetOrgUid()) {
		err := CreateBizgrantRequestValidationError{
			field:  "OrgUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateBizgrantRequestMultiError(errors)
	}

	return nil
}

// CreateBizgrantRequestMultiError is an error wrapping multiple validation
// errors returned by CreateBizgrantRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateBizgrantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateBizgrantRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateBizgrantRequestMultiError) AllErrors() []error { return m }

// CreateBizgrantRequestValidationError is the validation error returned by
// CreateBizgrantRequest.Validate if the designated constraints aren't met.
type CreateBizgrantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateBizgrantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateBizgrantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateBizgrantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateBizgrantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateBizgrantRequestValidationError) ErrorName() string {
	return "CreateBizgrantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateBizgrantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateBizgrantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateBizgrantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateBizgrantRequestValidationError{}

var _CreateBizgrantRequest_GranteeUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

var _CreateBizgrantRequest_OrgUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on Bizgrant with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Bizgrant) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Bizgrant with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BizgrantMultiError, or nil
// if none found.
func (m *Bizgrant) ValidateAll() error {
	return m.validate(true)
}

func (m *Bizgrant) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_Bizgrant_GrantorUid_Pattern.MatchString(m.GetGrantorUid()) {
		err := BizgrantValidationError{
			field:  "GrantorUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_Bizgrant_GranteeUid_Pattern.MatchString(m.GetGranteeUid()) {
		err := BizgrantValidationError{
			field:  "GranteeUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_Bizgrant_OrgUid_Pattern.MatchString(m.GetOrgUid()) {
		err := BizgrantValidationError{
			field:  "OrgUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BizgrantValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BizgrantValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BizgrantValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BizgrantMultiError(errors)
	}

	return nil
}

// BizgrantMultiError is an error wrapping multiple validation errors returned
// by Bizgrant.ValidateAll() if the designated constraints aren't met.
type BizgrantMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BizgrantMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BizgrantMultiError) AllErrors() []error { return m }

// BizgrantValidationError is the validation error returned by
// Bizgrant.Validate if the designated constraints aren't met.
type BizgrantValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BizgrantValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BizgrantValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BizgrantValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BizgrantValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BizgrantValidationError) ErrorName() string { return "BizgrantValidationError" }

// Error satisfies the builtin error interface
func (e BizgrantValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBizgrant.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BizgrantValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BizgrantValidationError{}

var _Bizgrant_GrantorUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

var _Bizgrant_GranteeUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

var _Bizgrant_OrgUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on BizgrantFilter with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BizgrantFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BizgrantFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BizgrantFilterMultiError,
// or nil if none found.
func (m *BizgrantFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *BizgrantFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GrantorUid

	// no validation rules for GranteeUid

	// no validation rules for OrgUid

	if len(errors) > 0 {
		return BizgrantFilterMultiError(errors)
	}

	return nil
}

// BizgrantFilterMultiError is an error wrapping multiple validation errors
// returned by BizgrantFilter.ValidateAll() if the designated constraints
// aren't met.
type BizgrantFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BizgrantFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BizgrantFilterMultiError) AllErrors() []error { return m }

// BizgrantFilterValidationError is the validation error returned by
// BizgrantFilter.Validate if the designated constraints aren't met.
type BizgrantFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BizgrantFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BizgrantFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BizgrantFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BizgrantFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BizgrantFilterValidationError) ErrorName() string { return "BizgrantFilterValidationError" }

// Error satisfies the builtin error interface
func (e BizgrantFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBizgrantFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BizgrantFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BizgrantFilterValidationError{}

// Validate checks the field values on DeleteBizgrantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteBizgrantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteBizgrantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteBizgrantRequestMultiError, or nil if none found.
func (m *DeleteBizgrantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteBizgrantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFilter() == nil {
		err := DeleteBizgrantRequestValidationError{
			field:  "Filter",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteBizgrantRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteBizgrantRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteBizgrantRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteBizgrantRequestMultiError(errors)
	}

	return nil
}

// DeleteBizgrantRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteBizgrantRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteBizgrantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteBizgrantRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteBizgrantRequestMultiError) AllErrors() []error { return m }

// DeleteBizgrantRequestValidationError is the validation error returned by
// DeleteBizgrantRequest.Validate if the designated constraints aren't met.
type DeleteBizgrantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteBizgrantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteBizgrantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteBizgrantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteBizgrantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteBizgrantRequestValidationError) ErrorName() string {
	return "DeleteBizgrantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteBizgrantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteBizgrantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteBizgrantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteBizgrantRequestValidationError{}

// Validate checks the field values on ListBizgrantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListBizgrantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListBizgrantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListBizgrantRequestMultiError, or nil if none found.
func (m *ListBizgrantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListBizgrantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListBizgrantRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListBizgrantRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListBizgrantRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListBizgrantRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListBizgrantRequestMultiError(errors)
	}

	return nil
}

// ListBizgrantRequestMultiError is an error wrapping multiple validation
// errors returned by ListBizgrantRequest.ValidateAll() if the designated
// constraints aren't met.
type ListBizgrantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListBizgrantRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListBizgrantRequestMultiError) AllErrors() []error { return m }

// ListBizgrantRequestValidationError is the validation error returned by
// ListBizgrantRequest.Validate if the designated constraints aren't met.
type ListBizgrantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListBizgrantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListBizgrantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListBizgrantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListBizgrantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListBizgrantRequestValidationError) ErrorName() string {
	return "ListBizgrantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListBizgrantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListBizgrantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListBizgrantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListBizgrantRequestValidationError{}

// Validate checks the field values on ListBizgrantReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListBizgrantReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListBizgrantReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListBizgrantReplyMultiError, or nil if none found.
func (m *ListBizgrantReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListBizgrantReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetGrants() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListBizgrantReplyValidationError{
						field:  fmt.Sprintf("Grants[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListBizgrantReplyValidationError{
						field:  fmt.Sprintf("Grants[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListBizgrantReplyValidationError{
					field:  fmt.Sprintf("Grants[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if len(errors) > 0 {
		return ListBizgrantReplyMultiError(errors)
	}

	return nil
}

// ListBizgrantReplyMultiError is an error wrapping multiple validation errors
// returned by ListBizgrantReply.ValidateAll() if the designated constraints
// aren't met.
type ListBizgrantReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListBizgrantReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListBizgrantReplyMultiError) AllErrors() []error { return m }

// ListBizgrantReplyValidationError is the validation error returned by
// ListBizgrantReply.Validate if the designated constraints aren't met.
type ListBizgrantReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListBizgrantReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListBizgrantReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListBizgrantReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListBizgrantReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListBizgrantReplyValidationError) ErrorName() string {
	return "ListBizgrantReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListBizgrantReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListBizgrantReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListBizgrantReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListBizgrantReplyValidationError{}
