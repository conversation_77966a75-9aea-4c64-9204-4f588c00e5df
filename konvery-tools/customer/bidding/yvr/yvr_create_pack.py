import sys
import os
import shutil
import numpy as np
import yaml
from scipy.interpolate import interp1d
from pyproj import Transformer

sys.path.append('.')
from customer.common import tools


def wgs84_to_epsg(lon, lat):
    """
    待转换的经纬度点lon, lat
    epsg编码对照：https://www.cnblogs.com/tangjielin/p/16561258.html
    """
    transformer = Transformer.from_crs("EPSG:4326", "epsg:4528")
    x, y = transformer.transform(lat, lon)
    return x, y


def get_interp_func(x, y):
    return interp1d(x, y)


def construct_cameras(input_dir, frame_info_mapping):
    cameras = {}
    intrinsics_dir = os.path.join(input_dir, 'intrinsics')
    extrinsics_dir = os.path.join(input_dir, 'extrinsics', 'lidar2camera')
    for ori_camera_name in frame_info_mapping:
        camera_name = frame_info_mapping[ori_camera_name]
        intrinsic_file = os.path.join(intrinsics_dir, f'{camera_name}_camera.yaml')
        with open(intrinsic_file) as f:
            intrinsic_conf = yaml.safe_load(f)
        extrinsic_file = os.path.join(extrinsics_dir, f'lidar2{camera_name.replace("_", "")}.yaml')
        with open(extrinsic_file) as f:
            extrinsic_conf = yaml.safe_load(f)
        cameras[ori_camera_name] = {
            'title': camera_name,
            'extrinsic': np.array(extrinsic_conf['transform']).flatten().tolist(),
            'intrinsic': [
                intrinsic_conf['K'][0], 0, intrinsic_conf['K'][2],
                0, intrinsic_conf['K'][1], intrinsic_conf['K'][3],
                0, 0, 1
            ],
            'distortion': [0 if intrinsic_conf['distortion_model'] == 'pinhole' else 2] + intrinsic_conf['D']
        }
    return cameras


def construct_lidars(input_dir):
    lidars = []
    loc_file = os.path.join(input_dir, 'localization.json')
    loc_data = tools.get_json_data(loc_file)
    timestamp_list = []
    x_list = []
    y_list = []
    z_list = []
    qx_list = []
    qy_list = []
    qz_list = []
    qw_list = []
    for frame in loc_data:
        timestamp_list.append(frame['timestamp'])
        x_world, y_world = wgs84_to_epsg(frame['pose']['position']['y'], frame['pose']['position']['x'])
        x_list.append(x_world)
        y_list.append(y_world)
        z_list.append(frame['pose']['position']['z'])
        qx_list.append(frame['pose']['orientation']['qx'])
        qy_list.append(frame['pose']['orientation']['qy'])
        qz_list.append(frame['pose']['orientation']['qz'])
        qw_list.append(frame['pose']['orientation']['qw'])
    x_func = get_interp_func(timestamp_list, x_list)
    y_func = get_interp_func(timestamp_list, y_list)
    z_func = get_interp_func(timestamp_list, z_list)
    qx_func = get_interp_func(timestamp_list, qx_list)
    qy_func = get_interp_func(timestamp_list, qy_list)
    qz_func = get_interp_func(timestamp_list, qz_list)
    qw_func = get_interp_func(timestamp_list, qw_list)
    frame_dirs = sorted(tools.find_dir_by_pre_name(input_dir, 'sample_'))
    for frame_dir in frame_dirs:
        timestamp = int(os.path.basename(frame_dir).split('_')[1]) / 1e6
        lidars.append({
            'pose': [x_func(timestamp).item(), y_func(timestamp).item(), z_func(timestamp).item(),
                     qx_func(timestamp).item(), qy_func(timestamp).item(), qz_func(timestamp).item(),
                     qw_func(timestamp).item()],
        })
    return lidars


def construct_config(input_dir, output_dir, frame_info_mapping):
    cameras = construct_cameras(input_dir, frame_info_mapping)
    lidars = construct_lidars(input_dir)
    config = {
        "meta": {
            "version": "v2"
        },
        "lidars": lidars,
        "cameras": cameras
    }
    tools.write_json_file(config, os.path.join(output_dir, 'params.json'))


def run(input_dir, output_dir):
    out_collection_dir = os.path.join(output_dir, os.path.basename(input_dir))
    os.mkdir(out_collection_dir)
    info_file = os.path.join(input_dir, 'info.json')
    info_data = tools.get_json_data(info_file)
    camera_mapping = {k: v.replace(' ', '_') for k, v in info_data['mapping'].items() if k.startswith('camera')}

    construct_config(input_dir, out_collection_dir, camera_mapping)

    frame_info_mapping = {frame['frame_name']: frame for frame in info_data['frames']}
    frame_dirs = tools.find_dir_by_pre_name(input_dir, 'sample_')
    for frame_dir in sorted(frame_dirs):
        out_frame_dir = os.path.join(out_collection_dir, os.path.basename(frame_dir))
        os.mkdir(out_frame_dir)
        frame_name = os.path.basename(frame_dir)
        frame_info = frame_info_mapping[frame_name]
        for camera_name in camera_mapping.keys():
            src_img = os.path.join(input_dir, frame_name, frame_info[camera_name])
            dst_img = os.path.join(out_frame_dir, camera_name + '.jpg')
            shutil.copyfile(src_img, dst_img)
        src_pcd = os.path.join(input_dir, frame_name, frame_info['combine'])
        dst_pcd = os.path.join(out_frame_dir, 'lidar.pcd')
        shutil.copyfile(src_pcd, dst_pcd)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
