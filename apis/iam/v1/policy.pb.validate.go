// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: iam/v1/policy.proto

package iam

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreatePolicyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreatePolicyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePolicyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreatePolicyRequestMultiError, or nil if none found.
func (m *CreatePolicyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePolicyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Role

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if !_CreatePolicyRequest_Users_Pattern.MatchString(item) {
			err := CreatePolicyRequestValidationError{
				field:  fmt.Sprintf("Users[%v]", idx),
				reason: "value does not match regex pattern \"^(IamUser|IamGroup):[\\\\w-]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if !_CreatePolicyRequest_Resource_Pattern.MatchString(m.GetResource()) {
		err := CreatePolicyRequestValidationError{
			field:  "Resource",
			reason: "value does not match regex pattern \"^([\\\\w-]+:){1,2}([\\\\w-]+(.|/))+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreatePolicyRequestMultiError(errors)
	}

	return nil
}

// CreatePolicyRequestMultiError is an error wrapping multiple validation
// errors returned by CreatePolicyRequest.ValidateAll() if the designated
// constraints aren't met.
type CreatePolicyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePolicyRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePolicyRequestMultiError) AllErrors() []error { return m }

// CreatePolicyRequestValidationError is the validation error returned by
// CreatePolicyRequest.Validate if the designated constraints aren't met.
type CreatePolicyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePolicyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePolicyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePolicyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePolicyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePolicyRequestValidationError) ErrorName() string {
	return "CreatePolicyRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePolicyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePolicyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePolicyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePolicyRequestValidationError{}

var _CreatePolicyRequest_Users_Pattern = regexp.MustCompile("^(IamUser|IamGroup):[\\w-]+$")

var _CreatePolicyRequest_Resource_Pattern = regexp.MustCompile("^([\\w-]+:){1,2}([\\w-]+(.|/))+$")

// Validate checks the field values on Policy with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Policy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Policy with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PolicyMultiError, or nil if none found.
func (m *Policy) ValidateAll() error {
	return m.validate(true)
}

func (m *Policy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Role

	if len(errors) > 0 {
		return PolicyMultiError(errors)
	}

	return nil
}

// PolicyMultiError is an error wrapping multiple validation errors returned by
// Policy.ValidateAll() if the designated constraints aren't met.
type PolicyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PolicyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PolicyMultiError) AllErrors() []error { return m }

// PolicyValidationError is the validation error returned by Policy.Validate if
// the designated constraints aren't met.
type PolicyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PolicyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PolicyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PolicyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PolicyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PolicyValidationError) ErrorName() string { return "PolicyValidationError" }

// Error satisfies the builtin error interface
func (e PolicyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPolicy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PolicyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PolicyValidationError{}

// Validate checks the field values on UpdatePolicyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdatePolicyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePolicyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdatePolicyRequestMultiError, or nil if none found.
func (m *UpdatePolicyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePolicyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if !_UpdatePolicyRequest_Users_Pattern.MatchString(item) {
			err := UpdatePolicyRequestValidationError{
				field:  fmt.Sprintf("Users[%v]", idx),
				reason: "value does not match regex pattern \"^(IamUser|IamGroup):[\\\\w-]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return UpdatePolicyRequestMultiError(errors)
	}

	return nil
}

// UpdatePolicyRequestMultiError is an error wrapping multiple validation
// errors returned by UpdatePolicyRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdatePolicyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePolicyRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePolicyRequestMultiError) AllErrors() []error { return m }

// UpdatePolicyRequestValidationError is the validation error returned by
// UpdatePolicyRequest.Validate if the designated constraints aren't met.
type UpdatePolicyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePolicyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePolicyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePolicyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePolicyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePolicyRequestValidationError) ErrorName() string {
	return "UpdatePolicyRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdatePolicyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePolicyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePolicyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePolicyRequestValidationError{}

var _UpdatePolicyRequest_Users_Pattern = regexp.MustCompile("^(IamUser|IamGroup):[\\w-]+$")

// Validate checks the field values on DeletePolicyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeletePolicyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeletePolicyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeletePolicyRequestMultiError, or nil if none found.
func (m *DeletePolicyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeletePolicyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return DeletePolicyRequestMultiError(errors)
	}

	return nil
}

// DeletePolicyRequestMultiError is an error wrapping multiple validation
// errors returned by DeletePolicyRequest.ValidateAll() if the designated
// constraints aren't met.
type DeletePolicyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeletePolicyRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeletePolicyRequestMultiError) AllErrors() []error { return m }

// DeletePolicyRequestValidationError is the validation error returned by
// DeletePolicyRequest.Validate if the designated constraints aren't met.
type DeletePolicyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeletePolicyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeletePolicyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeletePolicyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeletePolicyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeletePolicyRequestValidationError) ErrorName() string {
	return "DeletePolicyRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeletePolicyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeletePolicyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeletePolicyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeletePolicyRequestValidationError{}

// Validate checks the field values on GetPolicyRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetPolicyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPolicyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPolicyRequestMultiError, or nil if none found.
func (m *GetPolicyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPolicyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return GetPolicyRequestMultiError(errors)
	}

	return nil
}

// GetPolicyRequestMultiError is an error wrapping multiple validation errors
// returned by GetPolicyRequest.ValidateAll() if the designated constraints
// aren't met.
type GetPolicyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPolicyRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPolicyRequestMultiError) AllErrors() []error { return m }

// GetPolicyRequestValidationError is the validation error returned by
// GetPolicyRequest.Validate if the designated constraints aren't met.
type GetPolicyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPolicyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPolicyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPolicyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPolicyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPolicyRequestValidationError) ErrorName() string { return "GetPolicyRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetPolicyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPolicyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPolicyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPolicyRequestValidationError{}

// Validate checks the field values on GetAttachedPoliciesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAttachedPoliciesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAttachedPoliciesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAttachedPoliciesRequestMultiError, or nil if none found.
func (m *GetAttachedPoliciesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAttachedPoliciesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetAttachedPoliciesRequest_Name_Pattern.MatchString(m.GetName()) {
		err := GetAttachedPoliciesRequestValidationError{
			field:  "Name",
			reason: "value does not match regex pattern \"^([\\\\w-]+:){1,2}([\\\\w-]+(.|/))+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Role

	if len(errors) > 0 {
		return GetAttachedPoliciesRequestMultiError(errors)
	}

	return nil
}

// GetAttachedPoliciesRequestMultiError is an error wrapping multiple
// validation errors returned by GetAttachedPoliciesRequest.ValidateAll() if
// the designated constraints aren't met.
type GetAttachedPoliciesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAttachedPoliciesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAttachedPoliciesRequestMultiError) AllErrors() []error { return m }

// GetAttachedPoliciesRequestValidationError is the validation error returned
// by GetAttachedPoliciesRequest.Validate if the designated constraints aren't met.
type GetAttachedPoliciesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAttachedPoliciesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAttachedPoliciesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAttachedPoliciesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAttachedPoliciesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAttachedPoliciesRequestValidationError) ErrorName() string {
	return "GetAttachedPoliciesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAttachedPoliciesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAttachedPoliciesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAttachedPoliciesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAttachedPoliciesRequestValidationError{}

var _GetAttachedPoliciesRequest_Name_Pattern = regexp.MustCompile("^([\\w-]+:){1,2}([\\w-]+(.|/))+$")

// Validate checks the field values on GetAttachedPoliciesReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAttachedPoliciesReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAttachedPoliciesReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAttachedPoliciesReplyMultiError, or nil if none found.
func (m *GetAttachedPoliciesReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAttachedPoliciesReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetAttachedPoliciesReplyMultiError(errors)
	}

	return nil
}

// GetAttachedPoliciesReplyMultiError is an error wrapping multiple validation
// errors returned by GetAttachedPoliciesReply.ValidateAll() if the designated
// constraints aren't met.
type GetAttachedPoliciesReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAttachedPoliciesReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAttachedPoliciesReplyMultiError) AllErrors() []error { return m }

// GetAttachedPoliciesReplyValidationError is the validation error returned by
// GetAttachedPoliciesReply.Validate if the designated constraints aren't met.
type GetAttachedPoliciesReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAttachedPoliciesReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAttachedPoliciesReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAttachedPoliciesReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAttachedPoliciesReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAttachedPoliciesReplyValidationError) ErrorName() string {
	return "GetAttachedPoliciesReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetAttachedPoliciesReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAttachedPoliciesReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAttachedPoliciesReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAttachedPoliciesReplyValidationError{}

// Validate checks the field values on CreateResourceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateResourceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateResourceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateResourceRequestMultiError, or nil if none found.
func (m *CreateResourceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateResourceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return CreateResourceRequestMultiError(errors)
	}

	return nil
}

// CreateResourceRequestMultiError is an error wrapping multiple validation
// errors returned by CreateResourceRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateResourceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateResourceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateResourceRequestMultiError) AllErrors() []error { return m }

// CreateResourceRequestValidationError is the validation error returned by
// CreateResourceRequest.Validate if the designated constraints aren't met.
type CreateResourceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateResourceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateResourceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateResourceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateResourceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateResourceRequestValidationError) ErrorName() string {
	return "CreateResourceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateResourceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateResourceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateResourceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateResourceRequestValidationError{}

// Validate checks the field values on DeleteResourceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteResourceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteResourceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteResourceRequestMultiError, or nil if none found.
func (m *DeleteResourceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteResourceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_DeleteResourceRequest_Name_Pattern.MatchString(m.GetName()) {
		err := DeleteResourceRequestValidationError{
			field:  "Name",
			reason: "value does not match regex pattern \"^([\\\\w-]+:){1,2}([\\\\w-]+(.|/))+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteResourceRequestMultiError(errors)
	}

	return nil
}

// DeleteResourceRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteResourceRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteResourceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteResourceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteResourceRequestMultiError) AllErrors() []error { return m }

// DeleteResourceRequestValidationError is the validation error returned by
// DeleteResourceRequest.Validate if the designated constraints aren't met.
type DeleteResourceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteResourceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteResourceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteResourceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteResourceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteResourceRequestValidationError) ErrorName() string {
	return "DeleteResourceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteResourceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteResourceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteResourceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteResourceRequestValidationError{}

var _DeleteResourceRequest_Name_Pattern = regexp.MustCompile("^([\\w-]+:){1,2}([\\w-]+(.|/))+$")

// Validate checks the field values on RevokeUsersRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RevokeUsersRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RevokeUsersRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RevokeUsersRoleRequestMultiError, or nil if none found.
func (m *RevokeUsersRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RevokeUsersRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_RevokeUsersRoleRequest_Resource_Pattern.MatchString(m.GetResource()) {
		err := RevokeUsersRoleRequestValidationError{
			field:  "Resource",
			reason: "value does not match regex pattern \"^([\\\\w-]+:){1,2}([\\\\w-]+(.|/))+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Role

	if len(errors) > 0 {
		return RevokeUsersRoleRequestMultiError(errors)
	}

	return nil
}

// RevokeUsersRoleRequestMultiError is an error wrapping multiple validation
// errors returned by RevokeUsersRoleRequest.ValidateAll() if the designated
// constraints aren't met.
type RevokeUsersRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RevokeUsersRoleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RevokeUsersRoleRequestMultiError) AllErrors() []error { return m }

// RevokeUsersRoleRequestValidationError is the validation error returned by
// RevokeUsersRoleRequest.Validate if the designated constraints aren't met.
type RevokeUsersRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RevokeUsersRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RevokeUsersRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RevokeUsersRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RevokeUsersRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RevokeUsersRoleRequestValidationError) ErrorName() string {
	return "RevokeUsersRoleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RevokeUsersRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRevokeUsersRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RevokeUsersRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RevokeUsersRoleRequestValidationError{}

var _RevokeUsersRoleRequest_Resource_Pattern = regexp.MustCompile("^([\\w-]+:){1,2}([\\w-]+(.|/))+$")
