import sys
import os

import jsonschema
import numpy as np

import numpy as np
from scipy.spatial.transform import Rotation as R

sys.path.append(".")
from customer.common import tools

# 标志信息
sign_types = ['traffic light', 'hanging sign', 'road mark']
# 静态物体信息
object_types = ['barrier', 'building', 'vegetation', 'pole', 'obstacle']
# 车道信息
lane_types = ['driving', 'restricted', 'border', 'shoulder', 'entry', 'exit']
# 车道线信息
bound_types = ['curb', 'broken', 'solid', 'broken broken', 'solid solid', 'broken solid',
               'solid broken', 'grass', 'other', 'none']

object_subtypes_map = {
    "hedge": "barrier",
    "guard rail": "barrier",
    "jersey barrier": "barrier",
    "wall": "barrier",
    "noise protections": "barrier",
    "bus stop": "building",
    "toll booth": "building",
    "cone": "obstacle",
    "water barrier": "obstacle",
    "enclosure": "obstacle",
}

traffic_light_state = ["green", "red", "yellow", "none", "other"]


camera_id_mapping = {
    "front_camera": 0,
    "left_camera": 1,
    "back_camera": 2,
    "right_camera": 3
}


def listdir(input_dir):
    """wraps for os.listdir but dose not contains mac os system file"""
    mac_file = ['.DS_Store', '__MACOSX']
    return sorted([os.path.join(input_dir, f) for f in os.listdir(input_dir) if f not in mac_file])


def get_label_attrs_one(name, attrs, default=''):
    candidate = list(filter(lambda x: x.get('name') == name, attrs))
    if len(candidate) == 0:
        return default
    else:
        if len(candidate[0]['values']) == 0:
            return default
        return candidate[0]['values'][0]


def get_label_attrs(name, attrs, default=None):
    candidate = list(filter(lambda x: x.get('name') == name, attrs))
    if len(candidate) == 0:
        return default
    else:
        if len(candidate[0]['values']) == 0:
            return []
        return candidate[0]['values']


def format_int_list(str_list):
    int_list = []
    if str_list is None:
        return int_list
    for item in str_list:
        if item is None or item == '':
            continue
        if "," in item:
            int_list.extend([int(x) for x in item.split(",")])
        elif int(item) != 0:
            int_list.append(int(item))
    return int_list


def compute_bottom_corners(data):
    # 解析数据
    x, y, z = data[0:3]  # 中心点坐标
    sx, sy, sz = data[3:6]  # 轴长
    qx, qy, qz, qw = data[6:10]  # 旋转四元数

    # 矩形框底部的局部坐标
    local_corners = np.array([
        [-sx / 2, -sy / 2, -sz / 2],
        [-sx / 2, sy / 2, -sz / 2],
        [sx / 2, -sy / 2, -sz / 2],
        [sx / 2, sy / 2, -sz / 2]
    ])

    # 创建旋转对象
    rotation = R.from_quat([qx, qy, qz, qw])

    # 旋转局部坐标到全局坐标
    global_corners = rotation.apply(local_corners)

    # 平移到中心点
    global_corners += np.array([x, y, z])

    return global_corners


def compute_forward_face_corners(data, forward):
    # 解析数据
    x, y, z = data[0:3]  # 中心点坐标
    sx, sy, sz = data[3:6]  # 轴长
    qx, qy, qz, qw = data[6:10]  # 旋转四元数

    # 解析 forward 方向
    origin = np.array(forward["origin"])
    toward = np.array(forward["toward"])

    # 计算方向向量 (单位向量)
    direction = toward - origin
    direction /= np.linalg.norm(direction)  # 归一化

    # 计算立方体 8 个角点（局部坐标）
    local_corners = np.array([
        [-sx / 2, -sy / 2, -sz / 2],  # 角点 1
        [-sx / 2, sy / 2, -sz / 2],  # 角点 2
        [sx / 2, -sy / 2, -sz / 2],  # 角点 3
        [sx / 2, sy / 2, -sz / 2],  # 角点 4
        [-sx / 2, -sy / 2, sz / 2],  # 角点 5 (上层)
        [-sx / 2, sy / 2, sz / 2],  # 角点 6
        [sx / 2, -sy / 2, sz / 2],  # 角点 7
        [sx / 2, sy / 2, sz / 2],  # 角点 8
    ])

    # 旋转局部坐标到全局坐标
    rotation = R.from_quat([qx, qy, qz, qw])
    global_corners = rotation.apply(local_corners)

    # 平移到中心点
    global_corners += np.array([x, y, z])

    # 计算 forward 面（即四个点到目标方向最近）
    distances = np.dot(global_corners - origin, direction)  # 计算每个点在方向上的投影
    sorted_indices = np.argsort(distances)  # 找到投影最大的 4 个点（最接近 forward 方向）
    forward_face_corners = global_corners[sorted_indices[-4:]]  # 取最大 4 个点

    return forward_face_corners


def compute_box_corners(data):
    # 解析数据
    x, y, z = data[0:3]  # 中心点坐标
    sx, sy, sz = data[3:6]  # 轴长
    qx, qy, qz, qw = data[6:10]  # 旋转四元数

    # 局部坐标系下的 8 个角点
    local_corners = np.array([
        [-sx / 2, -sy / 2, -sz / 2],  # 角点 1
        [-sx / 2, sy / 2, -sz / 2],  # 角点 2
        [sx / 2, -sy / 2, -sz / 2],  # 角点 3
        [sx / 2, sy / 2, -sz / 2],  # 角点 4
        [-sx / 2, -sy / 2, sz / 2],  # 角点 5 (上层)
        [-sx / 2, sy / 2, sz / 2],  # 角点 6
        [sx / 2, -sy / 2, sz / 2],  # 角点 7
        [sx / 2, sy / 2, sz / 2],  # 角点 8
    ])

    # 旋转局部坐标到全局坐标
    rotation = R.from_quat([qx, qy, qz, qw])
    global_corners = rotation.apply(local_corners)

    # 平移到中心点
    global_corners += np.array([x, y, z])

    return global_corners


def find_folders(root_dir, target_folder):
    found_folders = []

    for dirpath, dirnames, filenames in os.walk(root_dir):
        if target_folder in dirnames:
            found_folders.append(os.path.join(dirpath, target_folder))
    return found_folders


def process_clip(clip_dir, output_dir, src_clip_path):
    # print(src_clip_path)
    clip_id = os.path.basename(os.path.dirname(clip_dir))
    frame_id = os.path.basename(clip_dir)
    output_file_name = output_dir + os.sep + clip_id + os.sep + frame_id + "_static_annoation.json"
    # print(clip_id, output_file_name)
    first_frame_json = os.path.join(clip_dir, 'annos.json')
    # print(first_frame_json)
    # print('------------------')
    # return
    timestamp = os.path.basename(src_clip_path)
    # print(timestamp)
    camera_params_path = os.path.join(src_clip_path, 'params.json')
    camera_params = tools.get_json_data(camera_params_path)
    data = tools.get_json_data(first_frame_json)
    svo_static_anno_data = convert(data, camera_params, timestamp)

    tools.write_json_file(svo_static_anno_data, output_file_name, indent=4, ensure_ascii=False)


def convert(data, camera_params, timestamp):
    if data is None or data["element_annos"] is None:
        return
    rawdata_annos = data["element_annos"][0]["rawdata_annos"]
    print(rawdata_annos[0]['name'])
    lanes = []
    bounds = []
    objects = []
    signs = []
    obj_id_index = 1
    lane_id_index = 1
    bounds_id_index = 1
    sign_id_index = 1
    for item in rawdata_annos:
        if len(item["objects"]) == 0:
            continue

        groups = {}
        for obj in item['objects']:
            if obj['label']['name'] == 'group':
                group_id = obj['uuid']
                for pid in obj['compound']['parts']:
                    groups[pid] = group_id

        for obj in item["objects"]:
            label_name = obj["label"]["name"].replace("_", " ")
            attrs = obj['label']['attrs']
            coordinates = []
            widget_name = ''
            if obj["label"]["widget"] is not None:
                coordinates = obj["label"]["widget"]['data']
                widget_name = obj["label"]["widget"]["name"]
            if label_name in object_types or label_name in object_subtypes_map.keys():
                obj_type = ''
                obj_subtype = ''
                if label_name in object_types:
                    obj_type = label_name
                    # print('obj - 1: ', label_name)
                if label_name in object_subtypes_map.keys():
                    obj_type = object_subtypes_map[label_name]
                    obj_subtype = label_name
                    # print('obj - 2: ', label_name)
                height = get_label_attrs_one('height', attrs)
                if height is None or height == "":
                    height = 0
                height = height.rstrip('.')
                outline = []
                if widget_name == "cuboid":
                    if len(coordinates) == 0 or len(coordinates) != 10:
                        outline = []
                    else:
                        bottom_pts = compute_bottom_corners(coordinates)
                        outline = [{"x": float(px), "y": float(py), "z": float(pz), "height": float(height)} for px, py, pz in bottom_pts]
                elif widget_name == "poly3d":
                    outline = [
                        {"x": coordinates[i], "y": coordinates[i + 1], "z": coordinates[i + 2], 'height': float(height)}
                        for i in range(0, len(coordinates), 3)]
                object_item = {
                    "id": obj_id_index,
                    "type": obj_type,
                    "sub_type": obj_subtype,
                    "outline": outline,
                }
                lane_id = get_label_attrs_one('id', attrs)
                if lane_id is None or lane_id == "":
                    lane_id = 0
                else:
                    lane_id = int(lane_id)
                if lane_id != 0:
                    object_item["lane_id"] = int(lane_id)

                objects.append(object_item)
                obj_id_index += 1

            if label_name in sign_types:
                print('sign: ', label_name)
                content = get_label_attrs_one('content', attrs)
                lane_ids = get_label_attrs('id6', attrs)
                # if lane_ids is None:
                #     lane_ids = get_label_attrs('id5', attrs)

                if lane_ids is None:
                    lane_ids = []
                else:
                    lane_ids = format_int_list(lane_ids)

                outline = []
                if widget_name == "cuboid":
                    forward = obj['label']['widget']['forward']
                    if len(coordinates) == 0 or len(coordinates) != 10:
                        outline = []
                    else:
                        box_pts = compute_forward_face_corners(coordinates, forward)
                        outline = [{"x": float(px), "y": float(py), "z": float(pz)} for px, py, pz in box_pts]

                sign_item = {
                    "id": sign_id_index,
                    "type": label_name,
                    "sub_type": "",
                    "outline": outline,
                    "content": content
                }
                # 关联group
                uuid = obj['uuid']
                group_id = groups.get(uuid)
                if group_id is not None:
                    sign_item["group_id"] = group_id
                # 交通灯
                states = []
                for state in traffic_light_state:
                    state_val = get_label_attrs(state, attrs)
                    state_val = format_int_list(state_val)

                    state_lanes = get_label_attrs(state+'_lanes', attrs)
                    if state_lanes is None or len(state_lanes) == 0:
                        state_lanes = []
                    else:
                        state_lanes = format_int_list(state_lanes)
                    if len(state_lanes) > 0:
                        lane_ids.extend(state_lanes)
                        lane_ids = list(set(lane_ids))
                    if state_val is not None and len(state_val) > 0:
                        state_obj = {
                            "name": state,
                            "start_time": state_val,
                            "lanes": state_lanes
                        }
                        states.append(state_obj)
                if len(states) > 0:
                    sign_item["states"] = states
                sign_item['lane_ids'] = lane_ids
                # camera
                cam_2d_position = []
                box_pts = compute_box_corners(coordinates)
                points = [[x, y, z] for x, y, z in box_pts]
                for camera_name in camera_id_mapping.keys():
                    camera_id = camera_id_mapping[camera_name]
                    camera_name_params = camera_params['cameras'][camera_name]
                    camera_extrinsic = camera_name_params['extrinsic']
                    if len(camera_extrinsic) == 16:
                        camera_extrinsic = np.array(camera_extrinsic).reshape((4, 4))
                    camera_intrinsic = camera_name_params['intrinsic']
                    if len(camera_intrinsic) == 9:
                        camera_intrinsic = np.array(camera_intrinsic).reshape((3, 3))
                    # camera_distortion = camera_name_params['distortion']
                    # camera_distortion = np.array(camera_distortion[1:])

                    x_vals = []
                    y_vals = []
                    for pw in points:
                        if isinstance(pw, (list, tuple)):
                            pw = np.array(pw)
                        if pw.shape == (3,):
                            pw = np.append(pw, values=1)
                        pc = camera_extrinsic @ pw
                        pi = camera_intrinsic @ pc[:-1]
                        if pi[2] > 0:
                            pi = pi / pi[2]
                            x_vals.append(pi[0])
                            y_vals.append(pi[1])

                    # print(x_vals, y_vals)
                    if len(x_vals) == 0 or len(y_vals) == 0:
                        continue
                    cam_2d_position.append({
                        "camera_id": camera_id,
                        "timestamp": float(timestamp),
                        "max_x": max(x_vals),
                        "max_y": max(y_vals),
                        "min_x": min(x_vals),
                        "min_y": min(y_vals),
                    })
                if len(cam_2d_position) > 0:
                    sign_item['cam_2d_position'] = cam_2d_position
                signs.append(sign_item)
                sign_id_index += 1

            if label_name in bound_types:
                pts = [{"x": coordinates[i], "y": coordinates[i + 1], "z": coordinates[i + 2]}
                       for i in range(0, len(coordinates), 3)]
                color = get_label_attrs_one('color', attrs)

                deceleration = get_label_attrs_one('deceleration', attrs)
                if deceleration is None:
                    deceleration = get_label_attrs_one('slow_down_lane', attrs)
                if deceleration is not None:
                    deceleration = bool(deceleration)
                else:
                    deceleration = False
                lane_mark = []
                if len(pts) > 0:
                    lane_mark_start = {
                        "x_start": pts[0]["x"],
                        "y_start": pts[0]["y"],
                        "z_start": pts[1]["z"],
                        "type": label_name,
                        "color": color,
                        "deceleration": deceleration
                    }
                    lane_mark.append(lane_mark_start)
                bound_item = {
                    "id": bounds_id_index,
                    "pts": pts,
                    "lane_mark": lane_mark
                }
                bounds.append(bound_item)
                bounds_id_index += 1
                # print('bound: ', label_name)

            if label_name in lane_types:
                left_bound_id = get_label_attrs_one('left_bound_id', attrs)
                if left_bound_id is None or left_bound_id == "":
                    left_bound_id = 0

                right_bound_id = get_label_attrs_one('right_bound_id', attrs)
                if right_bound_id is None or right_bound_id == "":
                    right_bound_id = 0
                if "," in right_bound_id:
                    right_bound_ids = []
                    right_bound_ids.extend([int(x) for x in right_bound_id.split(",")])
                    right_bound_id = right_bound_ids[0]

                left_lane_id = get_label_attrs_one('id1', attrs)
                if left_lane_id is None or left_lane_id == "":
                    left_lane_id = 0

                right_lane_id = get_label_attrs_one('id2', attrs)
                if right_lane_id is None or right_lane_id == "":
                    right_lane_id = 0

                front_lane_ids = get_label_attrs('id3', attrs)
                front_lane_ids = format_int_list(front_lane_ids)

                back_lane_ids = get_label_attrs('id4', attrs)
                back_lane_ids = format_int_list(back_lane_ids)
                lane_item = {
                    "id": lane_id_index,
                    "type": label_name,
                    "left_bound_id": int(left_bound_id),
                    "right_bound_id": int(right_bound_id),
                    "left_lane_id": int(left_lane_id),
                    "right_lane_id": int(right_lane_id),
                    "front_lane_ids": front_lane_ids,
                    "back_lane_ids": back_lane_ids
                }
                lanes.append(lane_item)
                lane_id_index += 1
                # print('lane: ', label_name)
    res = {
        "bounds": bounds,
        "lanes": lanes,
        "objects": objects,
        "sign": signs
    }
    return res


def run(input_dir, output_dir, src_dir):
    annos_dirs = find_folders(input_dir, 'annos')
    if len(annos_dirs) == 0:
        print("no annos found in dir: ", input_dir)
        return
    for annos_path in annos_dirs:
        seg_dirs = listdir(annos_path)
        for seg_dir in seg_dirs:
            src_seg_dir = os.path.join(src_dir, os.path.basename(seg_dir))
            for clip in listdir(seg_dir):
                src_clip_path = os.path.join(src_seg_dir, os.path.basename(clip))
                element = listdir(clip)[0]
                if os.path.basename(element) == 'annos.json':
                    process_clip(os.path.dirname(element), output_dir, src_clip_path)
                    break


if __name__ == "__main__":
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out, args.txt)
