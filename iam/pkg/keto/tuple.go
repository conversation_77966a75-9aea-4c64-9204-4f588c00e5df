package keto

import (
	"errors"
	"fmt"
	"strings"

	keto "github.com/ory/keto/proto/ory/keto/relation_tuples/v1alpha2"
)

const (
	RelPolicies = "policies"
	RelParents  = "parents"

	GlobalScope = "/"
	SysPrefix   = "sys/"
)

var (
	ErrForbidden = errors.New("cannot delete system object")
)

func IsSysObj(obj string) bool { return strings.HasPrefix(obj, SysPrefix) }

func NewSubjectId(id string) *Subject {
	return &Subject{Ref: &keto.Subject_Id{Id: id}}
}

func NewSubjectSet(ns, obj, relation string) *Subject {
	return &Subject{Ref: &keto.Subject_Set{Set: &keto.SubjectSet{
		Namespace: ns,
		Object:    obj,
		Relation:  relation,
	}}}
}

func NewTuple(ns, obj, relation string, subject *keto.Subject) *keto.RelationTuple {
	return &keto.RelationTuple{
		Namespace: ns,
		Object:    obj,
		Relation:  relation,
		Subject:   subject,
	}
}

func NewTupleWithSubjectID(ns, obj, relation, subject string) *keto.RelationTuple {
	return NewTuple(ns, obj, relation, NewSubjectId(subject))
}

func NewTupleWithSubjectSet(ns, obj, relation, subsetNs, subsetObj, subsetRelation string) *keto.RelationTuple {
	return NewTuple(ns, obj, relation, NewSubjectSet(subsetNs, subsetObj, subsetRelation))
}

func NewQuery(ns, obj, relation string, subject *keto.Subject) *keto.RelationQuery {
	return &keto.RelationQuery{
		Namespace: PtrIfNot(ns, ""),
		Object:    PtrIfNot(obj, ""),
		Relation:  PtrIfNot(relation, ""),
		Subject:   subject,
	}
}

func NewQueryWithSubjectID(ns, obj, relation, subject string) *keto.RelationQuery {
	return NewQuery(ns, obj, relation, NewSubjectId(subject))
}

func NewQueryWithSubjectSet(ns, obj, relation, subsetNs, subsetObj, subsetRelation string) *keto.RelationQuery {
	return NewQuery(ns, obj, relation, NewSubjectSet(subsetNs, subsetObj, subsetRelation))
}

func TupleToQuery(t *keto.RelationTuple) *keto.RelationQuery {
	return NewQuery(t.Namespace, t.Object, t.Relation, t.Subject)
}

func QueryToTuple(q *keto.RelationQuery) *keto.RelationTuple {
	t := &keto.RelationTuple{Subject: q.Subject}
	if q.Namespace != nil {
		t.Namespace = *q.Namespace
	}
	if q.Object != nil {
		t.Object = *q.Object
	}
	if q.Relation != nil {
		t.Relation = *q.Relation
	}
	return t
}

func ParseTuples(s string) (tps []*RelationTuple, err error) {
	parts := strings.Split(s, "\n")
	tps = make([]*RelationTuple, 0, len(parts))
	for _, row := range parts {
		row = strings.TrimSpace(row)
		// ignore comments and empty lines
		if row == "" || strings.HasPrefix(row, "#") || strings.HasPrefix(row, "//") {
			continue
		}

		rt, err := ParseTuple(row)
		if err != nil {
			return nil, err
		}
		tps = append(tps, rt)
	}
	return
}

func ParseTuple(s string) (r *RelationTuple, err error) {
	var (
		objectAndRelationAndSubject string
		relationAndSubject          string
		subject                     string
		ok                          bool
	)
	r = &RelationTuple{}
	if r.Namespace, objectAndRelationAndSubject, ok = strings.Cut(s, ":"); !ok {
		return nil, fmt.Errorf("malformed input: expected input to contain ':'")
	}

	if r.Object, relationAndSubject, ok = strings.Cut(objectAndRelationAndSubject, "#"); !ok {
		return nil, fmt.Errorf("malformed input: expected input to contain '#'")
	}

	if r.Relation, subject, ok = strings.Cut(relationAndSubject, "@"); !ok {
		return nil, fmt.Errorf("malformed input: expected input to contain '@'")
	}

	// remove optional brackets around the subject set
	subject = strings.Trim(subject, "()")
	r.Subject = ParseSubject(subject)

	return r, nil
}

func FormatTuple(r *RelationTuple) string {
	sb := strings.Builder{}
	sb.WriteString(r.Namespace)
	sb.WriteRune(':')
	sb.WriteString(r.Object)
	sb.WriteRune('#')
	sb.WriteString(r.Relation)
	sb.WriteRune('@')

	if subject := FormatSubject(r.Subject); subject != "" {
		sb.WriteString(subject)
	} else {
		sb.WriteString("<ERROR: no subject>")
	}
	return sb.String()
}

func ParseSubject(str string) *Subject {
	if !strings.Contains(str, ":") {
		return NewSubjectId(str)
	}

	// If there is no '#' we have a subject set without a relation, such as
	// Users:Bob, which just means that the relation is empty.
	namespaceAndObject, relation, _ := strings.Cut(str, "#")

	namespace, object, _ := strings.Cut(namespaceAndObject, ":")
	return NewSubjectSet(namespace, object, relation)
}

func FormatSubject(s *Subject) string {
	set := s.GetSet()
	if set == nil {
		return s.GetId()
	}
	if set.Relation == "" {
		return fmt.Sprintf("%s:%s", set.Namespace, set.Object)
	}
	return fmt.Sprintf("%s:%s#%s", set.Namespace, set.Object, set.Relation)
}
