import os
import sys
from PIL import Image, ImageDraw

sys.path.append('.')
from customer.common import tools


def run(input_dir, out_dir):
    color_start = 128
    color_step = 1
    out_img_dir = os.path.join(out_dir, 'img')
    out_human_dir = os.path.join(out_dir, 'human')
    os.mkdir(out_img_dir)
    os.mkdir(out_human_dir)
    csv_header = ['file_name', 'instance_id', 'blur', 'illumination', 'occlusion', 'not_front', 'too_small', 'invaild']
    csv_data = [','.join(csv_header)]
    label_dirs = tools.find_dir_by_pre_name(input_dir, "label")
    fail_list = set()
    for label_dir in label_dirs:
        for label_json in tools.get_json_files(label_dir):
            label_data = tools.get_json_data(label_json)
            file_name = label_data['label_meta']['source_info']['file_name'].replace('.jpg', '.png')
            filtered_label = list(filter(lambda x: x['annotation']['type'] == 'polygon', label_data['labels']))
            if len(filtered_label) == 0:
                continue
            img_size = (label_data['label_meta']['source_info']['width'], label_data['label_meta']['source_info']['height'])
            img = Image.new("L", img_size, 0)
            draw = ImageDraw.Draw(img)
            img_human = Image.new("L", img_size, 0)
            draw_human = ImageDraw.Draw(img_human)
            for idx, label in enumerate(filtered_label, start=1):
                try:
                    attr = label['attrs'][0][0]
                except Exception as e:
                    fail_list.add(label_json.path)
                    continue
                instance_id = idx
                blur = 1 if attr == 'blur' else 0
                illumination = 1 if attr == 'illumination' else 0
                occlusion = 1 if attr == 'occlusion' else 0
                not_front = 1 if attr == 'not_front' else 0
                too_small = 1 if attr == 'too_small' else 0
                invalid = sum([blur, illumination, occlusion, not_front, too_small])
                csv_data.append(','.join([str(ele) for ele in [file_name, instance_id, blur, illumination, occlusion, not_front, too_small, invalid]]))
                polygon_points = []
                for point in label['annotation']['data']:
                    polygon_points.append(point['x'])
                    polygon_points.append(point['y'])
                draw.polygon(polygon_points, fill=instance_id)
                draw_human.polygon(polygon_points, fill=color_start + (idx - 1) * color_step)
            mask_file = os.path.join(out_img_dir, file_name)
            img.save(mask_file)
            mask_human_file = os.path.join(out_human_dir, file_name)
            img_human.save(mask_human_file)
    if fail_list:
        print("处理失败文件列表为：", fail_list)

    tools.write_file(os.path.join(out_dir, 'result.csv'), '\n'.join(csv_data))


if __name__ == "__main__":
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
