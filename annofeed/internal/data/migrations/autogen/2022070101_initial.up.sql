BEGIN;

CREATE TABLE datas
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    -- uid         VARCHAR(32)  NOT NULL UNIQUE,
    base_on     BIGINT       NOT NULL DEFAULT 0,
    name        VARCHAR(128) NOT NULL,
    type        VA<PERSON><PERSON><PERSON>(128) NOT NULL DEFAULT '',
    "desc"      VARCHAR(512) NOT NULL DEFAULT '',
    source      JSONB        NOT NULL DEFAULT '{}'::JSONB,
    state       VARCHAR(128) NOT NULL,
    error       VARCHAR(512) NOT NULL DEFAULT '',
    size        INTEGER      NOT NULL DEFAULT 0,
    creator_uid VARCHAR(32)  NOT NULL DEFAULT '',
    org_uid     VARCHAR(32)  NOT NULL DEFAULT '',
    order_uid   VARCHAR(32)  NOT NULL DEFAULT '',
    hierarchy   VARCHAR(128) NOT NULL DEFAULT '',   -- team hierarchy: lvl1-id/lvl2-id/...
    summary     JSONB,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    deleted_at  TIMESTAMPTZ
);
CREATE INDEX idx_datas_name ON datas ((lower(name))) INCLUDE (state, deleted_at);
CREATE INDEX idx_datas_hierarchy ON datas (hierarchy) INCLUDE (state, deleted_at);
CREATE INDEX idx_datas_created_at ON datas (created_at) INCLUDE (state, deleted_at);
CREATE INDEX idx_datas_org_uid ON datas (org_uid) INCLUDE (state, deleted_at);
CREATE INDEX idx_datas_order_uid ON datas (order_uid) INCLUDE (state, deleted_at);

CREATE TABLE rawdatas
(
    id         BIGSERIAL    NOT NULL PRIMARY KEY,
    -- uid        VARCHAR(32)  NOT NULL UNIQUE,
    data_id    BIGINT       NOT NULL,
    folder     VARCHAR(4096) NOT NULL DEFAULT '',
    name       VARCHAR(4096) NOT NULL,
    orig_name  VARCHAR(4096) NOT NULL DEFAULT '',
    elem_idx_in_data INTEGER NOT NULL DEFAULT 0,
    info       JSON,
    type       VARCHAR(128) NOT NULL,
    format     VARCHAR(128) NOT NULL,
    cam_name   VARCHAR(128) NOT NULL DEFAULT '',
    uri        VARCHAR(4096) NOT NULL DEFAULT '',
    access_url VARCHAR(4096) NOT NULL DEFAULT '',
    url_expires TIMESTAMPTZ,
    embedding_uri VARCHAR(4096) NOT NULL DEFAULT '',
    -- storage    VARCHAR(32) NOT NULL DEFAULT '',
    -- key        VARCHAR(4096) NOT NULL DEFAULT '',
    sha256     VARCHAR(64)  NOT NULL DEFAULT '',
    size       INTEGER      NOT NULL DEFAULT 0,
    idx        INTEGER      NOT NULL DEFAULT 0, -- for order purpose only
    updated_at TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_rawdatas_name ON rawdatas ((lower(name)));
CREATE UNIQUE INDEX idx_rawdatas_data_id_name ON rawdatas (data_id, name);
CREATE INDEX idx_rawdatas_data_id_elem_idx_in_data_idx ON rawdatas (data_id, elem_idx_in_data, idx);
CREATE INDEX idx_rawdatas_created_at ON rawdatas (created_at);
CREATE INDEX idx_rawdatas_sha256 ON rawdatas (sha256);

CREATE TABLE files
(
    id         BIGSERIAL    NOT NULL PRIMARY KEY,
    share_id   VARCHAR(32)  UNIQUE,
    name       VARCHAR(128) NOT NULL,
    uri        VARCHAR(4096) NOT NULL,
    upload_id  VARCHAR(256),
    state      SMALLINT     NOT NULL DEFAULT 0, -- 0: uploading, 1: uploaded
    size       BIGINT       NOT NULL DEFAULT 0,
    mime       VARCHAR(128) NOT NULL DEFAULT '', -- MIME type of the file
    sha256     VARCHAR(64)  NOT NULL DEFAULT '',
    org_uid    VARCHAR(32)  NOT NULL DEFAULT '',
    creator_uid VARCHAR(32)  NOT NULL DEFAULT '',
    created_at TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);
CREATE INDEX idx_files_name ON files ((lower(name))) INCLUDE (deleted_at);
CREATE INDEX idx_files_uri ON files (uri) INCLUDE (deleted_at);
CREATE INDEX idx_files_created_at ON files (created_at) INCLUDE (deleted_at);
CREATE INDEX idx_files_creator_uid ON files (creator_uid) INCLUDE (deleted_at);
CREATE INDEX idx_files_org_uid ON files (org_uid) INCLUDE (deleted_at);
CREATE INDEX idx_files_sha256 ON files (sha256) INCLUDE (deleted_at);

COMMIT;
