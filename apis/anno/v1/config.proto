syntax = "proto3";

package anno.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "openapi/v3/annotations.proto";
import "types/lang.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anno/v1;anno";
option java_multiple_files = true;
option java_package = "anno.v1";

service Configs {
  // List Errors info
  rpc ListErrors (google.protobuf.Empty) returns (Errors) {
    option (google.api.http) = {
      get: "/v1/errors"
    };
  }

  rpc GetVersion (google.protobuf.Empty) returns (GetVersionReply) {
    option (google.api.http) = {
      get: "/v1/version"
    };
  }

  // List comment reasons
  rpc ListCommentReasons (google.protobuf.Empty) returns (ListCommentReasonsReply) {
    option (google.api.http) = {
      get: "/v1/commentreasons"
    };
  }

  // Modify comment reasons
  rpc PutCommentReasons (PutCommentReasonsRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/v1/commentreasons"
      body: "*"
    };
  }
}

message Errors {
  // error-reason -> (language -> display-name)
  map<string, types.Multilingual> errors = 1;
}

message GetVersionReply {
  string version = 1;
}

message CommentReasonClass {
  option (openapi.v3.schema) = {
    required: ["class", "reasons"]
  };

  // reason class
  types.DisplayItem class = 1;
  // reasons in the class
  repeated types.DisplayItem reasons = 2;
}

message ListCommentReasonsReply {
  repeated CommentReasonClass classes = 1;
}

message PutCommentReasonsRequest {
  // corresponding classes will be replaced by this request
  repeated CommentReasonClass classes = 1;
}
