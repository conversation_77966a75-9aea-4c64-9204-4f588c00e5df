// source: anno/v1/lot.proto
package data

import (
	"context"

	"anno/internal/biz"

	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gorm.io/gorm"
)

var (
	execFldLotID    = biz.LotexecutorSfldLotID.WithTable()
	execFldSubtype  = biz.LotexecutorSfldSubtype.WithTable()
	execFldPhase    = biz.LotexecutorSfldPhase.WithTable()
	phaseFldLotID   = biz.LotphaseSfldLotID.WithTable()
	phaseFldNumber  = biz.LotphaseSfldNumber.WithTable()
	phaseFldSubtype = biz.LotphaseSfldSubtype.WithTable()
)

func (o *lotsRepo) buildQueryByExecutor(ctx context.Context, p *biz.LotFilterByExecutor) *gorm.DB {
	q := o.data.WithCtx(ctx).Model(&biz.Lot{})
	if p.NamePattern != "" {
		q = q.Where(biz.LotSfldName.WithTable()+" LIKE ?", "%"+p.NamePattern+"%")
	}
	if p.Type != "" {
		q = q.Where(biz.LotSfldType.WithTable()+" = ?", p.Type)
	}
	if len(p.States) > 0 {
		q = q.Where(biz.LotSfldState.WithTable()+" IN ?", p.States)
	}

	if p.UserUid != "" {
		q = q.Joins("JOIN "+biz.LotexecutorTableName+" ON "+execFldLotID+" = "+biz.LotSfldID.WithTable()).
			Where(biz.LotexecutorSfldUserUid.WithTable()+" = ?", p.UserUid) // ensure the user is assigned to this lot phase
		if p.Claimable { // check if there is any claimable jobs
			db := o.data.WithCtx(ctx)
			subq := o.data.WithCtx(ctx).Model(&biz.Job{}).Select("1").
				Joins("JOIN " + biz.LotphaseTableName + " ON " + phaseFldLotID + " = " + jobFldLotID + " AND " +
					phaseFldNumber + " = " + jobFldPhase + " AND " + phaseFldSubtype + " = " + jobFldSubtype).
				Where(biz.LotSfldID.WithTable() + " = " + jobFldLotID).
				Where(execFldPhase + " = " + jobFldPhase).
				Where(biz.LotphaseSfldExecteam.WithTable() + " = ''"). // select only one of the multiple records of a phase
				Where(db.
					Or( // 1. unclaimed and unassigned jobs
						db.Where(jobFldExecutorUid+" = ''").
							Where(jobFldState+" = ?", biz.JobStateUnstart)).
					Or( // 2. claimed by me or assigned to me
						db.Where(jobFldExecutorUid+" = ?", p.UserUid).
							Where(jobFldState+" IN ?", []biz.JobState{biz.JobStateUnstart, biz.JobStateDoing})).
					Or( // 3. assigned to others but unclaimed for a while
						db.Where(jobFldExecutorUid+" <> ''").Where(jobFldExecutorUid+" <> ?", p.UserUid).
							Where(jobFldState+" = ?", biz.JobStateUnstart).
							Where(jobFldUpdatedAt + " < NOW() - '10 minutes'::interval")).
					Or( // 4. claimed by others but timeout
						db.Where(jobFldExecutorUid+" <> ''").Where(jobFldExecutorUid+" <> ?", p.UserUid).
							Where(jobFldState+" = ?", biz.JobStateDoing).
							Where(jobFldUpdatedAt + " < NOW() - CONCAT(" + biz.LotphaseSfldTimeout.WithTable() + ", 'seconds')::interval")))
			q = q.Where(biz.LotSfldState.WithTable()+" = ?", biz.LotStateOngoing).
				Where(biz.LotSfldJobReady.WithTable()). // JobReady should be true
				Where("EXISTS (?)", subq)
		}
	} else if len(p.ExecteamUids) > 0 {
		q = q.Joins("JOIN lotphases AS p ON p."+biz.LotphaseSfldLotID.String()+" = lots.id").
			Where("p."+biz.LotphaseSfldExecteam.String()+" IN ?", p.ExecteamUids)
	}
	return q
}

func (o *lotsRepo) QueryByExecutor(ctx context.Context, p *biz.LotFilterByExecutor, pager biz.Pager) ([]*biz.Lot, error) {
	datas := []*biz.Lot{}
	err := o.buildQueryByExecutor(ctx, p).Order(biz.LotSfldPriority.WithTable() + " DESC, " + biz.LotSfldID.WithTable() + " DESC").
		Offset(pager.Offset()).Limit(pager.Pagesz).
		Distinct("lots.*"). // probably not needed anymore in the case a user can only be assigned to one phase in a lot
		Find(&datas).Error
	if err != nil {
		return nil, Convert(&biz.Lot{}, err)
	}

	if len(datas) > 0 {
		phases, err := o.queryExecutionPhases(ctx, lo.Map(datas, func(v *biz.Lot, _ int) int64 { return v.ID }),
			p.UserUid, p.ExecteamUids)
		if err != nil {
			return nil, err
		}
		lo.ForEach(datas, func(v *biz.Lot, _ int) { v.Phases = phases[v.ID] })
	}
	return datas, nil
}

func (o *lotsRepo) CountByExecutor(ctx context.Context, p *biz.LotFilterByExecutor) (int, error) {
	var cnt int64
	err := o.buildQueryByExecutor(ctx, p).
		Distinct("lots.id").Count(&cnt).Error
	if err != nil {
		return 0, Convert(&biz.Lot{}, err)
	}
	return int(cnt), nil
}

func (o *lotsRepo) queryExecutionPhases(ctx context.Context, lotIDs []int64, userUid string, execteams []string) (
	phasesByLots map[int64][]*biz.Lotphase, err error) {
	// query phases where the user or team is involved
	var execphases []*biz.Lotphase
	q := o.data.WithCtx(ctx).Model(&biz.Lotphase{}).
		Select(field.Join(biz.LotphaseSfldLotID.WithTable(),
			biz.LotphaseSfldNumber.WithTable(),
			biz.LotphaseSfldExecteam.WithTable())).
		Where(biz.LotphaseSfldLotID.WithTable()+" IN ?", lotIDs)
	if userUid != "" {
		q = q.Joins("JOIN lotexecutors AS e ON e."+biz.LotexecutorSfldLotID.String()+" = "+biz.LotphaseSfldLotID.WithTable()+
			" AND e."+biz.LotexecutorSfldPhase.String()+" = "+biz.LotphaseSfldNumber.WithTable()+
			" AND e."+biz.LotexecutorSfldTeamUid.String()+" = "+biz.LotphaseSfldExecteam.WithTable()).
			Where("e."+biz.LotexecutorSfldUserUid.String()+" = ?", userUid)
	} else if len(execteams) > 0 {
		q = q.Where(biz.LotphaseSfldExecteam.WithTable()+" IN ?", execteams)
	}
	err = q.Find(&execphases).Error
	if err != nil {
		return nil, Convert(&biz.Lotphase{}, err)
	}

	// build involving phases and team map
	execmap := make(map[int64]map[int32]string, len(lotIDs))
	for _, p := range execphases {
		m := execmap[p.LotID]
		if m == nil {
			m = make(map[int32]string)
			execmap[p.LotID] = m
		}
		m[p.Number] = p.Execteam
	}

	// query all phases
	var phases []*biz.Lotphase
	err = o.data.WithCtx(ctx).
		Where(biz.LotphaseSfldLotID.WithTable()+" IN ?", lotIDs).
		Order(field.Join(biz.LotphaseSfldLotID.String(), biz.LotphaseSfldNumber.String())).
		Find(&phases).Error
	if err != nil {
		return nil, Convert(&biz.Lotphase{}, err)
	}
	// clear uninvolved phases and build result map
	phasesByLots = make(map[int64][]*biz.Lotphase, len(lotIDs))
	for _, p := range phases {
		p.Execteam = execmap[p.LotID][p.Number]
		phasesByLots[p.LotID] = append(phasesByLots[p.LotID], p)
	}
	return
}

func (o *lotsRepo) GetLotIDsByExecutor(ctx context.Context, filter *biz.LotFilterByExecutor) ([]int64, error) {
	var lotIDs []int64
	err := o.buildQueryByExecutor(ctx, filter).
		Distinct(biz.LotSfldID.WithTable()).
		Find(&lotIDs).Error
	if err != nil {
		return nil, Convert(&biz.Lotexecutor{}, err)
	}

	return lotIDs, nil
}

func (o *lotsRepo) GetLotCountByExecutor(ctx context.Context, filter *biz.LotFilterByExecutor) (map[biz.LotState]int32, error) {
	type LotCount struct {
		State biz.LotState
		Count int32
	}

	var lotCounts []LotCount
	err := o.buildQueryByExecutor(ctx, filter).
		Select(biz.LotSfldState.WithTable() + ", COUNT(DISTINCT " + biz.LotSfldID.WithTable() + ") AS count").
		Group(biz.LotSfldState.WithTable()).
		Scan(&lotCounts).Error
	if err != nil {
		return nil, Convert(&biz.Lotexecutor{}, err)
	}
	if len(lotCounts) == 0 {
		return nil, nil
	}

	return lo.SliceToMap(lotCounts, func(item LotCount) (biz.LotState, int32) { return item.State, item.Count }), nil
}
