package ostore

import (
	"annostat/internal/conf"

	"gitlab.rp.konvery.work/platform/pkg/download"
	"gitlab.rp.konvery.work/platform/pkg/upload"
)

func Init(cfg *conf.ObjectStorage) {
	if cfg.Type == "" {
		cfg.Type = upload.StorageTypeLocalFS
	}

	InitSigner(cfg)

	upload.Init(&upload.Config{
		Storage:      cfg.Type,
		BaseDir:      cfg.GetLocalfs().GetBaseDir(),
		Bucket:       cfg.GetS3().GetBucket(),
		PublicBucket: cfg.GetS3().GetPublicBucket(),
	})

	download.Init(&download.Config{
		WorkDir:       cfg.Workdir,
		LocalUploader: upload.LocalFSConfig{BaseDir: cfg.GetLocalfs().GetBaseDir()},
	})
}
