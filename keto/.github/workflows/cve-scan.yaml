name: Docker Image Scanners
on:
  push:
    branches:
      - "master"
    tags:
      - "v*.*.*"
  pull_request:
    branches:
      - "master"
  merge_group:

jobs:
  scanners:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Setup Env
        id: vars
        shell: bash
        run: |
          echo "##[set-output name=branch;]$(echo ${GITHUB_REF#refs/heads/})"
          echo "::set-output name=sha_short::$(git rev-parse --short HEAD)"
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Build images
        shell: bash
        run: |
          touch keto
          DOCKER_BUILDKIT=1 docker build -f .docker/Dockerfile-build --build-arg=COMMIT=${{ steps.vars.outputs.sha_short }} -t oryd/keto:${{ steps.vars.outputs.sha_short }} .
          rm keto
      - name: Anchore Scanner
        uses: anchore/scan-action@v3
        id: grype-scan
        with:
          image: oryd/keto:${{ steps.vars.outputs.sha_short }}
          fail-build: true
          severity-cutoff: high
          acs-report-enable: true
      - name: Inspect action SARIF report
        shell: bash
        if: ${{ always() }}
        run: |
          echo "::group::Anchore Scan Details"
          jq '.runs[0].results' ${{ steps.grype-scan.outputs.sarif }}
          echo "::endgroup::"
      - name: Trivy Scanner
        uses: aquasecurity/trivy-action@master
        if: ${{ always() }}
        with:
          image-ref: oryd/keto:${{ steps.vars.outputs.sha_short }}
          format: "table"
          exit-code: "42"
          ignore-unfixed: true
          vuln-type: "os,library"
          severity: "CRITICAL,HIGH"
      - name: Dockle Linter
        uses: erzz/dockle-action@v1.3.1
        if: ${{ always() }}
        with:
          image: oryd/keto:${{ steps.vars.outputs.sha_short }}
          exit-code: 42
          failure-threshold: fatal
