# cloud.google.com/go/compute v1.18.0
## explicit; go 1.19
cloud.google.com/go/compute/internal
# cloud.google.com/go/compute/metadata v0.2.3
## explicit; go 1.19
cloud.google.com/go/compute/metadata
# github.com/Azure/go-ansiterm v0.0.0-20230124172434-306776ec8161
## explicit; go 1.16
github.com/Azure/go-ansiterm
github.com/Azure/go-ansiterm/winterm
# github.com/Masterminds/semver/v3 v3.2.0
## explicit; go 1.18
github.com/Masterminds/semver/v3
# github.com/Microsoft/go-winio v0.6.0
## explicit; go 1.17
github.com/Microsoft/go-winio
github.com/Microsoft/go-winio/internal/socket
github.com/Microsoft/go-winio/pkg/guid
# github.com/Nvveen/Gotty v0.0.0-20120604004816-cd527374f1e5
## explicit
github.com/Nvveen/Gotty
# github.com/avast/retry-go/v4 v4.3.2
## explicit; go 1.13
github.com/avast/retry-go/v4
# github.com/aymerick/douceur v0.2.0
## explicit
github.com/aymerick/douceur/css
github.com/aymerick/douceur/parser
# github.com/beorn7/perks v1.0.1
## explicit; go 1.11
github.com/beorn7/perks/quantile
# github.com/bradleyjkemp/cupaloy/v2 v2.8.0
## explicit
github.com/bradleyjkemp/cupaloy/v2
github.com/bradleyjkemp/cupaloy/v2/internal
# github.com/cenkalti/backoff/v3 v3.2.2
## explicit; go 1.12
github.com/cenkalti/backoff/v3
# github.com/cenkalti/backoff/v4 v4.2.0
## explicit; go 1.18
github.com/cenkalti/backoff/v4
# github.com/cespare/xxhash/v2 v2.2.0
## explicit; go 1.11
github.com/cespare/xxhash/v2
# github.com/cockroachdb/cockroach-go/v2 v2.2.20
## explicit; go 1.13
github.com/cockroachdb/cockroach-go/v2/crdb
github.com/cockroachdb/cockroach-go/v2/testserver
github.com/cockroachdb/cockroach-go/v2/testserver/version
# github.com/containerd/continuity v0.3.0
## explicit; go 1.17
github.com/containerd/continuity/pathdriver
# github.com/davecgh/go-spew v1.1.1
## explicit
github.com/davecgh/go-spew/spew
# github.com/dgraph-io/ristretto v0.1.1
## explicit; go 1.12
github.com/dgraph-io/ristretto
github.com/dgraph-io/ristretto/z
github.com/dgraph-io/ristretto/z/simd
# github.com/docker/cli v20.10.23+incompatible
## explicit
github.com/docker/cli/cli/compose/interpolation
github.com/docker/cli/cli/compose/loader
github.com/docker/cli/cli/compose/schema
github.com/docker/cli/cli/compose/template
github.com/docker/cli/cli/compose/types
github.com/docker/cli/opts
# github.com/docker/distribution v2.8.1+incompatible
## explicit
github.com/docker/distribution/digestset
github.com/docker/distribution/reference
# github.com/docker/docker v20.10.23+incompatible
## explicit
github.com/docker/docker/api
github.com/docker/docker/api/types
github.com/docker/docker/api/types/blkiodev
github.com/docker/docker/api/types/container
github.com/docker/docker/api/types/events
github.com/docker/docker/api/types/filters
github.com/docker/docker/api/types/image
github.com/docker/docker/api/types/mount
github.com/docker/docker/api/types/network
github.com/docker/docker/api/types/registry
github.com/docker/docker/api/types/strslice
github.com/docker/docker/api/types/swarm
github.com/docker/docker/api/types/swarm/runtime
github.com/docker/docker/api/types/time
github.com/docker/docker/api/types/versions
github.com/docker/docker/api/types/volume
github.com/docker/docker/client
github.com/docker/docker/errdefs
# github.com/docker/go-connections v0.4.0
## explicit
github.com/docker/go-connections/nat
github.com/docker/go-connections/sockets
github.com/docker/go-connections/tlsconfig
# github.com/docker/go-units v0.5.0
## explicit
github.com/docker/go-units
# github.com/dustin/go-humanize v1.0.1
## explicit; go 1.16
github.com/dustin/go-humanize
# github.com/evanphx/json-patch v5.6.0+incompatible
## explicit
github.com/evanphx/json-patch
# github.com/fatih/color v1.14.1
## explicit; go 1.17
github.com/fatih/color
# github.com/fatih/structs v1.1.0
## explicit
github.com/fatih/structs
# github.com/felixge/fgprof v0.9.3
## explicit; go 1.14
github.com/felixge/fgprof
# github.com/felixge/httpsnoop v1.0.3
## explicit; go 1.13
github.com/felixge/httpsnoop
# github.com/fsnotify/fsnotify v1.6.0
## explicit; go 1.16
github.com/fsnotify/fsnotify
# github.com/ghodss/yaml v1.0.0
## explicit
github.com/ghodss/yaml
# github.com/go-logr/logr v1.2.3
## explicit; go 1.16
github.com/go-logr/logr
github.com/go-logr/logr/funcr
# github.com/go-logr/stdr v1.2.2
## explicit; go 1.16
github.com/go-logr/stdr
# github.com/go-openapi/jsonpointer v0.19.6
## explicit; go 1.13
github.com/go-openapi/jsonpointer
# github.com/go-openapi/swag v0.22.3
## explicit; go 1.18
github.com/go-openapi/swag
# github.com/go-sql-driver/mysql v1.7.0
## explicit; go 1.13
github.com/go-sql-driver/mysql
# github.com/gobuffalo/envy v1.10.2
## explicit; go 1.16
github.com/gobuffalo/envy
# github.com/gobuffalo/fizz v1.14.4
## explicit; go 1.16
github.com/gobuffalo/fizz
github.com/gobuffalo/fizz/translators
# github.com/gobuffalo/flect v1.0.0
## explicit; go 1.16
github.com/gobuffalo/flect
github.com/gobuffalo/flect/name
# github.com/gobuffalo/github_flavored_markdown v1.1.3
## explicit; go 1.16
github.com/gobuffalo/github_flavored_markdown
github.com/gobuffalo/github_flavored_markdown/internal/russross/blackfriday
github.com/gobuffalo/github_flavored_markdown/internal/shurcooL/highlight_diff
github.com/gobuffalo/github_flavored_markdown/internal/shurcooL/highlight_go
github.com/gobuffalo/github_flavored_markdown/internal/shurcooL/octicon
github.com/gobuffalo/github_flavored_markdown/internal/shurcooL/sanitized_anchor_name
# github.com/gobuffalo/helpers v0.6.7
## explicit; go 1.16
github.com/gobuffalo/helpers
github.com/gobuffalo/helpers/content
github.com/gobuffalo/helpers/debug
github.com/gobuffalo/helpers/encoders
github.com/gobuffalo/helpers/env
github.com/gobuffalo/helpers/escapes
github.com/gobuffalo/helpers/forms
github.com/gobuffalo/helpers/forms/bootstrap
github.com/gobuffalo/helpers/hctx
github.com/gobuffalo/helpers/inflections
github.com/gobuffalo/helpers/iterators
github.com/gobuffalo/helpers/meta
github.com/gobuffalo/helpers/paths
github.com/gobuffalo/helpers/tags
github.com/gobuffalo/helpers/text
# github.com/gobuffalo/httptest v1.5.2
## explicit; go 1.16
github.com/gobuffalo/httptest
github.com/gobuffalo/httptest/internal/takeon/github.com/ajg/form
github.com/gobuffalo/httptest/internal/takeon/github.com/markbates/hmax
# github.com/gobuffalo/nulls v0.4.2
## explicit; go 1.16
github.com/gobuffalo/nulls
# github.com/gobuffalo/plush/v4 v4.1.18
## explicit; go 1.16
github.com/gobuffalo/plush/v4
github.com/gobuffalo/plush/v4/ast
github.com/gobuffalo/plush/v4/lexer
github.com/gobuffalo/plush/v4/parser
github.com/gobuffalo/plush/v4/token
# github.com/gobuffalo/pop/v6 v6.1.1
## explicit; go 1.16
github.com/gobuffalo/pop/v6
github.com/gobuffalo/pop/v6/associations
github.com/gobuffalo/pop/v6/columns
github.com/gobuffalo/pop/v6/internal/defaults
github.com/gobuffalo/pop/v6/internal/randx
github.com/gobuffalo/pop/v6/logging
# github.com/gobuffalo/tags/v3 v3.1.4
## explicit; go 1.16
github.com/gobuffalo/tags/v3
github.com/gobuffalo/tags/v3/form
github.com/gobuffalo/tags/v3/form/bootstrap
# github.com/gobuffalo/validate/v3 v3.3.3
## explicit; go 1.16
github.com/gobuffalo/validate/v3
github.com/gobuffalo/validate/v3/validators
# github.com/goccy/go-yaml v1.9.8
## explicit; go 1.12
github.com/goccy/go-yaml
github.com/goccy/go-yaml/ast
github.com/goccy/go-yaml/internal/errors
github.com/goccy/go-yaml/lexer
github.com/goccy/go-yaml/parser
github.com/goccy/go-yaml/printer
github.com/goccy/go-yaml/scanner
github.com/goccy/go-yaml/token
# github.com/gofrs/flock v0.8.1
## explicit
github.com/gofrs/flock
# github.com/gofrs/uuid v4.4.0+incompatible
## explicit
github.com/gofrs/uuid
# github.com/gogo/protobuf v1.3.2
## explicit; go 1.15
github.com/gogo/protobuf/gogoproto
github.com/gogo/protobuf/proto
github.com/gogo/protobuf/protoc-gen-gogo/descriptor
# github.com/golang/glog v1.0.0
## explicit; go 1.11
github.com/golang/glog
# github.com/golang/protobuf v1.5.2
## explicit; go 1.9
github.com/golang/protobuf/jsonpb
github.com/golang/protobuf/proto
github.com/golang/protobuf/ptypes
github.com/golang/protobuf/ptypes/any
github.com/golang/protobuf/ptypes/duration
github.com/golang/protobuf/ptypes/timestamp
# github.com/google/pprof v0.0.0-20230131232505-5a9e8f65f08f
## explicit; go 1.18
github.com/google/pprof/profile
# github.com/google/shlex v0.0.0-20191202100458-e7afc7fbc510
## explicit; go 1.13
github.com/google/shlex
# github.com/gorilla/css v1.0.0
## explicit
github.com/gorilla/css/scanner
# github.com/gorilla/websocket v1.5.0
## explicit; go 1.12
github.com/gorilla/websocket
# github.com/grpc-ecosystem/go-grpc-middleware v1.3.0
## explicit; go 1.14
github.com/grpc-ecosystem/go-grpc-middleware
github.com/grpc-ecosystem/go-grpc-middleware/logging
github.com/grpc-ecosystem/go-grpc-middleware/logging/logrus
github.com/grpc-ecosystem/go-grpc-middleware/logging/logrus/ctxlogrus
github.com/grpc-ecosystem/go-grpc-middleware/recovery
github.com/grpc-ecosystem/go-grpc-middleware/tags
# github.com/grpc-ecosystem/grpc-gateway/v2 v2.15.0
## explicit; go 1.17
github.com/grpc-ecosystem/grpc-gateway/v2/internal/httprule
github.com/grpc-ecosystem/grpc-gateway/v2/runtime
github.com/grpc-ecosystem/grpc-gateway/v2/utilities
# github.com/hashicorp/go-cleanhttp v0.5.2
## explicit; go 1.13
github.com/hashicorp/go-cleanhttp
# github.com/hashicorp/go-hclog v1.4.0
## explicit; go 1.13
# github.com/hashicorp/go-retryablehttp v0.7.2
## explicit; go 1.13
github.com/hashicorp/go-retryablehttp
# github.com/hashicorp/hcl v1.0.0
## explicit
github.com/hashicorp/hcl
github.com/hashicorp/hcl/hcl/ast
github.com/hashicorp/hcl/hcl/parser
github.com/hashicorp/hcl/hcl/printer
github.com/hashicorp/hcl/hcl/scanner
github.com/hashicorp/hcl/hcl/strconv
github.com/hashicorp/hcl/hcl/token
github.com/hashicorp/hcl/json/parser
github.com/hashicorp/hcl/json/scanner
github.com/hashicorp/hcl/json/token
# github.com/imdario/mergo v0.3.13
## explicit; go 1.13
github.com/imdario/mergo
# github.com/inconshreveable/mousetrap v1.1.0
## explicit; go 1.18
github.com/inconshreveable/mousetrap
# github.com/inhies/go-bytesize v0.0.0-20220417184213-4913239db9cf
## explicit; go 1.12
github.com/inhies/go-bytesize
# github.com/jackc/chunkreader/v2 v2.0.1
## explicit; go 1.12
github.com/jackc/chunkreader/v2
# github.com/jackc/pgconn v1.13.0
## explicit; go 1.12
github.com/jackc/pgconn
github.com/jackc/pgconn/internal/ctxwatch
github.com/jackc/pgconn/stmtcache
# github.com/jackc/pgio v1.0.0
## explicit; go 1.12
github.com/jackc/pgio
# github.com/jackc/pgpassfile v1.0.0
## explicit; go 1.12
github.com/jackc/pgpassfile
# github.com/jackc/pgproto3/v2 v2.3.1
## explicit; go 1.12
github.com/jackc/pgproto3/v2
# github.com/jackc/pgservicefile v0.0.0-20221227161230-091c0ba34f0a
## explicit; go 1.14
github.com/jackc/pgservicefile
# github.com/jackc/pgtype v1.13.0
## explicit; go 1.13
github.com/jackc/pgtype
# github.com/jackc/pgx/v4 v4.17.2
## explicit; go 1.13
github.com/jackc/pgx/v4
github.com/jackc/pgx/v4/internal/sanitize
github.com/jackc/pgx/v4/stdlib
# github.com/jandelgado/gcov2lcov v1.0.5
## explicit; go 1.12
github.com/jandelgado/gcov2lcov
# github.com/jmoiron/sqlx v1.3.5
## explicit; go 1.10
github.com/jmoiron/sqlx
github.com/jmoiron/sqlx/reflectx
# github.com/joho/godotenv v1.4.0
## explicit; go 1.12
github.com/joho/godotenv
# github.com/josharian/intern v1.0.0
## explicit; go 1.5
github.com/josharian/intern
# github.com/julienschmidt/httprouter v1.3.0
## explicit; go 1.7
github.com/julienschmidt/httprouter
# github.com/kballard/go-shellquote v0.0.0-20180428030007-95032a82bc51
## explicit
github.com/kballard/go-shellquote
# github.com/knadh/koanf v1.5.0
## explicit; go 1.12
github.com/knadh/koanf
github.com/knadh/koanf/maps
github.com/knadh/koanf/parsers/json
github.com/knadh/koanf/parsers/toml
github.com/knadh/koanf/parsers/yaml
github.com/knadh/koanf/providers/posflag
# github.com/lib/pq v1.10.7
## explicit; go 1.13
github.com/lib/pq
github.com/lib/pq/oid
github.com/lib/pq/scram
# github.com/luna-duclos/instrumentedsql v1.1.3
## explicit; go 1.11
github.com/luna-duclos/instrumentedsql
# github.com/magiconair/properties v1.8.7
## explicit; go 1.19
github.com/magiconair/properties
# github.com/mailru/easyjson v0.7.7
## explicit; go 1.12
github.com/mailru/easyjson/buffer
github.com/mailru/easyjson/jlexer
github.com/mailru/easyjson/jwriter
# github.com/mattn/go-colorable v0.1.13
## explicit; go 1.15
github.com/mattn/go-colorable
# github.com/mattn/go-isatty v0.0.17
## explicit; go 1.15
github.com/mattn/go-isatty
# github.com/mattn/go-sqlite3 v1.14.16
## explicit; go 1.16
github.com/mattn/go-sqlite3
# github.com/matttproud/golang_protobuf_extensions v1.0.4
## explicit; go 1.9
github.com/matttproud/golang_protobuf_extensions/pbutil
# github.com/microcosm-cc/bluemonday v1.0.22
## explicit; go 1.19
github.com/microcosm-cc/bluemonday
github.com/microcosm-cc/bluemonday/css
# github.com/mitchellh/copystructure v1.2.0
## explicit; go 1.15
github.com/mitchellh/copystructure
# github.com/mitchellh/mapstructure v1.5.0
## explicit; go 1.14
github.com/mitchellh/mapstructure
# github.com/mitchellh/reflectwalk v1.0.2
## explicit
github.com/mitchellh/reflectwalk
# github.com/moby/term v0.0.0-20221205130635-1aeaba878587
## explicit; go 1.18
github.com/moby/term
github.com/moby/term/windows
# github.com/nyaruka/phonenumbers v1.1.5
## explicit; go 1.18
github.com/nyaruka/phonenumbers
# github.com/opencontainers/go-digest v1.0.0
## explicit; go 1.13
github.com/opencontainers/go-digest
# github.com/opencontainers/image-spec v1.1.0-rc2
## explicit; go 1.17
github.com/opencontainers/image-spec/specs-go
github.com/opencontainers/image-spec/specs-go/v1
# github.com/opencontainers/runc v1.1.4
## explicit; go 1.16
github.com/opencontainers/runc/libcontainer/user
# github.com/openzipkin/zipkin-go v0.4.1
## explicit; go 1.18
github.com/openzipkin/zipkin-go/model
# github.com/orca-zhang/ecache v1.1.1
## explicit; go 1.14
github.com/orca-zhang/ecache
# github.com/ory/analytics-go/v4 v4.0.3
## explicit; go 1.14
github.com/ory/analytics-go/v4
# github.com/ory/dockertest/v3 v3.9.1
## explicit; go 1.17
github.com/ory/dockertest/v3
github.com/ory/dockertest/v3/docker
github.com/ory/dockertest/v3/docker/opts
github.com/ory/dockertest/v3/docker/pkg/archive
github.com/ory/dockertest/v3/docker/pkg/fileutils
github.com/ory/dockertest/v3/docker/pkg/homedir
github.com/ory/dockertest/v3/docker/pkg/idtools
github.com/ory/dockertest/v3/docker/pkg/ioutils
github.com/ory/dockertest/v3/docker/pkg/jsonmessage
github.com/ory/dockertest/v3/docker/pkg/longpath
github.com/ory/dockertest/v3/docker/pkg/mount
github.com/ory/dockertest/v3/docker/pkg/pools
github.com/ory/dockertest/v3/docker/pkg/stdcopy
github.com/ory/dockertest/v3/docker/pkg/system
github.com/ory/dockertest/v3/docker/types
github.com/ory/dockertest/v3/docker/types/blkiodev
github.com/ory/dockertest/v3/docker/types/container
github.com/ory/dockertest/v3/docker/types/filters
github.com/ory/dockertest/v3/docker/types/mount
github.com/ory/dockertest/v3/docker/types/network
github.com/ory/dockertest/v3/docker/types/registry
github.com/ory/dockertest/v3/docker/types/strslice
github.com/ory/dockertest/v3/docker/types/versions
# github.com/ory/go-acc v0.2.9-0.20230103102148-6b1c9a70dbbe
## explicit; go 1.19
github.com/ory/go-acc
github.com/ory/go-acc/cmd
# github.com/ory/graceful v0.1.3
## explicit; go 1.14
github.com/ory/graceful
# github.com/ory/herodot v0.9.13
## explicit; go 1.16
github.com/ory/herodot
github.com/ory/herodot/httputil
github.com/ory/herodot/httputil/header
# github.com/ory/jsonschema/v3 v3.0.7
## explicit; go 1.16
github.com/ory/jsonschema/v3
github.com/ory/jsonschema/v3/httploader
# github.com/ory/keto/proto v0.10.0-alpha.0 => ./proto
## explicit; go 1.19
github.com/ory/keto/proto/ory/keto/opl/v1alpha1
github.com/ory/keto/proto/ory/keto/relation_tuples/v1alpha2
# github.com/ory/x v0.0.540
## explicit; go 1.19
github.com/ory/x/castx
github.com/ory/x/clidoc
github.com/ory/x/cmdx
github.com/ory/x/configx
github.com/ory/x/dbal
github.com/ory/x/errorsx
github.com/ory/x/fetcher
github.com/ory/x/flagx
github.com/ory/x/fsx
github.com/ory/x/healthx
github.com/ory/x/httpx
github.com/ory/x/jsonschemax
github.com/ory/x/jsonx
github.com/ory/x/logrusx
github.com/ory/x/metricsx
github.com/ory/x/networkx
github.com/ory/x/osx
github.com/ory/x/otelx
github.com/ory/x/otelx/semconv
github.com/ory/x/otelx/sql
github.com/ory/x/pointerx
github.com/ory/x/popx
github.com/ory/x/profilex
github.com/ory/x/prometheusx
github.com/ory/x/reqlog
github.com/ory/x/resilience
github.com/ory/x/snapshotx
github.com/ory/x/sqlcon
github.com/ory/x/sqlcon/dockertest
github.com/ory/x/stringslice
github.com/ory/x/stringsx
github.com/ory/x/tlsx
github.com/ory/x/urlx
github.com/ory/x/watcherx
# github.com/pelletier/go-toml v1.9.5
## explicit; go 1.12
github.com/pelletier/go-toml
# github.com/pelletier/go-toml/v2 v2.0.6
## explicit; go 1.16
github.com/pelletier/go-toml/v2
github.com/pelletier/go-toml/v2/internal/characters
github.com/pelletier/go-toml/v2/internal/danger
github.com/pelletier/go-toml/v2/internal/tracker
github.com/pelletier/go-toml/v2/unstable
# github.com/phayes/freeport v0.0.0-20220201140144-74d24b5ae9f5
## explicit
github.com/phayes/freeport
# github.com/pkg/errors v0.9.1
## explicit
github.com/pkg/errors
# github.com/pkg/profile v1.7.0
## explicit; go 1.13
github.com/pkg/profile
# github.com/pmezard/go-difflib v1.0.0
## explicit
github.com/pmezard/go-difflib/difflib
# github.com/prometheus/client_golang v1.14.0
## explicit; go 1.17
github.com/prometheus/client_golang/prometheus
github.com/prometheus/client_golang/prometheus/internal
github.com/prometheus/client_golang/prometheus/promhttp
# github.com/prometheus/client_model v0.3.0
## explicit; go 1.9
github.com/prometheus/client_model/go
# github.com/prometheus/common v0.39.0
## explicit; go 1.17
github.com/prometheus/common/expfmt
github.com/prometheus/common/internal/bitbucket.org/ww/goautoneg
github.com/prometheus/common/model
# github.com/prometheus/procfs v0.9.0
## explicit; go 1.18
github.com/prometheus/procfs
github.com/prometheus/procfs/internal/fs
github.com/prometheus/procfs/internal/util
# github.com/rogpeppe/go-internal v1.9.0
## explicit; go 1.17
github.com/rogpeppe/go-internal/modfile
github.com/rogpeppe/go-internal/module
github.com/rogpeppe/go-internal/semver
# github.com/rs/cors v1.8.3
## explicit; go 1.13
github.com/rs/cors
# github.com/samber/lo v1.37.0
## explicit; go 1.18
github.com/samber/lo
# github.com/seatgeek/logrus-gelf-formatter v0.0.0-20210414080842-5b05eb8ff761
## explicit
github.com/seatgeek/logrus-gelf-formatter
# github.com/segmentio/backo-go v1.0.1
## explicit; go 1.13
github.com/segmentio/backo-go
# github.com/segmentio/objconv v1.0.1
## explicit
github.com/segmentio/objconv
github.com/segmentio/objconv/objutil
github.com/segmentio/objconv/yaml
# github.com/sergi/go-diff v1.3.1
## explicit; go 1.12
github.com/sergi/go-diff/diffmatchpatch
# github.com/sirupsen/logrus v1.9.0
## explicit; go 1.13
github.com/sirupsen/logrus
github.com/sirupsen/logrus/hooks/test
# github.com/soheilhy/cmux v0.1.5
## explicit; go 1.11
github.com/soheilhy/cmux
# github.com/sourcegraph/annotate v0.0.0-20160123013949-f4cad6c6324d
## explicit
github.com/sourcegraph/annotate
# github.com/sourcegraph/syntaxhighlight v0.0.0-20170531221838-bd320f5d308e
## explicit
github.com/sourcegraph/syntaxhighlight
# github.com/spf13/afero v1.9.3
## explicit; go 1.16
github.com/spf13/afero
github.com/spf13/afero/internal/common
github.com/spf13/afero/mem
# github.com/spf13/cast v1.5.0
## explicit; go 1.18
github.com/spf13/cast
# github.com/spf13/cobra v1.6.1
## explicit; go 1.15
github.com/spf13/cobra
# github.com/spf13/jwalterweatherman v1.1.0
## explicit
github.com/spf13/jwalterweatherman
# github.com/spf13/pflag v1.0.5
## explicit; go 1.12
github.com/spf13/pflag
# github.com/spf13/viper v1.15.0
## explicit; go 1.17
github.com/spf13/viper
github.com/spf13/viper/internal/encoding
github.com/spf13/viper/internal/encoding/dotenv
github.com/spf13/viper/internal/encoding/hcl
github.com/spf13/viper/internal/encoding/ini
github.com/spf13/viper/internal/encoding/javaproperties
github.com/spf13/viper/internal/encoding/json
github.com/spf13/viper/internal/encoding/toml
github.com/spf13/viper/internal/encoding/yaml
# github.com/stretchr/testify v1.8.1
## explicit; go 1.13
github.com/stretchr/testify/assert
github.com/stretchr/testify/require
# github.com/subosito/gotenv v1.4.2
## explicit; go 1.18
github.com/subosito/gotenv
# github.com/tidwall/gjson v1.14.4
## explicit; go 1.12
github.com/tidwall/gjson
# github.com/tidwall/match v1.1.1
## explicit; go 1.15
github.com/tidwall/match
# github.com/tidwall/pretty v1.2.1
## explicit; go 1.16
github.com/tidwall/pretty
# github.com/tidwall/sjson v1.2.5
## explicit; go 1.14
github.com/tidwall/sjson
# github.com/urfave/negroni v1.0.0
## explicit
github.com/urfave/negroni
# github.com/xeipuuv/gojsonpointer v0.0.0-20190905194746-02993c407bfb
## explicit
github.com/xeipuuv/gojsonpointer
# github.com/xeipuuv/gojsonreference v0.0.0-20180127040603-bd5ef7bd5415
## explicit
github.com/xeipuuv/gojsonreference
# github.com/xeipuuv/gojsonschema v1.2.0
## explicit
github.com/xeipuuv/gojsonschema
# github.com/xtgo/uuid v0.0.0-20140804021211-a0b114877d4c
## explicit
github.com/xtgo/uuid
# go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc v0.38.0
## explicit; go 1.18
go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc
go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc/internal
# go.opentelemetry.io/contrib/instrumentation/net/http/httptrace/otelhttptrace v0.38.0
## explicit; go 1.18
go.opentelemetry.io/contrib/instrumentation/net/http/httptrace/otelhttptrace
# go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.39.0
## explicit; go 1.18
go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp
# go.opentelemetry.io/contrib/propagators/b3 v1.13.0
## explicit; go 1.18
go.opentelemetry.io/contrib/propagators/b3
# go.opentelemetry.io/contrib/propagators/jaeger v1.13.0
## explicit; go 1.18
go.opentelemetry.io/contrib/propagators/jaeger
# go.opentelemetry.io/contrib/samplers/jaegerremote v0.7.0
## explicit; go 1.18
go.opentelemetry.io/contrib/samplers/jaegerremote
go.opentelemetry.io/contrib/samplers/jaegerremote/internal/proto-gen/jaeger-idl/proto/api_v2
go.opentelemetry.io/contrib/samplers/jaegerremote/internal/utils
# go.opentelemetry.io/otel v1.13.0
## explicit; go 1.18
go.opentelemetry.io/otel
go.opentelemetry.io/otel/attribute
go.opentelemetry.io/otel/baggage
go.opentelemetry.io/otel/codes
go.opentelemetry.io/otel/exporters/otlp/internal
go.opentelemetry.io/otel/exporters/otlp/internal/envconfig
go.opentelemetry.io/otel/internal
go.opentelemetry.io/otel/internal/attribute
go.opentelemetry.io/otel/internal/baggage
go.opentelemetry.io/otel/internal/global
go.opentelemetry.io/otel/propagation
go.opentelemetry.io/otel/semconv/internal
go.opentelemetry.io/otel/semconv/internal/v2
go.opentelemetry.io/otel/semconv/v1.17.0
go.opentelemetry.io/otel/semconv/v1.17.0/httpconv
go.opentelemetry.io/otel/semconv/v1.17.0/netconv
go.opentelemetry.io/otel/semconv/v1.7.0
# go.opentelemetry.io/otel/exporters/jaeger v1.12.0
## explicit; go 1.18
go.opentelemetry.io/otel/exporters/jaeger
go.opentelemetry.io/otel/exporters/jaeger/internal/gen-go/agent
go.opentelemetry.io/otel/exporters/jaeger/internal/gen-go/jaeger
go.opentelemetry.io/otel/exporters/jaeger/internal/gen-go/zipkincore
go.opentelemetry.io/otel/exporters/jaeger/internal/third_party/thrift/lib/go/thrift
# go.opentelemetry.io/otel/exporters/otlp/internal/retry v1.12.0
## explicit; go 1.18
go.opentelemetry.io/otel/exporters/otlp/internal/retry
# go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.12.0
## explicit; go 1.18
go.opentelemetry.io/otel/exporters/otlp/otlptrace
go.opentelemetry.io/otel/exporters/otlp/otlptrace/internal/otlpconfig
go.opentelemetry.io/otel/exporters/otlp/otlptrace/internal/tracetransform
# go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp v1.12.0
## explicit; go 1.18
go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp
# go.opentelemetry.io/otel/exporters/zipkin v1.12.0
## explicit; go 1.18
go.opentelemetry.io/otel/exporters/zipkin
# go.opentelemetry.io/otel/metric v0.36.0
## explicit; go 1.18
go.opentelemetry.io/otel/metric
go.opentelemetry.io/otel/metric/global
go.opentelemetry.io/otel/metric/instrument
go.opentelemetry.io/otel/metric/internal/global
go.opentelemetry.io/otel/metric/unit
# go.opentelemetry.io/otel/sdk v1.12.0
## explicit; go 1.18
go.opentelemetry.io/otel/sdk/instrumentation
go.opentelemetry.io/otel/sdk/internal
go.opentelemetry.io/otel/sdk/internal/env
go.opentelemetry.io/otel/sdk/resource
go.opentelemetry.io/otel/sdk/trace
go.opentelemetry.io/otel/sdk/trace/tracetest
# go.opentelemetry.io/otel/trace v1.13.0
## explicit; go 1.18
go.opentelemetry.io/otel/trace
# go.opentelemetry.io/proto/otlp v0.19.0
## explicit; go 1.14
go.opentelemetry.io/proto/otlp/collector/trace/v1
go.opentelemetry.io/proto/otlp/common/v1
go.opentelemetry.io/proto/otlp/resource/v1
go.opentelemetry.io/proto/otlp/trace/v1
# go.uber.org/goleak v1.2.1
## explicit; go 1.18
go.uber.org/goleak
go.uber.org/goleak/internal/stack
# golang.org/x/crypto v0.5.0
## explicit; go 1.17
golang.org/x/crypto/pbkdf2
# golang.org/x/exp v0.0.0-20230131160201-f062dba9d201
## explicit; go 1.18
golang.org/x/exp/constraints
golang.org/x/exp/maps
golang.org/x/exp/slices
# golang.org/x/mod v0.7.0
## explicit; go 1.17
golang.org/x/mod/internal/lazyregexp
golang.org/x/mod/module
golang.org/x/mod/semver
# golang.org/x/net v0.7.0
## explicit; go 1.17
golang.org/x/net/context
golang.org/x/net/html
golang.org/x/net/html/atom
golang.org/x/net/http/httpguts
golang.org/x/net/http2
golang.org/x/net/http2/hpack
golang.org/x/net/idna
golang.org/x/net/internal/socks
golang.org/x/net/internal/timeseries
golang.org/x/net/proxy
golang.org/x/net/trace
# golang.org/x/oauth2 v0.5.0
## explicit; go 1.17
golang.org/x/oauth2
golang.org/x/oauth2/authhandler
golang.org/x/oauth2/google
golang.org/x/oauth2/google/internal/externalaccount
golang.org/x/oauth2/internal
golang.org/x/oauth2/jws
golang.org/x/oauth2/jwt
# golang.org/x/sync v0.1.0
## explicit
golang.org/x/sync/errgroup
# golang.org/x/sys v0.5.0
## explicit; go 1.17
golang.org/x/sys/execabs
golang.org/x/sys/internal/unsafeheader
golang.org/x/sys/unix
golang.org/x/sys/windows
golang.org/x/sys/windows/registry
# golang.org/x/text v0.7.0
## explicit; go 1.17
golang.org/x/text/cases
golang.org/x/text/internal
golang.org/x/text/internal/format
golang.org/x/text/internal/language
golang.org/x/text/internal/language/compact
golang.org/x/text/internal/tag
golang.org/x/text/language
golang.org/x/text/language/display
golang.org/x/text/runes
golang.org/x/text/secure/bidirule
golang.org/x/text/secure/precis
golang.org/x/text/transform
golang.org/x/text/unicode/bidi
golang.org/x/text/unicode/norm
golang.org/x/text/width
# golang.org/x/tools v0.5.0
## explicit; go 1.18
golang.org/x/tools/cmd/goimports
golang.org/x/tools/cmd/stringer
golang.org/x/tools/go/ast/astutil
golang.org/x/tools/go/gcexportdata
golang.org/x/tools/go/internal/packagesdriver
golang.org/x/tools/go/packages
golang.org/x/tools/internal/event
golang.org/x/tools/internal/event/core
golang.org/x/tools/internal/event/keys
golang.org/x/tools/internal/event/label
golang.org/x/tools/internal/fastwalk
golang.org/x/tools/internal/gcimporter
golang.org/x/tools/internal/gocommand
golang.org/x/tools/internal/gopathwalk
golang.org/x/tools/internal/imports
golang.org/x/tools/internal/packagesinternal
golang.org/x/tools/internal/pkgbits
golang.org/x/tools/internal/typeparams
golang.org/x/tools/internal/typesinternal
# golang.org/x/xerrors v0.0.0-20220907171357-04be3eba64a2
## explicit; go 1.17
golang.org/x/xerrors
golang.org/x/xerrors/internal
# google.golang.org/appengine v1.6.7
## explicit; go 1.11
google.golang.org/appengine
google.golang.org/appengine/internal
google.golang.org/appengine/internal/app_identity
google.golang.org/appengine/internal/base
google.golang.org/appengine/internal/datastore
google.golang.org/appengine/internal/log
google.golang.org/appengine/internal/modules
google.golang.org/appengine/internal/remote_api
google.golang.org/appengine/internal/urlfetch
google.golang.org/appengine/urlfetch
# google.golang.org/genproto v0.0.0-20230131230820-1c016267d619
## explicit; go 1.19
google.golang.org/genproto/googleapis/api
google.golang.org/genproto/googleapis/api/annotations
google.golang.org/genproto/googleapis/api/httpbody
google.golang.org/genproto/googleapis/rpc/errdetails
google.golang.org/genproto/googleapis/rpc/status
google.golang.org/genproto/protobuf/field_mask
# google.golang.org/grpc v1.53.0
## explicit; go 1.17
google.golang.org/grpc
google.golang.org/grpc/attributes
google.golang.org/grpc/backoff
google.golang.org/grpc/balancer
google.golang.org/grpc/balancer/base
google.golang.org/grpc/balancer/grpclb/state
google.golang.org/grpc/balancer/roundrobin
google.golang.org/grpc/binarylog/grpc_binarylog_v1
google.golang.org/grpc/channelz
google.golang.org/grpc/codes
google.golang.org/grpc/connectivity
google.golang.org/grpc/credentials
google.golang.org/grpc/credentials/insecure
google.golang.org/grpc/credentials/oauth
google.golang.org/grpc/encoding
google.golang.org/grpc/encoding/gzip
google.golang.org/grpc/encoding/proto
google.golang.org/grpc/grpclog
google.golang.org/grpc/health
google.golang.org/grpc/health/grpc_health_v1
google.golang.org/grpc/internal
google.golang.org/grpc/internal/backoff
google.golang.org/grpc/internal/balancer/gracefulswitch
google.golang.org/grpc/internal/balancerload
google.golang.org/grpc/internal/binarylog
google.golang.org/grpc/internal/buffer
google.golang.org/grpc/internal/channelz
google.golang.org/grpc/internal/credentials
google.golang.org/grpc/internal/envconfig
google.golang.org/grpc/internal/grpclog
google.golang.org/grpc/internal/grpcrand
google.golang.org/grpc/internal/grpcsync
google.golang.org/grpc/internal/grpcutil
google.golang.org/grpc/internal/metadata
google.golang.org/grpc/internal/pretty
google.golang.org/grpc/internal/resolver
google.golang.org/grpc/internal/resolver/dns
google.golang.org/grpc/internal/resolver/passthrough
google.golang.org/grpc/internal/resolver/unix
google.golang.org/grpc/internal/serviceconfig
google.golang.org/grpc/internal/status
google.golang.org/grpc/internal/syscall
google.golang.org/grpc/internal/transport
google.golang.org/grpc/internal/transport/networktype
google.golang.org/grpc/keepalive
google.golang.org/grpc/metadata
google.golang.org/grpc/peer
google.golang.org/grpc/reflection
google.golang.org/grpc/reflection/grpc_reflection_v1alpha
google.golang.org/grpc/resolver
google.golang.org/grpc/serviceconfig
google.golang.org/grpc/stats
google.golang.org/grpc/status
google.golang.org/grpc/tap
google.golang.org/grpc/test/bufconn
# google.golang.org/protobuf v1.28.1
## explicit; go 1.11
google.golang.org/protobuf/encoding/protojson
google.golang.org/protobuf/encoding/prototext
google.golang.org/protobuf/encoding/protowire
google.golang.org/protobuf/internal/descfmt
google.golang.org/protobuf/internal/descopts
google.golang.org/protobuf/internal/detrand
google.golang.org/protobuf/internal/encoding/defval
google.golang.org/protobuf/internal/encoding/json
google.golang.org/protobuf/internal/encoding/messageset
google.golang.org/protobuf/internal/encoding/tag
google.golang.org/protobuf/internal/encoding/text
google.golang.org/protobuf/internal/errors
google.golang.org/protobuf/internal/filedesc
google.golang.org/protobuf/internal/filetype
google.golang.org/protobuf/internal/flags
google.golang.org/protobuf/internal/genid
google.golang.org/protobuf/internal/impl
google.golang.org/protobuf/internal/order
google.golang.org/protobuf/internal/pragma
google.golang.org/protobuf/internal/set
google.golang.org/protobuf/internal/strs
google.golang.org/protobuf/internal/version
google.golang.org/protobuf/proto
google.golang.org/protobuf/reflect/protodesc
google.golang.org/protobuf/reflect/protoreflect
google.golang.org/protobuf/reflect/protoregistry
google.golang.org/protobuf/runtime/protoiface
google.golang.org/protobuf/runtime/protoimpl
google.golang.org/protobuf/types/descriptorpb
google.golang.org/protobuf/types/known/anypb
google.golang.org/protobuf/types/known/durationpb
google.golang.org/protobuf/types/known/fieldmaskpb
google.golang.org/protobuf/types/known/structpb
google.golang.org/protobuf/types/known/timestamppb
google.golang.org/protobuf/types/known/wrapperspb
# gopkg.in/ini.v1 v1.67.0
## explicit
gopkg.in/ini.v1
# gopkg.in/yaml.v2 v2.4.0
## explicit; go 1.15
gopkg.in/yaml.v2
# gopkg.in/yaml.v3 v3.0.1
## explicit
gopkg.in/yaml.v3
# github.com/ory/keto/proto => ./proto
