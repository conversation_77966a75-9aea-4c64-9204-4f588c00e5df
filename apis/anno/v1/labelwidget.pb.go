// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anno/v1/labelwidget.proto

package anno

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListLabelwidgetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListLabelwidgetRequest) Reset() {
	*x = ListLabelwidgetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_labelwidget_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLabelwidgetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLabelwidgetRequest) ProtoMessage() {}

func (x *ListLabelwidgetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_labelwidget_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLabelwidgetRequest.ProtoReflect.Descriptor instead.
func (*ListLabelwidgetRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_labelwidget_proto_rawDescGZIP(), []int{0}
}

type ListLabelwidgetReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total   int32          `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Widgets []*Labelwidget `protobuf:"bytes,2,rep,name=widgets,proto3" json:"widgets,omitempty"`
}

func (x *ListLabelwidgetReply) Reset() {
	*x = ListLabelwidgetReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_labelwidget_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLabelwidgetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLabelwidgetReply) ProtoMessage() {}

func (x *ListLabelwidgetReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_labelwidget_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLabelwidgetReply.ProtoReflect.Descriptor instead.
func (*ListLabelwidgetReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_labelwidget_proto_rawDescGZIP(), []int{1}
}

func (x *ListLabelwidgetReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListLabelwidgetReply) GetWidgets() []*Labelwidget {
	if x != nil {
		return x.Widgets
	}
	return nil
}

type Labelwidget struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// language => name
	Langs map[string]string `protobuf:"bytes,2,rep,name=langs,proto3" json:"langs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Labelwidget) Reset() {
	*x = Labelwidget{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_labelwidget_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Labelwidget) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Labelwidget) ProtoMessage() {}

func (x *Labelwidget) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_labelwidget_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Labelwidget.ProtoReflect.Descriptor instead.
func (*Labelwidget) Descriptor() ([]byte, []int) {
	return file_anno_v1_labelwidget_proto_rawDescGZIP(), []int{2}
}

func (x *Labelwidget) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Labelwidget) GetLangs() map[string]string {
	if x != nil {
		return x.Langs
	}
	return nil
}

var File_anno_v1_labelwidget_proto protoreflect.FileDescriptor

var file_anno_v1_labelwidget_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x18, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x5c, 0x0a, 0x14,
	0x4c, 0x69, 0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2e, 0x0a, 0x07, 0x77, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x52, 0x07, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x22, 0x92, 0x01, 0x0a, 0x0b, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x35,
	0x0a, 0x05, 0x6c, 0x61, 0x6e, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x2e, 0x4c, 0x61, 0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05,
	0x6c, 0x61, 0x6e, 0x67, 0x73, 0x1a, 0x38, 0x0a, 0x0a, 0x4c, 0x61, 0x6e, 0x67, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32,
	0x7b, 0x0a, 0x0c, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x12,
	0x6b, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x77, 0x69, 0x64, 0x67,
	0x65, 0x74, 0x12, 0x1f, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f, 0x76, 0x31, 0x2f,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x42, 0x3e, 0x0a, 0x07,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x31, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72,
	0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e, 0x6e, 0x6f, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_anno_v1_labelwidget_proto_rawDescOnce sync.Once
	file_anno_v1_labelwidget_proto_rawDescData = file_anno_v1_labelwidget_proto_rawDesc
)

func file_anno_v1_labelwidget_proto_rawDescGZIP() []byte {
	file_anno_v1_labelwidget_proto_rawDescOnce.Do(func() {
		file_anno_v1_labelwidget_proto_rawDescData = protoimpl.X.CompressGZIP(file_anno_v1_labelwidget_proto_rawDescData)
	})
	return file_anno_v1_labelwidget_proto_rawDescData
}

var file_anno_v1_labelwidget_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_anno_v1_labelwidget_proto_goTypes = []interface{}{
	(*ListLabelwidgetRequest)(nil), // 0: anno.v1.ListLabelwidgetRequest
	(*ListLabelwidgetReply)(nil),   // 1: anno.v1.ListLabelwidgetReply
	(*Labelwidget)(nil),            // 2: anno.v1.Labelwidget
	nil,                            // 3: anno.v1.Labelwidget.LangsEntry
}
var file_anno_v1_labelwidget_proto_depIdxs = []int32{
	2, // 0: anno.v1.ListLabelwidgetReply.widgets:type_name -> anno.v1.Labelwidget
	3, // 1: anno.v1.Labelwidget.langs:type_name -> anno.v1.Labelwidget.LangsEntry
	0, // 2: anno.v1.Labelwidgets.ListLabelwidget:input_type -> anno.v1.ListLabelwidgetRequest
	1, // 3: anno.v1.Labelwidgets.ListLabelwidget:output_type -> anno.v1.ListLabelwidgetReply
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_anno_v1_labelwidget_proto_init() }
func file_anno_v1_labelwidget_proto_init() {
	if File_anno_v1_labelwidget_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_anno_v1_labelwidget_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLabelwidgetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_labelwidget_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLabelwidgetReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_labelwidget_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Labelwidget); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anno_v1_labelwidget_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_anno_v1_labelwidget_proto_goTypes,
		DependencyIndexes: file_anno_v1_labelwidget_proto_depIdxs,
		MessageInfos:      file_anno_v1_labelwidget_proto_msgTypes,
	}.Build()
	File_anno_v1_labelwidget_proto = out.File
	file_anno_v1_labelwidget_proto_rawDesc = nil
	file_anno_v1_labelwidget_proto_goTypes = nil
	file_anno_v1_labelwidget_proto_depIdxs = nil
}
