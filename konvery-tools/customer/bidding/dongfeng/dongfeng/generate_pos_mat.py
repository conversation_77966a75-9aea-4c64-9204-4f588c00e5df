import os
import numpy as np
import yaml
from scipy.spatial.transform import Rotation
from glob import glob
import json

def remap_angle(angle):
    if angle > 180:
        angle -= 360
    elif angle < -180:
        angle += 360

    return angle

def read_best_position(file_path):
    # 读取yaml文件内容
    with open(file_path, 'r') as f:
        data = yaml.safe_load(f)

    # 提取最佳位置信息，单位米
    guassian_x = data['gaussX'] / 100
    guassian_y = data['gaussY'] / 100
    height = data['height']

    # 提取姿态角，单位度
    roll = data['roll'] / 100
    pitch = data['pitch'] / 100
    yaw = data['azimuth'] / 100

    roll = remap_angle(roll)
    pitch = remap_angle(pitch)
    yaw = remap_angle(yaw)

    output_dict = {'guassian_x': guassian_x, 'guassian_y': guassian_y, 'height': height,
                'roll': roll, 'pitch': pitch, 'yaw': yaw}

    return output_dict

def get_transformation_matrix(guassian_info: dict, origin_translation: np.array):
    r_z = Rotation.from_euler('z', guassian_info['yaw'], degrees=True)
    r_y = Rotation.from_euler('y', guassian_info['pitch'], degrees=True)
    r_x = Rotation.from_euler('x', guassian_info['roll'], degrees=True)

    rotation = (r_x * r_y * r_z).as_matrix()
    # rotation = r_z.as_matrix()

    translation = np.array([guassian_info['guassian_x'], 
                            guassian_info['guassian_y'], 
                            guassian_info['height']]) - origin_translation
    
    # 合并为4x4矩阵
    transformation_matrix = np.eye(4)
    transformation_matrix[:3, :3] = rotation.T
    transformation_matrix[:3, 3] = translation
    return transformation_matrix


if __name__ == '__main__':
    root = "/Users/<USER>/work/data/Dongfeng/raw/中海庭4D试标数据/4d_test/yunce_lh_1_20231121_chunsun_part1/"
    best_position_dir = root + '2045_bus_16_NULL_NULL_NULL_2023-11-21-14-49-55_23/bestposition'
    output_file = 'output_poses.json'

    # 暂时用第一帧自车高斯坐标作为原点
    origin_translation = np.array([3370356.95, 8308.92, 28.3660283023])

    best_posision_files = glob(os.path.join(best_position_dir, '*.yaml'))

    json_data = {'poses': {}}
    for idx, file in enumerate(best_posision_files):
        guassian_info = read_best_position(file)
        transformation_matrix = get_transformation_matrix(guassian_info, origin_translation)

        json_data['poses'][str(idx)] = transformation_matrix.T.flatten().tolist()

    # 保存json
    with open(output_file, 'w') as f:
        json.dump(json_data, f, indent=4)

