package lotwf

import (
	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/kmath"
)

// aggCamParams tries to aggregate the camera params.
func aggCamParams(elems []*anno.Element) (camParams map[string]*anno.Job_CamParam) {
	compareRawdataParams := func(src, dst []*anno.RawdataParam, idxs ...int) (same bool) {
		if len(src) != len(dst) {
			return false
		}

		for _, i := range idxs {
			srcParam := src[i]
			dstParam := dst[i]
			if srcParam.Type != dstParam.Type {
				return false
			}
			if srcParam.ColumnCnt != dstParam.ColumnCnt {
				return false
			}
			if !kslice.EqualInDelta(srcParam.Data, dstParam.Data, 10e-4) {
				return false
			}
		}
		return true
	}

	removeParams := func(elems []*anno.Element, camParams map[string]*anno.Job_CamParam, paramIdxList [][]int) {
		for _, elem := range elems {
			for rdIdx, rawdata := range elem.Datas {
				paramIdx := paramIdxList[rdIdx]
				if len(paramIdx) == 0 {
					continue
				}

				camera := rawdata.Meta.Image.Camera
				if camera == "" {
					camera = rawdata.Title // title is always not empty according to Annofeed
					rawdata.Meta.Image.Camera = camera
				}

				if camParams[camera] == nil {
					camParams[camera] = &anno.Job_CamParam{}
				}
				var newTransforms []*anno.RawdataParam
				idx := 0
				for i, transform := range rawdata.Transform { // remove some params from original elems if possible
					if idx < len(paramIdx) && i == paramIdx[idx] {
						idx += 1

						switch transform.Type {
						case anno.RawdataParam_Type_matrix:
							pose := kmath.MatrixToPose(transform.Data, true)
							camParams[camera].Extrinsic = pose[:]
						case anno.RawdataParam_Type_extrinsic:
							camParams[camera].Extrinsic = transform.Data
						case anno.RawdataParam_Type_intrinsic:
							camParams[camera].Intrinsic = transform.Data
						case anno.RawdataParam_Type_distortion:
							camParams[camera].Distortion = transform.Data
						}

						continue
					}
					newTransforms = append(newTransforms, transform)
				}
				rawdata.Transform = newTransforms
			}
		}
	}

	/*
	 Note: 比较映射参数，包括内参、畸变参数以及映射参数中除了这两个的最后一个参数。如果存在一个不同，那就把映射参数放到帧里面，否则放到job里。
	*/

	// 比较内参和畸变参数，如果所有帧都相同就从帧里面面把这些参数去除，否则就直接返回。
	baseElem := elems[0]
	paramIdxList := make([][]int, len(baseElem.Datas))
	for _, elem := range elems[1:] {
		if len(baseElem.Datas) != len(elem.Datas) { // rawdata 数量不相同
			return nil
		}

		for rdIdx, rawdata := range elem.Datas {
			if rawdata.Type != anno.Rawdata_Type_image {
				continue
			}

			paramIdx := paramIdxList[rdIdx]
			if paramIdx == nil {
				paramIdx = []int{}
				for i, trans := range rawdata.Transform {
					if trans.Type == anno.RawdataParam_Type_intrinsic || trans.Type == anno.RawdataParam_Type_distortion {
						paramIdx = append(paramIdx, i)
					}
				}
				paramIdxList[rdIdx] = paramIdx
			}

			if len(paramIdx) > 0 {
				baseRawdata := baseElem.Datas[rdIdx]
				if !compareRawdataParams(baseRawdata.Transform, rawdata.Transform, paramIdx...) {
					return nil
				}
			}
		}
	}

	camParams = make(map[string]*anno.Job_CamParam)
	removeParams(elems, camParams, paramIdxList)

	// 比较除了内参和畸变的最后一个映射参数
	hasDifference := false
	paramIdxList = make([][]int, len(baseElem.Datas))
outer:
	for _, elem := range elems[1:] {
		for rdIdx, rawdata := range elem.Datas {
			if rawdata.Type != anno.Rawdata_Type_image {
				continue
			}

			baseRawdata := baseElem.Datas[rdIdx]
			if len(baseRawdata.Transform) == 0 {
				continue
			}

			if !compareRawdataParams(baseRawdata.Transform, rawdata.Transform, len(baseRawdata.Transform)-1) {
				hasDifference = true
				break outer
			}
			paramIdxList[rdIdx] = []int{len(baseRawdata.Transform) - 1}
		}
	}
	if !hasDifference {
		removeParams(elems, camParams, paramIdxList)
	}

	return lo.Ternary(len(camParams) == 0, nil, camParams)
}
