package biz

import (
	"context"
	"time"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
)

type Meta struct {
	ID        int64     `json:"id" gorm:"default:null"`
	Name      string    `json:"name" gorm:"default:null"`
	Value     string    `json:"value" gorm:"default:null"`
	CreatedAt time.Time `json:"created_at" gorm:"default:null"`
}

func (o *Meta) GetUid() string   { return o.Name }
func (o *Meta) UidField() string { return Meta_Name }

const MetaTableName = "meta"

var (
	meta_             = field.RegObject(&Meta{})
	MetaUpdatableFlds = field.NewModel(MetaTableName, meta_)

	Meta_Name  = field.Sname(&meta_.Name)
	Meta_Value = field.Sname(&meta_.Value)
)

const (
	MetaNameAutoCreateRoot = "autoCreateRoot"
	MetaNameTrafficControl = "trafficControl" // traffic control configuration

	MetaValueTrue  = "true"
	MetaValueFalse = "false"
)

type MetasRepo interface {
	Get(ctx context.Context, name string) (string, error)
	Is(ctx context.Context, name, value string) (bool, error)
	Set(ctx context.Context, name, value string) error
	Delete(ctx context.Context, name string) error
}
