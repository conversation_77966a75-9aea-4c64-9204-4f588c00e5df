// source: annostat/v1/order.proto
package biz

import (
	"time"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/kid"
)

type OrderState string

func (o OrderState) IsFinal() bool  { return o == OrderStateFinished || o == OrderStateCanceled }
func (o OrderState) String() string { return string(o) }

const (
	OrderStateUnspecified  OrderState = "unspecified_state"
	OrderStateInitializing OrderState = "initializing" // preparing data
	OrderStateOngoing      OrderState = "ongoing"
	OrderStatePaused       OrderState = "paused"
	OrderStateFinished     OrderState = "finished"
	OrderStateCanceled     OrderState = "canceled"
)

type Order struct {
	ID         int64      `json:"id" gorm:"default:null"`
	Name       string     `json:"name" gorm:"default:null"`
	DataUid    string     `json:"data_uid" gorm:"default:null"`
	Size       int32      `json:"size" gorm:"default:null"`
	State      OrderState `json:"state" gorm:"default:null"`
	InsTotal   int32      `json:"ins_total" gorm:"default:null"` // include interpolated objects
	CreatorUid string     `json:"creator_uid" gorm:"default:null"`
	OrgUid     string     `json:"org_uid" gorm:"default:null"`
	FinishedAt time.Time  `json:"finished_at" gorm:"default:null"`
	CreatedAt  time.Time  `json:"created_at" gorm:"default:null"`
}

func (o *Order) GetID() int64   { return o.ID }
func (o *Order) GetUid() string { return kid.StringID(o.ID) }
func (o *Order) UidFld() string { panic("code error") }

const OrderTableName = "orders"

var (
	order_ = field.RegObject(&Order{})
	// OrderUpdatableFlds = field.NewModel(order_, "Name", "State", "Size", "DataUid", "InsTotal", "AnnoResultURL")

	OrderSfldName       = field.Sname(&order_.Name)
	OrderSfldSize       = field.Sname(&order_.Size)
	OrderSfldState      = field.Sname(&order_.State)
	OrderSfldInsTotal   = field.Sname(&order_.InsTotal)
	OrderSfldDataUid    = field.Sname(&order_.DataUid)
	OrderSfldOrgUid     = field.Sname(&order_.OrgUid)
	OrderSfldCreatorUid = field.Sname(&order_.CreatorUid)
	OrderSfldCreatedAt  = field.Sname(&order_.CreatedAt)
)

type OrderListFilter struct {
	TimeRange
	IDs         []int64
	OrgUids     []string
	CreatorUid  string
	NamePattern string
	States      []OrderState

	BizgranteeUid string // to check kam's grants
}

func (o *OrderListFilter) Apply(tx Tx) Tx {
	if o == nil {
		return tx
	}
	tbl := "orders."
	tx = ApplyFieldFilter(tx, tbl+"id", o.IDs)
	tx = ApplyFieldFilter(tx, tbl+OrderSfldOrgUid, o.OrgUids)
	tx = ApplyFieldFilter(tx, tbl+OrderSfldCreatorUid, o.CreatorUid)
	tx = ApplyFieldFilter(tx, tbl+OrderSfldState, o.States)
	tx = ApplyPatternFilter(tx, tbl+OrderSfldName, o.NamePattern)
	tx = ApplyTimeRangeFilter(tx, tbl+OrderSfldCreatedAt, &o.TimeRange)

	if o.BizgranteeUid != "" { // check kam's grants: join on bizgrants table
		f := &JoinFilter{
			LeftModel:    &Order{},
			LeftJoinFld:  OrderSfldOrgUid,
			RightModel:   &Bizgrant{},
			RightJoinFld: BizgrantSfldOrgUid.String(),
			Filter:       &BizgrantFilter{GranteeUid: o.BizgranteeUid},
		}
		tx = f.Apply(tx)
	}

	return tx
}

type OrdersRepo interface {
	repo.GenericRepo[Order]
}

// type OrdersBiz struct {
// 	repo OrdersRepo
// 	log  *log.Helper
// }

// func NewOrdersBiz(repo OrdersRepo, logger log.Logger) *OrdersBiz {
// 	return &OrdersBiz{repo: repo, log: log.NewHelper(logger)}
// }
