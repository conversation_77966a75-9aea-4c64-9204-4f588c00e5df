// source: gitlab.rp.konvery.work/platform/apis/iam/v1/role.proto
package service

import (
	"context"

	"iam/internal/biz"
	"iam/pkg/keto"

	"gitlab.rp.konvery.work/platform/apis/iam/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"

	"google.golang.org/protobuf/types/known/emptypb"
)

func FromBizRole(d *biz.Role) *iam.Role {
	if d == nil {
		return nil
	}
	o := &iam.Role{
		Name:        d.Name,
		DisplayName: d.DisplayName,
		Perms:       d.Perms,
	}
	return o
}

func ToBizRole(d *iam.Role) *biz.Role {
	if d == nil {
		return nil
	}
	o := &biz.Role{
		Name:        d.Name,
		DisplayName: d.<PERSON>lay<PERSON>ame,
		Perms:       d.<PERSON><PERSON>,
	}
	return o
}

type RolesService struct {
	iam.UnimplementedRolesServer
	bz   *biz.RolesBiz
	repo biz.RolesRepo
}

func NewRolesService(bz *biz.RolesBiz, repo biz.RolesRepo) *RolesService {
	return &RolesService{bz: bz, repo: repo}
}

func (o *RolesService) fixRoleName(op *biz.User, name string) (parent, scope, newName string) {
	newName = name
	scope, _ = keto.ParseObjectName(name)
	if scope != "" {
		newName = keto.EscapeName(name)
	}
	if op.OrgID != 0 && scope == "" && !op.IsPrivileged() {
		scope = kid.StringID(op.OrgID)
		// it is an org internal role, prefix its name with org uid
		newName = scope + "." + name
	}
	if scope != "" {
		parent = scope
		scope = keto.MakeScopeObj(keto.GroupNs, scope)
	}
	return
}

func (o *RolesService) CreateRole(ctx context.Context, req *iam.Role) (*iam.Role, error) {
	op := biz.UserFromCtx(ctx)
	parent, scope, name := o.fixRoleName(op.User, req.Name)
	req.Name = name
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermCreate, keto.MakeRoleName(scope)) {
		return nil, errors.NewErrForbidden()
	}
	role := ToBizRole(req)
	if parent != "" {
		role.OrgID = kid.ParseID(parent)
	}
	data, err := o.bz.Create(ctx, role)
	return FromBizRole(data), err
}

func (o *RolesService) UpdateRole(ctx context.Context, req *iam.UpdateRoleRequest) (*emptypb.Empty, error) {
	op := biz.UserFromCtx(ctx)
	// _, _, req.Name = o.fixRoleName(op, req.Name)
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermUpdate, keto.MakeRoleName(req.Name)) {
		return nil, errors.NewErrForbidden()
	}

	if req.Action == iam.EditAction_add {
		return nil, keto.DefaultAccessMgr().AddRolePerms(ctx, req.Name, req.Perms)
	}
	return &emptypb.Empty{}, keto.DefaultAccessMgr().DeleteRolePerms(ctx, req.Name, req.Perms)
}

func (o *RolesService) DeleteRole(ctx context.Context, req *iam.DeleteRoleRequest) (*emptypb.Empty, error) {
	op := biz.UserFromCtx(ctx)
	// _, _, req.Name = o.fixRoleName(op, req.Name)
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermDelete, keto.MakeRoleName(req.Name)) {
		return nil, errors.NewErrForbidden()
	}

	return &emptypb.Empty{}, o.bz.DeleteByUid(ctx, req.Name)
}

func (o *RolesService) GetRole(ctx context.Context, req *iam.GetRoleRequest) (*iam.GetRoleReply, error) {
	op := biz.UserFromCtx(ctx)
	// _, _, req.Name = o.fixRoleName(op, req.Name)
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermGet, keto.MakeRoleName(req.Name)) {
		return nil, errors.NewErrForbidden()
	}

	data, err := o.bz.GetByUid(ctx, req.Name)
	if err != nil {
		return nil, err
	}
	data.Perms, err = keto.DefaultAccessMgr().GetRole(ctx, req.Name)
	if err != nil {
		return nil, err
	}
	return &iam.GetRoleReply{
		Name:        data.Name,
		DisplayName: data.DisplayName,
		Perms:       data.Perms,
	}, nil
}

func (o *RolesService) ListRole(ctx context.Context, req *iam.ListRoleRequest) (*iam.ListRoleReply, error) {
	op := biz.UserFromCtx(ctx)
	scope := req.OrgUid
	if op.OrgID != 0 && scope == "" && !op.IsPrivileged() {
		scope = kid.StringID(op.OrgID)
		req.OrgUid = scope
		scope = keto.MakeScopeObj(keto.GroupNs, scope)
	}

	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermList, keto.MakeRoleName(scope)) {
		return nil, errors.NewErrForbidden()
	}

	pager := biz.Pager{
		Pagesz:    int(req.Pagesz),
		PageToken: biz.PageToken(req.PageToken),
	}
	filter := &biz.RoleListFilter{
		// Names:   req.Uids,
		OrgID:       kid.ParseID(req.OrgUid),
		NamePattern: req.NamePattern,
	}

	datas, nextPageToken, err := o.bz.List(ctx, filter, pager)
	if err != nil {
		return nil, err
	}
	return &iam.ListRoleReply{NextPageToken: nextPageToken.String(), Roles: kslice.Map(FromBizRole, datas)}, nil
}

func (o *RolesService) GetRoleFeperm(ctx context.Context, req *iam.GetRoleRequest) (*iam.GetRoleFepermReply, error) {
	op := biz.UserFromCtx(ctx)
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermGet, keto.MakeRoleName(req.Name)) {
		return nil, errors.NewErrForbidden()
	}

	perms, err := o.repo.GetFeperm(ctx, req.Name)
	if err != nil {
		return nil, err
	}
	return &iam.GetRoleFepermReply{Perms: perms}, nil
}

func (o *RolesService) SetRoleFeperm(ctx context.Context, req *iam.SetRoleFepermRequest) (*emptypb.Empty, error) {
	op := biz.UserFromCtx(ctx)
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermCreate, keto.MakeRoleName(req.Name)) {
		return nil, errors.NewErrForbidden()
	}

	return &emptypb.Empty{}, o.repo.SetFeperm(ctx, req.Name, req.Perms)
}
