# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
security:
    - jwt: []
paths:
    /iam/v1/bizgrants:
        get:
            tags:
                - Bizgrants
            operationId: Bizgrants_ListBizgrant
            parameters:
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: |-
                    An opaque pagination token returned from a previous call.
                     An empty token denotes the first page.
                  schema:
                    type: string
                - name: filter.grantor_uid
                  in: query
                  description: grantor uid
                  schema:
                    type: string
                - name: filter.grantee_uid
                  in: query
                  description: grantee uid
                  schema:
                    type: string
                - name: filter.org_uid
                  in: query
                  description: uid of the orgnization whose business permissions are granted
                  schema:
                    type: string
                - name: filter.biz
                  in: query
                  description: business scope granted
                  schema:
                    enum:
                        - unspecified
                        - anno
                    type: string
                    format: enum
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.ListBizgrantReply'
        post:
            tags:
                - Bizgrants
            operationId: Bizgrants_CreateBizgrant
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/iam.v1.CreateBizgrantRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.CreateBizgrantReply'
        delete:
            tags:
                - Bizgrants
            operationId: Bizgrants_DeleteBizgrant
            parameters:
                - name: filter.grantor_uid
                  in: query
                  description: grantor uid
                  schema:
                    type: string
                - name: filter.grantee_uid
                  in: query
                  description: grantee uid
                  schema:
                    type: string
                - name: filter.org_uid
                  in: query
                  description: uid of the orgnization whose business permissions are granted
                  schema:
                    type: string
                - name: filter.biz
                  in: query
                  description: business scope granted
                  schema:
                    enum:
                        - unspecified
                        - anno
                    type: string
                    format: enum
            responses:
                "200":
                    description: OK
                    content: {}
    /iam/v1/conf/{name}:
        get:
            tags:
                - Configs
            description: get a configuration item
            operationId: Configs_GetConf
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/types.NameValue'
        put:
            tags:
                - Configs
            description: set/change a configuration item
            operationId: Configs_SetConf
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/types.NameValue'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /iam/v1/errors:
        get:
            tags:
                - Configs
            description: List Errors info
            operationId: Configs_ListErrors
            security: []
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.Errors'
    /iam/v1/perms:
        get:
            tags:
                - Perms
            description: get permission list
            operationId: Perms_ListPerm
            parameters:
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: |-
                    An opaque pagination token returned from a previous call.
                     An empty token denotes the first page.
                  schema:
                    type: string
                - name: class
                  in: query
                  description: 'resource type: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...'
                  schema:
                    type: string
                - name: name_pattern
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.ListPermReply'
        post:
            tags:
                - Perms
            operationId: Perms_EditPerms
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/iam.v1.EditPermsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /iam/v1/perms/classes:
        get:
            tags:
                - Perms
            description: 'get permission class (resource type) list: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...'
            operationId: Perms_ListPermClass
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.ListPermClassReply'
    /iam/v1/policies:
        post:
            tags:
                - Policies
            description: Create and attach a policy to a resource
            operationId: Policies_CreatePolicy
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/iam.v1.CreatePolicyRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.Policy'
    /iam/v1/policies/{name}:
        get:
            tags:
                - Policies
            operationId: Policies_GetPolicy
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.Policy'
        delete:
            tags:
                - Policies
            description: detach and delete the policy
            operationId: Policies_DeletePolicy
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
        patch:
            tags:
                - Policies
            operationId: Policies_UpdatePolicy
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/iam.v1.UpdatePolicyRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /iam/v1/resources/{name}/policies:
        get:
            tags:
                - Policies
            description: get policies attached to a resource
            operationId: Policies_GetAttachedPolicies
            parameters:
                - name: name
                  in: path
                  description: |-
                    resource name in the format: type:(uid|name)
                     type: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
                  required: true
                  schema:
                    type: string
                - name: role
                  in: query
                  description: get attached policy binding the role
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.GetAttachedPoliciesReply'
    /iam/v1/roles:
        get:
            tags:
                - Roles
            operationId: Roles_ListRole
            security: []
            parameters:
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: |-
                    An opaque pagination token returned from a previous call.
                     An empty token denotes the first page.
                  schema:
                    type: string
                - name: org_uid
                  in: query
                  description: if not empty, only list roles created by this org
                  schema:
                    type: string
                - name: name_pattern
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.ListRoleReply'
        post:
            tags:
                - Roles
            operationId: Roles_CreateRole
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/iam.v1.Role'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.Role'
    /iam/v1/roles/{name}:
        get:
            tags:
                - Roles
            operationId: Roles_GetRole
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.GetRoleReply'
        delete:
            tags:
                - Roles
            operationId: Roles_DeleteRole
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
        patch:
            tags:
                - Roles
            operationId: Roles_UpdateRole
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/iam.v1.UpdateRoleRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /iam/v1/roles/{name}/feperm:
        get:
            tags:
                - Roles
            operationId: Roles_GetRoleFeperm
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.GetRoleFepermReply'
        put:
            tags:
                - Roles
            operationId: Roles_SetRoleFeperm
            parameters:
                - name: name
                  in: path
                  description: role name
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/iam.v1.SetRoleFepermRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /iam/v1/teams:
        get:
            tags:
                - Teams
            operationId: Teams_ListTeam
            parameters:
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: parent_uid
                  in: query
                  description: parent team uid; if not specified, only query organizations
                  schema:
                    type: string
                - name: name_pattern
                  in: query
                  description: find teams by name pattern
                  schema:
                    type: string
                - name: uids
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
                - name: team_type
                  in: query
                  schema:
                    enum:
                        - unspecified
                        - demander
                        - operator
                        - supplier
                    type: string
                    format: enum
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.ListTeamReply'
        post:
            tags:
                - Teams
            operationId: Teams_CreateTeam
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/iam.v1.CreateTeamRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.Team'
    /iam/v1/teams/root:
        get:
            tags:
                - Teams
            operationId: Teams_GetTeamsRoot
            parameters:
                - name: uids
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.ListTeamReply'
    /iam/v1/teams/{team.uid}:
        patch:
            tags:
                - Teams
            operationId: Teams_UpdateTeam
            parameters:
                - name: team.uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: fields
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/iam.v1.CreateTeamRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.Team'
    /iam/v1/teams/{uid}:
        get:
            tags:
                - Teams
            operationId: Teams_GetTeam
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.Team'
        delete:
            tags:
                - Teams
            operationId: Teams_DeleteTeam
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /iam/v1/teams/{uid}/members:
        get:
            tags:
                - Teams
            operationId: Teams_ListMembers
            parameters:
                - name: uid
                  in: path
                  description: team uid
                  required: true
                  schema:
                    type: string
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: name_pattern
                  in: query
                  description: list only members matching the name pattern
                  schema:
                    type: string
                - name: role
                  in: query
                  description: list only members with the specified role
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.ListMembersReply'
        post:
            tags:
                - Teams
            description: send join-team invitation to mentioned users
            operationId: Teams_AddMembers
            parameters:
                - name: uid
                  in: path
                  description: team uid
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/iam.v1.AddMembersRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
        delete:
            tags:
                - Teams
            operationId: Teams_DeleteMembers
            parameters:
                - name: uid
                  in: path
                  description: team uid
                  required: true
                  schema:
                    type: string
                - name: user_uids
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /iam/v1/teams/{uid}/role:
        put:
            tags:
                - Teams
            operationId: Teams_SetMembersRole
            parameters:
                - name: uid
                  in: path
                  description: uid of the team
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/iam.v1.SetMembersRoleRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /iam/v1/users:
        get:
            tags:
                - Users
            operationId: Users_ListUser
            parameters:
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pagesz
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: name_pattern
                  in: query
                  description: find by name pattern
                  schema:
                    type: string
                - name: uids
                  in: query
                  description: find by uid
                  schema:
                    type: array
                    items:
                        type: string
                - name: phones
                  in: query
                  description: find by phone number
                  schema:
                    type: array
                    items:
                        type: string
                - name: emails
                  in: query
                  description: find by emails
                  schema:
                    type: array
                    items:
                        type: string
                - name: with_org
                  in: query
                  description: include user's organization in the reply
                  schema:
                    type: boolean
                - name: tags
                  in: query
                  description: find by attached tags
                  schema:
                    type: array
                    items:
                        type: string
                - name: org_uid
                  in: query
                  description: find by org_uid
                  schema:
                    type: string
                - name: roles
                  in: query
                  description: find by roles
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.ListUserReply'
        post:
            tags:
                - Users
            operationId: Users_CreateUser
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/iam.v1.CreateUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.User'
    /iam/v1/users/batch:
        post:
            tags:
                - Users
            operationId: Users_BatchCreateUsers
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/iam.v1.BatchCreateUsersRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.ListUserReply'
    /iam/v1/users/login:
        post:
            tags:
                - Users
            operationId: Users_Login
            security: []
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/iam.v1.LoginRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.LoginReply'
    /iam/v1/users/logout:
        post:
            tags:
                - Users
            description: clear HTTP cookies
            operationId: Users_Logout
            requestBody:
                content:
                    application/json: {}
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /iam/v1/users/me:
        get:
            tags:
                - Users
            description: retrieve my info
            operationId: Users_GetMe
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.User'
    /iam/v1/users/me/feperm:
        get:
            tags:
                - Users
            description: get my front-end permissions
            operationId: Users_GetMyFeperm
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.Feperm'
    /iam/v1/users/me2:
        get:
            tags:
                - Users
            description: retrieve my info; also returns the real user in an assume context
            operationId: Users_GetMe2
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.UserContext'
    /iam/v1/users/send-auth-code:
        post:
            tags:
                - Users
            operationId: Users_SendAuthCode
            security: []
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/iam.v1.SendAuthCodeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.SendAuthCodeReply'
    /iam/v1/users/token/refresh:
        get:
            tags:
                - Users
            operationId: Users_RefreshToken
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.LoginReply'
    /iam/v1/users/{uid}:
        get:
            tags:
                - Users
            operationId: Users_GetUser
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.User'
        delete:
            tags:
                - Users
            operationId: Users_DeleteUser
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
    /iam/v1/users/{uid}/assume:
        get:
            tags:
                - Users
            operationId: Users_AssumeUser
            parameters:
                - name: uid
                  in: path
                  description: |-
                    uid of the user to assume; use "me" to unassume;
                     use "identity" to specify the user by his/her phone or email
                  required: true
                  schema:
                    type: string
                - name: identity
                  in: query
                  description: when uid is "identity", this holds the phone or email of the user to assume
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.LoginReply'
    /iam/v1/users/{uid}/is-allowed:
        get:
            tags:
                - Users
            description: check if the user is allowed to perform the actions
            operationId: Users_IsAllowed
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: action.resource
                  in: query
                  description: |-
                    resource name. format: type:(uid|IamGroup:team-uid)
                     pattern: "^\\w+:([\\w:.-]+)*$"
                  schema:
                    type: string
                - name: action.perm
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.IsAllowedReply'
    /iam/v1/users/{uid}/perms:
        get:
            tags:
                - Users
            description: get the permissions granted on a resource
            operationId: Users_GetPerms
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: resource
                  in: query
                  description: |-
                    resource name in format: type:uid; when scope is specified, uid may be omitted
                     pattern: "^\\w+:([\\w.-]+)?$"
                  schema:
                    type: string
                - name: scope
                  in: query
                  description: |-
                    check if the user can create/list resources within an organization/team
                     format: IamGroup:uid, pattern: "^(IamGroup:[\\w-]+)?$"
                  schema:
                    type: string
                - name: perms
                  in: query
                  description: |-
                    if perms is not empty, only check for the permissions in the list
                     if it is empty, check all permissions valid to the resource type.
                     pattern: "^\\w+\\.\\w+$"
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.GetPermsReply'
    /iam/v1/users/{uid}/tag:
        put:
            tags:
                - Users
            operationId: Users_AddTag
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            type: string
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/types.TagList'
        delete:
            tags:
                - Users
            operationId: Users_DeleteTag
            parameters:
                - name: uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: tags
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/types.TagList'
    /iam/v1/users/{user.uid}:
        patch:
            tags:
                - Users
            operationId: Users_UpdateUser
            parameters:
                - name: user.uid
                  in: path
                  required: true
                  schema:
                    type: string
                - name: fields
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/iam.v1.CreateUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.User'
    /iam/v1/version:
        get:
            tags:
                - Configs
            operationId: Configs_GetVersion
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/iam.v1.GetVersionReply'
components:
    securitySchemes:
        jwt:
            type: http
            scheme: bearer
            bearerFormat: JWT
    schemas:
        iam.v1.AddMembersRequest:
            required:
                - uid
                - id_type
                - identities
                - role
            type: object
            properties:
                uid:
                    type: string
                    description: team uid
                id_type:
                    enum:
                        - unspecified
                        - uid
                        - phone
                        - email
                    type: string
                    description: 'type of user identities: uid/email/phone/...'
                    format: enum
                identities:
                    type: array
                    items:
                        type: string
                    description: max number of user identities is 100
                role:
                    type: string
                    description: one of owner/manager/member
        iam.v1.BaseUser:
            required:
                - uid
                - name
                - avatar
            type: object
            properties:
                uid:
                    type: string
                    description: user/team uid
                name:
                    type: string
                    description: user/team name
                avatar:
                    type: string
                    description: user/team avatar url
        iam.v1.BatchCreateUsersRequest:
            required:
                - users
            type: object
            properties:
                users:
                    type: array
                    items:
                        $ref: '#/components/schemas/iam.v1.CreateUserRequest'
        iam.v1.Bizgrant:
            required:
                - grantor_uid
                - grantee_uid
                - org_uid
                - biz
                - created_at
            type: object
            properties:
                grantor_uid:
                    type: string
                    description: grantor uid
                grantee_uid:
                    type: string
                    description: grantee uid
                org_uid:
                    type: string
                    description: uid of the organization whose business permissions are granted
                biz:
                    enum:
                        - unspecified
                        - anno
                    type: string
                    description: business scope granted
                    format: enum
                created_at:
                    type: string
                    format: date-time
        iam.v1.CreateBizgrantReply:
            required:
                - grants
            type: object
            properties:
                grants:
                    type: array
                    items:
                        $ref: '#/components/schemas/iam.v1.Bizgrant'
        iam.v1.CreateBizgrantRequest:
            required:
                - grantee_uid
                - org_uid
                - biz
            type: object
            properties:
                grantee_uid:
                    type: string
                    description: grantee uid
                org_uid:
                    type: string
                    description: uid of the organization whose business permissions are granted
                bizz:
                    type: array
                    items:
                        enum:
                            - unspecified
                            - anno
                        type: string
                        format: enum
                    description: business scope granted
        iam.v1.CreatePolicyRequest:
            required:
                - role
                - users
                - resource
            type: object
            properties:
                role:
                    type: string
                users:
                    type: array
                    items:
                        type: string
                    description: |-
                        uid of users (IamUser:xxx) or groups(IamGroup:xxx)
                         pattern: "^(IamUser|IamGroup):[\\w-]+(:[\\w-]+)?$"
                resource:
                    type: string
                    description: 'the name of the resource to be attached to; pattern: "^([\\w-]+:){1,2}([\\w-]+(.|/))+$"'
        iam.v1.CreateTeamRequest:
            type: object
            properties:
                uid:
                    type: string
                name:
                    type: string
                    description: mandatory in create-team request
                desc:
                    type: string
                avatar:
                    type: string
                province:
                    type: string
                city:
                    type: string
                parent_uid:
                    type: string
                    description: parent team uid
                type:
                    enum:
                        - unspecified
                        - demander
                        - operator
                        - supplier
                    type: string
                    description: mandatory in create-team request
                    format: enum
                owner:
                    type: string
                    description: |-
                        specify the team owner.
                         supported formats: uid:xxx, phone:+8612345678901 or email:<EMAIL>
        iam.v1.CreateUserRequest:
            type: object
            properties:
                uid:
                    type: string
                name:
                    type: string
                    description: mandatory in create-user requests
                phone:
                    type: string
                    description: 'phone number: +8613412345678; mandatory in create-user requests'
                email:
                    type: string
                    description: 'email: <EMAIL>'
                avatar:
                    type: string
                role:
                    type: string
                    description: 'user''s system role: member/admin/root'
                gender:
                    enum:
                        - unspecified
                        - male
                        - female
                    type: string
                    description: user's gender
                    format: enum
                birthday:
                    type: string
                    description: |-
                        user's birthday in RFC339 format: 2010-06-07T00:00:00Z
                         use a string type to allow an empty birthday
                province:
                    type: string
                city:
                    type: string
        iam.v1.EditPermsRequest:
            required:
                - perms
                - action
            type: object
            properties:
                action:
                    enum:
                        - unspecified
                        - add
                        - delete
                    type: string
                    format: enum
                perms:
                    type: array
                    items:
                        type: string
                    description: 'pattern: "^\\w+\\.\\w+$", max_bytes: 64'
        iam.v1.Errors:
            required:
                - errors
            type: object
            properties:
                errors:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/types.Multilingual'
                    description: error-reason -> (language -> display-name)
        iam.v1.Feperm:
            required:
                - visible_pages
            type: object
            properties:
                visible_pages:
                    type: array
                    items:
                        type: string
                    description: pages visible to the user
        iam.v1.FepermItem:
            type: object
            properties:
                team_type:
                    enum:
                        - unspecified
                        - demander
                        - operator
                        - supplier
                    type: string
                    format: enum
                perm:
                    $ref: '#/components/schemas/iam.v1.Feperm'
        iam.v1.GetAttachedPoliciesReply:
            required:
                - policy_names
            type: object
            properties:
                policy_names:
                    type: array
                    items:
                        type: string
        iam.v1.GetPermsReply:
            required:
                - perms
            type: object
            properties:
                perms:
                    type: array
                    items:
                        type: string
        iam.v1.GetRoleFepermReply:
            type: object
            properties:
                perms:
                    type: array
                    items:
                        $ref: '#/components/schemas/iam.v1.FepermItem'
        iam.v1.GetRoleReply:
            required:
                - name
                - display_name
                - perms
            type: object
            properties:
                name:
                    type: string
                display_name:
                    type: string
                perms:
                    type: array
                    items:
                        type: string
                    description: list of roles (IamRole:xxx) and permissions
        iam.v1.GetVersionReply:
            type: object
            properties:
                version:
                    type: string
        iam.v1.IsAllowedReply:
            required:
                - allowed
            type: object
            properties:
                allowed:
                    type: boolean
                    description: true if all of the actions are allowed; false otherwise
        iam.v1.ListBizgrantReply:
            required:
                - grants
                - next_page_token
            type: object
            properties:
                grants:
                    type: array
                    items:
                        $ref: '#/components/schemas/iam.v1.Bizgrant'
                next_page_token:
                    type: string
                    description: An opaque pagination token, if not empty, to be used to fetch the next page of results
        iam.v1.ListMembersReply:
            required:
                - members
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                members:
                    type: array
                    items:
                        $ref: '#/components/schemas/iam.v1.User'
                team:
                    $ref: '#/components/schemas/iam.v1.BaseUser'
        iam.v1.ListPermClassReply:
            type: object
            properties:
                classes:
                    type: array
                    items:
                        type: string
                    description: 'resource type: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...'
        iam.v1.ListPermReply:
            required:
                - perms
                - next_page_token
            type: object
            properties:
                perms:
                    type: array
                    items:
                        type: string
                next_page_token:
                    type: string
        iam.v1.ListRoleReply:
            required:
                - roles
                - next_page_token
            type: object
            properties:
                roles:
                    type: array
                    items:
                        $ref: '#/components/schemas/iam.v1.Role'
                    description: roles info without perms
                next_page_token:
                    type: string
        iam.v1.ListTeamReply:
            required:
                - teams
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                teams:
                    type: array
                    items:
                        $ref: '#/components/schemas/iam.v1.Team'
        iam.v1.ListUserReply:
            required:
                - users
            type: object
            properties:
                total:
                    type: integer
                    description: total number of items found; valid only in the first page reply.
                    format: int32
                users:
                    type: array
                    items:
                        $ref: '#/components/schemas/iam.v1.User'
                orgs:
                    type: array
                    items:
                        $ref: '#/components/schemas/iam.v1.BaseUser'
                    description: the organizations that the user, at the corresponding position, belongs to.
        iam.v1.LoginReply:
            required:
                - user
                - token
                - expire_time
                - feperm
            type: object
            properties:
                user:
                    allOf:
                        - $ref: '#/components/schemas/iam.v1.User'
                    description: user info; it may be an assumed user in an assume request
                token:
                    type: string
                    description: JWT token
                expire_time:
                    type: string
                    description: 'token expire time in RFC3339 format: 2016-01-01T00:00:00+08:00'
                    format: date-time
                feperm:
                    allOf:
                        - $ref: '#/components/schemas/iam.v1.Feperm'
                    description: front-end permissions
                assume_by:
                    allOf:
                        - $ref: '#/components/schemas/iam.v1.User'
                    description: the real user in an assume request
        iam.v1.LoginRequest:
            required:
                - id_type
                - identity
                - auth_type
                - credential
            type: object
            properties:
                id_type:
                    enum:
                        - unspecified
                        - uid
                        - phone
                        - email
                    type: string
                    description: type of user's identity
                    format: enum
                identity:
                    type: string
                    description: user's phone number-number/email/...
                auth_type:
                    enum:
                        - unspecified
                        - authcode
                        - password
                        - otp
                    type: string
                    description: type of the authentication
                    format: enum
                credential:
                    type: string
                    description: authentication credential according to auth_type
                agreement:
                    type: integer
                    description: signed version of user agreement; 0 means no update
                    format: int32
        iam.v1.Policy:
            required:
                - name
                - role
                - users
            type: object
            properties:
                name:
                    type: string
                role:
                    type: string
                users:
                    type: array
                    items:
                        type: string
                    description: uid of users (IamUser:xxx) or groups(IamGroup:xxx)
        iam.v1.Role:
            required:
                - name
                - perms
            type: object
            properties:
                name:
                    type: string
                    description: |-
                        name of the role
                         admins can create global roles; others can only create org-scope roles
                         role names should be unique within their scopes.
                         name of org-scope roles are in the format: org-uid.role-name
                         pattern: "^[\\w-]+(\\.[\\w-]+)?$"; length-in-bytes: [3, 30]
                display_name:
                    type: string
                    description: 'name displayed in UI; length-in-bytes: [3, 30]'
                perms:
                    type: array
                    items:
                        type: string
                    description: list of roles (IamRole:xxx) and permissions
        iam.v1.SendAuthCodeReply:
            required:
                - sequence
            type: object
            properties:
                sequence:
                    type: integer
                    description: sequence number of the auth code
                    format: int32
                agreement:
                    type: integer
                    description: signed version of user agreement; 0 means not signed
                    format: int32
        iam.v1.SendAuthCodeRequest:
            required:
                - purpose
                - channel
                - receiver
                - locale
            type: object
            properties:
                purpose:
                    enum:
                        - unspecified
                        - login
                    type: string
                    description: usage of the auth code
                    format: enum
                channel:
                    enum:
                        - unspecified
                        - sms
                        - email
                    type: string
                    description: authentication code dispatch channel
                    format: enum
                receiver:
                    type: string
                    description: a phone number or email address according to the channel
                locale:
                    type: string
                    description: 'language code: zh-Hans, en-US'
        iam.v1.SetMembersRoleRequest:
            required:
                - uid
                - user_uids
                - role
            type: object
            properties:
                uid:
                    type: string
                    description: uid of the team
                user_uids:
                    type: array
                    items:
                        type: string
                role:
                    type: string
                    description: 'new role: owner/manager/member'
        iam.v1.SetRoleFepermRequest:
            type: object
            properties:
                name:
                    type: string
                    description: role name
                perms:
                    type: array
                    items:
                        $ref: '#/components/schemas/iam.v1.FepermItem'
        iam.v1.Team:
            required:
                - uid
                - name
                - desc
                - avatar
                - type
                - province
                - city
                - created_at
            type: object
            properties:
                uid:
                    readOnly: true
                    type: string
                name:
                    type: string
                desc:
                    type: string
                avatar:
                    type: string
                type:
                    enum:
                        - unspecified
                        - demander
                        - operator
                        - supplier
                    type: string
                    format: enum
                province:
                    type: string
                city:
                    type: string
                created_at:
                    readOnly: true
                    type: string
                    description: |-
                        parent team uid
                         string parent_uid = 11 [(google.api.field_behavior) = INPUT_ONLY];
                    format: date-time
        iam.v1.UpdatePolicyRequest:
            required:
                - name
                - users
            type: object
            properties:
                name:
                    type: string
                users:
                    type: array
                    items:
                        type: string
                    description: uid of users (IamUser:xxx) or groups(IamGroup:xxx)
        iam.v1.UpdateRoleRequest:
            required:
                - name
                - perms
                - action
            type: object
            properties:
                action:
                    enum:
                        - unspecified
                        - add
                        - delete
                    type: string
                    format: enum
                name:
                    type: string
                perms:
                    type: array
                    items:
                        type: string
                    description: list of roles (IamRole:xxx) and permissions
        iam.v1.User:
            required:
                - uid
                - name
                - avatar
                - role
                - gender
                - province
                - city
                - imperfect
                - created_at
            type: object
            properties:
                uid:
                    type: string
                name:
                    type: string
                phone:
                    type: string
                    description: 'phone number: +8613412345678'
                email:
                    type: string
                    description: 'email: <EMAIL>'
                avatar:
                    type: string
                role:
                    type: string
                    description: 'user''s role: admin/manager/member'
                gender:
                    enum:
                        - unspecified
                        - male
                        - female
                    type: string
                    description: user's gender
                    format: enum
                birthday:
                    type: string
                    description: 'user''s birthday: 2010-06-07'
                province:
                    type: string
                city:
                    type: string
                org_uid:
                    type: string
                    description: top level team the user belongs to
                org_type:
                    enum:
                        - unspecified
                        - demander
                        - operator
                        - supplier
                    type: string
                    description: type of the user's organization
                    format: enum
                imperfect:
                    readOnly: true
                    type: boolean
                    description: |-
                        string hierarchy = 13;
                         indicates if the user's information should be improved
                created_at:
                    readOnly: true
                    type: string
                    format: date-time
                tags:
                    type: array
                    items:
                        type: string
                    description: tags attached to the user
        iam.v1.UserContext:
            type: object
            properties:
                user:
                    allOf:
                        - $ref: '#/components/schemas/iam.v1.User'
                    description: the effective user; it may be an assumed user in an assume context
                assume_by:
                    allOf:
                        - $ref: '#/components/schemas/iam.v1.User'
                    description: the real user in an assume context
        types.Multilingual:
            required:
                - langs
            type: object
            properties:
                langs:
                    type: object
                    additionalProperties:
                        type: string
                    description: language => text
        types.NameValue:
            required:
                - name
                - value
            type: object
            properties:
                name:
                    type: string
                value:
                    type: string
        types.TagList:
            required:
                - tags
            type: object
            properties:
                tags:
                    type: array
                    items:
                        type: string
tags:
    - name: Bizgrants
    - name: Configs
    - name: Perms
    - name: Policies
    - name: Roles
    - name: Teams
    - name: Users
