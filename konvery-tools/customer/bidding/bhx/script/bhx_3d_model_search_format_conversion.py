import os
import zipfile
import glob
import csv
import shutil
import json
import re
import sys

sys.path.append('.')
from customer.common import tools


def run(input_dir, annotation_csv):
    file_counter = 0
    miss_match_count = {}

    # unzip file
    unzip_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_unzip')
    if os.path.exists(unzip_dir):
        shutil.rmtree(unzip_dir)
    with zipfile.ZipFile(input_dir, 'r') as zip_ref:
        zip_ref.extractall(unzip_dir)
    # create result_folder
    result_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_result')
    try:
        os.mkdir(result_dir)
    except FileExistsError:
        shutil.rmtree(result_dir)
        os.mkdir(result_dir)

    # load annotation.csv
    with open(os.path.join(annotation_csv, 'annotation.csv'), "r") as f:
        reader = csv.reader(f, delimiter="\t")
        annotation_list = list(reader)

    annotation_dict = {}
    for item in annotation_list[1:]:
        annotation_dict[str(item[0])] = item[1:]

    # build result json
    for json_dir in glob.glob(os.path.join(unzip_dir, '**', '**.json'), recursive=True):
        file_name = os.path.basename(json_dir)
        if file_name[:5] != '00000':
            result_list = []
            # load json file
            json_data = json.load(open(json_dir))

            line_id = os.path.basename(os.path.dirname(os.path.dirname(json_dir)))
            line_id = re.findall(r'\d+', line_id)[0]

            if line_id not in annotation_dict:
                miss_match_count[line_id] = miss_match_count.get(line_id, 0) + 1
                continue

            result_line_dir = os.path.join(result_dir, str(line_id))
            if not os.path.isdir(result_line_dir):
                os.mkdir(result_line_dir)

            # build result dict
            result_dict = {
                'line_id': line_id,
                'query_id': annotation_dict[line_id][0],
                'query': annotation_dict[line_id][1],
                'query_cn': annotation_dict[line_id][2],
                'is_valid': 'yes',
                'target_id': file_name.split('.json')[0],
                'result': json_data['labels'][0]['class']
            }

            result_list.append(result_dict)

            # save the result
            with open(os.path.join(result_line_dir, file_name), 'w') as f:
                json.dump(result_list,
                          f,
                          indent=4,
                          ensure_ascii=False)

    trans_result = {
        "code": 0,
        "output": result_dir,
        "err_msg": '成功',
        "moved_files": file_counter,
        "miss_match_count": miss_match_count
    }

    print(trans_result)
    return trans_result


if __name__ == "__main__":
    args = tools.get_args()
    run(args.input, args.pcd)

    # args_input = '/Users/<USER>/Downloads/bhx/BHX-伯明翰3D模型检索标注_231221af4b1_质检_已通过_JSON标注结果文件_20231225_6016.zip'
    # args_pcd = '/Users/<USER>/Downloads/bhx/annotation.csv'
    # run(args_input, args_pcd)
