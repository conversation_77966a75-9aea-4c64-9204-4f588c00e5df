import sys
import os
import shutil
sys.path.append('.')
from customer.common import tools


def run(input_dir, output_dir):
    img_src_dir = os.path.join(input_dir, 'tupian')
    img_dst_dir = os.path.join(output_dir, 'img')
    shutil.copytree(img_src_dir, img_dst_dir)
    json_dir = os.path.join(input_dir, 'json')
    json_files = tools.get_json_files(json_dir)
    out_pre_label_dir = os.path.join(output_dir, 'pre_label')
    os.mkdir(out_pre_label_dir)
    group_id = 1
    for json_file in json_files:
        labels = []
        json_data = tools.get_json_data(json_file)
        for instance in json_data['instance']:
            for region in instance['regions']:
                label_data = [{"x": pt[0], "y": pt[1]} for pt in region["contour"][0]]
                label_gaps = [[{"x": pt[0], "y": pt[1]} for pt in gap] for gap in region["contour"][1:]]
                label = {
                    "class": region["label"],
                    "attrs": {},
                    "group_id": group_id,
                    "annotation": {
                        "type": "polygon",
                        "data": label_data,
                        "gaps": label_gaps
                    }
                }
                labels.append(label)
            group_id += 1
        pre_label_data = {
            "label_meta": {"mark_status": 0, "global": {}},
            "labels": labels
        }
        tools.write_json_file(pre_label_data, os.path.join(out_pre_label_dir, json_file.name), indent=4)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
