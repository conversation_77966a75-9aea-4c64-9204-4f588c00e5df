package reap

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"os"
	"path/filepath"

	"annout/api/client"
	"annout/api/interpolate"
	"annout/internal/biz"
	"annout/workflow/codec"
	"annout/workflow/style"
	"annout/workflow/types"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/kfs"
	"go.temporal.io/sdk/activity"
)

type Activities struct {
	log *log.Helper

	listJobPagesz int
}

func NewActivities(logger log.Logger) *Activities {
	pagesz := 10
	if n := cast.ToInt(os.Getenv("DBG_LIST_JOB_PAGESZ")); n > 0 && n <= 100 {
		pagesz = n
	}
	return &Activities{log: log.<PERSON>(logger), listJobPagesz: pagesz}
}

func (o *Activities) ReapGetVolumeSizeGB(ctx context.Context, data *biz.Reaper) (sizeGB int, err error) {
	return 0, nil
}

func (o *Activities) ReapConverterScript(ctx context.Context, dir string, data *biz.Reaper) (uri string, err error) {
	return data.Config.GetConverter().GetUri(), nil
}

// ReapCollectAnnos collects annotations of a lot.
func (o *Activities) ReapCollectAnnos(ctx context.Context, dir string, reaper *biz.Reaper) (err error) {
	ctx = client.NewCtxUseSvcAccount(ctx)
	cfg := reaper.Config
	codec := codec.GetCodec(cfg.Encoder.String())
	if codec == nil {
		return fmt.Errorf("unsupported codec %v", cfg.Encoder.String())
	}

	styleTrans := style.Get(cfg.Style)
	if styleTrans == nil {
		return fmt.Errorf("unsupported style %v for lot %v", cfg.Style, reaper.LotUid)
	}

	var jobState anno.Job_State_Enum
	if reaper.Option == anno.ExportOrderAnnosRequest_Option_finished {
		jobState = anno.Job_State_finished
	}
	for i := 0; ; i++ {
		rs, err := client.ListJob(ctx, &anno.ListJobRequest{
			Page:   int32(i),
			Pagesz: int32(o.listJobPagesz),
			Filter: &anno.ListJobFilter{
				LotUid: reaper.LotUid,
				State:  jobState,
				Phases: reaper.Phases,
			},
			ShowExecutors: true,
		})
		if err != nil {
			return fmt.Errorf("failed to ListJob %v-%v: %w", reaper.LotUid, i, err)
		}
		if len(rs.Jobs) == 0 {
			break
		}
		err = client.DownloadJobDetails(ctx, rs.Jobs...)
		if err != nil {
			return err
		}
		patchAnnos(rs)

		slicer, err := styleTrans(reaper, rs.Jobs)
		if err != nil {
			return fmt.Errorf("failed to apply style for lot %v: %w", reaper.LotUid, err)
		}
		err = o.saveElemAnnos(ctx, dir, codec, slicer)
		if err != nil {
			return fmt.Errorf("failed to save element annos for lot %v: %w", reaper.LotUid, err)
		}

		activity.RecordHeartbeat(ctx) // heartbeat.
	}

	return nil
}

func (o *Activities) saveElemAnnos(ctx context.Context, dir string, codec codec.Codec, slicer types.Slicer) error {
	pieces, err := slicer.Slice()
	if err != nil {
		return fmt.Errorf("failed to call Slice: %w", err)
	}
	for _, p := range pieces {
		data, err := codec.Marshal(p.Data)
		if err != nil {
			return fmt.Errorf("failed to marshal %s: %w", p.Name, err)
		}
		fpath := filepath.Join(dir, types.AnnosDir, p.Name)
		if err := kfs.MkdirAll(filepath.Dir(fpath), 0777); err != nil {
			return fmt.Errorf("failed to mkdir %s: %w", filepath.Dir(fpath), err)
		}
		err = os.WriteFile(fpath, data, 0644)
		if err != nil {
			return fmt.Errorf("failed to write file %s: %w", p.Name, err)
		}
	}
	return nil
}

func patchAnnos(r *anno.ListJobReply) {
	for _, job := range r.Jobs {
		if job.NeedInterpolation {
			interpolate.Interpolate(client.JobAnnoFromJob(job))
		}

		jobAttrs := lo.Map(job.JobAttrs, func(ev *anno.AttrAndValues, _ int) *anno.AttrAndValues {
			// distinguish job attributes with element attributes
			return &anno.AttrAndValues{
				Name:   "konv:job_attr:" + ev.Name,
				Values: ev.Values,
			}
		})
		for i, ea := range job.Annotations {
			elem := job.Elements[i]
			ea.Name = elem.Name
			ea.Index = elem.Index
			ea.Metadata = r.Executors[job.Uid]

			for j, ra := range ea.RawdataAnnos {
				rd := elem.Datas[j]
				if rd == nil {
					continue
				}
				ra.Name = rd.Name
				ra.OrigName = rd.OrigName
				spew.Dump("--->ra: ", rd, ra)
				spew.Dump(rd.Type, rd.Meta, rd.Meta.Image)
				ra.Metadata = &anno.RawdataAnno_Metadata{
					Type: rd.Type,
					//Camera: lo.Ternary(rd.Type == anno.Rawdata_Type_image, rd.Meta.Image.Camera, ""),
				}
				if rd.Type == anno.Rawdata_Type_image && rd.Meta != nil && rd.Meta.Image != nil {
					ra.Metadata.Camera = rd.Meta.Image.Camera
				}
				spew.Dump(ra)
			}

			// ensure TrackID is unique within the lot
			for _, r := range ea.RawdataAnnos {
				for _, o := range r.Objects {
					if o.TrackId != "" {
						o.TrackId = fmt.Sprintf("konv:job%05d.%v", job.IdxInLot, o.TrackId)
					}
				}
			}
			// add job attrs to the elem attrs in each element
			ea.Attrs = append(ea.Attrs, jobAttrs...)
		}
	}
}
