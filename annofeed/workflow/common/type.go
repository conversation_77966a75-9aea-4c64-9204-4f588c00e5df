package common

import (
	"context"
	"path"
	"strings"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
)

var (
	RawdataFormatJSON     = anno.Rawdata_Format_json.String()
	RawdataFormatPCD      = anno.Rawdata_Format_pcd.String()
	RawdataFormatPLY      = "ply"  // will be converted to pcd
	RawdataFormatJPEG     = "jpeg" // will be regarded as JPG format
	RawdataFormatJPG      = anno.Rawdata_Format_jpg.String()
	RawdataFormatPNG      = anno.Rawdata_Format_png.String()
	RawdataFormatFilelist = anno.Rawdata_Format_filelist.String() // indicate the rawdata has a filelist
	RawdataFormatWebP     = anno.Rawdata_Format_webp.String()
)

const (
	// ParamTypeMatrix    = "matrix"
	// ParamTypeExtrinsic = "extrinsic"
	// ParamTypeIntrinsic = "intrinsic"

	KeyVehicle = "vehicle"
	KeyLidar   = "lidar"

	LidarPCD   = KeyLidar + ".pcd"
	ParamsFile = "params.json"
	AnnosFile  = "annos.json"
	ElemsFile  = "elems.json"
	MetaFile   = "meta.json"

	DataPrefix  = "data/"
	DataDir     = "data"
	MetaDir     = "meta"
	AnnosPrefix = "annos/"
	AnnosDir    = "annos"

	OrigExt = ".orig"
)

var SupportedImageFormat = map[string]string{
	RawdataFormatJPEG: RawdataFormatJPG,
	RawdataFormatJPG:  RawdataFormatJPG,
	RawdataFormatPNG:  RawdataFormatPNG,
	RawdataFormatWebP: RawdataFormatWebP,
}

var SupportedFileFormat = map[string]string{
	RawdataFormatJSON: RawdataFormatJSON,
	RawdataFormatPCD:  RawdataFormatPCD,
}

var StandardTopFiles = map[string]bool{
	MetaFile:  true,
	ElemsFile: true, // DEPRECATED
	AnnosFile: true,
}

var StandardTopDirs = map[string]bool{
	DataDir:  true,
	AnnosDir: true,
}

// NonstandardDirs defines nonstandard directories in data.
var NonstandardDirs = map[string]bool{
	MetaDir: true,
}

func init() {
	for k, v := range SupportedImageFormat {
		SupportedFileFormat[k] = v
	}
}

// FileExt returns the lower-case file name extension used in pathname without the dot prefix.
func FileExt(pathname string) string {
	ext := path.Ext(pathname)
	if ext == "" {
		return ext
	}
	return strings.ToLower(ext[1:])
}

// FileFormat returns file format according to the file name extension in pathname.
func FileFormat(pathname string) string {
	return SupportedFileFormat[FileExt(pathname)]
}

// IsSupportedImageFormat returns true if the file name extension in pathname is a supported image format.
func IsSupportedImageFormat(pathname string) bool {
	return SupportedImageFormat[FileExt(pathname)] != ""
}

// IsSupportedRawdataFormat checks if the file name extension in pathnamme is a supported rawdata format.
func IsSupportedRawdataFormat(pathname string) bool {
	ext := FileExt(pathname)
	return SupportedImageFormat[ext] != "" || ext == RawdataFormatPCD
}

type Heartbeat func(ctx context.Context, details ...interface{})
