import os
import shutil
import zipfile
import sys

sys.path.append('.')
from customer.common import tools

mac_file = '.DS_Store'


def run(input_dir):
    try:
        file_counter = 0
        # unzip file
        root_dir = os.path.join(os.path.dirname(input_dir), 'unzip')
        if os.path.exists(root_dir):
            shutil.rmtree(root_dir)
        with zipfile.ZipFile(input_dir, 'r') as zip_ref:
            zip_ref.extractall(root_dir)

        # create result_folder
        result_dir = os.path.join(os.path.dirname(input_dir), input_dir.split('/')[-1].split('.')[0] + '_result')
        try:
            os.mkdir(result_dir)
        except FileExistsError:
            shutil.rmtree(result_dir)
            os.mkdir(result_dir)

        for cloud in os.listdir(root_dir):
            if cloud != mac_file:
                # cloud
                cloud_dir = os.path.join(root_dir, cloud)
                for pcd in os.listdir(cloud_dir):
                    if pcd != mac_file:
                        pcd_dir = os.path.join(cloud_dir, pcd)
                        segment_dir = os.path.join(result_dir, 'segment' + str(file_counter))
                        lidar_dir = os.path.join(segment_dir, 'lidar')
                        os.mkdir(segment_dir)
                        os.mkdir(lidar_dir)
                        shutil.copy2(pcd_dir,
                                     lidar_dir)
                        file_counter += 1

        # compress the result folder
        shutil.make_archive(result_dir,
                            format='zip',
                            root_dir=os.path.dirname(result_dir),
                            base_dir=input_dir.split('/')[-1].split('.')[0] + '_result')

        trans_result = {
            "code": 0,
            "output": result_dir,
            "err_msg": '成功',
            "summary": {
                '处理文件数': file_counter,
            }
        }

    except Exception as e:
        trans_result = {
            "code": 1,
            "err_msg": e,
        }

    for key in trans_result:
        print(key + ': ' + str(trans_result[key]))

    print(trans_result)
    return trans_result


if __name__ == "__main__":
    args = tools.get_args()
    # args.input = '/Users/<USER>/Downloads/AGU-阿瓜斯卡连特斯3D挖装运_230915a6652_验收_待处理_JSON标注结果文件_20230919_2339.zip'
    run(args.input)
