package biz

import (
	"context"
	"time"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
)

type Joblog struct {
	ID            int64     `json:"id"`
	LotID         int64     `json:"lot_id" gorm:"default:null"`
	JobID         int64     `json:"job_id" gorm:"default:null"`
	OperatorUid   string    `json:"operator_uid" gorm:"default:null"`
	OpOrgUid      string    `json:"op_org_uid" gorm:"default:null"`
	Action        string    `json:"action" gorm:"default:null"`
	FromPhase     int32     `json:"from_phase" gorm:"default:null"`
	FromState     int32     `json:"from_state" gorm:"default:null"`
	ToPhase       int32     `json:"to_phase" gorm:"default:null"`
	ToState       int32     `json:"to_state" gorm:"default:null"`
	ToExecutorUid string    `json:"to_executor_uid" gorm:"default:null"` // the user which the job is assigned to
	ToExecteam    string    `json:"to_execteam" gorm:"default:null"`     // the team which the executor belongs to
	CreatedAt     time.Time `json:"created_at" gorm:"default:null"`
	// Details       JoblogDetailsWrap `json:"details" gorm:"default:null"`
}

func (o *Joblog) GetID() int64 { return o.ID }

const JoblogTableName = "joblogs"

var (
	joblog_ = field.RegObject(&Joblog{})
	// JoblogUpdatableFlds = field.NewModel(joblog_, "ToPhase", "ToState", "ToExecutorUid", "ToExecteam")

	JoblogSfldJobID         = field.Sname(&joblog_.JobID)
	JoblogSfldOperatorUid   = field.Sname(&joblog_.OperatorUid)
	JoblogSfldAction        = field.Sname(&joblog_.Action)
	JoblogSfldFromPhase     = field.Sname(&joblog_.FromPhase)
	JoblogSfldFromState     = field.Sname(&joblog_.FromState)
	JoblogSfldToPhase       = field.Sname(&joblog_.ToPhase)
	JoblogSfldToState       = field.Sname(&joblog_.ToState)
	JoblogSfldToExecutorUid = field.Sname(&joblog_.ToExecutorUid)
	JoblogSfldToExecteam    = field.Sname(&joblog_.ToExecteam)
	// JoblogSfldDetails       = field.Sname(&joblog_.Details)
)

// type GiveupReason struct {
// 	Reason  string `json:"reason"`
// 	Details string `json:"details"`
// }

// type JoblogDetails struct {
// 	AddComments     []*v1.ReviewJobRequest_Comment `json:"add_comments"`
// 	ResolveComments []string                       `json:"resolve_comments"`
// 	GiveupReason    *GiveupReason                  `json:"giveup_reason"`
// }

// type JoblogDetailsWrap = serial.Type[*JoblogDetails]

type JoblogsRepo interface {
	repo.GenericRepo[Joblog]
	GetPhaseLastExecutor(ctx context.Context, jobID int64, phase int) (user, team string, err error)
	GetPhaseLastSubmitter(ctx context.Context, jobID int64, phase int) (user, team string, err error)
}
