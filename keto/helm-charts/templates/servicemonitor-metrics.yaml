{{- if and (.Capabilities.APIVersions.Has "monitoring.coreos.com/v1") (.Values.service.metrics.enabled) }}
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "keto.fullname" . }}-metrics
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/component: metrics
{{ include "keto.labels" . | indent 4 }}
  {{- with .Values.serviceMonitor.labels }}
      {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.service.metrics.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  endpoints:
  - path: /metrics/prometheus
    port: {{ .Values.service.metrics.name }}
    scheme: {{ .Values.serviceMonitor.scheme }}
    interval: {{ .Values.serviceMonitor.scrapeInterval }}
    scrapeTimeout: {{ .Values.serviceMonitor.scrapeTimeout }}
    {{- with .Values.serviceMonitor.tlsConfig }}
    tlsConfig:
      {{- toYaml . | nindent 6 }}
    {{- end }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "keto.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
      app.kubernetes.io/component: metrics
  namespaceSelector:
    any: true
{{- end -}}
