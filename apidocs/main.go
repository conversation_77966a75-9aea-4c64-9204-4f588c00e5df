package main

import (
	"fmt"
	"log"
	"os"
	"runtime/debug"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var rootCmd = &cobra.Command{
	Use:              "apidocs",
	Short:            "API documentation server",
	TraverseChildren: true,
	PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
		logPath := viper.GetString("log")
		if logPath != "" && logPath != "stdout" && logPath != "-" {
			f, err := os.OpenFile(logPath, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0644)
			Assert(err, "failed to open log file")
			log.SetOutput(f)
			// defer f.Close()
		}
		return nil
	},
}

var version string
var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "Print version info",
	RunE: func(cmd *cobra.Command, args []string) error {
		if version == "" {
			if bi, ok := debug.ReadBuildInfo(); ok {
				version = bi.Main.Version
			}
		}
		fmt.Println(version)
		return nil
	},
}

func main() {
	log.SetFlags(log.Ldate | log.Ltime | log.Lmicroseconds)

	rootCmd.AddCommand(versionCmd)
	rootCmd.PersistentFlags().StringP("log", "l", "", "path to log file; default to stdout")
	viper.AutomaticEnv()
	viper.BindPFlags(rootCmd.Flags())

	if err := rootCmd.Execute(); err != nil {
		os.Exit(1)
	}
}

func Assert(err error, msg string) {
	if err != nil {
		log.Fatalln(msg+":", err)
	}
}

func PanicIf(err error, msg string) {
	if err != nil {
		log.Println(msg+":", err)
		panic(err)
	}
}
