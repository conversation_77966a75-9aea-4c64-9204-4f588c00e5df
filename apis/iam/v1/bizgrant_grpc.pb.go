// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: iam/v1/bizgrant.proto

package iam

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Bizgrants_CreateBizgrant_FullMethodName = "/iam.v1.Bizgrants/CreateBizgrant"
	Bizgrants_DeleteBizgrant_FullMethodName = "/iam.v1.Bizgrants/DeleteBizgrant"
	Bizgrants_ListBizgrant_FullMethodName   = "/iam.v1.Bizgrants/ListBizgrant"
)

// BizgrantsClient is the client API for Bizgrants service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BizgrantsClient interface {
	CreateBizgrant(ctx context.Context, in *CreateBizgrantRequest, opts ...grpc.CallOption) (*CreateBizgrantReply, error)
	DeleteBizgrant(ctx context.Context, in *DeleteBizgrantRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListBizgrant(ctx context.Context, in *ListBizgrantRequest, opts ...grpc.CallOption) (*ListBizgrantReply, error)
}

type bizgrantsClient struct {
	cc grpc.ClientConnInterface
}

func NewBizgrantsClient(cc grpc.ClientConnInterface) BizgrantsClient {
	return &bizgrantsClient{cc}
}

func (c *bizgrantsClient) CreateBizgrant(ctx context.Context, in *CreateBizgrantRequest, opts ...grpc.CallOption) (*CreateBizgrantReply, error) {
	out := new(CreateBizgrantReply)
	err := c.cc.Invoke(ctx, Bizgrants_CreateBizgrant_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bizgrantsClient) DeleteBizgrant(ctx context.Context, in *DeleteBizgrantRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Bizgrants_DeleteBizgrant_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bizgrantsClient) ListBizgrant(ctx context.Context, in *ListBizgrantRequest, opts ...grpc.CallOption) (*ListBizgrantReply, error) {
	out := new(ListBizgrantReply)
	err := c.cc.Invoke(ctx, Bizgrants_ListBizgrant_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BizgrantsServer is the server API for Bizgrants service.
// All implementations must embed UnimplementedBizgrantsServer
// for forward compatibility
type BizgrantsServer interface {
	CreateBizgrant(context.Context, *CreateBizgrantRequest) (*CreateBizgrantReply, error)
	DeleteBizgrant(context.Context, *DeleteBizgrantRequest) (*emptypb.Empty, error)
	ListBizgrant(context.Context, *ListBizgrantRequest) (*ListBizgrantReply, error)
	mustEmbedUnimplementedBizgrantsServer()
}

// UnimplementedBizgrantsServer must be embedded to have forward compatible implementations.
type UnimplementedBizgrantsServer struct {
}

func (UnimplementedBizgrantsServer) CreateBizgrant(context.Context, *CreateBizgrantRequest) (*CreateBizgrantReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBizgrant not implemented")
}
func (UnimplementedBizgrantsServer) DeleteBizgrant(context.Context, *DeleteBizgrantRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBizgrant not implemented")
}
func (UnimplementedBizgrantsServer) ListBizgrant(context.Context, *ListBizgrantRequest) (*ListBizgrantReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBizgrant not implemented")
}
func (UnimplementedBizgrantsServer) mustEmbedUnimplementedBizgrantsServer() {}

// UnsafeBizgrantsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BizgrantsServer will
// result in compilation errors.
type UnsafeBizgrantsServer interface {
	mustEmbedUnimplementedBizgrantsServer()
}

func RegisterBizgrantsServer(s grpc.ServiceRegistrar, srv BizgrantsServer) {
	s.RegisterService(&Bizgrants_ServiceDesc, srv)
}

func _Bizgrants_CreateBizgrant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBizgrantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BizgrantsServer).CreateBizgrant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bizgrants_CreateBizgrant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BizgrantsServer).CreateBizgrant(ctx, req.(*CreateBizgrantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bizgrants_DeleteBizgrant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBizgrantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BizgrantsServer).DeleteBizgrant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bizgrants_DeleteBizgrant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BizgrantsServer).DeleteBizgrant(ctx, req.(*DeleteBizgrantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bizgrants_ListBizgrant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBizgrantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BizgrantsServer).ListBizgrant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bizgrants_ListBizgrant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BizgrantsServer).ListBizgrant(ctx, req.(*ListBizgrantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Bizgrants_ServiceDesc is the grpc.ServiceDesc for Bizgrants service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Bizgrants_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "iam.v1.Bizgrants",
	HandlerType: (*BizgrantsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateBizgrant",
			Handler:    _Bizgrants_CreateBizgrant_Handler,
		},
		{
			MethodName: "DeleteBizgrant",
			Handler:    _Bizgrants_DeleteBizgrant_Handler,
		},
		{
			MethodName: "ListBizgrant",
			Handler:    _Bizgrants_ListBizgrant_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "iam/v1/bizgrant.proto",
}
