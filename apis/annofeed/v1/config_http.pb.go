// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: annofeed/v1/config.proto

package annofeed

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationConfigsGetVersion = "/annofeed.v1.Configs/GetVersion"

type ConfigsHTTPServer interface {
	GetVersion(context.Context, *emptypb.Empty) (*GetVersionReply, error)
}

func RegisterConfigsHTTPServer(s *http.Server, srv ConfigsHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/version", _Configs_GetVersion0_HTTP_Handler(srv))
}

func _Configs_GetVersion0_HTTP_Handler(srv ConfigsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationConfigsGetVersion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetVersion(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetVersionReply)
		return ctx.Result(200, reply)
	}
}

type ConfigsHTTPClient interface {
	GetVersion(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *GetVersionReply, err error)
}

type ConfigsHTTPClientImpl struct {
	cc *http.Client
}

func NewConfigsHTTPClient(client *http.Client) ConfigsHTTPClient {
	return &ConfigsHTTPClientImpl{client}
}

func (c *ConfigsHTTPClientImpl) GetVersion(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*GetVersionReply, error) {
	var out GetVersionReply
	pattern := "/v1/version"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationConfigsGetVersion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
