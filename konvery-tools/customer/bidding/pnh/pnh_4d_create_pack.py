import os
import sys
import shutil
import numpy as np
import copy
sys.path.append('.')
from customer.common import tools
from customer.common.merge_pcd import merge_pcds_to_one


def parse_params(params_file):
    sensor_params = tools.get_json_data(params_file)
    rotation = sensor_params['rotation']
    translation = sensor_params['translation']
    extrinsic = np.eye(4)
    extrinsic[:3, :3] = np.array(rotation).reshape(3, 3)
    extrinsic[:3, 3] = translation
    distortion = sensor_params['distortion']
    intrinsic = np.array(sensor_params['intrinsic'])
    image_size = sensor_params['image_size']
    if os.path.basename(params_file) == 'cam_front.json':
        distortion = sensor_params['undistort_distortion']
        intrinsic = np.array(sensor_params['undistort_intrinsic'])
        intrinsic = intrinsic * 2
        intrinsic[-1] = 1
        image_size = image_size * 2

    params = {
        'intrinsic': intrinsic.tolist(),
        'extrinsic': np.linalg.inv(extrinsic).flatten().tolist(),
        'distortion': [0] + distortion,
    }
    return params, image_size


def run(input_dir, output_dir, use_imu=False):
    camera_names = [cam for cam in os.listdir(input_dir) if cam.startswith('cam_')]
    sensor_params_dir = os.path.join(input_dir, 'sensors')
    out_clip_dir = os.path.join(output_dir, os.path.basename(input_dir))
    os.mkdir(out_clip_dir)
    lidar_dir = os.path.join(input_dir, 'rslidar_points')
    lidar_files = sorted(tools.get_file_by_extension(lidar_dir, '.pcd'), key=lambda x: x.name)
    cameras_params = dict()
    for camera_name in camera_names:
        sensor_params_file = os.path.join(sensor_params_dir, f'{camera_name}.json')
        sensor_params, image_size = parse_params(sensor_params_file)
        cameras_params[camera_name] = sensor_params
    pcds = []
    poses = []
    first_frame = None

    if not use_imu:
        with open(os.path.join(input_dir, 'pose.txt')) as f:
            pose_lines = f.readlines()
    for lidar_file_idx in range(0, len(lidar_files)):
        lidar_file = lidar_files[lidar_file_idx]
        if use_imu:
            ins_file = os.path.join(input_dir, 'Ins', lidar_file.name.split('.')[0] + '.json')
            ins_data = tools.get_json_data(ins_file)
            pose_mat = tools.get_extrinsic_by_txyz_rxyz_opencv(
                np.array([ins_data['utm_position.x'], ins_data['utm_position.y'], ins_data['utm_position.z']]),
                np.array([ins_data['attitude.x'], ins_data['attitude.y'], ins_data['attitude.z']])
            )
        else:
            pose_mat = np.array((pose_lines[lidar_file_idx].strip() + ' 0 0 0 1').split(), dtype=np.float32).reshape(4, 4)

        pose_mat_inv = np.linalg.inv(pose_mat)
        pose = tools.mat_to_pose(pose_mat)
        pcds.append(lidar_file)
        poses.append(pose_mat)
        out_frame_dir = os.path.join(out_clip_dir, lidar_file.name.split('.')[0])
        os.mkdir(out_frame_dir)
        if first_frame is None:
            first_frame = out_frame_dir
        for camera_name in camera_names:
            img_file = os.path.join(input_dir, camera_name, lidar_file.name.split('.')[0] + '.jpg')
            shutil.copyfile(img_file, os.path.join(out_frame_dir, f'{camera_name}.jpg'))
        new_cameras_params = copy.deepcopy(cameras_params)
        for camera_name in camera_names:
            sensor_params = np.array(new_cameras_params[camera_name]['extrinsic']).reshape(4, 4)
            new_cameras_params[camera_name]['extrinsic'] = (sensor_params @ pose_mat_inv).flatten().tolist()
        params = {
            "meta": {
                "version": "v2"
            },
            "lidar": {
                "viewpoint": pose,
                "pose": [0, 0, 0, 0, 0, 0, 1],
            },
            "cameras": new_cameras_params,
        }
        tools.write_json_file(params, os.path.join(out_frame_dir, 'params.json'))
    if use_imu:
        merge_pcds_to_one(pcds, poses, os.path.join(first_frame, 'lidar.pcd'), has_intensity=True)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
