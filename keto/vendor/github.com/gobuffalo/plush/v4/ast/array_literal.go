package ast

import (
	"bytes"
	"strings"
)

type ArrayLiteral struct {
	TokenAble
	Elements []Expression
}

var _ Expression = &ArrayLiteral{}

func (al *ArrayLiteral) expressionNode() {}

func (al *ArrayLiteral) String() string {
	var out bytes.Buffer

	elements := []string{}
	for _, el := range al.Elements {
		elements = append(elements, el.String())
	}

	out.WriteString("[")
	out.WriteString(strings.Join(elements, ", "))
	out.WriteString("]")

	return out.String()
}
