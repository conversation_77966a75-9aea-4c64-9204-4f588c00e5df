package main

import (
	"iam/internal/conf"

	"gitlab.rp.konvery.work/platform/pkg/jwt"
	"gitlab.rp.konvery.work/platform/pkg/notice/alisms"
)

func initJWT(cfg *conf.JWT) {
	jwt.Init(&jwt.Config{
		SignMethod: cfg.SignMethod,
		SignKey:    cfg.Sign<PERSON>ey,
		Issuer:     cfg.Issuer,
		Audience:   cfg.Audience,
		TTL:        cfg.Ttl.AsDuration(),
	})
}

func initAlisms(cfg *conf.AliSMS) {
	alisms.Init(&alisms.Config{
		AccessKey:  cfg.AccessKey,
		SecretKey:  cfg.<PERSON>,
		RegionID:   cfg.RegionId,
		SignName:   cfg.SignName,
		TemplateID: cfg.TemplateId,
	})
}
