version: v1

plugins:
  - plugin: buf.build/protocolbuffers/go
    out: proto
    opt: paths=source_relative

  - plugin: buf.build/grpc/go
    out: proto
    opt: paths=source_relative,require_unimplemented_servers=false

  - plugin: buf.build/protocolbuffers/js
    out: proto
    opt: import_style=commonjs,binary

  - plugin: buf.build/grpc/node
    out: proto
    opt: grpc_js,binary,import_style=commonjs

  - name: ts
    out: proto
    opt: ts_out=proto
    path: node_modules/.bin/protoc-gen-ts

  - name: doc
    out: proto
    opt: markdown,proto/buf.md
    strategy: all
