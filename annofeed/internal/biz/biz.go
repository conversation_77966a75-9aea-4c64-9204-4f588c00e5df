package biz

import (
	"context"

	"annofeed/internal/data/serial"
	"annofeed/internal/signer"

	"github.com/go-kratos/kratos/v2/encoding"
	"github.com/go-kratos/kratos/v2/encoding/json"
	"github.com/google/wire"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// ProviderSet is biz providers.
var ProviderSet = wire.NewSet(NewFilesBiz, NewDatasBiz, NewRawdataBiz, signer.NewS3Client, NewEventsBiz)

type JSON = datatypes.JSON
type DeletedAt = gorm.DeletedAt
type Tx = *gorm.DB
type FieldMask = field.Mask
type TxAction func(ctx context.Context, v any) error

func JSONCodec() encoding.Codec { return encoding.GetCodec("json") }

type StrMap = serial.Map[string, string]

func init() {
	json.MarshalOptions.EmitUnpopulated = true
	json.MarshalOptions.UseProtoNames = true
}

type Pager = repo.Pager
type PageToken = repo.PageToken

type ValidValue[T any] struct {
	Value T
	Valid bool
}

func NewValidValue[T any](v T) ValidValue[T] {
	return ValidValue[T]{
		Value: v,
		Valid: true,
	}
}

func (o *ValidValue[T]) Set(v T) {
	o.Value = v
	o.Valid = true
}
