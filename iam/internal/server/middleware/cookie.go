package middleware

import (
	"context"
	stdhttp "net/http"
	"regexp"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
)

type cookieCfg struct {
	Domain string
}

func SetCookieFilter(logger log.Logger, cookieDomain string) http.FilterFunc {
	cc := &cookieCfg{
		Domain: cookieDomain,
	}
	assumePathExp := regexp.MustCompile("/users/[^/]+/assume$")
	return func(h stdhttp.Handler) stdhttp.Handler {
		return Handler(func(w stdhttp.ResponseWriter, r *stdhttp.Request) {
			if strings.HasSuffix(r.URL.Path, "/users/logout") {
				logout(w, cc)
				return
			}
			if !(strings.HasSuffix(r.URL.Path, "/users/login") ||
				strings.HasSuffix(r.URL.Path, "/users/token/refresh") ||
				assumePathExp.MatchString(r.URL.Path)) {
				h.ServeHTTP(w, r)
				return
			}

			ctx := newCtxWithCookieSlot(r.Context())
			r = r.WithContext(ctx)
			w = &respWriter{
				ResponseWriter: w,
				ctx:            ctx,
				cookie:         cc,
			}
			h.ServeHTTP(w, r)
		})
	}
}

type Handler func(w stdhttp.ResponseWriter, r *stdhttp.Request)

func (o Handler) ServeHTTP(w stdhttp.ResponseWriter, r *stdhttp.Request) {
	o(w, r)
}

type respWriter struct {
	stdhttp.ResponseWriter
	cookie *cookieCfg
	ctx    context.Context
}

func (o *respWriter) WriteHeader(statusCode int) {
	c := cookieSlotFromCtx(o.ctx)
	expires := time.Now().Add(24 * time.Hour)
	writeCookiesAndHeader(o.ResponseWriter, o.cookie, c, expires, statusCode)
}

func writeCookie(w stdhttp.ResponseWriter, cc *cookieCfg, name, value string, expires time.Time) {
	cookie := &stdhttp.Cookie{
		Name:     name,
		Value:    value,
		Path:     "/",
		Domain:   cc.Domain,
		HttpOnly: true,
		Expires:  expires,
		SameSite: stdhttp.SameSiteNoneMode,
		Secure:   true,
	}
	stdhttp.SetCookie(w, cookie)
}

func writeCookiesAndHeader(w stdhttp.ResponseWriter, cc *cookieCfg, c *Cookies, expires time.Time, statusCode int) {
	if statusCode == stdhttp.StatusOK {
		writeCookie(w, cc, "JWT", c.Token, expires)
		writeCookie(w, cc, "Shunt", c.Shunt, expires)
		if c.Shunt != "" {
			w.Header().Add("x-md-Shunt", c.Shunt)
		}
	}
	w.WriteHeader(statusCode)
}

func logout(w stdhttp.ResponseWriter, cc *cookieCfg) {
	writeCookiesAndHeader(w, cc, &Cookies{}, time.Unix(0, 0), stdhttp.StatusOK)
}

// func writeErr(w stdhttp.ResponseWriter, err *errors.Error) {
// 	w.WriteHeader(int(err.Code))
// 	data, _ := json.Marshal(err)
// 	w.Write(data)
// }

type Cookies struct {
	Token string
	Shunt string // for traffic control
}
type cookieSlotContextKey struct{}

func newCtxWithCookieSlot(ctx context.Context) context.Context {
	var v Cookies
	return context.WithValue(ctx, cookieSlotContextKey{}, &v)
}

func cookieSlotFromCtx(ctx context.Context) *Cookies {
	c, _ := ctx.Value(cookieSlotContextKey{}).(*Cookies)
	return c
}

func SetCookie(ctx context.Context, c Cookies) {
	if pc := cookieSlotFromCtx(ctx); pc != nil {
		*pc = c
	}
}
