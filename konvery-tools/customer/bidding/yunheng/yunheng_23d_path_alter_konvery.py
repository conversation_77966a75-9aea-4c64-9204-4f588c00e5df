import os
import sys
import shutil
import json
import numpy as np
import math

sys.path.append('.')
from customer.common import tools

cameras = ['FRAME_CAMERA360_FRONT', 'FRAME_CAMERA360_REAR']
camera_names = ['imagePath_front', 'imagePath_rear']
total_frames = 30


def construct_config(input_dir, output_seg_dir):
    config = {
        "meta": {
            "version": "v2"
        },
        "lidars": {},
        "cameras": {},
    }
    file = tools.get_file_by_extension(os.path.join(input_dir, 'label'), '.json')[1]
    with open(file) as f:
        params = json.load(f)['conf']
    for camera in cameras:
        trans = list(filter(lambda x: x['targetFrame'] == camera, params['connectedFrameTransContainer']['connectedFrameTrans']))[0]['trans']
        extrinsic = tools.get_extrinsic_by_txyz_rxyz(np.array(trans))
        extrinsic_list = extrinsic.reshape((1, -1)).tolist()[0]
        extrinsic_list.extend([0, 0, 0, 1])
        extrinsic = extrinsic_list
        distortion = [0] + params['cameraCalibrationIntrinsicMap']['cameraParamMap'][camera]['distortionParam']
        intrinsic = params['cameraCalibrationIntrinsicMap']['cameraParamMap'][camera]['intrisicParam']
        camera_json = {
            "extrinsic": extrinsic,
            "intrinsic": intrinsic,
            "distortion": distortion,
        }
        config['cameras'][camera] = camera_json

    lidars = []
    with open(os.path.join(input_dir, 'processed_pointcloud', 'timestamp_with_tf.json')) as f:
        poses_json = json.load(f)
    lidars_file = sorted(tools.get_file_by_extension(output_seg_dir, '.pcd'), key=lambda x: x.name)
    for lidar_file in lidars_file:
        pose = tools.mat_to_pose(np.array(poses_json[os.path.basename(os.path.dirname(lidar_file))]).reshape((4, 4)))
        lidars.append({'pose': pose})
    config['lidars'] = lidars
    with open(os.path.join(output_seg_dir, 'params.json'), 'w') as f:
        json.dump(config, f, indent=4)


def construct_annos(input_dir, output_dir):
    data_dir = os.path.join(output_dir, 'data')
    annos_dir = os.path.join(output_dir, 'annos')
    os.mkdir(annos_dir)
    for collection in tools.listdir(data_dir):
        annos_collection_dir = os.path.join(annos_dir, collection)
        os.mkdir(annos_collection_dir)
        data_collection_dir = os.path.join(data_dir, collection)
        for element in tools.listdir(data_collection_dir):
            if not os.path.isdir(os.path.join(data_collection_dir, element)):
                continue
            annos_element_dir = os.path.join(annos_collection_dir, element)
            os.mkdir(annos_element_dir)

            label_file = os.path.join(input_dir, 'label', element + '.json')
            with open(label_file) as f:
                label_datas = json.load(f)['elem']
            track_ids = dict()
            objs = []
            for label_data in label_datas:
                if label_data['token'] not in track_ids:
                    track_ids[label_data['token']] = len(track_ids) + 1
                qx, qy, qz, qw = tools.convert_eular_to_quaternion(float(label_data['yaw']), 0, 0, False)
                obj = {
                    "uuid": label_data['token'],
                    "track_id": str(track_ids[label_data['token']]),
                    "label": {
                        "name": label_data['class'],
                        "widget": {
                            "name": "cuboid",
                            "data": [float(label_data['position']['x']), float(label_data['position']['y']), float(label_data['position']['z']),
                                     float(label_data['size']['length']), float(label_data['size']['width']), float(label_data['size']['height']),
                                     qx, qy, qz, qw]},
                        "attrs": [{"flag": label_data['flag']}]
                    }
                }
                objs.append(obj)

            rawdata_anno = {
                "name": f"{collection}/{element}/lidar.pcd",
                "objects": objs,
                "attrs": []
            }
            annos = {
                "name": f"{collection}/{element}",
                "rawdata_annos": [rawdata_anno],
                "attrs": [],
                "ins_cnt": len(label_datas)
            }
            annos_result = {"element_annos": [annos]}

            with open(os.path.join(annos_element_dir, 'annos.json'), 'w') as f:
                json.dump(annos_result, f, indent=4)


def run(input_dir, output_dir, pre_label=False):
    frame_cnt = 0
    tools.check_dir(output_dir)
    data_dir = os.path.join(output_dir, 'data')
    collection_dir = os.path.join(data_dir, "collection0001")
    os.makedirs(collection_dir)
    labels = tools.get_json_files(os.path.join(input_dir, 'label'))

    period = int(len(labels) / total_frames)
    for idx, label in enumerate(labels):
        if idx % period != 0:
            continue
        label_data = tools.get_json_data(label)
        seg_dir = os.path.join(collection_dir, os.path.basename(label_data['recordname']).replace("processed_pointcloud", "").split('.')[0])
        os.mkdir(seg_dir)
        shutil.copyfile(os.path.join(input_dir, label_data['recordname']),
                        os.path.join(seg_dir, 'lidar.pcd'))
        for idx, camera_name in enumerate(camera_names):
            shutil.copyfile(os.path.join(input_dir, label_data[camera_name]),
                            os.path.join(seg_dir, cameras[idx] + '.jpg'))
        frame_cnt += 1
        if frame_cnt == total_frames:
            break

    construct_config(input_dir, collection_dir)
    if pre_label:
        construct_annos(input_dir, output_dir)


if __name__ == '__main__':
    args = tools.get_args()
    run(args.input, args.out, True)
