-- mig:up 2022070101 initial
CREATE TABLE jobs
(
    id           BIGSERIAL    NOT NULL PRIMARY KEY,
    -- uid          VARCHAR(32)  NOT NULL UNIQUE,
    lot_id       BIGINT       NOT NULL,
    idx_in_lot   INTEGER      NOT NULL DEFAULT 0,
    subtype      VARCHAR(32)  NOT NULL DEFAULT '', -- '': hybrid, non-empty: subjobs
    elems_cnt    INTEGER      NOT NULL DEFAULT 0,
    ins_cnt      INTEGER      NOT NULL DEFAULT 0,
    ins_total    INTEGER      NOT NULL DEFAULT 0,  -- include interpolated objects
    phase        SMALLINT     NOT NULL DEFAULT 0,  -- phase number, starts from 1; last_phase+1 means finished
    -- phase_name   VARCHAR(32)  NOT NULL,           -- labeling, review-1, review-2, ...
    state        SMALLINT     NOT NULL DEFAULT 0,  -- state in phase: 0-unstart, 1-doing, 2-checking, 3-committed, 4-finished
    cause        VARCHAR(64)  NOT NULL DEFAULT '', -- state cause
    executor_uid VARCHAR(32)  NOT NULL DEFAULT '',
    last_executor VARCHAR(32) NOT NULL DEFAULT '',
    last_execteam VARCHAR(32) NOT NULL DEFAULT '',
    created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_jobs_lot_id_idx_in_lot ON jobs (lot_id, idx_in_lot);
CREATE INDEX idx_jobs_lot_id_phase_state ON jobs (lot_id, phase, state);
CREATE INDEX idx_jobs_lot_id_executor_uid ON jobs (lot_id, executor_uid);
CREATE INDEX idx_jobs_lot_id_last_executor ON jobs (lot_id, last_executor);
CREATE INDEX idx_jobs_lot_id_last_execteam ON jobs (lot_id, last_execteam);

CREATE TABLE joballies
(
    id           BIGSERIAL    NOT NULL PRIMARY KEY,  -- job id
    elements     JSON         NOT NULL, -- info of data to be annotated
    annotations  JSON, -- annotation result
    comments     JSON, -- comments to objects in annotations
    created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);

-- mig:up 2023091201 add column rawdata_params
ALTER TABLE joballies ADD COLUMN rawdata_params JSON;

-- mig:down 2023091201 add column rawdata_params
ALTER TABLE joballies DROP COLUMN rawdata_params;

-- mig:up 2023122901 add column elements_segments, annotations_uri and comments_uri
ALTER TABLE joballies ADD COLUMN elements_segments JSON;
ALTER TABLE joballies ADD COLUMN annotations_uri VARCHAR(4096);
ALTER TABLE joballies ADD COLUMN comments_uri VARCHAR(4096);

-- mig:down 2023122901 add column elements_segments, annotations_uri and comments_uri
ALTER TABLE joballies DROP COLUMN elements_segments;
ALTER TABLE joballies DROP COLUMN annotations_uri;
ALTER TABLE joballies DROP COLUMN comments_uri;

-- mig:up 2024012601 add column draft
ALTER TABLE joballies ADD COLUMN draft JSONB;

-- mig:down 2024012601 add column draft
ALTER TABLE joballies DROP COLUMN draft;

-- mig:up 2024041701 add jobelems table to record mapping between element names and job ids
CREATE TABLE jobelems
(
    id            BIGSERIAL    NOT NULL PRIMARY KEY,
    lot_id        BIGINT       NOT NULL,
    job_id        BIGINT       NOT NULL,
    data_uid      VARCHAR(32)  NOT NULL,
    elem_idx      INTEGER      NOT NULL DEFAULT 0,
    elem_name     VARCHAR(256) NOT NULL,
    created_at    TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_jobelems_lot_id_elem_name_job_id ON jobelems (lot_id, elem_name, job_id);
CREATE INDEX idx_jobelems_lot_id_elem_idx_job_id ON jobelems (lot_id, elem_idx, job_id);
CREATE INDEX idx_jobelems_data_uid_elem_name_job_id ON jobelems (data_uid, elem_name, job_id);
CREATE INDEX idx_jobelems_data_uid_elem_idx_job_id ON jobelems (data_uid, elem_idx, job_id);

-- mig:down 2024041701 add jobelems table to record mapping between element names and job ids
DROP TABLE jobelems;

-- mig:up 2022070101 initial
CREATE TABLE joblogs
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    job_id      BIGINT       NOT NULL,
    operator_uid VARCHAR(32)  NOT NULL,
    op_org_uid  VARCHAR(32)  NOT NULL DEFAULT 'aaaaanoteam', -- operator orgnization uid
    action      VARCHAR(32)  NOT NULL,
    -- event       VARCHAR(32)  NOT NULL,
    from_phase  SMALLINT     NOT NULL DEFAULT 0,
    from_state  SMALLINT     NOT NULL DEFAULT 0,
    to_phase    SMALLINT     NOT NULL DEFAULT 0,
    to_state    SMALLINT     NOT NULL DEFAULT 0,
    to_executor_uid VARCHAR(32) NOT NULL DEFAULT '',
    to_execteam VARCHAR(32)  NOT NULL DEFAULT '',
    details     JSONB,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_joblogs_job_id ON joblogs (job_id);
CREATE INDEX idx_joblogs_created_at ON joblogs (created_at);

-- CREATE TABLE labeldescriptors
-- (
--     id         BIGSERIAL    NOT NULL PRIMARY KEY,
--     type       VARCHAR(128) NOT NULL UNIQUE, -- class/widget/...
--     name       VARCHAR(128) NOT NULL UNIQUE,
--     "desc"     VARCHAR(512) NOT NULL DEFAULT '',
--     langs      JSONB,
--     -- created_at TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
--     -- updated_at TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
--     -- deleted_at TIMESTAMPTZ
-- );

CREATE TABLE labelcls
(
    id         BIGSERIAL    NOT NULL PRIMARY KEY,
    name       VARCHAR(128) NOT NULL UNIQUE,
    langs      JSONB,
    created_at TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);

CREATE TABLE labelwidgets
(
    id         BIGSERIAL    NOT NULL PRIMARY KEY,
    name       VARCHAR(128) NOT NULL UNIQUE,
    langs      JSONB,
    created_at TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);

CREATE TABLE lots
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    -- uid         VARCHAR(32)  NOT NULL UNIQUE,
    name        VARCHAR(128) NOT NULL,
    type        VARCHAR(128) NOT NULL,
    "desc"      VARCHAR(512) NOT NULL DEFAULT '',
    project_id  BIGINT       NOT NULL DEFAULT 0,
    tpl_id      BIGINT       NOT NULL DEFAULT 0, -- template ID
    order_id    BIGINT       NOT NULL DEFAULT 0,
    data_uid    VARCHAR(32)  NOT NULL,
    data_type   VARCHAR(128) NOT NULL DEFAULT '',
    autostart   BOOLEAN      NOT NULL DEFAULT false,
    priority    SMALLINT     NOT NULL DEFAULT 0, -- larger number indicates higher priority
    phase_count SMALLINT     NOT NULL,
    subtypes    SMALLINT     NOT NULL DEFAULT 0, -- number of sub-lots if it is splitted
    data_size   INTEGER      NOT NULL DEFAULT 0,
    ins_cnt     INTEGER      NOT NULL DEFAULT 0,
    ins_total   INTEGER      NOT NULL DEFAULT 0, -- include interpolated objects
    state       VARCHAR(32)  NOT NULL,
    creator_uid VARCHAR(32)  NOT NULL,
    org_uid     VARCHAR(32)  NOT NULL,
    hierarchy   VARCHAR(128) NOT NULL DEFAULT '', -- team hierarchy: lvl1-id/lvl2-id/...
    exp_end_time TIMESTAMPTZ,
    error       JSONB,
    has_error   BOOLEAN      NOT NULL DEFAULT false,
    job_ready   BOOLEAN      NOT NULL DEFAULT false,
    job_count   INTEGER      NOT NULL DEFAULT 0,
    job_size    INTEGER      NOT NULL DEFAULT 0,
    is_frame_series BOOLEAN  NOT NULL DEFAULT false,
    extra_data  JSONB,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    deleted_at  TIMESTAMPTZ
);
CREATE INDEX idx_lots_name ON lots ((lower(name))) INCLUDE (state, deleted_at);
CREATE INDEX idx_lots_type ON lots (type) INCLUDE (state, deleted_at);
CREATE INDEX idx_lots_project ON lots (project_id) INCLUDE (state, deleted_at);
CREATE INDEX idx_lots_state ON lots (state) INCLUDE (deleted_at);
CREATE INDEX idx_lots_creator_uid ON lots (creator_uid) INCLUDE (state, deleted_at);
CREATE INDEX idx_lots_org_uid ON lots (org_uid) INCLUDE (state, deleted_at);
-- CREATE INDEX idx_lots_hierarchy ON lots (hierarchy) INCLUDE (state, deleted_at);
CREATE INDEX idx_lots_order_id ON lots (order_id) INCLUDE (state, deleted_at);
CREATE INDEX idx_lots_created_at ON lots (created_at) INCLUDE (state, deleted_at);

-- mig:up 2023082901 add column can_export_annos
ALTER TABLE lots ADD COLUMN can_export_annos BOOLEAN NOT NULL DEFAULT TRUE;

-- mig:down 2023082901 add column can_export_annos
ALTER TABLE lots DROP COLUMN can_export_annos;

-- mig:up 2024031201 add column tags to lots
ALTER TABLE lots ADD COLUMN tags VARCHAR(64)[];
CREATE INDEX idx_lots_tags ON lots USING GIN (tags);

-- mig:down 2024031201 add column tags to lots
DROP INDEX idx_lots_tags;
ALTER TABLE lots DROP COLUMN tags;

-- mig:up 2022070101 initial
CREATE TABLE lotallies
(
    id           BIGSERIAL    NOT NULL PRIMARY KEY, -- lot id
    ontologies   JSONB,
    -- phases       JSONB,
    out          JSONB,
    -- reject_reasons JSONB,
    instruction  TEXT,
    sample_bits  JSONB,
    tool_config  JSONB,
    created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);

CREATE TABLE lottpls
(
    id         BIGSERIAL    NOT NULL PRIMARY KEY,
    -- uid        VARCHAR(32)  NOT NULL UNIQUE,
    name       VARCHAR(128) NOT NULL,
    type       VARCHAR(128) NOT NULL,
    "desc"     VARCHAR(512) NOT NULL DEFAULT '',
    job_size   INTEGER      NOT NULL DEFAULT 0,
    ontologies JSONB,
    phases     JSONB,
    out        JSONB,
    instruction TEXT,
    -- reject_reasons JSONB,
    creator_uid VARCHAR(32) NOT NULL,
    org_uid    VARCHAR(32) NOT NULL,
    created_at TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);
CREATE INDEX idx_lottpls_name ON lottpls ((lower(name))) INCLUDE (type, deleted_at);;
CREATE INDEX idx_lottpls_type ON lottpls (type) INCLUDE (deleted_at);;
CREATE INDEX idx_lottpls_creator ON lottpls (creator_uid) INCLUDE (type, deleted_at);;
CREATE INDEX idx_lottpls_org_uid ON lottpls (org_uid) INCLUDE (type, deleted_at);;
CREATE INDEX idx_lottpls_created_at ON lottpls (created_at) INCLUDE (type, deleted_at);;

CREATE TABLE lotexecutors
(
    id           BIGSERIAL    NOT NULL PRIMARY KEY,
    lot_id       BIGINT       NOT NULL,
    phase        SMALLINT     NOT NULL DEFAULT 0, -- phase number, starts from 1
    subtype      VARCHAR(32)  NOT NULL DEFAULT '', -- '': hybrid, non-empty: subjobs
    user_uid     VARCHAR(32)  NOT NULL,
    team_uid     VARCHAR(32)  NOT NULL,
    status       SMALLINT     NOT NULL DEFAULT 0, -- 0-valid, 1-; reserved for future use
    created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_lotexecutors_lot_id_phase_subtype_user ON lotexecutors (lot_id, phase, subtype, user_uid) INCLUDE (team_uid, status);

CREATE TABLE lotphases
(
    id           BIGSERIAL    NOT NULL PRIMARY KEY,
    lot_id       BIGINT       NOT NULL,
    number       SMALLINT     NOT NULL DEFAULT 0, -- phase number, starts from 1
    subtype      VARCHAR(32)  NOT NULL DEFAULT '', -- '': hybrid, non-empty: subjobs
    name         VARCHAR(64)  NOT NULL, -- label/review-1/review-2/...
    type         VARCHAR(32)  NOT NULL, -- label/review
    execteam     VARCHAR(32)  NOT NULL DEFAULT '', -- assigned team uid
    editable     BOOLEAN      NOT NULL DEFAULT false,
    merge        BOOLEAN      NOT NULL DEFAULT false, -- whether to merge the annotation results of splitted jobs after this phase
    sample_percent REAL       NOT NULL DEFAULT 0, -- percent
    min_skill_level SMALLINT  NOT NULL DEFAULT 0,
    timeout      INTEGER      NOT NULL DEFAULT 0, -- in seconds
    quota        JSONB,                           -- execteam quota in this phase
    claimed_jobs INTEGER      NOT NULL DEFAULT 0, -- the number of claimed jobs
    updated_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_lotphases_lot_id_number_execteam ON lotphases (lot_id, number, execteam) INCLUDE (name, type, editable, sample_percent, min_skill_level, timeout, merge);

-- mig:up 2024030101 add claim_policy column
ALTER TABLE lotphases ADD COLUMN claim_policy VARCHAR(32) NOT NULL DEFAULT '';

-- mig:down 2024030101 add claim_policy column
ALTER TABLE lotphases DROP COLUMN claim_policy;

-- mig:up 2022070101 initial
CREATE TABLE orders
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    name        VARCHAR(128) NOT NULL,
    size        INTEGER      NOT NULL DEFAULT 0,
    source      JSONB,
    state       VARCHAR(32)  NOT NULL,
    error       VARCHAR(512) NOT NULL DEFAULT '',
    ins_total   INTEGER      NOT NULL DEFAULT 0, -- include interpolated objects
    anno_result_url VARCHAR(4096) NOT NULL DEFAULT '',
    data_uid    VARCHAR(32)  NOT NULL DEFAULT '',
    org_uid     VARCHAR(32)  NOT NULL DEFAULT '',
    creator_uid VARCHAR(32)  NOT NULL DEFAULT '',
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    deleted_at  TIMESTAMPTZ
);
CREATE INDEX idx_orders_name ON orders ((lower(name))) INCLUDE (state, deleted_at);
CREATE INDEX idx_orders_creator ON orders (creator_uid) INCLUDE (state, deleted_at);
CREATE INDEX idx_orders_org_uid ON orders (org_uid) INCLUDE (state, deleted_at);
CREATE INDEX idx_orders_created_at ON orders (created_at) INCLUDE (state, deleted_at);

-- mig:up 2023082901 add column can_export_annos
ALTER TABLE orders ADD COLUMN can_export_annos BOOLEAN NOT NULL DEFAULT TRUE;

-- mig:down 2023082901 add column can_export_annos
ALTER TABLE orders DROP COLUMN can_export_annos;

-- mig:up 2022070101 initial
CREATE TABLE projects
(
    id           BIGSERIAL    NOT NULL PRIMARY KEY,
    -- uid          VARCHAR(32)  NOT NULL UNIQUE,
    name         VARCHAR(128) NOT NULL,
    "desc"       VARCHAR(512) NOT NULL DEFAULT '',
    avatar       VARCHAR(512) NOT NULL DEFAULT '',
    status       VARCHAR(32)  NOT NULL,
    creator_uid  VARCHAR(32)  NOT NULL DEFAULT '',
    org_uid     VARCHAR(32)  NOT NULL DEFAULT '',
    created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    deleted_at   TIMESTAMPTZ
);
CREATE INDEX idx_projects_name ON projects ((lower(name))) INCLUDE (status, deleted_at);
CREATE INDEX idx_projects_creator ON projects (creator_uid) INCLUDE (status, deleted_at);
CREATE INDEX idx_projects_org_uid ON projects (org_uid) INCLUDE (status, deleted_at);
CREATE INDEX idx_projects_created_at ON projects (created_at) INCLUDE (status, deleted_at);

-- CREATE TABLE roles
-- (
--     id           BIGSERIAL    NOT NULL PRIMARY KEY,
--     name         VARCHAR(128) NOT NULL UNIQUE,
--     "desc"       VARCHAR(256) NOT NULL DEFAULT '',
--     avatar       VARCHAR(4096) NOT NULL DEFAULT '',
--     langs JSONB,
--     creator_uid  VARCHAR(32)  NOT NULL,
--     created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
--     updated_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
--     deleted_at   TIMESTAMPTZ
-- );

-- CREATE TABLE policies -- template
-- (
--     id           BIGSERIAL    NOT NULL PRIMARY KEY,
--     uid          VARCHAR(32)  NOT NULL UNIQUE,
--     name         VARCHAR(128) NOT NULL '',
--     -- role_name    VARCHAR(128) NOT NULL,
--     -- role_scope   JSONB, -- usually a team

--     resource     JSONB, -- no scope
--     actions      JSONB,
--     effect       VARCHAR(32)  NOT NULL DEFAULT 'allow',
--     creator_uid  VARCHAR(32)  NOT NULL,
--     created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
--     updated_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW()
-- );
-- CREATE INDEX idx_policies_role_name_scope ON policies (role_name, role_scope) INCLUDE (actions, effect);
-- CREATE INDEX idx_policies_resource ON policies (resource) INCLUDE (actions, effect);
-- CREATE INDEX idx_policies_name ON policies ((lower(name))) INCLUDE (actions, effect);
-- CREATE INDEX idx_policies_created_at ON policies (created_at) INCLUDE (actions, effect);

-- -- CREATE TABLE userroles (user <-> policy)
-- CREATE TABLE userpolicices (user <-> policy)
-- (
--     id           BIGSERIAL    NOT NULL PRIMARY KEY,
--     user_uid     VARCHAR(32)  NOT NULL,
--     -- role         VARCHAR(128) NOT NULL,
--     policy_id    VARCHAR(128) NOT NULL,
--     scope        JSONB, -- usually a project/lot
--     creator_uid  VARCHAR(32)  NOT NULL,
--     created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
--     updated_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW()
-- );
-- CREATE INDEX idx_userroles_user_uid_role_scope ON userroles (user_uid, role, scope);
-- CREATE INDEX idx_userroles_role_scope ON userroles (role, scope) INCLUDE (user_uid);
-- CREATE INDEX idx_userroles_scope ON userroles (scope) INCLUDE (user_uid, scope);
-- CREATE INDEX idx_userroles_created_at ON userroles (created_at) INCLUDE (user_uid, scope);

CREATE TABLE skills
(
    id           BIGSERIAL    NOT NULL PRIMARY KEY,
    type         VARCHAR(128) NOT NULL, -- lot type | label class | widget
    name         VARCHAR(128) NOT NULL,
    "desc"       VARCHAR(256) NOT NULL DEFAULT '',
    avatar       VARCHAR(512) NOT NULL DEFAULT '',
    max_level    SMALLINT     NOT NULL DEFAULT 0,
    langs        JSONB,
    creator_uid  VARCHAR(32)  NOT NULL,
    created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    deleted_at   TIMESTAMPTZ
);
CREATE UNIQUE INDEX idx_skills_type_name ON skills (type, name) INCLUDE (max_level);

CREATE TABLE userskills
(
    id           BIGSERIAL    NOT NULL PRIMARY KEY,
    user_uid     VARCHAR(32)  NOT NULL,
    skill_type   VARCHAR(128) NOT NULL,
    skill_name   VARCHAR(128) NOT NULL,
    skill_level  INTEGER      NOT NULL DEFAULT 0, -- 0 for practice, >= 1 for production
    -- scope        JSONB        NOT NULL DEFAULT '{}'::JSONB, -- usually a project/lot
    creator_uid  VARCHAR(32)  NOT NULL,
    created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_userskills_user_uid ON userskills (user_uid) INCLUDE (skill_type, skill_name, skill_level);
CREATE INDEX idx_userskills_skill ON userskills (skill_type, skill_name, skill_level) INCLUDE (user_uid);
-- CREATE INDEX idx_userskills_scope ON userskills (scope);

-- mig:down 2022070101 initial
DROP TABLE jobs;
DROP TABLE joballies;
DROP TABLE joblogs;
-- DROP TABLE labeldescriptors;
DROP TABLE labelcls;
DROP TABLE labelwidgets;
DROP TABLE lots;
DROP TABLE lotallies;
DROP TABLE lottpls;
DROP TABLE lotexecutors;
DROP TABLE lotphases;
DROP TABLE orders;
DROP TABLE projects;
-- DROP TABLE roles;
-- DROP TABLE policies;
-- DROP TABLE userroles;
DROP TABLE skills;
DROP TABLE userskills;

-- mig:up 2023031501 initial
-- grant organization biz permissions to a kam
CREATE TABLE bizgrants
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    grantor_uid VARCHAR(32)  NOT NULL,
    grantee_uid VARCHAR(32)  NOT NULL,
    org_uid     VARCHAR(32)  NOT NULL,
    -- biz         VARCHAR(32)  NOT NULL, -- business type, e.g. anno
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_bizgrants_grantee_uid_org_uid ON bizgrants (grantee_uid, org_uid);
CREATE INDEX idx_bizgrants_org_uid ON bizgrants (org_uid) INCLUDE (grantee_uid);
CREATE INDEX idx_bizgrants_grantor_uid ON bizgrants (grantor_uid);

-- grant a specific resource access to a pm
CREATE TABLE specgrants
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    grantor_uid VARCHAR(32)  NOT NULL,
    grantee_uid VARCHAR(32)  NOT NULL,
    item_id     BIGINT       NOT NULL,
    item_type   VARCHAR(32)  NOT NULL, -- item type, e.g. AnnoLot/...
    org_uid     VARCHAR(32)  NOT NULL, -- item's owner organization
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_specgrants_item_type_item_id_grantee_uid ON specgrants (item_type, item_id, grantee_uid) INCLUDE (org_uid);
CREATE INDEX idx_specgrants_grantee_uid_item_type_org_uid ON specgrants (grantee_uid, item_type, org_uid) INCLUDE (item_id);
CREATE INDEX idx_specgrants_org_uid_item_type ON specgrants (org_uid, item_type) INCLUDE (grantee_uid, item_id);
CREATE INDEX idx_specgrants_grantor_uid ON specgrants (grantor_uid);

-- mig:down 2023031501 initial
DROP TABLE bizgrants;
DROP TABLE specgrants;
