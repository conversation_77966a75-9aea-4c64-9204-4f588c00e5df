package middleware

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.rp.konvery.work/platform/apis/iam/v1"
)

func TestX(t *testing.T) {
	cases := []struct {
		in  any
		exp *iam.UserContext
	}{
		{
			in:  &iam.User{},
			exp: &iam.UserContext{},
		},
		{
			in:  &iam.User{Uid: "test", OrgType: iam.Team_Type_operator},
			exp: &iam.UserContext{User: &iam.User{Uid: "test", OrgType: iam.Team_Type_operator}},
		},
		{
			in: &iam.UserContext{
				User:     &iam.User{Uid: "test", OrgType: iam.Team_Type_supplier},
				AssumeBy: &iam.User{Uid: "asby", OrgType: iam.Team_Type_operator},
			},
			exp: &iam.UserContext{
				User:     &iam.User{Uid: "test", OrgType: iam.Team_Type_supplier},
				AssumeBy: &iam.User{Uid: "asby", OrgType: iam.Team_Type_operator},
			},
		},
	}
	for _, c := range cases {
		bs, err := enc.Marshal(c.in)
		assert.NoError(t, err)
		uc, err := parseUserCtx(bs)
		assert.NoError(t, err)
		assert.Equal(t, c.exp.GetUid(), uc.GetUid())
		assert.Equal(t, c.exp.GetOrgType(), uc.GetOrgType())
		assert.Equal(t, c.exp.AssumeBy.GetUid(), uc.AssumeBy.GetUid())
		assert.Equal(t, c.exp.AssumeBy.GetOrgType(), uc.AssumeBy.GetOrgType())
	}
}
