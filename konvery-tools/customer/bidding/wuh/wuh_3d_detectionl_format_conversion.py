import os
import sys
sys.path.append('.')
from customer.common import tools


def run(input_dir, output_dir):
    label_dirs = tools.find_dir_by_pre_name(input_dir, 'label')
    for label_dir in label_dirs:
        clip_name = os.path.basename(os.path.dirname(label_dir))
        out_sub_dir = os.path.join(output_dir, clip_name)
        os.makedirs(out_sub_dir)

        json_files = sorted(tools.get_json_files(label_dir), key=lambda x: x.name)
        for json_file in json_files:
            results = []
            json_data = tools.get_json_data(json_file)
            for label in json_data.get('lidar', []):
                anno = label['annotation']['data']
                rst = {
                    "obj_id": str(label['track_id']),
                    "obj_type": label['class'],
                    "psr": {
                        "position": anno['position'],
                        "rotation": anno['rotation'],
                        "scale": {
                            "x": anno['dimension']['l'],
                            "y": anno['dimension']['w'],
                            "z": anno['dimension']['h']
                        }
                    }
                }
                results.append(rst)
            out_file = os.path.join(out_sub_dir, json_file.name)
            tools.write_json_file(results, out_file)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
