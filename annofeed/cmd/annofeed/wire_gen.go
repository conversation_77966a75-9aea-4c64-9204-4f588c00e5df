// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"annofeed/internal/biz"
	"annofeed/internal/conf"
	"annofeed/internal/data"
	"annofeed/internal/server"
	"annofeed/internal/service"
	"annofeed/internal/signer"
	"annofeed/workflow"
	"annofeed/workflow/preparedata"
	"github.com/go-kratos/kratos/v2/log"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(string2 string, confServer *conf.Server, confData *conf.Data, fileServer *conf.FileServer, logger log.Logger) (*App, func(), error) {
	configsService := service.NewConfigsService()
	dataData, cleanup, err := data.NewData(confData, logger)
	if err != nil {
		return nil, nil, err
	}
	datasRepo := data.NewDatasRepo(dataData, logger)
	rawdataRepo := data.NewRawdataRepo(dataData, logger)
	client := signer.NewS3Client(fileServer)
	rawdataBiz := biz.NewRawdataBiz(rawdataRepo, fileServer, client, logger)
	backgroundTask := workflow.NewWorkflowStarter()
	datasBiz := biz.NewDatasBiz(datasRepo, rawdataRepo, rawdataBiz, backgroundTask, logger)
	datasService := service.NewDatasService(datasBiz, rawdataBiz)
	filesRepo := data.NewFilesRepo(dataData, logger)
	filesBiz := biz.NewFilesBiz(filesRepo, logger)
	filesService := service.NewFilesService(filesBiz, fileServer, client, logger)
	grpcServer := server.NewGRPCServer(confServer, configsService, datasService, filesService, logger)
	httpServer := server.NewHTTPServer(confServer, fileServer, rawdataBiz, configsService, datasService, filesBiz, filesService, client, logger)
	activities := preparedata.NewActivities(string2, datasBiz, filesBiz, rawdataBiz, logger)
	bizgrantsRepo := data.NewBizgrantsRepo(dataData, logger)
	eventsBiz := biz.NewEventsBiz(bizgrantsRepo, backgroundTask, logger)
	app := newApp(logger, grpcServer, httpServer, activities, eventsBiz)
	return app, func() {
		cleanup()
	}, nil
}
