import os
import shutil
import sys

sys.path.append('.')
from customer.common import tools

code = 0
structure_data = {}
processed_file_count = 0


def get_code_msg(msg_code):
    msg = "失败"
    if 0 == msg_code:
        msg = "成功"
    return msg


def check_mark_status(data):
    markable = False

    if "label_meta" in data.keys():
        if data["label_meta"]["mark_status"] == 0:
            markable = True
    else:
        markable = True

    return markable


def convert(json_file):
    json_data = tools.get_json_data(json_file)
    mark_status = check_mark_status(json_data)
    pcd_name = os.path.basename(json_file).replace(".json", ".pcd")
    res_data = structure_data[pcd_name]

    if not mark_status:
        print(f"{json_file} is not markable!!!")

    mark_data = json_data["lidar"]
    res_list = []

    for elem in mark_data:
        class_name = elem["class"]
        attrs = elem["attrs"]
        track_id = elem["track_id"]
        point_num = elem["point_num"]

        data = elem["annotation"]["data"]
        position = data["position"]
        dimension = data["dimension"]
        rotation = data["rotation"]
        res = {
            "class": class_name,
            "id": track_id,
            "position": position,
            "camera_visible": int(attrs["camera_visible"][0]),
            "size": {
                "width": dimension["w"],
                "height": dimension["h"],
                "depth": dimension["l"]
            },
            "yaw": rotation["z"]
        }
        res_list.append(res)
    res_data["elem"] = res_list
    return res_data


def run(input_dir, output_dir, structure_file):
    """
       主函数
    """
    global structure_data
    global processed_file_count

    input_dir = tools.unzip(input_dir)
    tools.check_dir(output_dir)
    structure_data = tools.get_json_data(structure_file)

    for label_dir in tools.find_dir_by_pre_name(input_dir, "label"):
        for json_file in tools.get_json_files(label_dir):
            res_data = convert(json_file.path)
            output_file = output_dir + os.sep + json_file.name
            tools.write_json_file(data=res_data, file=output_file, indent=4)
            processed_file_count += 1

    trans_result = {"code": code, "output": output_dir, "err_msg": get_code_msg(code),
                    "converted_file_count": processed_file_count}

    print(trans_result)
    return trans_result


"""
    项目： CWB项目 23D
    对接平台：云测新平台 
    功能： 将云测导出的标注结果，转换成客户要求的格式
    输入： 云测导出的结果文件
    输出： 分包后的数据
    开发： Justin
    https://qcjmmm41aj.feishu.cn/sheets/C1tfspMz1h6OMZtg5gLcN8S8nmf?from=from_copylink
"""
if __name__ == "__main__":

    args = tools.get_args()

    if args.input == "../../input/test":
        args.input = "/Users/<USER>/Downloads/Convert/CWB/test_in"
        args.pcd = "/Users/<USER>/Downloads/Convert/CWB/保留这个文件-导出时会用到.json"
        args.out = "/Users/<USER>/Downloads/cwb-23d-output"

    run(input_dir=args.input, output_dir=args.out, structure_file=args.pcd)
