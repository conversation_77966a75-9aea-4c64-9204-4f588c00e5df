package anno

import (
	"testing"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
)

func Test_CreateBizgrantRequest(t *testing.T) {
	req := anno.CreateBizgrantRequest{
		GranteeUid: "grantee1234",
		OrgUid:     "org12345678",
	}

	cases := []struct {
		name       string
		newReqFunc func(req anno.CreateBizgrantRequest) anno.CreateBizgrantRequest
		expectErr  bool
	}{
		{
			"happy_flow",
			nil,
			false,
		},
		{
			"GranteeUid_is_too_short",
			func(req anno.CreateBizgrantRequest) anno.CreateBizgrantRequest {
				req.GranteeUid = "grantee"
				return req
			},
			true,
		},
		{
			"GranteeUid_is_too_long",
			func(req anno.CreateBizgrantRequest) anno.CreateBizgrantRequest {
				req.GranteeUid = "grantee123456789"
				return req
			},
			true,
		},
		{
			"GranteeUid_doesnot_match_regexp",
			func(req anno.CreateBizgrantRequest) anno.CreateBizgrantRequest {
				req.GranteeUid = "GRANTEE1234"
				return req
			},
			true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			newReq := req
			if c.newReqFunc != nil {
				newReq = c.newReqFunc(req)
			}
			if err := newReq.Validate(); (err != nil) != c.expectErr {
				t.Errorf("Validate() expect error: %v, got: %v", c.expectErr, err)
				return
			}
			if err := newReq.ValidateAll(); (err != nil) != c.expectErr {
				t.Errorf("ValidateAll() expect error: %v, got: %v", c.expectErr, err)
				return
			}
		})
	}
}

func Test_SetRawdataEmbeddingRequest(t *testing.T) {
	req := anno.SetRawdataEmbeddingRequest{
		Uid:          "uid12345678",
		ElemIdx:      1,
		RawdataIdx:   1,
		EmbeddingUri: "s3://test.txt", // [scheme:][//[userinfo@]host][/]path[?query][#fragment]
	}

	cases := []struct {
		name       string
		newReqFunc func(req anno.SetRawdataEmbeddingRequest) anno.SetRawdataEmbeddingRequest
		expectErr  bool
	}{
		{
			"happy_flow",
			nil,
			false,
		},
		{
			"Uid_is_empty",
			func(req anno.SetRawdataEmbeddingRequest) anno.SetRawdataEmbeddingRequest {
				req.Uid = ""
				return req
			},
			true,
		},
		{
			"EmbeddingUri_is_wrong",
			func(req anno.SetRawdataEmbeddingRequest) anno.SetRawdataEmbeddingRequest {
				req.EmbeddingUri = "s3://@#$%"
				return req
			},
			true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			newReq := req
			if c.newReqFunc != nil {
				newReq = c.newReqFunc(req)
			}
			if err := newReq.Validate(); (err != nil) != c.expectErr {
				t.Errorf("Validate() expect error: %v, got: %v", c.expectErr, err)
				return
			}
			if err := newReq.ValidateAll(); (err != nil) != c.expectErr {
				t.Errorf("ValidateAll() expect error: %v, got: %v", c.expectErr, err)
				return
			}
		})
	}
}
