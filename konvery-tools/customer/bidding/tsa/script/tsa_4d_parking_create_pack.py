import os
import shutil
import sys
import pathlib
from customer.common import pypcd
import numpy as np
sys.path.append('.')
from customer.common import tools
from customer.common.merge_pcd import merge_pcds_to_one


camera_names = ['fish_back', 'fish_front', 'fish_left', 'fish_right']


# monkey patch for custom pypcd
def parse_ascii_pc_data(f, dtype, metadata):
    """ Use numpy to parse ascii pointcloud data.
    """
    try:
        data = np.loadtxt(f, dtype=dtype, delimiter=' ')
    except ValueError:
        data = np.loadtxt(f, dtype=dtype, delimiter=' ', usecols=tuple(range(len(metadata['fields']))))
    return data


pypcd.check_sanity = False
pypcd.parse_ascii_pc_data = parse_ascii_pc_data


def merge_pcd(input_dir, output_file):
    input_pcds = []
    lidar2worlds = []
    global_pose_file = input_dir.joinpath('pose.json')
    global_pose_data = tools.get_json_data(global_pose_file)
    for frame_dir in sorted([i for i in input_dir.iterdir() if not i.name.startswith('.') and not i.name.startswith('__')]):
        if not frame_dir.is_dir():
            continue
        pcd1 = frame_dir.joinpath('pts_ground_in_lidar.pcd')
        pcd2 = frame_dir.joinpath('pts_ng_in_lidar.pcd')
        input_pcds.append(pcd1)
        input_pcds.append(pcd2)
        lidar2worlds.append(global_pose_data[f'{frame_dir.name}.pcd'])
        lidar2worlds.append(global_pose_data[f'{frame_dir.name}.pcd'])
    merge_pcds_to_one(input_pcds, lidar2worlds, output_file, has_intensity=True)


def run(input_dir, output_dir):
    out_collection_dir = output_dir.joinpath(input_dir.name)
    out_collection_dir.mkdir()
    out_lidar_dir = None
    global_pose_file = input_dir.joinpath('pose.json')
    global_pose_data = tools.get_json_data(global_pose_file)
    for frame_dir in sorted([i for i in input_dir.iterdir() if not i.name.startswith('.') and not i.name.startswith('__')]):
        if not frame_dir.is_dir():
            continue
        out_frame_dir = out_collection_dir.joinpath(frame_dir.name)
        out_frame_dir.mkdir()
        if out_lidar_dir is None:
            out_lidar_dir = out_frame_dir
        pose_file = frame_dir.joinpath('pose.json')
        pose_data = tools.get_json_data(pose_file)
        cameras = dict()
        for camera_name in camera_names:
            src_img = frame_dir.joinpath(f'{camera_name}.jpg')
            dst_img = out_frame_dir.joinpath(f'{camera_name}.jpg')
            shutil.copyfile(src_img, dst_img)
            lidar2camera = np.array(pose_data[camera_name]['extrinsics']['lidar2camera'])
            distortions = pose_data[camera_name]['distortions']
            cameras[camera_name] = {
                "extrinsic": (lidar2camera @ np.linalg.inv(global_pose_data[f'{frame_dir.name}.pcd'])).flatten().tolist(),
                "intrinsic": [
                    pose_data[camera_name]['intrinsics']['fx'],
                    0,
                    pose_data[camera_name]['intrinsics']['cx'],
                    0,
                    pose_data[camera_name]['intrinsics']['fy'],
                    pose_data[camera_name]['intrinsics']['cy'],
                    0,
                    0,
                    1
                ],
                "distortion": [
                    2, distortions['k1'], distortions['k2'], distortions['k3'], distortions['k4']
                ]
            }
        params = {
            "meta": {
                "version": "v2"
            },
            "lidar": {
                "viewpoint": tools.mat_to_pose(np.array(global_pose_data[f'{frame_dir.name}.pcd'])),
                "pose": [0, 0, 0, 0, 0, 0, 1]
            },
            "cameras": cameras
        }
        tools.write_json_file(params, out_frame_dir.joinpath('params.json'))
    merged_pcd_file = os.path.join(os.path.dirname(output_dir), 'merged.pcd')
    merge_pcd(input_dir, merged_pcd_file)
    shutil.copyfile(merged_pcd_file, out_lidar_dir.joinpath('lidar.pcd'))


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(pathlib.Path(args.input), pathlib.Path(args.out))
