// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anno/v1/job.proto

package anno

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	v1 "gitlab.rp.konvery.work/platform/apis/iam/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetJoblogReply_Log_Action_Enum int32

const (
	GetJoblogReply_Log_Action_unspecified GetJoblogReply_Log_Action_Enum = 0
	GetJoblogReply_Log_Action_accept      GetJoblogReply_Log_Action_Enum = 1
	GetJoblogReply_Log_Action_reject      GetJoblogReply_Log_Action_Enum = 2
	GetJoblogReply_Log_Action_recycle     GetJoblogReply_Log_Action_Enum = 3
	GetJoblogReply_Log_Action_submit      GetJoblogReply_Log_Action_Enum = 4
	GetJoblogReply_Log_Action_claim       GetJoblogReply_Log_Action_Enum = 5
	GetJoblogReply_Log_Action_giveup      GetJoblogReply_Log_Action_Enum = 6
	GetJoblogReply_Log_Action_assign      GetJoblogReply_Log_Action_Enum = 7
	GetJoblogReply_Log_Action_timeout     GetJoblogReply_Log_Action_Enum = 8
	GetJoblogReply_Log_Action_end         GetJoblogReply_Log_Action_Enum = 9 // lot completes or is cancelled
)

// Enum value maps for GetJoblogReply_Log_Action_Enum.
var (
	GetJoblogReply_Log_Action_Enum_name = map[int32]string{
		0: "unspecified",
		1: "accept",
		2: "reject",
		3: "recycle",
		4: "submit",
		5: "claim",
		6: "giveup",
		7: "assign",
		8: "timeout",
		9: "end",
	}
	GetJoblogReply_Log_Action_Enum_value = map[string]int32{
		"unspecified": 0,
		"accept":      1,
		"reject":      2,
		"recycle":     3,
		"submit":      4,
		"claim":       5,
		"giveup":      6,
		"assign":      7,
		"timeout":     8,
		"end":         9,
	}
)

func (x GetJoblogReply_Log_Action_Enum) Enum() *GetJoblogReply_Log_Action_Enum {
	p := new(GetJoblogReply_Log_Action_Enum)
	*p = x
	return p
}

func (x GetJoblogReply_Log_Action_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetJoblogReply_Log_Action_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_job_proto_enumTypes[0].Descriptor()
}

func (GetJoblogReply_Log_Action_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_job_proto_enumTypes[0]
}

func (x GetJoblogReply_Log_Action_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetJoblogReply_Log_Action_Enum.Descriptor instead.
func (GetJoblogReply_Log_Action_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{8, 0, 0, 0}
}

type ClaimJobRequest_Prefer_Enum int32

const (
	ClaimJobRequest_Prefer_unspecified    ClaimJobRequest_Prefer_Enum = 0
	ClaimJobRequest_Prefer_rejected_first ClaimJobRequest_Prefer_Enum = 1
)

// Enum value maps for ClaimJobRequest_Prefer_Enum.
var (
	ClaimJobRequest_Prefer_Enum_name = map[int32]string{
		0: "unspecified",
		1: "rejected_first",
	}
	ClaimJobRequest_Prefer_Enum_value = map[string]int32{
		"unspecified":    0,
		"rejected_first": 1,
	}
)

func (x ClaimJobRequest_Prefer_Enum) Enum() *ClaimJobRequest_Prefer_Enum {
	p := new(ClaimJobRequest_Prefer_Enum)
	*p = x
	return p
}

func (x ClaimJobRequest_Prefer_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClaimJobRequest_Prefer_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_job_proto_enumTypes[1].Descriptor()
}

func (ClaimJobRequest_Prefer_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_job_proto_enumTypes[1]
}

func (x ClaimJobRequest_Prefer_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClaimJobRequest_Prefer_Enum.Descriptor instead.
func (ClaimJobRequest_Prefer_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{11, 0, 0}
}

type ReviewJobRequest_Decision_Enum int32

const (
	ReviewJobRequest_Decision_unspecified ReviewJobRequest_Decision_Enum = 0
	ReviewJobRequest_Decision_accept      ReviewJobRequest_Decision_Enum = 1
	ReviewJobRequest_Decision_reject      ReviewJobRequest_Decision_Enum = 2
	ReviewJobRequest_Decision_recycle     ReviewJobRequest_Decision_Enum = 3
)

// Enum value maps for ReviewJobRequest_Decision_Enum.
var (
	ReviewJobRequest_Decision_Enum_name = map[int32]string{
		0: "unspecified",
		1: "accept",
		2: "reject",
		3: "recycle",
	}
	ReviewJobRequest_Decision_Enum_value = map[string]int32{
		"unspecified": 0,
		"accept":      1,
		"reject":      2,
		"recycle":     3,
	}
)

func (x ReviewJobRequest_Decision_Enum) Enum() *ReviewJobRequest_Decision_Enum {
	p := new(ReviewJobRequest_Decision_Enum)
	*p = x
	return p
}

func (x ReviewJobRequest_Decision_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReviewJobRequest_Decision_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_job_proto_enumTypes[2].Descriptor()
}

func (ReviewJobRequest_Decision_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_job_proto_enumTypes[2]
}

func (x ReviewJobRequest_Decision_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReviewJobRequest_Decision_Enum.Descriptor instead.
func (ReviewJobRequest_Decision_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{15, 0, 0}
}

type AnnoComment_Scope_Enum int32

const (
	AnnoComment_Scope_unspecified AnnoComment_Scope_Enum = 0
	AnnoComment_Scope_object      AnnoComment_Scope_Enum = 1
	AnnoComment_Scope_element     AnnoComment_Scope_Enum = 2
	AnnoComment_Scope_job         AnnoComment_Scope_Enum = 3
)

// Enum value maps for AnnoComment_Scope_Enum.
var (
	AnnoComment_Scope_Enum_name = map[int32]string{
		0: "unspecified",
		1: "object",
		2: "element",
		3: "job",
	}
	AnnoComment_Scope_Enum_value = map[string]int32{
		"unspecified": 0,
		"object":      1,
		"element":     2,
		"job":         3,
	}
)

func (x AnnoComment_Scope_Enum) Enum() *AnnoComment_Scope_Enum {
	p := new(AnnoComment_Scope_Enum)
	*p = x
	return p
}

func (x AnnoComment_Scope_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AnnoComment_Scope_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_job_proto_enumTypes[3].Descriptor()
}

func (AnnoComment_Scope_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_job_proto_enumTypes[3]
}

func (x AnnoComment_Scope_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AnnoComment_Scope_Enum.Descriptor instead.
func (AnnoComment_Scope_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{17, 1, 0}
}

type Job_State_Enum int32

const (
	Job_State_unspecified Job_State_Enum = 0
	Job_State_unstart     Job_State_Enum = 1
	Job_State_doing       Job_State_Enum = 2
	Job_State_finished    Job_State_Enum = 3
)

// Enum value maps for Job_State_Enum.
var (
	Job_State_Enum_name = map[int32]string{
		0: "unspecified",
		1: "unstart",
		2: "doing",
		3: "finished",
	}
	Job_State_Enum_value = map[string]int32{
		"unspecified": 0,
		"unstart":     1,
		"doing":       2,
		"finished":    3,
	}
)

func (x Job_State_Enum) Enum() *Job_State_Enum {
	p := new(Job_State_Enum)
	*p = x
	return p
}

func (x Job_State_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Job_State_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_job_proto_enumTypes[4].Descriptor()
}

func (Job_State_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_job_proto_enumTypes[4]
}

func (x Job_State_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Job_State_Enum.Descriptor instead.
func (Job_State_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{18, 0, 0}
}

type GetJobLastCommitLogRequest_Direction_Enum int32

const (
	GetJobLastCommitLogRequest_Direction_unspecified GetJobLastCommitLogRequest_Direction_Enum = 0
	// submit/accept
	GetJobLastCommitLogRequest_Direction_up GetJobLastCommitLogRequest_Direction_Enum = 1
	// reject/recycle
	GetJobLastCommitLogRequest_Direction_down GetJobLastCommitLogRequest_Direction_Enum = 2
)

// Enum value maps for GetJobLastCommitLogRequest_Direction_Enum.
var (
	GetJobLastCommitLogRequest_Direction_Enum_name = map[int32]string{
		0: "unspecified",
		1: "up",
		2: "down",
	}
	GetJobLastCommitLogRequest_Direction_Enum_value = map[string]int32{
		"unspecified": 0,
		"up":          1,
		"down":        2,
	}
)

func (x GetJobLastCommitLogRequest_Direction_Enum) Enum() *GetJobLastCommitLogRequest_Direction_Enum {
	p := new(GetJobLastCommitLogRequest_Direction_Enum)
	*p = x
	return p
}

func (x GetJobLastCommitLogRequest_Direction_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetJobLastCommitLogRequest_Direction_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_job_proto_enumTypes[5].Descriptor()
}

func (GetJobLastCommitLogRequest_Direction_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_job_proto_enumTypes[5]
}

func (x GetJobLastCommitLogRequest_Direction_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetJobLastCommitLogRequest_Direction_Enum.Descriptor instead.
func (GetJobLastCommitLogRequest_Direction_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{29, 0, 0}
}

type CreateJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotUid string `protobuf:"bytes,1,opt,name=lot_uid,json=lotUid,proto3" json:"lot_uid,omitempty"`
	// job index in the lot
	IdxInLot int32 `protobuf:"varint,2,opt,name=idx_in_lot,json=idxInLot,proto3" json:"idx_in_lot,omitempty"`
	// identify a subjob in a splitted lot.
	Subtype string `protobuf:"bytes,4,opt,name=subtype,proto3" json:"subtype,omitempty"`
	// 待标注的内容
	Elements []*Element `protobuf:"bytes,5,rep,name=elements,proto3" json:"elements,omitempty"`
	// 标注结果
	Annotations *JobAnno       `protobuf:"bytes,6,opt,name=annotations,proto3" json:"annotations,omitempty"`
	Comments    []*AnnoComment `protobuf:"bytes,7,rep,name=comments,proto3" json:"comments,omitempty"`
	State       string         `protobuf:"bytes,8,opt,name=state,proto3" json:"state,omitempty"`
	// phase number, starts from 1
	Phase int32 `protobuf:"varint,9,opt,name=phase,proto3" json:"phase,omitempty"`
}

func (x *CreateJobRequest) Reset() {
	*x = CreateJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateJobRequest) ProtoMessage() {}

func (x *CreateJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateJobRequest.ProtoReflect.Descriptor instead.
func (*CreateJobRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{0}
}

func (x *CreateJobRequest) GetLotUid() string {
	if x != nil {
		return x.LotUid
	}
	return ""
}

func (x *CreateJobRequest) GetIdxInLot() int32 {
	if x != nil {
		return x.IdxInLot
	}
	return 0
}

func (x *CreateJobRequest) GetSubtype() string {
	if x != nil {
		return x.Subtype
	}
	return ""
}

func (x *CreateJobRequest) GetElements() []*Element {
	if x != nil {
		return x.Elements
	}
	return nil
}

func (x *CreateJobRequest) GetAnnotations() *JobAnno {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *CreateJobRequest) GetComments() []*AnnoComment {
	if x != nil {
		return x.Comments
	}
	return nil
}

func (x *CreateJobRequest) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *CreateJobRequest) GetPhase() int32 {
	if x != nil {
		return x.Phase
	}
	return 0
}

type UpdateJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Job *UpdateJobRequest_Updates `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`
	// name of fields to be updated
	Fields []string `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *UpdateJobRequest) Reset() {
	*x = UpdateJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobRequest) ProtoMessage() {}

func (x *UpdateJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobRequest.ProtoReflect.Descriptor instead.
func (*UpdateJobRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateJobRequest) GetJob() *UpdateJobRequest_Updates {
	if x != nil {
		return x.Job
	}
	return nil
}

func (x *UpdateJobRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type DeleteJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *DeleteJobRequest) Reset() {
	*x = DeleteJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteJobRequest) ProtoMessage() {}

func (x *DeleteJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteJobRequest.ProtoReflect.Descriptor instead.
func (*DeleteJobRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteJobRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// [PRIVILEGED] if true, the job data (e.g. elements, annotations) will be expanded
	Expand bool `protobuf:"varint,2,opt,name=expand,proto3" json:"expand,omitempty"`
}

func (x *GetJobRequest) Reset() {
	*x = GetJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobRequest) ProtoMessage() {}

func (x *GetJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobRequest.ProtoReflect.Descriptor instead.
func (*GetJobRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{3}
}

func (x *GetJobRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *GetJobRequest) GetExpand() bool {
	if x != nil {
		return x.Expand
	}
	return false
}

type ListJobFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter by job uids; max length is 100
	Uids []string `protobuf:"bytes,1,rep,name=uids,proto3" json:"uids,omitempty"`
	// filter by belonging lot
	LotUid string `protobuf:"bytes,2,opt,name=lot_uid,json=lotUid,proto3" json:"lot_uid,omitempty"`
	// filter by jobs current phase; starts from 1
	Phase int32 `protobuf:"varint,3,opt,name=phase,proto3" json:"phase,omitempty"`
	// filter by jobs current state
	State Job_State_Enum `protobuf:"varint,4,opt,name=state,proto3,enum=anno.v1.Job_State_Enum" json:"state,omitempty"`
	// query jobs by their last executors; max length is 100
	LastExecutors []string `protobuf:"bytes,5,rep,name=last_executors,json=lastExecutors,proto3" json:"last_executors,omitempty"`
	// query jobs by the last executor's belonging team
	LastExecteam string  `protobuf:"bytes,6,opt,name=last_execteam,json=lastExecteam,proto3" json:"last_execteam,omitempty"`
	Phases       []int32 `protobuf:"varint,7,rep,packed,name=phases,proto3" json:"phases,omitempty"`
	Jobclip      string  `protobuf:"bytes,8,opt,name=jobclip,proto3" json:"jobclip,omitempty"`
}

func (x *ListJobFilter) Reset() {
	*x = ListJobFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobFilter) ProtoMessage() {}

func (x *ListJobFilter) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobFilter.ProtoReflect.Descriptor instead.
func (*ListJobFilter) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{4}
}

func (x *ListJobFilter) GetUids() []string {
	if x != nil {
		return x.Uids
	}
	return nil
}

func (x *ListJobFilter) GetLotUid() string {
	if x != nil {
		return x.LotUid
	}
	return ""
}

func (x *ListJobFilter) GetPhase() int32 {
	if x != nil {
		return x.Phase
	}
	return 0
}

func (x *ListJobFilter) GetState() Job_State_Enum {
	if x != nil {
		return x.State
	}
	return Job_State_unspecified
}

func (x *ListJobFilter) GetLastExecutors() []string {
	if x != nil {
		return x.LastExecutors
	}
	return nil
}

func (x *ListJobFilter) GetLastExecteam() string {
	if x != nil {
		return x.LastExecteam
	}
	return ""
}

func (x *ListJobFilter) GetPhases() []int32 {
	if x != nil {
		return x.Phases
	}
	return nil
}

func (x *ListJobFilter) GetJobclip() string {
	if x != nil {
		return x.Jobclip
	}
	return ""
}

type ListJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page   int32          `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Pagesz int32          `protobuf:"varint,2,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	Filter *ListJobFilter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	// if to return jobs' last_executor and last_execteam in the response
	ShowLastExecutor bool `protobuf:"varint,4,opt,name=show_last_executor,json=showLastExecutor,proto3" json:"show_last_executor,omitempty"`
	// [PRIVILEGED] show full job info, including annotations, comments, etc.
	FullJob bool `protobuf:"varint,5,opt,name=full_job,json=fullJob,proto3" json:"full_job,omitempty"`
	// whether to return jobs' executor in each phase
	ShowExecutors bool `protobuf:"varint,6,opt,name=show_executors,json=showExecutors,proto3" json:"show_executors,omitempty"`
	// query jobs by their containing elements' name pattern
	ElemNamePattern string `protobuf:"bytes,7,opt,name=elem_name_pattern,json=elemNamePattern,proto3" json:"elem_name_pattern,omitempty"`
}

func (x *ListJobRequest) Reset() {
	*x = ListJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobRequest) ProtoMessage() {}

func (x *ListJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobRequest.ProtoReflect.Descriptor instead.
func (*ListJobRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{5}
}

func (x *ListJobRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListJobRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListJobRequest) GetFilter() *ListJobFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListJobRequest) GetShowLastExecutor() bool {
	if x != nil {
		return x.ShowLastExecutor
	}
	return false
}

func (x *ListJobRequest) GetFullJob() bool {
	if x != nil {
		return x.FullJob
	}
	return false
}

func (x *ListJobRequest) GetShowExecutors() bool {
	if x != nil {
		return x.ShowExecutors
	}
	return false
}

func (x *ListJobRequest) GetElemNamePattern() string {
	if x != nil {
		return x.ElemNamePattern
	}
	return ""
}

type ListJobReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total int32  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Jobs  []*Job `protobuf:"bytes,2,rep,name=jobs,proto3" json:"jobs,omitempty"`
	// jobs' executors in each phase. the key is job uid.
	Executors map[string]*ElementAnno_Metadata `protobuf:"bytes,6,rep,name=executors,proto3" json:"executors,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ListJobReply) Reset() {
	*x = ListJobReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobReply) ProtoMessage() {}

func (x *ListJobReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobReply.ProtoReflect.Descriptor instead.
func (*ListJobReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{6}
}

func (x *ListJobReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListJobReply) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

func (x *ListJobReply) GetExecutors() map[string]*ElementAnno_Metadata {
	if x != nil {
		return x.Executors
	}
	return nil
}

type GetJoblogRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page   int32  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Pagesz int32  `protobuf:"varint,2,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	Uid    string `protobuf:"bytes,3,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *GetJoblogRequest) Reset() {
	*x = GetJoblogRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJoblogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJoblogRequest) ProtoMessage() {}

func (x *GetJoblogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJoblogRequest.ProtoReflect.Descriptor instead.
func (*GetJoblogRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{7}
}

func (x *GetJoblogRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetJoblogRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *GetJoblogRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetJoblogReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total int32                 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Logs  []*GetJoblogReply_Log `protobuf:"bytes,2,rep,name=logs,proto3" json:"logs,omitempty"`
}

func (x *GetJoblogReply) Reset() {
	*x = GetJoblogReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJoblogReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJoblogReply) ProtoMessage() {}

func (x *GetJoblogReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJoblogReply.ProtoReflect.Descriptor instead.
func (*GetJoblogReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{8}
}

func (x *GetJoblogReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetJoblogReply) GetLogs() []*GetJoblogReply_Log {
	if x != nil {
		return x.Logs
	}
	return nil
}

type GetRawJoblogReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// json encoded raw logs
	Logs string `protobuf:"bytes,1,opt,name=logs,proto3" json:"logs,omitempty"`
}

func (x *GetRawJoblogReply) Reset() {
	*x = GetRawJoblogReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRawJoblogReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRawJoblogReply) ProtoMessage() {}

func (x *GetRawJoblogReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRawJoblogReply.ProtoReflect.Descriptor instead.
func (*GetRawJoblogReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{9}
}

func (x *GetRawJoblogReply) GetLogs() string {
	if x != nil {
		return x.Logs
	}
	return ""
}

type AssignJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid         string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ExecutorUid string `protobuf:"bytes,2,opt,name=executor_uid,json=executorUid,proto3" json:"executor_uid,omitempty"`
}

func (x *AssignJobRequest) Reset() {
	*x = AssignJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssignJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignJobRequest) ProtoMessage() {}

func (x *AssignJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignJobRequest.ProtoReflect.Descriptor instead.
func (*AssignJobRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{10}
}

func (x *AssignJobRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *AssignJobRequest) GetExecutorUid() string {
	if x != nil {
		return x.ExecutorUid
	}
	return ""
}

type ClaimJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// which lot the job is claimed from; if not specified, the job is claimed from any assigned lot
	LotUid string `protobuf:"bytes,1,opt,name=lot_uid,json=lotUid,proto3" json:"lot_uid,omitempty"`
	// for a splitted lot, this is to specify the sub-lot.
	Subtype string `protobuf:"bytes,2,opt,name=subtype,proto3" json:"subtype,omitempty"`
	// when lot_uid/subtype is specified and no available job can be claimed, this controls if it
	// should retry to claim in disregard of lot_uid/subtype
	Fallback bool `protobuf:"varint,3,opt,name=fallback,proto3" json:"fallback,omitempty"`
	// claim preference
	Prefer ClaimJobRequest_Prefer_Enum `protobuf:"varint,4,opt,name=prefer,proto3,enum=anno.v1.ClaimJobRequest_Prefer_Enum" json:"prefer,omitempty"`
	// true to reset the claim countdown timer; the response will not contain the job info.
	Renew bool `protobuf:"varint,6,opt,name=renew,proto3" json:"renew,omitempty"`
	// required if renew is true
	JobUid string `protobuf:"bytes,7,opt,name=job_uid,json=jobUid,proto3" json:"job_uid,omitempty"`
}

func (x *ClaimJobRequest) Reset() {
	*x = ClaimJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClaimJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClaimJobRequest) ProtoMessage() {}

func (x *ClaimJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClaimJobRequest.ProtoReflect.Descriptor instead.
func (*ClaimJobRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{11}
}

func (x *ClaimJobRequest) GetLotUid() string {
	if x != nil {
		return x.LotUid
	}
	return ""
}

func (x *ClaimJobRequest) GetSubtype() string {
	if x != nil {
		return x.Subtype
	}
	return ""
}

func (x *ClaimJobRequest) GetFallback() bool {
	if x != nil {
		return x.Fallback
	}
	return false
}

func (x *ClaimJobRequest) GetPrefer() ClaimJobRequest_Prefer_Enum {
	if x != nil {
		return x.Prefer
	}
	return ClaimJobRequest_Prefer_unspecified
}

func (x *ClaimJobRequest) GetRenew() bool {
	if x != nil {
		return x.Renew
	}
	return false
}

func (x *ClaimJobRequest) GetJobUid() string {
	if x != nil {
		return x.JobUid
	}
	return ""
}

type ClaimJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Job *Job `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`
}

func (x *ClaimJobResponse) Reset() {
	*x = ClaimJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClaimJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClaimJobResponse) ProtoMessage() {}

func (x *ClaimJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClaimJobResponse.ProtoReflect.Descriptor instead.
func (*ClaimJobResponse) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{12}
}

func (x *ClaimJobResponse) GetJob() *Job {
	if x != nil {
		return x.Job
	}
	return nil
}

type GiveupJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid     string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Reason  string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	Details string `protobuf:"bytes,3,opt,name=details,proto3" json:"details,omitempty"`
}

func (x *GiveupJobRequest) Reset() {
	*x = GiveupJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiveupJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiveupJobRequest) ProtoMessage() {}

func (x *GiveupJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiveupJobRequest.ProtoReflect.Descriptor instead.
func (*GiveupJobRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{13}
}

func (x *GiveupJobRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *GiveupJobRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *GiveupJobRequest) GetDetails() string {
	if x != nil {
		return x.Details
	}
	return ""
}

type SubmitJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid         string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Annotations *JobAnno `protobuf:"bytes,2,opt,name=annotations,proto3" json:"annotations,omitempty"`
	// comments to be resolved
	Resolves []*ResolveAnnoComment `protobuf:"bytes,3,rep,name=resolves,proto3" json:"resolves,omitempty"`
}

func (x *SubmitJobRequest) Reset() {
	*x = SubmitJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitJobRequest) ProtoMessage() {}

func (x *SubmitJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitJobRequest.ProtoReflect.Descriptor instead.
func (*SubmitJobRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{14}
}

func (x *SubmitJobRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *SubmitJobRequest) GetAnnotations() *JobAnno {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *SubmitJobRequest) GetResolves() []*ResolveAnnoComment {
	if x != nil {
		return x.Resolves
	}
	return nil
}

type ReviewJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// job uid
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// if it is null, this review makes no change to previous job annotations;
	// otherwise, it is a complete result to replace previous job annotations.
	Annotations *JobAnno `protobuf:"bytes,2,opt,name=annotations,proto3" json:"annotations,omitempty"`
	// "accept" will put the job to the next phase; annotations and resolve_comments can be set.
	// "reject" will put the job to the previous phase, and assign it to the previous executor at that phase;
	// annotations, comments and resolve_comments can be set.
	// "recycle" will put the job to its initial phase; previous annotations and comments will be cleared;
	// other fields will be ignored.
	Decision ReviewJobRequest_Decision_Enum `protobuf:"varint,3,opt,name=decision,proto3,enum=anno.v1.ReviewJobRequest_Decision_Enum" json:"decision,omitempty"`
	// new comments to the annotations when decision is "reject"
	Comments []*AnnoComment `protobuf:"bytes,4,rep,name=comments,proto3" json:"comments,omitempty"`
	// comments to resolve
	Resolves []*ResolveAnnoComment `protobuf:"bytes,5,rep,name=resolves,proto3" json:"resolves,omitempty"`
	// comments to update
	UpdatedComments []*AnnoComment `protobuf:"bytes,6,rep,name=updated_comments,json=updatedComments,proto3" json:"updated_comments,omitempty"`
	// comments to delete
	DeletedComments []*ResolveAnnoComment `protobuf:"bytes,7,rep,name=deleted_comments,json=deletedComments,proto3" json:"deleted_comments,omitempty"`
}

func (x *ReviewJobRequest) Reset() {
	*x = ReviewJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewJobRequest) ProtoMessage() {}

func (x *ReviewJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewJobRequest.ProtoReflect.Descriptor instead.
func (*ReviewJobRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{15}
}

func (x *ReviewJobRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ReviewJobRequest) GetAnnotations() *JobAnno {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *ReviewJobRequest) GetDecision() ReviewJobRequest_Decision_Enum {
	if x != nil {
		return x.Decision
	}
	return ReviewJobRequest_Decision_unspecified
}

func (x *ReviewJobRequest) GetComments() []*AnnoComment {
	if x != nil {
		return x.Comments
	}
	return nil
}

func (x *ReviewJobRequest) GetResolves() []*ResolveAnnoComment {
	if x != nil {
		return x.Resolves
	}
	return nil
}

func (x *ReviewJobRequest) GetUpdatedComments() []*AnnoComment {
	if x != nil {
		return x.UpdatedComments
	}
	return nil
}

func (x *ReviewJobRequest) GetDeletedComments() []*ResolveAnnoComment {
	if x != nil {
		return x.DeletedComments
	}
	return nil
}

type ResolveAnnoComment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// comment uuid
	Uuid string `protobuf:"bytes,3,opt,name=uuid,proto3" json:"uuid,omitempty"`
	// UUID of the associated objects if the comment is missed(漏标)
	ObjUuids []string `protobuf:"bytes,4,rep,name=obj_uuids,json=objUuids,proto3" json:"obj_uuids,omitempty"`
}

func (x *ResolveAnnoComment) Reset() {
	*x = ResolveAnnoComment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResolveAnnoComment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolveAnnoComment) ProtoMessage() {}

func (x *ResolveAnnoComment) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolveAnnoComment.ProtoReflect.Descriptor instead.
func (*ResolveAnnoComment) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{16}
}

func (x *ResolveAnnoComment) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *ResolveAnnoComment) GetObjUuids() []string {
	if x != nil {
		return x.ObjUuids
	}
	return nil
}

// comment to an annotation
type AnnoComment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// element index within the job
	ElemIdx int32 `protobuf:"varint,1,opt,name=elem_idx,json=elemIdx,proto3" json:"elem_idx,omitempty"`
	// details or additional information
	Content string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	// the user who added the comment;
	// the field is ignored in review requests
	Commenter *v1.BaseUser `protobuf:"bytes,5,opt,name=commenter,proto3" json:"commenter,omitempty"`
	// number of the phase in which the comment is added;
	// the field is ignored in review requests
	AddPhase int32 `protobuf:"varint,6,opt,name=add_phase,json=addPhase,proto3" json:"add_phase,omitempty"`
	// number of the highest phase in which the comment is marked as resolved
	// in frontend, only executors in phases within (resolve_phase, add_phase] can see the comment and react to it;
	// the field is ignored in review requests
	ResolvePhase int32 `protobuf:"varint,7,opt,name=resolve_phase,json=resolvePhase,proto3" json:"resolve_phase,omitempty"`
	// rawdata index within the element; should not be omitted if the comment is missed（漏标）
	RdIdx int32 `protobuf:"varint,8,opt,name=rd_idx,json=rdIdx,proto3" json:"rd_idx,omitempty"`
	// extra info associated with the comment
	ExtraInfo *AnnoComment_ExtraInfo `protobuf:"bytes,9,opt,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"`
	// UUID of the comment; it is obj_uuid when omitted. Must not be empty if reason is missed（漏标）.
	Uuid string `protobuf:"bytes,10,opt,name=uuid,proto3" json:"uuid,omitempty"`
	// UUID of the associated objects; it may be empty if the comment is missed(漏标), or assicated with the element or job
	ObjUuids []string `protobuf:"bytes,11,rep,name=obj_uuids,json=objUuids,proto3" json:"obj_uuids,omitempty"`
	// comment reasons
	Reasons *AnnoCommentReason_Reasons `protobuf:"bytes,12,opt,name=reasons,proto3" json:"reasons,omitempty"`
	// scope of the comment
	Scope AnnoComment_Scope_Enum `protobuf:"varint,13,opt,name=scope,proto3,enum=anno.v1.AnnoComment_Scope_Enum" json:"scope,omitempty"`
	// the field is ignored in review requests
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *AnnoComment) Reset() {
	*x = AnnoComment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnoComment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnoComment) ProtoMessage() {}

func (x *AnnoComment) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnoComment.ProtoReflect.Descriptor instead.
func (*AnnoComment) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{17}
}

func (x *AnnoComment) GetElemIdx() int32 {
	if x != nil {
		return x.ElemIdx
	}
	return 0
}

func (x *AnnoComment) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *AnnoComment) GetCommenter() *v1.BaseUser {
	if x != nil {
		return x.Commenter
	}
	return nil
}

func (x *AnnoComment) GetAddPhase() int32 {
	if x != nil {
		return x.AddPhase
	}
	return 0
}

func (x *AnnoComment) GetResolvePhase() int32 {
	if x != nil {
		return x.ResolvePhase
	}
	return 0
}

func (x *AnnoComment) GetRdIdx() int32 {
	if x != nil {
		return x.RdIdx
	}
	return 0
}

func (x *AnnoComment) GetExtraInfo() *AnnoComment_ExtraInfo {
	if x != nil {
		return x.ExtraInfo
	}
	return nil
}

func (x *AnnoComment) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *AnnoComment) GetObjUuids() []string {
	if x != nil {
		return x.ObjUuids
	}
	return nil
}

func (x *AnnoComment) GetReasons() *AnnoCommentReason_Reasons {
	if x != nil {
		return x.Reasons
	}
	return nil
}

func (x *AnnoComment) GetScope() AnnoComment_Scope_Enum {
	if x != nil {
		return x.Scope
	}
	return AnnoComment_Scope_unspecified
}

func (x *AnnoComment) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type Job struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// job UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// lot UID
	LotUid string `protobuf:"bytes,2,opt,name=lot_uid,json=lotUid,proto3" json:"lot_uid,omitempty"`
	// job index in the lot
	IdxInLot int32 `protobuf:"varint,3,opt,name=idx_in_lot,json=idxInLot,proto3" json:"idx_in_lot,omitempty"`
	// identify a subjob in a splitted lot.
	Subtype string `protobuf:"bytes,4,opt,name=subtype,proto3" json:"subtype,omitempty"`
	// 待标注的内容
	Elements []*Element `protobuf:"bytes,5,rep,name=elements,proto3" json:"elements,omitempty"`
	// 标注结果
	Annotations []*ElementAnno `protobuf:"bytes,6,rep,name=annotations,proto3" json:"annotations,omitempty"`
	// unresolved comments (a comment can only be resolved at the same phase when it is added)
	Comments []*AnnoComment `protobuf:"bytes,7,rep,name=comments,proto3" json:"comments,omitempty"`
	State    Job_State_Enum `protobuf:"varint,8,opt,name=state,proto3,enum=anno.v1.Job_State_Enum" json:"state,omitempty"`
	// why job is in the state
	Cause string `protobuf:"bytes,9,opt,name=cause,proto3" json:"cause,omitempty"`
	// phase number, starts from 1
	Phase int32 `protobuf:"varint,10,opt,name=phase,proto3" json:"phase,omitempty"`
	// manually annotated objects count in the job
	InsCnt int32 `protobuf:"varint,11,opt,name=ins_cnt,json=insCnt,proto3" json:"ins_cnt,omitempty"`
	// annotated objects count in the job (including the interpolated ones)
	InsTotal int32 `protobuf:"varint,12,opt,name=ins_total,json=insTotal,proto3" json:"ins_total,omitempty"`
	// current executor; valid for privileged requestors
	ExecutorUid string `protobuf:"bytes,13,opt,name=executor_uid,json=executorUid,proto3" json:"executor_uid,omitempty"`
	// if it is true, the annotations contain only the key element results,
	// and interpolation is needed to get the final result
	NeedInterpolation bool `protobuf:"varint,16,opt,name=need_interpolation,json=needInterpolation,proto3" json:"need_interpolation,omitempty"`
	// current executor or last submitter; valid for privileged requestors
	LastExecutor *v1.BaseUser `protobuf:"bytes,17,opt,name=last_executor,json=lastExecutor,proto3" json:"last_executor,omitempty"`
	// team of last_executor; valid for privileged requestors
	LastExecteam *v1.BaseUser `protobuf:"bytes,18,opt,name=last_execteam,json=lastExecteam,proto3" json:"last_execteam,omitempty"`
	// number of elements in this job
	ElemsCnt int32 `protobuf:"varint,19,opt,name=elems_cnt,json=elemsCnt,proto3" json:"elems_cnt,omitempty"`
	// job attributes
	JobAttrs []*AttrAndValues `protobuf:"bytes,20,rep,name=job_attrs,json=jobAttrs,proto3" json:"job_attrs,omitempty"`
	// 相机映射参数，其中 key is rawdata.meta.image_meta.camera
	CamParams map[string]*Job_CamParam `protobuf:"bytes,21,rep,name=cam_params,json=camParams,proto3" json:"cam_params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 保存了 elements 等数据, 其结构参考 Job_ElementData;
	// 如果有多个 url，完整的内容将是这些 url 所指文件的合集，cam_params 只会放在第一个文件里
	ElementsUrls []string `protobuf:"bytes,22,rep,name=elements_urls,json=elementsUrls,proto3" json:"elements_urls,omitempty"`
	// 保存了 annotations 等数据, 其结构参考 Job_AnnotationData
	AnnotationsUrl string `protobuf:"bytes,23,opt,name=annotations_url,json=annotationsUrl,proto3" json:"annotations_url,omitempty"`
	// 保存了 comments 等数据，其结构参考 Job_CommentData
	CommentsUrl string                 `protobuf:"bytes,24,opt,name=comments_url,json=commentsUrl,proto3" json:"comments_url,omitempty"`
	JobElemClip string                 `protobuf:"bytes,25,opt,name=job_elem_clip,json=jobElemClip,proto3" json:"job_elem_clip,omitempty"`
	UpdatedAt   *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	CreatedAt   *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *Job) Reset() {
	*x = Job{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Job) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Job) ProtoMessage() {}

func (x *Job) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Job.ProtoReflect.Descriptor instead.
func (*Job) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{18}
}

func (x *Job) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Job) GetLotUid() string {
	if x != nil {
		return x.LotUid
	}
	return ""
}

func (x *Job) GetIdxInLot() int32 {
	if x != nil {
		return x.IdxInLot
	}
	return 0
}

func (x *Job) GetSubtype() string {
	if x != nil {
		return x.Subtype
	}
	return ""
}

func (x *Job) GetElements() []*Element {
	if x != nil {
		return x.Elements
	}
	return nil
}

func (x *Job) GetAnnotations() []*ElementAnno {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *Job) GetComments() []*AnnoComment {
	if x != nil {
		return x.Comments
	}
	return nil
}

func (x *Job) GetState() Job_State_Enum {
	if x != nil {
		return x.State
	}
	return Job_State_unspecified
}

func (x *Job) GetCause() string {
	if x != nil {
		return x.Cause
	}
	return ""
}

func (x *Job) GetPhase() int32 {
	if x != nil {
		return x.Phase
	}
	return 0
}

func (x *Job) GetInsCnt() int32 {
	if x != nil {
		return x.InsCnt
	}
	return 0
}

func (x *Job) GetInsTotal() int32 {
	if x != nil {
		return x.InsTotal
	}
	return 0
}

func (x *Job) GetExecutorUid() string {
	if x != nil {
		return x.ExecutorUid
	}
	return ""
}

func (x *Job) GetNeedInterpolation() bool {
	if x != nil {
		return x.NeedInterpolation
	}
	return false
}

func (x *Job) GetLastExecutor() *v1.BaseUser {
	if x != nil {
		return x.LastExecutor
	}
	return nil
}

func (x *Job) GetLastExecteam() *v1.BaseUser {
	if x != nil {
		return x.LastExecteam
	}
	return nil
}

func (x *Job) GetElemsCnt() int32 {
	if x != nil {
		return x.ElemsCnt
	}
	return 0
}

func (x *Job) GetJobAttrs() []*AttrAndValues {
	if x != nil {
		return x.JobAttrs
	}
	return nil
}

func (x *Job) GetCamParams() map[string]*Job_CamParam {
	if x != nil {
		return x.CamParams
	}
	return nil
}

func (x *Job) GetElementsUrls() []string {
	if x != nil {
		return x.ElementsUrls
	}
	return nil
}

func (x *Job) GetAnnotationsUrl() string {
	if x != nil {
		return x.AnnotationsUrl
	}
	return ""
}

func (x *Job) GetCommentsUrl() string {
	if x != nil {
		return x.CommentsUrl
	}
	return ""
}

func (x *Job) GetJobElemClip() string {
	if x != nil {
		return x.JobElemClip
	}
	return ""
}

func (x *Job) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Job) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// annotations of a job
type JobAnno struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 相应位置的元素对应 Job 的 Elements 相应位置的结果
	ElementAnnos []*ElementAnno `protobuf:"bytes,1,rep,name=element_annos,json=elementAnnos,proto3" json:"element_annos,omitempty"`
	// if it is true, the annotations contain only the key element results,
	// and interpolation is needed to get the final result
	NeedInterpolation bool `protobuf:"varint,2,opt,name=need_interpolation,json=needInterpolation,proto3" json:"need_interpolation,omitempty"`
	// number of objects annotated in the job (including the interpolated objects)
	InsCnt int32 `protobuf:"varint,3,opt,name=ins_cnt,json=insCnt,proto3" json:"ins_cnt,omitempty"`
	// job index in the lot
	JobIndex int32 `protobuf:"varint,4,opt,name=job_index,json=jobIndex,proto3" json:"job_index,omitempty"`
	// job attributes
	Attrs []*AttrAndValues `protobuf:"bytes,5,rep,name=attrs,proto3" json:"attrs,omitempty"`
}

func (x *JobAnno) Reset() {
	*x = JobAnno{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobAnno) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobAnno) ProtoMessage() {}

func (x *JobAnno) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobAnno.ProtoReflect.Descriptor instead.
func (*JobAnno) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{19}
}

func (x *JobAnno) GetElementAnnos() []*ElementAnno {
	if x != nil {
		return x.ElementAnnos
	}
	return nil
}

func (x *JobAnno) GetNeedInterpolation() bool {
	if x != nil {
		return x.NeedInterpolation
	}
	return false
}

func (x *JobAnno) GetInsCnt() int32 {
	if x != nil {
		return x.InsCnt
	}
	return 0
}

func (x *JobAnno) GetJobIndex() int32 {
	if x != nil {
		return x.JobIndex
	}
	return 0
}

func (x *JobAnno) GetAttrs() []*AttrAndValues {
	if x != nil {
		return x.Attrs
	}
	return nil
}

type BatchRevertJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// changes to be made to the jobs
	Action *BatchRevertJobRequest_Action `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
	// options to the action
	Options *BatchRevertJobRequest_Options `protobuf:"bytes,2,opt,name=options,proto3" json:"options,omitempty"`
	// job filter
	Filter *ListJobFilter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *BatchRevertJobRequest) Reset() {
	*x = BatchRevertJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchRevertJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchRevertJobRequest) ProtoMessage() {}

func (x *BatchRevertJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchRevertJobRequest.ProtoReflect.Descriptor instead.
func (*BatchRevertJobRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{20}
}

func (x *BatchRevertJobRequest) GetAction() *BatchRevertJobRequest_Action {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *BatchRevertJobRequest) GetOptions() *BatchRevertJobRequest_Options {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *BatchRevertJobRequest) GetFilter() *ListJobFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type BatchRevertJobReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// jobs successfully updated
	OkJobUids []string `protobuf:"bytes,1,rep,name=ok_job_uids,json=okJobUids,proto3" json:"ok_job_uids,omitempty"`
	// jobs failed to update
	FailJobUids []string `protobuf:"bytes,2,rep,name=fail_job_uids,json=failJobUids,proto3" json:"fail_job_uids,omitempty"`
}

func (x *BatchRevertJobReply) Reset() {
	*x = BatchRevertJobReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchRevertJobReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchRevertJobReply) ProtoMessage() {}

func (x *BatchRevertJobReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchRevertJobReply.ProtoReflect.Descriptor instead.
func (*BatchRevertJobReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{21}
}

func (x *BatchRevertJobReply) GetOkJobUids() []string {
	if x != nil {
		return x.OkJobUids
	}
	return nil
}

func (x *BatchRevertJobReply) GetFailJobUids() []string {
	if x != nil {
		return x.FailJobUids
	}
	return nil
}

type GetJobAnnosRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagesz int32 `protobuf:"varint,1,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// An opaque pagination token returned from a previous call.
	// An empty token denotes the first page.
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// job filter
	Filter *ListJobFilter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *GetJobAnnosRequest) Reset() {
	*x = GetJobAnnosRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobAnnosRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobAnnosRequest) ProtoMessage() {}

func (x *GetJobAnnosRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobAnnosRequest.ProtoReflect.Descriptor instead.
func (*GetJobAnnosRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{22}
}

func (x *GetJobAnnosRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *GetJobAnnosRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *GetJobAnnosRequest) GetFilter() *ListJobFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type GetJobAnnosReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// annotations
	Annos         []*JobAnno `protobuf:"bytes,1,rep,name=annos,proto3" json:"annos,omitempty"`
	NextPageToken string     `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *GetJobAnnosReply) Reset() {
	*x = GetJobAnnosReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobAnnosReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobAnnosReply) ProtoMessage() {}

func (x *GetJobAnnosReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobAnnosReply.ProtoReflect.Descriptor instead.
func (*GetJobAnnosReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{23}
}

func (x *GetJobAnnosReply) GetAnnos() []*JobAnno {
	if x != nil {
		return x.Annos
	}
	return nil
}

func (x *GetJobAnnosReply) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

type SetRawdataEmbeddingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// job UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// element index within job
	ElemIdx int32 `protobuf:"varint,2,opt,name=elem_idx,json=elemIdx,proto3" json:"elem_idx,omitempty"`
	// rawdata index within element
	RawdataIdx int32 `protobuf:"varint,3,opt,name=rawdata_idx,json=rawdataIdx,proto3" json:"rawdata_idx,omitempty"`
	// embedding file URI
	EmbeddingUri string `protobuf:"bytes,4,opt,name=embedding_uri,json=embeddingUri,proto3" json:"embedding_uri,omitempty"`
}

func (x *SetRawdataEmbeddingRequest) Reset() {
	*x = SetRawdataEmbeddingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRawdataEmbeddingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRawdataEmbeddingRequest) ProtoMessage() {}

func (x *SetRawdataEmbeddingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRawdataEmbeddingRequest.ProtoReflect.Descriptor instead.
func (*SetRawdataEmbeddingRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{24}
}

func (x *SetRawdataEmbeddingRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *SetRawdataEmbeddingRequest) GetElemIdx() int32 {
	if x != nil {
		return x.ElemIdx
	}
	return 0
}

func (x *SetRawdataEmbeddingRequest) GetRawdataIdx() int32 {
	if x != nil {
		return x.RawdataIdx
	}
	return 0
}

func (x *SetRawdataEmbeddingRequest) GetEmbeddingUri() string {
	if x != nil {
		return x.EmbeddingUri
	}
	return ""
}

type SaveJobDraftRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// job uid
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// draft version
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// job draft might contains annos, comments and etc
	Draft string `protobuf:"bytes,3,opt,name=draft,proto3" json:"draft,omitempty"`
}

func (x *SaveJobDraftRequest) Reset() {
	*x = SaveJobDraftRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveJobDraftRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveJobDraftRequest) ProtoMessage() {}

func (x *SaveJobDraftRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveJobDraftRequest.ProtoReflect.Descriptor instead.
func (*SaveJobDraftRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{25}
}

func (x *SaveJobDraftRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *SaveJobDraftRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *SaveJobDraftRequest) GetDraft() string {
	if x != nil {
		return x.Draft
	}
	return ""
}

type SaveJobDraftReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// time in RFC3339 format: 2016-01-01T00:00:00+08:00
	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *SaveJobDraftReply) Reset() {
	*x = SaveJobDraftReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveJobDraftReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveJobDraftReply) ProtoMessage() {}

func (x *SaveJobDraftReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveJobDraftReply.ProtoReflect.Descriptor instead.
func (*SaveJobDraftReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{26}
}

func (x *SaveJobDraftReply) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type GetJobDraftRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *GetJobDraftRequest) Reset() {
	*x = GetJobDraftRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobDraftRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobDraftRequest) ProtoMessage() {}

func (x *GetJobDraftRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobDraftRequest.ProtoReflect.Descriptor instead.
func (*GetJobDraftRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{27}
}

func (x *GetJobDraftRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetJobDraftReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// time in RFC3339 format: 2016-01-01T00:00:00+08:00
	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	// draft annos/comments url, refer to SaveJobDraftRequest
	DraftUrl string `protobuf:"bytes,2,opt,name=draft_url,json=draftUrl,proto3" json:"draft_url,omitempty"`
}

func (x *GetJobDraftReply) Reset() {
	*x = GetJobDraftReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobDraftReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobDraftReply) ProtoMessage() {}

func (x *GetJobDraftReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobDraftReply.ProtoReflect.Descriptor instead.
func (*GetJobDraftReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{28}
}

func (x *GetJobDraftReply) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *GetJobDraftReply) GetDraftUrl() string {
	if x != nil {
		return x.DraftUrl
	}
	return ""
}

type GetJobLastCommitLogRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// job uid
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// phase number: 0 means all the phases
	Phase int32 `protobuf:"varint,2,opt,name=phase,proto3" json:"phase,omitempty"`
	// commit direction
	Direction GetJobLastCommitLogRequest_Direction_Enum `protobuf:"varint,3,opt,name=direction,proto3,enum=anno.v1.GetJobLastCommitLogRequest_Direction_Enum" json:"direction,omitempty"`
}

func (x *GetJobLastCommitLogRequest) Reset() {
	*x = GetJobLastCommitLogRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobLastCommitLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobLastCommitLogRequest) ProtoMessage() {}

func (x *GetJobLastCommitLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobLastCommitLogRequest.ProtoReflect.Descriptor instead.
func (*GetJobLastCommitLogRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{29}
}

func (x *GetJobLastCommitLogRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *GetJobLastCommitLogRequest) GetPhase() int32 {
	if x != nil {
		return x.Phase
	}
	return 0
}

func (x *GetJobLastCommitLogRequest) GetDirection() GetJobLastCommitLogRequest_Direction_Enum {
	if x != nil {
		return x.Direction
	}
	return GetJobLastCommitLogRequest_Direction_unspecified
}

type GetJobLastCommitLogReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// joblog without the details field
	Log *GetJoblogReply_Log `protobuf:"bytes,1,opt,name=log,proto3" json:"log,omitempty"`
}

func (x *GetJobLastCommitLogReply) Reset() {
	*x = GetJobLastCommitLogReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobLastCommitLogReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobLastCommitLogReply) ProtoMessage() {}

func (x *GetJobLastCommitLogReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobLastCommitLogReply.ProtoReflect.Descriptor instead.
func (*GetJobLastCommitLogReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{30}
}

func (x *GetJobLastCommitLogReply) GetLog() *GetJoblogReply_Log {
	if x != nil {
		return x.Log
	}
	return nil
}

type HasHoldingJobsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// if available, only query by org_uid; otherwise, query by user_uids
	OrgUid   string   `protobuf:"bytes,1,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	UserUids []string `protobuf:"bytes,2,rep,name=user_uids,json=userUids,proto3" json:"user_uids,omitempty"`
}

func (x *HasHoldingJobsRequest) Reset() {
	*x = HasHoldingJobsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HasHoldingJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HasHoldingJobsRequest) ProtoMessage() {}

func (x *HasHoldingJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HasHoldingJobsRequest.ProtoReflect.Descriptor instead.
func (*HasHoldingJobsRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{31}
}

func (x *HasHoldingJobsRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *HasHoldingJobsRequest) GetUserUids() []string {
	if x != nil {
		return x.UserUids
	}
	return nil
}

type HasHoldingJobsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Holding map[string]bool `protobuf:"bytes,1,rep,name=holding,proto3" json:"holding,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *HasHoldingJobsReply) Reset() {
	*x = HasHoldingJobsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HasHoldingJobsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HasHoldingJobsReply) ProtoMessage() {}

func (x *HasHoldingJobsReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HasHoldingJobsReply.ProtoReflect.Descriptor instead.
func (*HasHoldingJobsReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{32}
}

func (x *HasHoldingJobsReply) GetHolding() map[string]bool {
	if x != nil {
		return x.Holding
	}
	return nil
}

type SkipAnnotationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// if available, only query by org_uid; otherwise, query by user_uids
	Uid            string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	LotId          string `protobuf:"bytes,2,opt,name=lot_id,json=lotId,proto3" json:"lot_id,omitempty"`
	SkipAnnotation bool   `protobuf:"varint,3,opt,name=skip_annotation,json=skipAnnotation,proto3" json:"skip_annotation,omitempty"`
}

func (x *SkipAnnotationRequest) Reset() {
	*x = SkipAnnotationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SkipAnnotationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkipAnnotationRequest) ProtoMessage() {}

func (x *SkipAnnotationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkipAnnotationRequest.ProtoReflect.Descriptor instead.
func (*SkipAnnotationRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{33}
}

func (x *SkipAnnotationRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *SkipAnnotationRequest) GetLotId() string {
	if x != nil {
		return x.LotId
	}
	return ""
}

func (x *SkipAnnotationRequest) GetSkipAnnotation() bool {
	if x != nil {
		return x.SkipAnnotation
	}
	return false
}

type SkipAnnotationReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// if available, only query by org_uid; otherwise, query by user_uids
	SkipAnnotation bool `protobuf:"varint,1,opt,name=skip_annotation,json=skipAnnotation,proto3" json:"skip_annotation,omitempty"`
}

func (x *SkipAnnotationReply) Reset() {
	*x = SkipAnnotationReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SkipAnnotationReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkipAnnotationReply) ProtoMessage() {}

func (x *SkipAnnotationReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkipAnnotationReply.ProtoReflect.Descriptor instead.
func (*SkipAnnotationReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{34}
}

func (x *SkipAnnotationReply) GetSkipAnnotation() bool {
	if x != nil {
		return x.SkipAnnotation
	}
	return false
}

type GetSkipAnnotationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// if available, only query by org_uid; otherwise, query by user_uids
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *GetSkipAnnotationRequest) Reset() {
	*x = GetSkipAnnotationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSkipAnnotationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSkipAnnotationRequest) ProtoMessage() {}

func (x *GetSkipAnnotationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSkipAnnotationRequest.ProtoReflect.Descriptor instead.
func (*GetSkipAnnotationRequest) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{35}
}

func (x *GetSkipAnnotationRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetSkipAnnotationReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// if available, only query by org_uid; otherwise, query by user_uids
	SkipAnnotation bool `protobuf:"varint,1,opt,name=skip_annotation,json=skipAnnotation,proto3" json:"skip_annotation,omitempty"`
}

func (x *GetSkipAnnotationReply) Reset() {
	*x = GetSkipAnnotationReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSkipAnnotationReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSkipAnnotationReply) ProtoMessage() {}

func (x *GetSkipAnnotationReply) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSkipAnnotationReply.ProtoReflect.Descriptor instead.
func (*GetSkipAnnotationReply) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{36}
}

func (x *GetSkipAnnotationReply) GetSkipAnnotation() bool {
	if x != nil {
		return x.SkipAnnotation
	}
	return false
}

type UpdateJobRequest_Updates struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"` // string state = 2;
}

func (x *UpdateJobRequest_Updates) Reset() {
	*x = UpdateJobRequest_Updates{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateJobRequest_Updates) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobRequest_Updates) ProtoMessage() {}

func (x *UpdateJobRequest_Updates) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobRequest_Updates.ProtoReflect.Descriptor instead.
func (*UpdateJobRequest_Updates) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{1, 0}
}

func (x *UpdateJobRequest_Updates) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetJoblogReply_Log struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Operator *v1.BaseUser                   `protobuf:"bytes,1,opt,name=operator,proto3" json:"operator,omitempty"`
	Action   GetJoblogReply_Log_Action_Enum `protobuf:"varint,2,opt,name=action,proto3,enum=anno.v1.GetJoblogReply_Log_Action_Enum" json:"action,omitempty"`
	// detailed information if any
	Details *GetJoblogReply_Log_Details `protobuf:"bytes,3,opt,name=details,proto3" json:"details,omitempty"`
	// phase number at this event
	FromPhase int32 `protobuf:"varint,4,opt,name=from_phase,json=fromPhase,proto3" json:"from_phase,omitempty"`
	// phase number after this event
	ToPhase int32 `protobuf:"varint,5,opt,name=to_phase,json=toPhase,proto3" json:"to_phase,omitempty"`
	// created_at timestamp
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *GetJoblogReply_Log) Reset() {
	*x = GetJoblogReply_Log{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJoblogReply_Log) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJoblogReply_Log) ProtoMessage() {}

func (x *GetJoblogReply_Log) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJoblogReply_Log.ProtoReflect.Descriptor instead.
func (*GetJoblogReply_Log) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{8, 0}
}

func (x *GetJoblogReply_Log) GetOperator() *v1.BaseUser {
	if x != nil {
		return x.Operator
	}
	return nil
}

func (x *GetJoblogReply_Log) GetAction() GetJoblogReply_Log_Action_Enum {
	if x != nil {
		return x.Action
	}
	return GetJoblogReply_Log_Action_unspecified
}

func (x *GetJoblogReply_Log) GetDetails() *GetJoblogReply_Log_Details {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *GetJoblogReply_Log) GetFromPhase() int32 {
	if x != nil {
		return x.FromPhase
	}
	return 0
}

func (x *GetJoblogReply_Log) GetToPhase() int32 {
	if x != nil {
		return x.ToPhase
	}
	return 0
}

func (x *GetJoblogReply_Log) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type GetJoblogReply_Log_Action struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetJoblogReply_Log_Action) Reset() {
	*x = GetJoblogReply_Log_Action{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJoblogReply_Log_Action) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJoblogReply_Log_Action) ProtoMessage() {}

func (x *GetJoblogReply_Log_Action) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJoblogReply_Log_Action.ProtoReflect.Descriptor instead.
func (*GetJoblogReply_Log_Action) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{8, 0, 0}
}

type GetJoblogReply_Log_GiveupReason struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Reason  string `protobuf:"bytes,1,opt,name=reason,proto3" json:"reason,omitempty"`
	Details string `protobuf:"bytes,2,opt,name=details,proto3" json:"details,omitempty"`
}

func (x *GetJoblogReply_Log_GiveupReason) Reset() {
	*x = GetJoblogReply_Log_GiveupReason{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJoblogReply_Log_GiveupReason) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJoblogReply_Log_GiveupReason) ProtoMessage() {}

func (x *GetJoblogReply_Log_GiveupReason) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJoblogReply_Log_GiveupReason.ProtoReflect.Descriptor instead.
func (*GetJoblogReply_Log_GiveupReason) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{8, 0, 1}
}

func (x *GetJoblogReply_Log_GiveupReason) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *GetJoblogReply_Log_GiveupReason) GetDetails() string {
	if x != nil {
		return x.Details
	}
	return ""
}

type GetJoblogReply_Log_Details struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// added comments in this log
	AddComments []*AnnoComment `protobuf:"bytes,1,rep,name=add_comments,json=addComments,proto3" json:"add_comments,omitempty"`
	// resolved comments in this log
	Resolves []*ResolveAnnoComment `protobuf:"bytes,2,rep,name=resolves,proto3" json:"resolves,omitempty"`
	// job giveup reason
	GiveupReason *GetJoblogReply_Log_GiveupReason `protobuf:"bytes,3,opt,name=giveup_reason,json=giveupReason,proto3" json:"giveup_reason,omitempty"`
}

func (x *GetJoblogReply_Log_Details) Reset() {
	*x = GetJoblogReply_Log_Details{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJoblogReply_Log_Details) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJoblogReply_Log_Details) ProtoMessage() {}

func (x *GetJoblogReply_Log_Details) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJoblogReply_Log_Details.ProtoReflect.Descriptor instead.
func (*GetJoblogReply_Log_Details) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{8, 0, 2}
}

func (x *GetJoblogReply_Log_Details) GetAddComments() []*AnnoComment {
	if x != nil {
		return x.AddComments
	}
	return nil
}

func (x *GetJoblogReply_Log_Details) GetResolves() []*ResolveAnnoComment {
	if x != nil {
		return x.Resolves
	}
	return nil
}

func (x *GetJoblogReply_Log_Details) GetGiveupReason() *GetJoblogReply_Log_GiveupReason {
	if x != nil {
		return x.GiveupReason
	}
	return nil
}

type ClaimJobRequest_Prefer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ClaimJobRequest_Prefer) Reset() {
	*x = ClaimJobRequest_Prefer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClaimJobRequest_Prefer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClaimJobRequest_Prefer) ProtoMessage() {}

func (x *ClaimJobRequest_Prefer) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClaimJobRequest_Prefer.ProtoReflect.Descriptor instead.
func (*ClaimJobRequest_Prefer) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{11, 0}
}

type ReviewJobRequest_Decision struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReviewJobRequest_Decision) Reset() {
	*x = ReviewJobRequest_Decision{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewJobRequest_Decision) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewJobRequest_Decision) ProtoMessage() {}

func (x *ReviewJobRequest_Decision) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewJobRequest_Decision.ProtoReflect.Descriptor instead.
func (*ReviewJobRequest_Decision) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{15, 0}
}

type AnnoComment_ExtraInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// location of the missing object: (x,y,z)
	Position []float64 `protobuf:"fixed64,2,rep,packed,name=position,proto3" json:"position,omitempty"`
	// label of the missed object
	Labels []string `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty"`
}

func (x *AnnoComment_ExtraInfo) Reset() {
	*x = AnnoComment_ExtraInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnoComment_ExtraInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnoComment_ExtraInfo) ProtoMessage() {}

func (x *AnnoComment_ExtraInfo) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnoComment_ExtraInfo.ProtoReflect.Descriptor instead.
func (*AnnoComment_ExtraInfo) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{17, 0}
}

func (x *AnnoComment_ExtraInfo) GetPosition() []float64 {
	if x != nil {
		return x.Position
	}
	return nil
}

func (x *AnnoComment_ExtraInfo) GetLabels() []string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type AnnoComment_Scope struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AnnoComment_Scope) Reset() {
	*x = AnnoComment_Scope{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnoComment_Scope) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnoComment_Scope) ProtoMessage() {}

func (x *AnnoComment_Scope) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnoComment_Scope.ProtoReflect.Descriptor instead.
func (*AnnoComment_Scope) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{17, 1}
}

type Job_State struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Job_State) Reset() {
	*x = Job_State{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Job_State) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Job_State) ProtoMessage() {}

func (x *Job_State) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Job_State.ProtoReflect.Descriptor instead.
func (*Job_State) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{18, 0}
}

type Job_CamParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// extrinsic params: x,y,z,qx,qy,qz,qw
	Extrinsic []float64 `protobuf:"fixed64,1,rep,packed,name=extrinsic,proto3" json:"extrinsic,omitempty"`
	// intrinsic params: fx,fy,cx,cy
	Intrinsic []float64 `protobuf:"fixed64,2,rep,packed,name=intrinsic,proto3" json:"intrinsic,omitempty"`
	// distortion params: distortion_type,k1,k2,...
	Distortion []float64 `protobuf:"fixed64,3,rep,packed,name=distortion,proto3" json:"distortion,omitempty"`
}

func (x *Job_CamParam) Reset() {
	*x = Job_CamParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Job_CamParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Job_CamParam) ProtoMessage() {}

func (x *Job_CamParam) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Job_CamParam.ProtoReflect.Descriptor instead.
func (*Job_CamParam) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{18, 1}
}

func (x *Job_CamParam) GetExtrinsic() []float64 {
	if x != nil {
		return x.Extrinsic
	}
	return nil
}

func (x *Job_CamParam) GetIntrinsic() []float64 {
	if x != nil {
		return x.Intrinsic
	}
	return nil
}

func (x *Job_CamParam) GetDistortion() []float64 {
	if x != nil {
		return x.Distortion
	}
	return nil
}

type Job_ElementData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 所有帧
	Elements []*Element `protobuf:"bytes,1,rep,name=elements,proto3" json:"elements,omitempty"`
	// 相机映射参数，其中 key is rawdata.meta.image_meta.camera
	CamParams map[string]*Job_CamParam `protobuf:"bytes,2,rep,name=cam_params,json=camParams,proto3" json:"cam_params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Job_ElementData) Reset() {
	*x = Job_ElementData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Job_ElementData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Job_ElementData) ProtoMessage() {}

func (x *Job_ElementData) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Job_ElementData.ProtoReflect.Descriptor instead.
func (*Job_ElementData) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{18, 3}
}

func (x *Job_ElementData) GetElements() []*Element {
	if x != nil {
		return x.Elements
	}
	return nil
}

func (x *Job_ElementData) GetCamParams() map[string]*Job_CamParam {
	if x != nil {
		return x.CamParams
	}
	return nil
}

type Job_AnnotationData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 所有标注结果
	ElementAnnos []*ElementAnno `protobuf:"bytes,1,rep,name=element_annos,json=elementAnnos,proto3" json:"element_annos,omitempty"`
	// 包属性
	JobAttrs []*AttrAndValues `protobuf:"bytes,2,rep,name=job_attrs,json=jobAttrs,proto3" json:"job_attrs,omitempty"`
}

func (x *Job_AnnotationData) Reset() {
	*x = Job_AnnotationData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Job_AnnotationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Job_AnnotationData) ProtoMessage() {}

func (x *Job_AnnotationData) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Job_AnnotationData.ProtoReflect.Descriptor instead.
func (*Job_AnnotationData) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{18, 4}
}

func (x *Job_AnnotationData) GetElementAnnos() []*ElementAnno {
	if x != nil {
		return x.ElementAnnos
	}
	return nil
}

func (x *Job_AnnotationData) GetJobAttrs() []*AttrAndValues {
	if x != nil {
		return x.JobAttrs
	}
	return nil
}

type Job_CommentData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 所有批注
	Comments []*AnnoComment `protobuf:"bytes,1,rep,name=comments,proto3" json:"comments,omitempty"`
}

func (x *Job_CommentData) Reset() {
	*x = Job_CommentData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Job_CommentData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Job_CommentData) ProtoMessage() {}

func (x *Job_CommentData) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Job_CommentData.ProtoReflect.Descriptor instead.
func (*Job_CommentData) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{18, 5}
}

func (x *Job_CommentData) GetComments() []*AnnoComment {
	if x != nil {
		return x.Comments
	}
	return nil
}

type BatchRevertJobRequest_Action struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// set jobs to this phase number; starts from 1
	ToPhase int32 `protobuf:"varint,1,opt,name=to_phase,json=toPhase,proto3" json:"to_phase,omitempty"`
}

func (x *BatchRevertJobRequest_Action) Reset() {
	*x = BatchRevertJobRequest_Action{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchRevertJobRequest_Action) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchRevertJobRequest_Action) ProtoMessage() {}

func (x *BatchRevertJobRequest_Action) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchRevertJobRequest_Action.ProtoReflect.Descriptor instead.
func (*BatchRevertJobRequest_Action) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{20, 0}
}

func (x *BatchRevertJobRequest_Action) GetToPhase() int32 {
	if x != nil {
		return x.ToPhase
	}
	return 0
}

type BatchRevertJobRequest_Options struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// if to keep annotations
	KeepAnnos bool `protobuf:"varint,1,opt,name=keep_annos,json=keepAnnos,proto3" json:"keep_annos,omitempty"`
	// if to keep comments
	KeepComments bool `protobuf:"varint,2,opt,name=keep_comments,json=keepComments,proto3" json:"keep_comments,omitempty"`
	// if to reassign to previous executor
	ToPreviousExecutor bool `protobuf:"varint,3,opt,name=to_previous_executor,json=toPreviousExecutor,proto3" json:"to_previous_executor,omitempty"`
}

func (x *BatchRevertJobRequest_Options) Reset() {
	*x = BatchRevertJobRequest_Options{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchRevertJobRequest_Options) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchRevertJobRequest_Options) ProtoMessage() {}

func (x *BatchRevertJobRequest_Options) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchRevertJobRequest_Options.ProtoReflect.Descriptor instead.
func (*BatchRevertJobRequest_Options) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{20, 1}
}

func (x *BatchRevertJobRequest_Options) GetKeepAnnos() bool {
	if x != nil {
		return x.KeepAnnos
	}
	return false
}

func (x *BatchRevertJobRequest_Options) GetKeepComments() bool {
	if x != nil {
		return x.KeepComments
	}
	return false
}

func (x *BatchRevertJobRequest_Options) GetToPreviousExecutor() bool {
	if x != nil {
		return x.ToPreviousExecutor
	}
	return false
}

type GetJobLastCommitLogRequest_Direction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetJobLastCommitLogRequest_Direction) Reset() {
	*x = GetJobLastCommitLogRequest_Direction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_job_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobLastCommitLogRequest_Direction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobLastCommitLogRequest_Direction) ProtoMessage() {}

func (x *GetJobLastCommitLogRequest_Direction) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_job_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobLastCommitLogRequest_Direction.ProtoReflect.Descriptor instead.
func (*GetJobLastCommitLogRequest_Direction) Descriptor() ([]byte, []int) {
	return file_anno_v1_job_proto_rawDescGZIP(), []int{29, 0}
}

var File_anno_v1_job_proto protoreflect.FileDescriptor

var file_anno_v1_job_proto_rawDesc = []byte{
	0x0a, 0x11, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76,
	0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x11, 0x69, 0x61, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6c, 0x65,
	0x6d, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x6e, 0x6e,
	0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x6f, 0x74, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xba, 0x02, 0x0a, 0x10, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e,
	0x0a, 0x07, 0x6c, 0x6f, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x06, 0x6c, 0x6f, 0x74, 0x55, 0x69, 0x64, 0x12, 0x1c,
	0x0a, 0x0a, 0x69, 0x64, 0x78, 0x5f, 0x69, 0x6e, 0x5f, 0x6c, 0x6f, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x69, 0x64, 0x78, 0x49, 0x6e, 0x4c, 0x6f, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x75, 0x62, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x75, 0x62, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x32, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x0b, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x30, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x22, 0x93, 0x01, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x03, 0x6a,
	0x6f, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x52, 0x03, 0x6a, 0x6f, 0x62,
	0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x1a, 0x32, 0x0a, 0x07, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x3b, 0x0a, 0x10,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa,
	0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b,
	0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x50, 0x0a, 0x0d, 0x47, 0x65, 0x74,
	0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x61, 0x6e, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x78, 0x70, 0x61, 0x6e, 0x64, 0x22, 0x83, 0x03, 0x0a, 0x0d,
	0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2e, 0x0a,
	0x04, 0x75, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17,
	0x92, 0x01, 0x14, 0x22, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x04, 0x75, 0x69, 0x64, 0x73, 0x12, 0x31, 0x0a,
	0x07, 0x6c, 0x6f, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18,
	0xfa, 0x42, 0x15, 0x72, 0x13, 0x32, 0x11, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d,
	0x7b, 0x31, 0x31, 0x7d, 0x24, 0x7c, 0x5e, 0x24, 0x52, 0x06, 0x6c, 0x6f, 0x74, 0x55, 0x69, 0x64,
	0x12, 0x1d, 0x0a, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x52, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x12,
	0x2d, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x41,
	0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x92, 0x01, 0x14, 0x22, 0x12,
	0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31,
	0x7d, 0x24, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72,
	0x73, 0x12, 0x3d, 0x0a, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x74, 0x65,
	0x61, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x72, 0x13, 0x32,
	0x11, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x7c,
	0x5e, 0x24, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x45, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d,
	0x12, 0x16, 0x0a, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x63,
	0x6c, 0x69, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x63, 0x6c,
	0x69, 0x70, 0x3a, 0x0d, 0xba, 0x47, 0x0a, 0xba, 0x01, 0x07, 0x6c, 0x6f, 0x74, 0x5f, 0x75, 0x69,
	0x64, 0x22, 0xa1, 0x02, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65,
	0x73, 0x7a, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18,
	0x64, 0x28, 0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x12, 0x2e, 0x0a, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x12, 0x73,
	0x68, 0x6f, 0x77, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x73, 0x68, 0x6f, 0x77, 0x4c, 0x61, 0x73,
	0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x75, 0x6c,
	0x6c, 0x5f, 0x6a, 0x6f, 0x62, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x66, 0x75, 0x6c,
	0x6c, 0x4a, 0x6f, 0x62, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x68,
	0x6f, 0x77, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x65,
	0x6c, 0x65, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x6c, 0x65, 0x6d, 0x4e, 0x61, 0x6d, 0x65,
	0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x3a, 0x0c, 0xba, 0x47, 0x09, 0xba, 0x01, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xe7, 0x01, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f,
	0x62, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x20, 0x0a, 0x04,
	0x6a, 0x6f, 0x62, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x12, 0x42,
	0x0a, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x4a, 0x6f, 0x62, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f,
	0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f,
	0x72, 0x73, 0x1a, 0x5b, 0x0a, 0x0e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x33, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x2e, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x72, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73,
	0x7a, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x64,
	0x28, 0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x22, 0xf7, 0x06, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x6c, 0x6f,
	0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2f, 0x0a, 0x04,
	0x6c, 0x6f, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x6c, 0x6f, 0x67, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x2e, 0x4c, 0x6f, 0x67, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x73, 0x1a, 0x9d, 0x06,
	0x0a, 0x03, 0x4c, 0x6f, 0x67, 0x12, 0x2c, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x61, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x12, 0x3f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x4a, 0x6f, 0x62, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x4c, 0x6f, 0x67,
	0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x4c,
	0x6f, 0x67, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x70, 0x68, 0x61, 0x73,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x50, 0x68, 0x61,
	0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x6f, 0x5f, 0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x6f, 0x50, 0x68, 0x61, 0x73, 0x65, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0x8c, 0x01, 0x0a, 0x06, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x81, 0x01, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b,
	0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x0a, 0x0a,
	0x06, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x72, 0x65, 0x6a,
	0x65, 0x63, 0x74, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65,
	0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x10, 0x04, 0x12, 0x09,
	0x0a, 0x05, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x67, 0x69, 0x76,
	0x65, 0x75, 0x70, 0x10, 0x06, 0x12, 0x0a, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x10,
	0x07, 0x12, 0x0b, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x10, 0x08, 0x12, 0x07,
	0x0a, 0x03, 0x65, 0x6e, 0x64, 0x10, 0x09, 0x1a, 0x58, 0x0a, 0x0c, 0x47, 0x69, 0x76, 0x65, 0x75,
	0x70, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x18, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x3a, 0x16, 0xba, 0x47, 0x13, 0xba, 0x01,
	0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0xba, 0x01, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x1a, 0xe9, 0x01, 0x0a, 0x07, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x37, 0x0a,
	0x0c, 0x61, 0x64, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e,
	0x6e, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x61, 0x64, 0x64, 0x43, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x43, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x73, 0x12,
	0x4d, 0x0a, 0x0d, 0x67, 0x69, 0x76, 0x65, 0x75, 0x70, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e,
	0x4c, 0x6f, 0x67, 0x2e, 0x47, 0x69, 0x76, 0x65, 0x75, 0x70, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x52, 0x0c, 0x67, 0x69, 0x76, 0x65, 0x75, 0x70, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x3a, 0x1d,
	0xba, 0x47, 0x1a, 0xba, 0x01, 0x0c, 0x61, 0x64, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0xba, 0x01, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x73, 0x3a, 0x1e, 0xba,
	0x47, 0x1b, 0xba, 0x01, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0xba, 0x01, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0xba, 0x01, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x27, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x52, 0x61, 0x77, 0x4a, 0x6f, 0x62, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6c, 0x6f, 0x67, 0x73, 0x22, 0x92, 0x01, 0x0a, 0x10, 0x41, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32,
	0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52,
	0x03, 0x75, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72,
	0x5f, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x72,
	0x13, 0x32, 0x11, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d,
	0x24, 0x7c, 0x5e, 0x24, 0x52, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x55, 0x69,
	0x64, 0x3a, 0x18, 0xba, 0x47, 0x15, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x0c, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x22, 0xca, 0x02, 0x0a, 0x0f,
	0x43, 0x6c, 0x61, 0x69, 0x6d, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2e, 0x0a, 0x07, 0x6c, 0x6f, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x06, 0x6c, 0x6f, 0x74, 0x55, 0x69, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x75, 0x62, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x66, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x3c, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6c, 0x61, 0x69, 0x6d, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x06, 0x70, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x12, 0x31, 0x0a, 0x07, 0x6a, 0x6f, 0x62,
	0x5f, 0x75, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x72,
	0x13, 0x32, 0x11, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d,
	0x24, 0x7c, 0x5e, 0x24, 0x52, 0x06, 0x6a, 0x6f, 0x62, 0x55, 0x69, 0x64, 0x1a, 0x35, 0x0a, 0x06,
	0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x22, 0x2b, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f,
	0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12,
	0x12, 0x0a, 0x0e, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x10, 0x01, 0x3a, 0x0d, 0xba, 0x47, 0x0a, 0xba, 0x01, 0x07, 0x6c, 0x6f, 0x74, 0x5f, 0x75,
	0x69, 0x64, 0x4a, 0x04, 0x08, 0x05, 0x10, 0x06, 0x22, 0x32, 0x0a, 0x10, 0x43, 0x6c, 0x61, 0x69,
	0x6d, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1e, 0x0a, 0x03,
	0x6a, 0x6f, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x03, 0x6a, 0x6f, 0x62, 0x22, 0x81, 0x01, 0x0a,
	0x10, 0x47, 0x69, 0x76, 0x65, 0x75, 0x70, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15,
	0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d,
	0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x3a, 0x12, 0xba, 0x47,
	0x0f, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x22, 0xc1, 0x01, 0x0a, 0x10, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x32,
	0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f,
	0x62, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x37, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x73, 0x3a, 0x17, 0xba, 0x47, 0x14,
	0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x22, 0x94, 0x04, 0x0a, 0x10, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4a,
	0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e,
	0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x12, 0x32, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4f, 0x0a, 0x08, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6e, 0x75,
	0x6d, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x64,
	0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x08, 0x72, 0x65, 0x73,
	0x6f, 0x6c, 0x76, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x41, 0x6e, 0x6e,
	0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76,
	0x65, 0x73, 0x12, 0x3f, 0x0a, 0x10, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x0f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x46, 0x0a, 0x10, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x41,
	0x6e, 0x6e, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0f, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0x48, 0x0a, 0x08, 0x44,
	0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x3c, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12,
	0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00,
	0x12, 0x0a, 0x0a, 0x06, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06,
	0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x79,
	0x63, 0x6c, 0x65, 0x10, 0x03, 0x3a, 0x14, 0xba, 0x47, 0x11, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64,
	0xba, 0x01, 0x08, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x51, 0x0a, 0x12, 0x52,
	0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x62, 0x6a, 0x5f, 0x75, 0x75, 0x69,
	0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x62, 0x6a, 0x55, 0x75, 0x69,
	0x64, 0x73, 0x4a, 0x04, 0x08, 0x01, 0x10, 0x02, 0x4a, 0x04, 0x08, 0x02, 0x10, 0x03, 0x22, 0xe4,
	0x05, 0x0a, 0x0b, 0x41, 0x6e, 0x6e, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x65, 0x6c, 0x65, 0x6d, 0x49, 0x64, 0x78, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x61, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x5f, 0x70, 0x68, 0x61, 0x73, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x64, 0x64, 0x50, 0x68, 0x61, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x5f, 0x70, 0x68, 0x61, 0x73,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65,
	0x50, 0x68, 0x61, 0x73, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x78, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x64, 0x49, 0x64, 0x78, 0x12, 0x3d, 0x0a, 0x0a,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x75, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x6f, 0x62, 0x6a, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x08, 0x6f, 0x62, 0x6a, 0x55, 0x75, 0x69, 0x64, 0x73, 0x12, 0x3c, 0x0a, 0x07,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x73, 0x52, 0x07, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x12, 0x35, 0x0a, 0x05, 0x73, 0x63,
	0x6f, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x53, 0x63, 0x6f, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70,
	0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0x45, 0x0a, 0x09,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x01, 0x52, 0x08, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x4a, 0x04, 0x08,
	0x01, 0x10, 0x02, 0x1a, 0x42, 0x0a, 0x05, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x22, 0x39, 0x0a, 0x04,
	0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x10,
	0x01, 0x12, 0x0b, 0x0a, 0x07, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x02, 0x12, 0x07,
	0x0a, 0x03, 0x6a, 0x6f, 0x62, 0x10, 0x03, 0x3a, 0x60, 0xba, 0x47, 0x5d, 0xba, 0x01, 0x04, 0x75,
	0x75, 0x69, 0x64, 0xba, 0x01, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x78, 0xba, 0x01,
	0x09, 0x6f, 0x62, 0x6a, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x73, 0xba, 0x01, 0x07, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x73, 0xba, 0x01, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0xba, 0x01, 0x09, 0x61, 0x64, 0x64, 0x5f, 0x70, 0x68, 0x61, 0x73, 0x65, 0xba, 0x01, 0x0d, 0x72,
	0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x5f, 0x70, 0x68, 0x61, 0x73, 0x65, 0xba, 0x01, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x4a, 0x04, 0x08, 0x02, 0x10, 0x03, 0x4a,
	0x04, 0x08, 0x03, 0x10, 0x04, 0x22, 0xc5, 0x0e, 0x0a, 0x03, 0x4a, 0x6f, 0x62, 0x12, 0x27, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72,
	0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d,
	0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x2e, 0x0a, 0x07, 0x6c, 0x6f, 0x74, 0x5f, 0x75, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x06,
	0x6c, 0x6f, 0x74, 0x55, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x69, 0x64, 0x78, 0x5f, 0x69, 0x6e,
	0x5f, 0x6c, 0x6f, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x64, 0x78, 0x49,
	0x6e, 0x4c, 0x6f, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2c,
	0x0a, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x36, 0x0a, 0x0b,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x30, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2d, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x4a, 0x6f, 0x62, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x61, 0x75, 0x73, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x75, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70,
	0x68, 0x61, 0x73, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x68, 0x61, 0x73,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x43, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6e,
	0x73, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69,
	0x6e, 0x73, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x12, 0x6e, 0x65,
	0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x6e, 0x65, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x0d, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72,
	0x12, 0x35, 0x0a, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x74, 0x65, 0x61,
	0x6d, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x61, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x45,
	0x78, 0x65, 0x63, 0x74, 0x65, 0x61, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6c, 0x65, 0x6d, 0x73,
	0x5f, 0x63, 0x6e, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x65, 0x6c, 0x65, 0x6d,
	0x73, 0x43, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x09, 0x6a, 0x6f, 0x62, 0x5f, 0x61, 0x74, 0x74, 0x72,
	0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x41, 0x6e, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52,
	0x08, 0x6a, 0x6f, 0x62, 0x41, 0x74, 0x74, 0x72, 0x73, 0x12, 0x3a, 0x0a, 0x0a, 0x63, 0x61, 0x6d,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x2e, 0x43, 0x61, 0x6d, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x63, 0x61, 0x6d, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0d, 0x6a, 0x6f, 0x62, 0x5f, 0x65, 0x6c,
	0x65, 0x6d, 0x5f, 0x63, 0x6c, 0x69, 0x70, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6a,
	0x6f, 0x62, 0x45, 0x6c, 0x65, 0x6d, 0x43, 0x6c, 0x69, 0x70, 0x12, 0x3e, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3e, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0x46, 0x0a, 0x05, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x22, 0x3d, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07,
	0x75, 0x6e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x64, 0x6f, 0x69,
	0x6e, 0x67, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64,
	0x10, 0x03, 0x1a, 0x66, 0x0a, 0x08, 0x43, 0x61, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1c,
	0x0a, 0x09, 0x65, 0x78, 0x74, 0x72, 0x69, 0x6e, 0x73, 0x69, 0x63, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x01, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x69, 0x6e, 0x73, 0x69, 0x63, 0x12, 0x1c, 0x0a, 0x09,
	0x69, 0x6e, 0x74, 0x72, 0x69, 0x6e, 0x73, 0x69, 0x63, 0x18, 0x02, 0x20, 0x03, 0x28, 0x01, 0x52,
	0x09, 0x69, 0x6e, 0x74, 0x72, 0x69, 0x6e, 0x73, 0x69, 0x63, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x01, 0x52, 0x0a,
	0x64, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x53, 0x0a, 0x0e, 0x43, 0x61,
	0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2b,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x2e, 0x43, 0x61, 0x6d, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0xd8, 0x01, 0x0a, 0x0b, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x2c, 0x0a, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x46, 0x0a,
	0x0a, 0x63, 0x61, 0x6d, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x2e,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x61, 0x6d, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x63, 0x61, 0x6d, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x53, 0x0a, 0x0e, 0x43, 0x61, 0x6d, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2b, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x2e, 0x43, 0x61, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x80, 0x01, 0x0a, 0x0e, 0x41,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a,
	0x0d, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x0c, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x6a, 0x6f, 0x62, 0x5f,
	0x61, 0x74, 0x74, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x41, 0x6e, 0x64, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x52, 0x08, 0x6a, 0x6f, 0x62, 0x41, 0x74, 0x74, 0x72, 0x73, 0x1a, 0x3f, 0x0a,
	0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x30, 0x0a, 0x08,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x3a, 0x7d,
	0xba, 0x47, 0x7a, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x0a, 0x69, 0x64, 0x78, 0x5f,
	0x69, 0x6e, 0x5f, 0x6c, 0x6f, 0x74, 0xba, 0x01, 0x07, 0x73, 0x75, 0x62, 0x74, 0x79, 0x70, 0x65,
	0xba, 0x01, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0xba, 0x01, 0x0b, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0xba, 0x01, 0x08, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0xba, 0x01, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0xba, 0x01, 0x05, 0x63,
	0x61, 0x75, 0x73, 0x65, 0xba, 0x01, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0xba, 0x01, 0x07, 0x69,
	0x6e, 0x73, 0x5f, 0x63, 0x6e, 0x74, 0xba, 0x01, 0x07, 0x6c, 0x6f, 0x74, 0x5f, 0x75, 0x69, 0x64,
	0xba, 0x01, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0x8b, 0x02,
	0x0a, 0x07, 0x4a, 0x6f, 0x62, 0x41, 0x6e, 0x6e, 0x6f, 0x12, 0x39, 0x0a, 0x0d, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x0c, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x41,
	0x6e, 0x6e, 0x6f, 0x73, 0x12, 0x2d, 0x0a, 0x12, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x11, 0x6e, 0x65, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x6f, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x43, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x6a, 0x6f, 0x62, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2c, 0x0a, 0x05, 0x61, 0x74, 0x74,
	0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x41, 0x6e, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x52, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x3a, 0x32, 0xba, 0x47, 0x2f, 0xba, 0x01, 0x0d, 0x65,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0xba, 0x01, 0x12, 0x6e,
	0x65, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0xba, 0x01, 0x07, 0x69, 0x6e, 0x73, 0x5f, 0x63, 0x6e, 0x74, 0x22, 0xe2, 0x03, 0x0a, 0x15,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x76, 0x65, 0x72, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3d, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x76, 0x65, 0x72, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x76, 0x65, 0x72, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x07, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2e, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x3c, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x22, 0x0a, 0x08, 0x74, 0x6f, 0x5f, 0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x01, 0x52, 0x07, 0x74, 0x6f, 0x50,
	0x68, 0x61, 0x73, 0x65, 0x3a, 0x0e, 0xba, 0x47, 0x0b, 0xba, 0x01, 0x08, 0x74, 0x6f, 0x5f, 0x70,
	0x68, 0x61, 0x73, 0x65, 0x1a, 0xb8, 0x01, 0x0a, 0x07, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x6b, 0x65, 0x65, 0x70, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6b, 0x65, 0x65, 0x70, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x12,
	0x23, 0x0a, 0x0d, 0x6b, 0x65, 0x65, 0x70, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6b, 0x65, 0x65, 0x70, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x6f, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69,
	0x6f, 0x75, 0x73, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x12, 0x74, 0x6f, 0x50, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x3a, 0x37, 0xba, 0x47, 0x34, 0xba, 0x01, 0x0a, 0x6b, 0x65,
	0x65, 0x70, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0xba, 0x01, 0x0d, 0x6b, 0x65, 0x65, 0x70, 0x5f,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0xba, 0x01, 0x14, 0x74, 0x6f, 0x5f, 0x70, 0x72,
	0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x3a,
	0x1f, 0xba, 0x47, 0x1c, 0xba, 0x01, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0xba, 0x01, 0x07,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0xba, 0x01, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x22, 0x7c, 0x0a, 0x13, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x76, 0x65, 0x72, 0x74, 0x4a,
	0x6f, 0x62, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1e, 0x0a, 0x0b, 0x6f, 0x6b, 0x5f, 0x6a, 0x6f,
	0x62, 0x5f, 0x75, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x6b,
	0x4a, 0x6f, 0x62, 0x55, 0x69, 0x64, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x5f,
	0x6a, 0x6f, 0x62, 0x5f, 0x75, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b,
	0x66, 0x61, 0x69, 0x6c, 0x4a, 0x6f, 0x62, 0x55, 0x69, 0x64, 0x73, 0x3a, 0x21, 0xba, 0x47, 0x1e,
	0xba, 0x01, 0x0b, 0x6f, 0x6b, 0x5f, 0x6a, 0x6f, 0x62, 0x5f, 0x75, 0x69, 0x64, 0x73, 0xba, 0x01,
	0x0d, 0x66, 0x61, 0x69, 0x6c, 0x5f, 0x6a, 0x6f, 0x62, 0x5f, 0x75, 0x69, 0x64, 0x73, 0x22, 0x94,
	0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x00,
	0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2e, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x3a, 0x0c, 0xba, 0x47, 0x09, 0xba, 0x01, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x81, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62,
	0x41, 0x6e, 0x6e, 0x6f, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x26, 0x0a, 0x05, 0x61, 0x6e,
	0x6e, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x41, 0x6e, 0x6e, 0x6f, 0x52, 0x05, 0x61, 0x6e, 0x6e,
	0x6f, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78,
	0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x3a, 0x1d, 0xba, 0x47, 0x1a, 0xba,
	0x01, 0x05, 0x61, 0x6e, 0x6e, 0x6f, 0x73, 0xba, 0x01, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xe4, 0x01, 0x0a, 0x1a, 0x53, 0x65,
	0x74, 0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b,
	0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x78, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6c, 0x65, 0x6d, 0x49, 0x64, 0x78, 0x12, 0x1f, 0x0a, 0x0b,
	0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x78, 0x12, 0x2d, 0x0a,
	0x0d, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x90, 0x01, 0x01, 0x52, 0x0c,
	0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x69, 0x3a, 0x32, 0xba, 0x47,
	0x2f, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x5f, 0x69,
	0x64, 0x78, 0xba, 0x01, 0x0b, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x78,
	0xba, 0x01, 0x0d, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x69,
	0x22, 0x6e, 0x0a, 0x13, 0x53, 0x61, 0x76, 0x65, 0x4a, 0x6f, 0x62, 0x44, 0x72, 0x61, 0x66, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x72,
	0x61, 0x66, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x72, 0x61, 0x66, 0x74,
	0x22, 0x2d, 0x0a, 0x11, 0x53, 0x61, 0x76, 0x65, 0x4a, 0x6f, 0x62, 0x44, 0x72, 0x61, 0x66, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0x3d, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x44, 0x72, 0x61, 0x66, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x58,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x44, 0x72, 0x61, 0x66, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09,
	0x64, 0x72, 0x61, 0x66, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x64, 0x72, 0x61, 0x66, 0x74, 0x55, 0x72, 0x6c, 0x3a, 0x0d, 0xba, 0x47, 0x0a, 0xba, 0x01,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xee, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74,
	0x4a, 0x6f, 0x62, 0x4c, 0x61, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x4c, 0x6f, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x12, 0x1d, 0x0a, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x52, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x12,
	0x50, 0x0a, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x32, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4a, 0x6f, 0x62, 0x4c, 0x61, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x4c, 0x6f, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x1a, 0x36, 0x0a, 0x09, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x29,
	0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x75, 0x70, 0x10, 0x01, 0x12,
	0x08, 0x0a, 0x04, 0x64, 0x6f, 0x77, 0x6e, 0x10, 0x02, 0x22, 0x49, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x4a, 0x6f, 0x62, 0x4c, 0x61, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x4c, 0x6f, 0x67,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2d, 0x0a, 0x03, 0x6c, 0x6f, 0x67, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4a, 0x6f, 0x62, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x4c, 0x6f, 0x67, 0x52,
	0x03, 0x6c, 0x6f, 0x67, 0x22, 0x4d, 0x0a, 0x15, 0x48, 0x61, 0x73, 0x48, 0x6f, 0x6c, 0x64, 0x69,
	0x6e, 0x67, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a,
	0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6f, 0x72, 0x67, 0x55, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x75,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x55,
	0x69, 0x64, 0x73, 0x22, 0xa5, 0x01, 0x0a, 0x13, 0x48, 0x61, 0x73, 0x48, 0x6f, 0x6c, 0x64, 0x69,
	0x6e, 0x67, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x43, 0x0a, 0x07, 0x68,
	0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x61, 0x73, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e,
	0x67, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x48, 0x6f, 0x6c, 0x64, 0x69,
	0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x68, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67,
	0x1a, 0x3a, 0x0a, 0x0c, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x3a, 0x0d, 0xba, 0x47,
	0x0a, 0xba, 0x01, 0x07, 0x68, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x22, 0x69, 0x0a, 0x15, 0x53,
	0x6b, 0x69, 0x70, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6c, 0x6f, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x27, 0x0a,
	0x0f, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x73, 0x6b, 0x69, 0x70, 0x41, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x3e, 0x0a, 0x13, 0x53, 0x6b, 0x69, 0x70, 0x41, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x27, 0x0a,
	0x0f, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x73, 0x6b, 0x69, 0x70, 0x41, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x2c, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x6b, 0x69,
	0x70, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x75, 0x69, 0x64, 0x22, 0x41, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x53, 0x6b, 0x69, 0x70, 0x41,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x27,
	0x0a, 0x0f, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x73, 0x6b, 0x69, 0x70, 0x41, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0xd6, 0x0f, 0x0a, 0x04, 0x4a, 0x6f, 0x62, 0x73,
	0x12, 0x36, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x12, 0x19, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f,
	0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x22, 0x00, 0x12, 0x36, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4a, 0x6f, 0x62, 0x12, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x0c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x22, 0x00,
	0x12, 0x40, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x12, 0x19, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f,
	0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x00, 0x12, 0x46, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x12, 0x16, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x0c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4a,
	0x6f, 0x62, 0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x12, 0x0e, 0x2f, 0x76, 0x31, 0x2f,
	0x6a, 0x6f, 0x62, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x4b, 0x0a, 0x07, 0x4c, 0x69,
	0x73, 0x74, 0x4a, 0x6f, 0x62, 0x12, 0x17, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x10, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0a, 0x12, 0x08, 0x2f,
	0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x12, 0x5b, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x4a, 0x6f,
	0x62, 0x6c, 0x6f, 0x67, 0x12, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x4a, 0x6f, 0x62, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x17, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62,
	0x6c, 0x6f, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14,
	0x12, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d,
	0x2f, 0x6c, 0x6f, 0x67, 0x12, 0x64, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x52, 0x61, 0x77, 0x4a, 0x6f,
	0x62, 0x6c, 0x6f, 0x67, 0x12, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x4a, 0x6f, 0x62, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x61, 0x77,
	0x4a, 0x6f, 0x62, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x17, 0x12, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x2f, 0x7b, 0x75,
	0x69, 0x64, 0x7d, 0x2f, 0x72, 0x61, 0x77, 0x6c, 0x6f, 0x67, 0x12, 0x5a, 0x0a, 0x08, 0x43, 0x6c,
	0x61, 0x69, 0x6d, 0x4a, 0x6f, 0x62, 0x12, 0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d,
	0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x19, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x13, 0x3a, 0x01, 0x2a, 0x22, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x73,
	0x2f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x12, 0x60, 0x0a, 0x09, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e,
	0x4a, 0x6f, 0x62, 0x12, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01,
	0x2a, 0x22, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64,
	0x7d, 0x2f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x60, 0x0a, 0x09, 0x47, 0x69, 0x76, 0x65,
	0x75, 0x70, 0x4a, 0x6f, 0x62, 0x12, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x69, 0x76, 0x65, 0x75, 0x70, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a,
	0x3a, 0x01, 0x2a, 0x22, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x2f, 0x7b, 0x75,
	0x69, 0x64, 0x7d, 0x2f, 0x67, 0x69, 0x76, 0x65, 0x75, 0x70, 0x12, 0x60, 0x0a, 0x09, 0x53, 0x75,
	0x62, 0x6d, 0x69, 0x74, 0x4a, 0x6f, 0x62, 0x12, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x2f,
	0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x12, 0x60, 0x0a, 0x09,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4a, 0x6f, 0x62, 0x12, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x20, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62,
	0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x6a,
	0x0a, 0x0e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x76, 0x65, 0x72, 0x74, 0x4a, 0x6f, 0x62,
	0x12, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x76, 0x65, 0x72, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x76, 0x65, 0x72, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1a,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x3a, 0x01, 0x2a, 0x22, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x6a,
	0x6f, 0x62, 0x73, 0x2f, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x12, 0x9f, 0x01, 0x0a, 0x13, 0x53,
	0x65, 0x74, 0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69,
	0x6e, 0x67, 0x12, 0x23, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74,
	0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x4b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x45, 0x3a, 0x01, 0x2a, 0x1a, 0x40, 0x2f, 0x76, 0x31, 0x2f,
	0x6a, 0x6f, 0x62, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x73,
	0x2f, 0x7b, 0x65, 0x6c, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x78, 0x7d, 0x2f, 0x72, 0x61, 0x77, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x2f, 0x7b, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64,
	0x78, 0x7d, 0x2f, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x69, 0x0a, 0x0c,
	0x53, 0x61, 0x76, 0x65, 0x4a, 0x6f, 0x62, 0x44, 0x72, 0x61, 0x66, 0x74, 0x12, 0x1c, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x4a, 0x6f, 0x62, 0x44, 0x72,
	0x61, 0x66, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x4a, 0x6f, 0x62, 0x44, 0x72, 0x61, 0x66,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01,
	0x2a, 0x1a, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64,
	0x7d, 0x2f, 0x64, 0x72, 0x61, 0x66, 0x74, 0x12, 0x63, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4a, 0x6f,
	0x62, 0x44, 0x72, 0x61, 0x66, 0x74, 0x12, 0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x44, 0x72, 0x61, 0x66, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x4a, 0x6f, 0x62, 0x44, 0x72, 0x61, 0x66, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1c,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x73,
	0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x64, 0x72, 0x61, 0x66, 0x74, 0x12, 0x94, 0x01, 0x0a,
	0x13, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x4c, 0x61, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69,
	0x74, 0x4c, 0x6f, 0x67, 0x12, 0x23, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x4a, 0x6f, 0x62, 0x4c, 0x61, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x4c,
	0x6f, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x4c, 0x61, 0x73, 0x74, 0x43, 0x6f,
	0x6d, 0x6d, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x35, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x2f, 0x12, 0x2d, 0x2f, 0x76, 0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x2f, 0x7b,
	0x75, 0x69, 0x64, 0x7d, 0x2f, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x2f, 0x7b, 0x70, 0x68, 0x61,
	0x73, 0x65, 0x7d, 0x2f, 0x6c, 0x61, 0x73, 0x74, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x2d,
	0x6c, 0x6f, 0x67, 0x12, 0x6a, 0x0a, 0x0e, 0x48, 0x61, 0x73, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e,
	0x67, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x48, 0x61, 0x73, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x48, 0x61, 0x73, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x12, 0x12, 0x2f, 0x76, 0x31,
	0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x2f, 0x2d, 0x2f, 0x68, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x12,
	0x79, 0x0a, 0x0e, 0x53, 0x6b, 0x69, 0x70, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6b, 0x69, 0x70,
	0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6b, 0x69, 0x70,
	0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x76, 0x31, 0x2f,
	0x6a, 0x6f, 0x62, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x73, 0x6b, 0x69, 0x70, 0x2d,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x83, 0x01, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x53, 0x6b, 0x69, 0x70, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x21, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x6b,
	0x69, 0x70, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x53, 0x6b, 0x69, 0x70, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x12, 0x22, 0x2f, 0x76,
	0x31, 0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x67, 0x65, 0x74,
	0x2d, 0x73, 0x6b, 0x69, 0x70, 0x2d, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x3e, 0x0a, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x31, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e, 0x6e, 0x6f,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_anno_v1_job_proto_rawDescOnce sync.Once
	file_anno_v1_job_proto_rawDescData = file_anno_v1_job_proto_rawDesc
)

func file_anno_v1_job_proto_rawDescGZIP() []byte {
	file_anno_v1_job_proto_rawDescOnce.Do(func() {
		file_anno_v1_job_proto_rawDescData = protoimpl.X.CompressGZIP(file_anno_v1_job_proto_rawDescData)
	})
	return file_anno_v1_job_proto_rawDescData
}

var file_anno_v1_job_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_anno_v1_job_proto_msgTypes = make([]protoimpl.MessageInfo, 58)
var file_anno_v1_job_proto_goTypes = []interface{}{
	(GetJoblogReply_Log_Action_Enum)(0),            // 0: anno.v1.GetJoblogReply.Log.Action.Enum
	(ClaimJobRequest_Prefer_Enum)(0),               // 1: anno.v1.ClaimJobRequest.Prefer.Enum
	(ReviewJobRequest_Decision_Enum)(0),            // 2: anno.v1.ReviewJobRequest.Decision.Enum
	(AnnoComment_Scope_Enum)(0),                    // 3: anno.v1.AnnoComment.Scope.Enum
	(Job_State_Enum)(0),                            // 4: anno.v1.Job.State.Enum
	(GetJobLastCommitLogRequest_Direction_Enum)(0), // 5: anno.v1.GetJobLastCommitLogRequest.Direction.Enum
	(*CreateJobRequest)(nil),                       // 6: anno.v1.CreateJobRequest
	(*UpdateJobRequest)(nil),                       // 7: anno.v1.UpdateJobRequest
	(*DeleteJobRequest)(nil),                       // 8: anno.v1.DeleteJobRequest
	(*GetJobRequest)(nil),                          // 9: anno.v1.GetJobRequest
	(*ListJobFilter)(nil),                          // 10: anno.v1.ListJobFilter
	(*ListJobRequest)(nil),                         // 11: anno.v1.ListJobRequest
	(*ListJobReply)(nil),                           // 12: anno.v1.ListJobReply
	(*GetJoblogRequest)(nil),                       // 13: anno.v1.GetJoblogRequest
	(*GetJoblogReply)(nil),                         // 14: anno.v1.GetJoblogReply
	(*GetRawJoblogReply)(nil),                      // 15: anno.v1.GetRawJoblogReply
	(*AssignJobRequest)(nil),                       // 16: anno.v1.AssignJobRequest
	(*ClaimJobRequest)(nil),                        // 17: anno.v1.ClaimJobRequest
	(*ClaimJobResponse)(nil),                       // 18: anno.v1.ClaimJobResponse
	(*GiveupJobRequest)(nil),                       // 19: anno.v1.GiveupJobRequest
	(*SubmitJobRequest)(nil),                       // 20: anno.v1.SubmitJobRequest
	(*ReviewJobRequest)(nil),                       // 21: anno.v1.ReviewJobRequest
	(*ResolveAnnoComment)(nil),                     // 22: anno.v1.ResolveAnnoComment
	(*AnnoComment)(nil),                            // 23: anno.v1.AnnoComment
	(*Job)(nil),                                    // 24: anno.v1.Job
	(*JobAnno)(nil),                                // 25: anno.v1.JobAnno
	(*BatchRevertJobRequest)(nil),                  // 26: anno.v1.BatchRevertJobRequest
	(*BatchRevertJobReply)(nil),                    // 27: anno.v1.BatchRevertJobReply
	(*GetJobAnnosRequest)(nil),                     // 28: anno.v1.GetJobAnnosRequest
	(*GetJobAnnosReply)(nil),                       // 29: anno.v1.GetJobAnnosReply
	(*SetRawdataEmbeddingRequest)(nil),             // 30: anno.v1.SetRawdataEmbeddingRequest
	(*SaveJobDraftRequest)(nil),                    // 31: anno.v1.SaveJobDraftRequest
	(*SaveJobDraftReply)(nil),                      // 32: anno.v1.SaveJobDraftReply
	(*GetJobDraftRequest)(nil),                     // 33: anno.v1.GetJobDraftRequest
	(*GetJobDraftReply)(nil),                       // 34: anno.v1.GetJobDraftReply
	(*GetJobLastCommitLogRequest)(nil),             // 35: anno.v1.GetJobLastCommitLogRequest
	(*GetJobLastCommitLogReply)(nil),               // 36: anno.v1.GetJobLastCommitLogReply
	(*HasHoldingJobsRequest)(nil),                  // 37: anno.v1.HasHoldingJobsRequest
	(*HasHoldingJobsReply)(nil),                    // 38: anno.v1.HasHoldingJobsReply
	(*SkipAnnotationRequest)(nil),                  // 39: anno.v1.SkipAnnotationRequest
	(*SkipAnnotationReply)(nil),                    // 40: anno.v1.SkipAnnotationReply
	(*GetSkipAnnotationRequest)(nil),               // 41: anno.v1.GetSkipAnnotationRequest
	(*GetSkipAnnotationReply)(nil),                 // 42: anno.v1.GetSkipAnnotationReply
	(*UpdateJobRequest_Updates)(nil),               // 43: anno.v1.UpdateJobRequest.Updates
	nil,                                            // 44: anno.v1.ListJobReply.ExecutorsEntry
	(*GetJoblogReply_Log)(nil),                     // 45: anno.v1.GetJoblogReply.Log
	(*GetJoblogReply_Log_Action)(nil),              // 46: anno.v1.GetJoblogReply.Log.Action
	(*GetJoblogReply_Log_GiveupReason)(nil),        // 47: anno.v1.GetJoblogReply.Log.GiveupReason
	(*GetJoblogReply_Log_Details)(nil),             // 48: anno.v1.GetJoblogReply.Log.Details
	(*ClaimJobRequest_Prefer)(nil),                 // 49: anno.v1.ClaimJobRequest.Prefer
	(*ReviewJobRequest_Decision)(nil),              // 50: anno.v1.ReviewJobRequest.Decision
	(*AnnoComment_ExtraInfo)(nil),                  // 51: anno.v1.AnnoComment.ExtraInfo
	(*AnnoComment_Scope)(nil),                      // 52: anno.v1.AnnoComment.Scope
	(*Job_State)(nil),                              // 53: anno.v1.Job.State
	(*Job_CamParam)(nil),                           // 54: anno.v1.Job.CamParam
	nil,                                            // 55: anno.v1.Job.CamParamsEntry
	(*Job_ElementData)(nil),                        // 56: anno.v1.Job.ElementData
	(*Job_AnnotationData)(nil),                     // 57: anno.v1.Job.AnnotationData
	(*Job_CommentData)(nil),                        // 58: anno.v1.Job.CommentData
	nil,                                            // 59: anno.v1.Job.ElementData.CamParamsEntry
	(*BatchRevertJobRequest_Action)(nil),           // 60: anno.v1.BatchRevertJobRequest.Action
	(*BatchRevertJobRequest_Options)(nil),          // 61: anno.v1.BatchRevertJobRequest.Options
	(*GetJobLastCommitLogRequest_Direction)(nil),   // 62: anno.v1.GetJobLastCommitLogRequest.Direction
	nil,                               // 63: anno.v1.HasHoldingJobsReply.HoldingEntry
	(*Element)(nil),                   // 64: anno.v1.Element
	(*v1.BaseUser)(nil),               // 65: iam.v1.BaseUser
	(*AnnoCommentReason_Reasons)(nil), // 66: anno.v1.AnnoCommentReason.Reasons
	(*timestamppb.Timestamp)(nil),     // 67: google.protobuf.Timestamp
	(*ElementAnno)(nil),               // 68: anno.v1.ElementAnno
	(*AttrAndValues)(nil),             // 69: anno.v1.AttrAndValues
	(*ElementAnno_Metadata)(nil),      // 70: anno.v1.ElementAnno.Metadata
	(*emptypb.Empty)(nil),             // 71: google.protobuf.Empty
}
var file_anno_v1_job_proto_depIdxs = []int32{
	64, // 0: anno.v1.CreateJobRequest.elements:type_name -> anno.v1.Element
	25, // 1: anno.v1.CreateJobRequest.annotations:type_name -> anno.v1.JobAnno
	23, // 2: anno.v1.CreateJobRequest.comments:type_name -> anno.v1.AnnoComment
	43, // 3: anno.v1.UpdateJobRequest.job:type_name -> anno.v1.UpdateJobRequest.Updates
	4,  // 4: anno.v1.ListJobFilter.state:type_name -> anno.v1.Job.State.Enum
	10, // 5: anno.v1.ListJobRequest.filter:type_name -> anno.v1.ListJobFilter
	24, // 6: anno.v1.ListJobReply.jobs:type_name -> anno.v1.Job
	44, // 7: anno.v1.ListJobReply.executors:type_name -> anno.v1.ListJobReply.ExecutorsEntry
	45, // 8: anno.v1.GetJoblogReply.logs:type_name -> anno.v1.GetJoblogReply.Log
	1,  // 9: anno.v1.ClaimJobRequest.prefer:type_name -> anno.v1.ClaimJobRequest.Prefer.Enum
	24, // 10: anno.v1.ClaimJobResponse.job:type_name -> anno.v1.Job
	25, // 11: anno.v1.SubmitJobRequest.annotations:type_name -> anno.v1.JobAnno
	22, // 12: anno.v1.SubmitJobRequest.resolves:type_name -> anno.v1.ResolveAnnoComment
	25, // 13: anno.v1.ReviewJobRequest.annotations:type_name -> anno.v1.JobAnno
	2,  // 14: anno.v1.ReviewJobRequest.decision:type_name -> anno.v1.ReviewJobRequest.Decision.Enum
	23, // 15: anno.v1.ReviewJobRequest.comments:type_name -> anno.v1.AnnoComment
	22, // 16: anno.v1.ReviewJobRequest.resolves:type_name -> anno.v1.ResolveAnnoComment
	23, // 17: anno.v1.ReviewJobRequest.updated_comments:type_name -> anno.v1.AnnoComment
	22, // 18: anno.v1.ReviewJobRequest.deleted_comments:type_name -> anno.v1.ResolveAnnoComment
	65, // 19: anno.v1.AnnoComment.commenter:type_name -> iam.v1.BaseUser
	51, // 20: anno.v1.AnnoComment.extra_info:type_name -> anno.v1.AnnoComment.ExtraInfo
	66, // 21: anno.v1.AnnoComment.reasons:type_name -> anno.v1.AnnoCommentReason.Reasons
	3,  // 22: anno.v1.AnnoComment.scope:type_name -> anno.v1.AnnoComment.Scope.Enum
	67, // 23: anno.v1.AnnoComment.created_at:type_name -> google.protobuf.Timestamp
	64, // 24: anno.v1.Job.elements:type_name -> anno.v1.Element
	68, // 25: anno.v1.Job.annotations:type_name -> anno.v1.ElementAnno
	23, // 26: anno.v1.Job.comments:type_name -> anno.v1.AnnoComment
	4,  // 27: anno.v1.Job.state:type_name -> anno.v1.Job.State.Enum
	65, // 28: anno.v1.Job.last_executor:type_name -> iam.v1.BaseUser
	65, // 29: anno.v1.Job.last_execteam:type_name -> iam.v1.BaseUser
	69, // 30: anno.v1.Job.job_attrs:type_name -> anno.v1.AttrAndValues
	55, // 31: anno.v1.Job.cam_params:type_name -> anno.v1.Job.CamParamsEntry
	67, // 32: anno.v1.Job.updated_at:type_name -> google.protobuf.Timestamp
	67, // 33: anno.v1.Job.created_at:type_name -> google.protobuf.Timestamp
	68, // 34: anno.v1.JobAnno.element_annos:type_name -> anno.v1.ElementAnno
	69, // 35: anno.v1.JobAnno.attrs:type_name -> anno.v1.AttrAndValues
	60, // 36: anno.v1.BatchRevertJobRequest.action:type_name -> anno.v1.BatchRevertJobRequest.Action
	61, // 37: anno.v1.BatchRevertJobRequest.options:type_name -> anno.v1.BatchRevertJobRequest.Options
	10, // 38: anno.v1.BatchRevertJobRequest.filter:type_name -> anno.v1.ListJobFilter
	10, // 39: anno.v1.GetJobAnnosRequest.filter:type_name -> anno.v1.ListJobFilter
	25, // 40: anno.v1.GetJobAnnosReply.annos:type_name -> anno.v1.JobAnno
	5,  // 41: anno.v1.GetJobLastCommitLogRequest.direction:type_name -> anno.v1.GetJobLastCommitLogRequest.Direction.Enum
	45, // 42: anno.v1.GetJobLastCommitLogReply.log:type_name -> anno.v1.GetJoblogReply.Log
	63, // 43: anno.v1.HasHoldingJobsReply.holding:type_name -> anno.v1.HasHoldingJobsReply.HoldingEntry
	70, // 44: anno.v1.ListJobReply.ExecutorsEntry.value:type_name -> anno.v1.ElementAnno.Metadata
	65, // 45: anno.v1.GetJoblogReply.Log.operator:type_name -> iam.v1.BaseUser
	0,  // 46: anno.v1.GetJoblogReply.Log.action:type_name -> anno.v1.GetJoblogReply.Log.Action.Enum
	48, // 47: anno.v1.GetJoblogReply.Log.details:type_name -> anno.v1.GetJoblogReply.Log.Details
	67, // 48: anno.v1.GetJoblogReply.Log.created_at:type_name -> google.protobuf.Timestamp
	23, // 49: anno.v1.GetJoblogReply.Log.Details.add_comments:type_name -> anno.v1.AnnoComment
	22, // 50: anno.v1.GetJoblogReply.Log.Details.resolves:type_name -> anno.v1.ResolveAnnoComment
	47, // 51: anno.v1.GetJoblogReply.Log.Details.giveup_reason:type_name -> anno.v1.GetJoblogReply.Log.GiveupReason
	54, // 52: anno.v1.Job.CamParamsEntry.value:type_name -> anno.v1.Job.CamParam
	64, // 53: anno.v1.Job.ElementData.elements:type_name -> anno.v1.Element
	59, // 54: anno.v1.Job.ElementData.cam_params:type_name -> anno.v1.Job.ElementData.CamParamsEntry
	68, // 55: anno.v1.Job.AnnotationData.element_annos:type_name -> anno.v1.ElementAnno
	69, // 56: anno.v1.Job.AnnotationData.job_attrs:type_name -> anno.v1.AttrAndValues
	23, // 57: anno.v1.Job.CommentData.comments:type_name -> anno.v1.AnnoComment
	54, // 58: anno.v1.Job.ElementData.CamParamsEntry.value:type_name -> anno.v1.Job.CamParam
	6,  // 59: anno.v1.Jobs.CreateJob:input_type -> anno.v1.CreateJobRequest
	7,  // 60: anno.v1.Jobs.UpdateJob:input_type -> anno.v1.UpdateJobRequest
	8,  // 61: anno.v1.Jobs.DeleteJob:input_type -> anno.v1.DeleteJobRequest
	9,  // 62: anno.v1.Jobs.GetJob:input_type -> anno.v1.GetJobRequest
	11, // 63: anno.v1.Jobs.ListJob:input_type -> anno.v1.ListJobRequest
	13, // 64: anno.v1.Jobs.GetJoblog:input_type -> anno.v1.GetJoblogRequest
	13, // 65: anno.v1.Jobs.GetRawJoblog:input_type -> anno.v1.GetJoblogRequest
	17, // 66: anno.v1.Jobs.ClaimJob:input_type -> anno.v1.ClaimJobRequest
	16, // 67: anno.v1.Jobs.AssignJob:input_type -> anno.v1.AssignJobRequest
	19, // 68: anno.v1.Jobs.GiveupJob:input_type -> anno.v1.GiveupJobRequest
	20, // 69: anno.v1.Jobs.SubmitJob:input_type -> anno.v1.SubmitJobRequest
	21, // 70: anno.v1.Jobs.ReviewJob:input_type -> anno.v1.ReviewJobRequest
	26, // 71: anno.v1.Jobs.BatchRevertJob:input_type -> anno.v1.BatchRevertJobRequest
	30, // 72: anno.v1.Jobs.SetRawdataEmbedding:input_type -> anno.v1.SetRawdataEmbeddingRequest
	31, // 73: anno.v1.Jobs.SaveJobDraft:input_type -> anno.v1.SaveJobDraftRequest
	33, // 74: anno.v1.Jobs.GetJobDraft:input_type -> anno.v1.GetJobDraftRequest
	35, // 75: anno.v1.Jobs.GetJobLastCommitLog:input_type -> anno.v1.GetJobLastCommitLogRequest
	37, // 76: anno.v1.Jobs.HasHoldingJobs:input_type -> anno.v1.HasHoldingJobsRequest
	39, // 77: anno.v1.Jobs.SkipAnnotation:input_type -> anno.v1.SkipAnnotationRequest
	41, // 78: anno.v1.Jobs.GetSkipAnnotation:input_type -> anno.v1.GetSkipAnnotationRequest
	24, // 79: anno.v1.Jobs.CreateJob:output_type -> anno.v1.Job
	24, // 80: anno.v1.Jobs.UpdateJob:output_type -> anno.v1.Job
	71, // 81: anno.v1.Jobs.DeleteJob:output_type -> google.protobuf.Empty
	24, // 82: anno.v1.Jobs.GetJob:output_type -> anno.v1.Job
	12, // 83: anno.v1.Jobs.ListJob:output_type -> anno.v1.ListJobReply
	14, // 84: anno.v1.Jobs.GetJoblog:output_type -> anno.v1.GetJoblogReply
	15, // 85: anno.v1.Jobs.GetRawJoblog:output_type -> anno.v1.GetRawJoblogReply
	18, // 86: anno.v1.Jobs.ClaimJob:output_type -> anno.v1.ClaimJobResponse
	71, // 87: anno.v1.Jobs.AssignJob:output_type -> google.protobuf.Empty
	71, // 88: anno.v1.Jobs.GiveupJob:output_type -> google.protobuf.Empty
	71, // 89: anno.v1.Jobs.SubmitJob:output_type -> google.protobuf.Empty
	71, // 90: anno.v1.Jobs.ReviewJob:output_type -> google.protobuf.Empty
	27, // 91: anno.v1.Jobs.BatchRevertJob:output_type -> anno.v1.BatchRevertJobReply
	71, // 92: anno.v1.Jobs.SetRawdataEmbedding:output_type -> google.protobuf.Empty
	32, // 93: anno.v1.Jobs.SaveJobDraft:output_type -> anno.v1.SaveJobDraftReply
	34, // 94: anno.v1.Jobs.GetJobDraft:output_type -> anno.v1.GetJobDraftReply
	36, // 95: anno.v1.Jobs.GetJobLastCommitLog:output_type -> anno.v1.GetJobLastCommitLogReply
	38, // 96: anno.v1.Jobs.HasHoldingJobs:output_type -> anno.v1.HasHoldingJobsReply
	40, // 97: anno.v1.Jobs.SkipAnnotation:output_type -> anno.v1.SkipAnnotationReply
	42, // 98: anno.v1.Jobs.GetSkipAnnotation:output_type -> anno.v1.GetSkipAnnotationReply
	79, // [79:99] is the sub-list for method output_type
	59, // [59:79] is the sub-list for method input_type
	59, // [59:59] is the sub-list for extension type_name
	59, // [59:59] is the sub-list for extension extendee
	0,  // [0:59] is the sub-list for field type_name
}

func init() { file_anno_v1_job_proto_init() }
func file_anno_v1_job_proto_init() {
	if File_anno_v1_job_proto != nil {
		return
	}
	file_anno_v1_elemanno_proto_init()
	file_anno_v1_type_lotconfig_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_anno_v1_job_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListJobFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListJobReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJoblogRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJoblogReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRawJoblogReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssignJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClaimJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClaimJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiveupJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResolveAnnoComment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnoComment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Job); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobAnno); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchRevertJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchRevertJobReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobAnnosRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobAnnosReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRawdataEmbeddingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveJobDraftRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveJobDraftReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobDraftRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobDraftReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobLastCommitLogRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobLastCommitLogReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HasHoldingJobsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HasHoldingJobsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SkipAnnotationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SkipAnnotationReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSkipAnnotationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSkipAnnotationReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateJobRequest_Updates); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJoblogReply_Log); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJoblogReply_Log_Action); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJoblogReply_Log_GiveupReason); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJoblogReply_Log_Details); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClaimJobRequest_Prefer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewJobRequest_Decision); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnoComment_ExtraInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnoComment_Scope); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Job_State); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Job_CamParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Job_ElementData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Job_AnnotationData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Job_CommentData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchRevertJobRequest_Action); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchRevertJobRequest_Options); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_job_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobLastCommitLogRequest_Direction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anno_v1_job_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   58,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_anno_v1_job_proto_goTypes,
		DependencyIndexes: file_anno_v1_job_proto_depIdxs,
		EnumInfos:         file_anno_v1_job_proto_enumTypes,
		MessageInfos:      file_anno_v1_job_proto_msgTypes,
	}.Build()
	File_anno_v1_job_proto = out.File
	file_anno_v1_job_proto_rawDesc = nil
	file_anno_v1_job_proto_goTypes = nil
	file_anno_v1_job_proto_depIdxs = nil
}
