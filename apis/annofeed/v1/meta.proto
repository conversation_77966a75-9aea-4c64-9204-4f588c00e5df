syntax = "proto3";

package annofeed.v1;

import "anno/v1/elemanno.proto";
import "types/filelist.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/annofeed/v1;annofeed";
option java_multiple_files = true;
option java_package = "annofeed.v1";

message Meta {
    anno.v1.Element.Type.Enum type = 1;
    string style = 2;
    repeated anno.v1.Element elems = 3;
    // if to create new data based on an existing data
    string base_on_uid = 4;
    // metadata from original data
    map<string, string> metadata = 5;
    // metafiles from original data
    types.Filelist metafiles = 6;

    message Lot {
        string uid = 1;
        string name = 2;
    }
    Lot related_lot = 7;
}
