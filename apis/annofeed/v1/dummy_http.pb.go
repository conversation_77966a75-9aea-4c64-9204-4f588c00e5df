// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: annofeed/v1/dummy.proto

package annofeed

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationDummyDummy = "/annofeed.v1.Dummy/Dummy"

type DummyHTTPServer interface {
	Dummy(context.Context, *emptypb.Empty) (*DummyReply, error)
}

func RegisterDummyHTTPServer(s *http.Server, srv DummyHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/dummy", _Dummy_Dummy0_HTTP_Handler(srv))
}

func _Dummy_Dummy0_HTTP_Handler(srv DummyHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDummyDummy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Dummy(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DummyReply)
		return ctx.Result(200, reply)
	}
}

type DummyHTTPClient interface {
	Dummy(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *DummyReply, err error)
}

type DummyHTTPClientImpl struct {
	cc *http.Client
}

func NewDummyHTTPClient(client *http.Client) DummyHTTPClient {
	return &DummyHTTPClientImpl{client}
}

func (c *DummyHTTPClientImpl) Dummy(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*DummyReply, error) {
	var out DummyReply
	pattern := "/v1/dummy"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDummyDummy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
