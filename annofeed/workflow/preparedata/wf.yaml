workflow:
  args:
    convDataImg: 'artifactory.rp.konvery.work/docker/python:3.11' # TODO: make it configurable
    convDataCmd: '["python", "main.py"]'
    data: "{}"

  storage:
    calSizeActivity:
      activity: PrepareDataGetVolumeSizeGB
      args: ["$<data>"]

  steps:
    - type: activity
      executor: PrepareDataWrapup
      args: ["$<wfVolumePath>/data", "$<data>", "$<wf.error>"]
      defer: true
      timeout: 6h

    - type: activity
      executor: PrepareDataFetch
      args: ["$<wfVolumePath>/data", "$<data>"]
      timeout: 24h
    - type: activity
      executor: PrepareDataParseMeta
      args: ["$<wfVolumePath>/data", "$<data>"]
    - type: activity
      executor: PrepareDataConverterScript
      args: ["$<wfVolumePath>/data", "$<data>"]
      resultRef: convScriptURI

    - type: activity
      executor: WFMkdir
      args: ['{"$<wfVolumePath>/tmp": 511}'] # use 511 instead of 0777 as json doesn't recognize octal numbers
      when: 'convScriptURI != ""'
    - type: kubejob
      executor: convertData
      args: ["$<wfVolumePath>/data", "$<wfVolumePath>/tmp"]
      when: 'convScriptURI != ""'
      timeout: 24h
    - type: activity
      executor: WFRemovePath
      args: ['["$<wfVolumePath>/tmp"]', 'true']
      when: 'convScriptURI != ""'

    - type: activity
      executor: PrepareDataTransform
      args: ["$<wfVolumePath>/data", "$<data>"]
      timeout: 24h
    - type: activity
      executor: PrepareDataStandardize
      args: ["$<wfVolumePath>/data", "$<data>"]
      timeout: 24h
    - type: activity
      executor: PrepareDataDetectDataType
      args: ["$<wfVolumePath>/data", "$<data>"]
    - type: activity
      executor: PrepareDataValidateRawdatas
      args: ["$<wfVolumePath>/data", "$<data>"]
    - type: activity
      executor: PrepareDataParseRawdatas
      args: ["$<wfVolumePath>/data", "$<data>"]
      timeout: 6h
    - type: activity
      executor: PrepareDataParseAnnos
      args: ["$<wfVolumePath>/data", "$<data>"]
      timeout: 6h
    - type: activity
      executor: PrepareDataUploadRawdatas
      args: ["$<wfVolumePath>/data", "$<data>"]
      timeout: 24h


kubeJobs:
  convertData:
    image: $<convDataImg> # docker image url and tag
    command: ["$<convDataCmd[*]>"]
    downloadFiles:
      "auto:": "$<convScriptURI>"
