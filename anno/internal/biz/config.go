package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

const (
	ConfigNameCommentReasons = "CommentReasons"
)

type Config struct {
	ID        int64           `json:"id" gorm:"default:null"`
	Name      string          `json:"name" gorm:"default:null"`
	Type      string          `json:"type" gorm:"default:null"`
	Value     json.RawMessage `json:"value" gorm:"default:null"`
	UpdatedAt time.Time       `json:"updated_at" gorm:"default:null"`
	CreatedAt time.Time       `json:"created_at" gorm:"default:null"`
}

func (o Config) TableName() string { return "configs" }
func (o *Config) GetID() int64     { return o.ID }
func (o *Config) GetUid() string   { return o.Name }
func (o *Config) UidFld() string   { return Config_Name.Bare() }

var (
	_config   = field.RegObject(&Config{})
	_configTF = repo.NewPartialTableField(Config{}.TableName())

	Config_ID        = _configTF(field.Sname(&_config.ID))
	Config_Name      = _configTF(field.Sname(&_config.Name))
	Config_Type      = _configTF(field.Sname(&_config.Type))
	Config_Value     = _configTF(field.Sname(&_config.Value))
	Config_UpdatedAt = _configTF(field.Sname(&_config.UpdatedAt))
	Config_CreatedAt = _configTF(field.Sname(&_config.CreatedAt))
)

type ConfigRepo interface {
	repo.GenericRepo[Config]
}

type ConfigBiz struct {
	ConfigRepo
	log *log.Helper
}

func NewConfigBiz(repo ConfigRepo, logger log.Logger) *ConfigBiz {
	return &ConfigBiz{ConfigRepo: repo, log: log.NewHelper(logger)}
}

func (o *ConfigBiz) GetByName(ctx context.Context, name string, v any) (*Config, error) {
	c, err := o.GetByFilter(ctx, repo.NewEqualFilter(Config_Name.Bare(), name))
	if err != nil {
		return nil, err
	}
	if v != nil {
		if err = JSONCodec().Unmarshal(c.Value, v); err != nil {
			return nil, fmt.Errorf("failed to unmarshal config: %w", err)
		}
	}
	return c, err
}

func (o *ConfigBiz) GetCommentReasons(ctx context.Context) (cr []*anno.CommentReasonClass, err error) {
	_, err = o.GetByName(ctx, ConfigNameCommentReasons, &cr)
	return
}

func (o *ConfigBiz) PutCommentReasons(ctx context.Context, cr []*anno.CommentReasonClass) (err error) {
	var old []*anno.CommentReasonClass
	cfg, err := o.GetByName(ctx, ConfigNameCommentReasons, &old)
	if err != nil && !errors.IsNotFound(err) {
		return
	}

	// merge comment reasons
	for _, c := range cr {
		_, i, _ := lo.FindIndexOf(old, func(v *anno.CommentReasonClass) bool { return c.Class.Name == v.Class.Name })
		if i > -1 {
			old[i] = c
		} else {
			old = append(old, c)
		}
	}
	// delete classes with an empty DisplayName
	old = lo.Filter(old, func(v *anno.CommentReasonClass, _ int) bool { return v.Class.DisplayName != "" })

	val, err := JSONCodec().Marshal(old)
	if err != nil {
		return errors.NewErrServerError().WithCause(err)
	}
	if cfg == nil {
		cfg = &Config{
			Name:  ConfigNameCommentReasons,
			Value: val,
		}
		_, err = o.Create(ctx, cfg)
	} else {
		cfg.Value = val
		_, err = o.Update(ctx, cfg, field.NewMask(Config_Value.Bare()))
	}
	return
}
