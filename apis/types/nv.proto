syntax = "proto3";

package types;

import "openapi/v3/annotations.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/types;types";
option java_multiple_files = true;
option java_package = "types";

message Name {
  option (openapi.v3.schema) = {
    required: ["name"]
  };

  string name = 1;
}

message NameValue {
  option (openapi.v3.schema) = {
    required: ["name", "value"]
  };

  string name = 1;
  string value = 2;
}
