package biz

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_ParamMeta_IsV2(t *testing.T) {
	cases := []struct {
		name        string
		fileContent string
		expected    bool
	}{
		{
			name:        "no meta field",
			fileContent: `{}`,
			expected:    false,
		},
		{
			name:        "version is v1",
			fileContent: `{"meta":{"version":"v1"}}`,
			expected:    false,
		},
		{
			name:        "version is v2",
			fileContent: `{"meta":{"version":"v2"}}`,
			expected:    true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			data := []byte(c.fileContent)
			meta := &ParamMeta{}
			if err := json.Unmarshal(data, meta); err != nil {
				t.Fatal("unmarshal failed")
			}

			actual := meta.IsV2()
			assert.Equal(t, c.expected, actual)
		})
	}
}
