syntax = "proto3";

package anno.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
// import "google/protobuf/timestamp.proto";
// import "google/protobuf/field_mask.proto";
// import "google/api/field_behavior.proto";
// import "anno/v1/type.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/anno/v1;anno";
option java_multiple_files = true;
option java_package = "anno.v1";

service Labelclz {
  rpc CreateLabelcls (Labelcls) returns (Labelcls) {
    option (google.api.http) = {
      post: "/v1/labelcls"
      body: "*"
    };
  }

  rpc UpdateLabelcls (Labelcls) returns (Labelcls) {
    option (google.api.http) = {
      put: "/v1/labelcls/{name}"
      body: "*"
    };
  }

  rpc DeleteLabelcls (DeleteLabelclsRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/labelcls/{name}"
    };
  }

  rpc GetLabelcls (GetLabelclsRequest) returns (Labelcls) {
    option (google.api.http) = {
      get: "/v1/labelcls/{name}"
    };
  }

  rpc ListLabelcls (ListLabelclsRequest) returns (ListLabelclsReply) {
    option (google.api.http) = {
      get: "/v1/labelcls"
    };
  }
}

//message CreateLabelclsRequest {}
//message CreateLabelclsReply {}

// message UpdateLabelclsRequest {
//   Labelcls cls = 1;
//   google.protobuf.FieldMask mask = 2;
// }
//message UpdateLabelclsReply {}

message DeleteLabelclsRequest {
  string name = 1;
}
//message DeleteLabelclsReply {}

message GetLabelclsRequest {
  string name = 1;
}
//message GetLabelclsReply {}

message ListLabelclsRequest {
  // int32 page = 1;
  // int32 pagesz = 2;
  // string name_pattern = 3;
}

message ListLabelclsReply {
  // total number of items found; valid only in the first page reply.
  int32 total = 1;
  repeated Labelcls labelcls = 2;
}

message Labelcls {
  string name = 1;
  // language => name
  map<string, string> langs = 2;
}
