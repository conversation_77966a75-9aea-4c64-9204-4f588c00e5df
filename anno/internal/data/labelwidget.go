// source: anno/v1/labelwidget.proto
package data

import (
	"context"

	"anno/internal/biz"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

type labelwidgetsRepo struct {
	data *Data
	log  *log.Helper
}

func NewLabelwidgetsRepo(data *Data, logger log.Logger) biz.LabelwidgetsRepo {
	return &labelwidgetsRepo{data: data, log: log.NewHelper(logger)}
}

func (o *labelwidgetsRepo) Create(ctx context.Context, p *biz.Labelwidget) (*biz.Labelwidget, error) {
	return Create(ctx, o.data, p)
}

func (o *labelwidgetsRepo) Update(ctx context.Context, p *biz.Labelwidget, fldMask *biz.FieldMask) (*biz.Labelwidget, error) {
	return Update(ctx, o.data, p, biz.LabelwidgetUpdatableFlds, fldMask)
}

func (o *labelwidgetsRepo) loadAssociations(ctx context.Context, data *Data, mod *biz.Labelwidget) error {
	return nil
}

func (o *labelwidgetsRepo) GetByID(ctx context.Context, id int64) (*biz.Labelwidget, error) {
	return GetByID[biz.Labelwidget](ctx, o.data, id, o.loadAssociations)
}

func (o *labelwidgetsRepo) GetByUid(ctx context.Context, uid string) (*biz.Labelwidget, error) {
	return GetByUid[biz.Labelwidget](ctx, o.data, uid, o.loadAssociations)
}

func (o *labelwidgetsRepo) DeleteByID(ctx context.Context, id int64) error {
	return Delete(ctx, o.data, &biz.Labelwidget{ID: id})
}

func (o *labelwidgetsRepo) DeleteByUid(ctx context.Context, uid string) error {
	return Delete(ctx, o.data, &biz.Labelwidget{Name: uid})
}

func (o *labelwidgetsRepo) buildListQuery(ctx context.Context, p *biz.LabelwidgetListFilter) *gorm.DB {
	q := o.data.WithCtx(ctx)
	if len(p.Uids) > 0 {
		q = q.Where(biz.LabelwidgetSfldName+" IN ?", p.Uids)
	}
	if p.NamePattern != "" {
		q = q.Where(biz.LabelwidgetSfldName+" LIKE ?", "%"+p.NamePattern+"%")
	}
	return q
}

func (o *labelwidgetsRepo) List(ctx context.Context, p *biz.LabelwidgetListFilter, pager biz.Pager) ([]*biz.Labelwidget, error) {
	datas := []*biz.Labelwidget{}
	q := o.buildListQuery(ctx, p).Order("id")
	if len(p.Uids) == 0 {
		q = q.Offset(pager.Offset()).Limit(pager.Pagesz)
	}
	err := q.Find(&datas).Error
	if err != nil {
		return nil, err
	}
	return datas, nil
}

func (o *labelwidgetsRepo) Count(ctx context.Context, p *biz.LabelwidgetListFilter) (int, error) {
	mod := &biz.Labelwidget{}
	var cnt int64
	q := o.buildListQuery(ctx, p)
	err := q.Model(mod).Count(&cnt).Error
	if err != nil {
		return 0, err
	}
	return int(cnt), nil
}
