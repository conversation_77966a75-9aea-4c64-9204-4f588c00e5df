BEGIN;

CREATE TABLE jobs
(
    id          BIGINT       NOT NULL PRIMARY KEY,
    lot_id      BIGINT       NOT NULL,
    -- executor_uid VARCHAR(32) NOT NULL,
    -- execteam_uid VARCHAR(32)   NOT NULL,
    -- action      VARCHAR(32)  NOT NULL,
    idx_in_lot  INTEGER      NOT NULL DEFAULT 0,
    elems       INTEGER      NOT NULL DEFAULT 0,
    -- ins_cnt     INTEGER      NOT NULL DEFAULT 0, -- NOT include interpolated objects
    -- ins_total   INTEGER      NOT NULL DEFAULT 0, -- include interpolated objects
    -- ins_2d      INTEGER      NOT NULL DEFAULT 0,
    -- ins_3d      INTEGER      NOT NULL DEFAULT 0,
    -- ins_by_class JSONB        NOT NULL DEFAULT '{}', -- ins count by object class
    phase       SMALLINT     NOT NULL DEFAULT 0, -- current phase
    submits     SMALLINT     NOT NULL DEFAULT 0, -- number of submissions
    finished    BOOLEAN      NOT NULL DEFAULT false, -- is finished
    duration    INTEGER      NOT NULL DEFAULT 0, -- real time cost in seconds; sum of each phase cost
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(), -- job creation time
    first_claim TIMESTAMPTZ  NOT NULL DEFAULT NOW(), -- first claim time
    last_submit TIMESTAMPTZ  NOT NULL DEFAULT NOW()  -- last submit time
);
CREATE INDEX idx_jobs_lot_id_phase ON jobs (lot_id, phase);

CREATE TABLE jobsubmits
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY, -- job log id
    job_id      BIGINT       NOT NULL,
    lot_id      BIGINT       NOT NULL,
    phase       SMALLINT     NOT NULL DEFAULT 0, -- phase before submission
    executor_uid VARCHAR(32)  NOT NULL, -- executor uid
    execteam_uid VARCHAR(32)  NOT NULL, -- executor orgnization uid
    last_executor_uid VARCHAR(32), -- last phase executor; valid when phase > 1
    last_execteam_uid VARCHAR(32), -- last phase executor team; valid when phase > 1
    action      VARCHAR(32)  NOT NULL,
    elems       INTEGER      NOT NULL DEFAULT 0,
    -- ins         INTEGER      NOT NULL DEFAULT 0,
    ins_cnt     INTEGER      NOT NULL DEFAULT 0, -- NOT include interpolated objects
    ins_total   INTEGER      NOT NULL DEFAULT 0, -- include interpolated objects
    ins_2d      INTEGER      NOT NULL DEFAULT 0, -- include interpolated objects
    ins_3d      INTEGER      NOT NULL DEFAULT 0, -- include interpolated objects
    ins_by_class JSONB       NOT NULL DEFAULT '{}', -- ins count by class
    rr_elems    INTEGER      NOT NULL DEFAULT 0, -- number of reject/resolve elements
    rr_ins      INTEGER      NOT NULL DEFAULT 0, -- number of reject/resolve ins
    rr_ins_2d   INTEGER      NOT NULL DEFAULT 0, -- number of reject/resolve 2d ins
    rr_ins_3d   INTEGER      NOT NULL DEFAULT 0, -- number of reject/resolve 3d ins
    rr_ins_by_class JSONB    NOT NULL DEFAULT '{}', -- ins count by class
    -- redo        SMALLINT     NOT NULL DEFAULT 0, -- number of re-submissions at the phase
    duration    INTEGER      NOT NULL DEFAULT 0, -- time cost in seconds
    created_at  TIMESTAMPTZ  NOT NULL, -- submit time
    claimed_at  TIMESTAMPTZ  NOT NULL -- claim time = submit time - duration
);
CREATE INDEX idx_jobsubmits_lot_id_phase ON jobsubmits (lot_id, phase);
CREATE INDEX idx_jobsubmits_created_at ON jobsubmits (created_at);

CREATE TABLE joblogs
(
    id          BIGINT       NOT NULL PRIMARY KEY,
    lot_id      BIGINT       NOT NULL,
    job_id      BIGINT       NOT NULL,
    operator_uid VARCHAR(32) NOT NULL,
    op_org_uid  VARCHAR(32)  NOT NULL, -- operator orgnization uid
    action      VARCHAR(32)  NOT NULL,
    from_phase  SMALLINT     NOT NULL DEFAULT 0,
    from_state  SMALLINT     NOT NULL DEFAULT 0,
    to_phase    SMALLINT     NOT NULL DEFAULT 0,
    to_state    SMALLINT     NOT NULL DEFAULT 0,
    to_executor_uid VARCHAR(32) NOT NULL DEFAULT '',
    to_execteam VARCHAR(32)  NOT NULL DEFAULT '',
    -- details     JSONB,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_joblogs_lot_id ON joblogs (lot_id);
CREATE INDEX idx_joblogs_job_id ON joblogs (job_id);
CREATE INDEX idx_joblogs_operator_uid ON joblogs (operator_uid);
CREATE INDEX idx_joblogs_created_at ON joblogs (created_at);

CREATE TABLE lots
(
    id          BIGINT       NOT NULL PRIMARY KEY,
    name        VARCHAR(128) NOT NULL,
    type        VARCHAR(128) NOT NULL,
    order_id    BIGINT       NOT NULL DEFAULT 0,
    data_uid    VARCHAR(32)  NOT NULL,
    data_type   VARCHAR(128) NOT NULL DEFAULT '',
    data_size   INTEGER      NOT NULL DEFAULT 0,
    priority    SMALLINT     NOT NULL DEFAULT 0, -- larger number indicates higher priority
    phase_count SMALLINT     NOT NULL,
    ins_cnt     INTEGER      NOT NULL DEFAULT 0,
    ins_total   INTEGER      NOT NULL DEFAULT 0, -- include interpolated objects
    state       VARCHAR(32)  NOT NULL,
    creator_uid VARCHAR(32)  NOT NULL,
    org_uid     VARCHAR(32)  NOT NULL,
    exp_end_time TIMESTAMPTZ,
    job_ready   BOOLEAN      NOT NULL DEFAULT false,
    job_count   INTEGER      NOT NULL DEFAULT 0,
    job_size    INTEGER      NOT NULL DEFAULT 0,
    is_frame_series BOOLEAN  NOT NULL DEFAULT false,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    finished_at TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_lots_name ON lots ((lower(name))) INCLUDE (state);
CREATE INDEX idx_lots_type ON lots (type) INCLUDE (state);
CREATE INDEX idx_lots_creator_uid ON lots (creator_uid) INCLUDE (state);
CREATE INDEX idx_lots_org_uid ON lots (org_uid) INCLUDE (state);
CREATE INDEX idx_lots_order_id ON lots (order_id) INCLUDE (state);
CREATE INDEX idx_lots_created_at ON lots (created_at) INCLUDE (state);

CREATE TABLE lotphases
(
    id           BIGSERIAL    NOT NULL PRIMARY KEY,
    lot_id       BIGINT       NOT NULL,
    number       SMALLINT     NOT NULL DEFAULT 0, -- phase number, starts from 1
    name         VARCHAR(64)  NOT NULL, -- label/review-1/review-2/...
    type         VARCHAR(32)  NOT NULL, -- label/review
    execteam     VARCHAR(32)  NOT NULL DEFAULT '', -- assigned team uid
    editable     BOOLEAN      NOT NULL DEFAULT false,
    sample_percent REAL       NOT NULL DEFAULT 0, -- percent
    min_skill_level SMALLINT  NOT NULL DEFAULT 0,
    timeout      INTEGER      NOT NULL DEFAULT 0, -- in seconds
    updated_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    created_at   TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_lotphases_lot_id_number_execteam ON lotphases (lot_id, number, execteam) INCLUDE (name, type, editable, sample_percent, min_skill_level, timeout);

CREATE TABLE orders
(
    id          BIGINT       NOT NULL PRIMARY KEY,
    name        VARCHAR(128) NOT NULL,
    size        INTEGER      NOT NULL DEFAULT 0,
    state       VARCHAR(32)  NOT NULL,
    ins_total   INTEGER      NOT NULL DEFAULT 0, -- include interpolated objects
    data_uid    VARCHAR(32)  NOT NULL DEFAULT '',
    org_uid     VARCHAR(32)  NOT NULL DEFAULT '',
    creator_uid VARCHAR(32)  NOT NULL DEFAULT '',
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    finished_at TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_orders_name ON orders ((lower(name))) INCLUDE (state);
CREATE INDEX idx_orders_creator ON orders (creator_uid) INCLUDE (state);
CREATE INDEX idx_orders_org_uid ON orders (org_uid) INCLUDE (state);
CREATE INDEX idx_orders_created_at ON orders (created_at) INCLUDE (state);

-- hourly/daily/weekly production
-- 分时统计中，如果 lot_id 为 0，表示该行为人员或团队统计；如果人员和团队为空，则表示为 lot 范围内统计；如果二者均不为空，则表示 lot 范围内的人员或团队统计
CREATE TABLE productions
(
    id            BIGSERIAL    NOT NULL PRIMARY KEY,
    lot_id        BIGINT,
    phase         SMALLINT, -- production of the lot in the phase; starts from 1, valid only for lot stat
    executor_uid  VARCHAR(32),
    execteam_uid  VARCHAR(32), -- executor orgnization uid
    ins           INTEGER      NOT NULL DEFAULT 0,
    ins_2d        INTEGER      NOT NULL DEFAULT 0,
    ins_3d        INTEGER      NOT NULL DEFAULT 0,
    ins_by_class  JSONB        NOT NULL DEFAULT '{}', -- ins count by class
    jobs          INTEGER      NOT NULL DEFAULT 0,
    elems         INTEGER      NOT NULL DEFAULT 0,
    period_start  TIMESTAMPTZ  NOT NULL,
    duration      INTEGER      NOT NULL, -- period duration in seconds
    work_duration INTEGER      NOT NULL, -- actual working duration within this period
    work_from     TIMESTAMPTZ  NOT NULL,
    work_to       TIMESTAMPTZ  NOT NULL,
    executor_cnt  INTEGER      NOT NULL,
    created_at    TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_productions_period_start_duration ON productions (period_start, duration);

-- daily/weekly accuracy
-- 分时统计中，如果 lot_id 为 0，表示该行为人员或团队统计；如果人员和团队为空，则表示为 lot 范围内统计；如果二者均不为空，则表示 lot 范围内的人员或团队统计
CREATE TABLE accuracies
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    lot_id      BIGINT,
    phase       SMALLINT, -- accuracy of the lot in the phase; starts from 1, valid only for lot stat
    executor_uid VARCHAR(32),
    execteam_uid VARCHAR(32), -- executor orgnization uid
    rate_elems  REAL         NOT NULL DEFAULT 0,
    rate_ins    REAL         NOT NULL DEFAULT 0,
    rate_ins_2d REAL         NOT NULL DEFAULT 0,
    rate_ins_3d REAL         NOT NULL DEFAULT 0,
    rate_by_class JSONB      NOT NULL DEFAULT '{}', -- accuracy by class
    period_start TIMESTAMPTZ NOT NULL,
    duration    INTEGER      NOT NULL, -- period duration in seconds
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_accuracies_period_start_duration ON accuracies (period_start, duration);

-- job label stat
CREATE TABLE jobstats
(
    id          BIGSERIAL    NOT NULL PRIMARY KEY,
    lot_id      BIGINT       NOT NULL,
    job_id      BIGINT       NOT NULL DEFAULT 0,
    cuboids     JSONB,
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_jobstats_lot_id ON jobstats (lot_id, job_id);
CREATE INDEX idx_jobstats_job_id ON jobstats (job_id);

COMMIT;
