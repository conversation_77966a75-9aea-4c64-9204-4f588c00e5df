package data

import (
	"annostat/internal/biz"
	"annostat/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"gitlab.rp.konvery.work/platform/pkg/data"
	"gitlab.rp.konvery.work/platform/pkg/ktypes"
	"gorm.io/datatypes"
)

// ProviderSet is data providers.
// var ProviderSet = wire.NewSet(NewData, NewGenericRepo[biz.Order], NewGenericRepo[biz.Job],
//	NewGenericRepo[biz.Lot], NewGenericRepo[biz.Lotphase])

var ProviderSet = wire.NewSet(NewData, NewOrdersRepo, NewLotsRepo, NewLotphasesRepo,
	NewJobsRepo, NewJoblogsRepo, NewJobsubmitsRepo, NewJobstatsRepo,
	NewProductionsRepo, NewAccuraciesRepo,
	NewBizgrantsRepo, NewSpecgrantsRepo,
)

func NewOrdersRepo(d *Data, logger log.Logger) biz.OrdersRepo {
	return data.NewGenericRepo[biz.Order](d, logger, biz.OrderTableName)
}
func NewJobsRepo(d *Data, logger log.Logger) biz.JobsRepo {
	return data.NewGenericRepo[biz.Job](d, logger, biz.JobTableName)
}

//	func NewJoblogsRepo(d *Data, logger log.Logger) biz.JoblogsRepo {
//		return NewGenericRepo[biz.Joblog](data, logger)
//	}
func NewJobsubmitsRepo(d *Data, logger log.Logger) biz.JobsubmitsRepo {
	return data.NewGenericRepo[biz.Jobsubmit](d, logger, biz.JobsubmitTableName)
}
func NewJobstatsRepo(d *Data, logger log.Logger) biz.JobstatsRepo {
	return data.NewGenericRepo[biz.Jobstat](d, logger, biz.JobstatTableName)
}
func NewLotsRepo(d *Data, logger log.Logger) biz.LotsRepo {
	return data.NewGenericRepo[biz.Lot](d, logger, biz.LotTableName)
}
func NewLotphasesRepo(d *Data, logger log.Logger) biz.LotphasesRepo {
	return data.NewGenericRepo[biz.Lotphase](d, logger, biz.LotphaseTableName)
}
func NewProductionsRepo(d *Data, logger log.Logger) biz.ProductionsRepo {
	return data.NewGenericRepo[biz.Production](d, logger, biz.ProdutionTableName)
}
func NewAccuraciesRepo(d *Data, logger log.Logger) biz.AccuraciesRepo {
	return data.NewGenericRepo[biz.Accuracy](d, logger, biz.AccuracyTableName)
}

func NewBizgrantsRepo(d *Data, logger log.Logger) biz.BizgrantsRepo {
	return data.NewGenericRepo[biz.Bizgrant](d, logger, biz.BizgrantTableName)
}

func NewSpecgrantsRepo(d *Data, logger log.Logger) biz.SpecgrantsRepo {
	return data.NewGenericRepo[biz.Specgrant](d, logger, biz.SpecgrantTableName)
}

type JSON = datatypes.JSON
type Data = data.Data

var Convert = data.Convert
var TypeName = data.TypeName

func NewData(c *conf.Data, logger log.Logger) (*Data, func(), error) {
	return data.NewData(NewDataCfg(c), logger)
}

func NewDataCfg(c *conf.Data) *data.Config {
	return &data.Config{Database: NewDBCfg(c.Database), Redis: NewRedisCfg(c.Redis)}
}

func NewDBCfg(c *conf.Data_Database) *data.DBCfg {
	if c == nil {
		return nil
	}
	cfg := &data.DBCfg{
		Driver:   c.Driver,
		Source:   c.Source,
		Endpoint: c.Endpoint,
		Port:     c.Port,
		Database: c.Database,
		Username: c.Username,
		Password: c.Password,
		Options:  c.Options,

		MaxOpenConns: int(c.MaxOpenConns),
		MaxIdleConns: int(c.MaxIdleConns),
	}
	if t := c.ConnMaxIdleTime; t != nil {
		cfg.ConnMaxIdleTime = ktypes.PointerOf(t.AsDuration())
	}
	return cfg
}

func NewRedisCfg(c *conf.Data_Redis) *data.RedisCfg {
	if c == nil {
		return nil
	}
	rdc := &data.RedisCfg{
		Network: c.Network,
		Addr:    c.Addr,
	}
	if d := c.ReadTimeout; d != nil {
		rdc.ReadTimeout = ktypes.PointerOf(d.AsDuration())
	}
	if d := c.WriteTimeout; d != nil {
		rdc.WriteTimeout = ktypes.PointerOf(d.AsDuration())
	}
	return rdc
}
