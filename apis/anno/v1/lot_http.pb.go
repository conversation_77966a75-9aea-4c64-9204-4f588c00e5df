// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.3
// source: anno/v1/lot.proto

package anno

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	types "gitlab.rp.konvery.work/platform/apis/types"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationLotsAddTag = "/anno.v1.Lots/AddTag"
const OperationLotsAllowDownloadAnnos = "/anno.v1.Lots/AllowDownloadAnnos"
const OperationLotsAssignExecteam = "/anno.v1.Lots/AssignExecteam"
const OperationLotsCancelLot = "/anno.v1.Lots/CancelLot"
const OperationLotsCloneLot = "/anno.v1.Lots/CloneLot"
const OperationLotsCreateLot = "/anno.v1.Lots/CreateLot"
const OperationLotsDeleteLot = "/anno.v1.Lots/DeleteLot"
const OperationLotsDeleteTag = "/anno.v1.Lots/DeleteTag"
const OperationLotsExportLotAnnos = "/anno.v1.Lots/ExportLotAnnos"
const OperationLotsGetLot = "/anno.v1.Lots/GetLot"
const OperationLotsGetSummary = "/anno.v1.Lots/GetSummary"
const OperationLotsJobCountByLots = "/anno.v1.Lots/JobCountByLots"
const OperationLotsListExecteams = "/anno.v1.Lots/ListExecteams"
const OperationLotsListExecutors = "/anno.v1.Lots/ListExecutors"
const OperationLotsListLot = "/anno.v1.Lots/ListLot"
const OperationLotsListLotsByExecutor = "/anno.v1.Lots/ListLotsByExecutor"
const OperationLotsManageExecutors = "/anno.v1.Lots/ManageExecutors"
const OperationLotsPauseLot = "/anno.v1.Lots/PauseLot"
const OperationLotsStartLot = "/anno.v1.Lots/StartLot"
const OperationLotsUpdateLot = "/anno.v1.Lots/UpdateLot"

type LotsHTTPServer interface {
	AddTag(context.Context, *types.TagRequest) (*types.TagList, error)
	// AllowDownloadAnnos allows demander to download annos
	AllowDownloadAnnos(context.Context, *AllowDownloadAnnosRequest) (*emptypb.Empty, error)
	// AssignExecteam assign execution team, update or delete an execution team
	AssignExecteam(context.Context, *AssignExecteamRequest) (*emptypb.Empty, error)
	CancelLot(context.Context, *GetLotRequest) (*emptypb.Empty, error)
	// CloneLot create a new lot by cloning an existing lot.
	CloneLot(context.Context, *CloneLotRequest) (*Lot, error)
	CreateLot(context.Context, *CreateLotRequest) (*Lot, error)
	DeleteLot(context.Context, *DeleteLotRequest) (*emptypb.Empty, error)
	DeleteTag(context.Context, *types.TagRequest) (*types.TagList, error)
	// ExportLotAnnos ExportLotAnnos exports lot annotations
	ExportLotAnnos(context.Context, *ExportLotAnnosRequest) (*emptypb.Empty, error)
	GetLot(context.Context, *GetLotRequest) (*Lot, error)
	// GetSummary Get summary of a lot
	GetSummary(context.Context, *GetLotRequest) (*GetLotSummaryReply, error)
	JobCountByLots(context.Context, *JobCountByLotidsRequest) (*JobCountByLotidsReply, error)
	// ListExecteams list assigned execution teams and executors
	ListExecteams(context.Context, *ListExecteamsRequest) (*ListExecteamsReply, error)
	// ListExecutors list executors at a phase
	ListExecutors(context.Context, *ListExecutorsRequest) (*ListExecutorsReply, error)
	ListLot(context.Context, *ListLotRequest) (*ListLotReply, error)
	// ListLotsByExecutor get lots assigned to a user or a team.
	// if the phase's execteam is empty, the phase is not assigned to the user or team.
	ListLotsByExecutor(context.Context, *ListLotsByExecutorRequest) (*ListLotsByExecutorReply, error)
	// ManageExecutors add or remove executors
	ManageExecutors(context.Context, *ManageExecutorsRequest) (*emptypb.Empty, error)
	PauseLot(context.Context, *GetLotRequest) (*emptypb.Empty, error)
	StartLot(context.Context, *GetLotRequest) (*emptypb.Empty, error)
	// UpdateLot update lot. only simple information like name or desc update are allowed.
	UpdateLot(context.Context, *UpdateLotRequest) (*Lot, error)
}

func RegisterLotsHTTPServer(s *http.Server, srv LotsHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/lots", _Lots_CreateLot0_HTTP_Handler(srv))
	r.POST("/v1/lots/{uid}/clone", _Lots_CloneLot0_HTTP_Handler(srv))
	r.PATCH("/v1/lots/{lot.uid}", _Lots_UpdateLot0_HTTP_Handler(srv))
	r.DELETE("/v1/lots/{uid}", _Lots_DeleteLot0_HTTP_Handler(srv))
	r.GET("/v1/lots/by-executor", _Lots_ListLotsByExecutor0_HTTP_Handler(srv))
	r.GET("/v1/lots/{uid}", _Lots_GetLot0_HTTP_Handler(srv))
	r.GET("/v1/lots", _Lots_ListLot0_HTTP_Handler(srv))
	r.PUT("/v1/lots/{uid}/start", _Lots_StartLot0_HTTP_Handler(srv))
	r.PUT("/v1/lots/{uid}/pause", _Lots_PauseLot0_HTTP_Handler(srv))
	r.PUT("/v1/lots/{uid}/cancel", _Lots_CancelLot0_HTTP_Handler(srv))
	r.GET("/v1/lots/{uid}/execteams", _Lots_ListExecteams0_HTTP_Handler(srv))
	r.PUT("/v1/lots/{uid}/execteam", _Lots_AssignExecteam0_HTTP_Handler(srv))
	r.POST("/v1/lots/{uid}/executors", _Lots_ManageExecutors0_HTTP_Handler(srv))
	r.GET("/v1/lots/{uid}/executors", _Lots_ListExecutors0_HTTP_Handler(srv))
	r.GET("/v1/lots/{uid}/summary", _Lots_GetSummary0_HTTP_Handler(srv))
	r.PUT("/v1/lots/{uid}/export-annos", _Lots_ExportLotAnnos0_HTTP_Handler(srv))
	r.PUT("/v1/lots/{uid}/allow-download-annos", _Lots_AllowDownloadAnnos0_HTTP_Handler(srv))
	r.PUT("/v1/lots/{uid}/tag", _Lots_AddTag0_HTTP_Handler(srv))
	r.DELETE("/v1/lots/{uid}/tag", _Lots_DeleteTag0_HTTP_Handler(srv))
	r.GET("/v1/job-count-by-lots", _Lots_JobCountByLots0_HTTP_Handler(srv))
}

func _Lots_CreateLot0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateLotRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsCreateLot)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateLot(ctx, req.(*CreateLotRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Lot)
		return ctx.Result(200, reply)
	}
}

func _Lots_CloneLot0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CloneLotRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsCloneLot)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CloneLot(ctx, req.(*CloneLotRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Lot)
		return ctx.Result(200, reply)
	}
}

func _Lots_UpdateLot0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateLotRequest
		if err := ctx.Bind(&in.Lot); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsUpdateLot)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateLot(ctx, req.(*UpdateLotRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Lot)
		return ctx.Result(200, reply)
	}
}

func _Lots_DeleteLot0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteLotRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsDeleteLot)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteLot(ctx, req.(*DeleteLotRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Lots_ListLotsByExecutor0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListLotsByExecutorRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsListLotsByExecutor)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListLotsByExecutor(ctx, req.(*ListLotsByExecutorRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListLotsByExecutorReply)
		return ctx.Result(200, reply)
	}
}

func _Lots_GetLot0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLotRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsGetLot)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLot(ctx, req.(*GetLotRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Lot)
		return ctx.Result(200, reply)
	}
}

func _Lots_ListLot0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListLotRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsListLot)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListLot(ctx, req.(*ListLotRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListLotReply)
		return ctx.Result(200, reply)
	}
}

func _Lots_StartLot0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLotRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsStartLot)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StartLot(ctx, req.(*GetLotRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Lots_PauseLot0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLotRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsPauseLot)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PauseLot(ctx, req.(*GetLotRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Lots_CancelLot0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLotRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsCancelLot)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CancelLot(ctx, req.(*GetLotRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Lots_ListExecteams0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListExecteamsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsListExecteams)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListExecteams(ctx, req.(*ListExecteamsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListExecteamsReply)
		return ctx.Result(200, reply)
	}
}

func _Lots_AssignExecteam0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AssignExecteamRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsAssignExecteam)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AssignExecteam(ctx, req.(*AssignExecteamRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Lots_ManageExecutors0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ManageExecutorsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsManageExecutors)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ManageExecutors(ctx, req.(*ManageExecutorsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Lots_ListExecutors0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListExecutorsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsListExecutors)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListExecutors(ctx, req.(*ListExecutorsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListExecutorsReply)
		return ctx.Result(200, reply)
	}
}

func _Lots_GetSummary0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLotRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsGetSummary)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSummary(ctx, req.(*GetLotRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetLotSummaryReply)
		return ctx.Result(200, reply)
	}
}

func _Lots_ExportLotAnnos0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExportLotAnnosRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsExportLotAnnos)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExportLotAnnos(ctx, req.(*ExportLotAnnosRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Lots_AllowDownloadAnnos0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AllowDownloadAnnosRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsAllowDownloadAnnos)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AllowDownloadAnnos(ctx, req.(*AllowDownloadAnnosRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Lots_AddTag0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in types.TagRequest
		if err := ctx.Bind(&in.Tags); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsAddTag)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddTag(ctx, req.(*types.TagRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*types.TagList)
		return ctx.Result(200, reply)
	}
}

func _Lots_DeleteTag0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in types.TagRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsDeleteTag)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteTag(ctx, req.(*types.TagRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*types.TagList)
		return ctx.Result(200, reply)
	}
}

func _Lots_JobCountByLots0_HTTP_Handler(srv LotsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in JobCountByLotidsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLotsJobCountByLots)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.JobCountByLots(ctx, req.(*JobCountByLotidsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*JobCountByLotidsReply)
		return ctx.Result(200, reply)
	}
}

type LotsHTTPClient interface {
	AddTag(ctx context.Context, req *types.TagRequest, opts ...http.CallOption) (rsp *types.TagList, err error)
	AllowDownloadAnnos(ctx context.Context, req *AllowDownloadAnnosRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	AssignExecteam(ctx context.Context, req *AssignExecteamRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CancelLot(ctx context.Context, req *GetLotRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	CloneLot(ctx context.Context, req *CloneLotRequest, opts ...http.CallOption) (rsp *Lot, err error)
	CreateLot(ctx context.Context, req *CreateLotRequest, opts ...http.CallOption) (rsp *Lot, err error)
	DeleteLot(ctx context.Context, req *DeleteLotRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteTag(ctx context.Context, req *types.TagRequest, opts ...http.CallOption) (rsp *types.TagList, err error)
	ExportLotAnnos(ctx context.Context, req *ExportLotAnnosRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetLot(ctx context.Context, req *GetLotRequest, opts ...http.CallOption) (rsp *Lot, err error)
	GetSummary(ctx context.Context, req *GetLotRequest, opts ...http.CallOption) (rsp *GetLotSummaryReply, err error)
	JobCountByLots(ctx context.Context, req *JobCountByLotidsRequest, opts ...http.CallOption) (rsp *JobCountByLotidsReply, err error)
	ListExecteams(ctx context.Context, req *ListExecteamsRequest, opts ...http.CallOption) (rsp *ListExecteamsReply, err error)
	ListExecutors(ctx context.Context, req *ListExecutorsRequest, opts ...http.CallOption) (rsp *ListExecutorsReply, err error)
	ListLot(ctx context.Context, req *ListLotRequest, opts ...http.CallOption) (rsp *ListLotReply, err error)
	ListLotsByExecutor(ctx context.Context, req *ListLotsByExecutorRequest, opts ...http.CallOption) (rsp *ListLotsByExecutorReply, err error)
	ManageExecutors(ctx context.Context, req *ManageExecutorsRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	PauseLot(ctx context.Context, req *GetLotRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	StartLot(ctx context.Context, req *GetLotRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateLot(ctx context.Context, req *UpdateLotRequest, opts ...http.CallOption) (rsp *Lot, err error)
}

type LotsHTTPClientImpl struct {
	cc *http.Client
}

func NewLotsHTTPClient(client *http.Client) LotsHTTPClient {
	return &LotsHTTPClientImpl{client}
}

func (c *LotsHTTPClientImpl) AddTag(ctx context.Context, in *types.TagRequest, opts ...http.CallOption) (*types.TagList, error) {
	var out types.TagList
	pattern := "/v1/lots/{uid}/tag"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLotsAddTag))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in.Tags, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) AllowDownloadAnnos(ctx context.Context, in *AllowDownloadAnnosRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/lots/{uid}/allow-download-annos"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLotsAllowDownloadAnnos))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) AssignExecteam(ctx context.Context, in *AssignExecteamRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/lots/{uid}/execteam"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLotsAssignExecteam))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) CancelLot(ctx context.Context, in *GetLotRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/lots/{uid}/cancel"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLotsCancelLot))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) CloneLot(ctx context.Context, in *CloneLotRequest, opts ...http.CallOption) (*Lot, error) {
	var out Lot
	pattern := "/v1/lots/{uid}/clone"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLotsCloneLot))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) CreateLot(ctx context.Context, in *CreateLotRequest, opts ...http.CallOption) (*Lot, error) {
	var out Lot
	pattern := "/v1/lots"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLotsCreateLot))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) DeleteLot(ctx context.Context, in *DeleteLotRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/lots/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLotsDeleteLot))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) DeleteTag(ctx context.Context, in *types.TagRequest, opts ...http.CallOption) (*types.TagList, error) {
	var out types.TagList
	pattern := "/v1/lots/{uid}/tag"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLotsDeleteTag))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) ExportLotAnnos(ctx context.Context, in *ExportLotAnnosRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/lots/{uid}/export-annos"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLotsExportLotAnnos))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) GetLot(ctx context.Context, in *GetLotRequest, opts ...http.CallOption) (*Lot, error) {
	var out Lot
	pattern := "/v1/lots/{uid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLotsGetLot))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) GetSummary(ctx context.Context, in *GetLotRequest, opts ...http.CallOption) (*GetLotSummaryReply, error) {
	var out GetLotSummaryReply
	pattern := "/v1/lots/{uid}/summary"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLotsGetSummary))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) JobCountByLots(ctx context.Context, in *JobCountByLotidsRequest, opts ...http.CallOption) (*JobCountByLotidsReply, error) {
	var out JobCountByLotidsReply
	pattern := "/v1/job-count-by-lots"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLotsJobCountByLots))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) ListExecteams(ctx context.Context, in *ListExecteamsRequest, opts ...http.CallOption) (*ListExecteamsReply, error) {
	var out ListExecteamsReply
	pattern := "/v1/lots/{uid}/execteams"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLotsListExecteams))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) ListExecutors(ctx context.Context, in *ListExecutorsRequest, opts ...http.CallOption) (*ListExecutorsReply, error) {
	var out ListExecutorsReply
	pattern := "/v1/lots/{uid}/executors"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLotsListExecutors))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) ListLot(ctx context.Context, in *ListLotRequest, opts ...http.CallOption) (*ListLotReply, error) {
	var out ListLotReply
	pattern := "/v1/lots"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLotsListLot))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) ListLotsByExecutor(ctx context.Context, in *ListLotsByExecutorRequest, opts ...http.CallOption) (*ListLotsByExecutorReply, error) {
	var out ListLotsByExecutorReply
	pattern := "/v1/lots/by-executor"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLotsListLotsByExecutor))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) ManageExecutors(ctx context.Context, in *ManageExecutorsRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/lots/{uid}/executors"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLotsManageExecutors))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) PauseLot(ctx context.Context, in *GetLotRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/lots/{uid}/pause"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLotsPauseLot))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) StartLot(ctx context.Context, in *GetLotRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/lots/{uid}/start"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLotsStartLot))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LotsHTTPClientImpl) UpdateLot(ctx context.Context, in *UpdateLotRequest, opts ...http.CallOption) (*Lot, error) {
	var out Lot
	pattern := "/v1/lots/{lot.uid}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLotsUpdateLot))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in.Lot, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
