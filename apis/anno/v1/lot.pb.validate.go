// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/lot.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateLotRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateLotRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLotRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLotRequestMultiError, or nil if none found.
func (m *CreateLotRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLotRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for Desc

	// no validation rules for ProjectUid

	if _, ok := Lot_Type_Enum_name[int32(m.GetType())]; !ok {
		err := CreateLotRequestValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Priority

	// no validation rules for Autostart

	// no validation rules for JobSize

	if all {
		switch v := interface{}(m.GetOntologies()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLotRequestValidationError{
					field:  "Ontologies",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLotRequestValidationError{
					field:  "Ontologies",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOntologies()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLotRequestValidationError{
				field:  "Ontologies",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPhases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateLotRequestValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateLotRequestValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateLotRequestValidationError{
					field:  fmt.Sprintf("Phases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Instruction

	// no validation rules for OrgUid

	// no validation rules for DataUid

	if all {
		switch v := interface{}(m.GetOut()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLotRequestValidationError{
					field:  "Out",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLotRequestValidationError{
					field:  "Out",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOut()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLotRequestValidationError{
				field:  "Out",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLotRequestValidationError{
					field:  "ExpEndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLotRequestValidationError{
					field:  "ExpEndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLotRequestValidationError{
				field:  "ExpEndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsFrameSeries

	// no validation rules for OrderUid

	if all {
		switch v := interface{}(m.GetToolCfg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLotRequestValidationError{
					field:  "ToolCfg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLotRequestValidationError{
					field:  "ToolCfg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToolCfg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLotRequestValidationError{
				field:  "ToolCfg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCommentReasons() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateLotRequestValidationError{
						field:  fmt.Sprintf("CommentReasons[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateLotRequestValidationError{
						field:  fmt.Sprintf("CommentReasons[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateLotRequestValidationError{
					field:  fmt.Sprintf("CommentReasons[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateLotRequestMultiError(errors)
	}

	return nil
}

// CreateLotRequestMultiError is an error wrapping multiple validation errors
// returned by CreateLotRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateLotRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLotRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLotRequestMultiError) AllErrors() []error { return m }

// CreateLotRequestValidationError is the validation error returned by
// CreateLotRequest.Validate if the designated constraints aren't met.
type CreateLotRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLotRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLotRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLotRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLotRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLotRequestValidationError) ErrorName() string { return "CreateLotRequestValidationError" }

// Error satisfies the builtin error interface
func (e CreateLotRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLotRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLotRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLotRequestValidationError{}

// Validate checks the field values on CloneLotRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CloneLotRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CloneLotRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CloneLotRequestMultiError, or nil if none found.
func (m *CloneLotRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CloneLotRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_CloneLotRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := CloneLotRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetUpdates()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CloneLotRequestValidationError{
					field:  "Updates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CloneLotRequestValidationError{
					field:  "Updates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdates()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CloneLotRequestValidationError{
				field:  "Updates",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CopyExecutors

	if len(errors) > 0 {
		return CloneLotRequestMultiError(errors)
	}

	return nil
}

// CloneLotRequestMultiError is an error wrapping multiple validation errors
// returned by CloneLotRequest.ValidateAll() if the designated constraints
// aren't met.
type CloneLotRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CloneLotRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CloneLotRequestMultiError) AllErrors() []error { return m }

// CloneLotRequestValidationError is the validation error returned by
// CloneLotRequest.Validate if the designated constraints aren't met.
type CloneLotRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CloneLotRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CloneLotRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CloneLotRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CloneLotRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CloneLotRequestValidationError) ErrorName() string { return "CloneLotRequestValidationError" }

// Error satisfies the builtin error interface
func (e CloneLotRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCloneLotRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CloneLotRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CloneLotRequestValidationError{}

var _CloneLotRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on UpdateLotRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateLotRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLotRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLotRequestMultiError, or nil if none found.
func (m *UpdateLotRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLotRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLot()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLotRequestValidationError{
					field:  "Lot",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLotRequestValidationError{
					field:  "Lot",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLot()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLotRequestValidationError{
				field:  "Lot",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateLotRequestMultiError(errors)
	}

	return nil
}

// UpdateLotRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateLotRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateLotRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLotRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLotRequestMultiError) AllErrors() []error { return m }

// UpdateLotRequestValidationError is the validation error returned by
// UpdateLotRequest.Validate if the designated constraints aren't met.
type UpdateLotRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLotRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLotRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLotRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLotRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLotRequestValidationError) ErrorName() string { return "UpdateLotRequestValidationError" }

// Error satisfies the builtin error interface
func (e UpdateLotRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLotRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLotRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLotRequestValidationError{}

// Validate checks the field values on DeleteLotRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteLotRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteLotRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteLotRequestMultiError, or nil if none found.
func (m *DeleteLotRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteLotRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_DeleteLotRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := DeleteLotRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteLotRequestMultiError(errors)
	}

	return nil
}

// DeleteLotRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteLotRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteLotRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteLotRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteLotRequestMultiError) AllErrors() []error { return m }

// DeleteLotRequestValidationError is the validation error returned by
// DeleteLotRequest.Validate if the designated constraints aren't met.
type DeleteLotRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteLotRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteLotRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteLotRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteLotRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteLotRequestValidationError) ErrorName() string { return "DeleteLotRequestValidationError" }

// Error satisfies the builtin error interface
func (e DeleteLotRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteLotRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteLotRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteLotRequestValidationError{}

var _DeleteLotRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on GetLotRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetLotRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLotRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetLotRequestMultiError, or
// nil if none found.
func (m *GetLotRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLotRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetLotRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := GetLotRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetLotRequestMultiError(errors)
	}

	return nil
}

// GetLotRequestMultiError is an error wrapping multiple validation errors
// returned by GetLotRequest.ValidateAll() if the designated constraints
// aren't met.
type GetLotRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLotRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLotRequestMultiError) AllErrors() []error { return m }

// GetLotRequestValidationError is the validation error returned by
// GetLotRequest.Validate if the designated constraints aren't met.
type GetLotRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLotRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLotRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLotRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLotRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLotRequestValidationError) ErrorName() string { return "GetLotRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetLotRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLotRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLotRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLotRequestValidationError{}

var _GetLotRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on ListLotRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListLotRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLotRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListLotRequestMultiError,
// or nil if none found.
func (m *ListLotRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLotRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListLotRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ListLotRequest_OrgUid_Pattern.MatchString(m.GetOrgUid()) {
		err := ListLotRequestValidationError{
			field:  "OrgUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|^\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ListLotRequest_CreatorUid_Pattern.MatchString(m.GetCreatorUid()) {
		err := ListLotRequestValidationError{
			field:  "CreatorUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|^\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for NamePattern

	// no validation rules for Type

	if !_ListLotRequest_OrderUid_Pattern.MatchString(m.GetOrderUid()) {
		err := ListLotRequestValidationError{
			field:  "OrderUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|^\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for WithOrg

	if len(errors) > 0 {
		return ListLotRequestMultiError(errors)
	}

	return nil
}

// ListLotRequestMultiError is an error wrapping multiple validation errors
// returned by ListLotRequest.ValidateAll() if the designated constraints
// aren't met.
type ListLotRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLotRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLotRequestMultiError) AllErrors() []error { return m }

// ListLotRequestValidationError is the validation error returned by
// ListLotRequest.Validate if the designated constraints aren't met.
type ListLotRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLotRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLotRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLotRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLotRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLotRequestValidationError) ErrorName() string { return "ListLotRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListLotRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLotRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLotRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLotRequestValidationError{}

var _ListLotRequest_OrgUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|^")

var _ListLotRequest_CreatorUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|^")

var _ListLotRequest_OrderUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|^")

// Validate checks the field values on ListLotReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListLotReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLotReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListLotReplyMultiError, or
// nil if none found.
func (m *ListLotReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLotReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetLots() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListLotReplyValidationError{
						field:  fmt.Sprintf("Lots[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListLotReplyValidationError{
						field:  fmt.Sprintf("Lots[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListLotReplyValidationError{
					field:  fmt.Sprintf("Lots[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetOrgs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListLotReplyValidationError{
						field:  fmt.Sprintf("Orgs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListLotReplyValidationError{
						field:  fmt.Sprintf("Orgs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListLotReplyValidationError{
					field:  fmt.Sprintf("Orgs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListLotReplyMultiError(errors)
	}

	return nil
}

// ListLotReplyMultiError is an error wrapping multiple validation errors
// returned by ListLotReply.ValidateAll() if the designated constraints aren't met.
type ListLotReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLotReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLotReplyMultiError) AllErrors() []error { return m }

// ListLotReplyValidationError is the validation error returned by
// ListLotReply.Validate if the designated constraints aren't met.
type ListLotReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLotReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLotReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLotReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLotReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLotReplyValidationError) ErrorName() string { return "ListLotReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListLotReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLotReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLotReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLotReplyValidationError{}

// Validate checks the field values on ListLotsByExecutorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListLotsByExecutorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLotsByExecutorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListLotsByExecutorRequestMultiError, or nil if none found.
func (m *ListLotsByExecutorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLotsByExecutorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListLotsByExecutorRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ListLotsByExecutorRequest_UserUid_Pattern.MatchString(m.GetUserUid()) {
		err := ListLotsByExecutorRequestValidationError{
			field:  "UserUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|^\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ListLotsByExecutorRequest_TeamUid_Pattern.MatchString(m.GetTeamUid()) {
		err := ListLotsByExecutorRequestValidationError{
			field:  "TeamUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|^\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ListLotsByExecutorRequest_OrgUid_Pattern.MatchString(m.GetOrgUid()) {
		err := ListLotsByExecutorRequestValidationError{
			field:  "OrgUid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$|^\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Type

	// no validation rules for NamePattern

	// no validation rules for Claimable

	if len(errors) > 0 {
		return ListLotsByExecutorRequestMultiError(errors)
	}

	return nil
}

// ListLotsByExecutorRequestMultiError is an error wrapping multiple validation
// errors returned by ListLotsByExecutorRequest.ValidateAll() if the
// designated constraints aren't met.
type ListLotsByExecutorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLotsByExecutorRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLotsByExecutorRequestMultiError) AllErrors() []error { return m }

// ListLotsByExecutorRequestValidationError is the validation error returned by
// ListLotsByExecutorRequest.Validate if the designated constraints aren't met.
type ListLotsByExecutorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLotsByExecutorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLotsByExecutorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLotsByExecutorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLotsByExecutorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLotsByExecutorRequestValidationError) ErrorName() string {
	return "ListLotsByExecutorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListLotsByExecutorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLotsByExecutorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLotsByExecutorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLotsByExecutorRequestValidationError{}

var _ListLotsByExecutorRequest_UserUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|^")

var _ListLotsByExecutorRequest_TeamUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|^")

var _ListLotsByExecutorRequest_OrgUid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$|^")

// Validate checks the field values on ListLotsByExecutorReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListLotsByExecutorReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLotsByExecutorReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListLotsByExecutorReplyMultiError, or nil if none found.
func (m *ListLotsByExecutorReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLotsByExecutorReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetLots() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListLotsByExecutorReplyValidationError{
						field:  fmt.Sprintf("Lots[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListLotsByExecutorReplyValidationError{
						field:  fmt.Sprintf("Lots[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListLotsByExecutorReplyValidationError{
					field:  fmt.Sprintf("Lots[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	{
		sorted_keys := make([]string, len(m.GetExtras()))
		i := 0
		for key := range m.GetExtras() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetExtras()[key]
			_ = val

			// no validation rules for Extras[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ListLotsByExecutorReplyValidationError{
							field:  fmt.Sprintf("Extras[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ListLotsByExecutorReplyValidationError{
							field:  fmt.Sprintf("Extras[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ListLotsByExecutorReplyValidationError{
						field:  fmt.Sprintf("Extras[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return ListLotsByExecutorReplyMultiError(errors)
	}

	return nil
}

// ListLotsByExecutorReplyMultiError is an error wrapping multiple validation
// errors returned by ListLotsByExecutorReply.ValidateAll() if the designated
// constraints aren't met.
type ListLotsByExecutorReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLotsByExecutorReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLotsByExecutorReplyMultiError) AllErrors() []error { return m }

// ListLotsByExecutorReplyValidationError is the validation error returned by
// ListLotsByExecutorReply.Validate if the designated constraints aren't met.
type ListLotsByExecutorReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLotsByExecutorReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLotsByExecutorReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLotsByExecutorReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLotsByExecutorReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLotsByExecutorReplyValidationError) ErrorName() string {
	return "ListLotsByExecutorReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListLotsByExecutorReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLotsByExecutorReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLotsByExecutorReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLotsByExecutorReplyValidationError{}

// Validate checks the field values on ListExecteamsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListExecteamsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListExecteamsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListExecteamsRequestMultiError, or nil if none found.
func (m *ListExecteamsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListExecteamsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ListExecteamsRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := ListExecteamsRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for WithExecteams

	// no validation rules for WithExecutors

	if len(errors) > 0 {
		return ListExecteamsRequestMultiError(errors)
	}

	return nil
}

// ListExecteamsRequestMultiError is an error wrapping multiple validation
// errors returned by ListExecteamsRequest.ValidateAll() if the designated
// constraints aren't met.
type ListExecteamsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListExecteamsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListExecteamsRequestMultiError) AllErrors() []error { return m }

// ListExecteamsRequestValidationError is the validation error returned by
// ListExecteamsRequest.Validate if the designated constraints aren't met.
type ListExecteamsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListExecteamsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListExecteamsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListExecteamsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListExecteamsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListExecteamsRequestValidationError) ErrorName() string {
	return "ListExecteamsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListExecteamsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListExecteamsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListExecteamsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListExecteamsRequestValidationError{}

var _ListExecteamsRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on ListExecteamsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListExecteamsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListExecteamsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListExecteamsReplyMultiError, or nil if none found.
func (m *ListExecteamsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListExecteamsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPhases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListExecteamsReplyValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListExecteamsReplyValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListExecteamsReplyValidationError{
					field:  fmt.Sprintf("Phases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListExecteamsReplyMultiError(errors)
	}

	return nil
}

// ListExecteamsReplyMultiError is an error wrapping multiple validation errors
// returned by ListExecteamsReply.ValidateAll() if the designated constraints
// aren't met.
type ListExecteamsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListExecteamsReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListExecteamsReplyMultiError) AllErrors() []error { return m }

// ListExecteamsReplyValidationError is the validation error returned by
// ListExecteamsReply.Validate if the designated constraints aren't met.
type ListExecteamsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListExecteamsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListExecteamsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListExecteamsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListExecteamsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListExecteamsReplyValidationError) ErrorName() string {
	return "ListExecteamsReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListExecteamsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListExecteamsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListExecteamsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListExecteamsReplyValidationError{}

// Validate checks the field values on AssignExecteamRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AssignExecteamRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssignExecteamRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AssignExecteamRequestMultiError, or nil if none found.
func (m *AssignExecteamRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AssignExecteamRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_AssignExecteamRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := AssignExecteamRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetPhases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AssignExecteamRequestValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AssignExecteamRequestValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AssignExecteamRequestValidationError{
					field:  fmt.Sprintf("Phases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AssignExecteamRequestMultiError(errors)
	}

	return nil
}

// AssignExecteamRequestMultiError is an error wrapping multiple validation
// errors returned by AssignExecteamRequest.ValidateAll() if the designated
// constraints aren't met.
type AssignExecteamRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssignExecteamRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssignExecteamRequestMultiError) AllErrors() []error { return m }

// AssignExecteamRequestValidationError is the validation error returned by
// AssignExecteamRequest.Validate if the designated constraints aren't met.
type AssignExecteamRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssignExecteamRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssignExecteamRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssignExecteamRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssignExecteamRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssignExecteamRequestValidationError) ErrorName() string {
	return "AssignExecteamRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AssignExecteamRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssignExecteamRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssignExecteamRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssignExecteamRequestValidationError{}

var _AssignExecteamRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on ManageExecutorsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ManageExecutorsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ManageExecutorsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ManageExecutorsRequestMultiError, or nil if none found.
func (m *ManageExecutorsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ManageExecutorsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ManageExecutorsRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := ManageExecutorsRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetPhases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ManageExecutorsRequestValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ManageExecutorsRequestValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ManageExecutorsRequestValidationError{
					field:  fmt.Sprintf("Phases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ManageExecutorsRequestMultiError(errors)
	}

	return nil
}

// ManageExecutorsRequestMultiError is an error wrapping multiple validation
// errors returned by ManageExecutorsRequest.ValidateAll() if the designated
// constraints aren't met.
type ManageExecutorsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ManageExecutorsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ManageExecutorsRequestMultiError) AllErrors() []error { return m }

// ManageExecutorsRequestValidationError is the validation error returned by
// ManageExecutorsRequest.Validate if the designated constraints aren't met.
type ManageExecutorsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ManageExecutorsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ManageExecutorsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ManageExecutorsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ManageExecutorsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ManageExecutorsRequestValidationError) ErrorName() string {
	return "ManageExecutorsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ManageExecutorsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sManageExecutorsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ManageExecutorsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ManageExecutorsRequestValidationError{}

var _ManageExecutorsRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on ListExecutorsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListExecutorsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListExecutorsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListExecutorsRequestMultiError, or nil if none found.
func (m *ListExecutorsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListExecutorsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if m.GetPhase() < 1 {
		err := ListExecutorsRequestValidationError{
			field:  "Phase",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Subtype

	// no validation rules for TeamUid

	// no validation rules for Page

	if val := m.GetPagesz(); val < 0 || val > 500 {
		err := ListExecutorsRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 500]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ListExecutorsRequestMultiError(errors)
	}

	return nil
}

// ListExecutorsRequestMultiError is an error wrapping multiple validation
// errors returned by ListExecutorsRequest.ValidateAll() if the designated
// constraints aren't met.
type ListExecutorsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListExecutorsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListExecutorsRequestMultiError) AllErrors() []error { return m }

// ListExecutorsRequestValidationError is the validation error returned by
// ListExecutorsRequest.Validate if the designated constraints aren't met.
type ListExecutorsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListExecutorsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListExecutorsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListExecutorsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListExecutorsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListExecutorsRequestValidationError) ErrorName() string {
	return "ListExecutorsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListExecutorsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListExecutorsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListExecutorsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListExecutorsRequestValidationError{}

// Validate checks the field values on ListExecutorsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListExecutorsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListExecutorsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListExecutorsReplyMultiError, or nil if none found.
func (m *ListExecutorsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListExecutorsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListExecutorsReplyValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListExecutorsReplyValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListExecutorsReplyValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListExecutorsReplyMultiError(errors)
	}

	return nil
}

// ListExecutorsReplyMultiError is an error wrapping multiple validation errors
// returned by ListExecutorsReply.ValidateAll() if the designated constraints
// aren't met.
type ListExecutorsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListExecutorsReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListExecutorsReplyMultiError) AllErrors() []error { return m }

// ListExecutorsReplyValidationError is the validation error returned by
// ListExecutorsReply.Validate if the designated constraints aren't met.
type ListExecutorsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListExecutorsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListExecutorsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListExecutorsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListExecutorsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListExecutorsReplyValidationError) ErrorName() string {
	return "ListExecutorsReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListExecutorsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListExecutorsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListExecutorsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListExecutorsReplyValidationError{}

// Validate checks the field values on Lot with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Lot) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lot with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in LotMultiError, or nil if none found.
func (m *Lot) ValidateAll() error {
	return m.validate(true)
}

func (m *Lot) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for Desc

	// no validation rules for State

	// no validation rules for Type

	// no validation rules for DataType

	// no validation rules for IsFrameSeries

	// no validation rules for Priority

	// no validation rules for Autostart

	// no validation rules for JobSize

	if all {
		switch v := interface{}(m.GetOntologies()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LotValidationError{
					field:  "Ontologies",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LotValidationError{
					field:  "Ontologies",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOntologies()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LotValidationError{
				field:  "Ontologies",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPhases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LotValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LotValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LotValidationError{
					field:  fmt.Sprintf("Phases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Instruction

	// no validation rules for CreatorUid

	// no validation rules for DataSize

	if all {
		switch v := interface{}(m.GetExpEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LotValidationError{
					field:  "ExpEndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LotValidationError{
					field:  "ExpEndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LotValidationError{
				field:  "ExpEndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LotValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LotValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LotValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LotValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LotValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LotValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetError()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LotValidationError{
					field:  "Error",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LotValidationError{
					field:  "Error",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetError()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LotValidationError{
				field:  "Error",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrgUid

	// no validation rules for DataUid

	if all {
		switch v := interface{}(m.GetOut()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LotValidationError{
					field:  "Out",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LotValidationError{
					field:  "Out",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOut()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LotValidationError{
				field:  "Out",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for JobCount

	// no validation rules for JobReady

	// no validation rules for OrderUid

	// no validation rules for InsCnt

	// no validation rules for InsTotal

	if all {
		switch v := interface{}(m.GetToolCfg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LotValidationError{
					field:  "ToolCfg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LotValidationError{
					field:  "ToolCfg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToolCfg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LotValidationError{
				field:  "ToolCfg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AnnoResultUrl

	// no validation rules for CanExportAnnos

	for idx, item := range m.GetCommentReasons() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LotValidationError{
						field:  fmt.Sprintf("CommentReasons[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LotValidationError{
						field:  fmt.Sprintf("CommentReasons[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LotValidationError{
					field:  fmt.Sprintf("CommentReasons[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return LotMultiError(errors)
	}

	return nil
}

// LotMultiError is an error wrapping multiple validation errors returned by
// Lot.ValidateAll() if the designated constraints aren't met.
type LotMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LotMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LotMultiError) AllErrors() []error { return m }

// LotValidationError is the validation error returned by Lot.Validate if the
// designated constraints aren't met.
type LotValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LotValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LotValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LotValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LotValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LotValidationError) ErrorName() string { return "LotValidationError" }

// Error satisfies the builtin error interface
func (e LotValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLot.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LotValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LotValidationError{}

// Validate checks the field values on GetLotSummaryReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLotSummaryReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLotSummaryReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLotSummaryReplyMultiError, or nil if none found.
func (m *GetLotSummaryReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLotSummaryReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalJobs

	if len(errors) > 0 {
		return GetLotSummaryReplyMultiError(errors)
	}

	return nil
}

// GetLotSummaryReplyMultiError is an error wrapping multiple validation errors
// returned by GetLotSummaryReply.ValidateAll() if the designated constraints
// aren't met.
type GetLotSummaryReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLotSummaryReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLotSummaryReplyMultiError) AllErrors() []error { return m }

// GetLotSummaryReplyValidationError is the validation error returned by
// GetLotSummaryReply.Validate if the designated constraints aren't met.
type GetLotSummaryReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLotSummaryReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLotSummaryReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLotSummaryReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLotSummaryReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLotSummaryReplyValidationError) ErrorName() string {
	return "GetLotSummaryReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetLotSummaryReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLotSummaryReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLotSummaryReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLotSummaryReplyValidationError{}

// Validate checks the field values on GetLotAnnosReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetLotAnnosReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLotAnnosReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLotAnnosReplyMultiError, or nil if none found.
func (m *GetLotAnnosReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLotAnnosReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAnnotations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLotAnnosReplyValidationError{
						field:  fmt.Sprintf("Annotations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLotAnnosReplyValidationError{
						field:  fmt.Sprintf("Annotations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLotAnnosReplyValidationError{
					field:  fmt.Sprintf("Annotations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for InsCnt

	if len(errors) > 0 {
		return GetLotAnnosReplyMultiError(errors)
	}

	return nil
}

// GetLotAnnosReplyMultiError is an error wrapping multiple validation errors
// returned by GetLotAnnosReply.ValidateAll() if the designated constraints
// aren't met.
type GetLotAnnosReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLotAnnosReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLotAnnosReplyMultiError) AllErrors() []error { return m }

// GetLotAnnosReplyValidationError is the validation error returned by
// GetLotAnnosReply.Validate if the designated constraints aren't met.
type GetLotAnnosReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLotAnnosReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLotAnnosReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLotAnnosReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLotAnnosReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLotAnnosReplyValidationError) ErrorName() string { return "GetLotAnnosReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetLotAnnosReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLotAnnosReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLotAnnosReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLotAnnosReplyValidationError{}

// Validate checks the field values on GetVisibleLotsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetVisibleLotsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVisibleLotsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetVisibleLotsRequestMultiError, or nil if none found.
func (m *GetVisibleLotsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVisibleLotsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ExecutorUid

	for idx, item := range m.GetStates() {
		_, _ = idx, item

		if _, ok := Lot_State_Enum_name[int32(item)]; !ok {
			err := GetVisibleLotsRequestValidationError{
				field:  fmt.Sprintf("States[%v]", idx),
				reason: "value must be one of the defined enum values",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for CountOnly

	// no validation rules for Claimable

	if len(errors) > 0 {
		return GetVisibleLotsRequestMultiError(errors)
	}

	return nil
}

// GetVisibleLotsRequestMultiError is an error wrapping multiple validation
// errors returned by GetVisibleLotsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetVisibleLotsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVisibleLotsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVisibleLotsRequestMultiError) AllErrors() []error { return m }

// GetVisibleLotsRequestValidationError is the validation error returned by
// GetVisibleLotsRequest.Validate if the designated constraints aren't met.
type GetVisibleLotsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVisibleLotsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVisibleLotsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVisibleLotsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVisibleLotsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVisibleLotsRequestValidationError) ErrorName() string {
	return "GetVisibleLotsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetVisibleLotsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVisibleLotsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVisibleLotsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVisibleLotsRequestValidationError{}

// Validate checks the field values on GetVisibleLotsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetVisibleLotsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVisibleLotsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetVisibleLotsReplyMultiError, or nil if none found.
func (m *GetVisibleLotsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVisibleLotsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetLotUids() {
		_, _ = idx, item

		if !_GetVisibleLotsReply_LotUids_Pattern.MatchString(item) {
			err := GetVisibleLotsReplyValidationError{
				field:  fmt.Sprintf("LotUids[%v]", idx),
				reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for LotCount

	if len(errors) > 0 {
		return GetVisibleLotsReplyMultiError(errors)
	}

	return nil
}

// GetVisibleLotsReplyMultiError is an error wrapping multiple validation
// errors returned by GetVisibleLotsReply.ValidateAll() if the designated
// constraints aren't met.
type GetVisibleLotsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVisibleLotsReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVisibleLotsReplyMultiError) AllErrors() []error { return m }

// GetVisibleLotsReplyValidationError is the validation error returned by
// GetVisibleLotsReply.Validate if the designated constraints aren't met.
type GetVisibleLotsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVisibleLotsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVisibleLotsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVisibleLotsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVisibleLotsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVisibleLotsReplyValidationError) ErrorName() string {
	return "GetVisibleLotsReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetVisibleLotsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVisibleLotsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVisibleLotsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVisibleLotsReplyValidationError{}

var _GetVisibleLotsReply_LotUids_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on ExportLotAnnosRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExportLotAnnosRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExportLotAnnosRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExportLotAnnosRequestMultiError, or nil if none found.
func (m *ExportLotAnnosRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ExportLotAnnosRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ExportLotAnnosRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := ExportLotAnnosRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := ExportOrderAnnosRequest_Option_Enum_name[int32(m.GetOption())]; !ok {
		err := ExportLotAnnosRequestValidationError{
			field:  "Option",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ExportLotAnnosRequestMultiError(errors)
	}

	return nil
}

// ExportLotAnnosRequestMultiError is an error wrapping multiple validation
// errors returned by ExportLotAnnosRequest.ValidateAll() if the designated
// constraints aren't met.
type ExportLotAnnosRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExportLotAnnosRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExportLotAnnosRequestMultiError) AllErrors() []error { return m }

// ExportLotAnnosRequestValidationError is the validation error returned by
// ExportLotAnnosRequest.Validate if the designated constraints aren't met.
type ExportLotAnnosRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExportLotAnnosRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExportLotAnnosRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExportLotAnnosRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExportLotAnnosRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExportLotAnnosRequestValidationError) ErrorName() string {
	return "ExportLotAnnosRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ExportLotAnnosRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExportLotAnnosRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExportLotAnnosRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExportLotAnnosRequestValidationError{}

var _ExportLotAnnosRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on SetLotAnnoResultRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetLotAnnoResultRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetLotAnnoResultRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetLotAnnoResultRequestMultiError, or nil if none found.
func (m *SetLotAnnoResultRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetLotAnnoResultRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_SetLotAnnoResultRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := SetLotAnnoResultRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, err := url.Parse(m.GetUrl()); err != nil {
		err = SetLotAnnoResultRequestValidationError{
			field:  "Url",
			reason: "value must be a valid URI",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SetLotAnnoResultRequestMultiError(errors)
	}

	return nil
}

// SetLotAnnoResultRequestMultiError is an error wrapping multiple validation
// errors returned by SetLotAnnoResultRequest.ValidateAll() if the designated
// constraints aren't met.
type SetLotAnnoResultRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetLotAnnoResultRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetLotAnnoResultRequestMultiError) AllErrors() []error { return m }

// SetLotAnnoResultRequestValidationError is the validation error returned by
// SetLotAnnoResultRequest.Validate if the designated constraints aren't met.
type SetLotAnnoResultRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetLotAnnoResultRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetLotAnnoResultRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetLotAnnoResultRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetLotAnnoResultRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetLotAnnoResultRequestValidationError) ErrorName() string {
	return "SetLotAnnoResultRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SetLotAnnoResultRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetLotAnnoResultRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetLotAnnoResultRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetLotAnnoResultRequestValidationError{}

var _SetLotAnnoResultRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on AllowDownloadAnnosRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AllowDownloadAnnosRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AllowDownloadAnnosRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AllowDownloadAnnosRequestMultiError, or nil if none found.
func (m *AllowDownloadAnnosRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AllowDownloadAnnosRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_AllowDownloadAnnosRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := AllowDownloadAnnosRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Allow

	if len(errors) > 0 {
		return AllowDownloadAnnosRequestMultiError(errors)
	}

	return nil
}

// AllowDownloadAnnosRequestMultiError is an error wrapping multiple validation
// errors returned by AllowDownloadAnnosRequest.ValidateAll() if the
// designated constraints aren't met.
type AllowDownloadAnnosRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AllowDownloadAnnosRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AllowDownloadAnnosRequestMultiError) AllErrors() []error { return m }

// AllowDownloadAnnosRequestValidationError is the validation error returned by
// AllowDownloadAnnosRequest.Validate if the designated constraints aren't met.
type AllowDownloadAnnosRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AllowDownloadAnnosRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AllowDownloadAnnosRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AllowDownloadAnnosRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AllowDownloadAnnosRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AllowDownloadAnnosRequestValidationError) ErrorName() string {
	return "AllowDownloadAnnosRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AllowDownloadAnnosRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAllowDownloadAnnosRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AllowDownloadAnnosRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AllowDownloadAnnosRequestValidationError{}

var _AllowDownloadAnnosRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on JobCountByLotidsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *JobCountByLotidsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JobCountByLotidsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// JobCountByLotidsRequestMultiError, or nil if none found.
func (m *JobCountByLotidsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *JobCountByLotidsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return JobCountByLotidsRequestMultiError(errors)
	}

	return nil
}

// JobCountByLotidsRequestMultiError is an error wrapping multiple validation
// errors returned by JobCountByLotidsRequest.ValidateAll() if the designated
// constraints aren't met.
type JobCountByLotidsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JobCountByLotidsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JobCountByLotidsRequestMultiError) AllErrors() []error { return m }

// JobCountByLotidsRequestValidationError is the validation error returned by
// JobCountByLotidsRequest.Validate if the designated constraints aren't met.
type JobCountByLotidsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JobCountByLotidsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JobCountByLotidsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JobCountByLotidsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JobCountByLotidsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JobCountByLotidsRequestValidationError) ErrorName() string {
	return "JobCountByLotidsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e JobCountByLotidsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJobCountByLotidsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JobCountByLotidsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JobCountByLotidsRequestValidationError{}

// Validate checks the field values on JobCountByLotidsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *JobCountByLotidsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JobCountByLotidsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// JobCountByLotidsReplyMultiError, or nil if none found.
func (m *JobCountByLotidsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *JobCountByLotidsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetLots() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, JobCountByLotidsReplyValidationError{
						field:  fmt.Sprintf("Lots[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, JobCountByLotidsReplyValidationError{
						field:  fmt.Sprintf("Lots[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return JobCountByLotidsReplyValidationError{
					field:  fmt.Sprintf("Lots[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return JobCountByLotidsReplyMultiError(errors)
	}

	return nil
}

// JobCountByLotidsReplyMultiError is an error wrapping multiple validation
// errors returned by JobCountByLotidsReply.ValidateAll() if the designated
// constraints aren't met.
type JobCountByLotidsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JobCountByLotidsReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JobCountByLotidsReplyMultiError) AllErrors() []error { return m }

// JobCountByLotidsReplyValidationError is the validation error returned by
// JobCountByLotidsReply.Validate if the designated constraints aren't met.
type JobCountByLotidsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JobCountByLotidsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JobCountByLotidsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JobCountByLotidsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JobCountByLotidsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JobCountByLotidsReplyValidationError) ErrorName() string {
	return "JobCountByLotidsReplyValidationError"
}

// Error satisfies the builtin error interface
func (e JobCountByLotidsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJobCountByLotidsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JobCountByLotidsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JobCountByLotidsReplyValidationError{}

// Validate checks the field values on ListLotsByExecutorReply_Extra with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListLotsByExecutorReply_Extra) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLotsByExecutorReply_Extra with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListLotsByExecutorReply_ExtraMultiError, or nil if none found.
func (m *ListLotsByExecutorReply_Extra) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLotsByExecutorReply_Extra) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HasRejectedJobs

	if len(errors) > 0 {
		return ListLotsByExecutorReply_ExtraMultiError(errors)
	}

	return nil
}

// ListLotsByExecutorReply_ExtraMultiError is an error wrapping multiple
// validation errors returned by ListLotsByExecutorReply_Extra.ValidateAll()
// if the designated constraints aren't met.
type ListLotsByExecutorReply_ExtraMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLotsByExecutorReply_ExtraMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLotsByExecutorReply_ExtraMultiError) AllErrors() []error { return m }

// ListLotsByExecutorReply_ExtraValidationError is the validation error
// returned by ListLotsByExecutorReply_Extra.Validate if the designated
// constraints aren't met.
type ListLotsByExecutorReply_ExtraValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLotsByExecutorReply_ExtraValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLotsByExecutorReply_ExtraValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLotsByExecutorReply_ExtraValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLotsByExecutorReply_ExtraValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLotsByExecutorReply_ExtraValidationError) ErrorName() string {
	return "ListLotsByExecutorReply_ExtraValidationError"
}

// Error satisfies the builtin error interface
func (e ListLotsByExecutorReply_ExtraValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLotsByExecutorReply_Extra.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLotsByExecutorReply_ExtraValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLotsByExecutorReply_ExtraValidationError{}

// Validate checks the field values on ListExecteamsReply_Execteam with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListExecteamsReply_Execteam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListExecteamsReply_Execteam with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListExecteamsReply_ExecteamMultiError, or nil if none found.
func (m *ListExecteamsReply_Execteam) ValidateAll() error {
	return m.validate(true)
}

func (m *ListExecteamsReply_Execteam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTeam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListExecteamsReply_ExecteamValidationError{
					field:  "Team",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListExecteamsReply_ExecteamValidationError{
					field:  "Team",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTeam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListExecteamsReply_ExecteamValidationError{
				field:  "Team",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetExecutors() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListExecteamsReply_ExecteamValidationError{
						field:  fmt.Sprintf("Executors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListExecteamsReply_ExecteamValidationError{
						field:  fmt.Sprintf("Executors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListExecteamsReply_ExecteamValidationError{
					field:  fmt.Sprintf("Executors[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetQuota()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListExecteamsReply_ExecteamValidationError{
					field:  "Quota",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListExecteamsReply_ExecteamValidationError{
					field:  "Quota",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuota()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListExecteamsReply_ExecteamValidationError{
				field:  "Quota",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListExecteamsReply_ExecteamMultiError(errors)
	}

	return nil
}

// ListExecteamsReply_ExecteamMultiError is an error wrapping multiple
// validation errors returned by ListExecteamsReply_Execteam.ValidateAll() if
// the designated constraints aren't met.
type ListExecteamsReply_ExecteamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListExecteamsReply_ExecteamMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListExecteamsReply_ExecteamMultiError) AllErrors() []error { return m }

// ListExecteamsReply_ExecteamValidationError is the validation error returned
// by ListExecteamsReply_Execteam.Validate if the designated constraints
// aren't met.
type ListExecteamsReply_ExecteamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListExecteamsReply_ExecteamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListExecteamsReply_ExecteamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListExecteamsReply_ExecteamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListExecteamsReply_ExecteamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListExecteamsReply_ExecteamValidationError) ErrorName() string {
	return "ListExecteamsReply_ExecteamValidationError"
}

// Error satisfies the builtin error interface
func (e ListExecteamsReply_ExecteamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListExecteamsReply_Execteam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListExecteamsReply_ExecteamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListExecteamsReply_ExecteamValidationError{}

// Validate checks the field values on ListExecteamsReply_Phase with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListExecteamsReply_Phase) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListExecteamsReply_Phase with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListExecteamsReply_PhaseMultiError, or nil if none found.
func (m *ListExecteamsReply_Phase) ValidateAll() error {
	return m.validate(true)
}

func (m *ListExecteamsReply_Phase) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTeams() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListExecteamsReply_PhaseValidationError{
						field:  fmt.Sprintf("Teams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListExecteamsReply_PhaseValidationError{
						field:  fmt.Sprintf("Teams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListExecteamsReply_PhaseValidationError{
					field:  fmt.Sprintf("Teams[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListExecteamsReply_PhaseMultiError(errors)
	}

	return nil
}

// ListExecteamsReply_PhaseMultiError is an error wrapping multiple validation
// errors returned by ListExecteamsReply_Phase.ValidateAll() if the designated
// constraints aren't met.
type ListExecteamsReply_PhaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListExecteamsReply_PhaseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListExecteamsReply_PhaseMultiError) AllErrors() []error { return m }

// ListExecteamsReply_PhaseValidationError is the validation error returned by
// ListExecteamsReply_Phase.Validate if the designated constraints aren't met.
type ListExecteamsReply_PhaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListExecteamsReply_PhaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListExecteamsReply_PhaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListExecteamsReply_PhaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListExecteamsReply_PhaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListExecteamsReply_PhaseValidationError) ErrorName() string {
	return "ListExecteamsReply_PhaseValidationError"
}

// Error satisfies the builtin error interface
func (e ListExecteamsReply_PhaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListExecteamsReply_Phase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListExecteamsReply_PhaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListExecteamsReply_PhaseValidationError{}

// Validate checks the field values on AssignExecteamRequest_Phase with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AssignExecteamRequest_Phase) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssignExecteamRequest_Phase with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AssignExecteamRequest_PhaseMultiError, or nil if none found.
func (m *AssignExecteamRequest_Phase) ValidateAll() error {
	return m.validate(true)
}

func (m *AssignExecteamRequest_Phase) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Phase

	for idx, item := range m.GetExecteams() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AssignExecteamRequest_PhaseValidationError{
						field:  fmt.Sprintf("Execteams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AssignExecteamRequest_PhaseValidationError{
						field:  fmt.Sprintf("Execteams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AssignExecteamRequest_PhaseValidationError{
					field:  fmt.Sprintf("Execteams[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AssignExecteamRequest_PhaseMultiError(errors)
	}

	return nil
}

// AssignExecteamRequest_PhaseMultiError is an error wrapping multiple
// validation errors returned by AssignExecteamRequest_Phase.ValidateAll() if
// the designated constraints aren't met.
type AssignExecteamRequest_PhaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssignExecteamRequest_PhaseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssignExecteamRequest_PhaseMultiError) AllErrors() []error { return m }

// AssignExecteamRequest_PhaseValidationError is the validation error returned
// by AssignExecteamRequest_Phase.Validate if the designated constraints
// aren't met.
type AssignExecteamRequest_PhaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssignExecteamRequest_PhaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssignExecteamRequest_PhaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssignExecteamRequest_PhaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssignExecteamRequest_PhaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssignExecteamRequest_PhaseValidationError) ErrorName() string {
	return "AssignExecteamRequest_PhaseValidationError"
}

// Error satisfies the builtin error interface
func (e AssignExecteamRequest_PhaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssignExecteamRequest_Phase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssignExecteamRequest_PhaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssignExecteamRequest_PhaseValidationError{}

// Validate checks the field values on ManageExecutorsRequest_Phase with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ManageExecutorsRequest_Phase) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ManageExecutorsRequest_Phase with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ManageExecutorsRequest_PhaseMultiError, or nil if none found.
func (m *ManageExecutorsRequest_Phase) ValidateAll() error {
	return m.validate(true)
}

func (m *ManageExecutorsRequest_Phase) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Phase

	// no validation rules for TeamUid

	if len(errors) > 0 {
		return ManageExecutorsRequest_PhaseMultiError(errors)
	}

	return nil
}

// ManageExecutorsRequest_PhaseMultiError is an error wrapping multiple
// validation errors returned by ManageExecutorsRequest_Phase.ValidateAll() if
// the designated constraints aren't met.
type ManageExecutorsRequest_PhaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ManageExecutorsRequest_PhaseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ManageExecutorsRequest_PhaseMultiError) AllErrors() []error { return m }

// ManageExecutorsRequest_PhaseValidationError is the validation error returned
// by ManageExecutorsRequest_Phase.Validate if the designated constraints
// aren't met.
type ManageExecutorsRequest_PhaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ManageExecutorsRequest_PhaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ManageExecutorsRequest_PhaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ManageExecutorsRequest_PhaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ManageExecutorsRequest_PhaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ManageExecutorsRequest_PhaseValidationError) ErrorName() string {
	return "ManageExecutorsRequest_PhaseValidationError"
}

// Error satisfies the builtin error interface
func (e ManageExecutorsRequest_PhaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sManageExecutorsRequest_Phase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ManageExecutorsRequest_PhaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ManageExecutorsRequest_PhaseValidationError{}

// Validate checks the field values on Lot_Type with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Lot_Type) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lot_Type with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Lot_TypeMultiError, or nil
// if none found.
func (m *Lot_Type) ValidateAll() error {
	return m.validate(true)
}

func (m *Lot_Type) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Lot_TypeMultiError(errors)
	}

	return nil
}

// Lot_TypeMultiError is an error wrapping multiple validation errors returned
// by Lot_Type.ValidateAll() if the designated constraints aren't met.
type Lot_TypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Lot_TypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Lot_TypeMultiError) AllErrors() []error { return m }

// Lot_TypeValidationError is the validation error returned by
// Lot_Type.Validate if the designated constraints aren't met.
type Lot_TypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Lot_TypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Lot_TypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Lot_TypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Lot_TypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Lot_TypeValidationError) ErrorName() string { return "Lot_TypeValidationError" }

// Error satisfies the builtin error interface
func (e Lot_TypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLot_Type.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Lot_TypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Lot_TypeValidationError{}

// Validate checks the field values on Lot_State with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Lot_State) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lot_State with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Lot_StateMultiError, or nil
// if none found.
func (m *Lot_State) ValidateAll() error {
	return m.validate(true)
}

func (m *Lot_State) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Lot_StateMultiError(errors)
	}

	return nil
}

// Lot_StateMultiError is an error wrapping multiple validation errors returned
// by Lot_State.ValidateAll() if the designated constraints aren't met.
type Lot_StateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Lot_StateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Lot_StateMultiError) AllErrors() []error { return m }

// Lot_StateValidationError is the validation error returned by
// Lot_State.Validate if the designated constraints aren't met.
type Lot_StateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Lot_StateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Lot_StateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Lot_StateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Lot_StateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Lot_StateValidationError) ErrorName() string { return "Lot_StateValidationError" }

// Error satisfies the builtin error interface
func (e Lot_StateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLot_State.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Lot_StateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Lot_StateValidationError{}

// Validate checks the field values on Lot_Range with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Lot_Range) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lot_Range with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Lot_RangeMultiError, or nil
// if none found.
func (m *Lot_Range) ValidateAll() error {
	return m.validate(true)
}

func (m *Lot_Range) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Shape

	if all {
		switch v := interface{}(m.GetZrange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Lot_RangeValidationError{
					field:  "Zrange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Lot_RangeValidationError{
					field:  "Zrange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetZrange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Lot_RangeValidationError{
				field:  "Zrange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return Lot_RangeMultiError(errors)
	}

	return nil
}

// Lot_RangeMultiError is an error wrapping multiple validation errors returned
// by Lot_Range.ValidateAll() if the designated constraints aren't met.
type Lot_RangeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Lot_RangeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Lot_RangeMultiError) AllErrors() []error { return m }

// Lot_RangeValidationError is the validation error returned by
// Lot_Range.Validate if the designated constraints aren't met.
type Lot_RangeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Lot_RangeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Lot_RangeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Lot_RangeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Lot_RangeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Lot_RangeValidationError) ErrorName() string { return "Lot_RangeValidationError" }

// Error satisfies the builtin error interface
func (e Lot_RangeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLot_Range.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Lot_RangeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Lot_RangeValidationError{}

// Validate checks the field values on Lot_ToolConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Lot_ToolConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lot_ToolConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Lot_ToolConfigMultiError,
// or nil if none found.
func (m *Lot_ToolConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *Lot_ToolConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRanges() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, Lot_ToolConfigValidationError{
						field:  fmt.Sprintf("Ranges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, Lot_ToolConfigValidationError{
						field:  fmt.Sprintf("Ranges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return Lot_ToolConfigValidationError{
					field:  fmt.Sprintf("Ranges[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ProjectedEditable

	// no validation rules for RedoProjection

	for idx, item := range m.GetRulers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, Lot_ToolConfigValidationError{
						field:  fmt.Sprintf("Rulers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, Lot_ToolConfigValidationError{
						field:  fmt.Sprintf("Rulers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return Lot_ToolConfigValidationError{
					field:  fmt.Sprintf("Rulers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for SaveProjected

	// no validation rules for Segmentation_3DEnabled

	for idx, item := range m.GetPreBox() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, Lot_ToolConfigValidationError{
						field:  fmt.Sprintf("PreBox[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, Lot_ToolConfigValidationError{
						field:  fmt.Sprintf("PreBox[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return Lot_ToolConfigValidationError{
					field:  fmt.Sprintf("PreBox[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return Lot_ToolConfigMultiError(errors)
	}

	return nil
}

// Lot_ToolConfigMultiError is an error wrapping multiple validation errors
// returned by Lot_ToolConfig.ValidateAll() if the designated constraints
// aren't met.
type Lot_ToolConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Lot_ToolConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Lot_ToolConfigMultiError) AllErrors() []error { return m }

// Lot_ToolConfigValidationError is the validation error returned by
// Lot_ToolConfig.Validate if the designated constraints aren't met.
type Lot_ToolConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Lot_ToolConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Lot_ToolConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Lot_ToolConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Lot_ToolConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Lot_ToolConfigValidationError) ErrorName() string { return "Lot_ToolConfigValidationError" }

// Error satisfies the builtin error interface
func (e Lot_ToolConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLot_ToolConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Lot_ToolConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Lot_ToolConfigValidationError{}

// Validate checks the field values on Lot_Range_Shape with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *Lot_Range_Shape) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lot_Range_Shape with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Lot_Range_ShapeMultiError, or nil if none found.
func (m *Lot_Range_Shape) ValidateAll() error {
	return m.validate(true)
}

func (m *Lot_Range_Shape) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return Lot_Range_ShapeMultiError(errors)
	}

	return nil
}

// Lot_Range_ShapeMultiError is an error wrapping multiple validation errors
// returned by Lot_Range_Shape.ValidateAll() if the designated constraints
// aren't met.
type Lot_Range_ShapeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Lot_Range_ShapeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Lot_Range_ShapeMultiError) AllErrors() []error { return m }

// Lot_Range_ShapeValidationError is the validation error returned by
// Lot_Range_Shape.Validate if the designated constraints aren't met.
type Lot_Range_ShapeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Lot_Range_ShapeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Lot_Range_ShapeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Lot_Range_ShapeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Lot_Range_ShapeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Lot_Range_ShapeValidationError) ErrorName() string { return "Lot_Range_ShapeValidationError" }

// Error satisfies the builtin error interface
func (e Lot_Range_ShapeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLot_Range_Shape.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Lot_Range_ShapeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Lot_Range_ShapeValidationError{}

// Validate checks the field values on Lot_ToolConfig_Ruler with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Lot_ToolConfig_Ruler) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lot_ToolConfig_Ruler with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Lot_ToolConfig_RulerMultiError, or nil if none found.
func (m *Lot_ToolConfig_Ruler) ValidateAll() error {
	return m.validate(true)
}

func (m *Lot_ToolConfig_Ruler) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Type

	if len(errors) > 0 {
		return Lot_ToolConfig_RulerMultiError(errors)
	}

	return nil
}

// Lot_ToolConfig_RulerMultiError is an error wrapping multiple validation
// errors returned by Lot_ToolConfig_Ruler.ValidateAll() if the designated
// constraints aren't met.
type Lot_ToolConfig_RulerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Lot_ToolConfig_RulerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Lot_ToolConfig_RulerMultiError) AllErrors() []error { return m }

// Lot_ToolConfig_RulerValidationError is the validation error returned by
// Lot_ToolConfig_Ruler.Validate if the designated constraints aren't met.
type Lot_ToolConfig_RulerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Lot_ToolConfig_RulerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Lot_ToolConfig_RulerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Lot_ToolConfig_RulerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Lot_ToolConfig_RulerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Lot_ToolConfig_RulerValidationError) ErrorName() string {
	return "Lot_ToolConfig_RulerValidationError"
}

// Error satisfies the builtin error interface
func (e Lot_ToolConfig_RulerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLot_ToolConfig_Ruler.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Lot_ToolConfig_RulerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Lot_ToolConfig_RulerValidationError{}

// Validate checks the field values on Lot_ToolConfig_PreBox with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Lot_ToolConfig_PreBox) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lot_ToolConfig_PreBox with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Lot_ToolConfig_PreBoxMultiError, or nil if none found.
func (m *Lot_ToolConfig_PreBox) ValidateAll() error {
	return m.validate(true)
}

func (m *Lot_ToolConfig_PreBox) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Length

	// no validation rules for Width

	// no validation rules for Height

	if len(errors) > 0 {
		return Lot_ToolConfig_PreBoxMultiError(errors)
	}

	return nil
}

// Lot_ToolConfig_PreBoxMultiError is an error wrapping multiple validation
// errors returned by Lot_ToolConfig_PreBox.ValidateAll() if the designated
// constraints aren't met.
type Lot_ToolConfig_PreBoxMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Lot_ToolConfig_PreBoxMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Lot_ToolConfig_PreBoxMultiError) AllErrors() []error { return m }

// Lot_ToolConfig_PreBoxValidationError is the validation error returned by
// Lot_ToolConfig_PreBox.Validate if the designated constraints aren't met.
type Lot_ToolConfig_PreBoxValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Lot_ToolConfig_PreBoxValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Lot_ToolConfig_PreBoxValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Lot_ToolConfig_PreBoxValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Lot_ToolConfig_PreBoxValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Lot_ToolConfig_PreBoxValidationError) ErrorName() string {
	return "Lot_ToolConfig_PreBoxValidationError"
}

// Error satisfies the builtin error interface
func (e Lot_ToolConfig_PreBoxValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLot_ToolConfig_PreBox.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Lot_ToolConfig_PreBoxValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Lot_ToolConfig_PreBoxValidationError{}

// Validate checks the field values on JobCountByLotidsReply_PhaseCount with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *JobCountByLotidsReply_PhaseCount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JobCountByLotidsReply_PhaseCount with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// JobCountByLotidsReply_PhaseCountMultiError, or nil if none found.
func (m *JobCountByLotidsReply_PhaseCount) ValidateAll() error {
	return m.validate(true)
}

func (m *JobCountByLotidsReply_PhaseCount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Phase

	// no validation rules for Count

	if len(errors) > 0 {
		return JobCountByLotidsReply_PhaseCountMultiError(errors)
	}

	return nil
}

// JobCountByLotidsReply_PhaseCountMultiError is an error wrapping multiple
// validation errors returned by
// JobCountByLotidsReply_PhaseCount.ValidateAll() if the designated
// constraints aren't met.
type JobCountByLotidsReply_PhaseCountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JobCountByLotidsReply_PhaseCountMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JobCountByLotidsReply_PhaseCountMultiError) AllErrors() []error { return m }

// JobCountByLotidsReply_PhaseCountValidationError is the validation error
// returned by JobCountByLotidsReply_PhaseCount.Validate if the designated
// constraints aren't met.
type JobCountByLotidsReply_PhaseCountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JobCountByLotidsReply_PhaseCountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JobCountByLotidsReply_PhaseCountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JobCountByLotidsReply_PhaseCountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JobCountByLotidsReply_PhaseCountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JobCountByLotidsReply_PhaseCountValidationError) ErrorName() string {
	return "JobCountByLotidsReply_PhaseCountValidationError"
}

// Error satisfies the builtin error interface
func (e JobCountByLotidsReply_PhaseCountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJobCountByLotidsReply_PhaseCount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JobCountByLotidsReply_PhaseCountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JobCountByLotidsReply_PhaseCountValidationError{}

// Validate checks the field values on JobCountByLotidsReply_LotInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *JobCountByLotidsReply_LotInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JobCountByLotidsReply_LotInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// JobCountByLotidsReply_LotInfoMultiError, or nil if none found.
func (m *JobCountByLotidsReply_LotInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *JobCountByLotidsReply_LotInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LotId

	for idx, item := range m.GetCount() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, JobCountByLotidsReply_LotInfoValidationError{
						field:  fmt.Sprintf("Count[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, JobCountByLotidsReply_LotInfoValidationError{
						field:  fmt.Sprintf("Count[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return JobCountByLotidsReply_LotInfoValidationError{
					field:  fmt.Sprintf("Count[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return JobCountByLotidsReply_LotInfoMultiError(errors)
	}

	return nil
}

// JobCountByLotidsReply_LotInfoMultiError is an error wrapping multiple
// validation errors returned by JobCountByLotidsReply_LotInfo.ValidateAll()
// if the designated constraints aren't met.
type JobCountByLotidsReply_LotInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JobCountByLotidsReply_LotInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JobCountByLotidsReply_LotInfoMultiError) AllErrors() []error { return m }

// JobCountByLotidsReply_LotInfoValidationError is the validation error
// returned by JobCountByLotidsReply_LotInfo.Validate if the designated
// constraints aren't met.
type JobCountByLotidsReply_LotInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JobCountByLotidsReply_LotInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JobCountByLotidsReply_LotInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JobCountByLotidsReply_LotInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JobCountByLotidsReply_LotInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JobCountByLotidsReply_LotInfoValidationError) ErrorName() string {
	return "JobCountByLotidsReply_LotInfoValidationError"
}

// Error satisfies the builtin error interface
func (e JobCountByLotidsReply_LotInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJobCountByLotidsReply_LotInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JobCountByLotidsReply_LotInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JobCountByLotidsReply_LotInfoValidationError{}
