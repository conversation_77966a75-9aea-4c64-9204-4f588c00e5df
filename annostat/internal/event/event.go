package event

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"annostat/api/client"
	"annostat/internal/biz"
	"annostat/internal/mq"
	"annostat/workflow/common"
	"annostat/workflow/jobstat"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/google/wire"
	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
)

var ProviderSet = wire.NewSet(NewEventsBiz)

const (
	EvtTypeAnnoLot       = "AnnoLot"
	EvtTypeAnnoBizgrant  = "AnnoBizgrant"
	EvtTypeAnnoJobsubmit = "AnnoJobsubmit"
	EvtTypeAnnoJoblog    = "AnnoJoblog"
	EvtTypeAnnoOrder     = "AnnoOrder"
	EvtTypeAnnoSpecgrant = "AnnoSpecgrant"
	EvtTypeAnnoLotphase  = "AnnoLotphase"
)

const (
	EvtSubtypeCreate      = "create"
	EvtSubtypeBatchCreate = "batchCreate"
	EvtSubtypeStart       = "start"
	EvtSubtypeStop        = "stop"
	EvtSubtypeDelete      = "delete"
	EvtSubtypeUpdate      = "update"
)

type EventsBiz struct {
	bizgrantRepo  biz.BizgrantsRepo
	jobrepo       biz.JobsRepo
	joblogrepo    biz.JoblogsRepo
	jobsubmitrepo biz.JobsubmitsRepo
	lotrepo       biz.LotsRepo
	lotphaserepo  biz.LotphasesRepo
	orderrepo     biz.OrdersRepo
	specgrantRepo biz.SpecgrantsRepo
	bgtask        biz.BackgroundTask
	log           *log.Helper
}

func NewEventsBiz(
	bizgrantRepo biz.BizgrantsRepo,
	jobrepo biz.JobsRepo,
	joblogrepo biz.JoblogsRepo,
	jobsubmitrepo biz.JobsubmitsRepo,
	lotrepo biz.LotsRepo,
	lotphaserepo biz.LotphasesRepo,
	orderrepo biz.OrdersRepo,
	specgrantRepo biz.SpecgrantsRepo,
	bgtask biz.BackgroundTask, logger log.Logger) *EventsBiz {

	o := &EventsBiz{
		bizgrantRepo:  bizgrantRepo,
		jobrepo:       jobrepo,
		joblogrepo:    joblogrepo,
		jobsubmitrepo: jobsubmitrepo,
		lotrepo:       lotrepo,
		lotphaserepo:  lotphaserepo,
		orderrepo:     orderrepo,
		specgrantRepo: specgrantRepo,
		bgtask:        bgtask,
		log:           log.NewHelper(logger),
	}
	o.init()
	return o
}

func (o *EventsBiz) init() {
	mq.RegEventHandler(EvtTypeAnnoJoblog, EvtSubtypeCreate, o.createJoblog)
	// mq.RegEventHandler(EvtTypeAnnoJoblog, EvtSubtypeBatchCreate, o.createJoblogs)
	mq.RegEventHandler(EvtTypeAnnoJobsubmit, EvtSubtypeCreate, o.createJobsubmit)
	mq.RegEventHandler(EvtTypeAnnoLot, EvtSubtypeCreate, o.createLot)
	mq.RegEventHandler(EvtTypeAnnoLot, EvtSubtypeStart, o.startLot)
	mq.RegEventHandler(EvtTypeAnnoLot, EvtSubtypeStop, o.finishLot)
	mq.RegEventHandler(EvtTypeAnnoLot, EvtSubtypeUpdate, o.updateLot)
	mq.RegEventHandler(EvtTypeAnnoLotphase, EvtSubtypeBatchCreate, o.bacthCreateLotphases)
	mq.RegEventHandler(EvtTypeAnnoLotphase, EvtSubtypeDelete, o.deleteLotphases)
	mq.RegEventHandler(EvtTypeAnnoOrder, EvtSubtypeCreate, o.createOrder)
	mq.RegEventHandler(EvtTypeAnnoBizgrant, EvtSubtypeCreate, o.createBizgrant)
	mq.RegEventHandler(EvtTypeAnnoBizgrant, EvtSubtypeDelete, o.deleteBizgrant)
	mq.RegEventHandler(EvtTypeAnnoSpecgrant, EvtSubtypeCreate, o.createSpecgrant)
	mq.RegEventHandler(EvtTypeAnnoSpecgrant, EvtSubtypeDelete, o.deleteSpecgrant)
}

func (o *EventsBiz) startLot(ctx context.Context, ev *mq.Event) error {
	body, _ := ev.Body.(string)
	data := &struct {
		Uid      string
		JobCount int    `json:"job_count"`
		DataType string `json:"data_type"`
		DataSize int32  `json:"data_size"`
	}{}
	if err := json.Unmarshal([]byte(body), data); err != nil {
		return fmt.Errorf("failed to unmarshal event body: %w", err)
	}
	lotID := kid.ParseID(data.Uid)
	lot := &biz.Lot{ID: lotID, State: biz.LotStateOngoing, JobReady: true, JobCount: data.JobCount, DataType: data.DataType,
		DataSize: int64(data.DataSize)}
	_, err := o.lotrepo.Update(ctx, lot, field.NewMask(biz.LotSfldState, biz.LotSfldJobReady, biz.LotSfldJobCount, biz.LotSfldDataType,
		biz.LotSfldDataSize))
	if err != nil {
		return fmt.Errorf("failed to udpate lot state: %w", err)
	}

	for page := 0; ; page++ {
		reply, err := client.ListJob(client.NewCtxUseSvcAccount(ctx), &anno.ListJobRequest{
			Page:   int32(page),
			Pagesz: 100,
			Filter: &anno.ListJobFilter{LotUid: data.Uid},
		})
		if err != nil {
			return fmt.Errorf("failed to list jobs: %w", err)
		}
		if len(reply.Jobs) == 0 {
			break
		}
		mods := lo.Map(reply.Jobs, func(v *client.Job, _ int) *biz.Job {
			return &biz.Job{
				ID:        kid.ParseID(v.Uid),
				LotID:     kid.ParseID(v.LotUid),
				IdxInLot:  v.IdxInLot,
				Elems:     v.ElemsCnt,
				Phase:     v.Phase,
				CreatedAt: v.CreatedAt.AsTime(),
			}
		})
		_, err = o.jobrepo.BatchCreate(ctx, mods)
		if err != nil {
			if errors.IsConflict(err) {
				continue
			}
			return fmt.Errorf("failed to create Jobs: %w", err)
		}
	}
	return nil
}

func (o *EventsBiz) finishLot(ctx context.Context, ev *mq.Event) error {
	body, _ := ev.Body.(string)
	data := &struct {
		Uid      string
		State    biz.LotState
		InsCnt   int64
		InsTotal int64
	}{}
	if err := json.Unmarshal([]byte(body), data); err != nil {
		return fmt.Errorf("failed to unmarshal event body: %w", err)
	}
	lotID := kid.ParseID(data.Uid)
	lot, err := o.lotrepo.Update(ctx,
		&biz.Lot{ID: lotID, State: data.State, InsCnt: data.InsCnt, InsTotal: data.InsTotal, FinishedAt: time.Now()},
		field.NewMask(biz.LotSfldState, biz.LotSfldInsCnt, biz.LotSfldInsTotal, biz.LotSfldFinishedAt))
	if err != nil {
		return fmt.Errorf("failed to udpate lot state: %w", err)
	}
	if lot.OrderID > 0 {
		_, err = o.orderrepo.Update(ctx, &biz.Order{ID: lot.OrderID, State: biz.OrderState(data.State)}, field.NewMask(biz.OrderSfldState))
		if err != nil {
			return fmt.Errorf("failed to udpate order state: %w", err)
		}
	}

	filter := &biz.JobListFilter{LotIDs: []int64{lotID}}
	for page := 0; ; page++ {
		jobs, _, err := o.jobrepo.List(ctx, filter, biz.Pager{Page: page, Pagesz: 100})
		if err != nil {
			return fmt.Errorf("failed to udpate Jobs: %w", err)
		}
		if len(jobs) == 0 {
			break
		}
		for _, job := range jobs {
			job.Finished = true
			job.Duration = int32(job.LastSubmit.Sub(job.FirstClaim) / time.Second)
			_, err = o.jobrepo.Update(ctx, job, field.NewMask(biz.JobSfldFinished, biz.JobSfldDuration))
			if err != nil {
				return fmt.Errorf("failed to udpate Job: %w", err)
			}
		}
	}
	return nil
}

func (o *EventsBiz) updateLot(ctx context.Context, ev *mq.Event) error {
	body, _ := ev.Body.(string)
	data := &struct {
		Lot    *biz.Lot
		Fields []string
	}{}
	if err := json.Unmarshal([]byte(body), data); err != nil {
		return fmt.Errorf("failed to unmarshal event body: %w", err)
	}
	_, err := o.lotrepo.Update(ctx, &biz.Lot{ID: data.Lot.ID, State: data.Lot.State}, field.NewMask(data.Fields...))
	if err != nil {
		return fmt.Errorf("failed to udpate lot state: %w", err)
	}
	return nil
}

func (o *EventsBiz) createJoblog(ctx context.Context, ev *mq.Event) error {
	body, _ := ev.Body.(string)
	data := &biz.Joblog{}
	if err := json.Unmarshal([]byte(body), data); err != nil {
		return fmt.Errorf("failed to unmarshal event body: %w", err)
	}
	job, err := o.jobrepo.GetByID(ctx, data.JobID)
	if err != nil { // && !errors.IsNotFound(err) {
		return fmt.Errorf("failed to get Job: %w", err)
	}

	return o.jobsubmitrepo.DoTx(ctx, func(ctx context.Context, tx biz.Tx) error {
		data.LotID = job.LotID
		_, err := o.joblogrepo.Create(ctx, data)
		if err != nil {
			if errors.IsConflict(err) {
				return nil
			}
			return fmt.Errorf("failed to create Joblog: %w", err)
		}
		flds := []string{}
		if job.FirstClaim.IsZero() && data.Action == biz.JobActionClaim {
			job.FirstClaim = data.CreatedAt
			flds = append(flds, biz.JobSfldFirstClaim)
		}
		// FIXME: what if logs don't arrive in their creation order?
		if job.Phase != data.ToPhase {
			job.Phase = data.ToPhase
			flds = append(flds, biz.JobSfldPhase)
		}
		if len(flds) == 0 {
			return nil
		}
		_, err = o.jobrepo.Update(ctx, job, field.NewMask(flds...))
		if err != nil {
			return fmt.Errorf("failed to update Job: %w", err)
		}
		return nil
	})
}

// func (o *EventsBiz) createJoblogs(ctx context.Context, ev *mq.Event) error {
// 	body, _ := ev.Body.(string)
// 	jlogs := []*Joblog{}
// 	if err := json.Unmarshal([]byte(body), &jlogs); err != nil {
// 		return fmt.Errorf("failed to unmarshal event body: %w", err)
// 	}
// 	if len(jlogs) == 0 {
// 		return nil
// 	}

// 	ids := lo.Uniq(lo.Map(jlogs, func(v *Joblog, _ int) int64 { return v.JobID }))
// 	jobs, err := o.jobrepo.List(ctx, &JobListFilter{IDs: ids}, Pager{Pagesz: len(ids)})
// 	if err != nil {
// 		return fmt.Errorf("failed to get Job: %w", err)
// 	}
// 	if len(jobs) < len(ids) {
// 		return fmt.Errorf("some jobs are not found")
// 	}
// 	jobMap := lo.SliceToMap(jobs, func(v *Job) (int64, *Job) { return v.ID, v })
// 	return o.jobsubmitrepo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
// 		_, err := o.joblogrepo.BatchCreate(ctx, jlogs)
// 		if err != nil {
// 			if errors.IsConflict(err) {
// 				return nil
// 			}
// 			return fmt.Errorf("failed to create Joblog: %w", err)
// 		}
// 		for _, jlog := range jlogs {
// 			flds := []string{}
// 			job := jobMap[jlog.JobID]
// 			if job.FirstClaim.IsZero() && jlog.Action == JobActionClaim {
// 				job.FirstClaim = jlog.CreatedAt
// 				flds = append(flds, biz.JobSfldPhase)
// 			}
// 			if job.Phase != jlog.ToPhase {
// 				job.Phase = jlog.ToPhase
// 				flds = append(flds, biz.JobSfldPhase)
// 			}
// 			if len(flds) == 0 {
// 				return nil
// 			}
// 			_, err = o.jobrepo.Update(ctx, job, field.NewMask(flds...))
// 			if err != nil {
// 				return fmt.Errorf("failed to update Job: %w", err)
// 			}
// 		}
// 		return nil
// 	})
// }

var threeDDataTypes = []string{
	anno.Element_Type_pointcloud.String(),
	anno.Element_Type_fusion3d.String(),
	anno.Element_Type_fusion4d.String(),
}

func (o *EventsBiz) createJobsubmit(ctx context.Context, ev *mq.Event) error {
	body, _ := ev.Body.(string)
	data := &biz.Jobsubmit{}
	if err := json.Unmarshal([]byte(body), data); err != nil {
		return fmt.Errorf("failed to unmarshal event body: %w", err)
	}
	job, err := o.jobrepo.GetByID(ctx, data.JobID)
	if err != nil { // && !errors.IsNotFound(err) {
		return fmt.Errorf("failed to get Job: %w", err)
	}
	lot, err := o.lotrepo.GetByID(ctx, job.LotID)
	if err != nil {
		return fmt.Errorf("failed to get Lot: %w", err)
	}
	if lot.FinishedAt.After(lot.CreatedAt) {
		// check whether the lot is finished according to the FinishedAt and CreatedAt fields
		o.log.Warn(ctx, "ignore the event since lot is already finished.")
		return nil
	}

	if data.InsCnt == -1 || data.RrIns == -1 { // means this job submit is forcely rejected or recycled
		jobsubmits, _, err := o.jobsubmitrepo.List(ctx, &biz.JobsubmitListFilter{
			JobID:   data.JobID,
			Actions: []string{biz.JobActionSubmit, biz.JobActionAccept},
		}, biz.Pager{Pagesz: 1, OrderBy: "-id"})
		if err != nil {
			return fmt.Errorf("failed to list jobsubmits: %w", err)
		}
		if len(jobsubmits) == 0 {
			return nil
		}
		// set stats from last jobsubmit of submit/accept
		lastJobsubmit := jobsubmits[0]
		switch {
		case data.InsCnt == -1: // keep annos
			data.Elems = lastJobsubmit.Elems
			data.InsCnt = lastJobsubmit.InsCnt
			data.Ins2d = lastJobsubmit.Ins2d
			data.Ins3d = lastJobsubmit.Ins3d
			data.InsByClass = lastJobsubmit.InsByClass
		case data.RrIns == -1: // dont keep annos
			data.RrElems = lastJobsubmit.Elems
			data.RrIns = lastJobsubmit.InsCnt
			data.RrIns2d = lastJobsubmit.Ins2d
			data.RrIns3d = lastJobsubmit.Ins3d
			data.RrInsByClass = lastJobsubmit.InsByClass
		}
	}

	// fill LastExecutorUid and LastExecteamUid
	if data.Phase > 1 {
		data.LastExecutorUid, data.LastExecteamUid, err =
			o.joblogrepo.GetPhaseLastSubmitter(ctx, data.JobID, int(data.Phase-1))
		if err != nil {
			return fmt.Errorf("failed to GetPhaseLastSubmitter: %w", err)
		}
	}

	err = o.jobsubmitrepo.DoTx(ctx, func(ctx context.Context, tx biz.Tx) error {
		if data.ClaimedAt.IsZero() {
			data.ClaimedAt = data.CreatedAt.Add(-time.Second * time.Duration(data.Duration))
		}

		// update Final as false for all the previous jobsubmits in the same phase
		if err := o.jobsubmitrepo.BatchUpdate(ctx, &biz.Jobsubmit{Final: false},
			field.NewMask(biz.JobsubmitSfldFinal).WithCondition(biz.JobsubmitSfldJobID, job.ID).
				WithCondition(biz.JobsubmitSfldPhase, data.Phase),
			nil,
		); err != nil {
			return fmt.Errorf("failed to udpate Jobsubmit: %w", err)
		}

		data.Final = data.Action == biz.JobActionSubmit || data.Action == biz.JobActionAccept
		_, err := o.jobsubmitrepo.Create(ctx, data)
		if err != nil {
			if errors.IsConflict(err) {
				return nil
			}
			return fmt.Errorf("failed to create Jobsubmit: %w", err)
		}
		switch data.Action {
		case biz.JobActionSubmit, biz.JobActionAccept:
			job.LastSubmit = data.CreatedAt
			job.Submits++
			_, err = o.jobrepo.Update(ctx, job, field.NewMask(biz.JobSfldLastSubmit, biz.JobSfldSubmits))
			if err != nil {
				return fmt.Errorf("failed to update Job: %w", err)
			}
		}
		return nil
	})
	// signal workflow only when job is accepted after phase 1 and data type is 3D data types and lot type is annotate
	if err != nil || data.Phase < 2 || data.Action != biz.JobActionAccept ||
		!lo.Contains(threeDDataTypes, lot.DataType) || lot.Type != anno.Lot_Type_annotate.String() {
		return err
	}

	wfid := jobstat.GetWorkflowID(kid.StringID(job.LotID))
	wfev := &common.Event{
		Event: common.EvtJobsubmit,
		LotID: job.LotID,
		JobID: job.ID,
	}
	if err := o.bgtask.SignalEventWithStart(ctx, wfid, wfev, jobstat.JobstatWorkflow); err != nil {
		o.log.Error(ctx, "failed to signal event to workflow", err, "wfid", wfid, "ev", ev)
	}

	return nil
}

func (o *EventsBiz) createLot(ctx context.Context, ev *mq.Event) error {
	body, _ := ev.Body.(string)
	lot := &biz.Lot{}
	if err := json.Unmarshal([]byte(body), lot); err != nil {
		return fmt.Errorf("failed to unmarshal event body: %w", err)
	}
	_, err := o.lotrepo.Create(ctx, lot)
	if err != nil {
		if errors.IsConflict(err) {
			return nil
		}
		return fmt.Errorf("failed to create Lot: %w", err)
	}
	return nil
}

func (o *EventsBiz) bacthCreateLotphases(ctx context.Context, ev *mq.Event) error {
	body, _ := ev.Body.(string)
	data := []*biz.Lotphase{}
	if err := json.Unmarshal([]byte(body), &data); err != nil {
		return fmt.Errorf("failed to unmarshal event body: %w", err)
	}
	_, err := o.lotphaserepo.BatchCreate(ctx, data)
	if err != nil {
		if errors.IsConflict(err) {
			return nil
		}
		return fmt.Errorf("failed to create Lotphases: %w", err)
	}
	return nil
}

func (o *EventsBiz) deleteLotphases(ctx context.Context, ev *mq.Event) error {
	body, _ := ev.Body.(string)
	data := []int64{}
	if err := json.Unmarshal([]byte(body), &data); err != nil {
		return fmt.Errorf("failed to unmarshal event body: %w", err)
	}
	if err := o.lotphaserepo.DeleteByIDs(ctx, data); err != nil {
		return fmt.Errorf("failed to delete Lotphases: %w", err)
	}
	return nil
}

func (o *EventsBiz) createOrder(ctx context.Context, ev *mq.Event) error {
	body, _ := ev.Body.(string)
	data := &biz.Order{}
	if err := json.Unmarshal([]byte(body), data); err != nil {
		return fmt.Errorf("failed to unmarshal event body: %w", err)
	}
	fmt.Println(data.State.String())
	_, err := o.orderrepo.Create(ctx, data)
	if err != nil {
		if errors.IsConflict(err) {
			return nil
		}
		return fmt.Errorf("failed to create Order: %w", err)
	}
	return nil
}

func (o *EventsBiz) createBizgrant(ctx context.Context, ev *mq.Event) error {
	body, _ := ev.Body.(string)
	data := &biz.Bizgrant{}
	if err := json.Unmarshal([]byte(body), data); err != nil {
		return fmt.Errorf("failed to unmarshal event body: %w", err)
	}
	_, err := o.bizgrantRepo.Create(ctx, data)
	if err != nil {
		if errors.IsConflict(err) {
			return nil
		}
		return fmt.Errorf("failed to create Bizgrant: %w", err)
	}
	return nil
}

func (o *EventsBiz) deleteBizgrant(ctx context.Context, ev *mq.Event) error {
	body, _ := ev.Body.(string)
	bizgrantIDs := []int64{}
	if err := json.Unmarshal([]byte(body), &bizgrantIDs); err != nil {
		return fmt.Errorf("failed to unmarshal event body: %w", err)
	}

	if err := o.bizgrantRepo.DeleteByIDs(ctx, bizgrantIDs); err != nil {
		return fmt.Errorf("failed to delete Bizgrant: %w", err)
	}
	return nil
}

func (o *EventsBiz) createSpecgrant(ctx context.Context, ev *mq.Event) error {
	body, _ := ev.Body.(string)
	data := &biz.Specgrant{}
	if err := json.Unmarshal([]byte(body), data); err != nil {
		return fmt.Errorf("failed to unmarshal event body: %w", err)
	}
	_, err := o.specgrantRepo.Create(ctx, data)
	if err != nil {
		if errors.IsConflict(err) {
			return nil
		}
		return fmt.Errorf("failed to create Specgrant: %w", err)
	}
	return nil
}

func (o *EventsBiz) deleteSpecgrant(ctx context.Context, ev *mq.Event) error {
	body, _ := ev.Body.(string)
	specgrantIDs := []int64{}
	if err := json.Unmarshal([]byte(body), &specgrantIDs); err != nil {
		return fmt.Errorf("failed to unmarshal event body: %w", err)
	}

	if err := o.specgrantRepo.DeleteByIDs(ctx, specgrantIDs); err != nil {
		return fmt.Errorf("failed to delete Specgrant: %w", err)
	}
	return nil
}
