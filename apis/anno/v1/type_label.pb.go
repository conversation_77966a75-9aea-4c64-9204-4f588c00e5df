// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: anno/v1/type_label.proto

package anno

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	types "gitlab.rp.konvery.work/platform/apis/types"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Attr_Type_Enum int32

const (
	Attr_Type_input    Attr_Type_Enum = 0
	Attr_Type_bool     Attr_Type_Enum = 1
	Attr_Type_radiobox Attr_Type_Enum = 2
	Attr_Type_checkbox Attr_Type_Enum = 3
)

// Enum value maps for Attr_Type_Enum.
var (
	Attr_Type_Enum_name = map[int32]string{
		0: "input",
		1: "bool",
		2: "radiobox",
		3: "checkbox",
	}
	Attr_Type_Enum_value = map[string]int32{
		"input":    0,
		"bool":     1,
		"radiobox": 2,
		"checkbox": 3,
	}
)

func (x Attr_Type_Enum) Enum() *Attr_Type_Enum {
	p := new(Attr_Type_Enum)
	*p = x
	return p
}

func (x Attr_Type_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Attr_Type_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_type_label_proto_enumTypes[0].Descriptor()
}

func (Attr_Type_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_type_label_proto_enumTypes[0]
}

func (x Attr_Type_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Attr_Type_Enum.Descriptor instead.
func (Attr_Type_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_type_label_proto_rawDescGZIP(), []int{2, 0, 0}
}

type Attr_ValueType_Enum int32

const (
	Attr_ValueType_int      Attr_ValueType_Enum = 0
	Attr_ValueType_float    Attr_ValueType_Enum = 1
	Attr_ValueType_text     Attr_ValueType_Enum = 2
	Attr_ValueType_color    Attr_ValueType_Enum = 5
	Attr_ValueType_date     Attr_ValueType_Enum = 6
	Attr_ValueType_time     Attr_ValueType_Enum = 7
	Attr_ValueType_datetime Attr_ValueType_Enum = 8
	Attr_ValueType_bool     Attr_ValueType_Enum = 9
)

// Enum value maps for Attr_ValueType_Enum.
var (
	Attr_ValueType_Enum_name = map[int32]string{
		0: "int",
		1: "float",
		2: "text",
		5: "color",
		6: "date",
		7: "time",
		8: "datetime",
		9: "bool",
	}
	Attr_ValueType_Enum_value = map[string]int32{
		"int":      0,
		"float":    1,
		"text":     2,
		"color":    5,
		"date":     6,
		"time":     7,
		"datetime": 8,
		"bool":     9,
	}
)

func (x Attr_ValueType_Enum) Enum() *Attr_ValueType_Enum {
	p := new(Attr_ValueType_Enum)
	*p = x
	return p
}

func (x Attr_ValueType_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Attr_ValueType_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_anno_v1_type_label_proto_enumTypes[1].Descriptor()
}

func (Attr_ValueType_Enum) Type() protoreflect.EnumType {
	return &file_anno_v1_type_label_proto_enumTypes[1]
}

func (x Attr_ValueType_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Attr_ValueType_Enum.Descriptor instead.
func (Attr_ValueType_Enum) EnumDescriptor() ([]byte, []int) {
	return file_anno_v1_type_label_proto_rawDescGZIP(), []int{2, 1, 0}
}

// Label used in a task config
type Label struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key of the label
	Name        string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	Desc        string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Avatar      string `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// RGB value: #RRGGBBAA
	Color string `protobuf:"bytes,5,opt,name=color,proto3" json:"color,omitempty"`
	// attributes to be attached
	Attrs *AttrRefList `protobuf:"bytes,7,opt,name=attrs,proto3" json:"attrs,omitempty"`
	// muti-level classes: human -> adult, car -> mpv, ...
	Class     []string        `protobuf:"bytes,8,rep,name=class,proto3" json:"class,omitempty"`
	WidgetsV2 []*Label_Widget `protobuf:"bytes,9,rep,name=widgets_v2,json=widgetsV2,proto3" json:"widgets_v2,omitempty"`
	// a compounded object
	Compound          *Label_Compound `protobuf:"bytes,10,opt,name=compound,proto3" json:"compound,omitempty"`
	Is_3DSegmentation bool            `protobuf:"varint,11,opt,name=is_3d_segmentation,json=is3dSegmentation,proto3" json:"is_3d_segmentation,omitempty"`
	HasInstance       bool            `protobuf:"varint,12,opt,name=has_instance,json=hasInstance,proto3" json:"has_instance,omitempty"`
}

func (x *Label) Reset() {
	*x = Label{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_label_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Label) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Label) ProtoMessage() {}

func (x *Label) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_label_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Label.ProtoReflect.Descriptor instead.
func (*Label) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_label_proto_rawDescGZIP(), []int{0}
}

func (x *Label) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Label) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Label) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Label) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *Label) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *Label) GetAttrs() *AttrRefList {
	if x != nil {
		return x.Attrs
	}
	return nil
}

func (x *Label) GetClass() []string {
	if x != nil {
		return x.Class
	}
	return nil
}

func (x *Label) GetWidgetsV2() []*Label_Widget {
	if x != nil {
		return x.WidgetsV2
	}
	return nil
}

func (x *Label) GetCompound() *Label_Compound {
	if x != nil {
		return x.Compound
	}
	return nil
}

func (x *Label) GetIs_3DSegmentation() bool {
	if x != nil {
		return x.Is_3DSegmentation
	}
	return false
}

func (x *Label) GetHasInstance() bool {
	if x != nil {
		return x.HasInstance
	}
	return false
}

type AttrValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key of the AttrValue
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// display name
	DisplayName string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// description
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	// avatar of the value
	Avatar string `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// value, e.g. #FF00FF for RGB color
	Value string `protobuf:"bytes,5,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *AttrValue) Reset() {
	*x = AttrValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_label_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttrValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttrValue) ProtoMessage() {}

func (x *AttrValue) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_label_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttrValue.ProtoReflect.Descriptor instead.
func (*AttrValue) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_label_proto_rawDescGZIP(), []int{1}
}

func (x *AttrValue) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AttrValue) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *AttrValue) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *AttrValue) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *AttrValue) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type Attr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key of the attribute
	Name        string         `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName string         `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	Desc        string         `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Avatar      string         `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Type        Attr_Type_Enum `protobuf:"varint,5,opt,name=type,proto3,enum=anno.v1.Attr_Type_Enum" json:"type,omitempty"`
	// accepted value type if type is input
	ValueType Attr_ValueType_Enum `protobuf:"varint,6,opt,name=value_type,json=valueType,proto3,enum=anno.v1.Attr_ValueType_Enum" json:"value_type,omitempty"`
	// if not empty, value should be chosen from the list
	Choices []*AttrValue `protobuf:"bytes,7,rep,name=choices,proto3" json:"choices,omitempty"`
}

func (x *Attr) Reset() {
	*x = Attr{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_label_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Attr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Attr) ProtoMessage() {}

func (x *Attr) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_label_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Attr.ProtoReflect.Descriptor instead.
func (*Attr) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_label_proto_rawDescGZIP(), []int{2}
}

func (x *Attr) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Attr) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Attr) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Attr) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *Attr) GetType() Attr_Type_Enum {
	if x != nil {
		return x.Type
	}
	return Attr_Type_input
}

func (x *Attr) GetValueType() Attr_ValueType_Enum {
	if x != nil {
		return x.ValueType
	}
	return Attr_ValueType_int
}

func (x *Attr) GetChoices() []*AttrValue {
	if x != nil {
		return x.Choices
	}
	return nil
}

type AttrRefList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// optional attributes
	OptionalV2 []*AttrRefList_Attr `protobuf:"bytes,3,rep,name=optional_v2,json=optionalV2,proto3" json:"optional_v2,omitempty"`
	// required attributes
	RequiredV2 []*AttrRefList_Attr `protobuf:"bytes,4,rep,name=required_v2,json=requiredV2,proto3" json:"required_v2,omitempty"`
}

func (x *AttrRefList) Reset() {
	*x = AttrRefList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_label_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttrRefList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttrRefList) ProtoMessage() {}

func (x *AttrRefList) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_label_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttrRefList.ProtoReflect.Descriptor instead.
func (*AttrRefList) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_label_proto_rawDescGZIP(), []int{3}
}

func (x *AttrRefList) GetOptionalV2() []*AttrRefList_Attr {
	if x != nil {
		return x.OptionalV2
	}
	return nil
}

func (x *AttrRefList) GetRequiredV2() []*AttrRefList_Attr {
	if x != nil {
		return x.RequiredV2
	}
	return nil
}

type Label_Widget struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// widget name
	Name WidgetName_Enum `protobuf:"varint,1,opt,name=name,proto3,enum=anno.v1.WidgetName_Enum" json:"name,omitempty"`
	// rawdata type the widget is applicable to
	RawdataType Rawdata_Type_Enum `protobuf:"varint,2,opt,name=rawdata_type,json=rawdataType,proto3,enum=anno.v1.Rawdata_Type_Enum" json:"rawdata_type,omitempty"`
	// preset scale: it is
	// [width, height] for box2d in pixel;
	// [width, height, depth (distance between two center-points)] for pscuboid in pixel;
	// [sx, sy, sz] for cuboid in meter;
	Preset []float32 `protobuf:"fixed32,3,rep,packed,name=preset,proto3" json:"preset,omitempty"`
	// define the minimum and maximum scales the widget must conform to; see comments on the preset field for its format
	Scale *types.Range `protobuf:"bytes,4,opt,name=scale,proto3" json:"scale,omitempty"`
	// line type used to draw the widget
	LineType WidgetLineType_Enum `protobuf:"varint,5,opt,name=line_type,json=lineType,proto3,enum=anno.v1.WidgetLineType_Enum" json:"line_type,omitempty"`
}

func (x *Label_Widget) Reset() {
	*x = Label_Widget{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_label_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Label_Widget) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Label_Widget) ProtoMessage() {}

func (x *Label_Widget) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_label_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Label_Widget.ProtoReflect.Descriptor instead.
func (*Label_Widget) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_label_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Label_Widget) GetName() WidgetName_Enum {
	if x != nil {
		return x.Name
	}
	return WidgetName_unspecified
}

func (x *Label_Widget) GetRawdataType() Rawdata_Type_Enum {
	if x != nil {
		return x.RawdataType
	}
	return Rawdata_Type_unspecified
}

func (x *Label_Widget) GetPreset() []float32 {
	if x != nil {
		return x.Preset
	}
	return nil
}

func (x *Label_Widget) GetScale() *types.Range {
	if x != nil {
		return x.Scale
	}
	return nil
}

func (x *Label_Widget) GetLineType() WidgetLineType_Enum {
	if x != nil {
		return x.LineType
	}
	return WidgetLineType_line
}

type Label_Compound struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// components
	Parts []*Label_Compound_Part `protobuf:"bytes,1,rep,name=parts,proto3" json:"parts,omitempty"`
}

func (x *Label_Compound) Reset() {
	*x = Label_Compound{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_label_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Label_Compound) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Label_Compound) ProtoMessage() {}

func (x *Label_Compound) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_label_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Label_Compound.ProtoReflect.Descriptor instead.
func (*Label_Compound) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_label_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Label_Compound) GetParts() []*Label_Compound_Part {
	if x != nil {
		return x.Parts
	}
	return nil
}

type Label_Compound_Part struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// label name
	Label string `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	// define minimum and maximum occurences of the part within a compound
	Occurs *types.RangeInt32 `protobuf:"bytes,2,opt,name=occurs,proto3" json:"occurs,omitempty"`
}

func (x *Label_Compound_Part) Reset() {
	*x = Label_Compound_Part{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_label_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Label_Compound_Part) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Label_Compound_Part) ProtoMessage() {}

func (x *Label_Compound_Part) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_label_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Label_Compound_Part.ProtoReflect.Descriptor instead.
func (*Label_Compound_Part) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_label_proto_rawDescGZIP(), []int{0, 1, 0}
}

func (x *Label_Compound_Part) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *Label_Compound_Part) GetOccurs() *types.RangeInt32 {
	if x != nil {
		return x.Occurs
	}
	return nil
}

type Attr_Type struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Attr_Type) Reset() {
	*x = Attr_Type{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_label_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Attr_Type) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Attr_Type) ProtoMessage() {}

func (x *Attr_Type) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_label_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Attr_Type.ProtoReflect.Descriptor instead.
func (*Attr_Type) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_label_proto_rawDescGZIP(), []int{2, 0}
}

type Attr_ValueType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Attr_ValueType) Reset() {
	*x = Attr_ValueType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_label_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Attr_ValueType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Attr_ValueType) ProtoMessage() {}

func (x *Attr_ValueType) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_label_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Attr_ValueType.ProtoReflect.Descriptor instead.
func (*Attr_ValueType) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_label_proto_rawDescGZIP(), []int{2, 1}
}

type AttrRefList_Attr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// attribute name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// default value of this attribute
	Default string `protobuf:"bytes,2,opt,name=default,proto3" json:"default,omitempty"`
	// which rawdata types the attribute is applicable to
	RawdataTypes []Rawdata_Type_Enum `protobuf:"varint,3,rep,packed,name=rawdata_types,json=rawdataTypes,proto3,enum=anno.v1.Rawdata_Type_Enum" json:"rawdata_types,omitempty"`
}

func (x *AttrRefList_Attr) Reset() {
	*x = AttrRefList_Attr{}
	if protoimpl.UnsafeEnabled {
		mi := &file_anno_v1_type_label_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttrRefList_Attr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttrRefList_Attr) ProtoMessage() {}

func (x *AttrRefList_Attr) ProtoReflect() protoreflect.Message {
	mi := &file_anno_v1_type_label_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttrRefList_Attr.ProtoReflect.Descriptor instead.
func (*AttrRefList_Attr) Descriptor() ([]byte, []int) {
	return file_anno_v1_type_label_proto_rawDescGZIP(), []int{3, 0}
}

func (x *AttrRefList_Attr) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AttrRefList_Attr) GetDefault() string {
	if x != nil {
		return x.Default
	}
	return ""
}

func (x *AttrRefList_Attr) GetRawdataTypes() []Rawdata_Type_Enum {
	if x != nil {
		return x.RawdataTypes
	}
	return nil
}

var File_anno_v1_type_label_proto protoreflect.FileDescriptor

var file_anno_v1_type_label_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x6e, 0x6e, 0x6f,
	0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61,
	0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf3, 0x06, 0x0a, 0x05,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2a,
	0x0a, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x52, 0x65, 0x66, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x12, 0x34, 0x0a, 0x0a, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x5f, 0x76, 0x32, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x2e, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x52, 0x09, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x73, 0x56, 0x32, 0x12, 0x33, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x75,
	0x6e, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e,
	0x64, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x69,
	0x73, 0x5f, 0x33, 0x64, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x33, 0x64, 0x53, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x61, 0x73,
	0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0b, 0x68, 0x61, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0xa5, 0x02, 0x0a,
	0x06, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x12, 0x36, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x47, 0x0a, 0x0c, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75,
	0x6d, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x72, 0x61, 0x77,
	0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x73,
	0x65, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x02, 0x52, 0x06, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74,
	0x12, 0x22, 0x0a, 0x05, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0c, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x05, 0x73,
	0x63, 0x61, 0x6c, 0x65, 0x12, 0x43, 0x0a, 0x09, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x08, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x3a, 0x19, 0xba, 0x47, 0x16, 0xba, 0x01,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x0c, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x1a, 0xa1, 0x01, 0x0a, 0x08, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e,
	0x64, 0x12, 0x32, 0x0a, 0x05, 0x70, 0x61, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e, 0x64, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x52, 0x05,
	0x70, 0x61, 0x72, 0x74, 0x73, 0x1a, 0x54, 0x0a, 0x04, 0x50, 0x61, 0x72, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x12, 0x29, 0x0a, 0x06, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x06, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x73, 0x3a, 0x0b,
	0xba, 0x47, 0x08, 0xba, 0x01, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x3a, 0x0b, 0xba, 0x47, 0x08,
	0xba, 0x01, 0x05, 0x70, 0x61, 0x72, 0x74, 0x73, 0x3a, 0x21, 0xba, 0x47, 0x1e, 0xba, 0x01, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0xba, 0x01, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x4a, 0x04, 0x08, 0x06, 0x10,
	0x07, 0x22, 0xa7, 0x01, 0x0a, 0x09, 0x41, 0x74, 0x74, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x21, 0xba, 0x47, 0x1e, 0xba, 0x01, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0xba, 0x01, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xf9, 0x03, 0x0a, 0x04,
	0x41, 0x74, 0x74, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x35, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x74, 0x74, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x45,
	0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x74, 0x74,
	0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x07, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x74, 0x74, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x63, 0x68, 0x6f, 0x69,
	0x63, 0x65, 0x73, 0x1a, 0x3f, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x22, 0x37, 0x0a, 0x04, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x09, 0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x10, 0x00, 0x12, 0x08,
	0x0a, 0x04, 0x62, 0x6f, 0x6f, 0x6c, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x72, 0x61, 0x64, 0x69,
	0x6f, 0x62, 0x6f, 0x78, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x62,
	0x6f, 0x78, 0x10, 0x03, 0x1a, 0x68, 0x0a, 0x09, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x5b, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x07, 0x0a, 0x03, 0x69, 0x6e, 0x74,
	0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x10, 0x01, 0x12, 0x08, 0x0a,
	0x04, 0x74, 0x65, 0x78, 0x74, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x10, 0x05, 0x12, 0x08, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x10, 0x06, 0x12, 0x08, 0x0a, 0x04,
	0x74, 0x69, 0x6d, 0x65, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x10, 0x08, 0x12, 0x08, 0x0a, 0x04, 0x62, 0x6f, 0x6f, 0x6c, 0x10, 0x09, 0x3a, 0x37,
	0xba, 0x47, 0x34, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x0c, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x04, 0x74, 0x79, 0x70, 0x65,
	0xba, 0x01, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0xba, 0x01, 0x07,
	0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x22, 0xb6, 0x02, 0x0a, 0x0b, 0x41, 0x74, 0x74, 0x72,
	0x52, 0x65, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0b, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x76, 0x32, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x52, 0x65, 0x66, 0x4c, 0x69,
	0x73, 0x74, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x52, 0x0a, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x56, 0x32, 0x12, 0x3a, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f,
	0x76, 0x32, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x52, 0x65, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x2e, 0x41,
	0x74, 0x74, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x56, 0x32, 0x1a,
	0x81, 0x01, 0x0a, 0x04, 0x41, 0x74, 0x74, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x3f, 0x0a, 0x0d, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1a, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0c, 0x72, 0x61, 0x77, 0x64, 0x61,
	0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x73, 0x3a, 0x0a, 0xba, 0x47, 0x07, 0xba, 0x01, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x3a, 0x1f, 0xba, 0x47, 0x1c, 0xba, 0x01, 0x0b, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x76, 0x32, 0xba, 0x01, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x5f, 0x76, 0x32, 0x4a, 0x04, 0x08, 0x01, 0x10, 0x02, 0x4a, 0x04, 0x08, 0x02, 0x10, 0x03,
	0x42, 0x3e, 0x0a, 0x07, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x31, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e, 0x6e, 0x6f,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_anno_v1_type_label_proto_rawDescOnce sync.Once
	file_anno_v1_type_label_proto_rawDescData = file_anno_v1_type_label_proto_rawDesc
)

func file_anno_v1_type_label_proto_rawDescGZIP() []byte {
	file_anno_v1_type_label_proto_rawDescOnce.Do(func() {
		file_anno_v1_type_label_proto_rawDescData = protoimpl.X.CompressGZIP(file_anno_v1_type_label_proto_rawDescData)
	})
	return file_anno_v1_type_label_proto_rawDescData
}

var file_anno_v1_type_label_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_anno_v1_type_label_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_anno_v1_type_label_proto_goTypes = []interface{}{
	(Attr_Type_Enum)(0),         // 0: anno.v1.Attr.Type.Enum
	(Attr_ValueType_Enum)(0),    // 1: anno.v1.Attr.ValueType.Enum
	(*Label)(nil),               // 2: anno.v1.Label
	(*AttrValue)(nil),           // 3: anno.v1.AttrValue
	(*Attr)(nil),                // 4: anno.v1.Attr
	(*AttrRefList)(nil),         // 5: anno.v1.AttrRefList
	(*Label_Widget)(nil),        // 6: anno.v1.Label.Widget
	(*Label_Compound)(nil),      // 7: anno.v1.Label.Compound
	(*Label_Compound_Part)(nil), // 8: anno.v1.Label.Compound.Part
	(*Attr_Type)(nil),           // 9: anno.v1.Attr.Type
	(*Attr_ValueType)(nil),      // 10: anno.v1.Attr.ValueType
	(*AttrRefList_Attr)(nil),    // 11: anno.v1.AttrRefList.Attr
	(WidgetName_Enum)(0),        // 12: anno.v1.WidgetName.Enum
	(Rawdata_Type_Enum)(0),      // 13: anno.v1.Rawdata.Type.Enum
	(*types.Range)(nil),         // 14: types.Range
	(WidgetLineType_Enum)(0),    // 15: anno.v1.WidgetLineType.Enum
	(*types.RangeInt32)(nil),    // 16: types.RangeInt32
}
var file_anno_v1_type_label_proto_depIdxs = []int32{
	5,  // 0: anno.v1.Label.attrs:type_name -> anno.v1.AttrRefList
	6,  // 1: anno.v1.Label.widgets_v2:type_name -> anno.v1.Label.Widget
	7,  // 2: anno.v1.Label.compound:type_name -> anno.v1.Label.Compound
	0,  // 3: anno.v1.Attr.type:type_name -> anno.v1.Attr.Type.Enum
	1,  // 4: anno.v1.Attr.value_type:type_name -> anno.v1.Attr.ValueType.Enum
	3,  // 5: anno.v1.Attr.choices:type_name -> anno.v1.AttrValue
	11, // 6: anno.v1.AttrRefList.optional_v2:type_name -> anno.v1.AttrRefList.Attr
	11, // 7: anno.v1.AttrRefList.required_v2:type_name -> anno.v1.AttrRefList.Attr
	12, // 8: anno.v1.Label.Widget.name:type_name -> anno.v1.WidgetName.Enum
	13, // 9: anno.v1.Label.Widget.rawdata_type:type_name -> anno.v1.Rawdata.Type.Enum
	14, // 10: anno.v1.Label.Widget.scale:type_name -> types.Range
	15, // 11: anno.v1.Label.Widget.line_type:type_name -> anno.v1.WidgetLineType.Enum
	8,  // 12: anno.v1.Label.Compound.parts:type_name -> anno.v1.Label.Compound.Part
	16, // 13: anno.v1.Label.Compound.Part.occurs:type_name -> types.RangeInt32
	13, // 14: anno.v1.AttrRefList.Attr.rawdata_types:type_name -> anno.v1.Rawdata.Type.Enum
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_anno_v1_type_label_proto_init() }
func file_anno_v1_type_label_proto_init() {
	if File_anno_v1_type_label_proto != nil {
		return
	}
	file_anno_v1_elemanno_proto_init()
	file_anno_v1_widget_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_anno_v1_type_label_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Label); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_label_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttrValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_label_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Attr); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_label_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttrRefList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_label_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Label_Widget); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_label_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Label_Compound); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_label_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Label_Compound_Part); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_label_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Attr_Type); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_label_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Attr_ValueType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_anno_v1_type_label_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttrRefList_Attr); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_anno_v1_type_label_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_anno_v1_type_label_proto_goTypes,
		DependencyIndexes: file_anno_v1_type_label_proto_depIdxs,
		EnumInfos:         file_anno_v1_type_label_proto_enumTypes,
		MessageInfos:      file_anno_v1_type_label_proto_msgTypes,
	}.Build()
	File_anno_v1_type_label_proto = out.File
	file_anno_v1_type_label_proto_rawDesc = nil
	file_anno_v1_type_label_proto_goTypes = nil
	file_anno_v1_type_label_proto_depIdxs = nil
}
