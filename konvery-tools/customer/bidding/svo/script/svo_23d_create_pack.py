import os
import numpy as np
import math
import pandas as pd
import sys
import shutil
import yaml
sys.path.append('.')
from customer.common import tools
from customer.common import ply2pcd


camera_names = ['camera_front_center', 'camera_front_center_tele', 'camera_front_left', 'camera_front_right',
                'camera_rear', 'camera_rear_left', 'camera_rear_right']


def read_yaml_file(file):
    with open(file, 'r') as fr:
        data = yaml.safe_load(fr)
        return data


def get_nearest_frame(src, dsts):
    distance = math.inf
    target = None
    for dst in dsts:
        if abs(dst-src) < distance:
            distance = abs(dst-src)
            target = dst
    return target


def construct_pose(pose_file, frame_timestamps):
    poses = dict()
    df = pd.read_csv(pose_file)
    for frame_timestamp in frame_timestamps:
        target_timestamp = get_nearest_frame(frame_timestamp, df['timestamp'])
        target_pose = df[df['timestamp'] == target_timestamp]
        pose = [
            target_pose['pose_pose_position_x'].values[0],
            target_pose['pose_pose_position_y'].values[0],
            target_pose['pose_pose_position_z'].values[0],
            target_pose['pose_pose_orientation_x'].values[0],
            target_pose['pose_pose_orientation_y'].values[0],
            target_pose['pose_pose_orientation_z'].values[0],
            target_pose['pose_pose_orientation_w'].values[0],
        ]
        pose_mat = tools.pose_to_mat(pose)
        poses[str(frame_timestamp)] = pose_mat.T.flatten().tolist()
    return poses


def construct_config(input_dir):
    params_dirs = [os.path.dirname(_) for _ in tools.find_dir_by_pre_name(os.path.join(input_dir, 'params'), 'extrinsic')]
    assert len(params_dirs) == 1, 'find more than one params dir'
    params_dir = params_dirs[0]
    sensor_params = dict()
    for camera_name in camera_names:
        if camera_name == 'camera_rear':
            intrinsic_yaml_file = os.path.join(params_dir, 'intrinsics', f'camera_rear_center_intrinsic.yaml')
            extrinsic_yaml_file = os.path.join(params_dir, 'extrinsics', f'camera_rear_center_extrinsic.yaml')
        else:
            intrinsic_yaml_file = os.path.join(params_dir, 'intrinsics', f'{camera_name}_intrinsic.yaml')
            extrinsic_yaml_file = os.path.join(params_dir, 'extrinsics', f'{camera_name}_extrinsic.yaml')
        intrinsic_data = read_yaml_file(intrinsic_yaml_file)
        extrinsic_data = read_yaml_file(extrinsic_yaml_file)
        extrinsic = tools.pose_to_mat(
            np.array([
                extrinsic_data['x'],
                extrinsic_data['y'],
                extrinsic_data['z'],
                extrinsic_data['qx'],
                extrinsic_data['qy'],
                extrinsic_data['qz'],
                extrinsic_data['qw']
            ])
        )
        assert intrinsic_data['model'] == 'pinhole', f'{camera_name} model not support'
        sensor_params[camera_name] = {
            'camera_model': intrinsic_data['model'],
            'extrinsic': np.linalg.inv(extrinsic).tolist(),
            'fx': intrinsic_data['fx'],
            'fy': intrinsic_data['fy'],
            'cx': intrinsic_data['cx'],
            'cy': intrinsic_data['cy'],
            'k1': intrinsic_data['k1'],
            'k2': intrinsic_data['k2'],
            'p1': intrinsic_data['p1'],
            'p2': intrinsic_data['p2'],
            'k3': intrinsic_data['k3'],
            'k4': intrinsic_data['k4'],
            'k5': intrinsic_data['k5'],
            'k6': intrinsic_data['k6'],
        }
    config = {
        "data_type": "fusion_pointcloud",
        "sensor_params": sensor_params,
    }
    return config


def process_sub_dir(sub_dir, output_dir):
    out_clip_dir = os.path.join(output_dir, os.path.basename(sub_dir))
    os.mkdir(out_clip_dir)
    out_lidar_dir = os.path.join(out_clip_dir, 'lidar')
    os.mkdir(out_lidar_dir)
    out_cameras_dir = os.path.join(out_clip_dir, 'camera')
    os.mkdir(out_cameras_dir)
    for camera_name in camera_names:
        os.mkdir(os.path.join(out_cameras_dir, camera_name))
    extract_file = os.path.join(sub_dir, 'extract_result.json')
    extract_datas = tools.get_json_data(extract_file)
    plys = tools.get_file_by_extension(os.path.join(sub_dir, 'processed', 'pandar128'), '.ply')
    assert {i['pandar128'] for i in extract_datas} == {i.name for i in plys}, "pcd and extract file not match"
    for extract_data in extract_datas:
        ply_src_path = os.path.join(sub_dir, 'processed', 'pandar128', extract_data['pandar128'])
        pcd_dst_path = os.path.join(out_lidar_dir, extract_data['pandar128'].replace('.ply', '.pcd'))
        ply2pcd.convert_ply_to_pcd(ply_src_path, pcd_dst_path)
        for camera_name in camera_names:
            src_img = os.path.join(sub_dir, camera_name, extract_data[camera_name])
            dst_img = os.path.join(out_cameras_dir, camera_name, extract_data['pandar128'].replace('.ply', '.jpg'))
            shutil.copyfile(src_img, dst_img)
    return [float(i['pandar128'].rsplit('.', 1)[0]) for i in extract_datas]


def run(input_dir, output_dir):
    config = construct_config(input_dir)
    sub_dirs = [os.path.dirname(_) for _ in tools.find_dir_by_pre_name(os.path.join(input_dir, 'data'), 'processed')]
    for sub_dir in sub_dirs:
        pose_file = os.path.join(sub_dir, 'novatel', 'novatel.csv')
        frame_timestamps = process_sub_dir(sub_dir, output_dir)
        poses = construct_pose(pose_file, frame_timestamps)
        config['poses'] = poses
        tools.write_json_file(config, os.path.join(output_dir, os.path.basename(sub_dir), 'config.json'), indent=4, ensure_ascii=False)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
