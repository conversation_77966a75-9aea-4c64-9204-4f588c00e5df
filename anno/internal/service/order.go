// source: anno/v1/order.proto
package service

import (
	"context"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"time"

	"anno/api/client"
	"anno/internal/biz"
	"anno/internal/ostore"

	"github.com/samber/lo"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/kmath"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func FromBizOrder(d *biz.Order) *anno.Order {
	if d == nil {
		return nil
	}
	o := &anno.Order{
		Uid:           d.GetUid(),
		Name:          d.Name,
		Size:          d.Size,
		DataUid:       d.DataUid,
		State:         anno.Order_State_Enum(anno.Order_State_Enum_value[d.State.String()]),
		Error:         d.Error,
		Source:        d.Source.E,
		OrgUid:        d.OrgUid,
		CreatorUid:    d.CreatorUid,
		InsTotal:      d.InsTotal,
		AnnoResultUrl: d.AnnoResultURL,
		CreatedAt:     timestamppb.New(d.CreatedAt),

		CanExportAnnos: d.CanExportAnnos,
	}
	if !d.CanExportAnnos {
		o.AnnoResultUrl = ""
	}
	return o
}

func ConvertCreateOrderRequestToBizOrder(d *anno.CreateOrderRequest) *biz.Order {
	if d == nil {
		return nil
	}
	o := &biz.Order{
		Name:   d.Name,
		OrgUid: d.OrgUid,
		Source: *serial.New(d.Source),
	}
	return o
}

func ToBizOrder(d *anno.Order) *biz.Order {
	if d == nil {
		return nil
	}
	o := &biz.Order{
		ID:     kid.ParseID(d.Uid),
		Name:   d.Name,
		OrgUid: d.OrgUid,
		Source: *serial.New(d.Source),
		Size:   d.Size,
		State:  biz.OrderState(d.State.String()),
		Error:  d.Error[:kmath.Min(len(d.Error), 512)],
	}
	return o
}

type OrdersService struct {
	anno.UnimplementedOrdersServer
	bz      *biz.OrdersBiz
	lotsBiz *biz.LotsBiz
	log     *log.Helper
}

func NewOrdersService(logger log.Logger, bz *biz.OrdersBiz, lotsBiz *biz.LotsBiz) *OrdersService {
	return &OrdersService{log: log.NewHelper(logger), bz: bz, lotsBiz: lotsBiz}
}

func (o *OrdersService) CreateOrder(ctx context.Context, req *anno.CreateOrderRequest) (*anno.Order, error) {
	if req.Source == nil || (len(req.Source.Uris) == 0 && len(req.Source.NamedUris) == 0) {
		return nil, errors.NewErrEmptyField(errors.WithFields("source", "source.uris", "source.named_uris"))
	}
	spew.Dump("---> create order req: ", req)
	user := biz.UserFromCtx(ctx)
	scope := req.OrgUid
	if scope == "" {
		scope = user.GetOrgUid()
		req.OrgUid = scope
	}
	if scope == "" {
		return nil, errors.NewErrEmptyField(errors.WithFields("org_uid"))
	}
	scope = client.GroupScope(scope)
	fmt.Println("---> scope:", scope)
	if !client.IsAllowed(ctx, "", biz.PermCreate, biz.PermClsOrder, scope) {
		return nil, errors.NewErrForbidden()
	}
	if len(req.Source.ErrorHandlers) > 4 { // count(Rawdata_Type_Enum) * count(Source_ParseErrorHandler_Error_Enum)
		return nil, errors.NewErrTooManyItemsInField(errors.WithMessage("too many error handlers"),
			errors.WithFields("error_handlers"))
	}

	order := ConvertCreateOrderRequestToBizOrder(req)
	order.CreatorUid = user.GetUid()
	fmt.Println("---> create order: ", order)
	data, err := o.bz.Create(ctx, order)
	return FromBizOrder(data), err
}

func (o *OrdersService) UpdateOrder(ctx context.Context, req *anno.UpdateOrderRequest) (*anno.Order, error) {
	allowFlds := []string{biz.OrderSfldName.String()}
	isPrivileged := client.IsPrivilegedUser(client.UserFromCtx(ctx).User)
	if isPrivileged {
		allowFlds = append(allowFlds, biz.OrderSfldState.String(), biz.OrderSfldSize.String(), biz.OrderSfldError.String())
	}
	flds, err := CheckUpdateFlds(biz.Order{}, req.Fields, allowFlds...)
	if err != nil {
		return nil, err
	}

	if !isPrivileged || !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsOrder, req.Order.Uid) {
		return nil, errors.NewErrForbidden()
	}

	data, err := o.bz.Update(ctx, ToBizOrder(req.Order), field.NewMask(flds...))
	return FromBizOrder(data), err
}

func (o *OrdersService) CancelOrder(ctx context.Context, req *anno.GetOrderRequest) (*emptypb.Empty, error) {
	if !client.IsPrivilegedUser(client.UserFromCtx(ctx).User) {
		return nil, errors.NewErrForbidden()
	}
	data, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if data.State.IsFinal() {
		return nil, errors.NewErrFailedPrecondition(errors.WithMessage("order is already final"))
	}

	// TODO: check status of related data and lot, and cancel them
	data.State = biz.OrderStateCanceled
	_, err = o.bz.Update(ctx, data, field.NewMask(biz.OrderSfldState.String()))
	return &emptypb.Empty{}, err
}

func (o *OrdersService) DeleteOrder(ctx context.Context, req *anno.DeleteOrderRequest) (*emptypb.Empty, error) {
	if !client.IsAllowed(ctx, "", biz.PermDelete, biz.PermClsOrder, req.Uid) {
		return nil, errors.NewErrForbidden()
	}
	data, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if !data.State.IsFinal() {
		return nil, errors.NewErrFailedPrecondition(errors.WithMessage("order is not stopped"))
	}
	if time.Since(data.UpdatedAt) < 24*time.Hour {
		// give enough time for the order worflow to complete
		return nil, errors.NewErrFailedPrecondition(errors.WithMessage("too short time since the order is stopped"))
	}

	return &emptypb.Empty{}, o.bz.DeleteByUid(ctx, req.Uid)
}

func (o *OrdersService) GetOrder(ctx context.Context, req *anno.GetOrderRequest) (*anno.Order, error) {
	if !client.IsAllowed(ctx, "", biz.PermGet, biz.PermClsOrder, req.Uid) {
		return nil, errors.NewErrForbidden()
	}
	data, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	annoOrder := FromBizOrder(data)
	annoOrder.AnnoResultUrl = signAnnoResult(ctx, annoOrder.AnnoResultUrl, o.log)

	summary, err := client.GetDataValidationSummary(ctx, annoOrder.DataUid)
	if err != nil && !errors.IsNotFound(err) {
		return nil, err
	}
	annoOrder.DataSummary = summary

	return annoOrder, nil
}

func (o *OrdersService) ListOrder(ctx context.Context, req *anno.ListOrderRequest) (*anno.ListOrderReply, error) {
	creator, scope := getListScope(ctx, req.OrgUid, req.CreatorUid)
	fmt.Printf("---> create: %s,  scope: %s\n", creator, scope)
	if !client.IsAllowed(ctx, "", biz.PermList, biz.PermClsOrder, scope) {
		return nil, errors.NewErrForbidden()
	}

	pager := biz.Pager{
		Pagesz: int(req.Pagesz),
		Page:   int(req.Page),
	}
	filter := &biz.OrderListFilter{
		OrgUid:      req.OrgUid,
		CreatorUid:  creator,
		NamePattern: req.NamePattern,
		States: lo.Map(req.States, func(v anno.Order_State_Enum, _ int) biz.OrderState {
			return biz.OrderState(v.String())
		}),
	}
	fmt.Println("---> ListOrder filter: ", filter)

	var cnt int
	if pager.Page == 0 {
		var err error
		cnt, err = o.bz.Count(ctx, filter)
		if err != nil {
			return nil, err
		}
	}
	datas, err := o.bz.List(ctx, filter, pager)
	if err != nil {
		return nil, err
	}
	lo.ForEach(datas, func(v *biz.Order, _ int) {
		v.Source.E.Proprietary = nil
		v.Source.E.Uris = nil
		v.AnnoResultURL = signAnnoResult(ctx, v.AnnoResultURL, o.log)
	})
	var orgs []*client.BaseUser
	if req.WithOrg {
		uids := lo.Map(datas, func(d *biz.Order, _ int) string { return d.OrgUid })
		teams, err := client.ListTeamsInMap(ctx, uids)
		if err != nil {
			return nil, err
		}
		orgs = lo.Map(datas, func(d *biz.Order, _ int) *client.BaseUser { return teams[d.OrgUid] })
	}
	return &anno.ListOrderReply{Total: int32(cnt),
		Orders: kslice.Map(FromBizOrder, datas),
		Orgs:   orgs,
	}, err
}

func (o *OrdersService) SetAnnoResult(ctx context.Context, req *anno.SetOrderAnnoResultRequest) (*emptypb.Empty, error) {
	// FIXME: The operation is open to all PMs/KAMs!
	op := biz.UserFromCtx(ctx).User
	if !client.IsPrivilegedUser(op) && !lo.Contains([]string{client.SysRoleKAM, client.SysRolePM}, op.Role) {
		return nil, errors.NewErrForbidden()
	}
	_, err := o.bz.Update(ctx, &biz.Order{
		ID:            kid.ParseID(req.Uid),
		AnnoResultURL: req.Url,
	}, field.NewMask(biz.OrderSfldAnnoResultURL.String()))
	return &emptypb.Empty{}, err
}

func (o *OrdersService) GetAnnoResult(ctx context.Context, req *anno.GetOrderRequest) (*anno.GetOrderAnnoResultReply, error) {
	if !client.IsAllowed(ctx, "", biz.PermExportAnno, biz.PermClsOrder, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	data, err := o.bz.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if !data.CanExportAnnos {
		return nil, errors.NewErrForbidden(errors.WithMessage("cannot export annotations"))
	}

	readyTime := time.Time{}
	if data.State == biz.OrderStateFinished {
		readyTime = data.UpdatedAt.Add(time.Hour)
	}

	return &anno.GetOrderAnnoResultReply{
		WillReadyAt: timestamppb.New(readyTime),
		Url:         signAnnoResult(ctx, data.AnnoResultURL, o.log),
	}, nil
}

func (o *OrdersService) ExportOrderAnnos(ctx context.Context, req *anno.ExportOrderAnnosRequest) (*emptypb.Empty, error) {
	if !client.IsAllowed(ctx, "", biz.PermExportAnno, biz.PermClsOrder, req.Uid) {
		return nil, errors.NewErrForbidden()
	}

	orderID := kid.ParseID(req.Uid)
	order, err := o.bz.GetByID(ctx, orderID)
	if err != nil {
		return nil, err
	}
	if !order.CanExportAnnos {
		return nil, errors.NewErrForbidden(errors.WithMessage("cannot export annotations"))
	}

	lotIDs, err := o.lotsBiz.GetLotIDsByOrderID(ctx, kid.ParseID(req.Uid))
	if err != nil {
		return nil, err
	}
	if len(lotIDs) == 0 {
		return nil, errors.NewErrFailedPrecondition(errors.WithMessage("order has no lots"),
			errors.WithModel("lot"))
	}

	latestLotID := lotIDs[len(lotIDs)-1]
	latestLot, err := o.lotsBiz.GetByID(ctx, latestLotID, false)
	if err != nil {
		return nil, err
	}
	if err := o.lotsBiz.ClearAnnoResult(ctx, latestLot); err != nil {
		return nil, err
	}
	if err := o.bz.ClearAnnoResult(ctx, orderID); err != nil {
		return nil, err
	}

	if _, err := client.ExportLotAnnos(ctx, &client.ExportLotAnnosRequest{
		Uid:      kid.StringID(latestLotID),
		OrderUid: req.Uid,
		Option:   req.Option,
	}); err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func signAnnoResult(ctx context.Context, url string, lg *log.Helper) string {
	r, err := ostore.SignGetURL(ctx, url, 0, 0)
	if err != nil {
		url = ""
		lg.Error(ctx, "failed to sign anno result", err)
	} else {
		url = r.URL
	}
	return url
}
