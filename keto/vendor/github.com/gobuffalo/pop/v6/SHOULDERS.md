# Pop Stands on the Shoulders of Giants

Pop does not try to reinvent the wheel! Instead, it uses the already great wheels developed by the Go community and puts them all together in the best way possible. Without these giants, this project would not be possible. Please make sure to check them out and thank them for all of their hard work.

Thank you to the following **GIANTS**:

* [github.com/BurntSushi/toml](https://godoc.org/github.com/BurntSushi/toml)
* [github.com/Masterminds/semver/v3](https://godoc.org/github.com/Masterminds/semver/v3)
* [github.com/aymerick/douceur](https://godoc.org/github.com/aymerick/douceur)
* [github.com/cockroachdb/apd](https://godoc.org/github.com/cockroachdb/apd)
* [github.com/coreos/go-systemd](https://godoc.org/github.com/coreos/go-systemd)
* [github.com/cpuguy83/go-md2man/v2](https://godoc.org/github.com/cpuguy83/go-md2man/v2)
* [github.com/creack/pty](https://godoc.org/github.com/creack/pty)
* [github.com/davecgh/go-spew](https://godoc.org/github.com/davecgh/go-spew)
* [github.com/fatih/color](https://godoc.org/github.com/fatih/color)
* [github.com/fatih/structs](https://godoc.org/github.com/fatih/structs)
* [github.com/go-kit/log](https://godoc.org/github.com/go-kit/log)
* [github.com/go-logfmt/logfmt](https://godoc.org/github.com/go-logfmt/logfmt)
* [github.com/go-sql-driver/mysql](https://godoc.org/github.com/go-sql-driver/mysql)
* [github.com/go-stack/stack](https://godoc.org/github.com/go-stack/stack)
* [github.com/gobuffalo/attrs](https://godoc.org/github.com/gobuffalo/attrs)
* [github.com/gobuffalo/envy](https://godoc.org/github.com/gobuffalo/envy)
* [github.com/gobuffalo/fizz](https://godoc.org/github.com/gobuffalo/fizz)
* [github.com/gobuffalo/flect](https://godoc.org/github.com/gobuffalo/flect)
* [github.com/gobuffalo/genny/v2](https://godoc.org/github.com/gobuffalo/genny/v2)
* [github.com/gobuffalo/github_flavored_markdown](https://godoc.org/github.com/gobuffalo/github_flavored_markdown)
* [github.com/gobuffalo/helpers](https://godoc.org/github.com/gobuffalo/helpers)
* [github.com/gobuffalo/logger](https://godoc.org/github.com/gobuffalo/logger)
* [github.com/gobuffalo/nulls](https://godoc.org/github.com/gobuffalo/nulls)
* [github.com/gobuffalo/packd](https://godoc.org/github.com/gobuffalo/packd)
* [github.com/gobuffalo/plush/v4](https://godoc.org/github.com/gobuffalo/plush/v4)
* [github.com/gobuffalo/tags/v3](https://godoc.org/github.com/gobuffalo/tags/v3)
* [github.com/gobuffalo/validate/v3](https://godoc.org/github.com/gobuffalo/validate/v3)
* [github.com/gofrs/uuid](https://godoc.org/github.com/gofrs/uuid)
* [github.com/google/renameio](https://godoc.org/github.com/google/renameio)
* [github.com/gorilla/css](https://godoc.org/github.com/gorilla/css)
* [github.com/inconshreveable/mousetrap](https://godoc.org/github.com/inconshreveable/mousetrap)
* [github.com/jackc/chunkreader](https://godoc.org/github.com/jackc/chunkreader)
* [github.com/jackc/chunkreader/v2](https://godoc.org/github.com/jackc/chunkreader/v2)
* [github.com/jackc/pgconn](https://godoc.org/github.com/jackc/pgconn)
* [github.com/jackc/pgio](https://godoc.org/github.com/jackc/pgio)
* [github.com/jackc/pgmock](https://godoc.org/github.com/jackc/pgmock)
* [github.com/jackc/pgpassfile](https://godoc.org/github.com/jackc/pgpassfile)
* [github.com/jackc/pgproto3](https://godoc.org/github.com/jackc/pgproto3)
* [github.com/jackc/pgproto3/v2](https://godoc.org/github.com/jackc/pgproto3/v2)
* [github.com/jackc/pgservicefile](https://godoc.org/github.com/jackc/pgservicefile)
* [github.com/jackc/pgtype](https://godoc.org/github.com/jackc/pgtype)
* [github.com/jackc/pgx/v4](https://godoc.org/github.com/jackc/pgx/v4)
* [github.com/jackc/puddle](https://godoc.org/github.com/jackc/puddle)
* [github.com/jmoiron/sqlx](https://godoc.org/github.com/jmoiron/sqlx)
* [github.com/joho/godotenv](https://godoc.org/github.com/joho/godotenv)
* [github.com/kballard/go-shellquote](https://godoc.org/github.com/kballard/go-shellquote)
* [github.com/kisielk/gotool](https://godoc.org/github.com/kisielk/gotool)
* [github.com/konsorten/go-windows-terminal-sequences](https://godoc.org/github.com/konsorten/go-windows-terminal-sequences)
* [github.com/kr/pretty](https://godoc.org/github.com/kr/pretty)
* [github.com/kr/pty](https://godoc.org/github.com/kr/pty)
* [github.com/kr/text](https://godoc.org/github.com/kr/text)
* [github.com/lib/pq](https://godoc.org/github.com/lib/pq)
* [github.com/luna-duclos/instrumentedsql](https://godoc.org/github.com/luna-duclos/instrumentedsql)
* [github.com/mattn/go-colorable](https://godoc.org/github.com/mattn/go-colorable)
* [github.com/mattn/go-isatty](https://godoc.org/github.com/mattn/go-isatty)
* [github.com/mattn/go-sqlite3](https://godoc.org/github.com/mattn/go-sqlite3)
* [github.com/microcosm-cc/bluemonday](https://godoc.org/github.com/microcosm-cc/bluemonday)
* [github.com/pkg/diff](https://godoc.org/github.com/pkg/diff)
* [github.com/pkg/errors](https://godoc.org/github.com/pkg/errors)
* [github.com/pmezard/go-difflib](https://godoc.org/github.com/pmezard/go-difflib)
* [github.com/rogpeppe/go-internal](https://godoc.org/github.com/rogpeppe/go-internal)
* [github.com/rs/xid](https://godoc.org/github.com/rs/xid)
* [github.com/rs/zerolog](https://godoc.org/github.com/rs/zerolog)
* [github.com/russross/blackfriday/v2](https://godoc.org/github.com/russross/blackfriday/v2)
* [github.com/satori/go.uuid](https://godoc.org/github.com/satori/go.uuid)
* [github.com/sergi/go-diff](https://godoc.org/github.com/sergi/go-diff)
* [github.com/shopspring/decimal](https://godoc.org/github.com/shopspring/decimal)
* [github.com/sirupsen/logrus](https://godoc.org/github.com/sirupsen/logrus)
* [github.com/sourcegraph/annotate](https://godoc.org/github.com/sourcegraph/annotate)
* [github.com/sourcegraph/syntaxhighlight](https://godoc.org/github.com/sourcegraph/syntaxhighlight)
* [github.com/spf13/cobra](https://godoc.org/github.com/spf13/cobra)
* [github.com/spf13/pflag](https://godoc.org/github.com/spf13/pflag)
* [github.com/stretchr/objx](https://godoc.org/github.com/stretchr/objx)
* [github.com/stretchr/testify](https://godoc.org/github.com/stretchr/testify)
* [github.com/yuin/goldmark](https://godoc.org/github.com/yuin/goldmark)
* [github.com/zenazn/goji](https://godoc.org/github.com/zenazn/goji)
* [go.uber.org/atomic](https://godoc.org/go.uber.org/atomic)
* [go.uber.org/multierr](https://godoc.org/go.uber.org/multierr)
* [go.uber.org/tools](https://godoc.org/go.uber.org/tools)
* [go.uber.org/zap](https://godoc.org/go.uber.org/zap)
* [golang.org/x/crypto](https://godoc.org/golang.org/x/crypto)
* [golang.org/x/lint](https://godoc.org/golang.org/x/lint)
* [golang.org/x/mod](https://godoc.org/golang.org/x/mod)
* [golang.org/x/net](https://godoc.org/golang.org/x/net)
* [golang.org/x/sync](https://godoc.org/golang.org/x/sync)
* [golang.org/x/sys](https://godoc.org/golang.org/x/sys)
* [golang.org/x/term](https://godoc.org/golang.org/x/term)
* [golang.org/x/text](https://godoc.org/golang.org/x/text)
* [golang.org/x/tools](https://godoc.org/golang.org/x/tools)
* [golang.org/x/xerrors](https://godoc.org/golang.org/x/xerrors)
* [gopkg.in/check.v1](https://godoc.org/gopkg.in/check.v1)
* [gopkg.in/errgo.v2](https://godoc.org/gopkg.in/errgo.v2)
* [gopkg.in/inconshreveable/log15.v2](https://godoc.org/gopkg.in/inconshreveable/log15.v2)
* [gopkg.in/yaml.v2](https://godoc.org/gopkg.in/yaml.v2)
* [gopkg.in/yaml.v3](https://godoc.org/gopkg.in/yaml.v3)
* [honnef.co/go/tools](https://godoc.org/honnef.co/go/tools)
