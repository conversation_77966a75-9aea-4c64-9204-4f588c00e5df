import numpy as np
from scipy.spatial.transform import Rotation as R
import math

'''
用于管理4个鱼眼，7个行车摄像头的内外参数
'''
class Transform:
    def __init__(self):
        self.is_fisheye = False
        self.trans = np.zeros((3, 3))
        self.tvec = np.zeros((3, 1))
        
        self.intrinsic_mat = np.zeros((3, 3))
        self.extrinsic_mat = np.zeros((4, 4))

        self.r_yxz1 = np.zeros((4, 4))
        self.r_yxz1[0][1] = -1
        self.r_yxz1[1][2] = -1
        self.r_yxz1[2][0] = 1
        self.r_yxz1[3][3] = 1
        # r_yxz  车身坐标系到相机坐标系
        # [[ 0. -1.  0.]    相机坐标系X   <=   车身坐标系-Y
        # [ 0.  0. -1.]    相机坐标系Y   <=   车身坐标系-Z
        # [ 1.  0.  0.]]    相机坐标系Z   <=   车身坐标系X

        self.height = 0
        self.width = 0

        # k1, k2, p1, p2, k3, k4, k5, k6, s1, s2, s3, s4 标准畸变参数
        # k1, k2, k3, k4 鱼眼畸变参数
        self.K = [0 for i in range(12)]

    def SetIntrinsicMat(self, instrinsic):
        self.intrinsic_mat = np.array(instrinsic).reshape(3, 3)

    def SetExtrinsic(self, pos=None, pitch_yaw_roll=None, extrinsic_mat=None):
        if not pitch_yaw_roll is None:
            su = np.sin(pitch_yaw_roll[2] * np.pi / 180)
            cu = np.cos(pitch_yaw_roll[2] * np.pi / 180)

            sv = np.sin(pitch_yaw_roll[0] * np.pi / 180)
            cv = np.cos(pitch_yaw_roll[0] * np.pi / 180)

            sw = np.sin(pitch_yaw_roll[1] * np.pi / 180)
            cw = np.cos(pitch_yaw_roll[1] * np.pi / 180)
            vcsgnd2img = [cv * cw, su * sv * cw -cu * sw, su * sw + cu * sv * cw, \
                cv * sw, cu * cw + su * sv *sw, cu * sv * sw -su * cw, \
                -sv, su * cv, cu * cv]
            self.trans = np.array(vcsgnd2img).reshape(3, 3).T
            self.tvec[:3, 0] = -1 * np.array(pos[0:3])

            self.trans = self.r_yxz1[:3, :3].dot(self.trans)
            self.tvec = self.trans[:3, :3].dot(self.tvec)

            self.extrinsic_mat = np.vstack(
                (np.hstack((self.trans, self.tvec)),
                np.array([[0, 0, 0, 1]]))
            )
        elif not extrinsic_mat is None:
            self.extrinsic_mat = np.array(extrinsic_mat).reshape((4, 4))
        return True
    
    def rot_to_euler(self, R, degree_mode=1):
        sy = np.sqrt(R[0,0] * R[0,0] +  R[1,0] * R[1,0])
        singular = sy < 1e-6
        if not singular :
            roll = np.arctan2(R[2,1] , R[2,2])
            pitch = np.arctan2(-R[2,0], sy)
            yaw = np.arctan2(R[1,0], R[0,0])
        else :
            roll = np.arctan2(-R[1,2], R[1,1])
            pitch = np.arctan2(-R[2,0], sy)
            yaw = 0
        # degree_mode=1:【输出】是角度制，否则弧度制
        if degree_mode == 1:
            roll = np.rad2deg(roll)
            pitch = np.rad2deg(pitch)
            yaw = np.rad2deg(yaw)
        euler = np.array([roll, pitch, yaw])
        return euler

    def euler_to_rot(self, euler, degree_mode=1):
        roll, pitch, yaw = euler
        # degree_mode=1:【输入】是角度制，否则弧度制
        if degree_mode == 1:
            roll = np.deg2rad(roll)
            pitch = np.deg2rad(pitch)
            yaw = np.deg2rad(yaw)
        R_x = np.array([
            [1,     0,              0              ],
            [0,     math.cos(roll), -math.sin(roll)],
            [0,     math.sin(roll), math.cos(roll) ]
            ])
        R_y = np.array([
            [math.cos(pitch),  0,   math.sin(pitch) ],
            [0,                1,   0               ],
            [-math.sin(pitch), 0,   math.cos(pitch) ]
            ])
        R_z = np.array([
            [math.cos(yaw), -math.sin(yaw),  0],
            [math.sin(yaw), math.cos(yaw),   0],
            [0,             0,               1]
            ])
        R = np.dot(R_z, np.dot( R_y, R_x ))
        return R
    
    def quaternion_to_rot(w, x, y, z):
        return Quaternion(w, x, y, z).rotation_matrix

    def SetExtrinsic2(self, pos=[0, 0, 0], rotation=None):
        self.tvec[:3, 0] = np.array(pos[0:3])
        self.trans = rotation
        self.extrinsic_mat = np.vstack(
                (np.hstack((rotation, self.tvec)),
                np.array([[0, 0, 0, 1]]))
            )

    def AddRoll(self, degree):
        rpy = self.rot_to_euler(self.extrinsic_mat.copy())
        rpy[0] += degree
        extrinsic_mat = self.euler_to_rot(rpy)
        self.extrinsic_mat[:3, :3] = extrinsic_mat
        self.trans = extrinsic_mat

    def AddPitch(self, degree):
        rpy = self.rot_to_euler(self.extrinsic_mat.copy())
        rpy[1] += degree
        extrinsic_mat = self.euler_to_rot(rpy)
        self.extrinsic_mat[:3, :3] = extrinsic_mat
        self.trans = extrinsic_mat

    def AddYaw(self, degree):
        rpy = self.rot_to_euler(self.extrinsic_mat.copy())
        rpy[2] += degree
        extrinsic_mat = self.euler_to_rot(rpy)
        self.extrinsic_mat[:3, :3] = extrinsic_mat
        self.trans = extrinsic_mat
    
    def AddTvec(self, offset):
        self.tvec[0] += offset[0]
        self.tvec[1] += offset[1]
        self.tvec[2] += offset[2]
        self.info()

    def SetImageWidth(self, width):
        self.width = width

    def SetImageHeight(self, height):
        self.height = height

    def SetDistortion(self, distortion):
        for id in range(len(distortion)):
            self.K[id] = distortion[id]
    
    def SetImageScale(self, image_scale):
        self.image_scale = image_scale

    def GetTransform(self):
        return self.trans
    
    def GetTvec(self):
        return self.tvec

    def GetDistortion(self):
        return self.K

    def GetIntrinsicMat(self):
        return self.intrinsic_mat
    
    def GetExtrinsicMat(self):
        return self.extrinsic_mat

    def AddDistortion(self, camera_model, point):
        pt2d = np.array([point[0] / point[2], point[1] / point[2]])
        r2 = pt2d.dot(pt2d)

        if camera_model == 'fisheye':
            r = math.sqrt(r2)

            if point[2] > 0:
                theta = math.atan(r)
            elif point[2] == 0:
                theta = math.pi / 2
            else:
                theta = math.pi - math.atan(r)

            theta_d = theta + self.K[0] * math.pow(theta, 3) + self.K[1] * math.pow(theta, 5) +  \
                        self.K[2] * math.pow(theta, 7) +  self.K[3] * math.pow(theta, 9)

            pt2d_distort = [(theta_d / r) * pt2d[0] * self.intrinsic_mat[0][0] + self.intrinsic_mat[0][2],
                                (theta_d / r) * pt2d[1] * self.intrinsic_mat[1][1] + self.intrinsic_mat[1][2]]

        elif camera_model == 'pinhole':
            pt2d_distort = [0, 0]
            radial_ratio = (1 + self.K[0] * r2 + self.K[1] * math.pow(r2, 2) +  self.K[4] * math.pow(r2, 3)) / \
                            (1 + self.K[5] * r2 + self.K[6] * math.pow(r2, 2) + self.K[7] * math.pow(r2, 3))

            pt2d_distort[0] = pt2d[0] * radial_ratio + 2 * self.K[2] * pt2d[0] * pt2d[1] + \
                                self.K[3] * (r2 + 2 * math.pow(pt2d[0], 2)) + \
                                self.K[8] * r2 + self.K[9] * math.pow(r2, 2)

            pt2d_distort[1] = pt2d[1] * radial_ratio + 2 * self.K[2] * (r2 + 2 * math.pow(pt2d[1], 2)) + \
                                self.K[3] * pt2d[0] * pt2d[1] + self.K[10] * r2 + self.K[11] * math.pow(r2, 2)

            pt2d_distort[0] = pt2d_distort[0] * self.intrinsic_mat[0][0] +  self.intrinsic_mat[0][2]
            pt2d_distort[1] = pt2d_distort[1] * self.intrinsic_mat[1][1] +  self.intrinsic_mat[1][2]

        elif camera_model == 'null':
            # 下面是不加畸变, 可以用于查看畸变参数是否正确，通常情况下加入点的畸变，点云和图像的重合度会更加高
            pt2d_distort = [pt2d[0] * self.intrinsic_mat[0][0] + self.intrinsic_mat[0][2], 
                                pt2d[1] * self.intrinsic_mat[1][1] + self.intrinsic_mat[1][2]]
            
        else:
            print('{} is unknown camera model'.format(camera_model))

        return pt2d_distort
