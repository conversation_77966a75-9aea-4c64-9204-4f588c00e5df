package service

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"iam/internal/biz"

	"gitlab.rp.konvery.work/platform/apis/iam/v1"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/jwt"
	"gitlab.rp.konvery.work/platform/pkg/kutil"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (o *UsersService) AssumeUser(ctx context.Context, req *iam.AssumeUserRequest) (*iam.LoginReply, error) {
	user, assumeBy, err := o.getAssumeUsers(ctx, req)
	if err != nil {
		return nil, err
	}
	// grant access token
	token, expires, err := jwt.Default().New(user.GetUid(), jwt.WithAssumeBy(assumeBy.GetUid()))
	if err != nil {
		return nil, err
	}
	o.setCookie(ctx, token, user)

	feperm, err := o.getFeperm(ctx, user)
	if err != nil {
		return nil, err
	}

	clearSysInfo(ctx, user, assumeBy)
	return &iam.LoginReply{
		User:       FromBizUser(user),
		Token:      token,
		ExpireTime: timestamppb.New(expires),
		Feperm:     feperm,
		AssumeBy:   FromBizUser(assumeBy),
	}, nil
}

func (o *UsersService) getAssumeUsers(ctx context.Context, req *iam.AssumeUserRequest) (
	user, assumeBy *biz.User, err error) {
	fmt.Println("---> assumeUser - getAssumeUsers req: ", req)
	if req.Uid == "identity" {
		typ, id, err := parseUserIdentity(req.Identity)
		if err != nil {
			return nil, nil, err
		}
		_, u, err := o.bz.GetBy(ctx, typ+":"+id)
		if err != nil {
			return nil, nil, err
		}
		req.Uid = u.GetUid()
	}
	fmt.Println("---> getAssumeUsers req new: ", req)
	op := biz.UserFromCtx(ctx)
	assumeBy = op.RealUser()
	if req.Uid == "me" || req.Uid == assumeBy.GetUid() {
		return assumeBy, nil, nil
	}
	if req.Uid == op.GetUid() {
		return op.User, op.AssumeBy, nil
	}

	if assumeBy.Role == biz.TeamRoleMember {
		return nil, nil, errors.NewErrForbidden()
	}
	user, err = o.bz.Get(ctx, req.Uid)
	if err != nil {
		return nil, nil, err
	}
	if !o.canAssume(ctx, user, assumeBy) {
		return nil, nil, errors.NewErrForbidden()
	}
	return
}

var uidRegx = regexp.MustCompile(`^[a-z3-8]{11}$`)
var chinaMobileRegx = regexp.MustCompile(`^1[3-9]\d{9}$`)
var fakeMobileRegx = regexp.MustCompile(`^0[a-z0-9]{3,15}$`)
var mobileRegx = regexp.MustCompile(`^\+[1-9]\d{3,15}$`)

func parseUserIdentity(s string) (idtyp, identity string, err error) {
	switch {
	case len(s) == 11 && uidRegx.MatchString(s):
		return biz.IDTypeUid, s, nil
	case len(s) == 11 && chinaMobileRegx.MatchString(s):
		return biz.IDTypePhone, "+86" + s, nil
	case len(s) == 13 && strings.HasPrefix(s, "86") && chinaMobileRegx.MatchString(s[2:]):
		return biz.IDTypePhone, "+" + s, nil
	case len(s) >= 4 && len(s) <= 16 && s[0] == '0' && fakeMobileRegx.MatchString(s):
		return biz.IDTypePhone, "+" + s, nil
	case len(s) >= 4 && len(s) <= 16 && s[0] == '+' &&
		(s[1] == '0' && fakeMobileRegx.MatchString(s[1:]) ||
			strings.HasPrefix(s[1:], "86") && chinaMobileRegx.MatchString(s[3:]) ||
			!strings.HasPrefix(s[1:], "86") && mobileRegx.MatchString(s)):
		return biz.IDTypePhone, s, nil
	case kutil.ValidateEmail(s) == nil:
		return biz.IDTypeEmail, s, nil
	}
	return "", "", errors.NewErrInvalidField(errors.WithFields("identity"))
}

// TODO: add unit test
func (o *UsersService) canAssume(ctx context.Context, user, assumeBy *biz.User) bool {
	if assumeBy.IsPrivileged() {
		return assumeBy.Role == biz.SysRoleRoot && user.Role != biz.SysRoleRoot || !user.IsPrivileged()
	}
	switch assumeBy.Role {
	case biz.TeamRoleOwner:
		return user.OrgID == assumeBy.OrgID
	case biz.TeamRoleManager:
		return user.OrgID == assumeBy.OrgID && user.Role == biz.TeamRoleMember
	case biz.SysRoleKAM:
		g, _, err := o.grantbz.List(ctx, &biz.BizgrantFilter{GranteeID: assumeBy.ID, OrgID: user.OrgID},
			biz.Pager{Pagesz: 1})
		if err != nil {
			o.log.Error(ctx, "failed to query bizgrant", err)
		}
		return len(g) > 0
	}
	return false
}

func (o *UsersService) RefreshToken(ctx context.Context, req *emptypb.Empty) (*iam.LoginReply, error) {
	// renew the token
	op := biz.UserFromCtx(ctx)
	token, expires, err := jwt.Default().New(op.GetUid(), jwt.WithAssumeBy(op.AssumeBy.GetUid()))
	if err != nil {
		return nil, err
	}

	o.setCookie(ctx, token, op.User)
	clearSysInfo(ctx, op.User, op.AssumeBy)
	return &iam.LoginReply{
		User:       FromBizUser(op.User),
		Token:      token,
		ExpireTime: timestamppb.New(expires),
		AssumeBy:   FromBizUser(op.AssumeBy),
	}, nil
}
