package client

import (
	"anno/internal/conf"

	"gitlab.rp.konvery.work/platform/apis/annofeed/v1"
	"gitlab.rp.konvery.work/platform/apis/client"
)

const (
	DataStateReady = client.DataStateReady
)

type (
	CreateDataRequest = annofeed.CreateDataRequest
)

func initAnnofeed(c *conf.Bootstrap) {
	svc := &client.Service{Addr: c.Rpc.Annofeed.Addr}
	if c.Rpc.Annofeed.Timeout != nil {
		svc.Timeout = c.Rpc.Annofeed.Timeout.AsDuration()
	}
	client.InitAnnofeed(svc)
}

var (
	CreateData          = client.CreateData
	GetData             = client.GetData
	GetDataElements     = client.GetDataElements
	FindDataElements    = client.FindDataElements
	SetRawdataEmbedding = client.SetRawdataEmbedding

	GetDataValidationSummary = client.GetDataValidationSummary
)
