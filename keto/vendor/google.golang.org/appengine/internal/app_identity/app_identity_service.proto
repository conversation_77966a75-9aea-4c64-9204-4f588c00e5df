syntax = "proto2";
option go_package = "app_identity";

package appengine;

message AppIdentityServiceError {
  enum ErrorCode {
    SUCCESS = 0;
    UNKNOWN_SCOPE = 9;
    BLOB_TOO_LARGE = 1000;
    DEADLINE_EXCEEDED = 1001;
    NOT_A_VALID_APP = 1002;
    UNKNOWN_ERROR = 1003;
    NOT_ALLOWED = 1005;
    NOT_IMPLEMENTED = 1006;
  }
}

message SignForAppRequest {
  optional bytes bytes_to_sign = 1;
}

message SignForAppResponse {
  optional string key_name = 1;
  optional bytes signature_bytes = 2;
}

message GetPublicCertificateForAppRequest {
}

message PublicCertificate {
  optional string key_name = 1;
  optional string x509_certificate_pem = 2;
}

message GetPublicCertificateForAppResponse {
  repeated PublicCertificate public_certificate_list = 1;
  optional int64 max_client_cache_time_in_second = 2;
}

message GetServiceAccountNameRequest {
}

message GetServiceAccountNameResponse {
  optional string service_account_name = 1;
}

message GetAccessTokenRequest {
  repeated string scope = 1;
  optional int64 service_account_id = 2;
  optional string service_account_name = 3;
}

message GetAccessTokenResponse {
  optional string access_token = 1;
  optional int64 expiration_time = 2;
}

message GetDefaultGcsBucketNameRequest {
}

message GetDefaultGcsBucketNameResponse {
  optional string default_gcs_bucket_name = 1;
}
