BEGIN;

CREATE TABLE jobelems
(
    id            BIGSERIAL    NOT NULL PRIMARY KEY,
    lot_id        BIGINT       NOT NULL,
    job_id        BIGINT       NOT NULL,
    data_uid      VARCHAR(32)  NOT NULL,
    elem_idx      INTEGER      NOT NULL DEFAULT 0,
    elem_name     VARCHAR(256) NOT NULL,
    created_at    TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_jobelems_lot_id_elem_name_job_id ON jobelems (lot_id, elem_name, job_id);
CREATE INDEX idx_jobelems_lot_id_elem_idx_job_id ON jobelems (lot_id, elem_idx, job_id);
CREATE INDEX idx_jobelems_data_uid_elem_name_job_id ON jobelems (data_uid, elem_name, job_id);
CREATE INDEX idx_jobelems_data_uid_elem_idx_job_id ON jobelems (data_uid, elem_idx, job_id);

COMMIT;
