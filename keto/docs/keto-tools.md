# Keto 权限规则管理工具说明

本文档描述了用于管理 Keto 权限规则的命令行工具集。这些工具通过 `ketow.sh` 脚本提供，简化了与 Keto 服务器的交互。

## 工具概览

脚本提供了以下主要功能：

1. `ketow` - Keto CLI 命令的包装函数
2. `parse_tuple` - 解析关系元组的辅助函数
3. `tpget` - 获取关系元组
4. `tpcheck` - 检查权限
5. `tpexpand` - 展开关系
6. `tpadd` - 添加关系元组
7. `tpdel` - 删除关系元组

## 关系元组格式

所有工具使用统一的格式来表示关系元组：
```
namespace:object#relation@subject
```

其中：
- `namespace` - 命名空间
- `object` - 对象
- `relation` - 关系
- `subject` - 主体

## 使用示例

### 1. 检查权限
```bash
tpcheck "namespace:object#relation@subject"
```

### 2. 添加权限
```bash
tpadd "namespace:object#relation@subject"
```

### 3. 删除权限
```bash
tpdel "namespace:object#relation@subject"
```

### 4. 获取权限
```bash
tpget "namespace:object#relation@subject"
```

### 5. 展开关系
```bash
tpexpand "namespace:object#relation"
```

## 实际应用示例

```bash
# 添加一个权限：用户 alice 可以查看文档 doc1
tpadd "document:doc1#view@user:alice"

# 检查用户 bob 是否可以编辑文档 doc2
tpcheck "document:doc2#edit@user:bob"

# 删除用户 charlie 对文档 doc3 的删除权限
tpdel "document:doc3#delete@user:charlie"
```

## 技术说明

1. 这些工具通过 Keto 的 gRPC API 与服务器通信
2. 使用 `--insecure-disable-transport-security` 和 `--insecure-skip-hostname-verification` 选项来简化开发环境中的使用
3. 工具会自动处理与 Keto 服务器的通信细节

## 主要优点

1. 提供了简单的命令行接口来管理权限
2. 统一了关系元组的格式
3. 自动处理了与 Keto 服务器的通信
4. 提供了权限检查、添加、删除等基本操作

这些工具可以帮助开发人员和管理员更方便地管理 Keto 中的权限规则。 