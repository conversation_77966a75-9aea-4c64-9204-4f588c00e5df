package main

import (
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strings"

	"apidocs/util"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

const filelistTpl = "filelist.tpl"

var viewerTpl = "viewer.tpl"

type APIDocParams struct {
	App string `uri:"app"` // whose API to upload
	Rev string `uri:"rev"` // API revision
}

var regexpApp = regexp.MustCompile(`[\w-.]+`)

func (o *APIDocParams) Validate() error {
	app := o.App
	if ok := regexpApp.MatchString(app); !ok {
		return fmt.Errorf("invalid app name: " + app)
	}
	rev := o.Rev
	if ok := regexpApp.MatchString(rev); !ok {
		return fmt.Errorf("invalid revision: " + rev)
	}
	return nil
}

type Error struct {
	Status  int
	Cause   error
	Message string
}

func NewError(status int, message string, cause error) *Error {
	return &Error{
		Status:  status,
		Cause:   cause,
		Message: message,
	}
}

func (o *Error) Error() string {
	return o.Message + ": " + o.Cause.Error()
}

func wrap(handler func(c *gin.Context) error) func(c *gin.Context) {
	return func(c *gin.Context) {
		err := handler(c)
		if err != nil {
			var e *Error
			status := http.StatusInternalServerError
			if errors.As(err, &e) {
				if e.Message != "" || e.Cause != nil {
					log.Println(e.Message+":", e.Cause)
				}
				status = e.Status
			}
			c.AbortWithStatus(status)
		}
	}
}

type httpHandler struct {
	assets    string
	viewerTpl string
	apiPrefix string
	docsHash  *DocsHash
}

// curl -X POST -T <path-to-local-file> http://localhost:8888/docs/:app/:rev?set-current=true
func (o *httpHandler) uploadAPIDoc(c *gin.Context) error {
	params := &APIDocParams{}
	err := c.BindUri(params)
	if err != nil {
		return NewError(http.StatusBadRequest, "failed to parse upload params", err)
	}
	aliases := c.QueryArray("alias")

	err = params.Validate()
	if err != nil {
		return NewError(http.StatusBadRequest, "invalid upload params", err)
	}

	dir := filepath.Join(o.assets, params.App)
	if err := os.MkdirAll(dir, 0750); err != nil {
		return NewError(http.StatusInternalServerError, "Failed to create dir "+dir, err)
	}

	// save the doc
	log.Printf("receive a API doc: %+v", params)
	path := filepath.Join(dir, params.Rev)
	if util.IsFileExist(path) {
		return NewError(http.StatusBadRequest, fmt.Sprintf("API doc already exist: %v@%v", params.App, params.Rev), err)
	}
	of, err := os.Create(path)
	if err != nil {
		return NewError(http.StatusInternalServerError, "Failed to create file "+path, err)
	}
	defer of.Close()
	_, err = io.Copy(of, c.Request.Body)
	if err != nil {
		return NewError(http.StatusInternalServerError, "Failed to save file "+path, err)
	}

	// link to an existing doc if their contents are the same
	orev, err := o.docsHash.AddOrMatchDoc(params.App, params.Rev)
	if err != nil {
		return NewError(http.StatusInternalServerError, "failed to add doc", err)
	}
	if orev != "" {
		os.Remove(path)
		err = os.Symlink(orev, path)
		if err != nil {
			return NewError(http.StatusInternalServerError, fmt.Sprintf("Failed to link %v to %v", path, orev), err)
		}
	}

	// set aliases
	err = o.createAliases(params.App, params.Rev, aliases)
	if err != nil {
		return err
	}
	c.String(http.StatusOK, fmt.Sprintf("%v@%v is accepted!\n", params.App, params.Rev))
	return nil
}

func (o *httpHandler) createAliases(app, rev string, aliases []string) error {
	dir := filepath.Join(o.assets, app)
	for _, alias := range aliases {
		link := filepath.Join(dir, alias)
		fi, _ := os.Lstat(link)
		if fi != nil && fi.Mode()&os.ModeSymlink == 0 {
			return NewError(http.StatusBadRequest, fmt.Sprintf("alias %v is not a symbol link in %v", alias, app), nil)
		}

		os.Remove(link)
		err := os.Symlink(rev, link)
		if err != nil {
			return NewError(http.StatusInternalServerError, fmt.Sprintf("failed to link %v to %v in %v", alias, rev, app), err)
		}
	}
	return nil
}

func (o *httpHandler) deleteAPIDoc(c *gin.Context) error {
	params := &APIDocParams{}
	err := c.BindUri(params)
	if err != nil {
		return NewError(http.StatusBadRequest, "failed to parse params", err)
	}

	path := filepath.Join(o.assets, params.App, params.Rev)
	if err := os.Remove(path); err != nil && !errors.Is(err, os.ErrNotExist) {
		return NewError(http.StatusInternalServerError, "failed to remove file "+path, err)
	}
	c.String(http.StatusNoContent, "")
	return nil
}

func (o *httpHandler) setAPIDocAlias(c *gin.Context) error {
	params := &APIDocParams{}
	err := c.BindUri(params)
	if err != nil {
		return NewError(http.StatusBadRequest, "failed to parse params", err)
	}
	aliases := c.QueryArray("alias")

	path := filepath.Join(o.assets, params.App, params.Rev)
	if !util.IsFileExist(path) {
		return NewError(http.StatusNotFound, fmt.Sprintf("API doc %v not exist", path), nil)
	}
	err = o.createAliases(params.App, params.Rev, aliases)
	if err != nil {
		return err
	}
	c.String(http.StatusNoContent, "")
	return nil
}

func (o *httpHandler) getAPIDoc(c *gin.Context) error {
	params := &APIDocParams{}
	err := c.BindUri(params)
	if err != nil {
		return NewError(http.StatusBadRequest, "failed to parse params", err)
	}

	path := filepath.Join(o.assets, params.App, params.Rev)
	if !util.IsFileExist(path) {
		return NewError(http.StatusNotFound, fmt.Sprintf("API doc %v not exist", path), nil)
	}
	if lnk, err := os.Readlink(path); err == nil {
		c.Header("X-Symlink", lnk)
	}
	c.File(path)
	return nil
}

type DirParams struct {
	Dir string `uri:"dir"`
}

func (o *httpHandler) handleList(c *gin.Context) error {
	params := &DirParams{}
	err := c.BindUri(params)
	if err != nil {
		return NewError(http.StatusBadRequest, "failed to parse params", err)
	}
	fpath := filepath.Join(o.assets, params.Dir)
	if !util.IsDir(fpath) {
		c.File(fpath)
		return nil
	}
	prefix := strings.Trim(params.Dir, "/")
	files, err := o.listDir(fpath, prefix, false)
	if err != nil {
		return err
	}
	c.HTML(http.StatusOK, filelistTpl, map[string]any{"files": files})
	return nil
}

func (o *httpHandler) viewAPIDoc(c *gin.Context) error {
	params := &APIDocParams{}
	err := c.BindUri(params)
	if err != nil {
		return NewError(http.StatusBadRequest, "failed to parse upload params", err)
	}

	if params.App == "" || params.Rev == "" {
		dir := filepath.Join(o.assets, params.App)
		files, err := o.listDir(dir, params.App, true)
		if err != nil {
			return err
		}
		c.HTML(http.StatusOK, filelistTpl, map[string]any{"files": files})
		return nil
	}

	fpath := filepath.Join(o.assets, params.App, params.Rev)
	if !util.IsFileExist(fpath) {
		return NewError(http.StatusNotFound, fmt.Sprintf("API doc %v not exist", fpath), nil)
	}
	c.HTML(http.StatusOK, viewerTpl, map[string]any{"docUrl": path.Join(o.apiPrefix, apidocPrefix, params.App, params.Rev)})
	return nil
}

func (o *httpHandler) listDir(dir, prefix string, skipDotFiles bool) (map[string]string, error) {
	files, err := util.ReadDir(dir)
	if err != nil {
		return nil, NewError(http.StatusInternalServerError, "Failed to read dir "+dir, err)
	}
	files = lo.Filter(files, func(v []string, _ int) bool {
		return v[0] != "lost+found" && (!skipDotFiles || !strings.HasPrefix(v[0], "."))
	})
	return lo.SliceToMap(files, func(file []string) (k, v string) {
		k = file[0]
		v = k
		if len(file) > 1 {
			v += " -> " + file[1]
		}
		if prefix != "" {
			k = path.Join(prefix, k)
		}
		return
	}), nil
}
