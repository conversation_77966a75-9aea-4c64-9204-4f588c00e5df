// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: iam/v1/role.proto

package iam

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Role struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the role
	// admins can create global roles; others can only create org-scope roles
	// role names should be unique within their scopes.
	// name of org-scope roles are in the format: org-uid.role-name
	// pattern: "^[\\w-]+(\\.[\\w-]+)?$"; length-in-bytes: [3, 30]
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// name displayed in UI; length-in-bytes: [3, 30]
	DisplayName string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// list of roles (IamRole:xxx) and permissions
	Perms []string `protobuf:"bytes,3,rep,name=perms,proto3" json:"perms,omitempty"`
}

func (x *Role) Reset() {
	*x = Role{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_role_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Role) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Role) ProtoMessage() {}

func (x *Role) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_role_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Role.ProtoReflect.Descriptor instead.
func (*Role) Descriptor() ([]byte, []int) {
	return file_iam_v1_role_proto_rawDescGZIP(), []int{0}
}

func (x *Role) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Role) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Role) GetPerms() []string {
	if x != nil {
		return x.Perms
	}
	return nil
}

type UpdateRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Action EditAction_Enum `protobuf:"varint,1,opt,name=action,proto3,enum=iam.v1.EditAction_Enum" json:"action,omitempty"`
	Name   string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// list of roles (IamRole:xxx) and permissions
	Perms []string `protobuf:"bytes,3,rep,name=perms,proto3" json:"perms,omitempty"`
}

func (x *UpdateRoleRequest) Reset() {
	*x = UpdateRoleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_role_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoleRequest) ProtoMessage() {}

func (x *UpdateRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_role_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoleRequest.ProtoReflect.Descriptor instead.
func (*UpdateRoleRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_role_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateRoleRequest) GetAction() EditAction_Enum {
	if x != nil {
		return x.Action
	}
	return EditAction_unspecified
}

func (x *UpdateRoleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateRoleRequest) GetPerms() []string {
	if x != nil {
		return x.Perms
	}
	return nil
}

type DeleteRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteRoleRequest) Reset() {
	*x = DeleteRoleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_role_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRoleRequest) ProtoMessage() {}

func (x *DeleteRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_role_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRoleRequest.ProtoReflect.Descriptor instead.
func (*DeleteRoleRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_role_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteRoleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetRoleRequest) Reset() {
	*x = GetRoleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_role_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoleRequest) ProtoMessage() {}

func (x *GetRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_role_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoleRequest.ProtoReflect.Descriptor instead.
func (*GetRoleRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_role_proto_rawDescGZIP(), []int{3}
}

func (x *GetRoleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetRoleReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// list of roles (IamRole:xxx) and permissions
	Perms []string `protobuf:"bytes,3,rep,name=perms,proto3" json:"perms,omitempty"`
}

func (x *GetRoleReply) Reset() {
	*x = GetRoleReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_role_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoleReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoleReply) ProtoMessage() {}

func (x *GetRoleReply) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_role_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoleReply.ProtoReflect.Descriptor instead.
func (*GetRoleReply) Descriptor() ([]byte, []int) {
	return file_iam_v1_role_proto_rawDescGZIP(), []int{4}
}

func (x *GetRoleReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetRoleReply) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *GetRoleReply) GetPerms() []string {
	if x != nil {
		return x.Perms
	}
	return nil
}

type ListRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagesz int32 `protobuf:"varint,1,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// An opaque pagination token returned from a previous call.
	// An empty token denotes the first page.
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// if not empty, only list roles created by this org
	OrgUid      string `protobuf:"bytes,3,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	NamePattern string `protobuf:"bytes,4,opt,name=name_pattern,json=namePattern,proto3" json:"name_pattern,omitempty"`
}

func (x *ListRoleRequest) Reset() {
	*x = ListRoleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_role_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRoleRequest) ProtoMessage() {}

func (x *ListRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_role_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRoleRequest.ProtoReflect.Descriptor instead.
func (*ListRoleRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_role_proto_rawDescGZIP(), []int{5}
}

func (x *ListRoleRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListRoleRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListRoleRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *ListRoleRequest) GetNamePattern() string {
	if x != nil {
		return x.NamePattern
	}
	return ""
}

type ListRoleReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// roles info without perms
	Roles         []*Role `protobuf:"bytes,1,rep,name=roles,proto3" json:"roles,omitempty"`
	NextPageToken string  `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListRoleReply) Reset() {
	*x = ListRoleReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_role_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRoleReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRoleReply) ProtoMessage() {}

func (x *ListRoleReply) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_role_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRoleReply.ProtoReflect.Descriptor instead.
func (*ListRoleReply) Descriptor() ([]byte, []int) {
	return file_iam_v1_role_proto_rawDescGZIP(), []int{6}
}

func (x *ListRoleReply) GetRoles() []*Role {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *ListRoleReply) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

type FepermItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamType Team_Type_Enum `protobuf:"varint,1,opt,name=team_type,json=teamType,proto3,enum=iam.v1.Team_Type_Enum" json:"team_type,omitempty"`
	Perm     *Feperm        `protobuf:"bytes,2,opt,name=perm,proto3" json:"perm,omitempty"`
}

func (x *FepermItem) Reset() {
	*x = FepermItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_role_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FepermItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FepermItem) ProtoMessage() {}

func (x *FepermItem) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_role_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FepermItem.ProtoReflect.Descriptor instead.
func (*FepermItem) Descriptor() ([]byte, []int) {
	return file_iam_v1_role_proto_rawDescGZIP(), []int{7}
}

func (x *FepermItem) GetTeamType() Team_Type_Enum {
	if x != nil {
		return x.TeamType
	}
	return Team_Type_unspecified
}

func (x *FepermItem) GetPerm() *Feperm {
	if x != nil {
		return x.Perm
	}
	return nil
}

type GetRoleFepermReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Perms []*FepermItem `protobuf:"bytes,1,rep,name=perms,proto3" json:"perms,omitempty"`
}

func (x *GetRoleFepermReply) Reset() {
	*x = GetRoleFepermReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_role_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoleFepermReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoleFepermReply) ProtoMessage() {}

func (x *GetRoleFepermReply) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_role_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoleFepermReply.ProtoReflect.Descriptor instead.
func (*GetRoleFepermReply) Descriptor() ([]byte, []int) {
	return file_iam_v1_role_proto_rawDescGZIP(), []int{8}
}

func (x *GetRoleFepermReply) GetPerms() []*FepermItem {
	if x != nil {
		return x.Perms
	}
	return nil
}

type SetRoleFepermRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role name
	Name  string        `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Perms []*FepermItem `protobuf:"bytes,2,rep,name=perms,proto3" json:"perms,omitempty"`
}

func (x *SetRoleFepermRequest) Reset() {
	*x = SetRoleFepermRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_role_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRoleFepermRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRoleFepermRequest) ProtoMessage() {}

func (x *SetRoleFepermRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_role_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRoleFepermRequest.ProtoReflect.Descriptor instead.
func (*SetRoleFepermRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_role_proto_rawDescGZIP(), []int{9}
}

func (x *SetRoleFepermRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SetRoleFepermRequest) GetPerms() []*FepermItem {
	if x != nil {
		return x.Perms
	}
	return nil
}

var File_iam_v1_role_proto protoreflect.FileDescriptor

var file_iam_v1_role_proto_rawDesc = []byte{
	0x0a, 0x11, 0x69, 0x61, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x69,
	0x61, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xbc, 0x01, 0x0a, 0x04, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x32, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72, 0x19, 0x20, 0x03,
	0x28, 0x1e, 0x32, 0x13, 0x5e, 0x5b, 0x5c, 0x77, 0x2d, 0x5d, 0x2b, 0x28, 0x5c, 0x2e, 0x5b, 0x5c,
	0x77, 0x2d, 0x5d, 0x2b, 0x29, 0x3f, 0x24, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a,
	0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x20, 0x03, 0x28, 0x1e, 0x52, 0x0b,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x05, 0x70,
	0x65, 0x72, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x28, 0xfa, 0x42, 0x25, 0x92,
	0x01, 0x22, 0x22, 0x20, 0x72, 0x1e, 0x20, 0x03, 0x28, 0x3c, 0x32, 0x18, 0x5e, 0x28, 0x49, 0x61,
	0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x3a, 0x29, 0x3f, 0x5c, 0x77, 0x2b, 0x28, 0x5c, 0x2e, 0x5c, 0x77,
	0x2b, 0x29, 0x2a, 0x24, 0x52, 0x05, 0x70, 0x65, 0x72, 0x6d, 0x73, 0x3a, 0x12, 0xba, 0x47, 0x0f,
	0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x05, 0x70, 0x65, 0x72, 0x6d, 0x73, 0x22,
	0xc1, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x64, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x05, 0x70, 0x65, 0x72, 0x6d, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x28, 0xfa, 0x42, 0x25, 0x92, 0x01, 0x22, 0x22, 0x20, 0x72,
	0x1e, 0x20, 0x03, 0x28, 0x3c, 0x32, 0x18, 0x5e, 0x28, 0x49, 0x61, 0x6d, 0x52, 0x6f, 0x6c, 0x65,
	0x3a, 0x29, 0x3f, 0x5c, 0x77, 0x2b, 0x28, 0x5c, 0x2e, 0x5c, 0x77, 0x2b, 0x29, 0x2a, 0x24, 0x52,
	0x05, 0x70, 0x65, 0x72, 0x6d, 0x73, 0x3a, 0x1b, 0xba, 0x47, 0x18, 0xba, 0x01, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0xba, 0x01, 0x05, 0x70, 0x65, 0x72, 0x6d, 0x73, 0xba, 0x01, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x27, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x24, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0x7e, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x65, 0x72,
	0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x70, 0x65, 0x72, 0x6d, 0x73, 0x3a,
	0x21, 0xba, 0x47, 0x1e, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x0c, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x05, 0x70, 0x65, 0x72,
	0x6d, 0x73, 0x22, 0x8f, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x64, 0x28,
	0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f,
	0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x74,
	0x74, 0x65, 0x72, 0x6e, 0x22, 0x7a, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f, 0x6c, 0x65,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x22, 0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f,
	0x6c, 0x65, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x3a, 0x1d, 0xba, 0x47, 0x1a, 0xba, 0x01, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0xba, 0x01,
	0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x22, 0x65, 0x0a, 0x0a, 0x46, 0x65, 0x70, 0x65, 0x72, 0x6d, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x33,
	0x0a, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x74, 0x65, 0x61, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x70, 0x65, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x70, 0x65, 0x72,
	0x6d, 0x52, 0x04, 0x70, 0x65, 0x72, 0x6d, 0x22, 0x3e, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x52, 0x6f,
	0x6c, 0x65, 0x46, 0x65, 0x70, 0x65, 0x72, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x28, 0x0a,
	0x05, 0x70, 0x65, 0x72, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x70, 0x65, 0x72, 0x6d, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x05, 0x70, 0x65, 0x72, 0x6d, 0x73, 0x22, 0x54, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x52, 0x6f,
	0x6c, 0x65, 0x46, 0x65, 0x70, 0x65, 0x72, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x05, 0x70, 0x65, 0x72, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x70, 0x65,
	0x72, 0x6d, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x70, 0x65, 0x72, 0x6d, 0x73, 0x32, 0xf3, 0x04,
	0x0a, 0x05, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x3e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x0c, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x6f, 0x6c, 0x65, 0x1a, 0x0c, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c,
	0x65, 0x22, 0x14, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0e, 0x3a, 0x01, 0x2a, 0x22, 0x09, 0x2f, 0x76,
	0x31, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x5c, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x19, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15,
	0x3a, 0x01, 0x2a, 0x32, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x2f, 0x7b,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x59, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52,
	0x6f, 0x6c, 0x65, 0x12, 0x19, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x2a, 0x10,
	0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x12, 0x51, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x16, 0x2e, 0x69, 0x61,
	0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x12, 0x12, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x2f, 0x7b, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x12, 0x4d, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x12,
	0x17, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x11, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0b, 0x12, 0x09, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f, 0x6c,
	0x65, 0x73, 0x12, 0x64, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x46, 0x65, 0x70,
	0x65, 0x72, 0x6d, 0x12, 0x16, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x69, 0x61,
	0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x46, 0x65, 0x70, 0x65,
	0x72, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x12,
	0x17, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x66, 0x65, 0x70, 0x65, 0x72, 0x6d, 0x12, 0x69, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x52,
	0x6f, 0x6c, 0x65, 0x46, 0x65, 0x70, 0x65, 0x72, 0x6d, 0x12, 0x1c, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x46, 0x65, 0x70, 0x65, 0x72, 0x6d,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x1a, 0x17, 0x2f, 0x76, 0x31, 0x2f,
	0x72, 0x6f, 0x6c, 0x65, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x66, 0x65, 0x70,
	0x65, 0x72, 0x6d, 0x42, 0x3f, 0x0a, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x76, 0x31,
	0x3b, 0x69, 0x61, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_iam_v1_role_proto_rawDescOnce sync.Once
	file_iam_v1_role_proto_rawDescData = file_iam_v1_role_proto_rawDesc
)

func file_iam_v1_role_proto_rawDescGZIP() []byte {
	file_iam_v1_role_proto_rawDescOnce.Do(func() {
		file_iam_v1_role_proto_rawDescData = protoimpl.X.CompressGZIP(file_iam_v1_role_proto_rawDescData)
	})
	return file_iam_v1_role_proto_rawDescData
}

var file_iam_v1_role_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_iam_v1_role_proto_goTypes = []interface{}{
	(*Role)(nil),                 // 0: iam.v1.Role
	(*UpdateRoleRequest)(nil),    // 1: iam.v1.UpdateRoleRequest
	(*DeleteRoleRequest)(nil),    // 2: iam.v1.DeleteRoleRequest
	(*GetRoleRequest)(nil),       // 3: iam.v1.GetRoleRequest
	(*GetRoleReply)(nil),         // 4: iam.v1.GetRoleReply
	(*ListRoleRequest)(nil),      // 5: iam.v1.ListRoleRequest
	(*ListRoleReply)(nil),        // 6: iam.v1.ListRoleReply
	(*FepermItem)(nil),           // 7: iam.v1.FepermItem
	(*GetRoleFepermReply)(nil),   // 8: iam.v1.GetRoleFepermReply
	(*SetRoleFepermRequest)(nil), // 9: iam.v1.SetRoleFepermRequest
	(EditAction_Enum)(0),         // 10: iam.v1.EditAction.Enum
	(Team_Type_Enum)(0),          // 11: iam.v1.Team.Type.Enum
	(*Feperm)(nil),               // 12: iam.v1.Feperm
	(*emptypb.Empty)(nil),        // 13: google.protobuf.Empty
}
var file_iam_v1_role_proto_depIdxs = []int32{
	10, // 0: iam.v1.UpdateRoleRequest.action:type_name -> iam.v1.EditAction.Enum
	0,  // 1: iam.v1.ListRoleReply.roles:type_name -> iam.v1.Role
	11, // 2: iam.v1.FepermItem.team_type:type_name -> iam.v1.Team.Type.Enum
	12, // 3: iam.v1.FepermItem.perm:type_name -> iam.v1.Feperm
	7,  // 4: iam.v1.GetRoleFepermReply.perms:type_name -> iam.v1.FepermItem
	7,  // 5: iam.v1.SetRoleFepermRequest.perms:type_name -> iam.v1.FepermItem
	0,  // 6: iam.v1.Roles.CreateRole:input_type -> iam.v1.Role
	1,  // 7: iam.v1.Roles.UpdateRole:input_type -> iam.v1.UpdateRoleRequest
	2,  // 8: iam.v1.Roles.DeleteRole:input_type -> iam.v1.DeleteRoleRequest
	3,  // 9: iam.v1.Roles.GetRole:input_type -> iam.v1.GetRoleRequest
	5,  // 10: iam.v1.Roles.ListRole:input_type -> iam.v1.ListRoleRequest
	3,  // 11: iam.v1.Roles.GetRoleFeperm:input_type -> iam.v1.GetRoleRequest
	9,  // 12: iam.v1.Roles.SetRoleFeperm:input_type -> iam.v1.SetRoleFepermRequest
	0,  // 13: iam.v1.Roles.CreateRole:output_type -> iam.v1.Role
	13, // 14: iam.v1.Roles.UpdateRole:output_type -> google.protobuf.Empty
	13, // 15: iam.v1.Roles.DeleteRole:output_type -> google.protobuf.Empty
	4,  // 16: iam.v1.Roles.GetRole:output_type -> iam.v1.GetRoleReply
	6,  // 17: iam.v1.Roles.ListRole:output_type -> iam.v1.ListRoleReply
	8,  // 18: iam.v1.Roles.GetRoleFeperm:output_type -> iam.v1.GetRoleFepermReply
	13, // 19: iam.v1.Roles.SetRoleFeperm:output_type -> google.protobuf.Empty
	13, // [13:20] is the sub-list for method output_type
	6,  // [6:13] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_iam_v1_role_proto_init() }
func file_iam_v1_role_proto_init() {
	if File_iam_v1_role_proto != nil {
		return
	}
	file_iam_v1_type_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_iam_v1_role_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Role); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_role_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRoleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_role_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRoleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_role_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_role_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoleReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_role_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRoleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_role_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRoleReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_role_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FepermItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_role_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoleFepermReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_role_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRoleFepermRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_iam_v1_role_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_iam_v1_role_proto_goTypes,
		DependencyIndexes: file_iam_v1_role_proto_depIdxs,
		MessageInfos:      file_iam_v1_role_proto_msgTypes,
	}.Build()
	File_iam_v1_role_proto = out.File
	file_iam_v1_role_proto_rawDesc = nil
	file_iam_v1_role_proto_goTypes = nil
	file_iam_v1_role_proto_depIdxs = nil
}
