package v1

import (
	"strings"

	"annout/api/client"
	"annout/api/interpolate"
	"annout/internal/biz"
	"annout/workflow/style/common"
	"annout/workflow/types"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"

	"github.com/samber/lo"
)

func init() {
	common.Register("deeproute_v1", Transform)
}

func Transform(reaper *biz.Reaper, annos []*client.Job) (out types.Slicer, err error) {
	return newSlicer(annos), nil
}

type deeproute struct {
	annos []*client.Job
}

func newSlicer(annos []*client.Job) *deeproute {
	return &deeproute{annos: annos}
}

func (o *deeproute) Slice() ([]*types.Piece, error) {
	if len(o.annos) > 0 && o.annos[0].NeedInterpolation {
		lo.ForEach(o.annos, func(v *anno.Job, _ int) { interpolate.Interpolate(client.JobAnnoFromJob(v)) })
	}

	var out []*types.Piece
	for _, job := range o.annos {
		for j, elAnno := range job.Annotations {
			for k, rdAnno := range elAnno.RawdataAnnos {
				out = append(out, o.transRawdataAnno(job.Elements[j].Datas[k], rdAnno))
			}
		}
	}
	return out, nil
}

func (o *deeproute) transRawdataAnno(rd *anno.Rawdata, rdAnno *anno.RawdataAnno) *types.Piece {
	name := rd.OrigName
	if name == "" {
		name = rd.Name
	}
	return &types.Piece{
		Name: name + ".json",
		Data: map[string]any{
			"objects": lo.Map(rdAnno.Objects, toDeeprouteObj),
		},
	}
}

func toDeeprouteObj(o *anno.Object, _ int) any {
	var withSubtype = []string{"RED_", "GREEN_", "YELLOW_", "BLACK_", "LANEMARK_", "SIGN_"}
	typ, subtype := strings.ToUpper(o.Label.Name), ""
	for _, v := range withSubtype {
		if strings.HasPrefix(typ, v) {
			subtype = typ[len(v):]
			typ = v[:len(v)-1]
			break
		}
	}
	labelMap := map[string]string{
		"box2d": "bounding_box",
	}
	var do any
	labelName := o.Label.Widget.Name
	data := o.Label.Widget.Data
	switch labelName {
	case anno.WidgetName_box2d:
		if data[4] != 0 {
			panic("object rotation is not supported")
		}
		do = map[string]float64{
			"x1": data[0] - data[2]/2,
			"x2": data[0] + data[2]/2,
			"y1": data[1] - data[3]/2,
			"y2": data[1] + data[3]/2,
		}
	default:
		panic("unkown object type " + labelName.String())
	}
	return map[string]any{
		labelMap[labelName.String()]: do,
		"type":                       typ,
		"subtype":                    subtype,
	}
}
