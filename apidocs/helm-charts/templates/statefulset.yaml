apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "appx.fullname" . }}
  labels:
    {{- include "appx.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "appx.selectorLabels" . | nindent 6 }}
  serviceName: {{ include "appx.fullname" . }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "appx.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "appx.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
            {{- toYaml .Values.image.svcCommand | nindent 12 }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
            - name: grpc
              containerPort:  {{ .Values.service.grpcPort }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: {{ .Values.service.healthzPath }}
              port: http
              # httpHeaders:
              #   - name: Host
              #     value: {{ .Values.hostName }}
          readinessProbe:
            httpGet:
              path: {{ .Values.service.healthzPath }}
              port: http
              # httpHeaders:
              #   - name: Host
              #     value: {{ .Values.hostName }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - mountPath: /app/assets
              name: data
          envFrom:
            - secretRef:
                name: {{ .Values.secretS3 }}
          env:
            {{- include "helper.secretDBToEnv" . | nindent 12 }}
            {{- include "helper.envVarsToEnv" . | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
  {{- if not .Values.persistence.enabled }}
        - name: data
          emptyDir: {}
  {{- else }}
  {{- if .Values.persistentVolumeClaimRetentionPolicy.enabled }}
  persistentVolumeClaimRetentionPolicy:
    whenDeleted: {{ .Values.persistentVolumeClaimRetentionPolicy.whenDeleted }}
    whenScaled: {{ .Values.persistentVolumeClaimRetentionPolicy.whenScaled }}
  {{- end }}
  volumeClaimTemplates:
    - metadata:
        name: data
        {{- if .Values.persistence.annotations }}
        annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.persistence.annotations "context" $) | nindent 10 }}
        {{- end }}
      spec:
        accessModes:
        {{- range .Values.persistence.accessModes }}
          - {{ . | quote }}
        {{- end }}
        resources:
          requests:
            storage: {{ .Values.persistence.size | quote }}
        {{- if .Values.persistence.selector }}
        selector: {{- include "common.tplvalues.render" ( dict "value" .Values.persistence.selector "context" $) | nindent 10 }}
        {{- end }}
        {{ include "common.storage.class" (dict "persistence" .Values.persistence "global" .Values.global) }}
  {{- end }}
