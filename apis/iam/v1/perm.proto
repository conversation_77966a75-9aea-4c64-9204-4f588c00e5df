syntax = "proto3";

package iam.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "openapi/v3/annotations.proto";
import "validate/validate.proto";
import "iam/v1/type.proto";

option go_package = "gitlab.rp.konvery.work/platform/apis/iam/v1;iam";
option java_multiple_files = true;
option java_package = "iam.v1";

service Perms {
  rpc EditPerms (EditPermsRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/perms"
      body: "*"
    };
  }

  // get permission class (resource type) list: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
  rpc ListPermClass (google.protobuf.Empty) returns (ListPermClassReply) {
    option (google.api.http) = {
      get: "/v1/perms/classes"
    };
  }

  // get permission list
  rpc ListPerm (ListPermRequest) returns (ListPermReply) {
    option (google.api.http) = {
      get: "/v1/perms"
    };
  }
}

message EditPermsRequest {
  option (openapi.v3.schema) = {
    required: ["perms", "action"]
  };

  EditAction.Enum action = 1 [(validate.rules).enum = {defined_only: true, not_in: [0]}];
  // pattern: "^\\w+\\.\\w+$", max_bytes: 64
  repeated string perms = 2 [(validate.rules).repeated.items.string =
                              {pattern: "^\\w+\\.\\w+$", max_bytes: 64}];
}

message ListPermClassReply {
  // resource type: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
  repeated string classes = 1;
}

message ListPermRequest {
  int32 pagesz = 1 [(validate.rules).int32 = {gte:0, lte: 100}];
  // An opaque pagination token returned from a previous call.
  // An empty token denotes the first page.
  string page_token = 2;
  // resource type: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
  string class = 3;
  string name_pattern = 4;
}

message ListPermReply {
  option (openapi.v3.schema) = {
    required: ["perms", "next_page_token"]
  };

  repeated string perms = 1;
  string next_page_token = 2;
}
