// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: annofeed/v1/file.proto

package annofeed

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateFileRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateFileRequestMultiError, or nil if none found.
func (m *CreateFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Size

	// no validation rules for Parts

	// no validation rules for Mime

	// no validation rules for Sha256

	// no validation rules for OrgUid

	if len(errors) > 0 {
		return CreateFileRequestMultiError(errors)
	}

	return nil
}

// CreateFileRequestMultiError is an error wrapping multiple validation errors
// returned by CreateFileRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateFileRequestMultiError) AllErrors() []error { return m }

// CreateFileRequestValidationError is the validation error returned by
// CreateFileRequest.Validate if the designated constraints aren't met.
type CreateFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateFileRequestValidationError) ErrorName() string {
	return "CreateFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateFileRequestValidationError{}

// Validate checks the field values on CreateFileReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateFileReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateFileReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateFileReplyMultiError, or nil if none found.
func (m *CreateFileReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateFileReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFileReplyValidationError{
					field:  "File",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFileReplyValidationError{
					field:  "File",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFileReplyValidationError{
				field:  "File",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUrlExpiresAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFileReplyValidationError{
					field:  "UrlExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFileReplyValidationError{
					field:  "UrlExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUrlExpiresAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFileReplyValidationError{
				field:  "UrlExpiresAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PreExisting

	if len(errors) > 0 {
		return CreateFileReplyMultiError(errors)
	}

	return nil
}

// CreateFileReplyMultiError is an error wrapping multiple validation errors
// returned by CreateFileReply.ValidateAll() if the designated constraints
// aren't met.
type CreateFileReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateFileReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateFileReplyMultiError) AllErrors() []error { return m }

// CreateFileReplyValidationError is the validation error returned by
// CreateFileReply.Validate if the designated constraints aren't met.
type CreateFileReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateFileReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateFileReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateFileReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateFileReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateFileReplyValidationError) ErrorName() string { return "CreateFileReplyValidationError" }

// Error satisfies the builtin error interface
func (e CreateFileReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateFileReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateFileReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateFileReplyValidationError{}

// Validate checks the field values on UpdateFileRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateFileRequestMultiError, or nil if none found.
func (m *UpdateFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFileRequestValidationError{
					field:  "File",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFileRequestValidationError{
					field:  "File",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFileRequestValidationError{
				field:  "File",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateFileRequestMultiError(errors)
	}

	return nil
}

// UpdateFileRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateFileRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFileRequestMultiError) AllErrors() []error { return m }

// UpdateFileRequestValidationError is the validation error returned by
// UpdateFileRequest.Validate if the designated constraints aren't met.
type UpdateFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFileRequestValidationError) ErrorName() string {
	return "UpdateFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFileRequestValidationError{}

// Validate checks the field values on GetFileUploadURLsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFileUploadURLsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFileUploadURLsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFileUploadURLsRequestMultiError, or nil if none found.
func (m *GetFileUploadURLsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFileUploadURLsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetFileUploadURLsRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := GetFileUploadURLsRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Parts

	if len(errors) > 0 {
		return GetFileUploadURLsRequestMultiError(errors)
	}

	return nil
}

// GetFileUploadURLsRequestMultiError is an error wrapping multiple validation
// errors returned by GetFileUploadURLsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFileUploadURLsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFileUploadURLsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFileUploadURLsRequestMultiError) AllErrors() []error { return m }

// GetFileUploadURLsRequestValidationError is the validation error returned by
// GetFileUploadURLsRequest.Validate if the designated constraints aren't met.
type GetFileUploadURLsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFileUploadURLsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFileUploadURLsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFileUploadURLsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFileUploadURLsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFileUploadURLsRequestValidationError) ErrorName() string {
	return "GetFileUploadURLsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFileUploadURLsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFileUploadURLsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFileUploadURLsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFileUploadURLsRequestValidationError{}

var _GetFileUploadURLsRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on GetFileUploadURLsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFileUploadURLsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFileUploadURLsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFileUploadURLsReplyMultiError, or nil if none found.
func (m *GetFileUploadURLsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFileUploadURLsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUrlExpiresAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFileUploadURLsReplyValidationError{
					field:  "UrlExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFileUploadURLsReplyValidationError{
					field:  "UrlExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUrlExpiresAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFileUploadURLsReplyValidationError{
				field:  "UrlExpiresAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFileUploadURLsReplyMultiError(errors)
	}

	return nil
}

// GetFileUploadURLsReplyMultiError is an error wrapping multiple validation
// errors returned by GetFileUploadURLsReply.ValidateAll() if the designated
// constraints aren't met.
type GetFileUploadURLsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFileUploadURLsReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFileUploadURLsReplyMultiError) AllErrors() []error { return m }

// GetFileUploadURLsReplyValidationError is the validation error returned by
// GetFileUploadURLsReply.Validate if the designated constraints aren't met.
type GetFileUploadURLsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFileUploadURLsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFileUploadURLsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFileUploadURLsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFileUploadURLsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFileUploadURLsReplyValidationError) ErrorName() string {
	return "GetFileUploadURLsReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetFileUploadURLsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFileUploadURLsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFileUploadURLsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFileUploadURLsReplyValidationError{}

// Validate checks the field values on FinishFileUploadRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FinishFileUploadRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FinishFileUploadRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FinishFileUploadRequestMultiError, or nil if none found.
func (m *FinishFileUploadRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FinishFileUploadRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_FinishFileUploadRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := FinishFileUploadRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FinishFileUploadRequestMultiError(errors)
	}

	return nil
}

// FinishFileUploadRequestMultiError is an error wrapping multiple validation
// errors returned by FinishFileUploadRequest.ValidateAll() if the designated
// constraints aren't met.
type FinishFileUploadRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FinishFileUploadRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FinishFileUploadRequestMultiError) AllErrors() []error { return m }

// FinishFileUploadRequestValidationError is the validation error returned by
// FinishFileUploadRequest.Validate if the designated constraints aren't met.
type FinishFileUploadRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FinishFileUploadRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FinishFileUploadRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FinishFileUploadRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FinishFileUploadRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FinishFileUploadRequestValidationError) ErrorName() string {
	return "FinishFileUploadRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FinishFileUploadRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFinishFileUploadRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FinishFileUploadRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FinishFileUploadRequestValidationError{}

var _FinishFileUploadRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on DeleteFileRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteFileRequestMultiError, or nil if none found.
func (m *DeleteFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_DeleteFileRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := DeleteFileRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteFileRequestMultiError(errors)
	}

	return nil
}

// DeleteFileRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteFileRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteFileRequestMultiError) AllErrors() []error { return m }

// DeleteFileRequestValidationError is the validation error returned by
// DeleteFileRequest.Validate if the designated constraints aren't met.
type DeleteFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteFileRequestValidationError) ErrorName() string {
	return "DeleteFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteFileRequestValidationError{}

var _DeleteFileRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on GetFileRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFileRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetFileRequestMultiError,
// or nil if none found.
func (m *GetFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetFileRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := GetFileRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetFileRequestMultiError(errors)
	}

	return nil
}

// GetFileRequestMultiError is an error wrapping multiple validation errors
// returned by GetFileRequest.ValidateAll() if the designated constraints
// aren't met.
type GetFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFileRequestMultiError) AllErrors() []error { return m }

// GetFileRequestValidationError is the validation error returned by
// GetFileRequest.Validate if the designated constraints aren't met.
type GetFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFileRequestValidationError) ErrorName() string { return "GetFileRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFileRequestValidationError{}

var _GetFileRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on ListFileRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListFileRequestMultiError, or nil if none found.
func (m *ListFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListFileRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageToken

	// no validation rules for OrgUid

	// no validation rules for CreatorUid

	// no validation rules for NamePattern

	if len(errors) > 0 {
		return ListFileRequestMultiError(errors)
	}

	return nil
}

// ListFileRequestMultiError is an error wrapping multiple validation errors
// returned by ListFileRequest.ValidateAll() if the designated constraints
// aren't met.
type ListFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListFileRequestMultiError) AllErrors() []error { return m }

// ListFileRequestValidationError is the validation error returned by
// ListFileRequest.Validate if the designated constraints aren't met.
type ListFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListFileRequestValidationError) ErrorName() string { return "ListFileRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListFileRequestValidationError{}

// Validate checks the field values on ListFileReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListFileReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListFileReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListFileReplyMultiError, or
// nil if none found.
func (m *ListFileReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListFileReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListFileReplyValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListFileReplyValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListFileReplyValidationError{
					field:  fmt.Sprintf("Files[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if len(errors) > 0 {
		return ListFileReplyMultiError(errors)
	}

	return nil
}

// ListFileReplyMultiError is an error wrapping multiple validation errors
// returned by ListFileReply.ValidateAll() if the designated constraints
// aren't met.
type ListFileReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListFileReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListFileReplyMultiError) AllErrors() []error { return m }

// ListFileReplyValidationError is the validation error returned by
// ListFileReply.Validate if the designated constraints aren't met.
type ListFileReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListFileReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListFileReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListFileReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListFileReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListFileReplyValidationError) ErrorName() string { return "ListFileReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListFileReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListFileReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListFileReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListFileReplyValidationError{}

// Validate checks the field values on ShareFileRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ShareFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShareFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShareFileRequestMultiError, or nil if none found.
func (m *ShareFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ShareFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ShareFileRequest_Uid_Pattern.MatchString(m.GetUid()) {
		err := ShareFileRequestValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Timeout

	if len(errors) > 0 {
		return ShareFileRequestMultiError(errors)
	}

	return nil
}

// ShareFileRequestMultiError is an error wrapping multiple validation errors
// returned by ShareFileRequest.ValidateAll() if the designated constraints
// aren't met.
type ShareFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShareFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShareFileRequestMultiError) AllErrors() []error { return m }

// ShareFileRequestValidationError is the validation error returned by
// ShareFileRequest.Validate if the designated constraints aren't met.
type ShareFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShareFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShareFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShareFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShareFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShareFileRequestValidationError) ErrorName() string { return "ShareFileRequestValidationError" }

// Error satisfies the builtin error interface
func (e ShareFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShareFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShareFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShareFileRequestValidationError{}

var _ShareFileRequest_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on ShareFileReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ShareFileReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShareFileReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ShareFileReplyMultiError,
// or nil if none found.
func (m *ShareFileReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ShareFileReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	if all {
		switch v := interface{}(m.GetExpiresAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ShareFileReplyValidationError{
					field:  "ExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ShareFileReplyValidationError{
					field:  "ExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiresAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShareFileReplyValidationError{
				field:  "ExpiresAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ShareFileReplyMultiError(errors)
	}

	return nil
}

// ShareFileReplyMultiError is an error wrapping multiple validation errors
// returned by ShareFileReply.ValidateAll() if the designated constraints
// aren't met.
type ShareFileReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShareFileReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShareFileReplyMultiError) AllErrors() []error { return m }

// ShareFileReplyValidationError is the validation error returned by
// ShareFileReply.Validate if the designated constraints aren't met.
type ShareFileReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShareFileReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShareFileReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShareFileReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShareFileReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShareFileReplyValidationError) ErrorName() string { return "ShareFileReplyValidationError" }

// Error satisfies the builtin error interface
func (e ShareFileReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShareFileReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShareFileReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShareFileReplyValidationError{}

// Validate checks the field values on File with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *File) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on File with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in FileMultiError, or nil if none found.
func (m *File) ValidateAll() error {
	return m.validate(true)
}

func (m *File) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_File_Uid_Pattern.MatchString(m.GetUid()) {
		err := FileValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Uri

	// no validation rules for Name

	// no validation rules for Size

	// no validation rules for Mime

	// no validation rules for Sha256

	// no validation rules for OrgUid

	if _, ok := File_State_Enum_name[int32(m.GetState())]; !ok {
		err := FileValidationError{
			field:  "State",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CreatorUid

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FileMultiError(errors)
	}

	return nil
}

// FileMultiError is an error wrapping multiple validation errors returned by
// File.ValidateAll() if the designated constraints aren't met.
type FileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileMultiError) AllErrors() []error { return m }

// FileValidationError is the validation error returned by File.Validate if the
// designated constraints aren't met.
type FileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileValidationError) ErrorName() string { return "FileValidationError" }

// Error satisfies the builtin error interface
func (e FileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileValidationError{}

var _File_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

// Validate checks the field values on File_State with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *File_State) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on File_State with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in File_StateMultiError, or
// nil if none found.
func (m *File_State) ValidateAll() error {
	return m.validate(true)
}

func (m *File_State) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return File_StateMultiError(errors)
	}

	return nil
}

// File_StateMultiError is an error wrapping multiple validation errors
// returned by File_State.ValidateAll() if the designated constraints aren't met.
type File_StateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m File_StateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m File_StateMultiError) AllErrors() []error { return m }

// File_StateValidationError is the validation error returned by
// File_State.Validate if the designated constraints aren't met.
type File_StateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e File_StateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e File_StateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e File_StateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e File_StateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e File_StateValidationError) ErrorName() string { return "File_StateValidationError" }

// Error satisfies the builtin error interface
func (e File_StateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFile_State.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = File_StateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = File_StateValidationError{}
