// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: types/nv.proto

package types

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Name with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Name) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Name with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in NameMultiError, or nil if none found.
func (m *Name) ValidateAll() error {
	return m.validate(true)
}

func (m *Name) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return NameMultiError(errors)
	}

	return nil
}

// NameMultiError is an error wrapping multiple validation errors returned by
// Name.ValidateAll() if the designated constraints aren't met.
type NameMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NameMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NameMultiError) AllErrors() []error { return m }

// NameValidationError is the validation error returned by Name.Validate if the
// designated constraints aren't met.
type NameValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NameValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NameValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NameValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NameValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NameValidationError) ErrorName() string { return "NameValidationError" }

// Error satisfies the builtin error interface
func (e NameValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sName.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NameValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NameValidationError{}

// Validate checks the field values on NameValue with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NameValue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NameValue with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NameValueMultiError, or nil
// if none found.
func (m *NameValue) ValidateAll() error {
	return m.validate(true)
}

func (m *NameValue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Value

	if len(errors) > 0 {
		return NameValueMultiError(errors)
	}

	return nil
}

// NameValueMultiError is an error wrapping multiple validation errors returned
// by NameValue.ValidateAll() if the designated constraints aren't met.
type NameValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NameValueMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NameValueMultiError) AllErrors() []error { return m }

// NameValueValidationError is the validation error returned by
// NameValue.Validate if the designated constraints aren't met.
type NameValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NameValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NameValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NameValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NameValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NameValueValidationError) ErrorName() string { return "NameValueValidationError" }

// Error satisfies the builtin error interface
func (e NameValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNameValue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NameValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NameValueValidationError{}
