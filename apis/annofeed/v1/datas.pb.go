// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: annofeed/v1/datas.proto

package annofeed

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	v1 "gitlab.rp.konvery.work/platform/apis/anno/v1"
	types "gitlab.rp.konvery.work/platform/apis/types"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Data_State_Enum int32

const (
	Data_State_unspecified Data_State_Enum = 0
	Data_State_raw         Data_State_Enum = 1
	Data_State_fetching    Data_State_Enum = 2
	Data_State_processing  Data_State_Enum = 3
	Data_State_ready       Data_State_Enum = 4
	Data_State_abandoned   Data_State_Enum = 5
	Data_State_disabled    Data_State_Enum = 6
	Data_State_failed      Data_State_Enum = 7
)

// Enum value maps for Data_State_Enum.
var (
	Data_State_Enum_name = map[int32]string{
		0: "unspecified",
		1: "raw",
		2: "fetching",
		3: "processing",
		4: "ready",
		5: "abandoned",
		6: "disabled",
		7: "failed",
	}
	Data_State_Enum_value = map[string]int32{
		"unspecified": 0,
		"raw":         1,
		"fetching":    2,
		"processing":  3,
		"ready":       4,
		"abandoned":   5,
		"disabled":    6,
		"failed":      7,
	}
)

func (x Data_State_Enum) Enum() *Data_State_Enum {
	p := new(Data_State_Enum)
	*p = x
	return p
}

func (x Data_State_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Data_State_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_annofeed_v1_datas_proto_enumTypes[0].Descriptor()
}

func (Data_State_Enum) Type() protoreflect.EnumType {
	return &file_annofeed_v1_datas_proto_enumTypes[0]
}

func (x Data_State_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Data_State_Enum.Descriptor instead.
func (Data_State_Enum) EnumDescriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{6, 0, 0}
}

type ParseDataRequest_Option int32

const (
	// 只解析未曾解析的数据
	ParseDataRequest_unspecified ParseDataRequest_Option = 0
	// 无论上次解析结果如何，都清除之前的解析结果并重新解析数据
	ParseDataRequest_force ParseDataRequest_Option = 1
	// 解析未曾解析的或者失败的数据（清除之前的解析结果）
	ParseDataRequest_reparse_failed ParseDataRequest_Option = 2
)

// Enum value maps for ParseDataRequest_Option.
var (
	ParseDataRequest_Option_name = map[int32]string{
		0: "unspecified",
		1: "force",
		2: "reparse_failed",
	}
	ParseDataRequest_Option_value = map[string]int32{
		"unspecified":    0,
		"force":          1,
		"reparse_failed": 2,
	}
)

func (x ParseDataRequest_Option) Enum() *ParseDataRequest_Option {
	p := new(ParseDataRequest_Option)
	*p = x
	return p
}

func (x ParseDataRequest_Option) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ParseDataRequest_Option) Descriptor() protoreflect.EnumDescriptor {
	return file_annofeed_v1_datas_proto_enumTypes[1].Descriptor()
}

func (ParseDataRequest_Option) Type() protoreflect.EnumType {
	return &file_annofeed_v1_datas_proto_enumTypes[1]
}

func (x ParseDataRequest_Option) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ParseDataRequest_Option.Descriptor instead.
func (ParseDataRequest_Option) EnumDescriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{14, 0}
}

type CreateDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// data UID (only needed in update-requests)
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// data name (mandatory in create-requests)
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// data description
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	// data type
	Type v1.Element_Type_Enum `protobuf:"varint,4,opt,name=type,proto3,enum=anno.v1.Element_Type_Enum" json:"type,omitempty"`
	// based on data UID
	BaseOnUid string `protobuf:"bytes,5,opt,name=base_on_uid,json=baseOnUid,proto3" json:"base_on_uid,omitempty"`
	// source files which the data originates from
	Source *v1.Source `protobuf:"bytes,6,opt,name=source,proto3" json:"source,omitempty"`
	// UID of the organization which the data belongs to
	OrgUid string `protobuf:"bytes,7,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// UID of the order which caused the data
	OrderUid string `protobuf:"bytes,8,opt,name=order_uid,json=orderUid,proto3" json:"order_uid,omitempty"`
}

func (x *CreateDataRequest) Reset() {
	*x = CreateDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_datas_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDataRequest) ProtoMessage() {}

func (x *CreateDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_datas_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDataRequest.ProtoReflect.Descriptor instead.
func (*CreateDataRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{0}
}

func (x *CreateDataRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *CreateDataRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateDataRequest) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *CreateDataRequest) GetType() v1.Element_Type_Enum {
	if x != nil {
		return x.Type
	}
	return v1.Element_Type_Enum(0)
}

func (x *CreateDataRequest) GetBaseOnUid() string {
	if x != nil {
		return x.BaseOnUid
	}
	return ""
}

func (x *CreateDataRequest) GetSource() *v1.Source {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *CreateDataRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *CreateDataRequest) GetOrderUid() string {
	if x != nil {
		return x.OrderUid
	}
	return ""
}

type UpdateDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update contents
	Data *CreateDataRequest `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	// name of fields to be updated
	Fields []string `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *UpdateDataRequest) Reset() {
	*x = UpdateDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_datas_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDataRequest) ProtoMessage() {}

func (x *UpdateDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_datas_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDataRequest.ProtoReflect.Descriptor instead.
func (*UpdateDataRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateDataRequest) GetData() *CreateDataRequest {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateDataRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type DeleteDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *DeleteDataRequest) Reset() {
	*x = DeleteDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_datas_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDataRequest) ProtoMessage() {}

func (x *DeleteDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_datas_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDataRequest.ProtoReflect.Descriptor instead.
func (*DeleteDataRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteDataRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// if true, only return the simple info: name, desc, state, etc.
	Simple bool `protobuf:"varint,2,opt,name=simple,proto3" json:"simple,omitempty"`
}

func (x *GetDataRequest) Reset() {
	*x = GetDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_datas_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataRequest) ProtoMessage() {}

func (x *GetDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_datas_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataRequest.ProtoReflect.Descriptor instead.
func (*GetDataRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{3}
}

func (x *GetDataRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *GetDataRequest) GetSimple() bool {
	if x != nil {
		return x.Simple
	}
	return false
}

type ListDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page   int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Pagesz int32 `protobuf:"varint,2,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// filter by organization
	OrgUid string `protobuf:"bytes,3,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// filter by creator
	CreatorUid string `protobuf:"bytes,4,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	// filter by name pattern
	NamePattern string `protobuf:"bytes,5,opt,name=name_pattern,json=namePattern,proto3" json:"name_pattern,omitempty"`
	// filter by order
	OrderUid string `protobuf:"bytes,6,opt,name=order_uid,json=orderUid,proto3" json:"order_uid,omitempty"`
	// filter by data state
	States []Data_State_Enum `protobuf:"varint,7,rep,packed,name=states,proto3,enum=annofeed.v1.Data_State_Enum" json:"states,omitempty"`
}

func (x *ListDataRequest) Reset() {
	*x = ListDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_datas_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDataRequest) ProtoMessage() {}

func (x *ListDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_datas_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDataRequest.ProtoReflect.Descriptor instead.
func (*ListDataRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{4}
}

func (x *ListDataRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListDataRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListDataRequest) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *ListDataRequest) GetCreatorUid() string {
	if x != nil {
		return x.CreatorUid
	}
	return ""
}

func (x *ListDataRequest) GetNamePattern() string {
	if x != nil {
		return x.NamePattern
	}
	return ""
}

func (x *ListDataRequest) GetOrderUid() string {
	if x != nil {
		return x.OrderUid
	}
	return ""
}

func (x *ListDataRequest) GetStates() []Data_State_Enum {
	if x != nil {
		return x.States
	}
	return nil
}

type ListDataReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total number of items found; valid only in the first page reply.
	Total int32   `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Datas []*Data `protobuf:"bytes,2,rep,name=datas,proto3" json:"datas,omitempty"`
}

func (x *ListDataReply) Reset() {
	*x = ListDataReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_datas_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDataReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDataReply) ProtoMessage() {}

func (x *ListDataReply) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_datas_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDataReply.ProtoReflect.Descriptor instead.
func (*ListDataReply) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{5}
}

func (x *ListDataReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListDataReply) GetDatas() []*Data {
	if x != nil {
		return x.Datas
	}
	return nil
}

type Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// data UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// data name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// data description
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	// data type
	Type v1.Element_Type_Enum `protobuf:"varint,4,opt,name=type,proto3,enum=anno.v1.Element_Type_Enum" json:"type,omitempty"`
	// based on data UID
	BaseOnUid string `protobuf:"bytes,5,opt,name=base_on_uid,json=baseOnUid,proto3" json:"base_on_uid,omitempty"`
	// source files which the data originates from
	Source *v1.Source `protobuf:"bytes,6,opt,name=source,proto3" json:"source,omitempty"`
	// UID of the organization which the data belongs to
	OrgUid string `protobuf:"bytes,7,opt,name=org_uid,json=orgUid,proto3" json:"org_uid,omitempty"`
	// UID of the order which caused the data
	OrderUid string `protobuf:"bytes,8,opt,name=order_uid,json=orderUid,proto3" json:"order_uid,omitempty"`
	// number of elements in the data
	Size int32 `protobuf:"varint,10,opt,name=size,proto3" json:"size,omitempty"`
	// data state: fetching, processing, ready, failed
	State Data_State_Enum `protobuf:"varint,11,opt,name=state,proto3,enum=annofeed.v1.Data_State_Enum" json:"state,omitempty"`
	// when state is failed, this field will contain the error message
	Error string `protobuf:"bytes,12,opt,name=error,proto3" json:"error,omitempty"`
	// UID of the creator
	CreatorUid string `protobuf:"bytes,13,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	// data creation time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// data validation summary
	Summary *v1.DataValidationSummary `protobuf:"bytes,16,opt,name=summary,proto3" json:"summary,omitempty"`
}

func (x *Data) Reset() {
	*x = Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_datas_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data) ProtoMessage() {}

func (x *Data) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_datas_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data.ProtoReflect.Descriptor instead.
func (*Data) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{6}
}

func (x *Data) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Data) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Data) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Data) GetType() v1.Element_Type_Enum {
	if x != nil {
		return x.Type
	}
	return v1.Element_Type_Enum(0)
}

func (x *Data) GetBaseOnUid() string {
	if x != nil {
		return x.BaseOnUid
	}
	return ""
}

func (x *Data) GetSource() *v1.Source {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *Data) GetOrgUid() string {
	if x != nil {
		return x.OrgUid
	}
	return ""
}

func (x *Data) GetOrderUid() string {
	if x != nil {
		return x.OrderUid
	}
	return ""
}

func (x *Data) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Data) GetState() Data_State_Enum {
	if x != nil {
		return x.State
	}
	return Data_State_unspecified
}

func (x *Data) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *Data) GetCreatorUid() string {
	if x != nil {
		return x.CreatorUid
	}
	return ""
}

func (x *Data) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Data) GetSummary() *v1.DataValidationSummary {
	if x != nil {
		return x.Summary
	}
	return nil
}

type GetDataElementsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// data UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// element start index
	StartIdx int32 `protobuf:"varint,2,opt,name=start_idx,json=startIdx,proto3" json:"start_idx,omitempty"`
	// element count
	Count int32 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *GetDataElementsRequest) Reset() {
	*x = GetDataElementsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_datas_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataElementsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataElementsRequest) ProtoMessage() {}

func (x *GetDataElementsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_datas_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataElementsRequest.ProtoReflect.Descriptor instead.
func (*GetDataElementsRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{7}
}

func (x *GetDataElementsRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *GetDataElementsRequest) GetStartIdx() int32 {
	if x != nil {
		return x.StartIdx
	}
	return 0
}

func (x *GetDataElementsRequest) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GetDataElementsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Elements []*v1.Element `protobuf:"bytes,1,rep,name=elements,proto3" json:"elements,omitempty"`
}

func (x *GetDataElementsReply) Reset() {
	*x = GetDataElementsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_datas_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataElementsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataElementsReply) ProtoMessage() {}

func (x *GetDataElementsReply) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_datas_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataElementsReply.ProtoReflect.Descriptor instead.
func (*GetDataElementsReply) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{8}
}

func (x *GetDataElementsReply) GetElements() []*v1.Element {
	if x != nil {
		return x.Elements
	}
	return nil
}

type FindDataElementsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// data UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// element start index
	StartIdx int32 `protobuf:"varint,2,opt,name=start_idx,json=startIdx,proto3" json:"start_idx,omitempty"`
	// element count
	Count int32 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	// search elements by the pattern of their names
	NamePattern string `protobuf:"bytes,4,opt,name=name_pattern,json=namePattern,proto3" json:"name_pattern,omitempty"`
}

func (x *FindDataElementsRequest) Reset() {
	*x = FindDataElementsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_datas_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindDataElementsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindDataElementsRequest) ProtoMessage() {}

func (x *FindDataElementsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_datas_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindDataElementsRequest.ProtoReflect.Descriptor instead.
func (*FindDataElementsRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{9}
}

func (x *FindDataElementsRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *FindDataElementsRequest) GetStartIdx() int32 {
	if x != nil {
		return x.StartIdx
	}
	return 0
}

func (x *FindDataElementsRequest) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *FindDataElementsRequest) GetNamePattern() string {
	if x != nil {
		return x.NamePattern
	}
	return ""
}

type GetDataMetaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// data UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// whether to return metafiles in response
	WithMetafiles bool `protobuf:"varint,2,opt,name=with_metafiles,json=withMetafiles,proto3" json:"with_metafiles,omitempty"`
}

func (x *GetDataMetaRequest) Reset() {
	*x = GetDataMetaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_datas_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataMetaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataMetaRequest) ProtoMessage() {}

func (x *GetDataMetaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_datas_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataMetaRequest.ProtoReflect.Descriptor instead.
func (*GetDataMetaRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{10}
}

func (x *GetDataMetaRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *GetDataMetaRequest) GetWithMetafiles() bool {
	if x != nil {
		return x.WithMetafiles
	}
	return false
}

type GetDataMetaReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Metadata  map[string]string `protobuf:"bytes,1,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Metafiles *types.Filelist   `protobuf:"bytes,2,opt,name=metafiles,proto3" json:"metafiles,omitempty"`
}

func (x *GetDataMetaReply) Reset() {
	*x = GetDataMetaReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_datas_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataMetaReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataMetaReply) ProtoMessage() {}

func (x *GetDataMetaReply) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_datas_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataMetaReply.ProtoReflect.Descriptor instead.
func (*GetDataMetaReply) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{11}
}

func (x *GetDataMetaReply) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetDataMetaReply) GetMetafiles() *types.Filelist {
	if x != nil {
		return x.Metafiles
	}
	return nil
}

type SetRawdataEmbeddingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// data UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// rawdata name
	RawdataName string `protobuf:"bytes,2,opt,name=rawdata_name,json=rawdataName,proto3" json:"rawdata_name,omitempty"`
	// embedding file URI
	EmbeddingUri string `protobuf:"bytes,3,opt,name=embedding_uri,json=embeddingUri,proto3" json:"embedding_uri,omitempty"`
}

func (x *SetRawdataEmbeddingRequest) Reset() {
	*x = SetRawdataEmbeddingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_datas_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRawdataEmbeddingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRawdataEmbeddingRequest) ProtoMessage() {}

func (x *SetRawdataEmbeddingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_datas_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRawdataEmbeddingRequest.ProtoReflect.Descriptor instead.
func (*SetRawdataEmbeddingRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{12}
}

func (x *SetRawdataEmbeddingRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *SetRawdataEmbeddingRequest) GetRawdataName() string {
	if x != nil {
		return x.RawdataName
	}
	return ""
}

func (x *SetRawdataEmbeddingRequest) GetEmbeddingUri() string {
	if x != nil {
		return x.EmbeddingUri
	}
	return ""
}

type SetRawdataEmbeddingReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// embedding file access URL
	EmbeddingUrl string `protobuf:"bytes,1,opt,name=embedding_url,json=embeddingUrl,proto3" json:"embedding_url,omitempty"`
}

func (x *SetRawdataEmbeddingReply) Reset() {
	*x = SetRawdataEmbeddingReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_datas_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRawdataEmbeddingReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRawdataEmbeddingReply) ProtoMessage() {}

func (x *SetRawdataEmbeddingReply) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_datas_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRawdataEmbeddingReply.ProtoReflect.Descriptor instead.
func (*SetRawdataEmbeddingReply) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{13}
}

func (x *SetRawdataEmbeddingReply) GetEmbeddingUrl() string {
	if x != nil {
		return x.EmbeddingUrl
	}
	return ""
}

type ParseDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// data UID
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// parse option will not break current data parsing workflow if any
	Option ParseDataRequest_Option `protobuf:"varint,2,opt,name=option,proto3,enum=annofeed.v1.ParseDataRequest_Option" json:"option,omitempty"`
}

func (x *ParseDataRequest) Reset() {
	*x = ParseDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_datas_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParseDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseDataRequest) ProtoMessage() {}

func (x *ParseDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_datas_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseDataRequest.ProtoReflect.Descriptor instead.
func (*ParseDataRequest) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{14}
}

func (x *ParseDataRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ParseDataRequest) GetOption() ParseDataRequest_Option {
	if x != nil {
		return x.Option
	}
	return ParseDataRequest_unspecified
}

type Data_State struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Data_State) Reset() {
	*x = Data_State{}
	if protoimpl.UnsafeEnabled {
		mi := &file_annofeed_v1_datas_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_State) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_State) ProtoMessage() {}

func (x *Data_State) ProtoReflect() protoreflect.Message {
	mi := &file_annofeed_v1_datas_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_State.ProtoReflect.Descriptor instead.
func (*Data_State) Descriptor() ([]byte, []int) {
	return file_annofeed_v1_datas_proto_rawDescGZIP(), []int{6, 0}
}

var File_annofeed_v1_datas_proto protoreflect.FileDescriptor

var file_annofeed_v1_datas_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61,
	0x74, 0x61, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x66,
	0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61, 0x6e, 0x6e, 0x6f,
	0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x13, 0x61, 0x6e, 0x6e, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x66,
	0x69, 0x6c, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbc, 0x02,
	0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x38, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x6e,
	0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x79,
	0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x62, 0x61, 0x73, 0x65, 0x5f,
	0x6f, 0x6e, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x61,
	0x73, 0x65, 0x4f, 0x6e, 0x55, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x2e, 0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x69, 0x64, 0x3a, 0x1d, 0xba,
	0x47, 0x1a, 0xba, 0x01, 0x04, 0x74, 0x79, 0x70, 0x65, 0xba, 0x01, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0xba, 0x01, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x22, 0x74, 0x0a, 0x11,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x32, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x3a, 0x13, 0xba,
	0x47, 0x10, 0xba, 0x01, 0x04, 0x64, 0x61, 0x74, 0x61, 0xba, 0x01, 0x06, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x22, 0x3c, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x22, 0x51, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x69, 0x6d, 0x70, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x69, 0x6d,
	0x70, 0x6c, 0x65, 0x22, 0xf8, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x06, 0x70,
	0x61, 0x67, 0x65, 0x73, 0x7a, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x1a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x12, 0x17,
	0x0a, 0x07, 0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65,
	0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x6e, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x69, 0x64, 0x12, 0x34, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66,
	0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x22, 0x4e,
	0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x27, 0x0a, 0x05, 0x64, 0x61, 0x74, 0x61, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x64, 0x61, 0x74, 0x61, 0x73, 0x22, 0xdb,
	0x05, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x15, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x38, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x1e, 0x0a, 0x0b, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x6f, 0x6e, 0x5f, 0x75, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x61, 0x73, 0x65, 0x4f, 0x6e, 0x55, 0x69, 0x64,
	0x12, 0x27, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2e, 0x0a, 0x07, 0x6f, 0x72, 0x67,
	0x5f, 0x75, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72,
	0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d,
	0x24, 0x52, 0x06, 0x6f, 0x72, 0x67, 0x55, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x55, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x37, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c,
	0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x03, 0xe0, 0x41,
	0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x12,
	0x3e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42,
	0x03, 0xe0, 0x41, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x3d, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x1a, 0x7b,
	0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x72, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12,
	0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00,
	0x12, 0x07, 0x0a, 0x03, 0x72, 0x61, 0x77, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x66, 0x65, 0x74,
	0x63, 0x68, 0x69, 0x6e, 0x67, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79,
	0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x61, 0x62, 0x61, 0x6e, 0x64, 0x6f, 0x6e, 0x65, 0x64, 0x10,
	0x05, 0x12, 0x0c, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x10, 0x06, 0x12,
	0x0a, 0x0a, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x07, 0x3a, 0x3f, 0xba, 0x47, 0x3c,
	0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0xba, 0x01, 0x04,
	0x74, 0x79, 0x70, 0x65, 0xba, 0x01, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0xba, 0x01, 0x07,
	0x6f, 0x72, 0x67, 0x5f, 0x75, 0x69, 0x64, 0xba, 0x01, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0xba,
	0x01, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0x9c, 0x01, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x12, 0x24, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x78, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x52, 0x08, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x49, 0x64, 0x78, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x3a, 0x1d, 0xba, 0x47,
	0x1a, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0xba, 0x01, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x69, 0x64, 0x78, 0xba, 0x01, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x44, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x2c, 0x0a, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x22, 0xc0, 0x01, 0x0a, 0x17, 0x46, 0x69, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72,
	0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d,
	0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x69, 0x64, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02,
	0x28, 0x00, 0x52, 0x08, 0x73, 0x74, 0x61, 0x72, 0x74, 0x49, 0x64, 0x78, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x74, 0x65,
	0x72, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x50, 0x61,
	0x74, 0x74, 0x65, 0x72, 0x6e, 0x3a, 0x1d, 0xba, 0x47, 0x1a, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64,
	0xba, 0x01, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x78, 0xba, 0x01, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x64, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x4d,
	0x65, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x6d, 0x65, 0x74, 0x61,
	0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x77, 0x69, 0x74,
	0x68, 0x4d, 0x65, 0x74, 0x61, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x22, 0xc7, 0x01, 0x0a, 0x10, 0x47,
	0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x47, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61,
	0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x09, 0x6d, 0x65,
	0x74, 0x61, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0xca, 0x01, 0x0a, 0x1a, 0x53, 0x65, 0x74, 0x52, 0x61, 0x77, 0x64,
	0x61, 0x74, 0x61, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x0c,
	0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x72, 0x61, 0x77,
	0x64, 0x61, 0x74, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x0d, 0x65, 0x6d, 0x62, 0x65,
	0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x90, 0x01, 0x01, 0x52, 0x0c, 0x65, 0x6d, 0x62, 0x65, 0x64,
	0x64, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x69, 0x3a, 0x28, 0xba, 0x47, 0x25, 0xba, 0x01, 0x03, 0x75,
	0x69, 0x64, 0xba, 0x01, 0x0c, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0xba, 0x01, 0x0d, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72,
	0x69, 0x22, 0x3f, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x45,
	0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x23, 0x0a,
	0x0d, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x55,
	0x72, 0x6c, 0x22, 0xc8, 0x01, 0x0a, 0x10, 0x50, 0x61, 0x72, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x31, 0x7d, 0x24, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x12, 0x46, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x24, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x61, 0x72, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x38, 0x0a, 0x06, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x0f, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x10, 0x01, 0x12, 0x12,
	0x0a, 0x0e, 0x72, 0x65, 0x70, 0x61, 0x72, 0x73, 0x65, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x10, 0x02, 0x3a, 0x09, 0xba, 0x47, 0x06, 0xba, 0x01, 0x03, 0x75, 0x69, 0x64, 0x32, 0x95, 0x09,
	0x0a, 0x05, 0x44, 0x61, 0x74, 0x61, 0x73, 0x12, 0x55, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x11, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x22, 0x14, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0e,
	0x3a, 0x01, 0x2a, 0x22, 0x09, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x12, 0x63,
	0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x11, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x22,
	0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0x14, 0x2f,
	0x76, 0x31, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x2f, 0x7b, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x75,
	0x69, 0x64, 0x7d, 0x12, 0x5d, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x1e, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x11, 0x2a, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x2f, 0x7b, 0x75, 0x69,
	0x64, 0x7d, 0x12, 0x52, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x11, 0x2e, 0x61, 0x6e, 0x6e,
	0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x22, 0x17, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x11, 0x12, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73,
	0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x57, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x1c, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1a, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x11, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x0b, 0x12, 0x09, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x12,
	0x7b, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x12, 0x23, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65,
	0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1a, 0x12, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x2f, 0x7b, 0x75,
	0x69, 0x64, 0x7d, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x82, 0x01, 0x0a,
	0x10, 0x46, 0x69, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x24, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e,
	0x46, 0x69, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65,
	0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1f, 0x12, 0x1d, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x2f, 0x7b, 0x75,
	0x69, 0x64, 0x7d, 0x2f, 0x66, 0x69, 0x6e, 0x64, 0x2d, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x6b, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x65, 0x74, 0x61,
	0x12, 0x1f, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61,
	0x74, 0x61, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x12, 0x93,
	0x01, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6d, 0x62,
	0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x27, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65,
	0x64, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x45,
	0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x25, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x74, 0x52, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x3a, 0x01,
	0x2a, 0x1a, 0x21, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x2f, 0x7b, 0x75, 0x69,
	0x64, 0x7d, 0x2f, 0x72, 0x61, 0x77, 0x64, 0x61, 0x74, 0x61, 0x2d, 0x65, 0x6d, 0x62, 0x65, 0x64,
	0x64, 0x69, 0x6e, 0x67, 0x12, 0x59, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x12, 0x1b, 0x2e, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x61, 0x6e, 0x6e, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x22, 0x00, 0x12,
	0x64, 0x0a, 0x09, 0x50, 0x61, 0x72, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x2e, 0x61,
	0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x1a, 0x15,
	0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x2f, 0x7b, 0x75, 0x69, 0x64, 0x7d, 0x2f,
	0x70, 0x61, 0x72, 0x73, 0x65, 0x42, 0x4a, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65,
	0x64, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x39, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72,
	0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x66, 0x65, 0x65, 0x64, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x6e, 0x6e, 0x6f, 0x66, 0x65, 0x65,
	0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_annofeed_v1_datas_proto_rawDescOnce sync.Once
	file_annofeed_v1_datas_proto_rawDescData = file_annofeed_v1_datas_proto_rawDesc
)

func file_annofeed_v1_datas_proto_rawDescGZIP() []byte {
	file_annofeed_v1_datas_proto_rawDescOnce.Do(func() {
		file_annofeed_v1_datas_proto_rawDescData = protoimpl.X.CompressGZIP(file_annofeed_v1_datas_proto_rawDescData)
	})
	return file_annofeed_v1_datas_proto_rawDescData
}

var file_annofeed_v1_datas_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_annofeed_v1_datas_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_annofeed_v1_datas_proto_goTypes = []interface{}{
	(Data_State_Enum)(0),               // 0: annofeed.v1.Data.State.Enum
	(ParseDataRequest_Option)(0),       // 1: annofeed.v1.ParseDataRequest.Option
	(*CreateDataRequest)(nil),          // 2: annofeed.v1.CreateDataRequest
	(*UpdateDataRequest)(nil),          // 3: annofeed.v1.UpdateDataRequest
	(*DeleteDataRequest)(nil),          // 4: annofeed.v1.DeleteDataRequest
	(*GetDataRequest)(nil),             // 5: annofeed.v1.GetDataRequest
	(*ListDataRequest)(nil),            // 6: annofeed.v1.ListDataRequest
	(*ListDataReply)(nil),              // 7: annofeed.v1.ListDataReply
	(*Data)(nil),                       // 8: annofeed.v1.Data
	(*GetDataElementsRequest)(nil),     // 9: annofeed.v1.GetDataElementsRequest
	(*GetDataElementsReply)(nil),       // 10: annofeed.v1.GetDataElementsReply
	(*FindDataElementsRequest)(nil),    // 11: annofeed.v1.FindDataElementsRequest
	(*GetDataMetaRequest)(nil),         // 12: annofeed.v1.GetDataMetaRequest
	(*GetDataMetaReply)(nil),           // 13: annofeed.v1.GetDataMetaReply
	(*SetRawdataEmbeddingRequest)(nil), // 14: annofeed.v1.SetRawdataEmbeddingRequest
	(*SetRawdataEmbeddingReply)(nil),   // 15: annofeed.v1.SetRawdataEmbeddingReply
	(*ParseDataRequest)(nil),           // 16: annofeed.v1.ParseDataRequest
	(*Data_State)(nil),                 // 17: annofeed.v1.Data.State
	nil,                                // 18: annofeed.v1.GetDataMetaReply.MetadataEntry
	(v1.Element_Type_Enum)(0),          // 19: anno.v1.Element.Type.Enum
	(*v1.Source)(nil),                  // 20: anno.v1.Source
	(*timestamppb.Timestamp)(nil),      // 21: google.protobuf.Timestamp
	(*v1.DataValidationSummary)(nil),   // 22: anno.v1.DataValidationSummary
	(*v1.Element)(nil),                 // 23: anno.v1.Element
	(*types.Filelist)(nil),             // 24: types.Filelist
	(*emptypb.Empty)(nil),              // 25: google.protobuf.Empty
}
var file_annofeed_v1_datas_proto_depIdxs = []int32{
	19, // 0: annofeed.v1.CreateDataRequest.type:type_name -> anno.v1.Element.Type.Enum
	20, // 1: annofeed.v1.CreateDataRequest.source:type_name -> anno.v1.Source
	2,  // 2: annofeed.v1.UpdateDataRequest.data:type_name -> annofeed.v1.CreateDataRequest
	0,  // 3: annofeed.v1.ListDataRequest.states:type_name -> annofeed.v1.Data.State.Enum
	8,  // 4: annofeed.v1.ListDataReply.datas:type_name -> annofeed.v1.Data
	19, // 5: annofeed.v1.Data.type:type_name -> anno.v1.Element.Type.Enum
	20, // 6: annofeed.v1.Data.source:type_name -> anno.v1.Source
	0,  // 7: annofeed.v1.Data.state:type_name -> annofeed.v1.Data.State.Enum
	21, // 8: annofeed.v1.Data.created_at:type_name -> google.protobuf.Timestamp
	22, // 9: annofeed.v1.Data.summary:type_name -> anno.v1.DataValidationSummary
	23, // 10: annofeed.v1.GetDataElementsReply.elements:type_name -> anno.v1.Element
	18, // 11: annofeed.v1.GetDataMetaReply.metadata:type_name -> annofeed.v1.GetDataMetaReply.MetadataEntry
	24, // 12: annofeed.v1.GetDataMetaReply.metafiles:type_name -> types.Filelist
	1,  // 13: annofeed.v1.ParseDataRequest.option:type_name -> annofeed.v1.ParseDataRequest.Option
	2,  // 14: annofeed.v1.Datas.CreateData:input_type -> annofeed.v1.CreateDataRequest
	3,  // 15: annofeed.v1.Datas.UpdateData:input_type -> annofeed.v1.UpdateDataRequest
	4,  // 16: annofeed.v1.Datas.DeleteData:input_type -> annofeed.v1.DeleteDataRequest
	5,  // 17: annofeed.v1.Datas.GetData:input_type -> annofeed.v1.GetDataRequest
	6,  // 18: annofeed.v1.Datas.ListData:input_type -> annofeed.v1.ListDataRequest
	9,  // 19: annofeed.v1.Datas.GetDataElements:input_type -> annofeed.v1.GetDataElementsRequest
	11, // 20: annofeed.v1.Datas.FindDataElements:input_type -> annofeed.v1.FindDataElementsRequest
	12, // 21: annofeed.v1.Datas.GetDataMeta:input_type -> annofeed.v1.GetDataMetaRequest
	14, // 22: annofeed.v1.Datas.SetRawdataEmbedding:input_type -> annofeed.v1.SetRawdataEmbeddingRequest
	5,  // 23: annofeed.v1.Datas.GetDataValidationSummary:input_type -> annofeed.v1.GetDataRequest
	16, // 24: annofeed.v1.Datas.ParseData:input_type -> annofeed.v1.ParseDataRequest
	8,  // 25: annofeed.v1.Datas.CreateData:output_type -> annofeed.v1.Data
	8,  // 26: annofeed.v1.Datas.UpdateData:output_type -> annofeed.v1.Data
	25, // 27: annofeed.v1.Datas.DeleteData:output_type -> google.protobuf.Empty
	8,  // 28: annofeed.v1.Datas.GetData:output_type -> annofeed.v1.Data
	7,  // 29: annofeed.v1.Datas.ListData:output_type -> annofeed.v1.ListDataReply
	10, // 30: annofeed.v1.Datas.GetDataElements:output_type -> annofeed.v1.GetDataElementsReply
	10, // 31: annofeed.v1.Datas.FindDataElements:output_type -> annofeed.v1.GetDataElementsReply
	13, // 32: annofeed.v1.Datas.GetDataMeta:output_type -> annofeed.v1.GetDataMetaReply
	15, // 33: annofeed.v1.Datas.SetRawdataEmbedding:output_type -> annofeed.v1.SetRawdataEmbeddingReply
	22, // 34: annofeed.v1.Datas.GetDataValidationSummary:output_type -> anno.v1.DataValidationSummary
	25, // 35: annofeed.v1.Datas.ParseData:output_type -> google.protobuf.Empty
	25, // [25:36] is the sub-list for method output_type
	14, // [14:25] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_annofeed_v1_datas_proto_init() }
func file_annofeed_v1_datas_proto_init() {
	if File_annofeed_v1_datas_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_annofeed_v1_datas_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_datas_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_datas_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_datas_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_datas_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_datas_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDataReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_datas_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_datas_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataElementsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_datas_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataElementsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_datas_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindDataElementsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_datas_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataMetaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_datas_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataMetaReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_datas_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRawdataEmbeddingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_datas_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRawdataEmbeddingReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_datas_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParseDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_annofeed_v1_datas_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_State); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_annofeed_v1_datas_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_annofeed_v1_datas_proto_goTypes,
		DependencyIndexes: file_annofeed_v1_datas_proto_depIdxs,
		EnumInfos:         file_annofeed_v1_datas_proto_enumTypes,
		MessageInfos:      file_annofeed_v1_datas_proto_msgTypes,
	}.Build()
	File_annofeed_v1_datas_proto = out.File
	file_annofeed_v1_datas_proto_rawDesc = nil
	file_annofeed_v1_datas_proto_goTypes = nil
	file_annofeed_v1_datas_proto_depIdxs = nil
}
