// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.3
// source: anno/v1/lottpl.proto

package anno

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Lottpls_CreateLottpl_FullMethodName = "/anno.v1.Lottpls/CreateLottpl"
	Lottpls_UpdateLottpl_FullMethodName = "/anno.v1.Lottpls/UpdateLottpl"
	Lottpls_DeleteLottpl_FullMethodName = "/anno.v1.Lottpls/DeleteLottpl"
	Lottpls_GetLottpl_FullMethodName    = "/anno.v1.Lottpls/GetLottpl"
	Lottpls_ListLottpl_FullMethodName   = "/anno.v1.Lottpls/ListLottpl"
)

// LottplsClient is the client API for Lottpls service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LottplsClient interface {
	CreateLottpl(ctx context.Context, in *CreateLottplRequest, opts ...grpc.CallOption) (*Lottpl, error)
	UpdateLottpl(ctx context.Context, in *UpdateLottplRequest, opts ...grpc.CallOption) (*Lottpl, error)
	DeleteLottpl(ctx context.Context, in *DeleteLottplRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetLottpl(ctx context.Context, in *GetLottplRequest, opts ...grpc.CallOption) (*Lottpl, error)
	ListLottpl(ctx context.Context, in *ListLottplRequest, opts ...grpc.CallOption) (*ListLottplReply, error)
}

type lottplsClient struct {
	cc grpc.ClientConnInterface
}

func NewLottplsClient(cc grpc.ClientConnInterface) LottplsClient {
	return &lottplsClient{cc}
}

func (c *lottplsClient) CreateLottpl(ctx context.Context, in *CreateLottplRequest, opts ...grpc.CallOption) (*Lottpl, error) {
	out := new(Lottpl)
	err := c.cc.Invoke(ctx, Lottpls_CreateLottpl_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lottplsClient) UpdateLottpl(ctx context.Context, in *UpdateLottplRequest, opts ...grpc.CallOption) (*Lottpl, error) {
	out := new(Lottpl)
	err := c.cc.Invoke(ctx, Lottpls_UpdateLottpl_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lottplsClient) DeleteLottpl(ctx context.Context, in *DeleteLottplRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Lottpls_DeleteLottpl_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lottplsClient) GetLottpl(ctx context.Context, in *GetLottplRequest, opts ...grpc.CallOption) (*Lottpl, error) {
	out := new(Lottpl)
	err := c.cc.Invoke(ctx, Lottpls_GetLottpl_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lottplsClient) ListLottpl(ctx context.Context, in *ListLottplRequest, opts ...grpc.CallOption) (*ListLottplReply, error) {
	out := new(ListLottplReply)
	err := c.cc.Invoke(ctx, Lottpls_ListLottpl_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LottplsServer is the server API for Lottpls service.
// All implementations must embed UnimplementedLottplsServer
// for forward compatibility
type LottplsServer interface {
	CreateLottpl(context.Context, *CreateLottplRequest) (*Lottpl, error)
	UpdateLottpl(context.Context, *UpdateLottplRequest) (*Lottpl, error)
	DeleteLottpl(context.Context, *DeleteLottplRequest) (*emptypb.Empty, error)
	GetLottpl(context.Context, *GetLottplRequest) (*Lottpl, error)
	ListLottpl(context.Context, *ListLottplRequest) (*ListLottplReply, error)
	mustEmbedUnimplementedLottplsServer()
}

// UnimplementedLottplsServer must be embedded to have forward compatible implementations.
type UnimplementedLottplsServer struct {
}

func (UnimplementedLottplsServer) CreateLottpl(context.Context, *CreateLottplRequest) (*Lottpl, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLottpl not implemented")
}
func (UnimplementedLottplsServer) UpdateLottpl(context.Context, *UpdateLottplRequest) (*Lottpl, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLottpl not implemented")
}
func (UnimplementedLottplsServer) DeleteLottpl(context.Context, *DeleteLottplRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLottpl not implemented")
}
func (UnimplementedLottplsServer) GetLottpl(context.Context, *GetLottplRequest) (*Lottpl, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLottpl not implemented")
}
func (UnimplementedLottplsServer) ListLottpl(context.Context, *ListLottplRequest) (*ListLottplReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLottpl not implemented")
}
func (UnimplementedLottplsServer) mustEmbedUnimplementedLottplsServer() {}

// UnsafeLottplsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LottplsServer will
// result in compilation errors.
type UnsafeLottplsServer interface {
	mustEmbedUnimplementedLottplsServer()
}

func RegisterLottplsServer(s grpc.ServiceRegistrar, srv LottplsServer) {
	s.RegisterService(&Lottpls_ServiceDesc, srv)
}

func _Lottpls_CreateLottpl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLottplRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LottplsServer).CreateLottpl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottpls_CreateLottpl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LottplsServer).CreateLottpl(ctx, req.(*CreateLottplRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottpls_UpdateLottpl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLottplRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LottplsServer).UpdateLottpl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottpls_UpdateLottpl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LottplsServer).UpdateLottpl(ctx, req.(*UpdateLottplRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottpls_DeleteLottpl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLottplRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LottplsServer).DeleteLottpl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottpls_DeleteLottpl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LottplsServer).DeleteLottpl(ctx, req.(*DeleteLottplRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottpls_GetLottpl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLottplRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LottplsServer).GetLottpl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottpls_GetLottpl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LottplsServer).GetLottpl(ctx, req.(*GetLottplRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottpls_ListLottpl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLottplRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LottplsServer).ListLottpl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottpls_ListLottpl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LottplsServer).ListLottpl(ctx, req.(*ListLottplRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Lottpls_ServiceDesc is the grpc.ServiceDesc for Lottpls service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Lottpls_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "anno.v1.Lottpls",
	HandlerType: (*LottplsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateLottpl",
			Handler:    _Lottpls_CreateLottpl_Handler,
		},
		{
			MethodName: "UpdateLottpl",
			Handler:    _Lottpls_UpdateLottpl_Handler,
		},
		{
			MethodName: "DeleteLottpl",
			Handler:    _Lottpls_DeleteLottpl_Handler,
		},
		{
			MethodName: "GetLottpl",
			Handler:    _Lottpls_GetLottpl_Handler,
		},
		{
			MethodName: "ListLottpl",
			Handler:    _Lottpls_ListLottpl_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "anno/v1/lottpl.proto",
}
