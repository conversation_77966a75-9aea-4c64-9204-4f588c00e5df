package style

import (
	"context"
	"fmt"
	"os"
	"path"

	"annofeed/internal/biz"
	"annofeed/workflow/common"
	"annofeed/workflow/style/bosch"
	"annofeed/workflow/style/ecarx"
	"annofeed/workflow/style/qcraft"
	"annofeed/workflow/style/singlestream"
	"annofeed/workflow/style/varylidar"

	"gitlab.rp.konvery.work/platform/pkg/kfs"
	"gitlab.rp.konvery.work/platform/pkg/wf/wfutil"
)

type Heartbeat = common.Heartbeat

func Transform(ctx context.Context, data *biz.Data, dir string, startIdx int, hb Heartbeat) (dataType string, err error) {
	fmt.Println("---> data source style: ", data.Source.E.Style)
	switch typ := data.Source.E.Style; typ {
	case "", "default":
		err = expandClipParams(ctx, dir)
	case "qcraft":
		dataType, err = qcraft.Transform(ctx, data, dir, startIdx, hb)
	case "ecarx":
		dataType, err = ecarx.Transform(ctx, data, dir, startIdx, hb)
	case "single_stream", "single_image", "deeproute_image":
		dataType, err = singlestream.Transform(ctx, data, dir, startIdx, hb)
	case "vary_lidar":
		dataType, err = varylidar.Transform(ctx, data, dir, startIdx, hb)
	case "bosch":
		dataType, err = bosch.Transform(ctx, data, dir, startIdx, hb)
	default:
		err = fmt.Errorf("unsupported transformer " + typ)
	}
	if err != nil {
		err = wfutil.NewNonRetryableError("failed to do transform", err)
	}
	return
}

// expandClipParams generates elem param files from the clip param file.
// Note: this function only works for params v2.
func expandClipParams(ctx context.Context, dir string) error {
	err := kfs.IterateDir(dir, func(entry os.DirEntry, subdir string, depth int) error {
		// skip directories and non-param files
		if entry.IsDir() || entry.Name() != common.ParamsFile {
			return nil
		}
		fmt.Printf("---> expandClipParams: entry.Name %s\n", entry.Name())
		paramData, err := biz.LoadElemParamData(dir, subdir, common.ParamsFile)
		if err != nil {
			return err
		}

		return saveElemParams(dir, subdir, paramData)
	})
	return err
}

func saveElemParams(dir, subdir string, param *biz.ElemParamData) error {
	fmt.Printf("---> saveElemParams: paramsVer: %s, lidarsLen: %d\n", param.ElemParam.ElemParamV2, len(param.ElemParam.ElemParamV2.GetLidars()))
	if param.ElemParam.ElemParamV2 == nil || len(param.ElemParam.ElemParamV2.GetLidars()) == 0 {
		fmt.Println("---> failed save elem params - 1.")
		return nil
	}

	// split lidars params in clip params to each element
	entries, err := os.ReadDir(path.Join(dir, subdir))
	if err != nil {
		fmt.Println("---> failed save elem params - 2.")
		return err
	}

	var elems []string
	for _, entry := range entries {
		if !entry.IsDir() || common.NonstandardDirs[entry.Name()] {
			continue
		}
		elems = append(elems, entry.Name())
	}

	if len(elems) != len(param.ElemParam.ElemParamV2.GetLidars()) {
		fmt.Println("---> elems len: ", len(elems), " subdir: ", subdir)
		fmt.Println("---> lidars len: ", len(param.ElemParam.ElemParamV2.GetLidars()))
		return fmt.Errorf("elems quantity does not match clip lidars")
	}

	dummyElemParamGroup := &biz.ElemParamGroup{}
	for i, elem := range elems {
		newElemParam := &biz.ElemParamV2{
			Meta:    param.ElemParam.ElemParamV2.Meta,
			Lidar:   param.ElemParam.ElemParamV2.Lidars[i],
			Cameras: param.ElemParam.ElemParamV2.Cameras,
		}
		dummyElemParamGroup.ElemParamV2 = newElemParam
		if err := dummyElemParamGroup.WriteFile(path.Join(dir, subdir, elem, common.ParamsFile)); err != nil {
			return err
		}
	}
	fmt.Printf("---> saved elem params - remove params file: %s\n.", path.Join(dir, subdir, common.ParamsFile))
	if err := os.Remove(path.Join(dir, subdir, common.ParamsFile)); err != nil {
		return fmt.Errorf("failed to remove clip param file: %w", err)
	}
	return nil
}
