apiVersion: v2
name: keto
icon: https://raw.githubusercontent.com/ory/docs/master/docs/static/img/logo-keto.svg
description: Access Control Policies as a Server
type: application
home: https://www.ory.sh/keto/
keywords:
  - rbac
  - hrbac
  - acl
  - iam
  - api-security
  - security
sources:
  - https://github.com/ory/keto
  - https://github.com/ory/k8s
maintainers:
  - name: ORY Team
    email: <EMAIL>
    url: https://www.ory.sh/
# This is the chart version. This version number should be incremented each time you make changes
# to the chart and its templates, including the app version.
# Versions are expected to follow Semantic Versioning (https://semver.org/)
version: 0.1.0

# This is the version number of the application being deployed. This version number should be
# incremented each time you make changes to the application. Versions are not expected to
# follow Semantic Versioning. They should reflect the version the application is using.
# It is recommended to use it with quotes.
appVersion: "v0.1.0"
