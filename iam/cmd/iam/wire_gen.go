// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"iam/internal/biz"
	"iam/internal/conf"
	"iam/internal/data"
	"iam/internal/server"
	"iam/internal/service"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, dangerTest *conf.DangerTest, logger log.Logger) (*kratos.App, func(), error) {
	dataData, cleanup, err := data.NewData(confData, logger)
	if err != nil {
		return nil, nil, err
	}
	metasRepo := data.NewMetasRepo(dataData, logger)
	configsService := service.NewConfigsService(metasRepo, logger)
	permsRepo := data.NewPermsRepo(dataData, logger)
	permsBiz := biz.NewPermsBiz(permsRepo, logger)
	permsService := service.NewPermsService(permsBiz)
	policiesService := service.NewPoliciesService()
	rolesRepo := data.NewRolesRepo(dataData, logger)
	rolesBiz := biz.NewRolesBiz(rolesRepo, logger)
	rolesService := service.NewRolesService(rolesBiz, rolesRepo)
	teamsRepo := data.NewTeamsRepo(dataData, logger)
	usersRepo := data.NewUsersRepo(dataData, logger)
	teamsBiz := biz.NewTeamsBiz(teamsRepo, usersRepo, logger)
	usersBiz := biz.NewUsersBiz(usersRepo, metasRepo, logger, dangerTest)
	teamsService := service.NewTeamsService(teamsBiz, usersBiz)
	bizgrantsRepo := data.NewBizgrantsRepo(dataData, logger)
	bizgrantsBiz := biz.NewBizgrantsBiz(bizgrantsRepo, logger)
	usersService := service.NewUsersService(usersBiz, teamsBiz, permsBiz, rolesRepo, bizgrantsBiz, logger)
	bizgrantsService := service.NewBizgrantsService(bizgrantsBiz, bizgrantsRepo)
	httpServer := server.NewHTTPServer(confServer, configsService, permsService, policiesService, rolesService, teamsService, usersService, usersBiz, bizgrantsService, logger)
	grpcServer := server.NewGRPCServer(confServer, configsService, permsService, policiesService, rolesService, teamsService, usersService, usersBiz, bizgrantsService, logger)
	app := newApp(logger, httpServer, grpcServer)
	return app, func() {
		cleanup()
	}, nil
}
