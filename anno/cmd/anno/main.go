package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"time"

	"anno/api/client"
	"anno/internal/conf"
	"anno/internal/data"
	"anno/internal/mq"
	"anno/internal/ostore"
	"anno/workflow"
	"anno/workflow/jobwf"
	"anno/workflow/lotwf"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/env"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	ilog "gitlab.rp.konvery.work/platform/pkg/log"
	"gitlab.rp.konvery.work/platform/pkg/otel"
)

var (
	// Name is the name of the compiled software.
	Name string = "anno"
	// flagconf is the config flag.
	flagconf string

	id, _ = os.Hostname()
)

func init() {
	flag.StringVar(&flagconf, "conf", "../../configs", "config path, eg: -conf config.yaml")
}

type App struct {
	*kratos.App
	lact *lotwf.Activities
	jact *jobwf.Activities
}

func newApp(logger log.Logger, gs *grpc.Server, hs *http.Server, lact *lotwf.Activities, jact *jobwf.Activities,
) *App {
	return &App{
		App: kratos.New(
			kratos.ID(id),
			kratos.Name(Name),
			kratos.Version(conf.Version),
			kratos.Metadata(map[string]string{}),
			kratos.Logger(logger),
			kratos.RegistrarTimeout(20*time.Second),
			kratos.StopTimeout(20*time.Second),
			kratos.Server(
				gs,
				hs,
			),
		),
		lact: lact,
		jact: jact,
	}
}

func main() {
	flag.Parse()
	c := config.New(
		config.WithSource(
			file.NewSource(flagconf),
			env.NewSource(),
		),
		config.WithResolver(CustomResolver),
	)
	defer c.Close()

	if err := c.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}
	logger := ilog.GetLogger(&ilog.Config{
		Level:  bc.Otel.Log.Level,
		Format: bc.Otel.Log.Format,
	})
	log.SetLogger(logger)

	switch flag.Arg(0) {
	case "migrate":
		data.Migrate(bc.Data.Database, flag.Arg(1))
		return
	case "create-all-policies": // recreate access policies for all objects
		am := newAccessMgr(&bc, logger)
		if err := am.CreateAllPolicies(); err != nil {
			fmt.Println("failed to recreate policies:", err)
			os.Exit(1)
		}
		am.Close()
		return
	}

	ostore.Init(bc.ObjectStorage)

	if bc.Mq.Redis == nil {
		bc.Mq.Redis = bc.Data.Redis
	}
	mq.Init(bc.Mq, logger)

	client.Init(&bc)
	app, cleanup, err := wireApp(bc.Server, bc.Data, logger)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	otelcfg := otel.NewOtelCfg(bc.Otel.GetLog(), bc.Otel.GetMetrics(), bc.Otel.GetTracing())
	shutdown, err := otel.InitOtel(Name, conf.Version, otelcfg, logger)
	if err != nil {
		panic(err)
	}
	defer shutdown()

	workflow.Init(bc.Temporal, logger)
	if !bc.Temporal.DisableWorker {
		workflow.StartWorker(app.lact, app.jact)
	}

	ctx, stopmq := context.WithCancel(context.Background())
	defer stopmq()
	err = mq.StartConsumer(ctx, bc.Mq.Consumer)
	if err != nil {
		panic(fmt.Errorf("failed to start consumer: %w", err))
	}

	// start and wait for stop signal
	log.Infof("[main] current version: %s", conf.Version)
	if err := app.Run(); err != nil {
		panic(err)
	}
}
