// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: anno/v1/lottpl.proto

package anno

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateLottplRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLottplRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLottplRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLottplRequestMultiError, or nil if none found.
func (m *CreateLottplRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLottplRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for Desc

	if _, ok := _CreateLottplRequest_Type_NotInLookup[m.GetType()]; ok {
		err := CreateLottplRequestValidationError{
			field:  "Type",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := Lot_Type_Enum_name[int32(m.GetType())]; !ok {
		err := CreateLottplRequestValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOntologies()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLottplRequestValidationError{
					field:  "Ontologies",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLottplRequestValidationError{
					field:  "Ontologies",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOntologies()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLottplRequestValidationError{
				field:  "Ontologies",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPhases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateLottplRequestValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateLottplRequestValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateLottplRequestValidationError{
					field:  fmt.Sprintf("Phases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Instruction

	if all {
		switch v := interface{}(m.GetOut()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLottplRequestValidationError{
					field:  "Out",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLottplRequestValidationError{
					field:  "Out",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOut()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLottplRequestValidationError{
				field:  "Out",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for JobSize

	// no validation rules for WorkRange

	if len(errors) > 0 {
		return CreateLottplRequestMultiError(errors)
	}

	return nil
}

// CreateLottplRequestMultiError is an error wrapping multiple validation
// errors returned by CreateLottplRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateLottplRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLottplRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLottplRequestMultiError) AllErrors() []error { return m }

// CreateLottplRequestValidationError is the validation error returned by
// CreateLottplRequest.Validate if the designated constraints aren't met.
type CreateLottplRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLottplRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLottplRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLottplRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLottplRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLottplRequestValidationError) ErrorName() string {
	return "CreateLottplRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLottplRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLottplRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLottplRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLottplRequestValidationError{}

var _CreateLottplRequest_Type_NotInLookup = map[Lot_Type_Enum]struct{}{
	0: {},
}

// Validate checks the field values on UpdateLottplRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateLottplRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLottplRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLottplRequestMultiError, or nil if none found.
func (m *UpdateLottplRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLottplRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLottpl()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLottplRequestValidationError{
					field:  "Lottpl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLottplRequestValidationError{
					field:  "Lottpl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLottpl()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLottplRequestValidationError{
				field:  "Lottpl",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateLottplRequestMultiError(errors)
	}

	return nil
}

// UpdateLottplRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateLottplRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateLottplRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLottplRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLottplRequestMultiError) AllErrors() []error { return m }

// UpdateLottplRequestValidationError is the validation error returned by
// UpdateLottplRequest.Validate if the designated constraints aren't met.
type UpdateLottplRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLottplRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLottplRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLottplRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLottplRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLottplRequestValidationError) ErrorName() string {
	return "UpdateLottplRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLottplRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLottplRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLottplRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLottplRequestValidationError{}

// Validate checks the field values on DeleteLottplRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteLottplRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteLottplRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteLottplRequestMultiError, or nil if none found.
func (m *DeleteLottplRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteLottplRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return DeleteLottplRequestMultiError(errors)
	}

	return nil
}

// DeleteLottplRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteLottplRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteLottplRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteLottplRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteLottplRequestMultiError) AllErrors() []error { return m }

// DeleteLottplRequestValidationError is the validation error returned by
// DeleteLottplRequest.Validate if the designated constraints aren't met.
type DeleteLottplRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteLottplRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteLottplRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteLottplRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteLottplRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteLottplRequestValidationError) ErrorName() string {
	return "DeleteLottplRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteLottplRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteLottplRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteLottplRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteLottplRequestValidationError{}

// Validate checks the field values on GetLottplRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetLottplRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLottplRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLottplRequestMultiError, or nil if none found.
func (m *GetLottplRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLottplRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	if len(errors) > 0 {
		return GetLottplRequestMultiError(errors)
	}

	return nil
}

// GetLottplRequestMultiError is an error wrapping multiple validation errors
// returned by GetLottplRequest.ValidateAll() if the designated constraints
// aren't met.
type GetLottplRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLottplRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLottplRequestMultiError) AllErrors() []error { return m }

// GetLottplRequestValidationError is the validation error returned by
// GetLottplRequest.Validate if the designated constraints aren't met.
type GetLottplRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLottplRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLottplRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLottplRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLottplRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLottplRequestValidationError) ErrorName() string { return "GetLottplRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetLottplRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLottplRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLottplRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLottplRequestValidationError{}

// Validate checks the field values on ListLottplRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListLottplRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLottplRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListLottplRequestMultiError, or nil if none found.
func (m *ListLottplRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLottplRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	if val := m.GetPagesz(); val < 0 || val > 100 {
		err := ListLottplRequestValidationError{
			field:  "Pagesz",
			reason: "value must be inside range [0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for OrgUid

	// no validation rules for CreatorUid

	// no validation rules for NamePattern

	// no validation rules for Type

	if len(errors) > 0 {
		return ListLottplRequestMultiError(errors)
	}

	return nil
}

// ListLottplRequestMultiError is an error wrapping multiple validation errors
// returned by ListLottplRequest.ValidateAll() if the designated constraints
// aren't met.
type ListLottplRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLottplRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLottplRequestMultiError) AllErrors() []error { return m }

// ListLottplRequestValidationError is the validation error returned by
// ListLottplRequest.Validate if the designated constraints aren't met.
type ListLottplRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLottplRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLottplRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLottplRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLottplRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLottplRequestValidationError) ErrorName() string {
	return "ListLottplRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListLottplRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLottplRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLottplRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLottplRequestValidationError{}

// Validate checks the field values on ListLottplReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListLottplReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLottplReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListLottplReplyMultiError, or nil if none found.
func (m *ListLottplReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLottplReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetLottpls() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListLottplReplyValidationError{
						field:  fmt.Sprintf("Lottpls[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListLottplReplyValidationError{
						field:  fmt.Sprintf("Lottpls[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListLottplReplyValidationError{
					field:  fmt.Sprintf("Lottpls[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListLottplReplyMultiError(errors)
	}

	return nil
}

// ListLottplReplyMultiError is an error wrapping multiple validation errors
// returned by ListLottplReply.ValidateAll() if the designated constraints
// aren't met.
type ListLottplReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLottplReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLottplReplyMultiError) AllErrors() []error { return m }

// ListLottplReplyValidationError is the validation error returned by
// ListLottplReply.Validate if the designated constraints aren't met.
type ListLottplReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLottplReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLottplReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLottplReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLottplReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLottplReplyValidationError) ErrorName() string { return "ListLottplReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListLottplReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLottplReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLottplReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLottplReplyValidationError{}

// Validate checks the field values on Lottpl with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Lottpl) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Lottpl with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in LottplMultiError, or nil if none found.
func (m *Lottpl) ValidateAll() error {
	return m.validate(true)
}

func (m *Lottpl) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_Lottpl_Uid_Pattern.MatchString(m.GetUid()) {
		err := LottplValidationError{
			field:  "Uid",
			reason: "value does not match regex pattern \"^[a-z0-9]{11}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Name

	// no validation rules for Desc

	if _, ok := _Lottpl_Type_NotInLookup[m.GetType()]; ok {
		err := LottplValidationError{
			field:  "Type",
			reason: "value must not be in list [unspecified]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := Lot_Type_Enum_name[int32(m.GetType())]; !ok {
		err := LottplValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOntologies()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LottplValidationError{
					field:  "Ontologies",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LottplValidationError{
					field:  "Ontologies",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOntologies()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LottplValidationError{
				field:  "Ontologies",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPhases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LottplValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LottplValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LottplValidationError{
					field:  fmt.Sprintf("Phases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Instruction

	if all {
		switch v := interface{}(m.GetOut()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LottplValidationError{
					field:  "Out",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LottplValidationError{
					field:  "Out",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOut()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LottplValidationError{
				field:  "Out",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for JobSize

	// no validation rules for WorkRange

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LottplValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LottplValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LottplValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LottplValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LottplValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LottplValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LottplMultiError(errors)
	}

	return nil
}

// LottplMultiError is an error wrapping multiple validation errors returned by
// Lottpl.ValidateAll() if the designated constraints aren't met.
type LottplMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LottplMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LottplMultiError) AllErrors() []error { return m }

// LottplValidationError is the validation error returned by Lottpl.Validate if
// the designated constraints aren't met.
type LottplValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LottplValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LottplValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LottplValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LottplValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LottplValidationError) ErrorName() string { return "LottplValidationError" }

// Error satisfies the builtin error interface
func (e LottplValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLottpl.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LottplValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LottplValidationError{}

var _Lottpl_Uid_Pattern = regexp.MustCompile("^[a-z0-9]{11}$")

var _Lottpl_Type_NotInLookup = map[Lot_Type_Enum]struct{}{
	0: {},
}
