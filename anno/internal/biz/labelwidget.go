// source: anno/v1/labelwidget.proto
package biz

import (
	"context"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

type Labelwidget struct {
	ID    int64  `json:"id" gorm:"default:null"`
	Name  string `json:"name" gorm:"default:null"`
	Langs StrMap `json:"langs" gorm:"default:null"`
}

func (o *Labelwidget) GetID() int64 { return o.ID }
func (o *Labelwidget) EnsureID() {
	if o.ID == 0 {
		o.ID = kid.NewID()
	}
}
func (o *Labelwidget) GetUid() string { return kid.StringID(o.ID) }
func (o *Labelwidget) UidFld() string { return LabelwidgetSfldName }

const LabelwidgetTableName = "labelwidgets"

var (
	Labelwidget_             = field.RegObject(&Labelwidget{})
	LabelwidgetUpdatableFlds = field.NewModel(LabelwidgetTableName, Labelwidget_, "langs")

	LabelwidgetSfldName  = field.Sname(&Labelwidget_.Name)
	LabelwidgetSfldLangs = field.Sname(&Labelwidget_.Langs)
)

type LabelwidgetListFilter struct {
	Uids        []string
	NamePattern string
}

type LabelwidgetsRepo interface {
	Create(context.Context, *Labelwidget) (*Labelwidget, error)
	Update(context.Context, *Labelwidget, *FieldMask) (*Labelwidget, error)
	GetByID(context.Context, int64) (*Labelwidget, error)
	GetByUid(context.Context, string) (*Labelwidget, error)
	DeleteByID(context.Context, int64) error
	DeleteByUid(context.Context, string) error
	List(context.Context, *LabelwidgetListFilter, Pager) ([]*Labelwidget, error)
	Count(context.Context, *LabelwidgetListFilter) (int, error)
}

type LabelwidgetsBiz struct {
	repo LabelwidgetsRepo
	log  *log.Helper
}

func NewLabelwidgetsBiz(repo LabelwidgetsRepo, logger log.Logger) *LabelwidgetsBiz {
	return &LabelwidgetsBiz{repo: repo, log: log.NewHelper(logger)}
}

func (o *LabelwidgetsBiz) Create(ctx context.Context, p *Labelwidget) (*Labelwidget, error) {
	o.log.Info(ctx, "CreateLabelwidget", "param", p)
	return o.repo.Create(ctx, p)
}

func (o *LabelwidgetsBiz) Update(ctx context.Context, p *Labelwidget, fldMask *FieldMask) (*Labelwidget, error) {
	o.log.Info(ctx, "UpdateLabelwidget", "param", p)
	return o.repo.Update(ctx, p, fldMask)
}

func (o *LabelwidgetsBiz) GetByID(ctx context.Context, id int64) (*Labelwidget, error) {
	return o.repo.GetByID(ctx, id)
}

func (o *LabelwidgetsBiz) GetByUid(ctx context.Context, uid string) (*Labelwidget, error) {
	return o.repo.GetByUid(ctx, uid)
}

func (o *LabelwidgetsBiz) DeleteByID(ctx context.Context, id int64) error {
	o.log.Info(ctx, "DeleteByID", "id", id)
	return o.repo.DeleteByID(ctx, id)
}

func (o *LabelwidgetsBiz) DeleteByUid(ctx context.Context, uid string) error {
	o.log.Info(ctx, "DeleteByUid", "uid", uid)
	return o.repo.DeleteByUid(ctx, uid)
}

func (o *LabelwidgetsBiz) List(ctx context.Context, filter *LabelwidgetListFilter, pager Pager) ([]*Labelwidget, error) {
	o.log.Info(ctx, "ListLabelwidget", "param", filter)
	return o.repo.List(ctx, filter, pager)
}

func (o *LabelwidgetsBiz) Count(ctx context.Context, filter *LabelwidgetListFilter) (int, error) {
	return o.repo.Count(ctx, filter)
}
