// source: anno/v1/lottpl.proto
package data

import (
	biz "anno/internal/biz"
	context "context"

	log "github.com/go-kratos/kratos/v2/log"
	gorm "gorm.io/gorm"
)

type lottplsRepo struct {
	data *Data
	log  *log.Helper
}

func NewLottplsRepo(data *Data, logger log.Logger) biz.LottplsRepo {
	return &lottplsRepo{data: data, log: log.NewHelper(logger)}
}

func (o *lottplsRepo) Create(ctx context.Context, p *biz.Lottpl) (*biz.Lottpl, error) {
	return Create(ctx, o.data, p)
}

func (o *lottplsRepo) Update(ctx context.Context, p *biz.Lottpl, fldMask *biz.FieldMask) (*biz.Lottpl, error) {
	return Update(ctx, o.data, p, biz.LottplUpdatableFlds, fldMask)
}

func (o *lottplsRepo) loadAssociations(ctx context.Context, data *Data, mod *biz.Lottpl) error {
	return nil
}

func (o *lottplsRepo) GetByID(ctx context.Context, id int64) (*biz.Lottpl, error) {
	return GetByID[biz.Lottpl](ctx, o.data, id, o.loadAssociations)
}

func (o *lottplsRepo) DeleteByID(ctx context.Context, id int64) error {
	return Delete(ctx, o.data, &biz.Lottpl{ID: id})
}

func (o *lottplsRepo) buildListQuery(ctx context.Context, p *biz.LottplListFilter) *gorm.DB {
	q := o.data.WithCtx(ctx)
	if len(p.IDs) > 0 {
		q = q.Where("id IN ?", p.IDs)
	}
	if p.OrgUid != "" {
		q = q.Where(biz.LottplSfldOrgUid+" = ?", p.OrgUid)
	}
	if p.CreatorUid != "" {
		q = q.Where(biz.LottplSfldCreatorUid+" = ?", p.CreatorUid)
	}
	if p.NamePattern != "" {
		q = q.Where(biz.LottplSfldName+" LIKE ?", "%"+p.NamePattern+"%")
	}
	if p.Type != "" {
		q = q.Where(biz.LottplSfldType+" = ?", p.Type)
	}
	return q
}

func (o *lottplsRepo) List(ctx context.Context, p *biz.LottplListFilter, pager biz.Pager) ([]*biz.Lottpl, error) {
	datas := []*biz.Lottpl{}
	q := o.buildListQuery(ctx, p).Order("id DESC")
	if len(p.IDs) == 0 {
		q = q.Offset(pager.Offset()).Limit(pager.Pagesz)
	}
	err := q.Find(&datas).Error
	if err != nil {
		return nil, err
	}
	return datas, nil
}

func (o *lottplsRepo) Count(ctx context.Context, p *biz.LottplListFilter) (int, error) {
	mod := &biz.Lottpl{}
	var cnt int64
	q := o.buildListQuery(ctx, p)
	err := q.Model(mod).Count(&cnt).Error
	if err != nil {
		return 0, err
	}
	return int(cnt), nil
}
