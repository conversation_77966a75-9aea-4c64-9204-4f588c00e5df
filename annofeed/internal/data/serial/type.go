package serial

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"reflect"

	"github.com/go-kratos/kratos/v2/encoding"
	_ "github.com/go-kratos/kratos/v2/encoding/json"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

var codec = encoding.GetCodec("json")

type Type[T any] struct {
	E T `json:"e"`
}

func New[T any](e T) *Type[T] {
	return &Type[T]{E: e}
}

// Scan scan value into the object, implements sql.Scanner interface
func (o *Type[T]) Scan(value interface{}) error {
	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("failed to unmarshal value: %#v", value)
	}

	if _, ok := any(o.E).(proto.Message); ok {
		w := &struct {
			E json.RawMessage `json:"e"`
		}{}
		err := json.Unmarshal(bytes, w)
		if err != nil {
			return err
		}
		return codec.Unmarshal(w.E, &o.E)
	}

	return codec.Unmarshal(bytes, o)
}

// Value return json value, implement driver.Valuer interface
func (o Type[T]) Value() (driver.Value, error) {
	if v := reflect.ValueOf(o.E); v.IsZero() {
		return nil, nil
	}

	if _, ok := any(o.E).(proto.Message); ok {
		data, err := codec.Marshal(o.E)
		if err != nil {
			return nil, err
		}

		w := &struct {
			E json.RawMessage `json:"e"`
		}{
			E: data,
		}

		return json.Marshal(w)
	}

	return json.Marshal(o)
}

// GormDataType gorm common data type
func (Type[T]) GormDataType() string {
	return "serializable"
}

// GormDBDataType gorm db data type
func (Type[T]) GormDBDataType(db *gorm.DB, field *schema.Field) string {
	switch db.Dialector.Name() {
	case "sqlite", "mysql":
		return "JSON"
	case "postgres":
		return "JSONB"
	}
	return ""
}

// // MarshalJSON to output non base64 encoded []byte
// func (o Serial[T]) MarshalJSON() ([]byte, error) {
// 	return json.Marshal(o)
// }

// // UnmarshalJSON to deserialize []byte
// func (o *Serial[T]) UnmarshalJSON(b []byte) error {
// 	return json.Unmarshal(b, o)
// }

// func (o Serial[T]) GormValue(ctx context.Context, db *gorm.DB) clause.Expr {
// 	data, _ := json.Marshal(o)
// 	switch db.Dialector.Name() {
// 	case "mysql":
// 		if v, ok := db.Dialector.(*mysql.Dialector); ok && !strings.Contains(v.ServerVersion, "MariaDB") {
// 			return gorm.Expr("CAST(? AS JSON)", string(data))
// 		}
// 	}
// 	return gorm.Expr("?", string(data))
// }
