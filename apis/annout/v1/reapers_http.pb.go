// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.0
// - protoc             v4.24.4
// source: annout/v1/reapers.proto

package annout

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationReapersExportResultByReaper = "/annout.v1.Reapers/ExportResultByReaper"

type ReapersHTTPServer interface {
	ExportResultByReaper(context.Context, *GetReaperRequest) (*Reaper, error)
}

func RegisterReapersHTTPServer(s *http.Server, srv ReapersHTTPServer) {
	r := s.Route("/")
	r.PUT("/v1/reaper/{lot_uid}/export", _Reapers_ExportResultByReaper0_HTTP_Handler(srv))
}

func _Reapers_ExportResultByReaper0_HTTP_Handler(srv ReapersHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetReaperRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationReapersExportResultByReaper)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExportResultByReaper(ctx, req.(*GetReaperRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Reaper)
		return ctx.Result(200, reply)
	}
}

type ReapersHTTPClient interface {
	ExportResultByReaper(ctx context.Context, req *GetReaperRequest, opts ...http.CallOption) (rsp *Reaper, err error)
}

type ReapersHTTPClientImpl struct {
	cc *http.Client
}

func NewReapersHTTPClient(client *http.Client) ReapersHTTPClient {
	return &ReapersHTTPClientImpl{client}
}

func (c *ReapersHTTPClientImpl) ExportResultByReaper(ctx context.Context, in *GetReaperRequest, opts ...http.CallOption) (*Reaper, error) {
	var out Reaper
	pattern := "/v1/reaper/{lot_uid}/export"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationReapersExportResultByReaper))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
