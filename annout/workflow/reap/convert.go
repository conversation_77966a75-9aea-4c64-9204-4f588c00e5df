package reap

import (
	"context"
	"fmt"

	"annout/api/client"
	"annout/internal/biz"
	"annout/workflow/exporter"
)

// ReapSaveConvertedAnnos saves converted annos under `dir`.
func (o *Activities) ReapSaveConvertedAnnos(ctx context.Context, dir string, reaper *biz.Reaper) error {
	ctx = client.NewCtxUseSvcAccount(ctx)

	// get lot
	lot, err := client.GetLot(ctx, reaper.LotUid)
	if err != nil {
		return fmt.Errorf("failed to GetLot: %w", err)
	}

	if err := o.packAnnos(ctx, dir, reaper, lot, true); err != nil {
		return err
	}

	cfg, err := o.prepareUpload(ctx, reaper, lot, true)
	if err != nil {
		return err
	}
	err = o.uploadAnnos(ctx, cfg.UploadURL)
	if err != nil {
		return err
	}
	return o.postUpload(ctx, reaper, cfg, true)
}

func (o *Activities) ReapExportConvertedAnnos(ctx context.Context, dir string, reaper *biz.Reaper) (err error) {
	cfg := reaper.Config
	if cfg.Exporter == nil || cfg.Exporter.Name == "" {
		return nil
	}

	export, err := exporter.GetExporter(cfg.Exporter.Name, cfg.Exporter.Config)
	if err != nil {
		return fmt.Errorf("failed to get exporter %v: %w", cfg.Exporter.Name, err)
	}
	err = export.Export(ctx, dir, reaper)
	if err != nil {
		return fmt.Errorf("failed to export annos: %w", err)
	}
	return nil
}
