version: "3.2"
services:
  keto-migrate:
    image: oryd/keto:v0.10.0-alpha.0
    links:
      - mysqld:mysqld
    volumes:
      - type: bind
        source: ./config
        target: /home/<USER>
    environment:
      - LOG_LEVEL=debug
      - DSN=mysql://root:secret@mysqld:3306/mysql&max_conns=20&max_idle_conns=4
    command: ["migrate", "up", "-y"]
    restart: on-failure
  keto:
    links:
      - mysqld:mysqld
    volumes:
      - type: bind
        source: ./config
        target: /home/<USER>
    ports:
      - "4466:4466"
      - "4467:4467"
    depends_on:
      - keto-migrate
    environment:
      - DSN=mysql://root:secret@mysqld:3306/mysql&max_conns=20&max_idle_conns=4
  mysqld:
    image: mysql:5.7
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=secret
