workflow:
  args:
    convDataImg: 'artifactory.rp.konvery.work/docker/python:3.11' # TODO: make it configurable
    convDataCmd: '["python", "main.py"]'
    data: "{}" # reaper

  # storage:
  #   calSizeActivity:
  #     activity: ReapGetVolumeSizeGB
  #     args: ["$<data>"]

  steps:
    - type: activity
      executor: WFMkdir
      args: ['{"$<wfVolumePath>/data": 511}'] # use 511 instead of 0777 as json doesn't recognize octal numbers
    - type: activity
      executor: ReapCollectAnnos
      args: ["$<wfVolumePath>/data", "$<data>"]
      heartbeat: 60s
    # save original annos
    - type: activity
      executor: ReapSaveAnnos
      args: ["$<wfVolumePath>/data", "$<data>"]

    # convert original annos if convert script is provided
    - type: activity
      executor: ReapConverterScript
      args: ["$<wfVolumePath>/data", "$<data>"]
      resultRef: convScriptURI
    - type: activity
      executor: WFMkdir
      args: ['{"$<wfVolumePath>/convert": 511}'] # use 511 instead of 0777 as json doesn't recognize octal numbers
      when: 'convScriptURI != ""'
    - type: kubejob
      executor: convertData
      args: ["$<wfVolumePath>/data", "$<wfVolumePath>/convert"] # input, output
      when: 'convScriptURI != ""'
    - type: activity
      executor: ReapSaveConvertedAnnos
      args: ["$<wfVolumePath>/convert", "$<data>"]
      when: 'convScriptURI != ""'
    - type: activity
      executor: ReapExportConvertedAnnos
      args: [ "$<wfVolumePath>/convert", "$<data>" ]
      when: 'convScriptURI != ""'

kubeJobs:
  convertData:
    image: $<convDataImg> # docker image url and tag
    command: ["$<convDataCmd[*]>"]
    downloadFiles:
      "auto:main.py": "$<convScriptURI>"
