// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.20.3
// source: iam/v1/perm.proto

package iam

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EditPermsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Action EditAction_Enum `protobuf:"varint,1,opt,name=action,proto3,enum=iam.v1.EditAction_Enum" json:"action,omitempty"`
	// pattern: "^\\w+\\.\\w+$", max_bytes: 64
	Perms []string `protobuf:"bytes,2,rep,name=perms,proto3" json:"perms,omitempty"`
}

func (x *EditPermsRequest) Reset() {
	*x = EditPermsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_perm_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditPermsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditPermsRequest) ProtoMessage() {}

func (x *EditPermsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_perm_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditPermsRequest.ProtoReflect.Descriptor instead.
func (*EditPermsRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_perm_proto_rawDescGZIP(), []int{0}
}

func (x *EditPermsRequest) GetAction() EditAction_Enum {
	if x != nil {
		return x.Action
	}
	return EditAction_unspecified
}

func (x *EditPermsRequest) GetPerms() []string {
	if x != nil {
		return x.Perms
	}
	return nil
}

type ListPermClassReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// resource type: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
	Classes []string `protobuf:"bytes,1,rep,name=classes,proto3" json:"classes,omitempty"`
}

func (x *ListPermClassReply) Reset() {
	*x = ListPermClassReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_perm_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPermClassReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPermClassReply) ProtoMessage() {}

func (x *ListPermClassReply) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_perm_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPermClassReply.ProtoReflect.Descriptor instead.
func (*ListPermClassReply) Descriptor() ([]byte, []int) {
	return file_iam_v1_perm_proto_rawDescGZIP(), []int{1}
}

func (x *ListPermClassReply) GetClasses() []string {
	if x != nil {
		return x.Classes
	}
	return nil
}

type ListPermRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagesz int32 `protobuf:"varint,1,opt,name=pagesz,proto3" json:"pagesz,omitempty"`
	// An opaque pagination token returned from a previous call.
	// An empty token denotes the first page.
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// resource type: IamGroup/IamUser/IamRole/AnnoLot/AnnoJob/...
	Class       string `protobuf:"bytes,3,opt,name=class,proto3" json:"class,omitempty"`
	NamePattern string `protobuf:"bytes,4,opt,name=name_pattern,json=namePattern,proto3" json:"name_pattern,omitempty"`
}

func (x *ListPermRequest) Reset() {
	*x = ListPermRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_perm_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPermRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPermRequest) ProtoMessage() {}

func (x *ListPermRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_perm_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPermRequest.ProtoReflect.Descriptor instead.
func (*ListPermRequest) Descriptor() ([]byte, []int) {
	return file_iam_v1_perm_proto_rawDescGZIP(), []int{2}
}

func (x *ListPermRequest) GetPagesz() int32 {
	if x != nil {
		return x.Pagesz
	}
	return 0
}

func (x *ListPermRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListPermRequest) GetClass() string {
	if x != nil {
		return x.Class
	}
	return ""
}

func (x *ListPermRequest) GetNamePattern() string {
	if x != nil {
		return x.NamePattern
	}
	return ""
}

type ListPermReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Perms         []string `protobuf:"bytes,1,rep,name=perms,proto3" json:"perms,omitempty"`
	NextPageToken string   `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListPermReply) Reset() {
	*x = ListPermReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iam_v1_perm_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPermReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPermReply) ProtoMessage() {}

func (x *ListPermReply) ProtoReflect() protoreflect.Message {
	mi := &file_iam_v1_perm_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPermReply.ProtoReflect.Descriptor instead.
func (*ListPermReply) Descriptor() ([]byte, []int) {
	return file_iam_v1_perm_proto_rawDescGZIP(), []int{3}
}

func (x *ListPermReply) GetPerms() []string {
	if x != nil {
		return x.Perms
	}
	return nil
}

func (x *ListPermReply) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

var File_iam_v1_perm_proto protoreflect.FileDescriptor

var file_iam_v1_perm_proto_rawDesc = []byte{
	0x0a, 0x11, 0x69, 0x61, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x69,
	0x61, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x95, 0x01, 0x0a, 0x10, 0x45, 0x64, 0x69, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x64, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x70, 0x65, 0x72, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x92, 0x01, 0x12, 0x22, 0x10, 0x72, 0x0e, 0x28, 0x40, 0x32,
	0x0a, 0x5e, 0x5c, 0x77, 0x2b, 0x5c, 0x2e, 0x5c, 0x77, 0x2b, 0x24, 0x52, 0x05, 0x70, 0x65, 0x72,
	0x6d, 0x73, 0x3a, 0x14, 0xba, 0x47, 0x11, 0xba, 0x01, 0x05, 0x70, 0x65, 0x72, 0x6d, 0x73, 0xba,
	0x01, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x2e, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x65, 0x72, 0x6d, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x22, 0x8c, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x65, 0x72, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x06,
	0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x73, 0x7a, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x74,
	0x74, 0x65, 0x72, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x61, 0x6d, 0x65,
	0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x22, 0x6c, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x65, 0x72, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x65, 0x72, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x70, 0x65, 0x72, 0x6d, 0x73, 0x12, 0x26,
	0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x3a, 0x1d, 0xba, 0x47, 0x1a, 0xba, 0x01, 0x05, 0x70, 0x65,
	0x72, 0x6d, 0x73, 0xba, 0x01, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x32, 0x8b, 0x02, 0x0a, 0x05, 0x50, 0x65, 0x72, 0x6d, 0x73, 0x12,
	0x53, 0x0a, 0x09, 0x45, 0x64, 0x69, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x73, 0x12, 0x18, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x14,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0e, 0x3a, 0x01, 0x2a, 0x22, 0x09, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x65, 0x72, 0x6d, 0x73, 0x12, 0x5e, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x72, 0x6d,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1a, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x13, 0x12, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x73, 0x2f, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x65, 0x73, 0x12, 0x4d, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x72, 0x6d,
	0x12, 0x17, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65,
	0x72, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x11, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0b, 0x12, 0x09, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65,
	0x72, 0x6d, 0x73, 0x42, 0x3b, 0x0a, 0x06, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x2f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x72, 0x70, 0x2e, 0x6b, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x79, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x76, 0x31, 0x3b, 0x69, 0x61, 0x6d,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_iam_v1_perm_proto_rawDescOnce sync.Once
	file_iam_v1_perm_proto_rawDescData = file_iam_v1_perm_proto_rawDesc
)

func file_iam_v1_perm_proto_rawDescGZIP() []byte {
	file_iam_v1_perm_proto_rawDescOnce.Do(func() {
		file_iam_v1_perm_proto_rawDescData = protoimpl.X.CompressGZIP(file_iam_v1_perm_proto_rawDescData)
	})
	return file_iam_v1_perm_proto_rawDescData
}

var file_iam_v1_perm_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_iam_v1_perm_proto_goTypes = []interface{}{
	(*EditPermsRequest)(nil),   // 0: iam.v1.EditPermsRequest
	(*ListPermClassReply)(nil), // 1: iam.v1.ListPermClassReply
	(*ListPermRequest)(nil),    // 2: iam.v1.ListPermRequest
	(*ListPermReply)(nil),      // 3: iam.v1.ListPermReply
	(EditAction_Enum)(0),       // 4: iam.v1.EditAction.Enum
	(*emptypb.Empty)(nil),      // 5: google.protobuf.Empty
}
var file_iam_v1_perm_proto_depIdxs = []int32{
	4, // 0: iam.v1.EditPermsRequest.action:type_name -> iam.v1.EditAction.Enum
	0, // 1: iam.v1.Perms.EditPerms:input_type -> iam.v1.EditPermsRequest
	5, // 2: iam.v1.Perms.ListPermClass:input_type -> google.protobuf.Empty
	2, // 3: iam.v1.Perms.ListPerm:input_type -> iam.v1.ListPermRequest
	5, // 4: iam.v1.Perms.EditPerms:output_type -> google.protobuf.Empty
	1, // 5: iam.v1.Perms.ListPermClass:output_type -> iam.v1.ListPermClassReply
	3, // 6: iam.v1.Perms.ListPerm:output_type -> iam.v1.ListPermReply
	4, // [4:7] is the sub-list for method output_type
	1, // [1:4] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_iam_v1_perm_proto_init() }
func file_iam_v1_perm_proto_init() {
	if File_iam_v1_perm_proto != nil {
		return
	}
	file_iam_v1_type_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_iam_v1_perm_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditPermsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_perm_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPermClassReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_perm_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPermRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iam_v1_perm_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPermReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_iam_v1_perm_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_iam_v1_perm_proto_goTypes,
		DependencyIndexes: file_iam_v1_perm_proto_depIdxs,
		MessageInfos:      file_iam_v1_perm_proto_msgTypes,
	}.Build()
	File_iam_v1_perm_proto = out.File
	file_iam_v1_perm_proto_rawDesc = nil
	file_iam_v1_perm_proto_goTypes = nil
	file_iam_v1_perm_proto_depIdxs = nil
}
