["class", "'File'", "implements", "'Namespace'", "{", "'related'", ":", "{", "'parents'", ":", "'File'", "[", "]", "'viewers'", ":", "'User'", "[", "]", "'owners'", ":", "'User'", "[", "]", "'siblings'", ":", "'File'", "[", "]", "}", "'permits'", "=", "{", "'view'", ":", "(", "ctx", ":", "'Context'", ")", ":", "'boolean'", "=>", "this", ".", "'related'", ".", "'parents'", ".", "'some'", "(", "'p'", "=>", "'p'", ".", "'permits'", ".", "'view'", "(", "ctx", ")", ")", "||", "this", ".", "'related'", ".", "'viewers'", ".", "'includes'", "(", "ctx", ".", "'subject'", ")", "||", "!", "this", ".", "'related'", ".", "'owners'", ".", "'includes'", "(", "ctx", ".", "'subject'", ")", ",", "'edit'", ":", "(", "ctx", ":", "'Context'", ")", "=>", "this", ".", "'related'", ".", "'owners'", ".", "'includes'", "(", "ctx", ".", "'subject'", ")", ",", "'rename'", ":", "(", "ctx", ":", "'Context'", ")", "=>", "this", ".", "'related'", ".", "'siblings'", ".", "'some'", "(", "'s'", "=>", "'s'", ".", "'permits'", ".", "'edit'", "(", "ctx", ")", ")", "}", "}", "EOF"]