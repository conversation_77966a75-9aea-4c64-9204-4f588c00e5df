{"noEdit": true, "task_label": "point_cloud", "task_type": 902, "dotLimitLine": "", "dotLimit": "", "template_name": "", "is_online": true, "per_type": false, "task_key": "", "template_key": "", "deny_reason": "", "postil_config": [{"name": "漏标", "value": 1, "status": 1}, {"name": "多标", "value": 2, "status": 1}, {"name": "错标", "value": 3, "status": 1}], "is_ocr_auto_fill": 0, "is_open_camera_tag": 0, "selection_method": 0, "fuse_configure_method": 0, "merge_point_cloud": 0, "boxsize": [], "sub_round": [], "dataclass_list": [], "is_mark_region": false, "mark_region": [], "save_mark_region": {}, "properties": [{"toolName": "立体框", "toolType": "volume", "fuse_2d_tools_checkbox": [], "min_points_num": 0, "attrs_same": 1, "is_attrs_sync": 0, "fuse_tool": "", "class": [{"ptitle": "汽车", "pname": "car", "color": "rgb(146, 162, 146)", "pcode": "5f03ac15", "is_instance": 0, "size_same": 1, "attrs": [{"ptitle": "车辆状态", "pname": "Vehiclestatus", "type": 0, "pvalue": [{"pname": "parked", "ptitle": "路边停车，始终静止", "code": "51d0db9a"}, {"pname": "stopped", "ptitle": "暂时停车", "code": "d658e819"}, {"pname": "moving", "ptitle": "始终移动", "code": "0e91c86d"}], "default_value": ""}, {"ptitle": "特殊车辆", "pname": "specialve<PERSON>les", "type": 0, "pvalue": [{"pname": "policecar", "ptitle": "警车", "code": "c15ba567"}, {"pname": "ambulance", "ptitle": "救护车", "code": "d630ee57"}, {"pname": "no", "ptitle": "否", "code": "7d402eef"}], "default_value": "7d402eef"}]}, {"ptitle": "公交车", "pname": "bus", "color": "rgb(32, 182, 132)", "pcode": "0d50444d", "is_instance": 0, "size_same": 1, "attrs": [{"ptitle": "车辆状态", "pname": "Vehiclestatus", "type": 0, "pvalue": [{"pname": "parked", "ptitle": "路边停车，始终静止", "code": "51d0db9a"}, {"pname": "stopped", "ptitle": "暂时停车", "code": "d658e819"}, {"pname": "moving", "ptitle": "始终移动", "code": "0e91c86d"}], "default_value": ""}]}, {"ptitle": "卡车", "pname": "truck", "color": "rgb(32, 167, 182)", "pcode": "56cc1324", "is_instance": 0, "size_same": 1, "attrs": [{"ptitle": "车辆状态", "pname": "Vehiclestatus", "type": 0, "pvalue": [{"pname": "parked", "ptitle": "路边停车，始终静止", "code": "51d0db9a"}, {"pname": "stopped", "ptitle": "暂时停车", "code": "d658e819"}, {"pname": "moving", "ptitle": "始终移动", "code": "0e91c86d"}], "default_value": ""}]}, {"ptitle": "拖车", "pname": "trailer", "color": "rgb(32, 167, 182)", "pcode": "0b08f447", "is_instance": 0, "size_same": 1, "attrs": [{"ptitle": "车辆状态", "pname": "Vehiclestatus", "type": 0, "pvalue": [{"pname": "parked", "ptitle": "路边停车，始终静止", "code": "51d0db9a"}, {"pname": "stopped", "ptitle": "暂时停车", "code": "d658e819"}, {"pname": "moving", "ptitle": "始终移动", "code": "0e91c86d"}], "default_value": ""}]}, {"ptitle": "小型异形车", "pname": "other_vehicle_small", "color": "rgb(174, 32, 182)", "pcode": "4118d360", "is_instance": 0, "size_same": 1, "attrs": [{"ptitle": "车辆状态", "pname": "Vehiclestatus", "type": 0, "pvalue": [{"pname": "parked", "ptitle": "路边停车，始终静止", "code": "51d0db9a"}, {"pname": "stopped", "ptitle": "暂时停车", "code": "d658e819"}, {"pname": "moving", "ptitle": "始终移动", "code": "0e91c86d"}], "default_value": ""}]}, {"ptitle": "大型异形车", "pname": "other_vehicle_large", "color": "rgb(182, 32, 114)", "pcode": "e3d047cc", "is_instance": 0, "size_same": 1, "attrs": [{"ptitle": "车辆状态", "pname": "Vehiclestatus", "type": 0, "pvalue": [{"pname": "parked", "ptitle": "路边停车，始终静止", "code": "51d0db9a"}, {"pname": "stopped", "ptitle": "暂时停车", "code": "d658e819"}, {"pname": "moving", "ptitle": "始终移动", "code": "0e91c86d"}], "default_value": ""}]}, {"ptitle": "自行车", "pname": "bicycle", "color": "rgb(182, 32, 57)", "pcode": "b80272b2", "is_instance": 0, "size_same": 1, "attrs": [{"ptitle": "车辆状态", "pname": "Vehiclestatus", "type": 0, "pvalue": [{"pname": "parked", "ptitle": "路边停车，始终静止", "code": "51d0db9a"}, {"pname": "stopped", "ptitle": "暂时停车", "code": "d658e819"}, {"pname": "moving", "ptitle": "始终移动", "code": "0e91c86d"}], "default_value": ""}, {"ptitle": "是否有骑行者", "pname": "bicycle", "type": 0, "pvalue": [{"pname": "yes", "ptitle": "是", "code": "05468989"}, {"pname": "no", "ptitle": "否", "code": "12363ee2"}], "default_value": ""}]}, {"ptitle": "摩托车", "pname": "motorcycle", "color": "rgb(58, 10, 18)", "pcode": "a5d5b73a", "is_instance": 0, "size_same": 1, "attrs": [{"ptitle": "车辆状态", "pname": "Vehiclestatus", "type": 0, "pvalue": [{"pname": "parked", "ptitle": "路边停车，始终静止", "code": "51d0db9a"}, {"pname": "stopped", "ptitle": "暂时停车", "code": "d658e819"}, {"pname": "moving", "ptitle": "始终移动", "code": "0e91c86d"}], "default_value": ""}, {"ptitle": "是否有骑行者", "pname": "bicycle", "type": 0, "pvalue": [{"pname": "yes", "ptitle": "是", "code": "05468989"}, {"pname": "no", "ptitle": "否", "code": "12363ee2"}], "default_value": ""}]}, {"ptitle": "三轮车", "pname": "tricycle", "color": "rgb(3, 187, 215)", "pcode": "22e29a01", "is_instance": 0, "size_same": 1, "attrs": [{"ptitle": "车辆状态", "pname": "Vehiclestatus", "type": 0, "pvalue": [{"pname": "parked", "ptitle": "路边停车，始终静止", "code": "51d0db9a"}, {"pname": "stopped", "ptitle": "暂时停车", "code": "d658e819"}, {"pname": "moving", "ptitle": "始终移动", "code": "0e91c86d"}], "default_value": ""}, {"ptitle": "是否有骑行者", "pname": "bicycle", "type": 0, "pvalue": [{"pname": "yes", "ptitle": "是", "code": "05468989"}, {"pname": "no", "ptitle": "否", "code": "12363ee2"}], "default_value": ""}]}, {"ptitle": "轮椅", "pname": "wheelchair", "color": "rgb(236, 184, 105)", "pcode": "83943853", "is_instance": 0, "size_same": 1}, {"ptitle": "成人", "pname": "adult", "color": "rgb(215, 233, 12)", "pcode": "43579130", "is_instance": 0, "size_same": 0, "attrs": [{"ptitle": "行人姿态", "pname": "Pedestrianposture", "type": 0, "pvalue": [{"pname": "standing", "ptitle": "站立", "code": "b7201624"}, {"pname": "sitting-lying", "ptitle": "坐着/躺着", "code": "8bfd32f3"}, {"pname": "moving", "ptitle": "移动", "code": "4f29202f"}], "default_value": ""}]}, {"ptitle": "儿童", "pname": "child", "color": "rgb(215, 233, 12)", "pcode": "88d7a53d", "is_instance": 0, "size_same": 0, "attrs": [{"ptitle": "行人姿态", "pname": "Pedestrianposture", "type": 0, "pvalue": [{"pname": "standing", "ptitle": "站立", "code": "b7201624"}, {"pname": "sitting-lying", "ptitle": "坐着/躺着", "code": "8bfd32f3"}, {"pname": "moving", "ptitle": "移动", "code": "4f29202f"}], "default_value": ""}]}, {"ptitle": "工作者", "pname": "worker", "color": "rgb(215, 233, 12)", "pcode": "b97d9c55", "is_instance": 0, "size_same": 0, "attrs": [{"ptitle": "行人姿态", "pname": "Pedestrianposture", "type": 0, "pvalue": [{"pname": "standing", "ptitle": "站立", "code": "b7201624"}, {"pname": "sitting-lying", "ptitle": "坐着/躺着", "code": "8bfd32f3"}, {"pname": "moving", "ptitle": "移动", "code": "4f29202f"}], "default_value": ""}]}, {"ptitle": "警察", "pname": "police_officer", "color": "rgb(215, 233, 12)", "pcode": "846d7c30", "is_instance": 0, "size_same": 0, "attrs": [{"ptitle": "行人姿态", "pname": "Pedestrianposture", "type": 0, "pvalue": [{"pname": "standing", "ptitle": "站立", "code": "b7201624"}, {"pname": "sitting-lying", "ptitle": "坐着/躺着", "code": "8bfd32f3"}, {"pname": "moving", "ptitle": "移动", "code": "4f29202f"}], "default_value": ""}]}, {"ptitle": "障碍物", "pname": "barrier", "color": "rgb(222, 47, 23)", "pcode": "7a5a3d46", "is_instance": 0, "size_same": 1}, {"ptitle": "锥形交通路标", "pname": "trafficcone", "color": "rgb(24, 184, 20)", "pcode": "d0a5d3c2", "is_instance": 0, "size_same": 1}, {"ptitle": "附属物", "pname": "attachment", "color": "rgb(42, 184, 229)", "pcode": "53928cb8", "is_instance": 0, "size_same": 1}], "attrs": [], "preset": [{"name": "车辆", "length": 4.59, "width": 1.96, "height": 1.67}, {"name": "摩托车", "length": 2.1, "width": 0.91, "height": 1.48}, {"name": "人", "length": 0.57, "width": 0.61, "height": 1.68}, {"name": "卡车", "length": 8.58, "width": 2.76, "height": 2.38}, {"name": "拖车", "length": 14.01, "width": 3.18, "height": 2.87}, {"name": "巴士", "length": 10.15, "width": 3.02, "height": 2.6}, {"name": "自行车", "length": 1.75, "width": 0.63, "height": 1.39}, {"name": "交通路锥", "length": 0.36, "width": 0.35, "height": 0.73}, {"name": "障碍物", "length": 0.91, "width": 0.88, "height": 0.87}, {"name": "小型异型车", "length": 3.16, "width": 1.4, "height": 1.64}, {"name": "三轮车", "length": 2.61, "width": 1.26, "height": 1.66}, {"name": "大型异型车", "length": 8.07, "width": 2.95, "height": 3.75}, {"name": "轮椅", "length": 1.06, "width": 0.8, "height": 1.22}]}], "attributeSeparation": 0, "properties2D": [], "global": {"attrs": [], "class": []}, "price": {"channel_price": {}, "customer_price": {}}, "train_desc": "", "train_num": "", "train_max_num": "", "train_rate": "", "package_type": 1, "submit_at": "", "update_time": "", "check_limit": "", "rotate_picture_export_type": 1, "is_panoramic": 0, "is_cover": 0, "is_class_type": 1, "group_type": 0, "assist": [], "is_vsample_interval": 0, "vsample_interval": "", "isCutting": 0, "splitChannels": 0, "minNum": "", "maxNum": "", "isConversion": 0, "conversion_type": 0, "isPinyin": 0, "task_mode": 1, "cut_type": 0, "isAudioProps": 1, "isAddTime": 0, "addTime": "", "is_prop_check": 0, "is_escape": 0, "appropriate_types": 1, "props_save": 0, "labeling_method": 0, "add_attr": 0, "modify_corpus": 0, "is_pre_labeled": 0, "pro_type": 0, "num_of_task": 1, "num_of_pack": "", "articleTitle_type": 0, "article_titleArr": [], "corpus_type": 0, "text_title": [], "ocr_content_configure": [], "camera_properties": [], "textadd": false, "add_tag": 0, "tag_type": 0, "reference_tag": 0, "min_words_length": "", "max_words_length": "", "min_num_pieces": "", "max_num_pieces": "", "regStatus": 0, "is_expression": 0, "expression_content": "", "generate_mode": 0, "cue_word": [], "check_rule_name": 0, "translate": 0, "discrete": 0, "relationship_definition": [], "events_definition": [], "attribute_definition": [], "entity_premark": 0, "entity_library_id": "", "force_submit": 0, "cleaning_type": "1", "cleaningTypeArr": [], "type_list": [], "preset_type": "", "pass_total_limit": 0, "boxConsistent": 0, "is_private": 1, "project_type": {"pic": [{"task_type": 1001, "title": "图像通用标注", "imgsrc": "/images/show_1001.png", "icon": "#icontongyongbiaozhu", "is_allow": 1}, {"task_type": 1002, "title": "OCR文字转写", "imgsrc": "/images/show_1002.png", "icon": "#iconwenzizhuanxie", "is_allow": 1}, {"task_type": 1003, "title": "REID目标跟踪", "imgsrc": "/images/show_1003.png", "icon": "#iconmubiaogenzong", "is_allow": 1}, {"task_type": 1004, "title": "图像语义分割", "imgsrc": "/images/show_1004.png", "icon": "#iconyuyifenge1", "is_allow": 1}, {"task_type": 1006, "title": "人体关键点", "imgsrc": "/images/show_1006.png", "icon": "#<PERSON>rentiguanjiandian", "is_allow": 1}], "point_cloud": [{"task_type": 902, "title": "点云", "imgsrc": "/images/show_902.png", "icon": "#icondianyun2d3dronghe", "is_allow": 1}], "audio": [{"task_type": 704, "title": "音频标注", "imgsrc": "/images/show_704.png", "icon": "#iconyin<PERSON><PERSON><PERSON>hu", "is_allow": 1}], "text": [{"task_type": 1111, "title": "意图及实体标注", "imgsrc": "/images/show_1111.png", "icon": "#iconyi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_allow": 1}, {"task_type": 1202, "title": "命名实体识别与关系标注", "imgsrc": "/images/show_1202.png", "icon": "#iconmingmingshitishibieyuguanxibiaozhu", "is_allow": 1}, {"task_type": 1103, "title": "文本生成", "imgsrc": "/images/show_1103.png", "icon": "#iconwenben", "is_allow": 1}, {"task_type": 1112, "title": "文章判断", "imgsrc": "/images/show_1112.png", "icon": "#icon<PERSON>zhangpanduan", "is_allow": 1}, {"task_type": 1113, "title": "相似文本判断", "des": "给定参考语料，从一组句子中选择单条或多条相似文本", "imgsrc": "/images/show_1113.png", "icon": "#icon<PERSON>zhangpanduan", "is_allow": 1}], "video": [{"task_type": 1201, "title": "视频标注", "imgsrc": "/images/show_1201.png", "icon": "#icons<PERSON><PERSON><PERSON><PERSON><PERSON>", "is_allow": 1}]}, "issues": [{"name": "漏标", "value": 1, "status": 1}, {"name": "多标", "value": 2, "status": 1}, {"name": "错标", "value": 3, "status": 1}]}