 [![Test Status](https://github.com/gobuffalo/tags/workflows/Tests/badge.svg)](https://github.com/gobuffalo/tags/actions)
 [![Go Report Card](https://goreportcard.com/badge/github.com/gobuffalo/tags)](https://goreportcard.com/report/github.com/gobuffalo/tags)

# Tags

Easily build HTML tags in Go! This package is especially useful when using [http://gobuffalo.io](http://gobuffalo.io).

## Getting Started
### Prerequisites

Tags has a minimum Go dependency of 1.12.0 and is being actively tested against go 1.12 and 1.13.

### Installing

To get this lib just get it via `go get`

```
go get github.com/gobuffalo/tags/...
```
## Running the tests

In order to run the tests you can execute:

```
go test ./...--race
```
## Documentation

Documentation is currently in [this repository Wiki](https://github.com/gobuffalo/tags/wiki), you can find there instructions on how to use tags within your app.

## Contributing

If you want to contribute, please read this article first: [Contributing to Open Source Git Repositories in Go](https://splice.com/blog/contributing-open-source-git-repositories-go/). It shows how to configure your git environment to avoid common pitfalls. This article is recommended to all those who are looking to contribute to any Go projects.

^ Taken from [gobuffalo.io](https://https://gobuffalo.io/docs/contributing)
