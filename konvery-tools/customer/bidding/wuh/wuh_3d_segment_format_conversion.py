import os
import sys
import numpy as np
sys.path.append('.')
from customer.common import tools


label2idx = {
    'Road': 1,
    'Background': 2,
    'Obstacle': 3,
    'Dust': 4
}

def run(input_dir, output_dir):
    json_files = tools.get_json_files(input_dir)
    for json_file in json_files:
        json_data = tools.get_json_data(json_file)
        labels = [label2idx[label] for label in json_data['segmentation']['result'][0]]
        labels = np.array(labels, dtype=np.uint32)
        out_file = os.path.join(output_dir, json_file.name.replace(".json", ".label"))
        labels.tofile(out_file)


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out)
