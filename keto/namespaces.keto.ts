import { Namespace, SubjectSet, Context } from '@ory/keto-namespace-types';

class IamPerm implements Namespace {}

// IamRole ID:
// for global roles: <role-name>
// for org roles: <org-uid>.<role-name>
class IamRole implements Namespace {
  related: {
    parents: IamGroup[]
    policies: IamPolicy[]
    perms: (IamPerm | SubjectSet<IamRole, "perms">)[]
  }

  permits = {
    has_perm: (ctx: Context, perm: string): boolean => this.related.perms.includes(perm),

    check: (ctx: Context, perm: string): boolean =>
      IamRole:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),

    get: (ctx: Context): boolean => this.permits.check(ctx, "IamRole.get"),
    list: (ctx: Context): boolean => this.permits.check(ctx, "IamRole.list"),
    update: (ctx: Context): boolean => this.permits.check(ctx, "IamRole.update"),
    delete: (ctx: Context): boolean => this.permits.check(ctx, "IamRole.delete"),
    create: (ctx: Context): boolean => this.permits.check(ctx, "IamRole.create"),
    getPolicy: (ctx: Context): boolean => this.permits.check(ctx, "IamRole.getPolicy"),
    setPolicy: (ctx: Context): boolean => this.permits.check(ctx, "IamRole.setPolicy"),
  }
}

// for system policies: sys/<name>
// for per resource policies: <resource-ns>/<resource-name>.<role-name>
class IamPolicy implements Namespace {
  related: {
    roles: IamRole[] // only one role is allowed
    users: (IamUser | SubjectSet<IamGroup, "members">)[]
  }

  permits = {
    // check describes if the policy binds the subject and the permission
    check: (ctx: Context, perm: string): boolean =>
      this.related.users.includes(ctx.subject) &&
      this.related.roles.traverse((p) => p.permits.has_perm(ctx, perm)),
  }
}

// IamGroup ID: <group-uid>
class IamGroup implements Namespace {
  related: {
    parents: IamGroup[]
    policies: IamPolicy[]
    members: (IamUser | SubjectSet<IamGroup, "members">)[]
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      IamGroup:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),

    get: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.get"),
    list: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.list"),
    update: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.update"),
    delete: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.delete"),
    create: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.create"),
    getPolicy: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.getPolicy"),
    setPolicy: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.setPolicy"),

    listMember: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.listMember"),
    addMember: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.addMember"),
    deleteMember: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.deleteMember"),
    setMemberRole: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.setMemberRole"),

    stat: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.stat"),
    // allocate some members to a task
    deploy: (ctx: Context): boolean => this.permits.check(ctx, "IamGroup.deploy"),
  }
}

// IamUser ID: <user-uid>
class IamUser implements Namespace {
  related: {
    parents: IamGroup[]
    policies: IamPolicy[]
    owners: IamUser[] // one is always the owner of oneself
  }

  permits = {
    check: (ctx: Context, perm: string): boolean => this == ctx.subject ||
      IamUser:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.owners.includes(ctx.subject),
    get: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.get"),
    list: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.list"),
    update: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.update"),
    delete: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.delete"),
    create: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.create"),
    getPolicy: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.getPolicy"),
    setPolicy: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.setPolicy"),
    getPrivacy: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.getPrivacy"),

    stat: (ctx: Context): boolean => this.permits.check(ctx, "IamUser.stat"),
  }
}

// AnnoLot ID:
// for top lots: <lot-uid>
// for phased lots: <lot-uid>.phase-<index>
class AnnoLot implements Namespace {
  related: {
    parents: (IamGroup|AnnoLot)[] // IamGroup for general lots and AnnoLot for phased lots
    policies: IamPolicy[]
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      AnnoLot:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),

    get: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.get"),
    list: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.list"),
    update: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.update"),
    delete: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.delete"),
    create: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.create"),
    getPolicy: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.getPolicy"),
    setPolicy: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.setPolicy"),

    // start: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.start"),
    // pause: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.pause"),
    // cancel: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.cancel"),
    exportAnno: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.exportAnno"),
    assignExecteam: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.assignExecteam"),
    listJob: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.listJob"),
    stat: (ctx: Context): boolean => this.permits.check(ctx, "AnnoLot.stat"),
  }
}

// AnnoJob ID:
// for top jobs: <job-uid>
// for sub jobs: <job-uid>.<subtype>
class AnnoJob implements Namespace {
  related: {
    parents: AnnoLot[]
    policies: IamPolicy[]
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      AnnoJob:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),

    get: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.get"),
    list: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.list"),
    update: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.update"),
    delete: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.delete"),
    create: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.create"),
    getPolicy: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.getPolicy"),
    setPolicy: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.setPolicy"),

    // claim: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.claim"),
    // submit: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.submit"),
    assign: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.assign"),
    reject: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.reject"), // force reject/recycle
    log: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.log"), // check log
    stat: (ctx: Context): boolean => this.permits.check(ctx, "AnnoJob.stat"),
  }
}

class AnnoOrder implements Namespace {
  related: {
    parents: IamGroup[]
    policies: IamPolicy[]
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      AnnoOrder:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),

    get: (ctx: Context): boolean => this.permits.check(ctx, "AnnoOrder.get"),
    list: (ctx: Context): boolean => this.permits.check(ctx, "AnnoOrder.list"),
    update: (ctx: Context): boolean => this.permits.check(ctx, "AnnoOrder.update"),
    delete: (ctx: Context): boolean => this.permits.check(ctx, "AnnoOrder.delete"),
    create: (ctx: Context): boolean => this.permits.check(ctx, "AnnoOrder.create"),
    getPolicy: (ctx: Context): boolean => this.permits.check(ctx, "AnnoOrder.getPolicy"),
    setPolicy: (ctx: Context): boolean => this.permits.check(ctx, "AnnoOrder.setPolicy"),

    exportAnno: (ctx: Context): boolean => this.permits.check(ctx, "AnnoOrder.exportAnno"),
    stat: (ctx: Context): boolean => this.permits.check(ctx, "AnnoOrder.stat"),
  }
}

class AnnofeedData implements Namespace {
  related: {
    parents: IamGroup[]
    policies: IamPolicy[]
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      AnnofeedData:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),

    get: (ctx: Context): boolean => this.permits.check(ctx, "AnnofeedData.get"),
    list: (ctx: Context): boolean => this.permits.check(ctx, "AnnofeedData.list"),
    update: (ctx: Context): boolean => this.permits.check(ctx, "AnnofeedData.update"),
    delete: (ctx: Context): boolean => this.permits.check(ctx, "AnnofeedData.delete"),
    create: (ctx: Context): boolean => this.permits.check(ctx, "AnnofeedData.create"),
    getPolicy: (ctx: Context): boolean => this.permits.check(ctx, "AnnofeedData.getPolicy"),
    setPolicy: (ctx: Context): boolean => this.permits.check(ctx, "AnnofeedData.setPolicy"),
  }
}

class AnnofeedFile implements Namespace {
  related: {
    parents: IamGroup[]
    policies: IamPolicy[]
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      AnnofeedFile:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),

    get: (ctx: Context): boolean => this.permits.check(ctx, "AnnofeedFile.get"),
    list: (ctx: Context): boolean => this.permits.check(ctx, "AnnofeedFile.list"),
    update: (ctx: Context): boolean => this.permits.check(ctx, "AnnofeedFile.update"),
    delete: (ctx: Context): boolean => this.permits.check(ctx, "AnnofeedFile.delete"),
    create: (ctx: Context): boolean => this.permits.check(ctx, "AnnofeedFile.create"),
    getPolicy: (ctx: Context): boolean => this.permits.check(ctx, "AnnofeedFile.getPolicy"),
    setPolicy: (ctx: Context): boolean => this.permits.check(ctx, "AnnofeedFile.setPolicy"),
  }
}

// FePage ID: <page-path>
class FePage implements Namespace {
  related: {
    parents: FePage[]
    policies: IamPolicy[]
    // viewers: (IamUser | SubjectSet<IamGroup, "members">)[]
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      FePage:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),

    view: (ctx: Context): boolean => this.permits.check(ctx, "FePage.view"),
    getPolicy: (ctx: Context): boolean => this.permits.check(ctx, "FePage.getPolicy"),
    setPolicy: (ctx: Context): boolean => this.permits.check(ctx, "FePage.setPolicy"),
  }
}
