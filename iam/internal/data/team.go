// source: gitlab.rp.konvery.work/platform/apis/iam/v1/team.proto
package data

import (
	"context"

	"iam/internal/biz"

	"gitlab.rp.konvery.work/platform/pkg/data"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/log"
	"gorm.io/gorm"
)

type teamsRepo struct {
	repo.GenericRepo[biz.Team]
	data *Data
}

func NewTeamsRepo(d *Data, logger log.Logger) biz.TeamsRepo {
	return &teamsRepo{GenericRepo: data.NewGenericRepo[biz.Team](d, logger, biz.TeamTableName), data: d}
}

func (o *teamsRepo) DeleteByID(ctx context.Context, id int64) error {
	return o.DoTx(ctx, func(ctx context.Context, tx biz.Tx) error {
		err := o.data.WithCtx(ctx).Delete(&biz.TeamMember{}, biz.TeamMember_TeamID+" = ?", id).Error
		if err != nil {
			return Convert(&biz.TeamMember{}, err)
		}
		return o.GenericRepo.DeleteByID(ctx, id)
	})
}

func (o *teamsRepo) ListByIDs(ctx context.Context, ids []int64) ([]*biz.Team, error) {
	datas := []*biz.Team{}
	q := o.data.WithCtx(ctx).Where("id IN ?", ids)
	err := q.Find(&datas).Error
	if err != nil {
		return nil, Convert(&biz.Team{}, err)
	}
	return datas, nil
}

// orgnization member operations

func (o *teamsRepo) buildListMembersQuery(ctx context.Context, id int64, p *biz.MemberListFilter) *gorm.DB {
	q := o.data.WithCtx(ctx).Model(&biz.User{}).
		Joins("JOIN team_members AS m ON users.id = m."+biz.TeamMember_UserID).
		Where("m."+biz.TeamMember_TeamID+" = ?", id)
	if p.Role != "" {
		q = q.Where("m."+biz.TeamMember_Role+" = ?", p.Role)
	} else if p.NamePattern != "" {
		q = q.Where("users."+biz.User_Name+" LIKE ?", "%"+p.NamePattern+"%")
	}
	return q
}

func (o *teamsRepo) ListMembers(ctx context.Context, id int64, p *biz.MemberListFilter) ([]*biz.User, error) {
	datas := []*biz.User{}
	// role from team_members table will take precedence
	q := o.buildListMembersQuery(ctx, id, p).Select("users.*, m." + biz.TeamMember_Role).
		Order("m." + biz.TeamMember_Role).Order("lower(users." + biz.User_Name + ")").
		Offset(p.Page * p.Pagesz).Limit(p.Pagesz)
	err := q.Find(&datas).Error
	if err != nil {
		return nil, Convert(&biz.User{}, err)
	}
	return datas, nil
}

func (o *teamsRepo) GetMembership(ctx context.Context, id int64, userIDs []int64) (mems []*biz.TeamMember, err error) {
	err = o.data.WithCtx(ctx).
		Where(biz.TeamMember_TeamID+" = ?", id).
		Where(biz.TeamMember_UserID+" IN ?", userIDs).
		Find(&mems).Error
	err = Convert(&biz.TeamMember{}, err)
	return
}

// CountMembers returns the number of items matching the filter, ignore the Page and Pagesz.
func (o *teamsRepo) CountMembers(ctx context.Context, id int64, p *biz.MemberListFilter) (int, error) {
	var cnt int64
	q := o.buildListMembersQuery(ctx, id, p)
	err := q.Model(&biz.User{}).Count(&cnt).Error
	if err != nil {
		return 0, Convert(&biz.User{}, err)
	}
	return int(cnt), nil
}

func (o *teamsRepo) EditMembers(ctx context.Context, isAdd bool, teamID int64, role string, userIDs []int64) error {
	if isAdd {
		rows := make([]biz.TeamMember, len(userIDs))
		for i, id := range userIDs {
			rows[i].TeamID = teamID
			rows[i].UserID = id
			rows[i].Role = role
		}
		err := o.data.WithCtx(ctx).Create(&rows).Error
		return Convert(&biz.TeamMember{}, err)
	}
	err := o.data.WithCtx(ctx).
		Where(biz.TeamMember_TeamID+" = ?", teamID).
		Where(biz.TeamMember_UserID+" IN ?", userIDs).
		Delete(&biz.TeamMember{}).Error
	return Convert(&biz.TeamMember{}, err)
}

func (o *teamsRepo) DeleteAllMembers(ctx context.Context, teamID int64) error {
	err := o.data.WithCtx(ctx).
		Where(biz.TeamMember_TeamID+" = ?", teamID).
		Delete(&biz.TeamMember{}).Error
	return Convert(&biz.TeamMember{}, err)
}

func (o *teamsRepo) SetMembersRole(ctx context.Context, teamID int64, role string, userIDs []int64) error {
	err := o.data.WithCtx(ctx).Model(&biz.TeamMember{}).
		Where(biz.TeamMember_TeamID+" = ?", teamID).
		Where(biz.TeamMember_UserID+" IN ?", userIDs).
		Updates(map[string]any{biz.TeamMember_Role: role}).Error
	return Convert(&biz.TeamMember{}, err)
}
