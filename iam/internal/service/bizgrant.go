// source: gitlab.rp.konvery.work/platform/apis/iam/v1/grant.proto
package service

import (
	"context"

	"iam/internal/biz"
	"iam/pkg/keto"

	"gitlab.rp.konvery.work/platform/apis/iam/v1"
	"gitlab.rp.konvery.work/platform/pkg/container/kslice"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kid"

	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func FromBizBizgrant(d *biz.Bizgrant) *iam.Bizgrant {
	if d == nil {
		return nil
	}
	o := &iam.Bizgrant{
		GrantorUid: kid.StringID(d.GrantorID),
		GranteeUid: kid.StringID(d.GranteeID),
		OrgUid:     kid.StringID(d.Org<PERSON>),
		Biz:        iam.BizType_Enum(iam.BizType_Enum_value[d.Biz]),
		CreatedAt:  timestamppb.New(d.CreatedAt),
	}
	return o
}

func ToBizBizgrant(d *iam.CreateBizgrantRequest) []*biz.Bizgrant {
	if d == nil {
		return nil
	}
	g := biz.Bizgrant{
		GranteeID: kid.ParseID(d.GranteeUid),
		OrgID:     kid.ParseID(d.OrgUid),
	}
	r := make([]*biz.Bizgrant, 0, len(d.Bizz))
	for _, v := range d.Bizz {
		if v == iam.BizType_unspecified {
			continue
		}

		g := g
		g.Biz = v.String()
		r = append(r, &g)
	}
	return r
}

func ToBizBizgrantFilter(f *iam.BizgrantFilter) *biz.BizgrantFilter {
	if f == nil {
		return &biz.BizgrantFilter{}
	}

	bz := ""
	if f.Biz != iam.BizType_unspecified {
		bz = f.Biz.String()
	}
	return &biz.BizgrantFilter{
		GrantorID: kid.ParseID(f.GrantorUid),
		GranteeID: kid.ParseID(f.GranteeUid),
		OrgID:     kid.ParseID(f.OrgUid),
		Biz:       bz,
	}
}

type BizgrantsService struct {
	iam.UnimplementedBizgrantsServer
	bz   *biz.BizgrantsBiz
	repo biz.BizgrantsRepo
}

func NewBizgrantsService(bz *biz.BizgrantsBiz, repo biz.BizgrantsRepo) *BizgrantsService {
	return &BizgrantsService{bz: bz, repo: repo}
}

func (o *BizgrantsService) getOrgUid(op *biz.User, orgUid string) string {
	if orgUid == "" && (biz.IsSysRole(op.Role) || op.OrgID == 0) {
		return ""
	}
	if orgUid == "" {
		orgUid = kid.StringID(op.OrgID)
	}
	return orgUid
}

func (o *BizgrantsService) CreateBizgrant(ctx context.Context, req *iam.CreateBizgrantRequest) (
	*iam.CreateBizgrantReply, error) {
	op := biz.UserFromCtx(ctx)
	req.OrgUid = o.getOrgUid(op.User, req.OrgUid)
	if req.OrgUid == "" {
		return nil, errors.NewErrEmptyField(errors.WithFields("org_uid"))
	}

	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermSetPolicy, keto.MakeGroupName(req.OrgUid)) {
		return nil, errors.NewErrForbidden()
	}
	gs := ToBizBizgrant(req)
	for i := range gs {
		gs[i].GrantorID = op.ID
	}
	if len(gs) == 0 {
		return nil, errors.NewErrEmptyField(errors.WithFields("bizz"))
	}
	gs, err := o.bz.BatchCreate(ctx, gs)
	if err != nil {
		return nil, err
	}
	return &iam.CreateBizgrantReply{Grants: kslice.Map(FromBizBizgrant, gs)}, err
}

func (o *BizgrantsService) DeleteBizgrant(ctx context.Context, req *iam.DeleteBizgrantRequest) (*emptypb.Empty, error) {
	filter := req.Filter
	if filter.GranteeUid == "" && filter.OrgUid == "" {
		return nil, errors.NewErrEmptyFieldAll(errors.WithFields("grantee_uid", "org_uid"))
	}
	op := biz.UserFromCtx(ctx)
	if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermSetPolicy, keto.MakeGroupName(filter.OrgUid)) {
		return nil, errors.NewErrForbidden()
	}

	return &emptypb.Empty{}, o.bz.DeleteByFilter(ctx, ToBizBizgrantFilter(filter))
}

func (o *BizgrantsService) ListBizgrant(ctx context.Context, req *iam.ListBizgrantRequest) (*iam.ListBizgrantReply, error) {
	f := req.Filter
	if f == nil || (f.GrantorUid == "" && f.GranteeUid == "" && f.OrgUid == "") {
		return nil, errors.NewErrEmptyFieldAll(errors.WithFields("grantor_uid", "grantee_uid", "org_uid"))
	}
	filter := ToBizBizgrantFilter(f)

	// TODO: check permissions
	// op := biz.UserFromCtx(ctx)
	// if !keto.DefaultAccessMgr().Allow(ctx, op.GetUid(), keto.PermGetPolicy, keto.MakeGroupName(scope)) {
	// 	return nil, errors.NewErrForbidden()
	// }

	pager := biz.Pager{
		Pagesz:    int(req.Pagesz),
		PageToken: biz.PageToken(req.PageToken),
	}
	datas, nextPageToken, err := o.bz.List(ctx, filter, pager)
	if err != nil {
		return nil, err
	}
	return &iam.ListBizgrantReply{NextPageToken: nextPageToken.String(), Grants: kslice.Map(FromBizBizgrant, datas)}, nil
}
