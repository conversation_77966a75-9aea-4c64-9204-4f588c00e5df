import os
import sys
import datetime

sys.path.append('.')
from customer.common import tools


object_to_cat_mapping = {
    'car': 'vehicle',
    'truck': 'vehicle',

    'pedestrian': 'pedestrian',

    'bicycle': 'cyclist',
    'tricycle': 'cyclist',
    'others_cyclist': 'cyclist',

    'cone': 'static_object',
    'pole': 'static_object',
    'bar': 'static_object',
    'water_barrier': 'static_object',
    'other_static_object': 'static_object',
    'pillar_cylin': 'static_object',
    'der': 'static_object',
    'pillar_cube': 'static_object',
    'pillar_other': 'static_object',
    'pillar_ignore': 'static_object',

    'other': 'others',
    'others': 'others',

    'ignore': 'ignore'
}


def construct_resources_and_collected_time(uri, mapping):
    resource = []
    for frame in mapping['collected_frames']:
        candidate_resource = list(filter(lambda resource: resource['uri'] == uri, frame['resources']))
        if len(candidate_resource) == 1:
            ori_resources = frame['resources']
            collected_time = frame['collected_time']
            break
    else:
        raise ValueError('No resources found')
    for ori_resource in ori_resources:
        resource.append({
            'sensor': ori_resource['sensor'],
            'uri': ori_resource['uri'].split('/')[-1].split('?')[0]
        })
    return resource, collected_time


def construct_lidarfusion_bbox(obj):
    obj_lidar = {
        "sensor": "LidarFusion",
        "type": "LidarFusion_bbox",
        "geometry": "cube",
        "cube": {
            "position": {
                "x": obj['geometry']['position']['x'],
                "y": obj['geometry']['position']['y'],
                "z": obj['geometry']['position']['z']
            },
            "rotation": {
                "pitch": obj['geometry']['rotation']['y'],
                "yaw": obj['geometry']['rotation']['z'],
                "roll": obj['geometry']['rotation']['x']
            },
            "dimension": {
                "length": obj['geometry']['size']['x'],
                "width": obj['geometry']['size']['y'],
                "height": obj['geometry']['size']['z']
            }
        }
    }
    return obj_lidar


def construct_cam_bbox(mapping_obj):
    obj_cam = {
        "sensor": mapping_obj['cameraName'],
        "type": f"{mapping_obj['cameraName']}_bbox",
        "geometry": "box_2d",
        "properties": {
            "truncated": mapping_obj['properties']['truncated']['value'][0].split('/')[0],
            "occluded": mapping_obj['properties']['occluded']['value'][0].split('/')[0]
        },
        "points": [
            {
                "x": mapping_obj['geometry']['left'],
                "y": mapping_obj['geometry']['top']
            },
            {
                "x": mapping_obj['geometry']['left'] + mapping_obj['geometry']['width'],
                "y": mapping_obj['geometry']['top'] + mapping_obj['geometry']['height']
            }
        ]
    }
    return obj_cam


def process_3d_obj(idx, obj):
    sub_category = obj['aliasName'].lower()
    category = object_to_cat_mapping[sub_category]
    obj_lidar = construct_lidarfusion_bbox(obj)
    objects = [obj_lidar]

    if category != 'ignore':
        for mapping_obj in obj['mappingObjects']:
            obj_cam = construct_cam_bbox(mapping_obj)
            objects.append(obj_cam)

    if category == 'vehicle':
        properties = {
            "category": category,
            "sub_category": sub_category,
            "door_opened": obj['mappingObjects'][0]['properties']['door_opened']['value'][0].split('/')[0],
            "track_id": str(idx)
        }
    elif category in {'pedestrian', 'static_object', 'others'}:
        properties = {
            "category": category,
            "sub_category": sub_category,
            "track_id": str(idx)
        }
    elif category == 'cyclist':
        properties = {
            "category": category,
            "sub_category": sub_category,
            "people_driving": obj['mappingObjects'][0]['properties']['people_driving']['value'][0].split('/')[0],
            "group": obj['mappingObjects'][0]['properties']['group']['value'][0].split('/')[0],
            "track_id": str(idx)
        }
    elif category == 'ignore':
        properties = {
            "category": category,
            "sub_category": sub_category,
            "is_auxiliary": "1"
        }
    else:
        raise ValueError(f'upsupported category: {category}')
    result = {
        "type": "detected_object",
        "properties": properties,
        "objects": objects
    }
    return result


def process_2d_obj(idx, obj):
    sub_category = obj['aliasName'].lower()
    category = object_to_cat_mapping[sub_category]
    objects = []

    if sub_category != 'ignore':
        obj_cam = construct_cam_bbox(obj)
        objects.append(obj_cam)

    if category == 'vehicle':
        properties = {
            "category": category,
            "sub_category": sub_category,
            "door_opened": obj['properties']['door_opened']['value'][0].split('/')[0],
            "track_id": str(idx)
        }
    elif category in {'pedestrian', 'static_object', 'others'}:
        properties = {
            "category": category,
            "sub_category": sub_category,
            "track_id": str(idx)
        }
    elif category == 'cyclist':
        properties = {
            "category": category,
            "sub_category": sub_category,
            "people_driving": obj['properties']['people_driving']['value'][0].split('/')[0],
            "group": obj['properties']['group']['value'][0].split('/')[0],
            "track_id": str(idx)
        }
    elif category == 'ignore':
        properties = {
            "category": category,
            "sub_category": sub_category,
            "is_auxiliary": "1"
        }
    else:
        raise ValueError(f'upsupported category: {category}')
    result = {
        "type": "detected_object",
        "properties": properties,
        "objects": objects
    }
    return result


def construct_frames(json_data):
    groups = []
    for idx, obj in enumerate(json_data['objects'], start=1):
        if obj['toolType'] == 'POINT_CLOUD_BOX':
            groups.append(process_3d_obj(idx, obj))
        else:
            groups.append(process_2d_obj(idx, obj))

    frame = {
        'properties': {
            'valid': '1'
        },
        'groups': groups
    }
    return [frame]


def process(vin, file, version, mapping, out_file):
    json_data = tools.get_json_data(file)
    uri = json_data['frameInfo']['metadata']['url']
    resource, collected_time = construct_resources_and_collected_time(uri, mapping)
    frames = construct_frames(json_data)
    result = {
        'schema_version': '1.0',
        'collect_metadata': {
            'vin': vin,
            'collected_time': collected_time // 1000
        },
        'resources': [
            resource
        ],
        'labeled_data': {
            'metadata': {
                "label_project": 'w3_ap_mono_3d_t',
                "label_rule_version": version,
                "supplier": 'konvery',
                "delivery_date": str(datetime.datetime.now().date())
            },
            'frames': frames
        }
    }
    tools.write_json_file(result, out_file, indent=4)


def run(input_dir, output_dir, mapping_dir):
    mapping_file = tools.get_json_files(mapping_dir)[0]
    mapping_data = tools.get_json_data(mapping_file)
    data_dirs = {os.path.dirname(os.path.dirname(file)) for file in tools.get_json_files(input_dir)}
    for data_dir in data_dirs:
        out_data_dir = os.path.join(output_dir, os.path.basename(data_dir))
        os.mkdir(out_data_dir)
        data_id = os.path.basename(data_dir).split('_')[0]
        vin = os.path.basename(data_dir).split('_')[1]
        mapping = list(filter(lambda x: x['data_id'] == data_id and x['vin'] == vin, mapping_data['dataset']))[0]
        version = mapping_data['batch_info']['rule_version']
        files = tools.get_json_files(data_dir)
        for file in files:
            process(vin, file, version, mapping, os.path.join(out_data_dir, os.path.basename(file)))


if __name__ == '__main__':
    args = tools.get_args()
    tools.check_dir(args.out)
    run(args.input, args.out, args.pcd)
